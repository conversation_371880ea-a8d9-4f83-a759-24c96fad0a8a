package com.jackrain.nea.oc.oms.es;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.es.util.ElasticSearchUtil;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2020/11/12 10:21 上午
 */
public class ES4MessageConsumeLog {

    /**
     * 表名，也是ES index/type名
     */
    public static final String TABLENAME_MESSAGE_CONSUME_LOG = "oc_b_message_consume_log";

    private ES4MessageConsumeLog() {
    }

    /**
     * 从ES中查找
     *
     * 根据topic和messageId查询id
     * @param topic     主题
     * @param messageId 消息ids
     *
     * topic  消息
     * MESSAGE_ID 消息id
     * @return id
     */
    public static Long selectIdByTopicAndMsgId(String topic, String messageId) {
        String returnFieldId = "ID";

        String[] returnFields = {returnFieldId};
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("TOPIC", topic);
        whereKeys.put("MESSAGE_ID", messageId);
        JSONObject search = ElasticSearchUtil.search(TABLENAME_MESSAGE_CONSUME_LOG, TABLENAME_MESSAGE_CONSUME_LOG,
                whereKeys, null, null, 50, 0, returnFields);

        JSONArray returnData = search.getJSONArray("data");

        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(returnData)) {
            Set<Long> idSet = new HashSet<>();

            for (int i = 0; i < returnData.size(); i++) {
                Long orderId = returnData.getJSONObject(i).getLong(returnFieldId);
                idSet.add(orderId);
            }

            if (idSet.size() > 0) {
                return idSet.iterator().next();
            }
        }

        return null;
    }
}
