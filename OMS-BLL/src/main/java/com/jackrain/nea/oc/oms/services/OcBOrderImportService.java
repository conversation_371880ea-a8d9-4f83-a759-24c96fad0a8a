package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.services.RegionNewService;
import com.jackrain.nea.cpext.model.ProvinceCityAreaInfo;
import com.jackrain.nea.cpext.model.RegionInfo;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OmsPayStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.order.address.ReceiverAddressDto;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderExtend;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderItemExtend;
import com.jackrain.nea.oc.oms.model.resources.OrderOccupyStatus;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderLog;
import com.jackrain.nea.oc.oms.model.table.OcBOrderPayment;
import com.jackrain.nea.oc.oms.model.table.task.OcBToBeConfirmedTask;
import com.jackrain.nea.oc.oms.nums.OcOrderRefundStatusEnum;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.oc.oms.util.ImportUtil;
import com.jackrain.nea.oc.oms.vo.OcBOrderRemarkImpVO;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.AddressResolutionUtils;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.OrderAddressConvertUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.TreeSet;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * @author: 李龙飞
 * @create: 2019-05-13 17:33
 **/
@Component
@Slf4j
public class OcBOrderImportService {
    //订单明细表
    private static final String ORDER_ITEM_TABLE_NAME = "OC_B_ORDER_ITEM";
    //订单表
    private static final String ORDER_TABLE_NAME = "OC_B_ORDER";
    @Autowired
    CpRpcService cpRpcService;
    @Autowired
    SaveBillService saveBillService;
    @Autowired
    BatchSaveBillService batchSaveBillService;

    @Autowired
    private BuildSequenceUtil sequenceUtil;

    @Autowired
    OmsOrderLogService omsOrderLogService;

    @Autowired
    private RegionNewService regionNewService;

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private RegionNewService regionService;

    @Autowired
    private OcBorderUpdateService service;

    @Autowired
    private AddressResolutionUtils addressResolutionUtils;
    @Autowired
    private ThreadPoolTaskExecutor orderImportThreadPoolExecutor;

    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 batchSaveOrders(List<OcBOrderExtend> ocBOrderList, Map<String, CpShop> cpShopMap,
                                          Map<String, ProductSku> productSkuMap, Integer isGift, User loginUser) {
        if (log.isDebugEnabled()) {
            log.debug(" 订单批量导入 batchSaveOrders ,ocBOrderList:{}", JSONObject.toJSONString(ocBOrderList));
        }

        ValueHolderV14 holderV14 = new ValueHolderV14();
        int count = 0;
        List<OcBOrderExtend> allOrderList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(ocBOrderList)) {
            allOrderList = ocBOrderList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(
                    () -> new TreeSet<>(Comparator.comparing(o -> o.getSourceCode()))), ArrayList::new));
        }
        // 将导入做成参数配置（默认250） 一头牛优化
        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
        int maxImportSize = config.getProperty("r3.oc.oms.import.order.max.qty", 5000);
        int pageSize = maxImportSize / 20;

        if (log.isDebugEnabled()) {
            log.debug(" batchSaveOrders maxImportSize:{},pageSize:{}", maxImportSize, pageSize);
        }

        // 配置批次数量：最大只支持 20个线程
        List<List<OcBOrderExtend>> baseModelPageList = Lists.partition(allOrderList, pageSize);

        List<OcBOrderExtend> resultOrderList = new ArrayList<>();
        try {
            long start = System.currentTimeMillis();
            List<Future<ValueHolderV14<List<OcBOrderExtend>>>> results = new ArrayList<>();
            for (List<OcBOrderExtend> ocBOrderExtends : baseModelPageList) {
                results.add(orderImportThreadPoolExecutor.submit(new CallableBuildOcBOrder(ocBOrderExtends, cpShopMap,
                        productSkuMap, loginUser, isGift)));
            }
            //线程执行结果获取
            for (Future<ValueHolderV14<List<OcBOrderExtend>>> futureResult : results) {
                try {
                    ValueHolderV14 valueHolderV14 = futureResult.get();
                    if (valueHolderV14.isOK()) {
                        resultOrderList.addAll((List<OcBOrderExtend>) valueHolderV14.getData());
                    } else {
                        resultOrderList.addAll((List<OcBOrderExtend>) valueHolderV14.getData());
                        count++;
                    }
                } catch (InterruptedException e) {
                    log.error(LogUtil.format("订单导入多线程获取InterruptedException异常：{}"),
                            Throwables.getStackTraceAsString(e));
                    Thread.currentThread().interrupt();
                } catch (ExecutionException e) {
                    log.error(LogUtil.format("订单导入多线程获取ExecutionException异常：{}"), Throwables.getStackTraceAsString(e));
                }
            }

            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("订单导入完成耗时:{}"), (System.currentTimeMillis() - start));
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("订单导入异常，异常信息,error：{}"), Throwables.getStackTraceAsString(ex));
        }
        if (count > 0) {
            holderV14.setCode(ImportUtil.IMPORT_ERROR_CODE);
            holderV14.setMessage("订单管理导入失败,请看错误信息文档!");
            holderV14.setData(resultOrderList);
            return holderV14;
        }

        //分批调用批量插入
        List<List<OcBOrderExtend>> pageList = Lists.partition(resultOrderList, 500);
        for (List<OcBOrderExtend> ocBOrderExtends : pageList) {
            if (log.isDebugEnabled()) {
                log.debug(" start importOrderList batchSaveBill list size :{}", pageList.size());
            }
            batchSaveBillService.batchSaveBill(ocBOrderExtends, isGift, loginUser);
        }
        return new ValueHolderV14(ResultCode.SUCCESS, "导入成功！");
    }

    private ValueHolderV14<List<OcBOrderExtend>> buildOrderAndItems(List<OcBOrderExtend> ocBOrderExtends, Map<String, CpShop> cpShopMap,
                                                                    Map<String, ProductSku> productSkuMap, User user, Integer isGift) {
        ValueHolderV14<List<OcBOrderExtend>> vh14 = new ValueHolderV14<>();
        boolean checkFlag = this.buildOcBOrder(ocBOrderExtends, cpShopMap, productSkuMap, user, isGift);
        if (checkFlag) {
            vh14.setData(ocBOrderExtends);
            vh14.setCode(ResultCode.SUCCESS);
            vh14.setMessage("成功");
        } else {
            vh14.setData(ocBOrderExtends);
            vh14.setCode(ResultCode.FAIL);
            vh14.setMessage("失败");
        }
        return vh14;
    }

    private boolean buildOcBOrder(List<OcBOrderExtend> ocBOrderExtends, Map<String, CpShop> cpShopMap,
                                  Map<String, ProductSku> proSkuMap, User user, Integer isGift) {

        boolean checkFlag = true;
        for (OcBOrderExtend ocBOrder : ocBOrderExtends) {
            ocBOrder.setId(ModelUtil.getSequence(ORDER_TABLE_NAME));
            ocBOrder.setBillNo(sequenceUtil.buildBillNo());
            ocBOrder.setIsInterecept(0);
            ocBOrder.setIsInreturning(0);
            ocBOrder.setQtySplit(0L);
            ocBOrder.setIsSplit(0);
            ocBOrder.setIsMerge(0);
            ocBOrder.setIsCancelMerge(0);
            ocBOrder.setOrderSource("手工新增");
            // 待分配
            ocBOrder.setOrderStatus(50);
            ocBOrder.setOrderType(OrderTypeEnum.NORMAL.getVal());
            ocBOrder.setInvoiceStatus(0);
            ocBOrder.setTid(ocBOrder.getSourceCode());
            ocBOrder.setOccupyStatus(OrderOccupyStatus.STATUS_0);
            ocBOrder.setIsSameCityPurchase(0);
            ocBOrder.setOrderDate(Optional.ofNullable(ocBOrder.getOrderDate()).orElse(new Date()));
            if (ocBOrder.getServiceAmt() == null) {
                ocBOrder.setServiceAmt(BigDecimal.ZERO);
            }
            ocBOrder.setOrderFlag("0");
            ocBOrder.setOutStatus(1);
            ocBOrder.setWmsCancelStatus(0);
            ocBOrder.setRefundConfirmStatus(0);
            ocBOrder.setAutoAuditStatus(0);
            // ocBOrder.setSysPresaleStatus(0);
            ocBOrder.setIsModifiedOrder(0);
            //复制订单 需要清空审核时间 、配货时间
            //配货时间
            ocBOrder.setDistributionTime(null);
            // 审核时间
            ocBOrder.setAuditTime(null);
            // 拆单状态
            ocBOrder.setSplitStatus(0);
            // 是否换货未入库
            ocBOrder.setIsExchangeNoIn(0L);
            // 设置店铺信息
            CpShop cpShop = cpShopMap.get(ocBOrder.getCpCShopTitle());
            if (cpShop == null) {
                ocBOrder.setDesc("店铺《" + ocBOrder.getCpCShopTitle() + "》信息不存在");
                checkFlag = false;
                continue;
            } else {
                ocBOrder.setCpCShopId(cpShop.getCpCShopId());
                ocBOrder.setCpCShopTitle(cpShop.getCpCShopTitle());
                ocBOrder.setCpCShopSellerNick(cpShop.getSellerNick());
                ocBOrder.setCpCShopEcode(cpShop.getEcode());
                ocBOrder.setPlatform(Optional.ofNullable(cpShop.getCpCPlatformId()).orElse(-1L).intValue());
            }
            // 省名称或者市名称为空 ,就从详细地址中解析
            if ((StringUtils.isBlank(ocBOrder.getCpCRegionProvinceEname())
                    || StringUtils.isBlank(ocBOrder.getCpCRegionCityEname()))
                    && StringUtils.isNotBlank(ocBOrder.getReceiverAddress())) {
                Map<String, String> map = new HashMap<>();
                try {
                    map = addressResolutionUtils.addressResolutionNew(ocBOrder.getReceiverAddress());
                } catch (Exception e) {
                    checkFlag = false;
                    ocBOrder.setDesc((ocBOrder.getDesc() == null ? "" : ocBOrder.getDesc()) + "行政区域解析失败,请检查");
                }
                ocBOrder.setCpCRegionProvinceEname(map.get("province"));
                ocBOrder.setCpCRegionCityEname(map.get("city"));
                ocBOrder.setCpCRegionAreaEname(map.get("area"));
            }

            if (StringUtils.isEmpty(ocBOrder.getDesc()) || !ocBOrder.getDesc().contains("行政区域解析失败,请检查")) {
                //新增根据省市区名称查询省市区的信息
                ProvinceCityAreaInfo provinceCityAreaInfo = regionNewService.selectNewProvinceCityAreaInfo(
                        ocBOrder.getCpCRegionProvinceEname(), ocBOrder.getCpCRegionCityEname(), ocBOrder.getCpCRegionAreaEname());
                RegionInfo province = provinceCityAreaInfo.getProvinceInfo();
                if (province != null) {
                    ocBOrder.setCpCRegionProvinceId(province.getId());
                    ocBOrder.setCpCRegionProvinceEcode(province.getCode());
                } else {
                    checkFlag = false;
                    ocBOrder.setDesc((ocBOrder.getDesc() == null ? "" : ocBOrder.getDesc()) + "省不存在!请检查!");
                }
                //开始赋值市的信息
                RegionInfo city = provinceCityAreaInfo.getCityInfo();
                if (city != null) {
                    ocBOrder.setCpCRegionCityId(city.getId());
                    ocBOrder.setCpCRegionCityEcode(city.getCode());
                } else {
                    checkFlag = false;
                    ocBOrder.setDesc((ocBOrder.getDesc() == null ? "" : ocBOrder.getDesc()) + "市不存在!请检查!");
                }
                //开始赋值区的信息
                RegionInfo area = provinceCityAreaInfo.getAreaInfo();
                if (area != null) {
                    ocBOrder.setCpCRegionAreaId(area.getId());
                    ocBOrder.setCpCRegionAreaEcode(area.getCode());
                }
            }

            if (ocBOrder.getReceiverAddress() != null && ocBOrder.getReceiverAddress().length() > 400) {
                checkFlag = false;
                ocBOrder.setDesc((ocBOrder.getDesc() == null ? "" : ocBOrder.getDesc()) + "详细地址的长度不能大于400");
            }
            makeCreateField(ocBOrder, user);
            ocBOrder.setOwnerename(user.getEname());
            ocBOrder.setModifierename(user.getEname());
            if (checkFlag) {
                checkFlag = buildOcBOrderItems(ocBOrder.getOrderItemList(), proSkuMap, ocBOrder, user);
            }
            OrderAddressConvertUtil.convert(ocBOrder);
            // 重新订单头金额
            recountAmount(ocBOrder);

            OcBToBeConfirmedTask toBeConfirmedTask = new OcBToBeConfirmedTask();
            toBeConfirmedTask.setId(sequenceUtil.buildToBeConfirmedTaskId());
            toBeConfirmedTask.setOrderId(ocBOrder.getId());
            toBeConfirmedTask.setCreationdate(new Date());
            toBeConfirmedTask.setStatus(0);
            toBeConfirmedTask.setIsactive("Y");
            ocBOrder.setOcBToBeConfirmedTask(toBeConfirmedTask);
            OcBOrderLog ocBOrderLog = new OcBOrderLog();
            if (isGift == null || 1 != isGift) {
                ocBOrderLog = omsOrderLogService.getOcBOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.ORDER_IMPORT.getKey(),
                        "订单导入成功", "", "", user);
            } else {
                ocBOrderLog = omsOrderLogService.getOcBOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.ORDER_IMPORT.getKey(),
                        "手工新增赠品订单成功", "", "", user);
            }
            ocBOrder.setOcBOrderLog(ocBOrderLog);

            OcBOrderPayment payment = createPayMent(ocBOrder);
            ocBOrder.setPayment(payment);
        }
        return checkFlag;
    }

    private OcBOrderPayment createPayMent(OcBOrderExtend ocBOrderExtend) {
        OcBOrderPayment payment = new OcBOrderPayment();
        payment.setId(ModelUtil.getSequence("OC_B_ORDER_PAYMENT"));
        payment.setOcBOrderId(ocBOrderExtend.getId());
        payment.setPayType(ocBOrderExtend.getPayType());
        payment.setAmtOrder(ocBOrderExtend.getOrderAmt());
        payment.setPaymentAmt(ocBOrderExtend.getOrderAmt());
        /*支付时间逻辑优化，如果订单上有支付时间则以订单的支付时间为准*/
        payment.setPayTime(ocBOrderExtend.getPayTime() == null ? new Date(System.currentTimeMillis()) : ocBOrderExtend.getPayTime());
        payment.setOwnerename(SystemUserResource.getRootUser().getEname());
        payment.setPayStatus(OmsPayStatus.PAID.toInteger());
        payment.setAdClientId((long) SystemUserResource.getRootUser().getClientId());
        payment.setAdOrgId((long) SystemUserResource.getRootUser().getOrgId());
        payment.setIsactive("Y");
        payment.setCreationdate(new Date(System.currentTimeMillis()));
        return payment;

    }

    private boolean buildOcBOrderItems(List<OcBOrderItemExtend> orderItems, Map<String, ProductSku> proSkuMap,
                                       OcBOrderExtend ocBOrder, User user) {
        if (log.isDebugEnabled()) {
            log.debug(" 订单批量导入 batchSaveOrders buildOcBOrderItems ,orderItems:{}", JSONObject.toJSONString(orderItems));
        }
        boolean checkFlag = true;
        for (OcBOrderItem item : orderItems) {
            if (StringUtils.isEmpty(item.getPsCSkuEcode())) {
                ocBOrder.setDesc("订单明细商品SKU条码为空");
                checkFlag = false;
                break;
            }
            if (item.getQty() == null || item.getQty().compareTo(BigDecimal.ZERO) == 0) {
                ocBOrder.setDesc("订单明细的数量不能为0或null");
                checkFlag = false;
                break;
            }
            makeCreateField(item, user);
            item.setModifierename(user.getEname());
            item.setOwnerename(user.getEname());
            item.setId(ModelUtil.getSequence(ORDER_ITEM_TABLE_NAME));
            item.setOcBOrderId(ocBOrder.getId());
            item.setPsCSkuEcode(item.getPsCSkuEcode().toUpperCase());
            if (proSkuMap == null || proSkuMap.isEmpty()) {
                ocBOrder.setDesc("Sku《" + item.getPsCSkuEcode() + "》在商品中心不存在！");
                checkFlag = false;
                break;
            }
            ProductSku productSku = proSkuMap.get(item.getPsCSkuEcode());
            if (productSku == null) {
                ocBOrder.setDesc("Sku《" + item.getPsCSkuEcode() + "》在商品中心不存在！");
                checkFlag = false;
                break;
            }
            // 供应类型 0 普通 1.代销轻供 2.寄售轻供
            item.setPsCProSupplyType(productSku.getPsCProSupplyType());
            item.setIsManualAdd("1");
            item.setQtyRefund(BigDecimal.ZERO);
            item.setTid(ocBOrder.getTid());
            item.setProType((long) productSku.getSkuType());
            item.setIsGift(Optional.ofNullable(item.getIsGift()).orElse(0));
            item.setNumIid(Optional.ofNullable(item.getNumIid()).orElse("0"));
            // 一米有品
            item.setReserveVarchar04(item.getReserveVarchar04());
            item.setPsCSkuId(productSku.getId());
            item.setPsCSkuPtEcode(productSku.getSkuEcode());
            item.setPsCSkuEname(productSku.getSkuName());
            item.setSkuSpec(productSku.getSkuSpec());
            item.setBarcode(productSku.getBarcode69());
            item.setPsCProId(productSku.getProdId());
            item.setPsCProEcode(productSku.getProdCode());
            item.setPsCProEname(productSku.getName());
            item.setPsCBrandId(productSku.getPsCBrandId());
            item.setSex(productSku.getSex());
            item.setPsCClrId(productSku.getColorId());
            item.setPsCClrEcode(productSku.getColorCode());
            item.setPsCClrEname(productSku.getColorName());
            item.setPsCSizeId(productSku.getSizeId());
            item.setPsCSizeEcode(productSku.getSizeCode());
            item.setPsCSizeEname(productSku.getSizeName());
            item.setPsCProMaterieltype(productSku.getMaterialType());
            item.setStandardWeight(Optional.ofNullable(productSku.getWeight()).orElse(BigDecimal.ZERO));
            item.setRefundStatus(OcOrderRefundStatusEnum.NOTREFUND.getVal());

            // 一头牛优化内容：补充字段赋值 0917 产线问题修复
            item.setMDim4Id(productSku.getMDim4Id());
            item.setMDim6Id(productSku.getMDim6Id());
            if ("Y".equals(productSku.getIsEnableExpiry())) {
                item.setIsEnableExpiry(1);
            } else {
                item.setIsEnableExpiry(0);
            }
            // 吊牌价
            item.setPriceTag(Optional.ofNullable(productSku.getPricelist()).orElse(BigDecimal.ZERO));
            // 吊牌价
            item.setPriceList(Optional.ofNullable(productSku.getPricelist()).orElse(BigDecimal.ZERO));
            // 金额相关字段的值依靠前端传入，若前端未传默认给0 成交金额，成交单价，平台售价之前已经处理了
            // 平摊金额
            item.setOrderSplitAmt(Optional.ofNullable(item.getOrderSplitAmt()).orElse(BigDecimal.ZERO));
            // 商品优惠金额
            item.setAmtDiscount(Optional.ofNullable(item.getAmtDiscount()).orElse(BigDecimal.ZERO));
            // 调整金额
            BigDecimal adjustAmt = item.getRealAmt()
                    .subtract(item.getPrice().multiply(item.getQty()))
                    .add(Optional.ofNullable(item.getAmtDiscount()).orElse(BigDecimal.ZERO))
                    .add(Optional.ofNullable(item.getOrderSplitAmt()).orElse(BigDecimal.ZERO));
            // 调整金额
            item.setAdjustAmt(adjustAmt);
            if (productSku.getSkuType() == SkuType.NORMAL_PRODUCT) {
                item.setProType(Long.valueOf(productSku.getSkuType()));
            } else {
                item.setProType(Long.valueOf(SkuType.NO_SPLIT_COMBINE));
            }

            if (StringUtils.isEmpty(item.getOoid())) {
                item.setOoid(item.getTid() + productSku.getSkuEcode() + item.getIsGift());
            }


            if (log.isDebugEnabled()) {
                log.debug(" 订单批量导入 batchSaveOrders buildOcBOrderItems ,item:{}", JSONObject.toJSONString(item));
            }
        }
        return checkFlag;
    }

    private void makeCreateField(BaseModel model, User user) {
        Date date = new Date();
        // 所属公司
        model.setAdClientId((long) user.getClientId());
        // 所属组织
        model.setAdOrgId((long) user.getOrgId());
        // 创建人id
        model.setOwnerid(Long.valueOf(user.getId()));
        // 创建时间
        model.setCreationdate(date);
        // 创建人用户名
        model.setOwnername(user.getName());
        // 修改人id
        model.setModifierid(Long.valueOf(user.getId()));
        // 修改人用户名
        model.setModifiername(user.getName());
        // 修改时间
        model.setModifieddate(date);
        // 是否启用
        model.setIsactive("Y");
    }

    private void recountAmount(OcBOrderExtend ocBOrder) {
        ocBOrder.setIsInterecept(0);
        List<OcBOrderItemExtend> orderItems = ocBOrder.getOrderItemList();
        BigDecimal productAmt = BigDecimal.ZERO;
        BigDecimal orderAmt;
        BigDecimal productDiscountAmt = BigDecimal.ZERO;
        BigDecimal orderDiscountAmt = BigDecimal.ZERO;
        BigDecimal qtyAll = BigDecimal.ZERO;
        BigDecimal adjustAmt = BigDecimal.ZERO;
        BigDecimal weight = BigDecimal.ZERO;
        Boolean flag = false;
        for (OcBOrderItemExtend item : orderItems) {
            if (item.getIsGift() == 1) {
                flag = true;
            }
            Long proType = Optional.ofNullable(item.getProType()).orElse(0L);
            if (proType.intValue() == SkuType.GIFT_PRODUCT || proType.intValue() == SkuType.COMBINE_PRODUCT) {
                continue;
            }
            // 打组合标
            if (proType.intValue() == SkuType.NO_SPLIT_COMBINE) {
                ocBOrder.setIsCombination(1);
            }
            productAmt = productAmt.add(Optional.ofNullable(item.getPrice()).orElse(BigDecimal.ZERO)
                    .multiply(Optional.ofNullable(item.getQty()).orElse(BigDecimal.ZERO)));
            productDiscountAmt =
                    productDiscountAmt.add(Optional.ofNullable(item.getAmtDiscount()).orElse(BigDecimal.ZERO));
            orderDiscountAmt =
                    orderDiscountAmt.add(Optional.ofNullable(item.getOrderSplitAmt()).orElse(BigDecimal.ZERO));
            adjustAmt = adjustAmt.add(Optional.ofNullable(item.getAdjustAmt()).orElse(BigDecimal.ZERO));
            qtyAll = qtyAll.add(Optional.ofNullable(item.getQty()).orElse(BigDecimal.ZERO));
            weight = weight.add(Optional.ofNullable(item.getStandardWeight()).orElse(BigDecimal.ZERO));
        }
        if (flag) {
            ocBOrder.setIsHasgift(1);
        }
        ocBOrder.setProductAmt(productAmt);
        ocBOrder.setProductDiscountAmt(productDiscountAmt);
        ocBOrder.setOrderDiscountAmt(orderDiscountAmt);
        ocBOrder.setAdjustAmt(adjustAmt);
        ocBOrder.setQtyAll(qtyAll);
        orderAmt = productAmt.subtract(productDiscountAmt)
                .subtract(orderDiscountAmt)
                .add(adjustAmt).add(ocBOrder.getShipAmt());
        ocBOrder.setOrderAmt(orderAmt);
        ocBOrder.setReceivedAmt(orderAmt);
        ocBOrder.setAmtReceive(orderAmt);
        ocBOrder.setServiceAmt(Optional.ofNullable(ocBOrder.getServiceAmt()).orElse(BigDecimal.ZERO));
        ocBOrder.setConsignAmt(Optional.ofNullable(ocBOrder.getConsignAmt()).orElse(BigDecimal.ZERO));
        ocBOrder.setConsignShipAmt(Optional.ofNullable(ocBOrder.getConsignShipAmt()).orElse(BigDecimal.ZERO));
        ocBOrder.setCodAmt(Optional.ofNullable(ocBOrder.getCodAmt()).orElse(BigDecimal.ZERO));
        // ocBOrder.setOperateAmt(Optional.ofNullable(ocBOrder.getOperateAmt()).orElse(BigDecimal.ZERO));
        ocBOrder.setJdReceiveAmt(Optional.ofNullable(ocBOrder.getJdReceiveAmt()).orElse(BigDecimal.ZERO));
        ocBOrder.setJdSettleAmt(Optional.ofNullable(ocBOrder.getJdSettleAmt()).orElse(BigDecimal.ZERO));
        ocBOrder.setLogisticsCost(Optional.ofNullable(ocBOrder.getLogisticsCost()).orElse(BigDecimal.ZERO));
        ocBOrder.setWeight(weight);
    }

    /**
     * 开启线程类
     */
    class CallableBuildOcBOrder implements Callable<ValueHolderV14<List<OcBOrderExtend>>> {

        List<OcBOrderExtend> ocBOrderExtends;

        Map<String, CpShop> cpShopMap;

        Map<String, ProductSku> proSkuMap;

        User user;

        Integer isGift;

        public CallableBuildOcBOrder(List<OcBOrderExtend> ocBOrderExtends, Map<String, CpShop> cpShopMap,
                                     Map<String, ProductSku> proSkuMap, User user, Integer isGift) {
            this.ocBOrderExtends = ocBOrderExtends;
            this.cpShopMap = cpShopMap;
            this.proSkuMap = proSkuMap;
            this.user = user;
            this.isGift = isGift;
        }

        @Override
        public ValueHolderV14<List<OcBOrderExtend>> call() {
            long start = System.currentTimeMillis();
            ValueHolderV14<List<OcBOrderExtend>> vh14 = buildOrderAndItems(ocBOrderExtends, cpShopMap, proSkuMap, user, isGift);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("构建订单耗时:{}ms,一共:{}条,线程=", Thread.currentThread().getName()),
                        System.currentTimeMillis() - start, ocBOrderExtends.size());
            }
            return vh14;
        }

    }

    public ValueHolderV14<List<OcBOrderRemarkImpVO>> updateOrderAddress(List<OcBOrderRemarkImpVO> orderRemarkList, List<String> sourCodeList, List<Long> idList, User user) {
        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, "success");
        List<OcBOrderRemarkImpVO> resultList = new ArrayList<>();
        List<OcBOrder> ocBOrders = ocBOrderMapper.selectByIdsList(idList);
        Map<String, List<OcBOrder>> ocBOrderMap = ocBOrders.stream()
                .collect(Collectors.groupingBy(OcBOrder::getSourceCode));
//                .collect(Collectors.toMap(OcBOrder::getSourceCode, v -> v,
//                (v1, v2) -> v1));
        //记录失败原因
        List<String> errorList = new ArrayList<>();
        //纪录失败数
        int failCount = 0;
        for (OcBOrderRemarkImpVO ocBOrder : orderRemarkList) {
            OcBOrderRemarkImpVO vo = new OcBOrderRemarkImpVO();
            if(StringUtils.isNotBlank(ocBOrder.getDesc())){
                errorList.add(ocBOrder.getDesc());
                vo.setDesc(ocBOrder.getDesc());
                vo.setRowNum(ocBOrder.getRowNum());
                resultList.add(vo);
                failCount++;
                continue;
            }


            //OcBOrder bOrder = ocBOrderMap.get(ocBOrder.getSourceCode());
            List<OcBOrder> ocBOrderList = ocBOrderMap.get(ocBOrder.getSourceCode());
            if(CollectionUtils.isEmpty(ocBOrderList)){
                String error = "平台单号：" + ocBOrder.getSourceCode() + "，数据不存在，请检查！";
                errorList.add(error);
                vo.setDesc(error);
                vo.setRowNum(ocBOrder.getRowNum());
                resultList.add(vo);
                failCount++;
                continue;
            }
            StringBuffer errorBuffer = new StringBuffer();
            for(OcBOrder bOrder : ocBOrderList){
                if (bOrder == null) {
                    errorBuffer.append("平台单号：" + ocBOrder.getSourceCode() + "，数据不存在，请检查！！");
                    continue;
                }
                /**
                 * 判断订单状态是否允许修改地址
                 */
                if (!OmsOrderStatus.UNCONFIRMED.toInteger().equals(bOrder.getOrderStatus())
                        && !OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(bOrder.getOrderStatus())) {
                    errorBuffer.append("单据编号：" + bOrder.getBillNo() + ",订单状态非待审核、带寻源，不允许修改地址！");
                    continue;
                }
                /**
                 * 合单不允许修改地址
                 */
                if(YesNoEnum.Y.getVal().equals(bOrder.getIsMerge())){
                    errorBuffer.append("单据编号：" + bOrder.getBillNo() + ",订单为合并订单，不允许修改地址！");
                }

                ReceiverAddressDto addressDto = new ReceiverAddressDto();
                addressDto.setId(bOrder.getId());
                ValueHolderV14 holderV14 = setOrderRegoin(ocBOrder.getSourceCode(), ocBOrder.getCpCRegionProvinceEname(), ocBOrder.getCpCRegionCityEname(), ocBOrder.getCpCRegionAreaEname(), addressDto);
                if (!holderV14.isOK()) {
                    errorBuffer.append(holderV14.getMessage());
                    continue;
                }
                addressDto.setReceiverAddress(ocBOrder.getReceiverAddress());
                addressDto.setReceiverName(ocBOrder.getReceiverName());
                addressDto.setReceiverMobile(ocBOrder.getReceiverMobile());
                addressDto.setReceiverPhone(ocBOrder.getReceiverPhone());
                addressDto.setReceiverZip(ocBOrder.getReceiverZip());
                addressDto.setIsPlainAddr(ocBOrder.getIsPlainAddr());
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("OcBOrderImportService.updateOrderRemark.addressDto:{}"),
                            JSON.toJSONString(addressDto));
                }
                ValueHolderV14 vhRaw = service.updateReceiveAddressNew(addressDto, user, true, true);
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("OcBOrderImportService.updateReceiveAddressNew.vhRaw:{}"),
                            JSON.toJSONString(vhRaw));
                }
                if (!vhRaw.isOK()) {
                    errorBuffer.append(vhRaw.getMessage());
                }
            }
            if(errorBuffer.length()>0){
                errorList.add(errorBuffer.toString());
                vo.setDesc(errorBuffer.toString());
                vo.setRowNum(ocBOrder.getRowNum());
                resultList.add(vo);
                failCount++;
            }

        }
        if (CollectionUtils.isNotEmpty(errorList)) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("导入成功数:" + (orderRemarkList.size() - failCount) + ", 失败:" + failCount);
            v14.setData(resultList);
        } else {
            v14.setMessage("全部导入成功");
        }
        return v14;
    }


    public ValueHolderV14 setOrderRegoin(String sourceCode, String provName, String cityName, String areaName, ReceiverAddressDto order) {
        ValueHolderV14 holderV14 = new ValueHolderV14(ResultCode.SUCCESS, "success");
        try {
            ProvinceCityAreaInfo provinceCityAreaInfo = regionService.selectProvinceCityAreaInfo(provName,
                    cityName, areaName);
            if (Objects.isNull(provinceCityAreaInfo) || Objects.isNull(provinceCityAreaInfo.getProvinceInfo())) {
                throw new NDSException("平台单号:" + sourceCode + ", 匹配省市区失败,不存在省：" + provName);
            }
            if (Objects.isNull(provinceCityAreaInfo.getCityInfo())) {
                throw new NDSException("平台单号:" + sourceCode + ", 匹配市区失败，省" + provName + ", 不存在城市" + cityName);
            }

            order.setCpCRegionProvinceId(provinceCityAreaInfo.getProvinceInfo().getId());
            order.setCpCRegionProvinceEcode(provinceCityAreaInfo.getProvinceInfo().getCode());
            order.setCpCRegionProvinceEname(provinceCityAreaInfo.getProvinceInfo().getName());

            order.setCpCRegionCityId(provinceCityAreaInfo.getCityInfo().getId());
            order.setCpCRegionCityEcode(provinceCityAreaInfo.getCityInfo().getCode());
            order.setCpCRegionCityEname(provinceCityAreaInfo.getCityInfo().getName());

            // 区可不匹配
            if (Objects.nonNull(provinceCityAreaInfo.getAreaInfo())) {
                order.setCpCRegionAreaId(provinceCityAreaInfo.getAreaInfo().getId());
                order.setCpCRegionAreaEcode(provinceCityAreaInfo.getAreaInfo().getCode());
                order.setCpCRegionAreaEname(provinceCityAreaInfo.getAreaInfo().getName());
            } else {
                order.setCpCRegionAreaId(0L);
                order.setCpCRegionAreaEcode("");
                order.setCpCRegionAreaEname("");
            }
            return holderV14;
        } catch (Exception ex) {
            log.error(LogUtil.format("OcBOrderImportService.setOrderRegoin异常：{}"), Throwables.getStackTraceAsString(ex));
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage(ex.getMessage());
            return holderV14;
        }


    }
}
