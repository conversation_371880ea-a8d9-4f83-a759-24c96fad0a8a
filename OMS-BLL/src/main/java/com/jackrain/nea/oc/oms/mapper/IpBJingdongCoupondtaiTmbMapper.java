package com.jackrain.nea.oc.oms.mapper;

import com.alibaba.fastjson.JSONObject;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2019/9/18 3:52 下午
 * @Version 1.0
 */
@Mapper
public interface IpBJingdongCoupondtaiTmbMapper {

    @Select("SELECT * FROM ip_b_jingdong_coupondtai_tmp WHERE order_id = #{orderId}")
    List<JSONObject> selectIpBJingdongCoupondtaiTmbList(@Param("orderId") Long orderId);

    @Select("SELECT * FROM ip_b_jingdong_coupondtai_tmp")
    List<JSONObject> selectIpBJingdongCoupondtai();


    @Update("update ip_b_jingdong_coupondtai_tmp set reserve_bigint01 = #{id} where order_id = #{orderId} and sku_id = #{skuId}")
    int updateIpBJingdongCoupondtaiTmb(@Param("id") Long id, @Param("orderId") Long orderId, @Param("skuId") Long skuId);
}
