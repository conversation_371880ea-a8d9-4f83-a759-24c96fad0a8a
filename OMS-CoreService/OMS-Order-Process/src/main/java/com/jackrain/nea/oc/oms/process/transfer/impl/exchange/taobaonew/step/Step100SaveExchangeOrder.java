package com.jackrain.nea.oc.oms.process.transfer.impl.exchange.taobaonew.step;

import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OcBReturnOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderExchangeRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsTaobaoExchangeRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoExchange;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2020/12/1 5:06 下午
 * @Version 1.0
 */
@Step(order = 100, description = "生成退换货单以及换货订单")
@Slf4j
@Component
public class Step100SaveExchangeOrder extends BaseTaobaoExchangeProcessStep
        implements IOmsOrderProcessStep<OmsTaobaoExchangeRelation> {

    @Override
    public ProcessStepResult<OmsTaobaoExchangeRelation> startProcess(OmsTaobaoExchangeRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        IpBTaobaoExchange ipBTaobaoExchange = orderInfo.getIpBTaobaoExchange();
        try {
            //获取原单明细的已申请数量
            BigDecimal returnQty = BigDecimal.ZERO;
            BigDecimal buyQty = BigDecimal.ZERO;
            BigDecimal exchangeQty = new BigDecimal(ipBTaobaoExchange.getQty());
            List<OmsOrderExchangeRelation> originalSingleOrder = orderInfo.getOriginalSingleOrder();
            for (OmsOrderExchangeRelation orderExchangeRelation : originalSingleOrder) {
                List<OcBOrderItem> ocBOrderItems = orderExchangeRelation.getOcBOrderItems();
                ocBOrderItems = ocBOrderItems.stream().filter(p -> p.getProType() == SkuType.NO_SPLIT_COMBINE || p.getProType() == SkuType.NORMAL_PRODUCT).collect(Collectors.toList());
                BigDecimal qtySum = ocBOrderItems.stream().filter(p -> p.getQtyReturnApply() != null).map(OcBOrderItem::getQtyReturnApply).
                        reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal qty = ocBOrderItems.stream().map(OcBOrderItem::getQty).
                        reduce(BigDecimal.ZERO, BigDecimal::add);
                buyQty = buyQty.add(qty);
                returnQty = returnQty.add(qtySum);
            }
            returnQty = buyQty.subtract(returnQty);
            if (exchangeQty.compareTo(returnQty) > 0) {
                String message = "申请换货数量大于可退数量,转换结束";
                ipTaobaoExchangeService.updateExchangeRemarkAndIsTrans(TransferOrderStatus.TRANSFERFAIL.toInteger(), message,
                        ipBTaobaoExchange);
                return new ProcessStepResult<>(StepStatus.FAILED, message);
            }
            OcBReturnOrderRelation relation = exchangeOrderTransferUtil.buildOcBReturnOrder(orderInfo, operateUser);
            if (relation.getReturnOrderInfo().getReceiverProvinceId() == null) {
                String message = "换货省市区匹配异常!转换失败";
                ipTaobaoExchangeService.updateExchangeRemarkAndIsTrans(TransferOrderStatus.TRANSFERFAIL.toInteger(), message,
                        ipBTaobaoExchange);
                return new ProcessStepResult<>(StepStatus.FAILED, message);
            }
            exchangeOrderTransferUtil.buildExchangeOrder(relation, orderInfo, operateUser);
            //保存信息
            List<OcBReturnOrderRelation> orderRelations = new ArrayList<>();
            orderRelations.add(relation);
            List<Long> longs = omsReturnOrderService.insertOmsReturnOrderInfo(orderRelations, operateUser);
            if (CollectionUtils.isNotEmpty(longs)) {
                String message = "生成退换货单及换货订单成功!";
                OcBOrder ocBOrder = relation.getOcBOrder();
                String skuEcode = orderInfo.getProductSku().getSkuEcode();
                ipBTaobaoExchange.setTid(NumberUtils.toLong(ocBOrder.getTid()));
                ipBTaobaoExchange.setOuterSkuId(skuEcode);
                ipTaobaoExchangeService.updateExchangeRemarkAndIsTrans(TransferOrderStatus.TRANSFERRED.toInteger(), message,
                        ipBTaobaoExchange);
                return new ProcessStepResult<>(StepStatus.SUCCESS, message);
            } else {
                String message = "生成退换货单及换货订单失败";
                ipTaobaoExchangeService.updateExchangeRemarkAndIsTrans(TransferOrderStatus.NOT_TRANSFER.toInteger(), message,
                        ipBTaobaoExchange);
                return new ProcessStepResult<>(StepStatus.SUCCESS, message);
            }
        } catch (Exception e) {
            ipTaobaoExchangeService.updateExchangeIsTransError(ipBTaobaoExchange, "生成退换货单以及换货订单失败" + e.getMessage());
            log.error(LogUtil.format("生成退换货单以及换货订单失败:{}", "生成退换货单以及换货订单失败"), Throwables.getStackTraceAsString(e));
            String errorMessage = "生成退换货单以及换货订单失败!" + e.getMessage();
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
    }
}
