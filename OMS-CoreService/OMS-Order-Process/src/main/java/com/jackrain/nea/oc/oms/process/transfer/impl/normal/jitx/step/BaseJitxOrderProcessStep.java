package com.jackrain.nea.oc.oms.process.transfer.impl.normal.jitx.step;

import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.MakeupOrderType;
import com.jackrain.nea.oc.oms.services.*;
import com.jackrain.nea.oc.oms.util.LockOrderType;
import com.jackrain.nea.rpc.PsRpcService;
import org.springframework.beans.factory.annotation.Autowired;

//import com.jackrain.nea.oc.oms.services.OmsOrderMakeupService;

/**
 * 基础唯品会JitxOrder处理阶段
 *
 * @author: 黄超
 * @since: 2019-06-25
 * create at : 2019-06-25 19:00
 */
public abstract class BaseJitxOrderProcessStep {

    @Autowired
    protected IpJitxOrderService ipJitxOrderService;

    @Autowired
    protected OmsOrderService orderService;

    @Autowired
    protected PsRpcService psRpcService;

    @Autowired
    protected OcBOrderHoldService ocBOrderHoldService;

    @Autowired
    protected OrderInterceptionService orderInterceptionService;

    @Autowired
    protected IpVipTimeOrderService ipVipTimeOrderService;

    @Autowired
    protected IpBJitxDeliveryRecordService deliveryRecordService;

    protected ChannelType getCurrentChannelType() {
        return ChannelType.VIPJITX;
    }

    protected MakeupOrderType getCurrentMakeupOrderType() {
        return MakeupOrderType.TRANSFER_ORDER;
    }

    protected LockOrderType getCurrentLockOrderType() {
        return LockOrderType.TRANSFER_JITX_ORDER;
    }


}
