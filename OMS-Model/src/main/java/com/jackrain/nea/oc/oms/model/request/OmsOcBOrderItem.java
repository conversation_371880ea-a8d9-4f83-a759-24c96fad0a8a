package com.jackrain.nea.oc.oms.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: 黄世新
 * @Date: 2019/11/4 1:51 下午
 * @Version 1.0
 */
@Data
public class OmsOcBOrderItem extends BaseModel {

    @JSONField(name = "sourceId")
    private Long sourceId; //对应POS订单的明细的id

    @JSONField(name = "PS_C_SKU_ECODE")
    private String psCSkuEcode;

    @JSONField(name = "REAL_AMT")
    private BigDecimal realAmt; //成交金额

    @JSONField(name = "QTY")
    private BigDecimal qty;

    @JSONField(name = "AMT_DISCOUNT")
    private BigDecimal amtDiscount; //优惠金额

    @JSONField(name = "PRICE_LIST")
    private BigDecimal priceList;


}
