package com.jackrain.nea.oc.oms.mapper.task;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.task.OcBSapSalesDataRecordAddTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.Update;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> wangbinyu
 * @since : 2022/9/20
 * description :
 */
@Mapper
public interface OcBSapSalesDataRecordAddTaskMapper extends ExtentionMapper<OcBSapSalesDataRecordAddTask> {

    @Update(" update oc_b_sap_sales_data_record_add_task set status = #{status},modifierid=#{userId},modifiername=#{userName},modifierename=#{userEname},modifieddate=now() where id = #{id}")
    int updateStatus(@Param("id") Long id, @Param("status") Integer status, @Param("userId") Long userId, @Param("userName") String userName, @Param("userEname") String userEname);

    @Update(" update oc_b_sap_sales_data_record_add_task set status = 0,next_time=#{next_time},retry_number=retry_number+1,modifierid=#{userId},modifiername=#{userName},modifierename=#{userEname},modifieddate=now() where id = #{id}")
    int initStatus(@Param("id") Long id, @Param("next_time") Date nextTime, @Param("userId") Long userId, @Param("userName") String userName, @Param("userEname") String userEname);

    @Select(" select * from oc_b_sap_sales_data_record_add_task where order_id = #{orderId} and bill_type=#{billType} order by id desc limit 1")
    OcBSapSalesDataRecordAddTask selectByOrderIdAndBillType(@Param("orderId") Long orderId, @Param("billType") Integer billType);

    /**
     * 分库查询待转换任务
     *
     * @param nodeName      节点名称
     * @param limit         分页数量
     * @param taskTableName 表名
     * @return ids
     */
    @SelectProvider(type = OcBSapSalesDataRecordAddTaskSql.class, method = "selectByNodeSql")
    List<Long> selectOcBSapSalesDataRecordAddTaskSqlList(@Param(value = "limit") int limit,
                                                         @Param(value = "taskTableName") String taskTableName);

    class OcBSapSalesDataRecordAddTaskSql {
        public String selectByNodeSql(Map<String, Object> para) {
            StringBuilder sql = new StringBuilder();
            StringBuilder limitStr = new StringBuilder(" LIMIT ");
            int limit = para.get("limit") != null ? (int) para.get("limit") : 100;
            limitStr.append(limit);

            String taskTableName = (String) para.get("taskTableName");
            sql.append("select id from ")
                    .append(taskTableName)
                    .append("  where status = 0 and retry_number < 6 and next_time < now()")
                    .append(limitStr);
            return sql.toString();
        }
    }
}
