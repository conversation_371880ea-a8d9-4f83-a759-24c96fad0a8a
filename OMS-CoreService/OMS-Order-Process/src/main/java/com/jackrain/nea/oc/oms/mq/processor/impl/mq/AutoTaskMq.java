package com.jackrain.nea.oc.oms.mq.processor.impl.mq;

import com.alibaba.fastjson.JSON;
import com.burgeon.mq.annotation.RocketMqMessageListener;
import com.burgeon.mq.core.BaseMessageListener;
import com.burgeon.mq.enums.MqTypeEnum;
import com.burgeon.mq.error.MqException;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.services.delivery.OrderDeliveryProcessor;
import com.jackrain.nea.utility.LogUtil;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.util.List;

/**
 * Description： 定时任务MQ，各种定时任务可以共用一个topic，以tag区分
 * Author: RESET
 * Date: Created in 2020/9/13 21:29
 * Modified By:
 */
@Slf4j
@RocketMqMessageListener(name = "AutoTaskMq", type = MqTypeEnum.DEFAULT)
public class AutoTaskMq implements BaseMessageListener {

    @Autowired
    private OrderDeliveryProcessor orderDeliveryProcessor;
    @Autowired
    private OcBOrderMapper orderMapper;

    /**
     * 平台发货
     *
     * @param messageBody
     */
    private void platformSend(String messageBody) throws IOException, ClassNotFoundException {
        List<Long> orderIds = JSON.parseArray(messageBody, Long.class);
        if (CollectionUtils.isNotEmpty(orderIds)) {
            List<OcBOrder> orders = orderMapper.selectByIdsList(orderIds);
            if (CollectionUtils.isNotEmpty(orders)) {
                orders.forEach(order -> {
                    // 状态为仓库发货的，否则不处理：多过滤一层为了规避es同步延迟问题
                    if (OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger().equals(order.getOrderStatus())) {
                        OcBOrderRelation ocBOrderRelation = new OcBOrderRelation();
                        ocBOrderRelation.setOrderInfo(order);

                        // 这里没有做并发控制
                        long start = System.currentTimeMillis();
                        if (log.isDebugEnabled()) {
                            log.debug(LogUtil.format("platformDelivery.mq.start", order.getId()));
                        }
                        ocBOrderRelation.setAutomaticOperation(true);
                        orderDeliveryProcessor.platformSend(ocBOrderRelation);
                        if (log.isDebugEnabled()) {
                            log.debug(LogUtil.format("platformDelivery.mq.ended.time:{}", order.getId()), (System.currentTimeMillis() - start));
                        }
                    } else {
                        log.info(LogUtil.format("StatusIncorrect", order.getId(), "platformSend"));
                    }
                });
            }
        }

    }

    /**
     * 消费消息接口，由应用来实现<br>
     * 网络抖动等不稳定的情形可能会带来消息重复，对重复消息敏感的业务可对消息做幂等处理
     *
     * @return 消费结果，如果应用抛出异常或者返回Null等价于返回Action.ReconsumeLater
     * @see <a href="https://help.aliyun.com/document_detail/44397.html">如何做到消费幂等</a>
     */
    @Override
    public void consume(String messageBody, String messageTopic, String messageKey, String messageTag, Object object) {
        String tag = messageTag;
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("消费消息接口, Body:{}", messageTopic, tag, messageKey, messageKey), messageBody);
        }
        try {
            if (AutoTaskTagEnum.PLATFORM_DELIVERY.getCode().equals(tag)) {
                platformSend(messageBody);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("消费消息接口.ExpMsg:{}"), Throwables.getStackTraceAsString(e));
            throw new MqException(e);
        }
    }

    /**
     * AutoTask tag enum
     */
    enum AutoTaskTagEnum {
        // 匹配策略类型
        PLATFORM_DELIVERY("tag_platform_delivery_send", "平台发货tag");

        @Getter
        private final String code;
        @Getter
        private final String description;

        AutoTaskTagEnum(String code, String description) {
            this.code = code;
            this.description = description;
        }

        @Override
        public String toString() {
            return code;
        }
    }
}
