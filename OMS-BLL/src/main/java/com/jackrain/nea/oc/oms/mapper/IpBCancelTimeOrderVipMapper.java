package com.jackrain.nea.oc.oms.mapper;


import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.IpBCancelTimeOrderVip;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.jdbc.SQL;
import org.springframework.stereotype.Component;

@Component
@Mapper
public interface IpBCancelTimeOrderVipMapper extends ExtentionMapper<IpBCancelTimeOrderVip> {
    /**
     * REMARK最长数量。默认200字符
     */
    int MAX_REMARK_LENGTH = 300;

    /**
     * 依据order_sn进行查询时效订单取消数据
     *
     * @param occupiedOrderSn 库存占用单号
     * @return 时效订单取消数据
     */
    @Select("SELECT * FROM ip_b_cancel_time_order_vip WHERE occupied_order_sn=#{occupiedOrderSn}")
    IpBCancelTimeOrderVip selectCancelTimeOrderByOccupiedOrderSn(@Param("occupiedOrderSn") String occupiedOrderSn);


    /**
     * 订单SQL创建器
     */
    class VipCancelTimeOrderSqlBuilder {
        /**
         * 创建更新订单转换状态SQL
         *
         * @param orderNo          订单编号
         * @param isTrans          转换状态
         * @param isUpdateTransNum 是否更新转换数量
         * @param remarks          转换备注信息
         * @return 更新SQL语句
         */
        public String buildUpdateCancelTimeOrderTransSQL(@Param("orderNo") String orderNo, @Param("isTrans") int isTrans,
                                                         @Param("isUpdateTransNum") boolean isUpdateTransNum,
                                                         @Param("remarks") String remarks) {

            return new SQL() {
                {
                    UPDATE("ip_b_cancel_time_order_vip");
                    SET("istrans=#{isTrans}");
                    if (isUpdateTransNum) {
                        SET("trans_count = IFNULL(trans_count, 0) + 1");
                        SET("trans_date = SYSDATE()");
                    }
                    SET("sysremark=#{remarks}");
                    WHERE("occupied_order_sn=#{orderNo}");
                }
            }.toString();
        }
    }

    /**
     * 更新订单转换状态
     *
     * @param orderNo          订单编号
     * @param isTrans          转换状态
     * @param isUpdateTransNum 是否更新转换数量
     * @param remarks          转换备注信息
     * @return 更新结果
     */
    @UpdateProvider(type = VipCancelTimeOrderSqlBuilder.class, method = "buildUpdateCancelTimeOrderTransSQL")
    int updateCancelTimeOrderIsTrans(@Param("orderNo") String orderNo, @Param("isTrans") int isTrans,
                                     @Param("isUpdateTransNum") boolean isUpdateTransNum, @Param("remarks") String remarks);
}