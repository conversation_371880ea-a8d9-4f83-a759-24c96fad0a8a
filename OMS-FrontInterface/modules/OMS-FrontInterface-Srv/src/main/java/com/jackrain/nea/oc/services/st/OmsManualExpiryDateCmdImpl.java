package com.jackrain.nea.oc.services.st;

import com.jackrain.nea.oc.oms.api.st.OmsManualExpiryDateCmd;
import com.jackrain.nea.oc.oms.dto.ExpiryDateItem;
import com.jackrain.nea.oc.oms.services.OmsManualExpiryDateService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2022/6/23 下午4:14
 * @Version 1.0
 */
@Component
public class OmsManualExpiryDateCmdImpl implements OmsManualExpiryDateCmd {

    @Autowired
    private OmsManualExpiryDateService omsManualExpiryDateService;

    @Override
    public ValueHolderV14 selectOmsOrderItem(List<Long> ids) {
        return omsManualExpiryDateService.selectOmsOrderItem(ids);
    }

    @Override
    public ValueHolderV14 executeOmsOrderItem(List<ExpiryDateItem> expiryDateItems, User user) {
        return omsManualExpiryDateService.executeOmsOrderItem(expiryDateItems, user);
    }

    @Override
    public ValueHolderV14 autoExpiryDate(List<Long> ids, User user) {
        return omsManualExpiryDateService.autoExpiryDate(ids, user);
    }
}
