package com.jackrain.nea.oc.oms.services.returnin;

import com.alibaba.fastjson.JSON;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.model.enums.AGStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.IsGenInEnum;
import com.jackrain.nea.oc.oms.model.enums.IsMatchEnum;
import com.jackrain.nea.oc.oms.model.enums.IsToWmsEnum;
import com.jackrain.nea.oc.oms.model.enums.MatchingSate;
import com.jackrain.nea.oc.oms.model.enums.OcBorderListEnums;
import com.jackrain.nea.oc.oms.model.enums.OcOrderCheckBoxEnum;
import com.jackrain.nea.oc.oms.model.enums.OrderBusinessTypeCodeEnum;
import com.jackrain.nea.oc.oms.model.enums.ProReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnInBilStatus;
import com.jackrain.nea.oc.oms.model.enums.ReturnInType;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcReturnInRelation;
import com.jackrain.nea.oc.oms.model.relation.RefundInRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderLog;
import com.jackrain.nea.oc.oms.model.table.OcBRefundIn;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInLog;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInProductItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderActual;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderLog;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.oc.oms.nums.OcOrderRefundStatusEnum;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
import com.jackrain.nea.oc.oms.refund.util.NumUtil;
import com.jackrain.nea.oc.oms.services.OcBReturnOrderBatchAddService;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.util.ThreadLocalUtil;
import com.jackrain.nea.utility.ExceptionUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.jackrain.nea.oc.oms.services.returnin.OcReturnInCommService.B2B_PLAT_TAG;
import static com.jackrain.nea.oc.oms.services.returnin.OcReturnInCommService.B2B_PLAT_TAG2;

/**
 * @Desc : match order
 * <AUTHOR> xiWen
 * @Date : 2022/8/8
 */
@Slf4j
@Component
public class OcRefundInMatchOrderService {

    @Autowired
    private OcReturnInSupport returnInService;

    @Autowired
    private OcReturnInCommService commService;

    @Autowired
    private OcReturnInAdjustService ocReturnInAdjustService;

    @Autowired
    private OcBReturnOrderBatchAddService ocBReturnOrderBatchAddService;

    /**
     * @param inRelation RefundInRelation
     * @return match result
     */
    public boolean normalMatchOrderProcessor(RefundInRelation inRelation, boolean isNormalProcess) {
        logStep("normalMatchOrderProcessor.match.order.start...");
        OcBRefundIn refundIn = inRelation.getRefundIn();
        String logisticNumber = refundIn.getLogisticNumber();
        if (StringUtils.isBlank(logisticNumber)) {
            logStep("refund in logistic number is blank");
            return false;
        }
        List<Long> orderIdList = ES4Order.queryIdsByLogisticsCode(logisticNumber);
        if (CollectionUtils.isEmpty(orderIdList)) {
            logStep("query es empty");
            return false;
        }
        for (Long id : orderIdList) {
            boolean isMatchSuccess = normalMatchOrder(inRelation, id, isNormalProcess);
            if (isMatchSuccess) {
                logStep("normalMatchOrderProcessor.match.order.success...");
                return true;
            }
        }
        logStep("normalMatchOrderProcessor.match.order.end...");
        return false;
    }

    /**
     * generate ReturnOrder
     * Return Order Id Not Exist, But Has Order Id
     *
     * @param inRelation RefundInRelation
     * @return new generate return id
     */
    private boolean normalMatchOrder(RefundInRelation inRelation, Long orderId, boolean isNormalProcess) {

        try {
            // 1. lock order
            String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(orderId);
            logStep(lockRedisKey);
            boolean lockSate = returnInService.lockBil(lockRedisKey);
            AssertUtil.isTrue(lockSate, "Order Lock fail");
            OcBOrder order = returnInService.getOrder(orderId);
            if (order == null) {
                logStep("query order null id=" + orderId);
                return false;
            }
            ReturnInType returnInType = inRelation.getReturnInType();
            if (ReturnInType.NAMELESS == returnInType) {
                String bilType = order.getGwSourceGroup();
                logStep("gwSourceGroup=%s", bilType);
                boolean isB2b = StringUtils.equalsIgnoreCase(B2B_PLAT_TAG, bilType) || StringUtils.equalsIgnoreCase(B2B_PLAT_TAG2, bilType);
                AssertUtil.assertException(isB2b, "nl.cannot.match b2b bil");
            }
            // 2. verify order
            boolean isLegalStatus = verifyOrderStatus(NumUtil.init(order.getOrderStatus()));
            if (!isLegalStatus) {
                logStep("isLegalStatus id=" + orderId);
                return false;
            }

            // 3. query item data
            List<OcBOrderItem> items = returnInService.getOrderItems(orderId);
            if (CollectionUtils.isEmpty(items)) {
                logStep("empty items id=" + orderId);
                return false;
            }

            // 4. generate returnOrder and item, then update origin order ,item
            boolean matchResult;
            if (isNormalProcess) {
                matchResult = matchProcessing(order, items, inRelation);
            } else {
                matchResult = nameLessMatchProcessing(order, items, inRelation);
            }
            if (matchResult) {
                inRelation.popMatchedItem(inRelation.getUnMatchItems());
                return true;
            }
        } catch (Exception ex) {
            logStep(OcReturnInSupport.expMsgFun.apply(ex));
        }
        return false;
    }

    /**
     * 订单生成退换货订单
     *
     * @param order       匹配目标订单
     * @param sourceItems 订单明细
     * @param inRelation  入库结果单
     * @return 生成退单编号
     */
    private boolean matchProcessing(OcBOrder order, List<OcBOrderItem> sourceItems, RefundInRelation inRelation) {
        logStep("matchProcessing");
        int step = 0;
        try {
            User user = ThreadLocalUtil.users.get();
            OcBReturnOrder preReturn = initDomainReturn();

            Map<Long, List<Long>> matchedMapping = matchHandler(inRelation, sourceItems, false);
            if (MapUtils.isEmpty(matchedMapping)) {
                return false;
            }
            List<OcBOrderItem> matchOrderItems = new ArrayList<>();
            List<OcBRefundInProductItem> matchInItems = inRelation.getUnMatchItems();
            List<OcBReturnOrderRefund> newReturnRefunds = generateReturnItems(sourceItems, matchInItems,
                    matchedMapping, true, preReturn, matchOrderItems);
            if (CollectionUtils.isEmpty(newReturnRefunds)) {
                return false;
            }
            OcBReturnOrder newReturn = ocBReturnOrderBatchAddService.genReturnOrder(order, preReturn.getId(),
                    0, preReturn.getReturnAmtList(), preReturn.getQtyInstore(), preReturn.getAllSku(), user);
            newReturn.setTid(preReturn.getTid());
            writeBackReturnOrder(inRelation.getRefundIn(), newReturn, user);
            commService.assignReturnConfirmInfo(newReturn, newReturn, user);
            logStep("order generate new return order");

            OcBOrder newOrder = initPreUpdateOrder(order, user);

            OcBRefundIn newRefundIn = returnWriteBackRefundIn(inRelation.getRefundIn(), newReturn);
            Map<String, BigDecimal> skuQtyMap = new HashMap<>();
            List<OcBRefundInProductItem> newInItems = new ArrayList<>();
            for (OcBRefundInProductItem inItem : matchInItems) {
                OcBRefundInProductItem item = commService.preUpdateRefundInItem4FistMatch(inItem, newReturn.getId());
                commService.statisticInQty(skuQtyMap, inItem.getPsCSkuEcode(), inItem.getQty());
                newInItems.add(item);
                if (inRelation.isAdjust(inItem)) {
                    inRelation.setHasAdjustedEle(true);
                }
            }

            if (OrderBusinessTypeCodeEnum.MILK_CARD_PICK_UP_GOODS_RETURN.getCode().equals(newReturn.getBusinessTypeCode()) ||
                    OrderBusinessTypeCodeEnum.CYCLE_PURCHASE_ORDER_PICK_UP_RETURN.getCode().equals(newReturn.getBusinessTypeCode())) {
                for (OcBReturnOrderRefund returnOrderRefund : newReturnRefunds) {
                    returnOrderRefund.setAmtRefundSingle(new BigDecimal("0.01"));
                    returnOrderRefund.setAmtRefund(returnOrderRefund.getAmtRefundSingle().multiply(returnOrderRefund.getQtyRefund()));
                    returnOrderRefund.setPriceSettle(new BigDecimal("0.01"));
                    returnOrderRefund.setAmtSettleTot(returnOrderRefund.getPriceSettle().multiply(returnOrderRefund.getQtyRefund()));
                }
            }

            List<OcBReturnOrderActual> actualItems = commService.generateActualItems(inRelation, newReturn, user);

            String skuMessage = commService.statisticMatchedSkuMessage(skuQtyMap);
            String returnMsg1 = String.format("退货入库结果单[%d],匹配零售发货单[%d],生成退换货单,匹配条码:%s", newRefundIn.getId(), order.getId(), skuMessage);
            OcBReturnOrderLog returnLog = commService.buildReturnOderLog("新增退货单", returnMsg1, newReturn.getId(), user);
            OcBOrderLog orderLog = commService.buildOrderLog(order.getId(), order.getBillNo(), OrderLogTypeEnum.REFUND_ORDER_ADD.getKey(), "新增退货单[入库匹配]", user);
            if ((inRelation.isHasAdjustedEle() || inRelation.isFluxWms())
                    && ReturnInType.NAMELESS == inRelation.getReturnInType()) {
                logStep("gen nameless special way start");
                inRelation.setForbidMinus(true);
                newReturn.setInTime(new Date());
                OcRefundInMatchOrderService bean = ApplicationContextHandle.getBean(OcRefundInMatchOrderService.class);
                bean.generateInNoticeResultAndAdjust(inRelation, matchOrderItems, newInItems, newReturnRefunds,
                        newReturn, newOrder, newRefundIn, actualItems, returnLog, orderLog);
                logStep("gen nameless special end");

            } else {
                if (inRelation.isHasAdjustedEle()) {
                    newReturn.setInTime(new Date());
                }
                logStep("gen notice,result and persist db start");
                commService.orderGenInNoticeAndInResult(newReturn, newReturnRefunds,
                        actualItems, newOrder, matchOrderItems, newRefundIn, newInItems, returnLog, orderLog, user);
                step = 1;
                inRelation.setCurrentMatchReturnId(preReturn.getId());
                commService.updateReturnOrderNoticeInfo(newReturn);
                logStep("update return notice no end");
            }
            return true;
        } catch (Exception e) {
            logStep(OcReturnInSupport.expMsgFun.apply(e));
            log.error(LogUtil.format("新增退单异常, {},订单id=", order.getId()), ExceptionUtil.getMessage(e));
        }
        if (step > 0) {
            logStep("step gt 0, return true");
            return true;
        }
        return false;
    }

    @Transactional
    public void generateInNoticeResultAndAdjust(RefundInRelation inRelation, List<OcBOrderItem> matchOrderItems,
                                                List<OcBRefundInProductItem> matchInItems,
                                                List<OcBReturnOrderRefund> newReturnRefunds, OcBReturnOrder newReturn,
                                                OcBOrder newOrder, OcBRefundIn newRefundIn,
                                                List<OcBReturnOrderActual> actualItems, OcBReturnOrderLog returnLog,
                                                OcBOrderLog orderLog) {

        newRefundIn.setBillStatus(ReturnInBilStatus.COMPLETE.val());
        returnInService.persistOrderMatchResult(newReturn, newReturnRefunds, actualItems,
                newOrder, matchOrderItems, newRefundIn, matchInItems, returnLog, orderLog);
        logStep("persist success");
        inRelation.popMatchedItem(inRelation.getUnMatchItems());
        ocReturnInAdjustService.generateCustomSpecialAdjust(inRelation, newReturn);
    }

    /**
     * 无名件已调整
     * 订单生成退换货订单
     *
     * @param order       匹配目标订单
     * @param sourceItems 订单明细
     * @param inRelation  入库结果单
     * @return 生成退单编号
     */
    private boolean nameLessMatchProcessing(OcBOrder order, List<OcBOrderItem> sourceItems, RefundInRelation inRelation) {
        logStep("nameLessMatchProcessing");
        int step = 0;
        try {
            User user = ThreadLocalUtil.users.get();
            OcBReturnOrder preReturn = initDomainReturn();

            Map<Long, List<Long>> matchedMapping = matchHandler(inRelation, sourceItems, true);
            if (MapUtils.isEmpty(matchedMapping)) {
                return false;
            }
            List<OcBRefundInProductItem> matchInItems = inRelation.getUnMatchItems();
            List<OcBOrderItem> matchOrderItems = new ArrayList<>();
            List<OcBReturnOrderRefund> newReturnRefunds = generateReturnItems(sourceItems, matchInItems,
                    matchedMapping, false, preReturn, matchOrderItems);
            if (CollectionUtils.isEmpty(newReturnRefunds)) {
                return false;
            }

            OcBReturnOrder newReturn = ocBReturnOrderBatchAddService.genReturnOrder(order, preReturn.getId(),
                    0, preReturn.getReturnAmtList(), preReturn.getQtyInstore(), preReturn.getAllSku(), user);
            newReturn.setTid(preReturn.getTid());
            writeBackReturnOrder4NameLess(inRelation.getRefundIn(), newReturn);
            commService.assignReturnConfirmInfo(newReturn, newReturn, user);
            logStep("generate Return end");

            OcBOrder newOrder = initPreUpdateOrder(order, user);

            logStep("write back refund by Return start");
            OcBRefundIn newRefundIn = returnWriteBackRefundIn(inRelation.getRefundIn(), newReturn);
            newRefundIn.setBillStatus(ReturnInBilStatus.COMPLETE.val());

            Map<String, BigDecimal> skuQtyMap = new HashMap<>();
            List<OcBRefundInProductItem> newInItems = new ArrayList<>();
            for (OcBRefundInProductItem inItem : matchInItems) {
                OcBRefundInProductItem item = commService.preUpdateRefundInItem4FistMatch(inItem, newReturn.getId());
                commService.statisticInQty(skuQtyMap, inItem.getPsCSkuEcode(), inItem.getQty());
                item.setIsGenInOrder(IsGenInEnum.NO.integer());
                newInItems.add(item);
            }
            OcBRefundIn refundIn = inRelation.getRefundIn();
            Long refundInId = refundIn.getId();
            logStep("generate logs");
            String skuMessage = commService.statisticMatchedSkuMessage(skuQtyMap);
            String message = String.format("匹配退货单[%d]成功, 匹配条码:%s", newReturn.getId(), skuMessage);
            OcBRefundInLog matchInLog = commService.buildRefundInLog("自动匹配退单", message, refundInId, user);

            List<OcBReturnOrderActual> actualItems = commService.generateActualItems(inRelation, newReturn, user);

            List<OcBReturnOrderLog> returnLogs = new ArrayList();
            String returnMsg1 = String.format("退货入库结果单[%d],匹配零售发货单[%d],生成退换货单,匹配条码:%s", refundInId, order.getId(), skuMessage);
            OcBReturnOrderLog returnLog1 = commService.buildReturnOderLog("新增退货单", returnMsg1, newReturn.getId(), user);
            String returnMsg2 = String.format("生成入库通知单");
            OcBReturnOrderLog returnLog2 = commService.buildReturnOderLog("生成入库通知单", returnMsg2, newReturn.getId(), user);
            returnLogs.add(returnLog1);
            returnLogs.add(returnLog2);

            OcBOrderLog orderLog = commService.buildOrderLog(order.getId(), order.getBillNo(), OrderLogTypeEnum.REFUND_ORDER_ADD.getKey(), "新增退货单[入库匹配]", user);
            logStep("generate notice pass wms and persist db start");
            commService.orderGenInNoticePassWms(newReturn, newReturnRefunds, actualItems, newOrder, matchOrderItems,
                    newRefundIn, newInItems, orderLog, matchInLog, returnLogs, user);
            logStep("generate notice pass wms and persist db end");
            step = 1;
            inRelation.setCurrentMatchReturnId(preReturn.getId());
            commService.updateReturnOrderNoticeInfo(newReturn);
            logStep("update return notice no id end");
            return true;
        } catch (Exception e) {
            logStep(OcReturnInSupport.expMsgFun.apply(e));
        }
        if (step > 0) {
            logStep("step gt 0, return true");
            return true;
        }
        return false;
    }

    /**
     * init return
     *
     * @return
     */
    private OcBReturnOrder initDomainReturn() {
        OcBReturnOrder preReturn = new OcBReturnOrder();
        preReturn.setReturnAmtList(BigDecimal.ZERO);
        preReturn.setQtyInstore(BigDecimal.ZERO);
        preReturn.setAllSku("");
        return preReturn;
    }

    /**
     * init order
     *
     * @param order
     * @param user
     * @return
     */
    private OcBOrder initPreUpdateOrder(OcBOrder order, User user) {
        OcBOrder newOrder = new OcBOrder();
        newOrder.setId(order.getId());
        newOrder.setDeliveryInStatus(OcBOrderConst.IS_STATUS_SY);
        newOrder.setReturnStatus(OcBorderListEnums.ReturnStatusEnum.RETURN_ING.getVal());
        BaseModelUtil.makeBaseModifyField(newOrder, user);
        return newOrder;
    }

    /**
     * verify orderStatus
     *
     * @param orderStatus Order_Status
     * @return isFit? true
     */
    private boolean verifyOrderStatus(int orderStatus) {
        return orderStatus == OcOrderCheckBoxEnum.CHECKBOX_PLATFORM_DELIVERY.getVal()
                || orderStatus == OcOrderCheckBoxEnum.CHECKBOX_WAREHOUSE_DELIVERY.getVal()
                || orderStatus == OcOrderCheckBoxEnum.CHECKBOX_TRANSACTION_COMPLETED.getVal();
    }


    /**
     * @param sourceItems
     * @param matchedInItems
     * @param matchedMapping
     * @param newReturn
     * @param matchItems
     * @return
     */
    private List<OcBReturnOrderRefund> generateReturnItems(List<OcBOrderItem> sourceItems,
                                                           List<OcBRefundInProductItem> matchedInItems,
                                                           Map<Long, List<Long>> matchedMapping, boolean isStock,
                                                           OcBReturnOrder newReturn, List<OcBOrderItem> matchItems) {
        logStep("generateReturnItems");
        Map<Long, OcBOrderItem> origItemMap = sourceItems.stream().collect(Collectors.toMap(OcBOrderItem::getId, x -> x));
        Map<Long, OcBRefundInProductItem> proItemMap = matchedInItems.stream().collect(Collectors.toMap(OcBRefundInProductItem::getId, x -> x));
        List<OcBReturnOrderRefund> newRefundList = new ArrayList<>();
        Set<String> tidSet = new HashSet<>();
        User user = ThreadLocalUtil.users.get();
        for (Map.Entry<Long, List<Long>> entry : matchedMapping.entrySet()) {
            OcBOrderItem oItem = origItemMap.get(entry.getKey());
            List<Long> inIds = entry.getValue();
            OcBReturnOrderRefund newRefund = buildReturnItem(oItem, proItemMap, inIds, newReturn, isStock, user);
            if (newRefund == null) {
                continue;
            }
            newRefund.setTid(oItem.getTid());
            tidSet.add(oItem.getTid());
            newRefundList.add(newRefund);
            OcBOrderItem newOrderItem = createOcBOrderItem(oItem);
            matchItems.add(newOrderItem);
        }
        newReturn.setTid(unionTid(tidSet));
        if (newReturn.getAllSku().length() > 0) {
            newReturn.setAllSku(newReturn.getAllSku().substring(1));
        }
        if (newReturn.getReturnAmtList().compareTo(BigDecimal.ZERO) < 0) {
            logStep("生成退单失败,退单总金额小于0");
            return null;
        }
        return newRefundList;
    }

    private Map<Long, List<Long>> matchHandler(RefundInRelation inRelation, List<OcBOrderItem> orderItems, boolean nl) {
        logStep("matchHandler start");
        Map<String, List<OcBOrderItem>> orderItemMap =
                orderItems.stream().collect(Collectors.groupingBy(OcBOrderItem::getPsCSkuEcode, Collectors.toList()));
        List<OcBRefundInProductItem> unMatchItems = inRelation.getUnMatchItems();
        Map<String, List<OcBRefundInProductItem>> inSkuItemMap = unMatchItems.stream().filter(x -> x.getOcBReturnOrderId() == null)
                .collect(Collectors.groupingBy(this::getRefundInSkuCode, Collectors.toList()));
        Map<Long, List<Long>> matchedMapping = new HashMap<>();
        Map<Long, Map<String, List<Long>>> newReturnItemMapping = new HashMap<>();
        int matchCount = 0;
        for (Map.Entry<String, List<OcBOrderItem>> entry : orderItemMap.entrySet()) {
            Collections.sort(entry.getValue(), Comparator.comparing(this::getCanRefundQty));
            int index = 0;
            itemIndex:
            for (OcBOrderItem orderItem : entry.getValue()) {
                if (isRefundSuccess(orderItem)) {
                    continue;
                }
                String skuCode = orderItem.getPsCSkuEcode();
                BigDecimal canRefund = getCanRefundQty(orderItem);
                if (!NumUtil.gtZero(canRefund)) {
//                    if (nl) {
//                        logStep(String.format(" %s,canRefund not gt 0", skuCode));
//                        return null;
//                    }
                    continue;
                }
                if (!inSkuItemMap.containsKey(skuCode)) {
//                    if (nl) {
//                        logStep(String.format("%s,current in stock items not contains this", skuCode));
//                        return null;
//                    }
                    continue;
                }
                List<OcBRefundInProductItem> proCodes = inSkuItemMap.get(skuCode);
                if (index == 0) {
                    Collections.sort(proCodes, Comparator.comparing(OcBRefundInProductItem::getQty));
                }
                for (; index < proCodes.size(); index++) {
                    OcBRefundInProductItem inItem = proCodes.get(index);
                    BigDecimal qtyIn = inItem.getQty();
                    if (inItem.getOcBReturnOrderId() != null) {
                        continue;
                    }
                    if (IsMatchEnum.MATCHED.getVal().equals(inItem.getIsMatch())) {
                        continue;
                    }
                    boolean isSameOrder = inItem.getOcBOrderId() == null || orderItem.getOcBOrderId().equals(inItem.getOcBOrderId());
                    if (!isSameOrder) {
                        if (nl) {
                            logStep(String.format(" %s,in item origin order id not same", skuCode));
                            return null;
                        }
                        continue;
                    }
                    if (!isFitQty(canRefund, qtyIn)) {
                        continue itemIndex;
                    }
                    matchedItemRecord(orderItem.getId(), inItem.getId(), matchedMapping);
                    matchedItmMapping(orderItem.getId(), inItem.getProductDate(), inItem.getId(), newReturnItemMapping);
                    canRefund = canRefund.subtract(qtyIn);
                    matchCount++;
                    orderItem.setQtyHasReturn(qtyIn.add(NumUtil.init(orderItem.getQtyHasReturn())));
                    orderItem.setQtyReturnApply(qtyIn.add(NumUtil.init(orderItem.getQtyReturnApply())));
                    if (!NumUtil.gtZero(canRefund)) {
                        index++;
                        continue itemIndex;
                    }
                }
                /*if (NumUtil.gtZero(canRefund)) {
                    if (nl) {
                        logStep(String.format(" %s,order item, has refund qty not use", skuCode));
                        return null;
                    }
                }*/
                break;
            }
        }
        logStep(String.format("matchHandler.匹配结果: %s", JSON.toJSONString(matchedMapping)));
        if (unMatchItems.size() > matchCount) {
            logStep("匹配结果-未全部匹配");
            return null;
        }
        return matchedMapping;
    }

    /**
     * 生成成退单. 入库单回写退单
     *
     * @param refundIn  入库单
     * @param newReturn 待更新退单.生成的退单
     */
    private void writeBackReturnOrder(OcBRefundIn refundIn, OcBReturnOrder newReturn, User user) {
        newReturn.setLogisticsCode(refundIn.getLogisticNumber());
        newReturn.setCpCLogisticsId(refundIn.getCpCLogisticsId());
        newReturn.setCpCLogisticsEcode(refundIn.getCpCLogisticsEcode());
        newReturn.setCpCLogisticsEname(refundIn.getCpCLogisticsEname());
        newReturn.setCpCPhyWarehouseInId(refundIn.getCpCPhyWarehouseId());
        newReturn.setIsNeedToWms(IsToWmsEnum.NO.val());
        newReturn.setOcBRefundInId(refundIn.getId());
        newReturn.setReturnStatus(ReturnStatusEnum.WAIT_AFTERSALE_ENSURE.getVal());
        newReturn.setProReturnStatus(ProReturnStatusEnum.WHOLE.getVal());
        newReturn.setIsTodrp(OcBOrderConst.IS_STATUS_IY);
        newReturn.setInerId(Long.valueOf(user.getId()));
        newReturn.setInerName(user.getName());
        newReturn.setInerEname(user.getEname());
        newReturn.setInTime(refundIn.getWarehouseInTime());
        if (StringUtils.isNotBlank(refundIn.getWmsBillNo())) {
            newReturn.setWmsBillNo(refundIn.getWmsBillNo());
        }
        Integer platform = newReturn.getPlatform();
        Integer integer = Integer.valueOf(38);
        Integer integer2 = Integer.valueOf(39);
        if (integer.equals(platform)) {
            newReturn.setReserveVarchar05(String.valueOf(integer));
        }
        if (integer2.equals(platform)) {
            newReturn.setReserveVarchar05(String.valueOf(integer2));
        }
        newReturn.setIsWrongReceive(refundIn.getIsWrongReceive());
        commService.preRefundInWriteReturn4StoreInfo(refundIn, newReturn);
        refundInReSetReturn(refundIn, newReturn);
    }

    private void writeBackReturnOrder4NameLess(OcBRefundIn refundIn, OcBReturnOrder newReturn) {
        newReturn.setLogisticsCode(refundIn.getLogisticNumber());
        newReturn.setCpCLogisticsId(refundIn.getCpCLogisticsId());
        newReturn.setCpCLogisticsEcode(refundIn.getCpCLogisticsEcode());
        newReturn.setCpCLogisticsEname(refundIn.getCpCLogisticsEname());
        newReturn.setCpCPhyWarehouseInId(refundIn.getCpCPhyWarehouseId());
        newReturn.setIsNeedToWms(IsToWmsEnum.YES.val());
        newReturn.setOcBRefundInId(refundIn.getId());
        newReturn.setReturnStatus(ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal());
        newReturn.setProReturnStatus(ProReturnStatusEnum.WAIT.getVal());
        newReturn.setIsTodrp(OcBOrderConst.IS_STATUS_IN);
        Integer platform = newReturn.getPlatform();
        newReturn.setInTime(refundIn.getWarehouseInTime());
        Integer integer = Integer.valueOf(38);
        Integer intege2 = Integer.valueOf(39);
        if (integer.equals(platform)) {
            newReturn.setReserveVarchar05(String.valueOf(integer));
        }
        if (intege2.equals(platform)) {
            newReturn.setReserveVarchar05(String.valueOf(intege2));
        }
        commService.preRefundInWriteReturn4StoreInfo(refundIn, newReturn);
        refundInReSetReturn(refundIn, newReturn);
    }

    /**
     * 生成退单: 入库单更新退单. o20字段
     *
     * @param refundIn  入库单
     * @param newReturn 退单
     */
    private void refundInReSetReturn(OcBRefundIn refundIn, OcBReturnOrder newReturn) {
       /* // 退货门店编码
        if (StringUtils.isNotBlank(refundIn.getStoreCode())) {
            newReturn.setStoreCode(refundIn.getStoreCode());
        }
        // 退货门店名称
        if (StringUtils.isNotBlank(refundIn.getStoreName())) {
            newReturn.setStoreName(refundIn.getStoreName());
        }
        // 结算组织编码
        if (StringUtils.isNotBlank(refundIn.getSettleOrgCode())) {
            newReturn.setSettleOrgCode(refundIn.getSettleOrgCode());
        }
        // 结算组织名称
        if (StringUtils.isNotBlank(refundIn.getSettleOrgName())) {
            newReturn.setSettleOrgName(refundIn.getSettleOrgName());
        }
        // 结算供应商编码
        if (StringUtils.isNotBlank(refundIn.getSettleSupplierCode())) {
            newReturn.setSettleSupplierCode(refundIn.getSettleSupplierCode());
        }
        // 结算供应商名称
        if (StringUtils.isNotBlank(refundIn.getSettleSupplierName())) {
            newReturn.setSettleSupplierName(refundIn.getSettleSupplierName());
        }*/
    }

    /**
     * if refundIn field is null  and return field is not null
     *
     * @param refundIn    RefundIn
     * @param returnOrder ReturnOrder
     */
    public OcBRefundIn returnWriteBackRefundIn(OcBRefundIn refundIn, OcBReturnOrder returnOrder) {

        User user = ThreadLocalUtil.users.get();
        OcBRefundIn newRefundIn = new OcBRefundIn();
        // 一阶
        newRefundIn.setId(refundIn.getId());
        newRefundIn.setMatcher(user.getEname());
        newRefundIn.setMatchedTime(new Date());
        newRefundIn.setMatchStatus(MatchingSate.MATCH_ALL.toInteger());
        commService.returnUpdateRefund(refundIn, returnOrder, newRefundIn);
        return newRefundIn;
    }

    /**
     * 可退数量计算
     *
     * @param item 订单明细
     * @return 可退数量
     */
    private BigDecimal getCanRefundQty(OcBOrderItem item) {
        BigDecimal qty = NumUtil.init(item.getQty());
        BigDecimal qtyReturnApply = NumUtil.init(item.getQtyReturnApply());
        return qty.subtract(qtyReturnApply);
    }

    /**
     * 获取匹配入库单明细条码
     *
     * @param item 入库单明细
     * @return 实际匹配条码
     */
    private String getRefundInSkuCode(OcBRefundInProductItem item) {
        return item.getRealSkuEcode() == null ? item.getPsCSkuEcode() : item.getRealSkuEcode();
    }

    /**
     * 退款成功
     *
     * @param item 订单商品明细
     * @return 退款成功? true
     */
    private boolean isRefundSuccess(OcBOrderItem item) {
        return OcOrderRefundStatusEnum.SUCCESS.getVal() == (item.getRefundStatus() == null ? 0 : item.getRefundStatus());
    }

    /**
     * 是否允许匹配
     *
     * @param canRefund 可退数量
     * @param qtyIn     入库数量
     * @return true 允许
     */
    private boolean isFitQty(BigDecimal canRefund, BigDecimal qtyIn) {

        return qtyIn.compareTo(canRefund) < 1;
    }

    /**
     * 订单明细与入库明细匹配映射
     *
     * @param itemId     订单明细编号
     * @param proId      入库明细编号
     * @param matchedMap 记录容器
     */
    private void matchedItemRecord(Long itemId, Long proId, Map<Long, List<Long>> matchedMap) {
        if (matchedMap.containsKey(itemId)) {
            matchedMap.get(itemId).add(proId);
        } else {
            matchedMap.put(itemId, new ArrayList<>());
            matchedMap.get(itemId).add(proId);
        }
    }

    private void matchedItmMapping(Long itemId, String key, Long proId, Map<Long, Map<String, List<Long>>> matchedMap) {
        Map<String, List<Long>> keyMap = matchedMap.get(itemId);
        if (keyMap == null) {
            keyMap = new HashMap<>();
            matchedMap.put(itemId, keyMap);
        }
        List<Long> list = keyMap.get(key);
        if (list == null) {
            list = new ArrayList<>();
            keyMap.put(key, list);
        }
        list.add(proId);
    }

    /**
     * 获取生成目标退单编号
     *
     * @param order 退单domain
     * @return 退单编号
     */
    private Long getNewReturnOrderId(OcBReturnOrder order) {

        if (order.getId() == null) {
            Long newReturnId = ModelUtil.getSequence("oc_b_return_order");
            order.setId(newReturnId);
            return newReturnId;

        }
        return order.getId();
    }

    /**
     * 生成退换货明细
     *
     * @param orderItem    当前匹配到的订单明细
     * @param proItemMap   入库明细
     * @param proIds       当前匹配订单明细与入库单明细关系集
     * @param domainReturn 退单信息
     * @param user         用户
     * @return 退换货明细
     */
    private OcBReturnOrderRefund buildReturnItem(OcBOrderItem orderItem, Map<Long, OcBRefundInProductItem> proItemMap,
                                                 List<Long> proIds, OcBReturnOrder domainReturn, boolean isStock, User user) {
        BigDecimal eachRefundQty = BigDecimal.ZERO;
        Map<String, BigDecimal> keyValuePair = new HashMap<>();
        for (Long proId : proIds) {
            OcBRefundInProductItem item = proItemMap.get(proId);
            if (item == null) {
                log.error(LogUtil.format(".buildReturnItem.生成退单明细时,统计数量,入库明细中未找到对应项, 入库明细Id={}",
                        "OcRefundInMatchOrderService"), proId);
                throw new NDSException("生成退单明细时,统计数量,入库明细中未找到对应项, 入库明细Id" + proId);
            }
            BigDecimal init = NumUtil.init(item.getQty());
            eachRefundQty = eachRefundQty.add(init);
            String groupKey = item.getProductDate();
            String productMark = item.getProductMark();
            productMark = StringUtils.isBlank(productMark) ? "null" : productMark;
            groupKey = groupKey + "," + productMark;
            statisticsQty(groupKey, init, keyValuePair);
        }

        OcBReturnOrderRefund newItem = new OcBReturnOrderRefund();
        Long newId = ModelUtil.getSequence("oc_b_return_order_refund");
        newItem.setId(newId);
        newItem.setOcBReturnOrderId(getNewReturnOrderId(domainReturn));
        newItem.setOcBOrderItemId(orderItem.getId());
        newItem.setOcBOrderId(orderItem.getOcBOrderId());
        newItem.setOid(orderItem.getOoid());

        // 商品标记 默认1, 退、换货标识  默认0 退, 是否AG退款，3不传
        newItem.setProductMark(OcBOrderConst.IS_STATUS_SY);
        newItem.setIsReturn(0);
        newItem.setIsToAg(AGStatusEnum.NOT.getVal());

        // 入库, 申请, 可退数量
        newItem.setQtyRefund(eachRefundQty);
        if (isStock) {
            newItem.setQtyIn(eachRefundQty);
            newItem.setQtyCanRefund(BigDecimal.ZERO);
            newItem.setQtyMatch(eachRefundQty.longValue());
        } else {
            newItem.setQtyIn(BigDecimal.ZERO);
            newItem.setQtyCanRefund(eachRefundQty);
            newItem.setQtyMatch(0L);
        }

        BigDecimal realAmt = NumUtil.init(orderItem.getRealAmt());
        BigDecimal qty = orderItem.getQty();
        // 单件退货金额
        AssertUtil.assertException(!NumUtil.gtZero(qty), "生成退单明细,计算单件退货金额时, 订单明细数量异常");
        BigDecimal sgAmt = realAmt.divide(qty, OcBOrderConst.DECIMAL_QTY, BigDecimal.ROUND_HALF_UP);
        newItem.setAmtRefundSingle(sgAmt);

        // 结算单价
        BigDecimal settle = orderItem.getPriceSettle();
        newItem.setPriceSettle(BigDecimal.ZERO);
        if (!NumUtil.gtZero(settle)) {
            if (NumUtil.gtZero(sgAmt)) {
                newItem.setPriceSettle(sgAmt);
            }
        }
        // 结算总额,如果没值,则启用成交价格
        BigDecimal totPrice = orderItem.getTotPriceSettle();
        newItem.setAmtSettleTot(BigDecimal.ZERO);
        if (!NumUtil.gtZero(totPrice)) {
            if (NumUtil.gtZero(realAmt)) {
                newItem.setAmtSettleTot(realAmt);
            }
        }
        // 吊牌价
        newItem.setPriceList(orderItem.getPriceTag());
        // 单件退货金额*申请数量
        newItem.setAmtRefund(newItem.getQtyRefund().multiply(newItem.getAmtRefundSingle()));

        // 国标码,规格,skuid,SKU编码,商品ID,商品编码,商品名称,商品单价, 尺寸id,尺寸编码,尺寸名称, 颜色id, 颜色编码, 颜色名称, 性别
        newItem.setBarcode(orderItem.getBarcode());
        newItem.setSkuSpec(orderItem.getSkuSpec());
        newItem.setPsCSkuId(orderItem.getPsCSkuId());
        newItem.setPsCSkuEcode(orderItem.getPsCSkuEcode());
        newItem.setPsCProId(orderItem.getPsCProId());
        newItem.setPsCProEcode(orderItem.getPsCProEcode());
        newItem.setPsCProEname(orderItem.getPsCProEname());
        newItem.setPrice(orderItem.getPriceTag());
        newItem.setPsCSizeId(orderItem.getPsCSizeId());
        newItem.setPsCSizeEcode(orderItem.getPsCSizeEcode());
        newItem.setPsCSizeEname(orderItem.getPsCSizeEname());
        newItem.setPsCClrId(orderItem.getPsCClrId());
        newItem.setPsCClrEcode(orderItem.getPsCClrEcode());
        newItem.setPsCClrEname(orderItem.getPsCClrEname());
        newItem.setSex(orderItem.getSex());

        newItem.setOwnerid(user.getId().longValue());
        newItem.setOwnername(user.getName());
        newItem.setOwnerename(user.getEname());
        newItem.setCreationdate(new Date());
        newItem.setModifierid(user.getId().longValue());
        newItem.setModifiername(user.getName());
        newItem.setModifierename(user.getEname());
        newItem.setModifieddate(new Date());
        newItem.setAdOrgId(Long.valueOf(user.getOrgId()));
        newItem.setAdClientId(Long.valueOf(user.getClientId()));
        newItem.setIsactive("Y");

        // 退单总金额, 退单总数量, sku
        BigDecimal returnAmtList = domainReturn.getReturnAmtList().add(newItem.getAmtRefund());
        domainReturn.setReturnAmtList(returnAmtList);
        BigDecimal qtyInStore = domainReturn.getQtyInstore().add(newItem.getQtyRefund());
        domainReturn.setQtyInstore(qtyInStore);

        StringBuilder allSku = new StringBuilder().append(",").append(domainReturn.getAllSku())
                .append(newItem.getPsCSkuEcode()).append("(").append(newItem.getQtyRefund()).append(")");
        domainReturn.setAllSku(allSku.toString());
        OcReturnInRelation inRelation = OcReturnInSupport.matchedReturn.get();
        if (inRelation == null) {
            inRelation = new OcReturnInRelation();
            OcReturnInSupport.matchedReturn.set(inRelation);
        }
        for (Map.Entry<String, BigDecimal> entry : keyValuePair.entrySet()) {
            inRelation.matchedMapping(entry.getKey(), entry.getValue(), newId);
        }
        return newItem;
    }

    /**
     * 待更新订单明细
     *
     * @param orderItem 已匹配订单明细
     * @return 待更新订单明细
     */
    private OcBOrderItem createOcBOrderItem(OcBOrderItem orderItem) {
        OcBOrderItem item = new OcBOrderItem();
        item.setId(orderItem.getId());
        item.setOcBOrderId(orderItem.getOcBOrderId());
        item.setQtyReturnApply(orderItem.getQtyReturnApply());
        item.setQtyHasReturn(orderItem.getQtyHasReturn());
        return item;
    }

    private void statisticsQty(String key, BigDecimal qty, Map<String, BigDecimal> map) {
        BigDecimal bigDecimal = map.get(key);
        if (bigDecimal == null) {
            map.put(key, qty);
            return;
        }
        map.put(key, bigDecimal.add(qty));
    }

    /**
     * tid组合
     *
     * @param tidSet 明细tid
     * @return 拼接tid
     */
    private String unionTid(Set<String> tidSet) {
        StringBuilder sb = new StringBuilder();
        for (String s : tidSet) {
            if (StringUtils.isBlank(s)) {
                continue;
            }
            sb.append(",").append(s);
        }
        return sb.length() > 0 ? sb.substring(1) : null;
    }

    private void logStep(String express, Object... obs) {
        ThreadLocalUtil.logStepMsg.get().add(String.format(express, obs));
    }

    /**
     * level debug recorder
     *
     * @param msg log message
     */
    private void logDebug(String msg, Object... params) {

        if (log.isDebugEnabled()) {
            if (params.length == 0) {
                log.debug(LogUtil.format("OcRefundInMatchOrderService.{}"), msg);
            } else {
                log.debug(LogUtil.format("OcRefundInMatchOrderService.") + msg, params);
            }
        }
    }


}
