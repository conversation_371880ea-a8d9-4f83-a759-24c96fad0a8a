package com.jackrain.nea.oc.oms.services.patrol;

import com.jackrain.nea.es.util.SpecialElasticSearchUtil;
import com.jackrain.nea.oc.oms.es.ES4ReturnOrder;
import com.jackrain.nea.oc.oms.mapper.*;
import com.jackrain.nea.oc.oms.model.table.*;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * @Author: 黄世新
 * @Date: 2019-07-25 21:05
 * @Version 1.0
 */

@Slf4j
@Component
public class DeleteOrderInfoService {

    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;
    @Autowired
    private OcBReturnAfSendMapper ocBReturnAfSendMapper;

    @Autowired
    private OcBReturnAfSendItemMapper ocBReturnAfSendItemMapper;

    @Autowired
    private OcBReturnOrderRefundMapper ocBReturnOrderRefundMapper;


    public String deleteOrderInfo(Long id, String type) {
        if (StringUtils.isNotEmpty(type)) {
            return deleteOrderReturn(id);
        }
        OcBOrder order = ocBOrderMapper.selectById(id);
        if (order == null) {
            return "订单数据不存在";
        }
        List<OcBOrderItem> list = ocBOrderItemMapper.selectOrderItemList(id);
        ocBOrderMapper.deleteById(id);
        ocBOrderItemMapper.deleteItemByorderId(id);

        //删除es
        try {
//            SpecialElasticSearchUtil.delDocument(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME,
//                    OcElasticSearchIndexResources.OC_B_ORDER_TYPE_NAME,
//                    id);
//            for (OcBOrderItem orderItem : list) {
//                SpecialElasticSearchUtil.delDocument(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME,
//                        OcElasticSearchIndexResources.OC_B_ORDER_ITEM_TYPE_NAME,
//                        orderItem.getId(), id);
//            }
            //删除redis数据
            String redisKey = BllRedisKeyResources.getOmsOrderKey(order.getSourceCode());
            CusRedisTemplate<String, Long> redisTemplate = RedisMasterUtils.getObjRedisTemplate();
            redisTemplate.delete(redisKey);
        } catch (Exception e) {

        }
        return "订单删除成功";
    }


    public String deleteOrderReturn(Long id) {
        OcBReturnOrder returnOrder = ocBReturnOrderMapper.selectById(id);
        if (returnOrder == null) {
            return "退单数据不存在";
        }
        List<OcBReturnOrderRefund> ocBReturnOrderRefunds = ocBReturnOrderRefundMapper.selectByOcOrderId(id);
        ocBReturnOrderMapper.deleteById(id);
        ocBReturnOrderRefundMapper.deleteItemByorderId(id);
        String returnId = returnOrder.getReturnId();
        OcBReturnAfSend ocBReturnAfSend = ocBReturnAfSendMapper.selectOcBReturnAfSendByRefundId(returnId);
        ocBReturnAfSendMapper.del(returnId);
        if (Objects.nonNull(ocBReturnAfSend)) {
            ocBReturnAfSendItemMapper.deleteById(ocBReturnAfSend.getId());
        }

        //删除es  @20201118 去除手推ES代码
//        try {
//            ES4ReturnOrder.deleteOrderReturnById(id);
//            for (OcBReturnOrderRefund ocBReturnOrderRefund : ocBReturnOrderRefunds) {
//                ES4ReturnOrder.deleteReturnOrderRefundById(id, ocBReturnOrderRefund);
//            }
////            //删除redis数据
////            String redisKey = BllRedisKeyResources.getOmsOrderKey(order.getSourceCode());
////            CusRedisTemplate<String, Long> redisTemplate = RedisOpsUtil.getStrRedisTemplate();
////            redisTemplate.delete(redisKey);
//        } catch (Exception e) {
//
//        }
        return "退单删除成功";


    }
}
