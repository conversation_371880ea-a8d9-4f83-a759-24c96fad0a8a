package com.jackrain.nea.oc.oms.model.enums;


/***
 * 打款状态 枚举类
 *
 */
public enum ReturnAfSendPaymentEnum {

    /** 未打款 */
    NO_PAYMENT("未打款", 0),

    /** 打款中 */
    PAYMENTING("打款中", 1),

    /** 打款成功 */
    PAYMENT_SUCCESS("打款成功", 2),

    /** 打款失败 */
    PAYMENT_FAIL("打款失败", 3);


    String key;
    int val;

    ReturnAfSendPaymentEnum(String k, int v) {
        this.key = k;
        this.val = v;
    }

    public String getKey() {
        return key;
    }

    public int getVal() {
        return val;
    }

    public Integer toInteger(){
      return this.getVal();
    }


}


