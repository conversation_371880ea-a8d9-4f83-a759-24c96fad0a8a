package com.jackrain.nea.oc.oms.services.naika;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.cpext.model.table.CpCPlatform;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.oc.oms.mapper.OcBReturnAfSendMapper;
import com.jackrain.nea.oc.oms.model.enums.CardAutoVoidEnum;
import com.jackrain.nea.oc.oms.model.enums.OrderBusinessTypeCodeEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnAfSendReturnBillTypeEnum;
import com.jackrain.nea.oc.oms.model.relation.NaiKaReturnQueryModel;
import com.jackrain.nea.oc.oms.model.request.naika.NaiKaReturnQueryRequest;
import com.jackrain.nea.oc.oms.model.table.OcBReturnAfSend;
import com.jackrain.nea.oc.oms.nums.RefundSourceEnum;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import com.jackrain.nea.web.query.QueryUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @ClassName OmsNaiKaReturnQueryService
 * @Description 奶卡退单列表
 * <AUTHOR>
 * @Date 2022/7/25 13:59
 * @Version 1.0
 */
@Component
@Slf4j
public class OmsNaiKaReturnQueryService {

    @Autowired
    private OcBReturnAfSendMapper ocBReturnAfSendMapper;
    @Autowired
    private CpRpcService cpRpcService;

    public ValueHolder naiKaReturnQuery(QuerySession querySession) {

        ValueHolder vh = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject resultData = new JSONObject();
        JSONObject param = JSON.parseObject(event.getParameterValue("param").toString());
        JSONObject fixColumn = param.getJSONObject("fixedcolumns");
        Integer range = param.getInteger("range") == null ? QueryUtils.getdefalutrange() : param.getInteger("range");
        Integer startIndex = param.getInteger("startindex") == null ? 0 : param.getInteger("startindex");
        NaiKaReturnQueryRequest naiKaReturnQueryRequest = fixColumn.toJavaObject(NaiKaReturnQueryRequest.class);
        log.info("OmsNaiKaReturnQueryService.naiKaReturnQuery.naiKaReturnQueryRequest:{}", JSONUtil.toJsonStr(naiKaReturnQueryRequest));
        // 构建es参数
        // 根据参数 查询es数据
        JSONObject whereKeys = new JSONObject();
        JSONObject filterKey = new JSONObject();
        JSONArray orderByKey = this.getOrderByKey();

        try {
            buildWhereKeys(whereKeys, naiKaReturnQueryRequest);
            buildFilterKey(filterKey, naiKaReturnQueryRequest);

            log.info("OmsNaiKaReturnQueryService.naiKaReturnQuery.whereKeys:{}", JSONUtil.toJsonStr(whereKeys));
            JSONObject esResult = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_RETURN_AF_SEND_INDEX_NAME, OcElasticSearchIndexResources.OC_B_RETURN_AF_SEND_TYPE_NAME,
                    whereKeys, filterKey, orderByKey, range, startIndex, new String[]{"ID"});

            if (null == esResult) {
                resultData.put("start", startIndex);
                resultData.put("row", "");
                resultData.put("totalRowCount", 0);
                vh.put("data", resultData);
                vh.put("code", 0);
                vh.put("message", "success");
                return vh;
            }
            JSONArray aryIds = esResult.getJSONArray("data");
            Integer totalCount = esResult.getInteger("total");
            if (CollectionUtils.isEmpty(aryIds)) {
                resultData.put("start", startIndex);
                resultData.put("row", "");
                resultData.put("totalRowCount", totalCount);
                vh.put("data", resultData);
                vh.put("code", 0);
                vh.put("message", "success");
                return vh;
            }
            List<Long> ids = Lists.newArrayList();
            for (int i = 0; i < aryIds.size(); i++) {
                Map<String, Long> map = (Map<String, Long>) aryIds.get(i);
                ids.add(map.get("ID"));
            }

            // 通过已发货退款单id 查询已发货退款单表
            List<OcBReturnAfSend> ocBReturnAfSends = ocBReturnAfSendMapper.selectOcBReturnAfSendListById(ids);
            List<NaiKaReturnQueryModel> modelList = new ArrayList<>();
            for (OcBReturnAfSend ocBReturnAfSend : ocBReturnAfSends) {
                NaiKaReturnQueryModel model = new NaiKaReturnQueryModel();
                buildModel(ocBReturnAfSend, model);
                modelList.add(model);
            }

            JSONArray jsonArray = (JSONArray) JSONArray.toJSON(modelList);
            List<JSONObject> jsonObjectList = JSONObject.parseArray(
                    JSONObject.toJSONString(jsonArray, SerializerFeature.WriteMapNullValue), JSONObject.class);
            JSONArray getFrameDataFormat = getFrameDataFormat(jsonObjectList);
            resultData.put("start", startIndex);
            resultData.put("rowCount", range);
            resultData.put("row", getFrameDataFormat);
            resultData.put("totalRowCount", totalCount);
            vh.put("data", resultData);
            vh.put("code", 0);
            vh.put("message", "success");
        } catch (Exception e) {
            log.error(LogUtil.format("查询奶卡管理-退单页面异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            resultData.put("start", startIndex);
            resultData.put("row", "");
            resultData.put("totalRowCount", 0);
            vh.put("data", resultData);
            vh.put("code", 0);
            vh.put("message", "success");
        }
        return vh;
    }

    private void buildModel(OcBReturnAfSend ocBReturnAfSend, NaiKaReturnQueryModel naiKaReturnQueryModel) {
        naiKaReturnQueryModel.setId(ocBReturnAfSend.getId());
        naiKaReturnQueryModel.setSourceCode(ocBReturnAfSend.getTid());
        naiKaReturnQueryModel.setBillNo(ocBReturnAfSend.getBillNo());
        naiKaReturnQueryModel.setCpCShopId(ocBReturnAfSend.getCpCShopId());
        CpShop cpShop = cpRpcService.selectCpCShopById(ocBReturnAfSend.getCpCShopId());
        CpCPlatform cpCPlatform = cpRpcService.selectCpcPlatformById(ocBReturnAfSend.getCpCPlatformId());
        // 根据店铺id查询店铺名称
        naiKaReturnQueryModel.setCpCShopTitle(cpShop.getCpCShopTitle());
        naiKaReturnQueryModel.setCpCPlatformName(cpCPlatform.getEname());
        if (ObjectUtil.equals(ocBReturnAfSend.getBillType(), 0)) {
            naiKaReturnQueryModel.setBillTypeName("退货退款");
        } else {
            naiKaReturnQueryModel.setBillTypeName("仅退款");
        }
        naiKaReturnQueryModel.setReturnStatusName(ReturnAfSendReturnBillTypeEnum.getKeyByVal(ocBReturnAfSend.getReturnStatus()));
        naiKaReturnQueryModel.setTReturnId(ocBReturnAfSend.getTReturnId());
        naiKaReturnQueryModel.setCardAutoVoid(ocBReturnAfSend.getCardAutoVoid());
        naiKaReturnQueryModel.setSysremark(ocBReturnAfSend.getSysremark());
        naiKaReturnQueryModel.setCardAutoVoidName(CardAutoVoidEnum.getMsgByCode(naiKaReturnQueryModel.getCardAutoVoid()));
        naiKaReturnQueryModel.setModifierName(ocBReturnAfSend.getModifiername());
        naiKaReturnQueryModel.setModifiedDate(DateUtil.format(ocBReturnAfSend.getModifieddate(), "yyyy/MM/dd HH:mm:ss"));
        naiKaReturnQueryModel.setRefundSourceName(RefundSourceEnum.getMsgByCode(ocBReturnAfSend.getRefundSource()));
    }

    /**
     * 框架格式返回
     *
     * @param dataList
     * @return
     */
    private static JSONArray getFrameDataFormat(List<JSONObject> dataList) {
        JSONArray array = new JSONArray();
        if (dataList != null && dataList.size() > 0) {
            for (JSONObject emp : dataList) {
                Set<String> keySet = emp.keySet();
                JSONObject json = new JSONObject();
                for (String key : keySet) {
                    JSONObject val = new JSONObject();
                    val.put("val", emp.get(key));
                    json.put(key.toUpperCase(), val);
                }
                array.add(json);
            }
        }
        return array;
    }

    private void buildWhereKeys(JSONObject whereKeys, NaiKaReturnQueryRequest request) {
        if (ObjectUtil.isNotEmpty(request.getSourceCode())) {
            String sourceCode = request.getSourceCode();
            String sourceCodeReplace = sourceCode.replaceAll("\\s*", "");
            String[] splitSourceCode = sourceCodeReplace.split(",|，");
            JSONArray jsonArray = new JSONArray(Arrays.asList(splitSourceCode));
            whereKeys.put("TID", jsonArray);
        }

        if (CollectionUtil.isNotEmpty(request.getCpcShopId())) {
            whereKeys.put("CP_C_SHOP_ID", request.getCpcShopId());
        }

        if (StringUtils.isNotEmpty(request.getTReturnId())) {
            whereKeys.put("T_RETURN_ID", request.getTReturnId());
        }

        if (ObjectUtil.isNotEmpty(request.getBillNo())) {
            String billNo = request.getBillNo();
            String billNoReplace = billNo.replaceAll("\\s*", "");
            String[] splitBillNoCode = billNoReplace.split(",|，");
            JSONArray jsonArray = new JSONArray(Arrays.asList(splitBillNoCode));
            whereKeys.put("BILL_NO", jsonArray);
        }

        if (CollectionUtil.isNotEmpty(request.getBillType())) {
            List<String> billType = request.getBillType();
            JSONArray jsonArray = new JSONArray();
            for (String to : billType) {
                jsonArray.add(to.replaceAll("=", ""));
            }
            whereKeys.put("BILL_TYPE", jsonArray);
        }

        if (CollectionUtil.isNotEmpty(request.getReturnStatus())) {
            List<String> billType = request.getReturnStatus();
            JSONArray jsonArray = new JSONArray();
            for (String to : billType) {
                jsonArray.add(to.replaceAll("=", ""));
            }
            whereKeys.put("RETURN_STATUS", jsonArray);
        }

        if (CollectionUtil.isNotEmpty(request.getCardAutoVoid())) {
            List<String> billType = request.getCardAutoVoid();
            JSONArray jsonArray = new JSONArray();
            for (String to : billType) {
                jsonArray.add(to.replaceAll("=", ""));
            }
            whereKeys.put("CARD_AUTO_VOID", jsonArray);
        }

        JSONArray jsonArray = new JSONArray();
        jsonArray.add(OrderBusinessTypeCodeEnum.MILK_RETURN_ONLY.getCode());
        jsonArray.add(OrderBusinessTypeCodeEnum.CYCLE_RETURN_ONLY.getCode());
        whereKeys.put("BUSINESS_TYPE_CODE", jsonArray);
    }

    private void buildFilterKey(JSONObject filterKey, NaiKaReturnQueryRequest request) {
        if (ObjectUtil.isNotEmpty(request.getCreationDate())) {
            String orderDate = request.getCreationDate();
            String[] orderSplitDate = orderDate.split("~");
            String orderDateResult = convertDate(orderSplitDate[0], orderSplitDate[1]);
            filterKey.put("CREATIONDATE", orderDateResult);
        }
    }

    /**
     * 日期转成ES需要的格式
     *
     * @return
     */
    public String convertDate(String begindate, String endDate) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
        if (StringUtils.isEmpty(begindate) || StringUtils.isEmpty(endDate)) {
            return "";
        }
        try {
            String result = sdf.parse(begindate).getTime() + "~" + sdf.parse(endDate).getTime();
            return result;

        } catch (ParseException e) {
            e.printStackTrace();
        }
        return "";
    }

    /**
     * ES 查询orderby条件
     *
     * @return
     */
    public JSONArray getOrderByKey() {
        JSONArray orderKeys = new JSONArray();
        JSONObject orderByKey = new JSONObject();
        orderByKey.put("desc", true);
        orderByKey.put("name", "ID");
        orderKeys.add(orderByKey);
        return orderKeys;
    }
}
