package com.jackrain.nea.oc.oms.services.invoice;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.google.common.base.Throwables;
import com.jackrain.nea.ac.service.InvoiceApplyService;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.hub.api.HubInvoicingCmd;
import com.jackrain.nea.hub.model.HXInvoicingModel.JsonRootObject;
import com.jackrain.nea.hub.model.HXInvoicingModel.ResultType;
import com.jackrain.nea.oc.oms.mapper.ac.AcFOrderInvoiceMapper;
import com.jackrain.nea.oc.oms.model.constant.InvoiceConst;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.table.AcFOrderInvoice;
import com.jackrain.nea.oc.oms.nums.OmsParamConstant;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.util.*;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/09/12 13:20
 */
@Slf4j
@Component
public class AcFOrderInvoiceVoidService {
    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    @Autowired
    private AcFOrderInvoiceMapper acOrderInvoiceMapper;

    @Reference(group = "hub", version = "1.0")
    private HubInvoicingCmd hubInvoicingCmd;

    @Autowired
    private InvoiceLogService invoiceLogService;

    @Autowired
    private InvoiceApplyService invoiceApplyService;

    public ValueHolder execute(QuerySession querySession) throws NDSException {
        SgR3BaseRequest request = R3ParamUtils.parseSaveObject(querySession, SgR3BaseRequest.class);
        request.setR3(true);
        AcFOrderInvoiceVoidService service = ApplicationContextHandle.getBean(this.getClass());
        return service.doVoid(request);
    }
    @Transactional(rollbackFor = Exception.class)
    public ValueHolder doVoid(SgR3BaseRequest request) {
        log.info(LogUtil.format("AcFOrderInvoiceVoidService.doVoid.request={}", "AcFOrderInvoiceVoidService.doVoid"),
                JSONObject.toJSONString(request));
        List<Long> batchObjIds = R3ParamUtils.getBatchObjIds(request);
        // 存储错误的Map
        Map<Long, Object> errorMap = new HashMap<>(batchObjIds.size());
        for (Long objId : batchObjIds) {
            String lockRedisKey = InvoiceConst.AC_F_ORDER_INVOICE + ":" + request.getObjId();
            RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
            try {
                if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                    AcFOrderInvoice acOrderInvoice = acOrderInvoiceMapper.selectById(objId);
                    if (acOrderInvoice == null) {
                        errorMap.put(objId, "当前记录已不存在！");
                        continue;
                    }
                    //如果【开票状态】=非开票成功状态且【发票种类】=电子或【红冲状态】=已红冲，则提示：“选择的订单发票不能作废！”
                    if (!OmsParamConstant.TWO.equals(acOrderInvoice.getInvoiceStatus())&&
                            (OmsParamConstant.ONE.equals(acOrderInvoice.getInvoiceKind())||
                                    OmsParamConstant.ONE.equals(acOrderInvoice.getRedRushStatus()))){
                        errorMap.put(objId, "选择的订单发票不能作废！");
                        continue;
                    }
                    //如果【开票状态】=开票成功状态且【发票种类】=纸质，开票时间非当月，则提示：“选择的订单发票跨月不能作废！”
                    if (OmsParamConstant.TWO.equals(acOrderInvoice.getInvoiceStatus())&&
                            (OmsParamConstant.ZERO.equals(acOrderInvoice.getInvoiceKind()))){
                        Date invoiceDate = acOrderInvoice.getInvoiceDate();
                        if (invoiceDate == null) {
                            errorMap.put(objId, "开票时间为空作废失败！");
                            continue;
                        }
                        Calendar calendar = Calendar.getInstance();
                        //获取当前年
                        int systemYear = calendar.get(Calendar.YEAR);
                        //获取当前月
                        int systemMonth = calendar.get(Calendar.MONTH) + 1;
                        calendar.setTime(invoiceDate);

                        int billYear = calendar.get(Calendar.YEAR);
                        int billMonth = calendar.get(Calendar.MONTH) + 1;
                        if (systemYear != billYear || systemMonth != billMonth) {
                            errorMap.put(objId, "选择的订单发票跨月不能作废！");
                            continue;
                        }
                    }
                    JsonRootObject jsonRootObject = new JsonRootObject();
                    jsonRootObject.setDocNum(acOrderInvoice.getBillNo());
                    ResultType resultType = hubInvoicingCmd.invalidInvoicing(jsonRootObject);
                    log.info(LogUtil.format("AcFOrderInvoiceVoidService.doVoid.resultType={}", "AcFOrderInvoiceVoidService.invalidInvoicing"),
                            JSONObject.toJSONString(resultType));
                    Boolean isSuccess = Boolean.valueOf(resultType.getSuccess());
                    String logMessage;
                    if (isSuccess){
                        acOrderInvoice.setInvoiceStatus(OmsParamConstant.FOUR);
                        acOrderInvoice.setIsactive(YesNoEnum.N.getKey());
                        BaseModelUtil.setupUpdateParam(acOrderInvoice,request.getLoginUser());
                        acOrderInvoiceMapper.updateById(acOrderInvoice);
                        Long applyId = acOrderInvoice.getInvoiceApplyId();
                        if (Objects.nonNull(applyId) && applyId.compareTo(0L) > 0) {
                            invoiceApplyService.updateInvoiceApplyItemTidSuffix(applyId);
                        }
                        logMessage="作废成功";
                    }else{
                        logMessage="作废失败:"+resultType.getMsg();
                    }
                    invoiceLogService.addUserOrderLog(acOrderInvoice.getId(),"作废",logMessage,
                            request.getLoginUser());

                } else {
                    errorMap.put(objId, "当前发票处于锁定状态！");
                }
            } catch (InterruptedException e) {
                log.error(LogUtil.format("AcFOrderInvoiceCancelService.doVoid.error={}", "error"),
                        Throwables.getStackTraceAsString(e));
                throw new NDSException("发票管理作废异常!");
            } finally {
                redisLock.unlock();
            }
        }

        return R3ParamUtils.getExcuteValueHolder(batchObjIds.size(),errorMap);
    }

}
