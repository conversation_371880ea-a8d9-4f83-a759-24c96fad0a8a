package com.jackrain.nea.oc.oms.gsi;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.util.ApplicationContextHandle;

import java.util.List;

/**
 * @Desc : 退单
 * <AUTHOR> xiWen
 * @Date : 2020/11/25
 */
public class GSI4ReturnOrder {

    private static OcBReturnOrderMapper ocBReturnOrderMapper;

    /**
     * @param origSourceCode ORIG_SOURCE_CODE
     * @return List Of ReturnOrderId
     */
    public static List<Long> getReturnIdListByOrigSourceCode(String origSourceCode) {
        return returnMapper().listIdFromGsiByOrigSourceCode(origSourceCode);
    }

    /**
     * @param origSourceCodes ORIG_SOURCE_CODE
     * @return List Of ReturnOrderId
     */
    public static List<Long> getReturnIdListByOrigSourceCodes(List<String> origSourceCodes) {
        return returnMapper().listIdFromGsiByOrigSourceCodes(origSourceCodes);
    }

    /**
     * @return OcBReturnOrderMapper
     */
    public static OcBReturnOrderMapper returnMapper() {
        if (ocBReturnOrderMapper == null) {
            ocBReturnOrderMapper = ApplicationContextHandle.getBean(OcBReturnOrderMapper.class);
            if (ocBReturnOrderMapper == null) {
                throw new NDSException("OcBReturnOrderMapper Not Found In Class GSI4ReturnOrder");
            }
        }
        return ocBReturnOrderMapper;
    }

    private GSI4ReturnOrder() {
    }

}
