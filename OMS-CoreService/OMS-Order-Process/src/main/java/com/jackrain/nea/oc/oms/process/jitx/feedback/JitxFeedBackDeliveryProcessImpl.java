package com.jackrain.nea.oc.oms.process.jitx.feedback;

import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.MakeupOrderType;
import com.jackrain.nea.oc.oms.model.relation.IpJitxDeliveryRelation;
import com.jackrain.nea.oc.oms.process.AbstractOrderProcess;
import com.jackrain.nea.oc.oms.util.LockOrderType;
import org.springframework.stereotype.Component;

/**
 * @author: chenxiulou
 * @description: JITX反馈寻仓结果
 * @since: 2019-06-26
 * create at : 2019-06-26 10:42
 */
@Component
public class JitxFeedBackDeliveryProcessImpl extends AbstractOrderProcess<IpJitxDeliveryRelation> {

    public JitxFeedBackDeliveryProcessImpl() {
        super();
    }

    @Override
    protected String getChildPackageName() {
        return "jitx";
    }

    @Override
    protected long getProcessOrderId(IpJitxDeliveryRelation deliveryInfo) {
        return deliveryInfo.getOrderId();
    }

    @Override
    protected String getProcessOrderNo(IpJitxDeliveryRelation deliveryInfo) {
        return deliveryInfo.getOrderNo();
    }

    @Override
    protected LockOrderType getCurrentLockOrderType() {
        return LockOrderType.TRANSFER_JITX_DELIVERY;
    }

    @Override
    protected ChannelType getCurrentChannelType() {
        return ChannelType.VIPJITX;
    }

    @Override
    protected MakeupOrderType getCurrentMakeupOrderType() {
        return MakeupOrderType.DO_NOT_MAKEUP;
    }


}
