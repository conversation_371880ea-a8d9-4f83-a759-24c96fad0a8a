package com.jackrain.nea.oc.oms.sap;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.services.RegionNewService;
import com.jackrain.nea.cpext.api.sap.SapShopApiCmd;
import com.jackrain.nea.cpext.model.ProvinceCityAreaInfo;
import com.jackrain.nea.cpext.utils.ValueHolderUtils;
import com.jackrain.nea.oc.oms.constant.SapSalesDateConstant;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBSapSalesDataGatherMapper;
import com.jackrain.nea.oc.oms.mapper.OcBSapSalesDataGatherSourceItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBSapSalesDataRecordMapper;
import com.jackrain.nea.oc.oms.model.constant.OcBSapSalesDataRecordConstant;
import com.jackrain.nea.oc.oms.model.table.*;
import com.jackrain.nea.oc.oms.services.OmsBusinessTypeStService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.AssertUtils;
import com.jackrain.nea.util.R3ParamUtils;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * sap common
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class SalesDataGatherBackSapService {

    @Autowired
    OcBSapSalesDataGatherMapper ocBSapSalesDataGatherMapper;

    @Autowired
    OcBSapSalesDataRecordMapper ocBSapSalesDataRecordMapper;

    @Autowired
    OcBSapSalesDataGatherSourceItemMapper ocBSapSalesDataGatherSourceItemMapper;

    @Autowired
    SapSalesDataGatherService sapSalesDataGatherService;

    @Reference(group = "cp-ext", version = "1.0")
    SapShopApiCmd sapShopApiCmd;

    @Autowired
    OmsBusinessTypeStService omsBusinessTypeStService;

    @Autowired
    OcBOrderMapper ocBOrderMapper;
    @Autowired
    RegionNewService regionNewService;

    /**
     * 按钮传SAP
     *
     * @param session
     * @return
     */
    public ValueHolder backSalesDataGather(QuerySession session) {
        SgR3BaseRequest request = R3ParamUtils.parseSaveObject(session, SgR3BaseRequest.class);
        request.setR3(true);
        SalesDataGatherBackSapService service = ApplicationContextHandle.getBean(SalesDataGatherBackSapService.class);
        return R3ParamUtils.convertV14WithResult(service.backSalesDataGather(request));
    }

    public ValueHolderV14<SgR3BaseResult> backSalesDataGather(SgR3BaseRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("Start SalesDataGatherBackSapService.backSalesDataGather.ReceiveParams:request{}"
                    , JSONObject.toJSONString(request));
        }
        List<Long> idList = request.getIds();
        checkParams(request, idList);
        sapSalesDataGatherService.salesOrderDataGatherBack(idList);

        return new ValueHolderV14<>(ResultCode.SUCCESS, "执行成功！");
    }


    public void checkParams(SgR3BaseRequest request, List<Long> idList) {

        if (CollectionUtils.isEmpty(idList)) {
            AssertUtils.logAndThrow("请选择需要传SAP的单据", request.getLoginUser().getLocale());
        }
        List<OcBSapSalesDataGather> ocBSapSalesDataGatherInfos = ocBSapSalesDataGatherMapper.selectList
                (new LambdaQueryWrapper<OcBSapSalesDataGather>().in(OcBSapSalesDataGather::getId, idList));

        List<String> isActiveInfo = ocBSapSalesDataGatherInfos.parallelStream().map(OcBSapSalesDataGather::getIsactive).collect(Collectors.toList());
        if (isActiveInfo.contains(SgConstants.IS_ACTIVE_N)) {
            AssertUtils.logAndThrow("存在已作废单据，不允许传SAP", request.getLoginUser().getLocale());
        }

        List<String> middleStatusInfo = ocBSapSalesDataGatherInfos.parallelStream().map(OcBSapSalesDataGather::getGatherMiddleStatus).collect(Collectors.toList());
        if (middleStatusInfo.contains(SapSalesDateConstant.GATHER_MIDDLE_STATUS_01)) {
            AssertUtils.logAndThrow("存在未汇总完成的单据，不允许传SAP", request.getLoginUser().getLocale());
        }

    }

    public ValueHolderV14 queryOcBOrderInfo(String billNo) throws Exception {
        ValueHolderV14 vh=new ValueHolderV14();
        OcBOrder ocBOrder=ocBOrderMapper.queryOcBOrderInfo(billNo);
        if (ocBOrder==null){
            AssertUtils.logAndThrow("查询零售发货单信息异常");
        }
        vh=sapShopApiCmd.searchQueryCostCenter(ocBOrder.getCpCShopId());
        return vh;
    }

    public StCBusinessType queryStCBusinessType(Long id) {
      return omsBusinessTypeStService.selectBusinessTypeById(id);

    }

    public ProvinceCityAreaInfo selectByProvinceAndCityAndArea(String provinceName, String cityName, String areaName) {
        return regionNewService.selectProvinceCityAreaInfo(provinceName, cityName, areaName);
    }
}
