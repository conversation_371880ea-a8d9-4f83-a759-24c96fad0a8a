package com.jackrain.nea.oc.oms.model.enums;

/**
 * 订阅状态枚举
 * 
 * <AUTHOR>
 * @date 2025/1/14
 */
public enum SubscriptionStatusEnum {
    
    /**
     * 未订阅
     */
    NOT_SUBSCRIBED(0, "未订阅"),
    
    /**
     * 订阅成功
     */
    SUBSCRIPTION_SUCCESS(1, "订阅成功"),
    
    /**
     * 订阅失败
     */
    SUBSCRIPTION_FAILED(2, "订阅失败");
    
    private final Integer code;
    private final String desc;
    
    SubscriptionStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public static SubscriptionStatusEnum getByCode(Integer code) {
        if (code == null) {
            return NOT_SUBSCRIBED;
        }
        for (SubscriptionStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return NOT_SUBSCRIBED;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
}
