package com.jackrain.nea.oc.oms.services.returnin;

import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.model.enums.ReturnStockInTimes;
import com.jackrain.nea.oc.oms.model.relation.OcReturnInRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsStockInMatchParam;
import com.jackrain.nea.oc.oms.model.relation.RefundInRelation;
import com.jackrain.nea.oc.oms.model.table.OcBRefundIn;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInLog;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInProductItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderActual;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderLog;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.util.ThreadLocalUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Desc : B2B 入库
 * B2B入库:
 * 入库明细必需全部匹配完成
 * 目前整体流程退单只允许单次入库, 不支持多次入库.
 * 匹配环节已支持多次入库, 库存中心服务调用前备参只支持单次入库,如需多次入库需要调整库存服务调用参数
 * <AUTHOR> xiWen
 * @Date : 2023/2/7
 */
@Slf4j
@Component
public class OcReturnInB2BNormalStockIn extends OcReturnInB2BStockIn implements IReturnMatchStockIn {

    /**
     * 预更新数据
     *
     * @param inRelation 入库单类
     * @param stockIn    退货单类
     * @return [refundIn, refund in items, return, return items, return actual items,refund log, return log,
     * orig_return, orig_return_items,user]
     */
    @Override
    public OmsStockInMatchParam handleBilProcessing(RefundInRelation inRelation, OcReturnInRelation stockIn) {

        logStep("first.prepare params");
        User user = ThreadLocalUtil.users.get();
        OcBRefundIn refundIn = inRelation.getRefundIn();
        OcBReturnOrder returnOrder = stockIn.getItem();

        inCommService.preRefundInWriteReturn4StoreInfo(inRelation.getRefundIn(), returnOrder);
        // re calc return status, amt
        inCommService.statisticsReturnBilStatusAndAmt(returnOrder, stockIn.getSubItems());

        OmsStockInMatchParam param = OmsStockInMatchParam.build(user);
        // 1. refund in. new update
        List<OcBRefundInProductItem> inItems = stockIn.getSubInMatchItems();
        OcBRefundIn newRefund = inCommService.preUpdateRefund4FistMatch(inRelation, returnOrder, inItems);
        param.setModRefundIn(newRefund);
        // 2. refund in item. new update
        Map<String, BigDecimal> stockInSkuQty = new HashMap<>();
        for (OcBRefundInProductItem inItem : inItems) {
            OcBRefundInProductItem item = inCommService.preUpdateRefundInItem4FistMatch(inItem, returnOrder.getId());
            param.addModRefundItem(item);
            inCommService.statisticInQty(stockInSkuQty, inItem.getPsCSkuEcode(), inItem.getQty());
        }
        // 3. return . new update
        OcBReturnOrder newReturn = new OcBReturnOrder();
        inCommService.preUpdateReturn4FistMatch(returnOrder, newReturn, refundIn);
        param.setModReturn(newReturn);
        // 4. return item . new update
        StringBuilder sb = new StringBuilder();
        List<OcBReturnOrderRefund> subMatchItems = stockIn.getSubMatchItems();
        for (OcBReturnOrderRefund returnItem : subMatchItems) {
            OcBReturnOrderRefund returnRefund = inCommService.preUpdateReturnItem4FistMatch(returnItem, true);
            sb.append(",").append(returnItem.getPsCSkuEcode());
            param.addModReturnItem(returnRefund);
        }
        // 5. return actual item
        List<OcBReturnOrderActual> newActualItems = inCommService.generateActualItems(inRelation, newReturn, user);
        param.setInsActualItems(newActualItems);
        // 6. refund log
        Long returnId = returnOrder.getId();
        String inSkuMessage = sb.substring(1);
        String msg = String.format("匹配退单编号:%d成功, 匹配条码:%s", returnId, inSkuMessage);
        OcBRefundInLog matchInLog = inCommService.buildRefundInLog("自动匹配退单", msg, refundIn.getId(), user);
        OcBRefundInLog inStockLog = inCommService.preInsertRefundLog4FistMatch(refundIn, stockInSkuQty, user);
        param.addInsRefundLog(matchInLog).addInsRefundLog(inStockLog);
        // 7. return log
        String content = String.format("入库结果单:%d入库完成[TOB], 入库条码:%s", refundIn.getId(), inSkuMessage);
        OcBReturnOrderLog stockInLog = inCommService.buildReturnOderLog("退货单入库", content, returnId, user);
        param.addInsReturnLog(stockInLog);
        return param.stockInParam(returnOrder, subMatchItems);
    }

    /**
     * generate bill, update bill
     *
     * @param inParam
     * @return
     */
    @Override
    public boolean persistDataAndStockIn(OmsStockInMatchParam inParam) {
        logStep("update data start");
        try {
            inCommService.atomicReturnFirstGenInResultAndUpdate(inParam);
        } catch (Exception ex) {
            String apply = OcReturnInSupport.expMsgFun.apply(ex);
            logStep(apply);
            log.error(LogUtil.format("B2B匹配入库更新数据,调用库存流程异常:{}"), Throwables.getStackTraceAsString(ex));
            return false;
        }
        logStep("update data end");
        return true;
    }

    @Override
    public ReturnStockInTimes stockInTimes() {
        return ReturnStockInTimes.INIT;
    }

}
