package com.jackrain.nea.oc.services.st;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.st.StCHoldOrderUnAuditCmd;
import com.jackrain.nea.st.service.StCHoldOrderUnAuditService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName StCHoldOrderUnAuditCmdImpl
 * @Description 订单hold策略反审核
 * <AUTHOR>
 * @Date 2023/2/15 11:29
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class StCHoldOrderUnAuditCmdImpl extends CommandAdapter implements StCHoldOrderUnAuditCmd {

    @Autowired
    private StCHoldOrderUnAuditService holdOrderUnAuditService;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        return holdOrderUnAuditService.execute(querySession);
    }

}
