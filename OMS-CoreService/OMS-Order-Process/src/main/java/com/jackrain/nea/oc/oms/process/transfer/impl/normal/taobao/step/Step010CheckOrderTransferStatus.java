package com.jackrain.nea.oc.oms.process.transfer.impl.normal.taobao.step;

import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoOrderRelation;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 判断淘宝订单单据转换状态
 *
 * @author: 易邵峰
 * @since: 2019-01-21
 * create at : 2019-01-21 14:44
 */
@Step(order = 10, description = "判断淘宝订单单据转换状态")
@Slf4j
@Component
public class Step010CheckOrderTransferStatus extends BaseTaobaoOrderProcessStep
        implements IOmsOrderProcessStep<IpTaobaoOrderRelation> {

    @Override
    public ProcessStepResult<IpTaobaoOrderRelation> startProcess(IpTaobaoOrderRelation orderInfo,
                                                                 ProcessStepResult preStepResult,
                                                                 boolean isAutoMakeup, User operateUser) {
        if (orderInfo == null || orderInfo.getTaobaoOrder() == null) {
            return new ProcessStepResult<>(StepStatus.FAILED, "OrderInfo为空或者Order.TaobaoOrder为空；退出转换");
        }
        int currentStatus = orderInfo.getTaobaoOrder().getIstrans();
        if (TransferOrderStatus.TRANSFERRED.toInteger() == currentStatus) {
            String operateMessage = Resources.getMessage("单据" + orderInfo.getOrderId() + "状态=已转换，转换完成");
            return new ProcessStepResult<>(StepStatus.FINISHED, operateMessage);
        } else if (TransferOrderStatus.TRANSFERRING.toInteger() == currentStatus) {
            String operateMessage = Resources.getMessage("单据" + orderInfo.getOrderId() + "正在转换中");
            return new ProcessStepResult<>(StepStatus.FINISHED, operateMessage);
        } else {
            String operateMessage = Resources.getMessage("单据" + orderInfo.getOrderId() + "检查状态成功，进入下一阶段");
            return new ProcessStepResult<>(StepStatus.SUCCESS, operateMessage);
        }
    }
}
