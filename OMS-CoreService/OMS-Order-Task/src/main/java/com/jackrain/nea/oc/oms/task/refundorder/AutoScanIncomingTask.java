package com.jackrain.nea.oc.oms.task.refundorder;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.oc.oms.services.ScanIncomingAutoService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> 孙俊磊
 * @since : 2019-08-22
 * create at : 2019-08-22 4:24 PM
 * 扫描入库自动任务
 */
@Slf4j
@Component
public class AutoScanIncomingTask extends BaseR3Task implements IR3Task {

    @Autowired
    ScanIncomingAutoService service;

    @Override
    @XxlJob("AutoScanIncomingTask")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult result = new RunTaskResult();

        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
        Integer range = config.getProperty("lts.AutoScanIncomingTask.range", 200);
        if (log.isDebugEnabled()) {
            log.debug("{} 扫描入库定时任务开始执行,设置的数据量为{}", this.getClass().getSimpleName(), range);
        }
        try {
            service.doScanIncoming(range);
            result.setSuccess(true);
        } catch (Exception ex) {
            result.setSuccess(false);
            result.setMessage(ex.getMessage());
        }
        return result;
    }
}

