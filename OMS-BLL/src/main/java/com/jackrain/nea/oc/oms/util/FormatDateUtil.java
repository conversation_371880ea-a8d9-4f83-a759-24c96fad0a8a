package com.jackrain.nea.oc.oms.util;

import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * @author: 郑立轩
 * @since: 2019/4/24
 * create at : 2019/4/24 14:35
 */
@Component
public class FormatDateUtil {

    public static String formatDate(Date date, String format) {
        //默认时区
        try {
            ZoneId zoneId = ZoneId.systemDefault();
            LocalDateTime localDateTime = LocalDateTime.ofInstant(date.toInstant(), zoneId);
            return localDateTime.format(DateTimeFormatter.ofPattern(format));
        } catch (Exception e) {
            return "00:00:00 00:00:00";
        }
    }


}
