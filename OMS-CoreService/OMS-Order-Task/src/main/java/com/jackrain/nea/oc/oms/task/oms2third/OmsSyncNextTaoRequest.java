package com.jackrain.nea.oc.oms.task.oms2third;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2020/8/23
 */
@Data
public class OmsSyncNextTaoRequest<T> implements Serializable {


    private static final long serialVersionUID = 8317447824401365552L;


    private String method;

    private String centerName;

    private List<T> data;


}
