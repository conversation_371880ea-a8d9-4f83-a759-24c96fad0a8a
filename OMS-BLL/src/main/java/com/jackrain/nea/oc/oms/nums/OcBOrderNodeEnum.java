package com.jackrain.nea.oc.oms.nums;

import com.jackrain.nea.exception.NDSException;
import lombok.Getter;

import java.util.Objects;

/**
 * description:零售发货单节点枚举
 * @Author:  l<PERSON><PERSON><PERSON>
 * @Date 2022/1/19 10:38 上午
 */
public enum OcBOrderNodeEnum {
    DETENTION_DATE(1,"卡单"),
    DETENTION_RELEASE_DATE(2,"卡单释放"),
    STOCK_OCCUPY_DATE(3,"首次缺货"),
    OCCUPY_SUCCESS_DATE(4,"占库存成功"),
    HOLD_DATE(5,"Hold单"),
    AUDIT_SUCCESS_DATE(6,"审核成功"),
    CANCEL_DATE(7,"取消"),
    EXAMINE_ORDER_DATE(8,"反审核"),
    ONROAD_DATE(9,"物流-揽收"),
    ONROAD_TRANSFER_DATE(10,"物流-中转"),
    ARRIVED_DATE(11,"物流-签收"),
    WAREHOUSE_DELIVERY_TIME(12,"仓库发货"),
    PLATFORM_DELIVERY_TIME(13,"平台发货"),
    HOLD_RELEASE_DATE(14,"HOLD单释放"),
    ORDER_CREAT(15,"订单创建");

    @Getter
    private Integer value;

    @Getter
    private String desc;

    OcBOrderNodeEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static String getDescByValue(Integer value) {
        OcBOrderNodeEnum[] values = OcBOrderNodeEnum.values();
        for (OcBOrderNodeEnum nodeEnum : values) {
            if (Objects.equals(nodeEnum.value, value)) {
                return nodeEnum.desc;
            }
        }
        throw new NDSException("错误的节点配置:" + value);
    }
}
