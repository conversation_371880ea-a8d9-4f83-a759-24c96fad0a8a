package com.jackrain.nea.oc.oms.model.enums.ac;

import lombok.Getter;

/**
 * @author: 陈俊明
 * @since: 2020/05/12
 * @create at : 2020/05/12 19:26
 */
@Getter
public enum ChannelTypeEnum {
    /**
     * 直营
     */
    STATUS_ZY(1,"直营"),
    /**
     * 分销
     */
    STATUS_FX(2,"分销");

    int val;
    String text;

    ChannelTypeEnum(int val, String text) {
        this.text = text;
        this.val = val;
    }

    public String getText() {
        return text;
    }

    public int getVal() {
        return val;
    }
}
