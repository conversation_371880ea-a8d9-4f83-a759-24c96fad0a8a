package com.jackrain.nea.oc.oms.constant;

/**
 * @program: ryytn-oc-oms-v3.0
 * @description:
 * @author: haiyang
 * @create: 2024-01-03 17:56
 **/
public class Mq5Constants {

    public static final String TOPIC_R3_OC_OMS_CALL_TRANSFER = "R3_OC_OMS_CALL_TRANSFER";
//    public static final String TAG_R3_OC_OMS_CALL_TRANSFER = "OperateTobeConfirmed";
    public static final String TAG_OPERATEMQORDER = "OperateMqOrder";


    public static final String TOPIC_R3_SG_TOBECONFIRM = "R3_SG_TOBECONFIRM";
    public static final String TAG_R3_SG_TOBECONFIRM = "OperateTobeConfirm";

    /**
     * toc残次
     */
    public static final String TOPIC_R3_SG_TOBECONFIRM_CC = "R3_SG_TOBECONFIRM_CC";
    public static final String TAG_R3_SG_TOBECONFIRM_CC = "OperateTobeConfirm_CC";


    /**
     * 批量修改物流
     * @see ModifyOrderLogisticsListenMq
     */
    public static final String TOPIC_R3_OC_OMS_MODIFY_LOGISTICS = "R3_OC_OMS_MODIFY_LOGISTICS";
    public static final String TAG_R3_OC_OMS_MODIFY_LOGISTICS = "logistics";

    public static final String TOPIC_R3_OC_OMS_MODIFY_WH_LG = "R3_OC_OMS_MODIFY_WH_LG";
    public static final String TAG_R3_OC_OMS_MODIFY_WH_LG = "warehouse";


    public static final String TOPIC_R3_OC_OMS_CALL_TOBECONFIRMED = "R3_OC_OMS_CALL_TOBECONFIRMED";
    public static final String TAG_R3_OC_OMS_CALL_TOBECONFIRMED = "OperateTobeConfirmed";


    public static final String TOPIC_R3_OC_OMS_CALL_REFUNDIN = "R3_OC_OMS_CALL_REFUNDIN";
    public static final String TAG_R3_OC_OMS_CALL_REFUNDIN = "OperateRefundIn";

    public static final String TOPIC_R3_ORDER_LOGISTICS_INTERCEPT = "R3_ORDER_LOGISTICS_INTERCEPT";
    public static final String TAG_R3_ORDER_LOGISTICS_INTERCEPT = "ztoLogisticsIntercept";


    public static final String TOPIC_R3_RETAIL_CALLBACK = "R3_RETAIL_CALLBACK";
    public static final String TAG_R3_RETAIL_CALLBACK = "oms_to_retail_notify";

    public static final String TOPIC_R3_CALCULATE_DATE_LABEL = "R3_CALCULATE_DATE_LABEL";
    public static final String TAG_R3_CALCULATE_DATE_LABEL = "calculateDateLabel";


}
