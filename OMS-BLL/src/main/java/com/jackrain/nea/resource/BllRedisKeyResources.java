package com.jackrain.nea.resource;

import cn.hutool.core.date.DatePattern;
import com.jackrain.nea.util.DateUtil;

import java.util.Calendar;
import java.util.Date;

/**
 * Redis关键字资源
 *
 * @author: 易邵峰
 * @since: 2019-01-28
 * create at : 2019-01-28 09:10
 */
public class BllRedisKeyResources {

    public static final long DEFAULT_REDIS_KEY_VALIDATE_TIME = 24 * 60 * 60 * 1000L;

    /**
     * 获取Product RedisKey
     *
     * @param sku SKU编码
     * @return Product SKU Redis Key内容
     */
    public static String getProductSkuKey(String sku) {
        return "ps:product:sku:" + sku;
    }

    public static String getOmsOrderKey(String sourceCode) {
        return "oc:oms:order:source:" + sourceCode;
    }

    public static String getOmsReturnOrderLogisticsKey(String logistics) {
        return "oc:oms:return:order:logistics:" + logistics;
    }

    /**
     * 退单redis.key
     *
     * @param returnId
     * @return
     */
    public static String getOmsReturnOrderReturnIdKey(String returnId) {
        // @20200803 统一退单redis key，加个id
        return "oc:oms:return:order:id:" + returnId;
    }

    public static String getPreDayBillNoSequence() {
        Calendar calCurrent = Calendar.getInstance();
        calCurrent.add(Calendar.DATE, -1);
        return "SEQ_" + "OM_" + com.jackrain.nea.core.db.Tools.dayFormatter.format(calCurrent.getTime());
    }

    public static String getCurrentBillNoSequence() {
        return "SEQ_" + "OM1_" + com.jackrain.nea.core.db.Tools.dayFormatter.format(new Date());
    }

    public static String getDeficiencyBillNoSequence() {
        Calendar calCurrent = Calendar.getInstance();
        calCurrent.add(Calendar.DATE, -1);
        return "SEQ_" + "FH_" + com.jackrain.nea.core.db.Tools.dayFormatter.format(calCurrent.getTime());
    }


    /**
     * 创建锁定订单Redis Key值
     *
     * @param orderId 订单ID值
     * @return 锁定订单Redis Key值
     */
    public static String buildLockOrderKey(long orderId) {
        return "oc:oms:order:" + orderId;
    }

    /**
     * 创建锁定订单Redis Key值
     *
     * @param cardCode 卡号
     * @return 锁定订单Redis Key值
     */
    public static String buildLockCardCodeKey(String cardCode) {
        return "oc:oms:cardcode:" + cardCode;
    }

    /**
     * 创建锁定订单Redis Key值
     *
     * @param orderId 订单ID值
     * @return 锁定订单Redis Key值
     */
    public static String buildLockOrderWmsKey(long orderId) {
        return "oc:oms:order:wms" + orderId;
    }

    /**
     * 创建锁定退换货订单Redis Key值
     *
     * @param returnOrderId
     * @return
     */
    public static String buildLockReturnOrderKey(long returnOrderId) {
        return "oc:oms:returnOrder:" + returnOrderId;
    }

    /**
     * 创建锁定退货入库单Redis Key值
     *
     * @param returnInId
     * @return
     */
    public static String buildLockReturnInKey(long returnInId) {
        return "oc:oms:returnIn:" + returnInId;
    }


    /**
     * 存物流单号用，redisKey=物流单号，redisValue=入库单Id
     *
     * @param logisticsNumber 物流单号
     * @return key
     */
    public static String buildReturnOrderLogisticsNumberKey(String logisticsNumber) {
        return "oc:oms:returnOrder:logisticsNumber:" + logisticsNumber;
    }

    /**
     * 锁单用  1分钟失效
     *
     * @param logisticsCode 物流单号 唯一
     * @return key
     */
    public static String buildLockReturnOrderLogisticsCodeKey(String logisticsCode) {
        return "oc:oms:returnOrder:logisticsCodeLock:" + logisticsCode;
    }

    /**
     * @param id 退货入库单id
     * @return key
     */
    public static String buildLockRefundInKey(Long id) {
        return "oc:oms:refundIn:" + id;
    }

    /**
     * @param tid 订单id
     * @return key
     */
    public static String getUpdateOrderAddressKey(String tid) {
        return "oc:oms:order:upRegion:" + tid;
    }

    /**
     * 创建缺货发送给线下pos订单redis key值
     *
     * @param orderId 订单ID值
     * @return 锁定订单Redis Key值
     */
    public static String buildOutOfStockNotifyRetailKey(long orderId) {
        return "oc:oms:order:notify:retail:" + orderId;
    }


    /**
     * 获取JITX修改仓库标识 redis key 值
     *
     * @return
     */
    public static String getJitxChangeWarehouseFlagKey(Long orderId) {
        return "oc:oms:order:jitx:changeWarehouseFlag:" + orderId;
    }

    /**
     * JITX订单改仓工单 redis key 值
     *
     * @param id
     * @return
     */
    public static String buildJitxChangeWarehouseWorkflowKey(Long id) {
        return new StringBuffer("oc:oms:order:jitx:changeWarehouseWorkflow:").append(id).toString();
    }

    /**
     * JITX订单创建改仓工单 redis key 值
     *
     * @param orderId
     * @return
     */
    public static String buildJitxCreateChangeWarehouseWorkflowKey(Long orderId) {
        return new StringBuffer("oc:oms:order:jitx:createChangeWarehouseWorkflow:").append(orderId).toString();
    }

    /**
     * 业务单据缓存，区别于锁的key，用于解决es同步延迟的问题
     *
     * @param topic
     * @param messageId
     * @return
     */
    public static String buildMqMessageKey(String topic, String messageId) {
        return new StringBuffer().append("oc:oms:mq:message:record:").append(topic).append(":").append(messageId).toString();
    }

    /**
     * 订单导入，ES延迟，防止文件重复导入
     *
     * @param key
     * @return
     */
    public static String buildImportKey(String key) {
        return "oc.oms.order.import." + key;
    }


    public static String getReturnOrderDisputeIdKey(String disputeId) {
        return "oc:oms:return:order:disputeId:" + disputeId;
    }

    /**
     * 创建锁定唯品会时效订单Redis Key值
     *
     * @param orderId 订单ID值
     * @return 锁定订单Redis Key值
     */
    public static String buildVipTimeOrderLockOrderKey(long orderId) {
        return "oc:oms:vipTimeOrder:" + orderId;
    }

    /**
     * 创建锁定唯品会时效订单Redis Key值
     *
     * @param orderId 订单ID值
     * @return 锁定订单Redis Key值
     */
    public static String buildVipDeliveryLockOrderKey(long orderId) {
        return "oc:oms:vipDelivery:" + orderId;
    }

    /**
     * 创建锁定唯品会根单号Redis Key值
     *
     * @param rootOrderSn 寻仓单根单号
     * @return 锁定订单Redis Key值
     */
    public static String buildVipDeliveryRootOrderSnLockOrderKey(String rootOrderSn) {
        return "oc:oms:vipDelivery:rootOrderSn:" + rootOrderSn;
    }

    /**
     * 获取JITX发货重置标识 redis key 值
     *
     * @return
     */
    public static String getJitxResetShipFlagKey(String tid) {
        return "oc:oms:order:jitx:resetShipFlag:" + tid;
    }

    public static String buildSapSalesDataRecordAddTaskRedisLockKey(Long orderId, Integer billType) {
        return "oc:oms:sap:SalesDataRecordAddTask:"+ billType +":id:" + orderId;
    }

    public static String buildBlackCardRecordKey(String mobile) {
        return "oc:oms:order:blackCardRecord:" + mobile;
    }

    /**
     * 黑名单策略卡单-手机号
     *
     * @param shopId
     * @param mobile
     * @return
     */
    public static String blackStMobileOrderNumKey(Long shopId, String mobile) {
        return "oc:oms:black:mobile:" + shopId + ":" + mobile;
    }

    /**
     * 黑名单策略卡单-手机号
     * 一级分类&内容
     *
     * @param shopId
     * @param mobile
     * @return
     */
    public static String blackStMobileKey(Long shopId, String mobile) {
        return "oc:oms:black:mobile:dim4:" + shopId + ":" + mobile;
    }

    /**
     * 黑名单策略卡单-地址
     *
     * @param shopId
     * @param address
     * @return
     */
    public static String blackStAddressOrderNumKey(Long shopId, String address) {
        return "oc:oms:black:address:" + shopId + ":" + address;
    }

    /**
     * 黑名单策略卡单-地址
     * 一级分类&内容
     *
     * @param shopId
     * @param address
     * @return
     */
    public static String blackStAddressKey(Long shopId, String address) {
        return "oc:oms:black:address:dim4:" + shopId + ":" + address;
    }

    /**
     * @param orderId
     * @return
     */
    public static String buildLockOrderInterceptKey(long orderId) {
        return "oc:oms:order:intercept:" + orderId;
    }

    /**
     * 标记需要取消的残次订单
     *
     * @param tid
     * @return
     */
    public static String getCOrderCancelMark(String tid) {
        return "oc:oms:c:cancel:mark:" + tid;
    }

    /**
     * 拼多多-非首单的第一个订单发货当时的时间戳（毫秒）记录
     *
     * @param tid
     * @return
     */
    public static String getPddDeliveryDelayMark(String tid) {
        return "oc:oms:pdd:delivery:delay:mark:" + tid;
    }



    /**
     * 出库汇波策略计算
     *
     * @return
     */
    public static String buildLockCalculateDateLabelKey() {
        return "st:expiry:date:label:calculate";
    }

    /**
     * 出库汇波策略计算(日任务)
     *
     * @return
     */
    public static String buildLockCalculateDateLabelKeyDaily() {
        return "st:expiry:date:label:calculate:daily";
    }

    /**
     * 直发预占操作锁
     */
    public static String buildDirectReportOptLockKey(Long id) {
        return "oc:oms:direct:report:order:opt:" + id;
    }

    /**
     * 定时任务排他锁
     */
    public static String buildTaskLockKey(String taskName) {
        return "oc:oms:task:" + taskName;
    }

    /**
     * 销售数据汇总任务完成标识
     */
    public static String buildSapSalesDataRecordTaskCompleteKey() {
        return "oc:oms:sap.sales.data.record.complete:" + DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN);
    }
}


