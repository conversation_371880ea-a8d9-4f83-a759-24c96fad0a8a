package com.jackrain.nea.oc.oms.model.table.task;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@TableName(value = "oc_b_towing_delivery_task")
@Data
public class OcBToWingDeliveryTask extends BaseModel {
    @JSONField(name = "ID")
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "TID")
    @Field(type = FieldType.Keyword)
    private String tid;

    @JSONField(name = "OUT_BILL_NO")
    @Field(type = FieldType.Keyword)
    private String outBillNo;

    @JSONField(name = "STATUS")
    @Field(type = FieldType.Integer)
    private Integer status;

    @JSONField(name = "REMAKE")
    @Field(type = FieldType.Keyword)
    private String remake;

    @JSONField(name = "CREATIONDATE")
    @Field(type = FieldType.Date)
    private Date creationdate;

    @JSONField(name = "MODIFIEDDATE")
    @Field(type = FieldType.Date)
    private Date modifieddate;

    @JSONField(name = "ISACTIVE")
    @Field(type = FieldType.Keyword)
    private String isactive;
}
