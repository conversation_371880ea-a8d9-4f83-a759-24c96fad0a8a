package com.jackrain.nea.oc.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.InvoicNoticeEstatusCmd;
import com.jackrain.nea.oc.oms.nums.OcInvoiceLogTypeEnum;
import com.jackrain.nea.oc.oms.nums.OcInvoiceStatusEnum;
import com.jackrain.nea.oc.oms.services.InvoiceNoticeEstatusService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: huang.zaizai
 * @description: 开票通知单 审核
 * @since: 2019-07-23
 * create at : 2019-07-23 15:00
 */
@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class InvoicNoticeEstatusCmdImpl extends CommandAdapter implements InvoicNoticeEstatusCmd {
    @Autowired
    private InvoiceNoticeEstatusService service;

    @Override
    public ValueHolder changeEstatusByUnAudit(QuerySession session) throws NDSException {
        return service.changeEstatus(session, OcInvoiceStatusEnum.WAIT_INVOICE,
                OcInvoiceStatusEnum.NOT_AUDITED, OcInvoiceLogTypeEnum.UNAUDIT);
    }

    @Override
    public ValueHolder changeEstatusByStop(QuerySession session) throws NDSException {
        return service.changeEstatus(session, OcInvoiceStatusEnum.WAIT_INVOICE,
                OcInvoiceStatusEnum.REPRIEVE_INVOICE, OcInvoiceLogTypeEnum.REPRIEVE_INVOICE);
    }

    @Override
    public ValueHolder changeEstatusByStart(QuerySession session) throws NDSException {
        return service.changeEstatus(session, OcInvoiceStatusEnum.REPRIEVE_INVOICE,
                OcInvoiceStatusEnum.WAIT_INVOICE, OcInvoiceLogTypeEnum.CANCEL_REPRIEVE);
    }
}
