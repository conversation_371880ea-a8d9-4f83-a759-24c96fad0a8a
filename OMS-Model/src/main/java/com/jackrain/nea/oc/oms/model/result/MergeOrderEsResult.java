package com.jackrain.nea.oc.oms.model.result;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import lombok.Data;

import java.util.List;

/**
 * @Desc : es查询符合合并订单加密字符串结果
 * <AUTHOR> xiWen
 * @Date : 2020/12/10
 */
@Data
public class MergeOrderEsResult {

    /**
     * 订单信息加密串. 合单
     */
    private List<String> encryptCodes;

    /**
     * 符合合单总数量
     */
    private int totalItemCount;

    public JSONArray encryptCodes2JsonAry() {
        return JSON.parseArray(JSON.toJSONString(encryptCodes));
    }
}
