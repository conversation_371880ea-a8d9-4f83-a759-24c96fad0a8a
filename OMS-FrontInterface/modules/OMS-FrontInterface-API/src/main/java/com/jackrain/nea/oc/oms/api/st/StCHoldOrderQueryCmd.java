package com.jackrain.nea.oc.oms.api.st;

import com.jackrain.nea.oc.oms.model.request.StCHoldOrderRequest;
import com.jackrain.nea.sys.Command;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/07/02 17:09
 */
public interface StCHoldOrderQueryCmd extends Command {

    /**
     * 根据店铺ID查询有效的Hold策略
     * @param shopId
     * @return
     */
    List<StCHoldOrderRequest> queryStCHoldOrderByShopId(Long shopId);

}
