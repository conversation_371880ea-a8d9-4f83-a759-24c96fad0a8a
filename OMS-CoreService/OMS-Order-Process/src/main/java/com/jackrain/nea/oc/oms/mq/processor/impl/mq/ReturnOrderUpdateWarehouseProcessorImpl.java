package com.jackrain.nea.oc.oms.mq.processor.impl.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.burgeon.mq.error.MqException;
import com.burgeon.r3.sg.store.model.request.in.SgBStoInBillSaveRequest;
import com.burgeon.r3.sg.store.model.request.in.SgBStoInItemSaveRequest;
import com.burgeon.r3.sg.store.model.request.in.SgBStoInSaveRequest;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpStore;
import com.jackrain.nea.oc.model.SgBPhyInResultExt;
import com.jackrain.nea.oc.model.SgBPhyInResultItemExt;
import com.jackrain.nea.oc.oms.services.OcBRetailReturnUpdateRefundInService;
import com.jackrain.nea.oc.oms.util.WmsUserCreateUtil;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.UserImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 零售退货更新入库结果服务(MQ已通)
 *  - 场景：富勒的退货单是通过富勒无名件入库完成
 *
 * @author: 郑立轩
 * @since: 2019/5/9
 * create at : 2019/5/9 19:28
 */
// fixme tag:sg_to_oms_in_result_verify_postback
@Component
@Slf4j
//@RocketMqMessageListener(name = "ReturnOrderUpdateWarehouseProcessorImpl", type = MqTypeEnum.DEFAULT, groupIdSuffix = "SG3")
public class ReturnOrderUpdateWarehouseProcessorImpl {
    @Autowired
    private OcBRetailReturnUpdateRefundInService retailService;

    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    WmsUserCreateUtil wmsUserCreateUtil;

    public void consume(String messageBody, String messageTopic, String messageKey, String messageTag, Object object) {
        try {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("零售退货更新入库结果服务: {}", messageKey), messageBody);
            }
            SgBStoInBillSaveRequest request = JSON.parseObject(messageBody, SgBStoInBillSaveRequest.class);
            SgBStoInSaveRequest exts = request.getSgStoInSaveRequest();
            List<SgBStoInItemSaveRequest> itemList = request.getSgStoInItemSaveRequestList();
            JSONObject jsonObject = JSON.parseObject(messageBody);
            String userStr = jsonObject.getString("loginUser");
            User loginUser = JSON.parseObject(userStr, UserImpl.class);
            // end
            //主表
            SgBPhyInResultExt ext = new SgBPhyInResultExt();
            //逻辑仓ID
            ext.setCpCStoreId(exts.getCpCStoreId());
            //逻辑仓CODE
            ext.setCpCStoreEcode(exts.getCpCStoreEcode());
            //逻辑仓名称
            ext.setCpCstoreEname(exts.getCpCStoreEname());
            CpStore cpStore = cpRpcService.selectCpCStoreById(exts.getCpCStoreId());
            if (cpStore != null) {
                //实体仓ID
                ext.setCpCPhyWarehouseId(cpStore.getCpCPhyWarehouseId());
            } else {
                log.error("未查询到逻辑仓");
                return;
            }
            //来源单据ID
            ext.setSourceBillId(exts.getSourceBillId());

            //明细表
            List<SgBPhyInResultItemExt> item = new ArrayList<>();
            itemList.forEach(f -> {
                SgBPhyInResultItemExt ext1 = new SgBPhyInResultItemExt();
                ext1.setQtyIn(f.getQtyIn());
                ext1.setPsCSkuEcode(f.getPsCSkuEcode());
                ext1.setPsCSkuId(f.getPsCSkuId());
                item.add(ext1);
            });
            //        ValueHolderV14 valueHolderV14 = retailService.updateWareHousingResultService(ext, item, loginUser);
            ValueHolderV14 valueHolderV14 = new ValueHolderV14(ResultCode.SUCCESS, "");
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("零售退单更新入库结果服务的返回值是: {}", messageKey), valueHolderV14);
            }
            if (valueHolderV14.getCode() != 0) {
                throw new MqException(valueHolderV14.getMessage());
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("零售退货更新入库结果服务.异常: {}"), Throwables.getStackTraceAsString(ex));
            throw new MqException(ex);
        }
    }
}
