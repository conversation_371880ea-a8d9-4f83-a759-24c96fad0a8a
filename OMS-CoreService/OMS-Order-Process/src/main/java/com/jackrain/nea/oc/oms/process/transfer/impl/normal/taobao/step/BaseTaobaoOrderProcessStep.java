package com.jackrain.nea.oc.oms.process.transfer.impl.normal.taobao.step;

import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.MakeupOrderType;
import com.jackrain.nea.oc.oms.services.IpTaobaoOrderService;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.util.LockOrderType;

import com.jackrain.nea.rpc.PsRpcService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 基础淘宝Order处理阶段
 *
 * @author: 易邵峰
 * @since: 2019-03-03
 * create at : 2019-03-03 23:13
 */
public abstract class BaseTaobaoOrderProcessStep {

    @Autowired
    protected IpTaobaoOrderService ipTaoBaoOrderService;

    @Autowired
    protected OmsOrderService orderService;

    @Autowired
    protected PsRpcService psRpcService;

    protected ChannelType getCurrentChannelType() {
        return ChannelType.TAOBAO;
    }

    protected MakeupOrderType getCurrentMakeupOrderType() {
        return MakeupOrderType.TRANSFER_ORDER;
    }

    protected LockOrderType getCurrentLockOrderType() {
        return LockOrderType.TRANSFER_TAOBAO_ORDER;
    }


}
