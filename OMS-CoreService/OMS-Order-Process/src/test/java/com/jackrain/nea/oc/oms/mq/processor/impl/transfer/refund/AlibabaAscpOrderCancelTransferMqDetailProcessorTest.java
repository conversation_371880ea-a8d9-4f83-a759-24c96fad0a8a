package com.jackrain.nea.oc.oms.mq.processor.impl.transfer.refund;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ip.api.ascp.AlibabaAscpInStorageFeedbackCmd;
import com.jackrain.nea.ip.api.ascp.AlibabaAscpOrderCancelFeedbackCmd;
import com.jackrain.nea.ip.api.ascp.AlibabaAscpOutOfStockCallbackCmd;
import com.jackrain.nea.ip.api.ascp.AlibabaAscpShippingBackCmd;
import com.jackrain.nea.ip.model.ascp.ConsignOrderCancelFeedbackModel;
import com.jackrain.nea.ip.model.ascp.ConsignOrderOutOfStockCallbackModel;
import com.jackrain.nea.ip.model.ascp.ConsignOrderShipModel;
import com.jackrain.nea.ip.model.ascp.InStorageFeedbackModel;
import com.jackrain.nea.oc.oms.mapper.IpBAlibabaAscpOrderRefundItemMapper;
import com.jackrain.nea.oc.oms.mapper.IpBAlibabaAscpOrderRefundMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
import com.jackrain.nea.oc.oms.model.table.IpBAlibabaAscpOrderRefund;
import com.jackrain.nea.oc.oms.model.table.IpBAlibabaAscpOrderRefundItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.process.MultiThreadOrderProcessor;
import com.jackrain.nea.oc.oms.step.ProcessStepResultList;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Profile;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;


@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest()
@Profile(value = "dev")
public class AlibabaAscpOrderCancelTransferMqDetailProcessorTest {

    /**
     * 销售订单发货回传接口
     */
    @Reference(version = "1.4.0", group = "ip")
    private AlibabaAscpShippingBackCmd alibabaAscpShippingBackCmd;

    /**
     * 猫超销售退货入库回传接口
     */
    @Reference(version = "1.4.0", group = "ip")
    private AlibabaAscpInStorageFeedbackCmd ascpInStorageFeedbackCmd;

    /**
     * 猫超销售订单取消发货回告接口
     */
    @Reference(version = "1.4.0", group = "ip")
    private AlibabaAscpOrderCancelFeedbackCmd orderCancelFeedbackCmd;

    /**
     * 猫超销售订单缺货回告
     */
    @Reference(version = "1.4.0", group = "ip")
    private AlibabaAscpOutOfStockCallbackCmd ascpOutOfStockCallbackCmd;

    @Autowired
    private AlibabaAscpOrderCancelTransferMqDetailProcessor cancelTransferMqDetailProcessor;
    @Autowired
    protected MultiThreadOrderProcessor threadOrderProcessor;
    @Autowired
    private IpBAlibabaAscpOrderRefundMapper orderRefundMapper;
    @Autowired
    private IpBAlibabaAscpOrderRefundItemMapper orderRefundItemMapper;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private PsRpcService psRpcService;

    @Test
    public void testAlibabaAscpShippingBackCmd() throws IOException {
        ValueHolderV14 vh;
        try {
//            AlibabaAscpShippingBackCmd alibabaAscpShippingBackCmd = (AlibabaAscpShippingBackCmd) ReferenceUtil.refer(ApplicationContextHandle.getApplicationContext(), AlibabaAscpShippingBackCmd.class.getName(),
//                    "ip", "1.4.0");
            ConsignOrderShipModel model = new ConsignOrderShipModel();
            model.setSupplierId("");
            model.setBizOrderCode("");
            model.setOutBizId("");
//            model.setInstorageTime(new Date());
            model.setBizOrderCode("");
            model.setBizOrderCode("");

            vh = alibabaAscpShippingBackCmd.shippingBack(model, null);
            JSONObject result = JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.PrettyFormat));
            //记录日志信息。Finish 标记结束
//            if (log.isDebugEnabled()) {
//                log.debug("Finish BillCopyCtrl.billcopy. Return Result=" + vh.toJSONObject());
//            }
            System.out.println(result);
        } catch (NDSException e) {
            vh = new ValueHolderV14();
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            System.out.println("错误日志为：" + vh.toJSONObject());

        }
    }

    @Test
    public void testAlibabaAscpInStorageFeedbackCmd() throws IOException {
        ValueHolderV14 vh;
        try {
//            AlibabaAscpInStorageFeedbackCmd ascpInStorageFeedbackCmd = (AlibabaAscpInStorageFeedbackCmd) ReferenceUtil.refer(ApplicationContextHandle.getApplicationContext(), AlibabaAscpInStorageFeedbackCmd.class.getName(),
//                    "ip", "1.4.0");
            InStorageFeedbackModel model = new InStorageFeedbackModel();
            model.setSupplierId("11121");
            model.setBizOrderCode("LP991");
            model.setOutBizId("ERP0099");
            model.setInstorageTime(new Date());
            model.setTmsOrderCode("10000999");
            model.setTmsServiceCode("SF");
            model.setStoreCode("t_0001");

            List<InStorageFeedbackModel.Orderitems> orderitemsList = new ArrayList<>();
            InStorageFeedbackModel.Orderitems orderitem = new InStorageFeedbackModel.Orderitems();
            orderitem.setSubOrderCode("8889999");
            orderitem.setScItemId("12222");
            orderitem.setActualReceivedQuantity(1L);
            orderitem.setActualLackQuantity(0L);
            orderitemsList.add(orderitem);

            List<InStorageFeedbackModel.Instoragedetails> instoragedetailList = new ArrayList<>();
            InStorageFeedbackModel.Instoragedetails instoragedetails = new InStorageFeedbackModel.Instoragedetails();
            instoragedetails.setReceivedQuantity(3l);
            // 库存类型:101=残次品;1=正品
            instoragedetails.setStorageType("101");
            instoragedetailList.add(instoragedetails);
            orderitem.setInstorageDetails(instoragedetailList);

            model.setOrderItems(orderitemsList);


            InStorageFeedbackModel.Receiverinfo receiverinfo = new InStorageFeedbackModel.Receiverinfo();
            receiverinfo.setReceiverPhone("0571-1000");
            receiverinfo.setReceiverMobile("13800000000");
            receiverinfo.setReceiverName("老三");
            receiverinfo.setReceiverAddress("文一西路969");
            receiverinfo.setReceiveTown("五常");
            receiverinfo.setReceiverArea("余杭");
            receiverinfo.setReceiverCity("杭州");
            receiverinfo.setReceiverProvince("浙江");
            receiverinfo.setReceiverCountry("中国");
            receiverinfo.setReceiverZipCode("310000");
            model.setReceiverInfo(receiverinfo);

            InStorageFeedbackModel.Senderinfo senderinfo = new InStorageFeedbackModel.Senderinfo();
            senderinfo.setSenderPhone("0571-2000");
            senderinfo.setSenderMobile("13300000000");
            senderinfo.setSenderName("老四");
            senderinfo.setSenderAddress("文一西路969");
            senderinfo.setSenderTown("五常");
            senderinfo.setSenderArea("余杭");
            senderinfo.setSenderCity("杭州");
            senderinfo.setSenderProvince("浙江");
            senderinfo.setSenderCountry("中国");
            senderinfo.setSenderZipCode("310000");
            model.setSenderInfo(senderinfo);


            vh = ascpInStorageFeedbackCmd.inStorageFeedback(model, "阿飞");
            JSONObject result = JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.PrettyFormat));
            //记录日志信息。Finish 标记结束
//            if (log.isDebugEnabled()) {
//                log.debug("Finish BillCopyCtrl.billcopy. Return Result=" + vh.toJSONObject());
//            }
            System.out.println(result);
        } catch (NDSException e) {
            vh = new ValueHolderV14();
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            System.out.println("错误日志为：" + vh.toJSONObject());

        }
    }

    @Test
    public void testAlibabaAscpInStorageFeedbackCmd123() throws IOException {
        IpBAlibabaAscpOrderRefund ipBAlibabaAscpOrderRefund = orderRefundMapper.selectAlibabaAscpOrderRefundByBizOrderCode("LP00410026384540");
        List<IpBAlibabaAscpOrderRefundItem> orderRefundItems = orderRefundItemMapper.selectAlibabaOrderRefundItemList(ipBAlibabaAscpOrderRefund.getId());
        List<OcBOrder> ocBOrders = ocBOrderMapper.selectList(new QueryWrapper<OcBOrder>().eq("tid", ipBAlibabaAscpOrderRefund.getForwardOrderCode()));
        OcBOrder ocBOrder = null;
        if (CollectionUtils.isNotEmpty(ocBOrders)) {
            ocBOrder = ocBOrders.get(0);
        }
        ValueHolderV14 vh;
        try {

            InStorageFeedbackModel model = new InStorageFeedbackModel();
            model.setSupplierId(ipBAlibabaAscpOrderRefund.getSupplierId());
            model.setBizOrderCode(ipBAlibabaAscpOrderRefund.getBizOrderCode());
            model.setOutBizId(ipBAlibabaAscpOrderRefund.getOutBizId());
            model.setInstorageTime(new Date());
            model.setTmsOrderCode(ipBAlibabaAscpOrderRefund.getTmsOrderCode());
            model.setTmsServiceCode(ipBAlibabaAscpOrderRefund.getTmsServiceCode());
            model.setStoreCode(ipBAlibabaAscpOrderRefund.getStoreCode());

            List<InStorageFeedbackModel.Orderitems> orderitemsList = new ArrayList<>();
            for (IpBAlibabaAscpOrderRefundItem item : orderRefundItems) {
                InStorageFeedbackModel.Orderitems orderitem = new InStorageFeedbackModel.Orderitems();
                orderitem.setSubOrderCode(item.getSubOrderCode());
                orderitem.setScItemId(item.getScItemId());
                long planReturnQuantity = StringUtils.isBlank(item.getPlanReturnQuantity()) ? 0l : Long.valueOf(item.getPlanReturnQuantity());
                // 货品实际收货总数量
                orderitem.setActualReceivedQuantity(planReturnQuantity);
                // 货品未收货总数量
                orderitem.setActualLackQuantity(0L);
                orderitemsList.add(orderitem);

                List<InStorageFeedbackModel.Instoragedetails> instoragedetailList = new ArrayList<>();
                InStorageFeedbackModel.Instoragedetails instoragedetails = new InStorageFeedbackModel.Instoragedetails();
                instoragedetails.setReceivedQuantity(planReturnQuantity);
                // 库存类型:101=残次品;1=正品
                instoragedetails.setStorageType("101");
                instoragedetailList.add(instoragedetails);
                orderitem.setInstorageDetails(instoragedetailList);
            }

            model.setOrderItems(orderitemsList);

//            //店铺id
            Long cpCShopId = ocBOrder.getCpCShopId();
            CpShop cpShop = cpRpcService.selectShopById(cpCShopId);
            // 商家 信息
            InStorageFeedbackModel.Receiverinfo receiverinfo = new InStorageFeedbackModel.Receiverinfo();
            receiverinfo.setReceiverPhone(cpShop.getSellerPhone());
            receiverinfo.setReceiverMobile(cpShop.getSellerPhone());
            receiverinfo.setReceiverName(cpShop.getSellerName());
            receiverinfo.setReceiverAddress(cpShop.getSellerAddress());
            String receiveTown = org.apache.commons.lang3.StringUtils.isBlank(ipBAlibabaAscpOrderRefund.getReceiveTown()) ? "无" : ipBAlibabaAscpOrderRefund.getReceiveTown();
            receiverinfo.setReceiveTown(receiveTown);
            receiverinfo.setReceiverArea(cpShop.getSellerArea());
            receiverinfo.setReceiverCity(cpShop.getSellerCity());
            receiverinfo.setReceiverProvince(cpShop.getSellerProvince());
            String receiveCountry = org.apache.commons.lang3.StringUtils.isBlank(ipBAlibabaAscpOrderRefund.getReceiverCountry()) ? "中国" : ipBAlibabaAscpOrderRefund.getReceiverCountry();
            receiverinfo.setReceiverCountry(receiveCountry);
            // 邮编
            String sellerZip = StringUtils.isBlank(cpShop.getSellerZip()) ? "无" : cpShop.getSellerZip();
            receiverinfo.setReceiverZipCode(sellerZip);
            model.setReceiverInfo(receiverinfo);

            // 消费者 信息
            InStorageFeedbackModel.Senderinfo senderinfo = new InStorageFeedbackModel.Senderinfo();
            senderinfo.setSenderPhone(ipBAlibabaAscpOrderRefund.getReceiverPhone());
            senderinfo.setSenderMobile(ipBAlibabaAscpOrderRefund.getReceiverMobile());
            senderinfo.setSenderName(ipBAlibabaAscpOrderRefund.getReceiverName());
            senderinfo.setSenderAddress(ipBAlibabaAscpOrderRefund.getSenderAddress());
            // 收件方镇
            String senderTown = org.apache.commons.lang3.StringUtils.isBlank(ipBAlibabaAscpOrderRefund.getSenderTown()) ? "无" : ipBAlibabaAscpOrderRefund.getSenderTown();
            senderinfo.setSenderTown(senderTown);
            senderinfo.setSenderArea(ipBAlibabaAscpOrderRefund.getReceiverArea());
            senderinfo.setSenderCity(ipBAlibabaAscpOrderRefund.getSenderCity());
            senderinfo.setSenderProvince(ipBAlibabaAscpOrderRefund.getSenderProvince());
            String senderCountry = org.apache.commons.lang3.StringUtils.isBlank(ipBAlibabaAscpOrderRefund.getSenderTown()) ? "中国" : ipBAlibabaAscpOrderRefund.getSenderTown();
            senderinfo.setSenderCountry(senderCountry);
            senderinfo.setSenderZipCode(ocBOrder.getReceiverZip());
            model.setSenderInfo(senderinfo);


            vh = ascpInStorageFeedbackCmd.inStorageFeedback(model, ipBAlibabaAscpOrderRefund.getSellerNick());
            JSONObject result = JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.PrettyFormat));
            //记录日志信息。Finish 标记结束
//            if (log.isDebugEnabled()) {
//                log.debug("Finish BillCopyCtrl.billcopy. Return Result=" + vh.toJSONObject());
//            }
            System.out.println(result);
        } catch (NDSException e) {
            vh = new ValueHolderV14();
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            System.out.println("错误日志为：" + vh.toJSONObject());

        }
    }

    @Test
    public void testAlibabaAscpOrderCancelFeedbackCmd() throws IOException {
        ValueHolderV14 vh;
        try {
//            AlibabaAscpOrderCancelFeedbackCmd ascpInStorageFeedbackCmd = (AlibabaAscpOrderCancelFeedbackCmd) ReferenceUtil.refer(ApplicationContextHandle.getApplicationContext(), AlibabaAscpOrderCancelFeedbackCmd.class.getName(),
//                    "ip", "1.4.0");

            ConsignOrderCancelFeedbackModel cancelFeedbackModel = new ConsignOrderCancelFeedbackModel();
            cancelFeedbackModel.setSupplierId("11111");
            cancelFeedbackModel.setBizOrderCode("scp0001");
            cancelFeedbackModel.setBizTime(new Date());
            cancelFeedbackModel.setCancelResult(true);
            cancelFeedbackModel.setCancelReason("已经发出去了");

            vh = orderCancelFeedbackCmd.orderCancelFeedback(cancelFeedbackModel, "阿飞");
            JSONObject result = JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.PrettyFormat));
            //记录日志信息。Finish 标记结束
//            if (log.isDebugEnabled()) {
//                log.debug("Finish BillCopyCtrl.billcopy. Return Result=" + vh.toJSONObject());
//            }
            System.out.println(result);
        } catch (NDSException e) {
            vh = new ValueHolderV14();
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            System.out.println("错误日志为：" + vh.toJSONObject());

        }
    }

    @Test
    public void testAlibabaAscpOutOfStockCallbackCmd() throws IOException {
        ValueHolderV14 vh;
        try {
//            AlibabaAscpOutOfStockCallbackCmd ascpOutOfStockCallbackCmd = (AlibabaAscpOutOfStockCallbackCmd) ReferenceUtil.refer(ApplicationContextHandle.getApplicationContext(), AlibabaAscpOutOfStockCallbackCmd.class.getName(),
//                    "ip", "1.4.0");
            ConsignOrderOutOfStockCallbackModel model = new ConsignOrderOutOfStockCallbackModel();
            model.setSupplierId("11111");
            model.setOutBizId("121333");
            model.setBizOrderCode("p000232");
            model.setOutOfStockReason("没货了");

            List<ConsignOrderOutOfStockCallbackModel.Outofstockitems> outofstockitemsList = new ArrayList<ConsignOrderOutOfStockCallbackModel.Outofstockitems>();
            ConsignOrderOutOfStockCallbackModel.Outofstockitems outofstockitem = new ConsignOrderOutOfStockCallbackModel.Outofstockitems();
            outofstockitem.setSubOrderCode("33222223");
            outofstockitem.setScItemId("333222");
            outofstockitem.setLackQuantity(1L);
            outofstockitemsList.add(outofstockitem);
            model.setOutOfStockItems(outofstockitemsList);


            vh = ascpOutOfStockCallbackCmd.outOfStockCallback(model, "阿飞");
            JSONObject result = JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.PrettyFormat));
            //记录日志信息。Finish 标记结束
//            if (log.isDebugEnabled()) {
//                log.debug("Finish BillCopyCtrl.billcopy. Return Result=" + vh.toJSONObject());
//            }
            System.out.println(result);
        } catch (NDSException e) {
            vh = new ValueHolderV14();
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            System.out.println("错误日志为：" + vh.toJSONObject());
        }
    }

    @Test
    public void start() {
    }

    @Test
    public void checkMqIsCanExecute() {
    }

    @Test
    public void getProcessStepResultList() {
        OperateOrderMqInfo operateOrderMqInfo = new OperateOrderMqInfo();
//        List<String> ids = Arrays.asList("scp0001", "scp0002");
        List<String> ids = Arrays.asList("LP00409332012407");
        String orderIds = ids.stream().collect(Collectors.joining(","));
        operateOrderMqInfo.setOrderIds(orderIds);
        ProcessStepResultList processStepResultList = cancelTransferMqDetailProcessor.getProcessStepResultList(operateOrderMqInfo);
        System.out.println(JSON.toJSONString(processStepResultList));
    }
}