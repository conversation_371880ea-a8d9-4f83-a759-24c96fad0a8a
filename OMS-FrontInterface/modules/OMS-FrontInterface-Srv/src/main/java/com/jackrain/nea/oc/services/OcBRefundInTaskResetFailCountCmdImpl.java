package com.jackrain.nea.oc.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBRefundInTaskResetFailCountCmd;
import com.jackrain.nea.oc.oms.services.OcBRefundInTaskThirdService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @program: r3-oc-oms
 * @author: lijin
 * @description: B2C出库回传中间表重置失败次数
 **/
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBRefundInTaskResetFailCountCmdImpl extends CommandAdapter implements OcBRefundInTaskResetFailCountCmd {
    @Autowired
    private OcBRefundInTaskThirdService service;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        return service.resetFailCount(querySession);
    }
}
