package com.jackrain.nea.oc.oms.process.tobeconfirm.step;

import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.OmsSpiltRuleEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderParam;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.SpiltOrderParam;
import com.jackrain.nea.oc.oms.model.resources.OrderOccupyStatus;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.services.OmsGiftLateHairService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.oc.oms.util.SplitMessageUtil;
import com.jackrain.nea.util.OmsOrderUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;
import java.util.Set;

/**
 * @Author: 黄世新
 * @Date: 2022/8/4 下午3:11
 * @Version 1.0
 */
@Step(order = 115, description = "处理赠品后发")
@Slf4j
public class Step115GiftLateHairService extends BaseTobeConfirmedProcessStep
        implements IOmsOrderProcessStep<OcBOrderRelation> {

    @Autowired
    private OmsGiftLateHairService omsGiftLateHairService;

    @Override
    public ProcessStepResult<OcBOrderRelation> startProcess(OcBOrderRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {

        OcBOrder ocBOrder = orderInfo.getOrderInfo();
        try {
            //toc残次订单跳过
            if (OmsOrderUtil.isToCCcOrder(orderInfo.getOrderInfo())){
                return new ProcessStepResult<>(StepStatus.SUCCESS);
            }

            OcBOrderParam param = new OcBOrderParam();
            param.setOcBOrder(orderInfo.getOrderInfo());
            param.setOrderItemList(orderInfo.getOrderItemList());
            Map<Set<Long>, SpiltOrderParam> setSpiltOrderParamMap = omsGiftLateHairService.giftLateService(param, operateUser);
            if (setSpiltOrderParamMap != null && !setSpiltOrderParamMap.isEmpty()) {
                Map<Integer, Map<Set<Long>, SpiltOrderParam>> spiltRule = orderInfo.getSpiltRule();
                spiltRule.put(OmsSpiltRuleEnum.GIFT_AFTER.getCode(), setSpiltOrderParamMap);
            }
            return new ProcessStepResult<>(StepStatus.SUCCESS);
        } catch (Exception e) {
            log.error(LogUtil.format("处理赠品后发失败:{}", ocBOrder.getId(), "处理赠品后发失败"), Throwables.getStackTraceAsString(e));
            OcBOrder errorOrderInfo = new OcBOrder();
            errorOrderInfo.setId(orderInfo.getOrderId());
            errorOrderInfo.setSysremark(SplitMessageUtil.splitMesssage("处理赠品后发失败"));
            errorOrderInfo.setOccupyStatus(OrderOccupyStatus.STATUS_50);
            omsOrderService.updateOrderInfo(errorOrderInfo);
            omsToBeConfirmedTaskService.updateToBeConfirmedTaskByOrderId(orderInfo.getOrderId());
            return new ProcessStepResult<>(StepStatus.FAILED, "处理赠品后发失败");
        }
    }
}
