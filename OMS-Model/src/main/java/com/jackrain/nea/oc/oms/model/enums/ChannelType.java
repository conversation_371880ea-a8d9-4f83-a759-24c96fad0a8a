package com.jackrain.nea.oc.oms.model.enums;

/**
 * 渠道类型
 *
 * @author: 易邵峰
 * @since: 2019-02-23
 * create at : 2019-02-23 18:29
 */
public enum ChannelType {

    /**
     * 淘宝类型订单
     */
    TAOBAO,

    /**
     * 淘宝分销类型订单
     */
    TAOBAOFX,

    /**
     * 京东类型订单
     */
    JINGDONG,

    /**
     * 苏宁订单
     */
    SUNING,

    /**
     * 唯品会订单
     */
    VIPJITX,

    /**
     * 其他标准订单
     */
    STANDARD,

    /**
     * 通用转单
     */
    STANDPLAT,
    /**
     * 淘宝经销类型订单
     */
    TAOBAOJX,
    /**
     * 猫超直发类型订单
     */
    ALIBABAASCP,
    /**
     * 京东厂直
     */
    JD_DIRECT,
    /**
     * 京东厂直订单
     */
    JINGDONG_DIRECT,
    /**
     * 默认系统渠道（主要用于补偿服务之类）
     */
    DEFAULT;

    public int toInteger() {
        if (this == ChannelType.TAOBAO) {
            return 1;
        } else if (this == ChannelType.JINGDONG) {
            return 2;
        } else if (this == ChannelType.SUNING) {
            return 3;
        } else if (this == ChannelType.VIPJITX) {
            return 4;
        } else if (this == ChannelType.TAOBAOFX) {
            return 5;
        } else if (this == ChannelType.STANDPLAT) {
            return 6;
        } else if (this == ChannelType.ALIBABAASCP) {
            return 7;
        } else if (this == ChannelType.STANDARD) {
            return 99;
        } else if (this == ChannelType.DEFAULT) {
            return -1;
        } else {
            return 0;
        }

    }

}
