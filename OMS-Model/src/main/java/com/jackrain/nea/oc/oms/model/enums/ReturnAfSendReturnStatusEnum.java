package com.jackrain.nea.oc.oms.model.enums;


/**
 * 发货后退款单退款状态枚举
 *
 * @author: 夏继超
 * create at: 2019/9/16
 */
public enum ReturnAfSendReturnStatusEnum {


    /**
     * 是否可用
     */
    TOBEAUDITED("待审核", 0),
    AUDITED("已审核", 1),
    FINANCIALAPPRAISAL("已财审", 2),
    INVALID("已作废", 3);

    String key;
    int val;

    ReturnAfSendReturnStatusEnum(String k, int v) {
        this.key = k;
        this.val = v;
    }

    public String getKey() {
        return key;
    }

    public int getVal() {
        return val;
    }


}


