package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.UpdateOrderInfoCmd;
import com.jackrain.nea.oc.oms.model.relation.UserPermission;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @author: 孙继东
 * @since: 2019-03-12
 * create at : 2019-03-12 9:25
 */

@Api(value = "UpdateOrderInfoCtrl", tags = "物流和仓库修改按钮")
@Slf4j
@RestController
public class UpdateOrderInfoCtrl {

    @Autowired
    private UpdateOrderInfoCmd updateOrderInfoCmd;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    /**
     * 修改物流
     *
     * @param request      获取用户
     * @param ids          订单id集合
     * @param cLogisticsId 物流公司id
     * @return 返回信息
     */
    @ApiOperation(value = "修改物流")
    @PostMapping("/api/cs/oc/oms/v1/updateLogistics")
    public JSONObject updateLogistics(HttpServletRequest request, @RequestParam(value = "ids") List<Long> ids, @RequestParam(value = "cLogisticsId") Long cLogisticsId) {
        User loginUser = r3PrimWebAuthService.getLoginPrimWebUser(request);
        ValueHolderV14 vh = new ValueHolderV14();
        try {

            vh = updateOrderInfoCmd.updateLogistics(ids, cLogisticsId, loginUser);

            //记录日志信息。Finish 标记结束
            return vh.toJSONObject();
        } catch (NDSException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return vh.toJSONObject();

        }
    }

    /**
     * 修改仓库
     *
     * @param request      获取用户信息
     * @param ids          订单id集合
     * @param warehouseId  仓库id
     * @param updateRemark 改仓原因
     * @return 返回信息
     */
    @ApiOperation(value = "修改仓库")
    @PostMapping("/api/cs/oc/oms/v1/updateWarehouse")
    public JSONObject updateWarehouse(HttpServletRequest request,
                                      @RequestParam(value = "ids") List<Long> ids,
                                      @RequestParam(value = "shareStoresId") Long shareStoresId,
                                      @RequestParam(value = "shareStoresEcode") String shareStoresEcode,
                                      @RequestParam(value = "warehouseId") Long warehouseId,
                                      @RequestParam(value = "warehouseEcode") String warehouseEcode,
                                      @RequestParam(value = "updateRemark") String updateRemark) {
        //记录日志信息
        User loginUser = r3PrimWebAuthService.getLoginPrimWebUser(request);
        ValueHolderV14 vh = new ValueHolderV14();
        try {

            vh = updateOrderInfoCmd.updateWarehouse(ids, shareStoresId, shareStoresEcode, warehouseId, warehouseEcode, updateRemark, loginUser);
            return vh.toJSONObject();
        } catch (NDSException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return vh.toJSONObject();

        }
    }
    @ApiOperation(value = "查询库存聚合仓接口")
    @PostMapping("/api/cs/oc/oms/v1/queryOmsShopStorage")
    public JSONObject queryOmsShopStorage(HttpServletRequest request,@RequestBody JSONObject JSONObject) {
         User loginUser = r3PrimWebAuthService.getLoginPrimWebUser(request);
        //User loginUser = SystemUserResource.getRootUser();
        ValueHolderV14 vh = new ValueHolderV14();
        try {
            vh = updateOrderInfoCmd.queryOmsShopStorage(JSONObject, loginUser);
            return vh.toJSONObject();
        } catch (NDSException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return vh.toJSONObject();

        }
    }

    @ApiOperation(value = "查询用户的实体仓接口")
    @PostMapping("/api/cs/oc/oms/v1/queryOmsWarehouse")
    public JSONObject queryOmsWarehouse(HttpServletRequest request,@RequestBody JSONObject JSONObject) {
        User loginUser = r3PrimWebAuthService.getLoginPrimWebUser(request);
        ValueHolderV14 vh = new ValueHolderV14();
        try {
            vh = updateOrderInfoCmd.queryOmsWarehouse(JSONObject, loginUser);
            return vh.toJSONObject();
        } catch (NDSException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return vh.toJSONObject();

        }
    }

    /**
     * 修改物流前检查订单
     *
     * @param ids 订单id集合
     * @return 返回信息
     */
    @ApiOperation(value = "修改物流前检查订单")
    @PostMapping("/api/cs/oc/oms/v1/checkOrderBeforeLogistics")
    public JSONObject checkOrderBeforeLogistics(@RequestParam("ids") List<Long> ids) {
        ValueHolderV14 vh = new ValueHolderV14();
        try {
            vh = updateOrderInfoCmd.checkOrder(ids, 1);
            return vh.toJSONObject();
        } catch (NDSException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return vh.toJSONObject();
        }
    }

    /**
     * 修改仓库前检查订单
     *
     * @param ids 订单id集合
     * @return 返回信息
     */
    @ApiOperation(value = "修改仓库前检查订单")
    @PostMapping("/api/cs/oc/oms/v1/checkOrderBeforeWarehouse")
    public JSONObject checkOrderBeforeWarehouse(@RequestParam("ids") List<Long> ids) {
        ValueHolderV14 vh = new ValueHolderV14();
        try {
            vh = updateOrderInfoCmd.checkOrder(ids, 2);
            return vh.toJSONObject();
        } catch (NDSException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return vh.toJSONObject();
        }
    }


    @ApiOperation(value = "筛选列表")
    @PostMapping("/api/cs/oc/oms/v1/getQueryList")
    public JSONObject getQueryList(HttpServletRequest request, @RequestBody JSONObject jsn) {

        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);

        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject = updateOrderInfoCmd.queryList(user, jsn);
            return jsonObject;
        } catch (NDSException e) {
            jsonObject.put("code", ResultCode.FAIL);
            jsonObject.put("message", Resources.getMessage(e.getMessage()));
            return jsonObject;
        }
    }

    @ApiOperation(value = "重新分配物流")
    @RequestMapping(value = "/api/cs/oc/oms/v1/reallocateLogistics", method = RequestMethod.POST)
    public JSONObject reallocateLogistics(HttpServletRequest request, @RequestParam(value = "param") String param) {

        ValueHolderV14 vh;
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        UserPermission usrPem = UserPermissionCtrlHelper.currentUserPermission(user, "OC_B_ORDER");
        if (usrPem == null) {
            vh = new ValueHolderV14();
            vh.setMessage("未获取到用户权限");
            vh.setCode(ResultCode.FAIL);
            return vh.toJSONObject();
        }
        vh = updateOrderInfoCmd.reallocateLogistics(param, user, usrPem);
        return vh.toJSONObject();
    }

    @ApiOperation(value = "重新分配仓库")
    @RequestMapping(value = "/api/cs/oc/oms/v1/reallocateWarehouse", method = RequestMethod.POST)
    public JSONObject reallocateWarehouse(HttpServletRequest request, @RequestParam(value = "param") String param) {
        ValueHolderV14 vh;
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        UserPermission usrPem = UserPermissionCtrlHelper.currentUserPermission(user, "OC_B_ORDER");
        if (usrPem == null) {
            vh = new ValueHolderV14<>();
            vh.setMessage("未获取到用户权限");
            vh.setCode(ResultCode.FAIL);
            return vh.toJSONObject();
        }
        vh = updateOrderInfoCmd.reallocateWarehouse(param, user, null);
        return vh.toJSONObject();
    }

    @ApiOperation(value = "重新分物流")
    @RequestMapping(value = "/api/cs/oc/oms/v1/reDistributionLogistics", method = RequestMethod.POST)
    public JSONObject reDistributionLogistics(HttpServletRequest request,@RequestParam("ids") List<Long> ids) {
        ValueHolderV14 vh;
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        UserPermission usrPem = UserPermissionCtrlHelper.currentUserPermission(user, "OC_B_ORDER");
        if (usrPem == null) {
            vh = new ValueHolderV14<>();
            vh.setMessage("未获取到用户权限");
            vh.setCode(ResultCode.FAIL);
            return vh.toJSONObject();
        }
        vh = updateOrderInfoCmd.reDistributionLogistics(ids, user);
        return vh.toJSONObject();
    }
}
