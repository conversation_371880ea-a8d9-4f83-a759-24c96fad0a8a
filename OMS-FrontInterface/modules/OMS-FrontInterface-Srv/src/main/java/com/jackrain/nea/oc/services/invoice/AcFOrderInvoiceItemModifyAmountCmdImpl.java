package com.jackrain.nea.oc.services.invoice;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.ac.service.OrderInvoiceService;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.invoice.AcFOrderInvoiceItemModifyAmountCmd;
import com.jackrain.nea.st.model.request.StCProLogisticStrategyRequest;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.JsonUtils;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName AcFOrderInvoiceItemModifyAmountCmdImpl
 * @Description
 * @Date 2022/9/24 上午10:34
 * @Created by wuhang
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class AcFOrderInvoiceItemModifyAmountCmdImpl extends CommandAdapter implements AcFOrderInvoiceItemModifyAmountCmd {

    @Autowired
    private OrderInvoiceService service;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        DefaultWebEvent event = session.getEvent();
        List<String> nullKeyList = new ArrayList<>();
        JSONObject param = JsonUtils.filterNull((JSONObject) event.getParameterValue("param"), nullKeyList);
        log.info(LogUtil.format("---| ##开票明细修改金额##入参：{}"), param);
        if (param != null) {
            Long id = param.getLong("objid");
            JSONObject fixColumn = param.getJSONObject("fixcolumn");
            StCProLogisticStrategyRequest stCProLogisticStrategyRequest = JsonUtils.jsonParseClass(fixColumn, StCProLogisticStrategyRequest.class);
            User user = session.getUser();
            if (fixColumn != null && id != null) {
                if (id != -1) {
                    log.info("---| 修改金额");
                    return service.modifyAmount(param,user);
                } else {
                    throw new NDSException("当前记录不允许新增！");
                }
            }
        }
        throw new NDSException("当前记录已不存在！");
    }
}
