package com.jackrain.nea.oc.oms.process.transfer.impl.normal.taobaojx;

import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.MakeupOrderType;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoJxOrderRelation;
import com.jackrain.nea.oc.oms.process.AbstractOrderProcess;
import com.jackrain.nea.oc.oms.util.LockOrderType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description 淘宝经销订单中间表转成全渠道订单
 **/
@Component
public class TaobaoJxTransferOrderProcessImpl extends AbstractOrderProcess<IpTaobaoJxOrderRelation> {
    @Override
    protected String getChildPackageName() {
        return "taobaojx";
    }

    @Override
    protected long getProcessOrderId(IpTaobaoJxOrderRelation orderInfo) {
        return orderInfo.getOrderId();
    }

    @Override
    protected String getProcessOrderNo(IpTaobaoJxOrderRelation orderInfo) {
        return orderInfo.getOrderNo();
    }

    @Override
    protected LockOrderType getCurrentLockOrderType() {
        return LockOrderType.TRANSFER_TAOBAO_JX_ORDER;
    }

    @Override
    protected ChannelType getCurrentChannelType() {
        return ChannelType.TAOBAO;
    }

    @Override
    protected MakeupOrderType getCurrentMakeupOrderType() {
        return MakeupOrderType.DO_NOT_MAKEUP;
    }
}
