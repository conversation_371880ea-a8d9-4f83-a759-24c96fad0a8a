package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.mq.core.DefaultProducerSend;
import com.burgeon.mq.model.MqSendResult;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsShareOutRequest;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.config.OmsModifyConfig;
import com.jackrain.nea.oc.oms.constant.Mq5Constants;
import com.jackrain.nea.oc.oms.mapper.IpBJitxDeliveryRecordMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OcOrderCheckBoxEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcModifyWarehouseModel;
import com.jackrain.nea.oc.oms.model.table.IpBJitxDeliveryRecord;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.nums.OmsParamConstant;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
import com.jackrain.nea.oc.oms.services.task.OcBJitxDealerOrderTaskService;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sg.service.SgOccupiedInventoryService;
import com.jackrain.nea.st.model.table.StCVipcomJitxWarehouse;
import com.jackrain.nea.st.service.VipcomJitxWarehouseService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.MD5Util;
import com.jackrain.nea.util.ThreadLocalUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.UserImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

/**
 * @Desc : 修改仓库
 * <AUTHOR> xiWen
 * @Date : 2022/10/5
 */
@Slf4j
@Component
public class ModifyWarehouseService {

//    @Autowired
//    private R3MqSendHelper r3MqSendHelper;

    @Autowired
    private DefaultProducerSend defaultProducerSend;

    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    @Autowired
    private OmsModifyConfig omsModifyConfig;

    @Autowired
    private OcBOrderMapper orderMapper;

    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;

    @Autowired
    private IpBJitxDeliveryRecordMapper deliveryRecordMapper;

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private SgRpcService sgRpcService;

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Autowired
    private OmsOccupyTaskService omsOccupyTaskService;

    @Autowired
    private VipcomJitxWarehouseService jitxWarehouseService;

    @Autowired
    private OcbCancelOrderMergeService cancelOrderMergeService;

    @Autowired
    private SgOccupiedInventoryService sgOccupiedInventoryService;

    @Autowired
    private OcBJitxModifyWarehouseLogService ocBJitxModifyWarehouseLogService;
    @Autowired
    private ModifyWarehouseService modifyWarehouseService;


    /**
     * producer
     * modify warehouse
     *
     * @param keys         order ids
     * @param warehouseId  target warehouse
     * @param modifyReason modify remark
     * @param usr          User
     * @return ValueHolderV14
     */
    public ValueHolderV14 modifyWarehouse(List<Long> keys, Long warehouseId, String modifyReason, User usr) {
        ValueHolderV14 vh;
        try {

            // 1. check
            AssertUtil.notEmpty(keys, "请选择需要修改的单据");
            AssertUtil.notNull(warehouseId, "请选择目标仓库");
            // 2. query check
            CpCPhyWarehouse warehouse = getWarehouse(warehouseId);
            AssertUtil.notNull(warehouse, "当前发货仓库无效，请重新录入");
            // 3. send message
            ValueHolderV14 sendResult = sendMessageProcessor(keys, warehouse, modifyReason, usr);
            return sendResult;
        } catch (Exception e) {
            vh = new ValueHolderV14(ResultCode.FAIL, "");
            if (e instanceof NDSException) {
                vh.setMessage(optimizeExpMsg.apply(e));
            } else {
                vh.setMessage("修改仓库异常");
                log.error(LogUtil.format("modifyWarehouse.exp:{}", "ModifyWarehouseService"), Throwables.getStackTraceAsString(e));
            }
        }
        return vh;
    }

    /**
     * consume
     * modify warehouse
     *
     * @param message message
     */
    public void modifyWarehouseHandler(String messageBody, String messageKey) {
        /* message parser */
        OcModifyWarehouseModel model = parseMessage(messageBody, messageKey);
        if (model == null) {
            return;
        }
        // modify
        modifyProcessing(model);
    }

    /**
     * message parser
     *
     * @param message Message
     * @return OcModifyWarehouseModel
     */
    private OcModifyWarehouseModel parseMessage(String messageBody, String messageKey) {
        try {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("modifyWarehouse.msgId.{},msgKey:{}", "ModifyWarehouseService"), messageKey, messageKey);
            }
            OcModifyWarehouseModel ocModifyWarehouseModel = JSON.parseObject(messageBody, OcModifyWarehouseModel.class);
            return ocModifyWarehouseModel;
        } catch (Exception e) {
            log.error(LogUtil.format("modifyWarehouse.msgId.{},msgKey:{},exp:{}",
                    "ModifyWarehouseService"), messageKey, messageKey, Throwables.getStackTraceAsString(e));
        }
        return null;
    }

    /**
     * modify warehouse processing
     *
     * @param model OcModifyWarehouseModel
     */
    private void modifyProcessing(OcModifyWarehouseModel model) {
        User user = model.getUser();
        Long id = model.getOrderId();
        Long warehouseId = model.getWarehouseId();
        String bilNo = null;
        RedisReentrantLock lock = null;
        try {
            AssertUtil.notNull(user, "用户信息为空");
            AssertUtil.isTrue(warehouseId != null && model.getWarehouseCode() != null && model.getWarehouseName() != null, "目标仓库信息缺失");
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("modifyWarehouse.{},warehouse:{},user:{}", "ModifyWarehouseService"),
                        id, model.getWarehouseId(), user.getEname());
            }
            // lock
            lock = lockBil(id);
            // query, check
            CpCPhyWarehouse sourceWarehouse = new CpCPhyWarehouse();
            OcBOrder order = queryAndCheckOrder(id, warehouseId, sourceWarehouse);
            bilNo = order.getBillNo();
            // jit x wms type
            jitXWmsTypeHandle(order, model.getWarehouseWmsType());
            // release and insert task
            modifyWarehouseService.releaseStockAddOccupyTask(order, model, user);
            // log
            addOrderLog(model, order, sourceWarehouse, user);
            // vip jit x
            vipJitXHandler(warehouseId, order, sourceWarehouse, user);
        } catch (Exception e) {
            String expMessage = optimizeExpMsg.apply(e);
            if (e instanceof NDSException) {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("modifyWarehouse.{}, fail:{}", "ModifyWarehouseService"),
                            id, expMessage);
                }
            } else {
                log.error(LogUtil.format("modifyWarehouse.{},exp:{}", "ModifyWarehouseService"),
                        id, Throwables.getStackTraceAsString(e));
            }
            addOrderLog(id, bilNo, expMessage, user);
        } finally {
            if (lock != null) {
                lock.unlock();
            }
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("modifyWarehouse.{}, end", "ModifyWarehouseService"), id);
            }
        }
    }

    /**
     * send message handler
     *
     * @param keys      source order ids
     * @param warehouse target warehouse
     * @param remark    update reason
     * @param usr       User
     * @return handle result
     */
    private ValueHolderV14 sendMessageProcessor(List<Long> keys, CpCPhyWarehouse warehouse, String remark, User usr) {

        OcModifyWarehouseModel model = new OcModifyWarehouseModel();
        model.setTopic(Mq5Constants.TOPIC_R3_OC_OMS_MODIFY_WH_LG);
        model.setTag(Mq5Constants.TAG_R3_OC_OMS_MODIFY_WH_LG);
        model.setModifyReason(remark);
        model.setWarehouseId(warehouse.getId());
        model.setWarehouseCode(warehouse.getEcode());
        model.setWarehouseName(warehouse.getEname());
        model.setWarehouseWmsType(warehouse.getWmsType());
        model.setUser((UserImpl) usr);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("sendMessageProcessor.{}", "ModifyWarehouseService"), JSON.toJSONString(model));
        }
        ValueHolderV14 vh = new ValueHolderV14(ResultCode.SUCCESS, "");
        int exp = 0;
        int success = 0;
        int size = keys.size();
        List<Long> expList = new ArrayList<>();
        for (Long key : keys) {
            model.setOrderId(key);
            boolean isSendSuccess = sendMessage(model);
            if (isSendSuccess) {
                success++;
            } else {
                exp++;
                expList.add(key);
            }
        }
        if (exp == size) {
            vh.setCode(ResultCode.FAIL);
            vh.setData(expList);
            vh.setMessage("消息发送失败");
            return vh;
        }
        if (success == size) {
            vh.setMessage("消息发送成功,请稍后查看结果");
            return vh;
        }
        vh.setMessage("消息发送,成功:" + success + "条, 失败:" + exp + "条");
        vh.setData(expList);
        return vh;
    }

    /**
     * mq发送
     *
     * @param model message model
     * @return send result
     */
    private boolean sendMessage(OcModifyWarehouseModel model) {
        try {
            Long id = model.getOrderId();
//            String msgId = r3MqSendHelper.sendMessage(JSON.toJSONString(model), model.getTopic(), model.getTag());
            MqSendResult result = defaultProducerSend.sendTopic(model.getTopic(), model.getTag(), JSON.toJSONString(model), null);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("modifyWarehouse.{}, msgId:{}", "ModifyWarehouseService", id), id, result.getMessageId());
            }
            return true;
        } catch (Exception e) {
            log.error(LogUtil.format("sendMessage.Producer.SendMQ.SendException: {}"
                    , model.getTopic() + ":" + model.getTag()), Throwables.getStackTraceAsString(e));
        }
        return false;
    }

    /**
     * vip
     *
     * @param targetId target warehouse
     * @param order    source order
     * @param user     user
     */
    private void vipJitXHandler(Long targetId, OcBOrder order, CpCPhyWarehouse origWarehouse, User user) {
        if (!PlatFormEnum.VIP_JITX.getCode().equals(order.getPlatform())) {
            return;
        }
        Integer status = order.getOrderStatus();
        boolean isUnAuditOrOutStock = OcOrderCheckBoxEnum.CHECKBOX_UNCONFIRMED.integerVal().equals(status)
                || OcOrderCheckBoxEnum.CHECKBOX_OUT_OF_STOCK.integerVal().equals(status);
        if (!isUnAuditOrOutStock) {
            return;
        }
        String jitWarehouseEcode;
        // 若是JITX平台订单调用【创建改仓申请单接口】
        StCVipcomJitxWarehouse jitxWarehouse = jitxWarehouseService.queryJitxCapacity(order.getCpCShopId(),
                targetId, null);
        AssertUtil.notNull(jitxWarehouse, "未查询到唯品会对照仓库数据");
        log.debug("JITX修改仓库，根据实体仓ID和店铺ID查询仓库编码：" + jitxWarehouse.getCpCPhyWarehouseEcode());
        jitWarehouseEcode = jitxWarehouse.getVipcomWarehouseEcode();
        if (!YesNoEnum.Y.getVal().equals(order.getIsStoreDelivery())) {
            jitWarehouseEcode = jitxWarehouse.getVipcomUnshopWarehouseEcode();
        }
        AssertUtil.isTrue(StringUtils.isNotEmpty(jitWarehouseEcode), "实体仓对应的【唯品会仓库编码】不能为空");
        // 设定redisJITX修改仓库标识，更新数据库JITX修改仓库标识
//        setJITXRedisChangeWarehouseFlag(order);
        // 创建JITX订单改仓日志表
        String preName = StringUtils.isEmpty(origWarehouse.getEname()) ? "空" : origWarehouse.getEname();
        ValueHolderV14 valueHolderV14 = ocBJitxModifyWarehouseLogService.createByOrder(order, false,
                origWarehouse.getId(), origWarehouse.getEcode(), preName, jitWarehouseEcode, user);
        String logMessage = "创建JITX订单改仓中间表失败";
        if (valueHolderV14.isOK()) {
            logMessage = "创建JITX订单改仓中间表成功";
        }
        omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(),
                OrderLogTypeEnum.JITX_ORDER_UPDATE_WAREHOUSE.getKey(),
                logMessage + valueHolderV14.getMessage(), "", valueHolderV14.getMessage(), user);
    }

    /**
     * release stock , insert task, add log
     *
     * @param order OcBOrder
     * @param model OcModifyWarehouseModel
     * @param user  User
     */
    @Transactional(rollbackFor = Exception.class)
    public void releaseStockAddOccupyTask(OcBOrder order, OcModifyWarehouseModel model, User user) {
        List<OcBOrderItem> items = ocBOrderItemMapper.selectUnSuccessRefund(order.getId());
        //调用指定商品释放库存服务
        Integer status = order.getOrderStatus();

        OcBOrder ocBOrder = new OcBOrder();
        ocBOrder.setId(order.getId());
        ocBOrder.setCpCPhyWarehouseId(model.getWarehouseId());
        ocBOrder.setOrderStatus(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger());
        ocBOrder.setCpCPhyWarehouseEcode(model.getWarehouseCode());
        ocBOrder.setCpCPhyWarehouseEname(model.getWarehouseName());

        order.setCpCPhyWarehouseId(model.getWarehouseId());
        order.setCpCPhyWarehouseEcode(model.getWarehouseCode());
        order.setCpCPhyWarehouseEname(model.getWarehouseName());
        // 需要传有地址信息的订单参数
        MD5Util.encryptOrderInfo4Merge(order);
        ocBOrder.setOrderEncryptionCode(order.getOrderEncryptionCode());
        //1、若【零售发货单】的【来源平台】=POS时，则对订单打标【o2o】；
        //2、若【零售发货单】的【来源平台】非POS时，若订单占用的【发货仓库】的对应【仓库类型】=门店，则对订单打标【o2o】；
        if (sgOccupiedInventoryService.isO2OOrder(order)) {
            ocBOrder.setIsO2oOrder(OcBOrderConst.IS_STATUS_IY);
        } else {
            ocBOrder.setIsO2oOrder(OcBOrderConst.IS_STATUS_IN);
        }
        orderMapper.updateById(ocBOrder);
        if (OmsOrderStatus.UNCONFIRMED.toInteger().equals(status)) {
            SgOmsShareOutRequest request = cancelOrderMergeService.buildSgOmsShareOutRequest(order, items, user);
            ValueHolderV14 v14 = sgRpcService.voidSgOmsShareOut(request, order, items);
            AssertUtil.assertException(!v14.isOK(), "释放库存失败");
            omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(),
                    OrderLogTypeEnum.WAREHOUSE_UPDATE.getKey(), "释放库存成功", "", "", user);
            //更新零售发货单占用状态
            updateOrderStatus(order, user);
        }

        //卡单不释放
        if (!YesNoEnum.Y.getVal().equals(order.getIsDetention())) {
            //加入占单表
            omsOccupyTaskService.addOcBOccupyTask(order, null, 0);
        }
        
    }

    /**
     * 判断是否是YY经销商仓库
     *
     * @param order            OcBOrder
     * @param warehouseWmsType warehouseWmsType
     */
    private void jitXWmsTypeHandle(OcBOrder order, String warehouseWmsType) {
        boolean jitXDealerYYWarehouse = OcBJitxDealerOrderTaskService.YY_WMS_TYPE.equals(warehouseWmsType);
        if (jitXDealerYYWarehouse) {
            // 判断YY寻仓结果表中已存在记录，则要排除该YY仓库
            List<IpBJitxDeliveryRecord> items = deliveryRecordMapper.selectList(new LambdaQueryWrapper<IpBJitxDeliveryRecord>()
                    .eq(IpBJitxDeliveryRecord::getTid, order.getTid()));
            AssertUtil.assertException(CollectionUtils.isNotEmpty(items), "YY寻仓结果表中已存在记录,不允许修改为当前仓库");
        }
    }

    /**
     * query order
     *
     * @param id          order id
     * @param warehouseId target warehouse id
     * @return source order
     */
    private OcBOrder queryAndCheckOrder(Long id, Long warehouseId, CpCPhyWarehouse sourceWarehouse) {
        OcBOrder order = orderMapper.selectById(id);
        AssertUtil.notNull(order, "未查询到单据信息");

        Long sourceWarehouseId = order.getCpCPhyWarehouseId();
        AssertUtil.assertException(warehouseId.equals(sourceWarehouseId), "发货仓库与目标仓库一致，无需修改");

        Integer status = order.getOrderStatus();
        AssertUtil.notNull(status, "单据状态为空");

        boolean isValidStatus = OcOrderCheckBoxEnum.CHECKBOX_UNCONFIRMED.integerVal().equals(status)
                || OcOrderCheckBoxEnum.CHECKBOX_OUT_OF_STOCK.integerVal().equals(status);
        AssertUtil.assertException(!isValidStatus, "当前单据状态,不允许修改发货仓库");
        sourceWarehouse.setId(order.getCpCPhyWarehouseId());
        sourceWarehouse.setEcode(order.getCpCPhyWarehouseEcode());
        sourceWarehouse.setEname(order.getCpCPhyWarehouseEname());
        return order;
    }

    /**
     * lock bill
     *
     * @param id order id
     * @return RedisReentrantLock
     * @throws InterruptedException InterruptedException
     */
    private RedisReentrantLock lockBil(Long id) throws InterruptedException {
        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(id);
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        boolean isLock = redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS);
        AssertUtil.isTrue(isLock, "单据处于锁定状态,请稍后尝试");
        return redisLock;
    }

    /**
     * update order
     *
     * @param ocBOrder  OcBOrder
     * @param loginUser User
     */
    private void updateOrderStatus(OcBOrder ocBOrder, User loginUser) {
        OcBOrder updateOrder = new OcBOrder();
        updateOrder.setId(ocBOrder.getId());
        updateOrder.setOrderStatus(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger());
        updateOrder.setOccupyStatus(OmsParamConstant.INT_ZERO);
        updateOrder.setSysremark("");
        updateOrder.setModifierename(loginUser.getName());
        updateOrder.setModifieddate(new Date());
        // 订单合单加密信息，需要传有地址信息的订单参数
        MD5Util.encryptOrderInfo4Merge(ocBOrder);
        updateOrder.setOrderEncryptionCode(ocBOrder.getOrderEncryptionCode());
        omsOrderService.updateOrderInfo(updateOrder);
    }

    /**
     * order log
     *
     * @param model message
     * @param order source  order
     * @param user  user
     */
    private void addOrderLog(OcModifyWarehouseModel model, OcBOrder order, CpCPhyWarehouse sourceWarehouse, User user) {
        String preName = StringUtils.isEmpty(sourceWarehouse.getEname()) ? "空" : sourceWarehouse.getEname();
        try {
            omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(),
                    OrderLogTypeEnum.WAREHOUSE_UPDATE.getKey(),
                    "发货仓库：修改前：" + preName + "，修改后：" + order.getCpCPhyWarehouseEname()
                            + ", 改仓原因：" + model.getModifyReason(), "", "", user);
        } catch (NDSException e) {
            log.error(LogUtil.format("modifyWarehouse.{},exp:{}", "ModifyWarehouseService"),
                    order.getId(), Throwables.getStackTraceAsString(e));
        }
    }

    /**
     * add order log
     *
     * @param id      order id
     * @param bilNo   order no
     * @param message log message
     * @param user    User
     */
    private void addOrderLog(Long id, String bilNo, String message, User user) {
        try {
            omsOrderLogService.addUserOrderLog(
                    id, bilNo, OrderLogTypeEnum.WAREHOUSE_UPDATE.getKey(), message, "", "", user);
        } catch (NDSException e) {
            log.error(LogUtil.format("modifyWarehouse.{},exp:{}", "ModifyWarehouseService"),
                    id, Throwables.getStackTraceAsString(e));
        }
    }

    /**
     * get CpCPhyWarehouse
     *
     * @param key CpCPhyWarehouse.id
     * @return CpCPhyWarehouse
     */
    private CpCPhyWarehouse getWarehouse(Long key) {
        CpCPhyWarehouse warehouse = null;
        try {
            warehouse = cpRpcService.queryByWarehouseId(key);
        } catch (Exception e) {
            log.error(LogUtil.format("仓库信息查询异常:{}", "ModifyWarehouseService"), Throwables.getStackTraceAsString(e));
        }
        return warehouse;
    }

    /**
     * vip jit x
     *
     * @param order OcBOrder
     */
    private void setJITXRedisChangeWarehouseFlag(OcBOrder order) {
        String redisKey = BllRedisKeyResources.getJitxChangeWarehouseFlagKey(order.getId());
        CusRedisTemplate<String, String> objRedisTemplate = RedisMasterUtils.getObjRedisTemplate();
        objRedisTemplate.opsForValue().set(redisKey, "1", 7, TimeUnit.DAYS);
        log.debug(LogUtil.format("修改仓库标识redisKey:{}"), redisKey);
        OcBOrder ocBOrder = new OcBOrder();
        ocBOrder.setId(order.getId());
        ocBOrder.setIsVipUpdateWarehouse(YesNoEnum.Y.getVal());
        orderMapper.updateById(ocBOrder);
    }

    /**
     * log
     *
     * @param express message
     * @param obs     key
     */
    private void logStep(String express, Object... obs) {
        ThreadLocalUtil.logStepMsg.get().add(String.format(express, obs));
    }

    /**
     * optimize exception
     */
    private Function<Exception, String> optimizeExpMsg = e -> {
        if (e == null) {
            return "null exception";
        }
        String message = e.getMessage();
        if (message == null) {
            return "null message";
        }
        return message.length() > 200 ? message.substring(0, 200) : message;
    };

}
