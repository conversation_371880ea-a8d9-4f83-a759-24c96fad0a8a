package com.jackrain.nea.oc.oms.mapper.task;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;


/**
 * <AUTHOR> ruan.gz
 * @Description :
 * @Date : 2020/8/29
 **/
@Slf4j
public class MsgDrdsSql {

    public String selectByNodeSql(Map<String, Object> para) {
        StringBuffer sql = new StringBuffer();
        StringBuffer limitStr = new StringBuffer(" LIMIT ");
        String nodeName = (String) para.get("nodeName");
        int limit = para.get("limit") != null ? (int) para.get("limit") : 3000;
        limitStr.append(limit);
        String taskTableName = (String) para.get("taskTableName");

        //TODO 如果是DRDS数据库 必传参节点名称nodeName
        if (StringUtils.isEmpty(nodeName)) {
            return null;
        }
        sql.append("/*!TDDL:NODE=" + nodeName + "*/").append("select * from ")
                .append(taskTableName)
                .append(" where status='0' ORDER BY modifieddate asc ")
                .append(limitStr);
        return sql.toString();
    }

}
