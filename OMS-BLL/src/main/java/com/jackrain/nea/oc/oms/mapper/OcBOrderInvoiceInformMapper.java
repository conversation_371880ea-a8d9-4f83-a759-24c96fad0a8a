package com.jackrain.nea.oc.oms.mapper;


import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrderInvoiceInform;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.jdbc.SQL;

import java.util.List;

/**
 * 开票
 */
@Mapper
public interface OcBOrderInvoiceInformMapper extends ExtentionMapper<OcBOrderInvoiceInform> {

    /**
     * 查询
     *
     * @param refIds   string
     * @param isActive string
     * @return list
     */
    @SelectProvider(type = SqlProvider.class, method = "selectInvoiceInformByRids")
    List<OcBOrderInvoiceInform> selectInvoiceInformByRids(@Param("refIds") String refIds,
                                                          @Param("isActive") String isActive);

    /**
     * 根据订单表id查询开票记录
     *
     * @param refId    id
     * @param isActive y
     * @return OcBOrderInvoiceInform
     */
    @Select("SELECT * FROM OC_B_ORDER_INVOICE_INFORM WHERE `OC_B_ORDER_ID`=#{refId} AND ISACTIVE =#{isActive} "
            + "ORDER BY CREATIONDATE DESC LIMIT 1 ")
    OcBOrderInvoiceInform selectInvoiceInformByRefId(@Param("refId") Long refId, @Param("isActive") String isActive);

    /**
     * 更新
     *
     * @param oif
     * @return
     */
    @UpdateProvider(type = SqlProvider.class, method = "updateInvoiceInformByFids")
    int updateInvoiceInformByFids(JSONObject oif);

    /**
     * sql
     */
    class SqlProvider {
        /**
         * @param refIds   string
         * @param isActive string
         * @return string
         */
        public String selectInvoiceInformByRids(@Param("refIds") String refIds, @Param("isActive") String isActive) {
            StringBuilder sb = new StringBuilder();
            sb.append("SELECT * FROM OC_B_ORDER_INVOICE_INFORM WHERE `OC_B_ORDER_ID` IN (");
            sb.append(refIds);
            sb.append(" ) AND ISACTIVE = '");
            sb.append(isActive);
            sb.append("';");
            return sb.toString();
        }


        /**
         * update
         *
         * @param oif
         * @return
         */
        public String updateInvoiceInformByFids(JSONObject oif) {
            String upSql = new SQL() {{
                UPDATE("OC_B_ORDER_INVOICE_INFORM");
                for (String k : oif.keySet()) {
                    if ("ID".equals(k) || "OC_B_ORDER_ID".equals(k) || "refId".equals(k)) {
                        continue;
                    }
                    SET(k + "=#{" + k + "}");
                }
            }}.toString();
            String refId = oif.getString("refId");
            return upSql + " WHERE `OC_B_ORDER_ID` IN (" + refId + ")";
        }

    }
}
