package com.jackrain.nea.oc.oms.model.enums;


/**
 * <AUTHOR>
 * @date 2019/9/25
 * <p>
 * 退单次品调拨
 */
public enum OrderReturnReserveBigint07Type {

    NOT_HAVE("无次品调拨", 0),
    NO("次品未调拨", 1),
    YES("次品已调拨", 2);

    String key;
    int val;

    OrderReturnReserveBigint07Type(String k, int v) {
        this.key = k;
        this.val = v;
    }

    public String getKey() {
        return key;
    }

    public int getVal() {
        return val;
    }
}


