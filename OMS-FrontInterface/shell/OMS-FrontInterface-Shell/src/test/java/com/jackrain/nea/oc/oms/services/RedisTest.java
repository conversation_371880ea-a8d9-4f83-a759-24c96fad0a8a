package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.OMSFrontInterfaceMainApplication;
import com.jackrain.nea.oc.oms.util.RedisHashCommonUtils;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.st.model.StCAutoCheck;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.SetOperations;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Map;
import java.util.Set;

/**
 * @Auther: 黄志优
 * @Date: 2020/12/2 14:47
 * @Description:
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = OMSFrontInterfaceMainApplication.class)
@Slf4j
public class RedisTest {

    String rediskey = "st:audit:test:all:";
    String setRediskey = "st:audit:test:set";

    @Autowired
    RedisHashCommonUtils redisHashCommonUtils;

    @Autowired
    private RedisOpsUtil<String, String> redisOpsUtil;

    @Test
    public void add2() {
        SetOperations<String, String> setOperations = redisOpsUtil.strRedisTemplate.opsForSet();
        setOperations.add(setRediskey, "1", "2");

        System.out.println("开始新增数据=========================");
        StCAutoCheck autoCheck = new StCAutoCheck();
        autoCheck.setId(1L);
        autoCheck.setCpCShopTitle("哈哈哈");

        StCAutoCheck autoCheck2 = new StCAutoCheck();
        autoCheck2.setId(2L);
        autoCheck2.setCpCShopTitle("啦啦啦");

        redisHashCommonUtils.hset(rediskey + autoCheck.getId().toString(), autoCheck);
        redisHashCommonUtils.hset(rediskey + autoCheck2.getId().toString(), autoCheck2);
        Set<String> members = setOperations.members(setRediskey);

        for (String member : members) {
            System.out.println("set------------" + member);
            printHash(rediskey + member);
        }
    }

    @Test
    public void add() {
        System.out.println("初始化=========================");

        System.out.println("开始新增数据=========================");
        StCAutoCheck autoCheck = new StCAutoCheck();
        autoCheck.setId(1L);
        autoCheck.setCpCShopTitle("哈哈哈");

        StCAutoCheck autoCheck2 = new StCAutoCheck();
        autoCheck2.setId(2L);
        autoCheck2.setCpCShopTitle("啦啦啦");

        redisHashCommonUtils.hset(rediskey, autoCheck.getId().toString(), JSON.toJSONString(autoCheck));
        redisHashCommonUtils.hset(rediskey, autoCheck2.getId().toString(), JSON.toJSONString(autoCheck2));
        printHash();

        System.out.println("修改2=========================");
        autoCheck2 = new StCAutoCheck();
        autoCheck2.setId(2L);
        autoCheck2.setCpCShopTitle("改改改");
        redisHashCommonUtils.hset(rediskey, autoCheck2.getId().toString(), JSON.toJSONString(autoCheck2));
        printHash();

        redisHashCommonUtils.hdel(rediskey, "1");
        System.out.println("删除1=========================");
        printHash();

        StCAutoCheck autoCheck3 = new StCAutoCheck();
        autoCheck3.setId(3L);
        autoCheck3.setCpCShopTitle("嘿嘿嘿");
        redisHashCommonUtils.hset(rediskey, autoCheck3.getId().toString(), JSON.toJSONString(autoCheck3));
        System.out.println("增加3=========================");
        printHash();
    }


    private void printHash() {
        Map<String, String> hgetall = redisHashCommonUtils.hgetall(rediskey);

        for (String shopid : hgetall.keySet()) {
            String shopStr = hgetall.get(shopid);
            StCAutoCheck temp = JSON.parseObject(shopStr, StCAutoCheck.class);
            System.out.println(JSON.toJSONString(temp));
        }
    }

    private void printHash(String key) {
        Map<String, String> hgetall = redisHashCommonUtils.hgetall(key);
        StCAutoCheck admin = JSONObject.parseObject(JSONObject.toJSONString(hgetall), StCAutoCheck.class);
        System.out.println(JSON.toJSONString(admin));
    }
}
