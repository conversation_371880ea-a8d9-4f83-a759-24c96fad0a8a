package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.ReturnReceiptMatchCmd;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;

/**
 * @author: 周琳胜
 * @since: 2019/3/26
 * create at : 2019/3/26 9:57
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class ReturnReceiptMatchCmdImpl implements ReturnReceiptMatchCmd {

//    @Autowired
//    private OcRefundInMatchService ocRefundInMatchService;

    @Override
    public ValueHolderV14 execute(JSONObject obj, User user) throws NDSException {
    //    return ocRefundInMatchService.warehousingMatching(obj, user);
        return null;
    }
}
