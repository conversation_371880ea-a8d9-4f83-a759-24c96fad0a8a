package com.jackrain.nea.oc.oms.services.logistics;

import com.jackrain.nea.oc.oms.model.enums.LogisticsTableEnum;
import com.jackrain.nea.oc.oms.model.request.LogisticsInfoQueryRequest;
import com.jackrain.nea.oc.oms.model.result.LogisticsInfoQueryResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;

/**
 * <AUTHOR> wangbinyu
 * @since : 2022/6/16
 * description :
 */
public interface LogisticsInfoQueryApi {
    /**
     * 处理类
     * @param request 参数
     * @return result
     */
    ValueHolderV14<LogisticsInfoQueryResult> doHandle(LogisticsInfoQueryRequest request);

    /**
     * method
     *
     * @return method
     */
    LogisticsTableEnum table();
}
