package com.jackrain.nea.oc.oms.services.refund.direct;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.es.ES4ReturnOrder;
import com.jackrain.nea.oc.oms.gsi.GSI4OrderItem;
import com.jackrain.nea.oc.oms.mapper.*;
import com.jackrain.nea.oc.oms.model.enums.*;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OmsJDDirectCancelRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.StepExecInfo;
import com.jackrain.nea.oc.oms.model.resources.OrderOccupyStatus;
import com.jackrain.nea.oc.oms.model.result.OcBReturnOrderRefundBatchResult;
import com.jackrain.nea.oc.oms.model.table.*;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
import com.jackrain.nea.oc.oms.services.*;
import com.jackrain.nea.oc.oms.services.refund.OmsReturnAfterUtil;
import com.jackrain.nea.oc.oms.util.OmsTransferSupply;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Desc : 京东厂直取消
 * <AUTHOR> xiWen
 * @Date : 2022/3/28
 */
@Slf4j
@Component
public class OmsJDDirectCancelService {

    @Autowired
    private OcBOrderHoldService ocBOrderHoldService;

    @Autowired
    private OcBOrderTheAuditService ocBOrderTheAuditService;

    @Autowired
    private OmsMarkCancelService omsMarkCancelService;

    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Autowired
    private OcBReturnOrderBatchAddService ocBReturnOrderBatchAddService;

    @Autowired
    private OcCancelChangingOrRefundService ocCancelChangingOrRefundService;

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;

    @Autowired
    private OcBOrderItemFiMapper ocBorderItemMapper;

    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderFiMapper;

    @Autowired
    private OcBReturnOrderRefundMapper ocBReturnOrderRefundFiMapper;

    @Autowired
    private OcBReturnOrderLogMapper ocBReturnOrderLogMapper;

    @Autowired
    private IpBJingdongDirectRefundMapper ipBJingdongDirectRefundMapper;

    @Autowired
    private IpBJingdongDirectRefundItemMapper ipBJingdongDirectRefundItemMapper;

    /**
     * @param omsRelation
     * @return
     */
    public void getOmsJDDCancelRelation(OmsJDDirectCancelRelation omsRelation) {
        StepExecInfo stepExecInfo = omsRelation.getStepExecInfo();
        try {
            // ip
            IpBJingdongDirectRefund refund = getOmsRelationWithinIpOrders(omsRelation);
            if (stepExecInfo.getId() < OcBOrderConst.ORDER_ID) {
                stepExecInfo.reSetId(refund.getId());
            }
            Integer isTrans = omsRelation.getIpRefund().getIstrans();
            isTrans = isTrans == null ? 0 : isTrans;
            if (TransferOrderStatus.TRANSFERRED.toInteger() == isTrans) {
                stepExecInfo.markStepInfo(StepExeState.FINAL, TransferOrderStatus.TRANSFERRED, "已转换,无需转换");
                return;
            }
            //oc 1
            List<Long> ocKeys = GSI4OrderItem.selectOcBOrderItemByTid(omsRelation.getIpRefund().getCustomOrderId());
            if (CollectionUtils.isEmpty(ocKeys)) {
                Date creationDate = omsRelation.getIpRefund().getCreationdate();
                AssertUtil.assertException(Objects.isNull(creationDate), "创建时间为空");
                Date deadline = Date.from(LocalDateTime.now().minusDays(3).atZone(ZoneId.systemDefault()).toInstant());
                if (deadline.before(creationDate)) {
                    stepExecInfo.markStepInfo(StepExeState.UPDATE,
                            TransferOrderStatus.NOT_TRANSFER, "不存在原始订单, 等待下次转换");
                    return;
                }
                stepExecInfo.markStepInfo(StepExeState.UPDATE,
                        TransferOrderStatus.TRANSFERRED, "不存在原始订单, 超过3天,系统自动标记已转换");
                return;
            }
            // oc 2
            List<Long> distinctKeys = ocKeys.stream().distinct().collect(Collectors.toList());
            OmsTransferSupply.logMsg(String.format("search ocOrderIds: %s", JSON.toJSONString(distinctKeys)));
            List<Long> lockFails = OmsTransferSupply.lockOcOrders(distinctKeys);
            if (CollectionUtils.isNotEmpty(lockFails)) {
                String keyStr = StringUtils.join(lockFails, OcBOrderConst.ORDER_COMMA);
                stepExecInfo.markGlobalFailStateMessage(String.format("零售发货单编号%s处于锁定状态,等待下次转换", keyStr));
            }
            if (CollectionUtils.isEmpty(distinctKeys)) {
                stepExecInfo.markStepInfo(StepExeState.UPDATE,
                        TransferOrderStatus.NOT_TRANSFER, "零售发货单全部处于锁定状态, 等待下次转换");
                return;
            }
            // oc 3
            List<OmsOrderRelation> ocRelations = getOcOrderRelations(distinctKeys);
            omsRelation.setOcOrderRelations(ocRelations);
            stepExecInfo.setStepExeState(StepExeState.SUCCESS);
        } catch (Exception e) {
            String message = OmsTransferSupply.expMsgFun.apply(e);
            stepExecInfo.markStepInfo(StepExeState.UPDATE, TransferOrderStatus.TRANSFERFAIL, message);
            OmsTransferSupply.logMsg(message);
        }
    }

    public IpBJingdongDirectRefund getIpBRefundOrder(String shardKey) {
        IpBJingdongDirectRefund refund = ipBJingdongDirectRefundMapper.selectByRefundId(shardKey);
        return refund;
    }

    /**
     * hold oc order
     *
     * @param ocRelation
     * @param stepExecInfo
     * @return
     */

    public boolean holdOcOrderProcess(OmsOrderRelation ocRelation, StepExecInfo stepExecInfo) {

        Long ipId = stepExecInfo.getId();
        String shardKey = stepExecInfo.getShardKey();
        Long ocOrderId = ocRelation.getOcBOrder().getId();
        OmsTransferSupply.logMsg(String.format("holdOcOrderProcess id:%d start", ocOrderId));
        try {
            ValueHolder auditVh = ocBOrderHoldService.businessHold(ocOrderId, OrderHoldReasonEnum.REFUND_HOLD);
            if (!auditVh.isOK()) {
                String message = (String) auditVh.get("message");
                OmsTransferSupply.logMsg(String.format("holdOcOrder id:%d failedMsg:%s", ocOrderId, message));
                stepExecInfo.markGlobalFailStateMessage(ocRelation.getOcBOrder().getBillNo() + "Hold单失败");
                return false;
            }
            stepExecInfo.markStepInfo(StepExeState.UPDATE,
                    TransferOrderStatus.TRANSFERRED, "买家申请退款转换成功");
            OmsTransferSupply.logMsg(String.format("holdOcOrderProcess id:%d success", ocOrderId));
            return true;
        } catch (Exception e) {
            String msg = OmsTransferSupply.expMsgFun.apply(e);
            OmsTransferSupply.logMsg(String.format("holdOcOrderProcess exp:%s", msg));
            stepExecInfo.markGlobalFailStateMessage(ocRelation.getOcBOrder().getBillNo() + "Hold单失败");
            log.error("JDDirectCancelTransfer.error.{},id:{},param:{}, holdOcOrderProcess.Exp: {}",
                    shardKey, ipId, JSON.toJSONString(stepExecInfo), Throwables.getStackTraceAsString(e));
        }
        OmsTransferSupply.logMsg(String.format("holdOcOrderProcess id:%d end", ocOrderId));
        return false;

    }

    /**
     * @param ocRelation
     * @param stepExecInfo
     */
    public boolean assignReturningStatus(OmsOrderRelation ocRelation, StepExecInfo stepExecInfo) {
        OcBOrder order = ocRelation.getOcBOrder();
        OmsTransferSupply.logMsg(String.format("assignReturningStatus id:%d start", order.getId()));
        Long ipId = stepExecInfo.getId();
        String shardKey = stepExecInfo.getShardKey();
        String relationShardKey = stepExecInfo.getShardKey();
        List<OcBOrderItem> ocBOrderItems = ocRelation.getOcBOrderItems();
        List<Long> list = new ArrayList<>();
        for (OcBOrderItem item : ocBOrderItems) {
            String tid = item.getTid();
            if (!StringUtils.equals(relationShardKey, tid)) {
                continue;
            }
            Integer refundStatus = item.getRefundStatus();
            if (OrderItemRefundStatus.CANCELED.val().equals(refundStatus)
                    || OrderItemRefundStatus.RETURNING.val().equals(refundStatus)) {
                continue;
            }
            list.add(item.getId());
        }
        try {
            updateOcBilReturning(order, list);
            OmsTransferSupply.logMsg(String.format("assignReturningStatus id:%d end", order.getId()));
            return true;
        } catch (Exception e) {
            stepExecInfo.markGlobalFailStateMessage(ocRelation.getOcBOrder().getBillNo() + "标记退款状态失败");
            String msg = OmsTransferSupply.expMsgFun.apply(e);
            OmsTransferSupply.logMsg(String.format("assignReturningStatus exp:%s", msg));
            log.error("JDDirectCancelTransfer.error.{},id:{},param:{}, assignReturningStatus.Exp: {}",
                    shardKey, ipId, JSON.toJSONString(stepExecInfo), Throwables.getStackTraceAsString(e));
            return false;
        }

    }

    /**
     * reserve audit order
     *
     * @param ocRelation
     * @param user
     * @param stepExecInfo
     */
    public boolean deAuditOcOrderProcess(OmsOrderRelation ocRelation, StepExecInfo stepExecInfo, User user) {
        Long ocOrderId = ocRelation.getOcBOrder().getId();
        OmsTransferSupply.logMsg(String.format("deAuditOcOrder id:%d start", ocOrderId));
        try {
            ValueHolderV14 vh = new ValueHolderV14();
            boolean isReserveSuccess = ocBOrderTheAuditService
                    .updateOrderInfo(user, vh, ocOrderId, LogTypeEnum.REFUND_REVERSE_AUDIT.getType());
            if (isReserveSuccess) {
                OmsTransferSupply.logMsg(String.format("deAuditOcOrder id:%d success", ocOrderId));
                return true;
            }
            stepExecInfo.markGlobalFailStateMessage(ocRelation.getOcBOrder().getBillNo() + "反审核失败");
            OmsTransferSupply.logMsg(String.format("deAuditOcOrder id:%d failed", ocOrderId));
        } catch (Exception e) {
            stepExecInfo.markGlobalFailStateMessage(ocRelation.getOcBOrder().getBillNo() + "反审核异常");
            OmsTransferSupply.logMsg(String.format("deAuditOcOrder exp: %s", OmsTransferSupply.expMsgFun.apply(e)));
            log.error("JDDirectCancelTransfer.error.{},id:{},orderId:{}, deAuditOcOrder.Exp: {}",
                    stepExecInfo.getShardKey(), stepExecInfo.getId(), ocOrderId, Throwables.getStackTraceAsString(e));
        }
        OmsTransferSupply.logMsg(String.format("deAuditOcOrder id:%d end", ocOrderId));
        return false;
    }

    /**
     * @param ocRelation
     * @param stepExecInfo
     */
    public void tobeConfirmOrderProcess(OmsOrderRelation ocRelation, StepExecInfo stepExecInfo) {
        String billNo = ocRelation.getOcBOrder().getBillNo();
        stepExecInfo.markGlobalFailStateMessage(String.format("订单:%s 待分配或传WMS中,等待下次转换", billNo));
    }

    /**
     * @param ocRelation
     * @param stepExecInfo
     * @param user
     */
    public void warehouseOrPlatformDeliveryProcess(OmsOrderRelation ocRelation, StepExecInfo stepExecInfo, User user) {
        OcBOrder order = ocRelation.getOcBOrder();
        Long ocOrderId = order.getId();
        OmsTransferSupply.logMsg(String.format("warehouseOrPlatformDeliveryProcess ocOrderId:%d start", ocOrderId));
        try {
            Long ocReturnId = ES4ReturnOrder.findIdByOrderIdAndTid(ocOrderId, stepExecInfo.getRelationShardKey());
            OmsTransferSupply.logMsg(String.format("findReturnOrderExist ReturnId:%d", ocReturnId));
            if (ocReturnId == null) {
                OmsTransferSupply.logMsg("generate returnOrder start");
                OmsJDDirectCancelService bean = ApplicationContextHandle.getBean(OmsJDDirectCancelService.class);
                bean.generateReturnOrderProcess(ocRelation, stepExecInfo, user);
                OmsTransferSupply.logMsg("generate returnOrder end");
            } else {
                String expressCode = ocRelation.getOcBOrder().getExpresscode();
                if (StringUtils.isNotBlank(expressCode)) {
                    OcBReturnOrder returnOrder = ocBReturnOrderFiMapper.selectByid(ocReturnId);
                    if (returnOrder != null && StringUtils.isBlank(returnOrder.getLogisticsCode())) {
                        OcBReturnOrder newReturn = new OcBReturnOrder();
                        newReturn.setId(returnOrder.getId());
                        newReturn.setLogisticsCode(expressCode);
                        int v = ocBReturnOrderFiMapper.updateById(newReturn);
                        OmsTransferSupply.logMsg(String.format("update.returnOrder.expressCode.result:%d", v));
                    }
                }
            }
        } catch (Exception e) {
            //    stepExecInfo.markGlobalFailStateMessage(order.getBillNo() + "单据已发货,生成退换货单异常");
            OmsTransferSupply.logMsg(
                    String.format("warehouseOrPlatformDeliveryProcess exp: %s", OmsTransferSupply.expMsgFun.apply(e)));
            log.error("JDDirectCancelTransfer.error.{},id:{},orderId:{}, deAuditOcOrder.Exp: {}",
                    stepExecInfo.getShardKey(), stepExecInfo.getId(), ocOrderId, Throwables.getStackTraceAsString(e));
        }
        stepExecInfo.markStepInfo(StepExeState.UPDATE,
                TransferOrderStatus.TRANSFERRED, String.format("订单:%s已发货,无法撤回", order.getBillNo()));
        OmsTransferSupply.logMsg(String.format("warehouseOrPlatformDeliveryProcess id:%d end", ocOrderId));
    }

    /**
     * @param ocRelation
     * @param execInfo
     * @param usr
     */
    public boolean deAuditDeliveryProcess(OmsOrderRelation ocRelation, StepExecInfo execInfo, User usr) {
        OcBOrder order = ocRelation.getOcBOrder();
        Long ocOrderId = order.getId();
        OmsTransferSupply.logMsg(String.format("warehouseOrPlatformDeliveryProcess ocOrderId:%d start", ocOrderId));
        try {
            Long ocReturnId = ES4ReturnOrder.findIdByOrderIdAndTid(ocOrderId, execInfo.getRelationShardKey());
            OmsTransferSupply.logMsg(String.format("findReturnOrderExist ReturnId:%d start", ocReturnId));
            if (ocReturnId == null) {
                execInfo.markStepInfo(StepExeState.UPDATE,
                        TransferOrderStatus.TRANSFERRED, String.format("订单:%s已发货,转换成功", order.getBillNo()));
                return true;
            }
            OcBReturnOrder returnBil = ocBReturnOrderFiMapper.selectByid(ocReturnId);
            boolean waitInState = ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal().equals(returnBil.getReturnStatus());
            if (!waitInState) {
                execInfo.markStepInfo(StepExeState.UPDATE, TransferOrderStatus.TRANSFERRED,
                        String.format("订单:%s已发货,退换货单非等待退货入库状态", order.getBillNo()));
                return true;
            }
            JSONObject jsn = new JSONObject();
            JSONArray jsnArray = new JSONArray();
            jsnArray.add(ocReturnId);
            jsn.put("ids", jsnArray);
            ValueHolderV14 vh = ocCancelChangingOrRefundService.orRefundService(jsn, usr, Boolean.FALSE);
            if (vh.isOK()) {
                execInfo.markStepInfo(StepExeState.UPDATE, TransferOrderStatus.TRANSFERRED,
                        String.format("退换货单:%s取消成功", returnBil.getBillNo()));
                return true;
            }
            execInfo.markGlobalFailStateMessage(String.format("退换货单:%s取消失败,等待下次转换", returnBil.getBillNo()));
        } catch (Exception e) {
            execInfo.markGlobalFailStateMessage(order.getBillNo() + "反审核异常");
            OmsTransferSupply.logMsg(
                    String.format("warehouseOrPlatformDeliveryProcess exp: %s", OmsTransferSupply.expMsgFun.apply(e)));
            log.error("JDDirectCancelTransfer.error.{},id:{},orderId:{}, deAuditOcOrder.Exp: {}",
                    execInfo.getShardKey(), execInfo.getId(), ocOrderId, Throwables.getStackTraceAsString(e));
        }
        OmsTransferSupply.logMsg(String.format("warehouseOrPlatformDeliveryProcess id:%d end", ocOrderId));
        return false;
    }

    /**
     * @param ocRelation
     * @param stepExecInfo
     */
    public void cancelOrVoidProcess(OmsOrderRelation ocRelation, StepExecInfo stepExecInfo) {
        stepExecInfo.markStepInfo(StepExeState.UPDATE, TransferOrderStatus.TRANSFERRED,
                String.format("订单:%s取消或系统作废", ocRelation.getOcBOrder().getBillNo()));
    }

    /**
     * update ip bill
     *
     * @param stepExecInfo
     */
    public void updateIpBilInfo(StepExecInfo stepExecInfo) {
        Long id = stepExecInfo.getId();
        String shardKey = stepExecInfo.getShardKey();
        OmsTransferSupply.logMsg(String.format("updateIpBilInfo id:%d start", id));
        try {
            int result = ipBJingdongDirectRefundMapper.updateTransStatusAndRemark(stepExecInfo);
            OmsTransferSupply.logMsg(String.format("updateIpBilInfo result:%d", result));
        } catch (Exception ex) {
            OmsTransferSupply.logMsg(String.format("updateIpBilInfo exp: %s", OmsTransferSupply.expMsgFun.apply(ex)));
            log.error("JDDirectCancelTransfer.error.{},id:{},param:{}, updateIpBilInfo.Exp: {}",
                    shardKey, id, JSON.toJSONString(stepExecInfo), Throwables.getStackTraceAsString(ex));
        }
        OmsTransferSupply.logMsg(String.format("updateIpBilInfo id:%d end", id));
    }

    /**
     * @param size
     * @return
     */
    public Map<Long, String> selectUnTransferredOrderFromEs(int size) {
        Map<Long, String> map = new HashMap<>(size);
        try {
            JSONObject whereKeys = new JSONObject();
            whereKeys.put("ISTRANS", "0");
            String[] returnFieldNames = new String[]{"REFUND_ID", "ID"};
            JSONArray orderKeys = new JSONArray();
            JSONObject orderKey = new JSONObject();

            orderKey.put("asc", true);
            orderKey.put("name", "MODIFIEDDATE");
            orderKeys.add(orderKey);
            JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.IP_B_JD_DIRECT_CANCEL_INDEX,
                    OcElasticSearchIndexResources.IP_B_JD_DIRECT_CANCEL_INDEX, whereKeys, null, orderKeys,
                    size, 0, returnFieldNames);

            if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
                JSONArray arrayObj = search.getJSONArray("data");
                for (Object obj : arrayObj) {
                    JSONObject jsonObject = (JSONObject) obj;
                    String orderNo = jsonObject.getString("TID");
                    Long id = jsonObject.getLong("ID");
                    map.put(id, orderNo);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error("selectUnTransferredOrderFromEs", ex);
        }
        return map;
    }

    /**
     * update oc bill
     *
     * @param order
     * @param value
     */
    private void updateOcBilReturning(OcBOrder order, List<Long> value) {
        if (CollectionUtils.isEmpty(value)) {
            return;
        }
        Long key = order.getId();
        int itemResult = ocBOrderItemMapper.updateRefundStatusByIds(key, value, OrderItemRefundStatus.RETURNING.val());
        Integer isInReturning = order.getIsInreturning();
        if (OcBOrderConst.IS_STATUS_IY.equals(isInReturning)) {
            return;
        }
        OcBOrder ocBOrder = new OcBOrder();
        ocBOrder.setId(key);
        ocBOrder.setIsInreturning(OcBOrderConst.IS_STATUS_IY);
        int updateResult = ocBOrderMapper.updateById(ocBOrder);
        OmsTransferSupply.logMsg(String.format("updateOcBilReturning id:%d, result:%d,ItemResult:%d,end",
                key, updateResult, itemResult));
    }

    /**
     * @param omsRelation ip key
     * @return OmsJDDirectCancelRelation
     */
    private IpBJingdongDirectRefund getOmsRelationWithinIpOrders(OmsJDDirectCancelRelation omsRelation) {
        IpBJingdongDirectRefund refund = ipBJingdongDirectRefundMapper.selectByRefundId(omsRelation.getIpRefundNo());
        AssertUtil.assertException(Objects.isNull(refund), "未查询到待转换单据");
        AssertUtil.assertException(StringUtils.isBlank(refund.getCustomOrderId()), "客户订单号不存在");
        List<IpBJingdongDirectRefundItem> refundSubItems =
                ipBJingdongDirectRefundItemMapper.selectRefundItemsByShardKey(refund.getId());
        AssertUtil.assertException(CollectionUtils.isEmpty(refundSubItems), "未查询到待转换单据明细");
        omsRelation.setIpRefund(refund);
        omsRelation.setIpRefundItems(refundSubItems);
        return refund;
    }

    /**
     * @param orderIds order ids
     * @return List<OmsOrderRelation>
     */
    private List<OmsOrderRelation> getOcOrderRelations(List<Long> orderIds) {
        List<OcBOrder> ocOrders = ocBOrderMapper.selectByIdsList(orderIds);
        AssertUtil.assertException(CollectionUtils.isEmpty(ocOrders), "未查询到零售发货单");
        List<OcBOrderItem> ocOrderItems = ocBOrderItemMapper.selectItemsByOrderIdWithOutStatus(orderIds);
        AssertUtil.assertException(CollectionUtils.isEmpty(ocOrderItems), "未查询到零售发货单明细");
        Map<Long, List<OcBOrderItem>> ocItemsMap =
                ocOrderItems.stream().collect(Collectors.groupingBy(OcBOrderItem::getOcBOrderId, Collectors.toList()));
        List<OmsOrderRelation> ocRelations = new ArrayList<>();
        OmsOrderRelation ocRelation;
        for (OcBOrder order : ocOrders) {
            Long id = order.getId();
            List<OcBOrderItem> items = ocItemsMap.get(id);
            if (CollectionUtils.isEmpty(items)) {
                OmsTransferSupply.logMsg(String.format("零售发货单ID:%d明细为空", id));
                continue;
            }
            ocRelation = new OmsOrderRelation();
            ocRelation.setOcBOrder(order);
            ocRelation.setOcBOrderItems(items);
            ocRelations.add(ocRelation);
        }
        AssertUtil.assertException(ocRelations.size() < 1, "取消单对应的零售发货单数据异常");
        return ocRelations;
    }

    /**
     * mark oc order item refund tag
     *
     * @param ocRelation
     * @param stepExecInfo
     */
    private List<Long> collectCurrentTidSubItems(OmsOrderRelation ocRelation, StepExecInfo stepExecInfo) {
        Long ocOrderId = ocRelation.getOcBOrder().getId();
        OmsTransferSupply.logMsg(String.format("markOcOrderItemsRefundFlag id:%d splitStart", ocOrderId));
        List<OcBOrderItem> ocBOrderItems = ocRelation.getOcBOrderItems();
        String relationShardKey = stepExecInfo.getRelationShardKey();
        List<Long> subItemIds = new ArrayList<>();
        for (OcBOrderItem item : ocBOrderItems) {
            String tid = item.getTid();
            if (!StringUtils.equals(relationShardKey, tid)) {
                continue;
            }
            subItemIds.add(item.getId());
        }
        //    stepExecInfo.collectOcKeys(ocOrderId, subItemIds);
        String itemStr = JSON.toJSONString(subItemIds);
        OmsTransferSupply.logMsg(String.format("markOcOrderItemsRefundFlag id:%d splitResult: %s", ocOrderId, itemStr));
        return subItemIds;
    }

    /**
     * generate return order
     *
     * @param ocRelation
     * @param stepExecInfo
     * @param user
     */
    @Transactional
    public void generateReturnOrderProcess(OmsOrderRelation ocRelation, StepExecInfo stepExecInfo, User user) {
        String tid = stepExecInfo.getRelationShardKey();
        List<OcBOrderItem> refundSubItems = ocRelation.getOcBOrderItems().stream()
                .filter(e -> StringUtils.equals(tid, e.getTid()) && e.getProType() != 4).collect(Collectors.toList());
        Long returnId = ModelUtil.getSequence("oc_b_return_order");
        List<OcBOrderItem> origRefundQtySubItems = new ArrayList<>();
        OcBReturnOrderRefundBatchResult tmpReturnInfo = ocBReturnOrderBatchAddService
                .getRefundOrder(refundSubItems, origRefundQtySubItems, returnId, user);
        AssertUtil.assertException(tmpReturnInfo.getTotamt().compareTo(BigDecimal.ZERO) < 0, "退单总金额计算为负数");
        AssertUtil.assertException(CollectionUtils.isEmpty(tmpReturnInfo.getListRefund()), "没有可退数量的明细");
        OcBOrder order = ocRelation.getOcBOrder();
        OcBReturnOrder returnOrder = ocBReturnOrderBatchAddService
                .genReturnOrder(order, returnId, OcBOrderConst.IS_STATUS_IY, tmpReturnInfo.getTotamt(),
                        tmpReturnInfo.getAddQty(), tmpReturnInfo.getAllSku(), user);
        for (OcBReturnOrderRefund refund : tmpReturnInfo.getListRefund()) {
            ocBReturnOrderRefundFiMapper.insert(refund);
        }
        String jointTid = OmsReturnAfterUtil.getJointTid(tmpReturnInfo.getListRefund());
        returnOrder.setTid(jointTid);
        ocBReturnOrderFiMapper.insert(returnOrder);

        if (CollectionUtils.isNotEmpty(origRefundQtySubItems)) {
            for (OcBOrderItem item : origRefundQtySubItems) {
                ocBorderItemMapper.updateById(item);
            }
        }
        OcBOrder newOrder = new OcBOrder();
        newOrder.setId(order.getId());
        newOrder.setReturnStatus(OcBorderListEnums.ReturnStatusEnum.RETURN_ING.getVal());
        ocBOrderMapper.updateById(newOrder);

        String logMessage = "京东厂直取消订单新增退换货单";
        OcBReturnOrderLog ocBReturnOrderLog = ocBReturnOrderBatchAddService.genReturnLog(returnId, user, logMessage);
        ocBReturnOrderLogMapper.insert(ocBReturnOrderLog);

        omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(), OrderLogTypeEnum.REFUND_ORDER_ADD.getKey(),
                String.format("%s,退单ID:%d", logMessage, returnOrder.getId()), null, null, user);

    }

    /**
     * cancel  mark order refund finish
     *
     * @param ocRelation
     * @param stepExecInfo
     * @param user
     * @return
     */
    public boolean markOrderRefundCompletedProcess(OmsOrderRelation ocRelation, StepExecInfo stepExecInfo, User user) {

        Long ocOrderId = ocRelation.getOcBOrder().getId();
        OmsTransferSupply.logMsg(String.format("cancelMarkOrderRefundCompleted OcOrderId:%d start", ocOrderId));
        List<Long> tidRelationSubItemIds = collectCurrentTidSubItems(ocRelation, stepExecInfo);
        try {

            OcBOrder order = ocBOrderMapper.selectById(ocOrderId);
            AssertUtil.assertException(OmsOrderStatus.OCCUPY_IN.toInteger().equals(order.getOrderStatus()),
                    String.format("订单%s占单中，不允许标记退款完成", order.getBillNo()));
            Integer orderStatus = order.getOrderStatus();
            AssertUtil.assertException(!(OmsOrderStatus.UNCONFIRMED.toInteger().equals(orderStatus)
                    || OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(orderStatus)), "当前订单状态不允许标记退款完成");

            List<OcBOrderItem> orderItems = ocBOrderItemMapper.selectOrderItemsNotRefundFroAppointSku(ocOrderId);
            if (CollectionUtils.isEmpty(orderItems)) {
                OmsTransferSupply.logMsg("subItems empty, " +
                        "maybe invoked this method mark refund completed but not cancel hold or cancel isInReturning ");
                return true;
            }
            AssertUtil.assertException(CollectionUtils.isEmpty(orderItems), "当前零售发货单明细查询为空");

            Map<Long, OcBOrderItem> orderItemMap = orderItems.stream().collect(Collectors.toMap(OcBOrderItem::getId, p -> p));
            List<OcBOrderItem> platformSubItems = new ArrayList<>();
            for (Long itemId : tidRelationSubItemIds) {
                OcBOrderItem subItm = orderItemMap.get(itemId);
                if (subItm == null) {
                    continue;
                }
                if (subItm.getProType() == SkuType.NO_SPLIT_COMBINE || subItm.getProType() == SkuType.NORMAL_PRODUCT) {
                    platformSubItems.add(subItm);
                    orderItems.remove(subItm);
                }
            }
            AssertUtil.assertException(CollectionUtils.isEmpty(platformSubItems), "只有正常商品和组合福袋商品并且未退款完成的明细才可以标记退款完成");
            omsMarkCancelService.handleCancelItem(order, platformSubItems, false, orderItems, user, OrderHoldReasonEnum.REFUND_HOLD);
            stepExecInfo.markStepInfo(StepExeState.UPDATE, TransferOrderStatus.TRANSFERRED, "退款成功, 转换成功");
            // cancel hold
            auditedCancelHoldOcOrderProcess(tidRelationSubItemIds, ocRelation, stepExecInfo, user);
            return true;
        } catch (Exception e) {
            String message = OmsTransferSupply.expMsgFun.apply(e);
            stepExecInfo.markGlobalFailStateMessage(ocRelation.getOcBOrder().getBillNo() + "标记退款完成异常");
            OmsTransferSupply.logMsg(String.format("cancelMarkOrderRefundCompleted exp: %s", message));
            log.error("JDDirectCancelTransfer.error.{},id:{},orderId:{}, cancelMarkOrderRefundCompleted.Exp: {}",
                    stepExecInfo.getShardKey(), stepExecInfo.getId(), ocOrderId, Throwables.getStackTraceAsString(e));
        }
        //    stepExecInfo.markGlobalFailStateMessage(String.format("零售发货单:%s标记退款完成失败", ocRelation.getOcBOrder().getBillNo()));
        OmsTransferSupply.logMsg(String.format("cancelMarkOrderRefundCompleted OcOrderId:%d end", ocOrderId));
        return false;
    }

    /**
     * @param tidRelationSubItemIds
     * @param ocRelation
     * @param user
     */
    private void auditedCancelHoldOcOrderProcess(List<Long> tidRelationSubItemIds,
                                                 OmsOrderRelation ocRelation, StepExecInfo stepExecInfo, User user) {
        Long ocOrderId = ocRelation.getOcBOrder().getId();
        OmsTransferSupply.logMsg(String.format("cancelHoldOcOrderProcess OcOrderId:%d start", ocOrderId));
        List<OcBOrderItem> orderItems = ocBOrderItemMapper.selectAllOrderItem(ocOrderId);
        Integer refundStatus = OrderItemRefundStatus.CANCELED.val();
        long count = orderItems.stream().filter(e -> !(refundStatus.equals(e.getRefundStatus()))).count();
        if (count < OcBOrderConst.IS_STATUS_IY) {
            // if empty means this order is all relation this tid,
            // cancel mark refund completed service done release intercepted
            return;
        }
        boolean isReturningEmpty = judgeIsReturningEmpty(tidRelationSubItemIds, stepExecInfo, orderItems);
        if (!isReturningEmpty) {
            OmsTransferSupply.logMsg(
                    String.format("cancelHoldOcOrderProcess OcOrderId:%d,not release,returning item exist", ocOrderId));
            return;
        }
        OcBOrder order = ocBOrderMapper.selectById(ocOrderId);
        // cancel hold, cancel inReturning status
        cancelHold(order);
        // has refund item not belongs this tid
        cancelInReturning(order, user);
        OmsTransferSupply.logMsg(String.format("cancelHoldOcOrderProcess OcOrderId:%d end", ocOrderId));
    }

    /**
     * @param ocRelation
     * @param stepExecInfo
     * @param user
     */
    public void cancelHoldReturningStatusProcess(OmsOrderRelation ocRelation, StepExecInfo stepExecInfo, User user) {
        OcBOrder ocOrder = ocRelation.getOcBOrder();
        Long ocOrderId = ocOrder.getId();
        String relationShardKey = stepExecInfo.getRelationShardKey();
        List<OcBOrderItem> ocBOrderItems = ocRelation.getOcBOrderItems();
        OmsTransferSupply.logMsg(String.format("cancelOrderReturningStatus OcOrderId:%d start", ocOrderId));
        List<Long> tidRelationItems = new ArrayList<>();
        boolean isCancelHoldReturning = true;
        try {
            for (OcBOrderItem subItm : ocBOrderItems) {
                if (StringUtils.equals(relationShardKey, subItm.getTid())) {
                    if (!OrderItemRefundStatus.UN.val().equals(subItm.getRefundStatus())) {
                        tidRelationItems.add(subItm.getId());
                    }
                    continue;
                }
                if (OrderItemRefundStatus.RETURNING.val().equals(subItm.getRefundStatus())) {
                    isCancelHoldReturning = false;
                }
            }
            if (CollectionUtils.isNotEmpty(tidRelationItems)) {
                ocBOrderItemMapper.updateRefundStatusByIds(ocOrderId, tidRelationItems, OrderItemRefundStatus.UN.val());
            }
            stepExecInfo.markStepInfo(StepExeState.UPDATE, TransferOrderStatus.TRANSFERRED, "更新商品退款状态,转换成功");
            if (!isCancelHoldReturning) {
                OmsTransferSupply.logMsg("has returning item, not cancel hold and returning status");
                return;
            }
            // cancel hold
            cancelHold(ocOrder);
            // cancel refund status
            cancelInReturning(ocOrder, user);
            stepExecInfo.markStepInfo(StepExeState.UPDATE, TransferOrderStatus.TRANSFERRED, "取消退款状态,转换成功");
            OmsTransferSupply.logMsg(String.format("cancelHoldReturningStatusProcess OcOrderId:%d end", ocOrderId));
            return;
        } catch (Exception e) {
            String message = OmsTransferSupply.expMsgFun.apply(e);
            stepExecInfo.markGlobalFailStateMessage(ocRelation.getOcBOrder().getBillNo() + "取消退款状态异常");
            OmsTransferSupply.logMsg(String.format("cancelOrderReturningStatus exp: %s", message));
            log.error("JDDirectCancelTransfer.error.{},id:{},orderId:{}, cancelOrderReturningStatus.Exp: {}",
                    stepExecInfo.getShardKey(), stepExecInfo.getId(), ocOrderId, Throwables.getStackTraceAsString(e));
        }
        OmsTransferSupply.logMsg(String.format("cancelMarkOrderRefundCompleted OcOrderId:%d end", ocOrderId));
    }

    /**
     * @param tidSubIds
     * @param execInfo
     * @param orderItems
     * @return
     */
    private boolean judgeIsReturningEmpty(List<Long> tidSubIds, StepExecInfo execInfo, List<OcBOrderItem> orderItems) {
        Integer refundStatus = OrderItemRefundStatus.CANCELED.val();
        boolean isReturningEmpty = true;
        for (OcBOrderItem item : orderItems) {
            if (tidSubIds.contains(item.getId())) {
                if (refundStatus.equals(item.getRefundStatus())) {
                    continue;
                }
                isReturningEmpty = false;
                execInfo.markStepInfo(StepExeState.UPDATE, TransferOrderStatus.TRANSFERFAIL, "标记退款完成后存在未取消商品");
                OmsTransferSupply.logMsg(String.format("SubItemId:%d,RefundStatus Not 6 After service", item.getId()));
            }
            if (refundStatus.equals(item.getRefundStatus())) {
                continue;
            }
            if (OrderItemRefundStatus.RETURNING.val().equals(item.getRefundStatus())) {
                isReturningEmpty = false;
            }
        }
        return isReturningEmpty;
    }

    /**
     * cancel hold
     *
     * @param ocOrder OcBOrder
     */
    private void cancelHold(OcBOrder ocOrder) {
        if (OmsOrderIsInterceptEnum.YES.getVal().equals(ocOrder.getIsInterecept())) {
            OcBOrder newOrder = new OcBOrder();
            newOrder.setId(ocOrder.getId());
            newOrder.setIsInterecept(0);
            ocBOrderHoldService.holdOrUnHoldOrder(newOrder, OrderHoldReasonEnum.REFUND_HOLD);
        }
    }

    /**
     * audited cancel Returning
     *
     * @param ocOrder OcBOrder
     * @param user    User
     */
    private void cancelInReturning(OcBOrder ocOrder, User user) {
        if (!OcBOrderConst.IS_STATUS_IY.equals(ocOrder.getIsInreturning())) {
            return;
        }
        OcBOrder newOrder = new OcBOrder();
        newOrder.setId(ocOrder.getId());
        newOrder.setIsInreturning(OcBorderListEnums.ReturnStatusEnum.NO_RETURN.getVal());
        newOrder.setModifierename(user.getEname());
        newOrder.setModifiername(user.getName());
        newOrder.setModifierid(Long.valueOf(user.getId()));
        newOrder.setModifieddate(new Date());
        ocBOrderMapper.updateById(newOrder);
        omsOrderLogService.addUserOrderLog(ocOrder.getId(),
                null, OrderLogTypeEnum.ORDER_REFUND_HOLD.getKey(),
                "取消退款中状态", null, null, user);
    }

}
