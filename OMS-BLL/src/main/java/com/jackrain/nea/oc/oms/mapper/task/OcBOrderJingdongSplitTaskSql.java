package com.jackrain.nea.oc.oms.mapper.task;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * @author: 江家雷
 * @since: 2020-09-14
 * create at : 2020-09-14 22:48
 */
@Slf4j
public class OcBOrderJingdongSplitTaskSql {

    public String selectByNodeSql(Map<String, Object> para) {
        StringBuffer sql = new StringBuffer();
        StringBuffer limitStr = new StringBuffer(" LIMIT ");
        String nodeName = (String) para.get("nodeName");
        int limit = para.get("limit") != null ? (int) para.get("limit") : 3000;
        limitStr.append(limit);

        String taskTableName = (String) para.get("taskTableName");
        int splitTimes = para.get("splitTimes") != null ? (int) para.get("splitTimes") : 0;

        if (StringUtils.isEmpty(nodeName)) {
            return null;
        }
        sql.append("/*!TDDL:NODE=" + nodeName + "*/").append("select oc_b_order_id from ")
                .append(taskTableName)
                .append("  where status in (0,2) ")
                .append(" and next_time < now()")
                .append(" and split_times < ")
                .append(splitTimes)
                .append(limitStr);
        return sql.toString();
    }

}
