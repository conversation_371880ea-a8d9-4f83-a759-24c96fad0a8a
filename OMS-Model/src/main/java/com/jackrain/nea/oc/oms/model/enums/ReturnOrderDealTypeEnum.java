package com.jackrain.nea.oc.oms.model.enums;

import lombok.Getter;

/**
 * 退换货单处理类型，退换货单处理的标记
 * type:字段最长16位-varchar(16)
 *
 * <AUTHOR>
 */
public enum ReturnOrderDealTypeEnum {

    UN_KNOWN(" ", "未知"),

    /**
     * 天猫退货完成回传平台是否处理标记，有值表示处理过，null认为未处理
     */
    TMALL_DDD_RETURN("tdr", "天猫食品平台退货完成回传平台");


    ReturnOrderDealTypeEnum(String type, String desc) {
        this.key = type;
        this.name = desc;
    }

    @Getter
    private String key;

    @Getter
    private String name;


    /**
     * 根据状态值,获取状态名
     *
     * @param key
     * @return String
     */
    public static String enumToStringBykey(String key) {
        String s = "";
        if (key == null) {
            return s;
        }
        for (ReturnOrderDealTypeEnum e : ReturnOrderDealTypeEnum.values()) {
            if (e.getKey().equals(key)) {
                s = e.getName();
                return s;
            }
        }
        return key;
    }
}
