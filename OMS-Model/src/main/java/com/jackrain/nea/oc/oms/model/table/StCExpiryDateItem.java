package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

/**
 * st_c_expiry_date_item
 * <AUTHOR>
@TableName
@Data
public class StCExpiryDateItem extends BaseModel {
    @JSONField(name = "ID")
    private Long id;

    /**
     * 主表id
     */
    @JSONField(name = "ST_C_EXPIRY_DATE_ID", alternateNames = "stCExpiryDateId")
    private Long stCExpiryDateId;

    /**
     * 指定维度(1.品项,2.商品编码,3.商品标题,4.平台商品ID)
     * 1.品项,2.商品编码,3.商品标题,4.平台商品ID
     */
    @JSONField(name = "APPOINT_DIMENSION", alternateNames = "appointDimension")
    private Integer appointDimension;

    /**
     * 指定内容
     */
    @JSONField(name = "APPOINT_CONTENT", alternateNames = "appointContent")
    private String appointContent;

    /**
     * 指定类型(1.生产日期范围,2.)生产天数
     */
    @JSONField(name = "APPOINT_TYPE", alternateNames = "appointType")
    private Integer appointType;

    /**
     * 开始生产日期/天数
     */
    @JSONField(name = "START_DATE_DAY", alternateNames = "startDateDay")
    private String startDateDay;

    /**
     * 结束生产日期/天数
     */
    @JSONField(name = "END_DATE_DAY", alternateNames = "endDateDay")
    private String endDateDay;

    /**
     * 订单标签
     */
    @JSONField(name = "ORDER_LABEL", alternateNames = "orderLabel")
    private String orderLabel;


    /**
     * 虚拟字段-开始时间
     */
    @JSONField(name = "START_DATE", alternateNames = "startDate")
    @TableField(exist = false)
    private String startDate;

    /**
     * 虚拟字段-结束时间
     */
    @JSONField(name = "END_DATE", alternateNames = "endDate")
    @TableField(exist = false)
    private String endDate;

    /**
     * 优先便宜快递
     */
    @JSONField(name = "CHEAPEST_EXPRESS")
    private Integer cheapestExpress;


    private static final long serialVersionUID = 1L;
}