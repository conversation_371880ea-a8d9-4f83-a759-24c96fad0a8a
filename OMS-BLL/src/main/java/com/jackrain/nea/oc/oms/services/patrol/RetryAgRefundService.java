package com.jackrain.nea.oc.oms.services.patrol;

import com.jackrain.nea.ip.service.IpOrderCancelToAgService;
import com.jackrain.nea.oc.oms.es.ES4TaoBaoRefund;
import com.jackrain.nea.oc.oms.gsi.GSI4Order;
import com.jackrain.nea.oc.oms.mapper.IpBTaobaoRefundMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2019/11/12 2:57 下午
 * @Version 1.0
 * 重试AG退款失败的订单
 */
@Slf4j
@Component
public class RetryAgRefundService {


    @Autowired
    private IpBTaobaoRefundMapper ipBTaobaoRefundMapper;
    @Autowired
    private IpOrderCancelToAgService ipOrderCancelToAgService;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private OmsOrderLogService omsOrderLogService;


    public ValueHolderV14 selectAgRefundFail(Integer pageSize) {
        ValueHolderV14 holderV14 = new ValueHolderV14();

        //通过es查询退款失败的退单
        List<String> returnId = ES4TaoBaoRefund.findRefundIdByToAgStatus(pageSize);

        List<IpBTaobaoRefund> ipBTaobaoRefunds = ipBTaobaoRefundMapper.selectTaobaoRefundByRefundIdList(returnId);
        //调用ag退款
        User rootUser = SystemUserResource.getRootUser();
        if (CollectionUtils.isNotEmpty(ipBTaobaoRefunds)) {
            for (IpBTaobaoRefund ipBTaobaoRefund : ipBTaobaoRefunds) {
                ValueHolderV14 valueHolderV14 = ipOrderCancelToAgService.orderCancelToAgRetry(ipBTaobaoRefund, rootUser);
                if (valueHolderV14 == null) {
                    ipBTaobaoRefundMapper.updateRefundType(ipBTaobaoRefund.getRefundId(), 3);
                    continue;
                }
                if (valueHolderV14.getCode() == 0) {
                    //调用ag成功 //将成功日志更新到订单中
                    this.updateOrder(ipBTaobaoRefund.getTid(), ipBTaobaoRefund.getOid(),
                            ipBTaobaoRefund.getRefundId(), rootUser);
                    ipBTaobaoRefundMapper.updateRefundType(ipBTaobaoRefund.getRefundId(), 2);

                } else {
                    ipBTaobaoRefundMapper.updateRefundType(ipBTaobaoRefund.getRefundId(), 3);
                }

            }

        }
        holderV14.setCode(0);
        holderV14.setMessage("重试成功");
        return holderV14;
    }

    private void updateOrder(Long tid, Long oid, String refundId, User user) {
        //    List<Long> orderIds = ES4Order.getIdsBySourceCode(tid);
        if (tid == null) {
            return;
        }
        List<Long> orderIds = GSI4Order.getIdListBySourceCode(String.valueOf(tid));

        //通过tid及oid判断出退款的明细在哪里
        List<OcBOrderItem> orderItems = ocBOrderItemMapper.selectOrderItemListOoid(oid + "", orderIds);
        if (CollectionUtils.isNotEmpty(orderItems)) {
            for (OcBOrderItem orderItem : orderItems) {
                //在订单明细表打日志信心
                omsOrderLogService.addUserOrderLog(orderItem.getOcBOrderId(), "", OrderLogTypeEnum.AG_SEND_CANCLE.getKey(), "退款单号为:" + refundId + ",重试订单AG取消发货成功", null, null, user);
            }
        }
    }
}
