package com.jackrain.nea.oc.oms.util;

/**
 * 锁定订单类型
 *
 * @author: 易邵峰
 * @since: 2019-01-28
 * create at : 2019-01-28 09:19
 */
public enum LockOrderType {
    /**
     * 默认锁定类型
     */
    DEFAULT,

    /**
     * 淘宝订单
     */
    TRANSFER_TAOBAO_ORDER,

    /**
     * 淘宝分销订单
     */
    TRANSFER_FX_TAOBAO_ORDER,

    /**
     * 淘宝退单
     */
    TRANSFER_TAOBAO_REFUND,

    /**
     * 淘宝分销退单
     */
    TRANSFER_FX_TAOBAO_REFUND,

    /**
     * 淘宝退换货订单服务
     */
    TRANSFER_TAOBAO_EXCHANGE,

    /**
     * 通用中间表转全渠道订单服务
     */
    TRANSFER_STANDPLAT_ORDER,

    /**
     * 通用换货
     */
    TRANSFER_STANDPLAT_EXCHANGE,

    /**
     * 待审核订单
     */
    UNCONFIRMED_ORDER,

    /**
     * 待拆分订单
     */
    SPLIT_ORDER,

    /**
     * WMS流程
     */
    CALL_WMS_PROCESS,

    /**
     * WMS被调用
     */
    PROVIDER_WMS_PROCESS,

    /**
     * 审核订单
     */
    AUDIT_ORDER,

    /**
     * 京东中间表转全渠道订单服务
     */
    TRANSFER_JINGDONG_ORDER,

    /**
     * 京东退单
     */
    TRANSFER_JINGDONG_REFUND,

    /**
     * 京东取消订单
     */
    TRANSFER_JINGDONG_CANCEL_ORDER,

    /**
     * 通用退单
     */
    CURRENCY_REFUND,
    /**
     * JITX订单
     */
    TRANSFER_JITX_ORDER,

    /**
     * JITX退单
     */
    TRANSFER_JITX_REFUND,
    /**
     * JITX待寻仓订单
     */
    TRANSFER_JITX_DELIVERY,
    /**
     * JITX取消时效订单
     */
    TRANSFER_JITX_CANCELTIMEORDER,
    /**
     * JITX时效订单
     */
    TRANSFER_JITX_TIMEORDER,
    /**
     * 淘宝经销订单
     */
    TRANSFER_TAOBAO_JX_ORDER,
    /**
     * 淘宝锁单
     */
    LOCK_ORDER,

    /**
     * 唯品会退供单
     */
    TRANSFER_VIP_RETURN_ORDER,

    /**
     * 猫超直发退单
     */
    TRANSFER_ALIBABA_ASCP_REFUND,

    /**
     * 猫超直发取消订单
     */
    TRANSFER_ALIBABA_ASCP_CANCEL_ORDER,

    /**
     * 猫超直发订单
     */
    ALIBABA_ASCP_ORDER,

    /**
     * 京东厂直取消
     */
    TRANSFER_JD_DIRECT_CANCEL,

    /**
     * 京东厂直订单
     */
    TRANSFER_JINGDONG_DIRECT_ORDER;

    /**
     * 转换成Redis关键字
     *
     * @return Redis关键字
     */
    public String toRedisKeyword() {
        if (this == LockOrderType.TRANSFER_TAOBAO_ORDER) {
            return "tborder";
        } else if (this == LockOrderType.TRANSFER_FX_TAOBAO_ORDER) {
            return "tbfxorder";
        } else if (this == LockOrderType.UNCONFIRMED_ORDER) {
            return "unconfirmed";
        } else if (this == LockOrderType.TRANSFER_TAOBAO_REFUND) {
            return "tbrefund";
        } else if (this == LockOrderType.TRANSFER_FX_TAOBAO_REFUND) {
            return "tbfxrefund";
        } else if (this == LockOrderType.SPLIT_ORDER) {
            return "split";
        } else if (this == LockOrderType.TRANSFER_TAOBAO_EXCHANGE) {
            return "tbexchange";
        } else if (this == LockOrderType.CALL_WMS_PROCESS) {
            return "wmsprocess";
        } else if (this == LockOrderType.PROVIDER_WMS_PROCESS) {
            return "wmsprovider";
        } else if (this == LockOrderType.DEFAULT) {
            return "default";
        } else if (this == LockOrderType.AUDIT_ORDER) {
            return "audit";
        } else if (this == LockOrderType.TRANSFER_JINGDONG_ORDER) {
            return "jdorder";
        } else if (this == LockOrderType.TRANSFER_JINGDONG_REFUND) {
            return "jdrefund";
        } else if (this == LockOrderType.TRANSFER_JITX_ORDER) {
            return "jitxorder";
        } else if (this == LockOrderType.TRANSFER_JITX_REFUND) {
            return "jitxrefund";
        } else if (this == LockOrderType.CURRENCY_REFUND) {
            return "currencyrefund";
        } else if (this == LockOrderType.TRANSFER_STANDPLAT_ORDER) {
            return "standplatOrder";
        } else if (this == LockOrderType.TRANSFER_TAOBAO_JX_ORDER) {
            return "taobaojx";
        } else if (this == LockOrderType.TRANSFER_JITX_DELIVERY) {
            return "jitxdelivery";
        } else if (this == LockOrderType.TRANSFER_JITX_CANCELTIMEORDER) {
            return "jitxcanceltimeorder";
        } else if (this == LockOrderType.TRANSFER_JITX_TIMEORDER) {
            return "jitxtimeorder";
        } else if (this == LockOrderType.LOCK_ORDER) {
            return "lockorder";
        } else if (this == LockOrderType.TRANSFER_ALIBABA_ASCP_REFUND) {
            return "transferalibabaascprefund";
        } else if (this == LockOrderType.TRANSFER_ALIBABA_ASCP_CANCEL_ORDER) {
            return "transferalibabaascpcancelorder";
        } else if (this == LockOrderType.ALIBABA_ASCP_ORDER) {
            return "alibabaascp";
        }else if(this==LockOrderType.TRANSFER_STANDPLAT_EXCHANGE){
            return "standplatExchange";
        } else if (this == LockOrderType.TRANSFER_JINGDONG_DIRECT_ORDER) {
            return "jdDirectOrder";
        } else if (this == LockOrderType.TRANSFER_JD_DIRECT_CANCEL) {
            return "jddirectcancel";
        } else {
            return "others";
        }
    }

}
