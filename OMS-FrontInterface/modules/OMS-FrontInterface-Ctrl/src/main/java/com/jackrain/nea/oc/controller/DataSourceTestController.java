package com.jackrain.nea.oc.controller;

import com.jackrain.nea.jdbc.datasource.TargetDataSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.http.ResponseEntity;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * oms-- drds、adb
 */
@RestController
@RequestMapping("/api/test")
public class DataSourceTestController {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private ApplicationContext applicationContext;

    @GetMapping("/datasource/status")
    public ResponseEntity<Map<String, Object>> testDataSourceConnection() {
        Map<String, Object> result = new HashMap<>();

        try {
            jdbcTemplate.queryForObject("SELECT 1", Integer.class);
            result.put("drds", "连接正常");
        } catch (Exception e) {
            result.put("drds", "连接异常: " + e.getMessage());
        }

        try {
            DataSourceTestController proxy = applicationContext.getBean(DataSourceTestController.class);
            proxy.testSecondaryDataSource();
            result.put("adbDataSource", "连接正常");
        } catch (Exception e) {
            result.put("secondaryDataSource", "连接异常: " + e.getMessage());
        }

        return ResponseEntity.ok(result);
    }

    @TargetDataSource(name = "adb")
    public void testSecondaryDataSource() {
        jdbcTemplate.queryForObject("SELECT 1", Integer.class);
    }
}