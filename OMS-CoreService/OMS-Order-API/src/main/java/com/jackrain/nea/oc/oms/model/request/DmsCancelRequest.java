package com.jackrain.nea.oc.oms.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * dms cancel request
 *
 * <AUTHOR>
 */
@Data
public class DmsCancelRequest implements Serializable {

    /**
     * 平台单号
     */
    @JSONField(name = "VERLN")
    private String tid;

    /**
     * 是否整单取消
     */
    @J<PERSON>NField(name = "ALL_CANCEL")
    private Boolean allCancel;

    /**
     * 1:正，2:退
     */
    @JSONField(name = "FUN")
    private Integer fun;

    /**
     * 明细
     */
    @JSONField(name = "ITEMS")
    private List<Item> items;

    @Data
    public static class Item {
        /**
         * 行号
         */
        @JSONField(name = "LINE")
        private Integer line;

        /**
         * sku
         */
        @JSONField(name = "SKU")
        private String sku;

        /**
         * 数量
         */
        @JSONField(name = "QTY")
        private BigDecimal qty;
    }
}
