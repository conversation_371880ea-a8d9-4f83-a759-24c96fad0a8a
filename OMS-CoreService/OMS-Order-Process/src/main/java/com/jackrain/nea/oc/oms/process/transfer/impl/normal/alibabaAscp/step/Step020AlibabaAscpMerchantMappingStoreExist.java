package com.jackrain.nea.oc.oms.process.transfer.impl.normal.alibabaAscp.step;

import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpAlibabaAscpOrderRelation;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: 秦雄飞
 * @date 2020/9/3 下午4:32
 */
@Step(order = 20, description = "供应商id是否能找到对应店铺")
@Slf4j
@Component
public class Step020AlibabaAscpMerchantMappingStoreExist extends BaseAlibabaAscpOrderProcessStep
        implements IOmsOrderProcessStep<IpAlibabaAscpOrderRelation> {

    @Autowired
    private CpRpcService cpRpcService;

    @Override
    public ProcessStepResult<IpAlibabaAscpOrderRelation> startProcess(IpAlibabaAscpOrderRelation orderInfo,
                                                                      ProcessStepResult preStepResult,
                                                                      boolean isAutoMakeup, User operateUser) {
        String orderNo = orderInfo.getOrderNo();
        String supplierId = orderInfo.getAlibabaAscpOrder().getSupplierId();
        List<CpShop> cpShopList = cpRpcService.queryShopBySupplierId(supplierId);
        if (CollectionUtils.isNotEmpty(cpShopList)) {
            return new ProcessStepResult<>(StepStatus.SUCCESS, "供应商id,店铺校验成功，orderId =" + orderInfo.getOrderId() + ",supplierId=" + supplierId + ",shopId=" + cpShopList.get(0).getId() + "进入下一阶段");
        } else {
            String message = "找不到供应商ID为" + supplierId + "的店铺";
            ipAlibabaAscpOrderService.updateAlibabaAscpOrderTransStatus(orderNo,
                    TransferOrderStatus.TRANSFERFAIL, message);
            return new ProcessStepResult<>(StepStatus.FAILED, message);
        }
    }
}