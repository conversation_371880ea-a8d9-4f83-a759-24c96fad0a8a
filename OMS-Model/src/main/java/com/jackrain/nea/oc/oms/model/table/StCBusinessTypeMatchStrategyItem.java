package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;


/**
 * @program: r3-oc-oms
 * @description: 业务类型匹配策略
 * @author: caomalai
 * @create: 2022-07-14 14:39
 **/
@TableName(value = "st_c_business_type_match_strategy_item")
@Data
public class StCBusinessTypeMatchStrategyItem extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 业务类型ID
     */
    @JSONField(name = "ST_C_BUSINESS_TYPE_MATCH_STRATEGY_ID")
    private Long stCBusinessTypeMatchStrategyId;

    /**
     * 识别类型 1=交易平台 2=物料组 3=SAP单据类型 4=商品实付
     */
    @JSONField(name = "DISCERN_TYPE")
    private Integer discernType;


    /**
     * 识别内容
     */
    @JSONField(name = "DISCERN_CONTENT")
    private String discernContent;

    /**
     * 运算符
     */
    @JSONField(name = "DISCERN_SYMBOL")
    private Integer discernSymbol;
}
