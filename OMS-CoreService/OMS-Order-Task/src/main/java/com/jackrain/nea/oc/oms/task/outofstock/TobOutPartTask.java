package com.jackrain.nea.oc.oms.task.outofstock;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.google.common.base.Throwables;
import com.jackrain.nea.ac.utils.CommonImportEnum;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.mapper.CommonIdempotentMapper;
import com.jackrain.nea.oc.oms.model.enums.CommonIdempotentTypeEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.CommonIdempotent;
import com.jackrain.nea.oc.oms.process.tobeconfirm.ToBeConfirmedOrderProcess;
import com.jackrain.nea.oc.oms.services.OmsOrderItemService;
import com.jackrain.nea.oc.oms.services.OmsOrderOutService;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.oc.oms.util.DingTalkUtil;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.RuntimeCompute;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * tob部分出库生成订单任务
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class TobOutPartTask extends BaseR3Task implements IR3Task {

    @NacosValue(value = "${r3.oc.oms.tob.out.part.limit:200}", autoRefreshed = true)
    private Integer limit;

    @Autowired
    private OmsOrderOutService outService;

    @Autowired
    private CommonIdempotentMapper commonIdempotentMapper;

    @Override
    @XxlJob("TobOutPartTask")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult result = new RunTaskResult();
        try {
            //查询需要处理的数据
            List<CommonIdempotent> commonIdempotents = commonIdempotentMapper.selectAvailableDatasLimit(CommonIdempotentTypeEnum.TOB_PART_OUT.getKey(), limit);
            if (CollectionUtils.isEmpty(commonIdempotents)) {
                result.setSuccess(true);
                result.setMessage("无数据需要处理");
                return result;
            }

            if (commonIdempotents.size() == limit) {
                DingTalkUtil.dingTobOutPart(0L, "tob部分出库定时任务处理数据达到limit:+" + limit + "的限制");
            }

            List<String> businessCode = commonIdempotents.stream().map(CommonIdempotent::getBusinessCode).collect(Collectors.toList());
            for (String orderId : businessCode) {
                try {
                    outService.outPartCreateNewOrder(Long.valueOf(orderId));
                } catch (Exception e) {
                    log.error(LogUtil.format("ToBOutPartTask.Execute orderDeal error orderId:{}", "tob部分出库生成订单任务"), orderId, e);
                    DingTalkUtil.dingTobOutPart(Long.valueOf(orderId), "处理生成新订单失败");
                }
            }

            log.info(LogUtil.format("tob部分出库生成订单任务执行完成", "tob部分出库生成订单任务"));
            result.setSuccess(true);
            result.setMessage("success");
        } catch (Exception ex) {
            log.error(LogUtil.format("ToBOutPartTask.Execute error", "tob部分出库生成订单任务"), ex);
            result.setSuccess(false);
            result.setMessage(ex.getMessage());
        }
        return result;
    }
}
