package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBReturnOrderVirtualLibraryCmd;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * 退单虚拟入库
 *
 * <AUTHOR>
 * @date 2019/3/26
 */
@Api(value = "OcBOrderCtrl", tags = "退单虚拟入库")
@Slf4j
@RestController
public class OcBResultOrderCtrl {

    @Autowired
    private OcBReturnOrderVirtualLibraryCmd ocBReturnOrderVirtualLibraryCmd;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    /**
     * 退单虚拟入库
     *
     * @param request
     * @param obj
     * @return
     */
    @ApiOperation(value = "虚拟入库")
    @RequestMapping(value = "/api/cs/oc/oms/v1/updateVirtualLibrary", method = RequestMethod.POST)
    public JSONObject updateVirtualLibrary(HttpServletRequest request,
                                           @RequestBody JSONObject obj) {
        ValueHolderV14 vh;
        //获取当前登陆用户
        //User user = OcBResultOrderCtrl.getRootUser();

        //获取用户登录信息
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        if (obj == null) {
            throw new NDSException(Resources.getMessage("请求参数不能为空!", user.getLocale()));
        }

        try {
            vh = ocBReturnOrderVirtualLibraryCmd.batchExecute(obj.getJSONArray("ids"), user);

            //记录日志信息。Finish 标记结束
            if (log.isDebugEnabled()) {
                log.debug("start OcBOrderCtrl.orderTheAudit.ReceiveParams=" + vh.toJSONObject());
            }
            return vh.toJSONObject();
        } catch (NDSException e) {
            vh = new ValueHolderV14();
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return vh.toJSONObject();

        }

    }

}

