package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Document(index = "oc_b_return_af_send", type = "oc_b_return_af_send")
@ApiModel(value = "oc_b_return_af_send", description = "发货后退款单")
public class OcBReturnAfSend extends BaseModel {


    @ApiModelProperty(value = "ID")
    @Field(type = FieldType.Long)
    @JSONField(name = "ID")
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @ApiModelProperty(value = "单据编号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "BILL_NO")
    private String billNo;

    @ApiModelProperty(value = "店铺ID")
    @Field(type = FieldType.Long)
    @JSONField(name = "CP_C_SHOP_ID")
    private Long cpCShopId;

    @ApiModelProperty(value = "店铺编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CP_C_SHOP_ECODE")
    private String cpCShopEcode;

    @ApiModelProperty(value = "店铺标题")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CP_C_SHOP_TITLE")
    private String cpCShopTitle;

    @ApiModelProperty(value = "退款状态")
    @Field(type = FieldType.Integer)
    @JSONField(name = "RETURN_STATUS")
    private Integer returnStatus;

    @ApiModelProperty(value = "打款时间")
    @Field(type = FieldType.Long)
    @JSONField(name = "RETURN_PAYMENT_TIME")
    private Date returnPaymentTime;

    @ApiModelProperty(value = "反审核时间")
    @Field(type = FieldType.Long)
    @JSONField(name = "UN_EXAMINE_TIME")
    private Date unExamineTime;

    @ApiModelProperty(value = "打款状态:0 未打款 1 打款中 2 打款成功 3 打款失败")
    @Field(type = FieldType.Integer)
    @JSONField(name = "PAYMENT_STATUS")
    private Integer paymentStatus;

    @ApiModelProperty(value = "平台单号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "TID")
    private String tid;

    @ApiModelProperty(value = "平台退款单号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "T_RETURN_ID")
    private String tReturnId;

    @ApiModelProperty(value = "单据类型 0 退货退款 1仅退款")
    @Field(type = FieldType.Integer)
    @JSONField(name = "BILL_TYPE")
    private Integer billType;

    @ApiModelProperty(value = "平台退款状态")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "T_RETURN_STATUS")
    private String tReturnStatus;

    @ApiModelProperty(value = "AG状态")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "AG_STATUS")
    private String agStatus;

    @ApiModelProperty(value = "会员昵称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "VIP_NICK")
    private String vipNick;

    @ApiModelProperty(value = "会员手机号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "VIP_PHONE")
    private String vipPhone;

    @ApiModelProperty(value = "退款原因")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "REASON")
    private String reason;

    @ApiModelProperty(value = "支付方式")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PAY_MODE")
    private String payMode;

    @ApiModelProperty(value = "支付账号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PAY_ACCOUNT")
    private String payAccount;

    @ApiModelProperty(value = "申请退款金额")
    @Field(type = FieldType.Double)
    @JSONField(name = "AMT_RETURN_APPLY")
    private BigDecimal amtReturnApply;

    @ApiModelProperty(value = "实际退款金额")
    @Field(type = FieldType.Double)
    @JSONField(name = "AMT_RETURN_ACTUAL")
    private BigDecimal amtReturnActual;

    @ApiModelProperty(value = "申请退款时间")
    @Field(type = FieldType.Long)
    @JSONField(name = "RETURN_APPLY_TIME")
    private Date returnApplyTime;

    @ApiModelProperty(value = "退款超时时间")
    @Field(type = FieldType.Long)
    @JSONField(name = "RETURN_OVER_TIME")
    private Date returnOverTime;

    @ApiModelProperty(value = "审核时间")
    @Field(type = FieldType.Long)
    @JSONField(name = "CHECK_TIME")
    private Date checkTime;

    @ApiModelProperty(value = "财审时间")
    @Field(type = FieldType.Long)
    @JSONField(name = "FINANCIAL_AUDIT_TIME")
    private Date financialAuditTime;

    @ApiModelProperty(value = "传sap状态 0未传 1 传中 2已传 3传失败")
    @Field(type = FieldType.Long)
    @JSONField(name = "SAP_STATUS")
    private Long sapStatus;

    @ApiModelProperty(value = "备注")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "REMARK")
    private String remark;

    @ApiModelProperty(value = "卖家备注")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SELLER_REMARK")
    private String sellerRemark;

    @ApiModelProperty(value = "版本号")
    @Field(type = FieldType.Long)
    @JSONField(name = "VERSION")
    private Long version;

    @ApiModelProperty(value = "创建人姓名")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @ApiModelProperty(value = "修改人姓名")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    @ApiModelProperty(value = "退货状态")
    @Field(type = FieldType.Integer)
    @JSONField(name = "PRO_RETURN_STATUS")
    private Integer proReturnStatus;

    @ApiModelProperty(value = "平台类型")
    @Field(type = FieldType.Long)
    @JSONField(name = "CP_C_PLATFORM_ID")
    private Long cpCPlatformId;

    @ApiModelProperty(value = "判责方")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RESPONSIBLE_PARTY")
    private String responsibleParty;

    @ApiModelProperty(value = "原订单编号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SOURCE_BILL_NO")
    private String sourceBillNo;

    @ApiModelProperty(value = "判责方备注 ")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RESPONSIBLE_PARTY_REMARK")
    private String responsiblePartyRemark;

    @ApiModelProperty(value = "退款账号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RETURN_ACCOUNT")
    private String returnAccount;

    @ApiModelProperty(value = "退款说明")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RETURN_EXPLAIN")
    private String returnExplain;

    @ApiModelProperty(value = "sap失败原因")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SAP_FAILURE_REASON")
    private String sapFailureReason;

    @ApiModelProperty(value = "sap失败次数")
    @Field(type = FieldType.Long)
    @JSONField(name = "SAP_FAILURE_TIMES")
    private Long sapFailureTimes;

    @ApiModelProperty(value = "待传结算标志（0:默认，1:待传，2：已传，3:失败）")
    @Field(type = FieldType.Integer)
    @JSONField(name = "TO_SETTLE_STATUS")
    private Integer toSettleStatus;

    @ApiModelProperty(value = "收货人姓名")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RECEIVER_NAME")
    private String receiverName;

    @ApiModelProperty(value = "收货人手机")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RECEIVER_MOBILE")
    private String receiverMobile;

    @ApiModelProperty(value = "总运费")
    @Field(type = FieldType.Double)
    @JSONField(name = "FREIGHT_PRICE")
    private BigDecimal freightPrice;

    @ApiModelProperty(value = "退款金额")
    @Field(type = FieldType.Double)
    @JSONField(name = "REFUND_FEE")
    private BigDecimal refundFee;

    @ApiModelProperty(value = "拦截状态")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "INTERCERPT_STATUS")
    private String intercerptStatus;

    @ApiModelProperty(value = "拒收状态")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "REFUND_STATUS")
    private String refundStatus;

    @ApiModelProperty(value = "平台商品ID")
    @Field(type = FieldType.Double)
    @JSONField(name = "WARE_ID")
    private BigDecimal wareId;

    @ApiModelProperty(value = "平台商品编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PS_C_SKU_PT_ECODE")
    private String psCSkuPtEcode;

    @ApiModelProperty(value = "平台SKU规格")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SKU_SPEC")
    private String skuSpec;

    @ApiModelProperty(value = "购买数量")
    @Field(type = FieldType.Double)
    @JSONField(name = "QUANTITY")
    private BigDecimal quantity;

    @ApiModelProperty(value = "单据来源 1 手动 2自动")
    @Field(type = FieldType.Integer)
    @JSONField(name = "REFUND_ORDER_SOURCE_TYPE")
    private Integer refundOrderSourceType;

    @ApiModelProperty(value = "退款分类")
    @Field(type = FieldType.Long)
    @JSONField(name = "OC_B_RETURN_TYPE_ID")
    private Long ocBReturnTypeId;

    @ApiModelProperty(value = "退款描述")
    @Field(type = FieldType.Long)
    @JSONField(name = "OC_B_RETURN_TYPE_ITEM_ID")
    private Long ocBReturnTypeItemId;

    @ApiModelProperty(value = "退款分类名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "OC_B_RETURN_TYPE_ENAME")
    private String ocBReturnTypeEname;

    @ApiModelProperty(value = "退款描述名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "OC_B_RETURN_TYPE_ITEM_ENAME")
    private String ocBReturnTypeItemEname;

    @Field(type = FieldType.Keyword)
    @JSONField(name = "IMAGE")
    private String image;

    @ApiModelProperty(value = "打款失败原因")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PAYMENT_FAIL_REASON")
    private String paymentFailReason;

    @ApiModelProperty(value = "数字类型备用字段1")
    @Field(type = FieldType.Long)
    @JSONField(name = "RESERVE_BIGINT01")
    private Long reserveBigint01;

    @ApiModelProperty(value = "数字类型备用字段2")
    @Field(type = FieldType.Long)
    @JSONField(name = "RESERVE_BIGINT02")
    private Long reserveBigint02;

    @ApiModelProperty(value = "数字类型备用字段3")
    @Field(type = FieldType.Long)
    @JSONField(name = "RESERVE_BIGINT03")
    private Long reserveBigint03;

    @ApiModelProperty(value = "数字类型备用字段4")
    @Field(type = FieldType.Long)
    @JSONField(name = "RESERVE_BIGINT04")
    private Long reserveBigint04;

    @ApiModelProperty(value = "数字类型备用字段5")
    @Field(type = FieldType.Long)
    @JSONField(name = "RESERVE_BIGINT05")
    private Long reserveBigint05;

    @ApiModelProperty(value = "价格备用字段1")
    @Field(type = FieldType.Double)
    @JSONField(name = "RESERVE_DECIMAL01")
    private BigDecimal reserveDecimal01;

    @ApiModelProperty(value = "价格备用字段2")
    @Field(type = FieldType.Double)
    @JSONField(name = "RESERVE_DECIMAL02")
    private BigDecimal reserveDecimal02;

    @ApiModelProperty(value = "价格备用字段3")
    @Field(type = FieldType.Double)
    @JSONField(name = "RESERVE_DECIMAL03")
    private BigDecimal reserveDecimal03;

    @ApiModelProperty(value = "价格备用字段4")
    @Field(type = FieldType.Double)
    @JSONField(name = "RESERVE_DECIMAL04")
    private BigDecimal reserveDecimal04;

    @ApiModelProperty(value = "价格备用字段5")
    @Field(type = FieldType.Double)
    @JSONField(name = "RESERVE_DECIMAL05")
    private BigDecimal reserveDecimal05;

    @ApiModelProperty(value = "文本备用字段1")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RESERVE_VARCHAR01")
    private String reserveVarchar01;

    @ApiModelProperty(value = "文本备用字段2")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RESERVE_VARCHAR02")
    private String reserveVarchar02;

    @ApiModelProperty(value = "文本备用字段3")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RESERVE_VARCHAR03")
    private String reserveVarchar03;

    @ApiModelProperty(value = "文本备用字段4")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RESERVE_VARCHAR04")
    private String reserveVarchar04;

    @ApiModelProperty(value = "文本备用字段5")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RESERVE_VARCHAR05")
    private String reserveVarchar05;

    @ApiModelProperty(value = "平台货物状态")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PT_GOOD_STATUS")
    private String ptGoodStatus;

    @ApiModelProperty(value = "平台退款状态")
    @Field(type = FieldType.Integer)
    @JSONField(name = "HAS_GOOD_RETURN")
    private Integer hasGoodReturn;

    /**
     * 0-未传DRP；1-传DRP中；2-传DRP成功；3-传DRP失败
     */
    @ApiModelProperty(value = "传DRP状态")
    @JSONField(name = "TO_DRP_STATUS")
    @Field(type = FieldType.Keyword)
    private String toDrpStatus;

    @ApiModelProperty(value = "传DRP次数")
    @JSONField(name = "TO_DRP_COUNT")
    @Field(type = FieldType.Integer)
    private Integer toDrpCount;

    @ApiModelProperty(value = "传DRP失败原因")
    @JSONField(name = "TO_DRP_FAILED_REASON")
    @Field(type = FieldType.Keyword)
    private String toDrpFailedReason;

    @ApiModelProperty(value = "订单业务类型id")
    @Field(type = FieldType.Long)
    @JSONField(name = "BUSINESS_TYPE_ID")
    private Long businessTypeId;

    @ApiModelProperty(value = "业务类型编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "BUSINESS_TYPE_CODE")
    private String businessTypeCode;

    @ApiModelProperty(value = "业务类型名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "BUSINESS_TYPE_NAME")
    private String businessTypeName;

    @ApiModelProperty(value = "新零售发货单id")
    @Field(type = FieldType.Long)
    @JSONField(name = "NEW_OC_B_ORDER_ID")
    private Long newOcBOrderId;

    @ApiModelProperty(value = "奶卡自动作废状态")
    @Field(type = FieldType.Integer)
    @JSONField(name = "CARD_AUTO_VOID")
    private Integer cardAutoVoid;

    @ApiModelProperty(value = "系统备注")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SYSREMARK")
    private String sysremark;

    @ApiModelProperty(value = "是否需要执行奶卡自动作废状态")
    @Field(type = FieldType.Integer)
    @JSONField(name = "CARD_AUTO_VOID_MARK")
    private Integer cardAutoVoidMark;

    @ApiModelProperty(value = "是否需要执行过奶卡自动作废状态")
    @Field(type = FieldType.Integer)
    @JSONField(name = "VOID_MARK")
    private Integer voidMark;

    @ApiModelProperty(value = "已发货退款单单据来源")
    @Field(type = FieldType.Integer)
    @JSONField(name = "REFUND_SOURCE")
    private Integer refundSource;
}