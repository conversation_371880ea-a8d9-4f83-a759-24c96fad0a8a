package com.jackrain.nea.oc.oms.model.enums;

import lombok.Getter;

/**
 * 门店接单状态枚举值
 *
 * @Auther: 黄志优
 * @Date: 2020/8/29 14:43
 * @Description:
 */
public enum OcOrderStoreDeliveryStatusEnum {

    HAVE_ROUTED("已路由", 0),
    HAVE_ORDER("已接单", 1);

    @Getter
    String key;
    @Getter
    int val;

    OcOrderStoreDeliveryStatusEnum(String key, int val) {
        this.key = key;
        this.val = val;
    }

    /**
     * 根据状态值,获取状态名
     *
     * @param integer integer
     * @return String
     */
    public static String enumToStringByValue(Integer integer) {
        String s = "";
        if (integer == null) {
            return s;
        }
        for (OcOrderStoreDeliveryStatusEnum e : OcOrderStoreDeliveryStatusEnum.values()) {
            if (e.getVal() == integer) {
                s = e.getKey();
                break;
            }
        }
        return s;
    }
}
