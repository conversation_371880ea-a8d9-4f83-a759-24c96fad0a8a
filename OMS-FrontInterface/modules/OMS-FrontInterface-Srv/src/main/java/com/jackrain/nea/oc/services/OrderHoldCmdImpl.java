package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OrderHoldCmd;
import com.jackrain.nea.oc.oms.model.request.OrderHoldRequest;
import com.jackrain.nea.oc.oms.model.result.OrderDetentionFutureResult;
import com.jackrain.nea.oc.oms.services.OcBOrderHoldService;
import com.jackrain.nea.oc.oms.vo.ExecuteErrorVO;
import com.jackrain.nea.oc.oms.vo.OcBOrderImpVO;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * @author: 江家雷
 * @since: 2020/07/04
 * create at : 2020/07/04
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OrderHoldCmdImpl implements OrderHoldCmd {

    @Autowired
    private OcBOrderHoldService ocBOrderHoldService;

    @Override
    public ValueHolder manualUnHoldOrder(JSONObject object, User operateUser) throws NDSException {

        return ocBOrderHoldService.syncTaskUnHold(object, operateUser);
    }

    @Override
    public ValueHolder manualHoldOrder(OrderHoldRequest request, User operateUser) throws NDSException {
        return ocBOrderHoldService.manualHoldOrder(request, operateUser);
    }

    @Override
    public ValueHolder orderDetentionRelease(JSONObject obj, User user) {
        return ocBOrderHoldService.orderDetentionRelease(obj,user);
    }

    @Override
    public ValueHolder orderDetention(JSONObject obj, User user) {
        return ocBOrderHoldService.orderDetention(obj, user, true);
    }

    @Override
    public OrderDetentionFutureResult orderDetentionFuture(JSONObject obj, User operateUser) {
        return ocBOrderHoldService.orderDetentionFuture(obj, operateUser, true);
    }

    @Override
    public String exportImpErrorResult(List<ExecuteErrorVO> executeErrors, User user, String origFileName) {
        return ocBOrderHoldService.exportExecuteErrorResult(executeErrors, user, origFileName);
    }


}
