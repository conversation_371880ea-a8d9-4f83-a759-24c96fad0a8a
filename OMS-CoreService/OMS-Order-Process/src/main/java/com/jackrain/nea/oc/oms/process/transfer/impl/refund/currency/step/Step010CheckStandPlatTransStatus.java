package com.jackrain.nea.oc.oms.process.transfer.impl.refund.currency.step;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderRefundMapper;
import com.jackrain.nea.oc.oms.model.Standplat.IpBStandplatRefudStatusEnum;
import com.jackrain.nea.oc.oms.model.Standplat.IpBStandplatRefundType;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.enums.TransNodeTipEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsStandPlatRefundRelation;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatRefund;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatRefundItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.refund.util.TransRefundNodeTipUtil;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.oc.oms.util.OmsPlatformUtil;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;


/**
 * @Author: 黄世新
 * @Date: 2020/2/27 2:53 下午
 * @Version 1.0
 */
@Step(order = 10, description = "判断退单转换状态")
@Slf4j
@Component
public class Step010CheckStandPlatTransStatus extends BaseStanPlatRefundProcessStep
        implements IOmsOrderProcessStep<OmsStandPlatRefundRelation> {

    @Override
    public ProcessStepResult<OmsStandPlatRefundRelation> startProcess(OmsStandPlatRefundRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {

        if (log.isDebugEnabled()) {
            log.debug("{} update standard refund order information to success with process 010 step. into standard refund order info :{}", this.getClass().getName(), JSON.toJSONString(orderInfo));
        }

        IpBStandplatRefund ipBStandplatRefund = orderInfo.getIpBStandplatRefund();
        //标记退货类型为空的通用退已转化
        if (ipBStandplatRefund.getRefundType() == null) {
            String remark = "单据退货类型为空，标记为已转换";
            ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                    remark, ipBStandplatRefund);
            return new ProcessStepResult<>(StepStatus.FINISHED, remark);
        }

        if (ObjectUtil.isNotNull(ipBStandplatRefund.getCreated())
                && ObjectUtil.isNotNull(ipBStandplatRefund.getModifieddate())
                && ObjectUtil.equal(ipBStandplatRefund.getReturnStatus(), IpBStandplatRefudStatusEnum.SUCCESS.getVal())
                && DateUtil.between(ipBStandplatRefund.getCreated(), ipBStandplatRefund.getModifieddate(), DateUnit.DAY) > 30L) {
            log.info("Step010CheckStandPlatTransStatus.startProcess create too orderInfo ={}",
                    JSON.toJSONString(orderInfo));
            String remark = "更新时间较申请时间超过30天，且已退款完成，标记为已转换";
            ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                    remark, ipBStandplatRefund);
            return new ProcessStepResult<>(StepStatus.FINISHED, remark);
        }

        // 如果退单是抖音价保单 则直接标记转换完成 不用走后面的逻辑
        Long platformId = ipBStandplatRefund.getCpCPlatformId();
        if (platformId != null
                && platformId.equals(Long.valueOf(PlatFormEnum.DOU_YIN.getCode()))
                && ipBStandplatRefund.getReserveDecimal06() != null
                && ipBStandplatRefund.getReserveDecimal06() == 1L) {
            String remark = "抖音价保退单，无需转换，直接标记为已转化";
            ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                    remark, ipBStandplatRefund);
            return new ProcessStepResult<>(StepStatus.FINISHED, remark);
        }


        //补发类型，直接标记已转换
        if (IpBStandplatRefundType.RESEND == ipBStandplatRefund.getRefundType()) {
          /*  String remark = "单据退货类型为补寄，标记为已转换";
            ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                    remark, ipBStandplatRefund);
            return new ProcessStepResult<>(StepStatus.FINISHED, remark);*/
            return new ProcessStepResult<>(StepStatus.SUCCESS, "", Step100AfterSaleReissue.class);
        }

        ipBStandplatRefund.setTransFailReason(TransNodeTipEnum.DEFAULT.val());
        //退单编号
        String returnNo = ipBStandplatRefund.getReturnNo();
        //平台单号
        String orderNo = ipBStandplatRefund.getOrderNo();
        //子订单号
        List<String> subOrderId = Lists.newArrayList();
        if (!orderInfo.isFullRefund()) {
            subOrderId = orderInfo.getIpBStandplatRefundItem().stream().filter(item -> item.getSubOrderId() != null)
                    .map(IpBStandplatRefundItem::getSubOrderId).collect(Collectors.toList());
        }
        //退款状态
        Integer returnStatus = ipBStandplatRefund.getReturnStatus();
        String status = TaobaoReturnOrderExt.RefundStandPlatStatus.getValueByKey(returnStatus);
        List<OmsOrderRelation> omsOrderRelation = orderInfo.getOmsOrderRelation();
        OmsPlatformUtil bean = ApplicationContextHandle.getBean(OmsPlatformUtil.class);
        Boolean isSpecialPlatform = bean.isSpecialPlatform(platformId);

        // 特殊订单类型
        if (isSpecialPlatform) {

            log.info("Step010CheckStandPlatTransStatus.startProcess Special type order returnStatus={}",
                    returnStatus);

            //订单的return_status是否为4
            boolean isReturn = IpBStandplatRefudStatusEnum.SUCCESS.getVal().equals(returnStatus);

            if (isReturn) {
                OcBReturnOrderRefundMapper ocReturnOrderRefundMapper = ApplicationContextHandle.getBean(OcBReturnOrderRefundMapper.class);
                List<OcBReturnOrderRefund> ocReturnOrderRefunds = ocReturnOrderRefundMapper.selectList(new LambdaQueryWrapper<OcBReturnOrderRefund>()
                        .eq(OcBReturnOrderRefund::getRefundBillNo, returnNo)
                        .eq(OcBReturnOrderRefund::getIsactive, "Y"));

                //继续判断是否存在对应退换货单
                if (CollectionUtils.isNotEmpty(ocReturnOrderRefunds)) {

                    //更新退货单的物流信息
                    omsRefundOrderService.updateRefundLogicNumber(orderInfo.getIpBStandplatRefund().getLogisticsNo(), orderInfo.getIpBStandplatRefund().getCompanyName(), returnNo, operateUser);
                    //更新退款单的退款状态
                    omsRefundOrderService.updateRefundSlip(returnNo, status);
                    //更新退货单的退款状态
                    omsRefundOrderService.updateReturnOrderItem(returnNo, subOrderId, status);
                    //更新订单明细的退款状态
                    omsRefundOrderService.updateOcOrderStatusInfo(omsOrderRelation, status);

                    String remark = "是特殊订单类型且存在对应退换货单,标记已转换";
                    TransRefundNodeTipUtil.standardTransTipCAS(ipBStandplatRefund, TransNodeTipEnum.DEFAULT);
                    ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                            remark, ipBStandplatRefund);
                    return new ProcessStepResult<>(StepStatus.FINISHED, remark);

                } else {
                    return new ProcessStepResult<>(StepStatus.SUCCESS, "是特殊订单类型且不存在对应退换货单,进入下一阶段!");
                }

            } else {
                String remark = "退单编号【" + returnNo + "】是特殊订单类型但退单状态不符合转换，标记已转换";
                ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                        remark, ipBStandplatRefund);
                return new ProcessStepResult<>(StepStatus.FINISHED, remark);
            }

        }

//
//        // 订单状态判断-传wms不允许继续
//        if (OmsRefundTransferUtil.isForbidOrderRelationTransfer(omsOrderRelation)) {
//            ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(),
//                    SysNotesConstant.SYS_REMARK90, ipBStandplatRefund);
//            return new ProcessStepResult<>(StepStatus.FINISHED, SysNotesConstant.SYS_REMARK90);
//        }

        //更新退货单的物流信息
        omsRefundOrderService.updateRefundLogicNumber(orderInfo.getIpBStandplatRefund().getLogisticsNo(), orderInfo.getIpBStandplatRefund().getCompanyName(), returnNo, operateUser);
//        boolean isGoOn = false;
//        if (subOrderId.isEmpty()) {
//            // 假如子单号为空,根据平台单号去查找相应的退换货单/发货后退款单
//            isGoOn = omsRefundOrderService.isExistReturnRelevantInfo(orderInfo.getTid());
//        }
        //更新退款单的退款状态
        omsRefundOrderService.updateRefundSlip(returnNo, status);
        //更新退货单的退款状态
        omsRefundOrderService.updateReturnOrderItem(returnNo, subOrderId, status);
        //更新订单明细的退款状态
        omsRefundOrderService.updateOcOrderStatusInfo(omsOrderRelation, status);
        Integer isTrans = ipBStandplatRefund.getIstrans();
        if (TransferOrderStatus.NOT_TRANSFER.toInteger() != isTrans) {
            String remark = "单不是未转换状态,转换失败";
            TransRefundNodeTipUtil.standardTransTipCAS(ipBStandplatRefund, TransNodeTipEnum.DEFAULT);
            ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                    remark, ipBStandplatRefund);
            return new ProcessStepResult<>(StepStatus.FINISHED, remark);
        }
        return new ProcessStepResult<>(StepStatus.SUCCESS, "退单转换状态校验成功,进入下一阶段!");
    }
}
