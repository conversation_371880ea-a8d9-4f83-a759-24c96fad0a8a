package com.jackrain.nea.oc.oms.api;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.sys.Command;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

/**
 * 退单从WMS撤回接口
 *
 * @author: 郑立轩
 * @since: 2019/5/15
 * create at : 2019/5/15 16:33
 */
public interface OrderReturnRecallFromWmsServiceCmd extends Command {

    ValueHolderV14 OrderReturnRecallFromWms(JSONObject object, User user);
}
