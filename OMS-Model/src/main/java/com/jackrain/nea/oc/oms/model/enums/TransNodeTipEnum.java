package com.jackrain.nea.oc.oms.model.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * @Desc : 逆向转单节点提示
 * <AUTHOR> xiWen
 * @Date : 2020/11/19
 */
public enum TransNodeTipEnum {

    /**
     * 1. 不存在原始订单
     */
    ORDER_NOT_FOUND(10, "无原单"),

    /**
     * 2. 发货后仅退款,未查询到发货信息
     */
    AF_SEND_NOT_FOUND_SEND_INFO(14, "发货后仅退款,未查询到发货信息"),

    /**
     * 3. Ag调用失败
     */
    AG_FAIL(20, "AG调用失败"),

    /**
     * 4. 订单调用反审核失败
     */
    DE_AUDIT(30, "反审核失败"),

    /**
     * 5. 已仓库发货需要拦截
     */
    DELIVERY_NEED_INTERCEPT(40, "已仓库发货需要拦截"),

    /**
     * 6. 已存在该SKU退换货单可退数量不足
     */
    QTY_NOT_ENOUGH(60, "退换货单可退数量不足"),

    /**
     * 7. 退款单作废失败
     */
    VOID_FAIL(80, "退款单作废失败"),

    /**
     * 0.
     */
    DEFAULT(0, "无");


    /**
     * 值
     */
    Integer val;

    /***
     * 原因
     */
    String txt;

    /**
     * @return 错误原因值
     */
    public Integer val() {
        return this.val;
    }

    /**
     * 获取日志描述
     *
     * @param v 日志类型
     * @return 文本描述
     */
    public static String txt(Integer v) {
        if (v == null) {
            return DEFAULT.txt;
        }
        return keyValuePairs.get(v);
    }

    TransNodeTipEnum(Integer v, String n) {
        this.val = v;
        this.txt = n;
    }

    static Map<Integer, String> keyValuePairs = new HashMap<>();

    static {
        for (TransNodeTipEnum e : TransNodeTipEnum.values()) {
            keyValuePairs.put(e.val, e.txt);
        }
        keyValuePairs.put(null, "");
    }


}
