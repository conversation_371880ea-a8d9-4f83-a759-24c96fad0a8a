package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.api.DynamicListCmd;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.UserPermission;
import com.jackrain.nea.oc.oms.services.DynamicListService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Set;

/**
 * @author: 夏继超
 * @since: 2019/6/19
 * create at : 2019/6/19 16:41
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class DynamicListCmdImpl implements DynamicListCmd {
    @Autowired
    DynamicListService dynamicListService;

    /**
     * 动态返回表头和查询
     *
     * @param param
     * @return
     */
    @Override
    public ValueHolderV14 dynamicList(String param, User user, UserPermission pem) {
        // return dynamicListService.dynamicList(param, user);
        // xiwen.start
        ValueHolderV14 vh = dynamicListService.dynamicList(param, user);
        if (ResultCode.SUCCESS == vh.getCode()) {
            Set<String> forbidCols = pem.getForbiddenColumns();
            if (forbidCols != null && forbidCols.size() > OcBOrderConst.IS_STATUS_IN) {
                JSONObject data = vh.toJSONObject().getJSONObject("data");
                JSONArray columns = data.getJSONArray("columns");
                for (int i = 0; i < columns.size(); i++) {
                    JSONObject obj = columns.getJSONObject(i);
                    String s = obj.getString("key");
                    if (forbidCols.contains(s)) {
                        columns.remove(i--);
                    }
                }

                data.put("columns", columns);
                vh.setData(data);
            }
        }
        return vh;
        // xiwen.end
    }

}
