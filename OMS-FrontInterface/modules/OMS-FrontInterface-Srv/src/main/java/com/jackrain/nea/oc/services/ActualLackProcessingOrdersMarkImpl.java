package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.ActualLackProcessingOrdersMarkCmd;
import com.jackrain.nea.oc.oms.services.ActualLackProcessingOrdersService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @program: R3-OC-OMS-V1.4
 * @author: Liqb
 * @Desc 实缺处理:标记业务逻辑单已处理
 * @create: 2019-07-18 10:00
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class ActualLackProcessingOrdersMarkImpl implements ActualLackProcessingOrdersMarkCmd {

    @Autowired
    private ActualLackProcessingOrdersService actualLackProcessingOrdersService;

    @Override
    public ValueHolderV14 execute(JSONObject obj, User user) throws NDSException {
        return actualLackProcessingOrdersService.mark(obj, user);
    }
}
