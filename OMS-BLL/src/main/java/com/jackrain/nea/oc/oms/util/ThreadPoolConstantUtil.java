package com.jackrain.nea.oc.oms.util;

import java.util.concurrent.ExecutorService;

/**
 * @Author: 黄世新
 * @Date: 2020/7/19 3:18 下午
 * @Version 1.0
 */
public class ThreadPoolConstantUtil {

    //    private static String THREAD_POOL_NAME_REFUND_ORDER = "R3_OMS_TOBAO_REFUND_ORDER_TO_MQ_TASK_THREAD_POOL_%d";
//
//    private static String THREAD_POOL_NAME_STAND_REFUND_ORDER = "R3_OMS_STAND_REFUND_ORDER_TO_MQ_TASK_THREAD_POOL_%d";
//
//    private static String THREAD_POOL_NAME_JD_REFUND_ORDER = "R3_OMS_JD_REFUND_ORDER_TO_MQ_TASK_THREAD_POOL_%d";
//
//    private static String THREAD_POOL_NAME_ORDER_SPLIT = "R3_OMS_ORDER_SPLIT_THREAD_POOL_%d";
//
//    private static String THREAD_POOL_NAME_ORDER_SPLIT_COMPENSATION = "R3_OMS_ORDER_SPLIT_COMPENSATION_THREAD_POOL_%d";
//
    private static String THREAD_POOL_NAME_EQUAL_EXCHANGE = "R3_OMS_ORDER_EQUAL_EXCHANGE_POOL_%d";
//    private static String THREAD_POOL_NAME_THE_AUDIT = "R3_OMS_ORDER_THE_AUDIT_POOL_%d";
//
//    private static String THREAD_POOL_NAME_THE_AUDIT_STOP = "R3_OMS_ORDER_THE_AUDIT_STOP_POOL_%d";

    private static String THREAD_POOL_NAME_ORDER_DETENTION = "R3_OMS_ORDER_DETENTION_POOL_%d";

    //    private static ExecutorService refundOrderExecutor;
//
//    private static ExecutorService standPlatRefundOrderExecutor;
//
//    private static ExecutorService jingDongRefundOrderExecutor;
//
//    private static ExecutorService orderSplitExecutor;
//
//    private static ExecutorService orderSplitCompensationExecutor;
//
    private static ExecutorService orderEqualExchangeExecutor;
//
//    private static ExecutorService orderTheAuditPollExecutor;
//
//    private static ExecutorService orderTheAuditStopPollExecutor;

    private static ExecutorService orderDetentionPollExecutor;

    private ThreadPoolConstantUtil() {

    }

//    /**
//     * 退单转换线城池
//     */
//    public static ExecutorService getRefundOrderExecutor() {
//        if (refundOrderExecutor == null) {
//            //ArrayBlockingQueue blockingQueue = new ArrayBlockingQueue<>(16);
//            refundOrderExecutor = new ThreadPoolExecutor(
//                    32,
//                    32,
//                    0,
//                    TimeUnit.SECONDS,
//                    new LinkedBlockingDeque<>(32),
//                    new ThreadFactoryBuilder().setNameFormat(THREAD_POOL_NAME_REFUND_ORDER).build());
//
//        }
//        return refundOrderExecutor;
//    }
//
//    /**
//     * 通用退单转换线城池
//     */
//    public static ExecutorService getStandPlatRefundOrderExecutor() {
//        if (standPlatRefundOrderExecutor == null) {
//            standPlatRefundOrderExecutor = new ThreadPoolExecutor(
//                    32,
//                    32,
//                    0,
//                    TimeUnit.SECONDS,
//                    new LinkedBlockingDeque<>(32),
//                    new ThreadFactoryBuilder().setNameFormat(THREAD_POOL_NAME_STAND_REFUND_ORDER).build());
//
//        }
//        return standPlatRefundOrderExecutor;
//    }
//
//    /**
//     * 京东退单转换线城池
//     */
//    public static ExecutorService getJingDongRefundOrderExecutor() {
//        if (jingDongRefundOrderExecutor == null) {
//            jingDongRefundOrderExecutor = new ThreadPoolExecutor(
//                    32,
//                    32,
//                    0,
//                    TimeUnit.SECONDS,
//                    new LinkedBlockingDeque<>(32),
//                    new ThreadFactoryBuilder().setNameFormat(THREAD_POOL_NAME_JD_REFUND_ORDER).build());
//
//        }
//        return jingDongRefundOrderExecutor;
//    }
//
//    /**
//     * 订单拆单线程池
//     */
//    public static ExecutorService getOrderSplitExecutor() {
//        if (orderSplitExecutor == null) {
//            orderSplitExecutor = new ThreadPoolExecutor(
//                    32,
//                    32,
//                    0,
//                    TimeUnit.SECONDS,
//                    new LinkedBlockingDeque<>(32),
//                    new ThreadFactoryBuilder().setNameFormat(THREAD_POOL_NAME_ORDER_SPLIT).build());
//
//        }
//        return orderSplitExecutor;
//    }
//
//    /**
//     * 订单拆单线程池(补偿oc_b_order_split_task)
//     */
//    public static ExecutorService getOrderSplitCompensationExecutor() {
//        if (orderSplitCompensationExecutor == null) {
//            orderSplitCompensationExecutor = new ThreadPoolExecutor(
//                    32,
//                    32,
//                    0,
//                    TimeUnit.SECONDS,
//                    new LinkedBlockingDeque<>(32),
//                    new ThreadFactoryBuilder().setNameFormat(THREAD_POOL_NAME_ORDER_SPLIT_COMPENSATION).build());
//
//        }
//        return orderSplitCompensationExecutor;
//    }
//
//    /**
//     * 对等换货
//     */
//    public static ExecutorService getOrderEqualExchangeExecutor() {
//        if (orderEqualExchangeExecutor == null) {
//            orderEqualExchangeExecutor = new ThreadPoolExecutor(
//                    20,
//                    20,
//                    0,
//                    TimeUnit.SECONDS,
//                    new LinkedBlockingDeque<>(32),
//                    new ThreadFactoryBuilder().setNameFormat(THREAD_POOL_NAME_EQUAL_EXCHANGE).build());
//
//        }
//        return orderEqualExchangeExecutor;
//    }

//
//    public static ExecutorService getOrderTheAuditPollExecutor() {
//        if (orderTheAuditPollExecutor == null) {
//            orderTheAuditPollExecutor = new ThreadPoolExecutor(
//                    30,
//                    50,
//                    0,
//                    TimeUnit.SECONDS,
//                    new LinkedBlockingDeque<>(32),
//                    new ThreadFactoryBuilder().setNameFormat(THREAD_POOL_NAME_THE_AUDIT).build());
//
//        }
//        return orderTheAuditPollExecutor;
//    }
//
//    public static ExecutorService getOrderTheAuditStopPollExecutor() {
//        if (orderTheAuditStopPollExecutor == null) {
//            orderTheAuditStopPollExecutor = new ThreadPoolExecutor(
//                    20,
//                    40,
//                    60,
//                    TimeUnit.SECONDS,
//                    new LinkedBlockingDeque<>(70),
//                    new ThreadFactoryBuilder().setNameFormat(THREAD_POOL_NAME_THE_AUDIT_STOP).build());
//
//        }
//        return orderTheAuditStopPollExecutor;
//    }

//    public static ExecutorService getOrderDetentionPollExecutor() {
//        if (orderDetentionPollExecutor == null) {
//            orderDetentionPollExecutor = new ThreadPoolExecutor(
//                    5,
//                    10,
//                    60,
//                    TimeUnit.SECONDS,
//                    new ArrayBlockingQueue<>(64),
//                    new ThreadFactoryBuilder().setNameFormat(THREAD_POOL_NAME_ORDER_DETENTION).build());
//
//        }
//        return orderDetentionPollExecutor;
//    }
}
