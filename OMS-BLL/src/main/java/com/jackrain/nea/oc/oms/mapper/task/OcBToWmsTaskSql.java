package com.jackrain.nea.oc.oms.mapper.task;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * @Description:
 * @author: 江家雷
 * @since: 2020/12/3
 * create at : 2020/12/3 9:54
 */
@Slf4j
public class OcBToWmsTaskSql {

    public String selectByNodeSql(Map<String, Object> para) {
        StringBuffer sql = new StringBuffer();
        StringBuffer limitStr = new StringBuffer(" LIMIT ");
        String nodeName = (String) para.get("nodeName");
        int limit = para.get("limit") != null ? (int) para.get("limit") : 3000;
        limitStr.append(limit);

        String taskTableName = (String) para.get("taskTableName");
        int orderStatus = para.get("status") != null ? (int) para.get("status") : 0;

        // 如果是DRDS数据库 必传参节点名称nodeName
        if (StringUtils.isEmpty(nodeName)) {
            return null;
        }
        sql.append("/*!TDDL:NODE=" + nodeName + "*/").append("select order_id from ")
                .append(taskTableName)
                .append("  where status=")
                .append(orderStatus)
                .append(limitStr);
        return sql.toString();
    }
}
