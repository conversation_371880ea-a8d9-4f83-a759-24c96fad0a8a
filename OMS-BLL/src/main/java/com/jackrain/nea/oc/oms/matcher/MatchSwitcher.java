//package com.jackrain.nea.oc.oms.matcher;
//
//import com.jackrain.nea.config.PropertiesConf;
//import com.jackrain.nea.util.ApplicationContextHandle;
//
///**
// * Description： 直播解析策略匹配开关
// * Author: RESET
// * Date: Created in 2020/6/16 20:53
// * Modified By:
// */
//public class MatchSwitcher {
//
//    static final String KEY_LIVE_SWITCHER = "r3.oc.liveStrategy.enable";
//
//    /**
//     * 直播解析策略开关
//     *
//     * @return
//     */
//    public static boolean isLiveSwitchEnable() {
//        PropertiesConf env = ApplicationContextHandle.getBean(PropertiesConf.class);
//        return "true".equals(env.getProperty(KEY_LIVE_SWITCHER, "true"));
//    }
//
//}
