package com.jackrain.nea.oc.oms.mapper;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInProductItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.jdbc.SQL;

import java.util.List;

@Mapper
public interface OcBRefundInProductItemMapper extends ExtentionMapper<OcBRefundInProductItem> {

    /**
     * 退货入库明细的数据查询
     *
     * @param id 退货入库表的id
     * @return 返回一个list 的集合
     */
    @Select("SELECT * FROM OC_B_REFUND_IN_PRODUCT_ITEM WHERE OC_B_REFUND_IN_ID =#{id}")
    List<OcBRefundInProductItem> selectForItem(Object id);

    /**
     * 更改入库单明细的【退换货订单号】
     *
     * @param ocbReturnOrderId
     * @param id
     * @param ocbRefundInId
     */
    @Update("UPDATE oc_b_refund_in_product_item  SET oc_b_return_order_id=#{ocbReturnOrderId} WHERE oc_b_refund_in_id=#{ocbRefundInId} and id=#{id}")
    int updateOcbReturnOrderId(@Param("ocbReturnOrderId") long ocbReturnOrderId, @Param("id") long id, @Param("ocbRefundInId") long ocbRefundInId);

    /**
     * 入库单匹配
     * 更改入库单明细的【退换货订单号】, 是否无原单条码
     *
     * @param ocbReturnOrderId
     * @param id
     * @param ocbRefundInId
     */
    @Update("UPDATE oc_b_refund_in_product_item  SET oc_b_return_order_id=#{ocbReturnOrderId}, "
            + "is_without_orig=#{isWithoutOrig} WHERE oc_b_refund_in_id=#{ocbRefundInId} and id=#{id}")
    int updateRefundInItem4Match(@Param("ocbReturnOrderId") Long ocbReturnOrderId, @Param("isWithoutOrig") long isWithoutOrig,
                                 @Param("id") long id, @Param("ocbRefundInId") long ocbRefundInId);

    /**
     * 更改入库单明细的【订单单号】
     *
     * @param ocbOrderId
     * @param id
     * @param ocbRefundInId
     */
    @Update("UPDATE oc_b_refund_in_product_item  SET oc_b_order_id=#{ocbOrderId} WHERE id=#{id} and oc_b_refund_in_id=#{ocbRefundInId}")
    int updateOcbOrderId(@Param("ocbOrderId") long ocbOrderId, @Param("id") long id, @Param("ocbRefundInId") long ocbRefundInId);

    /**
     * 更改入库单明细的【是否无原单条码】
     *
     * @param id
     * @param ocbRefundInId
     */
    @Update("UPDATE oc_b_refund_in_product_item  SET is_without_orig=#{isWithoutOrig},IS_GEN_MINUS_ADJUST=1 WHERE id=#{id} and oc_b_refund_in_id=#{ocbRefundInId}")
    int updateIsWithoutOrig(@Param("isWithoutOrig") int isWithoutOrig, @Param("id") long id, @Param("ocbRefundInId") long ocbRefundInId);

    /**
     * 根据入库单编号 查询入库明细
     *
     * @param ocBRefundInId 退货入库单id
     * @param isMatch       是否匹配
     * @param isActive      是否启用
     * @return 明细ID, 退换货单编号,入库单id // `ID`,OC_B_REFUND_IN_ID, OC_B_RETURN_ORDER_ID, PS_C_SKU_ID, QTY
     */
    @Select("SELECT * FROM  OC_B_REFUND_IN_PRODUCT_ITEM WHERE "
            + "OC_B_REFUND_IN_ID =#{ocBRefundInId} AND IS_MATCH=#{isMatch} AND ISACTIVE=#{isActive}")
    List<OcBRefundInProductItem> queryOcBreFundInProductItemsByFk(@Param("ocBRefundInId") Long ocBRefundInId,
                                                                  @Param("isMatch") Integer isMatch,
                                                                  @Param("isActive") String isActive);

    /**
     * 更新入库单明细的条码, 是否匹配值为已匹配
     *
     * @param ocBRefundInProductItem 入库明细
     * @return int
     */
    @Update("UPDATE OC_B_REFUND_IN_PRODUCT_ITEM SET IS_MATCH=#{isMatch} WHERE OC_B_REFUND_IN_ID=#{ocBRefundInId} AND "
            + "`ID`=#{id}")
    int updateRefundIsMatch(OcBRefundInProductItem ocBRefundInProductItem);

    @Update("UPDATE OC_B_REFUND_IN_PRODUCT_ITEM SET IS_MATCH=#{isMatch},IS_GEN_ADJUST=#{isGenAdjust} WHERE "
            + "OC_B_REFUND_IN_ID=#{ocBRefundInId} AND `ID`=#{id}")
    int updateRefundIsMatchAndAdJUST(OcBRefundInProductItem ocBRefundInProductItem);

    @Update("UPDATE OC_B_REFUND_IN_PRODUCT_ITEM SET IS_MATCH=#{isMatch},IS_GEN_ADJUST=#{isGenAdjust},"
            + "IS_GEN_IN_ORDER=#{isGenInOrder},IS_GEN_WRO_ADJUST=#{isGenWroAdjust} WHERE "
            + "OC_B_REFUND_IN_ID=#{ocBRefundInId} AND `ID`=#{id}")
    int updateRefundIsMatchSecondary(OcBRefundInProductItem ocBRefundInProductItem);

    @Update("update oc_b_refund_in_product_item set is_match=#{isMatch}, is_gen_in_order=#{isGenInOrder}," +
            "oc_b_return_order_id=#{ocBReturnOrderId},is_without_orig=#{isWithoutOrig} where "
            + "oc_b_refund_in_id=#{ocBRefundInId} and `id`=#{id}")
    int updateMatchStatusAndInStatus(OcBRefundInProductItem ocBRefundInProductItem);

    /**
     * 无名件.是否生成调整单
     *
     * @param jsn
     * @return
     */
    @UpdateProvider(type = OcBRefundInProductItemSql.class, method = "updateRefundItemIsGenAdjust")
    int updateRefundItemIsGenAdjust(JSONObject jsn);

    @Update("<script> "
            + "UPDATE OC_B_REFUND_IN_PRODUCT_ITEM SET IS_GEN_ADJUST=#{isGenAdjust} WHERE "
            + "OC_B_REFUND_IN_ID=#{refundId} AND `ID` IN "
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    int updateRefundInProductAdJUST(@Param("isGenAdjust") int isGenAdjust, @Param("refundId") Long refundId,
                                    @Param("ids") List<Long> ids);

    @Update("<script> "
            + "UPDATE OC_B_REFUND_IN_PRODUCT_ITEM SET is_gen_minus_adjust=#{isGenAdjust} WHERE "
            + "OC_B_REFUND_IN_ID=#{refundId} AND `ID` IN "
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    int updateMinusAdjust(@Param("isGenAdjust") int isGenAdjust, @Param("refundId") Long refundId,
                                    @Param("ids") List<Long> ids);

    @Update("UPDATE OC_B_REFUND_IN_PRODUCT_ITEM SET IS_GEN_ADJUST=#{isGenAdjust} WHERE ID=#{id} AND OC_B_REFUND_IN_ID=#{refundId}")
    int updateIsGenAdjust(@Param("id") Long id, @Param("refundId") Long refundId, @Param("isGenAdjust") int isGenAdjust);

    @Select("SELECT * FROM OC_B_REFUND_IN_PRODUCT_ITEM WHERE ID=#{id} AND OC_B_REFUND_IN_ID=#{refundId}")
    OcBRefundInProductItem selectByRefundInId(@Param("id") Long id, @Param("refundId") Long refundId);

    @Select("SELECT * FROM OC_B_REFUND_IN_PRODUCT_ITEM WHERE OC_B_REFUND_IN_ID=#{refundId} AND IS_MATCH = 0")
    List<OcBRefundInProductItem> selectProductItemByRefundInId(@Param("refundId") Long refundId);
    //sunjunlei end

    @UpdateProvider(type = OcBRefundInProductItemMapper.OcBRefundInProductItemSql.class, method = "updateByIdSql")
    int updateByIdSql(JSONObject entity);

    class OcBRefundInProductItemSql {
        public String updateByIdSql(JSONObject entity) {
            return new SQL() {
                {
                    UPDATE("OC_B_REFUND_IN_PRODUCT_ITEM");
                    for (String key : entity.keySet()) {
                        if (!"ID".equalsIgnoreCase(key)) {
                            SET(key + "=" + "#{" + key + "}");
                        }
                    }
                    WHERE("ID = #{ID}");
                }
            }.toString();
        }

        /**
         * 无名件入库更新
         *
         * @param jsn
         * @return
         */
        public String updateRefundItemIsGenAdjust(JSONObject jsn) {
            StringBuilder sb = new StringBuilder();
            sb.append("UPDATE `OC_B_REFUND_IN_PRODUCT_ITEM` SET `IS_GEN_ADJUST`=1, `MODIFIERID`=")
                    .append(jsn.getLong("MODIFIERID")).append(", `MODIFIERNAME`='")
                    .append(jsn.getString("MODIFIERNAME")).append("', `MODIFIERENAME`='")
                    .append(jsn.getString("MODIFIERENAME")).append("', `MODIFIEDDATE`='")
                    .append(jsn.getString("MODIFIEDDATE")).append("' WHERE `OC_B_REFUND_IN_ID`=")
                    .append(jsn.getLong("OC_B_REFUND_IN_ID")).append(" AND `ID` IN (")
                    .append(jsn.getString("IDS")).append(");");
            return sb.toString();
        }
    }

    /**
     * 入库单匹配
     *
     * @param refundId 入库单id
     * @return 入库单明细
     */
    @Select("SELECT * FROM OC_B_REFUND_IN_PRODUCT_ITEM WHERE OC_B_REFUND_IN_ID=#{refundId} AND ISACTIVE='Y'")
    List<OcBRefundInProductItem> selectProductItemsByRefundInId(@Param("refundId") Long refundId);

    /**
     * 入库单匹配
     * 更改入库单明细的【退换货订单号】, 是否无原单条码,订单id
     *
     * @return
     */
    @Update("UPDATE oc_b_refund_in_product_item  SET oc_b_return_order_id=#{ocBReturnOrderId}, "
            + "is_without_orig=#{isWithoutOrig}, oc_b_order_id=#{ocBOrderId}  WHERE oc_b_refund_in_id=#{ocbRefundInId} "
            + " and id=#{id}")
    int updateRefundInItem4MatchByModel(@Param("ocBReturnOrderId") Long ocBReturnOrderId,
                                        @Param("isWithoutOrig") Integer isWithoutOrig, @Param("ocBOrderId") Long ocBOrderId,
                                        @Param("ocbRefundInId") Long ocbRefundInId, @Param("id") Long id);

}
