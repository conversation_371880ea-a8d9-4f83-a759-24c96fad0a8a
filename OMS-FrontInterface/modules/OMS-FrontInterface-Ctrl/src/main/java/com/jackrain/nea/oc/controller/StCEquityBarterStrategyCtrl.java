package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.enums.YesOrNoEnum;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.st.StCEquityBarterStrategyImportCmd;
import com.jackrain.nea.oc.oms.model.enums.EquityBarterImportTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.EquityBarterTypeEnum;
import com.jackrain.nea.oc.oms.util.ExportUtil;
import com.jackrain.nea.oc.oms.vo.StCEquityBarterStrategyImpVo;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.async.AsyncTaskBody;
import com.jackrain.nea.web.common.AsyncTaskManager;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

/**
 * @author: lijin
 * @create: 2024-06-04
 * @description: 对等换货策略ctrl
 */
@RestController
@Slf4j
@Api(value = "StCEquityBarterStrategyCtrl", description = "对等换货策略")
public class StCEquityBarterStrategyCtrl {
    public static final String cellStr = "cell_";
    public static final String rowStr = "row_";
    public static final int NUMERIC = 0;
    public static final int FORMULA = 2;
    public static final int IMPORT_MAX_NUM = 10001;
    private static NumberFormat nf = NumberFormat.getInstance();

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;
    @Autowired
    private AsyncTaskManager asyncTaskManager;
    @Autowired
    private StCEquityBarterStrategyImportCmd stCEquityBarterStrategyImportCmd;
    @Autowired
    private ThreadPoolTaskExecutor commonTaskExecutor;
    @Autowired
    private ExportUtil exportUtil;
    public static String FORMAT = "yyyyMMdd HH:mm:ss";

    @ApiOperation(value = "获取导入模板地址")
    @RequestMapping(path = "/api/cs/oc/oms/stCEquityBarterStrategy/template/url", method = RequestMethod.GET)
    public ValueHolderV14<String> queryImportTemplateUrl() {
        return stCEquityBarterStrategyImportCmd.queryTemplateDownloadUrl();

    }

    @ApiOperation(value = "头明细覆盖")
    @RequestMapping(value = "/api/cs/oc/oms/stCEquityBarterStrategy/import", method = RequestMethod.POST)
    public ValueHolderV14 importCover(HttpServletRequest request, @RequestParam(value = "file") MultipartFile file) {
        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        //插入我的任务里
        AsyncTaskBody asyncTaskBody = new AsyncTaskBody();
        asyncTaskBody.setTaskId(UUID.randomUUID().toString());
        asyncTaskBody.setMenu("对等换货策略头明细覆盖");
        asyncTaskBody.setTaskType("导入");
        asyncTaskManager.beforeExecute(user, asyncTaskBody);
        return asyncImport(asyncTaskBody, user, file, EquityBarterImportTypeEnum.COVER.getKey());
    }

    @ApiOperation(value = "头明细追加")
    @RequestMapping(value = "/api/cs/oc/oms/stCEquityBarterStrategy/add/import", method = RequestMethod.POST)
    public ValueHolderV14 importAdd(HttpServletRequest request, @RequestParam(value = "file") MultipartFile file) {
        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        //插入我的任务里
        AsyncTaskBody asyncTaskBody = new AsyncTaskBody();
        asyncTaskBody.setTaskId(UUID.randomUUID().toString());
        asyncTaskBody.setMenu("对等换货策略头明细追加");
        asyncTaskBody.setTaskType("导入");
        asyncTaskManager.beforeExecute(user, asyncTaskBody);
        return asyncImport(asyncTaskBody, user, file, EquityBarterImportTypeEnum.ADD.getKey());
    }


    public ValueHolderV14 asyncImport(AsyncTaskBody asyncTaskBody, User user, MultipartFile file, Integer type) {
        ValueHolderV14 holderV14 = new ValueHolderV14();
        Map<Object, Object> retMap = new LinkedHashMap<>();
        commonTaskExecutor.submit(() -> {
            try {
                if (file == null) {
                    throw new NDSException("导入文件不能为空!");
                }
                InputStream inputStream = file.getInputStream();
                Workbook hssfWorkbook = WorkbookFactory.create(inputStream);
                if (hssfWorkbook.getNumberOfSheets() != 1) {
                    throw new NDSException("导入模板不正确!");
                }
                List<StCEquityBarterStrategyImpVo> dataImpVos = getImportDataList(hssfWorkbook);
                if (CollectionUtils.isEmpty(dataImpVos)) {
                    throw new NDSException("导入数据不能为空!");
                }
                ValueHolderV14 valueHolder;
                if (EquityBarterImportTypeEnum.COVER.getKey().equals(type)) {
                    valueHolder = stCEquityBarterStrategyImportCmd.importCoverDataList(dataImpVos, user);
                } else {
                    valueHolder = stCEquityBarterStrategyImportCmd.importAddDataList(dataImpVos, user);
                }
                StringBuilder sb = new StringBuilder();
                sb.append(new SimpleDateFormat(FORMAT).format(new Date())).append(user.getEname()).append("导入的对等换货策略文件(");
                if (valueHolder.isOK()) {
                    sb.append("成功");
                } else {
                    sb.append("失败");
                }
                sb.append("),taskId:").append(asyncTaskBody.getId());
                String fileName = sb.toString();
                String putMsg = exportUtil.saveFileAndPutOss(hssfWorkbook, fileName,
                        user, "OSS-Bucket/IMPORT/ST_C_EQUITY_BARTER_STRATEGY/");
                log.info(LogUtil.format("StCEquityBarterStrategyCtrl.asyncImport importUrl:{}",
                        "StCEquityBarterStrategyCtrl.asyncImport"), putMsg);
                retMap.put("code", valueHolder.getCode());
                retMap.put("data", valueHolder.getData());
                retMap.put("path", valueHolder.getData());
                retMap.put("message", valueHolder.getMessage());
                //任务完成
                asyncTaskBody.setUrl(String.valueOf(valueHolder.getData()));
                asyncTaskBody.setTaskType("导入");
                asyncTaskManager.afterExecute(user, asyncTaskBody, JSON.parseObject(JSON.toJSONString(retMap)));
            } catch (Exception ex) {
                log.error(LogUtil.format("头明细导入失败", "商品效期策略"), ex);
                retMap.put("code", ResultCode.FAIL);
                retMap.put("message", "导入异常：" + ex.getMessage());
                asyncTaskManager.afterExecute(user, asyncTaskBody, JSON.parseObject(JSON.toJSONString(retMap)));
            }
        });
        holderV14.setCode(ResultCode.SUCCESS);
        holderV14.setData(asyncTaskBody.getId());
        holderV14.setMessage(Resources.getMessage("对等换货策略头明细导入任务开始！详情请在我的任务查看"));
        return holderV14;
    }


    /**
     * 获取主it 表sheet数据，转换成主表对象
     */
    public List<StCEquityBarterStrategyImpVo> getImportDataList(Workbook hssfWorkbook) throws Exception {
        List<StCEquityBarterStrategyImpVo> dataImpVoList = Lists.newArrayList();
        //解析excel
        List<Map<String, String>> excelList = readExcel(0, hssfWorkbook);
        if (excelList.size() > IMPORT_MAX_NUM) {
            throw new NDSException("导入条数请勿超过9999条！");
        }
        int index = 0;
        // 数据缺失校验
        StringBuilder checkMessage = new StringBuilder();
        for (Map<String, String> columnMap : excelList) {
            if (index == 1) {
                // 校验excel字段
                if (columnMap.size() != 9
                        || !"类型".equals(columnMap.get(rowStr + index + cellStr + 0))
                        || !"平台店铺".equals(columnMap.get(rowStr + index + cellStr + 1))
                        || !"换货商品".equals(columnMap.get(rowStr + index + cellStr + 2))
                        || !"换货商品名称".equals(columnMap.get(rowStr + index + cellStr + 3))
                        || !"换货数量".equals(columnMap.get(rowStr + index + cellStr + 4))
                        || !"对等商品".equals(columnMap.get(rowStr + index + cellStr + 5))
                        || !"对等商品名称".equals(columnMap.get(rowStr + index + cellStr + 6))
                        || !"对等数量".equals(columnMap.get(rowStr + index + cellStr + 7))
                        || !"缺货不还原".equals(columnMap.get(rowStr + index + cellStr + 8))
                ) {
                    throw new NDSException("导入模板不正确!");
                }
            } else if (index > 1){
                StCEquityBarterStrategyImpVo impVo = StCEquityBarterStrategyImpVo.importCreate(index, columnMap);
                //转换枚举值
                if (StringUtils.isNotBlank(impVo.getTypeName())) {
                    EquityBarterTypeEnum barterTypeEnum = EquityBarterTypeEnum.getByDesc(impVo.getTypeName());
                    if (Objects.nonNull(barterTypeEnum)) {
                        impVo.setType(barterTypeEnum.getKey());
                        if (EquityBarterTypeEnum.APPOINT_SHOP.getKey().equals(impVo.getType()) &&
                                StringUtils.isEmpty(impVo.getPlatformShopName())) {
                            checkMessage.append("[指定店铺类型时平台店铺不能为空]");
                        }
                    } else {
                        checkMessage.append("[填写的类型有误]");
                    }
                } else {
                    checkMessage.append("[类型不能为空]");
                }
                if (StringUtils.isBlank(impVo.getSkuCode())) {
                    checkMessage.append("[换货商品不能为空]");
                }
                if (StringUtils.isBlank(impVo.getSkuQtyStr())) {
                    checkMessage.append("[换货商品数量不能为空]");
                } else {
                    BigDecimal skuQty = stringToBigDecimalIfValid(impVo.getSkuQtyStr());
                    if (skuQty != null) {
                        impVo.setSkuQty(skuQty);
                    } else {
                        checkMessage.append("[换货商品数量格式错误]");
                    }
                }
                if (StringUtils.isBlank(impVo.getEquitySkuCode())) {
                    checkMessage.append("[对等商品不能为空]");
                }
                if (StringUtils.isBlank(impVo.getEquitySkuQtyStr())) {
                    checkMessage.append("[对等商品数量不能为空]");
                } else {
                    BigDecimal skuQty = stringToBigDecimalIfValid(impVo.getEquitySkuQtyStr());
                    if (skuQty != null) {
                        impVo.setEquitySkuQty(skuQty);
                    } else {
                        checkMessage.append("[对等商品数量格式错误]");
                    }
                }
                if (StringUtils.isNotEmpty(impVo.getOutStockNoRestore())) {
                    if (YesOrNoEnum.YES.getName().equals(impVo.getOutStockNoRestore())) {
                        impVo.setOutStockNoRestore(YesOrNoEnum.YES.getCode().toString());
                    } else if (YesOrNoEnum.NO.getName().equals(impVo.getOutStockNoRestore())) {
                        impVo.setOutStockNoRestore(YesOrNoEnum.NO.getCode().toString());
                    } else {
                        checkMessage.append("[填写的缺货不还原有误]");
                    }
                } else {
                    impVo.setOutStockNoRestore(YesOrNoEnum.NO.getCode().toString());
                }
                if (StringUtils.isNotEmpty(checkMessage)) {
                    if (StringUtils.isNotBlank(impVo.getDesc())) {
                        impVo.setDesc(impVo.getDesc() + checkMessage);
                    } else {
                        impVo.setDesc(checkMessage.toString());
                    }
                    checkMessage.setLength(0);
                }
                dataImpVoList.add(impVo);
            }
            index++;
        }
        return dataImpVoList;
    }


    /**
     * 导入方法，
     *
     * @param hssfWorkbook
     * @return
     * @throws Exception
     */
    public List<Map<String, String>> readExcel(Integer sheetIndex, Workbook hssfWorkbook) throws Exception {
        List list = Lists.newArrayList();
        Sheet hssfSheet = hssfWorkbook.getSheetAt(sheetIndex);
        if (Objects.isNull(hssfSheet)) {
            return Lists.newArrayList();
        }

        for (int rowNum = 0; rowNum <= hssfSheet.getLastRowNum(); rowNum++) {
            Map<String, String> map = Maps.newTreeMap();
            Row hssfRow = hssfSheet.getRow(rowNum);
            if (hssfRow != null) {
                Iterator<Cell> cellItr = hssfRow.cellIterator();
                while (cellItr.hasNext()) {
                    Cell cell = cellItr.next();
                    map.put(rowStr + cell.getRowIndex() + cellStr + cell.getColumnIndex(), getCellValue(cell));
                }
            }
            list.add(map);
        }
        return list;
    }

    private String getCellValue(Cell cell) {
        int cellType = cell.getCellType();
        String cellValue;
        switch (cellType) {
            case NUMERIC:
                cellValue = nf.format(cell.getNumericCellValue());
                if (cellValue.indexOf(",") >= 0) {
                    cellValue = cellValue.replace(",", "");
                }
                break;
            case FORMULA:
                try {
                    cellValue = cell.getStringCellValue();
                } catch (IllegalStateException e) {
                    cellValue = String.valueOf(cell.getNumericCellValue());
                }
                break;

            default:
                cellValue = cell.getStringCellValue();
        }
        return cellValue.trim();
    }

    /**
     * 校验数字是否合法
     *
     * @param input
     * @return
     */
    private BigDecimal stringToBigDecimalIfValid(String input) {
        // 首先检查字符串是否为空或长度为0
        if (input == null || input.isEmpty()) {
            return null;
        }
        // 使用正则表达式检查字符串是否仅包含数字且长度小于等于18
        if (input.matches("[0-9]{1,18}")) {
            // 尝试将字符串转换为BigDecimal
            try {
                return new BigDecimal(input);
            } catch (NumberFormatException e) {
                // 如果字符串格式有误（虽然这种情况下不会发生，因为已经用正则表达式检查过）
                return null;
            }
        }
        // 如果字符串包含非数字字符或长度超过18位，则返回null
        return null;
    }
}
