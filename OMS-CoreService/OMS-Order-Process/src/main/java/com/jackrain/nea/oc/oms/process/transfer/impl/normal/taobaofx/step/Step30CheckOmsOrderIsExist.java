package com.jackrain.nea.oc.oms.process.transfer.impl.normal.taobaofx.step;

import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoFxOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoFxOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> 周琳胜
 * @since : 2019-07-11
 * create at : 2019-07-11 10:24
 * 检查中间表订单是否已经存在
 */
@Step(order = 30, description = "检查中间表订单是否已经存在")
@Slf4j
@Component
public class Step30CheckOmsOrderIsExist extends BaseTaobaoFxOrderProcessStep implements IOmsOrderProcessStep<IpTaobaoFxOrderRelation> {

    @Override
    public ProcessStepResult<IpTaobaoFxOrderRelation> startProcess(IpTaobaoFxOrderRelation orderInfo,
                                                                   ProcessStepResult preStepResult,
                                                                   boolean isAutoMakeup, User operateUser) {
        log.debug("TaobaoFxTransferOrder.step03" + orderInfo.toString());
        IpBTaobaoFxOrder order = orderInfo.getIpBTaobaoFxOrder();
        Long orderNo = orderInfo.getOrderNo();
        // 查ES和redis 判断中间表再全渠道表中是否存在
        List<OcBOrder> orderList = ipTaobaoFxService.selectOmsOrderInfo(orderNo.toString());
        log.debug("TaobaoFxTransferOrder.step03.isExitInOmsOrder" + orderList);
        if (orderList != null && orderList.size() > 0) {
            // 判断订单是否都为已作废或取消订单
            List<OcBOrder> orderListResult = new ArrayList<>();
            try {
                orderListResult = ipTaobaoFxService.checkOrderStatus(orderList);
            } catch (Exception e) {
                String errorMessage = "当前订单已存在，且订单状态为空";
                IpBTaobaoFxOrder ipBTaobaoFxOrder = new IpBTaobaoFxOrder();
                ipBTaobaoFxOrder.setId(orderInfo.getOrderId());
                ipBTaobaoFxOrder.setIstrans(4); // 转换失败
                ipBTaobaoFxOrder.setSysremark("当前订单已存在，且订单状态为空");
                if (null == order.getTransCount()) {
                    ipBTaobaoFxOrder.setTransCount(1L);
                } else {
                    ipBTaobaoFxOrder.setTransCount(order.getTransCount() + 1L);
                }
                ipTaobaoFxService.updateTransferStatus(ipBTaobaoFxOrder);
                return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
            }
            if (orderListResult.size() == 0) {
                // 若都是取消或作废订单，则更新转换状态为已转换（2）系统备注：订单已取消或作废转换次数=原次数+1
                IpBTaobaoFxOrder ipBTaobaoFxOrder = new IpBTaobaoFxOrder();
                ipBTaobaoFxOrder.setId(orderInfo.getOrderId());
                ipBTaobaoFxOrder.setIstrans(TransferOrderStatus.TRANSFERRED.toInteger());
                ipBTaobaoFxOrder.setSysremark("订单已取消或作废。");
                ipBTaobaoFxOrder.setTransCount(order.getTransCount() + 1L);
                ipTaobaoFxService.updateTransferStatus(ipBTaobaoFxOrder);
                String operateMessage = Resources.getMessage("单据" + orderInfo.getOrderId() + "状态改为已转换");
                return new ProcessStepResult<>(StepStatus.FINISHED, operateMessage);
            } else {
                // 若都不是取消或作废订单，则判断非取消或非作废的订单的【卖家备注】与平台的【卖家备注】
                for (OcBOrder ocBOrder : orderListResult) {
                    if (ocBOrder.getSellerMemo() == null) {
                        ocBOrder.setSellerMemo("");
                    }
                    if (ocBOrder.getSellerMemo().equals(order.getSupplierMemo())) {
                        // 一致。 则更新“转换状态”为已转换（2）
                        IpBTaobaoFxOrder ipBTaobaoFxOrder = new IpBTaobaoFxOrder();
                        ipBTaobaoFxOrder.setId(orderInfo.getOrderId());
                        ipBTaobaoFxOrder.setIstrans(TransferOrderStatus.TRANSFERRED.toInteger());
                        ipTaobaoFxService.updateTransferStatus(ipBTaobaoFxOrder);
                    } else {
                        // 则更新订单的【卖家备注】更新为【通用订单中间表】中【卖家备注】
                        if (order.getSupplierMemo() != null) {
                            OcBOrder order1 = new OcBOrder();
                            order1.setId(ocBOrder.getId());
                            order1.setSellerMemo(order.getSupplierMemo());
                            ipTaobaoFxService.updateOmsOrder(order1);
                        }
                        // todo 订单操作日志
                        // 更新“转换状态”为已转换（2），“系统备注”：卖家备注已更新，“转换次数”=原次数+1
                        IpBTaobaoFxOrder ipBTaobaoFxOrder = new IpBTaobaoFxOrder();
                        ipBTaobaoFxOrder.setId(orderInfo.getOrderId());
                        ipBTaobaoFxOrder.setIstrans(TransferOrderStatus.TRANSFERRED.toInteger());
                        ipBTaobaoFxOrder.setSysremark("卖家备注已更新。");
                        if (order.getTransCount() == null) {
                            ipBTaobaoFxOrder.setTransCount(1L);
                        } else {
                            ipBTaobaoFxOrder.setTransCount(order.getTransCount() + 1L);
                        }
                        ipTaobaoFxService.updateTransferStatus(ipBTaobaoFxOrder);
                    }
                }
                String operateMessage = Resources.getMessage("单据" + orderInfo.getOrderId() + "状态改为已转换");
                return new ProcessStepResult<>(StepStatus.FINISHED, operateMessage);
            }
        } else {
            // 若不存在，继续下一步
            String operateMessage = Resources.getMessage("单据" + orderInfo.getOrderId() + "进入下一阶段，判断平台订单商品明细中条码在系统中【条码档案】中是否存在");
            return new ProcessStepResult<>(StepStatus.SUCCESS, operateMessage);
        }
    }
}
