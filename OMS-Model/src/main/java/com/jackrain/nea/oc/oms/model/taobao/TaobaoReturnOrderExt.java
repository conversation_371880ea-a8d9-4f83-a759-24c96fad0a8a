package com.jackrain.nea.oc.oms.model.taobao;

import lombok.Getter;

/**
 * <AUTHOR> 孙勇生
 * create at:  19/3/13  10:35
 * @description: 淘宝退换货扩展基础类
 * 状态类型等等基础定义
 */

public class TaobaoReturnOrderExt {

    public static final String TABLENAME_OCORDER = "oc_b_order";
    public static final String TABLENAME_OCORDERITEM = "oc_b_order_item";
    public static final String TABLENAME_OCRETURNORDER = "oc_b_return_order";
    public static final String TABLENAME_OCRETURNORDERREFUND = "oc_b_return_order_refund";
    public static final String TABLENAME_OCRETURNORDEREXCHAGE = "oc_b_return_order_exchange";
    public static final String TABLENAME_OCBRETURNORDERLOG = "oc_b_return_order_log";
    public static final String TABLENAME_IPBTAOBAOEXCHANGE = "ip_b_taobao_exchange";
    public static final String TABLENAME_IPBTAOBAOREFUND = "ip_b_taobao_refund";
    public static final String TABLENAME_IPBTAOBAOFXREFUND = "ip_b_taobao_fx_refund";
    public static final String TABLENAME_OCBRETURNAFSEND = "oc_b_return_af_send";
    public static final String TABLENAME_OCBRETURNAFSENDITEM = "oc_b_return_af_send_item";
    public static final String TABLENAME_OCBRETURNBFSEND = "oc_b_return_bf_send";


    //拒绝原因
    public static final Long REFUSE_ID = 4001L;

    /**
     * 退换货订单退换货状态
     */
    public enum ReturnOrderStatus {
        WAITIN(20, "等待退货入库"),
        REFUND(30, "等待售后确认"),
        FINAL(50, "完成"),
        CANCEL(60, "取消");

        private Integer code;
        private String name;

        ReturnOrderStatus(Integer code, String name) {
            this.code = code;
            this.name = name;
        }

        public Integer getCode() {
            return code;
        }

        public String getName() {
            return name;
        }
    }

    /**
     * 退换货中间表退货状态
     * 退换货订单单据类型
     */
    public enum BillType {
        REFUND(1, "退货单"),
        EXCHANGE(2, "退换货单");
        // PRE_REFUND(3, "预退货单");

        private Integer code;
        private String name;

        BillType(Integer code, String name) {
            this.code = code;
            this.name = name;
        }

        public Integer getCode() {
            return code;
        }

        public String getName() {
            return name;
        }
    }

    /**
     * 退换货中间表平台退货状态
     */
    public enum RefundStatus {

        WAIT_SELLER_AGREE("WAIT_SELLER_AGREE", "买家已经申请退款，等待卖家同意"),

        WAIT_BUYER_RETURN_GOODS("WAIT_BUYER_RETURN_GOODS", "卖家已经同意退款，等待买家退货"),

        WAIT_SELLER_CONFIRM_GOODS("WAIT_SELLER_CONFIRM_GOODS", "买家已经退货，等待卖家确认收货"),

        SELLER_REFUSE_BUYER("SELLER_REFUSE_BUYER", "卖家拒绝退款"),

        CLOSED("CLOSED", "退款关闭"),

        SUCCESS("SUCCESS", "退款成功"),

        WAIT_RETURN_MONEY("WAIT_RETURN_MONEY", "同意退款，待打款"),
        NOT_APPLY_RETURN("NOT_APPLY_RETURN", "没有申请退款"),
        REFUSE_ENSURE_GOODS("REFUSE_ENSURE_GOODS", "卖家拒绝确认收货");

        public Long toLong() {
            if (this == RefundStatus.WAIT_SELLER_AGREE) {
                return 1L;
            } else if (this == RefundStatus.WAIT_BUYER_RETURN_GOODS) {
                return 2L;
            } else if (this == RefundStatus.WAIT_SELLER_CONFIRM_GOODS) {
                return 3L;
            } else if (this == RefundStatus.SELLER_REFUSE_BUYER) {
                return 6L;
            } else if (this == RefundStatus.CLOSED) {
                return 4L;
            } else if (this == RefundStatus.SUCCESS) {
                return 5L;
            } else if (this == RefundStatus.WAIT_RETURN_MONEY) {
                return 12L;
            } else if (this == RefundStatus.NOT_APPLY_RETURN) {
                return 9L;
            } else {
                return 10L;
            }
        }

        private String code;
        private String name;

        RefundStatus(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static String getValueByKey(String key) {

            String s = "";
            if (key == null) {
                return s;
            }
            for (RefundStatus e : RefundStatus.values()) {
                if (e.getCode().equals(key)) {
                    s = e.getName();
                    break;
                }
            }
            return s;
        }
    }

    public enum RefundStandPlatStatus {
        WAIT_SELLER_AGREE("WAIT_SELLER_AGREE", 1),
        WAIT_BUYER_RETURN_GOODS("WAIT_BUYER_RETURN_GOODS", 2),
        WAIT_SELLER_CONFIRM_GOODS("WAIT_SELLER_CONFIRM_GOODS", 3),
        SUCCESS("SUCCESS", 4),
        SELLER_REFUSE_BUYER("SELLER_REFUSE_BUYER", 5),
        CLOSED("CLOSED", 6),
        SELLER_REFUSE_BUYER_RETURN_GOODS("SELLER_REFUSE_BUYER_RETURN_GOODS", 20);

        private String name;
        private Integer code;

        RefundStandPlatStatus(String name, Integer code) {
            this.code = code;
            this.name = name;
        }

        public Integer getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static String getValueByKey(Integer key) {

            String s = "";
            if (key == null) {
                return s;
            }
            for (RefundStandPlatStatus e : RefundStandPlatStatus.values()) {
                if (e.getCode().equals(key)) {
                    s = e.getName();
                    break;
                }
            }
            return s;
        }

    }

    public enum SuborderStatus {
        TRADE_NO_CREATE_PAY("TRADE_NO_CREATE_PAY", "没有创建支付宝交易"),
        WAIT_BUYER_PAY("WAIT_BUYER_PAY", "等待买家付款"),
        WAIT_SELLER_SEND_GOODS("WAIT_SELLER_SEND_GOODS", "等待卖家发货,即:买家已付款"),
        WAIT_BUYER_CONFIRM_GOODS("WAIT_BUYER_CONFIRM_GOODS", "等待买家确认收货,即:卖家已发货"),
        TRADE_BUYER_SIGNED("TRADE_BUYER_SIGNED", "买家已签收,货到付款专用"),
        TRADE_FINISHED("TRADE_FINISHED", "交易成功"),
        TRADE_CLOSED("TRADE_CLOSED", "付款以后用户退款成功，交易自动关闭"),
        TRADE_CLOSED_BY_TAOBAO("TRADE_CLOSED_BY_TAOBAO", "付款以前，卖家或买家主动关闭交易"),
        PAY_PENDING("PAY_PENDING", "国际信用卡支付付款确认中");


        private String code;
        private String name;

        SuborderStatus(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static String getValueByKey(String key) {

            String s = "";
            if (key == null) {
                return s;
            }
            for (SuborderStatus e : SuborderStatus.values()) {
                if (e.getCode().equals(key)) {
                    s = e.getName();
                    break;
                }
            }
            return s;
        }
    }

    /**
     * 中间表退款平台
     */
    public enum RefundPlatformStatus {

        RADE_NO_CREATE_PAY("RADE_NO_CREATE_PAY", "没有创建支付宝交易"),

        WAIT_BUYER_PAY("WAIT_BUYER_PAY", "等待买家付款"),

        SELLER_CONSIGNED_PART("SELLER_CONSIGNED_PART", "卖家部分发货"),

        WAIT_SELLER_SEND_GOODS("WAIT_SELLER_SEND_GOODS", "等待卖家发货,即:买家已付款"),

        WAIT_BUYER_CONFIRM_GOODS("WAIT_BUYER_CONFIRM_GOODS", "等待买家确认收货,即:卖家已发货"),

        TRADE_BUYER_SIGNED("TRADE_BUYER_SIGNED", "买家已签收,货到付款专用"),

        TRADE_FINISHED("TRADE_FINISHED", "交易成功"),

        TRADE_CLOSED("TRADE_CLOSED", "付款以后用户退款成功，交易自动关闭"),

        TRADE_CLOSED_BY_TAOBAO("TRADE_CLOSED_BY_TAOBAO", "付款以前，卖家或买家主动关闭交易"),

        PAY_PENDING("PAY_PENDING", "国际信用卡支付付款确认中"),

        WAIT_PRE_AUTH_CONFIRM("WAIT_PRE_AUTH_CONFIRM", "0元购合约中"),

        PAID_FORBID_CONSIGN("PAID_FORBID_CONSIGN", "拼团中订单或者发货强管控的订单，已付款但禁止发货");


        private String code;
        private String name;

        RefundPlatformStatus(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }


    }

    /**
     * 退款或者退货的
     */
    public enum HasGoodReturnStatus {

        NO_RETURN(0, "退款"),

        YES_RETURN(1, "退货");

        private Integer code;
        private String name;

        HasGoodReturnStatus(Integer code, String name) {
            this.code = code;
            this.name = name;
        }

        public Integer getCode() {
            return code;
        }

        public String getName() {
            return name;
        }


    }

//  2020-06-26 易邵峰修改：将ReturnStatus注释。统一使用ReturnStatusEnum方法
//    /**
//     * '退换货状态,20等待退货入库，30等待售后确认，50完成，60取消'
//     */
//    public enum ReturnStatus {
//
//        WAIT_RETURN_LIBRARY(20, "等待退货入库"),
//
//        WAIT_AFTERSALE_CONFIRM(30, "等待售后确认"),
//
//        RETURN_COMPLETE(50, "退货完成"),
//
//        RETURN_CANCEL(60, "退货取消");
//
//        private Integer code;
//        private String name;
//
//        ReturnStatus(Integer code, String name) {
//            this.code = code;
//            this.name = name;
//        }
//
//        public Integer getCode() {
//            return code;
//        }
//
//        public String getName() {
//            return name;
//        }
//
//        public static String getNameByCode(Integer value) {
//            ReturnStatus[] businessModeEnums = values();
//            for (ReturnStatus returnStatus : businessModeEnums) {
//                if (returnStatus.getCode().equals(value)) {
//                    return returnStatus.getName();
//                }
//            }
//            return null;
//        }
//
//    }

    /**
     * 退单的单据类型
     */
    public enum ReturnBillsStatus {

        GOODS_FRONT(3, "发货前"),

        GOODS_AFTER(0, "客退"),

        GOODS_INTERCEPT(1, "拦截"),

        GOODS_REJECTION(2, "拒收"),

        EXCHANGE(8, "换货"),

        /**
         * 为业务标识
         */
        WAREHOUSE_DELIVERY(4, "仓库发货");


        private Integer code;
        private String name;

        ReturnBillsStatus(Integer code, String name) {
            this.code = code;
            this.name = name;
        }

        public Integer getCode() {
            return code;
        }

        public String getName() {
            return name;
        }


    }

    public enum GoodStatus {

        BUYER_NOT_RECEIVED("BUYER_NOT_RECEIVED", "买家未收到货"),
        BUYER_RECEIVED("BUYER_RECEIVED", "买家已收到货"),
        BUYER_RETURNED_GOODS("BUYER_RETURNED_GOODS", "买家已退货");
        @Getter
        private String code;
        @Getter
        private String name;

        GoodStatus(String code, String name) {
            this.code = code;
            this.name = name;
        }


    }


    public enum SendBillType {

        RETURN_REFUND(0, "退货退款"),

        REFUND_ONLY(1, "仅退款");


        private Integer code;
        private String name;

        SendBillType(Integer code, String name) {
            this.code = code;
            this.name = name;
        }

        public Integer getCode() {
            return code;
        }

        public String getName() {
            return name;
        }


    }
    /**
     * 调用ag
     */
    public enum AgStatus {

        SUCCESS(1L, "成功"),
        FAIL(2L, "失败"),
        NOT_CALL(0L, "未调用");
        @Getter
        private Long code;
        @Getter
        private String name;

        AgStatus(Long code, String name) {
            this.code = code;
            this.name = name;
        }

    }


}
