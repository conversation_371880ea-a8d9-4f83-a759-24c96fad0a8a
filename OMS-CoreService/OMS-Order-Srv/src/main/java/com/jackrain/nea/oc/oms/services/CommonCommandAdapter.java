package com.jackrain.nea.oc.oms.services;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.primitives.Longs;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.utils.AssertUtils;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.reflect.MethodUtils;
import org.assertj.core.util.Lists;

import java.util.List;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Date 2024/1/31
 */
@Slf4j
public class CommonCommandAdapter extends CommandAdapter {

    public List<Long> getObjIds(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        String objids = (String) event.getParameterValue("objids");

        if (StringUtils.isBlank(objids)) {

            String objid = (String) event.getParameterValue("objid");
            if (StringUtils.isNotBlank(objid)) {
                return Lists.newArrayList(Long.valueOf(objid));
            }

            JSONObject param = (JSONObject) event.getParameterValue("param");
            JSONArray ids = param.getJSONArray("ids");
            if (CollectionUtil.isEmpty(ids)) {
                return Lists.emptyList();
            }

            return ids.toJavaList(Long.class);
        }

        long[] longs = Stream.of(objids.split(","))
                .mapToLong(Long::new)
                .toArray();

        return Longs.asList(longs);
    }

    public <T> T parseObj(Class<T> objClass, String tableName, QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);

        JSONObject bodyJsn = param.getJSONObject("fixcolumn");
        AssertUtils.cannot(bodyJsn == null || bodyJsn.isEmpty(), "参数体为空");

        log.info("parseObj: {}", param.toJSONString());

        if (null == bodyJsn || bodyJsn.isEmpty()) {
            return null;
        }

        T obj = JSON.parseObject(bodyJsn.getString(tableName), objClass);

        if (!"-1".equals(event.getParameterValue("objid"))) {
            try {
                MethodUtils.invokeMethod(obj,
                        "setId",
                        Long.parseLong((String) event.getParameterValue("objid")));
            }
            catch (Exception e) {
                e.printStackTrace();
            }
        }

        return obj;
    }
}
