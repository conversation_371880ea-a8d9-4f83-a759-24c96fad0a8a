package com.jackrain.nea.oc.oms.nums.excel;

import com.jackrain.nea.util.excel.XlsAno;
import com.jackrain.nea.util.excel.XlsDBAno;
import com.jackrain.nea.util.excel.XlsSt;
import com.jackrain.nea.util.excel.XlsTyp;

import java.math.BigDecimal;

/**
 * @author: xiWen.z
 * create at: 2019/8/15 0015
 */
@XlsDBAno(name = "oc_b_return_order_refund", desc = "退货明细", index = 1, sort = "id:asc", st = {XlsSt.DB, XlsSt.ES})
public class OcBReturnOrderRefundModel {

    @XlsAno(name = "oc_b_return_order_id", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 0, desc = "退单编号")
    private Long ocBReturnOrderId;

    @XlsAno(name = "ps_c_sku_ecode", value = {XlsSt.NOTNULL}, type = XlsTyp.STRING, index = 10, desc = "条码")
    private String psCSkuEcode;

    @XlsAno(name = "barcode", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 20, desc = "国标码")
    private String barcode;

    @XlsAno(name = "ps_c_pro_ecode", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 30, desc = "商品编码")
    private String psCProEcode;

    @XlsAno(name = "ps_c_pro_ename", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 40, desc = "商品名称")
    private String psCProEname;

    @XlsAno(name = "qty_can_refund", value = {XlsSt.NORMAL}, type = XlsTyp.DOUBLE, index = 50, desc = "可退数量")
    private BigDecimal qtyCanRefund;

    @XlsAno(name = "qty_refund", value = {XlsSt.NORMAL}, type = XlsTyp.DOUBLE, index = 60, desc = "申请数量")
    private BigDecimal qtyRefund;

    @XlsAno(name = "price", value = {XlsSt.NORMAL}, type = XlsTyp.DOUBLE, index = 70, desc = "吊牌价")
    private BigDecimal price;

    @XlsAno(name = "amt_refund_single", value = {XlsSt.NORMAL}, type = XlsTyp.DOUBLE, index = 80, desc = "单件退货金额")
    private BigDecimal amtRefundSingle;

    @XlsAno(name = "amt_refund", value = {XlsSt.NORMAL}, type = XlsTyp.DOUBLE, index = 90, desc = "退货金额")
    private BigDecimal amtRefund;

    @XlsAno(name = "qty_in", value = {XlsSt.NORMAL}, type = XlsTyp.DOUBLE, index = 100, desc = "入库数量")
    private BigDecimal qtyIn;

    @XlsAno(name = "product_mark", value = {XlsSt.GROUP}, type = XlsTyp.STRING, index = 110, desc = "商品标记")
    private String productMark;


}
