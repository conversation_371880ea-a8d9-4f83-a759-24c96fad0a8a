package com.jackrain.nea.oc.oms.task.bn;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.shade.org.apache.commons.lang3.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jackrain.nea.oc.oms.mapper.BnTaskDataMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.table.BnTaskData;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.services.OcCancelChangingOrRefundService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.request.BnColumnListQueryRequest;
import com.jackrain.nea.st.model.request.BnProblemConfigQueryRequest;
import com.jackrain.nea.st.model.result.BnColumnListQueryResult;
import com.jackrain.nea.st.model.result.BnProblemConfigQueryResult;
import com.jackrain.nea.st.model.table.StCBnColumnListDO;
import com.jackrain.nea.st.model.table.StCBnProblemConfigDO;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @ClassName AutoCancelReturnOrderByBn
 * @Description 根据班牛工单取消退换货单定时任务
 * <AUTHOR>
 * @Date 2025/5/12 11:10
 * @Version 1.0
 */
@Component
@Slf4j
public class AutoCancelReturnOrderByBn extends BaseR3Task implements IR3Task {

    // 状态的id为5.枚举为["待处理","已完成","处理中","暂停中","已关闭"]
    private static final Map<Integer, String> statusMap = new HashMap<>();

    static {
        statusMap.put(0, "待处理");
        statusMap.put(1, "已完成");
        statusMap.put(2, "处理中");
        statusMap.put(3, "暂停中");
        statusMap.put(4, "已关闭");
    }

    @Autowired
    private BnTaskDataMapper bnTaskDataMapper;
    @Autowired
    private StRpcService stRpcService;
    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;
    @Autowired
    private OcCancelChangingOrRefundService cancelChangingOrRefundService;

    @Override
    @XxlJob("AutoCancelReturnOrderByBn")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult result = new RunTaskResult();
        result.setSuccess(true);
        Map<String, StCBnColumnListDO> columnMap = new HashMap<>();
        BnColumnListQueryRequest request = new BnColumnListQueryRequest();
        request.setProjectId(20940L);
        ValueHolderV14<BnColumnListQueryResult> valueHolderV14 = stRpcService.queryBnColumnList(request);
        if (valueHolderV14 != null && valueHolderV14.isOK() && valueHolderV14.getData() != null) {
            BnColumnListQueryResult bnColumnListQueryResult = valueHolderV14.getData();
            List<StCBnColumnListDO> columnListList = bnColumnListQueryResult.getColumnListList();
            if (columnListList != null) {
                for (StCBnColumnListDO column : columnListList) {
                    columnMap.put(column.getName(), column);
                }
                log.info("班牛组件列表缓存初始化成功，共{}\u4e2a列", columnMap.size());
            }
        } else {
            result.setSuccess(false);
            result.setMessage("班牛组件列表查询失败");
            return result;
        }
        // 查询bn_task_data表中项目ID为20940且PROCESS_STATUS为0的数据
        List<BnTaskData> taskDataList = bnTaskDataMapper.selectList(Wrappers.<BnTaskData>lambdaQuery()
                .eq(BnTaskData::getProjectId, 20940L)
                .eq(BnTaskData::getProcessStatus, 0));

        BnProblemConfigQueryRequest problemConfigQueryRequest = new BnProblemConfigQueryRequest();
        problemConfigQueryRequest.setIsCancelReturn(1);
        ValueHolderV14<BnProblemConfigQueryResult> problemConfigQueryResultValueHolderV14 = stRpcService.queryBnProblemConfigList(problemConfigQueryRequest);

        // 获取BnProblemConfigQueryResult中所有的problemText 并且维护成一个list
        List<String> bnProblemTextList = new ArrayList<>();
        if (problemConfigQueryResultValueHolderV14.isOK()) {
            BnProblemConfigQueryResult data = problemConfigQueryResultValueHolderV14.getData();
            if (data != null) {
                bnProblemTextList = data.getProblemConfigList().stream().map(StCBnProblemConfigDO::getProblemText).collect(Collectors.toList());
            }
        }

        // 注释掉的代码已经不需要，因为当前逻辑不依赖问题类型列

        // 处理查询结果（这里可以根据实际需求添加具体处理逻辑）
        if (taskDataList != null && !taskDataList.isEmpty()) {
            result.setMessage("找到 " + taskDataList.size() + " 条待处理数据");
            for (BnTaskData taskData : taskDataList) {
                try {
                    // taskData的内容格式为{"20962":"马玉静","20951":"31.00","20950":"110101000483","20960":"通惠街道","21421":"[{\"21422\":\"YT1934574421472\"}]","-1":"259163","20948":"物流问题-破损","20959":"东坡区","20947":"20916","20958":"眉山市","20957":"四川省","20945":"圆通速递","20944":"华东临沂自营液奶仓","20943":"1@#TKYPPy+MHGgPTGyXhtbvwO5NJFVM860icmdyEakl50fTeeLI83HMl5zwLoe83tMePpWdPZ4=","20942":"抖音小店","20953":"https://work.bytenew.com/pub/attachment/downloadAtta?encryptData=%23DtfpHBX/Njrz/cDVLwcsnnNcVXpnANMqzbaZyWuebjU=%230qtoEgbuxoOm%2BLxyWSLgmA==%23D0C7X1H6F1R5X1r1%231%23%23&type=.jpg,https://work.bytenew.com/pub/attachment/downloadAtta?encryptData=%23zewsX6AuqAd6x2jeusplkayTHMeFfBUNR1bqlPYGqsc=%230qtoEgbuxoOm%2BLxyWSLgmA==%23D0C7X1H6F1R5X1r1%231%23%23&type=.jpg","20941":"6941792384296948916","20952":"30.5","1":"110430206","3":"2025-05-12 23:40:20","4":"2025-05-12 23:40:20","5":"0","6":"6941792384296948916","20949":"退款"}。转换成map
                    JSONObject jsonObject = JSONObject.parseObject(taskData.getTaskJson());
                    // 状态的id为5.枚举为[{"id":0,"title":"待处理"},{"id":1,"title":"已完成"},{"id":2,"title":"处理中"},{"id":3,"title":"暂停中"},{"id":4,"title":"已关闭"}]
                    // 将[{"id":0,"title":"待处理"},{"id":1,"title":"已完成"},{"id":2,"title":"处理中"},{"id":3,"title":"暂停中"},{"id":4,"title":"已关闭"}] 转换成一个final map

                    Integer status = jsonObject.getInteger("5");
                    // 如果status的值为4  则将处理状态设置为1
                    if (status != 1) {
                        continue;
                    }
                    // 获取快递单号和快递公司信息
                    ExpressInfo expressInfo = getExpressInfoFromTask(taskData, jsonObject, columnMap);
                    if (expressInfo == null) {
                        continue; // 日志已在方法中记录
                    }

                    // 获取问题类型信息
                    String problemType = getProblemTypeFromTask(taskData, jsonObject, columnMap);
                    if (StringUtils.isBlank(problemType)) {
                        log.warn("问题类型为空，taskId={}", taskData.getId());
                        continue;
                    }
                    if (CollectionUtils.isEmpty(bnProblemTextList)) {
                        log.warn("问题类型列表为空，taskId={}", taskData.getId());
                        continue;
                    }

                    // 检查问题类型是否符合取消条件
                    if (!bnProblemTextList.contains(problemType)) {
                        log.info("问题类型不符合取消条件，problemType={}, taskId={}", problemType, taskData.getId());
                        continue;
                    }

                    // 处理所有快递单号
                    for (String expressNo : expressInfo.getExpressNos()) {
                        try {
                            // 根据快递单号查询退换货单
                            List<OcBReturnOrder> ocBReturnOrderList = ocBReturnOrderMapper.selectByLogisticsCode(expressNo);
                            // 处理符合条件的退换货单
                            if (CollectionUtils.isNotEmpty(ocBReturnOrderList)) {
                                for (OcBReturnOrder ocBReturnOrder : ocBReturnOrderList) {
                                    try {
                                        if (canCancelReturnOrder(ocBReturnOrder, expressInfo.getLogisticsCompany())) {
                                            cancelReturnOrder(taskData, ocBReturnOrder);
                                        }
                                    } catch (Exception ex) {
                                        // 捕获单个退换货单处理异常，但继续处理其他退换货单
                                        log.error("处理单个退换货单异常，taskId={}, returnId={}, expressNo={}", taskData.getId(), ocBReturnOrder.getId(), expressNo, ex);
                                    }
                                }
                            }
                        } catch (Exception ex) {
                            // 捕获单个快递单号处理异常，但继续处理其他快递单号
                            log.error("处理快递单号异常，taskId={}, expressNo={}", taskData.getId(), expressNo, ex);
                        }
                    }
                } catch (Exception e) {
                    // 捕获单个班牛工单处理异常，但继续处理其他工单
                    log.error("处理班牛工单异常，taskId={}", taskData.getId(), e);
                }
            }
            // 将taskDataList中所有的数据 处理状态都设置为1
            bnTaskDataMapper.batchUpdateStatus(taskDataList.stream().map(BnTaskData::getId).collect(Collectors.toList()), 1);
        } else {
            result.setMessage("没有找到需要处理的数据");
        }
        return result;
    }

    /**
     * 检查退换货单是否符合取消条件
     *
     * @param ocBReturnOrder 退换货单
     * @param logisticsCompany 快递公司名称
     * @return 是否符合取消条件
     */
    private boolean canCancelReturnOrder(OcBReturnOrder ocBReturnOrder, String logisticsCompany) {
        // 单据类型=退货单 单据状态=等待退货入库 退货状态=待入库 虚拟入库状态=未虚拟入库, 退换货单的快递公司名称=logisticsCompany
        return ocBReturnOrder.getBillType() == 1
                && ocBReturnOrder.getReturnStatus() == 20
                && ocBReturnOrder.getProReturnStatus() == 0
                && ocBReturnOrder.getInventedStatus() == 0
                && logisticsCompany.equals(ocBReturnOrder.getCpCLogisticsEname());
    }

    /**
     * 取消退换货单
     *
     * @param taskData 班牛工单数据
     * @param ocBReturnOrder 退换货单
     */
    private void cancelReturnOrder(BnTaskData taskData, OcBReturnOrder ocBReturnOrder) {
        try {
            JSONArray jsonArray = new JSONArray();
            jsonArray.add(ocBReturnOrder.getId());
            // 取消退换货单
            ValueHolderV14 valueHolderV14 = cancelChangingOrRefundService.oneOcCancle(SystemUserResource.getRootUser(), new ValueHolderV14<>(), jsonArray);
            if (valueHolderV14.isOK()) {
                // 取消成功之后。 将退换货单对应的取消备注设置为 班牛客服登记无需寄回取消
                OcBReturnOrder returnOrder = ocBReturnOrderMapper.selectByid(ocBReturnOrder.getId());
                if (Objects.equals(returnOrder.getReturnStatus(), ReturnStatusEnum.CANCLE.getVal())) {
                    returnOrder.setRemark("班牛客服登记无需寄回取消");
                    // 设置取消原因
                    returnOrder.setCancelReason("班牛客服登记无需寄回");
                    ocBReturnOrderMapper.updateById(returnOrder);
                    log.info("退换货单自动取消成功，taskId={},returnId={}", taskData.getId(), ocBReturnOrder.getId());
                }
            } else {
                log.info("退换货单自动取消失败，taskId={},returnId={}", taskData.getId(), ocBReturnOrder.getId());
            }
        } catch (Exception e) {
            log.error("退换货单自动取消异常，taskId={},returnId={}", taskData.getId(), ocBReturnOrder.getId(), e);
        }
    }

    /**
     * 从班牛工单中提取问题类型
     *
     * @param taskData 班牛工单数据
     * @param jsonObject 解析后的工单JSON对象
     * @return 问题类型，如果无法获取则返回null
     */
    private String getProblemTypeFromTask(BnTaskData taskData, JSONObject jsonObject, Map<String, StCBnColumnListDO> columnMap) {
        // 从缓存中获取问题类型列
        StCBnColumnListDO problemTypeColumn = columnMap.get("问题类型");

        // 如果缓存中没有对应列，则返回null
        if (problemTypeColumn == null) {
            log.warn("缓存中未找到问题类型列，taskId={}", taskData.getId());
            return null;
        }

        // 获取问题类型
        String columnId = problemTypeColumn.getColumnId().toString();
        return jsonObject.getString(columnId);
    }

    /**
     * 从班牛工单中提取快递信息
     *
     * @param taskData 班牛工单数据
     * @param jsonObject 解析后的工单JSON对象
     * @return 快递信息对象，如果无法获取则返回null
     */
    private ExpressInfo getExpressInfoFromTask(BnTaskData taskData, JSONObject jsonObject, Map<String, StCBnColumnListDO> columnMap) {
        // 从缓存中获取快递单号新的列
        StCBnColumnListDO expressColumn = columnMap.get("快递单号新");
        StCBnColumnListDO logisticsCompanyColumn = columnMap.get("快递公司");
        StCBnColumnListDO logisticsCompanyColumn = columnMap.get("快递公司");

        // 如果缓存中没有对应列，则返回null
        if (expressColumn == null) {
            log.warn("缓存中未找到快递单号新列，taskId={}", taskData.getId());
            return null;
        }

        if (logisticsCompanyColumn == null) {
            log.warn("缓存中未找到快递公司列，taskId={}", taskData.getId());
            return null;
        }

        // 获取快递单号和快递公司
        String expressColumnId = expressColumn.getColumnId().toString();
        String subExpressColumnId = null;
        if (org.apache.commons.lang.StringUtils.isNotBlank(expressColumn.getSonColumnBos())){
            // [{"column_id":21422,"name":"快递单号新","behavior_type":1,"is_inside":false,"type":"STRING"}] 获取子列的columnId
            subExpressColumnId = JSONObject.parseArray(expressColumn.getSonColumnBos()).getJSONObject(0).getString("column_id");
        }
        String logisticsColumnId = logisticsCompanyColumn.getColumnId().toString();

        String expressNoStr = jsonObject.getString(expressColumnId);
        String logisticsCompany = jsonObject.getString(logisticsColumnId);

        // 如果快递单号或快递公司为空，则返回null
        if (StringUtils.isBlank(expressNoStr)) {
            log.warn("快递单号为空，taskId={}", taskData.getId());
            return null;
        }

        if (StringUtils.isBlank(logisticsCompany)) {
            log.warn("快递公司为空，taskId={}", taskData.getId());
            return null;
        }

        // 解析快递单号，可能是单个快递单号或者JSON数组字符串
        List<String> expressNos = new ArrayList<>();

        try {
            // 尝试解析为JSON数组
            if (expressNoStr.startsWith("[") && expressNoStr.endsWith("]")) {
                JSONArray jsonArray = JSONArray.parseArray(expressNoStr);
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject item = jsonArray.getJSONObject(i);
                    String no = null;
                    // 假设每个快递单号项都有一个"21422"字段
                    if (StringUtils.isNotEmpty(subExpressColumnId)){
                        no = item.getString(subExpressColumnId);
                    }else {
                        no = item.getString("21422");
                    }
                    if (StringUtils.isNotBlank(no)) {
                        expressNos.add(no);
                    }
                }
            } else {
                // 如果不是JSON数组，则直接添加单个快递单号
                expressNos.add(expressNoStr);
            }
        } catch (Exception e) {
            // 如果解析失败，则将原始字符串作为单个快递单号
            log.warn("解析快递单号失败，使用原始字符串，taskId={}, expressNoStr={}", taskData.getId(), expressNoStr, e);
            expressNos.add(expressNoStr);
        }

        if (expressNos.isEmpty()) {
            log.warn("解析后的快递单号列表为空，taskId={}", taskData.getId());
            return null;
        }

        return new ExpressInfo(expressNos, logisticsCompany);
    }

    /**
     * 快递信息类，包含快递单号列表和快递公司
     */
    @Data
    private static class ExpressInfo {
        private List<String> expressNos;  // 快递单号列表
        private String logisticsCompany;  // 快递公司

        public ExpressInfo(List<String> expressNos, String logisticsCompany) {
            this.expressNos = expressNos;
            this.logisticsCompany = logisticsCompany;
        }
    }
}