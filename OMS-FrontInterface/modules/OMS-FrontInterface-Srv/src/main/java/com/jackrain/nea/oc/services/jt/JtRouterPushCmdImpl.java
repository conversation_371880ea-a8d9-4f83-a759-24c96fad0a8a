package com.jackrain.nea.oc.services.jt;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jackrain.nea.ip.common.JtResultCodeEnum;
import com.jackrain.nea.ip.model.result.jt.JtInterceptReceiveRequest;
import com.jackrain.nea.ip.model.result.jt.JtInterceptResponse;
import com.jackrain.nea.oc.oms.api.jt.JtRouterPushCmd;
import com.jackrain.nea.oc.oms.mapper.OcBOrderLogisticsInterceptMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrderLogisticsIntercept;
import com.jackrain.nea.oc.oms.nums.LogisticsInterceptStatusEnum;
import com.jackrain.nea.utility.LogUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @Date 2023/4/21 15:50
 * @Description TODO
 * @Version 1.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class JtRouterPushCmdImpl implements JtRouterPushCmd {
    
    final OcBOrderLogisticsInterceptMapper ocBOrderLogisticsInterceptMapper;
    
    
    @Override
    public String jtInterceptReceivePush(String request) {
        log.info(LogUtil.format("JtRouterPushCmdImpl.jtInterceptReceivePush,request:{}",
                "JtRouterPushCmdImpl.jtInterceptReceivePush"), request);
        JtInterceptResponse response = new JtInterceptResponse();
        response.setCode(JtResultCodeEnum.FAILURE.getCode());
        response.setMsg("系统异常");
        JtInterceptReceiveRequest jtInterceptReceiveRequest = JSONObject.parseObject(request, JtInterceptReceiveRequest.class);
        if (StringUtils.isEmpty(jtInterceptReceiveRequest.getMailNo()) || StringUtils.isEmpty(jtInterceptReceiveRequest.getInterceptResult())) {
            response.setMsg("运单号或拦截结果状态不能为空！");
            return JSON.toJSONString(response);
        }
        List<OcBOrderLogisticsIntercept> interceptList =
                ocBOrderLogisticsInterceptMapper.selectList(new LambdaQueryWrapper<OcBOrderLogisticsIntercept>()
                        .eq(OcBOrderLogisticsIntercept::getExpresscode, jtInterceptReceiveRequest.getMailNo())
                        .ne(OcBOrderLogisticsIntercept::getInterceptStatus, LogisticsInterceptStatusEnum.INTERCEPT_SUCCESS.getKey())
                        .ne(OcBOrderLogisticsIntercept::getInterceptStatus, LogisticsInterceptStatusEnum.SEND_INTERCEPT_FAIL.getKey())
//                        .ne(OcBOrderLogisticsIntercept::getInterceptStatus, LogisticsInterceptStatusEnum.INTERCEPT_FAIL.getKey())
//                        .ne(OcBOrderLogisticsIntercept::getInterceptStatus, LogisticsInterceptStatusEnum.NOT_INTERCEPT.getKey()) 不能过滤未拦截状态，有可能会漏
                );
        if (CollectionUtils.isEmpty(interceptList)) {
            response.setMsg("运单号不存在！(通过thirdBizNo查询)");
            return JSON.toJSONString(response);
        }
        for (OcBOrderLogisticsIntercept logisticsIntercept : interceptList) {
            Integer beforeStatus = logisticsIntercept.getInterceptStatus();

            logisticsIntercept.setModifieddate(new Date());
            LogisticsInterceptStatusEnum interceptStatus = convertInterceptStatus(jtInterceptReceiveRequest.getInterceptResult());
            logisticsIntercept.setInterceptStatus(interceptStatus.getKey());
            if (interceptStatus == LogisticsInterceptStatusEnum.INTERCEPT_SUCCESS) {
                logisticsIntercept.setRemark(jtInterceptReceiveRequest.getReturnMailNo());
            } else if (interceptStatus == LogisticsInterceptStatusEnum.INTERCEPT_FAIL) {
                logisticsIntercept.setRemark(jtInterceptReceiveRequest.getErrorDesc());
                logisticsIntercept.setInterceptFailureCount(logisticsIntercept.getInterceptFailureCount() + 1);
            }

            log.info(LogUtil.format("极兔接收拦截信息推送，报文:{},ID:{},更新前状态:{}，更新后状态:{}",
                            "JtRouterPushCmdImpl.jtInterceptReceivePush"), request, logisticsIntercept.getId(),
                    LogisticsInterceptStatusEnum.getEnumByKey(beforeStatus).getDesc(), interceptStatus.getDesc());
            ocBOrderLogisticsInterceptMapper.updateById(logisticsIntercept);
        }
        response.setCode(JtResultCodeEnum.SUCCESS.getCode());
        response.setMsg("拦截状态推送成功");
        return JSON.toJSONString(response);
    }

    /**
     * 极兔状态转换成通用的
     */
    public LogisticsInterceptStatusEnum convertInterceptStatus(String interceptResult){
        LogisticsInterceptStatusEnum logisticsInterceptStatusEnum = null;

        switch (interceptResult) {
            case "success":
                logisticsInterceptStatusEnum = LogisticsInterceptStatusEnum.INTERCEPT_SUCCESS;
                break;
            case "fail":
                logisticsInterceptStatusEnum = LogisticsInterceptStatusEnum.INTERCEPT_FAIL;
                break;
            case "processing":
                logisticsInterceptStatusEnum = LogisticsInterceptStatusEnum.INTERCEPTING;
                break;
            default:
        }
        return logisticsInterceptStatusEnum;
    }

}
