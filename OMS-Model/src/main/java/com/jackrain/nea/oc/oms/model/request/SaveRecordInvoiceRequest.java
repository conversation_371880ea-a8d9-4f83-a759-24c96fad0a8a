package com.jackrain.nea.oc.oms.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.oc.oms.model.result.QueryOrderResult;
import com.jackrain.nea.oc.oms.model.table.OcBOrderInvoiceInform;
import lombok.Data;

import java.io.Serializable;

/**
 * 记录开票
 *
 * @author: xiWen.z
 * create at: 2019/7/24 0024
 */
@Data
public class SaveRecordInvoiceRequest implements Serializable {

    @JSONField(name = "OC_B_ORDER_INVOICE_INFORM")
    private OcBOrderInvoiceInform ocBOrderInvoiceInform;

    @JSONField(name = "QUERYORDERRESULT")
    private QueryOrderResult queryOrderResult;
}
