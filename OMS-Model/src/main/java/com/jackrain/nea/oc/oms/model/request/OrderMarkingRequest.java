package com.jackrain.nea.oc.oms.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: ming.fz
 * @Date: 2022-06-14
 * @Version 1.0
 */
@Data
public class OrderMarkingRequest implements Serializable {

    @JSONField(name = "IDS")
    private Long[] ids;

    /**
     * 自定义标签档案id
     */
    @JSONField(name = "ST_C_CUSTOM_LABEL_ID")
    private String stCCustomLabelId;

    /**
     * 自定义标签档案ename
     */
    @JSONField(name = "ST_C_CUSTOM_LABEL_ENAME")
    private String stCCustomLabelEname;
}
