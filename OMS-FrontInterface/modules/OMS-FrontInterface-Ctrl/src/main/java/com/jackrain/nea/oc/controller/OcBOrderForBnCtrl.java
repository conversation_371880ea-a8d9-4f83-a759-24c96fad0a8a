package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.oc.model.BnQueryForLogisticsProblemResult;
import com.jackrain.nea.oc.oms.api.OcBOrderForBnCmd;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;

/**
 * @ClassName OcBOrderForBnCtrl
 * @Description 班牛对接相关
 * <AUTHOR>
 * @Date 2024/11/15 16:07
 * @Version 1.0
 */
@Slf4j
@RestController
public class OcBOrderForBnCtrl {


    @Autowired
    private OcBOrderForBnCmd bnCmd;
    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @ApiOperation(value = "根据订单id 查询班牛物流问题返回参数")
    @RequestMapping(path = "/api/cs/oc/oms/v1/getForLogisticsProblem", method = RequestMethod.POST)
    public JSONObject getForLogisticsProblem(HttpServletRequest request, @RequestParam(value = "param") String param) {
        User loginUser = r3PrimWebAuthService.getLoginPrimWebUser(request);
        JSONObject jsonObject = JSONObject.parseObject(param);
        ValueHolderV14<BnQueryForLogisticsProblemResult> problemResultValueHolderV14 = bnCmd.getBnInfoByOrderId(jsonObject.getLong("id"));
        return problemResultValueHolderV14.toJSONObject();
    }

    @ApiOperation(value = "推送物流问题到班牛系统")
    @RequestMapping(path = "/api/cs/oc/oms/v1/pushLogisticsProblem", method = RequestMethod.POST)
    public JSONObject pushLogisticsProblem(HttpServletRequest request, @RequestBody JSONObject obj) {
        User loginUser = r3PrimWebAuthService.getLoginPrimWebUser(request);
        return bnCmd.pushLogisticsProblem(obj.toJSONString(), loginUser).toJSONObject();
    }

    @ApiOperation(value = "批量推送物流问题到班牛系统")
    @RequestMapping(path = "/api/cs/oc/oms/v1/batchPushLogisticsProblem", method = RequestMethod.POST)
    public JSONObject batchPushLogisticsProblem(HttpServletRequest request, @RequestBody JSONObject obj) {
        User loginUser = r3PrimWebAuthService.getLoginPrimWebUser(request);
        return bnCmd.batchPushLogisticsProblem(obj, loginUser).toJSONObject();
    }

    @ApiOperation(value = "上传附件")
    @RequestMapping(path = "/api/cs/oc/oms/v1/uploadFile", method = RequestMethod.POST)
    public JSONObject uploadFile(HttpServletRequest request, @RequestParam(value = "file", required = true) MultipartFile file) {
        return bnCmd.uploadFile(file).toJSONObject();
    }
}
