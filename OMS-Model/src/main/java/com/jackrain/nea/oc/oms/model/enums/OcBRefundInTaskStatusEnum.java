package com.jackrain.nea.oc.oms.model.enums;

/**
 * <AUTHOR>
 * @date 2020/12/7 10:43
 * @desc 退货入库任务表 状态 枚举
 */
public enum OcBRefundInTaskStatusEnum {

    /**
     * STATUS
     */
    INIT("初始状态", 0),
    RESOLVE_FAIL("解析失败", 1),
    HANDLE_FAIL("处理失败", 2),
    SUCCESS("回传成功", 3);

    private final String desc;
    private final int val;

    OcBRefundInTaskStatusEnum(String desc, int v) {
        this.desc = desc;
        this.val = v;
    }

    public String getDesc() {
        return desc;
    }

    public int getVal() {
        return val;
    }
}
