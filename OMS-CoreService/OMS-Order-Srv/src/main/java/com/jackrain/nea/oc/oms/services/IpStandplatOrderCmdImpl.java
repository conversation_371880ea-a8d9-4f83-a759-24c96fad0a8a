package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.oc.oms.api.IpStandplatOrderCmd;
import com.jackrain.nea.oc.request.StandplatOrderCreateModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> zhuxing
 * @Date : 2021-12-16 17:52
 * @Description : 通用订单服务
 **/
@Component
@Service(protocol = "dubbo", validation = "true", group = "oc-core", version = "1.0")
public class IpStandplatOrderCmdImpl implements IpStandplatOrderCmd {

    @Autowired
    private IpStandplatOrderService ipStandplatOrderService;

    /**
     * 保存通用订单
     * @param standplatOrderCreateModel
     * @return
     */
    @Override
    public ValueHolderV14 saveIpStandplatOrder(StandplatOrderCreateModel standplatOrderCreateModel) {
        return ipStandplatOrderService.saveIpStandplatOrder(standplatOrderCreateModel);
    }
}
