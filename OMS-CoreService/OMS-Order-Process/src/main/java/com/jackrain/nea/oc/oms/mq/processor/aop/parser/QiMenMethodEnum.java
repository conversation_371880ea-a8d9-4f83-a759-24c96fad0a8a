package com.jackrain.nea.oc.oms.mq.processor.aop.parser;

import lombok.Getter;

import java.util.Objects;

/**
 * Description： 奇门标准接口类型
 * Author: RESET
 * Date: Created in 2020/6/15 21:39
 * Modified By:
 */
public enum QiMenMethodEnum {

    // 匹配策略类型
    RETURNORDER_CONFIRM(0, "returnorder.confirm", "退单回传"),
    ITEMLACK_REPORT(1, "itemlack.report", "实缺单");

    @Getter
    private Integer value;
    @Getter
    private String code;
    @Getter
    private String description;

    QiMenMethodEnum(Integer value, String code, String description) {
        this.value = value;
        this.code = code;
        this.description = description;
    }

    @Override
    public String toString() {
        return String.valueOf(value);
    }

    public static QiMenMethodEnum fromValue(Integer v) {
        for (QiMenMethodEnum c : QiMenMethodEnum.values()) {
            if (Objects.equals(v, c.value)) {
                return c;
            }
        }
        throw new IllegalArgumentException(String.valueOf(v));
    }

}
