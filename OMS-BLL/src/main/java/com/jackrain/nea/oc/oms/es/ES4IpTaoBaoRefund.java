package com.jackrain.nea.oc.oms.es;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;

/**
 * <AUTHOR>
 * @date 2020/11/12 10:13 上午
 */
public class ES4IpTaoBaoRefund {

    private ES4IpTaoBaoRefund() {
    }

    /**
     * 根据 oId 查询 id
     *
     * @param oId 明细id
     * @return JSONObject
     */
    public static JSONObject getIdByOid(String oId) {
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("OID", oId);
        JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.IP_B_TAOBAO_REfUND_INDEX_NAME,
                OcElasticSearchIndexResources.IP_B_TAOBAO_REfUND_INDEX_NAME,
                whereKeys, null, null, 200, 0, new String[]{"ID"});
        return search;
    }
}
