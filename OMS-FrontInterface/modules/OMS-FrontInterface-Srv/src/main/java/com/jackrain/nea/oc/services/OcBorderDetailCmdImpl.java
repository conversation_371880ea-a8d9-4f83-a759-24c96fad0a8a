package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBorderDetailCmd;
import com.jackrain.nea.oc.oms.model.table.OcBorderItemExtention;
import com.jackrain.nea.oc.oms.services.OcBorderDetailService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;


/**
 * @author: 孙俊磊
 * @since: 2019-03-11
 * create at:  2019-03-11 19:11
 */

@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBorderDetailCmdImpl implements OcBorderDetailCmd {

    @Autowired
    private OcBorderDetailService service;

    @Override
    public ValueHolderV14 getOrderDetailList(String param, User loginUser) {
        return service.getOrderDetailList(param, loginUser);
    }

    @Override
    public ValueHolderV14<Map<Long, List<OcBorderItemExtention>>> getBatchOrderDetailList(List<JSONObject> params, User loginUser) throws NDSException {
        return service.getBatchOrderDetailList(params, loginUser);
    }

    @Override
    public ValueHolderV14 saveStandards(String param, User loginUser) {
        return service.saveStandards(param, loginUser);
    }
}

