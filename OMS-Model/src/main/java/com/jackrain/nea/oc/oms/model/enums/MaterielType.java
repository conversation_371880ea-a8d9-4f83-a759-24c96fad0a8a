package com.jackrain.nea.oc.oms.model.enums;

/**
 * 物料类型
 *
 * @author: ming.fz
 * @since: 2019-3-21
 */
public enum MaterielType {

    /**
     * 物料类型为鞋 成人
     */
    ADULT,

    /**
     * 物料类型为鞋 儿童
     */
    CHILD;


    /**
     * 物料类型为服装 成人 ZF01
     * 物料类型为服装 儿童 Zf02
     * 物料类型为配件 成人 ZF05
     * 物料类型为配件 儿童 Zf06
     * 物料类型为推广用品  ZF07
     * 物料类型为主料     Zf08
     */

    /**
     * @return
     */
    public String getEcode() {
        if (this == MaterielType.ADULT) {
            return "ZF03";
        } else if (this == MaterielType.CHILD) {
            return "ZF04";
        }
        return null;
    }

}
