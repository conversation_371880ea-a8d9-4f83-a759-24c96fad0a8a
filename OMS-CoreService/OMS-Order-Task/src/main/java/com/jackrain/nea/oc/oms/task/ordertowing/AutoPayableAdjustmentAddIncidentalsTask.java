//package com.jackrain.nea.oc.oms.task.ordertowing;
//
//import com.alibaba.fastjson.JSONObject;
//import com.google.common.base.Throwables;
//import com.jackrain.nea.ac.model.AcFCompensationReason;
//import com.jackrain.nea.ac.model.AcFPayableAdjustmentDO;
//import com.jackrain.nea.cpext.model.table.CpShop;
//import com.jackrain.nea.data.basic.model.request.ShopQueryRequest;
//import com.jackrain.nea.data.basic.services.BasicCpQueryService;
//import com.jackrain.nea.ip.model.wing.WingIncidentalsOrderCreateModel;
//import com.jackrain.nea.ip.model.wing.WingReturnResult;
//import com.jackrain.nea.oc.oms.es.ES4PushToWingOrderQueryService;
//import com.jackrain.nea.oc.oms.mapper.ac.AcFCompensationReasonMapper;
//import com.jackrain.nea.oc.oms.mapper.ac.AcFPayableAdjustmentItemMapper;
//import com.jackrain.nea.oc.oms.mapper.ac.AcFPayableAdjustmentMapper;
//import com.jackrain.nea.oc.oms.model.enums.ToDRPStatusEnum;
//import com.jackrain.nea.oc.oms.task.BaseR3Task;
//import com.jackrain.nea.oc.oms.util.SplitMessageUtil;
//import com.jackrain.nea.rpc.IpRpcService;
//import com.jackrain.nea.sys.domain.ValueHolderV14;
//import com.jackrain.nea.task.IR3Task;
//import com.jackrain.nea.task.RunTaskResult;
//import com.jackrain.nea.util.DateUtil;
//import com.jackrain.nea.utility.LogUtil;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.apache.commons.collections.MapUtils;
//import org.assertj.core.util.Lists;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Component;
//
//import java.util.Date;
//import java.util.HashMap;
//import java.util.List;
//
//
///***
// * 丢件单传wing杂费单服务
// */
//@Slf4j
//@Component
//public class AutoPayableAdjustmentAddIncidentalsTask extends BaseR3Task implements IR3Task {
//
//    @Autowired
//    private AcFPayableAdjustmentMapper payableAdjustmentMapper;
//    @Autowired
//    private AcFPayableAdjustmentItemMapper adjustmentItemMapper;
//
//    @Autowired
//    private AcFCompensationReasonMapper acFCompensationReasonMapper;
//
//    @Autowired
//    private IpRpcService ipRpcService;
//
//    @Autowired
//    private BasicCpQueryService basicCpQueryService;
//
//    @Value("${wing.incidental.order.default.store.code:ECTZ1}")
//    private String defaultStoreCode;
//
//
//    @Override
//    public RunTaskResult execute(JSONObject params) {
//        log.debug(LogUtil.format("进入丢件单传wing杂费单服务..",  "丢件单传wing杂费单服务"));
//        RunTaskResult result = new RunTaskResult();
//        long starTime = System.currentTimeMillis();
//        try {
//            List<Long> idList = ES4PushToWingOrderQueryService.findAdjustPaymentAddIncidentals(0, 200);
//            if (CollectionUtils.isEmpty(idList)) {
//                result.setSuccess(true);
//                result.setMessage("未从ES查询到符合条件的数据");
//                return result;
//            }
//            List<AcFPayableAdjustmentDO> orderList = payableAdjustmentMapper.selectBatchIds(idList);
//            if (CollectionUtils.isEmpty(orderList)) {
//                result.setSuccess(true);
//                result.setMessage("未从数据库查询到有效的数据");
//                return result;
//            }
//            int fail = 0;
//            for (AcFPayableAdjustmentDO order : orderList) {
//                ValueHolderV14 v14 = null;
//                try {
//                    WingIncidentalsOrderCreateModel createModel = this.transferFeild(order);
//                    v14 = ipRpcService.addIncidentals(createModel);
//                } catch (Exception e) {
//                    fail++;
//                    log.error(LogUtil.format("丢件单传wing杂费单服务,异常！{}",  "丢件单传wing杂费单服务"), Throwables.getStackTraceAsString(e));
//                }
//                if (v14 != null) {
//                    AcFPayableAdjustmentDO update = new AcFPayableAdjustmentDO();
//                    update.setId(order.getId());
//                    if (v14.isOK()) {
//                        try {
//                            WingReturnResult returnResult = (WingReturnResult) v14.getData();
//                            if (returnResult.getStatus() == 1) {
//                                List<WingReturnResult.FailMsg> faileds = returnResult.getFaileds();
//                                if (CollectionUtils.isNotEmpty(faileds)) {
//                                    String message = SplitMessageUtil.splitErrMsgBySize(faileds.get(0).getErrMsg(), SplitMessageUtil.SIZE_200);
//                                    update.setToDrpStatus(ToDRPStatusEnum.FAIL.getCode());
//                                    update.setToDrpFailedReason(message);
//                                    int count = order.getToDrpCount() == null ? 0 : order.getToDrpCount();
//                                    update.setToDrpCount(count + 1);
//                                } else {
//                                    update.setToDrpStatus(ToDRPStatusEnum.SUCCESS.getCode());
//                                    update.setToDrpFailedReason("");
//                                    update.setToDrpCount(0);
//                                }
//                            } else {
//                                fail++;
//                                update.setToDrpStatus(ToDRPStatusEnum.FAIL.getCode());
//                                update.setToDrpFailedReason(returnResult.getMsg());
//                                int count = order.getToDrpCount() == null ? 0 : order.getToDrpCount();
//                                update.setToDrpCount(count + 1);
//                            }
//
//                        } catch (Exception e) {
//                            fail++;
//                            update.setToDrpStatus(ToDRPStatusEnum.FAIL.getCode());
//                            update.setToDrpFailedReason("解析转换传wing结果异常");
//                            int count = order.getToDrpCount() == null ? 0 : order.getToDrpCount();
//                            update.setToDrpCount(count + 1);
//                            log.error(LogUtil.format("解析转换传wing结果异常！{}",  "丢件单传wing杂费单服务"), Throwables.getStackTraceAsString(e));
//                        }
//                    } else {
//                        fail++;
//                        update.setToDrpStatus(ToDRPStatusEnum.FAIL.getCode());
//                        update.setToDrpFailedReason(v14.getMessage());
//                        int count = order.getToDrpCount() == null ? 0 : order.getToDrpCount();
//                        update.setToDrpCount(count + 1);
//                    }
//                    payableAdjustmentMapper.updateById(update);
//                }
//
//            }
//            result.setSuccess(true);
//            String msg = "执行成功条数：" + (orderList.size() - fail) + ",失败条数：" + fail;
//            result.setMessage(msg);
//            long endTime = System.currentTimeMillis();
//            long Time = endTime - starTime;
//            if (log.isDebugEnabled()) {
//                log.debug(LogUtil.format("定时任务耗时！{}",  "丢件单传wing杂费单服务"), Time);
//            }
//        } catch (Exception e) {
//            log.error(LogUtil.format("AutoPayableAdjustmentAddIncidentalsTask.Execute Error:{}",  "丢件单传wing杂费单服务"), Throwables.getStackTraceAsString(e));
//            result.setSuccess(false);
//            result.setMessage(e.getMessage());
//        }
//        return result;
//    }
//
//    private WingIncidentalsOrderCreateModel transferFeild(AcFPayableAdjustmentDO order) {
//        WingIncidentalsOrderCreateModel createModel = new WingIncidentalsOrderCreateModel();
//        //("中台订单号") id;
//        createModel.setId(order.getBillNo());
//        //("原始中台订单号") original_id;
//        createModel.setOriginal_id(order.getBillNo());
//        //("原始平台单号") original_tid;
//        createModel.setOriginal_tid(order.getTid());
//        //("下单店铺名称") shopName;
//        createModel.setShopName(order.getCpCShopTitle());
//        ShopQueryRequest shopQueryRequest = new ShopQueryRequest();
//        shopQueryRequest.setShopIds(Lists.newArrayList(order.getCpCShopId()));
//        HashMap<Long, CpShop> shopInfoMap = basicCpQueryService.getShopInfo(shopQueryRequest);
//        if (MapUtils.isNotEmpty(shopInfoMap)) {
//            CpShop cpShop = shopInfoMap.get(order.getCpCShopId());
//            if (cpShop != null) {
//                //("下单店铺CODE") shopCode;
//                createModel.setShopCode(cpShop.getEcode());
//                createModel.setShopName(cpShop.getCpCShopTitle());
//            }
//        }
//        //("退货逻辑仓编号:默认都是做到 ECTZ1") warehouseCode;
//        createModel.setWarehouseCode(defaultStoreCode);
//        //("传杂费单退货原因列表ID") omsreasontypeid;
//        createModel.setOmsreasontypeid("1");
//        //("单据类型") incidentalsType;
//        createModel.setIncidentalsType("丢件单");
//        //("退款商品描述") refunddescription;
//        createModel.setRefunddescription("丢件单");
//        AcFCompensationReason reason = this.acFCompensationReasonMapper.selectById(order.getAcFCompensationReasonId());
//        if (reason != null) {
//            createModel.setOmsreasontypeid(reason.getAcFCompensationTypeEcode());
//            //("退款商品描述") refunddescription;
//            createModel.setRefunddescription(reason.getAcFCompensationTypeEname());
//        }
//        //("支付方式:传支付类型列表ID") paytype;
//        createModel.setPaytype("2");
//        //("付款时间(原始订单付款时间)") paytime;
//        Date payTime = order.getPayTime() == null ? new Date() : order.getPayTime();
//        createModel.setPaytime(DateUtil.format(payTime, DateUtil.dateTimeSecondsFormatter.getPattern()));
//        //创建时间
//        Date creationDate = order.getCreationdate() == null ? new Date() : order.getCreationdate();
//        createModel.setCreationDate(DateUtil.format(creationDate, DateUtil.dateTimeSecondsFormatter.getPattern()));
//        //("退款日期(打款时间):yyyy-mm-dd hh24:mm:ss") refunddate;
//        Date financialTrialTime = order.getFinancialTrialTime() == null ? new Date() : order.getFinancialTrialTime();
//        createModel.setRefunddate(DateUtil.format(financialTrialTime, DateUtil.dateTimeSecondsFormatter.getPattern()));
//        //("退商品金额: 正数斯凯奇应付，负数斯凯奇应收") refundgoodsamount;
//        createModel.setRefundgoodsamount(order.getOriginOrderAmt() == null ? "0" : order.getOriginOrderAmt().toString());
//        //("备注") remark;
//        createModel.setRemark(order.getRemark());
//        return createModel;
//    }
//}
//
