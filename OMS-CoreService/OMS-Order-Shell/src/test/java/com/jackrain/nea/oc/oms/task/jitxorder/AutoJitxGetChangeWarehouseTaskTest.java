package com.jackrain.nea.oc.oms.task.jitxorder;

import com.jackrain.nea.OmsOrderProcessApplication;
import com.jackrain.nea.task.RunTaskResult;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = OmsOrderProcessApplication.class)
public class AutoJitxGetChangeWarehouseTaskTest {
    @Autowired
    private AutoJitxGetChangeWarehouseTask transferTask;

    @Test
    public void getRunTaskResult() {

        RunTaskResult runTaskResult = transferTask.execute(null);
        System.out.println(runTaskResult);
        Assert.assertTrue(runTaskResult.isSuccess());
    }
}