//package com.jackrain.nea.oc.oms.task.ordertowing;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import com.google.common.base.Throwables;
//import com.jackrain.nea.constants.ResultCode;
//import com.jackrain.nea.cpext.model.table.CpShop;
//import com.jackrain.nea.ip.model.wing.WingIncidentalsOrderCreateModel;
//import com.jackrain.nea.ip.model.wing.WingReturnResult;
//import com.jackrain.nea.oc.oms.es.ES4PushToWingOrderQueryService;
//import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
//import com.jackrain.nea.oc.oms.mapper.OcBReturnAfSendItemMapper;
//import com.jackrain.nea.oc.oms.mapper.OcBReturnAfSendMapper;
//import com.jackrain.nea.oc.oms.mapper.OcBReturnTypeMapper;
//import com.jackrain.nea.oc.oms.model.enums.ToDRPStatusEnum;
//import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
//import com.jackrain.nea.oc.oms.model.table.OcBReturnAfSend;
//import com.jackrain.nea.oc.oms.model.table.OcBReturnAfSendItem;
//import com.jackrain.nea.oc.oms.model.table.OcBReturnType;
//import com.jackrain.nea.oc.oms.task.BaseR3Task;
//import com.jackrain.nea.oc.oms.util.SplitMessageUtil;
//import com.jackrain.nea.rpc.CpRpcService;
//import com.jackrain.nea.rpc.IpRpcService;
//import com.jackrain.nea.sys.domain.ValueHolderV14;
//import com.jackrain.nea.task.IR3Task;
//import com.jackrain.nea.task.RunTaskResult;
//import com.jackrain.nea.util.DateUtil;
//import com.jackrain.nea.utility.LogUtil;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Component;
//
//import java.util.Date;
//import java.util.List;
//import java.util.Map;
//import java.util.stream.Collectors;
//
//
///***
// *  -发货后仅退款单传wing服务
// * 杂费单传WING
// */
//@Slf4j
//@Component
//public class AutoReturnAfSendOrderToWingTask extends BaseR3Task implements IR3Task {
//
//    @Autowired
//    private OcBReturnAfSendMapper ocBOrderMapper;
//    @Autowired
//    private OcBReturnAfSendItemMapper orderItemMapper;
//
//    @Autowired
//    private OcBReturnTypeMapper returnTypeMapper;
//
//    @Autowired
//    private OcBOrderItemMapper ocBOrderItemMapper;
//
//    @Autowired
//    private CpRpcService cpRpcService;
//
//    @Value("${wing.incidental.order.default.store.code:ECTZ1}")
//    private String defaultStoreCode;
//
//    @Autowired
//    private IpRpcService ipRpcService;
//
//    @Override
//    public RunTaskResult execute(JSONObject params) {
//        log.debug(LogUtil.format("进入发货后仅退款单传wing服务",  "发货后仅退款单传wing服务"));
//        RunTaskResult result = new RunTaskResult();
//        long starTime = System.currentTimeMillis();
//        try {
//            List<Long> orderIdList = ES4PushToWingOrderQueryService.findReturnAfSendOrder(0, 200);
//            if (CollectionUtils.isEmpty(orderIdList)) {
//                result.setSuccess(true);
//                result.setMessage("未从ES查询到符合条件的数据");
//                return result;
//            }
//            List<OcBReturnAfSend> orderList = ocBOrderMapper.selectBatchIds(orderIdList);
//            List<OcBReturnAfSendItem> ocBReturnAfSendItems = orderItemMapper.selectByOcBReturnAfSendIdList(orderIdList);
//            Map<Long, List<OcBReturnAfSendItem>> map = ocBReturnAfSendItems.stream().collect(Collectors.groupingBy(i -> i.getOcBReturnAfSendId()));
//            if (CollectionUtils.isEmpty(orderList)) {
//                result.setSuccess(true);
//                result.setMessage("未从数据库查询到有效的数据");
//                return result;
//            }
//            int fail = 0;
//            for (OcBReturnAfSend order : orderList) {
//                ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS,"success");
//                try {
//                    boolean flag = true;
//                    /**
//                     * 需要判断是否属于退全额（退款金额=金额），如果为退全额则不传wing不传DRP；
//                     */
//                    List<OcBReturnAfSendItem> itemList = map.get(order.getId());
//                    if (CollectionUtils.isEmpty(itemList)){
//                        continue;
//                    }
//                    for (OcBReturnAfSendItem item : itemList){
//                        OcBOrderItem ocBOrderItem = ocBOrderItemMapper.selectById(item.getRelationBillItemId());
//                        if (ocBOrderItem == null){
//                            flag = false;
//                        }
//                        if (ocBOrderItem.getRealAmt().compareTo(item.getAmtReturn()) == 0){
//                            flag = false;
//                        }
//                    }
//                    if (!flag){
//                       continue;
//                    }
//                    WingIncidentalsOrderCreateModel createModel = this.transferFeild(order);
//                    v14 = ipRpcService.addIncidentals(createModel);
//                } catch (Exception e) {
//                    fail++;
//                    log.error(LogUtil.format("当前数据生成杂费单异常：{}",  "发货后仅退款单传wing服务"), Throwables.getStackTraceAsString(e));
//                }
//                if (v14 != null) {
//                    OcBReturnAfSend update = new OcBReturnAfSend();
//                    update.setId(order.getId());
//                    if (v14.isOK()) {
//                        try {
//                            WingReturnResult returnResult = (WingReturnResult) v14.getData();
//                            if (returnResult.getStatus() == 1) {
//                                update.setToDrpStatus(ToDRPStatusEnum.FAIL.getCode());
//                                List<WingReturnResult.FailMsg> faileds = returnResult.getFaileds();
//                                if (CollectionUtils.isNotEmpty(faileds)) {
//                                    String message = SplitMessageUtil.splitErrMsgBySize(faileds.get(0).getErrMsg(), SplitMessageUtil.SIZE_200);
//                                    update.setToDrpFailedReason(message);
//                                    int count = order.getToDrpCount() == null ? 0 : order.getToDrpCount();
//                                    update.setToDrpCount(count + 1);
//                                } else {
//                                    update.setToDrpStatus(ToDRPStatusEnum.SUCCESS.getCode());
//                                    update.setToDrpFailedReason("");
//                                    update.setToDrpCount(0);
//                                }
//                            } else {
//                                update.setToDrpStatus(ToDRPStatusEnum.FAIL.getCode());
//                                update.setToDrpFailedReason(returnResult.getMsg());
//                                int count = order.getToDrpCount() == null ? 0 : order.getToDrpCount();
//                                update.setToDrpCount(count + 1);
//                            }
//
//                        } catch (Exception e) {
//                            update.setToDrpStatus(ToDRPStatusEnum.FAIL.getCode());
//                            update.setToDrpFailedReason("解析转换传wing结果异常");
//                            int count = order.getToDrpCount() == null ? 0 : order.getToDrpCount();
//                            update.setToDrpCount(count + 1);
//                            log.error(LogUtil.format("解析转换传wing结果异常：{}",  "发货后仅退款单传wing服务"), Throwables.getStackTraceAsString(e));
//                        }
//                    } else {
//                        update.setToDrpStatus(ToDRPStatusEnum.FAIL.getCode());
//                        update.setToDrpFailedReason(v14.getMessage());
//                        int count = order.getToDrpCount() == null ? 0 : order.getToDrpCount();
//                        update.setToDrpCount(count + 1);
//                    }
//                    ocBOrderMapper.updateById(update);
//                }
//            }
//            result.setSuccess(true);
//
//            long endTime = System.currentTimeMillis();
//            long Time = endTime - starTime;
//            String msg = "执行成功条数：" + (orderList.size() - fail) + ",失败条数：" + fail +"耗时:"+ Time;
//            result.setMessage(msg);
//        } catch (Exception e) {
//            log.error(LogUtil.format("AutoReturnAfSendOrderToWingTask.Execute Error:{}",  "发货后仅退款单传wing服务"), Throwables.getStackTraceAsString(e));
//            result.setSuccess(false);
//            result.setMessage(e.getMessage());
//        }
//        return result;
//    }
//
//    private WingIncidentalsOrderCreateModel transferFeild(OcBReturnAfSend order) {
//        WingIncidentalsOrderCreateModel createModel = new WingIncidentalsOrderCreateModel();
//        //("中台订单号") id;
//        createModel.setId(order.getBillNo());
//        //("原始中台订单号") original_id;
//        createModel.setOriginal_id(order.getBillNo());
//        //("原始平台单号") original_tid;
//        createModel.setOriginal_tid(order.getTid());
//        //("下单店铺CODE") shopCode;
//        createModel.setShopCode(order.getCpCShopEcode());
//        //("下单店铺名称") shopName;
//        createModel.setShopName(order.getCpCShopTitle());
//        if(StringUtils.isEmpty(order.getCpCShopEcode())){
//            CpShop cpShop = cpRpcService.selectShopById(order.getCpCShopId());
//            if(cpShop!=null){
//                //("下单店铺CODE") shopCode;
//                createModel.setShopCode(cpShop.getEcode());
//                //("下单店铺名称") shopName;
//                createModel.setShopName(cpShop.getCpCShopTitle());
//            }
//        }
//        //("退货逻辑仓编号:默认都是做到 ECTZ1") warehouseCode;
//        createModel.setWarehouseCode(defaultStoreCode);
//        //("传杂费单退货原因列表ID") omsreasontypeid;
//        createModel.setOmsreasontypeid("1");
//        //("单据类型") incidentalsType;
//        createModel.setIncidentalsType("已发货退款单");
//        //("退款商品描述") refunddescription;
//        createModel.setRefunddescription("已发货退款单");
//        if (order.getOcBReturnTypeId() != null) {
//            OcBReturnType ocBReturnType = returnTypeMapper.selectById(order.getOcBReturnTypeId());
//            if (ocBReturnType != null) {
//                //("传杂费单退货原因列表ID") omsreasontypeid;
//                createModel.setOmsreasontypeid(ocBReturnType.getThirdPartyNo());
//                //("退款商品描述") refunddescription;
//                createModel.setRefunddescription(ocBReturnType.getEname());
//            }
//        }
//        //("支付方式:传支付类型列表ID") paytype;
//        createModel.setPaytype("2");
//        //("付款时间(原始订单付款时间)") paytime;
//        Date payTime = order.getReturnPaymentTime() == null ? new Date() : order.getReturnPaymentTime();
//        createModel.setPaytime(DateUtil.format(payTime, DateUtil.dateTimeSecondsFormatter.getPattern()));
//        //("退款日期(打款时间):yyyy-mm-dd hh24:mm:ss") refunddate;
//        Date returnTime = order.getReturnPaymentTime() == null ? new Date() : order.getReturnPaymentTime();
//        createModel.setRefunddate(DateUtil.format(returnTime, DateUtil.dateTimeSecondsFormatter.getPattern()));
//        //创建时间
//        Date creationDate = order.getCreationdate() == null ? new Date() : order.getCreationdate();
//        createModel.setCreationDate(DateUtil.format(creationDate, DateUtil.dateTimeSecondsFormatter.getPattern()));
//        //("退商品金额: 正数斯凯奇应付，负数斯凯奇应收") refundgoodsamount;
//        createModel.setRefundgoodsamount(order.getAmtReturnActual() == null ? "0" : order.getAmtReturnActual().toString());
//        //("备注") remark;
//        createModel.setRemark(order.getRemark());
//        return createModel;
//    }
//}
//
