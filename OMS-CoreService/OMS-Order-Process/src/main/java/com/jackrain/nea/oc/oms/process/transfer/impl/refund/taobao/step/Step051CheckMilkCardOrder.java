package com.jackrain.nea.oc.oms.process.transfer.impl.refund.taobao.step;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransNodeTipEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsTaobaoRefundRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.process.transfer.impl.refund.taobao.util.OrderStatusUtil;
import com.jackrain.nea.oc.oms.refund.util.TransRefundNodeTipUtil;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2020/3/8 12:57 下午
 * @Version 1.0
 */
@Step(order = 55, description = "判断订单是否为奶卡订单")
@Slf4j
@Component
public class Step051CheckMilkCardOrder extends BaseTaobaoRefundProcessStep
        implements IOmsOrderProcessStep<OmsTaobaoRefundRelation> {
    @Override
    public ProcessStepResult<OmsTaobaoRefundRelation> startProcess(OmsTaobaoRefundRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        List<OmsOrderRelation> omsOrderRelations = orderInfo.getOmsOrderRelation();
        IpBTaobaoRefund ipBTaobaoRefund = orderInfo.getIpBTaobaoRefund();
        OcBOrder ocBOrder = omsOrderRelations.get(0).getOcBOrder();
        //按子订单维度创建发货后退款单（仅退款）
        List<OcBOrderItem> orderItems = new ArrayList<>();
        List<OcBOrder> ocBOrderList = new ArrayList<>();
        for (OmsOrderRelation orderRelation : omsOrderRelations) {
            boolean andVoid = OrderStatusUtil.checkOrderIsCancelAndVoid(orderRelation.getOcBOrder().getOrderStatus());
            if (andVoid) {
                continue;
            }
            orderItems.addAll(orderRelation.getOcBOrderItems());
            ocBOrderList.add(orderRelation.getOcBOrder());
        }

        //如果订单全都是奶卡类商品，则只生成已发货退款单
        boolean isMilkCardOrder = false;
        try{
            isMilkCardOrder = omsRefundOrderService.checkIsMilkCardOrderNew(ocBOrderList);
        }catch (Exception e){
            log.info(LogUtil.format("查询是否为奶卡订单失败={}",
                    "Step051CheckMilkCardOrder"), Throwables.getStackTraceAsString(e));
            ipTaobaoRefundService.updateRefundIsTransError(ipBTaobaoRefund, e.getMessage());
            String errorMessage = "退单转换异常!" + e.getMessage();
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
        //判断订单是否为虚拟订单
        if (!isMilkCardOrder) {
            return new ProcessStepResult<>(StepStatus.SUCCESS, "不为奶卡订单,继续下一阶段转换");
        }

        //根据退款单号查询退款
        omsRefundOrderService.foundRefundSlipAfterRefundOnly(orderItems, ocBOrder, ipBTaobaoRefund, operateUser);
        omsReturnOrderService.giftsThenSend(omsOrderRelations,orderInfo.getIsGiftOrderRelation(),orderInfo.getIntermediateTableRelation(),operateUser);
        String remark = "生成发货后退款单(仅退款)成功";
        TransRefundNodeTipUtil.taoBaoTransTipCAS(ipBTaobaoRefund, TransNodeTipEnum.DEFAULT);
        ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                remark, ipBTaobaoRefund);
        return new ProcessStepResult<>(StepStatus.FINISHED, remark);
    }
}
