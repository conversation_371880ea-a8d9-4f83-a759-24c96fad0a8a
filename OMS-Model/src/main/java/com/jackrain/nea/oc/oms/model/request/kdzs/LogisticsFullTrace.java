package com.jackrain.nea.oc.oms.model.request.kdzs;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * 物流轨迹信息类
 *
 * <AUTHOR>
 * @date 2022年04月01日 20:12
 */
@Data
@ToString
public class LogisticsFullTrace implements Serializable {

    /**
     * 非必须，路由节点所在地区行政编码
     */
    private String areaCode;
    /**
     * 非必须，路由节点所在地区
     */
    private String areaName;
    /**
     * 物流子状态
     */
    private String subLogisticsStatus;
    /**
     * 物流状态
     */
    private String logisticsStatus;
    /**
     * 物流变更时间
     */
    private Long time;
    /**
     * 物流路由信息描述内容
     */
    private String desc;


}
