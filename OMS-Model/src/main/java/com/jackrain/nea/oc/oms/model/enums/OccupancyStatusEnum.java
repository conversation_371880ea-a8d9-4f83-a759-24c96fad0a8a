package com.jackrain.nea.oc.oms.model.enums;


import java.util.Objects;

/**
 * <AUTHOR> ruan.gz
 * @Description : 换货占单状态
 * @Date : 2020/7/4
 **/
public enum OccupancyStatusEnum {

    INIT("初始化", 0),
    STOCK_OUT("缺货", 1),
    SUCCESS("占单成功", 2);


    String key;
    Integer val;

    OccupancyStatusEnum(String k, Integer v) {
        this.key = k;
        this.val = v;
    }

    public String getKey() {
        return key;
    }

    public Integer getVal() {
        return val;
    }

    public static OccupancyStatusEnum getFromValue(Integer value) {
        OccupancyStatusEnum[] values = OccupancyStatusEnum.values();
        for (OccupancyStatusEnum statusEnum : values) {
            if (Objects.equals(statusEnum.val, value)) {
                return statusEnum;
            }
        }
        throw new IllegalArgumentException(String.valueOf(value));
    }
}


