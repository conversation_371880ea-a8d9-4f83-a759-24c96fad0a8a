package com.jackrain.nea.oc.oms.process.transfer.impl.normal.jingdong.step;

import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpJingdongOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongOrder;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongUser;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.services.IpJingdongOrderService;
import com.jackrain.nea.oc.oms.services.OmsTransferModifyAddrService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.oc.oms.util.JDAddressMsgUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 更新京东中间表的收货地址
 *
 * <AUTHOR>
 */
@Step(order = 43, description = "京东订单地址修改")
@Slf4j
@Component
public class Step43UpdateAddress extends BaseJingdongOrderProcessStep implements IOmsOrderProcessStep<IpJingdongOrderRelation> {

    @Autowired
    private OmsTransferModifyAddrService omsTransferModifyAddrService;

    @Autowired
    private IpJingdongOrderService ipJingdongOrderService;

    @Override
    public ProcessStepResult<IpJingdongOrderRelation> startProcess(IpJingdongOrderRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {

        log.info("Step43UpdateAddress京东订单地址修改入口");

        ProcessStepResult<IpJingdongOrderRelation> stepResult = new ProcessStepResult<>(StepStatus.FINISHED, "京东转单修改地址");
        IpBJingdongOrder jingdongOrder = orderInfo.getJingdongOrder();
        IpBJingdongUser jingdongUser = orderInfo.getJingdongUser();

        List<OcBOrder> ocBOrderList = orderInfo.getOcBOrderList();
        if (CollectionUtils.isNotEmpty(ocBOrderList)) {
            ocBOrderList = ocBOrderList.stream()
                    .filter(o -> !OmsOrderStatus.SYS_VOID.toInteger().equals(o.getOrderStatus()))
                    .collect(Collectors.toList());

            boolean isModify = false;
            String jingdongUserKey = jingdongUser.getFullname() + jingdongUser.getMobile() + jingdongUser.getFullAddress();
            // 判断是否要改收货地址信息
            for (OcBOrder ocBOrder : ocBOrderList) {
                String ocBOrderKey = ocBOrder.getReceiverName() + ocBOrder.getReceiverMobile() + ocBOrder.getReceiverAddress();
                log.info("京东中间表地址判断key：{},零售单表地址判断key:{}", jingdongUserKey, ocBOrderKey);
                if (!jingdongUserKey.equals(ocBOrderKey)) {
                    isModify = true;
                    break;
                }
            }

            if (isModify) {
                for (OcBOrder ocBOrder : ocBOrderList) {
                    Integer orderStatus = ocBOrder.getOrderStatus();
                    if (Objects.nonNull(orderStatus) &&
                            !OmsOrderStatus.UNCONFIRMED.toInteger().equals(orderStatus) &&
                            !OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(orderStatus)) {
                        // 只有卡单、待寻源、待审核允许修改发货信息
                        ipJingdongOrderService.updateIpJingdongExchangeOrderInfo(TransferOrderStatus.TRANSFERRED.toInteger(), jingdongOrder.getOrderId(), "修改地址失败", null);
                        String msg = "店铺: " + ocBOrder.getCpCShopTitle() + "   " + "平台单号: " + jingdongOrder.getOrderId() + "   " + "原因: 订单地址修改失败";
                        JDAddressMsgUtil.sendDingTalk(msg);
                        stepResult.setMessage("存在拆单后已审核状态订单");
                        return stepResult;
                    }
                }

                try {
                    String s = omsTransferModifyAddrService.transferModifyAddrService(ocBOrderList, orderInfo, operateUser, "jingdong");
                    if (StringUtils.isNotEmpty(s)) {
                        ipJingdongOrderService.updateIpJingdongExchangeOrderInfo(TransferOrderStatus.TRANSFERRED.toInteger(), jingdongOrder.getOrderId(), s, null);
                        return stepResult;
                    }
                }
                catch (Exception ex) {
                    stepResult.setMessage("更新京东收货地址异常");
                    ex.printStackTrace();
                    log.error(LogUtil.format("Step43UpdateAddress:{}", "Step43UpdateAddress"), Throwables.getStackTraceAsString(ex));
                }
            }
        }
        else {
            stepResult.setMessage("没有更新京东收货地址");
        }
        return stepResult;
    }
}
