package com.jackrain.nea.oc.oms.model.result;

import com.jackrain.nea.oc.oms.model.table.OcBRefundInExt;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInProductItemExtend;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @author: 夏继超
 * @since: 2019/6/6
 * create at : 2019/6/6 15:52
 */
@Data
public class OcRefundExportResult implements Serializable {
    List<OcBRefundInExt> ocBRefundIns;
    List<OcBRefundInProductItemExtend> productItems;
}
