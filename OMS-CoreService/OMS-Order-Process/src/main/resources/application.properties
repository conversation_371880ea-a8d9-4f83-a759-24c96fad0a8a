# apollo.bootstrap.enabled=true\u8868\u793AApollo\u5728\u7A0B\u5E8F\u521D\u59CB\u5316\u7684\u65F6\u5019\u5C31\u5F00\u59CB\u6CE8\u5165\uFF0C\u5E76\u4E0B\u8F7D\u5BF9\u5E94\u7684\u914D\u7F6E\u4FE1\u606F\u3002
# \u5982\u679C\u8BBE\u7F6E\u4E3Afalse\u6216\u8005\u53D6\u6D88\u4F1A\u5BFC\u81F4\u67D0\u4E9B\u914D\u7F6E\u9519\u8BEF
#apollo.bootstrap.enabled=true
# apollo.bootstrap.namespace \u8868\u793A\u52A0\u8F7DApollo\u7684namespace\u7684\u5185\u5BB9\u3002\u91C7\u7528\u82F1\u6587,\u8FDE\u63A5\u8868\u793A\u591A\u4E2Anamespace
#apollo.bootstrap.namespaces=application,database,redis,local_dubbo,elasticsearch,slaverrds,mq,lts
# \u591A\u8BED\u8A00\u9ED8\u8BA4\u8BBE\u7F6E\u3002\u5728Apollo\u4E2D\u914D\u7F6E\u7684\u65E0\u6CD5\u751F\u6548\uFF0C\u53EA\u80FD\u5728application.properties\u914D\u7F6E
spring.locale.default=zh_CN
server.port=7080
spring.profiles.active=dev
tlog.pattern=[$preHost][$traceId][$spanId][$userId]
##r3 OMS \u8BA2\u5355\u6D88\u606FMQ\u914D\u7F6E\u4FE1\u606F
##Consumer MQ \u76D1\u542C\u662F\u5426\u542F\u52A8
#r3.oc.oms.process.mq.consumer.start=true
##Consumer MQ \u6D88\u8D39\u62E6\u622A\u5668
#r3.oc.oms.process.mq.consumer.logIntercept=
##Consumer ID
#r3.oc.oms.process.mq.consumer.groupid=GID_BJ_DEV_R3_OC_OMS_CALL_TRANSFER
#r3.oc.oms.process.mq.consumer.serveraddress=http://onsaddr.mq-internet-access.mq-internet.aliyuncs.com:80
##\u7528\u6237\u767B\u9646\u540D\u79F0 <EMAIL>
##AccessKey ID LTAIVCzaw18BMSzc
##AccessKeySecret 43w0j5NKzhIExV99Znv5seGKqcWpLx
##Consumer accesskey
#r3.oc.oms.process.mq.consumer.accesskey=LTAIt8G0CKp0kwoa
##Consumer secretkey
#r3.oc.oms.process.mq.consumer.secretkey=CcDHxlYrvl4bwYKFIgA0ACew7OfdxA
##Consumer \u7EBF\u7A0B\u6570\u91CF
#r3.oc.oms.process.mq.consumer.threadnum=50
##Consumer \u6D88\u8D39\u8D85\u65F6\u65F6\u95F4\u3002\u5355\u4F4D\uFF1A\u79D2
#r3.oc.oms.process.mq.consumer.timeout=5
#r3.oc.oms.process.mq.consumer.index=1
#r3.oc.oms.process.mq.consumer.listener1=com.jackrain.nea.oc.oms.mq.OrderMessageConsumerListener
#r3.oc.oms.process.mq.consumer.topic1=BJ_DEV_R3_OC_OMS_CALL_TRANSFER
##Consumer Tag\u8FC7\u6EE4\u5668
#r3.oc.oms.process.mq.consumer.expr1=OperateMqOrder||OperateTobeConfirmed
##Producer MQ\u662F\u5426\u542F\u52A8
#r3.oc.oms.process.mq.producer.start=true
##Producer MQ\u53D1\u9001\u8D85\u65F6\u65F6\u95F4
#r3.oc.oms.process.mq.producer.sendmsgtimeout=3000
##Producer accesskey
#r3.oc.oms.process.mq.producer.accesskey=LTAIt8G0CKp0kwoa
##Producer secretkey
#r3.oc.oms.process.mq.producer.secretkey=CcDHxlYrvl4bwYKFIgA0ACew7OfdxA
#r3.oc.oms.process.mq.producer.topic=BJ_DEV_R3_OC_OMS_CALL_TRANSFER
##Producer \u53D1\u9001Tag\u5185\u5BB9
#r3.oc.oms.process.mq.producer.sendtag=OperateTobeConfirmed
#
#r3.oc.oms.process.mq.producer.groupid=GID_DEV_R3_OC_OMS_CALL_TRANSFER
#r3.oc.oms.process.mq.producer.serveraddress=http://onsaddr.mq-internet-access.mq-internet.aliyuncs.com:80