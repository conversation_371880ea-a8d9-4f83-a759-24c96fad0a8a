package com.jackrain.nea.oc.oms.model.enums;

/**
 * 订单类型(操作费方案类型)
 *
 * @author: 胡林洋
 * @since: 2019-04-08
 * create at : 2019-04-08 21:28
 */
public enum OmsOperatingCostPlanType {

    /**
     * B2B订单处理
     */
    B2B_ORDER_HANDLE,
    /**
     * B2B退货处理
     */
    B2B_ORDER_REFUNDS,
    /**
     * B2C订单处理
     */
    B2C_ORDER_HANDLE,
    /**
     * B2C退货处理
     */
    B2C_ORDER_REFUNDS,
    /**
     * 京东闪购订单
     */
    JINGDONG_FLASH_PURCHASE_ORDER,
    /**
     * 唯品会JIT订单
     */
    VIP_ORDER_JITX;

    /**
     * 1，B2B订单处理, 2，B2B退货处理 3, B2C订单处理 4, B2C退货处理 5,  京东闪购订单 , 6 唯品会JIT订单
     */
    public int toInteger() {
        if (this == OmsOperatingCostPlanType.B2B_ORDER_HANDLE) {
            return 1;
        } else if (this == OmsOperatingCostPlanType.B2B_ORDER_REFUNDS) {
            return 2;
        } else if (this == OmsOperatingCostPlanType.B2C_ORDER_HANDLE) {
            return 3;
        } else if (this == OmsOperatingCostPlanType.B2C_ORDER_REFUNDS) {
            return 4;
        } else if (this == OmsOperatingCostPlanType.JINGDONG_FLASH_PURCHASE_ORDER) {
            return 5;
        } else if (this == OmsOperatingCostPlanType.VIP_ORDER_JITX) {
            return 6;
        } else {
            return 0;
        }
    }

}
