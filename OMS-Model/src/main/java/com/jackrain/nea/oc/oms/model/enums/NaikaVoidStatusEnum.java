package com.jackrain.nea.oc.oms.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @ClassName NaikaVoidStatusEnum
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/3/3 16:39
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum NaikaVoidStatusEnum {

    VOID(0, "待作废"),
    VOID_SUCCESS(1, "作废成功"),
    VOID_FAIL(2, "作废失败"),
    ;

    private Integer status;

    private String desc;
}
