package com.jackrain.nea.oc.oms.gsi;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.util.ApplicationContextHandle;

import java.util.List;

/**
 * @Desc : 零售发货单
 * <AUTHOR> xiWen
 * @Date : 2020/11/27
 */
public class GSI4Order {


    private static OcBOrderMapper ocBOrderMapper;


    /**
     * @param sourceCode 平台
     * @return 零售发货单编号集
     */
    public static List<Long> getIdListBySourceCode(String sourceCode) {
        return orderMapper().listIdFromGsiBySourceCode(sourceCode);
    }

    /**
     * @param sourceCodes 平台
     * @return 零售发货单编号集
     */
    public static List<Long> getIdListBySourceCodes(List<String> sourceCodes) {
        return orderMapper().listIdFromGsiBySourceCodes(sourceCodes);
    }

    /**
     * @param sourceCodes 平台
     * @return 平台单号集合
     */
    public static List<String> getSourceCodeListBySourceCodes(List<String> sourceCodes) {
        return orderMapper().listSourceCodeFromGsiBySourceCodes(sourceCodes);
    }


    /**
     * @param sourceCode 平台单号
     * @return 订单信息集合
     */
    public static List<OcBOrder> getOrderListBySourceCode(String sourceCode) {
        return orderMapper().getOrdersUnionGsiBySourceCode(sourceCode);
    }

    /**
     * 根据平台单号获取非已取消、已作废的单据信息
     *
     * @param sourceCode 平台单号
     * @return
     */
    public static List<OcBOrder> getValidOrderListBySourceCode(String sourceCode) {
        return orderMapper().getValidOrdersUnionGsiBySourceCode(sourceCode);
    }

    /**
     * 审核.校验重单
     *
     * @param sourceCode 平台单号
     * @return 零售发货单编号集
     */
    public static List<Long> listIdsBySourceCode4AuditOrder(String sourceCode, Long id) {
        return orderMapper().listIdsBySourceCode4Audit(sourceCode, id);
    }

    /**
     * 审核.校验重单
     *
     * @param sourceCode 平台单号
     * @return 零售发货单编号集
     */
    public static List<Long> listIdsBySourceCodeSuffixInfo4Audit(String sourceCode, Long id, String suffixInfo) {
        return orderMapper().listIdsBySourceCodeSuffixInfo4Audit(sourceCode, id, suffixInfo);
    }

    /**
     * @return OcBReturnOrderMapper
     */
    private static OcBOrderMapper orderMapper() {

        if (ocBOrderMapper == null) {
            ocBOrderMapper = ApplicationContextHandle.getBean(OcBOrderMapper.class);
            if (ocBOrderMapper == null) {
                throw new NDSException("OcBOrderMapper Not Found In Class GSI4Order");
            }
        }
        return ocBOrderMapper;
    }

    private GSI4Order() {
    }


}
