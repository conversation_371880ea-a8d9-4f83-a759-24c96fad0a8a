package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBOrderSearchWareHouseCmd;
import com.jackrain.nea.oc.oms.model.request.OrderSerarchCheckRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * 缺货重新寻仓
 *
 * @author: heliu
 * @since: 2019/4/2
 * create at : 2019/4/2 11:14
 */
@Api(value = "OcBorderSearchWareHouseOrderCtrl", tags = "缺货重新寻仓")
@Slf4j
@RestController
public class OcBorderSearchWareHouseOrderCtrl {


    @Autowired
    private OcBOrderSearchWareHouseCmd orderSearchWareHouseCmd;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    /**
     * 缺货重新寻仓
     *
     * @param request 请求对象
     * @param obj     请求参数
     * @return json对象
     */
    @ApiOperation(value = "缺货重新寻仓")
    @RequestMapping(value = "/api/cs/oc/oms/v1/queryshortagSearchOrder", method = RequestMethod.POST)
    public JSONObject queryshortagSearchOrder(HttpServletRequest request,
                                              @RequestBody JSONObject obj) {
        ValueHolderV14 vh = new ValueHolderV14();
        //获取当前登陆用户
        //User user = OcBorderAuditCtrl.getRootUser();
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        //获取用户登录信息
        if (obj == null) {
            throw new NDSException(Resources.getMessage("请求参数不能为空!", user.getLocale()));
        }

        OrderSerarchCheckRequest orderSerarchCheckRequest = new OrderSerarchCheckRequest();
        JSONArray jsonArray = obj.getJSONArray("ids");
        Long[] ids = new Long[jsonArray.size()];
        for (int i = 0; i < jsonArray.size(); i++) {
            ids[i] = jsonArray.getLong(i);
        }
        orderSerarchCheckRequest.setIds(ids);
        try {
            vh = orderSearchWareHouseCmd.queryOrderSearchWareHouse(orderSerarchCheckRequest, user);
            vh.setCode(vh.getCode());
            vh.setMessage(Resources.getMessage(vh.getMessage()));
            return vh.toJSONObject();
        } catch (NDSException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return vh.toJSONObject();
        }
    }

    /**
     * 福袋缺货重新寻仓
     *
     * @param request 请求对象
     * @param obj     请求参数
     * @return json对象
     */
    @ApiOperation(value = "福袋缺货重新寻仓")
    @RequestMapping(value = "/api/cs/oc/oms/v1/queryFortuneBagShortage", method = RequestMethod.POST)
    public JSONObject queryFortuneBagShortage(HttpServletRequest request, @RequestBody JSONObject obj) {

        ValueHolderV14 vh = new ValueHolderV14();
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        if (obj == null) {
            throw new NDSException(Resources.getMessage("请求参数不能为空!", user.getLocale()));
        }

        OrderSerarchCheckRequest orderSerarchCheckRequest = new OrderSerarchCheckRequest();
        JSONArray jsonArray = obj.getJSONArray("ids");
        Long[] ids = new Long[jsonArray.size()];
        for (int i = 0; i < jsonArray.size(); i++) {
            ids[i] = jsonArray.getLong(i);
        }
        orderSerarchCheckRequest.setIds(ids);
        orderSerarchCheckRequest.setIsFortuneBag(true);
        try {
            vh = orderSearchWareHouseCmd.queryOrderSearchWareHouse(orderSerarchCheckRequest, user);
            vh.setCode(vh.getCode());
            vh.setMessage(Resources.getMessage(vh.getMessage()));
            return vh.toJSONObject();
        } catch (NDSException e) {
            vh = new ValueHolderV14();
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return vh.toJSONObject();
        }
    }

}