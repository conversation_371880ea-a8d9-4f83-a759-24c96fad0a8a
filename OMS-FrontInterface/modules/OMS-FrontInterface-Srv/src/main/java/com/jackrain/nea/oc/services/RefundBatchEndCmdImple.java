package com.jackrain.nea.oc.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.RefundBatchEndCmd;
import com.jackrain.nea.oc.oms.services.RefundBatchEndService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: 孙继东
 * @since: 2019-03-26
 * create at : 2019-03-26 20:13
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class RefundBatchEndCmdImple extends CommandAdapter implements RefundBatchEndCmd {
    @Autowired
    private RefundBatchEndService refundBatchEndService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return refundBatchEndService.execute(session);
    }
}
