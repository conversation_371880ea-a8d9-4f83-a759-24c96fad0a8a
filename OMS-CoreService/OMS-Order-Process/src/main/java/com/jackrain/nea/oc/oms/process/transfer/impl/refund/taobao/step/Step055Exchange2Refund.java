package com.jackrain.nea.oc.oms.process.transfer.impl.refund.taobao.step;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.*;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsTaobaoRefundRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Step(order = 55, description = "换转退处理")
@Slf4j
@Component
public class Step055Exchange2Refund extends BaseTaobaoRefundProcessStep
        implements IOmsOrderProcessStep<OmsTaobaoRefundRelation> {
    @Override
    public ProcessStepResult<OmsTaobaoRefundRelation> startProcess(OmsTaobaoRefundRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        List<OmsOrderRelation> omsOrderRelations = orderInfo.getOmsOrderRelation();
        IpBTaobaoRefund ipBTaobaoRefund = orderInfo.getIpBTaobaoRefund();

        List<Long> orderIdList = new ArrayList<>();
        for(OmsOrderRelation relation:omsOrderRelations){
            orderIdList.add(relation.getOcBOrder().getId());
        }

        List<OcBReturnOrder> returnOrderList = ocBReturnOrderMapper.getReturnOrderUnionGsiByOrigSourceCode(String.valueOf(ipBTaobaoRefund.getTid()));
        returnOrderList = returnOrderList.stream().filter(x -> orderIdList.contains(x.getOrigOrderId()) && OcReturnBillTypeEnum.EXCHANGE.getVal().equals(x.getBillType())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(returnOrderList)){
            return new ProcessStepResult<>(StepStatus.SUCCESS, "非换转退,继续下一阶段转换");
        }
        Map<Long, List<OcBReturnOrder>> returnOrderMap = returnOrderList.stream().collect(Collectors.groupingBy(OcBReturnOrder::getOrigOrderId));
        List<OcBOrder> orderList = ocBOrderMapper.getOrdersUnionGsiBySourceCode(String.valueOf(ipBTaobaoRefund.getTid()));
        orderList = orderList.stream().filter(x -> x.getOrigOrderId() != null && x.getOrigReturnOrderId() != null).collect(Collectors.toList());
        Map<Long, List<OcBOrder>> orderMap = orderList.stream().collect(Collectors.groupingBy(OcBOrder::getOrigReturnOrderId));

        try{
            for(OmsOrderRelation omsOrderRelation:omsOrderRelations){
                OcBOrder ocBOrder = omsOrderRelation.getOcBOrder();
                List<OcBReturnOrder> ocBReturnOrders = returnOrderMap.get(ocBOrder.getId());
                if(CollectionUtils.isEmpty(ocBReturnOrders)){
                    continue;
                }
                OcBReturnOrder ocBReturnOrder = ocBReturnOrders.get(0);
                List<OcBOrder> orders = orderMap.get(ocBReturnOrder.getId());
                if(CollectionUtils.isEmpty(orders)){
                    continue;
                }
                OcBOrder ocBOrderExchange = orders.get(0);
                if( OrderTypeEnum.EXCHANGE.getVal().equals(ocBOrderExchange.getOrderType())){
                    //取消换货的零售发货单，退换货单执行换转退
                    Integer orderStatus = ocBOrderExchange.getOrderStatus();
                    if(OmsOrderStatus.TO_BE_ASSIGNED.toInteger().equals(orderStatus) || OmsOrderStatus.UNCONFIRMED.toInteger().equals(orderStatus) || OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(orderStatus)){
                        boolean cancelOrder = ocBOrderOffService.startCancelOrderByLock(operateUser,ocBOrderExchange.getId(), OrderLogTypeEnum.ORDER_CANCLE.getKey(),"订单取消成功");
                        if(!cancelOrder){
                            throw new NDSException(Resources.getMessage("取消换货订单失败！", operateUser.getLocale()));
                        }
                    }else if(OmsOrderStatus.CHECKED.toInteger().equals(orderStatus) || OmsOrderStatus.IN_DISTRIBUTION.toInteger().equals(orderStatus)){
                        boolean isAuditSuccess = ocBOrderTheAuditService.updateOrderInfo(operateUser,new ValueHolderV14(),ocBOrderExchange.getId(),false,1L,true);
                        if(!isAuditSuccess){
                            throw new NDSException("换货订单反审核失败！");
                        }
                        if(isAuditSuccess){
                            boolean isCancel = ocBOrderOffService.startCancelOrderByLock(operateUser, ocBOrderExchange.getId(), OrderLogTypeEnum.ORDER_CANCLE.getKey(), "订单取消成功");
                            if(!isCancel){
                                throw new NDSException("取消换货订单失败！");
                            }
                        }
                    }
                    JSONObject obj = new JSONObject();
                    Long[] idArr = {ocBReturnOrder.getId()};
                    obj.put("ids",idArr);
                    ValueHolderV14 v14 = exchange2RefundService.exchange2Refund(obj,operateUser);
                    if(!v14.isOK()){
                        throw new NDSException(Resources.getMessage("更新退换货单类型失败！", operateUser.getLocale()));
                    }
                }
            }
            ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                    "换转退转换成功", ipBTaobaoRefund);
            return new ProcessStepResult<>(StepStatus.FINISHED, "换转退转换成功");
        }catch (Exception e){
            log.error(LogUtil.format("换转退失败={}",
                    "Step055Exchange2Refund"), Throwables.getStackTraceAsString(e));
            ipTaobaoRefundService.updateRefundIsTransError(ipBTaobaoRefund, e.getMessage());
            String errorMessage = "退单转换异常!" + e.getMessage();
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
    }
}
