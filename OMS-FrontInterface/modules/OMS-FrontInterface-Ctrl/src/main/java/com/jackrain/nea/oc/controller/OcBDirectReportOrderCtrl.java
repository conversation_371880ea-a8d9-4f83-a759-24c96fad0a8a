package com.jackrain.nea.oc.controller;

import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.share.model.result.out.SgBShareOutQueryResult;
import com.burgeon.r3.sg.store.model.result.out.SgBStoOutQueryRes;
import com.google.common.base.Throwables;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.model.request.OcBDirectReportTransRequest;
import com.jackrain.nea.oc.oms.services.directreport.OcBDirectReportOrderTransStorageService;
import com.jackrain.nea.rpc.SgNewRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolderV14Utils;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

/**
 * 直发预占单
 *
 * <AUTHOR>
 * @since 2024-12-02 16:50
 */
@Slf4j
@RestController
public class OcBDirectReportOrderCtrl {
    @Resource
    private SgNewRpcService sgNewRpcService;
    @Resource
    private OcBDirectReportOrderTransStorageService ocBDirectReportOrderTransStorageService;
    @Resource
    private R3PrimWebAuthService r3PrimWebAuthService;

    @RequestMapping(value = "/api/cs/oc/oms/v1/OcBDirectReportOrder/qeuryShareOut", method = RequestMethod.GET)
    public ValueHolderV14<SgBShareOutQueryResult> qeuryShareOut(@RequestParam(value = "id") Long id) {
        if (Objects.isNull(id)) {
            return ValueHolderV14Utils.getFailValueHolder("ID不能为空");
        }

        SgBShareOutQueryResult data;
        try {
            data = sgNewRpcService.queryShareOut(SgConstantsIF.BILL_TYPE_DIRECT_ORDER, id, null);
        } catch (Exception e) {
            log.warn(LogUtil.format("查询配销占用失败，入参:{}，异常:{}", "SgNewRpcService.queryShareOut"),
                    id, Throwables.getStackTraceAsString(e));
            return ValueHolderV14Utils.getFailValueHolder("查询配销占用失败：" + e.getMessage());
        }
        return new ValueHolderV14<>(data, ResultCode.SUCCESS, "查询成功");
    }

    @RequestMapping(value = "/api/cs/oc/oms/v1/OcBDirectReportOrder/queryStoOut", method = RequestMethod.GET)
    public ValueHolderV14<SgBStoOutQueryRes> queryStoOut(@RequestParam(value = "id") Long id) {
        if (Objects.isNull(id)) {
            return ValueHolderV14Utils.getFailValueHolder("ID不能为空");
        }

        SgBStoOutQueryRes data;
        try {
            data = sgNewRpcService.queryStoOut(SgConstantsIF.BILL_TYPE_DIRECT_ORDER, id, null);
        } catch (Exception e) {
            log.warn(LogUtil.format("查询逻辑占用失败，入参:{}，异常:{}", "SgNewRpcService.queryStoOut"),
                    id, Throwables.getStackTraceAsString(e));
            return ValueHolderV14Utils.getFailValueHolder("查询逻辑占用失败：" + e.getMessage());
        }
        return new ValueHolderV14<>(data, ResultCode.SUCCESS, "查询成功");
    }

    @RequestMapping(value = "/api/cs/oc/oms/v1/OcBDirectReportOrder/storageTrans", method = RequestMethod.POST)
    public ValueHolderV14<Void> storageTrans(HttpServletRequest httpServletRequest,
                                             @RequestBody OcBDirectReportTransRequest request) {
        ValueHolderV14<Void> v14 = new ValueHolderV14<>();
        if (request == null) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("请求参数不能为空！");
            return v14;
        }
        if (request.getId() == null) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("直发预占单id不能为空！");
            return v14;
        }
        if (StringUtils.isEmpty(request.getOrderNo())) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("订单单据编号不能为空！");
            return v14;
        }
        User user = r3PrimWebAuthService.getLoginPrimWebUser(httpServletRequest);
        return ocBDirectReportOrderTransStorageService.storageTrans(request, user);
    }
}
