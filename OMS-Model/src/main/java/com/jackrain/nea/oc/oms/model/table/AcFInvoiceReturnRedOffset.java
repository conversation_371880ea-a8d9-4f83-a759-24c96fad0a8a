package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName AcFInvoiceReturnRedOffset
 * @Description 退单待红冲发票表
 * @Date 2022/8/31 下午4:53
 * @Created by wuhang
 */
@TableName(value = "ac_f_invoice_return_red_offset")
@Data
public class AcFInvoiceReturnRedOffset extends BaseModel {
    private static final long serialVersionUID = -1L;
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /** 平台单号 */
    @JSONField(name = "TID")
    private String tid;

    /** 单据编号 零售发货单、退换货或发货后仅退款单据编号*/
    @JSONField(name = "BILL_NO")
    private String billNo;

    /** 单据类型,1退换货单,2零售发货单,3已发货仅退款单*/
    @JSONField(name = "BILL_TYPE")
    private String billType;

    /** 店铺id */
    @JSONField(name = "CP_C_SHOP_ID")
    private Long cpCShopId;

    /** 店铺标题 */
    @JSONField(name = "CP_C_SHOP_TITLE")
    private String cpCShopTitle;

    /** 店铺编码 */
    @JSONField(name = "CP_C_SHOP_ECODE")
    private String cpCShopEcode;

    /** 平台id */
    @JSONField(name = "CP_C_PLATFORM_ID")
    private Long cpCPlatformId;

    /** 平台名称 */
    @JSONField(name = "CP_C_PLATFORM_ENAME")
    private String cpCPlatformEname;

    /** 平台编码 */
    @JSONField(name = "CP_C_PLATFORM_ECODE")
    private String cpCPlatformEcode;

    /** 退货类型 0退货,1仅退款 */
    @JSONField(name = "RETURN_TYPE")
    private String returnType;

    /** 转换状态 0未转换,1已转换 */
    @JSONField(name = "CHANGE_STATUS")
    private String changeStatus;

    /** 转换人 */
    @JSONField(name = "CHANGE_USER")
    private Long changeUser;

    /** 转换时间 */
    @JSONField(name = "CHANGE_DATE")
    private Date changeDate;

    /** 退货数量 */
    @JSONField(name = "RETURN_COUNT")
    private String returnCount;

    /** 退款金额 */
    @JSONField(name = "REFUND_MONEY")
    private BigDecimal refundMoney;

    /** 退货时间 */
    @JSONField(name = "RETURN_DATE")
    private Date returnDate;

    /** 运费 */
    @JSONField(name = "FREIGHT_AMOUNT")
    private BigDecimal freightAmount;

    /** 系统备注 */
    @JSONField(name = "SYSTEM_REMARK")
    private String systemRemark;

    /** 整单退货标识 0部分,1整单*/
    @JSONField(name = "ALL_RETURN_FLAG")
    private String allReturnFlag;

    /** 失败原因 */
    @JSONField(name = "FAIL_REASON")
    private String failReason;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;
}
