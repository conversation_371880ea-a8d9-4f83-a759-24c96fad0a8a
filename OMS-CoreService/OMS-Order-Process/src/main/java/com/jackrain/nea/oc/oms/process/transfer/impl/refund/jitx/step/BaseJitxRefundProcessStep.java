package com.jackrain.nea.oc.oms.process.transfer.impl.refund.jitx.step;

import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.services.*;
import com.jackrain.nea.st.service.VipcomJitxWarehouseService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @Description JITX退单转单处理阶段抽象基类
 * @Date 2019-6-26
 **/
public abstract class BaseJitxRefundProcessStep {
    @Autowired
    protected IpJitxRefundService ipJitxRefundService;
    /**
     * wms撤回服务
     **/
    @Autowired
    protected OrderCancleWmsService orderCancleWmsService;
    /**
     * 订单日志服务
     **/
    @Autowired
    protected OmsOrderLogService omsOrderLogService;
    /**
     * 订单查询服务
     **/
    @Autowired
    protected OmsOrderService orderService;

    /**
     * JITX仓库产能服务
     */
    @Autowired
    protected VipcomJitxWarehouseService jitxWarehouseService;

    /**
     * 释放时效订单服务
     */
    @Autowired
    protected TimeOrderVoidSgSendService timeOrderVoidSgSendService;

    /**
     * 订单反审核服务
     */
    @Autowired
    OcBOrderTheAuditService ocBOrderTheAuditService;


    @Autowired
    protected OcBReturnOrderBatchAddService ocBReturnOrderBatchAddService;

    @Autowired
    protected OcBOrderHoldService ocBOrderHoldService;

    @Autowired
    protected OcBOrderItemMapper orderItemMapper;
}
