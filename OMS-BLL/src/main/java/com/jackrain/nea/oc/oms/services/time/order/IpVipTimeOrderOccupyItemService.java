package com.jackrain.nea.oc.oms.services.time.order;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.IpBTimeOrderVipMapper;
import com.jackrain.nea.oc.oms.mapper.IpBTimeOrderVipOccupyItemMapper;
import com.jackrain.nea.oc.oms.model.enums.TimeOrderOccupyItemStatusEnum;
import com.jackrain.nea.oc.oms.model.table.IpBTimeOrderVip;
import com.jackrain.nea.oc.oms.model.table.IpBTimeOrderVipOccupyItem;
import com.jackrain.nea.ps.api.table.PsCSku;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.st.model.table.StCVipcomJitxWarehouse;
import com.jackrain.nea.st.service.VipcomJitxWarehouseService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.vo.PhyWarehouseVo;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @Description JITX退款转换服务类
 * @Date 2019-6-26
 **/
@Component
@Slf4j
public class IpVipTimeOrderOccupyItemService {
    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private SubWarehouseService subWarehouseService;
    @Autowired
    private VipTimeOrderOccupyItemCreateService occupyItemCreateService;
    @Autowired
    protected IpBTimeOrderVipOccupyItemMapper vipOccupyItemMapper;
    @Autowired
    private VipcomJitxWarehouseService jitxWarehouseService;
    @Autowired
    private IpBTimeOrderVipMapper timeOrderVipMapper;

    public static final String TABLE_NAME = "ip_b_time_order_vip_occupy_item";

    /**
     * 时效订单分仓服务
     *
     * @param timeOrderVip 时效订单
     * @param itemDOList   库存占用明细
     * @param user         操作人
     * @return 返回结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 vipTimeOrderBranchWarehouse(IpBTimeOrderVip timeOrderVip
            , List<IpBTimeOrderVipOccupyItem> itemDOList, User user) {
        IpVipTimeOrderOccupyItemService service =
                ApplicationContextHandle.getBean(IpVipTimeOrderOccupyItemService.class);

        ValueHolderV14 valueHolder = new ValueHolderV14<>();
        // 店铺id
        Long shopId = timeOrderVip.getCpCShopId();
        // vip平台-jit仓库编码
        String jitWarehouse = timeOrderVip.getSaleWarehouse();
        // 商品条码编码
        IpBTimeOrderVipOccupyItem skuVipOccupyItem = itemDOList.get(0);
        Long skuId = skuVipOccupyItem.getPsCSkuId();
        String defaultSkuEcode = skuVipOccupyItem.getPsCSkuEcode();

        BigDecimal totalAmount = BigDecimal.ZERO;
        for (IpBTimeOrderVipOccupyItem vipOccupyItem : itemDOList) {
            if (TimeOrderOccupyItemStatusEnum.NOT_CONFIRM.getValue() == vipOccupyItem.getStatus()
                    || TimeOrderOccupyItemStatusEnum.OUT_OF_STOCK.getValue() == vipOccupyItem.getStatus()) {
                totalAmount = totalAmount.add(vipOccupyItem.getAmount());
            }
        }
        if (totalAmount.compareTo(BigDecimal.ZERO) <= 0) {
            valueHolder.setCode(ResultCode.FAIL);
            valueHolder.setMessage("时效订单没有未确认或者缺货的占单明细，退出分仓服务");
            return valueHolder;
        }
        //这个map只用来分仓
        Map<PsCSku, BigDecimal> skuMap = new HashMap<>(256);
        PsCSku psSku = new PsCSku();
        psSku.setId(skuId);
        psSku.setEcode(defaultSkuEcode);
        skuMap.put(psSku, totalAmount);

        //记录SKU和数量的map
        Map<String, BigDecimal> skuAndNumMap = new HashMap<>(256);
        skuAndNumMap.put(defaultSkuEcode, totalAmount);
        // 分仓
        ValueHolderV14<Map<Long, Map<String, BigDecimal>>> mapValueHolderV14 = subWarehouseService
                .subWarehouseInput(shopId, skuMap, jitWarehouse, user);
        if (ResultCode.FAIL == mapValueHolderV14.getCode()) {
            service.createOutStockOccupyItem(timeOrderVip, skuVipOccupyItem, user, totalAmount, true);
            valueHolder.setCode(ResultCode.SUCCESS);
            valueHolder.setMessage("分仓失败：" + mapValueHolderV14.getMessage() + "，生成缺货占单明细成功！");
            return valueHolder;
        }
        Map<Long, Map<String, BigDecimal>> longMapMap = mapValueHolderV14.getData();
        if (longMapMap.isEmpty()) {
            service.createOutStockOccupyItem(timeOrderVip, skuVipOccupyItem, user, totalAmount, true);
            valueHolder.setCode(ResultCode.SUCCESS);
            valueHolder.setMessage("分仓返回结果为空，生成缺货占单明细成功！");
            return valueHolder;
        }

        // 生成库存占用明细
        // 每一个实体仓 对应需要拣货的 skuMap
        // 新分仓中， 若实体仓中没有 拣货的sku  则会返回 实体仓的key   value 为空
        // 循环每一个sku   判断所有仓库  是否发完
        List<IpBTimeOrderVipOccupyItem> meetStockVipOccupyItems = new ArrayList<>();
        for (Map.Entry<Long, Map<String, BigDecimal>> longMapEntry : longMapMap.entrySet()) {
            BigDecimal skuNum = skuAndNumMap.get(defaultSkuEcode);
            if (skuNum == null || skuNum.compareTo(BigDecimal.ZERO) <= 0) {
                break;
            }
            Long warehouseId = longMapEntry.getKey();
            CpCPhyWarehouse phyWarehouse = cpRpcService.queryByWarehouseId(warehouseId);
            Map<String, BigDecimal> stringBigDecimalMap = longMapMap.get(warehouseId);
            if (MapUtils.isEmpty(stringBigDecimalMap)) {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("分仓完成后生成时效订单占用明细，当前map值为空，实体仓id:{}", warehouseId), warehouseId);
                }
                continue;
            }
            BigDecimal number = stringBigDecimalMap.get(defaultSkuEcode);
            if (number == null || number.compareTo(BigDecimal.ZERO) <= 0) {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("时效订单分仓，仓库数量小于等于0，实体仓ID：{}", warehouseId), warehouseId);
                }
                continue;
            }

            IpBTimeOrderVipOccupyItem meetStockVipOccupyItem = new IpBTimeOrderVipOccupyItem();
            if (number.compareTo(skuNum) >= 0) {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("实体仓数量大于等于条码数量，时效订单ID：{}，实体仓ID：{}", timeOrderVip.getId(), warehouseId), timeOrderVip.getId(), warehouseId);
                }
                BeanUtils.copyProperties(skuVipOccupyItem, meetStockVipOccupyItem);
                meetStockVipOccupyItem.setId(ModelUtil.getSequence(TABLE_NAME));
                meetStockVipOccupyItem.setOutStockQuantity(BigDecimal.ZERO);
                meetStockVipOccupyItem.setAmount(skuNum);
                meetStockVipOccupyItem.setStatus(TimeOrderOccupyItemStatusEnum.NOT_CONFIRM.getValue());
                buildPhyWareHouse(phyWarehouse, meetStockVipOccupyItem, shopId);
                meetStockVipOccupyItems.add(meetStockVipOccupyItem);
                skuAndNumMap.put(defaultSkuEcode, BigDecimal.ZERO);
                break;
            }
            if (number.compareTo(skuNum) < 0) {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("实体仓数量小于条码数量，时效订单ID：{}，实体仓ID：{}", timeOrderVip.getId(), warehouseId), timeOrderVip.getId(), warehouseId);
                }
                BeanUtils.copyProperties(skuVipOccupyItem, meetStockVipOccupyItem);
                meetStockVipOccupyItem.setId(ModelUtil.getSequence(TABLE_NAME));
                meetStockVipOccupyItem.setOutStockQuantity(BigDecimal.ZERO);
                meetStockVipOccupyItem.setStatus(TimeOrderOccupyItemStatusEnum.NOT_CONFIRM.getValue());
                meetStockVipOccupyItem.setAmount(number);
                buildPhyWareHouse(phyWarehouse, meetStockVipOccupyItem, shopId);
                meetStockVipOccupyItems.add(meetStockVipOccupyItem);
                skuAndNumMap.put(defaultSkuEcode, skuNum.subtract(number));
            }
        }
        occupyItemCreateService.create(timeOrderVip, meetStockVipOccupyItems, user);
        BigDecimal lastNum = skuAndNumMap.get(defaultSkuEcode);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("时效订单分仓缺货数量：{}，时效订单ID：{}", timeOrderVip.getId()), lastNum, timeOrderVip.getId());
        }
        //大于0，证明有缺货的
        if (lastNum.compareTo(BigDecimal.ZERO) > 0) {
            service.createOutStockOccupyItem(timeOrderVip, skuVipOccupyItem, user, lastNum, false);
        }
        valueHolder.setCode(ResultCode.SUCCESS);
        valueHolder.setMessage("时效订单分仓成功！");
        return valueHolder;
    }

    /**
     * 创建缺货占单明细
     *
     * @param timeOrderVip  时效订单
     * @param oldOccupyItem 原明细
     * @param user          用户
     * @param outStockNum   缺货数量
     */
    @Transactional(rollbackFor = Exception.class)
    public void createOutStockOccupyItem(IpBTimeOrderVip timeOrderVip,
                                         IpBTimeOrderVipOccupyItem oldOccupyItem,
                                         User user, BigDecimal outStockNum, boolean isVoid) {
        int count = 1;
        if (isVoid) {
            // 把所有 状态 确认及缺货 唯品会时效订单库存占用明细 作废
            count = vipOccupyItemMapper.updateIsActiveNByStatusAndMainId(
                    timeOrderVip.getId(), Long.valueOf(user.getId()), user.getEname(),
                    user.getName(), new Date());
        }
        if (count > 0) {
            IpBTimeOrderVipOccupyItem outStockOccupyItem = new IpBTimeOrderVipOccupyItem();
            // 缺货
            BeanUtils.copyProperties(oldOccupyItem, outStockOccupyItem);
            ValueHolderV14<PhyWarehouseVo> vh =
                    subWarehouseService.getDefaultPhyWarehouse(timeOrderVip.getCpCShopId());
            if (vh.isOK() && vh.getData() != null) {
                PhyWarehouseVo phyWarehouseVo = vh.getData();
                Long phyWarehouseId = phyWarehouseVo.getPhyWarehouseId();
                CpCPhyWarehouse phyWarehouse = cpRpcService.queryByWarehouseId(phyWarehouseId);
                outStockOccupyItem.setCpCPhyWarehouseId(phyWarehouseId);
                outStockOccupyItem.setCpCPhyWarehouseEname(phyWarehouse.getEname());
                outStockOccupyItem.setCpCPhyWarehouseEcode(phyWarehouse.getEcode());
            }
            outStockOccupyItem.setId(ModelUtil.getSequence(TABLE_NAME));
            outStockOccupyItem.setCreationdate(new Date());
            outStockOccupyItem.setOwnerid(Long.valueOf(user.getId()));
            outStockOccupyItem.setOwnername(user.getName());
            outStockOccupyItem.setOwnerename(user.getEname());
            outStockOccupyItem.setAmount(outStockNum);
            outStockOccupyItem.setOutStockQuantity(outStockNum);
            outStockOccupyItem.setStatus(TimeOrderOccupyItemStatusEnum.OUT_OF_STOCK.getValue());
            vipOccupyItemMapper.insert(outStockOccupyItem);
        }

    }


    public void buildPhyWareHouse(CpCPhyWarehouse phyWarehouse, IpBTimeOrderVipOccupyItem occupyItem, Long shopId) {
        StCVipcomJitxWarehouse jitxWarehouse = jitxWarehouseService.queryJitxCapacity(shopId,
                phyWarehouse.getId(), null);
        if (jitxWarehouse == null) {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("实体仓没有在JITX仓库对照表中维护JITX仓库编码，店铺id：{}，实体仓：{}", shopId), shopId, JSON.toJSONString(phyWarehouse));
            }
        } else {
            occupyItem.setWarehouseCode(jitxWarehouse.getVipcomWarehouseEcode());
        }
        occupyItem.setCpCPhyWarehouseId(phyWarehouse.getId());
        occupyItem.setCpCPhyWarehouseEcode(phyWarehouse.getEcode());
        occupyItem.setCpCPhyWarehouseEname(phyWarehouse.getEname());
    }

    private void printLog(String message, Object... argos) {
        if (log.isDebugEnabled()) {
            message = this.getClass().getSimpleName() + message;
            log.debug(message, argos);
        }
    }
}
