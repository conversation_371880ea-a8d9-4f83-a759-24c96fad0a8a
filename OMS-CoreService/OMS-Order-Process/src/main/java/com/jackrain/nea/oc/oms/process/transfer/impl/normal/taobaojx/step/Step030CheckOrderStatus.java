package com.jackrain.nea.oc.oms.process.transfer.impl.normal.taobaojx.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoJxOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoJxOrder;
import com.jackrain.nea.oc.oms.model.taobao.TaoBaoJxOrderStatus;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description 判断交易状态并处理相应逻辑
 **/
@Step(order = 30, description = "判断交易状态")
@Slf4j
@Component
public class Step030CheckOrderStatus extends BaseTaobaoJxOrderProcessStep
        implements IOmsOrderProcessStep<IpTaobaoJxOrderRelation> {
    @Override
    public ProcessStepResult<IpTaobaoJxOrderRelation> startProcess(IpTaobaoJxOrderRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        //3.判断【交易状态】是否等于WAIT_FOR_SUPPLIER_DELIVER（付款成功，待供应商发货）
        //20190815 增加 交易完成（TRADE_FINISHED）、等待分销商收货（WAIT_FOR_APPLIER_STORAGE）
        //20191121 增加 交易关闭（TRADE_CLOSED）
        IpBTaobaoJxOrder ipBTaobaoJxOrder = orderInfo.getTaobaoJxOrder();
        try {
            if (TaoBaoJxOrderStatus.WAIT_FOR_SUPPLIER_DELIVER.equals(ipBTaobaoJxOrder.getOrderStatus()) ||
                    TaoBaoJxOrderStatus.TRADE_FINISHED.equals(ipBTaobaoJxOrder.getOrderStatus()) ||
                    TaoBaoJxOrderStatus.WAIT_FOR_APPLIER_STORAGE.equals(ipBTaobaoJxOrder.getOrderStatus()) ||
                    TaoBaoJxOrderStatus.TRADE_CLOSED.equals(ipBTaobaoJxOrder.getOrderStatus())
            ) {
                return new ProcessStepResult<>(StepStatus.SUCCESS, "单据" + orderInfo.getOrderNo() + "交易状态处理成功，进入下一阶段！");
            } else {
                //非付款成功，待供应商发货
                ipTaobaoJxOrderService.updateIsTrans(TransferOrderStatus.TRANSFERRED,
                        "订单状态不是待发货状态，退出转换！", ipBTaobaoJxOrder);
                return new ProcessStepResult<>(StepStatus.FINISHED, "订单状态不是待发货状态！");
            }
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 淘宝经销订单转换异常！", e);
            //修改中间表状态及系统备注
            ipTaobaoJxOrderService.updateIsTransError(ipBTaobaoJxOrder, e.getMessage());
            String errorMessage = "淘宝经销订单转换异常！" + e.getMessage();
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
    }
}
