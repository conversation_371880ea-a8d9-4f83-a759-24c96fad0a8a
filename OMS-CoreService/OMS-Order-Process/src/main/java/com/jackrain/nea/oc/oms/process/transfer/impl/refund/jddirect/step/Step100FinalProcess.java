package com.jackrain.nea.oc.oms.process.transfer.impl.refund.jddirect.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.StepExeState;
import com.jackrain.nea.oc.oms.model.relation.OmsJDDirectCancelRelation;
import com.jackrain.nea.oc.oms.model.relation.StepExecInfo;
import com.jackrain.nea.oc.oms.process.transfer.impl.refund.jddirect.JDDirectTransferSupply;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.oc.oms.util.OmsTransferSupply;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2022/3/28
 */
@Slf4j
@Component
@Step(order = 1000, description = "finally release resources")
public class Step100FinalProcess extends AbstractJDDirectCancelProcessStep {
    @Override
    public ProcessStepResult<OmsJDDirectCancelRelation> startProcess(OmsJDDirectCancelRelation o, ProcessStepResult v,
                                                                     boolean isAutoMakeup, User operateUser) {
        // 1. release lock
        OmsTransferSupply.unlockOcOrders();

        // 2. log
        OmsTransferSupply.logs("JDDirectCancelTransfer." + o.getIpRefundNo() + "," + o.getIpOrderId() + ",logs");

        // 3. release thread memory space
        OmsTransferSupply.releaseResources();
        // 3. reSet handle result
        StepExecInfo stepExecInfo = o.getStepExecInfo();
        if (StepStatus.FAILED == v.getStatus()) {
            return JDDirectTransferSupply.getTransStep(StepExeState.FAIL);
        }
        if (stepExecInfo.getIsNeedReTrans() > 0) {
            return JDDirectTransferSupply.getTransStep(StepExeState.RE_DEAL);
        }
        return JDDirectTransferSupply.getTransStep(StepExeState.SUCCESS);

    }
}
