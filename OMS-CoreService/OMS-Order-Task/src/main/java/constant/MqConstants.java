package constant;

import com.jackrain.nea.oc.oms.mq.processor.impl.OperateAuditProcessorImpl;

/**
 * @program: ryytn-oc-oms-v3.0
 * @description: task_mqConstant
 * @author: haiyang
 * @create: 2024-01-03 17:11
 **/
public class MqConstants {

    public static final String TOPIC_R3_OC_OMS_CALL_AUTOSPLIT = "R3_OC_OMS_CALL_AUTOSPLIT";
    public static final String TAG_R3_OC_OMS_CALL_AUTOSPLIT = "OperateSplit";


    /**
     * @see OperateAuditProcessorImpl
     */
    public static final String TOPIC_R3_OC_OMS_CALL_AUTOAUDIT = "R3_OC_OMS_CALL_AUTOAUDIT";
    public static final String TAG_R3_OC_OMS_CALL_AUTOAUDIT = "OperateAudit";

}
