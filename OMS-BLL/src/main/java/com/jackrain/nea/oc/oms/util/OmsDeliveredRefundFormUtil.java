package com.jackrain.nea.oc.oms.util;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.AGStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnAfSendReturnBillTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.ToACStatusEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBReturnAfSendRelation;
import com.jackrain.nea.oc.oms.model.table.*;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.nums.RefundOrderSourceTypeEnum;
import com.jackrain.nea.oc.oms.services.OmsRefundOrderService;
import com.jackrain.nea.oc.oms.services.refund.JdReturnUtil;
import com.jackrain.nea.util.OperateUserUtils;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;

/**
 * @Author: 黄世新
 * @Date: 2021/1/4 上午10:57
 * @Version 1.0
 * <p>
 * 已发货退款单的构建
 */
@Component
@Slf4j
public class OmsDeliveredRefundFormUtil {
    @Autowired
    private BuildSequenceUtil sequenceUtil;

    @Autowired
    private OmsRefundOrderService omsRefundOrderService;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;


    /**
     * 京东退单生成发货后退款单
     *
     * @return
     */
    private OcBReturnAfSend buildOcBReturnAfSend(OcBOrder ocBOrder,
                                                 IpBJingdongRefund jingdongRefund,
                                                 Integer billType, User user) {
        OcBReturnAfSend ocBReturnAfSend = new OcBReturnAfSend();
        ocBReturnAfSend.setCpCShopId(ocBOrder.getCpCShopId());
        ocBReturnAfSend.setCpCShopEcode(ocBOrder.getCpCShopEcode());
        ocBReturnAfSend.setCpCShopTitle(ocBOrder.getCpCShopTitle());
        ocBReturnAfSend.setTid(ocBOrder.getTid());
        ocBReturnAfSend.setBillNo(sequenceUtil.aFbuildBillNo());
        // 退款状态 0 待审核
        ocBReturnAfSend.setReturnStatus(ReturnAfSendReturnBillTypeEnum.REFUNDING.getVal());
        // 待审核待传对账,赋默认值
        ocBReturnAfSend.setToSettleStatus(ToACStatusEnum.INIT.val());
        ocBReturnAfSend.setTReturnId(jingdongRefund.getAfsserviceid() + "");
        ocBReturnAfSend.setTReturnStatus(jingdongRefund.getAfsservicestatusname());
        //单据类型 0 退货退款 1仅退款',
        ocBReturnAfSend.setBillType(billType);
        ocBReturnAfSend.setVipNick(jingdongRefund.getCustomerpin());
        // @******** 增加手机号码
        ocBReturnAfSend.setVipPhone(ocBOrder.getReceiverPhone());
        // @******** 增加阿里支付账号
        ocBReturnAfSend.setPayAccount(ocBOrder.getBuyerAlipayNo());
        ocBReturnAfSend.setReason(jingdongRefund.getReason());
        //单据来源设置默认值为2 自动
        ocBReturnAfSend.setRefundOrderSourceType(RefundOrderSourceTypeEnum.AUTO.getValue());
        //支付方式
        ocBReturnAfSend.setPayMode(ocBOrder.getPayType() + "");
        //支付宝账号
        ocBReturnAfSend.setPayAccount(ocBOrder.getBuyerAlipayNo());
        //申请退款金额
        ocBReturnAfSend.setAmtReturnApply(jingdongRefund.getPrice());
        ocBReturnAfSend.setTReturnStatus(jingdongRefund.getAfsservicestatusname());
        ocBReturnAfSend.setSourceBillNo(ocBOrder.getId() + "");
        ocBReturnAfSend.setCpCPlatformId(Long.valueOf(ocBOrder.getPlatform()));
        //ocBReturnAfSend.setPayMode(OcBReturnAfSendListEnums.PayTypeEnum.Alipay.getVal());
        //todo 实际退款金额
        ocBReturnAfSend.setAmtReturnActual(jingdongRefund.getPrice());
        //ocBReturnAfSend.setPtGoodStatus(taobaoRefund.getGoodStatus());
        //申请退款时间
        ocBReturnAfSend.setReturnApplyTime(new Date());
        ocBReturnAfSend.setAgStatus(AGStatusEnum.INIT.getVal() + "");
        //ocBReturnAfSend.setReturnExplain(jingdongRefund.getRefunddesc());
        String status = jingdongRefund.getAfsservicestatusname();
        if (JdReturnUtil.RefundStatus.COMPLETE.getCode().equals(status)) {
            ocBReturnAfSend.setReturnStatus(ReturnAfSendReturnBillTypeEnum.REFUNDCOMPLETED.getVal());
            ocBReturnAfSend.setReturnPaymentTime(new Date());
            // 退款成功待传对账
            ocBReturnAfSend.setToSettleStatus(ToACStatusEnum.PENDING.val());
        }
        //业务类型
        StCBusinessType stCBusinessType = omsRefundOrderService.queryRefundOrderType(ocBOrder);
        ocBReturnAfSend.setBusinessTypeId(stCBusinessType.getId());
        ocBReturnAfSend.setBusinessTypeCode(stCBusinessType.getEcode());
        ocBReturnAfSend.setBusinessTypeName(stCBusinessType.getEname());
        OperateUserUtils.saveOperator(ocBReturnAfSend, user);
        return ocBReturnAfSend;
    }


    private List<OcBReturnAfSendItem> buildOcBReturnAfSendItemRelation(List<OcBReturnOrderRefund> ocBReturnOrderRefunds,
                                                                       User user, List<OcBOrderItem> orderItems,
                                                                       OcBReturnOrder ocBReturnOrder) {

        Map<Long, OcBOrderItem> map = new HashMap<>(10);
        if (CollectionUtils.isNotEmpty(orderItems)) {
            for (OcBOrderItem item : orderItems) {
                map.put(item.getId(), item);
            }
        }
        List<OcBReturnAfSendItem> ocBReturnAfSendItems = new ArrayList<>();
        for (OcBReturnOrderRefund ocBReturnOrderRefund : ocBReturnOrderRefunds) {
            OcBReturnAfSendItem ocBReturnAfSendItem = new OcBReturnAfSendItem();
            //关联类型
            ocBReturnAfSendItem.setRelationBillType(0L);
            ocBReturnAfSendItem.setRelationBillId(ocBReturnOrderRefund.getOcBReturnOrderId());
            ocBReturnAfSendItem.setRelationBillNo(ocBReturnOrder.getBillNo());
            //'单据类型  客退 0，拦截 1，拒收 2 ',
            ocBReturnAfSendItem.setBillType(1);
            //todo 拦截状态
            //ocBReturnAfSendItem.setInterceptStatus();
            //赠品
            ocBReturnAfSendItem.setGift(ocBReturnOrderRefund.getGiftType());
            ocBReturnAfSendItem.setPsCSkuId(ocBReturnOrderRefund.getPsCSkuId());
            ocBReturnAfSendItem.setPsCSkuEcode(ocBReturnOrderRefund.getPsCSkuEcode());
            ocBReturnAfSendItem.setPsCProEcode(ocBReturnOrderRefund.getPsCProEcode());
            ocBReturnAfSendItem.setPsCProEname(ocBReturnOrderRefund.getPsCProEname());
            ocBReturnAfSendItem.setPsCProId(ocBReturnOrderRefund.getPsCProId());
            ocBReturnAfSendItem.setFreight(BigDecimal.ZERO);

            //todo 规格id
            // ocBReturnAfSendItem.setPsCSpecId(ocBReturnOrderRefund.);
            //todo 规格名称
            //ocBReturnAfSendItem.setPsCSpecEname();
            //申请退货数量
            ocBReturnAfSendItem.setQtyReturnApply(ocBReturnOrderRefund.getQtyRefund());
            ocBReturnAfSendItem.setAmtReturn(ocBReturnOrderRefund.getAmtRefund());
            ocBReturnAfSendItem.setPurchaseQty(ocBReturnOrderRefund.getQtyRefund());
            OcBOrderItem orderItem = map.get(ocBReturnOrderRefund.getOcBOrderItemId());
            if (orderItem != null) {
                ocBReturnAfSendItem.setPurchaseQty(orderItem.getQty());
                ocBReturnAfSendItem.setPtProName(orderItem.getPtProName());
                ocBReturnAfSendItem.setPsCSkuEname(orderItem.getPsCSkuEname());
                ocBReturnAfSendItem.setPsCSkuPtEcode(orderItem.getPsCSkuPtEcode());
                // BigDecimal realAmt = orderItem.getRealAmt().divide(orderItem.getQty(), 4, BigDecimal.ROUND_HALF_UP).multiply(ocBReturnOrderRefund.getQtyRefund());
                ocBReturnAfSendItem.setAmtActual(orderItem.getRealAmt());

                ocBReturnAfSendItem.setOcBOrderItemId(orderItem.getId());
                if (ObjectUtil.isNotNull(orderItem.getOcBOrderId())) {
                    OcBOrder order = ocBOrderMapper.get4AfReturn(orderItem.getOcBOrderId());
                    if (ObjectUtil.isNotNull(order)) {
                        ocBReturnAfSendItem.setOcBOrderId(order.getId());
                        ocBReturnAfSendItem.setBusinessTypeCode(order.getBusinessTypeCode());
                        ocBReturnAfSendItem.setBusinessTypeId(order.getBusinessTypeId());
                        ocBReturnAfSendItem.setBusinessTypeName(order.getBusinessTypeName());
                    }
                }
            }
            OperateUserUtils.saveOperator(ocBReturnAfSendItem, user);
            ocBReturnAfSendItems.add(ocBReturnAfSendItem);
        }
        return ocBReturnAfSendItems;
    }


    public OcBReturnAfSendRelation jDRefundAfSendToReturn(List<OcBReturnOrderRefund> ocBReturnOrderRefunds,
                                                          OcBOrder ocBOrder,
                                                          IpBJingdongRefund jingdongRefund,
                                                          User user, List<OcBOrderItem> orderItems,
                                                          OcBReturnOrder ocBReturnOrder) {
        OcBReturnAfSendRelation ocBReturnAfSendRelation = new OcBReturnAfSendRelation();
        OcBReturnAfSend ocBReturnAfSend = this.buildOcBReturnAfSend(ocBOrder, jingdongRefund,
                TaobaoReturnOrderExt.SendBillType.RETURN_REFUND.getCode(), user);
        List<OcBReturnAfSendItem> ocBReturnAfSendItems = buildOcBReturnAfSendItemRelation(ocBReturnOrderRefunds, user, orderItems, ocBReturnOrder);
        ocBReturnAfSendRelation.setOcBReturnAfSend(ocBReturnAfSend);
        ocBReturnAfSendRelation.setOcBReturnAfSendItems(ocBReturnAfSendItems);
        return ocBReturnAfSendRelation;
    }


    /**
     * 京东取消生成已发货退款单
     *
     * @param ocBOrder
     * @param ipBJingdongSaRefund
     * @param billType
     * @param user
     * @return
     */
    private OcBReturnAfSend buildOcBReturnAfSend(OcBOrder ocBOrder,
                                                 IpBJingdongSaRefund ipBJingdongSaRefund,
                                                 Integer billType, User user) {
        OcBReturnAfSend ocBReturnAfSend = new OcBReturnAfSend();
        ocBReturnAfSend.setCpCShopId(ocBOrder.getCpCShopId());
        ocBReturnAfSend.setCpCShopEcode(ocBOrder.getCpCShopEcode());
        ocBReturnAfSend.setCpCShopTitle(ocBOrder.getCpCShopTitle());
        ocBReturnAfSend.setTid(ocBOrder.getTid());
        ocBReturnAfSend.setBillNo(sequenceUtil.aFbuildBillNo());
        // 退款状态 0 待审核
        ocBReturnAfSend.setReturnStatus(ReturnAfSendReturnBillTypeEnum.REFUNDING.getVal());
        // 待审核待传对账,赋默认值
        ocBReturnAfSend.setToSettleStatus(ToACStatusEnum.INIT.val());
        ocBReturnAfSend.setTReturnId(ipBJingdongSaRefund.getPopafsrefundapplyid() + "");
        ocBReturnAfSend.setTReturnStatus(JdReturnUtil.transTaobaoRefundStatus(ipBJingdongSaRefund.getStatus()));
        //单据类型 0 退货退款 1仅退款',
        ocBReturnAfSend.setBillType(billType);
        ocBReturnAfSend.setVipNick(ipBJingdongSaRefund.getBuyername());
        // @******** 增加手机号码
        ocBReturnAfSend.setVipPhone(ocBOrder.getReceiverPhone());
        // @******** 增加阿里支付账号
        ocBReturnAfSend.setPayAccount(ocBOrder.getBuyerAlipayNo());
        ocBReturnAfSend.setReason(ipBJingdongSaRefund.getReason());
        //单据来源设置默认值为2 自动
        ocBReturnAfSend.setRefundOrderSourceType(RefundOrderSourceTypeEnum.AUTO.getValue());
        //支付方式
        ocBReturnAfSend.setPayMode(ocBOrder.getPayType() + "");
        //支付宝账号
        ocBReturnAfSend.setPayAccount(ocBOrder.getBuyerAlipayNo());
        //申请退款金额
        ocBReturnAfSend.setAmtReturnApply(ocBOrder.getOrderAmt());
        ocBReturnAfSend.setSourceBillNo(ocBOrder.getId() + "");
        ocBReturnAfSend.setCpCPlatformId(Long.valueOf(ocBOrder.getPlatform()));
        //ocBReturnAfSend.setPayMode(OcBReturnAfSendListEnums.PayTypeEnum.Alipay.getVal());
        //todo 实际退款金额
        ocBReturnAfSend.setAmtReturnActual(ocBOrder.getOrderAmt());
        //ocBReturnAfSend.setPtGoodStatus(taobaoRefund.getGoodStatus());
        //申请退款时间
        ocBReturnAfSend.setReturnApplyTime(new Date());
        ocBReturnAfSend.setAgStatus(AGStatusEnum.INIT.getVal() + "");
        String refundStatus = JdReturnUtil.transTaobaoRefundStatus(ipBJingdongSaRefund.getStatus());
        if (TaobaoReturnOrderExt.RefundStatus.SUCCESS.getCode().equals(refundStatus)) {
            ocBReturnAfSend.setReturnStatus(ReturnAfSendReturnBillTypeEnum.REFUNDCOMPLETED.getVal());
            ocBReturnAfSend.setReturnPaymentTime(new Date());
            // 退款成功待传对账
            ocBReturnAfSend.setToSettleStatus(ToACStatusEnum.PENDING.val());
        }
        //ocBReturnAfSend.setReturnExplain(jingdongRefund.getRefunddesc());
        StCBusinessType stCBusinessType = omsRefundOrderService.queryRefundOrderType(ocBOrder);
        ocBReturnAfSend.setBusinessTypeId(stCBusinessType.getId());
        ocBReturnAfSend.setBusinessTypeCode(stCBusinessType.getEcode());
        ocBReturnAfSend.setBusinessTypeName(stCBusinessType.getEname());
        OperateUserUtils.saveOperator(ocBReturnAfSend, user);

        return ocBReturnAfSend;
    }

    /**
     * 京东取消生成退换货单
     *
     * @param ocBReturnOrderRefunds
     * @param ocBOrder
     * @param ipBJingdongSaRefund
     * @param user
     * @param orderItems
     * @param ocBReturnOrder
     * @return
     */
    public OcBReturnAfSendRelation jDCancelRefundAfSendToReturn(List<OcBReturnOrderRefund> ocBReturnOrderRefunds,
                                                                OcBOrder ocBOrder, IpBJingdongSaRefund ipBJingdongSaRefund,
                                                                User user, List<OcBOrderItem> orderItems,
                                                                OcBReturnOrder ocBReturnOrder) {
        OcBReturnAfSendRelation ocBReturnAfSendRelation = new OcBReturnAfSendRelation();
        OcBReturnAfSend ocBReturnAfSend = this.buildOcBReturnAfSend(ocBOrder, ipBJingdongSaRefund,
                TaobaoReturnOrderExt.SendBillType.RETURN_REFUND.getCode(), user);
        List<OcBReturnAfSendItem> ocBReturnAfSendItems = buildOcBReturnAfSendItemRelation(ocBReturnOrderRefunds, user, orderItems, ocBReturnOrder);
        ocBReturnAfSendRelation.setOcBReturnAfSend(ocBReturnAfSend);
        ocBReturnAfSendRelation.setOcBReturnAfSendItems(ocBReturnAfSendItems);
        return ocBReturnAfSendRelation;
    }

    /***
     * 京东取消订单 - 奶卡订单生成已发货退款单
     * @param ocBOrder
     * @param ipBJingdongSaRefund
     * @param user
     * @param orderItems
     * @return
     */
    public OcBReturnAfSendRelation jDCancelOrderAfSendToReturn(OcBOrder ocBOrder,
                                                               IpBJingdongSaRefund ipBJingdongSaRefund,
                                                               User user, List<OcBOrderItem> orderItems) {
        OcBReturnAfSendRelation ocBReturnAfSendRelation = new OcBReturnAfSendRelation();
        OcBReturnAfSend ocBReturnAfSend = this.buildOcBReturnAfSend(ocBOrder, ipBJingdongSaRefund,
                TaobaoReturnOrderExt.SendBillType.REFUND_ONLY.getCode(), user);
        List<OcBReturnAfSendItem> ocBReturnAfSendItems = buildOcBReturnAfSendItemsByOrderItems(user, orderItems);
        ocBReturnAfSendRelation.setOcBReturnAfSend(ocBReturnAfSend);
        ocBReturnAfSendRelation.setOcBReturnAfSendItems(ocBReturnAfSendItems);
        return ocBReturnAfSendRelation;
    }

    /**
     * 京东退单 - 奶卡订单生成已发货退款单
     * @param ocBOrder
     * @param jingdongRefund
     * @param user
     * @param orderItems
     * @return
     */
    public OcBReturnAfSendRelation jDBulidRefundAfSendToReturn(OcBOrder ocBOrder,
                                                          IpBJingdongRefund jingdongRefund,
                                                          User user, List<OcBOrderItem> orderItems) {
        OcBReturnAfSendRelation ocBReturnAfSendRelation = new OcBReturnAfSendRelation();
        OcBReturnAfSend ocBReturnAfSend = this.buildOcBReturnAfSend(ocBOrder, jingdongRefund,
                TaobaoReturnOrderExt.SendBillType.REFUND_ONLY.getCode(), user);
        List<OcBReturnAfSendItem> ocBReturnAfSendItems = new ArrayList<>();
        for (OcBOrderItem ocBOrderItem : orderItems) {
            OcBReturnAfSendItem ocBReturnAfSendItem = new OcBReturnAfSendItem();
            //关联类型
            ocBReturnAfSendItem.setRelationBillType(0L);
            //ocBReturnAfSendItem.setRelationBillId(ocBReturnOrderRefund.getOcBReturnOrderId());
            //ocBReturnAfSendItem.setRelationBillNo(ocBReturnOrder.getBillNo());
            //'单据类型  客退 0，拦截 1，拒收 2 ',
            ocBReturnAfSendItem.setBillType(1);
            //赠品
            ocBReturnAfSendItem.setGift(String.valueOf(ocBOrderItem.getIsGift()));
            ocBReturnAfSendItem.setPsCSkuId(ocBOrderItem.getPsCSkuId());
            ocBReturnAfSendItem.setPsCSkuEcode(ocBOrderItem.getPsCSkuEcode());
            ocBReturnAfSendItem.setPsCProEcode(ocBOrderItem.getPsCProEcode());
            ocBReturnAfSendItem.setPsCProEname(ocBOrderItem.getPsCProEname());
            ocBReturnAfSendItem.setPsCProId(ocBOrderItem.getPsCProId());
            ocBReturnAfSendItem.setFreight(BigDecimal.ZERO);
            //申请退货数量
            ocBReturnAfSendItem.setQtyReturnApply(ocBOrderItem.getQty());
            ocBReturnAfSendItem.setAmtReturn(ocBOrderItem.getRealAmt());
            ocBReturnAfSendItem.setPurchaseQty(ocBOrderItem.getQty());
            ocBReturnAfSendItem.setPurchaseQty(ocBOrderItem.getQty());
            ocBReturnAfSendItem.setPtProName(ocBOrderItem.getPtProName());
            ocBReturnAfSendItem.setPsCSkuEname(ocBOrderItem.getPsCSkuEname());
            ocBReturnAfSendItem.setPsCSkuPtEcode(ocBOrderItem.getPsCSkuPtEcode());
            ocBReturnAfSendItem.setAmtActual(ocBOrderItem.getRealAmt());

            ocBReturnAfSendItem.setOcBOrderItemId(ocBOrderItem.getId());
            if (ObjectUtil.isNotNull(ocBOrderItem.getOcBOrderId())) {
                OcBOrder order = ocBOrderMapper.get4AfReturn(ocBOrderItem.getOcBOrderId());
                if (ObjectUtil.isNotNull(order)) {
                    ocBReturnAfSendItem.setOcBOrderId(order.getId());
                    ocBReturnAfSendItem.setBusinessTypeCode(order.getBusinessTypeCode());
                    ocBReturnAfSendItem.setBusinessTypeId(order.getBusinessTypeId());
                    ocBReturnAfSendItem.setBusinessTypeName(order.getBusinessTypeName());
                }
            }

            OperateUserUtils.saveOperator(ocBReturnAfSendItem, user);
            ocBReturnAfSendItems.add(ocBReturnAfSendItem);
        }
        ocBReturnAfSendRelation.setOcBReturnAfSend(ocBReturnAfSend);
        ocBReturnAfSendRelation.setOcBReturnAfSendItems(ocBReturnAfSendItems);
        return ocBReturnAfSendRelation;
    }


    /**
     * 根据零售发货单明细构建已发货退款单明细
     * @param user
     * @param orderItems
     * @return
     */
    private List<OcBReturnAfSendItem> buildOcBReturnAfSendItemsByOrderItems(User user, List<OcBOrderItem> orderItems) {
        List<OcBReturnAfSendItem> ocBReturnAfSendItems = new ArrayList<>();
        for (OcBOrderItem orderItem : orderItems) {
            OcBReturnAfSendItem ocBReturnAfSendItem = new OcBReturnAfSendItem();
            //关联类型
            ocBReturnAfSendItem.setRelationBillType(0L);
            //ocBReturnAfSendItem.setRelationBillId(ocBReturnOrderRefund.getOcBReturnOrderId());
            //ocBReturnAfSendItem.setRelationBillNo(ocBReturnOrder.getBillNo());
            //'单据类型  客退 0，拦截 1，拒收 2 ',
            ocBReturnAfSendItem.setBillType(1);
            //赠品
            ocBReturnAfSendItem.setGift(orderItem.getGiftType());
            ocBReturnAfSendItem.setPsCSkuId(orderItem.getPsCSkuId());
            ocBReturnAfSendItem.setPsCSkuEcode(orderItem.getPsCSkuEcode());
            ocBReturnAfSendItem.setPsCProEcode(orderItem.getPsCProEcode());
            ocBReturnAfSendItem.setPsCProEname(orderItem.getPsCProEname());
            ocBReturnAfSendItem.setPsCProId(orderItem.getPsCProId());
            ocBReturnAfSendItem.setFreight(BigDecimal.ZERO);
            //申请退货数量
            ocBReturnAfSendItem.setQtyReturnApply(orderItem.getQty());
            ocBReturnAfSendItem.setAmtReturn(orderItem.getRealAmt());
            ocBReturnAfSendItem.setPurchaseQty(orderItem.getQty());
            ocBReturnAfSendItem.setPurchaseQty(orderItem.getQty());
            ocBReturnAfSendItem.setPtProName(orderItem.getPtProName());
            ocBReturnAfSendItem.setPsCSkuEname(orderItem.getPsCSkuEname());
            ocBReturnAfSendItem.setPsCSkuPtEcode(orderItem.getPsCSkuPtEcode());
            ocBReturnAfSendItem.setAmtActual(orderItem.getRealAmt());

            ocBReturnAfSendItem.setOcBOrderItemId(orderItem.getId());
            if (ObjectUtil.isNotNull(orderItem.getOcBOrderId())) {
                OcBOrder order = ocBOrderMapper.get4AfReturn(orderItem.getOcBOrderId());
                if (ObjectUtil.isNotNull(order)) {
                    ocBReturnAfSendItem.setOcBOrderId(order.getId());
                    ocBReturnAfSendItem.setBusinessTypeCode(order.getBusinessTypeCode());
                    ocBReturnAfSendItem.setBusinessTypeId(order.getBusinessTypeId());
                    ocBReturnAfSendItem.setBusinessTypeName(order.getBusinessTypeName());
                }
            }

            OperateUserUtils.saveOperator(ocBReturnAfSendItem, user);
            ocBReturnAfSendItems.add(ocBReturnAfSendItem);
        }
        return ocBReturnAfSendItems;
    }

}
