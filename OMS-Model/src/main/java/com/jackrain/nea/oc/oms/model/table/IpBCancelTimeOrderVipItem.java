package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@TableName(value = "IP_B_CANCEL_TIME_ORDER_VIP_ITEM")
@Data
@Document(index = "ip_b_cancel_time_order_vip_item", type = "ip_b_cancel_time_order_vip_item")
public class IpBCancelTimeOrderVipItem extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "SALES_NO")
    @Field(type = FieldType.Keyword)
    private String salesNo;

    @JSONField(name = "WAREHOUSE")
    @Field(type = FieldType.Keyword)
    private String warehouse;

    @JSONField(name = "WAREHOUSE_ID")
    @Field(type = FieldType.Long)
    private Long warehouseId;

    @JSONField(name = "WAREHOUSE_CODE")
    @Field(type = FieldType.Keyword)
    private String warehouseCode;

    @JSONField(name = "WAREHOUSE_NAME")
    @Field(type = FieldType.Keyword)
    private String warehouseName;

    @JSONField(name = "COOPERATION_NO")
    @Field(type = FieldType.Keyword)
    private String cooperationNo;

    @JSONField(name = "SALES_SOURCE_INDICATOR")
    @Field(type = FieldType.Keyword)
    private String salesSourceIndicator;

    @JSONField(name = "BRAND_ID")
    @Field(type = FieldType.Long)
    private Long brandId;

    @JSONField(name = "AMOUNT")
    @Field(type = FieldType.Double)
    private BigDecimal amount;

    @JSONField(name = "BARCODE")
    @Field(type = FieldType.Keyword)
    private String barcode;

    @JSONField(name = "IP_B_CANCEL_TIME_ORDER_VIP_ID")
    @Field(type = FieldType.Long)
    private Long ipBCancelTimeOrderVipId;

    @JSONField(name = "CREATE_TIME")
    @Field(type = FieldType.Long)
    private Date createTime;

    @JSONField(name = "COOPERATION_MODE")
    @Field(type = FieldType.Keyword)
    private String cooperationMode;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;
}