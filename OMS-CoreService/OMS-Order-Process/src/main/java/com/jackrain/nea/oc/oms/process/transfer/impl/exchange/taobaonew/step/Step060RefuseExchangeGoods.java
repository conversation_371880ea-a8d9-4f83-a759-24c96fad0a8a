package com.jackrain.nea.oc.oms.process.transfer.impl.exchange.taobaonew.step;

import com.google.common.base.Throwables;
import com.jackrain.nea.ip.model.IpCTaobaoProductItem;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OmsTaobaoExchangeRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoExchange;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.st.model.table.StCExchangeStrategyOrderDO;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;

/**
 * @Author: 黄世新
 * @Date: 2020/11/30 9:57 下午
 * @Version 1.0
 */
@Step(order = 60, description = "拒绝换货")
@Slf4j
@Component
public class Step060RefuseExchangeGoods extends BaseTaobaoExchangeProcessStep
        implements IOmsOrderProcessStep<OmsTaobaoExchangeRelation> {
    @Override
    public ProcessStepResult<OmsTaobaoExchangeRelation> startProcess(OmsTaobaoExchangeRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        IpBTaobaoExchange ipBTaobaoExchange = orderInfo.getIpBTaobaoExchange();
        try {
            Long cpCShopId = ipBTaobaoExchange.getCpCShopId();
            StCExchangeStrategyOrderDO stCExchangeStrategyOrderDO = stRpcService.queryExchangeStrategyByShopId(cpCShopId);
            //缺货是否开启
            if (stCExchangeStrategyOrderDO == null) {
                String message = "未查询到该店铺的换货策略";
                ipTaobaoExchangeService.updateExchangeRemarkAndIsTrans(TransferOrderStatus.TRANSFERFAIL.toInteger(), message, ipBTaobaoExchange);
                return new ProcessStepResult<>(StepStatus.FINISHED, message);
            }
            if (ObjectUtils.isEmpty(stCExchangeStrategyOrderDO.getIsOffRefuse())
                    || (stCExchangeStrategyOrderDO.getIsOffRefuse() == 0 && stCExchangeStrategyOrderDO.getIsPriceOtherRefuse() == 0)) {
                String message = "未开启自动拒绝换货策略，处理失败";
                ipTaobaoExchangeService.updateExchangeRemarkAndIsTrans(TransferOrderStatus.TRANSFERFAIL.toInteger(), message, ipBTaobaoExchange);
                return new ProcessStepResult<>(StepStatus.FINISHED, message);
            }
            //勾选了缺货
            if (stCExchangeStrategyOrderDO.getIsOffRefuse() == 1) {
                Integer refuseId = stCExchangeStrategyOrderDO.getDeviationAmtRefuseReasonId();
                String copywriting = stCExchangeStrategyOrderDO.getDeviationAmtRefuseCopywriting();
                if (refuseId == null){
                    refuseId = 4001;
                    copywriting = "库存不足或商品已下架";
                }
                boolean refuse = omsTaobaoExchangeService.refuseExchange(ipBTaobaoExchange, refuseId, copywriting);
                if (refuse) {
                    String message = "缺货拒绝换货成功!";
                    ipTaobaoExchangeService.updateExchangeRemarkAndIsTrans(TransferOrderStatus.TRANSFERRED.toInteger(), message,
                            ipBTaobaoExchange);
                    return new ProcessStepResult<>(StepStatus.FINISHED, message);
                } else {
                    String message = "缺货拒绝换货失败!";
                    ipTaobaoExchangeService.updateExchangeRemarkAndIsTrans(TransferOrderStatus.NOT_TRANSFER.toInteger(), message,
                            ipBTaobaoExchange);
                    return new ProcessStepResult<>(StepStatus.FAILED, message);
                }
            }
            if (stCExchangeStrategyOrderDO.getIsPriceOtherRefuse() == 0) {
                BigDecimal compareAmt = ObjectUtils.isEmpty(stCExchangeStrategyOrderDO.getDeviationAmtRefuse()) ? BigDecimal.ZERO : stCExchangeStrategyOrderDO.getDeviationAmtRefuse();
                //判断价格 查询中间表金额
                IpCTaobaoProductItem exchangeTaobaoProductItem = cpRpcService.selectIpCTaobaoProductItemBySkuId(ipBTaobaoExchange.getExchangeSku());
                IpCTaobaoProductItem boughtTaobaoProductItem = cpRpcService.selectIpCTaobaoProductItemBySkuId(ipBTaobaoExchange.getBoughtSku());
                BigDecimal abs = exchangeTaobaoProductItem.getPrice().subtract(boughtTaobaoProductItem.getPrice()).abs();
                if (abs.compareTo(compareAmt) > 0) {
                    Integer refuseId = stCExchangeStrategyOrderDO.getDeviationAmtRefuseReasonId();
                    String copywriting = stCExchangeStrategyOrderDO.getDeviationAmtRefuseCopywriting();
                    if (refuseId == null){
                        refuseId = 4016;
                        copywriting = "所换商品差价，未协商一致";
                    }
                    boolean refuse = omsTaobaoExchangeService.refuseExchange(ipBTaobaoExchange, refuseId, copywriting);
                    if (refuse) {
                        String message = "金额偏差拒绝换货成功!";
                        ipTaobaoExchangeService.updateExchangeRemarkAndIsTrans(TransferOrderStatus.TRANSFERRED.toInteger(), message,
                                ipBTaobaoExchange);
                        return new ProcessStepResult<>(StepStatus.FINISHED, message);
                    } else {
                        String message = "金额偏差拒绝换货失败!";
                        ipTaobaoExchangeService.updateExchangeRemarkAndIsTrans(TransferOrderStatus.NOT_TRANSFER.toInteger(), message,
                                ipBTaobaoExchange);
                        return new ProcessStepResult<>(StepStatus.FAILED, message);
                    }
                }
            }
            String message = "拒绝换货未处理!";
            ipTaobaoExchangeService.updateExchangeRemarkAndIsTrans(TransferOrderStatus.TRANSFERFAIL.toInteger(), message,
                    ipBTaobaoExchange);
            return new ProcessStepResult<>(StepStatus.FAILED, message);
        } catch (Exception e) {
            ipTaobaoExchangeService.updateExchangeIsTransError(ipBTaobaoExchange, "拒绝换货处理失败"+e.getMessage());
            log.error(LogUtil.format("拒绝换货处理失败:{}", "拒绝换货处理失败"), Throwables.getStackTraceAsString(e));
            String errorMessage = " 拒绝换货处理失败!" + e.getMessage();
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
    }
}
