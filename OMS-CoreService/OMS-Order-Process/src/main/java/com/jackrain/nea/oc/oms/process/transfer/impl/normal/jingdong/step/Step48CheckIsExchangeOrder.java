package com.jackrain.nea.oc.oms.process.transfer.impl.normal.jingdong.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.relation.IpJingdongOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongOrder;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * Description:检查是订单是否为换货单，特征：
 * 1、订单备注内容为：售后返修换新！服务单号：654148117#返修发货#原订单号:102518213275
 * 2、订单总额、应付金额、销售金额都为0
 *
 * <AUTHOR> sunies
 * @since : 2020-04-28
 * create at : 2020-04-28 17:27
 */
@Step(order = 35, description = "检查是订单是否为换货单")
@Slf4j
@Component
public class Step48CheckIsExchangeOrder extends BaseJingdongOrderProcessStep implements IOmsOrderProcessStep<IpJingdongOrderRelation> {
    @Override
    public ProcessStepResult<IpJingdongOrderRelation> startProcess(IpJingdongOrderRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        IpBJingdongOrder order = orderInfo.getJingdongOrder();
        String orderRemark = order.getOrderRemark();
        //订单总额 orderTotalPrice、订单应付金额orderPayment、订单销售金额orderSellerPrice
        BigDecimal amount = order.getOrderTotalPrice().add(order.getOrderPayment()).add(order.getOrderSellerPrice());
        if (amount.compareTo(BigDecimal.ZERO) == 0 && StringUtils.isNotEmpty(orderRemark) && orderRemark.contains("售后返修换新")) {
            orderInfo.setIsExchange(true);
        }
        return new ProcessStepResult<>(StepStatus.SUCCESS, "京东换货单校验成功，进入下一阶段,判断订单中的明细条码是否在【条码档案】中存在且启用", Step50CheckOmsOrderItemSkuExist.class);
    }
}
