package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

/**
 * 残次策略明细
 *
 * <AUTHOR>
 */
@Data
@TableName("st_c_imperfect_strategy_item")
public class StCImperfectStrategyItem extends BaseModel {

    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 策略ID
     */
    @JSONField(name = "IMPERFECT_STRATEGY_ID")
    private Long imperfectStrategyId;

    /**
     * 指定规则(1.平台商品ID,2.SKU编码+商品标题)
     */
    @JSONField(name = "APPOINT_RULE")
    private Integer appointRule;

    /**
     * 识别内容
     */
    @JSONField(name = "APPOINT_CONTENT")
    private String appointContent;

}
