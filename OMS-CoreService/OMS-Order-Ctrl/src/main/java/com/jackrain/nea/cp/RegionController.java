package com.jackrain.nea.cp;

import com.jackrain.nea.cpext.model.ProvinceCityAreaInfo;
import com.jackrain.nea.cp.services.RegionNewService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: 易邵峰
 * @since: 2019-04-16
 * create at : 2019-04-16 16:31
 */
@RestController
@RequestMapping("/p")
public class RegionController {
    @Autowired
    private RegionNewService regionService;

    /**
     * 查找区域信息
     *
     * @param provinceName 区域类型
     * @param cityName     区域名称
     * @param areaName     父级区域Id
     * @return 区域信息
     */
    @RequestMapping("/selectRegion")
    public ProvinceCityAreaInfo selectProd(@RequestParam("provinceName") String provinceName,
                                           @RequestParam("cityName") String cityName,
                                           @RequestParam("areaName") String areaName) {
        return regionService.selectProvinceCityAreaInfo(provinceName, cityName, areaName);
    }


}
