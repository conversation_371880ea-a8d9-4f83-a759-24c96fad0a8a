package com.jackrain.nea.oc.oms.model.enums;


import java.util.Objects;

/**
 * 是否启用
 *
 * @author: ming.fz
 * create at: 2019/7/23
 */
public enum ToDRPStatusEnum {


    /**
     * 是否可用
     */
    NOT("未传DRP", "0"),
    PASSING("传DRP中", "1"),
    SUCCESS("传DRP成功", "2"),
    FAIL("传DRP失败", "3");

    String code;
    String text;

    ToDRPStatusEnum(String text, String code) {
        this.text = text;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public String getText() {
        return text;
    }

    public static String getTextByCode(String v) {
        for (ToDRPStatusEnum c : ToDRPStatusEnum.values()) {
            if (Objects.equals(v, c.getCode())) {
                return c.getText();
            }
        }
        return "";
    }

}


