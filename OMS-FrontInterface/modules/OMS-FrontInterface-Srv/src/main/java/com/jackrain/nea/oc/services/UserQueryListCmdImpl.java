package com.jackrain.nea.oc.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.UserQueryListCmd;
import com.jackrain.nea.oc.oms.services.UserQueryListService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class UserQueryListCmdImpl extends CommandAdapter implements UserQueryListCmd {

    @Autowired
    private UserQueryListService userQueryListService;

    @Override
    public ValueHolder getTableQuery(String obj, User userWeb) throws NDSException {
        return userQueryListService.getTableQuery(obj, userWeb);
    }

}
