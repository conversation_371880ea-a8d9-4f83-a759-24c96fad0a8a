package com.jackrain.nea.oc.oms.mq.processor.impl.transfer.refund;

import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
import com.jackrain.nea.oc.oms.mq.processor.impl.transfer.normal.StandPlatformTransferMqOrderDetailProcessor;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Profile;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * @ClassName StandPlatformTransferMqOrderDetailProcessorTest
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/7/14 14:08
 * @Version 1.0
 */

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest()
@Profile(value = "dev")
public class StandPlatformTransferMqOrderDetailProcessorTest {

    @Autowired
    private StandPlatformTransferMqOrderDetailProcessor standPlatformTransferMqOrderDetailProcessor;

    @Test
    public void testStandPlatformTransferMqOrderDetailProcessor() {
        OperateOrderMqInfo operateOrderMqInfo = new OperateOrderMqInfo();
        operateOrderMqInfo.setOrderNo("LPK202207151049046281034");
        standPlatformTransferMqOrderDetailProcessor.start(operateOrderMqInfo);
    }
}
