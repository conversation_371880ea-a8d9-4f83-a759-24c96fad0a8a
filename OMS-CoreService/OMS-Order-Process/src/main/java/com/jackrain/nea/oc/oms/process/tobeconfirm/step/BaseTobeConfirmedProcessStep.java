package com.jackrain.nea.oc.oms.process.tobeconfirm.step;

import com.jackrain.nea.oc.oms.config.ToBeConfirmedOrderMqConfig;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.services.task.OmsToBeConfirmedTaskService;
import com.jackrain.nea.oc.oms.util.LockOrderType;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @author: 易邵峰
 * @since: 2019-03-21
 * create at : 2019-03-21 17:31
 */
public abstract class BaseTobeConfirmedProcessStep {

//    @Autowired
//    protected RedisLockOrderUtil redisUtil;

    protected LockOrderType getCurrentLockOrderType() {
        return LockOrderType.UNCONFIRMED_ORDER;
    }

    @Autowired
    protected OmsToBeConfirmedTaskService omsToBeConfirmedTaskService;

    @Autowired
    protected OmsOrderService omsOrderService;
}
