package com.jackrain.nea.oc.oms.model.result;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.oc.oms.model.table.OcBInvoiceNotice;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: huang.z<PERSON><PERSON>
 * @Date: 2019-07-24 20:00
 * @Version 1.0
 */
//订单主表
@Data
public class OcBInvoiceNoticeResult extends OcBInvoiceNotice implements Serializable {

    @JSONField(name = "INVOICE_TYPE_NAME")
    private String invoiceTypeName;

    @JSONField(name = "HEADER_TYPE_NAME")
    private String headerTypeName;

    @JSONField(name = "ESTATUS_NAME")
    private String estatusName;

    @JSONField(name = "INVOICE_ORDER")
    private String invoiceOrder;

    @J<PERSON><PERSON><PERSON>(name = "UNIT_NAME")
    private String unitName;

    @J<PERSON><PERSON>ield(name = "PS_C_PRO_ENAME")
    private String psCProEname;

    @J<PERSON><PERSON>ield(name = "QTY")
    private String qty;

    @J<PERSON><PERSON><PERSON>(name = "AMT_TAXABLE")
    private String amtTaxable;

    @J<PERSON><PERSON>ield(name = "ERR_MSG")
    private String errMsg;
}
