package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;

@TableName(value = "ip_b_taobao_order_promotion")
@Data
@Document(index = "ip_b_taobao_order_promotion", type = "ip_b_taobao_order_promotion")
public class IpBTaobaoOrderPromotion extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "OID")
    @Field(type = FieldType.Long)
    private Long oid;

    @JSONField(name = "PROMOTION_NAME")
    @Field(type = FieldType.Keyword)
    private String promotionName;

    @JSONField(name = "DISCOUNT_FEE")
    @Field(type = FieldType.Double)
    private BigDecimal discountFee;

    @JSONField(name = "GIFT_ITEM_NAME")
    @Field(type = FieldType.Keyword)
    private String giftItemName;

    @JSONField(name = "GIFT_ITEM_ID")
    @Field(type = FieldType.Keyword)
    private String giftItemId;

    @JSONField(name = "GIFT_ITEM_NUM")
    @Field(type = FieldType.Long)
    private Long giftItemNum;

    @JSONField(name = "PROMOTION_DESC")
    @Field(type = FieldType.Keyword)
    private String promotionDesc;

    @JSONField(name = "PROMOTION_ID")
    @Field(type = FieldType.Keyword)
    private String promotionId;

    @JSONField(name = "IP_B_TAOBAO_ORDER_ID")
    @Field(type = FieldType.Long)
    private Long ipBTaobaoOrderId;

    @JSONField(name = "TEMPORARYID")
    @Field(type = FieldType.Keyword)
    private String temporaryid;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;
}