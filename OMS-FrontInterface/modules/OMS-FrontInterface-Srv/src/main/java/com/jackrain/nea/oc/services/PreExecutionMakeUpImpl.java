package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.PreExecutionMakeUpCmd;
import com.jackrain.nea.oc.oms.services.PreExecutionMakeUpOrdersService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: hly
 * @since: 2020/04/19
 * create at : 2020/04/19 21:08
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class PreExecutionMakeUpImpl implements PreExecutionMakeUpCmd {

    @Autowired
    private PreExecutionMakeUpOrdersService preExecutionMakeUpOrdersService;

    @Override
    public ValueHolderV14 execute(JSONObject obj, User user) throws NDSException {
        return preExecutionMakeUpOrdersService.preExecutionMakeUp(obj, user);
    }
}
