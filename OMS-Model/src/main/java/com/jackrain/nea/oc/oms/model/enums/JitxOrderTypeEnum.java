package com.jackrain.nea.oc.oms.model.enums;

/**
 * <AUTHOR>
 * @Date 2025/3/5 15:17
 * @Description
 */
public enum JitxOrderTypeEnum {

    ORDINARY(1, "普通JITX"),
    EXCHANGE(2, "换货JITX"),
    PAYMENT(3, "预付JITX"),
    CALL_FOR_EXCHANGE(4, "揽换JITX"),
    REDELIVER_TRANSPORT(5, "补寄JITX");

    Integer key;
    String val;

    JitxOrderTypeEnum(Integer k, String v) {
        this.key = k;
        this.val = v;
    }

    public Integer getKey() {
        return key;
    }

    public String getVal() {
        return val;
    }
}
