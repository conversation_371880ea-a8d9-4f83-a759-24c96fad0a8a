package com.jackrain.nea.oc.oms.config;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import lombok.Data;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
public class OmsModifyOrderLogisticsConfig {

    @NacosValue(value = "${r3.oms.oc.modify.logistics.mq.tag:}", autoRefreshed = true)
    private String modifyTag;

    @NacosValue(value = "${r3.oms.oc.modify.logistics.mq.topic:}", autoRefreshed = true)
    private String modifyTopic;

}
