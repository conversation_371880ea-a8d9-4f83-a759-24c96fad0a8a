package com.jackrain.nea.oc.oms.strategy;

import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.st.model.result.BaseResult;
import com.jackrain.nea.web.face.User;
import org.springframework.beans.factory.InitializingBean;

import java.util.List;

/**
 * <AUTHOR>
 * @Description: 拆单策略接口类
 * @date 2020/6/28 15:36
 */
public interface IOrderSplitStrategy extends InitializingBean {

    // 根据拆单策略进行拆单
    List<OcBOrderRelation> split(OcBOrderRelation ocBOrderRelation, User user, BaseResult strategy);

}
