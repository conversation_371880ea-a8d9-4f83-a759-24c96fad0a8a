package com.jackrain.nea.oc.oms.mq.processor.impl.transfer.normal;

import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.OperateType;
import com.jackrain.nea.oc.oms.model.enums.OrderType;
import com.jackrain.nea.oc.oms.model.relation.IpAlibabaAscpOrderRelation;
import com.jackrain.nea.oc.oms.mq.processor.IMqOrderDetailProcessor;
import com.jackrain.nea.oc.oms.process.transfer.impl.normal.alibabaAscp.AlibabaAscpTransferOrderProcessImpl;
import com.jackrain.nea.oc.oms.services.IpAlibabaAscpOrderService;
import com.jackrain.nea.oc.oms.step.ProcessStepResultList;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 天猫超市直发转单消息处理器
 * <p>
 * 2020-11-11易邵峰检查
 *
 * <AUTHOR>
 * @date 2020/9/3 下午2:02
 * @description 天猫超市直发转单消息处理器
 **/
@Slf4j
public class AlibabaAscpTransferMqOrderDetailProcessor implements IMqOrderDetailProcessor {

    @Autowired
    private IpAlibabaAscpOrderService ipAlibabaAscpOrderService;

    @Autowired
    private AlibabaAscpTransferOrderProcessImpl alibabaAscpTransferOrderProcess;

    @Override
    public ProcessStepResultList start(OperateOrderMqInfo orderMqInfo) {
        String orderNo = orderMqInfo.getOrderNo();

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("AlibabaAscpTransferMqOrderDetailProcessor.Start", orderNo));
        }

        IpAlibabaAscpOrderRelation orderRelation = this.ipAlibabaAscpOrderService.selectAlibabaAscpOrder(orderNo);
        if (orderRelation == null || orderRelation.getAlibabaAscpOrder() == null) {
            log.error(LogUtil.format("AlibabaAscpTransferMqOrderDetailProcessor.Error.Not.Exist", orderNo));
            return new ProcessStepResultList();
        } else {
            ProcessStepResultList resultList = alibabaAscpTransferOrderProcess.start(orderRelation,
                    false, SystemUserResource.getRootUser());
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("AlibabaAscpTransferMqOrderDetailProcessor.Finish", orderNo));
            }
            return resultList;
        }
    }

    @Override
    public boolean checkMqIsCanExecute(OperateOrderMqInfo orderMqInfo) {
        return orderMqInfo.getOperateType() == OperateType.TRANSFER_ORDER
                && orderMqInfo.getChannelType() == ChannelType.ALIBABAASCP
                && orderMqInfo.getOrderType() == OrderType.NORMAL;
    }
}
