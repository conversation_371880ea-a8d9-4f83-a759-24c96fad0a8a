package com.jackrain.nea.oc.oms.process.transfer.impl.normal.jitx;

import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.MakeupOrderType;
import com.jackrain.nea.oc.oms.model.relation.IpJitxOrderRelation;
import com.jackrain.nea.oc.oms.process.AbstractOrderProcess;
import com.jackrain.nea.oc.oms.util.LockOrderType;
import org.springframework.stereotype.Component;

/**
 * 订单中间表转成待分配订单服务
 * <p>
 * 完成从平台订单到订单的转换
 *
 * @author: 黄超
 * @since: 2019-06-26
 * create at : 2019-06-26 19:00
 */
@Component
public class JitxTransferOrderProcessImpl extends AbstractOrderProcess<IpJitxOrderRelation> {

    public JitxTransferOrderProcessImpl() {
        super();
    }

    @Override
    protected String getChildPackageName() {
        return "jitx";
    }

    @Override
    protected long getProcessOrderId(IpJitxOrderRelation orderInfo) {
        return orderInfo.getOrderId();
    }

    @Override
    protected String getProcessOrderNo(IpJitxOrderRelation orderInfo) {
        return orderInfo.getOrderNo();
    }

    @Override
    protected LockOrderType getCurrentLockOrderType() {
        return LockOrderType.TRANSFER_JITX_ORDER;
    }

    @Override
    protected ChannelType getCurrentChannelType() {
        return ChannelType.VIPJITX;
    }

    @Override
    protected MakeupOrderType getCurrentMakeupOrderType() {
        return MakeupOrderType.DO_NOT_MAKEUP;
    }


}
