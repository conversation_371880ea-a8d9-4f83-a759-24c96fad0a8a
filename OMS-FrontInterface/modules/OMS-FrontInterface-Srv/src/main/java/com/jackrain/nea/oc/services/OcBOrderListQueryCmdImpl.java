package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBOrderListQueryCmd;
import com.jackrain.nea.oc.oms.model.relation.UserPermission;
import com.jackrain.nea.oc.oms.model.result.QueryEsListResult;
import com.jackrain.nea.oc.oms.model.result.QueryOrderByBillNoResult;
import com.jackrain.nea.oc.oms.model.result.QueryOrderListResult;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.services.OcBOrderListQueryService;
import com.jackrain.nea.oc.oms.services.QueryOrderListService;
import com.jackrain.nea.oc.oms.services.QueryOrderService;
import com.jackrain.nea.oc.request.OcBOederQueryByBillNoRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 订单管理:列表-列表查询
 *
 * @author: xiwen.z
 * create at: 2019/3/5 0005
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBOrderListQueryCmdImpl implements OcBOrderListQueryCmd {
    @Autowired
    private OcBOrderListQueryService ocBOrderListQueryService;

    @Autowired
    private QueryOrderListService queryOrderListService;

    @Autowired
    private QueryOrderService queryOrderService;


    @Override
    public ValueHolderV14<QueryOrderListResult> queryOrderList(String jsonStr, User loginUser, UserPermission pem) {
        return ocBOrderListQueryService.queryOrderList(jsonStr, loginUser, pem);
    }

    @Override
    public ValueHolderV14 queryOrderList(User usr, String param) {
        ValueHolderV14 vh = queryOrderListService.queryOrderList(usr, param);
        return vh;
    }

    @Override
    public List<OcBOrder> queryOrder(User usr, String param) {
        JSONObject jsonObject = JSONObject.parseObject(param);
        if (StringUtils.isBlank(jsonObject.getString("sourceCode"))) {
            throw new NDSException("平台单号不能为空! ");
        }
        List<OcBOrder> ocBOrderList = queryOrderService.queryOrderList(jsonObject.getString("sourceCode"));
        if (CollectionUtils.isEmpty(ocBOrderList)) {
            throw new NDSException("查询不到零售发货单信息! ");
        }
        return ocBOrderList;
    }

    @Override
    public ValueHolderV14<QueryOrderByBillNoResult> queryOrderByBillNo(OcBOederQueryByBillNoRequest request) {

        return ocBOrderListQueryService.queryOrderByBillNoList(request);
    }

    @Override
    public OcBOrder queryOrderEsByBillNo(String request) {
        return ocBOrderListQueryService.getOrderByBillNo(request);
    }

    @Override
    public QueryEsListResult queryOrderEsList(User usr, String param) {
        QueryEsListResult orderIdsFromEs = queryOrderListService.getOrderIdsFromEs(usr, param);
        return orderIdsFromEs;
    }
}
