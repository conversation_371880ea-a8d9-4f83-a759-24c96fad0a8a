package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoFxOrderItem;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoFxOrderItemExt;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;

@Mapper
@Component
public interface IpBTaobaoFxOrderItemMapper extends ExtentionMapper<IpBTaobaoFxOrderItem> {

    @Select("SELECT * FROM ip_b_taobao_fx_order_item WHERE ip_b_taobao_fx_order_id=#{orderId}")
    List<IpBTaobaoFxOrderItemExt> selectOrderItemList(@Param("orderId") long orderId);
}