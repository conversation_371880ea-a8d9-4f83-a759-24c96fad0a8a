package com.jackrain.nea.oc.oms.model.result;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName Pod2BOrderQueryResult
 * @Description 结果
 * <AUTHOR>
 * @Date 2024/8/29 16:45
 * @Version 1.0
 */
@Data
public class Pod2BOrderQueryResult implements Serializable {
    private static final long serialVersionUID = 4725395912626439531L;

    /**
     * 第几页
     */
    private Integer pageNum;

    /**
     * 每页数量
     */
    private Integer pageSize;

    /**
     * 总数量
     */
    private Integer total;

    /**
     * 数据
     */
    private List<Pod2BOrderQueryDataResult> data;
}
