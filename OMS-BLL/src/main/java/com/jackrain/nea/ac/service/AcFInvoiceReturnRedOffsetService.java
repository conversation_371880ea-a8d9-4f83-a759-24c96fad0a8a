package com.jackrain.nea.ac.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.core.db.Tools;
import com.jackrain.nea.hub.api.HubInvoicingCmd;
import com.jackrain.nea.hub.model.HXInvoicingModel.JsonRootBean;
import com.jackrain.nea.hub.model.HXInvoicingModel.ResultType;
import com.jackrain.nea.oc.oms.dto.invoice.ReturnRedOffsetDTO;
import com.jackrain.nea.oc.oms.mapper.ac.*;
import com.jackrain.nea.oc.oms.model.constant.InvoiceConst;
import com.jackrain.nea.oc.oms.model.table.*;
import com.jackrain.nea.oc.oms.services.invoice.InvoiceLogService;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.TransactionUtils;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName AcFInvoiceReturnRedOffsetService
 * @Description
 * @Date 2022/9/14 下午4:29
 * @Created by wuhang
 */
@Slf4j
@Component
public class AcFInvoiceReturnRedOffsetService {

    @Autowired
    private AcFInvoiceReturnRedOffsetMapper redOffsetMapper;

    @Autowired
    private AcFOrderInvoiceMapper orderInvoiceMapper;

    @Autowired
    private AcFInvoiceApplyMapper invoiceApplyMapper;

    @Autowired
    private AcFInvoiceReturnRedOffsetItemMapper redOffsetItemMapper;

    @Autowired
    private AcFOrderInvoiceSystemItemMapper orderInvoiceSystemItemMapper;

    @Autowired
    private AcFInvoiceApplyItemMapper invoiceApplyItemMapper;

    @Autowired
    private OrderInvoiceService orderInvoiceService;

    @Reference(group = "hub", version = "1.0")
    private HubInvoicingCmd hubInvoicingCmd;

    @Autowired
    private OrderInvoiceChangeTaskService changeTaskService;

    @Autowired
    private InvoiceApplyService invoiceApplyService;

    @Autowired
    private InvoiceLogService logService;

    /**
     * 退货待红冲表标记转换成功
     * @param ids
     * @param user
     * @return
     */
    public ValueHolder markCHangeSuccess(JSONArray ids, User user) {
        log.info("---| markCHangeSuccess param:" + ids + ", user:" + JSON.toJSONString(user));
        ValueHolder resp = new ValueHolder();
        UpdateWrapper<AcFInvoiceReturnRedOffset> update = new UpdateWrapper<>();
        update.lambda().in(AcFInvoiceReturnRedOffset::getId,ids)
                .set(AcFInvoiceReturnRedOffset::getChangeStatus, InvoiceConst.ReturnRedOffsetChangeStatus.CHANGE_SUCCESS)
                .set(AcFInvoiceReturnRedOffset::getFailReason,"")
                .set(AcFInvoiceReturnRedOffset::getModifieddate, new Date())
                .set(AcFInvoiceReturnRedOffset::getModifierid, Long.valueOf(user.getId()))
                .set(AcFInvoiceReturnRedOffset::getModifierename, user.getEname())
                .set(AcFInvoiceReturnRedOffset::getModifiername, user.getName());
        redOffsetMapper.update(null,update);
        resp.put("code", ResultCode.SUCCESS);
        resp.put("message","标记转换成功");
        return resp;
    }

    /**
     * 退货待红冲手工转换
     * @param ids
     * @param user
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolder manualChange(JSONArray ids, User user) {
        log.info("---| invoice | red rush param : " + ids + ", user : " + JSON.toJSONString(user));
        ValueHolder vh = new ValueHolder();
        JSONArray errorMessage = new JSONArray(); // 错误信息
        int fail = 0;
        // 获取所有退单待红冲数据
        List<Long> idList = ids.toJavaList(Long.class);
        List<AcFInvoiceReturnRedOffset> redOffsetList = redOffsetMapper.selectByIds(idList);
//        List<AcFInvoiceReturnRedOffsetItem> redOffsetItemList = redOffsetItemMapper.selectByReturnRedOffsetIds(idList);
//        Map<Long, List<AcFInvoiceReturnRedOffsetItem>> redOffsetItemMap = redOffsetItemList.stream().collect(Collectors.groupingBy(AcFInvoiceReturnRedOffsetItem::getInvoiceReturnRedOffsetId));
        for(AcFInvoiceReturnRedOffset redOffset : redOffsetList){
            JSONObject jsonObject = new JSONObject();
            String msg = checkManualChange(redOffset);
            if (StringUtils.isNotBlank(msg)) {
                record(redOffset.getId(),false,msg,user);
                vh.put("code", ResultCode.FAIL);
                vh.put("message", msg);
                jsonObject.put("code", ResultCode.FAIL);
                jsonObject.put("message", msg);
                jsonObject.put("objid", redOffset.getId());
                errorMessage.add(jsonObject);
                fail++;
                continue;
            }
            // 如果没有开票数据, 则根据tid查询发票申请表, 如果申请表的状态为失败, 则将此条记录转换
            List<Long> orderInvoiceIdList = orderInvoiceSystemItemMapper.selectByTids(Lists.newArrayList(redOffset.getTid()));
            if(CollectionUtils.isEmpty(orderInvoiceIdList)){
                AcFInvoiceApplyItem invoiceApplyItem = invoiceApplyItemMapper.selectByTid(redOffset.getTid());
                if(Objects.nonNull(invoiceApplyItem)){
                    AcFInvoiceApply invoiceApply = invoiceApplyMapper.selectById(invoiceApplyItem.getAcFInvoiceApplyId());
                    // 如果查询到的发票申请状态是失败,则将该发票重新转换
                    if(Objects.nonNull(invoiceApply) && InvoiceConst.TransStatus.TRANS_FAIL.compareTo(invoiceApply.getTransStatus()) == 0){
                        ValueHolderV14<StCInvoiceStrategy> stCInvoiceStrategyValueHolderV14 = changeTaskService.changeApplyInvoice(null, null, null, null, null, invoiceApply, user);
                        if(stCInvoiceStrategyValueHolderV14.isOK()){
                            record(redOffset.getId(),true,"",user);
                            vh.put("code", ResultCode.SUCCESS);
                            vh.put("message", "手工转换申请成功");
                            continue;
                        } else {
                            record(redOffset.getId(),false,stCInvoiceStrategyValueHolderV14.getMessage(),user);
                            vh.put("code", ResultCode.FAIL);
                            vh.put("message", stCInvoiceStrategyValueHolderV14.getMessage());
                            continue;
                        }
                    } else {
                        record(redOffset.getId(), false, "该订单未开票", user);
                        vh.put("code", ResultCode.FAIL);
                        vh.put("message", "手工转换申请失败");
                        continue;
                    }
                } else {
                    record(redOffset.getId(), false, "该订单未开票", user);
                    vh.put("code", ResultCode.FAIL);
                    vh.put("message", "手工转换申请失败");
                    continue;
                }
            }
            // 根据开票订单明细tid查询未取消,未红冲的蓝票
            List<AcFOrderInvoice> invoiceList = orderInvoiceMapper.selectBlueByIds(orderInvoiceIdList);
            if(CollectionUtils.isEmpty(invoiceList)){
                record(redOffset.getId(),false,"该订单未开票",user);
                vh.put("code", ResultCode.FAIL);
                vh.put("message", "手工转换申请失败");
                continue;
            }
            Optional<AcFOrderInvoice> invoice = invoiceList.stream().sorted(Comparator.comparing(AcFOrderInvoice::getId).reversed()).findFirst();
            AcFOrderInvoice orderInvoice = invoice.get();
            // 判断是整单退还是部分退，根据退款金额和开票金额判断
            boolean isAllReturn  = false;

            if(redOffset.getRefundMoney().compareTo(orderInvoice.getInvoiceInclusiveTaxAmt()) >= 0){
                isAllReturn = true;
            }
            BigDecimal otherAmt = orderInvoice.getInvoiceInclusiveTaxAmt().subtract(redOffset.getRefundMoney());
            String errMsg = redOffsetManualChange(orderInvoice,user,isAllReturn,otherAmt);
            if(StringUtils.isNotBlank(errMsg)){
                vh.put("code", ResultCode.FAIL);
                vh.put("message", msg);
                jsonObject.put("code", ResultCode.FAIL);
                jsonObject.put("message", msg);
                jsonObject.put("objid", redOffset.getId());
                errorMessage.add(jsonObject);
                fail++;
                record(redOffset.getId(),false,errMsg,user);
                continue;
            }
            record(redOffset.getId(),true,errMsg,user);
            vh.put("code", ResultCode.SUCCESS);
            vh.put("message", "手工转换申请成功");
        }
        if (ids.size() == 1) {
            return vh;
        }
        if (fail == 0) {
            vh.put("code", ResultCode.SUCCESS);
            vh.put("message", "手工转换申请成功");
        } else {
            vh.put("data", errorMessage);
            vh.put("code", ResultCode.FAIL);
            vh.put("message", "手工转换申请成功" + (ids.size() - fail) + "条，失败" + fail + "条数据");
        }
        return vh;
    }

    /**
     * 退单待红冲手工转换
     * @param orderInvoice
     * @param user
     * @return
     */
    private String redOffsetManualChange(AcFOrderInvoice orderInvoice, User user,boolean isAllReturn,BigDecimal otherAmt) {
        Long redTicketId = null;
        if(InvoiceConst.InvoiceStatus.NOT_INVOICE.equals(orderInvoice.getInvoiceStatus()) || InvoiceConst.InvoiceStatus.INVOICE_FAIL.equals(orderInvoice.getInvoiceStatus())){
            // 未开票,将原发票取消
            orderInvoiceService.cancelOrderInvoice(orderInvoice.getId(),user,null);
            logService.addUserOrderLog(orderInvoice.getId(),"退货手工转换","退货待红冲-取消发票",user);
        } else if (InvoiceConst.InvoiceStatus.IN_INVOICE.equals(orderInvoice.getInvoiceStatus())){
            // 开票中
            // 先调用金税撤销接口,如果成功,修改开票状态已取消,记录时间,日志,如果失败,记录原因
            ValueHolderV14 result = invoiceRevoke(orderInvoice, user,"退货手工转换");
            if(!result.isOK()){
                return result.getMessage();
            }
        } else if (InvoiceConst.InvoiceStatus.INVOICE_SUCCESS.equals(orderInvoice.getInvoiceStatus())){
            // 开票成功,生成一张红票
            ValueHolder result = changeTaskService.createRedByBlue(orderInvoice, user, InvoiceConst.AuditStatus.AUDITED);
            if(!result.isOK()){
                return result.get("message").toString();
            }
            Long originId = (Long) result.get("originId");
            redTicketId = (Long) result.get("redTicketId");
            String isDelete = result.get("isDelete").toString();
            // 将原蓝票红冲状态修改为红冲中
            AcFOrderInvoice update = new AcFOrderInvoice();
            update.setId(originId);
            if(StringUtils.isNotBlank(isDelete) && "1".equals(isDelete)){
                update.setIsactive("N");// 作废
            } else {
                update.setRedRushStatus(InvoiceConst.RedRushStatus.IN_RED_RUSH);// 红冲中
            }
            update.setModifierename(user.getEname());
            update.setModifiername(user.getEname());
            update.setModifierid(Long.valueOf(user.getId().toString()));
            update.setModifieddate(new Date());
            orderInvoiceMapper.updateById(update);
            Long applyId = orderInvoice.getInvoiceApplyId();
            if (StringUtils.isNotBlank(isDelete) && "1".equals(isDelete) && Objects.nonNull(applyId) && applyId.compareTo(0L) > 0 && isAllReturn) {
                invoiceApplyService.updateInvoiceApplyItemTidSuffix(applyId);
            }
            logService.addUserOrderLog(originId,"退单手工转换","发票红冲中",user);
        }
        // 根据订单发票生成一个申请
        if(!isAllReturn) {
            orderInvoiceService.createApplyByOrderInvoice(Lists.newArrayList(orderInvoice), user,redTicketId, false,otherAmt);
        }
        return "";
    }

    /**
     * 退单待红冲手工转换校验
     * @param redOffset 待红冲数据
     * @return
     */
    private String checkManualChange(AcFInvoiceReturnRedOffset redOffset) {
        if(Objects.isNull(redOffset)){
            return "记录不存在";
        }
        if(InvoiceConst.ReturnRedOffsetChangeStatus.CHANGE_SUCCESS.equals(redOffset.getChangeStatus())){
            return "状态已转换不允许单据转换！";
        }
        return "";
    }

    private void record(Long id, boolean success, String msg, User user){
        Date now = new Date();
        UpdateWrapper<AcFInvoiceReturnRedOffset> update = new UpdateWrapper<>();
        update.lambda().eq(AcFInvoiceReturnRedOffset::getId,id)
                .set(AcFInvoiceReturnRedOffset::getChangeStatus,success ? InvoiceConst.ReturnRedOffsetChangeStatus.CHANGE_SUCCESS : InvoiceConst.ReturnRedOffsetChangeStatus.CHANGE_FAIL)
                .set(AcFInvoiceReturnRedOffset::getFailReason,msg)
                .set(AcFInvoiceReturnRedOffset::getChangeDate,now)
                .set(AcFInvoiceReturnRedOffset::getChangeUser,Long.valueOf(user.getId()))
                .set(AcFInvoiceReturnRedOffset::getModifieddate,now)
                .set(AcFInvoiceReturnRedOffset::getModifierename,user.getEname())
                .set(AcFInvoiceReturnRedOffset::getModifierid,Long.valueOf(user.getId()))
                .set(AcFInvoiceReturnRedOffset::getModifiername,user.getName());
        redOffsetMapper.update(null,update);
    }

    /**
     * 数据写入退货待红冲发票表
     * @param dto
     */
    public void createRedOffserRecord(ReturnRedOffsetDTO dto){
        if(Objects.isNull(dto)){
            return;
        }
        long count = redOffsetMapper.countByBillNo(dto.getBillNo());
        if(count > 0){
            log.info("---| bill no:"+dto.getBillNo()+"重复");
            return;
        }
        User user = SystemUserResource.getRootUser();
        AcFInvoiceReturnRedOffset offset = new AcFInvoiceReturnRedOffset();
        appendOwnerUserInfo(offset,user);
        Long id = Tools.getSequence(InvoiceConst.AC_F_INVOICE_RETURN_RED_OFFSET);
        offset.setId(id);
        BeanUtils.copyProperties(dto,offset);
        List<AcFInvoiceReturnRedOffsetItem> redOffsetItems = new ArrayList<>();
        for(ReturnRedOffsetDTO.ReturnOffsetItemDTO itemDTO : dto.getItemList()){
            AcFInvoiceReturnRedOffsetItem item = new AcFInvoiceReturnRedOffsetItem();
            BeanUtils.copyProperties(itemDTO,item);
            item.setId(Tools.getSequence(InvoiceConst.AC_F_INVOICE_RETURN_RED_OFFSET_ITEM));
            item.setAcFInvoiceReturnRedOffsetId(id);
            appendOwnerUserInfo(item,user);
            redOffsetItems.add(item);
        }
        redOffsetMapper.insert(offset);
        redOffsetItemMapper.batchInsert(redOffsetItems);
        // 事务提交后自动执行转换一次
        log.info("createRedOffserRecord.id:{}", id);
        orderInvoiceService.autoChange(id, user);
    }

    /**
     * 撤销开票中的发票
     * @param orderInvoice
     * @return
     */
    public ValueHolderV14 invoiceRevoke(AcFOrderInvoice orderInvoice, User user,String option){
        ValueHolderV14 resp = new ValueHolderV14();
        if(Objects.isNull(orderInvoice)){
            resp.setCode(ResultCode.FAIL);
            resp.setMessage("记录不存在");
            return resp;
        }
        if(!InvoiceConst.InvoiceStatus.IN_INVOICE.equals(orderInvoice.getInvoiceStatus())){
            resp.setCode(ResultCode.FAIL);
            resp.setMessage("当前发票开票状态不可撤销!");
            return resp;
        }
        JsonRootBean param = new JsonRootBean();
        param.setDocNum(orderInvoice.getBillNo());
        ResultType resultType = null;
        try {
            log.info("---| invoice revoke , param :" + JSON.toJSONString(param));
            resultType = hubInvoicingCmd.invoiceRevoke(param);
            log.info("---| invoice revoke , result :" + JSON.toJSONString(resultType));
        } catch (Exception e){
            resp.setCode(ResultCode.FAIL);
            resp.setMessage("发票撤销失败," + e.getLocalizedMessage());
            return resp;
        }
        Boolean success = Boolean.valueOf(resultType.getSuccess());
        if(success){
            // 撤销成功,发票状态更新为已取消,发票开票状态更新为已撤销
            UpdateWrapper<AcFOrderInvoice> update = new UpdateWrapper<>();
            update.lambda().eq(AcFOrderInvoice::getId, orderInvoice.getId())
                    .set(AcFOrderInvoice::getInvoiceStatus, InvoiceConst.InvoiceStatus.NOT_INVOICE)
                    .set(AcFOrderInvoice::getCancelStatus, InvoiceConst.CancelStatus.CANCELED)
                    .set(AcFOrderInvoice::getModifierid, Long.valueOf(user.getId()))
                    .set(AcFOrderInvoice::getModifierename, user.getEname())
                    .set(AcFOrderInvoice::getModifiername, user.getName())
                    .set(AcFOrderInvoice::getModifieddate, new Date());
            orderInvoiceMapper.update(null, update);
            logService.addUserOrderLog(orderInvoice.getId(),option,"发票撤销",user);
            resp.setCode(ResultCode.SUCCESS);
            resp.setMessage(resultType.getMsg());
        } else {
            // 撤销失败
            resp.setCode(ResultCode.FAIL);
            resp.setMessage("发票撤销失败," + resultType.getMsg());
        }
        return resp;
    }

    /**
     * 添加创建人信息
     * @param t
     * @param user
     * @param <T>
     * @return
     */
    private <T extends BaseModel> T appendOwnerUserInfo(T t, User user){
        t.setAdClientId(Long.valueOf(user.getClientId()));
        t.setAdOrgId(Long.valueOf(user.getOrgId()));
        t.setOwnerid(Long.valueOf(user.getId()));
        t.setIsactive("Y");
        t.setOwnername(user.getName());
        t.setCreationdate(new Date());
        return t;
    }
}
