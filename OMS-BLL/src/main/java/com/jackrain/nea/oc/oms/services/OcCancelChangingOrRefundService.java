package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.store.api.in.SgBStoInNoticesCmd;
import com.burgeon.r3.sg.store.model.request.in.SgBStoInNoticesBillVoidRequest;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.es.util.SpecialElasticSearchUtil;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.es.ES4ReturnOrder;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderLogMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderRefundMapper;
import com.jackrain.nea.oc.oms.mapper.StCBusinessTypeMapper;
import com.jackrain.nea.oc.oms.model.enums.OcBorderListEnums;
import com.jackrain.nea.oc.oms.model.enums.OcOrderCheckBoxEnum;
import com.jackrain.nea.oc.oms.model.enums.OcReturnBillTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderRefundCommonEum;
import com.jackrain.nea.oc.oms.model.enums.ProReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnOrderConfirmStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.WmsWithdrawalState;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderLog;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.oc.oms.model.table.StCBusinessType;
import com.jackrain.nea.oc.oms.nums.InreturningStatus;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Callable;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;


/**
 * 退换货取消服务
 *
 * @author: 夏继超
 * @since: 2019/3/21
 * create at : 2019/3/21 13:44
 */
@Component
@Slf4j
public class OcCancelChangingOrRefundService {
    @Autowired
    OcBReturnOrderMapper ocBReturnOrderMapper;
    @Autowired
    OcBOrderMapper ocBOrderMapper;
    @Autowired
    OcBReturnOrderLogMapper logMapper;
    @Autowired
    IpRpcService ipRpcService;
    @Autowired
    OcBReturnOrderRefundMapper refundMapper;
    @Autowired
    OcBOrderItemMapper itemMapper;
    @Autowired
    private BllRedisLockOrderUtil redisUtil;
    @Autowired
    private SgRpcService sgRpcService;
    @Autowired
    private OcBOrderOffService orderOffService;

    @DubboReference(group = "sg", version = "1.0")
    private SgBStoInNoticesCmd stoInNoticesCmd;
    @Autowired
    private StCBusinessTypeMapper stBusinessTypeMapper;

    @Resource
    private ThreadPoolTaskExecutor doBatchCancelReturnPollExecutor;

    /**
     * 字符串转集合
     *
     * @param param 传入的参数
     * @return 返回的信息
     */
    public static List stringToList(String param) {
        List list = new ArrayList();
        String[] split = param.split(",");
        for (String s : split) {
            list.add(Long.valueOf(s));
        }
        return list;
    }

    /**
     * 检查取消退单订单参数
     *
     * @param idsJson 请求参数
     * @param user    用户信息
     * @return ValueHolderV14
     */
    public ValueHolderV14 checkCancelReqParams(JSONObject idsJson, User user) {

        JSONArray ids = idsJson.getJSONArray("ids");

        if (CollectionUtils.isEmpty(ids)) {
            return new ValueHolderV14<>(ResultCode.FAIL, "请至少选择1条记录！");
        }

        ValueHolderV14 vh;

        String idsStr = StringUtils.join(ids, ",");
        List<OcBReturnOrder> returnOrders = ocBReturnOrderMapper.findByJoin(idsStr);

        if (CollectionUtils.isEmpty(returnOrders)) {
            vh = new ValueHolderV14();
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage("未找到对应的退换单信息", user.getLocale()));
            return vh;
        }

        for (OcBReturnOrder returnOrder : returnOrders) {
            //退换货单点击取消按钮时，会判断该退换货单的业务类型在单据业务类型档案中是否可取消订单=“否”，如果是=“否”，则不可进行取消操作
            if (returnOrder.getBusinessTypeId()!=null){
                StCBusinessType businessType = stBusinessTypeMapper.selectById(returnOrder.getBusinessTypeId());
                if (!YesNoEnum.Y.getKey().equals(businessType.getIsCancelOrder())){
                    vh = new ValueHolderV14();
                    vh.setCode(ResultCode.FAIL);
                    vh.setMessage(Resources.getMessage("该退单业务类型不支持操作取消", user.getLocale()));
                    return vh;
                }
            }

            if (OcReturnBillTypeEnum.EXCHANGE.getVal().equals(returnOrder.getBillType())) {
                if (ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal().equals(returnOrder.getReturnStatus())) {
                    Integer isToWms = returnOrder.getIsTowms();
                    if (WmsWithdrawalState.YES.toInteger().equals(isToWms)) {
                        vh = checkCancelResult(user, returnOrder);
                        if (vh != null) {
                            return vh;
                        }
                    }
                    if (WmsWithdrawalState.NO.toInteger().equals(isToWms) ||
                            WmsWithdrawalState.FAIL.toInteger().equals(isToWms)) {
                        vh = checkCancelResult(user, returnOrder);
                        if (vh != null) {
                            return vh;
                        }
                    }
                }
            }
        }
        vh = new ValueHolderV14();
        vh.setCode(ResultCode.SUCCESS);
        vh.setMessage(Resources.getMessage("参数校验通过", user.getLocale()));
        return vh;
    }

    /**
     * 奇门调用，封装一层
     *
     * @param obj  obj
     * @param user user
     * @return ValueHolderV14
     */
    public ValueHolderV14 qmRefundService(JSONObject obj, User user) {
        log.info(LogUtil.format("QM取消单据入参:{}", "qmRefundService"), obj.toJSONString());

        String orderCode = obj.getString("orderCode");

        if (StringUtils.isEmpty(orderCode)) {
            return new ValueHolderV14(ResultCode.FAIL, "orderCode为空");
        }
        List<OcBReturnOrder> ocReturnOrders = ocBReturnOrderMapper.selectList(new LambdaQueryWrapper<OcBReturnOrder>()
                .eq(OcBReturnOrder::getReturnId, orderCode));
        if (CollectionUtils.isNotEmpty(ocReturnOrders)) {
            List<Long> ids = ocReturnOrders.stream().map(OcBReturnOrder::getId).collect(Collectors.toList());
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("ids", ids);
            return orRefundService(jsonObject, user, Boolean.FALSE);
        }


        return new ValueHolderV14(ResultCode.SUCCESS, "取消成功！");
    }

    /**
     * 退换货服务取消
     *
     * @param obj  传入的参数
     * @param user 当前登录的用户
     * @return
     */
    //@Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 orRefundService(JSONObject obj, User user, Boolean isManual) throws NDSException {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("退单取消入参：{}"), obj.toJSONString());
        }
        ValueHolderV14 vh = new ValueHolderV14();
        JSONArray ids = obj.getJSONArray("ids");
        int fail = 0;
        Map<Object, String> errMap = new HashMap<>();

        OcCancelChangingOrRefundService bean = ApplicationContextHandle.getBean(OcCancelChangingOrRefundService.class);
        if (!ids.isEmpty()) {

            if (ids.size() == 1) {
                try {
                    long beginTime = System.currentTimeMillis();
                    ValueHolderV14 valueHolderV14 = bean.oneOcCancle(user, vh, ids);
                    long endTime = System.currentTimeMillis();
                    log.info(LogUtil.format("OcCancelChangingOrRefundService.orRefundService单个取消耗时") + (endTime - beginTime));
                    return valueHolderV14;
                } catch (Exception e) {
                    log.error(LogUtil.format("OcCancelChangingOrRefundService.orRefundService,error:{}"), Throwables.getStackTraceAsString(e));
                    errMap.put(ids.get(0), e.getMessage());
                }
            } else {
                //批量进行退换货取消
                if (isManual) {
                    // 多线程来优化
                    List<List<Object>> batchCancelIdList = Lists.partition(ids, 50);
                    List<Future<BatchCancelResult>> faildCountList = new ArrayList<>();
                    for (List<Object> batchCancelIds : batchCancelIdList) {
                        faildCountList.add(doBatchCancelReturnPollExecutor.submit(new BatchOcCancel(user, batchCancelIds)));
                    }
                    AtomicInteger failureCount = new AtomicInteger();
                    Map<Object, String> map = new ConcurrentHashMap<>();
                    for (Future<BatchCancelResult> resultFuture : faildCountList) {
                        try {
                            BatchCancelResult cancelResult = resultFuture.get();
                            failureCount.addAndGet(cancelResult.getFailNum());
                            map.putAll(cancelResult.getErrMap());
                        } catch (Exception e) {
                            log.error(LogUtil.format("退换货单批量取消ExecutionException异常：{}"), Throwables.getStackTraceAsString(e));
                        }
                    }
                    errMap = map;
                    fail = failureCount.get();
                } else {
                    // 如果是手动取消的话 可以考虑使用多线程来处理
                    long beginTime = System.currentTimeMillis();
                    for (Object id : ids) {
                        try {
                            fail = bean.batchOcCancel(user, vh, fail, id);
                        } catch (Exception e) {
                            log.error(LogUtil.format("OcCancelChangingOrRefundService.orRefundService,error:{}"), Throwables.getStackTraceAsString(e));
                            errMap.put(id, e.getMessage());
                            fail++;
                        }
                    }
                    long endTime = System.currentTimeMillis();
                    if (log.isDebugEnabled()) {
                        log.debug(LogUtil.format("单个取消耗时{}"), (endTime - beginTime));
                    }
                }


            }
            return this.getVhResult(ids, fail, errMap);
        } else {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage("请选择一条记录", user.getLocale()));
        }
        return vh;
    }

    @Data
    class BatchCancelResult {
        private Map<Object, String> errMap;
        private Integer failNum;
    }

    class BatchOcCancel implements Callable<BatchCancelResult> {

        User user;
        List<Object> ids;

        public BatchOcCancel(User user, List<Object> ids) {
            this.user = user;
            this.ids = ids;
        }

        @Override
        public BatchCancelResult call() throws Exception {
            int fail = 0;
            Map<Object, String> errMap = new HashMap<>();
            OcCancelChangingOrRefundService bean = ApplicationContextHandle.getBean(OcCancelChangingOrRefundService.class);
            BatchCancelResult batchCancelResult = new BatchCancelResult();
            ValueHolderV14 vh = new ValueHolderV14();
            for (Object id : ids) {
                try {
                    fail = bean.batchOcCancel(user, vh, fail, id);
                } catch (Exception e) {
                    log.error(LogUtil.format("OcCancelChangingOrRefundService.orRefundService,error:{}"), Throwables.getStackTraceAsString(e));
                    errMap.put(id, e.getMessage());
                    fail++;
                }
            }
            batchCancelResult.setFailNum(fail);
            batchCancelResult.setErrMap(errMap);
            return batchCancelResult;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public int batchOcCancel(User user, ValueHolderV14 vh, int fail, Object id) {
        //批量循环加锁
        String lockRedisKey = BllRedisKeyResources.buildLockReturnOrderKey(Long.valueOf(id.toString()));
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                try {
                    Long orderId = Long.valueOf(id.toString());
                    //根据id查询退换货主表数据
                    OcBReturnOrder ocBReturnOrder = ocBReturnOrderMapper.selectById(orderId);
                    if (ocBReturnOrder == null) {
                        throw new NDSException(Resources.getMessage("传入的id 无效请稍后重试", user.getLocale()));
                    } else {

                        paramCheck(ocBReturnOrder);
                        if (ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal().equals(ocBReturnOrder.getReturnStatus())) {
                            if (WmsWithdrawalState.NO.toInteger().equals(ocBReturnOrder.getIsTowms())
                                    && ReturnOrderConfirmStatusEnum.NOT_CONFIRM.getKey().equals(ocBReturnOrder.getConfirmStatus())
                                    && (ocBReturnOrder.getProReturnStatus() == null || ProReturnStatusEnum.WAIT.getVal().equals(ocBReturnOrder.getProReturnStatus()))) {

                                ValueHolderV14 valueHolderV141 = checkBatchParm(user, vh, fail, ocBReturnOrder);
                                if (valueHolderV141.getCode() != 0) {
                                    throw new NDSException(Resources.getMessage("服务执行异常", user.getLocale()));
                                }

                            } else if (ReturnOrderConfirmStatusEnum.CONFIRM.getKey().equals(ocBReturnOrder.getConfirmStatus())
                                    && (ocBReturnOrder.getProReturnStatus() == null || ProReturnStatusEnum.WAIT.getVal().equals(ocBReturnOrder.getProReturnStatus()))) {
                                ValueHolderV14 v14;
                                try {
                                    SgBStoInNoticesBillVoidRequest stoInNoticesBillVoidRequest = new SgBStoInNoticesBillVoidRequest();
                                    stoInNoticesBillVoidRequest.setSourceBillId(ocBReturnOrder.getId());
                                    stoInNoticesBillVoidRequest.setSourceBillNo(ocBReturnOrder.getBillNo());
                                    stoInNoticesBillVoidRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_RETAIL_REF);
                                    v14 = stoInNoticesCmd.voidInNotices(stoInNoticesBillVoidRequest, user);
                                } catch (Exception e) {
                                    log.error(LogUtil.format("调用从wms撤回服务失败:{}"), Throwables.getStackTraceAsString(e));
                                    throw new NDSException(Resources.getMessage("调用从wms撤回服务失败", user.getLocale()));
                                }
                                if (v14.isOK()) {
                                    ValueHolderV14 valueHolderV14 = this.updateCancelWmsStatus(ocBReturnOrder, 1, user, v14.getMessage());
                                    if (!valueHolderV14.isOK()) {
                                        throw new NDSException(Resources.getMessage("更新主表插入日志失败", user.getLocale()));
                                    }
                                    ValueHolderV14 valueHolderV141 = checkBatchParm(user, vh, fail, ocBReturnOrder);
                                    if (valueHolderV141.getCode() != 0) {
                                        throw new NDSException(Resources.getMessage("服务执行异常", user.getLocale()));
                                    }
                                } else {
                                    //则返回撤回失败信息，并添加到系统备注中，程序结束。
                                    this.updateCancelWmsStatus(ocBReturnOrder, 0, user, v14.getMessage());
                                    fail += 1;
                                }
                            }
                        } else {
                            throw new NDSException(Resources.getMessage("退换货取消失败,只有【等待退货入库】状态才可以操作取消，请检查后重试", user.getLocale()));
                        }
                    }
                } catch (Exception e) {
                    throw new NDSException(Resources.getMessage(e.getMessage(), user.getLocale()));
                }
            } else {
                throw new NDSException(Resources.getMessage(" 当前订单其他人在操作，请稍后再试!", user.getLocale()));
            }
        } catch (Exception e) {
            throw new NDSException(Resources.getMessage(e.getMessage(), user.getLocale()));
        } finally {
            redisLock.unlock();
        }
        return fail;
    }

    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 oneOcCancle(User user, ValueHolderV14 vh, JSONArray ids) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.multiFormat(" oneOcCancel:param:=", ids));
        }
        //单条取消加锁
        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(ids.getLong(0));
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                Long id = ids.getLong(0);
                OcBReturnOrder findReturnOrderInfo = ocBReturnOrderMapper.selectById(id);

                if (findReturnOrderInfo == null) {
                    throw new NDSException(Resources.getMessage("传入的id 无效请稍后重试", user.getLocale()));
                }

                try {

                    paramCheck(findReturnOrderInfo);

                    if (ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal().equals(findReturnOrderInfo.getReturnStatus())) {
                        if (WmsWithdrawalState.NO.toInteger().equals(findReturnOrderInfo.getIsTowms())
                                && ReturnOrderConfirmStatusEnum.NOT_CONFIRM.getKey().equals(findReturnOrderInfo.getConfirmStatus())
                                && (findReturnOrderInfo.getProReturnStatus() == null || ProReturnStatusEnum.WAIT.getVal().equals(findReturnOrderInfo.getProReturnStatus()))) {

                            try {
                                if (checkPram(user, vh, findReturnOrderInfo)) {
                                    return vh;
                                }
                            } catch (Exception e) {
                                log.error(LogUtil.format("调用checkPram异常,error:{}"), Throwables.getStackTraceAsString(e));
                                throw new NDSException(Resources.getMessage("退换货取消失败,请检查后重试", user.getLocale()));
                            }

                        } else if (ReturnOrderConfirmStatusEnum.CONFIRM.getKey().equals(findReturnOrderInfo.getConfirmStatus())
                                && (findReturnOrderInfo.getProReturnStatus() == null || ProReturnStatusEnum.WAIT.getVal().equals(findReturnOrderInfo.getProReturnStatus()))) {

                            ValueHolderV14 v14 = new ValueHolderV14();
                            try {
                                SgBStoInNoticesBillVoidRequest stoInNoticesBillVoidRequest = new SgBStoInNoticesBillVoidRequest();
                                stoInNoticesBillVoidRequest.setSourceBillId(findReturnOrderInfo.getId());
                                stoInNoticesBillVoidRequest.setSourceBillNo(findReturnOrderInfo.getBillNo());
                                stoInNoticesBillVoidRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_RETAIL_REF);
                                v14 = stoInNoticesCmd.voidInNotices(stoInNoticesBillVoidRequest, user);
                            } catch (Exception e) {
                                log.error(LogUtil.format("调用从wms撤回服务失败,error:{}"), Throwables.getStackTraceAsString(e));
                                throw new NDSException(Resources.getMessage("调用从wms撤回服务失败", user.getLocale()));
                            }
                            if (v14.getCode() == ResultCode.SUCCESS) {
                                ValueHolderV14 valueHolderV14 = new ValueHolderV14();
                                if (!OcBorderListEnums.WmsCanceStatusEnum.RECALL_SUCCESS.getVal().equals(findReturnOrderInfo.getWmsCancelStatus())) {
                                    valueHolderV14 = this.updateCancelWmsStatus(findReturnOrderInfo, 1, user, v14.getMessage());
                                } else {
                                    valueHolderV14.setCode(ResultCode.SUCCESS);
                                    valueHolderV14.setMessage("操作成功");
                                }
                                if (valueHolderV14.getCode() != 0) {
                                    return valueHolderV14;
                                }
                                try {
                                    if (checkPram(user, vh, findReturnOrderInfo)) {
                                        return vh;
                                    }
                                } catch (Exception e) {
                                    log.error(LogUtil.format("调用checkPram异常,error:{}"), Throwables.getStackTraceAsString(e));
                                    throw new NDSException(Resources.getMessage("退换货取消失败,请检查后重试", user.getLocale()));
                                }
                            } else {
                                //则返回撤回失败信息，并添加到系统备注中，程序结束。
                                ValueHolderV14 valueHolderV14 = this.updateCancelWmsStatus(findReturnOrderInfo, 0, user, v14.getMessage());
                                if (valueHolderV14.getCode() != 0) {
                                    throw new NDSException(Resources.getMessage("退换货取消失败,请检查后重试", user.getLocale()));
                                } else {
                                    vh.setCode(ResultCode.FAIL);
                                    vh.setMessage(Resources.getMessage("退换货取消失败,wms撤回失败" + v14.getMessage(), user.getLocale()));
                                    return vh;
                                }
                            }
                        }
                    } else {
                        throw new NDSException(Resources.getMessage("退换货取消失败,只有【等待退货入库】状态才可以操作取消，请检查后重试", user.getLocale()));
                    }
                } catch (Exception e) {
                    log.error(LogUtil.format("oneOcCancle,error:{}"), Throwables.getStackTraceAsString(e));
                    throw new NDSException(Resources.getMessage(e.getMessage(), user.getLocale()));
                }
            } else {
                throw new NDSException(Resources.getMessage(" 当前订单其他人在操作，请稍后再试!", user.getLocale()));
            }
        } catch (Exception e) {
            throw new NDSException(Resources.getMessage(e.getMessage(), user.getLocale()));
        } finally {
            redisLock.unlock();
        }
        return null;
    }

    private void paramCheck(OcBReturnOrder ocBReturnOrder) {

        if (ProReturnStatusEnum.PORTION.getVal().equals(ocBReturnOrder.getProReturnStatus())
                || ProReturnStatusEnum.WHOLE.getVal().equals(ocBReturnOrder.getProReturnStatus())) {
            throw new NDSException("退换货单已经入库，不能进行取消");
        }

        if (OmsOrderRefundCommonEum.ONE.parseValue() == ocBReturnOrder.getInventedStatus()
                || OmsOrderRefundCommonEum.TWO.parseValue() == ocBReturnOrder.getProReturnStatus()) {
            throw new NDSException("退换货单已经虚拟入库，不能进行取消");
        }

        // 20240126佳哥让传WMS状态=未传、已确认、退货状态=待入库OMS支持撤销
        /*if (WmsWithdrawalState.NO.toInteger().equals(ocBReturnOrder.getIsTowms())
                && ReturnOrderConfirmStatusEnum.CONFIRM.getKey().equals(ocBReturnOrder.getConfirmStatus())
                && (ocBReturnOrder.getProReturnStatus() == null || ProReturnStatusEnum.WAIT.getVal().equals(ocBReturnOrder.getProReturnStatus()))) {
            throw new NDSException("退换货单传WMS中，不能进行取消");
        }*/
    }

    private ValueHolderV14 checkCancelResult(User user, OcBReturnOrder returnOrder) {
        ValueHolderV14 vh = new ValueHolderV14();
        // 换货 如果换货订单的状态不为取消，作废状态
        List<Integer> orderStatus = new ArrayList<>(2);
        orderStatus.add(OcOrderCheckBoxEnum.CHECKBOX_CANCELLED.getVal());
        orderStatus.add(OcOrderCheckBoxEnum.CHECKBOX_SYSTEM_INVALIDATION.getVal());
        // OcBOrder order = this.ocBOrderMapper.selectByReturnIdAndOrderStatus(returnOrder.getId(), OmsOrderStatus.UNCONFIRMED);
        OcBOrder order = this.ocBOrderMapper.selectByReturnId(returnOrder.getId());
        if (Objects.nonNull(order)) {
            if (!orderStatus.contains(order.getOrderStatus())) {
                vh.setCode(1);
                vh.setMessage(Resources.getMessage(
                        "此退换货单存在未作废的换货单,不允许取消,请作废换货单后,重新操作!", user.getLocale()));
                return vh;
            }
        }
        return null;
    }

    /**
     * 获取执行结果
     *
     * @param ids     退换单id
     * @param failNum 失败次数
     * @param errMap  错误消息
     * @return ValueHolderV14
     */
    private ValueHolderV14<List<HashMap<String, Object>>> getVhResult(JSONArray ids, int failNum,
                                                                      Map<Object, String> errMap) {
        ValueHolderV14<List<HashMap<String, Object>>> vh = new ValueHolderV14<>();
        // 处理结果处理
        if (ids.size() == 1) {
            if (errMap.size() == 0) {
                vh.setCode(ResultCode.SUCCESS);
                vh.setMessage("执行成功！");
            } else {
                List<HashMap<String, Object>> list = new ArrayList<>();
                for (Map.Entry<Object, String> entry : errMap.entrySet()) {
                    HashMap<String, Object> map = new HashMap<>();
                    map.put("objid", entry.getKey());
                    map.put("message", entry.getValue());
                    list.add(map);
                }
                vh.setData(list);
                vh.setMessage("执行失败！");
                vh.setCode(ResultCode.FAIL);
            }
        } else {
            // 多选处理结果
            if (errMap.size() == 0) {
                // 全部成功
                vh.setCode(ResultCode.SUCCESS);
                vh.setMessage(String.format("执行成功记录数：%s", ids.size()));
            } else {
                List<HashMap<String, Object>> list = new ArrayList<>();
                for (Map.Entry<Object, String> entry : errMap.entrySet()) {
                    HashMap<String, Object> map = new HashMap<>();
                    map.put("objid", entry.getKey());
                    map.put("message", entry.getValue());
                    list.add(map);
                }
                vh.setMessage(String.format("执行成功记录数：%s，执行失败记录数：%s", (ids.size() - failNum), failNum));
                vh.setData(list);
                vh.setCode(ResultCode.FAIL);
            }
        }
        return vh;
    }

    private ValueHolderV14 checkBatchParm(User user, ValueHolderV14 vh, Integer fail, OcBReturnOrder ocBReturnOrder) throws IOException {
        //判断退单类型
        if (OcReturnBillTypeEnum.RETURN.getVal().equals(ocBReturnOrder.getBillType())) {

            this.checkExistSgPhyInRes(ocBReturnOrder);

            //如果退单类型为退货单时，直接更新退单状态为取消状态，程序结束；
            ocBReturnOrder.setReturnStatus(ReturnStatusEnum.CANCLE.getVal());
            ocBReturnOrderMapper.updateById(ocBReturnOrder);
            vh.setCode(ResultCode.SUCCESS);
            vh.setMessage(Resources.getMessage("退货单取消成功", user.getLocale()));
            insertReturnOrederLog("退货单取消", "退货单取消成功", null, user, ocBReturnOrder.getId());
            // @20201118 去除手推ES代码
            /*try {
                Boolean aBoolean = ES4ReturnOrder.updateReturnOrderById(ocBReturnOrder);
                if (!aBoolean) {
                    throw new NDSException(Resources.getMessage("退货单取消主表推送ES失败!", user.getLocale()));
                }
            } catch (Exception e) {
                throw new NDSException(Resources.getMessage("退货单取消主表推送ES失败!", user.getLocale()));
            }*/
            try {
                ValueHolderV14 v14 = this.updateOriginalOrderReturnStatus(ocBReturnOrder, user);
            } catch (Exception e) {
                log.debug(LogUtil.format("调用this.changeOrder ,error:{}"), Throwables.getStackTraceAsString(e));
                throw new NDSException(Resources.getMessage("退货单取消失败!", user.getLocale()));
            }
            try {
                changeOrederItem(ocBReturnOrder, user);
            } catch (Exception e) {
                log.info(this.getClass().getName() + "调用changeOrederItem() 方法》》" + e.getMessage());
            }
            return vh;
        } else {
            //?判断退换货单是否生成对应的换货订单，未生成换货订单，直接更新退单状态为取消状态，程序结束；
            OcBOrder ocBOrder = ocBOrderMapper.selectByReturnId(ocBReturnOrder.getId());
            if (ocBOrder == null) {
                ocBReturnOrder.setReturnStatus(ReturnStatusEnum.CANCLE.getVal());
                ocBReturnOrderMapper.updateById(ocBReturnOrder);
                vh.setCode(ResultCode.SUCCESS);
                vh.setMessage(Resources.getMessage("退货单取消成功", user.getLocale()));
                insertReturnOrederLog("退货单取消", "退货单取消成功", null, user, ocBReturnOrder.getId());
                //@20201118 去除手推ES代码
                /*try {
                    Boolean aBoolean = ES4ReturnOrder.updateReturnOrderById(ocBReturnOrder);
                    if (!aBoolean) {
                        throw new NDSException(Resources.getMessage("退货单取消主表推送ES失败!", user.getLocale()));
                    }
                } catch (Exception e) {
                    throw new NDSException(Resources.getMessage("退货单取消主表推送ES失败!", user.getLocale()));
                }*/
                try {
                    ValueHolderV14<Object> v14 = this.updateOriginalOrderReturnStatus(ocBReturnOrder, user);
                } catch (Exception e) {
                    log.debug(LogUtil.format("调用this.changeOrder 异常,error:{}"), Throwables.getStackTraceAsString(e));
                    throw new NDSException(Resources.getMessage("退货单取消失败!", user.getLocale()));
                }
                changeOrederItem(ocBReturnOrder, user);
                return vh;
            } else {
                //如果换货订单的状态不为取消，作废状态
                List list = new ArrayList();
                list.add(OcOrderCheckBoxEnum.CHECKBOX_CANCELLED.getVal());
                list.add(OcOrderCheckBoxEnum.CHECKBOX_SYSTEM_INVALIDATION.getVal());
                //?如果换货订单的状态不为取消，作废状态，则返回提示信息：“此退换货单已生成未作废的换货单,不允许取消,请作废换货单后,重新操作!”
                // ；换货单为取消、作废状态时，直接更新退单状态为取消状态，程序结束；
                if (!list.contains(ocBOrder.getOrderStatus())) {
                    //throw new NDSException(Resources.getMessage("此退换货单已生成未作废的换货单,不允许取消,请作废换货单后,重新操作!", user.getLocale()));
                    if (log.isDebugEnabled()) {
                        log.debug(LogUtil.format("Start cancelOrderByLock,orderId=", ocBOrder.getId()));
                    }
                    try {

                        this.orderOffService.startCancelOrderByLock(user, ocBOrder.getId(),
                                OrderLogTypeEnum.ORDER_CANCLE.getKey(), "订单取消成功");
                    } catch (Exception ex) {
                        log.error(LogUtil.format("零售发货单取消异常,error:{}"), Throwables.getStackTraceAsString(ex));
                        throw new NDSException(ex.getMessage());
                    }
                }
                this.cancelExchangeOrder(user, vh, ocBReturnOrder);
                return vh;
            }
        }
    }

    private boolean checkPram(User user, ValueHolderV14 vh, OcBReturnOrder ocBReturnOrder) {
        //判断退单类型
        if (OcReturnBillTypeEnum.RETURN.getVal().equals(ocBReturnOrder.getBillType())) {

            this.checkExistSgPhyInRes(ocBReturnOrder);

            //如果退单类型为退货单时，直接更新退单状态为取消状态，程序结束；
            QueryWrapper<OcBReturnOrder> wrapper = new QueryWrapper<>();
            wrapper.eq("id", ocBReturnOrder.getId());
            OcBReturnOrder returnOrder = new OcBReturnOrder();
            returnOrder.setReturnStatus(ReturnStatusEnum.CANCLE.getVal());
            ocBReturnOrder.setReturnStatus(ReturnStatusEnum.CANCLE.getVal());
            ocBReturnOrderMapper.update(returnOrder, wrapper);
            vh.setCode(ResultCode.SUCCESS);
            vh.setMessage(Resources.getMessage("退货单取消成功", user.getLocale()));
            insertReturnOrederLog("退货单取消", "退货单取消成功", null, user, ocBReturnOrder.getId());
            // @20201118 去除手推ES代码
            /*try {
                Boolean aBoolean = ES4ReturnOrder.updateReturnOrderById(ocBReturnOrder);
                if (!aBoolean) {
                    throw new NDSException(Resources.getMessage("退货单取消主表推送ES失败!", user.getLocale()));
                }
            } catch (Exception e) {
                throw new NDSException(Resources.getMessage("退货单取消主表推送ES失败!", user.getLocale()));
            }*/
            //修改订单主表
            try {
                ValueHolderV14 v14 = this.updateOriginalOrderReturnStatus(ocBReturnOrder, user);
            } catch (Exception e) {
                log.debug(LogUtil.format("调用this.changeOrder,error:{}"), Throwables.getStackTraceAsString(e));
                throw new NDSException(Resources.getMessage("退货单取消失败!", user.getLocale()));
            }
            // 修改 订单明细已退数量值

            try {
                changeOrederItem(ocBReturnOrder, user);
            } catch (Exception e) {
                log.error(LogUtil.format("调用changeOrederItem(),error:{}"), Throwables.getStackTraceAsString(e));
            }
            return true;
        } else {
            //?判断退换货单是否生成对应的换货订单，未生成换货订单，直接更新退单状态为取消状态，程序结束；

            List<Long> ids = ES4Order.getIdsByOrigReturnOrderId(ocBReturnOrder.getId());

            List<OcBOrder> orders = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(ids)) {
                QueryWrapper wrapper = new QueryWrapper();
                wrapper.in("id", ids);
                orders = ocBOrderMapper.selectList(wrapper);
            }
            //OcBOrder ocBOrder = ocBOrderMapper.selectByReturnId(ocBReturnOrder.getId());
            if (CollectionUtils.isEmpty(orders)) {
                ocBReturnOrder.setReturnStatus(ReturnStatusEnum.CANCLE.getVal());
                ocBReturnOrderMapper.updateById(ocBReturnOrder);
                vh.setCode(ResultCode.SUCCESS);
                vh.setMessage(Resources.getMessage("退货单取消成功", user.getLocale()));
                insertReturnOrederLog("退货单取消", "退货单取消成功", null, user, ocBReturnOrder.getId());
                // @20201118 去除手推ES代码
                /*try {
                    Boolean aBoolean = ES4ReturnOrder.updateReturnOrderById(ocBReturnOrder);
                    if (!aBoolean) {
                        throw new NDSException(Resources.getMessage("退货单取消主表推送ES失败!", user.getLocale()));
                    }
                } catch (Exception e) {
                    throw new NDSException(Resources.getMessage("退货单取消主表推送ES失败!", user.getLocale()));
                }*/
                try {
                    ValueHolderV14 v14 = this.updateOriginalOrderReturnStatus(ocBReturnOrder, user);
                } catch (Exception e) {
                    log.debug(LogUtil.format("调用this.changeOrder ,error:{}"), Throwables.getStackTraceAsString(e));
                    throw new NDSException(Resources.getMessage("退货单取消失败!", user.getLocale()));
                }
                try {
                    changeOrederItem(ocBReturnOrder, user);
                } catch (Exception e) {
                    log.error(LogUtil.format("调用changeOrederItem（）方法异常,error:{}"), Throwables.getStackTraceAsString(e));
                }
                return true;
            } else {
                OcBOrder ocBOrder = orders.get(0);
                //如果换货订单的状态不为取消，作废状态
                List list = new ArrayList();
                list.add(OcOrderCheckBoxEnum.CHECKBOX_CANCELLED.getVal());
                list.add(OcOrderCheckBoxEnum.CHECKBOX_SYSTEM_INVALIDATION.getVal());
                //?如果换货订单的状态不为取消，作废状态，则返回提示信息：“此退换货单已生成未作废的换货单,不允许取消,请作废换货单后,重新操作!”
                // ；换货单为取消、作废状态时，直接更新退单状态为取消状态，程序结束；
                if (!list.contains(ocBOrder.getOrderStatus())) {
                    //vh.setCode(ResultCode.FAIL);
                    //vh.setMessage(Resources.getMessage("此退换货单已生成未作废的换货单,不允许取消,请作废换货单后,重新操作!", user.getLocale()));
                    // 【taskId=28662】 取消零售发货单
                    if (log.isDebugEnabled()) {
                        log.debug(LogUtil.format("Start cancelOrderByLock，orderId=", ocBOrder.getId()));
                    }
                    try {
                        this.orderOffService.startCancelOrderByLock(user, ocBOrder.getId(),
                                OrderLogTypeEnum.ORDER_CANCLE.getKey(), "订单取消成功");
                    } catch (Exception ex) {
                        log.error(LogUtil.format("零售发货单取消异常,error:{}"), Throwables.getStackTraceAsString(ex));
                        throw new NDSException("零售发货单取消异常: " + ex.getMessage());
                    }
                    // 取消退换单
                }
                return this.cancelExchangeOrder(user, vh, ocBReturnOrder);
            }
        }
    }

    private boolean cancelExchangeOrder(User user, ValueHolderV14 vh, OcBReturnOrder ocBReturnOrder) {
        ocBReturnOrder.setReturnStatus(ReturnStatusEnum.CANCLE.getVal());
        ocBReturnOrderMapper.updateById(ocBReturnOrder);
        Long tbDisputeId = ocBReturnOrder.getTbDisputeId();
        if (tbDisputeId != null) {
            String redisKey = BllRedisKeyResources.getReturnOrderDisputeIdKey(tbDisputeId + "");
            RedisMasterUtils.getStrRedisTemplate().delete(redisKey);
        }
        vh.setCode(ResultCode.SUCCESS);
        vh.setMessage(Resources.getMessage("退换货单取消成功", user.getLocale()));
        insertReturnOrederLog("退换货单取消", "退换货单取消成功", null, user, ocBReturnOrder.getId());
        try {
            ValueHolderV14 v14 = this.updateOriginalOrderReturnStatus(ocBReturnOrder, user);
        } catch (Exception e) {
            log.error(LogUtil.format("退货单取消失败,error:{}"), Throwables.getStackTraceAsString(e));
            throw new NDSException(Resources.getMessage("退货单取消失败!", user.getLocale()));
        }
        try {
            changeOrederItem(ocBReturnOrder, user);
        } catch (Exception e) {
            log.error(LogUtil.format("调用changeOrederItem()方法异常,error:{}"), Throwables.getStackTraceAsString(e));
        }
        return true;
    }

    /**
     * 校验有效状态入库结果单
     *
     * @param ocBReturnOrder 退单信息
     */
    private void checkExistSgPhyInRes(OcBReturnOrder ocBReturnOrder) {
        // 【taskId=28657】 退单取消需要先校验有效状态入库结果单，若存在，不允许取消
//        SgBPhyInResult sgBPhyInResult = this.sgRpcService.selectPhyNotices(ocBReturnOrder.getId(),
//                SgConstantsIF.BILL_TYPE_RETAIL_REF);
//
//        if (sgBPhyInResult != null){
//            Integer billStatus = sgBPhyInResult.getBillStatus();
//            if (SgInNoticeConstants.BILL_STATUS_IN_VOID != billStatus){
//                throw new NDSException("存在有效状态入库结果单，不允许取消");
//            }
//        }
    }

    //修改订单明细已退数量字段
    private void changeOrederItem(OcBReturnOrder ocBReturnOrder, User user) {
        //获取主表id
        Long origOrderId = ocBReturnOrder.getOrigOrderId();
        if (origOrderId == null) {
            throw new NDSException("无原单,继续进行操作");
        }
        //退换货明细表
        QueryWrapper<OcBReturnOrderRefund> wrapper = new QueryWrapper<>();
        wrapper.eq("oc_b_return_order_id", ocBReturnOrder.getId());
        List<OcBReturnOrderRefund> returnOrderRefunds = refundMapper.selectList(wrapper);

        List<Long> orderIdList = returnOrderRefunds.stream().map(o -> o.getOcBOrderId()).distinct().collect(Collectors.toList());
        List<OcBOrder> ocBOrderList = ocBOrderMapper.selectList(new QueryWrapper<OcBOrder>().lambda()
                .in(OcBOrder::getId, orderIdList));
        for (OcBOrder ocBOrder : ocBOrderList) {
            origOrderId = ocBOrder.getId();
            //查询订单主表明细
            QueryWrapper<OcBOrderItem> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("oc_b_order_id", origOrderId);
            List<OcBOrderItem> ocBOrderItems = itemMapper.selectList(queryWrapper);
            if (ocBOrderItems == null || ocBOrderItems.size() == 0) {
                throw new NDSException("未找到原单，请检查重试");
            }
            for (int i = 0; i < ocBOrderItems.size(); i++) {
                OcBOrderItem orderItem = ocBOrderItems.get(i);
                for (int i1 = 0; i1 < returnOrderRefunds.size(); i1++) {
                    OcBReturnOrderRefund refund = returnOrderRefunds.get(i1);
                    if (orderItem.getId().equals(refund.getOcBOrderItemId())) {
                        //更新明细表
                        QueryWrapper queryWrapper1 = new QueryWrapper();
                        queryWrapper1.eq("id", orderItem.getId());
                        queryWrapper1.eq("oc_b_order_id", origOrderId);
                        OcBOrderItem item = new OcBOrderItem();
                        if (orderItem.getQtyHasReturn() == null) {
                            orderItem.setQtyHasReturn(BigDecimal.ZERO);
                        }
                        if (orderItem.getQtyReturnApply() == null) {
                            orderItem.setQtyReturnApply(BigDecimal.ZERO);
                        }
                        BigDecimal qtyRefund = refund.getQtyRefund();
                        if ((qtyRefund.subtract(orderItem.getQtyHasReturn())).compareTo(BigDecimal.ZERO) > 0) {
                            item.setQtyHasReturn(BigDecimal.ZERO);
                            item.setModifierename(user.getEname());
                            item.setModifieddate(new Date());
                            item.setModifierename(user.getName());
                        } else {
                            item.setQtyHasReturn(orderItem.getQtyHasReturn().subtract(qtyRefund));
                            item.setModifierename(user.getEname());
                            item.setModifieddate(new Date());
                            item.setModifierename(user.getName());
                        }
                        if ((qtyRefund.subtract(orderItem.getQtyReturnApply())).compareTo(BigDecimal.ZERO) > 0) {
                            item.setQtyReturnApply(BigDecimal.ZERO);
                        } else {
                            item.setQtyReturnApply(orderItem.getQtyReturnApply().subtract(qtyRefund));
                        }
                        itemMapper.update(item, queryWrapper1);
                    }
                }
            }
        }

    }

    //修改主表和调用退换货的日志服务

    /**
     * 添加退换货订单操作日志
     *
     * @param type     日志的类型
     * @param message  日志信息
     * @param param    日志参数
     * @param user     登录用户
     * @param returnID 退换货订单的id
     */
    public void insertReturnOrederLog(String type, String message, String param, User user, Long returnID) {
        OcBReturnOrderLog log = new OcBReturnOrderLog();
        log.setAdClientId(Long.valueOf(Integer.valueOf(user.getClientId()).toString()));
        log.setAdOrgId(Long.valueOf(Integer.valueOf(user.getOrgId()).toString()));
        log.setOwnername(user.getName());
        log.setOwnerename(user.getEname());
        log.setModifiername(user.getName());
        log.setModifierename(user.getEname());
        log.setId(ModelUtil.getSequence("oc_b_return_order_log"));
        log.setIpAddress(user.getLastloginip());
        log.setLogParam(param);
        log.setLogMessage(message);
        log.setLogType(type);
        log.setUserName(user.getName());
        log.setOcBReturnOrderId(returnID);
        log.setCreationdate(new Date());
        log.setModifieddate(new Date());
        logMapper.insert(log);
    }


    //根据条件来修改订单表的退单状态

    /**
     * 修改退换单Wms取消状态
     *
     * @param returnOrderInfo  退换单信息
     * @param tag              撤销WMS结果值。1=成功；0=失败
     * @param user             操作用户
     * @param cancelWmsMessage 撤销WMS
     * @return 更新WMS撤销状态结果值
     */
    public ValueHolderV14<Object> updateCancelWmsStatus(OcBReturnOrder returnOrderInfo, Integer tag, User user,
                                                        String cancelWmsMessage) {
        ValueHolderV14<Object> vh = new ValueHolderV14<>();
        //更新主表
        //A.“WMS撤回状态”：已撤回
        // B.“修改人”：当前操作人
        //C.“修改时间”：当前时间
        OcBReturnOrder order = new OcBReturnOrder();
        QueryWrapper<OcBReturnOrder> wrapper = new QueryWrapper<>();
        wrapper.eq("id", returnOrderInfo.getId());
        if (tag == 1) {
            order.setWmsCancelStatus(OcBorderListEnums.WmsCanceStatusEnum.RECALL_SUCCESS.getVal());
            returnOrderInfo.setWmsCancelStatus(OcBorderListEnums.WmsCanceStatusEnum.RECALL_SUCCESS.getVal());
        } else {
            order.setWmsCancelStatus(OcBorderListEnums.WmsCanceStatusEnum.RECALL_FAIL.getVal());
            returnOrderInfo.setWmsCancelStatus(OcBorderListEnums.WmsCanceStatusEnum.RECALL_FAIL.getVal());
        }
        order.setModifierid(Long.valueOf(user.getId()));
        order.setModifieddate(new Date());
        returnOrderInfo.setModifierid(Long.valueOf(user.getId()));
        returnOrderInfo.setModifieddate(new Date());
        try {
            if (tag == 0) {
                order.setRemark("撤回失败信息->" + cancelWmsMessage);
                returnOrderInfo.setRemark("撤回失败信息");
                // ocBReturnOrderMapper.updateById(order);
                ocBReturnOrderMapper.update(order, wrapper);
            } else {
                order.setRemark("撤回成功信息");
                returnOrderInfo.setRemark("撤回成功信息");
                ocBReturnOrderMapper.update(order, wrapper);
            }
            //调用退换货日志服务
            if (tag == 1) {
                this.insertReturnOrederLog("WMS撤回成功", "WMS撤回成功 ", null, user, returnOrderInfo.getId());
            } else {
                this.insertReturnOrederLog("WMS撤回失败", "WMS撤回失败-> " + cancelWmsMessage, null, user, returnOrderInfo.getId());
            }
        } catch (Exception e) {
            log.error(LogUtil.format("调用服务失败,error:{}"), Throwables.getStackTraceAsString(e));
            vh.setCode(-1);
            vh.setMessage("跟新主表失败或者调用日志服务失败");
            return vh;
        }
        //推主表的es  @20201118 去除手推ES代码
        /*try {
            Boolean aBoolean = ES4ReturnOrder.updateReturnOrderById(returnOrderInfo);
            if (!aBoolean) {
                throw new NDSException(Resources.getMessage("退换货单取消主表推送ES失败!", user.getLocale()));
            }
        } catch (Exception e) {
            throw new NDSException(Resources.getMessage("退换货单取消主表推送ES失败!", user.getLocale()));
        }*/
        return vh;
    }

    /**
     * 根据退货单列表判断是否需要修改零售发货单的退单状态
     * 如果退货单列表中退单状态不等于取消的 为空，则更新零售发货单状态
     *
     * @param returnOrderInfo
     * @param user
     * @return
     */
    private ValueHolderV14<Object> updateOriginalOrderReturnStatus(OcBReturnOrder returnOrderInfo, User user) {
        ValueHolderV14<Object> vh = new ValueHolderV14<>();
        //判断这个订单是否有其他的退单，判断退单表里是否存在此订单的其他有效退单(状态不为取消的)
        Long origOrderId = returnOrderInfo.getOrigOrderId();
        if (origOrderId == null) {
            vh.setMessage("无原单继续操作");
            vh.setCode(ResultCode.SUCCESS);
            return vh;
        }
        JSONObject filterKey = new JSONObject();
        JSONObject whereKey = new JSONObject();
        JSONArray orderKey = new JSONArray();
        whereKey.put("ORIG_ORDER_ID", returnOrderInfo.getOrigOrderId());
        List<OcBReturnOrder> list = null;
        int range = 10;
        int startIndex = 0;
        String[] returnFields = {"ID"};

        try {
            JSONObject search = ES4ReturnOrder.getReturnOrderSearchByUserinfo(whereKey, filterKey, orderKey, startIndex, range, returnFields);
            Integer totalCount = search.getInteger("total");
            JSONObject search1 = ES4ReturnOrder.getReturnOrderSearchByUserinfo(whereKey, filterKey, orderKey, startIndex, totalCount, returnFields);
            JSONArray data = search1.getJSONArray("data");
            //获取IDS编号数组
            JSONArray jsonArray = new JSONArray();
            List<Object> idList = new ArrayList<>();
            if (data.size() == 1) {
                idList.add(data.getJSONObject(0).getString("ID"));
            } else {
                for (int i = 0; i < data.size(); i++) {
                    Long id = data.getJSONObject(i).getLong("ID");
                    if (!returnOrderInfo.getId().equals(id)) {
                        idList.add(id);
                    }
                }
            }
          /*  String join = StringUtils.join(jsonArray, ",");
            List list1 = stringToList(join);*/
            //查询满足条件的数据
            QueryWrapper<OcBReturnOrder> wrapper1 = new QueryWrapper<>();
            wrapper1.in("id", idList);
            list = ocBReturnOrderMapper.selectList(wrapper1);
        } catch (Exception e) {
            throw new NDSException(Resources.getMessage("退换货单订单主表eS查询失败!", user.getLocale()));
        }
       /* QueryWrapper wrapper =new QueryWrapper();
        wrapper.eq("id",ocBReturnOrder.getId());
        wrapper.eq("orig_order_id",origOrderId);
        List<OcBReturnOrder> list = ocBReturnOrderMapper.selectList(wrapper);*/
        if (list == null || list.size() == 0) {
            vh.setCode(ResultCode.SUCCESS);
            vh.setMessage("取消成功");
            return vh;
        }
        // 判断退单排除取消的和他自己 还有其他有效退单吗 ，如果有 就 不做处理 不进行修改订单主表 ，如果不存在就修改主表的退货状态为 无退货
        List<OcBReturnOrder> collect = list.stream().filter(
                p -> !ReturnStatusEnum.CANCLE.getVal().equals(p.getReturnStatus())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            //更新订单的退货状态为未退货
            QueryWrapper<OcBOrder> wrapper = new QueryWrapper<>();
            wrapper.eq("id", origOrderId);
            OcBOrder updateOrderInfo = new OcBOrder();
            updateOrderInfo.setId(origOrderId);
            updateOrderInfo.setReturnStatus(OcBorderListEnums.ReturnStatusEnum.NO_RETURN.getVal());
            updateOrderInfo.setIsInreturning(InreturningStatus.INRETURN_NO);
            updateOrderInfo.setModifieddate(new Date());
            updateOrderInfo.setModifierename(user.getEname());
            updateOrderInfo.setModifiername(user.getName());
            ocBOrderMapper.update(updateOrderInfo, wrapper);

            if (SpecialElasticSearchUtil.isUseIndexDocument()) {
                OcBOrder afterOrderInfo = ocBOrderMapper.selectByID(origOrderId);
                log.info(LogUtil.format("updateOriginalOrderReturnStatus.afterOrderInfo={}", afterOrderInfo));
                //推主表的es
                try {
                    Boolean aBoolean = SpecialElasticSearchUtil.indexDocument(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME, OcElasticSearchIndexResources.OC_B_ORDER_TYPE_NAME, afterOrderInfo, afterOrderInfo.getId());
                    if (!aBoolean) {
                        throw new NDSException(Resources.getMessage("退换货单取消改订单主表推送ES失败!", user.getLocale()));
                    }
                } catch (Exception e) {
                    throw new NDSException(Resources.getMessage("退换货单取消改订单主表推送ES失败!", user.getLocale()));
                }
            }
            vh.setCode(ResultCode.SUCCESS);
            vh.setMessage("操作订单主表成功");
            vh.setData(null);
        } else {
            // 如果存在就不用更新
        }
        /*   for (int i = 0; i < list.size(); i++) {
         *//* if ((ocBReturnOrder.getId()).equals(list.get(i).getId())) {
                continue;
            }*//*
            //判断退单是不是有效
            if (WithdrawalStatus.CANCEL.toInteger() != list.get(i).getReturnStatus()) {
                // 如果存在就不用更新
            } else {
                //更新订单的退货状态为未退货
                QueryWrapper wrapper = new QueryWrapper();
                wrapper.eq("id", origOrderId);
                OcBOrder ocBOrder = new OcBOrder();
                ocBOrder.setId(origOrderId);
                ocBOrder.setReturnStatus(OcBorderListEnums.ReturnStatusEnum.NO_RETURN.getVal());
                ocBOrder.setModifieddate(new Date());
                ocBOrder.setModifierename(user.getEname());
                ocBOrder.setModifiername(user.getName());
                ocBOrderMapper.update(ocBOrder, wrapper);
                OcBOrder ocBOrder1 = ocBOrderMapper.selectByID(origOrderId);
                //推主表的es
                try {
                    Boolean aBoolean = SpecialElasticSearchUtil.indexDocument(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME, OcElasticSearchIndexResources.OC_B_ORDER_TYPE_NAME, ocBOrder1, ocBOrder1.getId());
                    if (!aBoolean) {
                        throw new NDSException(Resources.getMessage("退换货单取消改订单主表推送ES失败!", user.getLocale()));
                    }
                } catch (Exception e) {
                    throw new NDSException(Resources.getMessage("退换货单取消改订单主表推送ES失败!", user.getLocale()));
                }
                vh.setCode(ResultCode.SUCCESS);
                vh.setMessage("操作订单主表成功");
                vh.setData(null);
            }
        }*/
        return vh;
    }

}
