package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.api.QueryOrderItemCmd;
import com.jackrain.nea.oc.oms.services.QueryOrderItemService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by 王强 on 2019/4/11 13:20
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class QueryOrderItemCmdImpl extends CommandAdapter implements QueryOrderItemCmd {
    @Autowired
    QueryOrderItemService queryOrderItemService;

    @Override
    public ValueHolder queryOrderItem(JSONObject obj, User user) {
        return queryOrderItemService.queryOrderItem(obj, user);
    }
}
