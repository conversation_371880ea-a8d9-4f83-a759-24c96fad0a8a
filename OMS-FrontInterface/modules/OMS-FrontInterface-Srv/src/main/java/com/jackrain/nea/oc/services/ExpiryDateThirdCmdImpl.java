package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.api.ExpiryDateThirdCmd;
import com.jackrain.nea.oc.oms.model.table.StCExpiryDate;
import com.jackrain.nea.oc.oms.model.table.StCExpiryDateItem;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.st.service.StExpiryDateAddService;
import com.jackrain.nea.st.service.StExpiryDateAuditService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 外部对指定效期的操作相关接口
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class ExpiryDateThirdCmdImpl implements ExpiryDateThirdCmd {

    @Resource
    private CpRpcService cpRpcService;
    @Resource
    private StExpiryDateAddService stExpiryDateAddService;
    @Resource
    private StExpiryDateAddService stExpiryDateService;
    @Resource
    private StExpiryDateAuditService stExpiryDateAuditService;

    @Override
    public ValueHolderV14 save(JSONObject json) {
        log.info(" expiry date save json:{}", json.toJSONString());
        JSONObject jsonObject = json.getJSONObject("expiryDate");
        ValueHolderV14<CpShop> shopResult = cpRpcService.queryByShopCode(jsonObject.getString("shopCode"));
        if (!shopResult.isOK()) {
            return backErrorInfo("查询店铺信息异常", null);
        }
        CpShop shop = shopResult.getData();
        if (shop == null) {
            return backErrorInfo("未找到店铺信息，请核对店铺编码是否正确", null);
        }
        if (!shop.getCpCShopTitle().equals(jsonObject.getString("shopName"))) {
            return backErrorInfo("店铺名称错误，请核对店铺编码或店铺名称是否正确", null);
        }
        StCExpiryDate expiryDate = JsonUtils.jsonParseClass(jsonObject, StCExpiryDate.class);
        expiryDate.setShopId(shop.getCpCShopId());
        //解析明细
        Map<String, StCExpiryDateItem> map = Maps.newHashMap();
        JSONArray jsonArray = json.getJSONArray("expiryDateItems");
        for (Object o : jsonArray) {
            JSONObject role = (JSONObject) o;
            String serialNo = role.getString("serialNo");
            if (map.containsKey(serialNo)) {
                return backErrorInfo("serialNo重复,不允许！", null);
            }
            StCExpiryDateItem stCExpiryDateItem = JsonUtils.jsonParseClass(role, StCExpiryDateItem.class);
            map.put(serialNo, stCExpiryDateItem);
        }
        //校验
        ValueHolderV14 expiryDateCheck = checkDatas(expiryDate, map);
        if (expiryDateCheck != null) {
            return expiryDateCheck;
        }

        //新增
        expiryDate.setSubmitStatus(1);
        Long mainId = ModelUtil.getSequence("ST_C_EXPIRY_DATE");
        expiryDate.setId(mainId);
        BaseModelUtil.initialBaseModelSystemField(expiryDate, null);
        List<StCExpiryDateItem> expiryDateItems = new ArrayList(map.values());
        if (CollectionUtils.isNotEmpty(expiryDateItems)) {
            for (StCExpiryDateItem expiryDateItem : expiryDateItems) {
                expiryDateItem.setId(ModelUtil.getSequence("ST_C_EXPIRY_DATE_ITEM"));
                expiryDateItem.setStCExpiryDateId(mainId);
                expiryDateItem.setIsactive("Y");
                BaseModelUtil.initialBaseModelSystemField(expiryDateItem, null);
            }
        }
        log.info(" expiry date save expiryDate:{},expiryDateItems:{}", JSON.toJSONString(expiryDate), JSON.toJSONString(expiryDateItems));
        //保存效期策略数据并审核
        try {
            stExpiryDateService.saveData(expiryDate, expiryDateItems, true);
        } catch (Exception e) {
            return backErrorInfo("自动审核失败:" + e.getMessage(), null);
        }
        ValueHolderV14 v14 = new ValueHolderV14();
        v14.setCode(ResultCode.SUCCESS);
        v14.setData(expiryDate.getId());
        return v14;
    }

    @Override
    public ValueHolderV14 preCheck(JSONObject json) {
        log.info(" expiry date preCheck json:{}", json.toJSONString());
        JSONObject jsonObject = json.getJSONObject("expiryDate");

        String shopCode = jsonObject.getString("shopCode");
        log.info(" expiry date preCheck shopCode:{}", shopCode);
        ValueHolderV14<CpShop> shopResult = cpRpcService.queryByShopCode(shopCode);
        if (!shopResult.isOK()) {
            return backErrorInfo("查询店铺信息异常", null);
        }
        CpShop shop = shopResult.getData();
        if (shop == null) {
            return backErrorInfo("未找到店铺信息，请核对店铺编码是否正确", null);
        }
        if (!shop.getCpCShopTitle().equals(jsonObject.getString("shopName"))) {
            return backErrorInfo("店铺名称错误，请核对店铺编码或店铺名称是否正确", null);
        }
        StCExpiryDate expiryDate = JsonUtils.jsonParseClass(jsonObject, StCExpiryDate.class);
        expiryDate.setShopId(shop.getCpCShopId());
        //解析明细
        Map<String, StCExpiryDateItem> map = Maps.newHashMap();
        JSONArray jsonArray = json.getJSONArray("expiryDateItems");
        for (Object o : jsonArray) {
            JSONObject role = (JSONObject) o;
            String serialNo = role.getString("serialNo");
            if (map.containsKey(serialNo)) {
                return backErrorInfo("serialNo重复,不允许！", null);
            }
            StCExpiryDateItem stCExpiryDateItem = JsonUtils.jsonParseClass(role, StCExpiryDateItem.class);
            map.put(serialNo, stCExpiryDateItem);
        }
        ValueHolderV14 expiryDateCheck = checkDatas(expiryDate, map);
        if (expiryDateCheck != null) {
            return expiryDateCheck;
        }
        ValueHolderV14 vh = new ValueHolderV14();
        vh.setCode(ResultCode.SUCCESS);
        return vh;
    }

    private ValueHolderV14 checkDatas(StCExpiryDate expiryDate, Map<String, StCExpiryDateItem> map) {
        //效期主数据校验
        String expiryDateCheck = stExpiryDateAddService.oaExpiryDatePreCheck(expiryDate);
        if (StringUtils.isNotBlank(expiryDateCheck)) {
            return backErrorInfo(expiryDateCheck, null);
        }
        //效期明细数据校验
        Map<String, String> resultMap = stExpiryDateAddService.oaExpiryDateItemPreCheck(map);
        if (MapUtils.isNotEmpty(resultMap)) {
            return backErrorInfo("", resultMap);
        }
        return null;
    }

//    private Map<String, StCExpiryDateItem> getItemMap(JSONObject jsonObject) {
//        Map<String, StCExpiryDateItem> map = Maps.newHashMap();
//        JSONArray jsonArray = jsonObject.getJSONArray("expiryDateItems");
//        for (Object o : jsonArray) {
//            JSONObject role = (JSONObject) o;
//            String serialNo = role.getString("serialNo");
//            StCExpiryDateItem stCExpiryDateItem = JsonUtils.jsonParseClass(role, StCExpiryDateItem.class);
//            map.put(serialNo, stCExpiryDateItem);
//        }
//        return map;
//    }

    private ValueHolderV14 backErrorInfo(String content, Map<String, String> map) {
        ValueHolderV14 vh = new ValueHolderV14();
        vh.setCode(ResultCode.FAIL);

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("error", content);

        JSONArray jsonArray = new JSONArray();
        if (MapUtils.isNotEmpty(map)) {
            for (Map.Entry<String, String> entry : map.entrySet()) {
                JSONObject object = new JSONObject();
                object.put(entry.getKey(), entry.getValue());
                jsonArray.add(object);
            }
        }
        jsonObject.put("errorItem", jsonArray);

        vh.setMessage(jsonObject.toJSONString());
        return vh;
    }
}
