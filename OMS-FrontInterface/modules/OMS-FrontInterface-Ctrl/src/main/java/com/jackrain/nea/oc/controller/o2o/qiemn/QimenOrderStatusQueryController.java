package com.jackrain.nea.oc.controller.o2o.qiemn;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.api.qimen.QimenPosOrderStatusCmd;
import com.jackrain.nea.oc.oms.model.request.QimenPosOrderStatusRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * @Auther: 黄志优
 * @Date: 2020/9/18 17:10
 * @Description: 奇门订单状态查询
 */
@Api(value = "QimenOrderStatusQueryController", tags = "奇门订单状态查询")
@Slf4j
@RestController
public class QimenOrderStatusQueryController {

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @Autowired
    QimenPosOrderStatusCmd qimenPosOrderStatusCmd;

    @ApiOperation(value = "订单状态查询")
    @RequestMapping(value = "/api/cs/oc/oms/v1/queryOrderStatus", method = RequestMethod.POST)
    public ValueHolderV14 queryOrderStatus(HttpServletRequest request, @RequestBody String param) {
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);

        if (StringUtils.isBlank(param)) {
            ValueHolderV14 vh = new ValueHolderV14();
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("传入的参数不能为空");
            return vh;
        }

        QimenPosOrderStatusRequest orderStatusRequest = JSONObject.parseObject(param, QimenPosOrderStatusRequest.class);

        ValueHolderV14 vh = qimenPosOrderStatusCmd.queryOrderStatus(orderStatusRequest, user);
        return vh;
    }
}
