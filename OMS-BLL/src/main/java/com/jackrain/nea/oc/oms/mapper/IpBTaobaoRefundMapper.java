package com.jackrain.nea.oc.oms.mapper;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoRefund;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.Update;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.jdbc.SQL;
import org.springframework.stereotype.Component;

import java.util.List;

@Mapper
@Component
public interface IpBTaobaoRefundMapper extends ExtentionMapper<IpBTaobaoRefund> {

    @Select("SELECT * FROM ip_b_taobao_refund WHERE refund_id=#{orderNo}")
    IpBTaobaoRefund selectTaobaoRefundByRefundId(@Param("orderNo") String orderNo);


    @Select("SELECT * FROM ip_b_taobao_refund WHERE refund_id=#{orderNo}")
    IpBTaobaoRefund selectTaobaoByRefundId(@Param("orderNo") String orderNo);

    /***
     * 查询DRDS的分库RefundId
     *
     * @param node node name
     * @param name table name
     * @param size size
     * @param isTrans trans status
     * @param lessThanTransCnt less than trans Count
     * @param timestamp timestamp
     * @param minutes before n minutes
     * @return OrderNo（TID）
     */
    @SelectProvider(type = TaobaoRefundSqlBuilder.class, method = "selectDynamicRefundId")
    List<String> selectDynamicRefundId(@Param("name") String name,
                                       @Param("size") int size, @Param("isTrans") int isTrans,
                                       @Param("lessThanTransCnt") int lessThanTransCnt,
                                       @Param("timestamp") int timestamp, @Param("minutes") int minutes);

    /***
     * 更新Refund补偿转单的时间戳
     *
     * @param node node name
     * @param name table name
     * @param refundIds refundId集合(1,2,3...)
     * @param timestamp timestamp
     * @return
     */
    @UpdateProvider(type = TaobaoRefundSqlBuilder.class, method = "updateDynamicRefundTimeStamp")
    int updateDynamicRefundTimeStamp(@Param("node") String node,
                                     @Param("name") String name,
                                     @Param("refundIds") String refundIds,
                                     @Param("timestamp") int timestamp);

    class TaobaoRefundSqlBuilder {

        /**
         * 查询DRDS的分库RefundId
         *
         * @param node             node name
         * @param name             table name
         * @param size             page size
         * @param isTrans          trans status
         * @param lessThanTransCnt trans count
         * @param timestamp        second
         * @param minutes          Intervals
         * @return refund_id
         */
        public String selectDynamicRefundId(@Param("name") String name,
                                            @Param("size") int size, @Param("isTrans") int isTrans,
                                            @Param("lessThanTransCnt") int lessThanTransCnt,
                                            @Param("timestamp") int timestamp,
                                            @Param("minutes") int minutes) {

            StringBuilder sb = new StringBuilder();
            sb.append("SELECT refund_id FROM ");
            sb.append(name);
            sb.append(" WHERE istrans = ");
            sb.append(isTrans);
            if (lessThanTransCnt > 0) {
                sb.append(" AND IFNULL(trans_count,0) < ");
                sb.append(lessThanTransCnt);
            }
            sb.append(" AND IFNULL(reserve_bigint01,0) != ");
            sb.append(timestamp);
            if (minutes > 0) {
                sb.append(" AND creationdate > (DATE_SUB(NOW(),INTERVAL ");
                sb.append(minutes);
                sb.append(" MINUTE))");
            }
            sb.append(" ORDER BY modifieddate DESC");
            sb.append(" LIMIT ");
            sb.append(size);
            return sb.toString();
        }

        public String updateDynamicRefundTimeStamp(@Param("node") String node,
                                                   @Param("name") String name,
                                                   @Param("refundIds") String refundIds,
                                                   @Param("timestamp") int timestamp) {
            StringBuilder sb = new StringBuilder();
            sb.append("/*!TDDL:NODE=");
            sb.append(node);
            sb.append("*/ ");
            sb.append("UPDATE ");
            sb.append(name);
            sb.append(" SET reserve_bigint01 = ");
            sb.append(timestamp);
            sb.append(", modifieddate=NOW()");
            sb.append(" WHERE refund_id in (");
            sb.append(refundIds);
            sb.append(")");
            return sb.toString();
        }

        /**
         * 更新状态及次数
         */
        public String buildUpdateRefundTransSQL(@Param("id") long id, @Param("isTrans") int isTrans,
                                                @Param("isUpdateTransNum") boolean isUpdateTransNum, @Param("refundId"
        ) String refundId) {

            return new SQL() {
                {
                    UPDATE("ip_b_taobao_refund");
                    SET("istrans=#{isTrans}");
                    if (isUpdateTransNum) {
                        SET("trans_count = IFNULL(trans_count, 0) + 1");
                    }
                    WHERE("ID=#{id} and refund_id = #{refundId}");
                }
            }.toString();

        }

        public String update(JSONObject map) {
            return new SQL() {
                {
                    UPDATE("ip_b_taobao_refund");
                    SET("trans_count = IFNULL(trans_count, 0) + 1");
                    for (String key : map.keySet()) {
                        if (!"refund_id".equals(key)) {
                            SET(key + "= #{" + key + "}");
                        }
                    }
                    WHERE("refund_id = #{refund_id}");
                }
            }.toString();
        }


        public String updateByStatus(JSONObject map) {
            return new SQL() {
                {
                    UPDATE("ip_b_taobao_refund");
                    SET("trans_count = IFNULL(trans_count, 0) + 1");
                    for (String key : map.keySet()) {
                        if (!"refund_id".equals(key)) {
                            SET(key + "= #{" + key + "}");
                        }
                    }
                    WHERE("refund_id = #{refund_id} and status = 'WAIT_SELLER_AGREE'");
                }
            }.toString();
        }

        public String updateTransTip(JSONObject map) {
            return new SQL() {
                {
                    UPDATE("ip_b_taobao_refund");
                    for (String key : map.keySet()) {
                        if (!"refund_id".equals(key)) {
                            SET(key + "= #{" + key + "}");
                        }
                    }
                    WHERE("refund_id = #{refund_id}");
                }
            }.toString();
        }


    }


    /**
     * @param id
     * @param isTrans
     * @param isUpdateTransNum
     * @return
     */
    @UpdateProvider(type = TaobaoRefundSqlBuilder.class, method = "buildUpdateRefundTransSQL")
    int updateRefundIsTrans(@Param("id") long id, @Param("isTrans") int isTrans,
                            @Param("isUpdateTransNum") boolean isUpdateTransNum, @Param("refundId") String refundId);

    /**
     * 修改系统备注
     *
     * @param id
     * @param remark
     * @return
     */
    @Update("UPDATE ip_b_taobao_refund SET trans_count = IFNULL(trans_count, 0) + 1, sysremark=#{remark} WHERE " +
            "ID=#{id} and refund_id = #{refundId}")
    int updateRefundRemark(@Param("id") long id, @Param("refundId") String refundId, @Param("remark") String remark);


    @Update("UPDATE ip_b_taobao_refund SET trans_count = IFNULL(trans_count, 0) + 1, sysremark=#{remark}," +
            "istrans=#{isTrans} WHERE ID=#{id} and refund_id = #{refundId}")
    int updateRefundRemarkAndIsTrans(@Param("id") long id, @Param("refundId") String refundId,
                                     @Param("isTrans") int isTrans, @Param("remark") String remark);


    @Update("UPDATE ip_b_taobao_refund SET sku = #{sku} WHERE ID=#{id} and refund_id = #{refundId}")
    int updateRefundSku(@Param("id") long id, @Param("refundId") String refundId, @Param("sku") String sku);


    @UpdateProvider(type = TaobaoRefundSqlBuilder.class, method = "update")
    int updateRefundOrder(JSONObject jsonObject);

    /**
     * 更新转换节点提示
     *
     * @param jsonObject jsonObjct
     * @return int
     */
    @UpdateProvider(type = TaobaoRefundSqlBuilder.class, method = "updateTransTip")
    int updateTransTip(JSONObject jsonObject);


    @Select("<script> "
            + "SELECT * FROM ip_b_taobao_refund WHERE refund_id in"
            + "<foreach item='item' index='index' collection='refundIds' open='(' separator=',' close=')'> #{item} " +
            "</foreach>"
            + "</script>")
    List<IpBTaobaoRefund> selectTaobaoRefundByRefundIdList(@Param("refundIds") List<String> refundIds);


    @Update("UPDATE ip_b_taobao_refund SET reserve_bigint10 = #{type} WHERE refund_id = #{refundId}")
    int updateRefundType(@Param("refundId") String refundId, @Param("type") Integer type);


    /**
     * 根据退款状态 来更新数据 (买家申请退款 等待卖家同意)
     * @param jsonObject
     * @return
     */
    @UpdateProvider(type = TaobaoRefundSqlBuilder.class, method = "updateByStatus")
    int updateRefundOrderByStatus(JSONObject jsonObject);

}