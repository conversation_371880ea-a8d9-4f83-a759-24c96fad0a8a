package com.jackrain.nea.oc.oms.process;

import com.jackrain.nea.cp.services.RegionNewService;
import com.jackrain.nea.cpext.model.ProvinceCityAreaInfo;
import com.jackrain.nea.ip.model.CancelGoodsModel;
import com.jackrain.nea.oc.oms.model.relation.OmsTaobaoExchangeRelation;
import com.jackrain.nea.oc.oms.process.transfer.impl.exchange.taobaonew.TaobaoTransferExchangeProcessImpl;
import com.jackrain.nea.oc.oms.services.IpTaobaoExchangeService;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.services.OmsTaobaoExchangeService;
import com.jackrain.nea.oc.oms.step.ProcessStepResultList;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.web.face.User;
import org.apache.commons.lang3.math.NumberUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Profile;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: sunys
 * @since: 2019-01-21
 * create at : 2019-01-21 21:28
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest()
@Profile(value = "dev")
public class TransferExchangeProcessTest {

    @Autowired
    private TaobaoTransferExchangeProcessImpl taobaoTransferExchangeProcess;

    @Autowired
    private IpTaobaoExchangeService ipTaobaoExchangeService;

    @Autowired
    private IpRpcService ipRpcService;

    @Autowired
    private RegionNewService regionNewService;

    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private OmsTaobaoExchangeService omsTaobaoExchangeService;


    @Before
    public void initial() {
//        distributeProcess = new TransferWaitDistributeProcess();

    }

    @Test
    public void testDistributeProcess001() throws IOException {
       // IpTaobaoExchangeRelation orderInfo = ipTaobaoExchangeService.selectTaobaoExchangeRelation("35153954958404111");
        //SpecialElasticSearchUtil.indexCreate(IpBTaobaoExchange.class);
        OmsTaobaoExchangeRelation relation = omsTaobaoExchangeService.selectTaobaoExchangeInfo("91290673072976634");
        ProcessStepResultList resultList = taobaoTransferExchangeProcess.start(relation, false, SystemUserResource.getRootUser());
        System.out.println(resultList.size());
    }


    @Test
    public void test1() {
//        try {
//            Boolean aBoolean = SpecialElasticSearchUtil.indexExists("oc_b_return_order");
//            if (!aBoolean) {
//                List<Class> list = new ArrayList<>();
//                list.add(OcBReturnOrderExchange.class);
//                list.add(OcBReturnOrderRefund.class);
//                SpecialElasticSearchUtil.indexCreate(list, OcBReturnOrder.class);
//            }
//        } catch (Exception e) {
//
//        }
    }

    @Test
    public void test2() {
        CancelGoodsModel model = new CancelGoodsModel();
        User rootUser = SystemUserResource.getRootUser();
        model.setOperateUser(rootUser);
        model.setOid(454225346503539567L);
        model.setRefundId(26630882135536795L);
        model.setOperateTime(new Date());
        model.setRefundFee(NumberUtils.toLong(BigDecimal.valueOf(0.99).multiply(BigDecimal.valueOf(100)).toString()));
        model.setStatus("SUCCESS");
        model.setTid(454225346503539567L);
        model.setSessionKey("6100e06f2fd13a1126577ed9d618647df6d84e36000f6d3391142628");
        boolean b = ipRpcService.cancelGoodsToAg(model);
        System.out.println(b);
    }

    @Test
    public void test3() {
        String province = "";
        String city = "";
        String area = " 琼海市";
        ProvinceCityAreaInfo provinceCityAreaInfo = regionNewService.selectProvinceCityAreaInfo(province, city, area);
        System.out.println(provinceCityAreaInfo);

    }

    @Test
    public void test4() {
        User rootUser = SystemUserResource.getRootUser();
        // User rootUser = WmsUserResource.getWmsUser();;
        omsOrderLogService.addUserOrderLog(13040L, "11111",
                "121", "截单失败！订单流转中，稍后再截，谢谢", null, null, rootUser);

    }

}
