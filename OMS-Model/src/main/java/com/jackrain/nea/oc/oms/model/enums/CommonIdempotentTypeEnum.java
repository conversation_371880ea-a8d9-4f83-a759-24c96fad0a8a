package com.jackrain.nea.oc.oms.model.enums;

import lombok.Getter;

/**
 * 幂等表类型
 *
 * <AUTHOR>
 */
public enum CommonIdempotentTypeEnum {

    UN_KNOWN(" ", "未知"),
    TOB_PART_OUT("TOB_PART_OUT", "tob订单部分出库生成新订单"),
    BLACK_ST_CARD("BLACK_ST_CARD", "黑名单策略卡单新增策略控制");


    CommonIdempotentTypeEnum(String key, String name) {
        this.key = key;
        this.name = name;
    }

    @Getter
    private String key;

    @Getter
    private String name;


    /**
     * 根据状态值,获取状态名
     *
     * @param key
     * @return String
     */
    public static String enumToStringBykey(String key) {
        String s = "";
        if (key == null) {
            return s;
        }
        for (CommonIdempotentTypeEnum e : CommonIdempotentTypeEnum.values()) {
            if (e.getKey().equals(key)) {
                s = e.getName();
                return s;
            }
        }
        return key;
    }
}
