package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;


@Data
@TableName(value = "oc_b_refund_in_should_refund_item")
public class OcBRefundInShouldRefundItem extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "OC_B_REFUND_IN_ID")
    private Long ocBRefundInId;

    @JSONField(name = "PS_C_SKU_ID")
    private Long psCSkuId;

    @JSONField(name = "PS_C_SKU_ECODE")
    private String psCSkuEcode;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    @JSONField(name = "GBCODE")
    private String gbcode;

    @JSONField(name = "PS_C_PRO_ECODE")
    private String psCProEcode;

    @JSONField(name = "PS_C_PRO_ENAME")
    private String psCProEname;

    @JSONField(name = "APPLY_NUM")
    private BigDecimal applyNum;

    @JSONField(name = "ORDER_NUM")
    private BigDecimal orderNum;

    @JSONField(name = "DIFF_LESS")
    private BigDecimal diffLess;

    @JSONField(name = "DIFF_MORE")
    private BigDecimal diffMore;
}