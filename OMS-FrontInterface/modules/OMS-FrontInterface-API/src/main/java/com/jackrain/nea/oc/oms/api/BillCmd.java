package com.jackrain.nea.oc.oms.api;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;

/**
 * @Author: wang<PERSON><PERSON>
 * @Date: 2019-03-08 13:32
 * @Version 1.0
 */
public interface BillCmd {
    /**
     * 订单新增
     *
     * @param obj
     * @param user
     * @return
     */
    ValueHolder saveBill(JSONObject obj, User user) throws NDSException;

    /**
     * 更新订单明细的实缺接口
     *
     * @param object
     * @param user
     * @return
     */
    ValueHolder updateIsLackstock(JSONObject object, User user);
}
