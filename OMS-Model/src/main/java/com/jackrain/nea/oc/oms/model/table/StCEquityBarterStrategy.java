package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

/**
 * 对等换货策略
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-16 13:26:40
 */
@TableName(value = "st_c_equity_barter_strategy")
@Data
public class StCEquityBarterStrategy extends BaseModel {
    /**
     * ID
     */
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;
    /**
     * 类型 1公用
     * 2指定店铺
     */
    @JSONField(name = "TYPE")
    private String type;
    /**
     * 平台店铺
     */
    @JSONField(name = "CP_C_SHOP_ID")
    private Long cpCShopId;
    /**
     * 备注
     */
    @JSONField(name = "REMARK")
    private String remark;
    /**
     * 版本号
     */
    @J<PERSON>NField(name = "VERSION")
    private Long version;
    /**
     * 创建人姓名
     */
    @JSONField(name = "OWNERENAME")
    private String ownerename;
    /**
     * 修改人姓名
     */
    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

}
