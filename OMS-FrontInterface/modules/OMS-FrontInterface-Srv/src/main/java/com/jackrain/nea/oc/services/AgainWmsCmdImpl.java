package com.jackrain.nea.oc.services;

import com.jackrain.nea.oc.oms.api.AgainWmsCmd;
import com.jackrain.nea.oc.oms.services.AgainWmsService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2019-08-21 14:03
 * @Version 1.0
 */
@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class AgainWmsCmdImpl implements AgainWmsCmd {

    @Autowired
    private AgainWmsService againWmsService;

    @Override
    public ValueHolderV14 againWms(List<Long> orderIds) {
        return againWmsService.againWms(orderIds);
    }
}
