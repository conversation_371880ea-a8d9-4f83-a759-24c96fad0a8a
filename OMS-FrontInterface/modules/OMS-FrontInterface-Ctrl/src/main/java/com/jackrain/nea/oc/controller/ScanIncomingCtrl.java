package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBRefundOrderToAGCmd;
import com.jackrain.nea.oc.oms.api.RetransmissionWmsCmd;
import com.jackrain.nea.oc.oms.api.ScanIncomingCmd;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * @author: 孙俊磊
 * @since: 2019-03-24
 * create at:  2019-03-24 18:24
 * 退换货管理-扫描入库controller
 */

@Api(value = "ScanIncomingCtrl", tags = "退换货管理-扫描入库")
@Slf4j
@RestController
public class ScanIncomingCtrl {

    @Autowired
    private ScanIncomingCmd scanIncomingCmd;

    @Autowired
    private OcBRefundOrderToAGCmd ocBRefundOrderToAGCmd;

    @Autowired
    private RetransmissionWmsCmd retransmissionWmsCmd;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;


    @ApiOperation(value = "退换货管理-扫描入库")
    @RequestMapping(value = "/api/cs/oc/oms/v1/saveScanIncoming", method = RequestMethod.POST)
    public JSONObject saveScanIncoming(HttpServletRequest request, @RequestBody String param) {
        if (log.isDebugEnabled()) {
            log.debug("start ScanIncomingCtrl.saveScanIncoming.ReceiveParams=" + param);
        }
        User loginUser = r3PrimWebAuthService.getLoginPrimWebUser(request);

//        User loginUser = getLoginUser();

        ValueHolderV14 holderV14 = new ValueHolderV14();
        try {
            holderV14 = scanIncomingCmd.saveScanIncoming(param, loginUser);
        } catch (Exception ex) {
            holderV14.setCode(-1);
            holderV14.setMessage(ex.getMessage());
        }

        JSONObject result = JSONObject.parseObject(JSONObject.toJSONString(holderV14.toJSONObject(), SerializerFeature.WriteMapNullValue));
        return result;
    }

    @ApiOperation(value = "退换货管理-扫描入库-获取退换货信息")
    @RequestMapping(value = "/api/cs/oc/oms/v1/getScanIncomingInfo", method = RequestMethod.POST)
    public JSONObject getScanIncomingInfo(HttpServletRequest request, @RequestBody String param) {
        User loginUser = r3PrimWebAuthService.getLoginPrimWebUser(request);

//        User loginUser = getLoginUser();

        ValueHolderV14 holderV14 = new ValueHolderV14();
        try {
            holderV14 = scanIncomingCmd.getScanIncomingInfo(param, loginUser);
        } catch (Exception ex) {
            holderV14.setCode(-1);
            holderV14.setMessage(ex.getMessage());
        }

        JSONObject result = JSONObject.parseObject(JSONObject.toJSONString(holderV14.toJSONObject(), SerializerFeature.WriteMapNullValue));
        return result;
    }


    @ApiOperation(value = "退换货管理-扫描入库-获取一条退换货明细")
    @RequestMapping(value = "/api/cs/oc/oms/v1/getOneRefundItem", method = RequestMethod.POST)
    public JSONObject getOneRefundItem(HttpServletRequest request, @RequestBody String param) {
        User loginUser = r3PrimWebAuthService.getLoginPrimWebUser(request);

//        User loginUser = getLoginUser();

        ValueHolderV14 holderV14 = new ValueHolderV14();
        try {
            holderV14 = scanIncomingCmd.getOneRefundItem(param, loginUser);
        } catch (NDSException ex) {
            holderV14.setCode(-1);
            holderV14.setMessage(ex.getMessage());
        }

        JSONObject result = JSONObject.parseObject(JSONObject.toJSONString(holderV14.toJSONObject(), SerializerFeature.WriteMapNullValue));
        return result;
    }

    @ApiOperation(value = "退换货管理-扫描入库-获取当前用户创建的批次")
    @RequestMapping(value = "/api/cs/oc/oms/v1/getCurrentBatch", method = {RequestMethod.POST, RequestMethod.GET})
    public JSONObject getCurrentBatch(HttpServletRequest request, @Param("BATCH_TYPE") String batchType) {
        User loginUser = r3PrimWebAuthService.getLoginPrimWebUser(request);

//        User loginUser = getLoginUser();

        ValueHolderV14 holderV14 = new ValueHolderV14();

        try {
            holderV14 = scanIncomingCmd.getCurrentBatch(loginUser);
        } catch (NDSException ex) {
            holderV14.setCode(-1);
            holderV14.setMessage(ex.getMessage());
        }

        JSONObject result = JSONObject.parseObject(JSONObject.toJSONString(holderV14.toJSONObject(), SerializerFeature.WriteMapNullValue));

        return result;
    }

    @ApiOperation(value = "退换货管理-重传WMS")
    @RequestMapping(value = "/api/cs/oc/oms/v1/retransmissionWms", method = RequestMethod.POST)
    public JSONObject retransmissionWms(HttpServletRequest request, @RequestBody String param) {
        User loginUser = r3PrimWebAuthService.getLoginPrimWebUser(request);

//        User loginUser = getLoginUser();

        ValueHolderV14 holderV14 = new ValueHolderV14();
        try {
            holderV14 = retransmissionWmsCmd.retransmissionWms(param, loginUser);
        } catch (Exception ex) {
            holderV14.setCode(-1);
            holderV14.setMessage(ex.getMessage());
        }

        JSONObject result = JSONObject.parseObject(JSONObject.toJSONString(holderV14.toJSONObject(), SerializerFeature.WriteMapNullValue));

        return result;
    }

    /*
    private User getLoginUser() {
        UserImpl user=new UserImpl();
        user.setMemberId(0);
        user.setTruename("");
        user.setClientId(0);
        user.setClientName("");
        user.setStoreId(0);
        user.setStoreName("");
        user.setOrgId(0);
        user.setEmail("");
        user.setPhone("");
        user.setId(17);
        user.setName("");
        user.setEname("");
        user.setPasswordHash("");
        user.setPwdrand("");
        user.setIsEnabled(0);
        user.setIsEmployee(0);
        user.setIsDev(0);
        user.setIsAdmin(0);
        user.setDescription("");
        user.setActive(false);
        user.setSecurityGrade(0);
        user.setPasswordExpireDateTime(new Date());
        user.setFailedLoginAttempts(0);
        user.setLastlogindate(new Date());
        user.setLastloginip("");
        user.setHeadmg("");
        user.setLoginIPRule("");
        user.setSupClientId(0);
        user.setUserOption("","");
        user.setUserenv(new HashMap());

        return user;
    }
*/

    @ApiOperation(value = "退换货管理-扫描入库-获取逻辑仓信息")
    @RequestMapping(value = "/api/cs/oc/oms/v1/cpStoreInfo", method = RequestMethod.POST)
    public JSONObject getLogicalWarehouseInfo(HttpServletRequest request, @RequestBody String param) {
        User loginUser = r3PrimWebAuthService.getLoginPrimWebUser(request);

//        User loginUser = getLoginUser();
        JSONObject jsonObject = JSON.parseObject(param);
        Long batch_id = jsonObject.getLong("BATCH_ID");
        ValueHolderV14 holderV14 = new ValueHolderV14();
        try {
            holderV14 = scanIncomingCmd.getLogicalWarehouseInfo(batch_id);
        } catch (Exception ex) {
            holderV14.setCode(-1);
            holderV14.setMessage(ex.getMessage());
        }

        JSONObject result = JSONObject.parseObject(JSONObject.toJSONString(holderV14.toJSONObject(), SerializerFeature.WriteMapNullValue));
        return result;
    }
}
