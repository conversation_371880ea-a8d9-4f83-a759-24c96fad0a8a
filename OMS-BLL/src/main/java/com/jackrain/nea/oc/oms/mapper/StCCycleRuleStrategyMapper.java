package com.jackrain.nea.oc.oms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jackrain.nea.oc.oms.model.table.StCCycleRuleStrategy;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @ClassName StCCycleRuleStrategyMapper
 * @Description 周期购促销规则
 * <AUTHOR>
 * @Date 2024/8/19 14:52
 * @Version 1.0
 */
@Mapper
public interface StCCycleRuleStrategyMapper extends BaseMapper<StCCycleRuleStrategy> {

    @Select("<script> "
            + "SELECT * FROM st_c_cycle_rule_strategy WHERE isactive = 'Y' and strategy_id "
            + "in <foreach item='item' index='index' collection='strategyIdList' "
            + "open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<StCCycleRuleStrategy> selectByStrategyIdList(@Param("strategyIdList") List<Long> strategyIdList);

    @Delete("DELETE FROM st_c_cycle_rule_strategy WHERE strategy_id = #{strategyId}")
    int deleteByStrategyId(@Param("strategyId") Long strategyId);

    @Select("SELECT * FROM st_c_cycle_rule_strategy WHERE strategy_id = #{strategyId} and isactive = 'Y'")
    List<StCCycleRuleStrategy> selectByStrategyIdWithActive(Long strategyId);

    @Select("SELECT * FROM st_c_cycle_rule_strategy WHERE strategy_id = #{strategyId}")
    List<StCCycleRuleStrategy> selectByStrategyIdWithOutActive(Long strategyId);
}
