package com.jackrain.nea.oc.oms.services.delivery.impl;

import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.ip.model.LogisticsSendModel;
import com.jackrain.nea.ip.model.result.LogisticsSendResult;
import com.jackrain.nea.oc.oms.model.enums.OcBorderListEnums;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.services.OrderPlatformDeliveryService;
import com.jackrain.nea.oc.oms.services.delivery.OrderDeliveryCmd;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * Description:京东平台发货实现类
 *
 * <AUTHOR> sunies
 * @since : 2020-11-03
 * create at : 2020-11-03 20:02
 */
@Slf4j
@Component
public class OrderDeliveryOfJingDongImpl implements OrderDeliveryCmd {

    @Autowired
    private OrderPlatformDeliveryService orderPlatformDeliveryService;


    @Override
    public boolean deliveryDeal(OcBOrderRelation ocBOrderRelation, List<String> tips) {
        /**
         * ☆京东只需要考虑正常订单和合单，拆单不需要，因为是平台拆,但是有仓库拆
         * ☆多包裹也无需考虑，因为京东是整单发货
         * ☆京东无虚拟发货，正常订单和换货单是同一个接口
         *
         */
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("京东平台发货服务,订单id为={}", "京东平台发货服务", ocBOrderRelation.getOrderId()), ocBOrderRelation.getOrderId());
        }
        List<OcBOrderItem> orderItemList = ocBOrderRelation.getOrderItemList();
        OcBOrder orderInfo = ocBOrderRelation.getOrderInfo();
        orderItemList = orderItemList.stream().filter(s -> StringUtils.isNotEmpty(s.getOoid())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderItemList)) {
            return orderPlatformDeliveryService.updateOrderAfterPlatDeliverySuccess(orderInfo);
        }
        AtomicBoolean result = new AtomicBoolean(true);
        Map<String, List<OcBOrderItem>> collect = orderItemList.stream().collect(Collectors.groupingBy(OcBOrderItem::getTid));
        //TODO 多包裹待考虑
        collect.forEach((tid, items) -> {
            LogisticsSendModel tbLogisticsModel = new LogisticsSendModel();
            boolean split = OcBorderListEnums.YesOrNoEnum.IS_NO.getVal().equals(orderInfo.getIsSplit());
            tbLogisticsModel.setIsSplit(split ? 0L : 1L);
            //平台单号
            tbLogisticsModel.setTid(Long.valueOf(tid));
            //运单号
            tbLogisticsModel.setOutSid(orderInfo.getExpresscode());
            //平台为京东时，店铺类型“SOP”
            tbLogisticsModel.setShoptype("SOP");
            //子订单集合
            tbLogisticsModel.setSubTid(tid);
            //0=正常;1=换货
            tbLogisticsModel.setOrdertype(0L);
            tbLogisticsModel.setCompanyCode(orderPlatformDeliveryService.getLogisticCode(orderInfo.getCpCLogisticsId(), Long.valueOf(orderInfo.getPlatform())));
            tbLogisticsModel.setLogisticsCompanyName(orderInfo.getCpCLogisticsEname());
            if (OcBorderListEnums.YesOrNoEnum.IS_YES.getVal().equals(items.get(0).getIsExchangeItem())) {
                //换货单号
                tbLogisticsModel.setDisputeId(Long.valueOf(tid));
                tbLogisticsModel.setLogisticsType(200L);
                tbLogisticsModel.setOrdertype(1L);
            }
            ValueHolderV14<List<LogisticsSendResult>> v14 = orderPlatformDeliveryService.packageNormalInterfaceParam(tbLogisticsModel, orderInfo);
            result.set(result.get() && v14.getCode() == ResultCode.SUCCESS);
        });
        return result.get();
    }
}
