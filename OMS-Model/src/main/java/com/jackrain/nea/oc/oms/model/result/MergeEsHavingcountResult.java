package com.jackrain.nea.oc.oms.model.result;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.oc.oms.model.relation.MergeOderGroups;
import lombok.Data;

/**
 * <AUTHOR> 孙勇生
 * create at:  2019/4/3  13:10
 * @description: 合并订单ES分组返回对象
 */
@Data
public class MergeEsHavingcountResult {
    @JSONField(name = "key")
    private MergeOderGroups mergeOderGroups;
    private Long total;
}
