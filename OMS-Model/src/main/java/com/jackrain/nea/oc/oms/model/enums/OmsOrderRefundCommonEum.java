package com.jackrain.nea.oc.oms.model.enums;

/**
 * <AUTHOR> 孙俊磊
 * @since :  2019-03-26
 * create at:  2019-03-26 20:01
 * 退单虚拟入库状态
 * 一  虚拟入库状态,0未虚拟入库，1虚拟入库未入库，2虚拟入库已入库'
 * 二 退单状态为等待退货入库且是否原退字段值为否 0否1是
 * 三  1 处理中     2 已完结    3 已调拨 ，4已作废
 */
public enum OmsOrderRefundCommonEum {
    //未虚拟入库
    ZERO,
    ONE,
    TWO;

    public int parseValue() {
        if (this == ZERO) {
            return 0;
        } else if (this == ONE) {
            return 1;
        } else {
            return 2;
        }
    }
}
