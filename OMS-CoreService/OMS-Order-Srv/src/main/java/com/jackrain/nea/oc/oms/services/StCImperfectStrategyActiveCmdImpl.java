package com.jackrain.nea.oc.oms.services;

import com.alibaba.dubbo.config.annotation.Service;
import com.jackrain.nea.annotation.OmsOperationLog;
import com.jackrain.nea.cpext.utils.ValueHolderUtils;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.StCImperfectStrategyActiveCmd;
import com.jackrain.nea.oc.oms.mapper.StCImperfectStrategyItemMapper;
import com.jackrain.nea.oc.oms.mapper.StCImperfectStrategyMapper;
import com.jackrain.nea.oc.oms.model.resources.OmsRedisKeyResources;
import com.jackrain.nea.oc.oms.model.table.StCImperfectStrategy;
import com.jackrain.nea.oc.oms.model.table.StCImperfectStrategyItem;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.st.model.StCImperfectStrategyRelation;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;

/**
 * 残次策略-启用
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oc-core")
public class StCImperfectStrategyActiveCmdImpl extends CommonCommandAdapter implements StCImperfectStrategyActiveCmd {

    @Resource
    private StCImperfectStrategyMapper stCImperfectStrategyMapper;
    @Resource
    private StCImperfectStrategyItemMapper stCImperfectStrategyItemMapper;
    @Resource
    private StCImperfectStrategyServiceImpl stCImperfectStrategyService;

    @Override
    @OmsOperationLog(operationType = "OPEN", mainTableName = "ST_C_IMPERFECT_STRATEGY", itemsTableName = "ST_C_IMPERFECT_STRATEGY_ITEM")
    public ValueHolder execute(QuerySession session) throws NDSException {
        List<Long> objIds = getObjIds(session);

        if (CollectionUtils.isNotEmpty(objIds)) {
            for (Long objId : objIds) {
                StCImperfectStrategy strategy = stCImperfectStrategyMapper.selectById(objId);
                if (strategy == null) {
                    continue;
                }
                List<StCImperfectStrategyItem> itemStrategyList = stCImperfectStrategyItemMapper.selectByStrategyIdWithOutActive(objId);
                if (CollectionUtils.isEmpty(itemStrategyList)) {
                    return ValueHolderUtils.fail("策略" + strategy.getStrategyCode() + "启用失败!失败原因:规则明细未维护");
                }
                StCImperfectStrategyRelation strategyRelation = new StCImperfectStrategyRelation();
                strategyRelation.setStCImperfectStrategy(strategy);
                strategyRelation.setStCImperfectStrategyItems(itemStrategyList);
                try {
                    stCImperfectStrategyService.checkRule(strategyRelation);
                } catch (Exception e) {
                    return ValueHolderUtils.fail("策略" + strategy.getStrategyCode() + "启用失败!失败原因:" + e.getMessage());
                }

                stCImperfectStrategyMapper.activeStrategy(objId);

                //删除缓存
                String strategyRedisKey = OmsRedisKeyResources.buildStCImperfectStrategyRedisKey(strategy.getShopId());
                RedisOpsUtil.getStrRedisTemplate().delete(strategyRedisKey);
            }
        }
        return ValueHolderUtils.success("启用成功");
    }

    @Override
    public ValueHolder execute(HashMap map) throws NDSException {
        return null;
    }
}
