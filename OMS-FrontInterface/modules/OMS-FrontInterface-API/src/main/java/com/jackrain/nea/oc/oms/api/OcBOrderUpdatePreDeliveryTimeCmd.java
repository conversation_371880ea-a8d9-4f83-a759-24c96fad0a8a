package com.jackrain.nea.oc.oms.api;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

/**
 * <AUTHOR> wang<PERSON>yu
 * @since : 2022/9/6
 * description : 修改订单预计发货时间
 */
public interface OcBOrderUpdatePreDeliveryTimeCmd {
    /**
     * 修改预计发货时间
     * @param obj 参数
     * @param user 用户
     * @return ret
     */
    ValueHolderV14<String> updatePreDeliveryTime(JSONObject obj, User user);
}
