package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.mapper.task.CardAutoVoidTaskSql;
import com.jackrain.nea.oc.oms.model.table.OcBReturnAfSend;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
public interface OcBReturnAfSendMapper extends ExtentionMapper<OcBReturnAfSend> {

    @SelectProvider(type = CardAutoVoidTaskSql.class, method = "selectCardAutoVoid")
    List<OcBReturnAfSend> selectCardAutoVoid(@Param(value = "limit") int limit,
                                             @Param(value = "taskTableName") String taskTableName);

    @Select("SELECT * FROM oc_b_return_af_send WHERE t_return_id = #{refundId} and return_status != 3 ")
    OcBReturnAfSend selectOcBReturnAfSendByRefundId(@Param("refundId") String refundId);

    @Select("SELECT * FROM oc_b_return_af_send WHERE t_return_id = #{refundId} and return_status != 3 ")
    List<OcBReturnAfSend> selectListByRefundId(@Param("refundId") String refundId);

    @Select("SELECT * FROM OC_B_RETURN_AF_SEND WHERE T_RETURN_ID = #{tReturnId} AND ISACTIVE='Y'")
    List<OcBReturnAfSend> listQueryByTReturnId(@Param("tReturnId") String tReturnId);

    @Select("SELECT * FROM OC_B_RETURN_AF_SEND WHERE T_RETURN_ID = #{refundId} AND RETURN_STATUS !=3 AND ISACTIVE='Y'")
    List<OcBReturnAfSend> listReturnAfSend4ReturnAudit(@Param("refundId") String refundId);

    @Update("UPDATE oc_b_return_af_send SET t_return_status = #{refundStatus} WHERE t_return_id = #{refundId} and return_status != 3 ")
    int updateOcBReturnAfSend(@Param("refundStatus") String refundStatus, @Param("refundId") String refundId);

    @Select("SELECT * FROM OC_B_RETURN_AF_SEND WHERE tid = #{tid} AND RETURN_STATUS !=3 AND ISACTIVE='Y'")
    List<OcBReturnAfSend> listReturnAfSend5ReturnAudit(@Param("tid") String tid);

//    /**
//     * 更新传AC状态
//     * @param toACStatus
//     * @param tReturnId
//     * @return
//     */
//    @Update("UPDATE oc_b_return_af_send SET reserve_bigint02 = #{toACStatus} WHERE t_return_id = #{tReturnId}")
//    int updateToACStatusByTReturnId(@Param("toACStatus") Integer toACStatus, @Param("tReturnId") String tReturnId);

    /**
     * 批量更新
     *
     * @param toACStatus
     * @param tReturnIds
     * @return
     */
    @Update("<script> "
            + "UPDATE oc_b_return_af_send SET TO_SETTLE_STATUS = #{toACStatus}, modifieddate = now() WHERE t_return_id "
            + "in <foreach item='item' index='index' collection='tReturnIds' open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    int updateToACStatusByTReturnIds(@Param("toACStatus") Integer toACStatus, @Param("tReturnIds") List<String> tReturnIds);

    @Update("UPDATE oc_b_return_af_send SET return_status = #{refundStatus} WHERE t_return_id = #{refundId} and return_status != 3")
    int updateOcBReturnAfSendByReturnStatus(@Param("refundStatus") Integer refundStatus, @Param("refundId") String refundId);

    @SelectProvider(type = AfSqlProvider.class, method = "selectList4Sap")
    List<OcBReturnAfSend> selectList4Sap(@Param("sqlParam") String sqlParam);

    /**
     * @param tReturnIds
     * @return
     * @20200807 按t_return_id列表查询对象列表
     */
    @Select("<script> "
            + "SELECT * FROM oc_b_return_af_send WHERE t_return_id "
            + "in <foreach item='item' index='index' collection='tReturnIds' open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<OcBReturnAfSend> selectOcBReturnAfSendListByTReturnId(@Param("tReturnIds") List<String> tReturnIds);

    @Select("<script> "
            + "SELECT * FROM oc_b_return_af_send WHERE id "
            + "in <foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<OcBReturnAfSend> selectOcBReturnAfSendListById(@Param("ids") List<Long> ids);

    @Delete("delete from oc_b_return_af_send where t_return_id = #{refundId}")
    int del(@Param("refundId") String refundId);

    @Update("UPDATE oc_b_return_af_send SET return_status = #{refundStatus} WHERE t_return_id = #{refundId} and return_status != 3")
    int updateOcBReturnAfStatus(@Param("refundStatus") Integer refundStatus, @Param("refundId") String refundId);


    @Update("UPDATE oc_b_return_af_send SET return_status = #{refundStatus}," +
            "t_return_status = #{status}," +
            "check_time= now(),financial_audit_time = now() WHERE t_return_id = #{refundId} and return_status != 3")
    int updateOcBReturnAfSendRefundStatus(@Param("refundStatus") Integer refundStatus, @Param("status") String status, @Param("refundId") String refundId);

    @Update("UPDATE oc_b_return_af_send SET payment_status = #{paymentStatus},return_payment_time = now() WHERE id = #{refundId}")
    int updateOcBPaymentStatus(@Param("paymentStatus") Integer paymentStatus, @Param("refundId") Long refundId);

    @Update("UPDATE oc_b_return_af_send SET payment_status = #{paymentStatus},return_status = #{refundStatus},un_examine_time = now() WHERE id = #{refundId}")
    int unExamineTheFefundAfterDelivery(@Param("paymentStatus") Integer paymentStatus, @Param("refundStatus") Integer refundStatus, @Param("refundId") Long refundId);


    @Update("UPDATE oc_b_return_af_send SET reason = #{reason} WHERE t_return_id = #{refundId} and return_status != 3")
    int updateOcBReturnAfReason(@Param("reason") String reason, @Param("refundId") String refundId);

    /**
     * 更新传ag状态
     *
     * @param ocBReturnAfSend 退款单
     * @return 更新数量
     */
    @Update("UPDATE OC_B_RETURN_AF_SEND SET AG_STATUS=#{agStatus}, MODIFIEDDATE = NOW(), MODIFIERNAME=#{modifiername},"
            + " MODIFIERENAME=#{modifierename}, MODIFIERID=#{modifierid} WHERE T_RETURN_ID=#{tReturnId} AND `ID`=#{id}")
    int updateAgStatus(OcBReturnAfSend ocBReturnAfSend);


    /**
     * 查询要传支付宝对账的已发货退款单(淘宝),
     *
     * @param node
     * @param limit
     * @return
     */
    @SelectProvider(type = OcBReturnAfSendMapper.AfSqlProvider.class, method = "selectAf2SettlementByNode")
    List<OcBReturnAfSend> selectAf2SettlementByNode(@Param("node") String node, @Param("tableName") String tableName
            , @Param("limit") Integer limit);

    @Select("<script>" +
            "select * from oc_b_return_af_send where bill_type = #{billType} and return_status != 3 and isactive = 'Y' and tid in " +
            "<foreach item='item' index='index' collection='tids' open='(' separator=',' close=')'> #{item} </foreach>" +
            "</script>")
    List<OcBReturnAfSend> queryIdsByBillTypeAndTid(@Param("tids") List<String> tids,@Param("billType") int billType);

    /**
     * sql
     * author by zhang.xiwen
     */
    class AfSqlProvider {

        /**
         * 查询退换货订单信息集合.FOR SAP
         *
         * @param sqlParam ids
         */
        public String selectList4Sap(@Param("sqlParam") String sqlParam) {
            StringBuilder sb = new StringBuilder("SELECT * FROM OC_B_RETURN_AF_SEND WHERE ID IN (");
            sb.append(sqlParam).append(" )")
                    .append(" AND IFNULL(SAP_STATUS,0) =0 AND ISACTIVE='Y' ");
            return sb.toString();
        }

        /**
         * 退款完成 and 待传或者失败 and 来源为自动 平台为 淘宝
         *
         * @param node
         * @param tableName
         * @param limit
         * @return
         */
        public String selectAf2SettlementByNode(@Param("node") String node, @Param("tableName") String tableName
                , @Param("limit") Integer limit) {
            StringBuilder sb = new StringBuilder();
            sb.append("/*!TDDL:NODE=")
                    .append(node)
                    .append("*/ ")
                    .append("SELECT * FROM ")
                    .append(tableName)
                    .append(" where return_status = 2 ")
                    .append(" and to_settle_status in(0,1,3)")
                    .append(" and refund_order_source_type =2 ")
                    .append(" and cp_c_platform_id = 2 limit ")
                    .append(limit);
            return sb.toString();
        }


    }

    @Update("UPDATE oc_b_return_af_send SET return_status = #{refundStatus}," +
            "t_return_status = #{status}," +
            "check_time= now(),financial_audit_time = now(),return_payment_time = now() WHERE t_return_id = #{refundId} and return_status != 3")
    int updateOcBReturnAfSendRefundStatusAndTime(@Param("refundStatus") Integer refundStatus, @Param("status") String status, @Param("refundId") String refundId);


    @Select("SELECT * FROM oc_b_return_af_send WHERE bill_no = #{billNo}")
    List<OcBReturnAfSend> selectByBillNo(@Param("billNo") String billNo);

    @Select("SELECT * FROM oc_b_return_af_send WHERE source_bill_no = #{sourceBillNo}")
    List<OcBReturnAfSend> selectBySourceBillNo(@Param("sourceBillNo") String sourceBillNo);
}
