package com.jackrain.nea.oc.oms.model.enums;

/**
 * 判断条件，物流方案包裹明细表
 *
 * @author: 胡林洋
 * @since: 2019-03-15
 * create at : 2019-03-15 11:21
 */
public enum OmsJudgingCondition {
    /**
     * 小于
     */
    LESS_THAN,
    /**
     * 小于等于
     */
    LESS_THAN_OR_EQUAL_TO,
    /**
     * 等于
     */
    EQUAL_TO,
    /**
     * 大于
     */
    GREAT_THAN,
    /**
     * 大于等于
     */
    GREAT_THAN_OR_EQUAL_TO,
    /**
     * 介于
     */
    BETWEEN;

    public String paseValue() {
        if (this == LESS_THAN) {
            return "8";
        } else if (this == LESS_THAN_OR_EQUAL_TO) {
            return "10";
        } else if (this == EQUAL_TO) {
            return "5";
        } else if (this == GREAT_THAN) {
            return "7";
        } else if (this == GREAT_THAN_OR_EQUAL_TO) {
            return "9";
        } else if (this == BETWEEN) {
            return "11";
        } else {
            return "0";
        }
    }

}
