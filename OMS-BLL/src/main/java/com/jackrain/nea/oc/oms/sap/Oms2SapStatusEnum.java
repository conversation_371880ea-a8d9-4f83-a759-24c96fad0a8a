package com.jackrain.nea.oc.oms.sap;

/**
 * @Desc : oms to sap task status
 * <AUTHOR> xiWen
 * @Date : 2020/3/23
 */
public enum Oms2SapStatusEnum {

    /**
     * 待发
     */
    WAIT,

    /**
     * 发送成功 / 传输中
     */
    SUCCESS,

    /**
     * 完成
     */
    FINISH,

    /**
     * 失败
     */
    FAILED,

    /**
     * 异常单据
     * 已处理, 用于task表存在, 而原单不存在,或查询转换原单时发生异常
     */
    PROCESSED;

    public int val() {
        switch (this) {
            case WAIT:
                return 0;
            case SUCCESS:
                return 1;
            case FINISH:
                return 2;
            case FAILED:
                return 3;
            case PROCESSED:
                return 4;
        }
        return 0;
    }
}
