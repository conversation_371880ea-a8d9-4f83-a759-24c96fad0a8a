package com.jackrain.nea.oc.oms.api;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.model.table.OcBorderItemExtention;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

import java.util.List;
import java.util.Map;

/**
 * @author: 孙俊磊
 * @since: 2019-03-11
 * create at:  2019-03-11 17:50
 * 订单中心-订单管理-订单单对象-订单明细
 */
public interface OcBorderDetailCmd {

    /**
     * 获取订单明细列表（带条码规格）
     *
     * @param param     包含页码，每页数量，主表id的json数据
     * @param loginUser user
     * @return 包含页码，总页数，data的数据
     */
    ValueHolderV14 getOrderDetailList(String param, User loginUser) throws NDSException;

    ValueHolderV14<Map<Long, List<OcBorderItemExtention>>> getBatchOrderDetailList(List<JSONObject> param, User loginUser) throws NDSException;

    /**
     * 订单单对象-订单明细-保存规格
     *
     * @param param     包含订单明细id，规格文本的JsonArray
     * @param loginUser 当前操作用户信息
     * @return ValueHolderV14 包含成功信息
     */
    ValueHolderV14 saveStandards(String param, User loginUser) throws NDSException;
}
