package com.jackrain.nea.oc.oms.matcher.parser;

import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/06/15
 * 策略规则匹配所需信息提取
 */
public interface ILiveMatchInfoParser<T> {

    /**
     * 渠道原单解析，得出规则匹配锁需要的信息
     *
     * @param channelOrigOrder
     * @param <T>
     * @return
     */
    public void doParser(T channelOrigOrder, OcBOrder order, List<OcBOrderItem> items);

    /**
     * 渠道类型
     *
     * @return
     */
    public ChannelType getCurrentChannelType();

}
