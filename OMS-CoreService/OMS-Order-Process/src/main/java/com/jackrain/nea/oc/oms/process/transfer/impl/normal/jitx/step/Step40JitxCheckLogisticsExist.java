package com.jackrain.nea.oc.oms.process.transfer.impl.normal.jitx.step;

import com.jackrain.nea.config.Resources;
import com.jackrain.nea.cpext.model.LogisticsInfo;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpJitxOrderRelation;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.web.face.User;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 检查物流公司是否在系统中
 *
 * @author: 黄超
 * @since: 2019-06-27
 * create at : 2019-06-27 9:00
 */
@Step(order = 40, description = "检查物流公司是否在系统中")
@Component
public class Step40JitxCheckLogisticsExist extends BaseJitxOrderProcessStep
        implements IOmsOrderProcessStep<IpJitxOrderRelation> {

    @Autowired
    private CpRpcService cpRpcService;

    @Override
    public ProcessStepResult<IpJitxOrderRelation> startProcess(IpJitxOrderRelation orderInfo,
                                                               ProcessStepResult preStepResult,
                                                               boolean isAutoMakeup, User operateUser) {
        String orderNo = orderInfo.getOrderNo();

        boolean hasErrorInfo = false;
        StringBuilder sbErrorInfo = new StringBuilder();

        String carrierCode = orderInfo.getJitxOrder().getCarrierCode();
        if (StringUtils.isEmpty(carrierCode)) {
            hasErrorInfo = true;
            sbErrorInfo.append("承运商编码为空;");
            sbErrorInfo.append("\r\n");
        } else {
            //LogisticsInfo logisticsInfo = cpRpcService.selectLogisticsInfoByCarrierCode(carrierCode);
            LogisticsInfo logisticsInfo = cpRpcService.selectLogisticsInfoByVipCarrierCode(carrierCode);
            if (logisticsInfo == null) {
                hasErrorInfo = true;
                String msg = Resources.getMessage("不存在;");
                sbErrorInfo.append("物流公司档案中唯品会物流公司编码:");
                sbErrorInfo.append(carrierCode);
                sbErrorInfo.append(msg);
                sbErrorInfo.append("\r\n");
            } else {
                orderInfo.setLogisticsInfo(logisticsInfo);
            }
        }

        if (hasErrorInfo) {
            String errorMessage = "物流公司数据不存在，退出转单操作";
            boolean updateStatusRes = ipJitxOrderService.updateJitxOrderTransStatus(orderNo,
                    TransferOrderStatus.NOT_TRANSFER, sbErrorInfo.toString());
            if (!updateStatusRes) {
                errorMessage += ";更新状态失败=False";
            }
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        } else {
            return new ProcessStepResult<>(StepStatus.SUCCESS, null, "检查物流公司是否存在成功，进入下一阶段",
                    Step50JitxCheckWarehouseExist.class);
        }
    }
}
