package com.jackrain.nea.oc.oms.sap;


import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.es.ES4ReturnOrder;
import com.jackrain.nea.oc.oms.gsi.GSI4Order;
import com.jackrain.nea.oc.oms.gsi.GSI4OrderItem;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderRefundMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.services.OcCancelChangingOrRefundService;
import com.jackrain.nea.oc.oms.services.OmsMarkCancelService;
import com.jackrain.nea.oc.oms.services.refund.OmsReturnUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.oc.request.sap.PodHandleRequest;
import com.jackrain.nea.oc.request.sap.SapCancelRequest;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.ValueHolderV14Utils;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * sap common
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class SapCommonService {

    @Autowired
    private OcBOrderMapper orderMapper;

    @Autowired
    private OcBOrderItemMapper orderItemMapper;

    @Autowired
    private OmsMarkCancelService omsMarkCancelService;


    @Autowired
    private OcBReturnOrderMapper returnOrderMapper;

    @Autowired
    private OcBReturnOrderRefundMapper refundMapper;

    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    @Autowired
    private OcCancelChangingOrRefundService refundService;
    @Autowired
    private OmsReturnUtil omsReturnUtil;


    private static User SAP_USER;

    private static List<Integer> ORDER_STATUS;

    private static List<Integer> CAN_CANCEL_ORDER_STATUS;

    private static List<String> TERMINATION_TYPE;

    private static List<Integer> NOT_CANCEL_ORDER_STATUS;

    private static List<Integer> NEED_THE_AUDIT_ORDER_STATUS;

    private static List<Integer> USE_FUl_ORDER_STATUS;

    private static List<Integer> RETURN_STATUS;

    private static String STR = "A,B";

    //终止状态(1-全部，2-部门)
    private final  static String TERMINATION_TYPE_01 = "1";

    private final  static String TERMINATION_TYPE_02 = "2";


    public ValueHolderV14 podHandle(JSONObject params) {

        if (log.isDebugEnabled()) {
            log.debug("Start SapCommonService podHandle:{}", params);
        }
        PodHandleRequest request = null;
        try {
            request = JSONObject.parseObject(params.toJSONString(), PodHandleRequest.class);
        } catch (Exception e) {
            log.error("podHandle error e," + e.getMessage(), e);
            return ValueHolderV14Utils.getFailValueHolder("参数格式化异常!请检查入参!");
        }

        ValueHolderV14 validate = validate(request);
        if (validate != null) {
            return validate;
        }

        String billNo = request.getBillNo();

        Long id = ES4Order.getIdByBillNo(billNo);
        if (id == null) {
            log.info(LogUtil.format("根据bill:{}，未查询到订单信息", billNo), billNo);
            return ValueHolderV14Utils.getFailValueHolder("签收失败,签收的单据不存在!");
        }
        OcBOrder ocBOrder = orderMapper.selectById(id);

        if (!getOrderStatus().contains(ocBOrder.getOrderStatus())) {
            log.info(LogUtil.format("根据bill:{}，未查询到订单信息", billNo), billNo);
            return ValueHolderV14Utils.getFailValueHolder("签收失败,签收的单据状态为:" + OmsOrderStatus.toDesc(ocBOrder.getOrderStatus()) + "不满足签收!");
        }

        List<OcBOrderItem> ocBOrderItems = orderItemMapper.selectUnSuccessRefund(id);
        if (CollectionUtils.isEmpty(ocBOrderItems)) {
            return ValueHolderV14Utils.getFailValueHolder("签收失败,签收的单据明细为空!");
        }
        // todo 待产品确认
        orderMapper.setSapArrivedDate(id, STR.contains(request.getStatus()) ? null : request.getDate());
        if (log.isDebugEnabled()) {
            log.debug("Finish SapCommonService podHandle");
        }
        if (STR.contains(request.getStatus())) {
            return ValueHolderV14Utils.getSuccessValueHolder("取消签收成功");
        }
        return ValueHolderV14Utils.getSuccessValueHolder("签收成功");
    }


    public ValueHolderV14 cancelReturn(JSONObject params) {

        if (log.isDebugEnabled()) {
            log.debug("Start SapCommonService cancelReturn:{}", params);
        }
        SapCancelRequest request = null;
        try {
            request = JSONObject.parseObject(params.toJSONString(), SapCancelRequest.class);
        } catch (Exception e) {
            log.error("podHandle error e," + e.getMessage(), e);
            return ValueHolderV14Utils.getFailValueHolder("参数格式化异常!请检查入参!");
        }

        ValueHolderV14 validate = validate(request);
        if (validate != null) {
            return validate;
        }

        Set<Long> returnIds = ES4ReturnOrder.findIdByReturnId(request.getVerln());

        if (CollectionUtils.isEmpty(returnIds)) {
            log.info(LogUtil.format("根据bill:{}，未查询到订单信息", request.getVerln()), returnIds);
            return ValueHolderV14Utils.getFailValueHolder("取消失败，单据不存在!");
        }

        List<OcBReturnOrder> returnOrderList = returnOrderMapper.selectBatchIds(returnIds);

        List<OcBReturnOrder> terminationList=returnOrderList.stream().filter(returnOrder -> getTerminationType().contains(returnOrder.getTerminationType())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(terminationList)){
            return ValueHolderV14Utils.getSuccessValueHolder("取消成功");
        }


        List<OcBReturnOrder> collect = returnOrderList.stream().filter(returnOrder -> ReturnStatusEnum.CANCLE.getVal().equals(returnOrder.getReturnStatus())).collect(Collectors.toList());

        if (!collect.isEmpty()) {
            log.info(LogUtil.format("根据bill:{}，未查询到订单信息", request.getVerln()), returnIds);
            return ValueHolderV14Utils.getFailValueHolder("取消失败，单据不存在!");
        }

        ValueHolderV14 v14 = new ValueHolderV14();
        refundService.oneOcCancle(getSapUser(), v14, JSONObject.parseArray(JSONObject.toJSONString(returnIds)));
        return v14;
    }


    public ValueHolderV14 cancelOrder(JSONObject params) {

        if (log.isDebugEnabled()) {
            log.debug("Start SapCommonService cancelOrder:{}", params);
        }
        SapCancelRequest request = null;
        try {
            request = JSONObject.parseObject(params.toJSONString(), SapCancelRequest.class);
        } catch (Exception e) {
            log.error("cancelOrder error e," + e.getMessage(), e);
            return ValueHolderV14Utils.getFailValueHolder("参数格式化异常!请检查入参!");
        }

        ValueHolderV14 validate = validate(request);
        if (validate != null) {
            return validate;
        }

        String tid = request.getVerln();

        List<Long> orderIds = GSI4OrderItem.selectOcBOrderItemByTid(tid);
        if (CollectionUtils.isEmpty(orderIds)) {
            log.info(LogUtil.format("根据bill:{}，未查询到订单信息", tid), tid);
            return ValueHolderV14Utils.getFailValueHolder("取消失败，单据不存在!");
        }

        List<RedisReentrantLock> redisReentrantLocks = lockOrderList(orderIds);

        try {

            List<OcBOrder> orders = orderMapper.selectBatchIds(orderIds);

            if (CollectionUtils.isEmpty(orders)) {
                return ValueHolderV14Utils.getFailValueHolder("取消失败，单据不存在!");
            }

            List<OcBOrder> notCancelList = orders.stream().filter(order -> !getNotCancelOrderStatus().contains(order.getOrderStatus())).collect(Collectors.toList());


            if (CollectionUtils.isEmpty(notCancelList)) {
                return ValueHolderV14Utils.getFailValueHolder("取消失败，订单状态不满足取消!");
            }
            List<OcBOrder> canCancelList = notCancelList.stream().filter(order -> getNeedTheAuditOrderStatus().contains(order.getOrderStatus())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(canCancelList)) {
                User sapUser = SystemUserResource.getSapUser();
                for (OcBOrder ocBOrder : canCancelList) {
                    boolean b = omsReturnUtil.toExamineOrder(ocBOrder, sapUser);
                    if (!b) {
                        //反审核成功
                        return ValueHolderV14Utils.getFailValueHolder("订单反审核失败!");
                    }
                }
            }
            for (OcBOrder ocBOrder : notCancelList) {
                Integer orderStatus = ocBOrder.getOrderStatus();
                if (OmsOrderStatus.CHECKED.toInteger().equals(orderStatus)){
                    ocBOrder.setOrderStatus(OmsOrderStatus.UNCONFIRMED.toInteger());
                }
            }
            Map<Long, OcBOrder> orderMap = notCancelList.stream().collect(Collectors.toMap(OcBOrder::getId, Function.identity(), (key1, key2) -> key2));
            List<OcBOrderItem> orderItems = new ArrayList<>();
            int count = 0;
            List<SapCancelRequest.Item> item1 = request.getItem();
            for (SapCancelRequest.Item item : item1) {
                List<OcBOrderItem> ocBOrderItems = orderItemMapper.selectUnSuccessRefundByIds(orderMap.keySet(), tid + "-" + item.getLine());
                if (CollectionUtils.isEmpty(ocBOrderItems)) {
                    continue;
                }
                BigDecimal qtySum = ocBOrderItems.stream().map(OcBOrderItem::getQty).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                if (qtySum.compareTo(item.getQty()) == 0) {
                    orderItems.addAll(ocBOrderItems);
                    count++;
                    continue;
                }
                for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
                    BigDecimal qty = ocBOrderItem.getQty();
                    BigDecimal qty1 = item.getQty();
                    if (qty.compareTo(qty1) == 0) {
                        orderItems.add(ocBOrderItem);
                        count++;
                        break;
                    }
                }
            }
            if (count != item1.size()) {
                return ValueHolderV14Utils.getFailValueHolder("取消失败");
            }

            Map<Long, List<OcBOrderItem>> itemMap = orderItems.stream().collect(Collectors.groupingBy(OcBOrderItem::getOcBOrderId));
            for (Map.Entry<Long, List<OcBOrderItem>> entry : itemMap.entrySet()) {
                SapCommonService.getInstance().markCancel(orderMap.get(entry.getKey()), entry.getValue());
            }
        } catch (Exception e) {
            log.error(LogUtil.format("Sap cancelOrder error:{}", "cancelOrder"), Throwables.getStackTraceAsString(e));
            return ValueHolderV14Utils.getFailValueHolder(e.getMessage());
        } finally {
            unLockOrderList(redisReentrantLocks);
        }
        if (log.isDebugEnabled()) {
            log.debug("Finish SapCommonService cancelOrder");
        }
        return ValueHolderV14Utils.getSuccessValueHolder("success");
    }

    public ValueHolderV14 cancel411TReturn(JSONObject params) {
        if (log.isDebugEnabled()) {
            log.debug("Start SapCommonService cancel411TReturn:{}", params);
        }
        SapCancelRequest request = null;
        try {
            request = JSONObject.parseObject(params.toJSONString(), SapCancelRequest.class);
        } catch (Exception e) {
            log.error("cancel411TReturn error e," + e.getMessage(), e);
            return ValueHolderV14Utils.getFailValueHolder("参数格式化异常!请检查入参!");
        }

        ValueHolderV14 validate = vali411date(request);
        if (validate != null) {
            return validate;
        }

        Set<Long> returnIds = ES4ReturnOrder.findIdByReturnId(request.getZsQdh());
        log.info("cancel411TReturn returnIds:{}", returnIds);

        if (CollectionUtils.isEmpty(returnIds)) {
            log.info(LogUtil.format("根据bill:{}，未查询到订单信息", request.getZsQdh()), returnIds);
            return ValueHolderV14Utils.getFailValueHolder("取消失败，单据不存在!");
        }

        List<OcBReturnOrder> returnOrderList = returnOrderMapper.selectBatchIds(returnIds);

        List<OcBReturnOrder> terminationList=returnOrderList.stream().filter(returnOrder -> getTerminationType().contains(returnOrder.getTerminationType())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(terminationList)){
            return ValueHolderV14Utils.getSuccessValueHolder("取消成功");
        }
        List<OcBReturnOrder> collect = returnOrderList.stream().filter(returnOrder -> ReturnStatusEnum.CANCLE.getVal().equals(returnOrder.getReturnStatus())).collect(Collectors.toList());

        if (!collect.isEmpty()) {
            log.info(LogUtil.format("根据bill:{}，未查询到订单信息", request.getVerln()), returnIds);
            return ValueHolderV14Utils.getFailValueHolder("取消失败，单据不存在!");
        }

        ValueHolderV14 v14 = new ValueHolderV14();
        refundService.oneOcCancle(getSapUser(), v14, JSONObject.parseArray(JSONObject.toJSONString(returnIds)));
        return v14;
    }

    private ValueHolderV14 validate(PodHandleRequest request) {
        if (request == null) {
            return ValueHolderV14Utils.getFailValueHolder("签收失败,参数为空！");
        }
        if (StringUtils.isBlank(request.getBillNo())) {
            return ValueHolderV14Utils.getFailValueHolder("签收失败,单号为空!");
        }
        if (Objects.isNull(request.getDate())) {
            return ValueHolderV14Utils.getFailValueHolder("签收失败,签收时间为空!");
        }
        if (StringUtils.isBlank(request.getStatus())) {
            return ValueHolderV14Utils.getSuccessValueHolder("操作成功");
        }
        return null;
    }

    private ValueHolderV14 vali411date(SapCancelRequest request) {
        if (request == null) {
            return ValueHolderV14Utils.getFailValueHolder("取消失败,参数为空！");
        }
        if (StringUtils.isBlank(request.getZsQdh())) {
            return ValueHolderV14Utils.getFailValueHolder("取消失败,单号为空!");
        }
        return null;
    }

    private ValueHolderV14 validate(SapCancelRequest request) {
        if (request == null) {
            return ValueHolderV14Utils.getFailValueHolder("取消失败,参数为空！");
        }
        if (StringUtils.isBlank(request.getVerln())) {
            return ValueHolderV14Utils.getFailValueHolder("取消失败,单号为空!");
        }
        if (CollectionUtils.isEmpty(request.getItem())) {
            return ValueHolderV14Utils.getFailValueHolder("取消失败,明细为空!");
        }
        for (SapCancelRequest.Item item : request.getItem()) {
            if (Objects.isNull(item.getLine())) {
                return ValueHolderV14Utils.getFailValueHolder("取消失败,行号为空!");
            }
            if (StringUtils.isBlank(item.getSku())) {
                return ValueHolderV14Utils.getFailValueHolder("取消失败,SKU为空!");
            }
            if (Objects.isNull(item.getQty())) {
                return ValueHolderV14Utils.getFailValueHolder("取消失败,关闭数量为空!");
            }
        }
        return null;
    }


    /**
     * add lock
     *
     * @param toList
     * @return
     */
    private List<RedisReentrantLock> lockOrderList(Collection<Long> toList) {
        List<RedisReentrantLock> lockList = new ArrayList<>();
        for (Long orderId : toList) {
            String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(orderId);
            RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
            try {
                if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                    lockList.add(redisLock);
                } else {
                    redisLock.unlock();
                }
            } catch (Exception e) {
                redisLock.unlock();
            }
        }
        return lockList;
    }

    /**
     * 解锁
     *
     * @param lockList
     */
    private void unLockOrderList(List<RedisReentrantLock> lockList) {
        if (CollectionUtils.isEmpty(lockList)) {
            return;
        }
        for (RedisReentrantLock redisReentrantLock : lockList) {
            redisReentrantLock.unlock();
        }
    }


    public void markCancel(OcBOrder order, List<OcBOrderItem> items) {
        if (order == null) {
            return;
        }
        if (log.isDebugEnabled()) {
            log.debug(this.getClass().getName() + " 进入标记退款完成服务 订单id :{}", order.getId());
        }
        //全部明细
        List<OcBOrderItem> orderItemsList = orderItemMapper.selectOrderItemsNotRefundFroAppointSku(order.getId());
        for (OcBOrderItem ocBOrderItem : items) {
            if ((ocBOrderItem.getProType() == SkuType.NO_SPLIT_COMBINE || ocBOrderItem.getProType() == SkuType.NORMAL_PRODUCT)) {
                orderItemsList.remove(ocBOrderItem);
            }
        }
        if (CollectionUtils.isEmpty(items)) {
            throw new NDSException("只有正常商品和组合福袋商品并且未退款完成的明细才可以标记退款完成");
        }
        omsMarkCancelService.handleSapCancelItem(order, items, orderItemsList);
    }


    private static List<Integer> getOrderStatus() {
        if (CollectionUtils.isEmpty(ORDER_STATUS)) {
            ORDER_STATUS = Lists.newArrayList(OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger(), OmsOrderStatus.PLATFORM_DELIVERY.toInteger());
            return ORDER_STATUS;
        }
        return ORDER_STATUS;
    }

    private static List<String> getTerminationType() {
        if (CollectionUtils.isEmpty(TERMINATION_TYPE)) {
            TERMINATION_TYPE = Lists.newArrayList(TERMINATION_TYPE_01, TERMINATION_TYPE_02);
            return TERMINATION_TYPE;
        }
        return TERMINATION_TYPE;
    }

    private static List<Integer> getCanCancelOrderStatus() {
        if (CollectionUtils.isEmpty(CAN_CANCEL_ORDER_STATUS)) {
            CAN_CANCEL_ORDER_STATUS = Lists.newArrayList(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger(), OmsOrderStatus.UNCONFIRMED.toInteger());
            return CAN_CANCEL_ORDER_STATUS;
        }
        return CAN_CANCEL_ORDER_STATUS;
    }

    private static List<Integer> getNotCancelOrderStatus() {
        if (CollectionUtils.isEmpty(NOT_CANCEL_ORDER_STATUS)) {
            NOT_CANCEL_ORDER_STATUS = Lists.newArrayList(
                    OmsOrderStatus.TO_BE_ASSIGNED.toInteger(),
                    OmsOrderStatus.PENDING_WMS.toInteger(),
                    OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger()
                    , OmsOrderStatus.PLATFORM_DELIVERY.toInteger(),
                    OmsOrderStatus.CANCELLED.toInteger(),
                    OmsOrderStatus.SYS_VOID.toInteger(),
                    OmsOrderStatus.OCCUPY_IN.toInteger());
            return NOT_CANCEL_ORDER_STATUS;
        }
        return NOT_CANCEL_ORDER_STATUS;
    }


    private static List<Integer> getNeedTheAuditOrderStatus() {
        if (CollectionUtils.isEmpty(NEED_THE_AUDIT_ORDER_STATUS)) {
            NEED_THE_AUDIT_ORDER_STATUS = Lists.newArrayList(OmsOrderStatus.CHECKED.toInteger(), OmsOrderStatus.IN_DISTRIBUTION.toInteger());
            return NEED_THE_AUDIT_ORDER_STATUS;
        }
        return NEED_THE_AUDIT_ORDER_STATUS;
    }

    private static List<Integer> getUseFulStatus() {
        if (CollectionUtils.isEmpty(USE_FUl_ORDER_STATUS)) {
            USE_FUl_ORDER_STATUS = Lists.newArrayList(OmsOrderStatus.UNCONFIRMED.toInteger(), OmsOrderStatus.BE_OUT_OF_STOCK.toInteger());
            return USE_FUl_ORDER_STATUS;
        }
        return USE_FUl_ORDER_STATUS;
    }

    private static List<Integer> getReturnStatus() {
        if (CollectionUtils.isEmpty(RETURN_STATUS)) {
            RETURN_STATUS = Lists.newArrayList(OmsOrderStatus.UNCONFIRMED.toInteger(), OmsOrderStatus.BE_OUT_OF_STOCK.toInteger(), OmsOrderStatus.CHECKED.toInteger(), OmsOrderStatus.IN_DISTRIBUTION.toInteger());
            return RETURN_STATUS;
        }
        return RETURN_STATUS;
    }

    private static User getSapUser() {
        if (SAP_USER == null) {
            SAP_USER = SystemUserResource.getSapUser();
        }
        return SAP_USER;
    }

    public static SapCommonService getInstance() {
        return ApplicationContextHandle.getBean(SapCommonService.class);
    }
}
