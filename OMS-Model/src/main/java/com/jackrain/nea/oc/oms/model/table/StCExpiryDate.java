package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.util.Date;

/**
 * st_c_expiry_date
 * <AUTHOR>
@TableName
@Data
public class StCExpiryDate extends BaseModel {
    @JSONField(name = "ID")
    private Long id;

    /**
     * 类型(1,"公用",2,"指定店铺")
     */
    @JSONField(name = "EXPIRY_TYPE", alternateNames = "expiryType")
    private Integer expiryType;

    /**
     * 店铺id
     */
    @JSONField(name = "SHOP_ID", alternateNames = "shopId")
    private Long shopId;

    /**
     * 客户分组
     */
    @JSONField(name = "CUSTOMER_GROUPING", alternateNames = "customerGrouping")
    private Integer customerGrouping;

    /**
     * 开始时间
     */
    @JSONField(name = "START_TIME", alternateNames = "startTime")
    private Date startTime;

    /**
     * 结束时间
     */
    @JSONField(name = "END_TIME", alternateNames = "endTime")
    private Date endTime;

    /**
     * 备注
     */
    @JSONField(name = "REMARKS")
    private String remarks;

    /**
     * 提交状态(1,未提交,2,已提交,3已作废,4已结案)
     */
    @JSONField(name = "SUBMIT_STATUS", alternateNames = "submitStatus")
    private Integer submitStatus;

    /**
     * OA创建人
     */
    @JSONField(name = "OA_CREATOR")
    private String oaCreator;

    /**
     * OA单号
     */
    @JSONField(name = "OA_NUM")
    private String oaNum;

    private static final long serialVersionUID = 1L;
}