package com.jackrain.nea.oc.oms.mq.processor.impl.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.burgeon.mq.annotation.RocketMqMessageListener;
import com.burgeon.mq.core.BaseMessageListener;
import com.burgeon.mq.enums.MqTypeEnum;
import com.burgeon.mq.error.MqException;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.nums.OcBOrderNodeEnum;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.OcBOrderNodeRecordService;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.services.OrderPlatformDeliveryService;
import com.jackrain.nea.oc.oms.services.ZtoLogisticsInterceptService;
import com.jackrain.nea.oc.oms.util.OcBOrderDeliveryFailUtil;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 京东平台发货接口，接收云枢纽回传MQ
 *
 * @author: 胡林洋
 * @since: 2019/5/10
 * create at : 2019/5/23 16:22
 */
@Slf4j
@RocketMqMessageListener(name = "JdPlatformDeliveryOrderListener", type = MqTypeEnum.CLOUD)
public class JdPlatformDeliveryOrderListener implements BaseMessageListener {
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private OrderPlatformDeliveryService orderPlatformDeliveryService;

    @Autowired
    private OcBOrderNodeRecordService ocBOrderNodeRecordService;
    @Autowired
    private OcBOrderDeliveryFailUtil deliveryFailUtil;
    @Resource
    private ZtoLogisticsInterceptService ztoLogisticsInterceptService;

    @Override
    public void consume(String messageBody, String messageTopic, String messageKey, String messageTag, Object obj) {
        User user = SystemUserResource.getRootUser();
        try {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("京东平台发货接口，接收云枢纽回传MQ: {}", messageKey), messageBody);
            }
            JSONObject object = JSON.parseArray(messageBody).getJSONObject(0);
            String msg = object.getString("MSG");
            Long id = object.getLong("ID");
            String tid = object.getString("TID");
            String expressCode = object.getString("OUT_SID");
            OcBOrder ocBOrder = ocBOrderMapper.selectById(id);
            String logMsg = "订单" + id + "(平台单号=" + tid + ";物流单号=" + expressCode + ")JD平台发货MQ结果" + (object.getInteger("SYNCSTATUS") == 0 ? "成功" : msg);
            /**
             * ☆无论失败成功，添加日志
             */
            omsOrderLogService.addUserOrderLog(id, ocBOrder.getBillNo(), OrderLogTypeEnum.PLATFORM_SEND.getKey(), logMsg, null, msg, user);
            if (object.getInteger("SYNCSTATUS") == 0 || object.getInteger("SYNCSTATUS") == 2) {
                //失败处理
                orderPlatformDeliveryService.failUpdate(expressCode, msg, ocBOrder);
                deliveryFailUtil.addOcBOrderDeliveryFail(ocBOrder);
                ztoLogisticsInterceptService.keywordsIntercept(ocBOrder, msg);
                return;
            }

            /**
             * ☆1、更新明细发货状态
             * ☆2、更新发货信息列表
             * ☆3、校验是否更新主表为平台发货
             */
            orderPlatformDeliveryService.updateSendGoodsStatusByExpressCode(id, expressCode, 1L);
            ocBOrderItemMapper.updateItemsWhenDeliverySuccess(id, tid);
            List<String> tidList = ocBOrderItemMapper.selectCanPlatformItemTidList(id);
            tidList.remove(tid);
            if (tidList.size() > 0) {
                //先不做处理，只更新明细发货状态，等后面的tid发货消息过来一起更新主表状态
            } else {
                OcBOrder order = new OcBOrder();
                order.setId(ocBOrder.getId());
                order.setIsForce(1L);
                order.setOrderStatus(OmsOrderStatus.PLATFORM_DELIVERY.toInteger());
                //平台发货时间赋值
                order.setPlatformDeliveryTime(new Date());
                ocBOrderNodeRecordService.insertByNode(OcBOrderNodeEnum.PLATFORM_DELIVERY_TIME,new Date(),order.getId(),SystemUserResource.getRootUser());
                //作废退单
                orderPlatformDeliveryService.updateOrder(order);
                orderPlatformDeliveryService.updateReturnOrder(ocBOrder);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("京东平台发货失败.ExpMsg: {}"), Throwables.getStackTraceAsString(e));
            throw new MqException(e);
        }
    }
}
