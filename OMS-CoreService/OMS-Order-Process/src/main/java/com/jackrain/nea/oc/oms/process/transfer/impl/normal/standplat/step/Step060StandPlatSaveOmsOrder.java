package com.jackrain.nea.oc.oms.process.transfer.impl.normal.standplat.step;

import com.burgeon.mq.core.DefaultProducerSend;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.log.LogCat;
import com.jackrain.nea.log.LogEvent;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.config.ToBeConfirmedOrderMqConfig;
import com.jackrain.nea.oc.oms.matcher.MatchStrategyManager;
import com.jackrain.nea.oc.oms.model.enums.AbnormalTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderRefundStatus;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpStandplatOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatOrderItemEx;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.taobao.TaoBaoOrderStatus;
import com.jackrain.nea.oc.oms.nums.OcBOrderNodeEnum;
import com.jackrain.nea.oc.oms.services.OcBOrderNodeRecordService;
import com.jackrain.nea.oc.oms.services.OcBRemarkGiftTaskService;
import com.jackrain.nea.oc.oms.services.OmsStandplatReturnOrderService;
import com.jackrain.nea.oc.oms.services.OrderPriceValidate;
import com.jackrain.nea.oc.oms.services.advance.OmsOrderAdvanceParseService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.oc.oms.util.OmsPlatformUtil;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.OrderUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 保存订单信息
 *
 * @author: ming.fz
 * @since: 2019-07-09
 * create at : 2019-07-09
 */
@Slf4j
@Step(order = 60, description = "保存订单信息")
@Component
public class Step060StandPlatSaveOmsOrder extends BaseStandplatOrderProcessStep
        implements IOmsOrderProcessStep<IpStandplatOrderRelation> {

    @Autowired
    private DefaultProducerSend defaultProducerSend;

    @Autowired
    private ToBeConfirmedOrderMqConfig orderMqConfig;

    @Autowired
    OmsStandplatReturnOrderService omsStandplatReturnOrderService;

    @Autowired
    OmsOrderAdvanceParseService omsOrderAdvanceParseService;

    @Autowired
    private OcBOrderNodeRecordService ocBOrderNodeRecordService;

    @Autowired
    private OcBRemarkGiftTaskService ocBRemarkGiftTaskService;

    @Override
    public ProcessStepResult<IpStandplatOrderRelation> startProcess(IpStandplatOrderRelation orderInfo,
                                                                    ProcessStepResult preStepResult,
                                                                    boolean isAutoMakeup, User operateUser) {
        String orderStatus = orderInfo.getStandplatOrder().getStatus();
        Long platformId = orderInfo.getPlatformId();
        OmsPlatformUtil bean = ApplicationContextHandle.getBean(OmsPlatformUtil.class);
        Boolean isSpecialPlatform = bean.isSpecialPlatform(platformId);

        //isHistoryOrder 当前单据为已发货等待确认收货和交易完成就为true
        // 2021年12月15日 徐顺超(产品)：添加一个条件，如果是  特殊订单类型 当前单据为已发货等待确认收货和交易完成 为false
//        boolean isHistoryOrder = (TaoBaoOrderStatus.WAIT_BUYER_CONFIRM_GOODS.equals(orderStatus)
//                || TaoBaoOrderStatus.TRADE_FINISHED.equals(orderStatus)) && !isSpecialPlatform;
        //todo 因为有的平台下来就是等待买家确认收货 需要匹配业务类型 历史订单的判断放到拆分里
        boolean isHistoryOrder = false;

        String orderNo = orderInfo.getOrderNo();
        try {
            List<LogEvent> logEventList = new ArrayList<>();
            LogEvent eventConvert = LogCat.newEvent(Step060StandPlatSaveOmsOrder.class.getSimpleName(), "ConvertOrder");

            //盒马明细合并，相同sku按条件合并
            if (platformId != null && PlatFormEnum.HEMAOS.getCode().equals(platformId.intValue())) {
                this.hemaMergeItems(orderInfo);
            }

            //将中间表对象转成可保存的全渠道实体
            OcBOrderRelation saveOrderInfo = orderService.convertStandplatOrderToOrder(orderInfo, isHistoryOrder);
            // @20200616 策略解析匹配
            strategyMatch(orderInfo, saveOrderInfo);
            // @20200710 打标
            //TaggerManager.get().doTag(saveOrderInfo.getOrderInfo(), saveOrderInfo.getOrderItemList());

            eventConvert.complete();
            logEventList.add(eventConvert);
            LogEvent eventStartSave = LogCat.newEvent(Step060StandPlatSaveOmsOrder.class.getSimpleName(), "SaveOrder");
            //转单前进行预售解析
            omsOrderAdvanceParseService.advanceParse(saveOrderInfo, operateUser, orderStatus);
            boolean saveResult = orderService.saveOmsOrderInfoStandplat(saveOrderInfo, isHistoryOrder, operateUser, orderInfo);
            if (!saveResult) {
                saveOrderInfo = null;
            } else {
                OrderPriceValidate.isSendDingTalk(saveOrderInfo);
                ocBOrderNodeRecordService.insertByNode(OcBOrderNodeEnum.ORDER_CREAT,new Date(),saveOrderInfo.getOrderInfo().getId(),operateUser);

                //黑名单卡单数据记录
                try {
                    blackCardRecord(saveOrderInfo.getOrderInfo());
                } catch (Exception e) {
                    log.error(" 通用订单生成零售发货记录当天单数/金额失败 orderId:{}", saveOrderInfo.getOrderInfo().getId(), e);
                }

                //卖家添加备注任务表
                if (StringUtils.isNotBlank(saveOrderInfo.getOrderInfo().getSellerMemo())) {
                    ocBRemarkGiftTaskService.createRemarkGiftTask(saveOrderInfo.getOrderInfo().getCpCShopId(), saveOrderInfo.getOrderInfo().getTid());
                }
            }
            eventStartSave.complete();
            logEventList.add(eventStartSave);

            ProcessStepResult<IpStandplatOrderRelation> stepResult = new ProcessStepResult<>();
            String status = orderInfo.getStandplatOrder().getStatus();

            if ((checkItemStatus(orderInfo.getStandPlatOrderItemList()) || TaoBaoOrderStatus.REFUND_FINISHED.equals(status)) && !isHistoryOrder) {
                //更细中间表状态为已转换
                if (saveOrderInfo != null) {
//                    omsStandplatReturnOrderService.updateIsInterecept(saveOrderInfo.getOrderInfo(), operateUser,
//                            OmsOrderIsInterceptEnum.YES.getVal(), "拦截成功!", OmsOrderRefundStatus.WAITSELLERAGREE.toInteger());
                    ipStandplatOrderService.updateStandPlatOrderTransStatus(orderInfo.getOrderNo(),
                            TransferOrderStatus.NOT_TRANSFER, "订单转单时已有明细申请退款,等待下次转换处理", null);
                }
//                String remarks = "通用发货前退单转换成功且当前单据已拦截，单据" + orderInfo.getOrderId();
//                ipStandplatOrderService.updateStandPlatOrderTransStatus(orderInfo.getOrderNo(),
//                        TransferOrderStatus.TRANSFERRED, remarks, null);
//                String operateMessage = Resources.getMessage(remarks);
                stepResult = new ProcessStepResult<>(StepStatus.FINISHED, "订单转单时已有明细申请退款,等待下次转换处理");
                return stepResult;
            } else {
                stepResult.setMessage("存储数据成功，进入下一阶段 ");
                stepResult.setStatus(StepStatus.SUCCESS);
                stepResult.setNextStepClass(Step080StandplatUpdateOrderTransferStatus.class);
                stepResult.setLogEventList(logEventList);
                return stepResult;
            }

        } catch (NDSException ex) {
            ipStandplatOrderService.updateStandPlatOrderTransStatus(orderNo,
                    TransferOrderStatus.TRANSFEREXCEPTION, ex.getMessage(), AbnormalTypeEnum.MATE_ABNORMAL.getKey());
            return new ProcessStepResult<>(StepStatus.FAILED, ex.getMessage());
        } catch (Exception e) {
            log.error(LogUtil.format("Step060StandPlatSaveOmsOrder.startProcess.error={}", "Step060StandPlatSaveOmsOrder"), Throwables.getStackTraceAsString(e));
            String errorMessage = "存储数据失败，退出转单服务;" + e.getMessage();
            boolean updateStatusRes = ipStandplatOrderService.updateStandPlatOrderTransStatus(orderNo, TransferOrderStatus.NOT_TRANSFER, errorMessage, null);
            if (!updateStatusRes) {
                errorMessage += ";更新状态失败=False";
            }
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }

    }

    /**
     * 盒马合并订单明细
     *
     * @param orderInfo
     */
    private void hemaMergeItems(IpStandplatOrderRelation orderInfo) {
        List<IpBStandplatOrderItemEx> newList = Lists.newArrayList();
        List<IpBStandplatOrderItemEx> itemList = orderInfo.getStandPlatOrderItemList();
        Map<String, List<IpBStandplatOrderItemEx>> skuIdMap = itemList.stream().collect(Collectors.groupingBy(IpBStandplatOrderItemEx::getOuterSkuId));

        for (Map.Entry<String, List<IpBStandplatOrderItemEx>> entry : skuIdMap.entrySet()) {
            List<IpBStandplatOrderItemEx> itemExList = entry.getValue();
            if (itemExList.size() == 1) {
                //只有一条，不处理
                newList.addAll(itemExList);
                continue;
            }

            //多条合并。雷产品注：该平台同订单下一个SKU最多两行，但是会存在多个SKU有些一行有些两行的情况，每个SKU单独合并处理
            BigDecimal totalFee = BigDecimal.ZERO;
            BigDecimal payment = BigDecimal.ZERO;
            long sum = 0L;
            //合并后
            IpBStandplatOrderItemEx orderItemEx = null;
            for (IpBStandplatOrderItemEx itemEx : itemExList) {
                if (itemEx.getTotalFee() != null) {
                    totalFee = totalFee.add(itemEx.getTotalFee());
                }
                if (itemEx.getPayment() != null) {
                    payment = payment.add(itemEx.getPayment());
                }
                if (itemEx.getNum() != null) {
                    sum = sum + itemEx.getNum();
                }
                if (Objects.isNull(orderItemEx)) {
                    orderItemEx = itemEx;
                } else {
                    if ("false".equals(itemEx.getIsPresent())) {
                        orderItemEx = itemEx;
                    }
                }
            }

            if (orderItemEx == null) {
                orderItemEx = itemExList.get(0);
            }

            orderItemEx.setTotalFee(totalFee);
            orderItemEx.setPayment(payment);
            orderItemEx.setNum(sum);
            newList.add(orderItemEx);
        }
        orderInfo.setStandPlatOrderItemList(newList);
    }

    private void blackCardRecord(OcBOrder bOrder) {
        String receiverMobile = bOrder.getReceiverMobile();
        if (!OrderUtil.isMobileCheck(receiverMobile)) {
            return;
        }

        //获取手机号记录，无记录新增，有记录累加，失效时间当天24点
        CusRedisTemplate<String, String> objRedisTemplate = RedisOpsUtil.getObjRedisTemplate();
        String recordKey = BllRedisKeyResources.buildBlackCardRecordKey(receiverMobile);
        Boolean hasRedisKey = objRedisTemplate.hasKey(recordKey);
        String updateValue = "";
        if (hasRedisKey != null && hasRedisKey) {
            //累加
            String value = objRedisTemplate.opsForValue().get(recordKey);
            if (StringUtils.isNotBlank(value)) {
                String[] split = value.split(",");
                Double totalOrderNums = Double.valueOf(split[0]);
                BigDecimal totalOrderAmount = new BigDecimal(split[1]);

                totalOrderNums = totalOrderNums + 1;
                totalOrderAmount = totalOrderAmount.add(bOrder.getOrderAmt());

                updateValue = totalOrderNums + "," + totalOrderAmount;
            } else {
                //无，新增
                updateValue = 1 + "," + bOrder.getOrderAmt();
            }
        } else {
            //无，新增
            updateValue = 1 + "," + bOrder.getOrderAmt();
        }

        long times = ChronoUnit.SECONDS.between(LocalDateTime.now(), LocalDateTime.now().plusDays(1).withHour(0).withMinute(0).withSecond(0).withNano(0));
        objRedisTemplate.opsForValue().set(recordKey, updateValue, times, TimeUnit.SECONDS);
    }

    /**
     * 策略解析匹配
     *
     * @param orderInfo
     * @param orderRelation
     */
    private void strategyMatch(IpStandplatOrderRelation orderInfo, OcBOrderRelation orderRelation) {
        if (Objects.nonNull(orderInfo) && Objects.nonNull(orderRelation)) {
            MatchStrategyManager.get().match(orderInfo, this.getCurrentChannelType(), orderRelation.getOrderInfo(), orderRelation.getOrderItemList());
        }
    }

    /**
     * 判断明细中是否有退款中的商品有返回true,否false
     *
     * @param StandplatOrderItemList
     * @return
     */
    private boolean checkItemStatus(List<IpBStandplatOrderItemEx> StandplatOrderItemList) {
        for (IpBStandplatOrderItemEx standplatOrderItemEx : StandplatOrderItemList) {
            if (standplatOrderItemEx.getRefundStatus() == null) {
                continue;
            }
            //明细退款状态
            int standplatRefundStatus = Integer.parseInt(standplatOrderItemEx.getRefundStatus());
            if (OmsOrderRefundStatus.WAITSELLERAGREE.toInteger() == standplatRefundStatus ||
                    OmsOrderRefundStatus.WAITBUYERRETURNGOODS.toInteger() == standplatRefundStatus ||
                    OmsOrderRefundStatus.WAITSELLERCONFIRMGOODS.toInteger() == standplatRefundStatus) {
                return true;
            }
        }
        return false;
    }
}
