package com.jackrain.nea.oc.oms.services.patrol;

import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.es.ES4ReturnOrder;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderRefundMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.oc.oms.nums.OcOrderRefundStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;

/**
 * @Author: 黄世新
 * @Date: 2019/6/4 2:02 PM
 * @Version 1.0
 * <p>
 * 判断退款完成明细是否为空
 */
@Slf4j
@Component
public class ReturnOrderAmtIsZeroService {

    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private OcBReturnOrderRefundMapper ocBReturnOrderRefundMapper;


    public List<Long> selectOrderItem(int page, int size) {
        List<Long> longList = new ArrayList<>();
        //通过es查询退款完成的明细数据
        List<Long> ids = ES4Order.findIdByRefundStatusByPagination(OcOrderRefundStatusEnum.SUCCESS.getVal(), page, size);
        if (CollectionUtils.isNotEmpty(ids)) {
            List<OcBOrderItem> orderItems = ocBOrderItemMapper.selectOrderItemListByOrderIds(ids);
            for (OcBOrderItem orderItem : orderItems) {
                BigDecimal addAmt = orderItem.getPrice().add(orderItem.getOrderSplitAmt())
                        .add(orderItem.getAmtDiscount()).add(orderItem.getAdjustAmt()).add(orderItem.getRealAmt());
                if (addAmt.compareTo(BigDecimal.ZERO) != 0) {
                    longList.add(orderItem.getOcBOrderId());
                }
            }
        }
        return longList;
    }


    /**
     * 排除组合商品明细是否有ooid相同的
     */
    public List<String> selectOrderItemOoid(Date beginTime, Date endTime) {
        List<Long> ids = ES4Order.findIdByModifiedDate(beginTime.getTime() + "~" + endTime.getTime());
        List<String> strList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(ids)) {
            List<OcBOrderItem> orderItems = ocBOrderItemMapper.selectOrderItemListByOrderIds(ids);
            Map<String, Long> map = new HashMap<>();
            for (OcBOrderItem orderItem : orderItems) {
                //liqb 更改ooid类型从Long类型改成String类型
                String ooid = orderItem.getOoid();
                Long ocBOrderId = orderItem.getOcBOrderId();
                if (!map.containsKey(ooid)) {
                    map.put(ooid, ocBOrderId);
                } else {
                    Long aLong = map.get(ooid);
                    strList.add("订单编号:" + aLong + "和订单编号:" + ocBOrderId + "有明细ooid重复");
                }
            }
        }
        return strList;
    }

    /**
     * 查询退换货单没有生成明细的数据
     *
     * @param beginTime
     * @param endTime
     * @return
     */

    public List<String> selectReturnOrderNotItem(Date beginTime, Date endTime) {
        Set<Long> ids = ES4ReturnOrder.findIdByModifiedDate(beginTime, endTime);

        if (CollectionUtils.isEmpty(ids)) {
            return null;
        }

        List<String> list = new ArrayList<>();
        for (Long orderId : ids) {
            List<OcBReturnOrderRefund> returnOrderRefunds = ocBReturnOrderRefundMapper.selectByOcOrderId(orderId);
            if (CollectionUtils.isEmpty(returnOrderRefunds)) {
                list.add("退单编号为:" + orderId + "没有明细数据");
            }
        }
        return list;
    }
}
