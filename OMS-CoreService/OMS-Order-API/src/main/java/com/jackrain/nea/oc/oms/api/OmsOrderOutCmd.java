package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.oc.oms.model.request.OrderOutRequest;
import com.jackrain.nea.oc.oms.model.result.OrderOutResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;

/**
 * @author: 胡林洋
 * @since: 2019-04-28
 * create at : 2019-04-28 21:50
 */
public interface OmsOrderOutCmd {

    ValueHolderV14<OrderOutResult> startOrderOut(OrderOutRequest orderOutRequest);
}
