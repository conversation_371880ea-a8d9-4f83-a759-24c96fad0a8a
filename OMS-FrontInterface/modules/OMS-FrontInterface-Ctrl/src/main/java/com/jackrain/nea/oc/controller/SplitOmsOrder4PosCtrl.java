package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.SplitSaleOrderCmd;
import com.jackrain.nea.oc.request.o2o.SplitOrderRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2020/8/28
 */
@Api(value = "SplitOmsOrder4PosCtrl", tags = "Pos拆单")
@RestController
@Slf4j
public class SplitOmsOrder4PosCtrl {


    @Autowired
    private SplitSaleOrderCmd splitSaleOrder;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;


    @ApiOperation(value = "门店主动拆单")
    @RequestMapping(value = "api/cs/oc/oms/v1/onlinePosManualSplitOrder", method = RequestMethod.POST)
    public JSONObject posManualSplitOrder(HttpServletRequest request, @RequestBody JSONObject jsn) {

        ValueHolderV14 vh = null;
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        try {
            SplitOrderRequest splitOrderRequest = JSONObject.parseObject(jsn.toJSONString(), SplitOrderRequest.class);
            vh = splitSaleOrder.splitSaleOrder(splitOrderRequest, user);
            if (vh != null) {
                return vh.toJSONObject();
            }
            vh = new ValueHolderV14();
            vh.setCode(-1);
            vh.setMessage("返回结果为null");
            return vh.toJSONObject();
        } catch (NDSException e) {
            vh = new ValueHolderV14();
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return vh.toJSONObject();
        }
    }

}
