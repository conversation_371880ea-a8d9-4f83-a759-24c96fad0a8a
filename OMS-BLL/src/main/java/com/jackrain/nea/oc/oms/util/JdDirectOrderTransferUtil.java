package com.jackrain.nea.oc.oms.util;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.cp.services.RegionNewService;
import com.jackrain.nea.cpext.model.ProvinceCityAreaInfo;
import com.jackrain.nea.cpext.model.RegionInfo;
import com.jackrain.nea.cpext.model.RegionType;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OmsPayType;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.relation.IpJingdongDirectOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongDirect;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongDirectItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.util.BaseModelUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.jackrain.nea.resource.CpRedisKeyResources.getRegionKey;

/**
 * @Author: 黄世新
 * @Date: 2022/3/29 下午2:47
 * @Version 1.0
 */
@Slf4j
@Component
public class JdDirectOrderTransferUtil {

    @Autowired
    private BuildSequenceUtil buildSequenceUtil;
    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private RegionNewService regionService;
    @Autowired
    private PropertiesConf propertiesConf;

    private static final Map<Integer, String> platformStatusEnum = new HashMap<>();

    static {
        platformStatusEnum.put(7, "WAIT_SELLER_SEND_GOODS");
        platformStatusEnum.put(10, "WAIT_SELLER_SEND_GOODS");
        platformStatusEnum.put(16, "WAIT_BUYER_CONFIRM_GOODS");
        platformStatusEnum.put(19, "TRADE_FINISHED");
    }


    /**
     * 构建订单数据
     *
     * @param orderRelation
     * @param isHistoryOrder
     * @return
     */
    public OcBOrderRelation directOrderToOmsOrder(IpJingdongDirectOrderRelation orderRelation,
                                                  boolean isHistoryOrder, boolean isRefund) {
        OcBOrder ocBOrder = this.buildOcBOrderFromDirectOrder(orderRelation, isHistoryOrder, isRefund);
        List<IpBJingdongDirectItem> ipBJingdongDirectItems = orderRelation.getIpBJingdongDirectItems();
        Map<String, ProductSku> productSkuMap = orderRelation.getProductSkuMap();
        List<OcBOrderItem> ocBOrderItemList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(ipBJingdongDirectItems)) {
            for (IpBJingdongDirectItem ipBJingdongDirectItem : ipBJingdongDirectItems) {
                OcBOrderItem orderItem = this.buildOrderItemFromDirectOrderItem(ocBOrder, ipBJingdongDirectItem, productSkuMap, isHistoryOrder);
                ocBOrderItemList.add(orderItem);
            }
        }
        this.doRecountAmount(ocBOrder, ocBOrderItemList);
        OcBOrderRelation relation = new OcBOrderRelation();
        relation.setOrderInfo(ocBOrder);
        relation.setOrderItemList(ocBOrderItemList);
        return relation;
    }

    public static void main(String[] args) {
        String extInfo = "{\"outPlatformOrderId\":\"2405200016863206\",\"whereEnc\":\"~wlxQ+y2dEafZZZ74T2mzsgUZAXT8op94~Ci1tZXJjaGFudC5vcGVuLnNlY3JldC5rZXkua3M2NzUyNTg0NzIwNjExNzgyMjMSMAj6DeCsRq1YeShLXoL9qb+6dRIs3Nd5kshDatN5xkXcL/M1/wH7Q3SRS2CKiMaHLRoSp/UjPlFieIOgevCBk+lTiR1gIiDeNRvAj6zCC92WQd8vPUhDEumjEOlnyd8BgiM4NuNEdygFMAE=&amp;Ci1tZXJjaGFudC5vcGVuLnNlY3JldC5rZXkua3M2NzUyNTg0NzIwNjExNzgyMjMSMFtsHJmdcG/43/wdrH80iz4jfSxVpa2CV9gZEpmTWwiK97alkhD6BVDMZ1a69IzBMRoSUaVRL1meHSxZuYdmQuSzYVaHIiDbVDVcL5G2deff5EyFcotFWpP1paJCFvshnl0IUBd/tygFMAE=~1~~\",\"outPlatformShopId\":\"1631361546\",\"outPlatformParentOrderId\":\"2405200016863206\",\"nameEnc\":\"#U4dg98KF#Ci1tZXJjaGFudC5vcGVuLnNlY3JldC5rZXkua3M2NzUyNTg0NzIwNjExNzgyMjMSIFxRN5cQXx6aiDxQRT7Lmycq8LkbgceMUi6UWGyrTpmIGhLJP0u6WHOJWCoakUHFZnt4LVoiINom2V6sDkzhWQJKKZ6dSKwv9JC8IRcAfO/JBX2rHP4NKAUwAQ==&amp;Ci1tZXJjaGFudC5vcGVuLnNlY3JldC5rZXkua3M2NzUyNTg0NzIwNjExNzgyMjMSMLMtRUhdpOElumWe+vW5caQk2uKzU4ngz7/4IsYiHmE2rz77gXeo9j7NglT1U94izxoSp/UjPlFieIOgevCBk+lTiR1gIiBGAzS+z+tgORHtydB2LbdDcQUhvkbPJkmjWy5HdZgmeCgFMAE=#1##\",\"mobileEnc\":\"$CtPyUNMqMDlyKoMy9524sreyk/nvw4RqLHkJ4Q6hHuY=$Ci1tZXJjaGFudC5vcGVuLnNlY3JldC5rZXkua3M2NzUyNTg0NzIwNjExNzgyMjMSIIAyszjxFk3c/3dQ25ASZf6g2TE8vfsKmfTrfgZ0B8fYGhJ15rJdjUh1fAQt60q5Aznk6yciIKkovdD/EKCawG/X6yOa0+UWwOb8FLKF/J1ft5+D/zE4KAUwAQ==&amp;Ci1tZXJjaGFudC5vcGVuLnNlY3JldC5rZXkua3M2NzUyNTg0NzIwNjExNzgyMjMSMFBRVMRNw636lmSNo+hX/xLjznJL8njl70HZK4J3mQQAnHG+n3C/bII+TPNOucIUXxoST6u9hAyAcCXslKXsQn2ap2YEIiDGKTK03nkbnwH3J51poMM6mnvQblVLRDl2RSbEnCtO/SgFMAE=$1$$\"}";
        JSONObject jsonObject = JSONObject.parseObject(extInfo);
        System.out.println(jsonObject.getString("outPlatformOrderId"));
        System.out.println(jsonObject.getString("whereEnc"));
        System.out.println(jsonObject.getString("nameEnc"));
        System.out.println(jsonObject.getString("mobileEnc"));

    }

    /**
     * 构建主表数据
     *
     * @param orderRelation
     * @param isHistoryOrder
     * @return
     */
    private OcBOrder buildOcBOrderFromDirectOrder(IpJingdongDirectOrderRelation orderRelation,
                                                  boolean isHistoryOrder, boolean isRefund) {
        IpBJingdongDirect ipBJingdongDirect = orderRelation.getIpBJingdongDirect();
        OcBOrder order = new OcBOrder();
        //id自增长
        order.setId(buildSequenceUtil.buildOrderSequenceId());
        order.setModifierename(SystemUserResource.ROOT_USER_NAME);
        order.setOwnerename(SystemUserResource.ROOT_USER_NAME);
        order.setVersion(0L);
        BaseModelUtil.initialBaseModelSystemField(order);
        //调整金额
//        order.setAdjustAmt();
//        //配送费用。如果为空，则赋值0.
        order.setShipAmt(ipBJingdongDirect.getTotalCarriage());
//        //订单优惠金额。
        order.setOrderDiscountAmt(BigDecimal.ZERO);
//        //商品总额 2019-10-09中间表商品总价为0时取商品价格的值
//        order.setProductAmt();
//        //商品优惠金额.
//        order.setProductDiscountAmt();
//        // 已收金额。如果为空，则赋值0.
//        order.setReceivedAmt();
//        //货到付款服务费。如果为空，则赋值0.
        order.setServiceAmt(BigDecimal.ZERO);
//        //订单总额.“商品总额”+“物流费用”+“调整金额”-“订单优惠金额”-“商品优惠金额”
//        order.setOrderAmt();
//        //订单折扣 = （总金额-配送费用 -服务费）/ 商品金额。
//        order.setOrderDiscount();
        order.setIsInvented(0);
        //自动审核状态
        order.setAutoAuditStatus(0);
        //单据编号
        order.setBillNo(buildSequenceUtil.buildBillNo());
        //买家邮箱
        order.setBuyerEmail(ipBJingdongDirect.getEmail());
        //到付代收金额. 如果是支付方式=到付，则赋值订单金额
        order.setCodAmt(BigDecimal.ZERO);
        //代销结算金额. 默认为0
        order.setConsignAmt(BigDecimal.ZERO);
        //代销运费. 默认为0
        order.setConsignShipAmt(BigDecimal.ZERO);
        //发货实体仓.赋值null，后续在分配物流中使用
        order.setCpCPhyWarehouseId(null);
        //下单店铺id.
        // TODO: 需要按照云店类型进行赋值。现在还没有云店类型。暂时不进行判断赋值
        order.setCpCShopId(ipBJingdongDirect.getCpCShopId());
        //下单店铺标题。需要查个表获取Title（平台店铺信息表）
        //平台店铺标题
        CpShop shopInfo = null;
        if (ipBJingdongDirect.getCpCShopId() != null) {
            shopInfo = cpRpcService.selectShopById(ipBJingdongDirect.getCpCShopId());
        } else {
            throw new NDSException("平台店铺id为空!");
        }
        if (shopInfo != null) {
            order.setCpCShopTitle(shopInfo.getCpCShopTitle());
            //下单店仓编码. 到平台店铺信息表中获取下单店仓字段ID值；
            //order.setCpCStoreEcode(shopInfo.getCpCStoreEcode());
            //下单店仓名称. 到平台店铺信息表中获取下单店仓字段ID值；
            //order.setCpCStoreEname(shopInfo.getCpCStoreEname());
            //下单店仓id. 到平台店铺信息表中获取下单店仓字段ID值；
            //order.setCpCStoreId(shopInfo.getCpCStoreId());
            //下单店仓id. 到平台店铺信息表中获取下单卖家店铺名称
            order.setCpCShopSellerNick(shopInfo.getSellerNick());
            order.setCpCShopEcode(shopInfo.getEcode());
        } else {
            // 20190727修改：如果 平台店铺不存在，则不再继续保持。而是抛出异常，不允许转单操作
            throw new NDSException("平台店铺id=" + ipBJingdongDirect.getCpCShopId() + "不存在");
        }

        order.setIsCalcweight(0);
        //是否组合订单
        boolean hasCombine = this.hasCombineProduct(orderRelation);
        order.setIsCombination(hasCombine ? 1 : 0);
        //是否生成开票通知。现在赋值为N。占用订单后再进行赋值
        order.setIsGeninvoiceNotice(0);
        // 是否已给物流。占单后再进行赋值
        //order.setIsGiveLogistic(0);
        // 是否有赠品.0.否。计算完赠品策略赋值
        // TODO: 后期需要根据逻辑判断进行赋值
        order.setIsHasgift(0);
        //是否退款中
        order.setIsInreturning(0);
        //是否已经挂起
        //是否虚拟订单。现在赋值为N
        order.setIsInvented(0);
        //京仓订单
        order.setIsJcorder(0);
        //实缺标记
        //order.setIsLackstock(0);
        //是否合并订单 默认0不合并
        order.setIsMerge(0);
        //是否拆分订单
        order.setIsSplit(0);
        //缺货重占次数
        order.setQtySplit(0L);
        //是否生成调拨零售
        //order.setIsTodrp(0);
        //应收平台金额（京东）
        order.setJdReceiveAmt(BigDecimal.ZERO);
        //京东结算金额
        order.setJdSettleAmt(BigDecimal.ZERO);
        //物流成本.需要计算成本。默认为0
        order.setLogisticsCost(BigDecimal.ZERO);
        //合并单据后生成的订单，对原始数据进行修改
        order.setMergeOrderId(null);
        //订单占单状态
        order.setOccupyStatus(0);
        //操作费.默认为0
        //order.setOperateAmt(BigDecimal.ZERO);
        //下单时间
        order.setOrderDate(ipBJingdongDirect.getOrderCreateDate());
        if (isHistoryOrder) {
            //为历史订单时直接修改状态为平台发货
            order.setOrderStatus(OmsOrderStatus.PLATFORM_DELIVERY.toInteger());
            order.setIsHistory("Y");
            // @历史订单出库时间赋值 任务ID 29976
            order.setScanTime(new Date());
        } else {
            //订单状态. 默认状态为50
            order.setOrderStatus(OmsOrderStatus.ORDER_DEFAULT.toInteger());
            order.setIsHistory("N");
        }
        //订单标签
        order.setOrderTag(null);
        //订单类型
        order.setOrderType(OrderTypeEnum.NORMAL.getVal());
        //原始订单号。默认空
        order.setOrigOrderId(null);
        //原始退货单号
        order.setOrigReturnOrderId(null);
        //出库状态. WMS后调用,已出库未出库,现在没有用
        order.setOutStatus(null);
        //付款时间
        order.setPayTime(ipBJingdongDirect.getOrderCreateDate());
        //支付方式（淘宝，天猫没有货到付款类型。PRD中写到COD=货到付款的判断可用忽视）
        order.setPayType(OmsPayType.ON_LINE_PAY.toInteger());
        //平台
        order.setPlatform(PlatFormEnum.JINGDONG_CZ.getCode());
        // 双11的预售状态。现在暂时赋值0
        order.setDouble11PresaleStatus(0);
        //商品数量
        //order.setQtyAll(new BigDecimal(.getNum()));
        order.setSkuKindQty(new BigDecimal(orderRelation.getIpBJingdongDirectItems().size()));
        // 买家收货详细地址
        order.setReceiverAddress(ipBJingdongDirect.getAddress());
        //买家所在省
        String provinceName = ipBJingdongDirect.getProvinceName();
        //买家所在市
        String cityName = ipBJingdongDirect.getCityName();
        //买家所在区ID。
        String areaName = ipBJingdongDirect.getCountyName();
        ProvinceCityAreaInfo provinceCityAreaInfo = this.regionService.selectProvinceCityAreaInfo(provinceName,
                cityName, areaName);
        if (provinceCityAreaInfo != null && provinceCityAreaInfo.getProvinceInfo() != null) {
            order.setCpCRegionProvinceId(provinceCityAreaInfo.getProvinceInfo().getId());
            order.setCpCRegionProvinceEcode(provinceCityAreaInfo.getProvinceInfo().getCode());
            order.setCpCRegionProvinceEname(provinceName);
        } else {
            order.setCpCRegionProvinceId(null);
            order.setCpCRegionProvinceEcode(null);
        }

        if (provinceCityAreaInfo != null && provinceCityAreaInfo.getCityInfo() != null) {
            order.setCpCRegionCityId(provinceCityAreaInfo.getCityInfo().getId());
            order.setCpCRegionCityEcode(provinceCityAreaInfo.getCityInfo().getCode());
            order.setCpCRegionCityEname(provinceCityAreaInfo.getCityInfo().getName());
        } else {
            order.setCpCRegionCityId(null);
            order.setCpCRegionCityEcode(null);
        }
        if (provinceCityAreaInfo != null && provinceCityAreaInfo.getAreaInfo() != null) {
            order.setCpCRegionAreaId(provinceCityAreaInfo.getAreaInfo().getId());
            order.setCpCRegionAreaEcode(provinceCityAreaInfo.getAreaInfo().getCode());
            order.setCpCRegionAreaEname(provinceCityAreaInfo.getAreaInfo().getName());
        } else {
            order.setCpCRegionAreaEname(areaName);
        }
        order.setCpCRegionTownEname(ipBJingdongDirect.getTownName());
        order.setReceiverEmail(ipBJingdongDirect.getEmail());
        order.setReceiverMobile(ipBJingdongDirect.getPhone());
        order.setReceiverName(ipBJingdongDirect.getConsigneeName());
        order.setReceiverPhone(ipBJingdongDirect.getTelephone());
        order.setReceiverZip(ipBJingdongDirect.getPostcode());
        if (StringUtils.isNotEmpty(ipBJingdongDirect.getOrderRemark()) && ipBJingdongDirect.getOrderRemark().length() > 1000) {
            //卖家备注
            order.setSellerMemo(ipBJingdongDirect.getOrderRemark().substring(ipBJingdongDirect.getOrderRemark().length() - 1000, ipBJingdongDirect.getOrderRemark().length() - 1));
        } else {
            //卖家备注
            order.setSellerMemo(ipBJingdongDirect.getOrderRemark());
        }

        //平台单号信息
        order.setSourceCode(ipBJingdongDirect.getCustomOrderId());
        //初始平台单号（确定唯一）
        order.setTid(ipBJingdongDirect.getCustomOrderId());
        order.setMergeSourceCode(ipBJingdongDirect.getCustomOrderId());
        //版本信息
        order.setVersion(0L);
        //商品重量
        order.setWeight(BigDecimal.ZERO);
        //wms撤回状态调用WMS撤回是否成功。1=成功；2=失败
        order.setWmsCancelStatus(0);
        //仓储状态（拣货中，已打印，已装箱）
        //order.setWmsStatus(null);
        //是否插入核销流水
        //order.setIsWriteoff(0);
        //出库状态
        order.setOutStatus(1);

        // 平台状态 需要做一下映射
        String platformStatus = platformStatusEnum.get(ipBJingdongDirect.getOrderState());
        order.setPlatformStatus(platformStatus == null ? "" : platformStatus);
        //oaid
        order.setOaid(ipBJingdongDirect.getOaid());


        // 先获取出来extInfo 判断订单来源 429=2快手 429=1抖音  ，排除以上2个=厂直
        Integer sourcePlatform = null;
        String outPlatformOrderId = null;
        String whereEnc = null;
        String nameEnc = null;
        String mobileEnc = null;

        String extInfo = ipBJingdongDirect.getExtInfo();
        String outPlatformOrderInfo = ipBJingdongDirect.getOutPlatformOrderInfo();
        if (StringUtils.isNotEmpty(extInfo)) {
            // 获取里面的信息
            // {\"sendPayMap\":\"{\\\"9\\\":\\\"1\\\",\\\"20\\\":\\\"5\\\",\\\"26\\\":\\\"2\\\",\\\"29\\\":\\\"1\\\",\\\"34\\\":\\\"3\\\",\\\"37\\\":\\\"3\\\",\\\"39\\\":\\\"1\\\",\\\"51\\\":\\\"6\\\",\\\"62\\\":\\\"1\\\",\\\"64\\\":\\\"1\\\",\\\"72\\\":\\\"1\\\",\\\"101\\\":\\\"1\\\",\\\"104\\\":\\\"9\\\",\\\"183\\\":\\\"4\\\",\\\"184\\\":\\\"5\\\",\\\"190\\\":\\\"2\\\",\\\"205\\\":\\\"1\\\",\\\"229\\\":\\\"1\\\",\\\"261\\\":\\\"1\\\",\\\"262\\\":\\\"3\\\",\\\"292\\\":\\\"6\\\",\\\"298\\\":\\\"2\\\",\\\"300\\\":\\\"1\\\",\\\"302\\\":\\\"1\\\",\\\"316\\\":\\\"1\\\",\\\"327\\\":\\\"1\\\",\\\"332\\\":\\\"5\\\",\\\"368\\\":\\\"3\\\",\\\"371\\\":\\\"5\\\",\\\"384\\\":\\\"1\\\",\\\"386\\\":\\\"2\\\",\\\"413\\\":\\\"1\\\",\\\"421\\\":\\\"1\\\",\\\"429\\\":\\\"2\\\",\\\"436\\\":\\\"1\\\",\\\"738\\\":\\\"1\\\"}\",\"remark\":\"\",\"paymentConfirmTime\":\"2024-02-21 13:58:24\"}
            JSONObject extInfoJSONObject = JSONObject.parseObject(extInfo);
            JSONObject outPlatformJSONObject = JSONObject.parseObject(extInfoJSONObject.getString("sendPayMap"));
            if (ObjectUtil.equal(2, outPlatformJSONObject.getInteger("429"))) {
                // 快手
                sourcePlatform = PlatFormEnum.KUAISHOU.getCode();
            } else if (ObjectUtil.equal(1, outPlatformJSONObject.getInteger("429"))) {
                sourcePlatform = PlatFormEnum.DOU_YIN.getCode();
            }
        }
        if (ObjectUtil.isNotNull(sourcePlatform) && StringUtils.isNotEmpty(outPlatformOrderInfo)) {
            // 开始解析outPlatformOrderInfo
            JSONObject jsonObject = JSONObject.parseObject(outPlatformOrderInfo);
            outPlatformOrderId = jsonObject.getString("outPlatformOrderId");
            whereEnc = jsonObject.getString("whereEnc");
            nameEnc = jsonObject.getString("nameEnc");
            mobileEnc = jsonObject.getString("mobileEnc");
        }

        if (ObjectUtil.isNotNull(sourcePlatform) && StringUtils.isNotEmpty(outPlatformOrderId)) {
            order.setGwSourceGroup(String.valueOf(sourcePlatform));
            order.setOrderSourcePlatformEcode(outPlatformOrderId);
            if (StringUtils.isNotEmpty(whereEnc)) {
                order.setReceiverAddress(whereEnc);
            }
            if (StringUtils.isNotEmpty(nameEnc)) {
                order.setReceiverName(nameEnc);
            }
            if (StringUtils.isNotEmpty(mobileEnc)) {
                order.setReceiverMobile(mobileEnc);
            }

        }

        return order;
    }

    private boolean hasCombineProduct(IpJingdongDirectOrderRelation orderRelation) {
        Map<String, ProductSku> productSkuMap = orderRelation.getProductSkuMap();
        if (productSkuMap != null) {
            for (String skuId : productSkuMap.keySet()) {
                ProductSku productSku = productSkuMap.get(skuId);
                if (productSku != null) {
                    return productSku.getSkuType() == SkuType.COMBINE_PRODUCT
                            || productSku.getSkuType() == SkuType.GIFT_PRODUCT;
                }
            }
        }
        return false;
    }

    /**
     * 构建明细数据
     *
     * @param ocBOrder
     * @param directItem
     * @param productSkuMap
     * @param isHistoryOrder
     * @return
     */
    private OcBOrderItem buildOrderItemFromDirectOrderItem(OcBOrder ocBOrder, IpBJingdongDirectItem directItem,
                                                           Map<String, ProductSku> productSkuMap, boolean isHistoryOrder) {
        ProductSku productSku = productSkuMap.get(directItem.getSkuId());
        OcBOrderItem item = new OcBOrderItem();
        item.setId(buildSequenceUtil.buildOrderItemSequenceId());
        item.setModifierename(SystemUserResource.ROOT_USER_NAME);
        item.setOwnerename(SystemUserResource.ROOT_USER_NAME);
        item.setVersion(0L);

        BaseModelUtil.initialBaseModelSystemField(item);
        item.setAdjustAmt(BigDecimal.ZERO);
        item.setAmtDiscount(BigDecimal.ZERO);
        //整单平摊金额
        item.setOrderSplitAmt(BigDecimal.ZERO);
        item.setPrice(directItem.getJdPrice());
        item.setPriceList(productSku.getPricelist());
        // 成交单价
        item.setPriceActual(directItem.getJdPrice());
        item.setRealAmt(directItem.getJdPrice().multiply(BigDecimal.valueOf(directItem.getCommodityNum())));
        //退货金额.默认为0
        item.setAmtRefund(BigDecimal.ZERO);
        //国标码。SKU 69码。从条码档案中有一个69码字段
        item.setBarcode(productSku.getBarcode69());
        //使用积分
        item.setBuyerUsedIntegral(0L);
        //分销价格。默认为0
        item.setDistributionPrice(BigDecimal.ZERO);
        //组合名称
        item.setGroupName(null);
        //是否已经占用库存
        item.setIsAllocatestock(0);
        //买家是否已评价
        item.setIsBuyerRate(0);
        //是否是赠品
        item.setIsGift(0);
        //实缺标记
        item.setIsLackstock(0);
        //商品数字编号
        item.setNumIid(directItem.getSkuId());
        //平台条码
        item.setSkuNumiid(directItem.getSkuId() + "");
        //订单编号
        item.setOcBOrderId(ocBOrder.getId());
        //子订单编号(明细编号)
        item.setOoid(String.valueOf(directItem.getSkuId()));
        //数量
        item.setQty(BigDecimal.valueOf(directItem.getCommodityNum()));
        //已退数量。默认为0
        item.setQtyRefund(BigDecimal.ZERO);
        item.setRefundStatus(0);
        //若为组合商品的值，则【淘宝订单中间表】明细表的在【组合商品】中对应的实际商品编码（商品档案中存在，且状态为已启用），则【淘宝订单中间表】明细表的“商品编码”
        initialTaobaoOrderItem(directItem, productSku, item);

        //库位。不用赋值
        //item.setStoreSite(null);
        //标题
        item.setTitle(directItem.getCommodityName());

        item.setTid(ocBOrder.getTid());
        item.setPsCBrandId(productSku.getPsCBrandId());
        return item;
    }

    /**
     * 初始化TaobaoOrderItem内容
     * 2019-07-30 组合福袋商品修改
     *
     * @param directItem 淘宝中间表数据
     * @param item       需要赋值的taobaoorderItem
     */

    private void initialTaobaoOrderItem(IpBJingdongDirectItem directItem, ProductSku productSku, OcBOrderItem item) {
        if (productSku != null) {
            item.setPsCProId(productSku.getProdId());
            // ProECode
            item.setPsCProEcode(productSku.getProdCode());
            item.setPsCSkuId(productSku.getId());
            item.setSex(productSku.getSex());
            //2019-08-30吊牌价改为取商品表数据
            //吊牌价
            item.setPriceTag(productSku.getPricelist());
            item.setPsCClrEcode(productSku.getColorCode());
            item.setPsCClrEname(productSku.getColorName());
            item.setPsCClrId(productSku.getColorId());
            item.setPsCSizeEcode(productSku.getSizeCode());
            item.setPsCSizeEname(productSku.getSizeName());
            item.setPsCSizeId(productSku.getSizeId());
            item.setPsCProMaterieltype(productSku.getMaterialType());
            item.setStandardWeight(productSku.getWeight());
            item.setSkuSpec(productSku.getSkuSpec());
            item.setProType(NumberUtils.toLong(productSku.getSkuType() + ""));
            item.setPsCSkuPtEcode(directItem.getSkuId());
            //平台商品名称
            item.setPtProName(directItem.getCommodityName());
            //商品名称
            item.setPsCProEname(productSku.getName());
            item.setPsCSkuEname(productSku.getSkuName());
            item.setStandardWeight(productSku.getWeight());

            // 供应类型 0 普通 1.代销轻供 2.寄售轻供
            item.setPsCProSupplyType(productSku.getPsCProSupplyType());
            String psSkuEcode = productSku.getSkuEcode();
            item.setMDim4Id(productSku.getMDim4Id());
            item.setMDim6Id(productSku.getMDim6Id());
            if ("Y".equals(productSku.getIsEnableExpiry())) {
                item.setIsEnableExpiry(1);
            } else {
                item.setIsEnableExpiry(0);
            }
            if (checkIsNeedTransferSkuUpperCase()) {
                psSkuEcode = StringUtils.upperCase(psSkuEcode);
            }
            if (productSku.getSkuType() == SkuType.COMBINE_PRODUCT
                    || productSku.getSkuType() == SkuType.GIFT_PRODUCT) {
                //为福袋或者组合商品
                //虚拟条码
                item.setPsCSkuEcode(psSkuEcode);
                item.setPsCSizeEcode(psSkuEcode);
                item.setPsCProEcode(psSkuEcode);
                //组合商品数量
                item.setQtyGroup(BigDecimal.valueOf(directItem.getCommodityNum()));
                item.setProType(NumberUtils.toLong(SkuType.NO_SPLIT_COMBINE + ""));

            } else {
                item.setPsCSkuEcode(psSkuEcode);
            }
        } else {
            item.setSkuSpec(null);
            item.setStandardWeight(BigDecimal.ZERO);
        }
    }

    /**
     * 是否需要转换成大写
     * 乔丹项目中：SAP系统存储的SKU部分有小写。为了统一，库里存储的全部为大写。因此在转单的时候强制转换成大写。
     *
     * @return true
     */
    private boolean checkIsNeedTransferSkuUpperCase() {
        try {
            String value = propertiesConf.getProperty("r3.oc.oms.transfer.sku.toupper", "true");
            return StringUtils.equalsIgnoreCase(value, "true");
        } catch (Exception ex) {
            log.error("checkIsNeedTransferSkuUpperCase", ex);
            return true;
        }
    }

    public void doRecountAmount(OcBOrder ocBOrder, List<OcBOrderItem> ocBOrderItems) {
        try {
            //调整金额合计
            BigDecimal adJustAmountTotal = BigDecimal.ZERO;
            //优惠金额合计
            BigDecimal amtDiscountAmont = BigDecimal.ZERO;
            //订单优惠金额合计
            BigDecimal orderAmtDiscountAmont = BigDecimal.ZERO;
            //商品总额
            BigDecimal proCount = BigDecimal.ZERO;
            //计算非退款的明细去更新主表
            //排序正序，且空值排前面
            ocBOrderItems = ocBOrderItems.stream().filter(p -> p.getProType() != null &&
                    (p.getProType() == SkuType.NO_SPLIT_COMBINE || p.getProType() == SkuType.NORMAL_PRODUCT)).collect(Collectors.toList());
            for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
                //调整金额
                adJustAmountTotal = adJustAmountTotal.add(ocBOrderItem.getAdjustAmt() == null ? BigDecimal.ZERO : ocBOrderItem.getAdjustAmt());
                //优惠金额,如果优惠金额大于成交金额的话，默认优惠金额等于成交金额；2020/2/12追加逻辑，2/23号提交
                amtDiscountAmont = amtDiscountAmont.add(ocBOrderItem.getAmtDiscount() == null ? BigDecimal.ZERO : ocBOrderItem.getAmtDiscount());
//
                //整体平摊金额
                orderAmtDiscountAmont = orderAmtDiscountAmont.add(ocBOrderItem.getOrderSplitAmt() == null ? BigDecimal.ZERO : ocBOrderItem.getOrderSplitAmt());

                //商品总额 明细中“标准价”*（“商品数量”-“已退数量”）的绝对值合计
                proCount = proCount.add((ocBOrderItem.getPrice()
                        == null ? BigDecimal.ZERO : ocBOrderItem.getPrice()).multiply(((ocBOrderItem.getQty()
                        == null ? BigDecimal.ZERO : ocBOrderItem.getQty()).subtract(ocBOrderItem.getQtyRefund()
                        == null ? BigDecimal.ZERO : ocBOrderItem.getQtyRefund()).abs())));
            }
            //调整金额
            ocBOrder.setAdjustAmt(adJustAmountTotal);
            //商品总额
            ocBOrder.setProductAmt(proCount);
            //商品优惠金额
            ocBOrder.setProductDiscountAmt(amtDiscountAmont);
            //订单优惠金额
            ocBOrder.setOrderDiscountAmt(orderAmtDiscountAmont);
            BigDecimal orderAmt = proCount.add(ocBOrder.getShipAmt() == null ? BigDecimal.ZERO : ocBOrder.getShipAmt())
                    .add(adJustAmountTotal)
                    .add(ocBOrder.getServiceAmt() == null ? BigDecimal.ZERO : ocBOrder.getServiceAmt())
                    .subtract(amtDiscountAmont).subtract(orderAmtDiscountAmont);

            ocBOrder.setOrderAmt(orderAmt);
            ocBOrder.setReceivedAmt(orderAmt);
        } catch (Exception ex) {
            log.error("OmsComputeMainAmountService.doRecountAmount ==> 重新计算金额异常 ", ex);
            throw new NDSException(ex);
        }
    }


}

