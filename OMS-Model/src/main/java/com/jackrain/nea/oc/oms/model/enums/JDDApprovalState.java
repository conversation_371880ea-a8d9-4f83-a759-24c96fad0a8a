package com.jackrain.nea.oc.oms.model.enums;

/**
 * @Desc : 京东厂直.审核状态
 * <AUTHOR> xiWen
 * @Date : 2022/3/30
 */
public enum JDDApprovalState {

    /**
     * 未审核
     */
    UN(0),

    /**
     * 通过
     */
    PASS(1),

    /**
     * 不通过
     */
    NO(2),

    /**
     * 未定义
     */
    UNDEFINED(-1);

    Integer val;

    JDDApprovalState(Integer var) {
        this.val = var;
    }

    public Integer val() {
        return this.val;
    }

    public static JDDApprovalState convert2Enum(Integer val) {
        JDDApprovalState[] values = JDDApprovalState.values();
        for (JDDApprovalState value : values) {
            if (value.val.equals(val)) {
                return value;
            }
        }
        return UNDEFINED;
    }

}
