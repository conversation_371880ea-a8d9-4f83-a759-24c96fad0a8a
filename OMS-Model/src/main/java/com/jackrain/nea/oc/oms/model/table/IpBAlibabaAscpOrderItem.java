package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 猫超订单明细表
 */
@TableName(value = "ip_b_alibaba_ascp_order_item")
@Data
@Document(index = "ip_b_alibaba_ascp_order_item", type = "ip_b_alibaba_ascp_order_item")
public class IpBAlibabaAscpOrderItem extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    /**
     * 履约子单号
     */
    @JSONField(name = "SUB_ORDER_CODE")
    @Field(type = FieldType.Keyword)
    private String subOrderCode;
    /**
     * 货品id
     */
    @JSONField(name = "SC_ITEM_ID")
    @Field(type = FieldType.Keyword)
    private String scItemId;
    /**
     * 货品商家编码
     */
    @JSONField(name = "OUTER_ID")
    @Field(type = FieldType.Keyword)
    private String outerId;
    /**
     * 货品条形码
     */
    @JSONField(name = "BARCODE")
    @Field(type = FieldType.Keyword)
    private String barcode;

    /**
     * 货品数量
     */
    @JSONField(name = "QUANTITY")
    @Field(type = FieldType.Long)
    private Long quantity;

    /**
     * 货品名称
     */
    @JSONField(name = "SC_ITEM_NAME")
    @Field(type = FieldType.Keyword)
    private String scItemName;
    /**
     * 交易主单号
     */
    @JSONField(name = "TRADE_ORDER_ID")
    @Field(type = FieldType.Keyword)
    private String tradeOrderId;
    /**
     * 交易子单号
     */
    @JSONField(name = "SUB_TRADE_ORDER_ID")
    @Field(type = FieldType.Keyword)
    private String subTradeOrderId;

    /**
     * 对应猫超订单id
     */
    @JSONField(name = "IP_B_ALIBABA_ASCP_ORDER_ID")
    @Field(type = FieldType.Long)
    private Long ipBAlibabaAscpOrderId;

    //region 数据库扩展字段
    /**
     * 创建人ID
     */
    @JSONField(name = "OWNERID")
    @Field(type = FieldType.Long)
    private Long ownerId;

    /**
     * 创建人姓名
     */
    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownereName;

    /**
     * 创建人用户名
     */
    @JSONField(name = "OWNERNAME")
    @Field(type = FieldType.Keyword)
    private String ownerName;

    /**
     * 创建时间
     */
    @JSONField(name = "CREATIONDATE")
    @Field(type = FieldType.Long)
    private Date creationDate;

    /**
     * 修改人id
     */
    @JSONField(name = "MODIFIERID")
    @Field(type = FieldType.Long)
    private Long modifierId;

    /**
     * 修改人姓名
     */
    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifiereName;

    /**
     * 修改人用户名
     */
    @JSONField(name = "modifiername")
    @Field(type = FieldType.Keyword)
    private String modifierName;

    /**
     * 修改时间
     */
    @JSONField(name = "MODIFIEDDATE")
    @Field(type = FieldType.Long)
    private Date modifiedDate;

    /**
     * 是否可用 默认 "y"
     */
    @JSONField(name = "isactive")
    @Field(type = FieldType.Keyword)
    private String isActive;
    //endregion

    //region 预留扩展字段
    @JSONField(name = "RESERVE_BIGINT01")
    @Field(type = FieldType.Long)
    private Long reserveBigint01;

    @JSONField(name = "RESERVE_BIGINT02")
    @Field(type = FieldType.Long)
    private Long reserveBigint02;

    @JSONField(name = "RESERVE_BIGINT03")
    @Field(type = FieldType.Long)
    private Long reserveBigint03;

    @JSONField(name = "RESERVE_BIGINT04")
    @Field(type = FieldType.Long)
    private Long reserveBigint04;

    @JSONField(name = "RESERVE_BIGINT05")
    @Field(type = FieldType.Long)
    private Long reserveBigint05;

    @JSONField(name = "RESERVE_BIGINT06")
    @Field(type = FieldType.Long)
    private Long reserveBigint06;

    @JSONField(name = "RESERVE_BIGINT07")
    @Field(type = FieldType.Long)
    private Long reserveBigint07;

    @JSONField(name = "RESERVE_BIGINT08")
    @Field(type = FieldType.Long)
    private Long reserveBigint08;

    @JSONField(name = "RESERVE_BIGINT09")
    @Field(type = FieldType.Long)
    private Long reserveBigint09;

    @JSONField(name = "RESERVE_BIGINT10")
    @Field(type = FieldType.Long)
    private Long reserveBigint10;

    @JSONField(name = "RESERVE_DECIMAL01")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal01;

    @JSONField(name = "RESERVE_DECIMAL02")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal02;

    @JSONField(name = "RESERVE_DECIMAL03")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal03;

    @JSONField(name = "RESERVE_DECIMAL04")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal04;

    @JSONField(name = "RESERVE_DECIMAL05")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal05;

    @JSONField(name = "RESERVE_DECIMAL06")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal06;

    @JSONField(name = "RESERVE_DECIMAL07")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal07;

    @JSONField(name = "RESERVE_DECIMAL08")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal08;

    @JSONField(name = "RESERVE_DECIMAL09")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal09;

    @JSONField(name = "RESERVE_DECIMAL10")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal10;

    @JSONField(name = "RESERVE_VARCHAR01")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar01;

    @JSONField(name = "RESERVE_VARCHAR02")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar02;

    @JSONField(name = "RESERVE_VARCHAR03")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar03;

    @JSONField(name = "RESERVE_VARCHAR04")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar04;

    @JSONField(name = "RESERVE_VARCHAR05")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar05;

    @JSONField(name = "RESERVE_VARCHAR06")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar06;

    @JSONField(name = "RESERVE_VARCHAR07")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar07;

    @JSONField(name = "RESERVE_VARCHAR08")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar08;

    @JSONField(name = "RESERVE_VARCHAR09")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar09;

    @JSONField(name = "RESERVE_VARCHAR10")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar10;
    //endregion
}