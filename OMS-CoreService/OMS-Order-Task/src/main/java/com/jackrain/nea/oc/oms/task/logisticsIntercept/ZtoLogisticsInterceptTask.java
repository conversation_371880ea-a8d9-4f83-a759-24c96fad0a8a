package com.jackrain.nea.oc.oms.task.logisticsIntercept;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.basic.model.request.SgStorageRedisInitRequest;
import com.burgeon.r3.xxl.job.starter.helper.R3XxlJobParamHelper;
import com.google.common.base.Throwables;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.reflect.TypeToken;
import com.jackrain.nea.oc.oms.model.request.ZtoLogisticsInterceptTaskRequest;
import com.jackrain.nea.oc.oms.services.ZtoLogisticsInterceptService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2023/3/7 13:22
 * @Description
 */
@Slf4j
@Component
public class ZtoLogisticsInterceptTask implements IR3Task {

    @Resource
    private ZtoLogisticsInterceptService ztoLogisticsInterceptService;

    @Override
    @XxlJob("ZtoLogisticsInterceptTask")
    public RunTaskResult execute(JSONObject params) {
        params = R3XxlJobParamHelper.xxlParam2R3Json();
        log.info(LogUtil.format("Start.ZtoLogisticsInterceptTask.execute.params={};",
                "ZtoLogisticsInterceptTask.execute"), JSON.toJSONString(params));
        RunTaskResult result = new RunTaskResult();
        ZtoLogisticsInterceptTaskRequest request;
        JsonParser jsonParser = new JsonParser();
        Gson gson = new GsonBuilder()
                .setDateFormat("yyyy-MM-dd HH:mm:ss")
                .create();

        if (params.isEmpty()) {
            request = new ZtoLogisticsInterceptTaskRequest();
        } else {
            JsonObject jsonParam = jsonParser.parse(params.toString()).getAsJsonObject();

            request = gson.fromJson(jsonParam,
                    new TypeToken<ZtoLogisticsInterceptTaskRequest>() {
                    }.getType());
        }


        try {
            ValueHolderV14<Void> v14 = ztoLogisticsInterceptService.interceptCreateForFail(request);
            if (v14.isOK()) {
                result.setSuccess(true);
                result.setMessage(v14.getMessage());
            } else {
                result.setSuccess(false);
                result.setMessage(v14.getMessage());
            }
        } catch (Exception e) {
            log.error(LogUtil.format("ZtoLogisticsInterceptTask.Execute.Error: {}"), Throwables.getStackTraceAsString(e));
            result.setSuccess(false);
            result.setMessage(e.getMessage());
        }
        return result;
    }
}
