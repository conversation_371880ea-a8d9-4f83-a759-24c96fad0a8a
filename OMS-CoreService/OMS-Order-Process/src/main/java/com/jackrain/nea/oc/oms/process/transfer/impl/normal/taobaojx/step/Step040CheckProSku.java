package com.jackrain.nea.oc.oms.process.transfer.impl.normal.taobaojx.step;

import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoJxOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoJxOrder;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoJxOrderItemEx;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 判断经销商订单明细中商品条码是否在商品条码表中存在
 **/
@Step(order = 40, description = "判断经销商订单明细中商品条码是否在商品条码表中存在")
@Slf4j
@Component
public class Step040CheckProSku extends BaseTaobaoJxOrderProcessStep
        implements IOmsOrderProcessStep<IpTaobaoJxOrderRelation> {
    @Override
    public ProcessStepResult<IpTaobaoJxOrderRelation> startProcess(IpTaobaoJxOrderRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        IpBTaobaoJxOrder taobaoJxOrder = orderInfo.getTaobaoJxOrder();
        List<IpBTaobaoJxOrderItemEx> ipBTaobaoJxOrderItemList = orderInfo.getIpBTaobaoJxOrderItemExList();
        int hasErrorInfo = 0;
        StringBuilder sbErrorInfo = new StringBuilder();
        try {
            //判断经销商订单明细中商品条码是否在商品条码表中存在
            if (CollectionUtils.isEmpty(ipBTaobaoJxOrderItemList)) {
                ipTaobaoJxOrderService.updateIsTrans(TransferOrderStatus.TRANSFERFAIL,
                        "中间表明细为空，转换失败！", orderInfo.getTaobaoJxOrder());
                return new ProcessStepResult<>(StepStatus.FAILED, "中间表明细为空，转换失败！");
            }

            for (IpBTaobaoJxOrderItemEx ipBTaobaoJxOrderItem : ipBTaobaoJxOrderItemList) {
                String skuId = ipBTaobaoJxOrderItem.getSkuNumber();
                if (null == skuId) {
                    hasErrorInfo++;
                    sbErrorInfo.append("商品SkuId为空;");
                    sbErrorInfo.append("\r\n");
                } else {
                    ProductSku skuInfo = psRpcService.selectProductSku(skuId);
                    if (skuInfo == null) {
                        hasErrorInfo++;
                        String msg = Resources.getMessage("商品条码" + skuId + "在本地商品中不存在;");
                        sbErrorInfo.append(msg);
                        sbErrorInfo.append("\r\n");
                    } else if (YesNoEnum.N.getKey().equals(skuInfo.getIsactive())) {
                        hasErrorInfo++;
                        String msg = Resources.getMessage("商品条码" + skuId + "在本地商品中已作废;");
                        sbErrorInfo.append(msg);
                        sbErrorInfo.append("\r\n");
                    } else {
                        ipBTaobaoJxOrderItem.setProdSku(skuInfo);
                    }
                }
            }
            if (hasErrorInfo > 0) {
                String errorMessage = "商品不存在或已作废，不允许单据转换！";
                boolean updateStatusRes = ipTaobaoJxOrderService.updateIsTrans(TransferOrderStatus.TRANSFERFAIL,
                        sbErrorInfo.toString(), taobaoJxOrder);
                if (!updateStatusRes) {
                    errorMessage += ";更新状态失败=False";
                }
                return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
            } else {
                return new ProcessStepResult<>(StepStatus.SUCCESS, "单据" + orderInfo.getOrderNo() + "判断条码明细成功，进入下一阶段！");
            }
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 淘宝经销订单转换异常！", e);
            //修改中间表状态及系统备注
            ipTaobaoJxOrderService.updateIsTransError(taobaoJxOrder, e.getMessage());
            String errorMessage = "淘宝经销订单转换异常！" + e.getMessage();
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
    }
}
