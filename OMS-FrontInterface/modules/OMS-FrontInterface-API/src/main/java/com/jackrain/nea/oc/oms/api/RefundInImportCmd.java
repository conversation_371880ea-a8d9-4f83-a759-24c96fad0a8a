package com.jackrain.nea.oc.oms.api;


import com.jackrain.nea.oc.oms.model.table.OcBRefundIn;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

import java.util.List;

/**
 * @program: R3-OC-OMS-V1.4
 * @author: Lijp
 * @create: 2019-08-26 10:17
 */
public interface RefundInImportCmd {


    /**
     * 批量导入处理人，处理人备注
     *
     * @param ocBRefundInList 退货入库单主表信息
     * @param cover
     * @param user            当前登录用户
     * @return
     */
    ValueHolderV14 batchImport(List<OcBRefundIn> ocBRefundInList, Boolean cover, User user);
}
