package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.util.Date;

@TableName(value = "ST_C_HOLD_ORDER")
@Data
@Document(index = "st_c_hold_order", type = "st_c_hold_order")
public class StCHoldOrderDO extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    /**
     * 方案名称
     */
    @JSONField(name = "ENAME")
    @Field(type = FieldType.Keyword)
    private String ename;

    /**
     * 店铺ID
     */
    @JSONField(name = "CP_C_SHOP_ID")
    @Field(type = FieldType.Keyword)
    private String cpCShopId;

    /**
     * 店铺名称
     */
    @JSONField(name = "CP_C_SHOP_TITLE")
    @Field(type = FieldType.Keyword)
    private String cpCShopTitle;

    /**
     * 日期类型 1:下单时间 2:支付时间
     */
    @JSONField(name = "DAY_TYPE")
    @Field(type = FieldType.Integer)
    private Integer dayType;

    /**
     * 方案开始时间
     */
    @JSONField(name = "BEGIN_TIME")
    @Field(type = FieldType.Date)
    private Date beginTime;

    /**
     * 方案结束时间
     */
    @JSONField(name = "END_TIME")
    @Field(type = FieldType.Date)
    private Date endTime;

    /**
     * hold单原因 1:直播hold单 2:买家HOLD单
     */
    @JSONField(name = "HOLD_ORDER_REASON")
    @Field(type = FieldType.Integer)
    private Integer holdOrderReason;

    /**
     * 策略类型 1:卡单 2:HOLD单
     */
    @JSONField(name = "STRATEGY_TYPE")
    @Field(type = FieldType.Integer)
    private Integer strategyType;

    /**
     * 订单标识
     */
    @JSONField(name = "ORDER_FLAG")
    @Field(type = FieldType.Keyword)
    private String orderFlag;

    /**
     * 释放时点 1:指定时点释放 2:固定时长后释放
     */
    @JSONField(name = "RELEASE_TIME_TYPE")
    @Field(type = FieldType.Integer)
    private Integer releaseTimeType;

    /**
     * 指定时点释放
     */
    @JSONField(name = "RELEASE_TIME")
    @Field(type = FieldType.Date)
    private Date releaseTime;

    /**
     * 固定时长后释放
     */
    @JSONField(name = "FIXED_DURATION")
    @Field(type = FieldType.Integer)
    private Integer fixedDuration;

    /**
     * 是否自动释放 Y:是 N:否
     */
    @JSONField(name = "IS_AUTO_RELEASE")
    @Field(type = FieldType.Keyword)
    private String isAutoRelease;

    /**
     * 时间单位 1:分钟 2:小时 3:天
     */
    @JSONField(name = "TIME_UNIT")
    @Field(type = FieldType.Integer)
    private Integer timeUnit;

    /**
     * 方案状态 1:未审核 2:已审核 3:已作废 4:已结案
     */
    @JSONField(name = "ESTATUS")
    @Field(type = FieldType.Integer)
    private Integer estatus;

    /**
     * 具体hold直播场次
     */
    @JSONField(name = "HOLD_LIVE_EVENTS")
    @Field(type = FieldType.Keyword)
    private String holdLiveEvents;

    /**
     * HOLD/卡单原因
     */
    @JSONField(name = "HOLD_DETENTION_ORDER_REASON")
    @Field(type = FieldType.Long)
    private Integer holdDetentionOrderReason;

    /**
     * 提前时间
     */
    @JSONField(name = "ADVANCE_TIME")
    @Field(type = FieldType.Integer)
    private Integer advanceTime;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;
}