package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Document(index = "oc_b_refund_in", type = "oc_b_refund_in")
@ApiModel(value = "oc_b_refund_in", description = "退货入库单")
public class OcBRefundIn extends BaseModel {


    @ApiModelProperty(value = "入库单编号")
    @Field(type = FieldType.Long)
    @JSONField(name = "ID")
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @ApiModelProperty(value = "批次编号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "BATCH_NO")
    private String batchNo;

    @ApiModelProperty(value = "退货批次id")
    @Field(type = FieldType.Long)
    @JSONField(name = "OC_B_REFUND_BATCH_ID")
    private Long ocBRefundBatchId;

    @ApiModelProperty(value = "所有SKU")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "ALL_SKU")
    private String allSku;

    @ApiModelProperty(value = "下单用户编号")
    @Field(type = FieldType.Long)
    @JSONField(name = "USER_ID")
    private Long userId;

    @ApiModelProperty(value = "平台单号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SOURCE_CODE")
    private String sourceCode;

    @ApiModelProperty(value = "买家昵称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "USER_NICK")
    private String userNick;

    @ApiModelProperty(value = "入库店仓id")
    @Field(type = FieldType.Long)
    @JSONField(name = "IN_STORE_ID")
    private Long inStoreId;

    @ApiModelProperty(value = "入库店仓编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "IN_STORE_ECODE")
    private String inStoreEcode;

    @ApiModelProperty(value = "入库店仓名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "IN_STORE_ENAME")
    private String inStoreEname;

    @ApiModelProperty(value = "备注")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "REMARK")
    private String remark;

    @ApiModelProperty(value = "物流单号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "LOGISTIC_NUMBER")
    private String logisticNumber;

    @ApiModelProperty(value = "收货人姓名")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RECEIVER_NAME")
    private String receiverName;

    @ApiModelProperty(value = "收货人手机号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RECEIVER_MOBILE")
    private String receiverMobile;

    @ApiModelProperty(value = "发件地址")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RECEIVER_ADDRESS")
    private String receiverAddress;

    @ApiModelProperty(value = "物流公司id")
    @Field(type = FieldType.Long)
    @JSONField(name = "CP_C_LOGISTICS_ID")
    private Long cpCLogisticsId;

    @ApiModelProperty(value = "物流公司编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CP_C_LOGISTICS_ECODE")
    private String cpCLogisticsEcode;

    @ApiModelProperty(value = "物流公司名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CP_C_LOGISTICS_ENAME")
    private String cpCLogisticsEname;

    @ApiModelProperty(value = "原单单号")
    @Field(type = FieldType.Long)
    @JSONField(name = "ORIG_ORDER_NO")
    private Long origOrderNo;

    @Field(type = FieldType.Integer)
    @JSONField(name = "IN_STATUS")
    private Integer inStatus;

    @Field(type = FieldType.Integer)
    @JSONField(name = "MATCH_STATUS")
    private Integer matchStatus;

    @Field(type = FieldType.Keyword)
    @JSONField(name = "SPECIAL_TYPE")
    private String specialType;

    @ApiModelProperty(value = "版本号")
    @Field(type = FieldType.Long)
    @JSONField(name = "VERSION")
    private Long version;

    @ApiModelProperty(value = "创建人姓名")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @ApiModelProperty(value = "修改人姓名")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    @ApiModelProperty(value = "入库实体仓库id")
    @Field(type = FieldType.Long)
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID")
    private Long cpCPhyWarehouseId;

    @ApiModelProperty(value = "入库实体仓库编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ECODE")
    private String cpCPhyWarehouseEcode;

    @ApiModelProperty(value = "入库实体仓库名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ENAME")
    private String cpCPhyWarehouseEname;

    @ApiModelProperty(value = "门店档案的门店编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "STORE_CODE")
    private String storeCode;

    @ApiModelProperty(value = "门店档案的门店名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "STORE_NAME")
    private String storeName;

    @ApiModelProperty(value = "门店档案的门店id")
    @Field(type = FieldType.Long)
    @JSONField(name = "STORE_ID")
    private Long storeId;

    @ApiModelProperty(value = "门店档案的结算组织名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SETTLE_ORG_NAME")
    private String settleOrgName;

    @ApiModelProperty(value = "门店档案的结算组织编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SETTLE_ORG_CODE")
    private String settleOrgCode;

    @ApiModelProperty(value = "门店档案的结算供应商编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SETTLE_SUPPLIER_CODE")
    private String settleSupplierCode;

    @ApiModelProperty(value = "门店档案的结算供应商名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SETTLE_SUPPLIER_NAME")
    private String settleSupplierName;

    @ApiModelProperty(value = "扫描入库失败次数")
    @Field(type = FieldType.Long)
    @JSONField(name = "QTY_FAIL")
    private Long qtyFail;

    @ApiModelProperty(value = "自动匹配次数")
    @Field(type = FieldType.Long)
    @JSONField(name = "QTY_MATCH")
    private Long qtyMatch;

    @ApiModelProperty(value = "匹配人")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "MATCHER")
    private String matcher;

    @ApiModelProperty(value = "处理人备注")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "REMARK_HANDLE")
    private String remarkHandle;

    @ApiModelProperty(value = "匹配时间")
    @Field(type = FieldType.Long)
    @JSONField(name = "MATCHED_TIME")
    private Date matchedTime;

    @ApiModelProperty(value = "实际发出条码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PS_C_SKU_ECODE_ACTUAL")
    private String psCSkuEcodeActual;

    @ApiModelProperty(value = "处理人")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "HANDLER")
    private String handler;

    @ApiModelProperty(value = "入库备注")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "REMARK_IN")
    private String remarkIn;

    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_OFF_MATCH")
    private Integer isOffMatch;

    @ApiModelProperty(value = "WMS单据编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "WMS_BILL_NO")
    private String wmsBillNo;

    @ApiModelProperty(value = "数字类型备用字段1")
    @Field(type = FieldType.Long)
    @JSONField(name = "RESERVE_BIGINT01")
    private Long reserveBigint01;

    @ApiModelProperty(value = "数字类型备用字段2")
    @Field(type = FieldType.Long)
    @JSONField(name = "RESERVE_BIGINT02")
    private Long reserveBigint02;

    @ApiModelProperty(value = "数字类型备用字段3")
    @Field(type = FieldType.Long)
    @JSONField(name = "RESERVE_BIGINT03")
    private Long reserveBigint03;

    @ApiModelProperty(value = "数字类型备用字段4")
    @Field(type = FieldType.Long)
    @JSONField(name = "RESERVE_BIGINT04")
    private Long reserveBigint04;

    @ApiModelProperty(value = "数字类型备用字段5")
    @Field(type = FieldType.Long)
    @JSONField(name = "RESERVE_BIGINT05")
    private Long reserveBigint05;

    @ApiModelProperty(value = "价格备用字段1")
    @Field(type = FieldType.Double)
    @JSONField(name = "RESERVE_DECIMAL01")
    private BigDecimal reserveDecimal01;

    @ApiModelProperty(value = "价格备用字段2")
    @Field(type = FieldType.Double)
    @JSONField(name = "RESERVE_DECIMAL02")
    private BigDecimal reserveDecimal02;

    @ApiModelProperty(value = "价格备用字段3")
    @Field(type = FieldType.Double)
    @JSONField(name = "RESERVE_DECIMAL03")
    private BigDecimal reserveDecimal03;

    @ApiModelProperty(value = "价格备用字段4")
    @Field(type = FieldType.Double)
    @JSONField(name = "RESERVE_DECIMAL04")
    private BigDecimal reserveDecimal04;

    @ApiModelProperty(value = "价格备用字段5")
    @Field(type = FieldType.Double)
    @JSONField(name = "RESERVE_DECIMAL05")
    private BigDecimal reserveDecimal05;

    @ApiModelProperty(value = "文本备用字段1")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RESERVE_VARCHAR01")
    private String reserveVarchar01;

    @ApiModelProperty(value = "文本备用字段2")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RESERVE_VARCHAR02")
    private String reserveVarchar02;

    @ApiModelProperty(value = "文本备用字段3")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RESERVE_VARCHAR03")
    private String reserveVarchar03;

    @ApiModelProperty(value = "文本备用字段4")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RESERVE_VARCHAR04")
    private String reserveVarchar04;

    @ApiModelProperty(value = "文本备用字段5")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RESERVE_VARCHAR05")
    private String reserveVarchar05;

    @ApiModelProperty(value = "单据状态")
    @Field(type = FieldType.Integer)
    @JSONField(name = "ORDER_STATUS")
    private Integer orderStatus;

    @ApiModelProperty(value = "提交人")
    @Field(type = FieldType.Long)
    @JSONField(name = "SUBMIT_ID")
    private Long submitId;

    @ApiModelProperty(value = "作废人")
    @Field(type = FieldType.Long)
    @JSONField(name = "CANCEL_ID")
    private Long cancelId;

    @ApiModelProperty(value = "提交时间")
    @Field(type = FieldType.Date)
    @JSONField(name = "SUBMIT_DATE")
    private Date submitDate;

    @ApiModelProperty(value = "作废时间")
    @Field(type = FieldType.Date)
    @JSONField(name = "CANCEL_DATE")
    private Date cancelDate;

    @ApiModelProperty(value = "单据状态")
    @Field(type = FieldType.Integer)
    @JSONField(name = "BILL_STATUS")
    private Integer billStatus;

    @ApiModelProperty(value = "是否错收")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_WRONG_RECEIVE")
    private Integer isWrongReceive;

    @ApiModelProperty(value = "入库类型")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IN_TYPE")
    private Integer inType;

    @ApiModelProperty(value = "虚拟入库状态")
    @Field(type = FieldType.Integer)
    @JSONField(name = "VIRTUAL_IN_STATUS")
    private Integer virtualInStatus;

    @ApiModelProperty(value = "入库通知单号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SG_B_NOTICE_IN_BILL_NO")
    private String sgBNoticeInBillNo;

    @ApiModelProperty(value = "入库时间")
    @Field(type = FieldType.Date)
    @JSONField(name = "WAREHOUSE_IN_TIME")
    private Date warehouseInTime;

    @ApiModelProperty(value = "数异少（0.否 1.是）")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "NUM_LESS")
    private String numLess;

    @ApiModelProperty(value = "数异多（0.否 1.是）")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "NUM_MORE")
    private String numMore;

    @ApiModelProperty(value = "品异（0.否 1.是）")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PRODUCT_DIFF")
    private String productDiff;
}
