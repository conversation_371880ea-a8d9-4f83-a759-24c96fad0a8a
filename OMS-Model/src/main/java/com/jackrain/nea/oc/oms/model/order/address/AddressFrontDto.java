package com.jackrain.nea.oc.oms.model.order.address;

import java.io.Serializable;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @author: DXF
 * @since: 2020/11/26
 * create at : 2020/11/26 14:03
 */
@Data
@Accessors(chain = true)
public class AddressFrontDto implements Serializable {

    /**
     * 订单ID
     */
    @JSONField(name = "id")
    @JsonProperty(value = "id")
    private Long id;

    @JSONField(name = "updateInfo")
    @JsonProperty(value = "updateInfo")
    private ReceiverAddressDto updateInfo;

}
