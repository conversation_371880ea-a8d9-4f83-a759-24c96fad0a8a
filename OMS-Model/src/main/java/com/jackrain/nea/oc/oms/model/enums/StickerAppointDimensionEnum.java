package com.jackrain.nea.oc.oms.model.enums;

import lombok.Getter;

/**
 * 贴纸策略指定维度枚举
 *
 * <AUTHOR>
 * @date 2023/11/30
 */
@Getter
public enum StickerAppointDimensionEnum {
    /**
     * 无主播ID+SKU
     */
    NO_ANCHOR_SKU(0, "无主播ID+SKU"),

    /**
     * 指定主播ID+SKU
     */
    ANCHOR_SKU(1, "指定主播ID+SKU"),

    /**
     * 无主播ID+四级类目
     */
    NO_ANCHOR_CATEGORY(2, "无主播ID+四级类目"),

    /**
     * 指定主播ID+四级类目
     */
    ANCHOR_CATEGORY(3, "指定主播ID+四级类目");

    /**
     * 维度编码
     */
    private final Integer code;

    /**
     * 维度名称
     */
    private final String name;

    StickerAppointDimensionEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static StickerAppointDimensionEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (StickerAppointDimensionEnum value : StickerAppointDimensionEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 根据编码获取名称
     *
     * @param code 编码
     * @return 名称
     */
    public static String getNameByCode(Integer code) {
        StickerAppointDimensionEnum appointDimensionEnum = getByCode(code);
        return appointDimensionEnum == null ? String.valueOf(code) : appointDimensionEnum.getName();
    }
}
