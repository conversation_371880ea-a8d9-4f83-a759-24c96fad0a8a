package com.jackrain.nea.oc.oms.matcher.live;

import lombok.Getter;

import java.util.Objects;

/**
 * Description： 是否直播单枚举
 * Author: RESET
 * Date: Created in 2020/6/15 21:39
 * Modified By:
 */
public enum LiveFlagEnum {

    // 匹配策略类型
    Y(1, "Yes", "直播订单"),
    N(0, "No", "非直播订单");

    @Getter
    private Integer value;
    @Getter
    private String code;
    @Getter
    private String description;

    LiveFlagEnum(Integer value, String code, String description) {
        this.value = value;
        this.code = code;
        this.description = description;
    }

    @Override
    public String toString() {
        return String.valueOf(value);
    }

    public static LiveFlagEnum fromValue(Integer v) {
        for (LiveFlagEnum c : LiveFlagEnum.values()) {
            if (Objects.equals(v, c.value)) {
                return c;
            }
        }
        throw new IllegalArgumentException(String.valueOf(v));
    }

}
