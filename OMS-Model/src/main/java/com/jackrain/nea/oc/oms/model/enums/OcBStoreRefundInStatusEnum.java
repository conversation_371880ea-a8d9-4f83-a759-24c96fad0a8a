package com.jackrain.nea.oc.oms.model.enums;

/**
 * className: OcBStoreRefundInStatusEnum
 * description:门店退入库单状态
 *
 * <AUTHOR>
 * create: 2021-06-30
 * @since JDK 1.8
 */
public enum OcBStoreRefundInStatusEnum {
    /**
     * 0-创建；1-已提交；2-已作废
     */
    INIT( 0),
    SUBMITTED( 1),
    CANCELED( 2);


    Integer val;

    OcBStoreRefundInStatusEnum(Integer v) {
        this.val = v;
    }

    public Integer getVal() {
        return val;
    }
}
