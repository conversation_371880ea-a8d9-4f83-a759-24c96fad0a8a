package com.jackrain.nea.oc.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.ActualLackProcessingOrdersSaveCmd;
import com.jackrain.nea.oc.oms.services.ActualLackProcessingOrdersService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @program: R3-OC-OMS-V1.4
 * @author: Liqb
 * @Desc 实缺处理单据保存
 * @create: 2019-07-18 10:00
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class ActualLackProcessingOrdersSaveImpl extends CommandAdapter implements ActualLackProcessingOrdersSaveCmd {

    @Autowired
    private ActualLackProcessingOrdersService actualLackProcessingOrdersService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return actualLackProcessingOrdersService.saveData(session);
    }
}
