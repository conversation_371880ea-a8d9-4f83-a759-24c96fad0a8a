package com.jackrain.nea.oc.oms.process.transfer.impl.normal.standplat.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpStandplatOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatOrder;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 释放当前行，更新转换状态为2
 *
 * @author: ming.fz
 * @since: 2019-07-11
 * create at : 2019-07-11
 */
@Step(order = 80, description = "释放当前行，更新转换状态为2")
@Component
@Slf4j
public class Step080StandplatUpdateOrderTransferStatus extends BaseStandplatOrderProcessStep
        implements IOmsOrderProcessStep<IpStandplatOrderRelation> {

    @Override
    public ProcessStepResult<IpStandplatOrderRelation> startProcess(IpStandplatOrderRelation orderInfo,
                                                                    ProcessStepResult preStepResult,
                                                                    boolean isAutoMakeup, User operateUser) {
        String orderNo = orderInfo.getOrderNo();
        IpBStandplatOrder order = orderInfo.getStandplatOrder();

        if (Objects.nonNull(order) && Objects.nonNull(order.getIstrans()) && TransferOrderStatus.TRANSFERRED.toInteger() == order.getIstrans()) {
            log.info(LogUtil.format("Step080StandplatUpdateOrderTransferStatus.单据已转换不再更新","单据已转换不再更新",orderNo));
        } else {
            String remark = "转单成功";
            log.info(LogUtil.format("Step080StandplatUpdateOrderTransferStatus.单据未转换需要更新","单据未转换需要更新",orderNo));
            this.ipStandplatOrderService.updateStandPlatOrderTransStatus(orderNo, TransferOrderStatus.TRANSFERRED, remark,null);
        }

        return new ProcessStepResult<>(StepStatus.SUCCESS);
    }
}
