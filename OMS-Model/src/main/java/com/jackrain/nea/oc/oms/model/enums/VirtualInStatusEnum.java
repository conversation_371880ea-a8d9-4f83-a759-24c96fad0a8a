package com.jackrain.nea.oc.oms.model.enums;

/**
 * 退换货订单虚拟入库状态
 *
 * @author: ming.fz
 * @since: 2019-03-27
 */
public enum VirtualInStatusEnum {

    /**
     * 0未虚拟入库
     */
    UN,

    /**
     * 1虚拟入库未入库
     */
    NOT,

    /**
     * 2虚拟入库已入库
     */
    FINISH;

    public int toInt() {
        if (this == VirtualInStatusEnum.UN) {
            return 0;
        } else if (this == VirtualInStatusEnum.NOT) {
            return 1;
        } else if (this == VirtualInStatusEnum.FINISH) {
            return 2;
        } else {
            return -1;
        }

    }

    public Integer integer() {
        if (this == VirtualInStatusEnum.UN) {
            return 0;
        } else if (this == VirtualInStatusEnum.NOT) {
            return 1;
        } else if (this == VirtualInStatusEnum.FINISH) {
            return 2;
        } else {
            return -1;
        }
    }


}
