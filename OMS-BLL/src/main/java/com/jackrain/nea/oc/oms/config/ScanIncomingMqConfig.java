package com.jackrain.nea.oc.oms.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR> 孙俊磊
 * @since : 2019-08-26
 * create at : 2019-08-26 8:05 PM
 */
@Configuration
@Data
public class ScanIncomingMqConfig {
    @Value("${r3.oc.oms.refundIn.mq.topic:}")
    private String sendMqTopic;

    @Value("${r3.oc.oms.refundIn.mq.tag:}")
    private String sendMqTag;
}
