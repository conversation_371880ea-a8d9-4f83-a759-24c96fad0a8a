package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.api.OcBOrderAppointLogisticsCmd;
import com.jackrain.nea.oc.oms.services.OcBOrderAppointLogisticsService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/3/28 18:22
 * @Description 手动指定快递
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBOrderAppointLogisticsCmdImpl implements OcBOrderAppointLogisticsCmd {

    @Resource
    private OcBOrderAppointLogisticsService ocBOrderAppointLogisticsService;

    @Override
    public ValueHolderV14<JSONObject> queryLogistics(List<Long> ids, Integer page, Integer size) {
        return ocBOrderAppointLogisticsService.queryLogistics(ids, page, size);
    }

    @Override
    public ValueHolderV14<Void> appointLogistics(List<Long> ids, Long logisticsId, User user) {
        return ocBOrderAppointLogisticsService.appointLogistics(ids, logisticsId, user);
    }

    @Override
    public ValueHolderV14<Void> cancelAppointLogistics(List<Long> ids, User user, boolean isAuto) {
        return ocBOrderAppointLogisticsService.cancelAppointLogistics(ids, user, isAuto);
    }
}
