package com.jackrain.nea.oc.oms.process.transfer.impl.normal.standplat.step;

import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.AbnormalTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpStandplatOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatOrder;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatOrderItemEx;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * 判断通用订单单据转换状态
 *
 * @author: ming.fz
 * @since: 2019-07-04
 * create at : 2019-07-04
 */
@Step(order = 10, description = "判断通用订单单据转换状态")
@Slf4j
@Component
public class Step010StandplatCheckOrderTransferStatus extends BaseStandplatOrderProcessStep
        implements IOmsOrderProcessStep<IpStandplatOrderRelation> {

    @Override
    public ProcessStepResult<IpStandplatOrderRelation> startProcess(IpStandplatOrderRelation orderInfo,
                                                                    ProcessStepResult preStepResult,
                                                                    boolean isAutoMakeup, User operateUser) {
        IpStandplatOrderRelation orderRelation = ipStandplatOrderService.selectStandplatOrder(orderInfo.getOrderNo());

        if (orderInfo == null || orderInfo.getStandplatOrder() == null || orderRelation == null
        ) {
            return new ProcessStepResult<>(StepStatus.FAILED, "OrderInfo为空或者Order.StandplatOrder为空；退出转换");
        }
        if (CollectionUtils.isEmpty(orderRelation.getStandPlatOrderItemList())
        ) {
            String remarks = "标准转单失败，空明细；退出转换";
            boolean b = ipStandplatOrderService.updateStandPlatOrderTransStatus(orderInfo.getOrderNo(),
                    TransferOrderStatus.TRANSFERFAIL, remarks, null);
            return new ProcessStepResult<>(StepStatus.FAILED, remarks);
        }

        //如果是京东代销订单，并且没有seller_id 为空 直接标记已转换
        if (PlatFormEnum.JINGDONG_DX.getCode().equals(orderInfo.getStandplatOrder().getCpCPlatformId().intValue())){
            String venderId = orderInfo.getStandplatOrder().getSellerId();
            if (StringUtils.isEmpty(venderId)){
                String remarks = "京东代销订单，分销商id值（venderId）无值，则标记已转换！";
                ipStandplatOrderService.updateStandPlatOrderTransStatus(orderInfo.getOrderNo(),
                        TransferOrderStatus.TRANSFERRED, remarks, null);
                return new ProcessStepResult<>(StepStatus.FAILED, remarks);
            }
        }
        /**
         * 如果订单明细为退款成功且零售发货单中没有这个单据的订单自动标记为已转换；
         */
        List<IpBStandplatOrderItemEx> standPlatOrderItemList = orderRelation.getStandPlatOrderItemList();
        List<IpBStandplatOrderItemEx> collect = standPlatOrderItemList.stream().filter(s -> !"6".equals(s.getRefundStatus())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            String tid = orderInfo.getStandplatOrder().getTid();
            List<OcBOrder> findOrderInfoList = orderService.selectOmsOrderInfo(tid);
            if (CollectionUtils.isEmpty(findOrderInfoList)) {
                String remarks = "明细退款成功，并且没有零售发货单！";
                ipStandplatOrderService.updateStandPlatOrderTransStatus(orderInfo.getOrderNo(),
                        TransferOrderStatus.TRANSFERRED, remarks, null);
                return new ProcessStepResult<>(StepStatus.FAILED, remarks);
            }
        }
//        if (!isEmptySkuNumber(orderInfo)) {
//            String remarks = "明细中条码为空不予转换！";
//            ipStandplatOrderService.updateStandPlatOrderTransStatus(orderInfo.getOrderNo(),
//                    TransferOrderStatus.TRANSFERFAIL, remarks, null);
//            return new ProcessStepResult<>(StepStatus.FAILED, remarks);
//        }
        int currentStatus = orderInfo.getStandplatOrder().getIstrans();
        String orderNo = orderInfo.getOrderNo();
        //双重保险防止补偿任务和mq同时执行同张单据
        int istrans = orderRelation.getStandplatOrder().getIstrans();
        if (TransferOrderStatus.TRANSFERRED.toInteger() == currentStatus && TransferOrderStatus.TRANSFERRED.toInteger() == istrans) {
            String operateMessage = Resources.getMessage("单据" + orderInfo.getOrderId() + "状态=已转换，转换完成");
            return new ProcessStepResult<>(StepStatus.FINISHED, operateMessage);
        } else if (TransferOrderStatus.TRANSFERRING.toInteger() == currentStatus) {
            String operateMessage = Resources.getMessage("单据" + orderInfo.getOrderId() + "正在转换中");
            return new ProcessStepResult<>(StepStatus.FINISHED, operateMessage);
        } else {
            if (!this.isPresale(orderRelation)) {
                this.ipStandplatOrderService.updateStandPlatOrderTransStatus(orderNo, TransferOrderStatus.TRANSFERRED, "预售付定金暂不转单",null);
                return new ProcessStepResult<>(StepStatus.FINISHED, "预售付定金暂不转单");
            }
            String operateMessage = Resources.getMessage("单据" + orderInfo.getOrderId() + "检查状态成功， 进入下一阶段");
            return new ProcessStepResult<>(StepStatus.SUCCESS, operateMessage);
        }
    }

    private boolean isEmptySkuNumber(IpStandplatOrderRelation ipStandplatOrderRelation) {
        AtomicBoolean isEmptySku = new AtomicBoolean(true);
        ipStandplatOrderRelation.getStandPlatOrderItemList().stream().forEach(
                i -> {
                    if (StringUtils.isEmpty(i.getOuterSkuId())) {
                        isEmptySku.set(false);
                    }
                });
        return isEmptySku.get();
    }

    private boolean isPresale(IpStandplatOrderRelation orderRelation) {
        IpBStandplatOrder standplatOrder = orderRelation.getStandplatOrder();
        Long reserveBigint05 = standplatOrder.getReserveBigint05();
        if (reserveBigint05 != null) {
            boolean b = reserveBigint05 != 2L;
            if (b) {
                String presaleTransfer = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get("business_system:presale_to_oms_order");
                if (StringUtils.isNotEmpty(presaleTransfer)) {
                    b = "是".equals(presaleTransfer.trim());
                }
                return b;
            }
        }
        return true;
    }
}
