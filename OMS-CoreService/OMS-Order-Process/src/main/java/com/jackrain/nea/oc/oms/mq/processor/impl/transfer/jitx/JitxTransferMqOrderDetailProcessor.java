package com.jackrain.nea.oc.oms.mq.processor.impl.transfer.jitx;

import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.ErrorLogType;
import com.jackrain.nea.oc.oms.model.enums.OperateType;
import com.jackrain.nea.oc.oms.model.enums.OrderType;
import com.jackrain.nea.oc.oms.model.jitx.JitxOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpJitxOrderRelation;
import com.jackrain.nea.oc.oms.mq.processor.IMqOrderDetailProcessor;
import com.jackrain.nea.oc.oms.process.transfer.impl.normal.jitx.JitxTransferOrderProcessImpl;
import com.jackrain.nea.oc.oms.process.transfer.impl.refund.jitx.JitxTransferRefundProcessImpl;
import com.jackrain.nea.oc.oms.services.IpJitxOrderService;
import com.jackrain.nea.oc.oms.step.ProcessStepResultList;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @author: 黄超
 * @since: 2019-06-25
 * create at : 2019-06-25 18:00
 */
@Slf4j
public class JitxTransferMqOrderDetailProcessor implements IMqOrderDetailProcessor {

    @Autowired
    private IpJitxOrderService ipJitxOrderService;

    @Autowired
    private JitxTransferOrderProcessImpl jitxTransferOrderProcess;

    @Autowired
    private JitxTransferRefundProcessImpl jitxTransferRefundProcess;

    @Override
    public ProcessStepResultList start(OperateOrderMqInfo orderMqInfo) {
        String orderNo = orderMqInfo.getOrderNo();

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Start MqOrder Transfer", orderNo));
        }

        IpJitxOrderRelation jitxOrderRelation = this.ipJitxOrderService.selectJitxOrder(orderNo);
        if (jitxOrderRelation == null || jitxOrderRelation.getJitxOrder() == null) {
            String errorMessage = Resources.getMessage("Received OrderMqInfo Not Exist!OrderNo=" + orderNo);
            log.error(LogUtil.format(errorMessage));
            return new ProcessStepResultList();
        } else {
            //区分退单和转单
            ProcessStepResultList resultList = new ProcessStepResultList();
            String orderStatus = jitxOrderRelation.getJitxOrder().getOrderStatus();
            if (JitxOrderStatus.ORDER_UNSEND_REFUND.equals(orderStatus)
                    || JitxOrderStatus.ORDER_SEND_REFUND.equals(orderStatus)
                    || JitxOrderStatus.ORDER_COLLECTED_REFUND.equals(orderStatus)) {
                resultList = jitxTransferRefundProcess.start(jitxOrderRelation,
                        false, SystemUserResource.getRootUser());
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("Finished.MqOrder.Refund,Result= {}", orderNo), resultList);
                }
            } else {
                resultList = jitxTransferOrderProcess.start(jitxOrderRelation,
                        false, SystemUserResource.getRootUser());
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("Finished.MqOrder.Transfer,Result= {}", orderNo), resultList);
                }
            }
            return resultList;
        }
    }

    @Override
    public boolean checkMqIsCanExecute(OperateOrderMqInfo orderMqInfo) {
        return orderMqInfo.getOperateType() == OperateType.TRANSFER_ORDER
                && orderMqInfo.getChannelType() == ChannelType.VIPJITX
                && orderMqInfo.getOrderType() == OrderType.NORMAL;
    }
}
