package com.jackrain.nea.oc.oms.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 直发预占状态
 *
 * <AUTHOR>
 * @since 2024-11-29 14:24
 */
@Getter
@AllArgsConstructor
public enum OcBDirectReportOrderStatusEnum {
    /**
     * 创建
     * 已审核
     * 部分履约
     * 全部履约
     */
    UN_AUDITED(1, "未审核"),
    AUDITED(2, "已审核"),
    PARTIALLY_FULFILLED(3, "部分履约"),
    FULFILLED(4, "全部履约"),
    ;

    private final Integer value;
    private final String desc;

}
