package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.api.OcBOrderUpdatePreDeliveryTimeCmd;
import com.jackrain.nea.oc.oms.services.GenerateCyclePurchaseSubOrderService;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolderV14Utils;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> wangbinyu
 * @since : 2022/9/6
 * description :
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBOrderUpdatePreDeliveryTimeCmdImpl implements OcBOrderUpdatePreDeliveryTimeCmd {
    @Autowired
    private GenerateCyclePurchaseSubOrderService generateCyclePurchaseSubOrderService;


    @Override
    public ValueHolderV14<String> updatePreDeliveryTime(JSONObject obj, User user) {

        return generateCyclePurchaseSubOrderService.updatePreDeliveryTime(obj, user);
    }
}
