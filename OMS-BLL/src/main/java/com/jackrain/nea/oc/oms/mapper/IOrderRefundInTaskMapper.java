package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.task.OcBRefundInTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @author: 秦雄飞
 * @time: 2021/12/31 10:33 上午
 * @description:
 */

@Mapper
public interface IOrderRefundInTaskMapper extends ExtentionMapper<OcBRefundInTask> {

    @Select("SELECT * FROM OC_B_REFUND_IN_TASK WHERE WMS_BILL_NO = #{wmsBillNo} AND ISACTIVE = 'Y'")
    List<OcBRefundInTask> selectRefundInTaskByWmsBillNo(@Param("wmsBillNo") String wmsBillNo);

    @Select("SELECT * FROM OC_B_REFUND_IN_TASK WHERE FAILED_COUNT < #{count} AND BILL_STATUS IN(${statusStr}) " +
            " AND ISACTIVE ='Y' ORDER BY CREATIONDATE ASC LIMIT 0,#{pageSize} ")
    List<OcBRefundInTask> selectRefundInTaskBySize(@Param("pageSize") Integer pageSize, @Param("count") Integer count,
                                                   @Param("statusStr") String statusStr);

    @Select("SELECT * FROM OC_B_REFUND_IN_TASK WHERE ID IN (${idsStr}) AND ISACTIVE = 'Y'")
    List<OcBRefundInTask> selectRefundInTaskListByIds(@Param("idsStr") String idsStr);

    @Select("SELECT * FROM OC_B_REFUND_IN_TASK WHERE ID = #{id} ")
    OcBRefundInTask selectRefundInTaskById(@Param("id") Long id);
}
