package com.jackrain.nea.oc.oms.process.tobeconfirm.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.resources.OrderOccupyStatus;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.services.OmsOrderImperfectStrategyService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.oc.oms.util.SplitMessageUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;

/**
 * 残次策略
 *
 * <AUTHOR>
 */
@Step(order = 75, description = "残次策略")
@Slf4j
public class Step075ImperfectStrategyService extends BaseTobeConfirmedProcessStep implements IOmsOrderProcessStep<OcBOrderRelation> {

    @Resource
    private OmsOrderImperfectStrategyService imperfectStrategyService;

    @Override
    public ProcessStepResult<OcBOrderRelation> startProcess(OcBOrderRelation orderInfo, ProcessStepResult preStepResult,
                                                            boolean isAutoMakeup, User operateUser) {
        OcBOrder ocBOrder = orderInfo.getOrderInfo();
        log.info(LogUtil.format("TobeConfirmed Step075ImperfectStrategyService 残次策略执行 orderId:{}", "残次策略执行"), ocBOrder.getId());

        try {
            //残次策略匹配
            if (StringUtils.isNotBlank(ocBOrder.getSaleProductAttr())) {
                //存在销售商品属性，不匹配
                log.info(LogUtil.format("TobeConfirmed Step075ImperfectStrategyService 残次策略不匹配 orderId:{}", "残次策略执行"), ocBOrder.getId());
                return new ProcessStepResult<>(StepStatus.SUCCESS, null);
            }

            boolean b = imperfectStrategyService.matchImperfectStrategy(orderInfo, operateUser);
            if (b) {
                log.info(LogUtil.format("TobeConfirmed Step075ImperfectStrategyService 残次策略匹配拆单完成 orderId:{}", "残次策略执行"), ocBOrder.getId());
                return new ProcessStepResult<>(StepStatus.FINISHED, "残次策略匹配拆单完成");
            }

            return new ProcessStepResult<>(StepStatus.SUCCESS, null);
        } catch (Exception e) {
            log.error(LogUtil.format("TobeConfirmed Step075ImperfectStrategyService 残次策略执行异常 orderId:{}", "残次策略执行"), ocBOrder.getId(), e);
            OcBOrder ocBOrderDto = new OcBOrder();
            String operateMessage = "订单OrderId" + orderInfo.getOrderId() + "的订单执行残次策略执行异常,异常信息-->" + e.getMessage();
            ocBOrderDto.setId(orderInfo.getOrderId());
            ocBOrderDto.setSysremark(SplitMessageUtil.splitMesssage(operateMessage));
            ocBOrderDto.setOccupyStatus(OrderOccupyStatus.STATUS_75);
            omsOrderService.updateOrderInfo(ocBOrderDto);
            omsToBeConfirmedTaskService.updateToBeConfirmedTaskByOrderId(orderInfo.getOrderId());
            return new ProcessStepResult<>(StepStatus.FAILED, operateMessage);
        }
    }
}
