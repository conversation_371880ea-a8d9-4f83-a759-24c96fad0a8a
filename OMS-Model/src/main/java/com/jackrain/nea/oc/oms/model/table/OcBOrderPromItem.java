package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

@TableName(value = "oc_b_order_prom_item")
@Data
@Document(index = "oc_b_order_prom_item", type = "oc_b_order_prom_item")
public class OcBOrderPromItem extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "IS_EXCUTE")
    @Field(type = FieldType.Keyword)
    private String isExcute;

    @JSONField(name = "IS_HOLD")
    @Field(type = FieldType.Keyword)
    private String isHold;

    @JSONField(name = "OC_B_ORDER_ID")
    @Field(type = FieldType.Long)
    private Long ocBOrderId;

    @JSONField(name = "prom_id")
    @Field(type = FieldType.Long)
    private Long promId;

    @JSONField(name = "PROM_NAME")
    @Field(type = FieldType.Keyword)
    private String promName;

    @JSONField(name = "GROUP_CODE")
    @Field(type = FieldType.Keyword)
    private String groupCode;

    @JSONField(name = "PRO_LIST")
    @Field(type = FieldType.Keyword)
    private String proList;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;
}