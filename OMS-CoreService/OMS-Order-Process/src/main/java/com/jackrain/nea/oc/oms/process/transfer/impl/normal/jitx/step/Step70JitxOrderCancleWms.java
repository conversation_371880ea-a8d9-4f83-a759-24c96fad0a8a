package com.jackrain.nea.oc.oms.process.transfer.impl.normal.jitx.step;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.LogTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderHoldReasonEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpJitxOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.services.OcBOrderTheAuditService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.OrderAddressConvertUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 判断地址信息是否一致,不一致要反审核撤回
 *
 * @author: 黄超
 * @since: 2019-06-26
 * create at : 2019-06-26 9:00
 */
@Step(order = 70, description = "判断地址信息是否一致,不一致要进行反审核撤回")
@Component
@Slf4j
public class Step70JitxOrderCancleWms extends BaseJitxOrderProcessStep
        implements IOmsOrderProcessStep<IpJitxOrderRelation> {

    @Autowired
    private OcBOrderTheAuditService ocBOrderTheAuditService;

    @Override
    public ProcessStepResult<IpJitxOrderRelation> startProcess(IpJitxOrderRelation orderInfo,
                                                               ProcessStepResult preStepResult,
                                                               boolean isAutoMakeup, User operateUser) {
        ProcessStepResult<IpJitxOrderRelation> stepResult = new ProcessStepResult<>();

        if (orderInfo == null || orderInfo.getJitxOrder() == null) {
            return new ProcessStepResult<>(StepStatus.FAILED, "orderInfo is null or orderInfo.JitxOrder is null");
        }
        boolean isJitxUpdateAddress = false;
        String errorMessage = "";
        // 未转换
        TransferOrderStatus transferOrderStatus = TransferOrderStatus.NOT_TRANSFER;
        if (preStepResult.getNextStepOperateObj() != null) {
            try {
                List<OcBOrder> transferOrderList = (List<OcBOrder>) preStepResult.getNextStepOperateObj();
                for (OcBOrder transferOrder : transferOrderList) {
                    if (transferOrder == null) {
                        continue;
                    }
                    OcBOrder order = new OcBOrder();
                    order.setReceiverAddress(orderInfo.getJitxOrder().getBuyerAddress());
                    String newAddress = OrderAddressConvertUtil.convert(order);
                    if (log.isDebugEnabled()) {
                        log.debug(LogUtil.format("Step70JitxOrderCancleWms[判断地址信息是否一致,不一致要从wms撤回]{}","Step70JitxOrderCancleWms",orderInfo.getJitxOrder().getOrderSn()),orderInfo.getJitxOrder().getOrderSn());
                    }
                    if (!StringUtils.equalsIgnoreCase(newAddress, transferOrder.getReceiverAddress())) {
                        if (log.isDebugEnabled()) {
                            log.debug(LogUtil.format("Step70JitxOrderCancleWms,原地址和老地址不一致{}","Step70JitxOrderCancleWms",orderInfo.getJitxOrder().getOrderSn()),orderInfo.getJitxOrder().getOrderSn());
                        }
                        // 订单状态为待分配或传WMS中时，JITX订单不允许转换
                        if (OmsOrderStatus.TO_BE_ASSIGNED.toInteger().equals(transferOrder.getOrderStatus())
                                || OmsOrderStatus.PENDING_WMS.toInteger().equals(transferOrder.getOrderStatus())) {
                            if (log.isDebugEnabled()) {
                                log.debug(LogUtil.format("零售发货单状态为：待分配，不允许修改地址！, jitxOrder:{}","Step70JitxOrderCancleWms",orderInfo.getJitxOrder().getOrderSn()),orderInfo.getJitxOrder());
                            }
                            errorMessage = "零售发货单状态为：待分配或传WMS中，不允许修改地址！";
                        }
                        // 订单状态为"待审核"，"缺货"，"已审核"，"配货中并且WMS撤回状态已撤回"时，执行hold策略
                        else if (OmsOrderStatus.UNCONFIRMED.toInteger().equals(transferOrder.getOrderStatus())
                                || OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(transferOrder.getOrderStatus())) {
                            ValueHolder vh = execOrderHold(transferOrder);
                            if (log.isDebugEnabled()) {
                                log.debug(LogUtil.format("Step70JitxOrderCancleWms.holdAndTheAudit正常状态 {}","Step70JitxOrderCancleWms",orderInfo.getJitxOrder().getOrderSn()),JSONObject.toJSONString(vh));
                            }
                            if (vh != null && vh.isOK()) {
                                isJitxUpdateAddress = true;
                            } else {
                                transferOrderStatus = TransferOrderStatus.TRANSFEREXCEPTION;
                                errorMessage = String.valueOf(vh.get("message"));
                            }
                        }// 订单状态为"配货中并且WMS撤回状态未撤回"，执行wms撤回成功后，在执行hold策略
                        else if (OmsOrderStatus.IN_DISTRIBUTION.toInteger().equals(transferOrder.getOrderStatus()) || OmsOrderStatus.CHECKED.toInteger().equals(transferOrder.getOrderStatus())) {
                            ValueHolder vh = execOrderHold(transferOrder);
                            if (log.isDebugEnabled()) {
                                log.debug(LogUtil.format("Step70JitxOrderCancleWms.execOrderHold: {}","Step70JitxOrderCancleWms",orderInfo.getJitxOrder().getOrderSn()),JSONObject.toJSONString(vh));
                            }
                            if (vh == null) {
                                orderInfo.setRemarks("JITX修改地址hold单服务返回结果为空！");
                            } else {
                                if (vh.isOK()) {
                                    try {
                                        ValueHolderV14 v14 = new ValueHolderV14();
                                        //订单反审核;
                                        boolean backAuditSuccess = ocBOrderTheAuditService.updateOrderInfo(SystemUserResource.getRootUser(),
                                                v14, transferOrder.getId(),false, LogTypeEnum.AUTOMATIC_UPDATE_ADDRESS_REVERSE_AUDIT.getType(),true);
                                        if (backAuditSuccess) {
                                            isJitxUpdateAddress = true;
                                        } else {
                                            orderInfo.setRemarks("WMS撤回失败，地址修改失败");
                                            //取消Hold单
                                            ValueHolder holdVh = ocBOrderHoldService.businessUnHold(transferOrder.getId(), OrderHoldReasonEnum.JITX_HOLD);
                                            if (log.isDebugEnabled()) {
                                                log.debug(LogUtil.format("JITX订单修改地址，WMS撤回失败，取消hold单返回结果：{}","取消hold单返回结果",orderInfo.getJitxOrder().getOrderSn()),JSON.toJSONString(holdVh));
                                            }
                                        }
                                    } catch (Exception e) {
                                        log.error("JITX修改地址，WMS撤回异常", Throwables.getStackTraceAsString(e));
                                        orderInfo.setRemarks("WMS撤回发生异常，地址修改失败");
                                        //取消Hold单
                                        ValueHolder holdVh = ocBOrderHoldService.businessUnHold(transferOrder.getId(), OrderHoldReasonEnum.JITX_HOLD);
                                        if (log.isDebugEnabled()) {
                                            log.debug(LogUtil.format("JITX订单修改地址，WMS撤回异常，取消hold单返回结果 {}","WMS撤回异常取消hold单返回结果",orderInfo.getJitxOrder().getOrderSn()),JSONObject.toJSONString(holdVh));
                                        }
                                    }
                                } else {
                                    orderInfo.setRemarks("JITX修改地址hold失败：" + vh.get("message"));
                                }
                            }
                        } else {
                            log.info("Step70JitxOrderCancleWms JITX订单修改地址,零售发货单:{},状态:{}",
                                    transferOrder.getSourceCode(), transferOrder.getOrderStatus());
                            String remark = String.format("转换完成，退出装换，零售发货单的状态:%d", transferOrder.getOrderStatus());
                            orderInfo.setRemarks(remark);
                        }
                    }
                }
            } catch (Exception ex) {
                log.error(LogUtil.format("Step70JitxOrderCancleWms PreStep=:{}", "Step70JitxOrderCancleWms"), Throwables.getStackTraceAsString(ex));
            }
        }

        if (StringUtils.isNotBlank(errorMessage)) {
            boolean updateStatusRes = ipJitxOrderService.updateJitxOrderTransStatus(orderInfo.getOrderNo(),
                    transferOrderStatus, errorMessage);
            if (!updateStatusRes) {
                errorMessage += ";更新状态失败=False";
            }
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        } else {
            stepResult.setStatus(StepStatus.SUCCESS);
            if (isJitxUpdateAddress) {
                stepResult.setMessage("进入更新订单地址阶段");
                stepResult.setNextStepOperateObj(preStepResult.getNextStepOperateObj());
                stepResult.setNextStepClass(Step80JitxUpdateBuyerAddress.class);
            } else {
                stepResult.setMessage("未进入更新订单地址信息，进入禁发处理");
                stepResult.setNextStepClass(Step85JitxOrderFrobiddenDelivery.class);
            }
        }
        return stepResult;
    }

    /**
     * JITX订单hold策略执行，同时更新修改地址状态为更新为改地址中
     *
     * @param transferOrder
     * @return
     */
    private ValueHolder execOrderHold(OcBOrder transferOrder) {
        //更新为改地址中
        ipJitxOrderService.updateChangeAddrStatus(transferOrder.getSourceCode(), 1);
        //调用Hold单策略
        return ocBOrderHoldService.businessHold(transferOrder.getId(), OrderHoldReasonEnum.JITX_HOLD);

    }
}
