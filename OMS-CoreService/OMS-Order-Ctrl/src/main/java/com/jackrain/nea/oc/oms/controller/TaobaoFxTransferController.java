package com.jackrain.nea.oc.oms.controller;

import com.jackrain.nea.oc.oms.api.ExchangeOrderCmd;
import com.jackrain.nea.oc.oms.api.ReturnOrderCmd;
import com.jackrain.nea.oc.oms.api.TaobaoFxTransferOrderCmd;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.request.TransferOrderRequest;
import com.jackrain.nea.oc.oms.model.result.TransferOrderResult;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * 淘宝转换单据Controller
 *
 * @author: 易邵峰
 * @since: 2019-04-02
 * create at : 2019-04-02 15:18
 */
@RestController
@RequestMapping("/p")
public class TaobaoFxTransferController {

    @Reference(group = "oc-core", version = "1.0")
    private TaobaoFxTransferOrderCmd taobaoFxTransferOrderCmd;

    @Reference(group = "oc-core", version = "1.0")
    private ReturnOrderCmd returnOrderCmd;

    @Reference(group = "oc-core", version = "1.0")
    private ExchangeOrderCmd exchangeOrderCmd;

    /**
     * 转换单据内容
     *
     * @param orderNo 转换单据号
     * @return 转换单据结果
     */
    @RequestMapping("/taobaoFxTransfer")
    public ValueHolderV14<TransferOrderResult> startTaobaoTransfer(@Param("orderNo") String orderNo) {
        TransferOrderRequest orderRequest = new TransferOrderRequest();
        orderRequest.setChannelType(ChannelType.TAOBAO);
        orderRequest.setOperateUser(SystemUserResource.getRootUser());
        List<String> orderNoList = new ArrayList<>();
        orderNoList.add(orderNo);
        orderRequest.setOrderNoList(orderNoList);
        return taobaoFxTransferOrderCmd.startFxTransferOrder(orderRequest);
    }
}
