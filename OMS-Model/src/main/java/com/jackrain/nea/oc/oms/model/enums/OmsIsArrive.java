package com.jackrain.nea.oc.oms.model.enums;

/**
 * 是否送达
 *
 * @author: 胡林洋
 * @since: 2019-03-17
 * create at : 2019-03-17 16:45
 */
public enum OmsIsArrive {
    /**
     * 送达
     */
    TRUE,
    /**
     * 不送达
     */
    FALSE;

    public String paseValue() {
        if (this == TRUE) {
            return "Y";
        } else if (this == FALSE) {
            return "N";
        } else {
            return " ";
        }
    }

}
