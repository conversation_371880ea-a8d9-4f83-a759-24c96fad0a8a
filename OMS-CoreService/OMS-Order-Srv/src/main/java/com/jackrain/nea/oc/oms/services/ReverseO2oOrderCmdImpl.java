package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.ReverseO2oOrderCmd;
import com.jackrain.nea.oc.oms.model.request.ReverseO2oOrderRequest;
import com.jackrain.nea.oc.oms.model.request.ReverseO2oReturnOrderRequest;
import com.jackrain.nea.ps.api.request.SkuInfoListRequest;
import com.jackrain.nea.ps.api.result.ProAndSkuResult;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.util.ValueHolderV14Utils;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName ReverseO2oOrderCmdImpl
 * @description:
 * @author: Lay.Jiang
 * @create: 2021-06-08 14:26
 **/
@Component
@Slf4j
@Service(protocol = "dubbo", validation = "true", group = "oc-core", version = "1.0")
public class ReverseO2oOrderCmdImpl implements ReverseO2oOrderCmd {

    @Autowired
    private ReverseO2oOrderService reverseO2oOrderService;

    @Autowired
    private PsRpcService psRpcService;

    @Override
    public ValueHolderV14 orderAdd(ReverseO2oOrderRequest request) {
        try {
            transferOrderParam(request);
            reverseO2oOrderService.saveOcBOrderRelation(request);
            return ValueHolderV14Utils.getSuccessValueHolder("保存成功");
        } catch (Exception e) {
            log.error("订单{},反向O2O订单保存异常", request.getO2oSourceCode(), e);
            return ValueHolderV14Utils.getFailValueHolder(e.getMessage());
        }
    }

    @Override
    public ValueHolderV14 cancelOrder(ReverseO2oOrderRequest request) {
        try {
            reverseO2oOrderService.cancelOrder(request);
            return ValueHolderV14Utils.getSuccessValueHolder("取消成功");
        } catch (Exception e) {
            log.error("订单{},反向O2O订单取消异常", request.getO2oSourceCode(), e);
            return ValueHolderV14Utils.getFailValueHolder(e.getMessage());
        }
    }

    @Override
    public ValueHolderV14 orderFinish(ReverseO2oOrderRequest request) {
        try {
            reverseO2oOrderService.finishOrder(request);
            return ValueHolderV14Utils.getSuccessValueHolder("成功");
        } catch (Exception e) {
            log.error("订单{},反向O2O订单完成异常", request.getO2oSourceCode(), e);
            return ValueHolderV14Utils.getFailValueHolder(e.getMessage());
        }
    }

    @Override
    public ValueHolderV14 createReturnOrder(ReverseO2oReturnOrderRequest request) {
        try {
            transferReturnParam(request);
            User user = SystemUserResource.getRootUser();
            reverseO2oOrderService.createReturnOrder(request, user);
            return ValueHolderV14Utils.getSuccessValueHolder("成功");
        } catch (Exception e) {
            log.error("订单{},反向O2O退单创建异常", request.getO2oSourceCode(), e);
            return ValueHolderV14Utils.getFailValueHolder(e.getMessage());
        }
    }

    @Override
    public ValueHolderV14 cancelReturnOrder(ReverseO2oReturnOrderRequest request) {
        try {
            User user = SystemUserResource.getRootUser();
            reverseO2oOrderService.cancelReturnOrder(request, user);
            return ValueHolderV14Utils.getSuccessValueHolder("成功");
        } catch (Exception e) {
            log.error("订单{},反向O2O取消退单异常", request.getO2oSourceCode(), e);
            return ValueHolderV14Utils.getFailValueHolder(e.getMessage());
        }
    }

    @Override
    public ValueHolderV14 updateReturnOrder(ReverseO2oReturnOrderRequest request) {
        try {
            User user = SystemUserResource.getRootUser();
            reverseO2oOrderService.updateReturnOrder(request, user);
            return ValueHolderV14Utils.getSuccessValueHolder("成功");
        } catch (Exception e) {
            log.error("订单{},反向O2O更新退单物流信息异常", request.getO2oSourceCode(), e);
            return ValueHolderV14Utils.getFailValueHolder(e.getMessage());
        }
    }

    private void transferReturnParam(ReverseO2oReturnOrderRequest request) {
        if (CollectionUtils.isEmpty(request.getItemList())) {
            throw new NDSException("订单sku不能为空");
        }
        for (ReverseO2oReturnOrderRequest.ReturnOrderItem item : request.getItemList()) {
            List<SkuInfoListRequest> proList = new ArrayList<>();
            SkuInfoListRequest param = new SkuInfoListRequest();
            param.setProEcode(item.getProductCode());
            param.setColorCode(item.getColorCode());
            param.setSizeCode(item.getSizeCode());
            proList.add(param);
            ValueHolder valueHolder = psRpcService.querySkuInfoByProEcodeAndClrsize(proList);
            if (!valueHolder.isOK() || CollectionUtils.isEmpty((List<ProAndSkuResult>) valueHolder.get("data"))) {
                throw new NDSException(item.getSku() + "商品不存在!");
            }
            List<ProAndSkuResult> data = (List<ProAndSkuResult>) valueHolder.get("data");
            item.setSku(data.get(0).getSkuEcode());
        }
    }

    private void transferOrderParam(ReverseO2oOrderRequest request) {
        if (CollectionUtils.isEmpty(request.getItems())) {
            throw new NDSException("订单sku不能为空");
        }
        for (ReverseO2oOrderRequest.ReverseO2oOrderItem item : request.getItems()) {
            List<SkuInfoListRequest> proList = new ArrayList<>();
            SkuInfoListRequest param = new SkuInfoListRequest();
            param.setProEcode(item.getProductCode());
            param.setColorCode(item.getColorCode());
            param.setSizeCode(item.getSizeCode());
            proList.add(param);
            ValueHolder valueHolder = psRpcService.querySkuInfoByProEcodeAndClrsize(proList);
            if (!valueHolder.isOK() || CollectionUtils.isEmpty((List<ProAndSkuResult>) valueHolder.get("data"))) {
                throw new NDSException(item.getOuterSkuId() + "商品不存在!");
            }
            List<ProAndSkuResult> data = (List<ProAndSkuResult>) valueHolder.get("data");
            item.setOuterSkuId(data.get(0).getSkuEcode());
        }
    }
}