package com.jackrain.nea.oc.oms.model.enums.ac;

import lombok.Getter;

/**
 * @author: 陈俊明
 * @since: 2019-12-19
 * @create at : 2019-12-19 下午 11:40
 */
@Getter
public enum PayBillStatusEnum {
    /**
     * 未审核
     */
    STATUS_UNAUDIT(1,"未审核"),
    /**
     * 已客审
     */
    STATUS_AUDIT(2,"已客审"),

    STATUS_BIZAUDIT(3,"已业审"),
    /**
     * 已财审
     */
    STATUS_FIAUDIT(4,"已财审"),
    /**
     * 已作废
     */
    STATUS_VOID(5,"已作废");

    int val;
    String text;

    PayBillStatusEnum(int val,String text) {
        this.text = text;
        this.val = val;
    }

    public String getText() {
        return text;
    }

    public int getVal() {
        return val;
    }
}
