package com.jackrain.nea.oc.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBOrderCancelCmd;
import com.jackrain.nea.oc.oms.services.OcBOrderCancelService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * date ：Created in 10:38 2019/12/5
 * description ：
 * @ Modified By：
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBOrderCancelCmdImpl extends CommandAdapter implements OcBOrderCancelCmd {

    @Autowired
    private OcBOrderCancelService ocBOrderCancelService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return ocBOrderCancelService.cancelOrder(session);
    }
}
