package com.jackrain.nea.oc.oms.services;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.util.TypeUtils;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.WarehouseOperationCmd;
import com.jackrain.nea.oc.oms.es.ES4IpWhInnerOperate;
import com.jackrain.nea.oc.oms.model.table.IpBWhInnerOperate;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;

/**
 * @Author: 黄世新
 * @Date: 2020/3/23 5:07 下午
 * @Version 1.0
 */
@Service(protocol = "dubbo", validation = "true", group = "oc-core", version = "1.0")
@Slf4j
@Component
public class WarehouseOperationCmdImpl extends CommandAdapter implements WarehouseOperationCmd {

//    @Autowired
//    private OmsWarehouseOperationService omsWarehouseOperationService;
//    @Autowired
//    private IpBWhInnerOperateMapper ipBWhInnerOperateMapper;

//
//    public ValueHolderV14 warehouseOperationTransfer(WarehouseOperationRequest request) {
//
//        ValueHolderV14 resultValueHolderV14 = new ValueHolderV14<>();
//        try {
//            boolean hasError = false;
//            int failedNumber = 0;
//            int successNumber = 0;
//            User operateUser = request.getOperateUser();
//            List<String> resultList = new ArrayList<>();
//            for (String expressNo : request.getExpressNo()) {
//                try {
//                    String result = omsWarehouseOperationService.warehouseOperationTransfer(expressNo, operateUser);
//                    successNumber++;
//                    resultList.add(result);
//                } catch (Exception e) {
//                    log.error(this.getClass().getName()+" 物流单号为:"+expressNo+"转换异常", e);
//                    hasError = true;
//                    failedNumber++;
//                    resultList.add(e.getMessage());
//                }
//
//            }
//            if (hasError) {
//                StringBuilder sbMessage = new StringBuilder();
//                resultValueHolderV14.setCode(ResultCode.FAIL);
//                sbMessage.append(String.format("转换成功%s条；转换失败%s条；失败原因：\r\n", successNumber, failedNumber));
//                for (String result : resultList) {
//                    if (result != null) {
//                        sbMessage.append(result);
//                        sbMessage.append("\r\n");
//                    }
//                }
//                resultValueHolderV14.setMessage(sbMessage.toString());
//            } else {
//                resultValueHolderV14.setCode(ResultCode.SUCCESS);
//                resultValueHolderV14.setMessage("转换全部成功。");
//            }
//        } catch (Exception ex) {
//            ex.printStackTrace();
//            resultValueHolderV14.setCode(ResultCode.FAIL);
//            resultValueHolderV14.setMessage("转换异常：" + ex.getMessage());
//            log.error(this.getClass().getName()+" 仓内作业转换异常", ex);
//        }
//
//        return resultValueHolderV14;
//    }

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        log.info("======标记为已转换服务=========");
        ValueHolder vh = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (null == param) {
            throw new NDSException(Resources.getMessage("参数格式错误！", querySession.getLocale()));
        }
        log.info("param======>" + param.toString());

        boolean isIDS = param.containsKey("ids");
        boolean isObjId = param.containsKey("objid");
        if (!(isIDS || isObjId)) {
            throw new NDSException(Resources.getMessage("参数格式错误！", querySession.getLocale()));
        }
        if (isIDS && isObjId) {
            throw new NDSException(Resources.getMessage("参数格式错误！", querySession.getLocale()));
        }
        if (isIDS && (param.getJSONArray("ids").size() <= 0)) {
            throw new NDSException(Resources.getMessage("请选择需要标记的单据记录！", querySession.getLocale()));
        }
        String tableName = param.getString("table");

        if (StringUtils.isEmpty(tableName)) {
            throw new NDSException(Resources.getMessage("表名为空", querySession.getLocale()));
        }
        tableName = tableName.toLowerCase();
        if (isIDS) {
            //列表批量处理
            vh = batchMarked(param, querySession);
        } else {
            //单对象界面处理
            Long objid = param.getLongValue("objid");
            JSONObject jsonObject;
            try {
                jsonObject = separateMarked(objid, querySession);
                vh.put("code", TypeUtils.castToInt(jsonObject.get("code")));
                vh.put("message", Resources.getMessage(jsonObject.getString("message"), querySession.getLocale()));
            } catch (Exception e) {
                e.printStackTrace();
                vh.put("code", -1);
                vh.put("message", Resources.getMessage(e.getMessage(), querySession.getLocale()));
            }
        }
        return vh;
    }

    private ValueHolder batchMarked(JSONObject param, QuerySession querySession) {
        ValueHolder valueHolder = new ValueHolder();
        int success = 0;
        int fail = 0;
        Object[] ids = param.getJSONArray("ids").toArray();
        JSONArray listArray = new JSONArray();
        for (Object id : ids) {
            Long objid = TypeUtils.castToLong(id);
            JSONObject retJson;
            try {
                retJson = separateMarked(objid, querySession);
            } catch (Exception e) {
                e.printStackTrace();
                retJson = new JSONObject();
                retJson.put("message", Resources.getMessage(e.getMessage(), querySession.getLocale()));
                retJson.put("code", -1);
                retJson.put("objid", objid);
                listArray.add(retJson);
                fail++;
                continue;
            }
            if (retJson == null) {
                fail++;
                continue;
            }
            if (retJson.containsKey("code") && retJson.getInteger("code") == 0) {
                success++;
            } else {
                fail++;
                listArray.add(retJson);
            }
        }

        valueHolder.put("data", listArray);
        if (0 == fail) {
            valueHolder.put("code", 0);
            valueHolder.put("message", Resources.getMessage("标记成功的记录数：" + success + ",失败的记录数：" + fail, querySession.getLocale()));
        } else {
            valueHolder.put("code", -1);
            valueHolder.put("message", Resources.getMessage("标记成功的记录数：" + success + ",失败的记录数：" + fail, querySession.getLocale()));
        }
        return valueHolder;
    }


    private JSONObject separateMarked(Long objid, QuerySession querySession) throws Exception {
        JSONObject jsonObject = new JSONObject();
        User user = querySession.getUser();
        // 单个处理 根据id查询物流单号
        String expressCode = ES4IpWhInnerOperate.findExpressCodeById(objid);
        if (StringUtils.isEmpty(expressCode)) {
            jsonObject.put("code", -1);
            jsonObject.put("message", "未查询到数据");
            jsonObject.put("objid", objid);
            return jsonObject;
        }
        //根据物流单号查询发货信息表
        //IpBWhInnerOperate innerOperate = ipBWhInnerOperateMapper.selectIpBWhInnerOperateByCode(expressCode);
        IpBWhInnerOperate innerOperate = null;
        if (innerOperate == null) {
            jsonObject.put("code", -1);
            jsonObject.put("message", "未查询到有效的数据");
            jsonObject.put("objid", objid);
            return jsonObject;
        }
        if (innerOperate.getTransStatus() == 2) {
            jsonObject.put("code", -1);
            jsonObject.put("message", "单据状态为已转换,不允许准换!");
            jsonObject.put("objid", objid);
            return jsonObject;
        }
        try {
            //String result = omsWarehouseOperationService.warehouseOperationTransfer(expressCode, user);
            jsonObject.put("code", 0);
            //jsonObject.put("message", result);
            jsonObject.put("objid", objid);
        } catch (Exception e) {
            jsonObject.put("code", -1);
            jsonObject.put("message", e.getMessage());
            jsonObject.put("objid", objid);
        }
        return jsonObject;
    }
}
