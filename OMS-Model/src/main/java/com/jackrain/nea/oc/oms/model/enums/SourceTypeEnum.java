package com.jackrain.nea.oc.oms.model.enums;

/**
 * <AUTHOR>
 * @create 2020-11-19 14:33
 * @desc 单据来源
 **/
public enum SourceTypeEnum {
    /**
     * 手工创建
     */
    MANUALLY_CREATED("1", "手工创建"),
    /**
     * 自动下载
     */
    AUTO_DOWNLOAD("2", "自动下载");

    /**
     * code
     */
    private String code;
    /**
     * 名称
     */
    private String name;

    SourceTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code) {
        for (SourceTypeEnum select : SourceTypeEnum.values()) {
            if (select.code.equals(code)) {
                return select.name;
            }
        }
        return "";
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
