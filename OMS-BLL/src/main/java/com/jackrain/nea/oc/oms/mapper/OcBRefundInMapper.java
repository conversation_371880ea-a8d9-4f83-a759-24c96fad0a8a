package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.OcBRefundIn;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.apache.ibatis.annotations.UpdateProvider;

import java.util.List;

@Mapper
public interface OcBRefundInMapper extends ExtentionMapper<OcBRefundIn> {

    /**
     * 根据退换货订单的id去 退货商品表查询数据
     *
     * @param id 退换货订单的id
     * @return 返回的数据
     */
    @Select("SELECT * FROM OC_B_RETURN_ORDER_REFUND WHERE OC_B_RETURN_ORDER_ID = #{id}")
    List<OcBReturnOrderRefund> selectByOcBreturnOrderId(@Param("id") Object id);

    /**
     * 1. 0
     * 查询入库单信息
     *
     * @param ocBrefundInId 入库单表id
     * @param isActive      是否启用
     * @return OcBRefundIn 入库单
     */
    @Select("SELECT * FROM OC_B_REFUND_IN WHERE `ID`=#{ocBrefundInId} AND ISACTIVE=#{isActive}")
    OcBRefundIn getOcBrefundInById(@Param("ocBrefundInId") Long ocBrefundInId, @Param("isActive") String isActive);

    /**
     * 退货入库. 更新入库单
     *
     * @param matchStatus 匹配状态
     * @param id          入库单id
     * @return 结果
     */
    @Update("UPDATE OC_B_REFUND_IN SET MATCH_STATUS=#{matchStatus} WHERE `ID`=#{id}")
    int updateOcBRefundInMatchStatus(@Param("matchStatus") Integer matchStatus, @Param("id") Long id);

    /**
     *
     */
    @Select("SELECT ID FROM OC_B_REFUND_IN WHERE ISACTIVE = 'Y' AND (MATCH_STATUS = 1 OR MATCH_STATUS= 0)")
    List<Long> getRefundIdList();

    /**
     * 更新匹配次数. 单条
     *
     * @param id 入库单id
     */
    @Update("UPDATE OC_B_REFUND_IN SET QTY_MATCH=IFNULL(QTY_MATCH,0) + 1, MODIFIEDDATE=NOW() WHERE `ID`=#{id}")
    void updateSingleMatchTimes(@Param("id") Long id);

    /**
     * 重置匹配次数.单条
     *
     * @param id 入库单Id
     */
    void updateSingleReSetMatchTimes(@Param("id") String id);

    /**
     * 更新匹配次数. 批量
     *
     * @param ids 入库单Id
     * @return 更新数量
     */
    @UpdateProvider(type = RefundInSqlProvider.class, method = "updateMatchTimes")
    int updateMatchTimes(@Param("ids") String ids);

    /**
     * 重置匹配次数.批量
     *
     * @param ids 入库单Id
     * @return 更新数量
     */
    @UpdateProvider(type = RefundInSqlProvider.class, method = "updateReSetMatchTimes")
    int updateReSetMatchTimes(@Param("ids") String ids);

    class RefundInSqlProvider {

        /**
         * 更新匹配次数
         *
         * @param ids 入库单id字符串
         * @return sql
         */
        public String updateMatchTimes(@Param("ids") String ids) {
            StringBuilder sb = new StringBuilder();
            sb.append("UPDATE OC_B_REFUND_IN SET QTY_MATCH=IFNULL(QTY_MATCH,0) + 1, MODIFIEDDATE=NOW() ")
                    .append("WHERE `ID` IN (")
                    .append(ids).append(") AND MATCH_STATUS IN (0,1);");
            return sb.toString();
        }

        /**
         * 重置匹配次数
         *
         * @param ids 入库单id字符串
         * @return sql
         */
        public String updateReSetMatchTimes(@Param("ids") String ids) {
            StringBuilder sb = new StringBuilder();
            sb.append("UPDATE OC_B_REFUND_IN SET QTY_MATCH=0, MODIFIEDDATE=NOW() WHERE `ID` IN (");
            sb.append(ids).append(") AND MATCH_STATUS IN (0,1);");
            return sb.toString();
        }

    }

}
