package com.jackrain.nea.oc.services.st;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.st.StCHoldOrderReasonSaveCmd;
import com.jackrain.nea.st.service.StCHoldOrderReasonSaveService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: 秦雄飞
 * @time: 2023/2/15 16:46
 * @description: hold单原因保存
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class StCHoldOrderReasonSaveCmdImpl extends CommandAdapter implements StCHoldOrderReasonSaveCmd {
    @Autowired
    private StCHoldOrderReasonSaveService stCHoldOrderReasonSaveService;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        return stCHoldOrderReasonSaveService.saveHoldOrderReason(querySession);
    }
}

