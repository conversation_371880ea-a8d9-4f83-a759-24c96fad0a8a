package com.jackrain.nea.oc.oms.model.enums;


import com.jackrain.nea.oc.oms.model.result.QueryOrderCheckBoxResult;

import java.util.ArrayList;
import java.util.List;

/**
 * 退单是否确认收货
 *
 * @author: 周琳胜
 * create at: 2019/3/20 13:20
 */
public enum ReturnBillReceiveConfirm {

    RECEIPT_NOT_CONFIRMED("未确认收货", 0),
    RECEIPT_CONFIRMED("已确认收货", 1),
    CLOUD_COMFIRM_FAIL("云枢纽确认失败", 2);


    String key;
    Integer val;

    ReturnBillReceiveConfirm(String k, Integer v) {
        this.key = k;
        this.val = v;
    }

    public String getKey() {
        return key;
    }

    public Integer getVal() {
        return val;
    }

    /**
     * 转化成QueryOrderCheckBoxResult
     *
     * @return list<QueryOrderCheckBoxResult>
     */
    public static List<QueryOrderCheckBoxResult> toQueryOrderCheckBoxResult() {
        List<QueryOrderCheckBoxResult> list = new ArrayList<>();
        for (ReturnBillReceiveConfirm e : ReturnBillReceiveConfirm.values()) {
            QueryOrderCheckBoxResult o = new QueryOrderCheckBoxResult();
            o.setLabel(e.getKey());
            o.setValue(String.valueOf(e.getVal()));
            list.add(o);
        }
        return list;
    }

}


