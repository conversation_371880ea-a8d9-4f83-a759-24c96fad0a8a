package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.ActualLackProcessingOrdersBatchAuditingCmd;
import com.jackrain.nea.oc.oms.services.ActualLackProcessingOrdersService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @program: R3-OC-OMS-V1.4
 * @author: Liqb
 * @Desc 实缺处理单据批量审核
 * @create: 2019-07-18 10:00
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class ActualLackProcessingOrdersBatchAuditingImpl extends CommandAdapter implements ActualLackProcessingOrdersBatchAuditingCmd {

    @Autowired
    private ActualLackProcessingOrdersService actualLackProcessingOrdersService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        return actualLackProcessingOrdersService.batchAuditing(param, session.getUser());
    }

}
