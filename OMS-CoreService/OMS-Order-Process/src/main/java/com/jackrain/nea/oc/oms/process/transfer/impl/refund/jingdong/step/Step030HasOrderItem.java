package com.jackrain.nea.oc.oms.process.transfer.impl.refund.jingdong.step;

import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransNodeTipEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpJingdongRefundRelation;
import com.jackrain.nea.oc.oms.model.resources.SysNotesConstant;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.refund.util.TransRefundNodeTipUtil;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.utility.ExceptionUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Descroption 判断是否存在有效的全渠道订单表
 * <AUTHOR>
 * @Date 2019/4/26 14:44
 */
@Step(order = 30, description = "判断是否存在有效的全渠道订单表")
@Slf4j
@Component
public class Step030HasOrderItem extends BaseJingdongRefundProcessStep
        implements IOmsOrderProcessStep<IpJingdongRefundRelation> {
    @Override
    public ProcessStepResult<IpJingdongRefundRelation> startProcess(IpJingdongRefundRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        IpBJingdongRefund jingdongRefund = orderInfo.getJingdongRefund();
        try {
            List<OcBOrderItem> OrderItems = orderInfo.getOcBOrderItems();
            List<Long> orderIds = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(OrderItems)) {
                for (OcBOrderItem ocBOrderItem : OrderItems) {
                    orderIds.add(ocBOrderItem.getOcBOrderId());
                }
            }
            if(CollectionUtils.isNotEmpty(orderInfo.getGiftItemList())){
                for(OcBOrderItem ocBOrderItem : orderInfo.getGiftItemList()){
                    orderIds.add(ocBOrderItem.getOcBOrderId());
                }
            }
            orderIds = orderIds.stream().distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(orderIds)) {
                TransRefundNodeTipUtil.jinDongTransTipCAS(jingdongRefund, TransNodeTipEnum.ORDER_NOT_FOUND);
                ipJingdongRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(), SysNotesConstant.SYS_REMARK1, jingdongRefund);
                return new ProcessStepResult<>(StepStatus.FINISHED,
                        "单据" + orderInfo.getOrderNo() + "转换完成");
            } else {
                ipJingdongRefundService.updateOrderReturnStatus(orderInfo, orderIds);
            }
            return new ProcessStepResult<>(StepStatus.SUCCESS);
        } catch (Exception e) {
            log.error(LogUtil.format("京东退单转换异常:{}", "京东退单转换异常"), Throwables.getStackTraceAsString(e));
            //修改中间表状态及系统备注
            ipJingdongRefundService.updateRefundIsTransError(jingdongRefund,e.getMessage());
            return new ProcessStepResult<>(StepStatus.FAILED, "退单转换异常：" + e.getMessage());
        }
    }
}
