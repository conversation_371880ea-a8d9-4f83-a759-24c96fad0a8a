package com.jackrain.nea.oc.oms.process.transfer.impl.refund.jdcancel.step;

import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.MakeupOrderType;
import com.jackrain.nea.oc.oms.services.IpJingdongSaRefundService;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.util.LockOrderType;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @description: 京东取消订单转单处理抽象类
 * @author: 郑小龙
 * @date: 2020-05-29 11:43
 **/
public abstract class BaseJingdongCancelProcessStep {
    @Autowired
    protected OmsOrderService orderService;

    @Autowired
    protected IpJingdongSaRefundService saRefundService;

    protected ChannelType getCurrentChannelType() {
        return ChannelType.JINGDONG;
    }

    protected MakeupOrderType getCurrentMakeupOrderType() {
        return MakeupOrderType.TRANSFER_ORDER;
    }

    protected LockOrderType getCurrentLockOrderType() {
        return LockOrderType.TRANSFER_JINGDONG_CANCEL_ORDER;
    }
}
