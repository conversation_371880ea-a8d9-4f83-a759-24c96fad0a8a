package com.jackrain.nea.oc.oms.model.result;

import com.jackrain.nea.oc.oms.model.table.OcBRefundInExt;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @author: 夏继超
 * @since: 2019/4/12
 * create at : 2019/4/12 13:26
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryOcRefundInResult implements Serializable {
    //退货入库单的主表集合
    private List<OcBRefundInExt> queryResult;
    //总条数
    private Integer totalSize;
    //总页数
    private Integer totalNum;
    //页大小
    private Integer pageSize;
    //当前页
    private Integer pageNum;
}
