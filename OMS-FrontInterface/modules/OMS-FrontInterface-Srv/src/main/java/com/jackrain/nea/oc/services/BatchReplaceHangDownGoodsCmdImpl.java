package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.api.patrol.BatchReplaceHangDownGoodsCmd;
import com.jackrain.nea.oc.oms.model.relation.UserPermission;
import com.jackrain.nea.oc.oms.services.BatchReplaceHangDownGoodsService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: 黄世新
 * @Date: 2020/3/24 1:33 下午
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class BatchReplaceHangDownGoodsCmdImpl implements BatchReplaceHangDownGoodsCmd {

    @Autowired
    private BatchReplaceHangDownGoodsService batchReplaceHangDownGoodsService;

    @Override
    public ValueHolderV14 batchReplaceHangDownGoods(JSONObject param, User user, UserPermission usrPem) {
        return batchReplaceHangDownGoodsService.batchReplaceHangDownGoods(param, user, usrPem);
    }
}
