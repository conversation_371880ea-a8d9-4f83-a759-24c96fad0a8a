package com.jackrain.nea.cp.services;

import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.ProvinceCityAreaInfo;
import com.jackrain.nea.cpext.model.RegionInfo;
import com.jackrain.nea.cpext.model.RegionType;
import com.jackrain.nea.cpext.model.request.CpCRegionAliasCmdRequest;
import com.jackrain.nea.cpext.model.table.CpCRegionAlias;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

import static com.jackrain.nea.resource.CpRedisKeyResources.getRegionKey;

/**
 * @Author: 黄世新
 * @Date: 2019-08-26 10:08
 * @Version 1.0
 * 修改为新的省市区查询
 */
@Slf4j
@Component
public class RegionNewService {

    @Autowired
    private CpRpcService cpRpcService;

    /**
     * 中国直辖市列表
     */
    private static final String[] SPECIAL_PROVINCE_NAME_LIST = {"北京", "天津", "上海", "重庆"};

    /**
     * 省数组序号位置
     */
    private static final int PROVINCE_ARRAY_INDEX = 0;

    /**
     * 市数组序号位置
     */
    private static final int CITY_ARRAY_INDEX = 1;

    /**
     * 区数组序号位置
     */
    private static final int AREA_ARRAY_INDEX = 2;

    /**
     * 省的类别
     */
    private static final int PROVINCE_TYPE = 1;

    /**
     * 市的类别
     */
    private static final int CITY_TYPE = 2;


    /**
     * 区的类别
     */
    private static final int AREA_TYPE = 3;


    /**
     * 处理直辖市特殊省市区内容。
     * 例如：京东订单中如果是直辖市地址是不包含市名称。直接到区。天津 宁河区 东棘坨镇
     * 为了统一处理，将省市区进行转换处理。
     * 省 = 天津
     * 市 = 天津
     * 区 = 宁河区
     *
     * @param provinceName 省名称
     * @param cityName     市名称
     * @param areaName     区名称
     * @return 转换后的市名称
     */
    private String[] parseSpecialProvinceCityName(String provinceName, String cityName, String areaName) {
        for (String specialName : SPECIAL_PROVINCE_NAME_LIST) {
            if (StringUtils.isNotEmpty(provinceName) && StringUtils.isNotEmpty(cityName)) {
                if (provinceName.startsWith(specialName) && !cityName.startsWith(specialName)) {
                    areaName = cityName;
                    cityName = provinceName;
                }
            }
        }
        if (StringUtils.isNotEmpty(provinceName)) {
            provinceName = provinceName.trim();
        }
        if (StringUtils.isNotEmpty(areaName)) {
            areaName = areaName.trim();
        }
        if (StringUtils.isNotEmpty(cityName)) {
            cityName = cityName.trim();
        }
        if (StringUtils.isEmpty(cityName)) {
            cityName = areaName;
        }
        return new String[]{provinceName, cityName, areaName};
    }

    /**
     * 查询省市区信息，返回省市区对象信息
     * 注意点：区名称有可能是为空的。因为在某些地方只到市，没有到区
     *
     * @param provinceName 省名称
     * @param cityName     市名称
     * @param areaName     区名称
     * @return 省市区对象信息
     */
    public ProvinceCityAreaInfo selectProvinceCityAreaInfo(String provinceName, String cityName, String areaName) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("ProvinceName=" + provinceName + ";CityName=" + cityName + ";AreaName=" + areaName,
                    "selectProvinceCityAreaInfo", "CityName", "AreaName"));
        }
        if (StringUtils.isEmpty(areaName)) {
            areaName = "其它区";
        }
        ProvinceCityAreaInfo provinceCityAreaInfo = new ProvinceCityAreaInfo();

        String[] afterParseValues = this.parseSpecialProvinceCityName(provinceName, cityName, areaName);
        provinceName = afterParseValues[PROVINCE_ARRAY_INDEX];
        cityName = afterParseValues[CITY_ARRAY_INDEX];
        areaName = afterParseValues[AREA_ARRAY_INDEX];

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("AfterParseSpecial.ProvinceName=" + provinceName + ";CityName=" + cityName
                    + ";AreaName=" + areaName, "selectProvinceCityAreaInfo", "CityName", "AreaName"));
        }
        RegionInfo provinceInfo = null;

        if (StringUtils.isNotEmpty(provinceName)) {
            String provinceRedisKey = getRegionKey(RegionType.PROVINCE, provinceName, 1L);
            provinceInfo = this.getObjRedisTemplate().opsForValue().get(provinceRedisKey);
            if (provinceInfo == null || provinceInfo.getId() == null) {
                //查询别名表
                provinceInfo = this.selectRegionInfo(provinceName, PROVINCE_TYPE, 1L, RegionType.PROVINCE).getData();
                if (provinceInfo != null) {
                    provinceCityAreaInfo.setProvinceInfo(provinceInfo);
                } else {
                    //未匹配上 结束当前程序
                    return provinceCityAreaInfo;
                }
            } else {
                provinceCityAreaInfo.setProvinceInfo(provinceInfo);
            }
        }
        RegionInfo cityInfo = null;
        if (StringUtils.isNotEmpty(cityName)) {
            long parentId = 0L;
            if (provinceInfo != null && provinceInfo.getId() != null) {
                parentId = provinceInfo.getId();
            } else {
                return provinceCityAreaInfo;
            }
            String cityRedisKey = getRegionKey(RegionType.CITY, cityName, parentId);
            cityInfo = this.getObjRedisTemplate().opsForValue().get(cityRedisKey);

            if (cityInfo == null || cityInfo.getId() == null) {
                cityInfo = this.selectRegionInfo(cityName, CITY_TYPE, parentId, RegionType.CITY).getData();
                if (cityInfo != null) {
                    provinceCityAreaInfo.setCityInfo(cityInfo);
                } else {
                    //未匹配上 结束当前程序
                    return provinceCityAreaInfo;
                }
            } else {
                provinceCityAreaInfo.setCityInfo(cityInfo);
            }
        }
        RegionInfo areaInfo;
        if (StringUtils.isNotEmpty(areaName)) {
            long parentId = 0L;
            if (cityInfo != null && cityInfo.getId() != null) {
                parentId = cityInfo.getId();
            } else {
                return provinceCityAreaInfo;
            }
            String areaRedisKey = getRegionKey(RegionType.AREA, areaName, parentId);
            areaInfo = this.getObjRedisTemplate().opsForValue().get(areaRedisKey);
            if (areaInfo == null || areaInfo.getId() == null) {
                areaInfo = this.selectRegionInfo(areaName, AREA_TYPE, parentId, RegionType.AREA).getData();
                if (areaInfo != null) {
                    provinceCityAreaInfo.setAreaInfo(areaInfo);
                } else {
                    //未匹配上 结束当前程序
                    return provinceCityAreaInfo;
                }
            } else {
                provinceCityAreaInfo.setAreaInfo(areaInfo);
            }
        }
        return provinceCityAreaInfo;
    }


    /**
     * 封装省市区
     *
     * @param regionName
     * @param regionType
     * @param parentId
     * @return
     */
    public ValueHolderV14<RegionInfo> selectRegionInfo(String regionName, Integer regionType, Long parentId, RegionType type) {
        ValueHolderV14<CpCRegionAlias> vh14 = null;
        RegionInfo regionInfo = null;
        try {
            vh14 = this.cpRpcService.selectRegionInfo(regionName, regionType, parentId);
            CpCRegionAlias regionAliasInfo = vh14.getData();
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("别名名称为:{},返回结果{}", "selectRegionInfo"), regionName, vh14.getMessage());
            }
            if (vh14.getData() == null) {
                //未查询到数据 将数据插入到别名表
                CpCRegionAliasCmdRequest request = new CpCRegionAliasCmdRequest();
                request.setCpCRegionAlias(regionName);
                request.setRegiontype(regionType);
                request.setCUpId(parentId);
                User rootUser = SystemUserResource.getRootUser();
                request.setUser(rootUser);
                vh14 = this.cpRpcService.insertCpCRegionAlias(request);
                regionAliasInfo = vh14.getData();
                if (vh14.getCode() < 0) {
                    return new ValueHolderV14(ResultCode.FAIL, vh14.getMessage());
                }
            }
            //通过code和id判断当前数据是否已关联
            String regionEcode = regionAliasInfo.getCpCRegionEcode();
            Long regionId = regionAliasInfo.getCpCRegionId();
            if (regionId != null && StringUtils.isNotEmpty(regionEcode)) {
                //是否关联
                String isMatch = regionAliasInfo.getIsMatch();
                if ("Y".equals(isMatch)) {
                    //数据关联
                    regionInfo = this.buildRegionInfo(regionAliasInfo);
                    String regionRedisKey = getRegionKey(type, regionName, parentId);
                    this.setObjRedisTemplate().opsForValue().set(regionRedisKey, regionInfo);
                }
            } else {
                return new ValueHolderV14(ResultCode.FAIL, "别名信息关联失败!");
            }
        } catch (Exception e) {
            log.warn(LogUtil.format("查询或者新增省市区异常:{}", "selectRegionInfo", "新增省市区"),
                    Throwables.getStackTraceAsString(e));
        }
        String valueHoldMsg = vh14 == null ? "" : vh14.getMessage();
        return new ValueHolderV14(regionInfo, ResultCode.SUCCESS, valueHoldMsg);
    }


    /**
     * 转换RegionInfo对象内容
     *
     * @return Region对象
     */
    private RegionInfo buildRegionInfo(CpCRegionAlias cpCRegionAlias) {
        RegionInfo areaInfo = new RegionInfo();
        areaInfo.setCode(cpCRegionAlias.getCpCRegionEcode());
        areaInfo.setName(cpCRegionAlias.getCpCRegionAlias());
        areaInfo.setId(cpCRegionAlias.getCpCRegionId());
        areaInfo.setCreateDate(new Date());
        return areaInfo;
    }


    /**
     * 封装省市区 查不到不做插入操作
     *
     * @param regionName
     * @param regionType
     * @param parentId
     * @return
     */
    private RegionInfo selectRegionNewInfo(String regionName, Integer regionType, Long parentId, RegionType type) {
        RegionInfo regionInfo = null;
        CpCRegionAlias cpCRegionAlias = (CpCRegionAlias) this.cpRpcService.selectRegionInfo(regionName, regionType, parentId).getData();
        if (cpCRegionAlias != null) {
            //通过code和id判断当前数据是否已关联
            String cpCRegionEcode = cpCRegionAlias.getCpCRegionEcode();
            Long cpCRegionId = cpCRegionAlias.getCpCRegionId();
            if (cpCRegionId != null && StringUtils.isNotEmpty(cpCRegionEcode)) {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("selectRegionNewInfo入参:", "selectRegionNewInfo", regionName + cpCRegionId));
                }
                String isMatch = cpCRegionAlias.getIsMatch(); //是否关联
                if ("Y".equals(isMatch)) {
                    //数据关联
                    regionInfo = this.buildRegionInfo(cpCRegionAlias);
                    String regionRedisKey = getRegionKey(type, regionName, parentId);
                    this.setObjRedisTemplate().opsForValue().set(regionRedisKey, regionInfo);
                }
            }
        }
        return regionInfo;

    }


    /**
     * 查询省市区信息，返回省市区对象信息
     * 注意点：区名称有可能是为空的。因为在某些地方只到市，没有到区
     * 只是作为查询 查不到不插入到表中
     *
     * @param provinceName 省名称
     * @param cityName     市名称
     * @param areaName     区名称
     * @return 省市区对象信息
     */
    public ProvinceCityAreaInfo selectNewProvinceCityAreaInfo(String provinceName, String cityName, String areaName) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("RegionService selectNewProvinceCityAreaInfo ProvinceName=" + provinceName
                    + ";CityName=" + cityName + ";AreaName=" + areaName,
                    "selectNewProvinceCityAreaInfo", provinceName, cityName, areaName));
        }

        ProvinceCityAreaInfo provinceCityAreaInfo = new ProvinceCityAreaInfo();

        String[] afterParseValues = this.parseSpecialProvinceCityName(provinceName, cityName, areaName);
        provinceName = afterParseValues[PROVINCE_ARRAY_INDEX];
        cityName = afterParseValues[CITY_ARRAY_INDEX];
        areaName = afterParseValues[AREA_ARRAY_INDEX];

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("RegionService selectNewProvinceCityAreaInfo AfterParseSpecial ProvinceName="
                            + provinceName + ";CityName=" + cityName + ";AreaName=" + areaName,
                    "AfterParseSpecial", provinceName, cityName, areaName));
        }
        RegionInfo provinceInfo = null;

        if (StringUtils.isNotEmpty(provinceName)) {
            String provinceRedisKey = getRegionKey(RegionType.PROVINCE, provinceName, 1L);
            provinceInfo = this.getObjRedisTemplate().opsForValue().get(provinceRedisKey);
            if (provinceInfo == null || provinceInfo.getId() == null) {
                //查询别名表
                provinceInfo = this.selectRegionNewInfo(provinceName, PROVINCE_TYPE, 1L, RegionType.PROVINCE);
                if (provinceInfo != null) {
                    provinceCityAreaInfo.setProvinceInfo(provinceInfo);
                } else {
                    //未匹配上 结束当前程序
                    return provinceCityAreaInfo;
                }
            } else {
                provinceCityAreaInfo.setProvinceInfo(provinceInfo);
            }
        }

        RegionInfo cityInfo = null;
        if (StringUtils.isNotEmpty(cityName)) {
            long parentId = 0L;
            if (provinceInfo != null && provinceInfo.getId() != null) {
                parentId = provinceInfo.getId();
            }

            String cityRedisKey = getRegionKey(RegionType.CITY, cityName, parentId);
            cityInfo = this.getObjRedisTemplate().opsForValue().get(cityRedisKey);

            if (cityInfo == null || cityInfo.getId() == null) {
                cityInfo = this.selectRegionNewInfo(cityName, CITY_TYPE, parentId, RegionType.CITY);
                if (cityInfo != null) {
                    provinceCityAreaInfo.setCityInfo(cityInfo);
                } else {
                    //未匹配上 结束当前程序
                    return provinceCityAreaInfo;
                }
            } else {
                provinceCityAreaInfo.setCityInfo(cityInfo);
            }
        }

        RegionInfo areaInfo = null;
        if (StringUtils.isNotEmpty(areaName)) {
            long parentId = 0L;
            if (cityInfo != null && cityInfo.getId() != null) {
                parentId = cityInfo.getId();
            }
            String areaRedisKey = getRegionKey(RegionType.AREA, areaName, parentId);
            areaInfo = this.getObjRedisTemplate().opsForValue().get(areaRedisKey);
            if (areaInfo == null || areaInfo.getId() == null) {
                areaInfo = this.selectRegionNewInfo(areaName, AREA_TYPE, parentId, RegionType.AREA);
                if (areaInfo != null) {
                    provinceCityAreaInfo.setAreaInfo(areaInfo);
                } else {
                    //未匹配上 结束当前程序
                    return provinceCityAreaInfo;
                }
            } else {
                provinceCityAreaInfo.setAreaInfo(areaInfo);
            }
        }

        return provinceCityAreaInfo;
    }

    /**
     * 获取写的redis工具类
     *
     * @return
     */
    private CusRedisTemplate<String, RegionInfo> setObjRedisTemplate() {
        return RedisOpsUtil.getObjRedisTemplate();
    }

    /**
     * 获取读的redis工具类
     *
     * @return
     */
    public CusRedisTemplate<String, RegionInfo> getObjRedisTemplate() {
        return RedisOpsUtil.getObjRedisTemplate();
    }
}
