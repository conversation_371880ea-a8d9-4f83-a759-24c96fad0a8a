package com.jackrain.nea.oc.services;

import com.jackrain.nea.oc.oms.api.OcBOrderToBManualSourcingCmd;
import com.jackrain.nea.oc.oms.model.request.OcBOrderToBManualSourcingBatchRequest;
import com.jackrain.nea.oc.oms.model.request.OcBOrderToBManualSourcingRequest;
import com.jackrain.nea.oc.oms.model.result.OcBOrderToBManualSourcingBatchResult;
import com.jackrain.nea.oc.oms.model.result.OcBOrderToBManualSourcingResult;
import com.jackrain.nea.oc.oms.services.OcBOrderToBManualSourcingService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> lin yu
 * @date : 2022/8/4 下午1:29
 * @describe :
 */

@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBOrderToBManualSourcingCmdImpl implements OcBOrderToBManualSourcingCmd {

    @Autowired
    private OcBOrderToBManualSourcingService service;

    @Override
    public ValueHolderV14<OcBOrderToBManualSourcingResult> confirm(OcBOrderToBManualSourcingRequest request) {
        return service.confirm(request);
    }

    @Override
    public ValueHolderV14<OcBOrderToBManualSourcingResult> dataQuery(OcBOrderToBManualSourcingRequest request) {
        return service.dataQuery(request);
    }

    @Override
    public ValueHolderV14<List<OcBOrderToBManualSourcingBatchResult>> batchConfirm(OcBOrderToBManualSourcingBatchRequest request) {
        return service.batchConfirm(request);
    }
}
