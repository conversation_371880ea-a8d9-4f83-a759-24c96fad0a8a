package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.OcBReturnTypeItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;


@Mapper
@Component
public interface OcBReturnTypeItemMapper extends ExtentionMapper<OcBReturnTypeItem> {


    @Select("SELECT count(id) FROM oc_b_return_type_item WHERE ename = #{name} AND oc_b_return_type_id = #{returnTypeId}")
    int selectCountByName(@Param("name") String name, @Param("returnTypeId") Long returnTypeId);


}