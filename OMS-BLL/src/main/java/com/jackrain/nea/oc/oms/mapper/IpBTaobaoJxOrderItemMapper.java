package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoJxOrderItem;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoJxOrderItemEx;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface IpBTaobaoJxOrderItemMapper extends ExtentionMapper<IpBTaobaoJxOrderItem> {
    /**
     * @return java.util.List<com.jackrain.nea.oc.oms.model.table.IpBTaobaoJxOrderItemEx>
     * <AUTHOR>
     * @Description 分库键查询订单子表数据
     * @Date 2019-7-10
     * @Param [orderId]
     **/
    @Select("SELECT * FROM ip_b_taobao_jx_order_item WHERE ip_b_taobao_jx_order_id=#{orderId}")
    List<IpBTaobaoJxOrderItemEx> selectOrderItemList(@Param("orderId") long orderId);
}