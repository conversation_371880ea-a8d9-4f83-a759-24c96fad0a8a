package com.jackrain.nea.oc.oms.model.enums;


/**
 * 修改地址状态
 *
 * @Auther: 黄志优
 * @Date: 2020/12/10 10:31
 * @Description:
 */
public enum IpBTaobaoModifyAddrStatus {

    ISUPDATE_STATUS_NOT_SYN(0,"未同步"),
    ISUPDATE_STATUS_SYN(1,"同步成功"),
    ISUPDATE_STATUS_FAIL(2,"同步失败"),
    ISUPDATE_STATUS_WAIT(3,"待同步");


    private int key;
    private String value;


    IpBTaobaoModifyAddrStatus(int key, String value) {
        this.key = key;
        this.value = value;
    }

    public int getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }
}
