package com.jackrain.nea.oc.oms.mapper.task;

import com.jackrain.nea.oc.oms.model.enums.UnFreezeEnum;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * @ClassName OcBNaiKaUnFreezeTaskSql
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/6/30 16:14
 * @Version 1.0
 */
@Slf4j
public class OcBNaiKaUnFreezeTaskSql {

    public String selectUnFreezeNaiKaOrder(Map<String, Object> para) {
        StringBuffer sql = new StringBuffer();
        StringBuffer limitStr = new StringBuffer(" LIMIT ");
        int limit = para.get("limit") != null ? (int) para.get("limit") : 3000;
        limitStr.append(limit);

        String taskTableName = (String) para.get("taskTableName");
        int unfreezeTimes = para.get("unfreezeTimes") != null ? (int) para.get("unfreezeTimes") : 6;
        sql.append("select * from ")
                .append(taskTableName)
                .append("  where unfreeze_status!=")
                .append(UnFreezeEnum.UN_FREEZE_SUCCESS.getStatus())
                .append(" and unfreeze_times <= ")
                .append(unfreezeTimes)
                .append(" order by modifieddate asc")
                .append(limitStr);
        return sql.toString();
    }
}
