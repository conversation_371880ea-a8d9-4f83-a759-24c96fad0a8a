package com.jackrain.nea.oc.oms.services.oss;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * <p>oss 配置</p>
 *
 * <AUTHOR>
 * @since 2023/1/16
 */
@Configuration
@Getter
public class OSSConfig {
    @Value("${r3.oss.endpoint}")
    private String endpoint;

    @Value("${r3.oss.accessKey}")
    private String accessKeyId;

    @Value("${r3.oss.secretKey}")
    private String accessKeySecret;


    @Value("${r3.oss.bucketName}")
    private String bucketName;


    @Value("${r3.oss.timeout}")
    private String timeout;

    @Bean("OSSClient")
    public OSS creatOssClient() {
        return new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
    }

}
