package com.jackrain.nea.oc.oms.api.patrol;

import java.util.Date;
import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2019/6/6 9:58 AM
 * @Version 1.0
 */
public interface ReturnOrderProblemCmd {

    /**
     * 查看退款是否
     *
     * @param page
     * @param size
     * @return
     */
    List<Long> selectOrderItem(int page, int size);


    /**
     * 查看是否有明细的ooid重复
     *
     * @param beginTime
     * @param endTime
     * @return
     */
    List<String> selectOrderItemOoid(Date beginTime, Date endTime);


    /**
     * 退换货单无明细
     *
     * @param beginTime
     * @param endTime
     * @return
     */
    List<String> selectReturnOrderNotItem(Date beginTime, Date endTime);

}
