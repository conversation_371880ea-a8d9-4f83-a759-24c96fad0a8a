/*
package com.jackrain.nea.oc.oms.transfer;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.oc.oms.mapper.IpBJdOrderMapper;
import com.jackrain.nea.oc.oms.model.table.IpBJdOrder;
import com.jackrain.nea.oc.oms.task.canceltransfer.JdCancelOrderTransferTask;
import com.jackrain.nea.oc.oms.task.patrol.CheckDirtyDataTask;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import org.junit.Test222;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Profile;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.io.IOException;

*/

import com.jackrain.nea.oc.oms.mapper.IpBJdOrderMapper;
import com.jackrain.nea.oc.oms.task.canceltransfer.JdCancelOrderTransferTask;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Profile;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;


/**
 * @author: 孙继东
 * @since: 2019-05-06
 * create at : 2019-05-06 9:50
 */

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest()
@Profile(value = "dev")
public class JdCancelTransfer {
    @Autowired
    private JdCancelOrderTransferTask jdCancelOrderTransferTask;
    @Autowired
    private IpBJdOrderMapper jdOrderMapper;


    @Test
    public void test01() {

    }


}
