//package com.jackrain.nea.oc.oms.task.splitorder;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import com.google.common.collect.Lists;
//import com.google.common.util.concurrent.ThreadFactoryBuilder;
//import com.jackrain.nea.config.PropertiesConf;
//import com.jackrain.nea.exception.NDSException;
//import com.jackrain.nea.model.util.AdParamUtil;
//import com.jackrain.nea.oc.oms.config.AutoSplitOrderMqConfig;
//import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
//import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
//import com.jackrain.nea.oc.oms.mapper.task.DrdsHintDatas;
//import com.jackrain.nea.oc.oms.mapper.task.OcBOrderSplitTaskMapper;
//import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
//import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
//import com.jackrain.nea.oc.oms.model.enums.OperateType;
//import com.jackrain.nea.oc.oms.model.table.OcBOrder;
//import com.jackrain.nea.oc.oms.model.table.task.OcBOrderSplitTask;
//import com.jackrain.nea.oc.oms.sap.Oms2SapDBMetaCache;
//import com.jackrain.nea.oc.oms.services.OmsOrderService;
//import com.jackrain.nea.oc.oms.task.BaseR3Task;
//import com.jackrain.nea.r3.mq.exception.SendMqException;
//import com.jackrain.nea.r3.mq.util.R3MqSendHelper;
//import com.jackrain.nea.task.IR3Task;
//import com.jackrain.nea.task.RunTaskResult;
//import com.jackrain.nea.util.ApplicationContextHandle;
//import com.jackrain.nea.util.Tools;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//import org.springframework.transaction.annotation.Transactional;
//
//import java.util.*;
//import java.util.concurrent.*;
//import java.util.stream.Collectors;
//
//
///***
// * 拆单补偿任务从TASK表读
// * 已作废，新的定时任务 OrderSplitCompensationByStockTask
// */
//@Deprecated
//@Slf4j
//@Component
//public class AutoSplitOrderByStockCompensationTask extends BaseR3Task implements IR3Task {
//
//    /**
//     * 基本线程池常量定义
//     */
//    int corePoolSize = 16;
//    int maxPoolSize = 20;
//    long keepAliveThreadTime = 60000;
//    String threadPoolName = "R3_OMS_SPLIT_SHORTAGE_STOCK_ORDER_TASK_THREAD_POOL_%d";
//    ArrayBlockingQueue blockingQueue = new ArrayBlockingQueue(16);
//    @Autowired
//    private OcBOrderMapper ocBOrderMapper;
//    @Autowired
//    private OcBOrderItemMapper orderItemMapper;
//    @Autowired
//    private DrdsHintDatas drdsHintDatas;
//    @Autowired
//    private AutoSplitOrderMqConfig autoSplitOrderMqConfig;
//    @Autowired
//    private OcBOrderSplitTaskMapper ocBOrderSplitTaskMapper;
//    @Autowired
//    private R3MqSendHelper sendHelper;
//    @Autowired
//    private OmsOrderService omsOrderService;
//
//    @Override
//    public RunTaskResult execute(JSONObject params) {
//        if (log.isDebugEnabled()) {
//            log.debug("AutoSplitOrderByStockCompensationTask进入订单缺货拆单补偿任务");
//        }
//        RunTaskResult result = new RunTaskResult();
//        ExecutorService executor = new ThreadPoolExecutor(
//                32,
//                32,
//                0,
//                TimeUnit.SECONDS,
//                blockingQueue,
//                new ThreadFactoryBuilder().setNameFormat(threadPoolName).build());
//        try {
//
//            long start = System.currentTimeMillis();
//            final String taskTableName = "oc_b_order_split_task";
//            Set<String> nodes = topMap.keySet();
//            if (CollectionUtils.isEmpty(nodes)) {
//                log.debug("AutoSplitOrderByStockCompensationTask.nodes not get！");
//                result.setSuccess(false);
//                result.setMessage("请检查环境，node获取不到！！");
//                return result;
//            }
//            List<Future<Boolean>> results = new ArrayList<>();
//
//            for (String nodeName : nodes) {
//                results.add(executor.submit(new CallableSplitOrderTaskWithResult(nodeName, topMap.get(nodeName))));
//            }
//
//            //线程执行结果获取
//            for (Future<Boolean> futureResult : results) {
//                log.debug("AutoSplitOrderByStockCompensationTask------>线程结果={}", JSON.toJSONString(futureResult));
//            }
//
//            if (log.isDebugEnabled()) {
//                log.debug("AutoSplitOrderByStockCompensationTask 缺货拆单补偿服务定时任务完成 useTime :" + (System.currentTimeMillis() - start));
//            }
//            result.setSuccess(true);
//        } catch (Exception ex) {
//            log.error("AutoSplitOrderByStockCompensationTask 缺货拆单补偿任务完成", ex);
//            result.setSuccess(false);
//            result.setMessage(ex.getMessage());
//        } finally {
//            executor.shutdown();
//        }
//        return result;
//    }
//
//    /**
//     * 批量发送 MQ
//     *
//     * @param idsList
//     * @throws SendMqException
//     */
//    @Transactional(rollbackFor = Exception.class)
//    public void batchSendSplitOrderList(List<Long> idsList) {
//        if (log.isDebugEnabled()) {
//            log.debug("AutoSplitOrderByStockCompensationTask.batchSendSplitOrderList idsList={}",
//                    JSON.toJSONString(idsList));
//        }
//        List<OcBOrder> orders = omsOrderService.selectOrderListByIdsList(idsList);
//        List<Long> sendMqIds = new ArrayList<>();
//        List<OcBOrder> updateTaskIds = new ArrayList<>();
//        for (OcBOrder ocBOrder : orders) {
//            if (OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(ocBOrder.getOrderStatus())) {
//                sendMqIds.add(ocBOrder.getId());
//            } else {
//                updateTaskIds.add(ocBOrder);
//            }
//        }
//        if (CollectionUtils.isNotEmpty(updateTaskIds)) {
//            for (OcBOrder order : updateTaskIds) {
//                OcBOrderSplitTask task = new OcBOrderSplitTask();
//                task.setOcBOrderId(order.getId());
//                task.setModifieddate(new Date());
//                task.setStatus(2);
//                task.setRemark("订单状态为《" + order.getOrderStatus() + "》，已经不需要处理");
//                ocBOrderSplitTaskMapper.updateOcBOrderSplitTask(task);
//            }
//        }
//        if(CollectionUtils.isNotEmpty(sendMqIds)){
//            for (Long ocBOrderId : sendMqIds) {
//                OcBOrderSplitTask task = new OcBOrderSplitTask();
//                task.setOcBOrderId(ocBOrderId);
//                task.setModifieddate(new Date());
//                task.setStatus(1);
//                ocBOrderSplitTaskMapper.updateOcBOrderSplitTask(task);
//            }
//        }
//        if (CollectionUtils.isNotEmpty(sendMqIds)) {
//            String idsString =
//                    sendMqIds.stream().map(x -> String.valueOf(x)).distinct().collect(Collectors.joining(","));
//            sendMQ(idsString);
//        }
//    }
//
//    /**
//     * 发送MQ消息执行缺货拆单
//     *
//     * @param idsString 订单信息
//     */
//    public void sendMQ(String idsString){
//        if (log.isDebugEnabled()) {
//            log.debug("AutoSplitOrderByStockCompensationTask.sendMQ Start orderIds={}", idsString);
//        }
//        Long cutime = System.currentTimeMillis() * 1000; // 微秒
//        Long nanoTime = System.nanoTime(); // 纳秒
//        Long micTime = cutime + (nanoTime - nanoTime / 1000000 * 1000000) / 1000;
//        String msgKey = "SPLIT_ORDER_TR_BATCH" + "_" + micTime;
//        if (log.isDebugEnabled()) {
//            log.debug("msgKey" + msgKey);
//        }
//        OperateOrderMqInfo orderMqInfo = new OperateOrderMqInfo();
//        orderMqInfo.setOperateType(OperateType.SPLIT_ORDER);
//        orderMqInfo.setOrderIds(idsString);
//        List<OperateOrderMqInfo> mqInfoList = new ArrayList<>();
//        mqInfoList.add(orderMqInfo);
//        Object jsonValue = JSON.toJSONString(mqInfoList);
//        if (log.isDebugEnabled()) {
//            log.debug("AutoSplitOrderByStockCompensationTask.sendMQ开始发送MqTopic={},Tag={},message={}",
//                    autoSplitOrderMqConfig.getSendSplitMqTopic(), autoSplitOrderMqConfig.getSendSplitTag(), jsonValue);
//        }
//        try {
//            sendHelper.sendMessage(jsonValue, autoSplitOrderMqConfig.getSendSplitMqTopic(),
//                    autoSplitOrderMqConfig.getSendSplitTag(),
//                    msgKey);
//        } catch (Exception e){
//            throw new NDSException(e);
//        }
//    }
//
//    /**
//     * 开启线程类
//     */
//    class CallableSplitOrderTaskWithResult implements Callable<Boolean> {
//
//        private String nodeName;
//
//        private String taskTableName;
//
//        public CallableSplitOrderTaskWithResult(String nodeName, String taskTableName) {
//            this.nodeName = nodeName;
//            this.taskTableName = taskTableName;
//        }
//
//        @Override
//        public Boolean call() throws Exception {
//            PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
//            Integer pageSize = config.getProperty("lts.AutoSplitOrderByStockCompensationTask.range", 1000);
//            Integer splitTimes = config.getProperty("lts.AutoSplitOrderByStockCompensationTask.split.times", 5);
//            List<Long> list;
//            long start = System.currentTimeMillis();
//            list = ocBOrderSplitTaskMapper.selectTaskIdList(nodeName, pageSize, taskTableName, splitTimes);
//            long endTime1 = System.currentTimeMillis();
//            if (log.isDebugEnabled()) {
//                log.debug("缺货拆单补偿服务单线程查询耗时:{}ms,一共:{}条", endTime1 - start, list == null ? 0 : list.size());
//            }
//            if (CollectionUtils.isEmpty(list)) {
//                return true;
//            }
//            int pointsDataLimit = Tools.getInt(AdParamUtil.getParam("oms.oc.order.autoSplit.push.num"), 200);
//            if (log.isDebugEnabled()) {
//                log.debug("AutoSplitOrderByStockCompensationTask.batchSendSplitOrderList  oms.oc.order.autoSplit.push.num={}", pointsDataLimit);
//            }
//            List<List<Long>> allList = Lists.partition(list, pointsDataLimit);
//            for(List<Long> splitList : allList) {
//                try{
//                    ApplicationContextHandle.getBean(AutoSplitOrderByStockCompensationTask.class).batchSendSplitOrderList(splitList);
//                } catch (Exception e){
//                    log.error("AutoSplitOrderByStockCompensationTask 补偿任务异常，入参={},异常信息={}", JSON.toJSONString(splitList), e);
//                }
//            }
//
//            long endTime = System.currentTimeMillis();
//            if (log.isDebugEnabled()) {
//                log.debug("缺货拆单补偿服务单线程单线程耗时:{}ms", endTime - start);
//            }
//
//            return true;
//        }
//
//    }
//}
//
