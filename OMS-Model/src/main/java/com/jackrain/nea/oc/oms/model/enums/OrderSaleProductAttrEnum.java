package com.jackrain.nea.oc.oms.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> wang<PERSON><PERSON>
 * @since : 2023/3/27
 * description : 订单销售商品属性枚举
 */
@Getter
@AllArgsConstructor
public enum OrderSaleProductAttrEnum {
    NULL("NULL", "NULL", "空"),
    C("C", "C", "残次"),
    F("F", "C", "返修"),
    FC("FC", "C", "残次或返修"),
    D("D", "D", "大日期"),
    E("E", "E", "替换"),
    M("M", "M", "样本"),
    S("S", "S", "系列"),
    ZJ("ZJ", "ZJ", "质检");

    String val;
    String parentVal;
    String description;

    public static String getDescriptionByVal(String val) {
        if (val == null) {
            return null;
        }
        for (OrderSaleProductAttrEnum o : OrderSaleProductAttrEnum.values()) {
            if (o.getVal().equals(val)) {
                return o.getDescription();
            }
        }
        return "";
    }

    public static boolean isToBCC(String val) {
        if (StringUtils.isEmpty(val)) {
            return false;
        }
        return "C".equals(val) || "FC".equals(val) || "F".equals(val);
    }
}
