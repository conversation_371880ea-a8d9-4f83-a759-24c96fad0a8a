package com.jackrain.nea.oc.oms.services.refund;

import com.google.common.collect.Lists;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderLogMapper;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderLog;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: 秦雄飞
 * @time: 2022/5/21 20:23
 * @description: 退换货单日志
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class OcBReturnOrderLogService {

    private final OcBReturnOrderLogMapper logMapper;

    private static final String LOG_TABLE_NAME = "oc_b_return_order_log";
    private static final String LOCAL_IP = "127.0.0.1";

    private String getIpAddress(String lastLoginIp) {
        return StringUtils.isNotBlank(lastLoginIp) ? lastLoginIp : LOCAL_IP;
    }

    /**
     * @param refundInId 退货入库单编号
     * @param logType    日志类型
     * @param logMessage 日志内容
     * @param user       操作用户
     */
    public void saveOrderLogInfo(Long refundInId, String logType, String logMessage, User user) {
        try {
            OcBReturnOrderLog ocBRefundInLog = new OcBReturnOrderLog();
            Long logId = ModelUtil.getSequence(LOG_TABLE_NAME);
            ocBRefundInLog.setId(logId);
            ocBRefundInLog.setOcBReturnOrderId(refundInId);
            ocBRefundInLog.setLogType(logType);
            ocBRefundInLog.setLogMessage(logMessage);
            ocBRefundInLog.setIpAddress(getIpAddress(user.getLastloginip()));
            BaseModelUtil.initialBaseModelSystemField(ocBRefundInLog, user);
            ocBRefundInLog.setUserName(user.getName());
            logMapper.insert(ocBRefundInLog);
        } catch (Exception e) {
            log.error(LogUtil.format("OmsReturnOrderLogService.saveOrderLogInfo.error",
                    "returnOrder.saveOrderLogInfo.error"), e);
        }
    }

    /**
     * @param returnOrderIdList
     * @param logType
     * @param user
     */
    public void batchSaveOrderLogInfo(List<Long> returnOrderIdList, String logType, String msg, User user) {
        try {
            List<OcBReturnOrderLog> returnOrderLogList = returnOrderIdList.stream().map(id -> {
                OcBReturnOrderLog ocBRefundInLog = new OcBReturnOrderLog();
                ocBRefundInLog.setId(ModelUtil.getSequence(LOG_TABLE_NAME));
                ocBRefundInLog.setOcBReturnOrderId(id);
                ocBRefundInLog.setLogType(logType);
                ocBRefundInLog.setLogMessage(msg);
                ocBRefundInLog.setIpAddress(getIpAddress(user.getLastloginip()));
                BaseModelUtil.initialBaseModelSystemField(ocBRefundInLog, user);
                ocBRefundInLog.setUserName(user.getName());
                return ocBRefundInLog;
            }).collect(Collectors.toList());
            List<List<OcBReturnOrderLog>> partition = Lists.partition(returnOrderLogList, 200);
            for (List<OcBReturnOrderLog> stubLit : partition) {
                logMapper.batchInsert(stubLit);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("OmsReturnOrderLogService.batchSaveOrderLogInfo.error",
                    "returnOrder.batchSaveOrderLogInfo.error"), e);
        }
    }

    /**
     * @param orderId
     * @param logType
     * @param msgList
     * @param user
     */
    public void batchAddLog(Long orderId, String logType, List<String> msgList, User user) {
        try {
            List<OcBReturnOrderLog> returnOrderLogList = Lists.newArrayList();
            for (String msg : msgList) {
                OcBReturnOrderLog ocBRefundInLog = new OcBReturnOrderLog();
                ocBRefundInLog.setId(ModelUtil.getSequence(LOG_TABLE_NAME));
                ocBRefundInLog.setOcBReturnOrderId(orderId);
                ocBRefundInLog.setLogType(logType);
                ocBRefundInLog.setLogMessage(msg);
                ocBRefundInLog.setIpAddress(getIpAddress(user.getLastloginip()));
                BaseModelUtil.initialBaseModelSystemField(ocBRefundInLog, user);
                ocBRefundInLog.setUserName(user.getName());
                returnOrderLogList.add(ocBRefundInLog);
            }
            List<List<OcBReturnOrderLog>> partition = Lists.partition(returnOrderLogList, 200);
            for (List<OcBReturnOrderLog> stubLit : partition) {
                logMapper.batchInsert(stubLit);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("OmsReturnOrderLogService.batchAddLog.error",
                    "returnOrder.batchAddLog.error"), e);
        }
    }
}
