package com.jackrain.nea.oc.oms.task.naika;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderNaiKaMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderNaikaVoidMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnAfSendItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnAfSendMapper;
import com.jackrain.nea.oc.oms.model.enums.CardAutoVoidEnum;
import com.jackrain.nea.oc.oms.model.enums.NaikaVoidStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.enums.naika.OmsOrderNaiKaStatusEnum;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderNaiKa;
import com.jackrain.nea.oc.oms.model.table.OcBOrderNaikaVoid;
import com.jackrain.nea.oc.oms.model.table.OcBReturnAfSend;
import com.jackrain.nea.oc.oms.model.table.OcBReturnAfSendItem;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ListSplitUtil;
import com.jackrain.nea.util.OperateUserUtils;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * @ClassName CardAutoVoidStatusTask
 * @Description 奶卡自动作废状态维护
 * <AUTHOR>
 * @Date 2023/3/2 14:19
 * @Version 1.0
 */
@Component
@Slf4j
public class CardAutoVoidStatusTask extends BaseR3Task implements IR3Task {

    @Resource
    private ThreadPoolTaskExecutor cardAutoVoidPollExecutor;

    @Autowired
    private OcBReturnAfSendMapper ocBReturnAfSendMapper;
    @Autowired
    private OcBReturnAfSendItemMapper ocBReturnAfSendItemMapper;
    @Autowired
    private OcBOrderNaiKaMapper ocBOrderNaiKaMapper;
    @Autowired
    private OcBOrderNaikaVoidMapper naikaVoidMapper;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private BuildSequenceUtil sequenceUtil;


    @Override
    @XxlJob("CardAutoVoidStatusTask")
    public RunTaskResult execute(JSONObject params) {

        RunTaskResult resultTask = new RunTaskResult();
        resultTask.setSuccess(Boolean.TRUE);
        resultTask.setMessage("success");
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("CardAutoVoidStatusTask execute start"));
        }

        long start = System.currentTimeMillis();
        final String taskTableName = "oc_b_return_af_send";
        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
        int pageSize = config.getProperty("lts.CardAutoVoidStatusTask.range", 2400);
        List<OcBReturnAfSend> ocBReturnAfSendList = ocBReturnAfSendMapper.selectCardAutoVoid(pageSize, taskTableName);
        if (CollectionUtils.isEmpty(ocBReturnAfSendList)) {
            return resultTask;
        }
        List<List<OcBReturnAfSend>> lists = ListSplitUtil.averageAssign(ocBReturnAfSendList, 24);

        List<Future<Boolean>> results = new ArrayList<>();
        for (List<OcBReturnAfSend> data : lists) {
            results.add(cardAutoVoidPollExecutor.submit(new CardAutoVoidStatusTask.CardAutoVoidTaskWithResult(data)));
        }
        //线程执行结果获取
        for (Future<Boolean> futureResult : results) {
            try {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("CardAutoVoidStatusTask------>线程结果:{}"), futureResult.get().toString());
                }
            } catch (Exception e) {
                log.error(LogUtil.format("CardAutoVoidStatusTask多线程获取InterruptedException异常：{}", "CardAutoVoidStatusTask"), Throwables.getStackTraceAsString(e));
            }
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("CardAutoVoidStatusTask 奶卡自动作废 useTime : {}"), (System.currentTimeMillis() - start));
        }
        return resultTask;
    }

    private void insertOcBOrderNaikaVoid(OcBReturnAfSend ocBReturnAfSend, OcBReturnAfSendItem ocBReturnAfSendItem, OcBOrder ocBOrder) {
        OcBOrderNaikaVoid ocBOrderNaikaVoid = new OcBOrderNaikaVoid();
        ocBOrderNaikaVoid.setOcBOrderId(ocBReturnAfSendItem.getRelationBillId());
        ocBOrderNaikaVoid.setOcBOrderItemId(ocBReturnAfSendItem.getRelationBillItemId());
        ocBOrderNaikaVoid.setTid(ocBOrder.getTid());
        ocBOrderNaikaVoid.setVoidStatus(NaikaVoidStatusEnum.VOID.getStatus());
        ocBOrderNaikaVoid.setVoidTimes(0);
        ocBOrderNaikaVoid.setPlatformId(ocBReturnAfSend.getCpCPlatformId());
        ocBOrderNaikaVoid.setId(sequenceUtil.buildCardCodeVoidSequenceId());
        ocBOrderNaikaVoid.setOcBReturnAfSendId(ocBReturnAfSend.getId());
        OperateUserUtils.saveOperator(ocBOrderNaikaVoid, null);
            // insert
            naikaVoidMapper.insert(ocBOrderNaikaVoid);
    }

    class CardAutoVoidTaskWithResult implements Callable<Boolean> {

        private final List<OcBReturnAfSend> data;

        public CardAutoVoidTaskWithResult(List<OcBReturnAfSend> data) {
            this.data = data;
        }

        @Override
        public Boolean call() throws Exception {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("CardAutoVoidStatusTask execute start ocBReturnAfSendList:{}", JSONUtil.toJsonStr(data)));
            }
            if (CollectionUtils.isEmpty(data)) {
                return true;
            }
            for (OcBReturnAfSend ocBReturnAfSend : data) {
                try {
                    List<OcBReturnAfSendItem> ocBReturnAfSendItemList = ocBReturnAfSendItemMapper.selectByOcBReturnAfSendIdListBySendId(ocBReturnAfSend.getId());
                    OcBReturnAfSend updateOcBReturnAfSend = new OcBReturnAfSend();
                    updateOcBReturnAfSend.setId(ocBReturnAfSend.getId());
                    String sysMark = ocBReturnAfSend.getSysremark() == null ? "" : ocBReturnAfSend.getSysremark();
                    for (OcBReturnAfSendItem ocBReturnAfSendItem : ocBReturnAfSendItemList) {
                        if (ObjectUtil.equal(ocBReturnAfSendItem.getAmtActual(), ocBReturnAfSendItem.getAmtReturn())) {
                            // 根据订单id+sku信息查询出来奶卡表 奶卡的数量
                            // 根据已发货退款单明细中关联的订单明细id中的数量

                            // 订单ID
                            Long ocBOrderId = Long.valueOf(ocBReturnAfSend.getSourceBillNo());

                            List<OcBOrderNaiKa> ocBOrderNaiKaList =
                                    ocBOrderNaiKaMapper.selectIdByOcBOrderIdAndSku(ocBOrderId, ocBReturnAfSendItem.getPsCSkuEcode());
                            OcBOrder ocBOrder = ocBOrderMapper.selectById(ocBOrderId);
                            if (CollectionUtils.isNotEmpty(ocBOrderNaiKaList)) {
                                if (ObjectUtil.equal(new BigDecimal(ocBOrderNaiKaList.size()), ocBReturnAfSendItem.getQtyReturnApply())) {
                                    // 往自动作废表里写入数据(目前有赞/抖音/天猫/京东 将这几个渠道的数据写入)
                                    if (ObjectUtil.equal(ocBReturnAfSend.getCpCPlatformId(), Long.valueOf(PlatFormEnum.DOU_YIN.getCode())) ||
                                            ObjectUtil.equal(ocBReturnAfSend.getCpCPlatformId(), Long.valueOf(PlatFormEnum.YOUZAN.getCode())) ||
                                            ObjectUtil.equal(ocBReturnAfSend.getCpCPlatformId(), Long.valueOf(PlatFormEnum.TAOBAO.getCode())) ||
                                            ObjectUtil.equal(ocBReturnAfSend.getCpCPlatformId(), Long.valueOf(PlatFormEnum.JINGDONG.getCode())) ||
                                            ObjectUtil.equal(ocBReturnAfSend.getCpCPlatformId(), Long.valueOf(PlatFormEnum.KID_KING.getCode())) ||
                                            ObjectUtil.equal(ocBReturnAfSend.getCpCPlatformId(), Long.valueOf(PlatFormEnum.DXDOCTOR.getCode())) ||
                                            ObjectUtil.equal(ocBReturnAfSend.getCpCPlatformId(), Long.valueOf(PlatFormEnum.CARD_CODE.getCode()))) {
                                        insertOcBOrderNaikaVoid(ocBReturnAfSend, ocBReturnAfSendItem, ocBOrder);
                                        updateOcBReturnAfSend.setCardAutoVoid(CardAutoVoidEnum.TO_DO_VOID.getCode());
                                        List<Long> naikaIds = ocBOrderNaiKaList.stream().map(OcBOrderNaiKa::getId).collect(Collectors.toList());
                                        ocBOrderNaiKaMapper.updateNaiKaStatusByIdList(naikaIds, OmsOrderNaiKaStatusEnum.TO_VOID.getStatus());
                                    }
                                } else {
                                    sysMark = sysMark + "卡号数量与退款数量不一致，不触发自动作废";
                                }
                            } else {
                                sysMark = sysMark + "已发货退款单明细未关联到订单！";
                            }
                        } else {
                            sysMark = sysMark + "退款金额与成交金额不一致，不触发自动作废！";
                        }
                    }
                    updateOcBReturnAfSend.setSysremark(sysMark);
                    updateOcBReturnAfSend.setModifieddate(new Date());
                    updateOcBReturnAfSend.setVoidMark(1);
                    ocBReturnAfSendMapper.updateById(updateOcBReturnAfSend);
                } catch (Exception e) {
                    log.error(LogUtil.format("CardAutoVoidStatusTask execute error ocBReturnAfSend:{}", JSONUtil.toJsonStr(ocBReturnAfSend)));

                }
            }
            return true;
        }
    }
}
