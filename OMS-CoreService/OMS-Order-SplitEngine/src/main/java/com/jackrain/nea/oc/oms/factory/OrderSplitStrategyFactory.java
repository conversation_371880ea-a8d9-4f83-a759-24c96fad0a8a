package com.jackrain.nea.oc.oms.factory;

import com.jackrain.nea.oc.oms.strategy.IOrderSplitStrategy;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @Description: 策略工厂类
 * @author: 江家雷
 * @since: 2020/6/28
 * create at : 2020/6/28 16:17
 */
public class OrderSplitStrategyFactory {

    // 存放策略bean的容器
    private static Map<String, IOrderSplitStrategy> services = new ConcurrentHashMap<>();

    // 将策略bean注册到容器中
    public static void register(String strategy, IOrderSplitStrategy iOrderSplitStrategy) {
        services.put(strategy, iOrderSplitStrategy);
    }

    // 获取策略对应的bean
    public static IOrderSplitStrategy getOrderSplitStrategy(String strategy) {
        return services.get(strategy);
    }

}
