package com.jackrain.nea.oc.oms.model.result;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @ClassName : PosOrderQtyQueryResult  
 * @Description : 
 * <AUTHOR>  YCH
 * @Date: 2021-09-08 11:53  
 */
@Data
public class PosOrderQtyQueryItemResult implements Serializable {

    private Long psCSkuId;

    private String psCSkuEcode;

    private Long psCProId;

    private String psCProEcode;

    private String psCProEname;

    private BigDecimal qtyOut;
}
