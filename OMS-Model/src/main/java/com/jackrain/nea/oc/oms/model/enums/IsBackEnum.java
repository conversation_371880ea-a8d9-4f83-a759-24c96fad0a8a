package com.jackrain.nea.oc.oms.model.enums;


/**
 * 退单是否原退
 *
 * @author: ming.fz
 * create at: 2019/9/16
 */
public enum IsBackEnum {


    /**
     * 是否可用
     */
    Y("Y", 1),
    N("N", 0);

    String key;
    int val;

    IsBackEnum(String k, int v) {
        this.key = k;
        this.val = v;
    }

    public String getKey() {
        return key;
    }

    public int getVal() {
        return val;
    }


}


