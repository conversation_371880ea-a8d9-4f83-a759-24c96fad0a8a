package com.jackrain.nea.oc.oms.model.enums.naika;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @ClassName AccountToNaiKaEnum
 * @Description 对账传奶卡状态
 * <AUTHOR>
 * @Date 2022/9/6 18:29
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum AccountToNaiKaEnum {

    IGNORE(0, "暂不同步"),
    TODO(1, "待同步"),
    DOING(2, "同步中"),
    SUCCESS(3, "同步成功"),
    FAIL(4, "同步失败"),
    ;

    private Integer status;

    private String desc;

    public static AccountToNaiKaEnum getOrderStatusByStatus(Integer status) {
        for (AccountToNaiKaEnum value : AccountToNaiKaEnum.values()) {
            if (value.getStatus().equals(status)) {
                return value;
            }
        }
        return null;
    }
}
