package com.jackrain.nea.oc.oms.services.task;

import com.jackrain.nea.oc.oms.mapper.task.OcBMsgTaskMapper;
import com.jackrain.nea.oc.oms.model.table.OcBMsgSendRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * <AUTHOR> ruan.gz
 * @Description : 短信任务
 * @Date : 2020/8/29
 **/
@Component
public class OmsMsgTaskService {

    @Autowired
    private OcBMsgTaskMapper taskMapper;

    public void insertTask(OcBMsgSendRecord task) {
        taskMapper.insert(task);
    }

    public void updateAuditTask(List<Long> taskIdList) {
        taskMapper.updateTaskStatus(taskIdList);
    }

    public void updateAuditTaskById(Long id) {
        taskMapper.updateTaskStatusById(id);
    }

    public List<OcBMsgSendRecord> selectTask(String nodeName, int limit, String taskTableName) {
        return taskMapper.selectTaskIdList(nodeName, limit, taskTableName);
    }

    public void updateAuditTaskModitime(List<Long> orderIds) {
        taskMapper.updateTaskModitimes(orderIds);
    }

}
