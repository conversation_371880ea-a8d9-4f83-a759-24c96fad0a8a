package com.jackrain.nea.oc.oms.mapper.task;

import com.jackrain.nea.oc.oms.model.table.task.OcBOrderSplitTask;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.text.MessageFormat;
import java.util.List;
import java.util.Map;

/**
 * @author: 江家雷
 * @since: 2020-09-14
 * create at : 2020-09-14 22:48
 */
@Slf4j
public class OcBOrderSplitTaskSql {

    public String selectByNodeSql(Map<String, Object> para) {
        StringBuffer sql = new StringBuffer();
        StringBuffer limitStr = new StringBuffer(" LIMIT ");
        String nodeName = (String) para.get("nodeName");
        int limit = para.get("limit") != null ? (int) para.get("limit") : 3000;
        limitStr.append(limit);

        String taskTableName = (String) para.get("taskTableName");
        int splitTimes = para.get("splitTimes") != null ? (int) para.get("splitTimes") : 0;

        if (StringUtils.isEmpty(nodeName)) {
            return null;
        }
        sql.append("/*!TDDL:NODE=" + nodeName + "*/")
                .append("select * from ")
                .append(taskTableName)
                .append("  where status = 0 ")
                .append(" and next_time < now()")
                /*.append(" and split_times < ")
                .append(splitTimes)*/
                .append(limitStr);
        return sql.toString();
    }

    public String insertOcBOrderSplitTask(Map<String, Object> para) {
        List<OcBOrderSplitTask> list = (List<OcBOrderSplitTask>) para.get("list");
        StringBuffer sql = new StringBuffer();
        sql.append("insert ignore into `oc_b_order_split_task` (`id`,`oc_b_order_id`,`status`,`split_times`,`next_time`," +
                "`remark`,`is_lack_stock`,`pay_time`,`isactive`,`version`,`ad_org_id`,`ad_client_id`,`ownerid`,`ownerename`,`ownername`,`creationdate`," +
                "`modifierid`,`modifierename`,`modifiername`,`modifieddate`) VALUES ");
        MessageFormat mf = new MessageFormat("(#'{'list[{0}].id},#'{'list[{0}].ocBOrderId},#'{'list[{0}].status}," +
                "#'{'list[{0}].splitTimes},#'{'list[{0}].nextTime},#'{'list[{0}].remark},#'{'list[{0}].isLackStock},#'{'list[{0}].payTime},#'{'list[{0}].isactive}," +
                "#'{'list[{0}].version},#'{'list[{0}].adOrgId},#'{'list[{0}].adClientId},#'{'list[{0}].ownerid},#'{'list[{0}].ownerename}," +
                "#'{'list[{0}].ownername},#'{'list[{0}].creationdate},#'{'list[{0}].modifierid},#'{'list[{0}].modifierename},#'{'list[{0}].modifiername}," +
                "#'{'list[{0}].modifieddate})");
        for (int i = 0; i < list.size(); i++) {
            sql.append(mf.format(new Object[]{i}));
            sql.append(",");
        }
        return sql.toString().substring(0, sql.length() - 1);
    }


    public String getNodeSql(Map<String, Object> para) {
        StringBuilder sql = new StringBuilder();
        StringBuilder limitStr = new StringBuilder(" limit ");
        int limit = para.get("limit") != null ? (int) para.get("limit") : 200;
        limitStr.append(limit);
        String taskTableName = (String) para.get("taskTableName");
        int splitTimes = para.get("splitTimes") != null ? (int) para.get("splitTimes") : 0;
        int status = para.get("status") == null ? 0 : (int)para.get("status");
        sql.append("select * from ")
                .append(taskTableName)
                .append("  where status = ")
                .append(status)
                .append(" and next_time <= now()")
                .append(" and split_times < ")
                .append(splitTimes)
                .append(" order by pay_time desc ")
                .append(limitStr);
        return sql.toString();
    }

    public String getCompensationNodeSql(Map<String, Object> para) {
        StringBuilder sql = new StringBuilder();
        sql.append("/*!TDDL:NODE=" + para.get("nodeName") + "*/");
        sql.append("select * from ");
        sql.append(para.get("taskTableName"));
        sql.append(" where 1=1 ");
        if (para.get("statusList") != null) {
            List<String> statusList = (List<String>) para.get("statusList");
            if (CollectionUtils.isNotEmpty(statusList)) {
                sql.append(" and status in ( ");
                boolean flag = false;
                for (String status : statusList) {
                    if (flag) {
                        sql.append(",");
                    }
                    sql.append(status);
                    flag = true;
                }
                sql.append(" ) ");
            }
        }
        sql.append(" and split_times < ");
        sql.append(para.get("splitTimes"));
        sql.append(" order by pay_time desc ");
        sql.append(" limit ");
        sql.append(para.get("limit"));
        return sql.toString();
    }

}
