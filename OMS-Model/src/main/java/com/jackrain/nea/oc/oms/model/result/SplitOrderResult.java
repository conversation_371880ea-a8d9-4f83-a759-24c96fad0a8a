package com.jackrain.nea.oc.oms.model.result;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: heliu
 * @since: 2019/7/10
 * create at : 2019/7/10 12:12
 */
@Data
public class SplitOrderResult implements Serializable {

    /**
     * 原订单Id
     */
    private Long origOrderId;

    /**
     * 原订单明细Id
     */
    private Long origOrderItemId;

    /**
     * 实体仓Id
     */
    private Long phyWarehouseId;


    /**
     * 实体仓ecode
     */
    private String phyWarehouseEcode;

    /**
     * 实体仓ename;
     */
    private String phyWarehouseEname;

    /**
     * 数量
     */
    private BigDecimal qty;

    /**
     * 聚合仓id
     */
    private Long shareStorageId;

    /**
     * 聚合仓编码
     */
    private String shareStorageEcode;

    /**
     * 聚合仓名称
     */
    private String shareStorageEname;
}