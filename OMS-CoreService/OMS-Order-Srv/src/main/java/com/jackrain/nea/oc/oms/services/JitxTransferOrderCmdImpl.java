package com.jackrain.nea.oc.oms.services;

import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.api.JitxTransferOrderCmd;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.jitx.JitxOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpJitxOrderRelation;
import com.jackrain.nea.oc.oms.model.request.TransferOrderRequest;
import com.jackrain.nea.oc.oms.model.result.TransferOrderResult;
import com.jackrain.nea.oc.oms.process.transfer.impl.normal.jitx.JitxTransferOrderProcessImpl;
import com.jackrain.nea.oc.oms.process.transfer.impl.refund.jitx.JitxTransferRefundProcessImpl;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.ProcessStepResultList;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> 黄超
 * @since : 2019-06-27
 * create at : 2019-06-27 20:00
 */
@Service(protocol = "dubbo", validation = "true", group = "oc-core", version = "1.0")
@Slf4j
@Component
public class JitxTransferOrderCmdImpl implements JitxTransferOrderCmd {

    @Autowired
    private JitxTransferOrderProcessImpl jitxTransferOrderProcess;

    @Autowired
    private JitxTransferRefundProcessImpl jitxTransferRefundProcess;

    @Autowired
    private IpJitxOrderService ipJitxOrderService;

    @Override
    public ValueHolderV14<TransferOrderResult> startTransferOrder(TransferOrderRequest transferOrderRequest) {
        ValueHolderV14<TransferOrderResult> resultValueHolderV14 = new ValueHolderV14<>();
        try {
            boolean hasError = false;
            int failedNumber = 0;
            int successNumber = 0;
            List<ProcessStepResult> errorStepResultList = new ArrayList<>();
            for (String orderNo : transferOrderRequest.getOrderNoList()) {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("JitxTransferOrderCmdImpl,ChannelType：{}", orderNo, "JitxTransferOrderCmdImpl"), transferOrderRequest.getChannelType());
                }

                if (transferOrderRequest.getChannelType() == ChannelType.VIPJITX) {
                    IpJitxOrderRelation orderRelation = this.ipJitxOrderService.selectJitxOrder(orderNo);
                    if (orderRelation != null) {
                        ProcessStepResultList processStepResultList;
                        String orderStatus = orderRelation.getJitxOrder().getOrderStatus();
                        if (JitxOrderStatus.ORDER_UNSEND_REFUND.equals(orderStatus)
                                || JitxOrderStatus.ORDER_SEND_REFUND.equals(orderStatus)
                                || JitxOrderStatus.ORDER_COLLECTED_REFUND.equals(orderStatus)) {
                            processStepResultList = jitxTransferRefundProcess.start(orderRelation,
                                    false, transferOrderRequest.getOperateUser());
                        } else {
                            processStepResultList = jitxTransferOrderProcess.start(orderRelation,
                                    false, transferOrderRequest.getOperateUser());
                        }
                        if (!processStepResultList.isProcessFishSuccess()) {
                            hasError = true;
                            errorStepResultList.add(processStepResultList.getLastFaileOrFinishProcessStepResult());
                            failedNumber++;
                        } else {
                            successNumber++;
                        }
                    } else {
                        hasError = false;
                        failedNumber++;
                    }
                }
            }
            if (hasError) {
                resultValueHolderV14.setCode(ResultCode.FAIL);
                StringBuilder sbMessage = new StringBuilder();
                sbMessage.append(String.format("转换成功%s条；转换失败%s条；失败原因：\r\n", successNumber, failedNumber));
                for (ProcessStepResult stepResult : errorStepResultList) {
                    if (stepResult != null) {
                        sbMessage.append(stepResult.getMessage());
                        sbMessage.append("\r\n");
                    }
                }
                resultValueHolderV14.setMessage(sbMessage.toString());
            } else {
                resultValueHolderV14.setCode(ResultCode.SUCCESS);
                resultValueHolderV14.setMessage("转换全部成功。");
            }
        } catch (Exception ex) {
            resultValueHolderV14.setCode(ResultCode.FAIL);
            resultValueHolderV14.setMessage("转换异常：" + ex.getMessage());
            log.error(LogUtil.format("JitxTransferOrderCmdImpl,异常信息:{}", "JitxTransferOrderCmdImpl"), Throwables.getStackTraceAsString(ex));

        }
        return resultValueHolderV14;
    }
}