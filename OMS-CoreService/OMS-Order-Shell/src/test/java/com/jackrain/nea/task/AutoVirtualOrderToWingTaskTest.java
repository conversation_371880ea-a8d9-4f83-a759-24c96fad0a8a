/*
package com.jackrain.nea.task;

import com.jackrain.nea.OmsOrderProcessApplication;
import com.jackrain.nea.oc.oms.services.FindReturnOrderService;
import com.jackrain.nea.oc.oms.task.ordertowing.AutoPayableAdjustmentAddIncidentalsTask;
import com.jackrain.nea.oc.oms.task.ordertowing.AutoReturnAfSendOrderManualToWingTask;
import com.jackrain.nea.oc.oms.task.ordertowing.AutoReturnAfSendOrderToWingTask;
import com.jackrain.nea.oc.oms.task.ordertowing.AutoVirtualOrderToWingTask;
import com.jackrain.nea.oc.oms.task.refundorder.TimedReturnOrderTask;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

*/
/**
 * Description： 定时任务
 * Author: RESET
 * Date: Created in 2020/9/1 9:54
 * Modified By:
 *//*

@RunWith(SpringRunner.class)
@SpringBootTest(classes = OmsOrderProcessApplication.class)
@Slf4j
public class AutoVirtualOrderToWingTaskTest {

    @Autowired
    AutoVirtualOrderToWingTask transferTask;

    @Autowired
    AutoReturnAfSendOrderToWingTask returnAfSendOrderToWingTask;

    @Autowired
    AutoReturnAfSendOrderManualToWingTask returnAfSendOrderManualToWingTask;

    @Autowired
    AutoPayableAdjustmentAddIncidentalsTask autoPayableAdjustmentAddIncidentalsTask;

    @Autowired
    private TimedReturnOrderTask timedReturnOrderTask;

    @Autowired
    private FindReturnOrderService returnOrderService;

    @Test
    public void testAutoTask() {
        transferTask.execute(null);
    }

    @Test
    public void testAutoTask1() {
        returnAfSendOrderToWingTask.execute(null);
    }

    @Test
    public void testAutoTask2() {
        returnAfSendOrderManualToWingTask.execute(null);
    }

    @Test
    public void testAutoTask3() {
        autoPayableAdjustmentAddIncidentalsTask.execute(null);
    }


//    @Test
//    public void push() {
//        timedReturnOrderTask.execute(null);
//    }

    @Test
    public void query(){
        returnOrderService.findReturnOrderService(200);
    }

}
*/
