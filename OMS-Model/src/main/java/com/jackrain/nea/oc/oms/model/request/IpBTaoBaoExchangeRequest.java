package com.jackrain.nea.oc.oms.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoExchange;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: 黄世新
 * @Date: 2019/11/4 1:35 下午
 * @Version 1.0
 */
@Data
public class IpBTaoBaoExchangeRequest implements Serializable {
    /**
     *
     */
    @JSONField(name = "ip_b_taobao_exchange")
    private IpBTaobaoExchange ipBTaobaoExchange;
    /**
     *
     */
    private Long objId;


}
