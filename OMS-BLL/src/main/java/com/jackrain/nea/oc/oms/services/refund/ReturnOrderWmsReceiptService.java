package com.jackrain.nea.oc.oms.services.refund;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.WmsWithdrawalState;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.services.OmsReturnOrderService;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.utility.LogUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Optional;

/**
 * 零售退传WMS回执服务
 *
 * @author: 秦雄飞
 * @time: 2021/12/30 17:43 下午
 * @description:
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ReturnOrderWmsReceiptService {

    private final OmsReturnOrderService omsReturnOrderService;

    private final OcBReturnOrderMapper ocBReturnOrderMapper;

    private static final int MAX_REMARK_LENGTH = 200;

    public boolean consume(String messageBody) {

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("零售退传WMS回执服务：{}",
                    "零售退传WMS回执服务"), JSON.toJSONString(messageBody));
        }
        JSONObject object = JSONObject.parseObject(messageBody);
        JSONArray data = object.getJSONArray("data");
        Long id = null;
        for (int i = 0; i < data.size(); i++) {
            try {
                String code = data.getJSONObject(i).getString("code");
                String billNo = data.getJSONObject(i).getString("orderNo");
                if (StringUtils.isBlank(billNo)) {
                    log.error(LogUtil.format("RefundOrderToWmsReceiptService回执处理.orderNo为空"));
                    continue;
                }
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("RefundOrderToWmsReceiptService回执处理.code:{},orderNo:{}", billNo), code, billNo);
                }
                OcBReturnOrder ocBReturnOrder = ocBReturnOrderMapper.selectByBillNo(billNo);
                id = ocBReturnOrder.getId();
                //WMS单据编号
                String wmsBillCode = data.getJSONObject(i).getString("returnOrderId");
                OcBReturnOrder update = new OcBReturnOrder();
                if (ocBReturnOrder != null && ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal().equals(ocBReturnOrder.getReturnStatus())
                        && WmsWithdrawalState.PASS.toInteger().equals(ocBReturnOrder.getIsTowms())) {
                    update.setId(id);
                    if ("0".equals(code)) {
                        update.setIsTowms(WmsWithdrawalState.YES.toInteger());
                        update.setModifieddate(new Date());
                        update.setWmsBillNo(wmsBillCode);
                        ocBReturnOrderMapper.updateById(update);
                        omsReturnOrderService.saveAddOrderReturnLog(id,
                                "传WMS成功", "退单传WMS", SystemUserResource.getRootUser());
                    } else {
                        update.setIsTowms(WmsWithdrawalState.FAIL.toInteger());
                        String failReason = data.getJSONObject(i).getString("message");
                        failReason = subStringMsg(failReason);
                        update.setWmsFailreason(failReason);
                        update.setModifieddate(new Date());
                        //wms失败次数+1
                        update.setQtyWmsFail(Optional.ofNullable(ocBReturnOrder.getQtyWmsFail()).orElse(0L) + 1);
                        ocBReturnOrderMapper.updateById(update);
                        omsReturnOrderService.saveAddOrderReturnLog(id,
                                String.format("退货单传WMS失败,原因：%s", failReason), "退单传WMS", SystemUserResource.getRootUser());
                    }
                }
            } catch (Exception e) {
                omsReturnOrderService.saveAddOrderReturnLog(id,
                        String.format("退货单传WMS失败,原因：%s", subStringMsg(e.getMessage())), "退单传WMS", SystemUserResource.getRootUser());
                return false;
            }
        }
        return true;
    }

    private String subStringMsg(String msg) {
        return msg == null ? StringUtils.EMPTY : (msg.length() > MAX_REMARK_LENGTH ? msg.substring(0, MAX_REMARK_LENGTH - 1) : msg);
    }
}
