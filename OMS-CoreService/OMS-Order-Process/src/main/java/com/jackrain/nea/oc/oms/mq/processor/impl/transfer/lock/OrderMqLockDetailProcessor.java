package com.jackrain.nea.oc.oms.mq.processor.impl.transfer.lock;

import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.ErrorLogType;
import com.jackrain.nea.oc.oms.model.enums.OperateType;
import com.jackrain.nea.oc.oms.model.enums.OrderType;
import com.jackrain.nea.oc.oms.model.relation.IpOrderLockRelation;
import com.jackrain.nea.oc.oms.mq.processor.IMqOrderDetailProcessor;
import com.jackrain.nea.oc.oms.process.transfer.impl.lock.lockorder.OrderLockProcessImpl;
import com.jackrain.nea.oc.oms.services.IpOrderLockService;
import com.jackrain.nea.oc.oms.step.ProcessStepResultList;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Descroption 淘宝锁单消息处理器
 * <AUTHOR>
 * @Date 2019/10/10 10:30
 */
@Slf4j
public class OrderMqLockDetailProcessor implements IMqOrderDetailProcessor {
    @Autowired
    protected IpOrderLockService ipOrderLockService;
    @Autowired
    private OrderLockProcessImpl orderLockProcessImpl;

    @Override
    public ProcessStepResultList start(OperateOrderMqInfo orderMqInfo) {
        String orderNo = orderMqInfo.getOrderNo();
        Long orderId = orderMqInfo.getOrderId();
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Start MqOrder.TaobaoLock", orderNo,orderId));
        }
        IpOrderLockRelation ipOrderLockRelation = ipOrderLockService.getLockRelation(orderId);
        if (ipOrderLockRelation == null) {
            String errorMessage = Resources.getMessage
                    ("TaobaoLock Received OrderMqInfo Not Exist!OrderNo=" + orderNo);
            log.error(LogUtil.format(errorMessage));
            return new ProcessStepResultList();
        } else {
            ProcessStepResultList resultList = orderLockProcessImpl.start(ipOrderLockRelation,
                    false, SystemUserResource.getRootUser());
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("Finished.MqOrder.Lock OrderNo,Result= {}", orderNo), resultList);
            }
            return resultList;
        }
    }

    @Override
    public boolean checkMqIsCanExecute(OperateOrderMqInfo orderMqInfo) {
        return orderMqInfo.getOperateType() == OperateType.TRANSFER_ORDER
                && orderMqInfo.getChannelType() == ChannelType.TAOBAO
                && orderMqInfo.getOrderType() == OrderType.LOCKORDER;
    }
}
