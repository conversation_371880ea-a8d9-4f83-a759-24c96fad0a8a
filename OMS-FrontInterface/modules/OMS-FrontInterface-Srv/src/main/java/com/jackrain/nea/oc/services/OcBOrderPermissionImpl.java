package com.jackrain.nea.oc.services;

import com.jackrain.nea.oc.oms.api.OcBOrderPermissionCmd;
import com.jackrain.nea.oc.oms.model.relation.UserPermission;
import com.jackrain.nea.oc.oms.permission.OcOrderAuthorityMgtService;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: xiWen.z
 * create at: 2019/8/26 0026
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBOrderPermissionImpl implements OcBOrderPermissionCmd {
    @Autowired
    private OcOrderAuthorityMgtService ocOrderAuthorityMgtService;


    @Override
    public UserPermission getCurrentUserPermission(UserPermission pem, String tn, User usr) {
        return ocOrderAuthorityMgtService.getCurrentUserPermission(pem, tn, usr);
    }

}
