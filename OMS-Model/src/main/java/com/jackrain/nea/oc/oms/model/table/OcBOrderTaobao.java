package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.util.Date;

@TableName(value = "oc_b_order_taobao")
@Data
@Document(index = "oc_b_order", type = "oc_b_order_taobao")
public class OcBOrderTaobao extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "IS_ALLOCATEXP")
    @Field(type = FieldType.Integer)
    private Integer isAllocatexp;

    @JSONField(name = "IS_ACCEPTXP")
    @Field(type = FieldType.Integer)
    private Integer isAcceptxp;

    @JSONField(name = "THREE_PL_FLAG")
    @Field(type = FieldType.Integer)
    private Integer threePlFlag;

    @JSONField(name = "CLOUD_STORE")
    @Field(type = FieldType.Keyword)
    private String cloudStore;

    @JSONField(name = "O2O_SHOPID")
    @Field(type = FieldType.Keyword)
    private String o2oShopid;

    @JSONField(name = "O2O_SHOP_NAME")
    @Field(type = FieldType.Keyword)
    private String o2oShopName;

    @JSONField(name = "ORDER_TAKING")
    @Field(type = FieldType.Integer)
    private Integer orderTaking;

    @JSONField(name = "IS_STOREONESELF")
    @Field(type = FieldType.Integer)
    private Integer isStoreoneself;

    @JSONField(name = "CP_C_SHOP_ID")
    @Field(type = FieldType.Long)
    private Long cpCShopId;

    @JSONField(name = "CP_C_SHOP_TITLE")
    @Field(type = FieldType.Keyword)
    private String cpCShopTitle;

    @JSONField(name = "IS_TMALL_DELIVERY")
    @Field(type = FieldType.Integer)
    private Integer isTmallDelivery;

    @JSONField(name = "THREE_PL_TIMING_FLAG")
    @Field(type = FieldType.Integer)
    private Integer threePlTimingFlag;

    @JSONField(name = "CNSERVICE")
    @Field(type = FieldType.Integer)
    private Integer cnservice;

    @JSONField(name = "OS_DATE")
    @Field(type = FieldType.Long)
    private Date osDate;

    @JSONField(name = "ES_DATE")
    @Field(type = FieldType.Long)
    private Date esDate;

    @JSONField(name = "OS_RANGE")
    @Field(type = FieldType.Long)
    private Date osRange;

    @JSONField(name = "ES_RANGE")
    @Field(type = FieldType.Long)
    private Date esRange;

    @JSONField(name = "CUTOFF_MINUTES")
    @Field(type = FieldType.Keyword)
    private String cutoffMinutes;

    @JSONField(name = "ES_TIME")
    @Field(type = FieldType.Integer)
    private Integer esTime;

    @JSONField(name = "DELIVERY_TIME")
    @Field(type = FieldType.Long)
    private Date deliveryTime;

    @JSONField(name = "COLLECT_TIME")
    @Field(type = FieldType.Long)
    private Date collectTime;

    @JSONField(name = "SEND_TIME")
    @Field(type = FieldType.Long)
    private Date sendTime;

    @JSONField(name = "SIGN_TIME")
    @Field(type = FieldType.Long)
    private Date signTime;

    @JSONField(name = "DELIVERY_CPS")
    @Field(type = FieldType.Keyword)
    private String deliveryCps;

    @JSONField(name = "STORE_CODE")
    @Field(type = FieldType.Keyword)
    private String storeCode;

    @JSONField(name = "GATHER_DEST_CENTER")
    @Field(type = FieldType.Keyword)
    private String gatherDestCenter;

    @JSONField(name = "GATHER_DEST_STATION")
    @Field(type = FieldType.Keyword)
    private String gatherDestStation;

    @JSONField(name = "OC_B_ORDER_ID")
    @Field(type = FieldType.Long)
    private Long ocBOrderId;

    @JSONField(name = "VERSION")
    @Field(type = FieldType.Long)
    private Long version;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;
}