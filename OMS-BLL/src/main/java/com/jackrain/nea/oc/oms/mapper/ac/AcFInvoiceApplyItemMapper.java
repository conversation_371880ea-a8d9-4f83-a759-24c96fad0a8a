package com.jackrain.nea.oc.oms.mapper.ac;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.AcFInvoiceApplyItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface AcFInvoiceApplyItemMapper extends ExtentionMapper<AcFInvoiceApplyItem> {

    @Select("select tid from ac_f_invoice_apply_item where AC_F_INVOICE_APPLY_ID = #{id}")
    List<String> selectTidsByApplyId(@Param("id") Long id);

    @Select("select * from ac_f_invoice_apply_item where AC_F_INVOICE_APPLY_ID = #{id}")
    List<AcFInvoiceApplyItem> selectByApplyId(@Param("id") Long id);

    @Select("select * from ac_f_invoice_apply_item where tid = #{tid}")
    AcFInvoiceApplyItem selectByTid(@Param("tid") String tid);
}