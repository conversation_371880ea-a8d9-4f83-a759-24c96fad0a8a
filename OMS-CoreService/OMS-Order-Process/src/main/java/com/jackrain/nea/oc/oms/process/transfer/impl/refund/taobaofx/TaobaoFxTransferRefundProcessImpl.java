package com.jackrain.nea.oc.oms.process.transfer.impl.refund.taobaofx;

import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.MakeupOrderType;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoFxRefundRelation;
import com.jackrain.nea.oc.oms.process.AbstractOrderProcess;
import com.jackrain.nea.oc.oms.util.LockOrderType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> 周琳胜
 * create at:  19/7/15  16:32
 * @description: 淘宝分销退单中间表转换到退换货订单服务
 */
@Component
public class TaobaoFxTransferRefundProcessImpl extends AbstractOrderProcess<IpTaobaoFxRefundRelation> {
    public TaobaoFxTransferRefundProcessImpl() {
        super();
    }

    @Override
    protected String getChildPackageName() {
        return "taobaofx";
    }

    @Override
    protected long getProcessOrderId(IpTaobaoFxRefundRelation orderInfo) {
        return orderInfo.getOrderId();
    }

    @Override
    protected String getProcessOrderNo(IpTaobaoFxRefundRelation orderInfo) {
        return orderInfo.getOrderNo();
    }

    @Override
    protected LockOrderType getCurrentLockOrderType() {
        return LockOrderType.TRANSFER_FX_TAOBAO_REFUND;
    }

    @Override
    protected ChannelType getCurrentChannelType() {
        return ChannelType.TAOBAOFX;
    }

    @Override
    protected MakeupOrderType getCurrentMakeupOrderType() {
        return MakeupOrderType.TRANSFER_ORDER;
    }
}
