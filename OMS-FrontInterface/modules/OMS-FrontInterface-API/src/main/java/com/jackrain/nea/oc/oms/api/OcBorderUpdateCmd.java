package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

/**
 * @author: 孙俊磊
 * @since: 2019-03-13
 * create at:  2019-03-13 15:05
 * 订单单对象-更新
 */
public interface OcBorderUpdateCmd {

    /**
     * 更新收货地址
     *
     * @param param     更新的内容json
     * @param loginUser User信息
     * @return valueHolder
     */
    ValueHolderV14 updateReceiveAddress(String param, User loginUser) throws NDSException;


    /**
     * 参考一商修改地址代码重构
     *
     * @param param     更新的内容json
     * @param loginUser User信息
     * @return valueHolder
     */
    ValueHolderV14 updateReceiveAddressNew(String param, User loginUser) throws NDSException;
}
