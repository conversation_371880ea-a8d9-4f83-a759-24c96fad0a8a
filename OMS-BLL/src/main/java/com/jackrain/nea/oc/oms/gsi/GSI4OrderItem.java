package com.jackrain.nea.oc.oms.gsi;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.util.ApplicationContextHandle;

import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2021/1/7 上午11:04
 * @Version 1.0
 */
public class GSI4OrderItem {

    private static OcBOrderItemMapper ocBOrderItemMapper;



    public static List<Long> selectOcBOrderItemByTid(String tid){
        return orderMapper().selectOcBOrderIdByTid(tid);
    }



    private static OcBOrderItemMapper orderMapper() {

        if (ocBOrderItemMapper == null) {
            ocBOrderItemMapper = ApplicationContextHandle.getBean(OcBOrderItemMapper.class);
            if (ocBOrderItemMapper == null) {
                throw new NDSException("OcBOrderMapper Not Found In Class GSI4Order");
            }
        }
        return ocBOrderItemMapper;
    }

    private GSI4OrderItem() {
    }

}
