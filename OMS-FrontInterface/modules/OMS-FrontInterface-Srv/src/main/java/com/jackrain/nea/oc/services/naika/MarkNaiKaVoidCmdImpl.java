package com.jackrain.nea.oc.services.naika;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.api.naika.MarkNaiKaVoidCmd;
import com.jackrain.nea.oc.oms.mapper.OcBOrderNaiKaMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderNaikaVoidMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnAfSendMapper;
import com.jackrain.nea.oc.oms.model.enums.CardAutoVoidEnum;
import com.jackrain.nea.oc.oms.model.enums.NaikaVoidStatusEnum;
import com.jackrain.nea.oc.oms.model.table.OcBOrderNaikaVoid;
import com.jackrain.nea.oc.oms.model.table.OcBReturnAfSend;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * @ClassName MarkNaiKaVoidCmdImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/3/7 15:03
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class MarkNaiKaVoidCmdImpl extends CommandAdapter implements MarkNaiKaVoidCmd {

    @Autowired
    private OcBReturnAfSendMapper afSendMapper;
    @Autowired
    private OcBOrderNaiKaMapper ocBOrderNaiKaMapper;
    @Autowired
    private OcBOrderNaikaVoidMapper naikaVoidMapper;

    @Override
    public ValueHolder execute(QuerySession querySession) {
        ValueHolder vh = new ValueHolder();
        vh.put("code", ResultCode.SUCCESS);
        vh.put("message", "success");

        DefaultWebEvent event = querySession.getEvent();
        User user = querySession.getUser();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        JSONArray jsonArray = JSON.parseArray(param.get("ids").toString());
        if (CollectionUtil.isEmpty(jsonArray)) {
            vh.put("code", ResultCode.FAIL);
            vh.put("message", "请选择需要操作的单据数据！");
            return vh;
        }
        StringBuilder sb = new StringBuilder();
        for (Object object : jsonArray) {
            Long id = Long.valueOf(object.toString());
            OcBReturnAfSend ocBReturnAfSend = afSendMapper.selectById(id);
            if (ObjectUtil.isNull(ocBReturnAfSend)) {
                sb.append(id);
                sb.append("单据已不存在");
                continue;
            }
            if (ObjectUtil.equal(ocBReturnAfSend.getCardAutoVoid(), CardAutoVoidEnum.VOID_SUCCESS.getCode())) {
                sb.append(id);
                sb.append("退款自动作废状态为作废成功，不允许操作！");
                continue;
            }
            OcBReturnAfSend updateOcBReturnAfSend = new OcBReturnAfSend();
            // 修改已发货退款单
            updateOcBReturnAfSend.setId(id);
            updateOcBReturnAfSend.setCardAutoVoid(CardAutoVoidEnum.VOID_SUCCESS.getCode());
            updateOcBReturnAfSend.setModifierid(Long.valueOf(user.getId()));
            updateOcBReturnAfSend.setModifieddate(new Date());
            updateOcBReturnAfSend.setModifiername(user.getName());
            afSendMapper.updateById(updateOcBReturnAfSend);


            List<OcBOrderNaikaVoid> ocBOrderNaikaVoidList = naikaVoidMapper.getByAfReturnId(id);
            if (CollectionUtil.isEmpty(ocBOrderNaikaVoidList)) {
                continue;
            }
            OcBOrderNaikaVoid ocBOrderNaikaVoid = ocBOrderNaikaVoidList.get(0);
            OcBOrderNaikaVoid updateNaikaVoid = new OcBOrderNaikaVoid();

            // 修改作废订单、作废状态为 作废成功
            updateNaikaVoid.setVoidStatus(NaikaVoidStatusEnum.VOID_SUCCESS.getStatus());
            updateNaikaVoid.setId(ocBOrderNaikaVoid.getId());
            updateNaikaVoid.setModifierid(Long.valueOf(user.getId()));
            updateNaikaVoid.setModifieddate(new Date());
            updateNaikaVoid.setModifiername(user.getName());
            naikaVoidMapper.updateById(updateNaikaVoid);

        }
        if (StringUtils.isNotEmpty(sb.toString())) {
            vh.put("code", ResultCode.FAIL);
            vh.put("message", sb.toString());
        } else {
            vh.put("code", ResultCode.SUCCESS);
            vh.put("message", "标记作废成功" + jsonArray.size() + "条！");
        }
        return vh;
    }
}
