package com.jackrain.nea.oc.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OrderLogSearchCmd;
import com.jackrain.nea.oc.oms.services.OrderLogSearchService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: 郑立轩
 * @since: 2019/4/16
 * create at : 2019/4/16 14:46
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OrderLogSearchCmdImpl extends CommandAdapter implements OrderLogSearchCmd {
    @Autowired
    private OrderLogSearchService searchService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return searchService.OrderLogSearch(session);
    }
}
