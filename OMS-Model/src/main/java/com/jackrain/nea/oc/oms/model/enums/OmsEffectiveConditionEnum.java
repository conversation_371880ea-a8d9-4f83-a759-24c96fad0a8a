package com.jackrain.nea.oc.oms.model.enums;

/**
 * 自动审核策略，生效条件
 * 1.付款时间 2.订单金额 3.收货省份 4.收货地址 5.买家留言 6.卖家备注 7.平台sku编码
 *
 * @author: 胡林洋
 * @since: 2019-03-26
 * create at : 2019-03-26 17:14
 */
public enum OmsEffectiveConditionEnum {

    /**
     * 付款时间
     */
    PAY_TIME,

    /**
     * 订单金额
     */
    ORDER_AMOUNT,

    /**
     * 收货省份
     */
    RECIEVE_PROVINCE,

    /**
     * 收货地址
     */
    RECIEVE_ADRESS,

    /**
     * 买家留言
     */
    BUYER_REMARK,

    /**
     * 卖家备注
     */
    SELLER_REMARK,

    /**
     * 平台sku编码
     */
    PLATFORM_SKUCODE,

    /**
     * 订单折扣
     */
    ORDER_DISCOUNT,
    /**
     * 单条码数量
     */
    SINGLE_SKU_NUM,
    /**
     * 自定义标签档案
     */
    CUSTOM_LABEL
    ;

    /**
     * 转换成数字内容
     *
     * @return 数字内容
     */
    public String parseValue() {
        if (this == PAY_TIME) {
            return "1";
        } else if (this == ORDER_AMOUNT) {
            return "2";
        } else if (this == RECIEVE_PROVINCE) {
            return "3";
        } else if (this == RECIEVE_ADRESS) {
            return "4";
        } else if (this == BUYER_REMARK) {
            return "5";
        } else if (this == SELLER_REMARK) {
            return "6";
        } else if (this == PLATFORM_SKUCODE) {
            return "7";
        } else if (this == ORDER_DISCOUNT) {
            return "8";
        }else if (this == SINGLE_SKU_NUM) {
            return "9";
        }else if (this == CUSTOM_LABEL) {
            return "10";
        }else {
            return "0";
        }
    }

}
