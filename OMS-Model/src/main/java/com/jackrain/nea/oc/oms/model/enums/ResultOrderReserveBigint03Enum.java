package com.jackrain.nea.oc.oms.model.enums;


/**
 * 是否传wms
 *
 * @author: ming.fz
 * create at: 2019/9/18
 */
public enum ResultOrderReserveBigint03Enum {


    Y("Y", 1L),
    N("N", 0L);

    String key;
    Long val;

    ResultOrderReserveBigint03Enum(String k, Long v) {
        this.key = k;
        this.val = v;
    }

    public String getKey() {
        return key;
    }

    public Long getVal() {
        return val;
    }


}


