package com.jackrain.nea.oc.oms.services;

import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.api.StCCycleStrategyDelCmd;
import com.jackrain.nea.oc.oms.mapper.OcBOperationLogMapper;
import com.jackrain.nea.oc.oms.mapper.StCCycleItemStrategyMapper;
import com.jackrain.nea.oc.oms.mapper.StCCycleRuleStrategyMapper;
import com.jackrain.nea.oc.oms.mapper.StCCycleStrategyMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOperationLog;
import com.jackrain.nea.oc.oms.model.table.StCCycleItemStrategy;
import com.jackrain.nea.oc.oms.model.table.StCCycleRuleStrategy;
import com.jackrain.nea.oc.oms.nums.StConstant;
import com.jackrain.nea.st.model.enums.OperationTypeEnum;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.util.StBeanUtils;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

/**
 * @ClassName StCCycleStrategyDelCmdImpl
 * @Description 周期购策略删除
 * <AUTHOR>
 * @Date 2024/8/20 11:09
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oc-core")
public class StCCycleStrategyDelCmdImpl implements StCCycleStrategyDelCmd {
    @Autowired
    private StCCycleStrategyMapper strategyMapper;
    @Autowired
    private StCCycleRuleStrategyMapper ruleStrategyMapper;
    @Autowired
    private StCCycleItemStrategyMapper itemStrategyMapper;
    @Resource
    private OcBOperationLogMapper operationLogMapper;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        ValueHolder valueHolder = new ValueHolder();
        DefaultWebEvent event = session.getEvent();
        User user = session.getUser();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        JSONObject tabItem = param.getJSONObject("tabitem");
        Long objid = param.getLong("objid");
        //判断是不是只删除子表
        if (Objects.nonNull(tabItem)
                && (CollectionUtils.isNotEmpty(tabItem.getJSONArray("ST_C_CYCLE_RULE_STRATEGY"))
                || CollectionUtils.isNotEmpty(tabItem.getJSONArray("ST_C_CYCLE_ITEM_STRATEGY"))
        )) {
            // 删除的是子表
            JSONArray ruleList = tabItem.getJSONArray("ST_C_CYCLE_RULE_STRATEGY");
            JSONArray itemList = tabItem.getJSONArray("ST_C_CYCLE_ITEM_STRATEGY");
            List<OcBOperationLog> operationLogList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(ruleList)) {
                for (int i = 0; i < ruleList.size(); i++) {
                    Long ruleId = ruleList.getLong(i);
                    StCCycleRuleStrategy ruleStrategy = ruleStrategyMapper.selectById(ruleId);
                    if (Objects.nonNull(ruleId)) {
                        //删除规则
                        ruleStrategyMapper.deleteById(ruleId);
                    }
                    OcBOperationLog operationLog = buildRuleOperationLog(ruleStrategy, user);
                    operationLogList.add(operationLog);
                }
            }
            if (CollectionUtils.isNotEmpty(itemList)) {
                for (int i = 0; i < itemList.size(); i++) {
                    Long itemId = itemList.getLong(i);
                    StCCycleItemStrategy itemStrategy = itemStrategyMapper.selectById(itemId);
                    if (Objects.nonNull(itemId)) {
                        //删除规则
                        itemStrategyMapper.deleteById(itemId);
                    }
                    OcBOperationLog operationLog = buildItemOperationLog(itemStrategy, user);
                    operationLogList.add(operationLog);
                }
            }
            // 如果operationLogList不为空 则批量新增
            if (CollectionUtils.isNotEmpty(operationLogList)) {
                operationLogMapper.batchInsert(operationLogList);
            }
        } else {
            strategyMapper.deleteById(objid);
            ruleStrategyMapper.deleteByStrategyId(objid);
            itemStrategyMapper.deleteByStrategyId(objid);
        }
        valueHolder.put("code", ResultCode.SUCCESS);
        valueHolder.put("msg", "删除成功");
        return valueHolder;
    }

    private OcBOperationLog buildRuleOperationLog(StCCycleRuleStrategy ruleStrategy, User user) {
        StringBuilder beforeValue = new StringBuilder();
        beforeValue.setLength(0);
        beforeValue.append("[").append(ruleStrategy.getRuleType() == 1 ? "平台商品ID" : "SKU").append("],[").append(ruleStrategy.getRuleContent())
                .append("]");
        OcBOperationLog operationLog = getOperationLog("ST_C_CYCLE_RULE_STRATEGY", "DEL", ruleStrategy.getStrategyId(),
                "周期购规则", "删除周期购规则", beforeValue.toString(), "删除", user);
        return operationLog;
    }

    private OcBOperationLog buildItemOperationLog(StCCycleItemStrategy itemStrategy, User user) {
        StringBuilder beforeValue = new StringBuilder();
        beforeValue.setLength(0);
        beforeValue.append("[").append(itemStrategy.getSkuCode()).append("],[").append("期数:" + itemStrategy.getCycleNum())
                .append("],[").append("数量:" + itemStrategy.getQty()).append("],[").append("拆单方式:" + (itemStrategy.getSplitType() == 1 ? "不拆单" : "拆单")).append("]");
        OcBOperationLog operationLog = getOperationLog("ST_C_CYCLE_ITEM_STRATEGY", "DEL", itemStrategy.getStrategyId(),
                "周期购赠品明细", "删除周期购赠品明细", beforeValue.toString(), "删除", user);
        return operationLog;
    }

    /**
     * 获取操作日志对象
     *
     * @param tableName
     * @param operationType
     * @param updateId
     * @param tableDescription
     * @param columnName
     * @param columnBeforeValue
     * @param columnAfterValue
     * @param user
     * @return
     */
    private OcBOperationLog getOperationLog(String tableName, String operationType, Long updateId,
                                            String tableDescription, String columnName, String columnBeforeValue,
                                            String columnAfterValue, User user) {
        OcBOperationLog operationLog = new OcBOperationLog();
        operationLog.setId(ModelUtil.getSequence(StConstant.TAB_OC_B_OPERATION_LOG));
        operationLog.setTableName(tableName);
        operationLog.setOperationType(OperationTypeEnum.getNameByValue(operationType));
        operationLog.setUpdateId(updateId);
        operationLog.setUpdateModelName(tableDescription);
        operationLog.setModContent(columnName);
        operationLog.setBeforeData(columnBeforeValue);
        operationLog.setAfterData(columnAfterValue);
        StBeanUtils.makeCreateField(operationLog, user);
        return operationLog;
    }

    @Override
    public ValueHolder execute(HashMap map) throws NDSException {
        return null;
    }
}
