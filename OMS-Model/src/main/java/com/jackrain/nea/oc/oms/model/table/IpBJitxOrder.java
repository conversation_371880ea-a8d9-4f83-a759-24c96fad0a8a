package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@TableName(value = "ip_b_jitx_order")
@Data
@Document(index = "ip_b_jitx_order", type = "ip_b_jitx_order")
@ApiModel("jitx订单")
public class IpBJitxOrder extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "ORDER_SN")
    @Field(type = FieldType.Keyword)
    private String orderSn;

    @JSONField(name = "DELIVERY_WAREHOUSE")
    @Field(type = FieldType.Keyword)
    private String deliveryWarehouse;

    @JSONField(name = "ADD_TIME")
    @Field(type = FieldType.Long)
    private Date addTime;

    @JSONField(name = "BUYER")
    @Field(type = FieldType.Keyword)
    private String buyer;

    @JSONField(name = "BUYER_ADDRESS")
    @Field(type = FieldType.Keyword)
    private String buyerAddress;

    @JSONField(name = "BUYER_MOBILE")
    @Field(type = FieldType.Keyword)
    private String buyerMobile;

    @JSONField(name = "BUYER_TEL")
    @Field(type = FieldType.Keyword)
    private String buyerTel;

    @JSONField(name = "BUYER_POSTCODE")
    @Field(type = FieldType.Keyword)
    private String buyerPostcode;

    @JSONField(name = "BUYER_CITY")
    @Field(type = FieldType.Keyword)
    private String buyerCity;

    @JSONField(name = "BUYER_PROVINCE")
    @Field(type = FieldType.Keyword)
    private String buyerProvince;

    @JSONField(name = "BUYER_COUNTY")
    @Field(type = FieldType.Keyword)
    private String buyerCounty;

    @JSONField(name = "BUYER_COUNTRY_ID")
    @Field(type = FieldType.Keyword)
    private String buyerCountryId;

    @JSONField(name = "PAY_TYPE")
    @Field(type = FieldType.Keyword)
    private String payType;

    @JSONField(name = "COD_TYPE")
    @Field(type = FieldType.Keyword)
    private String codType;

    @JSONField(name = "COD_MONEY")
    @Field(type = FieldType.Double)
    private BigDecimal codMoney;

    @JSONField(name = "REMARK")
    @Field(type = FieldType.Keyword)
    private String remark;

    @JSONField(name = "TRANSPORT_TIME")
    @Field(type = FieldType.Keyword)
    private String transportTime;

    @JSONField(name = "TRANSPORT_DAY")
    @Field(type = FieldType.Keyword)
    private String transportDay;

    @JSONField(name = "VENDOR_ID")
    @Field(type = FieldType.Keyword)
    private String vendorId;

    @JSONField(name = "VENDOR_NAME")
    @Field(type = FieldType.Keyword)
    private String vendorName;

    @JSONField(name = "TRANSPORT_NO")
    @Field(type = FieldType.Keyword)
    private String transportNo;

    @JSONField(name = "CARRIER_CODE")
    @Field(type = FieldType.Keyword)
    private String carrierCode;

    @JSONField(name = "CARRIER_NAME")
    @Field(type = FieldType.Keyword)
    private String carrierName;

    @JSONField(name = "ORDER_STATUS")
    @Field(type = FieldType.Keyword)
    private String orderStatus;

    @JSONField(name = "LAST_UPDATE_TIME")
    @Field(type = FieldType.Long)
    private Date lastUpdateTime;

    @JSONField(name = "SELLERNICK")
    @Field(type = FieldType.Keyword)
    private String sellernick;

    @JSONField(name = "CP_C_SHOP_ID")
    @Field(type = FieldType.Long)
    private Long cpCShopId;

    @JSONField(name = "SYSREMARK")
    @Field(type = FieldType.Keyword)
    private String sysremark;

    @JSONField(name = "ISTRANS")
    @Field(type = FieldType.Integer)
    private Integer istrans;

    @JSONField(name = "TRANSDATE")
    @Field(type = FieldType.Long)
    private Date transdate;

    @JSONField(name = "TRANS_COUNT")
    @Field(type = FieldType.Long)
    private Long transCount;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;

    @JSONField(name = "CHANGE_ADDR_STATUS")
    @Field(type = FieldType.Integer)
    private Integer changeAddrStatus;

    @ApiModelProperty(value = "合包码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "MERGED_CODE")
    private String mergedCode;

    @ApiModelProperty(value = "合包单号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "MERGED_SN")
    private String mergedSn;

    @ApiModelProperty(value = "是否可发货，0=可发货 1=不可发货")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_FORBIDDEN_DELIVERY")
    private Integer isForbiddenDelivery;

    @ApiModelProperty(value = "发货考核开始时间 格式：yyyy-MM-dd HH:mm:ss")
    @JSONField(name = "DELIVERY_KPI_START_TIME")
    @Field(type = FieldType.Long)
    private Date deliveryKpiStartTime;

    @ApiModelProperty(value = "订单类型 1=普通JITX，2=换货JITX，3=预付JITX，4=揽换JITX，5=补寄JITX")
    @Field(type = FieldType.Integer)
    @JSONField(name = "ORDER_TYPE")
    private Integer orderType;

    @JSONField(name = "IS_STORE_DELIVERY")
    @Field(type = FieldType.Integer)
    private Integer isStoreDelivery;

    @ApiModelProperty(value = "补寄订单对应的原始单号")
    @JSONField(name = "OLD_ORDER_SN")
    @Field(type = FieldType.Keyword)
    private String oldOrderSn;
}