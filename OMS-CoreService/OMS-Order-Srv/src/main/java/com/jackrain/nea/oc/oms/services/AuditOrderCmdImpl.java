package com.jackrain.nea.oc.oms.services;

import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.api.AuditOrderCmd;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.request.AuditOrderRequest;
import com.jackrain.nea.oc.oms.process.audit.OrderAuditProcess;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.ProcessStepResultList;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * @Auther: 黄志优
 * @Date: 2020/11/4 13:57
 * @Description:
 */
@Service(protocol = "dubbo", validation = "true", group = "oc-core", version = "1.0")
@Slf4j
public class AuditOrderCmdImpl implements AuditOrderCmd {

    @Autowired
    private OrderAuditProcess orderAuditProcess;

    @Autowired
    private OmsOrderService omsOrderService;

    @Override
    public ValueHolderV14 auditOrder(AuditOrderRequest auditOrderRequest) {

        ValueHolderV14 resultValueHolderV14 = new ValueHolderV14<>();

        try {
            boolean hasError = false;
            int failedNumber = 0;
            int successNumber = 0;
            List<ProcessStepResult> errorStepResultList = new ArrayList<>();
            for (Long orderId : auditOrderRequest.getOrderIdList()) {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("auditOrder,ChannelType：{}", orderId, "auditOrder"), auditOrderRequest.getChannelType());
                }

                OcBOrderRelation orderRelation = omsOrderService.selectOmsOrderInfo(orderId);

                if (orderRelation == null) {
                    hasError = false;
                    failedNumber++;
                    continue;
                }

                ProcessStepResultList processStepResultList = orderAuditProcess.start(orderRelation,
                        false, auditOrderRequest.getOperateUser());

                if (processStepResultList.isProcessSuccess()) {
                    successNumber++;
                } else {
                    hasError = true;
                    errorStepResultList.add(processStepResultList.getLastFailedProcessStepResult());
                    failedNumber++;
                }
            }

            if (hasError) {
                resultValueHolderV14.setCode(ResultCode.FAIL);
                StringBuilder sbMessage = new StringBuilder();
                sbMessage.append(String.format("审单成功%s条；审单失败%s条；失败原因：\r\n", successNumber, failedNumber));
                for (ProcessStepResult stepResult : errorStepResultList) {
                    if (stepResult != null) {
                        sbMessage.append(stepResult.getMessage());
                        sbMessage.append("\r\n");
                    }
                }
                resultValueHolderV14.setMessage(sbMessage.toString());
            } else {
                resultValueHolderV14.setCode(ResultCode.SUCCESS);
                resultValueHolderV14.setMessage("审单全部成功。");
            }
        } catch (Exception e) {
            resultValueHolderV14.setCode(ResultCode.FAIL);
            resultValueHolderV14.setMessage("审单异常：" + e.getMessage());
            log.error(LogUtil.format("startTransferOrder,异常信息:{}", "startTransferOrder"), Throwables.getStackTraceAsString(e));

        }


        return resultValueHolderV14;
    }
}
