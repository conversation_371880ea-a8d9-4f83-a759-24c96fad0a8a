package com.jackrain.nea.oc.oms.process.transfer.impl.refund.jingdong.util;

import lombok.Getter;

/**
 * @Author: 黄世新
 * @Date: 2020/12/29 下午2:46
 * @Version 1.0
 */
public class JdRefundUtil {

    public enum RefundStatus{
        /**
         *
         */
        NOT_REVIEWED("待审核"),

        EXAMINE_CLOSE("审核关闭"),

        TO_BE_RECEIVED("待收货"),

        PENDING("待处理"),

        TO_BE_CONFIRMED("待用户确认"),

        COMPLETE("完成"),

        CANCEL("取消");

        RefundStatus(String code) {
            this.code = code;
        }

        @Getter
        private String code;

    }
}
