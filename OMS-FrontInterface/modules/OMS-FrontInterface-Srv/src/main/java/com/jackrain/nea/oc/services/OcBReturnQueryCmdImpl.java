package com.jackrain.nea.oc.services;

import com.jackrain.nea.oc.oms.api.OcBReturnQueryCmd;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.services.OcBReturnQueryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: 汪聿森
 * @Date: Created in 2019-09-17 14:21
 * @Description : 退换货订单查询
 */
@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBReturnQueryCmdImpl implements OcBReturnQueryCmd {
    @Autowired
    private OcBReturnQueryService ocBReturnQueryService;

    @Override
    public OcBReturnOrder queryOcBReturnById(Long id) {
        return ocBReturnQueryService.queryById(id);
    }
}
