package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.api.KdzsCallBackCmd;
import com.jackrain.nea.oc.oms.model.request.kdzs.KdzsCallBackRequest;
import com.jackrain.nea.oc.oms.model.result.kdzs.KdzsCallBackResponse;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.MD5Util;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.UnsupportedEncodingException;
import java.util.Base64;

/**
 * 快递助手回调落库&揽收，签收 埋点
 *
 * <AUTHOR>
 * @date 2022年04月06日 16:40
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", group = "oc-core", version = "1.0")
public class KdzsCallBackCmdImpl implements KdzsCallBackCmd {

    @Autowired
    private KdzsCallBackService kdzsCallBackService;

    @Value("${return.order.logistic.subscribe.kdzs.appSecret:9A78378F353A45678F0B7628C9D236DB}")
    private String appSecret;

    @Value("${return.order.logistic.trace.kdzs.ignoreSign:false}")
    private boolean notIgnoreSign;

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public KdzsCallBackResponse kdzsCallBack(String dataDigest, String param) {
        if (log.isDebugEnabled()) {
            log.debug("KdzsCallBackCmdImpl.kdzsCallBack start dataDigest={},param:{}", dataDigest, param);
        }
        try {
            if (notIgnoreSign) {
                // 签名校验
                if (!checkSign(dataDigest, param)) {
                    log.info("签名校验不通过");
                    return KdzsCallBackResponse.builder(100, "接收失败，签名不通过");
                }
            }
            KdzsCallBackRequest callBackRequest = JSON.parseObject(param, KdzsCallBackRequest.class);
            ValueHolderV14 valueHolderV14 = kdzsCallBackService.saveAndUpdateLogisticsTrace(callBackRequest);

            if (ResultCode.SUCCESS == valueHolderV14.getCode()) {
                return KdzsCallBackResponse.builder(200, "接收成功");
            } else {
                return KdzsCallBackResponse.builder(100, valueHolderV14.getMessage());
            }
        } catch (Exception e) {
            log.error("新增物流轨迹异常,信息为：{}", Throwables.getStackTraceAsString(e));
            return KdzsCallBackResponse.builder(100, "接收失败");
        }
    }

    /**
     * 快递助手签名校验
     *
     * @param data
     * @return boolean
     * <AUTHOR>
     * @date 2022/4/7 11:46
     */
    private boolean checkSign(String dataDigest, String data) {
        if (StringUtils.isBlank(dataDigest) || data == null) {
            return false;
        }
        // appSecret 拼接jsonObject的JSON字符串
        StringBuffer signature = new StringBuffer();
        signature.append(appSecret);
        signature.append(data);
        signature.append(appSecret);
        // MD5加密
        String ciphertext = MD5Util.encryptByMD5(signature.toString());
        // base64编码
        String baseCiphertext;
        try {
            baseCiphertext = Base64.getEncoder().encodeToString(ciphertext.getBytes("utf-8"));
        } catch (UnsupportedEncodingException e) {
            log.debug(this.getClass().getName() + "，UnsupportedEncodingException:", e.getMessage());
            return false;
        }
        // 签名校验（header中取到dataDigest内容比对）
        if (StringUtils.isBlank(baseCiphertext) || !StringUtils.equals(baseCiphertext, dataDigest)) {
            if (log.isDebugEnabled()) {
                log.debug("快递助手请求的签名：{} | 中台生成的签名：{}", dataDigest, baseCiphertext);
            }
            return false;
        }
        return true;
    }

}
