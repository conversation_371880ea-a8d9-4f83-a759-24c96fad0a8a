package com.jackrain.nea.controller;

import com.burgeon.mq.core.DefaultProducerSend;
import com.burgeon.mq.model.MqSendResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @program: ryytn-oc-oms-v3.0
 * @description: mq 测试Controller
 * @author: haiyang
 * @create: 2023-12-28 14:13
 **/
@RestController
@RequestMapping("/p")
@Slf4j
public class TestMqController {

    @Autowired
    private DefaultProducerSend defaultProducerSend;

    @GetMapping("/mq/test/send")
    public void sendMq() {
        MqSendResult result = defaultProducerSend.sendTopic("R3_OC_OMS_CALL_REFUNDIN", "OperateRefundIn", "helloMq", null);
        log.info("testSendMqResult.success: {}", result.getMessageKey());
    }
}
