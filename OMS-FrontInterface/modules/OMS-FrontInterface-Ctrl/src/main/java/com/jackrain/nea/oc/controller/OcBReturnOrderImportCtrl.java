package com.jackrain.nea.oc.controller;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBReturnOrderImportCmd;
import com.jackrain.nea.oc.oms.extmodel.ExtOcBReturnOrder;
import com.jackrain.nea.oc.oms.extmodel.ExtOcBReturnOrderExchange;
import com.jackrain.nea.oc.oms.extmodel.ExtOcBReturnOrderRefund;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.InputStream;
import java.text.NumberFormat;
import java.util.*;

/**
 * @author: 李龙飞
 * @create: 2019-06-03 11:14
 **/
@RestController
@Slf4j
public class OcBReturnOrderImportCtrl {

    public static final String cellStr = "cell_";
    public static final String rowStr = "row_";
    //CellType
    public static final int _NONE = -1;
    public static final int NUMERIC = 0;
    public static final int STRING = 1;
    public static final int FORMULA = 2;
    public static final int BLANK = 3;
    public static final int BOOLEAN = 4;
    public static final int ERROR = 5;

    //导入excel,业务校验错误提示返回 错误excel地址
    public static final int IMPORT_ERROR_CODE = 10001;
    //导入excelche直接返回数据
    public static final int IMPORT_RESULT_DATA = 10002;

    private static NumberFormat nf = NumberFormat.getInstance();

    @Autowired
    private OcBReturnOrderImportCmd ocBReturnOrderImportCmd;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @ApiOperation(value = "退换货订单导入")
    @RequestMapping(path = "/api/cs/oc/oms/v1/importReturnOrder", method = RequestMethod.POST)
    public ValueHolderV14 importReturnOrder(HttpServletRequest request, @RequestParam(value = "file", required = true) MultipartFile file) {
        //获取当前登陆用户
//        User user = SystemUserResource.getRootUser();
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        if (file == null) {
            throw new NDSException(Resources.getMessage("请求参数不能为空!", user.getLocale()));
        }
        InputStream inputStream = null;
        ValueHolderV14 holderV14 = new ValueHolderV14();
        Map<String, InputStream> inputStreamMap = new HashMap<>();
        try {
            inputStream = file.getInputStream();
            inputStreamMap.put("inputStream", inputStream);
        } catch (Exception e) {
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("文件转换成流失败!");
            return holderV14;
        }

        Workbook hssfWorkbook = null;
        try {
            hssfWorkbook = new XSSFWorkbook(inputStream);
        } catch (Exception ex) {
            try {
                hssfWorkbook = new HSSFWorkbook(inputStream);
            } catch (Exception e) {
                holderV14.setCode(ResultCode.FAIL);
                return holderV14;
            }
        }

        int sheetNum = hssfWorkbook.getNumberOfSheets();
        if (sheetNum != 3) {
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("退换货订单导入模板不正确!");
            return holderV14;
        }
        List<ExtOcBReturnOrder> extOcBReturnOrderList = getOcBReturnOrderList(hssfWorkbook);
        if (CollectionUtils.isEmpty(extOcBReturnOrderList)) {
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("退换货订单导入模板主表数据不能为空!");
            return holderV14;
        }

        //处理退货明细表数据--->转换成退货明细对象
        List<ExtOcBReturnOrderRefund> extOcBReturnOrderRefundList = getOcBReturnOrderRefund(hssfWorkbook);
        if (CollectionUtils.isEmpty(extOcBReturnOrderRefundList)) {
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("退换货订单导入模板退货明细表数据不能为空!");
            return holderV14;
        }

        //处理换货明细表数据--->转换成换货明细对象
        List<ExtOcBReturnOrderExchange> extOcBReturnOrderExchangeList = getOcBReturnOrderExchange(hssfWorkbook);
        if (CollectionUtils.isEmpty(extOcBReturnOrderExchangeList)) {
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("退换货订单导入模板换货明细表数据不能为空!");
            return holderV14;
        }
        holderV14 = ocBReturnOrderImportCmd.importList(extOcBReturnOrderList, extOcBReturnOrderRefundList, extOcBReturnOrderExchangeList, user);
        return holderV14;

    }

    /**
     * 获取主表sheet数据，转换成主表对象
     */
    public List<ExtOcBReturnOrder> getOcBReturnOrderList(Workbook hssfWorkbook) {
        List<ExtOcBReturnOrder> ocBOrderList = Lists.newArrayList();
        List<Map<String, String>> execlList = Lists.newArrayList();
        try {
            execlList = readExcel(0, hssfWorkbook);
        } catch (Exception e) {

        }
        int index = 0;
        for (Map<String, String> columnMap : execlList) {
            ExtOcBReturnOrder ExtOcBReturnOrder = new ExtOcBReturnOrder();
            if (index == 0) {
                // 校验excel字段
                if (columnMap.size() != 25
                        || !"原始订单编号".equals(columnMap.get(rowStr + index + cellStr + 0))
                        || !"单据类型".equals(columnMap.get(rowStr + index + cellStr + 1))
                        || !"买家昵称".equals(columnMap.get(rowStr + index + cellStr + 2))
                        || !"原始平台单号".equals(columnMap.get(rowStr + index + cellStr + 3))
                        || !"店铺名称".equals(columnMap.get(rowStr + index + cellStr + 4))
                        || !"平台退款单号".equals(columnMap.get(rowStr + index + cellStr + 5))
                        || !"退回物流公司".equals(columnMap.get(rowStr + index + cellStr + 6))
                        || !"退款原因".equals(columnMap.get(rowStr + index + cellStr + 7))
                        || !"退回物流单号".equals(columnMap.get(rowStr + index + cellStr + 8))
                        || !"换货预留库存".equals(columnMap.get(rowStr + index + cellStr + 9))
                        || !"是否原退".equals(columnMap.get(rowStr + index + cellStr + 10))
                        || !"备注".equals(columnMap.get(rowStr + index + cellStr + 11))
                        || !"收货人".equals(columnMap.get(rowStr + index + cellStr + 12))
                        || !"收货人手机".equals(columnMap.get(rowStr + index + cellStr + 13))
                        || !"收货人电话".equals(columnMap.get(rowStr + index + cellStr + 14))
                        || !"收货人邮编".equals(columnMap.get(rowStr + index + cellStr + 15))
                        || !"收货人省份".equals(columnMap.get(rowStr + index + cellStr + 16))
                        || !"收货人市".equals(columnMap.get(rowStr + index + cellStr + 17))
                        || !"收货人区".equals(columnMap.get(rowStr + index + cellStr + 18))
                        || !"收货人地址".equals(columnMap.get(rowStr + index + cellStr + 19))
                        || !"换货邮费".equals(columnMap.get(rowStr + index + cellStr + 20))
                        || !"应退邮费".equals(columnMap.get(rowStr + index + cellStr + 21))
                        || !"其他金额".equals(columnMap.get(rowStr + index + cellStr + 22))
                        || !"代销结算金额".equals(columnMap.get(rowStr + index + cellStr + 23))
                        || !"头子表关联列".equals(columnMap.get(rowStr + index + cellStr + 24))) {
                    return Lists.newArrayList();
                }
            } else {
                if (StringUtils.isNotEmpty(columnMap.get(rowStr + index + cellStr + 1))) {
                    // 组装待配货记录
                    ocBOrderList.add(com.jackrain.nea.oc.oms.extmodel.ExtOcBReturnOrder.importCreate(index, ExtOcBReturnOrder, columnMap));
                }
            }
            index++;
        }
        return ocBOrderList;
    }

    /**
     * 获取明细表sheet数据，转换成min表对象
     */
    public List<ExtOcBReturnOrderRefund> getOcBReturnOrderRefund(Workbook hssfWorkbook) {
        List<ExtOcBReturnOrderRefund> extOcBReturnOrderRefundList = Lists.newArrayList();
        List<Map<String, String>> execlList = Lists.newArrayList();
        try {
            execlList = readExcel(1, hssfWorkbook);
        } catch (Exception e) {

        }
        int index = 0;
        for (Map<String, String> columnMap : execlList) {
            ExtOcBReturnOrderRefund refund = new ExtOcBReturnOrderRefund();
            if (index == 0) {
                // 校验excel字段
                if (columnMap.size() != 3
                        || !"条码".equals(columnMap.get(rowStr + index + cellStr + 0))
                        || !"数量".equals(columnMap.get(rowStr + index + cellStr + 1))
                        || !"头子表关联列".equals(columnMap.get(rowStr + index + cellStr + 2))) {
                    return Lists.newArrayList();
                }
            } else {
                if (StringUtils.isNotEmpty(columnMap.get(rowStr + index + cellStr + 1))) {
                    // 组装待配货记录
                    extOcBReturnOrderRefundList.add(ExtOcBReturnOrderRefund.importCreate(index, refund, columnMap));
                }
            }
            index++;
        }
        return extOcBReturnOrderRefundList;
    }

    /**
     * 获取明细表sheet数据，转换成min表对象
     */
    public List<ExtOcBReturnOrderExchange> getOcBReturnOrderExchange(Workbook hssfWorkbook) {
        List<ExtOcBReturnOrderExchange> extOcBReturnOrderExchangeList = Lists.newArrayList();
        List<Map<String, String>> execlList = Lists.newArrayList();
        try {
            execlList = readExcel(1, hssfWorkbook);
        } catch (Exception e) {

        }
        int index = 0;
        for (Map<String, String> columnMap : execlList) {
            ExtOcBReturnOrderExchange exchange = new ExtOcBReturnOrderExchange();
            if (index == 0) {
                // 校验excel字段
                if (columnMap.size() != 3
                        || !"条码".equals(columnMap.get(rowStr + index + cellStr + 0))
                        || !"数量".equals(columnMap.get(rowStr + index + cellStr + 1))
                        || !"头子表关联列".equals(columnMap.get(rowStr + index + cellStr + 2))) {
                    return Lists.newArrayList();
                }
            } else {
                if (StringUtils.isNotEmpty(columnMap.get(rowStr + index + cellStr + 1))) {
                    // 组装待配货记录
                    extOcBReturnOrderExchangeList.add(ExtOcBReturnOrderExchange.importCreate(index, exchange, columnMap));
                }
            }
            index++;
        }
        return extOcBReturnOrderExchangeList;
    }


    /**
     * 导入方法，
     *
     * @param hssfWorkbook
     * @return
     * @throws Exception
     */
    public static List<Map<String, String>> readExcel(Integer sheetIndex, Workbook hssfWorkbook) throws Exception {
        List list = Lists.newArrayList();
        Sheet hssfSheet = hssfWorkbook.getSheetAt(sheetIndex);
        if (Objects.isNull(hssfSheet)) {
            return Lists.newArrayList();
        }
        for (int rowNum = 0; rowNum <= hssfSheet.getLastRowNum(); rowNum++) {
            Map<String, String> map = Maps.newTreeMap();
            Row hssfRow = hssfSheet.getRow(rowNum);
            if (hssfRow != null) {
                Iterator<Cell> cellItr = hssfRow.cellIterator();
                while (cellItr.hasNext()) {
                    Cell cell = cellItr.next();
                    map.put(rowStr + cell.getRowIndex() + cellStr + cell.getColumnIndex(), getCellValue(cell));
                }
            }
            list.add(map);
        }
        return list;
    }

    private static String getCellValue(Cell cell) {
        int cellType = cell.getCellType();
        String cellValue;
        switch (cellType) {
            case NUMERIC:
                cellValue = nf.format(cell.getNumericCellValue());
                if (cellValue.indexOf(",") >= 0) {
                    cellValue = cellValue.replace(",", "");
                }
                break;
            case FORMULA:
                try {
                    cellValue = cell.getStringCellValue();
                } catch (IllegalStateException e) {
                    cellValue = String.valueOf(cell.getNumericCellValue());
                }
                break;

            default:
                cellValue = cell.getStringCellValue();
        }

        return cellValue.trim();
    }


    private String getCellValue(HSSFCell cell) {
        String value = null;
        if (cell != null) {
            switch (cell.getCellType()) {
                case FORMULA:
                    try {
                        value = String.valueOf(cell.getNumericCellValue());
                    } catch (IllegalStateException e) {
                        value = String.valueOf(cell.getRichStringCellValue());
                    }
                    break;
                case NUMERIC:
                    value = String.valueOf(cell.getNumericCellValue());
                    break;
                case STRING:
                    value = String.valueOf(cell.getRichStringCellValue());
                    break;
            }
        }
        return value;
    }
}
