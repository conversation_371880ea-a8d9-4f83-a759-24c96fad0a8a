package com.jackrain.nea.oc.oms.transfer;

import com.jackrain.nea.oc.oms.model.relation.IpStandplatOrderRelation;
import com.jackrain.nea.oc.oms.process.transfer.impl.normal.standplat.StandPlatformTransferOrderProcessImpl;
import com.jackrain.nea.oc.oms.services.IpStandplatOrderService;
import com.jackrain.nea.resource.SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

/**
 * @author: ming.fz
 * @since: 2019-07-15
 * @data 2019-07-15
 */
@Slf4j
@RunWith(SpringRunner.class)
@EnableAutoConfiguration
@Transactional
@SpringBootTest
public class StandplatTransferTest {

    @Autowired
    private StandPlatformTransferOrderProcessImpl orderProcess;

    @Autowired
    private IpStandplatOrderService ipStandplatOrderService;

    @Test
    public void testTransfer001() {
        String orderNo = "PS20220823093090000015";
        IpStandplatOrderRelation orderRelation = ipStandplatOrderService.selectStandplatOrder(orderNo);
        if (orderRelation != null) {
            orderProcess.start(orderRelation, false, SystemUserResource.getRootUser());
        }
    }

}
