package com.jackrain.nea.oc.services;

import cn.hutool.core.io.IoUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.druid.support.json.JSONUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.model.BnQueryForLogisticsProblemResult;
import com.jackrain.nea.oc.oms.api.OcBOrderForBnCmd;
import com.jackrain.nea.oc.oms.api.RemarkUpdateCmd;
import com.jackrain.nea.oc.oms.mapper.OcBOrderBnTaskMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.BnColumnEnum;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderBnTask;
import com.jackrain.nea.oc.oms.services.OcBOrderForBnService;
import com.jackrain.nea.oc.oms.services.oss.OssService;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.oc.services.model.BnOrderModel;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.table.StCBnProblemConfigDO;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.web.face.User;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName OcBOrderForBnCmdImpl
 * @Description 班牛相关操作
 * <AUTHOR>
 * @Date 2024/11/15 16:09
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBOrderForBnCmdImpl implements OcBOrderForBnCmd {

    @Autowired
    private OcBOrderForBnService bnService;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private RemarkUpdateCmd remarkUpdateCmd;
    @Autowired
    private IpRpcService ipRpcService;
    @Resource
    private OssService ossService;
    @Resource
    private StRpcService stRpcService;
    @Autowired
    private BuildSequenceUtil buildSequenceUtil;
    @Autowired
    private ThreadPoolTaskExecutor commonTaskExecutor;
    @Autowired
    private OcBOrderBnTaskMapper ocBOrderBnTaskMapper;

    @Override
    public ValueHolderV14<BnQueryForLogisticsProblemResult> getBnInfoByOrderId(Long orderId) {
        return bnService.getBnInfoByOrderId(orderId);
    }

    @Override
    public ValueHolderV14 pushLogisticsProblem(String param, User user) {
        ValueHolderV14 result = new ValueHolderV14();
        JSONObject jsonObject = JSONObject.parseObject(param);
        // 获取前端透传过来的信息
        Long orderId = jsonObject.getLong("id");
        OcBOrder ocBOrder = ocBOrderMapper.selectByID(orderId);
        String tid = ocBOrder.getTid();
        String shopTitle = ocBOrder.getCpCShopTitle();
        String userNick = ocBOrder.getUserNick();
        String warehouseName = ocBOrder.getCpCPhyWarehouseEname();
        String logisticsCompanyName = ocBOrder.getCpCLogisticsEname();
        String logisticsCode = ocBOrder.getExpresscode();
        String logistics = jsonObject.getString("bnWarehouseLogisticsId");
        String problemText = jsonObject.getString("problemText");
        String customerRequest = jsonObject.getString("customerRequest");
        String payAmt = ocBOrder.getReceivedAmt().toString();
        String payFor = jsonObject.getString("payFor");
        // 附件可能是一个list
        String url = jsonObject.getString("url");
        String remark = jsonObject.getString("remark");
        String item = jsonObject.getString("items");
        StCBnProblemConfigDO stCBnProblemConfigDO = stRpcService.queryBnProblemByText(problemText);
        if (stCBnProblemConfigDO == null) {
            result.setCode(ResultCode.FAIL);
            result.setMessage("问题类型不存在");
            return result;
        }
        Map<String, String> paramMap = getParamMap(user, ocBOrder, tid, shopTitle, userNick, warehouseName, logisticsCompanyName, logisticsCode, logistics, problemText, customerRequest, payAmt, payFor, url, remark, item);
        ValueHolderV14 valueHolderV14 = ipRpcService.pushTask(paramMap, "20940", "36039", null);
        if (!valueHolderV14.isOK()) {
            result.setCode(ResultCode.FAIL);
            result.setMessage(valueHolderV14.getMessage());
            return result;
        }
        // 获取valueHolderV14中的data 然后解析里面的code
        JSONObject data = (JSONObject) valueHolderV14.getData();
        JSONObject response = (JSONObject) data.get("response");
        Integer code = response.getInteger("code");
        if (code == 0) {
            result.setCode(ResultCode.SUCCESS);
            result.setMessage("处理成功");
            insertBnTask(user, orderId, ocBOrder, paramMap);
            String newSellerMemo = "";
            if (StringUtils.isNotEmpty(stCBnProblemConfigDO.getSellerRemark())) {
                newSellerMemo = stCBnProblemConfigDO.getSellerRemark() + logisticsCode;
            } else {
                newSellerMemo = logisticsCode;
            }
            // 将备注推送到平台
            JSONObject obj = new JSONObject();
            obj.put("ids", orderId);
            obj.put("remark", user.getEname() + newSellerMemo);
            obj.put("cover", "false");
            obj.put("order_flag", "1");
            remarkUpdateCmd.updateRemark(obj, user);
        } else {
            result.setCode(ResultCode.FAIL);
            result.setMessage(response.getString("error_msg"));
        }
        return result;
    }

    private void insertBnTask(User user, Long orderId, OcBOrder ocBOrder, Map<String, String> paramMap) {
        // 记录下来推送的数据
        OcBOrderBnTask ocBOrderBnTask = new OcBOrderBnTask();
        ocBOrderBnTask.setId(buildSequenceUtil.buildOrderBnTaskSequenceId());
        ocBOrderBnTask.setOcBOrderId(orderId);
        ocBOrderBnTask.setBillNo(ocBOrder.getBillNo());
        ocBOrderBnTask.setTid(ocBOrder.getTid());
        ocBOrderBnTask.setParam(JSONUtils.toJSONString(paramMap));
        BaseModelUtil.initialBaseModelSystemField(ocBOrderBnTask, user);
        ocBOrderBnTaskMapper.insert(ocBOrderBnTask);
    }

    private Map<String, String> getParamMap(User user, OcBOrder ocBOrder, String tid, String shopTitle, String userNick, String warehouseName, String logisticsCompanyName, String logisticsCode, String logistics, String problemText, String customerRequest, String payAmt, String payFor, String url, String remark, String item) {
        String receiverName = ocBOrder.getReceiverName();
        String receiverPhone = ocBOrder.getReceiverMobile();
        String province = ocBOrder.getCpCRegionProvinceEname();
        String city = ocBOrder.getCpCRegionCityEname();
        String area = ocBOrder.getCpCRegionAreaEname();
        String street = ocBOrder.getCpCRegionTownEname();
        String address = ocBOrder.getReceiverAddress();
        String omsCreater = user == null ? "系统管理员" : user.getEname();
        List<JSONObject> logisticsCodeList = new ArrayList<>();
        JSONObject dataObject = new JSONObject();
        dataObject.put(BnColumnEnum.LOGISTICS_CODE_SUB.getColumnId(), ocBOrder.getExpresscode());
        logisticsCodeList.add(dataObject);

        // 组装map
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put(BnColumnEnum.TID.getColumnId(), tid);
        paramMap.put(BnColumnEnum.SHOP_TITLE.getColumnId(), shopTitle);
        paramMap.put(BnColumnEnum.USER_NICK.getColumnId(), userNick);
        paramMap.put(BnColumnEnum.WAREHOUSE_NAME.getColumnId(), warehouseName);
        paramMap.put(BnColumnEnum.LOGISTICS_COMPANY_NAME.getColumnId(), logisticsCompanyName);
//        paramMap.put(BnColumnEnum.LOGISTICS_CODE.getColumnId(), logisticsCode);
        paramMap.put(BnColumnEnum.LOGISTICS_CODE_NEW.getColumnId(), JSONUtil.toJsonStr(logisticsCodeList));
        paramMap.put(BnColumnEnum.LOGISTICS.getColumnId(), logistics);
        paramMap.put(BnColumnEnum.PROBLEM_TEXT.getColumnId(), problemText);
        paramMap.put(BnColumnEnum.CUSTOMER_REQUEST.getColumnId(), customerRequest);
        paramMap.put(BnColumnEnum.PAY_AMT.getColumnId(), payAmt);
        paramMap.put(BnColumnEnum.PAY_FOR.getColumnId(), payFor);
        paramMap.put(BnColumnEnum.URL.getColumnId(), url);
        paramMap.put(BnColumnEnum.REMARK.getColumnId(), remark);
        paramMap.put(BnColumnEnum.RECEIVER_NAME.getColumnId(), receiverName);
        paramMap.put(BnColumnEnum.RECEIVER_PHONE.getColumnId(), receiverPhone);
        paramMap.put(BnColumnEnum.PROVINCE.getColumnId(), province);
        paramMap.put(BnColumnEnum.CITY.getColumnId(), city);
        paramMap.put(BnColumnEnum.AREA.getColumnId(), area);
        paramMap.put(BnColumnEnum.STREET.getColumnId(), street);
        paramMap.put(BnColumnEnum.ADDRESS.getColumnId(), address);
        paramMap.put(BnColumnEnum.OMS_CREATER.getColumnId(), omsCreater);
        paramMap.put(BnColumnEnum.ITEM.getColumnId(), item);
        return paramMap;
    }

    @Override
    public ValueHolderV14 batchPushLogisticsProblem(JSONObject jsonObject, User user) {
        JSONArray jsonArray = jsonObject.getJSONArray("ids");
        if (jsonArray == null || jsonArray.isEmpty()) {
            return new ValueHolderV14(ResultCode.FAIL, "请选择要手动匹配的单据");
        }
        String problemText = jsonObject.getString("problemText");
        if (StringUtils.isEmpty(problemText)) {
            return new ValueHolderV14(ResultCode.FAIL, "请选择物流问题类型");
        }
        String customerRequest = jsonObject.getString("customerRequest");
        String remark = jsonObject.getString("remark");
        StCBnProblemConfigDO stCBnProblemConfigDO = stRpcService.queryBnProblemByText(problemText);
        if (stCBnProblemConfigDO == null) {
            return new ValueHolderV14(ResultCode.FAIL, "物流问题类型不存在");
        }
        List<Map<String, String>> contents = new ArrayList<>();
        List<Long> orderIds = new ArrayList<>();
        List<BnOrderModel> bnOrderModelList = new ArrayList<>();
        for (Object o : jsonArray) {
            Long orderId = Long.valueOf(o.toString());
            OcBOrder ocBOrder = ocBOrderMapper.selectById(orderId);
            ValueHolderV14<BnQueryForLogisticsProblemResult> logisticsResult = bnService.getBnInfoByOrderId(orderId);
            if (!logisticsResult.isOK()) {
                logisticsResult.setMessage("订单ID:" + ocBOrder.getBillNo() + "获取物流信息失败");
                return logisticsResult;
            }
            BnOrderModel bnOrderModel = new BnOrderModel();
            BeanUtils.copyProperties(ocBOrder, bnOrderModel);
            bnOrderModel.setBnWarehouseLogisticsId(logisticsResult.getData().getBnWarehouseLogisticsId());
            bnOrderModelList.add(bnOrderModel);
            orderIds.add(orderId);
        }
        // bnOrderModelList对tid与bnWarehouseLogisticsId的组合进行分组
        Map<String, List<BnOrderModel>> bnOrderModelMap = new HashMap<>();

        Map<Long, String> remarkMap = new HashMap<>();
        for (BnOrderModel bnOrderModel : bnOrderModelList) {
            String key = bnOrderModel.getTid() + bnOrderModel.getBnWarehouseLogisticsId();
            bnOrderModelMap.computeIfAbsent(key, k -> new ArrayList<>()).add(bnOrderModel);
        }

        // 按订单号分组存储订单模型
        Map<String, List<BnOrderModel>> bnOrderModelMap4Remark = bnOrderModelList.stream()
                .collect(Collectors.groupingBy(BnOrderModel::getTid));

        // 处理每组订单的物流信息和备注
        bnOrderModelMap4Remark.forEach((tid, orders) -> {
            List<BnOrderModel> orderGroup = bnOrderModelMap4Remark.get(tid);
            if (orderGroup != null && !orderGroup.isEmpty()) {
                BnOrderModel firstOrder = orderGroup.get(0);

                // 使用Stream收集所有物流编码
                String logisticsCodes = orderGroup.stream()
                        .map(BnOrderModel::getExpresscode)
                        .collect(Collectors.joining(","));

                // 构建最终备注内容
                String finalRemark = StringUtils.isNotEmpty(stCBnProblemConfigDO.getSellerRemark())
                        ? stCBnProblemConfigDO.getSellerRemark() + " " + logisticsCodes
                        : logisticsCodes;

                remarkMap.put(firstOrder.getId(), finalRemark);
            }
        });

        List<OcBOrderBnTask> ocBOrderBnTaskList = new ArrayList<>();
        for (String key : bnOrderModelMap.keySet()) {
            List<OcBOrderBnTask> tidBnTaskList = new ArrayList<>();
            List<BnOrderModel> bnOrderModelList1 = bnOrderModelMap.get(key);
            BnOrderModel ocBOrder = bnOrderModelList1.get(0);
            String tid = ocBOrder.getTid();
            String shopTitle = ocBOrder.getCpCShopTitle();
            String userNick = ocBOrder.getUserNick();
            String warehouseName = ocBOrder.getCpCPhyWarehouseEname();
            String logisticsCompanyName = ocBOrder.getCpCLogisticsEname();
            List<JSONObject> logisticsCodeList = new ArrayList<>();
            BigDecimal payAmt = new BigDecimal("0");
            for (BnOrderModel bnOrderModel : bnOrderModelList1) {
                // 拼接bnOrderModelList1中的expressCode
                OcBOrderBnTask ocBOrderBnTask = new OcBOrderBnTask();
                ocBOrderBnTask.setId(buildSequenceUtil.buildOrderBnTaskSequenceId());
                ocBOrderBnTask.setOcBOrderId(bnOrderModel.getId());
                ocBOrderBnTask.setBillNo(ocBOrder.getBillNo());
                ocBOrderBnTask.setTid(ocBOrder.getTid());
                BaseModelUtil.initialBaseModelSystemField(ocBOrderBnTask, user);
                tidBnTaskList.add(ocBOrderBnTask);
                JSONObject dataObject = new JSONObject();
                dataObject.put(BnColumnEnum.LOGISTICS_CODE_SUB.getColumnId(), bnOrderModel.getExpresscode());
                logisticsCodeList.add(dataObject);
                payAmt.add(bnOrderModel.getReceivedAmt());
            }
            String receiverName = ocBOrder.getReceiverName();
            String receiverPhone = ocBOrder.getReceiverMobile();
            String province = ocBOrder.getCpCRegionProvinceEname();
            String city = ocBOrder.getCpCRegionCityEname();
            String area = ocBOrder.getCpCRegionAreaEname();
            String street = ocBOrder.getCpCRegionTownEname();
            String address = ocBOrder.getReceiverAddress();
            String omsCreater = user == null ? "系统管理员" : user.getEname();
            // 组装map
            Map<String, String> paramMap = new HashMap<>();
            paramMap.put(BnColumnEnum.TID.getColumnId(), tid);
            paramMap.put(BnColumnEnum.SHOP_TITLE.getColumnId(), shopTitle);
            paramMap.put(BnColumnEnum.USER_NICK.getColumnId(), userNick);
            paramMap.put(BnColumnEnum.WAREHOUSE_NAME.getColumnId(), warehouseName);
            paramMap.put(BnColumnEnum.LOGISTICS_COMPANY_NAME.getColumnId(), logisticsCompanyName);
            paramMap.put(BnColumnEnum.LOGISTICS_CODE_NEW.getColumnId(), JSONUtil.toJsonStr(logisticsCodeList));
            paramMap.put(BnColumnEnum.LOGISTICS.getColumnId(), String.valueOf(ocBOrder.getBnWarehouseLogisticsId()));
            paramMap.put(BnColumnEnum.PROBLEM_TEXT.getColumnId(), problemText);
            paramMap.put(BnColumnEnum.CUSTOMER_REQUEST.getColumnId(), customerRequest);
            paramMap.put(BnColumnEnum.PAY_AMT.getColumnId(), payAmt.toString());
            paramMap.put(BnColumnEnum.REMARK.getColumnId(), remark);
            paramMap.put(BnColumnEnum.RECEIVER_NAME.getColumnId(), receiverName);
            paramMap.put(BnColumnEnum.RECEIVER_PHONE.getColumnId(), receiverPhone);
            paramMap.put(BnColumnEnum.PROVINCE.getColumnId(), province);
            paramMap.put(BnColumnEnum.CITY.getColumnId(), city);
            paramMap.put(BnColumnEnum.AREA.getColumnId(), area);
            paramMap.put(BnColumnEnum.STREET.getColumnId(), street);
            paramMap.put(BnColumnEnum.ADDRESS.getColumnId(), address);
            paramMap.put(BnColumnEnum.OMS_CREATER.getColumnId(), omsCreater);
            for (OcBOrderBnTask ocBOrderBnTask : tidBnTaskList) {
                ocBOrderBnTask.setParam(JSONUtil.toJsonStr(paramMap));
            }
            contents.add(paramMap);
            ocBOrderBnTaskList.addAll(tidBnTaskList);
        }

        commonTaskExecutor.submit(() -> {
            if (remarkMap != null) {
                for (Long orderId : remarkMap.keySet()) {
                    String sellerMemo = user.getEname() + remarkMap.get(orderId);
                    // 将备注推送到平台
                    JSONObject obj = new JSONObject();
                    obj.put("ids", orderId);
                    obj.put("remark", sellerMemo);
                    obj.put("cover", "false");
                    obj.put("order_flag", "1");
                    remarkUpdateCmd.updateRemark(obj, user);
                }
            }
        });
        ValueHolderV14 valueHolderV14 = ipRpcService.batchPushTask(contents, "20940", "36039", null);
        if (valueHolderV14.isOK()) {
            ocBOrderBnTaskMapper.batchInsert(ocBOrderBnTaskList);
        }
        return valueHolderV14;
    }

    @Override
    public ValueHolderV14 uploadFile(MultipartFile file) {
        ValueHolderV14 holderV14 = new ValueHolderV14();
        String filePath = "";
        Map<String, String> fileMap = new HashMap<>();
        String fileName = "";
        String fileExtension = "";
        try {
            // 获取原始文件名
            fileName = file.getOriginalFilename();
            // 获取文件扩展名
            fileExtension = fileName.substring(fileName.lastIndexOf(".") + 1);
            InputStream inputStream = file.getInputStream();
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream(1024);
            IoUtil.copy(inputStream, byteArrayOutputStream, 4096);
            ByteArrayInputStream readInputStream = new ByteArrayInputStream(byteArrayOutputStream.toByteArray());
            readInputStream.mark(0);
            // 上传oss
            readInputStream.reset();
            filePath = ossService.uploadFile(readInputStream,
                    "OSS-Bucket/IMPORT/BN/" + file.getOriginalFilename(), true);
        } catch (Exception e) {
            log.error("上传文件异常", e);
        }
        ValueHolderV14 result = ipRpcService.uploadFile(filePath, fileName, fileExtension);
        String bnId = "";
        log.info("上传文件结果:{}", JSONObject.toJSONString(result));
        try {
            if (result.isOK()) {
                JSONObject jsonObject = (JSONObject) result.getData();
                JSONObject response = jsonObject.getJSONObject("response");
                Integer code = response.getInteger("code");
                if (code != 0) {
                    holderV14.setCode(ResultCode.FAIL);
                    holderV14.setMessage("上传附件失败");
                    return holderV14;
                }
                JSONObject dataMap = response.getJSONObject("map");
                JSONArray resultData = dataMap.getJSONArray("result");
                for (Object o : resultData) {
                    JSONObject idJsonObject = (JSONObject) o;
                    bnId = idJsonObject.getString("id");
                    break;
                }
            }
        } catch (Exception e) {
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("上传附件失败");
            return holderV14;
        }
        fileMap.put("url", filePath);
        fileMap.put("bnId", bnId);
        holderV14.setCode(ResultCode.SUCCESS);
        holderV14.setMessage("success");
        holderV14.setData(fileMap);
        return holderV14;
    }

    @Data
    private static class BnModel implements Serializable {
        private static final long serialVersionUID = 4947374694343438340L;
        private String id;
        private String value;
    }
}
