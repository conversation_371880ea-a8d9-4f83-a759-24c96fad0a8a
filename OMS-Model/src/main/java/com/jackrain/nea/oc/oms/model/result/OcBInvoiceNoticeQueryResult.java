package com.jackrain.nea.oc.oms.model.result;

import com.jackrain.nea.oc.oms.model.relation.Page;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @author: huang.z<PERSON><PERSON>
 * @description: 开票通知单 实体
 * @since: 2019-07-20
 * create at : 2019-07-20 11:48
 */
@Data
public class OcBInvoiceNoticeQueryResult implements Serializable {
    private List<OcBInvoiceNoticeResult> invoiceNoticelist;
    private Page page; // 分页预定义信息
}
