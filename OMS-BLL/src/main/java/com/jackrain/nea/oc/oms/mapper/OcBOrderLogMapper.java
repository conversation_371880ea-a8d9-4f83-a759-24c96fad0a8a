package com.jackrain.nea.oc.oms.mapper;


import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.result.OrderLogSearchResult;
import com.jackrain.nea.oc.oms.model.table.OcBOrderLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Component;

import java.util.List;

@Mapper
@Component
public interface OcBOrderLogMapper extends ExtentionMapper<OcBOrderLog> {
    String filed = " ID, OC_B_ORDER_ID,BILL_NO,LOG_MESSAGE,USER_NAME,LOG_TYPE,LOG_PARAM,ERROR_INFO,IP_ADDRESS," +
            "MODIFIERENAME,OWNERENAME,ISACTIVE,CREATIONDATE,MODIFIEDDATE ";

   /* @SelectProvider(type = OcBLog.class,method = "search")
    List<JSONObject> findLogByOrderId(String join, String join2);*/

    @SelectProvider(type = OcBLog.class, method = "search")
    List<OrderLogSearchResult> findLogByOrderId(String join, String join2);

    class OcBLog {
        public String search(String join, String join2) {
            return "SELECT " + filed + " FROM OC_B_ORDER_LOG WHERE ID IN ( " + join + " ) " + " and OC_B_ORDER_ID IN(" + join2 + ") ORDER BY CREATIONDATE DESC";

        }
    }

    @Select("SELECT * FROM OC_B_ORDER_LOG WHERE OC_B_ORDER_ID = #{orderId} and LOG_TYPE = #{logType} and LOG_MESSAGE = #{logMessage}")
    List<OcBOrderLog> selectOcBOrderLogList(@Param("orderId") Long orderId, @Param("logType") String logType, @Param("logMessage") String logMessage);


    @Select("SELECT * FROM OC_B_ORDER_LOG WHERE OC_B_ORDER_ID = #{orderId} and ID = #{id}")
    OcBOrderLog selectOcBOrderLogById(@Param("id") Long id, @Param("orderId") Long orderId);

    @Select("SELECT * FROM OC_B_ORDER_LOG WHERE OC_B_ORDER_ID = #{orderId} ORDER BY id DESC LIMIT 1")
    OcBOrderLog selectOcBOrderLogByOrderId(@Param("orderId") Long orderId);


    @Select("SELECT * FROM OC_B_ORDER_LOG WHERE OC_B_ORDER_ID = #{orderId} ORDER BY id DESC limit #{offset}, #{size}")
    List<OcBOrderLog> getLogByOrderId(@Param("orderId") Long orderId, @Param("offset") Integer offset, @Param("size") Integer size);

    @Select("SELECT count(*) FROM OC_B_ORDER_LOG WHERE OC_B_ORDER_ID = #{orderId}")
    Integer countByOrderId(@Param("orderId") Long orderId);

    @Update("update OC_B_ORDER_LOG set modifieddate = now(), creationdate = now() where OC_B_ORDER_ID = #{orderId} and id = #{id}")
    void updateOcBOrderLogModifieddateById(@Param("orderId") Long orderId, @Param("id") Long id);
}