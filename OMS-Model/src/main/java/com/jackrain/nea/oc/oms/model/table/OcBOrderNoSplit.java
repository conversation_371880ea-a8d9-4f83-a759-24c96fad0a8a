package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @ClassName OcBOrderNoSplit
 * @Description 寻源缺货不拆订单中间表
 * <AUTHOR>
 * @Date 2024/2/29 09:31
 * @Version 1.0
 */
@TableName(value = "oc_b_order_no_split")
@Data
@Document(index = "oc_b_order_no_split", type = "oc_b_order_no_split")
@NoArgsConstructor(access = AccessLevel.PUBLIC)
@ApiModel(value = "oc_b_order_no_split", description = "寻源缺货不拆订单中间表")
public class OcBOrderNoSplit extends BaseModel {
    private static final long serialVersionUID = -1789712848902251932L;

    @ApiModelProperty(value = "唯一ID")
    @Field(type = FieldType.Long)
    @JSONField(name = "ID")
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "ORDER_ID")
    private Long orderId;

    /**
     * 可拆单的时间
     */
    @JSONField(name = "CAN_SPLIT_TIME")
    private Date canSplitTime;

    @JSONField(name = "CREATIONDATE")
    private Date creationdate;
}
