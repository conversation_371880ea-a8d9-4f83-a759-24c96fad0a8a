package com.jackrain.nea.oc.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBReturnTypeCmd;
import com.jackrain.nea.oc.oms.services.OcBReturnTypeService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 退款分类实现类
 *
 * <AUTHOR>
 * @date 2020/12/30 9:55 上午
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBReturnTypeCmdImpl extends CommandAdapter implements OcBReturnTypeCmd {

    @Autowired
    private OcBReturnTypeService returnTypeService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return returnTypeService.addReturnType(session);
    }
}
