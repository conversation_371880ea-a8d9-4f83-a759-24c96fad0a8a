package com.jackrain.nea.oc.oms.mapper.task;

import org.apache.ibatis.datasource.pooled.PooledDataSource;
import org.apache.ibatis.mapping.Environment;
import org.apache.ibatis.session.Configuration;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.session.SqlSessionFactoryBuilder;
import org.apache.ibatis.transaction.TransactionFactory;
import org.apache.ibatis.transaction.jdbc.JdbcTransactionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;

/**
 * @Author: 孙勇生
 * @CreateDate: 2019/3/11
 * 描述：DB
 */
@Service
public class DBManagerDrds {

    @Autowired
    private org.springframework.core.env.Environment env;
    private Configuration configuration;
    private SqlSessionFactory sqlSessionFactory;
    private SqlSession sqlSession;

    public void DBManager() {
        String dbDriver = env.getProperty("spring.datasource.driverClassName");
        String connString = env.getProperty("spring.datasource.url");
        String dbUser = env.getProperty("spring.datasource.username");
        String dbPass = env.getProperty("spring.datasource.password");
        DataSource dataSource = new PooledDataSource(dbDriver, connString, dbUser, dbPass);
        TransactionFactory transactionFactory = new JdbcTransactionFactory();
        Environment environment = new Environment("production", transactionFactory, dataSource);
        configuration = new Configuration(environment);
        configuration.addMappers("com.jackrain.nea.oc.oms.mapper.task");
        sqlSessionFactory = new SqlSessionFactoryBuilder().build(configuration);
    }

    public SqlSession getSqlSession() {
        if (null != sqlSession) {
            return this.sqlSession;
        }
        this.DBManager();
        this.sqlSession = sqlSessionFactory.openSession();
        return this.sqlSession;
    }
}
