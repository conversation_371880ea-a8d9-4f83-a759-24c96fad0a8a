package com.jackrain.nea.oc.oms.services;

import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.jackrain.nea.oc.oms.api.OcBOrderHoldCmd;
import com.jackrain.nea.oc.oms.model.enums.OrderHoldReasonEnum;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;

/**
 * @author: DXF
 * @since: 2020/12/7
 * create at : 2020/12/7 15:30
 */
@Component
@Service(protocol = "dubbo", validation = "true", group = "oc-core", version = "1.0")
public class OcBOrderHoldCmdImpl implements OcBOrderHoldCmd {

    @Autowired
    private OcBOrderHoldService ocBOrderHoldService;

    @Override
    public void holdOrUnHoldOrder(OcBOrder ocBOrder, OrderHoldReasonEnum holdReason) {
        ocBOrderHoldService.holdOrUnHoldOrder(ocBOrder,holdReason);
    }

    @Override
    public void holdOrUnHoldOrderList(List<OcBOrder> ocBOrderList, OrderHoldReasonEnum holdReason) {
        if (CollectionUtils.isNotEmpty(ocBOrderList)) {
            for (OcBOrder ocBOrder : ocBOrderList) {
                ocBOrderHoldService.holdOrUnHoldOrder(ocBOrder,holdReason);
            }
        }

    }
}
