package com.jackrain.nea.oc.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBOrderAddServiceArchiveCmd;
import com.jackrain.nea.oc.oms.services.OcBOrderAddServiceService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName OcBOrderAddServiceArchiveCmdImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/10/26 18:32
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBOrderAddServiceArchiveCmdImpl extends CommandAdapter implements OcBOrderAddServiceArchiveCmd {

    @Autowired
    private OcBOrderAddServiceService addServiceService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return addServiceService.archive(session);
    }
}
