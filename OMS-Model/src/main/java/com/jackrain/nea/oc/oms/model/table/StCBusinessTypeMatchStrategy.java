package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.util.Date;

/**
 * @program: r3-oc-oms
 * @description: 业务类型匹配策略
 * @author: caomalai
 * @create: 2022-07-14 14:39
 **/
@TableName(value = "st_c_business_type_match_strategy")
@Data
public class StCBusinessTypeMatchStrategy extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 单据类型 1=订单、2=退单、3=退款单
     */
    @JSONField(name = "BILL_TYPE")
    private Integer billType;

    /**
     * 业务类型ID
     */
    @JSONField(name = "ST_C_BUSINESS_TYPE_ID")
    private Long stCBusinessTypeId;

    /**
     * 业务类型名称
     */
    @JSONField(name = "ST_C_BUSINESS_TYPE_ENAME")
    private String stCBusinessTypeEname;


    @JSONField(name = "ST_C_BUSINESS_TYPE_ECODE")
    private String stCBusinessTypeEcode;

    /**
     * 备注
     */
    @JSONField(name = "REMARK")
    private String remark;

    /**
     * 策略状态 1=未审核 2=已审核 3=已作废 4=已结案
     */
    @JSONField(name = "STATUS")
    private Integer status;

    /**
     * 提交人ID
     */
    @JSONField(name = "SUBMIT_ID")
    private Long submitId;

    /**
     * 提交人用户名
     */
    @JSONField(name = "SUBMIT_NAME")
    private String submitName;

    /**
     * 提交时间
     */
    @JSONField(name = "SUBMIT_DATE")
    private Date submitDate;

    /**
     * 结案人ID
     */
    @JSONField(name = "END_ID")
    private Long endId;

    @JSONField(name = "END_NAME")
    private String endName;

    /**
     * 提交时间
     */
    @JSONField(name = "END_DATE")
    private Date endDate;

}
