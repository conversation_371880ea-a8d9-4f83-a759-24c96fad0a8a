package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TableName(value = "oc_b_order_naika_void")
@Data
public class OcBOrderNaikaVoid extends BaseModel {

    @ApiModelProperty(value = "订单作废id")
    @Field(type = FieldType.Long)
    @JSONField(name = "ID")
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @ApiModelProperty(value = "订单编号")
    @Field(type = FieldType.Long)
    @JSONField(name = "OC_B_ORDER_ID")
    private Long ocBOrderId;

    @ApiModelProperty(value = "订单明细编号")
    @Field(type = FieldType.Long)
    @JSONField(name = "OC_B_ORDER_ITEM_ID")
    private Long ocBOrderItemId;

    @ApiModelProperty(value = "初始平台单号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "TID")
    private String tid;

    @ApiModelProperty(value = "作废状态")
    @Field(type = FieldType.Integer)
    @JSONField(name = "VOID_STATUS")
    private Integer voidStatus;

    @ApiModelProperty(value = "作废次数")
    @Field(type = FieldType.Integer)
    @JSONField(name = "VOID_TIMES")
    private Integer voidTimes;

    @ApiModelProperty(value = "平台ID")
    @Field(type = FieldType.Long)
    @JSONField(name = "PLATFORM_ID")
    private Long platformId;

    @ApiModelProperty(value = "已发货退款ID")
    @Field(type = FieldType.Long)
    @JSONField(name = "OC_B_RETURN_AF_SEND_ID")
    private Long ocBReturnAfSendId;
}
