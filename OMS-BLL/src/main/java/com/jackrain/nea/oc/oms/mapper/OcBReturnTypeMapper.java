package com.jackrain.nea.oc.oms.mapper;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.OcBReturnType;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.jdbc.SQL;
import org.springframework.stereotype.Component;


@Mapper
@Component
public interface OcBReturnTypeMapper extends ExtentionMapper<OcBReturnType> {

    @Select("SELECT count(id) FROM oc_b_return_type WHERE ename = #{name}")
    int selectCountByName(@Param("name") String name);

    @Select("SELECT count(id) FROM oc_b_return_type WHERE ecode = #{code}")
    int selectCountByCode(@Param("code") String code);

    @Select("SELECT * FROM oc_b_return_type WHERE ecode = #{code}")
    OcBReturnType selectByCode(@Param("code") String code);

    @InsertProvider(type = OcBReturnTypeMapper.OcBReturnTypeProvider.class, method = "returnTypeAdd")
    int insertReturnType(JSONObject obj);

    @UpdateProvider(type = OcBReturnTypeMapper.OcBReturnTypeProvider.class, method = "returnTypeUpdate")
    int updateReturnType(JSONObject obj);

    class OcBReturnTypeProvider {

        public String returnTypeAdd(JSONObject jo) {
            return new SQL() {
                {
                    INSERT_INTO("OC_B_RETURN_TYPE");
                    for (String key : jo.keySet()) {
                        VALUES(key, "#{" + key + "}");
                    }
                }
            }.toString();
        }

        public String returnTypeUpdate(JSONObject jo){
            return new SQL() {
                {
                    UPDATE("OC_B_RETURN_TYPE");
                    for (String key : jo.keySet()) {
                        if (!"ID".equals(key)) {
                            SET(key + "= #{" + key + "}");
                        }
                    }
                    WHERE("ID = #{ID}");
                }
            }.toString();
        }
    }
}