package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.oc.oms.model.request.TransferOrderRequest;
import com.jackrain.nea.oc.oms.model.result.ReturnOrderResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2022/4/3
 */
public interface JDDirectRefundTransferCmd {

    ValueHolderV14<ReturnOrderResult> trans(TransferOrderRequest transferOrderRequest);
}
