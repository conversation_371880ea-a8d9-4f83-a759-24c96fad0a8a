package com.jackrain.nea.oc.oms.mq.processor.impl.transfer.normal;

import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.OperateType;
import com.jackrain.nea.oc.oms.model.enums.OrderType;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoOrderRelation;
import com.jackrain.nea.oc.oms.mq.processor.IMqOrderDetailProcessor;
import com.jackrain.nea.oc.oms.process.transfer.impl.normal.taobao.TaobaoTransferOrderProcessImpl;
import com.jackrain.nea.oc.oms.services.IpTaobaoOrderService;
import com.jackrain.nea.oc.oms.step.ProcessStepResultList;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 淘宝转单消息处理器
 * <p>
 * 2020-11-11易邵峰检查
 *
 * @author: 易邵峰
 * @since: 2019-03-06
 * create at : 2019-03-06 15:28
 */
@Slf4j
public class TaobaoTransferMqOrderDetailProcessor implements IMqOrderDetailProcessor {

    @Autowired
    private IpTaobaoOrderService ipTaobaoOrderService;

    @Autowired
    private TaobaoTransferOrderProcessImpl taobaoTransferOrderProcess;

    @Override
    public ProcessStepResultList start(OperateOrderMqInfo orderMqInfo) {
        String orderNo = orderMqInfo.getOrderNo();

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("TaobaoTransferMqOrderDetailProcessor.Start", orderNo));
        }

        IpTaobaoOrderRelation taobaoOrderRelation = this.ipTaobaoOrderService.selectTaobaoOrderByTid(orderNo);
        if (taobaoOrderRelation == null || taobaoOrderRelation.getTaobaoOrder() == null) {
            log.error(LogUtil.format("TaobaoTransferMqOrderDetailProcessor.Error.Not.Exist", orderNo));
            return new ProcessStepResultList();
        } else {
            ProcessStepResultList resultList = taobaoTransferOrderProcess.start(taobaoOrderRelation,
                    false, SystemUserResource.getRootUser());
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("TaobaoTransferMqOrderDetailProcessor.Finished,Result={}", orderNo), resultList);
            }
            return resultList;
        }
    }

    @Override
    public boolean checkMqIsCanExecute(OperateOrderMqInfo orderMqInfo) {
        return orderMqInfo.getOperateType() == OperateType.TRANSFER_ORDER
                && orderMqInfo.getChannelType() == ChannelType.TAOBAO
                && orderMqInfo.getOrderType() == OrderType.NORMAL;
    }
}
