package com.jackrain.nea.oc.oms.model.order.address;

import java.io.Serializable;

import com.alibaba.fastjson.annotation.JSONField;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @author: DXF
 * @since: 2020/11/26
 * create at : 2020/11/26 14:30
 */
@Data
@Accessors(chain = true)
public class WarehouseAndLogisticsDto implements Serializable {

    @J<PERSON><PERSON>ield(name = "CP_C_STORE_ID")
    private Long cpCStoreId;

    @J<PERSON><PERSON>ield(name = "CP_C_STORE_ECODE")
    private String cpCStoreEcode;

    @JSONField(name = "CP_C_STORE_ENAME")
    private String cpCStoreEname;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID")
    private Long cpCPhyWarehouseId;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ECODE")
    private String cpCPhyWarehouseEcode;

    @J<PERSON><PERSON><PERSON>(name = "CP_C_PHY_WAREHOUSE_ENAME")
    private String cpCPhyWarehouseEname;

    @J<PERSON><PERSON><PERSON>(name = "CP_C_LOGISTICS_ID")
    private Long cpCLogisticsId;

    @JSO<PERSON>ield(name = "CP_C_LOGISTICS_ECODE")
    private String cpCLogisticsEcode;

    @JSONField(name = "CP_C_LOGISTICS_ENAME")
    private String cpCLogisticsEname;


}
