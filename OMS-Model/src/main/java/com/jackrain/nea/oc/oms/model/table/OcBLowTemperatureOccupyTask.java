package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName OcBLowTemperatureOccupyTask
 * @Description 低温寻源中间表
 * <AUTHOR>
 * @Date 2024/5/9 14:10
 * @Version 1.0
 */
@TableName
@Data
public class OcBLowTemperatureOccupyTask extends BaseModel implements Serializable {
    private static final long serialVersionUID = 4853857305429283433L;

    @JSONField(name = "ID")
    private Long id;

    @JSONField(name = "ORDER_ID")
    private Long orderId;

    @JSONField(name = "BILL_NO")
    private String billNo;

    @JSONField(name = "TID")
    private String tid;

    @J<PERSON>NField(name = "ITEM_NUM")
    private Integer itemNum;

    @JSONField(name = "NEXT_TIME")
    private Date nextTime;

    @JSONField(name = "BUSINESS_TYPE_ID")
    private Long businessTypeId;

    @JSONField(name = "BUSINESS_TYPE_NAME")
    private String businessTypeName;

    @JSONField(name = "BUSINESS_TYPE_CODE")
    private String businessTypeCode;

    @JSONField(name = "IS_CYCLE")
    private Integer isCycle;

}
