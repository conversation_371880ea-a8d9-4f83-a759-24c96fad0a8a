package com.jackrain.nea.oc.oms.process.transfer.impl.refund.alibaba.ascp.refund.step;

import com.alibaba.fastjson.JSON;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpBAlibabaAscpOrderRefundRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderRelation;
import com.jackrain.nea.oc.oms.model.resources.SysNotesConstant;
import com.jackrain.nea.oc.oms.model.table.IpBAlibabaAscpOrderRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2020/2/27 3:12 下午
 * @Version 1.0
 */
@Step(order = 20, description = "判断原单的状态")
@Slf4j
@Component
public class Step020CheckAlibabaAscpOrderRefundOrderStatus extends BaseAlibabaAscpOrderRefundProcessStep
        implements IOmsOrderProcessStep<IpBAlibabaAscpOrderRefundRelation> {

    @Override
    public ProcessStepResult<IpBAlibabaAscpOrderRefundRelation> startProcess(IpBAlibabaAscpOrderRefundRelation orderInfo,
                                                                             ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        IpBAlibabaAscpOrderRefund orderRefund = orderInfo.getOrderRefund();
        //获取订单信息
        List<OmsOrderRelation> omsOrderRelation = orderInfo.getOmsOrderRelation();
        String message = "";
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("check alibabaAscpOrderRefund order to success with process 020 step. into order information params:{}","alibabaAscpOrderRefund",orderInfo.getOrderId()), JSON.toJSONString(orderInfo));
        }
        if (CollectionUtils.isEmpty(omsOrderRelation)) {
            // 原单信息不存在超过三天
            if (checkOrderRefundExpireTime(orderRefund)) {
                message = "找不到有效原始订单超过配置天数,系统自动标记已转换;";
                orderRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                        message, orderRefund);
            } else {
                message = SysNotesConstant.SYS_REMARK1;
                orderRefundService.updateReturnOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(),
                        message, orderRefund);
            }
            return new ProcessStepResult<>(StepStatus.FAILED, message + "转换失败");
        } else {
            if (checkOrderEffective(omsOrderRelation)) {
                message = "原始订单未完成发货，标记转换失败;";
                orderRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERFAIL.toInteger(),
                        message, orderRefund);
                return new ProcessStepResult<>(StepStatus.FAILED, message + "转换失败");
            }
        }
        log.debug(LogUtil.format("run transfer alibabaAscpOrderRefund process 020 step success. into order id :{}","alibabaAscpOrderRefund",orderInfo.getOrderNo()), orderInfo.getOrderNo());
        return new ProcessStepResult<>(StepStatus.SUCCESS, "判断原单状态成功,进入下一阶段");
    }


    /**
     * 检验原单不存在的时间
     *
     * @return
     */
    private boolean checkOrderRefundExpireTime(IpBAlibabaAscpOrderRefund orderRefund) {
        try {
            Date date = new Date();
            //判断退单时间是否超过三天
            Date created = orderRefund.getInsertDate();
            Long threeDays = 3 * 24 * 60 * 60 * 1000L + created.getTime();
            return threeDays < date.getTime();
        } catch (Exception e) {
            log.error(this.getClass().getName() + "检验原单不存在的时间,error={}", e.getMessage());
            return false;
        }
    }

    /**
     * 判断订单是否有效
     *
     * @param omsOrderRelation
     * @return
     */
    private boolean checkOrderEffective(List<OmsOrderRelation> omsOrderRelation) {
        boolean flag = true;
        for (int i = 0; i < omsOrderRelation.size(); i++) {
            OmsOrderRelation omsOrderRelation1 = omsOrderRelation.get(i);
            OcBOrder ocBOrder = omsOrderRelation1.getOcBOrder();
            Integer orderStatus = ocBOrder.getOrderStatus();
            if (!OmsOrderStatus.CANCELLED.toInteger().equals(orderStatus)
                    && !OmsOrderStatus.SYS_VOID.toInteger().equals(orderStatus)) {
                flag = false;
            } else {
                String suffixInfo = ocBOrder.getSuffixInfo();
                if (!"REFUND-VOID".equals(suffixInfo)) {
                    omsOrderRelation.remove(omsOrderRelation1);
                    i--;
                }
            }
        }
        return flag;
    }
}
