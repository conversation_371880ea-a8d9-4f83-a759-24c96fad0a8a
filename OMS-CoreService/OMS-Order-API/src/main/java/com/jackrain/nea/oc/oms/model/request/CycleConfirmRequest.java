package com.jackrain.nea.oc.oms.model.request;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 对账、取消对账
 *
 * <AUTHOR>
 */
@Data
public class CycleConfirmRequest implements Serializable {

    private static final long serialVersionUID = 1600871012976216698L;

    /**
     * 平台单号
     */
    private String tid;

    /**
     * 单据编号
     */
    private String billNo;

    /**
     * 商品编码
     */
//    private String skuCode;

    /**
     * 对账金额（正向正数，逆向负数）
     */
    private BigDecimal amt;

    /**
     * 对账状态；true:已对账，false:未对账
     */
    private Boolean isConfirm;

}