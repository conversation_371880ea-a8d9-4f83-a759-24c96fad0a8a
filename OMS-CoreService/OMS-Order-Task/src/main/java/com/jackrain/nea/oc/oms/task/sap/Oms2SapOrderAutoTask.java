//package com.jackrain.nea.oc.oms.task.sap;
//
//import com.burgeon.r3.sg.basic.common.SgConstantsIF;
//import com.jackrain.nea.cpext.model.table.CpShop;
//import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
//import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
//import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
//import com.jackrain.nea.oc.oms.model.request.sap.B2cOrderOutStockRequest;
//import com.jackrain.nea.oc.oms.model.table.OcBOrder;
//import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
//import com.jackrain.nea.oc.oms.sap.SapTaskTableEnum;
//import com.jackrain.nea.rpc.CpRpcService;
//import com.jackrain.nea.rpc.SgRpcService;
//import com.jackrain.nea.util.SapModelTransferUtil;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.util.*;
//
///**
// * @Desc :
// * <AUTHOR> xiWen
// * @Date : 2020/3/23
// */
//@Slf4j
//@Component
//@Deprecated
//public class Oms2SapOrderAutoTask extends AbstractOms2SapTask {
//
//
//    @Autowired
//    private OcBOrderMapper ocBOrderMapper;
//
//    @Autowired
//    private OcBOrderItemMapper ocBOrderItemMapper;
//
//    @Autowired
//    private CpRpcService cpRpcService;
//
//    @Autowired
//    private SgRpcService sgRpcService;
//
//
//    private String threadPoolName = "R3_OMS_SAP_ORDER_TASK_THREAD_POOL_%d";
//
//
//    @Override
//    protected String getTag() {
//        return oms2SapMqConfig.getOms2SapOrderTag();
//    }
//
//    @Override
//    protected String getTopic() {
//        return oms2SapMqConfig.getOms2SapOrderTopic();
//    }
//
//    @Override
//    protected String getTaskTableName() {
//        return SapTaskTableEnum.ORDER.txt();
//    }
//
//    @Override
//    protected String getOriginCol() {
//        //return "TO_SETTLE_STATUS"; @20200922 该字段不准确
//        return "TO_SAP_STATUS";
//    }
//
//    @Override
//    protected String getOriginTableName() {
//        return "OC_B_ORDER";
//    }
//
//    @Override
//    protected String getThreadPoolName() {
//        return threadPoolName;
//    }
//
//    @Override
//    protected List<B2cOrderOutStockRequest> getSapItems(List<Long> list, List<Long> activeIds) {
//        if (log.isDebugEnabled()) {
//            log.debug("{} start oms order push sap task, into order list:{}", this.getClass().getName(), list.toString());
//        }
//        String ids = joinList2String(list);
//        if (log.isDebugEnabled()) {
//            log.debug("Oms2SapOrderAutoTask.getSapItems.SapItemsid集合:{}", ids);
//        }
//        List<OcBOrder> ocBorders = ocBOrderMapper.selectListForSap(ids);
//        if (unExpect.test(ocBorders)) {
//            return null;
//        }
//        if (log.isDebugEnabled()) {
//            log.debug("Oms2SapOrderAutoTask.getSapItems.ocBorders集合:{}", ocBorders);
//        }
//        //根据订单单据编号查询逻辑仓档案数据
//       // SgSendBillQueryResult sgSendBillQueryResult = sgRpcService.querySgBSend(ocBorders, SgConstantsIF.BILL_TYPE_RETAIL).getData();
//        List<OcBOrder> ocBOrderList = new ArrayList<>();
//        for (OcBOrder ocBOrder : ocBorders) {
////            String billId = String.valueOf(ocBOrder.getId());
////            String billType = String.valueOf(SgConstantsIF.BILL_TYPE_RETAIL);
////            String mapKey = billId + "," + billType;
////            List<SgBSendItem> sgBSendItemOccupyList = new ArrayList<>();
////            Map<String, HashMap<SgBSend, List<SgBSendItem>>> resultsTmp = new HashMap<>();
////            Map<SgBSend, List<SgBSendItem>> sgSendMapTmp = new HashMap<>();
////            if (sgSendBillQueryResult != null) {
////                resultsTmp = sgSendBillQueryResult.getResults();
////                if (log.isDebugEnabled()) {
////                    log.debug("Oms2SapOrderAutoTask.getSapItems.resultsTmp集合:{},mapKey:{}", resultsTmp, mapKey);
////                }
////                if (resultsTmp != null) {
////                    sgSendMapTmp = resultsTmp.get(mapKey);
////                    if (sgSendMapTmp != null) {
////                        for (SgBSend sgBSend : sgSendMapTmp.keySet()) {
////                            sgBSendItemOccupyList = sgSendMapTmp.get(sgBSend);
////                        }
////                    } else {
////                        if (log.isDebugEnabled()) {
////                            log.debug("Oms2SapOrderAutoTask.getSapItems.sgSendMapTmp和sgBSendItemOccupyList集合为空,ocBOrderID:{}", ocBOrder.getId());
////                        }
////                        //@20200921 虚拟商品赋予逻辑仓ID默认值
////                        if (OrderTypeEnum.DIFFPRICE.getVal().equals(ocBOrder.getOrderType())) {
////                            ocBOrder.setCpCStoreId(1L);
////                            ocBOrder.setCpCStoreEcode("L-PH4-ZP");
////                        } else {
////                            continue;
////                        }
////                    }
////                } else {
////                    if (log.isDebugEnabled()) {
////                        log.debug("Oms2SapOrderAutoTask.getSapItems.resultsTmp集合为空");
////                    }
////                    return null;
////                }
////            } else {
////                if (log.isDebugEnabled()) {
////                    log.debug("Oms2SapOrderAutoTask.getSapItems.sgSendBillQueryResult集合为空");
////                }
////                return null;
////            }
////
////            if (CollectionUtils.isNotEmpty(sgBSendItemOccupyList)) {
////                SgBSendItem sgBSendItem = sgBSendItemOccupyList.get(0);
////                if (null != sgBSendItem) {
////                    ocBOrder.setCpCStoreId(sgBSendItem.getCpCStoreId());
////                    ocBOrder.setCpCStoreEcode(sgBSendItem.getCpCStoreEcode());
////                }
////            } else {
////                if (log.isDebugEnabled()) {
////                    log.debug("Oms2SapOrderAutoTask.getSapItems.sgBSendItemOccupyList集合为空,ocBOrderID:{}", ocBOrder.getId());
////                }
////            }
////            //调用渠道店铺接口
////            if (null != ocBOrder.getCpCShopId() && StringUtils.isEmpty(ocBOrder.getCpCShopEcode())) {
////                CpShop cpShop = cpRpcService.selectShopById(ocBOrder.getCpCShopId());
////                if (log.isDebugEnabled()) {
////                    log.debug("Oms2SapOrderAutoTask.getSapItems.cpShop集合为空,ocBOrderID:{},平台档案ID:{}", ocBOrder.getId(), ocBOrder.getCpCShopId());
////                }
////                if (cpShop != null) {
////                    //获取平台店铺档案 ecode
////                    ocBOrder.setCpCShopEcode(cpShop.getEcode());
////                }
////            }
////            //创建时间
////            ocBOrder.setCreationdate(new Date());
////            //扫描出库时间
////            ocBOrder.setScanTime(null == ocBOrder.getScanTime() ? new Date() : ocBOrder.getScanTime());
////            ocBOrderList.add(ocBOrder);
//        }
//        List<OcBOrderItem> items = ocBOrderItemMapper.selectOcOrderItems(list, null);
//        if (unExpect.test(items)) {
//            if (log.isDebugEnabled()) {
//                log.debug("Oms2SapOrderAutoTask.getSapItems.OcBOrderItem-list集合为空");
//            }
//            return null;
//        }
//        orderIdFun = o -> ((OcBOrder) o).getId();
//        statisticsCsm(ocBorders, activeIds);
//        return SapModelTransferUtil.saleOrderTransfer(ocBOrderList, items);
//    }
//
//}
