package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @program: r3-oc-oms
 * @description: 寻源前拆单策略-按sku拆单明细表
 * @author: caomalai
 * @create: 2022-07-14 14:39
 **/
@TableName(value = "st_c_split_before_source_strategy_sku_item")
@Data
public class StCSplitBeforeSourceStrategySkuItem extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 所属寻源前拆单策略
     */
    @JSONField(name = "ST_C_SPLIT_BEFORE_SOURCE_STRATEGY_ID")
    private Long stCSplitBeforeSourceStrategyId;

    /**
     * 商品SKU
     */
    @JSONField(name = "PS_C_SKU_ID")
    private Long psCSkuId;

    /**
     * 商品SKU编码
     */
    @JSONField(name = "PS_C_SKU_ECODE")
    private String psCSkuEcode;

    /**
     * 商品名称
     */
    @JSONField(name = "PS_C_PRO_NAME")
    private String psCProName;

    /**
     * 单包裹最大商品数量
     */
    @JSONField(name = "MAX_QTY")
    private BigDecimal maxQty;

}
