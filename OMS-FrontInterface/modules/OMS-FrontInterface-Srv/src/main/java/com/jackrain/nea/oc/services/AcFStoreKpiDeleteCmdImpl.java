package com.jackrain.nea.oc.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.AcFStoreKpiDeleteCmd;
import com.jackrain.nea.oc.oms.services.AcFStoreKpiDeleteService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * className: AcFStoreKpiDeleteCmdImpl
 * description:门店KPI删除
 *
 * <AUTHOR>
 * create: 2021-06-19
 * @since JDK 1.8
 */
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class AcFStoreKpiDeleteCmdImpl extends CommandAdapter implements AcFStoreKpiDeleteCmd {

    @Autowired
    private AcFStoreKpiDeleteService storeKpiDeleteService;


    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return storeKpiDeleteService.execute(session);
    }
}
