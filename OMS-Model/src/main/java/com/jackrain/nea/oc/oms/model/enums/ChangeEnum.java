package com.jackrain.nea.oc.oms.model.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: 李龙飞
 * @create: 2019-06-03 16:35
 **/
public class ChangeEnum {

    public static Map<String, Integer> billTypeNameDatas = new HashMap<>(); //退单类型选项值  汉字-》值
    public static Map<Integer, String> billTypeDatas = new HashMap<>(); //退单类型选项值  值-》汉字
    public static Map<Integer, String> platFormDatas = new HashMap<>(); //平台 值-》汉字
    public static Map<String, Integer> platFormNameDatas = new HashMap<>(); //平台 汉字-》值
    public static Map<Integer, String> yesOrNoDatas = new HashMap<>(); //通用是否 值-》汉字
    public static Map<String, Integer> yesOrNoNameDatas = new HashMap<>(); //通用是否 汉字-》值
    public static Map<String, Integer> returnStatusDatas = new HashMap<>(); //退款状态选项值 汉字-》值
    public static Map<Integer, String> returnStatusNameDatas = new HashMap<>(); //退款状态选项值 值-》汉字
    public static Map<String, String> refundReasonDatas = new HashMap<>(); //退款状态选项值 汉字-》值
    public static Map<String, String> refundReasonNameDatas = new HashMap<>(); //退款状态选项值 值-》汉字

    static {
        //退单类型选项值  值-》汉字
        OcReturnBillTypeEnum[] billType = OcReturnBillTypeEnum.values();
        for (OcReturnBillTypeEnum billTypeEnum : billType) {
            billTypeDatas.put(billTypeEnum.getVal(), billTypeEnum.getText());
        }
        //退单类型选项值  汉字-》值
        OcReturnBillTypeEnum[] billTypeName = OcReturnBillTypeEnum.values();
        for (OcReturnBillTypeEnum billTypeEnum : billTypeName) {
            billTypeNameDatas.put(billTypeEnum.getText(), billTypeEnum.getVal());
        }
        //平台 值-》汉字
        PlatFormEnum[] platForm = PlatFormEnum.values();
        for (PlatFormEnum platFormEnum : platForm) {
            platFormDatas.put(platFormEnum.getCode(), platFormEnum.getName());
        }
        //平台 汉字-》值
        PlatFormEnum[] platFormName = PlatFormEnum.values();
        for (PlatFormEnum platFormEnum : platFormName) {
            platFormNameDatas.put(platFormEnum.getName(), platFormEnum.getCode());
        }
        //通用是否 值-》汉字
        OcBorderListEnums.YesOrNoEnum[] yesOrNo = OcBorderListEnums.YesOrNoEnum.values();
        for (OcBorderListEnums.YesOrNoEnum yesOrNoEnum : yesOrNo) {
            yesOrNoDatas.put(yesOrNoEnum.getVal(), yesOrNoEnum.getText());
        }
        //通用是否 汉字-》值
        OcBorderListEnums.YesOrNoEnum[] yesOrNoName = OcBorderListEnums.YesOrNoEnum.values();
        for (OcBorderListEnums.YesOrNoEnum yesOrNoEnum : yesOrNoName) {
            yesOrNoNameDatas.put(yesOrNoEnum.getText(), yesOrNoEnum.getVal());
        }
        //退款状态选项值 值-》汉字
        ReturnStatusEnum[] returnStatusName = ReturnStatusEnum.values();
        for (ReturnStatusEnum returnStatusEnum : returnStatusName) {
            returnStatusNameDatas.put(returnStatusEnum.getVal(), returnStatusEnum.getKey());
        }
        //退款状态选项值 汉字-》值
        ReturnStatusEnum[] returnStatus = ReturnStatusEnum.values();
        for (ReturnStatusEnum returnStatusEnum : returnStatus) {
            returnStatusDatas.put(returnStatusEnum.getKey(), returnStatusEnum.getVal());
        }
        //退换货退款原因 值-》汉字
        RefundReasonEnum[] refundReasonName = RefundReasonEnum.values();
        for (RefundReasonEnum refundReasonEnum : refundReasonName) {
            refundReasonNameDatas.put(refundReasonEnum.getVal(), refundReasonEnum.getKey());
        }
        //退换货退款原因 汉字-》值
        RefundReasonEnum[] refundReason = RefundReasonEnum.values();
        for (RefundReasonEnum refundReasonEnum : refundReason) {
            refundReasonDatas.put(refundReasonEnum.getKey(), refundReasonEnum.getVal());
        }
    }
}
