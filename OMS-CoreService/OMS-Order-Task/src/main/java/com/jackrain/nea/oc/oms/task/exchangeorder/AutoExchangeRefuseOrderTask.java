//package com.jackrain.nea.oc.oms.task.exchangeorder;
//
//import com.alibaba.fastjson.JSONObject;
//import com.google.common.util.concurrent.ThreadFactoryBuilder;
//import com.jackrain.nea.oc.oms.es.ESIpTaoBaoExchange;
//import com.jackrain.nea.oc.oms.model.enums.AgreeOrRefuseEnum;
//import com.jackrain.nea.oc.oms.model.enums.OccupancyStatusEnum;
//import com.jackrain.nea.oc.oms.services.IpTaobaoExchangeService;
//import com.jackrain.nea.oc.oms.task.BaseR3Task;
//import com.jackrain.nea.task.IR3Task;
//import com.jackrain.nea.task.RunTaskResult;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.util.ArrayList;
//import java.util.List;
//import java.util.concurrent.*;
//import java.util.stream.Collectors;
//import java.util.stream.Stream;
//
//
///**
// * <AUTHOR> ruan.gz
// * @Description :换货单自动拒绝定时任务
// * @Date : 2020/7/3
// **/
//@Slf4j
//@Component
//public class AutoExchangeRefuseOrderTask extends BaseR3Task implements IR3Task {
//
//    @Autowired
//    private IpTaobaoExchangeService ipTaobaoExchangeService;
//
//    /**
//     * 基本线程池常量定义
//     */
//    int corePoolSize = 16;
//    int maxPoolSize = 20;
//    long keepAliveThreadTime = 60000;
//    String threadPoolName = "R3_OMS_EXCHANGE_REFUSE_TASK_THREAD_POOL_%d";
//    ArrayBlockingQueue blockingQueue = new ArrayBlockingQueue(16);
//
//    @Override
//    public RunTaskResult execute(JSONObject params) {
//        RunTaskResult result = new RunTaskResult();
//        ExecutorService executor = new ThreadPoolExecutor(
//                corePoolSize,
//                maxPoolSize,
//                keepAliveThreadTime,
//                TimeUnit.SECONDS,
//                blockingQueue,
//                new ThreadFactoryBuilder().setNameFormat(threadPoolName).build());
//
//        try {
//
//            List<Long> disputeIdList = ESIpTaoBaoExchange.selectExchangeOrderByOccupancyStatus(0, 1500, OccupancyStatusEnum.STOCK_OUT.getVal());
//            if (CollectionUtils.isEmpty(disputeIdList)) {
//                if(log.isDebugEnabled()) {
//                    log.info("换货待处理没有查询到处理的数据");
//                }
//                result.setSuccess(true);
//                return result;
//            }
//
//            //数据切割
//            int maxNum = 500;
//            int limit = (disputeIdList.size() + maxNum - 1) / maxNum;
//            List<List<Long>> splitList = Stream.iterate(0, n -> n + 1).limit(limit).parallel().map(a -> disputeIdList.stream().skip(a * maxNum).limit(maxNum).parallel().collect(Collectors.toList())).collect(Collectors.toList());
//
//            List<Future<Boolean>> results = new ArrayList<Future<Boolean>>();
//            for (List<Long> list : splitList) {
//                results.add(executor.submit(new AutoExchangeRefuseOrderTask.CallableExchangeAgreeTaskWithResult(list)));
//            }
//            //线程执行结果获取
//            /*for (Future<Boolean> futureResult : results) {
//                try {
//                    log.debug("AutoExchangeRefuseOrderTask------>线程结果:" + futureResult.get().toString());
//                } catch (InterruptedException e) {
//                    log.error("AutoExchangeRefuseOrderTask多线程获取InterruptedException异常：" + e);
//                } catch (ExecutionException e) {
//                    log.error("AutoExchangeRefuseOrderTask多线程获取ExecutionException异常：" + e);
//                }
//            }*/
//
//            result.setSuccess(true);
//        } catch (Exception ex) {
//            log.error("AutoExchangeRefuseOrderTask换货同意任务执行失败", ex);
//            result.setSuccess(false);
//            result.setMessage(ex.getMessage());
//        } finally {
//            executor.shutdown();
//        }
//        return result;
//    }
//
//    /**
//     * 开启线程类
//     */
//    class CallableExchangeAgreeTaskWithResult implements Callable<Boolean> {
//
//        private List<Long> list;
//
//        public CallableExchangeAgreeTaskWithResult(List<Long> list) {
//            this.list = list;
//        }
//
//        @Override
//        public Boolean call() throws Exception {
//
//            handleExchangeAgreeOrRefuse(list);
//
//            return true;
//        }
//    }
//
//    private void handleExchangeAgreeOrRefuse(List<Long> disputeIdList) {
//        if (CollectionUtils.isEmpty(disputeIdList)) {
//            return;
//        }
//        if(log.isDebugEnabled()) {
//            log.info("换货待处理拒绝数据{}",disputeIdList.toString());
//        }
//        ipTaobaoExchangeService.handleExchangeAgreeOrRefuse(disputeIdList, AgreeOrRefuseEnum.REFUSE.getVal());
//    }
//
//}
