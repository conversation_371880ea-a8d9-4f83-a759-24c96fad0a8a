package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.ReturnNewOrderQueryOrigOrderCmd;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * @author: ming.fz
 * @since: 2019-09-16
 */

@Api(value = "ReturnOrderCtrl", tags = "获取原单；更新退单")
@Slf4j
@RestController
public class ReturnOrderCtrl {

    @Autowired
    private ReturnNewOrderQueryOrigOrderCmd returnNewOrderQueryOrigOrderCmd;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    /**
     * 获取原单
     *
     * @param request
     * @param obj
     * @return
     */
    @ApiOperation(value = "获取原单")
    @RequestMapping(value = "/api/cs/oc/oms/v1/queryOcBOrder", method = RequestMethod.POST)
    public JSONObject queryOcBOrder(HttpServletRequest request,
                                    @RequestBody JSONObject obj) {
        ValueHolderV14 vh;
        //获取当前登陆用户
        //User user = ReturnOrderCtrl.getRootUser();

        //获取用户登录信息
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        if (obj == null) {
            throw new NDSException(Resources.getMessage("请求参数不能为空!", user.getLocale()));
        }

        try {
            vh = returnNewOrderQueryOrigOrderCmd.queryOcBOrder(obj, user);
            return vh.toJSONObject();
        } catch (NDSException e) {
            vh = new ValueHolderV14();
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return vh.toJSONObject();

        }
    }


    /**
     * 批量修改原单
     *
     * @param request
     * @param obj
     * @return
     */
    @ApiOperation(value = "批量修改原单")
    @RequestMapping(value = "/api/cs/oc/oms/v1/updateReturnBOrder", method = RequestMethod.POST)
    public JSONObject updateReturnBOrder(HttpServletRequest request,
                                         @RequestBody JSONObject obj) {
        //记录日志信息
        if (log.isDebugEnabled()) {
            log.debug("start ReturnOrderCtrl.updateReturnBOrder.ReceiveParams=" + obj);
        }

        ValueHolderV14 vh;
        //获取当前登陆用户
        //User user = ReturnOrderCtrl.getRootUser();

        //获取用户登录信息
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        if (obj == null) {
            throw new NDSException(Resources.getMessage("请求参数不能为空!", user.getLocale()));
        }

        try {
            vh = returnNewOrderQueryOrigOrderCmd.updateReturnBOrder(obj, user);

            //记录日志信息。Finish 标记结束
            if (log.isDebugEnabled()) {
                log.debug("Finish ReturnOrderCtrl.updateReturnBOrder.ReceiveParams=" + vh.toJSONObject());
            }
            return vh.toJSONObject();
        } catch (NDSException e) {
            vh = new ValueHolderV14();
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            log.error("ming.fz退单更新失败：" + e.toString());
            return vh.toJSONObject();

        }
    }

    /**
     * 批量修改原单
     *
     * @param request
     * @param obj
     * @return
     */
    @ApiOperation(value = "退单次品sku调拨")
    @RequestMapping(value = "/api/cs/oc/oms/v1/returnSkuDb", method = RequestMethod.POST)
    public JSONObject returnSkuDb(HttpServletRequest request,
                                  @RequestBody JSONObject obj) {
        //记录日志信息
        if (log.isDebugEnabled()) {
            log.debug("start ReturnOrderCtrl.returnSkuDb.ReceiveParams=" + obj);
        }

        ValueHolderV14 vh;
        //获取当前登陆用户
        //User user = ReturnOrderCtrl.getRootUser();

        //获取用户登录信息
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        if (obj == null) {
            throw new NDSException(Resources.getMessage("请求参数不能为空!", user.getLocale()));
        }

        try {
            vh = returnNewOrderQueryOrigOrderCmd.skuDb(obj.getLong("id"), user);

            //记录日志信息。Finish 标记结束
            if (log.isDebugEnabled()) {
                log.debug("Finish ReturnOrderCtrl.updateReturnBOrder.ReceiveParams=" + vh.toJSONObject());
            }
            return vh.toJSONObject();
        } catch (NDSException e) {
            vh = new ValueHolderV14();
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            log.error("ming.fz退单更新失败：" + e.toString());
            return vh.toJSONObject();

        }
    }
  /*  private static User getRootUser() {
        UserImpl user = new UserImpl();
        user.setId(893);
        user.setName("admin");
        user.setEname("Pokemon-mapper");
        user.setActive(true);
        user.setClientId(37);
        user.setOrgId(27);
        user.setIsAdmin(2);
        user.setIsDev(2);
        return user;
    }*/

}

