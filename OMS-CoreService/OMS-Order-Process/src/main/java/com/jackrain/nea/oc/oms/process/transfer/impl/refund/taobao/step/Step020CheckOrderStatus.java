package com.jackrain.nea.oc.oms.process.transfer.impl.refund.taobao.step;

import com.alibaba.fastjson.JSON;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.TransNodeTipEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsTaobaoRefundRelation;
import com.jackrain.nea.oc.oms.model.resources.SysNotesConstant;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.refund.util.TransRefundNodeTipUtil;
import com.jackrain.nea.oc.oms.services.OmsBeforeShipmentReturnService;
import com.jackrain.nea.oc.oms.services.OmsReturnOrderService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2020/2/27 3:12 下午
 * @Version 1.0
 */
@Step(order = 20, description = "判断原单的状态")
@Slf4j
@Component
public class Step020CheckOrderStatus extends BaseTaobaoRefundProcessStep
        implements IOmsOrderProcessStep<OmsTaobaoRefundRelation> {

    @Autowired
    private OmsReturnOrderService omsReturnOrderService;
    @Autowired
    private OmsBeforeShipmentReturnService omsBeforeShipmentReturnService;

    @Override
    public ProcessStepResult<OmsTaobaoRefundRelation> startProcess(OmsTaobaoRefundRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        log.debug(LogUtil.format("Step020CheckOrderStatus.startProcess:{}",
                "Step020CheckOrderStatus"), JSON.toJSONString(orderInfo));
        IpBTaobaoRefund ipBTaobaoRefund = orderInfo.getIpBTaobaoRefund();
        String status = ipBTaobaoRefund.getStatus();
        //获取订单信息
        List<OmsOrderRelation> omsOrderRelation = orderInfo.getOmsOrderRelation();
        List<OmsOrderRelation> isGiftOrderRelation = orderInfo.getIsGiftOrderRelation();
        List<OmsOrderRelation> giftItemRelation = orderInfo.getGiftItemRelation();
        String message = "";
        if (CollectionUtils.isEmpty(omsOrderRelation)) {
            // 判断isGiftOrderRelation是否为空 如果不是空 则直接取消
            if (CollectionUtils.isNotEmpty(isGiftOrderRelation)) {
                try {
                    omsReturnOrderService.giftsThenSend(omsOrderRelation, isGiftOrderRelation, orderInfo.getIntermediateTableRelation(), operateUser);
                } catch (Exception e) {
                    ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(),
                            message, ipBTaobaoRefund);
                    return new ProcessStepResult<>(StepStatus.FAILED, "赠品取消失败,等待下次执行");
                }
                ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                        message, ipBTaobaoRefund);
                return new ProcessStepResult<>(StepStatus.FINISHED, "订单处理完成");
            }

            if (CollectionUtils.isNotEmpty(giftItemRelation) && (TaobaoReturnOrderExt.RefundStatus.WAIT_SELLER_AGREE.getCode().equals(status) || TaobaoReturnOrderExt.RefundStatus.SUCCESS.getCode().equals(status))) {
                try {
                    for (OmsOrderRelation giftRelation : giftItemRelation) {
                        omsBeforeShipmentReturnService.refundGiftItem(giftRelation, operateUser);
                    }
                } catch (Exception e) {
                    ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(),
                            message, ipBTaobaoRefund);
                    return new ProcessStepResult<>(StepStatus.FAILED, "赠品取消失败,等待下次执行");
                }
                ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                        message, ipBTaobaoRefund);
                return new ProcessStepResult<>(StepStatus.FINISHED, "订单处理完成");
            }


            //原单信息不存在超过三天
            if (checkReturnOrderData(ipBTaobaoRefund)) {
                message = SysNotesConstant.SYS_REMARK2;
                TransRefundNodeTipUtil.taoBaoTransTipCAS(ipBTaobaoRefund, TransNodeTipEnum.ORDER_NOT_FOUND);
                ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(), message,
                        ipBTaobaoRefund);
            } else {
                if (CollectionUtils.isNotEmpty(isGiftOrderRelation) && !checkOrderEffective(isGiftOrderRelation)) {
                    log.debug(LogUtil.format("Step020CheckOrderStatus 无有效订单仅赠品取消:{}",
                            "Step020CheckOrderStatus"), JSON.toJSONString(orderInfo));
                    try {
                        omsReturnOrderService.giftsThenSend(omsOrderRelation, isGiftOrderRelation, orderInfo.getIntermediateTableRelation(), operateUser);
                    } catch (Exception e) {
                        ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(),
                                message, ipBTaobaoRefund);
                        return new ProcessStepResult<>(StepStatus.FAILED, "赠品取消失败,等待下次执行");
                    }
                    ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                            message, ipBTaobaoRefund);
                    return new ProcessStepResult<>(StepStatus.FINISHED, "订单处理完成");
                } else if (CollectionUtils.isNotEmpty(isGiftOrderRelation) && checkOrderEffective(isGiftOrderRelation)) {
                    ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                            "赠品订单已作废,直接标记转换成功", ipBTaobaoRefund);
                    return new ProcessStepResult<>(StepStatus.FINISHED, "订单处理完成");
                }
                message = SysNotesConstant.SYS_REMARK1;
                TransRefundNodeTipUtil.taoBaoTransTipCAS(ipBTaobaoRefund, TransNodeTipEnum.ORDER_NOT_FOUND);
                ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(),
                        message, ipBTaobaoRefund);
            }
            return new ProcessStepResult<>(StepStatus.FAILED, message + "转换失败");
        } else {
            //判断是否存在有效的原始订单
            if (checkOrderEffective(omsOrderRelation)) {
                if (CollectionUtils.isNotEmpty(isGiftOrderRelation) && !checkOrderEffective(isGiftOrderRelation)) {
                    log.debug(LogUtil.format("Step020CheckOrderStatus 无有效订单仅赠品取消:{}",
                            "Step020CheckOrderStatus"), JSON.toJSONString(orderInfo));
                    try {
                        omsReturnOrderService.giftsThenSend(omsOrderRelation, isGiftOrderRelation, orderInfo.getIntermediateTableRelation(), operateUser);
                    } catch (Exception e) {
                        ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(),
                                message, ipBTaobaoRefund);
                        return new ProcessStepResult<>(StepStatus.FAILED, "赠品取消失败,等待下次执行");
                    }
                    ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                            message, ipBTaobaoRefund);
                    return new ProcessStepResult<>(StepStatus.FINISHED, "订单处理完成");
                }
                message = SysNotesConstant.SYS_REMARK4;
                ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                        message, ipBTaobaoRefund);
                return new ProcessStepResult<>(StepStatus.FINISHED, message + "转换完成");
            }
        }
        return new ProcessStepResult<>(StepStatus.SUCCESS, "判断原单状态成功,进入下一阶段");
    }


    /**
     * 检验原单不存在的时间
     *
     * @return
     */
    private boolean checkReturnOrderData(IpBTaobaoRefund ipBTaobaoRefund) {
        Date date = new Date();
        //判断退单创建时间是否超过三天
        Date created = ipBTaobaoRefund.getCreationdate();
        Long threeDays = 5 * 24 * 60 * 60 * 1000L + created.getTime();
        return threeDays < date.getTime();
    }

    /**
     * 判断订单是否有效
     *
     * @param omsOrderRelation
     * @return
     */
    private boolean checkOrderEffective(List<OmsOrderRelation> omsOrderRelation) {
        for (int i = 0; i < omsOrderRelation.size(); i++) {
            OmsOrderRelation omsOrderRelation1 = omsOrderRelation.get(i);
            OcBOrder ocBOrder = omsOrderRelation1.getOcBOrder();
            Integer orderStatus = ocBOrder.getOrderStatus();
            if (OmsOrderStatus.CANCELLED.toInteger().equals(orderStatus)
                    || OmsOrderStatus.SYS_VOID.toInteger().equals(orderStatus)) {
                String suffixInfo = ocBOrder.getSuffixInfo();
                if (!"REFUND-VOID".equals(suffixInfo)) {
                    omsOrderRelation.remove(omsOrderRelation1);
                    i--;
                }
            }
        }
        return CollectionUtils.isEmpty(omsOrderRelation);
    }
}
