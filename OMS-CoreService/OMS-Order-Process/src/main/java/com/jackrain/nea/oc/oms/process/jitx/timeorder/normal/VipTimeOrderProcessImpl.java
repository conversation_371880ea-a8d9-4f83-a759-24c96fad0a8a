package com.jackrain.nea.oc.oms.process.jitx.timeorder.normal;

import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.MakeupOrderType;
import com.jackrain.nea.oc.oms.model.relation.IpVipTimeOrderRelation;
import com.jackrain.nea.oc.oms.process.AbstractOrderProcess;
import com.jackrain.nea.oc.oms.util.LockOrderType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description 时效订单转单实现类
 * @Date 2019-8-19
 **/
@Component
public class VipTimeOrderProcessImpl extends AbstractOrderProcess<IpVipTimeOrderRelation> {

    public VipTimeOrderProcessImpl() {
        super();
    }

    @Override
    protected String getChildPackageName() {
        return "jitx";
    }

    @Override
    protected long getProcessOrderId(IpVipTimeOrderRelation orderInfo) {
        return orderInfo.getOrderId();
    }

    @Override
    protected String getProcessOrderNo(IpVipTimeOrderRelation orderInfo) {
        return orderInfo.getOccupiedOrderSn();
    }

    @Override
    protected LockOrderType getCurrentLockOrderType() {
        return LockOrderType.TRANSFER_JITX_TIMEORDER;
    }

    @Override
    protected ChannelType getCurrentChannelType() {
        return ChannelType.VIPJITX;
    }

    @Override
    protected MakeupOrderType getCurrentMakeupOrderType() {
        return MakeupOrderType.DO_NOT_MAKEUP;
    }

}
