package com.jackrain.nea.oc.oms.task.appointLogistics;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrderAppointLogistics;
import com.jackrain.nea.oc.oms.services.OcBOrderAppointLogisticsMapperService;
import com.jackrain.nea.oc.oms.services.OcBOrderAppointLogisticsService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.ListSplitUtil;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;

/**
 * @ClassName CancelAutoAppointLogisticsTask
 * @Description 取消自动指定快递的订单
 * <AUTHOR>
 * @Date 2024/4/17 10:56
 * @Version 1.0
 */
@Slf4j
@Component
public class CancelAutoAppointLogisticsTask extends BaseR3Task implements IR3Task {

    private final static String TABLE_NAME = "oc_b_order_appoint_logistics";
    @Autowired
    private OcBOrderAppointLogisticsMapperService appointLogisticsMapperService;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderAppointLogisticsService ocBOrderAppointLogisticsService;
    @Autowired
    private ThreadPoolTaskExecutor batchCancelAppointLogisticsPollExecutor;

    @Override
    @XxlJob("CancelAutoAppointLogisticsTask")
    public RunTaskResult execute(JSONObject params) {

        RunTaskResult result = new RunTaskResult();

        try {
            List<Future<Boolean>> results = new ArrayList<>();
            List<OcBOrderAppointLogistics> appointLogisticsList = appointLogisticsMapperService.select4CancelAppointLogistics(DateUtil.now(), TABLE_NAME);
            if (CollectionUtils.isEmpty(appointLogisticsList)) {
                result.setSuccess(true);
                result.setMessage("没有需要取消的订单");
                return result;
            }
            List<List<OcBOrderAppointLogistics>> lists = ListSplitUtil.averageAssign(appointLogisticsList, 24);
            // 一个node查询一个库，每个线程查询一个库的数据做处理
            for (List<OcBOrderAppointLogistics> data : lists) {
                results.add(batchCancelAppointLogisticsPollExecutor.submit(
                        new CancelAutoAppointLogisticsTask.CancelAutoAppointLogisticsCallable(data)));
            }
            results.forEach(futureResult -> {
                try {
                    log.debug(LogUtil.format("CancelAutoAppointLogisticsTask------>线程结果:{}",
                            "CancelAutoAppointLogisticsTask"), futureResult.get().toString());
                } catch (Exception e) {
                    log.error(LogUtil.format("CancelAutoAppointLogisticsTask,异常：{}",
                            "CancelAutoAppointLogisticsTask"), Throwables.getStackTraceAsString(e));
                }
            });
            result.setSuccess(true);
        } catch (Exception ex) {
            log.error(LogUtil.format("CancelAutoAppointLogisticsTask,异常：{}",
                    "CancelAutoAppointLogisticsTask"), Throwables.getStackTraceAsString(ex));
            result.setSuccess(false);
            result.setMessage(ex.getMessage());
        }
        return result;
    }

    class CancelAutoAppointLogisticsCallable implements Callable<Boolean> {

        private final List<OcBOrderAppointLogistics> data;

        public CancelAutoAppointLogisticsCallable(List<OcBOrderAppointLogistics> data) {
            this.data = data;
        }

        @Override
        public Boolean call() throws Exception {
            if (CollectionUtils.isEmpty(data)) {
                return true;
            }
            // 获取appointLogisticsList中的订单id
            List<Long> orderIds = new ArrayList<>();
            data.forEach(appointLogistics -> {
                orderIds.add(appointLogistics.getOrderId());
            });
            // 查询零售发货单状态 查出来的数据都不是待寻源的 可以在中间表把数据进行逻辑删除
            List<Long> orderStatusList = ocBOrderMapper.select4CancelAppointLogistics(orderIds);
            if (CollectionUtils.isNotEmpty(orderStatusList)) {
                appointLogisticsMapperService.deleteByOrderIds(orderStatusList);
            }
            // 校验完成之后 进行取消
            orderIds.removeAll(orderStatusList);
            if (CollectionUtils.isEmpty(orderIds)) {
                return true;
            }
            for (Long orderId : orderIds) {
                try {
                    ocBOrderAppointLogisticsService.orderCancelAppointLogistics(orderId, SystemUserResource.getRootUser(), true);
                    appointLogisticsMapperService.deleteByOrderId(orderId);
                } catch (Exception e) {
                    log.error(LogUtil.format("CancelAutoAppointLogisticsCallable, 订单id : {}, 异常：{}",
                            "CancelAutoAppointLogisticsCallable"), orderId, Throwables.getStackTraceAsString(e));
                }
            }
            return true;
        }
    }
}
