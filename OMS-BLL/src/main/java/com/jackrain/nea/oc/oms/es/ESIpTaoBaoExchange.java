package com.jackrain.nea.oc.oms.es;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.oc.oms.model.enums.ExchangeOccupancyOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;

import java.util.ArrayList;
import java.util.List;

public class ESIpTaoBaoExchange {
    /**
     * 根据占单状态查询需要同意或换货的订单单号'
     *
     * @param pageIndex
     * @param pageSize
     * @return
     */
    public static List<Long> selectExchangeOrderByOccupancyStatus(int pageIndex, int pageSize, int type) {
        List<Long> list = new ArrayList<>();
        int startIndex = pageIndex * pageSize;
        if (startIndex < 0) {
            startIndex = 0;
        }
        JSONObject orderKes = new JSONObject();
        orderKes.put("name", "ID");
        orderKes.put("asc", false);
        JSONArray orderKeys = new JSONArray();
        orderKeys.add(orderKes);

        JSONObject whereKeys = new JSONObject();
        String[] returnFileds = {"DISPUTE_ID"};
        //whereKeys.put("ISTRANS", TransferOrderStatus.NOT_TRANSFER.toInteger());
        whereKeys.put("STATUS", "换货待处理");
        //未处理
        whereKeys.put("OCCUPANCY_DISPOSE_STATUS", ExchangeOccupancyOrderStatus.NOTOCCUPANCY.getVal());
        //占单状态
        whereKeys.put("OCCUPANCY_STATUS", type);

        JSONObject search = ElasticSearchUtil.search(TaobaoReturnOrderExt.TABLENAME_IPBTAOBAOEXCHANGE,
                TaobaoReturnOrderExt.TABLENAME_IPBTAOBAOEXCHANGE, whereKeys, null,
                orderKeys, pageSize, startIndex, returnFileds);
        if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
            JSONArray arrayObj = search.getJSONArray("data");
            for (Object obj : arrayObj) {
                JSONObject jsonObject = (JSONObject) obj;
                list.add(jsonObject.getLong("DISPUTE_ID"));
            }
        }
        return list;
    }

    /**
     * 退单补偿服务 查询未转换的数据
     *
     * 根据转换状态获取获取退单中间表数据分库键
     * @param pageIndex 起始页
     * @param pageSize  每页显示条数
     * @return 中间表数据分库键
     */
    public static List<Long> selectExchangeOrder(int pageIndex, int pageSize) {
        List<Long> list = new ArrayList<>();
        int startIndex = pageIndex * pageSize;
        if (startIndex < 0) {
            startIndex = 0;
        }
        JSONObject orderKes = new JSONObject();
        orderKes.put("name", "ID");
        orderKes.put("asc", false);
        JSONArray orderKeys = new JSONArray();
        orderKeys.add(orderKes);

        JSONObject whereKeys = new JSONObject();
        String[] returnFileds = {"DISPUTE_ID"};
        whereKeys.put("ISTRANS", TransferOrderStatus.NOT_TRANSFER.toInteger());
        JSONObject search = ElasticSearchUtil.search(TaobaoReturnOrderExt.TABLENAME_IPBTAOBAOEXCHANGE,
                TaobaoReturnOrderExt.TABLENAME_IPBTAOBAOEXCHANGE, whereKeys, null,
                orderKeys, pageSize, startIndex, returnFileds);
        if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
            JSONArray arrayObj = search.getJSONArray("data");
            for (Object obj : arrayObj) {
                JSONObject jsonObject = (JSONObject) obj;
                list.add(jsonObject.getLong("DISPUTE_ID"));
            }
        }
        return list;
    }

}
