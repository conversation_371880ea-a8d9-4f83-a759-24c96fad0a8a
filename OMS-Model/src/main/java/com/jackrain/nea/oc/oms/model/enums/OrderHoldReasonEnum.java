package com.jackrain.nea.oc.oms.model.enums;

/**
 * @Description hold单原因
 * @author: 江家雷
 * @since: 2020-07-03
 * create at:  2020-07-03 19:12
 */
public enum OrderHoldReasonEnum {

    LIVECAST_HOLD(1, "直播hold单"),

    BUYER_HOLD(2, "买家hold单"),

    REFUND_HOLD(3, "退款hold单"),

    JITX_HOLD(4, "JITX修改地址hold单"),

    ADD_EXCHANGE_ORDER(50, "退换货订单"),

    RETURN_AUDIT_COMPLETE(60, "退换货单审核完成"),

    RETURN_VIRTUAL_IN(70, "退换货单虚拟入库"),

    RETURN_FORCE_COMPLETE(80, "退换货单强制完成"),

    OC_B_ORDER_ERROR_HOLD(90, "审单Hold单-异常单Hold单"),

    ELE_PRE_HOLD(5, "预售hold单"),

    GOODS_HOLD(6, "普通商品hold单"),

    GIFT_AFTER(12, "赠品后发hold单"),

    TB_AUDIT_VOLUNTARILY_HOLD(7, "TB自助修改地址"),
    JITX_FORBIDDEN_DELIVERY(10, "JITX订单当前是否可发货"),
    PROVINCE_HOLD(8, "省市区hold单");
    public static String getMessageByKey(int key) {
        for (OrderHoldReasonEnum e : OrderHoldReasonEnum.values()) {
            if (e.getKey().equals(key)) {
                return e.getMessage();
            }
        }
        return null;
    }

    private Integer key;

    private String message;

    OrderHoldReasonEnum(int key, String message) {
        this.key = key;
        this.message = message;
    }

    public Integer getKey() {
        return key;
    }

    public String getMessage() {
        return message;
    }
}
