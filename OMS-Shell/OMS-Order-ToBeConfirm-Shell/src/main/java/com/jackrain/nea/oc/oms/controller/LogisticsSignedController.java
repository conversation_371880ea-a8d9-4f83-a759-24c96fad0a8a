package com.jackrain.nea.oc.oms.controller;

import com.jackrain.nea.oc.api.model.LogisticsSignedRequest;
import com.jackrain.nea.oc.oms.api.LogisticsSignedFiCmd;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @create 2024-05-13
 * @desc 物流签收信息处理控制器
 **/
@Slf4j
@RestController
@RequestMapping("/api/logistics")
@Api(tags = "物流签收信息处理")
public class LogisticsSignedController {

    @Reference(version = "1.0", group = "oms-fi", check = false)
    private LogisticsSignedFiCmd logisticsSignedFiCmd;

    /**
     * 处理已签收的物流单号
     *
     * @param logisticsSignedRequest 物流签收信息请求
     * @return 处理结果
     */
    @PostMapping("/signed")
    @ApiOperation(value = "处理已签收的物流单号", notes = "根据物流单号查询退换货单信息")
    public ValueHolderV14 processSignedLogistics(@RequestBody LogisticsSignedRequest logisticsSignedRequest) {
        log.info("接收到物流签收信息请求: {}", logisticsSignedRequest);
        return logisticsSignedFiCmd.processSignedLogistics(logisticsSignedRequest);
    }
}
