package com.jackrain.nea.oc.oms.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * 自动拆单任务，topic,tag
 *
 * @author: hulinyang
 * @since: 2019/7/31
 * create at : 2019/7/31 10:45
 */
@Configuration
@Data
public class AutoSplitOrderMqConfig {

    @Value("${r3.oc.oms.split.mq.topic:}")
    private String sendSplitMqTopic;

    @Value("${r3.oc.oms.split.mq.tag:}")
    private String sendSplitTag;

    @Value("${r3.oc.oms.split.by.goods.mq.topic:}")
    private String sendSplitOrderByGoodsMqTopic;

    @Value("${r3.oc.oms.split.by.goods.mq.tag:}")
    private String sendSplitOrderByGoodsTag;
}