package com.jackrain.nea.oc.oms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jackrain.nea.oc.oms.model.table.StCCycleItemStrategy;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @ClassName StCCycleItemStrategyMapper
 * @Description 周期购促销
 * <AUTHOR>
 * @Date 2024/8/19 15:07
 * @Version 1.0
 */
@Mapper
public interface StCCycleItemStrategyMapper extends BaseMapper<StCCycleItemStrategy> {

    @Delete("DELETE FROM st_c_cycle_item_strategy WHERE strategy_id = #{strategyId}")
    int deleteByStrategyId(@Param("strategyId") Long strategyId);

    @Select("SELECT * FROM st_c_cycle_item_strategy WHERE strategy_id = #{strategyId} and isactive = 'Y'")
    List<StCCycleItemStrategy> selectByStrategyIdWithActive(Long strategyId);

    @Select("SELECT * FROM st_c_cycle_item_strategy WHERE strategy_id = #{strategyId} and cycle_num = #{cycleNum}")
    List<StCCycleItemStrategy> selectByStrategyIdAndCycleNumber(@Param("strategyId") Long strategyId, @Param("cycleNum") Integer cycleNum);

    @Select("SELECT * FROM st_c_cycle_item_strategy WHERE strategy_id = #{strategyId}")
    List<StCCycleItemStrategy> selectByStrategyIdWithOutActive(Long strategyId);
}
