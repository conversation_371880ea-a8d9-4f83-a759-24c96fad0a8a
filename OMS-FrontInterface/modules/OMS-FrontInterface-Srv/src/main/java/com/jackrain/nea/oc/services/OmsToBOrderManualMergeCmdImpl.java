package com.jackrain.nea.oc.services;

import com.jackrain.nea.oc.oms.api.OmsToBOrderManualMergeCmd;
import com.jackrain.nea.oc.oms.services.OmsToBOrderManualMergeService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2022/10/24 下午1:44
 * @Version 1.0
 */
@Component
public class OmsToBOrderManualMergeCmdImpl implements OmsToBOrderManualMergeCmd {

    @Autowired
    private OmsToBOrderManualMergeService omsToBOrderManualMergeService;


    @Override
    public ValueHolderV14 orderManualMergeService(List<Long> ids, User user) {
        return omsToBOrderManualMergeService.orderManualMergeService(ids, user);
    }
}
