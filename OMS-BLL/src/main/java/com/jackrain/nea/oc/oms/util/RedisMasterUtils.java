package com.jackrain.nea.oc.oms.util;

import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.redis.util.R3RedisSlaverUtil;

/**
 * <AUTHOR>
 * @date 2020/10/26 下午6:17
 * @description redis 工具类
 **/
public class RedisMasterUtils {
    /**
     * oms-redis
     */
    private static final String OMS_MASTER = "oms-master";


    /**
     * 获取锁的实例
     *
     * @return
     */
    public static RedisReentrantLock getReentrantLock(String lockId) {
        return new RedisReentrantLock(lockId, OMS_MASTER);
    }


    /**
     * 获取oms-redis存储业务数据的redis实例
     */
    public static <K, V> CusRedisTemplate<K, V> getObjRedisTemplate() {
        return R3RedisSlaverUtil.getObjRedisTemplate(OMS_MASTER);
    }

    /**
     * 获取oms-redis存储业务数据的redis实例
     */
    public static <K, V> CusRedisTemplate<K, V> getStrRedisTemplate() {
        return R3RedisSlaverUtil.getStrRedisTemplate(OMS_MASTER);
    }

    /**
     * 获取oms-redis存储业务数据的redis实例
     *
     * @param clazz 返回V类型
     * @return
     */
    public static <K, V> CusRedisTemplate<K, V> getObjRedisTemplate(Class<V> clazz) {
        return R3RedisSlaverUtil.getObjRedisTemplate(OMS_MASTER);
    }
}
