package com.jackrain.nea.oc.oms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jackrain.nea.oc.oms.model.table.StCImperfectStrategy;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 残次策略
 *
 * <AUTHOR>
 */
@Mapper
public interface StCImperfectStrategyMapper extends BaseMapper<StCImperfectStrategy> {

    @Select("SELECT * FROM st_c_imperfect_strategy WHERE shop_id = #{shopId} and ISACTIVE = 'Y'")
    List<StCImperfectStrategy> selectByShopId(@Param("shopId") Long shopId);

    @Update("UPDATE st_c_imperfect_strategy SET ISACTIVE = 'Y',modifieddate = now() WHERE ID = #{id}")
    int activeStrategy(Long id);

    @Update("UPDATE st_c_imperfect_strategy SET ISACTIVE = 'N',modifieddate = now() WHERE ID = #{id}")
    int voidStrategy(Long id);
}
