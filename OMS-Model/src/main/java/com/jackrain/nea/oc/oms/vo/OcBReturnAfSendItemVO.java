package com.jackrain.nea.oc.oms.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @ClassName : OcBReturnAfSendItemVO  
 * @Description : 
 * <AUTHOR>  YCH
 * @Date: 2021-08-31 15:07  
 */
@Data
public class OcBReturnAfSendItemVO implements Serializable {

    private BigDecimal qty;//数量
    private String psCSkuEcode;//sku编码
    private BigDecimal amtReturn; //明细退款金额
    private BigDecimal freight; //运费

}
