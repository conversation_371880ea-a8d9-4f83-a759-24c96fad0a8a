package com.jackrain.nea.oc.oms.process.transfer.impl.refund.alibaba.ascp.refund.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpBAlibabaAscpOrderRefundRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBAlibabaAscpOrderRefund;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: gxx
 * @Date: 2020/6/5 11:40
 * @Version 1.0
 */
@Step(order = 53, description = "判断订单是否为仓库发货")
@Slf4j
@Component
public class Step053CheckAlibabaAscpOrderRefundWareHomeStatus extends BaseAlibabaAscpOrderRefundProcessStep
        implements IOmsOrderProcessStep<IpBAlibabaAscpOrderRefundRelation> {
    @Override
    public ProcessStepResult<IpBAlibabaAscpOrderRefundRelation> startProcess(IpBAlibabaAscpOrderRefundRelation orderInfo, ProcessStepResult preStepResult,
                                                                             boolean isAutoMakeup, User operateUser) {
        List<OmsOrderRelation> omsOrderRelations = orderInfo.getOmsOrderRelation();
        IpBAlibabaAscpOrderRefund orderRefund = orderInfo.getOrderRefund();
        int count = 0;
        for (OmsOrderRelation omsOrderRelation : omsOrderRelations) {
            Integer orderMark = omsOrderRelation.getOrderMark();
            if (TaobaoReturnOrderExt.ReturnBillsStatus.GOODS_FRONT.getCode().equals(orderMark)
                    || TaobaoReturnOrderExt.ReturnBillsStatus.WAREHOUSE_DELIVERY.getCode().equals(orderMark)) {
                count++;
            }
        }
        if (count == omsOrderRelations.size()) {
            String remark = "仓库发货,生成发货前退款单(仅退款)成功";
            orderRefundService.foundRefundFrontRefundOnly(omsOrderRelations.get(0).getOcBOrder(), orderRefund, operateUser);
            orderRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                    remark, orderRefund);
            return new ProcessStepResult<>(StepStatus.FINISHED, remark);
        }
        return new ProcessStepResult<>(StepStatus.SUCCESS, "仓库发货处理完成,进入下一阶段");
    }
}
