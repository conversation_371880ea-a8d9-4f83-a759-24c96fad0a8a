package com.jackrain.nea.oc.oms.task.jitxorder;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.model.table.OcBJitxModifyWarehouseLog;
import com.jackrain.nea.oc.oms.nums.VipJitxWorkflowCreatedStateEnum;
import com.jackrain.nea.oc.oms.nums.VipJitxWorkflowStateEnum;
import com.jackrain.nea.oc.oms.services.JitxService;
import com.jackrain.nea.oc.oms.services.OcBJitxModifyWarehouseLogService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.web.face.User;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.time.FastDateFormat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2020-12-15
 * @desc JITX订单获取改仓工单任务
 **/
@Slf4j
@Component
public class AutoJitxGetChangeWarehouseTask extends BaseR3Task implements IR3Task {


    private static final FastDateFormat FAST_DATE_FORMAT = FastDateFormat.getInstance("yyyy-MM-dd HH:mm:ss");
    private static final DateTimeFormatter LOCAL_DATE_PARSE = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private final String tableName = "oc_b_jitx_modify_warehouse_log";
    private final String threadPoolName = "R3_OMS_JITX_ORDER_MODIFY_WAREHOUSE_THREAD_POOL_%d";


    @Autowired
    private JitxService jitxService;

    @Value("${oms.oc.order.jitx.getChangeWarehouse.pull.num:1000}")
    private Integer pullNum;

    @Autowired
    private ThreadPoolTaskExecutor commonThreadPoolExecutor;

    @Value("${oms.oc.order.jitx.changeWarehouse.max.failNumber:5}")
    private Integer failNumber;

    @Autowired
    private OcBJitxModifyWarehouseLogService ocBJitxModifyWarehouseLogService;

    public void executeRunnable(String tableName, User operateUser) {
        try {
            StringBuffer whereSql = new StringBuffer();
            whereSql.append(" WHERE ");
            whereSql.append(" CREATED_STATUS= ").append(VipJitxWorkflowCreatedStateEnum.CREATE_SUCCESS.getCode());
            whereSql.append(" AND WORKFLOW_STATE IN ('").append(VipJitxWorkflowStateEnum.CREATE.getKey()).append("'")
                    .append(",'").append(VipJitxWorkflowStateEnum.DOING.getKey()).append("')");
            LocalDateTime now = LocalDateTime.now();
            String nowStr = LOCAL_DATE_PARSE.format(now);
            whereSql.append(" AND CREATIONDATE <= '").append(nowStr).append("'")
                    .append(" AND CREATIONDATE > '").append(LOCAL_DATE_PARSE.format(now.minusDays(3))).append("'");
            whereSql.append(" AND NEXT_COMPENSATION_DATE < '" ).append(nowStr).append("'");
            whereSql.append(" AND ISACTIVE = 'Y' ");
            String orderSql = " ORDER BY MODIFIEDDATE ASC";
            List<OcBJitxModifyWarehouseLog> modifyWarehouseLogList = ocBJitxModifyWarehouseLogService.select(tableName, whereSql.toString(), orderSql, pullNum);
            if (CollectionUtils.isEmpty(modifyWarehouseLogList)) {
                if (log.isDebugEnabled()) {
                    log.debug("符合条件数据：空");
                } else {
                    log.info("符合条件数据：空");
                }
                return;
            }
            this.exceuteData(modifyWarehouseLogList, operateUser);
        } catch (Exception e) {
            log.error("JITX订单创建改仓工单任务异常：{}/{}", Throwables.getStackTraceAsString(e), tableName);
        }
    }

    private void exceuteData(List<OcBJitxModifyWarehouseLog> modifyWarehouseLogList, User operateUser) {
        if (log.isDebugEnabled()) {
            log.debug("JITX订单获取改仓工单任务，modifyWarehouseLogListSize：{}", modifyWarehouseLogList.size());
        }
        jitxService.getChangeWarehouseWorkflows(modifyWarehouseLogList, operateUser);
    }

    @Override
    @XxlJob("AutoJitxGetChangeWarehouseTask")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult result = new RunTaskResult();

        User operateUser = SystemUserResource.getRootUser();
        executeRunnable(tableName, operateUser);
        result.setSuccess(true);
        result.setMessage("任务执行完成");
        return result;
    }
}
