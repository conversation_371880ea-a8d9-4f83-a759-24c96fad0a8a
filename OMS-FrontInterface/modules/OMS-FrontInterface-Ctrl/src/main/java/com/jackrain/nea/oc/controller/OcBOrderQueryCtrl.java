package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBOrderInitQueryCmd;
import com.jackrain.nea.oc.oms.api.OcBOrderListQueryCmd;
import com.jackrain.nea.oc.oms.api.Pod2BOrderQueryCmd;
import com.jackrain.nea.oc.oms.model.relation.UserPermission;
import com.jackrain.nea.oc.oms.model.result.Pod2BOrderQueryRequest;
import com.jackrain.nea.oc.oms.model.result.Pod2BOrderQueryResult;
import com.jackrain.nea.oc.oms.model.result.QueryOrderListResult;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @author: xiWen.z
 * create at: 2019/3/11 0011
 */
@RestController
@Slf4j
@Api(value = "OcBOrderQueryCtrl", tags = "订单查询")
public class OcBOrderQueryCtrl {

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @Autowired
    private OcBOrderInitQueryCmd ocBOrderInitQueryCmd;

    @Autowired
    private OcBOrderListQueryCmd ocBOrderListQueryCmd;
    @Autowired
    private Pod2BOrderQueryCmd pod2BOrderQueryCmd;

    /**
     * 查询条件初始化
     *
     * @param request req
     * @param param   para
     * @return Str
     * @throws Exception ex
     */
    @ApiOperation(value = "查询条件初始化")
    @RequestMapping(path = "/api/cs/oc/oms/v1/getSeniorQueryCondition", method = RequestMethod.POST)
    public JSONObject getSeniorQueryCondition(HttpServletRequest request, @RequestParam(value = "param") String param) {

        User loginUser = r3PrimWebAuthService.getLoginPrimWebUser(request);
        ValueHolderV14 vh;
        UserPermission usrPem = UserPermissionCtrlHelper.currentUserPermission(loginUser, "OC_B_ORDER");
        if (usrPem == null) {
            vh = new ValueHolderV14<>();
            vh.setMessage("未获取到用户权限");
            vh.setCode(ResultCode.FAIL);
            return vh.toJSONObject();
        }


        vh = ocBOrderInitQueryCmd.queryConditionInit(param, loginUser, usrPem);
        String result = JSON.toJSONStringWithDateFormat(vh.toJSONObject(),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue,
                SerializerFeature.DisableCircularReferenceDetect);
        JSONObject o;
        try {
            o = JSONObject.parseObject(result);
            if (o == null) {
                o = vh.toJSONObject();
            }
        } catch (Exception e) {
            o = vh.toJSONObject();
        }
        return o;
    }

    /**
     * 列表查询
     *
     * @param request req
     * @param param   string
     * @return string str
     * @throws Exception ex
     */
    @ApiOperation(value = "列表查询")
    @RequestMapping(path = "/api/cs/oc/oms/v1/queryOrderList", method = RequestMethod.POST)
    public JSONObject getOrderList(HttpServletRequest request, @RequestParam(value = "param") String param) {
        User loginUser = r3PrimWebAuthService.getLoginPrimWebUser(request);

        ValueHolderV14<QueryOrderListResult> vh;
        UserPermission usrPem = UserPermissionCtrlHelper.currentUserPermission(loginUser, "OC_B_ORDER");

        if (usrPem == null) {
            vh = new ValueHolderV14<>();
            vh.setMessage("未获取到用户权限");
            vh.setCode(ResultCode.FAIL);
            return vh.toJSONObject();
        }

        vh = ocBOrderListQueryCmd.queryOrderList(param, loginUser, usrPem);
        return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
    }

    @RequestMapping(path = "/api/cs/oc/oms/v1/queryOrderList/test", method = RequestMethod.POST)
    public JSONObject test(@RequestBody Pod2BOrderQueryRequest request) {
        ValueHolderV14<Pod2BOrderQueryResult> result = pod2BOrderQueryCmd.queryByPage(request);
        return result.toJSONObject();
    }

    /**
     * 订单列表查询
     *
     * @param request req
     * @param param   string
     * @return string zip string
     * @throws Exception ex
     */
    @ApiOperation(value = "列表查询")
    @RequestMapping(path = "/api/cs/oc/oms/v1/getOrderList", method = RequestMethod.POST)
    public JSONObject getOrderListExt(HttpServletRequest request, @RequestParam(value = "param") String param) {

        User loginUser = r3PrimWebAuthService.getLoginPrimWebUser(request);

        ValueHolderV14<QueryOrderListResult> vh;
        try {
            vh = ocBOrderListQueryCmd.queryOrderList(loginUser, param);
            if (vh == null) {
                throw new NDSException("返回异常查询结果");
            }
        } catch (Exception e) {
            vh = new ValueHolderV14();
            vh.setMessage(e.getMessage());
            vh.setCode(ResultCode.FAIL);
            return vh.toJSONObject();
        }

        return vh.toJSONObject();
    }

    @ApiOperation(value = "平台单号查询订单id")
    @RequestMapping(path = "/api/cs/oc/oms/v1/getOrderId", method = RequestMethod.POST)
    public JSONObject getOrderId(HttpServletRequest request, @RequestParam(value = "param") String param) {

        User loginUser = r3PrimWebAuthService.getLoginPrimWebUser(request);

        ValueHolderV14 vh = new ValueHolderV14();
        try {
            List<OcBOrder> ocBOrderList = ocBOrderListQueryCmd.queryOrder(loginUser, param);
            if (ocBOrderList.size() > 1) {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage("当前平台单号存在多个零售发货单，请进入零售发货单页面查找查看");
                return vh.toJSONObject();
            }
            vh.setCode(ResultCode.SUCCESS);
            vh.setData(ocBOrderList.get(0).getId());
        } catch (Exception e) {
            vh.setMessage(e.getMessage());
            vh.setCode(ResultCode.FAIL);
            return vh.toJSONObject();
        }
        return vh.toJSONObject();
    }
}