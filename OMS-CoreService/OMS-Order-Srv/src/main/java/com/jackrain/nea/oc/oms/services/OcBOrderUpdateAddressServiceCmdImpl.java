package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.oc.oms.api.OcBOrderUpdateAddressServiceCmd;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 淘宝预售地址修改
 *
 * @date 2019/10/12
 * @author: ming.fz
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oc-core")
public class OcBOrderUpdateAddressServiceCmdImpl implements OcBOrderUpdateAddressServiceCmd {

    @Autowired
    OcBOrderUpdateAddressService ocBOrderUpdateAddressService;

    @Override
    public ValueHolderV14 updateOrderAddress(boolean bl, String tid, User user) {
        return ocBOrderUpdateAddressService.updateOrderAddress(bl, tid, user);
    }

    /**
     * 通过sourceCode获取订单
     *
     * @param sourcesCode
     * @return
     */
    @Override
    public List<OcBOrder> getOrderList(String sourcesCode) {
        //通过tid获取全渠道所有订单
        return ocBOrderUpdateAddressService.getOrderList(sourcesCode);
    }

    @Override
    public List<OcBOrderItem> getOrderItemList(Long orderId) {
        OcBOrderRelation orderRelation = ocBOrderUpdateAddressService.getOrderItemList(orderId);
        if (orderRelation != null) {
            return orderRelation.getOrderItemList();
        }
        return null;
    }


    @Override
    public ValueHolderV14<String> updateOrderAddress(String tid, User user) {
        return ocBOrderUpdateAddressService.updateOrderRegion(true, tid, user);
    }

}
