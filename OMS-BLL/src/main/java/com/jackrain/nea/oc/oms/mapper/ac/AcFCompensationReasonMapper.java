package com.jackrain.nea.oc.oms.mapper.ac;


import com.jackrain.nea.ac.model.AcFCompensationReason;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: anna
 * @CreateDate: 2020/7/10$ 20:43$
 * @Description:
 */
@Mapper
@Component
public interface AcFCompensationReasonMapper extends ExtentionMapper<AcFCompensationReason> {
    @Select("select * from ac_f_compensation_reason where ac_f_compensation_type_id = #{id}")
    List<AcFCompensationReason> queryByCompesationTypeId(@Param("id") Integer id);
}
