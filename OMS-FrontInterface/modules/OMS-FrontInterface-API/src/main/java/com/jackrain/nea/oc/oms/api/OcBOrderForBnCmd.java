package com.jackrain.nea.oc.oms.api;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.model.BnQueryForLogisticsProblemResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import org.springframework.web.multipart.MultipartFile;

/**
 * @ClassName OcBOrderForBnCmd
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/15 16:08
 * @Version 1.0
 */
public interface OcBOrderForBnCmd {


    /**
     * 根据订单id 查询班牛需要的信息
     *
     * @param orderId
     * @return
     */
    ValueHolderV14<BnQueryForLogisticsProblemResult> getBnInfoByOrderId(Long orderId);

    /**
     * 推送物流问题到班牛系统
     *
     * @param param
     * @return
     */
    ValueHolderV14 pushLogisticsProblem(String param, User user);

    /**
     * 批量推送物流问题到班牛系统
     *
     * @param param
     * @param user
     * @return
     */
    ValueHolderV14 batchPushLogisticsProblem(JSONObject obj, User user);


    /**
     * 上传附件
     *
     * @param file
     * @return
     */
    ValueHolderV14 uploadFile(MultipartFile file);
}
