package com.jackrain.nea.oc.oms.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @author: 江家雷
 * create at: 2020/07/04 0015
 */
@Data
public class OrderHoldRequest implements Serializable {

    @JSONField(name = "IDS")
    private List<Long> ids;

    @JSONField(name = "DAY_TYPE")
    private Integer dayType;

    @JSONField(name = "HOLD_ORDER_REASON")
    private Integer holdOrderReason;

    @J<PERSON>NField(name = "HOLD_DETENTION_ORDER_REASON")
    private Integer holdDetentionOrderReason;

    @JSONField(name = "RELEASE_TIME_TYPE")
    @Field(type = FieldType.Integer)
    private Integer releaseTimeType;

    @JSONField(name = "RELEASE_TIME")
    @Field(type = FieldType.Long)
    private Date releaseTime;

    @JSONField(name = "FIXED_DURATION")
    @Field(type = FieldType.Integer)
    private Integer fixedDuration;

    @JSONField(name = "IS_AUTO_RELEASE")
    @Field(type = FieldType.Keyword)
    private String isAutoRelease;

    @JSONField(name = "TIME_UNIT")
    @Field(type = FieldType.Integer)
    private Integer timeUnit;


}
