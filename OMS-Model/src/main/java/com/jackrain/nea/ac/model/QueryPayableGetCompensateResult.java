package com.jackrain.nea.ac.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @author: 陈俊明
 * @since: 2019-05-15
 * @create at : 2019-05-15 19:30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryPayableGetCompensateResult implements Serializable {
    private BigDecimal payablePrice;
    private List<AcFPayableAdjustmentItemDO> acFPayableAdjustmentItemList;
}
