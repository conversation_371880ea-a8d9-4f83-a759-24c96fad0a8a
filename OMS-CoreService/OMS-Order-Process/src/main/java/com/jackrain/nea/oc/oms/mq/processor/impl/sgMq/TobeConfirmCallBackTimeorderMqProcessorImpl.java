package com.jackrain.nea.oc.oms.mq.processor.impl.sgMq;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.mq.annotation.RocketMqMessageListener;
import com.burgeon.mq.core.BaseMessageListener;
import com.burgeon.mq.enums.MqTypeEnum;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyOmsForVipTimeResult;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;


/**
 * @program: r3-oc-oms
 * @description: 唯品会时效订单占单回执mq监听mq
 * @author: zhuxing
 * @create: 2021-08-30
 * 废弃 by haiyang 20231129
 **/

//fixme tag:sg_to_oms_timeorder_vip_call_back
@Deprecated
@Slf4j
@RocketMqMessageListener(name = "TobeConfirmCallBackTimeorderMqProcessorImpl", type = MqTypeEnum.DEFAULT)
public class TobeConfirmCallBackTimeorderMqProcessorImpl implements BaseMessageListener {

    @Autowired
    private TobeConfirmCallBackVipTimeOrderMqService tobeConfirmCallBackVipTimeOrderMqService;

    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    @Override
    public void consume(String messageBody, String messageTopic, String messageKey, String messageTag, Object object) {
        String logPrefix = "TobeConfirmCallBackTimeorderMqProcessorImpl startProcess方法开始：";
        log.debug(logPrefix + "messageTopic={} messageKey = {} messageBody = {} messageTag = {}",
                messageTopic, messageKey, messageBody, messageTag);
        JSONObject messageJson = JSONObject.parseObject(messageBody);
        SgFindSourceStrategyOmsForVipTimeResult sgFindSourceStrategyOmsForVipTimeResult = JSONObject.toJavaObject(messageJson,SgFindSourceStrategyOmsForVipTimeResult.class);
        log.info("TobeConfirmCallBackTimeorderMqProcessorImpl 返回对象为：{}",sgFindSourceStrategyOmsForVipTimeResult);
        /**处理时效订单反馈结果*/
        tobeConfirmCallBackVipTimeOrderMqService.tobeConfirmTimeOrderCallBackService(sgFindSourceStrategyOmsForVipTimeResult);
    }

}
