package com.jackrain.nea.oc.oms.util;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.oc.oms.mapper.IpBJingdongCoupondtaiMapper;
import com.jackrain.nea.oc.oms.model.jingdong.JingdongCouponType;
import com.jackrain.nea.oc.oms.model.relation.IpJingdongOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongCoupondtai;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongOrderItemExt;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.services.IpJingdongOrderService;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.rpc.PsRpcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description:
 * @author: 江家雷
 * @since: 2020/7/31
 * create at : 2020/7/31 15:19
 */
@Component
@Slf4j
public class JingdongAmountCalculationUtil {

    @Autowired
    private IpBJingdongCoupondtaiMapper ipBJingdongCoupondtaiMapper;

    @Autowired
    private PsRpcService psRpcService;

    @Autowired
    private IpJingdongOrderService ipJingdongOrderService;

    /***
     * 根据订单编号获取明细对应的金额
     * @Parm orderNo 平台单号
     * @return
     */
    public List<OcBOrderItem> amountCalculation(String orderNo) {
        IpJingdongOrderRelation orderRelation = this.ipJingdongOrderService.selectJingdongOrder(orderNo);
        if (orderRelation == null) {
            return null;
        }
        return amountCalculation(calcOrderSplitAmt(orderRelation));
    }

    /**
     * 根据 中间表明细计算金额
     *
     * @param items
     * @return
     */
    public List<OcBOrderItem> amountCalculation(List<IpBJingdongOrderItemExt> items) {
        if (CollectionUtils.isEmpty(items)) {
            return null;
        }
        List<OcBOrderItem> ocBOrderItems = new ArrayList<>();
        for (IpBJingdongOrderItemExt jdItem : items) {
            ProductSku productSku = psRpcService.selectProductSku(jdItem.getOuterSkuId());
            OcBOrderItem item = new OcBOrderItem();
            //京东num_iid
            item.setNumIid(jdItem.getWareId());
            item.setSkuNumiid(jdItem.getSkuId() + "");
            // 吊牌价
            item.setPriceTag(productSku.getPricelist());
            item.setPriceList(jdItem.getJdPrice());
            // 平台售价
            item.setPrice(calcPrice(jdItem));
            // 实际成交金额
            item.setRealAmt(calcRealAmt(jdItem));
            // 成交单价
            item.setPriceActual(calcPriceActual(jdItem));
            // 商品优惠金额
            item.setAmtDiscount(discountAmt(jdItem));
            // 平摊金额 京东无平摊金额
            item.setOrderSplitAmt(jdItem.getAveragediscount());
            // 调整金额 京东无调整金额
            item.setAdjustAmt(BigDecimal.ZERO);
            ocBOrderItems.add(item);
        }
        return ocBOrderItems;
    }

    /***
     * 优惠金额
     * @param jdItem
     * @return
     */
    private BigDecimal discountAmt(IpBJingdongOrderItemExt jdItem) {
        BigDecimal discountAmt = BigDecimal.ZERO;
        QueryWrapper<IpBJingdongCoupondtai> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(IpBJingdongCoupondtai::getSkuId, jdItem.getSkuId())
                .eq(IpBJingdongCoupondtai::getIpBJingdongOrderId, jdItem.getIpBJingdongOrderId());

        List<IpBJingdongCoupondtai> coupondtaiList = ipBJingdongCoupondtaiMapper.selectList(wrapper);
        if (CollectionUtils.isNotEmpty(coupondtaiList)) {
            for (IpBJingdongCoupondtai ipBJingdongCoupondtai : coupondtaiList) {
                if (JingdongCouponType.TWENTY_COUPON.equals(ipBJingdongCoupondtai.getCouponType())
                        || JingdongCouponType.TWENTY_EIGHT_COUPON.equals(ipBJingdongCoupondtai.getCouponType())
                        || JingdongCouponType.TWENTY_NINE_COUPON.equals(ipBJingdongCoupondtai.getCouponType())
                        || JingdongCouponType.THIRTY_COUPON.equals(ipBJingdongCoupondtai.getCouponType())
                        || JingdongCouponType.THIRTY_FOUR_COUPON.equals(ipBJingdongCoupondtai.getCouponType())
                        || JingdongCouponType.THIRTY_FIVE_COUPON.equals(ipBJingdongCoupondtai.getCouponType())
                        || JingdongCouponType.ONE_HUNDRED_COUPON.equals(ipBJingdongCoupondtai.getCouponType())
                        || JingdongCouponType.ONE_SEVEN_ONE_COUPON.equals(ipBJingdongCoupondtai.getCouponType())
                        || JingdongCouponType.THREE_SIX_COUPON.equals(ipBJingdongCoupondtai.getCouponType())
                        || JingdongCouponType.ONE_SIX_TWO_COUPON.equals(ipBJingdongCoupondtai.getCouponType())
                        || JingdongCouponType.ONE_SIX_EIGHT_COUPON.equals(ipBJingdongCoupondtai.getCouponType())) {

                    if (null != ipBJingdongCoupondtai.getCouponPrice()) {
                        discountAmt = discountAmt.add(ipBJingdongCoupondtai.getCouponPrice());
                    }
                }
            }
        }
        return discountAmt;
    }

    /**
     * 计算平台售价
     *
     * @param jdItem
     * @return
     */
    private BigDecimal calcPrice(IpBJingdongOrderItemExt jdItem) {
        if (null == jdItem.getJdPrice() || null == jdItem.getItemTotal()) {
            jdItem.setJdPrice(BigDecimal.ZERO);
            jdItem.setItemTotal(0L);
            return BigDecimal.ZERO;
        }
        if (null == jdItem.getAveragediscount()) {
            jdItem.setAveragediscount(BigDecimal.ZERO);
        }
        if (null == jdItem.getAveragediscount()) {
            jdItem.setAveragediscount(BigDecimal.ZERO);
        }

        return jdItem.getJdPrice();

    }

    /**
     * 标准价*数量-优惠金额-优惠平摊金额
     *
     * @param jdItem 京东明细
     * @return 实际成交金额
     */
    private BigDecimal calcRealAmt(IpBJingdongOrderItemExt jdItem) {
        if (null == jdItem.getJdPrice() || null == jdItem.getItemTotal()) {
            jdItem.setJdPrice(BigDecimal.ZERO);
            jdItem.setItemTotal(0L);
            return BigDecimal.ZERO;
        }
        if (null == jdItem.getAveragediscount()) {
            jdItem.setAveragediscount(BigDecimal.ZERO);
        }
        BigDecimal decimal = jdItem.getJdPrice().multiply(BigDecimal.valueOf(jdItem.getItemTotal()))
                .subtract(discountAmt(jdItem)).subtract(jdItem.getAveragediscount());
        //四位小数
        return decimal.setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 成交单价
     *
     * @return
     */
    private BigDecimal calcPriceActual(IpBJingdongOrderItemExt jdItem) {
        if (jdItem.getItemTotal() == null || jdItem.getItemTotal().compareTo(0L) == 0) {
            return BigDecimal.ZERO;
        }
        return calcRealAmt(jdItem).divide(new BigDecimal(jdItem.getItemTotal()), 2, BigDecimal.ROUND_HALF_UP);
    }

    private List<IpBJingdongOrderItemExt> calcOrderSplitAmt(IpJingdongOrderRelation orderRelation) {
        BigDecimal orderDiscountAmt = getOrderDiscountAmount(orderRelation.getJingdongCoupondtaiList(), orderRelation.getJingdongOrderItems()) == null ? BigDecimal.ZERO : getOrderDiscountAmount(orderRelation.getJingdongCoupondtaiList(), orderRelation.getJingdongOrderItems());
        List<IpBJingdongOrderItemExt> jingdongOrderItems = orderRelation.getJingdongOrderItems();
        if (CollectionUtils.isEmpty(jingdongOrderItems)) {
            return null;
        }
        List<IpBJingdongOrderItemExt> unSuccessRefundList = jingdongOrderItems.stream().filter(
                o -> o.getJdPrice() != null && o.getJdPrice().compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(unSuccessRefundList)) {
            if (unSuccessRefundList.size() == 1) {
                unSuccessRefundList.get(0).setAveragediscount(orderDiscountAmt);
            } else {
                int j = 0;
                for (IpBJingdongOrderItemExt ipBJingdongOrderItem : unSuccessRefundList) {
                    //判断订单明细中的成交价格是否大于0
                    if (ipBJingdongOrderItem.getJdPrice().compareTo(BigDecimal.ZERO) > 0) {
                        j++;
                    }
                }
                //只要有一条满足都执行平摊金额
                if (j > 0) {
                    //除末尾行其他明细整单平摊金额之和
                    BigDecimal totalItemAmt = BigDecimal.ZERO;
                    //最后一行明细平摊金额
                    BigDecimal lastOrderSplitAmt = BigDecimal.ZERO;
                    //非退款明细求和
                    BigDecimal sumPrice = sumPrice(unSuccessRefundList);
                    //避免明细求和为0
                    if (sumPrice.compareTo(BigDecimal.ZERO) != 0) {
                        for (int i = 0; i < unSuccessRefundList.size(); i++) {
                            IpBJingdongOrderItemExt jingdongOrderItem = unSuccessRefundList.get(i);
                            if (i < unSuccessRefundList.size() - 1) {
                                //“整单平摊金额”= 【（当前明细“成交价格”*“数量”/sum（明细“成交价格”*“数量”）】*主表的订单优惠金额
                                BigDecimal orderSplitAmt = orderDiscountAmt.multiply(calcRealAmt(jingdongOrderItem)).divide(sumPrice, 0, BigDecimal.ROUND_HALF_UP).setScale(2);

                                //除去最后一行的整体平摊金额之和
                                totalItemAmt = totalItemAmt.add(orderSplitAmt);
                                //更新明细平摊金额
                                jingdongOrderItem.setAveragediscount(orderSplitAmt);
                            } else if (i == unSuccessRefundList.size() - 1) {
                                //减去平摊金额
                                lastOrderSplitAmt = orderDiscountAmt.subtract(totalItemAmt);
                                jingdongOrderItem.setAveragediscount(lastOrderSplitAmt);
                            }
                        }
                    }
                }
            }
        }
        return unSuccessRefundList;
    }

    /**
     * 非退款明细求和
     *
     * @param jingdongOrderItems
     * @return BigDecimal
     */
    private BigDecimal sumPrice(List<IpBJingdongOrderItemExt> jingdongOrderItems) {

        BigDecimal priceTotal = BigDecimal.ZERO;
        for (IpBJingdongOrderItemExt itemExt : jingdongOrderItems) {
            priceTotal = priceTotal.add(calcRealAmt(itemExt)).setScale(2, BigDecimal.ROUND_HALF_UP);
        }
        return priceTotal;
    }

    /**
     * @return java.math.BigDecimal
     * <AUTHOR>
     * @Description 订单优惠金额 优惠信息列表中优惠劵为店铺承担且SKU编码为空的优惠金额
     * 20-套装优惠
     * 28-闪团优惠
     * 29-团购优惠
     * 30-单品促销优惠
     * 34-手机红包
     * 35-满返满送(返现)
     * 100-店铺优惠中店铺承担
     * @Date 2019-11-27
     * @Param [coupondtaiList]
     **/
    private BigDecimal getOrderDiscountAmount(List<IpBJingdongCoupondtai> coupondtaiList, List<IpBJingdongOrderItemExt> ipBJingdongOrderItemExtList) {
        BigDecimal orderDiscountAmt = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(coupondtaiList)) {
            for (IpBJingdongCoupondtai ipBJingdongCoupondtai : coupondtaiList) {
                if (JingdongCouponType.TWENTY_COUPON.equals(ipBJingdongCoupondtai.getCouponType())
                        || JingdongCouponType.TWENTY_EIGHT_COUPON.equals(ipBJingdongCoupondtai.getCouponType())
                        || JingdongCouponType.TWENTY_NINE_COUPON.equals(ipBJingdongCoupondtai.getCouponType())
                        || JingdongCouponType.THIRTY_COUPON.equals(ipBJingdongCoupondtai.getCouponType())
                        || JingdongCouponType.THIRTY_FOUR_COUPON.equals(ipBJingdongCoupondtai.getCouponType())
                        || JingdongCouponType.THIRTY_FIVE_COUPON.equals(ipBJingdongCoupondtai.getCouponType())
                        || JingdongCouponType.FORTY_ONE_COUPON.equals(ipBJingdongCoupondtai.getCouponType())
                        || JingdongCouponType.FIFTY_TWO_COUPON.equals(ipBJingdongCoupondtai.getCouponType())
                        || JingdongCouponType.ONE_HUNDRED_COUPON.equals(ipBJingdongCoupondtai.getCouponType())
                        || JingdongCouponType.ONE_SEVEN_ONE_COUPON.equals(ipBJingdongCoupondtai.getCouponType())
                        || JingdongCouponType.THREE_SIX_COUPON.equals(ipBJingdongCoupondtai.getCouponType())
                        || JingdongCouponType.ONE_SIX_TWO_COUPON.equals(ipBJingdongCoupondtai.getCouponType())
                        || JingdongCouponType.ONE_SIX_EIGHT_COUPON.equals(ipBJingdongCoupondtai.getCouponType())) {
                    if (null == ipBJingdongCoupondtai.getSkuId()) {
                        if (null != ipBJingdongCoupondtai.getCouponPrice()) {
                            orderDiscountAmt = orderDiscountAmt.add(ipBJingdongCoupondtai.getCouponPrice());
                        }
                    }
                }
            }
        }

        //100-店铺优惠 扣减平台优惠
        BigDecimal platformDiscountAmt = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(ipBJingdongOrderItemExtList)) {
            for (IpBJingdongOrderItemExt ipBJingdongOrderItemExt : ipBJingdongOrderItemExtList) {
                if (null != ipBJingdongOrderItemExt.getReserveDecimal01()) {
                    platformDiscountAmt = platformDiscountAmt.add(ipBJingdongOrderItemExt.getReserveDecimal01());
                }
            }
        }
        orderDiscountAmt = orderDiscountAmt.subtract(platformDiscountAmt);
        return orderDiscountAmt;
    }
}