package com.jackrain.nea.oc.oms.services.naika;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpCPlatform;
import com.jackrain.nea.hub.api.naika.NaiKaOrderCmd;
import com.jackrain.nea.hub.request.naika.NaiKaThawRequest;
import com.jackrain.nea.hub.request.naika.NaiKaUnfreezeItemRequest;
import com.jackrain.nea.hub.request.naika.NaiKaUnfreezeRequest;
import com.jackrain.nea.oc.oms.constant.NaiKaTypeConstant;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderNaiKaMapper;
import com.jackrain.nea.oc.oms.model.enums.UnFreezeEnum;
import com.jackrain.nea.oc.oms.model.enums.naika.OcBOrderNaiKaStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.naika.OmsOrderNaiKaStatusEnum;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderNaiKa;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName OmsNaiKaOrderUnFreezeService
 * @Description 奶卡解冻
 * <AUTHOR>
 * @Date 2022/7/27 20:50
 * @Version 1.0
 */
@Component
@Slf4j
public class OmsNaiKaOrderUnFreezeService {

    @Reference(group = "hub", version = "1.0")
    private NaiKaOrderCmd naiKaOrderCmd;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private OcBOrderNaiKaMapper ocBOrderNaiKaMapper;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;

    public ValueHolder naiKaOrderUnFreeze(Long ocBOrderId, String remark, List<OcBOrderNaiKa> ocBOrderNaiKaList) {
        ValueHolder vh = new ValueHolder();
        OcBOrder ocBOrder = ocBOrderMapper.get4NaiKaOrder(ocBOrderId);

        // 如果解冻状态为解冻成功 则不用解冻
        if (ObjectUtil.equals(ocBOrder.getToNaikaStatus(), OmsOrderNaiKaStatusEnum.FREEZE_SUCCESS.getStatus())
                || ObjectUtil.equals(ocBOrder.getToNaikaStatus(), OmsOrderNaiKaStatusEnum.UN_NECESSARY.getStatus())) {
            vh.put("code", ResultCode.FAIL);
            vh.put("message", "解冻状态不合法");
            return vh;
        }

        // 需要过滤掉虚拟的订单 只要实体订单
        List<String> cardList = ocBOrderNaiKaList.stream().filter(ocBOrderNaiKa -> NaiKaTypeConstant.ENTITY.equals(ocBOrderNaiKa.getBusinessType()))
                .map(OcBOrderNaiKa::getCardCode).collect(Collectors.toList());

        if (CollectionUtil.isEmpty(cardList)) {
            vh.put("code", ResultCode.FAIL);
            vh.put("message", "未找到相应的实体奶卡");
            return vh;
        }

        NaiKaUnfreezeRequest unfreezeRequest = new NaiKaUnfreezeRequest();
        unfreezeRequest.setBillNo(ocBOrder.getBillNo());
        unfreezeRequest.setTid(ocBOrder.getSourceCode());
        CpCPlatform cpCPlatform = cpRpcService.selectCpcPlatformById(Long.valueOf(ocBOrder.getPlatform()));
        unfreezeRequest.setPlatformCode(cpCPlatform.getEcode());
        unfreezeRequest.setShopCode(ocBOrder.getCpCShopEcode());
        if (ocBOrder.getIsResetShip() == null || ObjectUtil.equals(0, ocBOrder.getIsResetShip())) {
            unfreezeRequest.setType(1);
        } else {
            unfreezeRequest.setType(2);
        }
        List<NaiKaUnfreezeItemRequest> unfreezeItemRequests = new ArrayList<>();
        List<String> unfreezeCodeList = new ArrayList<>();
        // 构造详情数据
        List<OcBOrderItem> ocBOrderItemList = ocBOrderItemMapper.selectAllOrderItem(ocBOrder.getId());
        for (OcBOrderItem ocBOrderItem : ocBOrderItemList) {
            List<OcBOrderNaiKa> ocBOrderNaiKas = ocBOrderNaiKaMapper.selectNaiKaByOcBOrderIdAndItemId(ocBOrderItem.getOcBOrderId(), ocBOrderItem.getId());
            if (CollectionUtils.isNotEmpty(ocBOrderNaiKas)) {
                List<String> cardCodeList = new ArrayList<>();
                for (OcBOrderNaiKa ocBOrderNaiKa : ocBOrderNaiKas) {
                    if (ObjectUtil.equals(ocBOrderNaiKa.getNaikaStatus(), OmsOrderNaiKaStatusEnum.FREEZE.getStatus())
                            || ObjectUtil.equals(ocBOrderNaiKa.getNaikaStatus(), OmsOrderNaiKaStatusEnum.FREEZE_FAILED.getStatus())) {
                        cardCodeList.add(ocBOrderNaiKa.getCardCode());
                    }
                }
                NaiKaUnfreezeItemRequest unfreezeItemRequest = new NaiKaUnfreezeItemRequest();
                unfreezeItemRequest.setOid(ocBOrderItem.getOoid());
                unfreezeItemRequest.setSubOrderNO(ocBOrderItem.getOoid());
                unfreezeItemRequest.setCardList(cardCodeList);
                unfreezeCodeList.addAll(cardCodeList);
                unfreezeItemRequests.add(unfreezeItemRequest);
            }
        }
        unfreezeRequest.setUnfreezeItemRequests(unfreezeItemRequests);
        OcBOrder updateOcBOrder = new OcBOrder();
        ValueHolderV14 valueHolderV14;

        try {
            valueHolderV14 = naiKaOrderCmd.orderUnfreeze(unfreezeRequest);
            // 解冻完成后 更新订单tonaikastatus状态
            updateOcBOrder.setId(ocBOrderId);
            updateOcBOrder.setModifieddate(new Date());
            Integer naikaStatus = OcBOrderNaiKaStatusEnum.FREEZE_SUCCESS.getStatus();
            String errorMsg = "";
            if (valueHolderV14.isOK()) {
                vh.put("code", ResultCode.SUCCESS);
                vh.put("message", "success");
            } else {
                naikaStatus = OcBOrderNaiKaStatusEnum.FREEZE_FAILED.getStatus();
                errorMsg = valueHolderV14.getMessage();
                vh.put("code", ResultCode.FAIL);
                vh.put("message", valueHolderV14.getMessage());
            }
            updateOcBOrder.setToNaikaStatus(naikaStatus);
            ocBOrderMapper.updateById(updateOcBOrder);

            // 更新奶卡表数据
            // 根据零售发货单 查到所有需要更新解冻的奶卡数据
            List<OcBOrderNaiKa> ocBOrderNaiKas = ocBOrderNaiKaMapper.selectNaiKaByOcBOrderId(ocBOrderId);
            Map<String, OcBOrderNaiKa> naikaMap = ocBOrderNaiKas.stream().collect(Collectors.toMap(OcBOrderNaiKa::getCardCode, Function.identity()));
            for (String cardCode : cardList) {
                OcBOrderNaiKa ocBOrderNaiKa = naikaMap.get(cardCode);
                ocBOrderNaiKa.setNaikaStatus(naikaStatus);
                ocBOrderNaiKa.setOcBOrderId(ocBOrderId);
                ocBOrderNaiKa.setModifieddate(new Date());
                if (ObjectUtil.isNotEmpty(errorMsg) && errorMsg.length() > 255) {
                    errorMsg = errorMsg.substring(0, 254);
                }
                ocBOrderNaiKa.setUnfreezeErrorMsg(errorMsg);
                ocBOrderNaiKaMapper.updateById(ocBOrderNaiKa);
            }
        } catch (Exception e) {
            log.error("err to unfreeze orderId:{}, errorMsg:{}", ocBOrderId, e.getMessage());
            updateOcBOrder.setToNaikaStatus(OcBOrderNaiKaStatusEnum.FREEZE_FAILED.getStatus());
            vh.put("code", ResultCode.FAIL);
            vh.put("message", e.getMessage());
            ocBOrderMapper.updateById(updateOcBOrder);
        }
        return vh;
    }
}
