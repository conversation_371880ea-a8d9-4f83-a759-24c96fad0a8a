package com.jackrain.nea.oc.oms.model.enums;

/**
 * 预售尾款状态
 *
 * @author: ming.fz
 * @since: 2019-03-21
 * create at : 2019-02-23 18:29
 */
public enum OcOrderDoublellPresaleStatus {

    /**
     * 0非预售
     */
    FEI_PRESALL,

    /**
     * 10 预售已付尾款
     */
    YES_PRESALL,

    /**
     * 20预售未付尾款
     */
    NOT_PRESALL;


    public int toInteger() {
        if (this == OcOrderDoublellPresaleStatus.FEI_PRESALL) {
            return 0;
        } else if (this == OcOrderDoublellPresaleStatus.YES_PRESALL) {
            return 10;
        } else {
            return 20;
        }

    }

}
