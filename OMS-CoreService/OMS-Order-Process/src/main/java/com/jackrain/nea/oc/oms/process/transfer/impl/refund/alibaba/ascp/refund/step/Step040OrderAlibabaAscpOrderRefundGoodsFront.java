package com.jackrain.nea.oc.oms.process.transfer.impl.refund.alibaba.ascp.refund.step;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpBAlibabaAscpOrderRefundRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderRelation;
import com.jackrain.nea.oc.oms.model.resources.SysNotesConstant;
import com.jackrain.nea.oc.oms.model.table.IpBAlibabaAscpOrderRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.services.OcBOrderHoldService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2020/2/27 8:21 下午
 * @Version 1.0
 */
@Step(order = 40, description = "发货前处理")
@Slf4j
@Component
public class Step040OrderAlibabaAscpOrderRefundGoodsFront extends BaseAlibabaAscpOrderRefundProcessStep
        implements IOmsOrderProcessStep<IpBAlibabaAscpOrderRefundRelation> {

    @Autowired
    private OcBOrderHoldService ocBOrderHoldService;


    @Override
    public ProcessStepResult<IpBAlibabaAscpOrderRefundRelation> startProcess(IpBAlibabaAscpOrderRefundRelation orderInfo,
                                                                             ProcessStepResult preStepResult,
                                                                             boolean isAutoMakeup, User operateUser) {
        IpBAlibabaAscpOrderRefund orderRefund = orderInfo.getOrderRefund();
        try {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("start alibabaAscpOrderRefund goods front action. into order No.{}","alibabaAscpOrderRefund",orderInfo.getOrderNo()), orderInfo.getOrderNo());
            }
            List<OmsOrderRelation> omsOrderRelation = orderInfo.getOmsOrderRelation();
            OcBOrder ocBOrder = omsOrderRelation.get(0).getOcBOrder();
            int success = 0;
            int statusNo = 0;
            for (int i = 0; i < omsOrderRelation.size(); i++) {
                OmsOrderRelation orderRelation = omsOrderRelation.get(i);
                OcBOrder order = orderRelation.getOcBOrder();
                Integer orderStatus = order.getOrderStatus();
                // 订单表示
                Integer orderMark = orderRelation.getOrderMark();
                //判断订单是否是退款成功作废的订单
                boolean voidOrder = (OmsOrderStatus.CANCELLED.toInteger().equals(orderStatus)
                        || OmsOrderStatus.SYS_VOID.toInteger().equals(orderStatus));
                if (voidOrder) {
                    success++;
                    continue;
                }
                if (TaobaoReturnOrderExt.ReturnBillsStatus.GOODS_FRONT.getCode().equals(orderMark)) {
//                    Boolean shipmentReturn = this.beforeShipmentReturn(orderRelation, orderRefund, operateUser);
//                    if (shipmentReturn == null) {
//                        statusNo++;
//                        continue;
//                    }
                    success++;
                }
            }
            if (success == omsOrderRelation.size()) {
//                ipTmallZfRefundService.foundRefundFrontRefundOnly(ocBOrder, orderRefund, operateUser);
                String remark = SysNotesConstant.SYS_REMARK17;
                orderRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERFAIL.toInteger(),
                        remark, orderRefund);
                log.info(LogUtil.format("run refund front action success. buyer request all oms order item refund, order NO.{}","alibabaAscpOrderRefund",orderInfo.getOrderNo()), "发货前退款,订单处理完成");
                return new ProcessStepResult<>(StepStatus.FINISHED, "发货前退款,订单处理完成");
            }
            if (statusNo == omsOrderRelation.size()) {
                //todo 生成发货前退款单
                String remark = SysNotesConstant.SYS_REMARK27;
                orderRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                        remark, orderRefund);
                return new ProcessStepResult<>(StepStatus.FINISHED, "发货前退款,状态不满足,退出转换");
            }
            return new ProcessStepResult<>(StepStatus.SUCCESS, "订单处理成功,进入下一阶段");

        } catch (Exception e) {
            log.error(LogUtil.format("发货前退款异常,退款单号:{}", "发货前退款异常,退款单号", orderRefund.getBizOrderCode()), Throwables.getStackTraceAsString(e));
            orderRefundService.updateRefundIsTransError(orderRefund, e.getMessage());
            return new ProcessStepResult<>(StepStatus.FAILED, "发货前退款处理失败!" + e.getMessage());
        }
    }

    /**
     * 处理发货前的逻辑
     *
     * @param orderRelation
     */
    private Boolean beforeShipmentReturn(OmsOrderRelation orderRelation, IpBAlibabaAscpOrderRefund orderRefund, User user) {
        //将订单拦截
        OcBOrder ocBOrder = orderRelation.getOcBOrder();
      /*  OcBOrder order = new OcBOrder();
        //是否退款中
        order.setIsInreturning(1);
        // 发货前之前取消订单
        // order.setOrderStatus(OmsOrderStatus.CANCELLED.toInteger());
        order.setId(ocBOrder.getId());
        omsOrderService.updateOrderInfo(order);
        // 修改hold单状态 使用HOLD单方法修改
        order.setIsInterecept(1);//是否已经拦截 修改hold单状态 使用HOLD单方法修改
        ocBOrderHoldService.holdOrUnHoldOrder(order, OrderHoldReasonEnum.REFUND_HOLD);*/
        omsRefundOrderService.holdOrderHandle(ocBOrder, true);
        List<OcBOrderItem> ocBOrderItems = orderRelation.getOcBOrderItems();
        List<OmsOrderRelation.OcOrderGifts> ocOrderGifts = orderRelation.getOcOrderGifts();
        if (CollectionUtils.isNotEmpty(ocOrderGifts)) {
            for (OmsOrderRelation.OcOrderGifts ocOrderGift : ocOrderGifts) {
                if (ocOrderGift.getGiftMark() == 2) {
                    ocBOrderItems.addAll(ocOrderGift.getOcBOrderGifts());
                }
            }
        }
        //淘宝退单中间表的退单状态
        Integer returnStatus = orderRefund.getReturnStatus();
        String status = TaobaoReturnOrderExt.RefundStandPlatStatus.getValueByKey(returnStatus);
        if (TaobaoReturnOrderExt.RefundStatus.WAIT_SELLER_AGREE.getCode().equals(status)) {
            //买家已经申请退款，等待卖家同意
            return orderRefundService.taobaoRefundStatusAgree(orderRelation,
                    orderRefund, user);
        } else if (TaobaoReturnOrderExt.RefundStatus.SUCCESS.getCode().equals(status)
                || TaobaoReturnOrderExt.RefundStatus.WAIT_BUYER_RETURN_GOODS.getCode().equals(status)) {
            //退款成功
            orderRefundService.refundStatusIsSuccess(orderRelation,
                    orderRefund, user);
            return false;
        } else if (TaobaoReturnOrderExt.RefundStatus.CLOSED.getCode().equals(status)) {
            //退款关闭
            orderRefundService.orderStatusIsClosed(orderRelation,
                    orderRefund, user);
            return false;
        }
        return null;
    }
}
