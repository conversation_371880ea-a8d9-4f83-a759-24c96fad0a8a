package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.api.ModifyReturnOrderLogisticsCmd;
import com.jackrain.nea.oc.oms.model.request.UpdateReturnOrderRequest;
import com.jackrain.nea.oc.oms.services.ModifyReturnOrderLogisticsService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 批量修改物流公司
 *
 * @author: xiWen.z
 * create at: 2019/8/16 0016
 */
@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class ModifyReturnOrderLogisticsCmdImpl implements ModifyReturnOrderLogisticsCmd {

    @Autowired
    ModifyReturnOrderLogisticsService modifyReturnOrderLogisticsService;

    @Override
    public ValueHolderV14 modifyReturnOrderLogistics(UpdateReturnOrderRequest req, User usr) {
        return modifyReturnOrderLogisticsService.updateLogistics(req, usr);
    }

    @Override
    public ValueHolderV14 modifyReturnExpress(UpdateReturnOrderRequest model, User loginUser) {
        return modifyReturnOrderLogisticsService.modifyReturnExpress(model, loginUser);
    }

    @Override
    public ValueHolderV14 returnToWms(JSONObject param, User loginUser) {
        return modifyReturnOrderLogisticsService.returnToWms(param, loginUser);
    }
}
