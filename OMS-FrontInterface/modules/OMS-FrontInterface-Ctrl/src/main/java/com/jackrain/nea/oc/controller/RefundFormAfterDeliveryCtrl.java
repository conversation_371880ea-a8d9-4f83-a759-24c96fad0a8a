package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.RefundFormAfterDeliveryCmd;
import com.jackrain.nea.oc.oms.model.enums.OcBReturnAfSendListEnums;
import com.jackrain.nea.oc.oms.model.relation.OcBReturnAfSendExtend;
import com.jackrain.nea.oc.oms.model.relation.OcBReturnAfSendItemExtend;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.InputStream;
import java.text.NumberFormat;
import java.util.*;

/**
 * @author: 夏继超
 * @since: 2020/3/13
 * create at : 2020/3/13 10:46
 */
@Api(value = "RefundFormAfterDeliveryCtrl", tags = "发货后退款单相关服务接口")
@Slf4j
@RestController
public class RefundFormAfterDeliveryCtrl {

    @Autowired
    RefundFormAfterDeliveryCmd deliveryCmd;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    public static final String cellStr = "cell_";
    public static final String rowStr = "row_";
    //CellType
    public static final int _NONE = -1;
    public static final int NUMERIC = 0;
    public static final int STRING = 1;
    public static final int FORMULA = 2;
    public static final int BLANK = 3;
    public static final int BOOLEAN = 4;
    public static final int ERROR = 5;

    //导入excel,业务校验错误提示返回 错误excel地址
    public static final int IMPORT_ERROR_CODE = 10001;
    //导入excelche直接返回数据
    public static final int IMPORT_RESULT_DATA = 10002;

    private static NumberFormat nf = NumberFormat.getInstance();

    /**
     * 发货后退款单明细新增
     *
     * @param request
     * @param param
     * @return
     */
    @ApiOperation(value = "发货后退款单明细新增")
    @RequestMapping(value = "/api/cs/oc/oms/v1/saveAfterDeliverItem", method = RequestMethod.POST)
    public JSONObject saveAfterDeliverItem(HttpServletRequest request, @RequestBody JSONObject param) {

        ValueHolderV14 result = new ValueHolderV14();
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
//        User user = SystemUserResource.getRootUser();
        try {
            result = deliveryCmd.saveAfterDeliverItem(param, user);
        } catch (Exception e) {
            result.setCode(ResultCode.FAIL);
            result.setMessage(e.getMessage());
        }
        return result.toJSONObject();
    }

    /**
     * 发货后退款单明细删除
     *
     * @param request
     * @param param
     * @return
     */
    @ApiOperation(value = "发货后退款单明细删除")
    @RequestMapping(value = "/api/cs/oc/oms/v1/deleteAfterDeliverItem", method = RequestMethod.POST)
    public JSONObject deleteAfterDeliverItem(HttpServletRequest request, @RequestBody JSONObject param) {

        ValueHolderV14 result = new ValueHolderV14();
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
//        User user = SystemUserResource.getRootUser();
        try {
            result = deliveryCmd.deleteAfterDeliverItem(param, user);
        } catch (Exception e) {
            result.setCode(ResultCode.FAIL);
            result.setMessage(e.getMessage());
        }
        return result.toJSONObject();
    }

    @ApiOperation(value = "发货后退款单编辑保存")
    @RequestMapping(value = "/api/cs/oc/oms/v1/saveAfterDeliver", method = RequestMethod.POST)
    public JSONObject saveAfterDeliver(HttpServletRequest request, @RequestBody JSONObject param) {

        ValueHolderV14 result = new ValueHolderV14();
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
//        User user = SystemUserResource.getRootUser();
        try {
            result = deliveryCmd.saveAfterDeliver(param, user);
        } catch (Exception e) {
            result.setCode(ResultCode.FAIL);
            result.setMessage(e.getMessage());
        }
        return result.toJSONObject();
    }

    @ApiOperation(value = "发货后退款单复制")
    @RequestMapping(value = "/api/cs/oc/oms/v1/copyAfterDeliver", method = RequestMethod.POST)
    public JSONObject copyAfterDeliver(HttpServletRequest request, @RequestBody JSONObject param) {

        ValueHolderV14 result = new ValueHolderV14();
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
//        User user = SystemUserResource.getRootUser();
        try {
            result = deliveryCmd.copyAfterDeliver(param, user);
        } catch (Exception e) {
            result.setCode(ResultCode.FAIL);
            result.setMessage(e.getMessage());
        }
        return result.toJSONObject();
    }

    @ApiOperation(value = "校验是否展示退款按钮")
    @RequestMapping(value = "/api/cs/oc/oms/v1/checkDisplayBtn", method = RequestMethod.POST)
    public JSONObject checkDisplayBtn(HttpServletRequest request, @RequestBody JSONObject param) {

        ValueHolderV14 result = new ValueHolderV14();
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        try {
            result = deliveryCmd.checkDisplayBtn(param, user);
        } catch (Exception e) {
            result.setCode(ResultCode.FAIL);
            result.setMessage(e.getMessage());
        }
        return result.toJSONObject();
    }

    @ApiOperation(value = "发货后退款单导入模板下载")
    @RequestMapping(value = "/api/cs/oc/oms/v1/afterDeliverImportDownload", method = RequestMethod.POST)
    public ValueHolderV14 afterDeliverImportDownload(HttpServletRequest request) {
//        User usr = SystemUserResource.getRootUser();
        User usr = (User) request.getSession().getAttribute("user");
        ValueHolderV14 vh = deliveryCmd.afterDeliverImportDownload(usr);
        return vh;
    }

    @ApiOperation(value = "发货后退款单导入")
    @RequestMapping(value = "/api/cs/oc/oms/v1/afterDeliverImport", method = RequestMethod.POST)
    public ValueHolderV14 afterDeliverImport(HttpServletRequest request, @RequestParam(value = "file", required = true) MultipartFile file) {
//        User user = SystemUserResource.getRootUser();
        User user = (User) request.getSession().getAttribute("user");
        if (file == null) {
            throw new NDSException(Resources.getMessage("请求参数不能为空!", user.getLocale()));
        }
        InputStream inputStream = null;
        ValueHolderV14 holderV14 = new ValueHolderV14();
        Map<String, InputStream> inputStreamMap = new HashMap<>();
        try {
            inputStream = file.getInputStream();
            inputStreamMap.put("inputStream", inputStream);
        } catch (Exception e) {
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("文件转换成流失败!");
            return holderV14;
        }
        Workbook hssfWorkbook = null;
        try {
            hssfWorkbook = new XSSFWorkbook(inputStream);
        } catch (Exception ex) {
            try {
                hssfWorkbook = new HSSFWorkbook(inputStream);
            } catch (Exception e) {
                holderV14.setCode(ResultCode.FAIL);
                return holderV14;
            }
        }

        //获取sheet页数
        int sheetNum = hssfWorkbook.getNumberOfSheets();
        if (sheetNum != 2) {
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("发货后退款单导入模板不正确!");
            return holderV14;
        }
        //处理主表数据--->转换成对象
        List<OcBReturnAfSendExtend> afSendList = getOcBReturnAfSendList(hssfWorkbook);

        if (CollectionUtils.isEmpty(afSendList)) {
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("发货后退款单导入模板主表数据不能为空!");
            return holderV14;
        }

        //处理明细表数据--->转换成对象
        List<OcBReturnAfSendItemExtend> afSendItemExtendList = getOcBReturnAfSendItemList(hssfWorkbook);
        if (CollectionUtils.isEmpty(afSendItemExtendList)) {
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("已发货后导入模板明细表数据不能为空!");
            return holderV14;
        }
        holderV14 = deliveryCmd.importList(afSendList, afSendItemExtendList, user);
        return holderV14;

    }

    /**
     * 处理明细
     *
     * @param hssfWorkbook
     * @return
     */
    private List<OcBReturnAfSendItemExtend> getOcBReturnAfSendItemList(Workbook hssfWorkbook) {
        List<OcBReturnAfSendItemExtend> ocBOrderItemExtendList = Lists.newArrayList();
        List<Map<String, String>> execlList = Lists.newArrayList();
        try {
            execlList = readExcel(1, hssfWorkbook);
        } catch (Exception e) {

        }
        int index = 0;
        for (Map<String, String> columnMap : execlList) {
            OcBReturnAfSendItemExtend returnAfSendItemExtend = new OcBReturnAfSendItemExtend();
            if (index == 0) {
                // 校验excel字段
                if (columnMap.size() != 4
                        || !"SKU编码".equals(columnMap.get(rowStr + index + cellStr + 0))
                        || !"退款金额".equals(columnMap.get(rowStr + index + cellStr + 1))
                        || !"运费".equals(columnMap.get(rowStr + index + cellStr + 2))
                        || !"头子表关联列".equals(columnMap.get(rowStr + index + cellStr + 3))
                ) {
                    return Lists.newArrayList();
                }
            } else {
                if (StringUtils.isNotEmpty(columnMap.get(rowStr + index + cellStr + 0))) {
                    // 组装待配货记录
                    ocBOrderItemExtendList.add(OcBReturnAfSendItemExtend.importCreate(index, returnAfSendItemExtend, columnMap));
                }
            }
            index++;
        }

        return ocBOrderItemExtendList;
    }

    /**
     * 处理主表
     *
     * @param hssfWorkbook
     * @return
     */
    private List<OcBReturnAfSendExtend> getOcBReturnAfSendList(Workbook hssfWorkbook) {
        List<OcBReturnAfSendExtend> returnAfSendList = Lists.newArrayList();
        List<Map<String, String>> execlList = Lists.newArrayList();

        try {
            execlList = readExcel(0, hssfWorkbook);
        } catch (Exception e) {

        }
        int index = 0;

        for (Map<String, String> columnMap : execlList) {
            OcBReturnAfSendExtend afSendExtend = new OcBReturnAfSendExtend();
            if (index == 0) {
                // 校验excel字段
                if (columnMap.size() != 14
                        || !"原始订单编号".equals(columnMap.get(rowStr + index + cellStr + 0))
                        || !"店铺名称".equals(columnMap.get(rowStr + index + cellStr + 1))
                        || !"原始平台单号".equals(columnMap.get(rowStr + index + cellStr + 2))
                        || !"平台退款单号".equals(columnMap.get(rowStr + index + cellStr + 3))
                        || !"退款类型".equals(columnMap.get(rowStr + index + cellStr + 4))
                        || !"买家昵称".equals(columnMap.get(rowStr + index + cellStr + 5))
                        || !"退款原因".equals(columnMap.get(rowStr + index + cellStr + 6))
                        || !"支付方式".equals(columnMap.get(rowStr + index + cellStr + 7))
                        || !"支付账号".equals(columnMap.get(rowStr + index + cellStr + 8))
                        || !"退款金额".equals(columnMap.get(rowStr + index + cellStr + 9))
                        || !"判责方".equals(columnMap.get(rowStr + index + cellStr + 10))
                        || !"判责方备注".equals(columnMap.get(rowStr + index + cellStr + 11))
                        || !"备注".equals(columnMap.get(rowStr + index + cellStr + 12))
                        || !"头子表关联列".equals(columnMap.get(rowStr + index + cellStr + 13))
                ) {
                    return Lists.newArrayList();
                }
            } else {
                if (StringUtils.isNotEmpty(columnMap.get(rowStr + index + cellStr + 0))) {
                    // 组装待配货记录
                    returnAfSendList.add(OcBReturnAfSendExtend.importCreate(index, afSendExtend, columnMap));
                }
            }
            index++;
        }

        OcBReturnAfSendListEnums.changeImportList(returnAfSendList);
        return returnAfSendList;
    }

    /**
     * 导入方法，
     *
     * @param hssfWorkbook
     * @return
     * @throws Exception
     */
    public List<Map<String, String>> readExcel(Integer sheetIndex, Workbook hssfWorkbook) throws Exception {
        List list = Lists.newArrayList();
        Sheet hssfSheet = hssfWorkbook.getSheetAt(sheetIndex);
        if (Objects.isNull(hssfSheet)) {
            return Lists.newArrayList();
        }

        for (int rowNum = 0; rowNum <= hssfSheet.getLastRowNum(); rowNum++) {
            Map<String, String> map = Maps.newTreeMap();
            Row hssfRow = hssfSheet.getRow(rowNum);
            if (hssfRow != null) {
                Iterator<Cell> cellItr = hssfRow.cellIterator();
                while (cellItr.hasNext()) {
                    Cell cell = cellItr.next();
                    map.put(rowStr + cell.getRowIndex() + cellStr + cell.getColumnIndex(), getCellValue(cell));
                }
            }
            list.add(map);
        }
        return list;
    }

    private static String getCellValue(Cell cell) {
        int cellType = cell.getCellType();
        String cellValue;
        switch (cellType) {
            case NUMERIC:
                cellValue = nf.format(cell.getNumericCellValue());
                if (cellValue.indexOf(",") >= 0) {
                    cellValue = cellValue.replace(",", "");
                }
                break;
            case FORMULA:
                try {
                    cellValue = cell.getStringCellValue();
                } catch (IllegalStateException e) {
                    cellValue = String.valueOf(cell.getNumericCellValue());
                }
                break;

            default:
                cellValue = cell.getStringCellValue();
        }

        return cellValue.trim();
    }

    private String getCellValue(HSSFCell cell) {
        String value = null;
        if (cell != null) {
            switch (cell.getCellType()) {
                case FORMULA:
                    try {
                        value = String.valueOf(cell.getNumericCellValue());
                    } catch (IllegalStateException e) {
                        value = String.valueOf(cell.getRichStringCellValue());
                    }
                    break;
                case NUMERIC:
                    value = String.valueOf(cell.getNumericCellValue());
                    break;
                case STRING:
                    value = String.valueOf(cell.getRichStringCellValue());
                    break;
            }
        }
        return value;
    }
}
