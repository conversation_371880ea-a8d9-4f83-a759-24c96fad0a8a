package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.api.OcBorderInvoiceCmd;
import com.jackrain.nea.oc.oms.model.request.SaveInvoiceRequest;
import com.jackrain.nea.oc.oms.model.request.SaveRecordInvoiceRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * @author: xiWen.z
 * create at: 2019/7/23 0023
 */
@RestController
@Slf4j
@Api(value = "OcBorderInvoiceCtrl", tags = "订单开票")
public class OcBorderInvoiceCtrl {

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @Autowired
    private OcBorderInvoiceCmd ocBorderInvoiceCmd;


    /**
     * 订单开票
     *
     * @param request htpRequest
     * @param param   String
     * @return JSONObject
     * @throws Exception ex
     */
    @ApiOperation(value = "订单开票")
    @RequestMapping(path = "/api/cs/oc/oms/v1/checkAddOrderInvoicing", method = {RequestMethod.POST, RequestMethod.GET})
    public JSONObject addInvoice(HttpServletRequest request, @RequestParam(value = "param")
            String param) throws Exception {
        SaveInvoiceRequest saveInvoiceRequest = JSON.parseObject(param, SaveInvoiceRequest.class);
        User loginUser = r3PrimWebAuthService.getLoginPrimWebUser(request);
        //User loginUser = this.getRootUser();
        if (!validateParam(saveInvoiceRequest, loginUser)) {
            ValueHolderV14 v = new ValueHolderV14();
            v.setMessage(Resources.getMessage("参数不正确"));
            v.setCode(ResultCode.FAIL);
            return v.toJSONObject();
        }

        ValueHolderV14 vh = ocBorderInvoiceCmd.checkAddOrderInvoicing(saveInvoiceRequest, loginUser);
        String result = JSON.toJSONStringWithDateFormat(vh.toJSONObject(),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue);

        return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
    }


    /**
     * 记录开票.校验
     *
     * @param request htpRequest
     * @param param   String
     * @return JSONObject
     * @throws Exception ex
     */
    @ApiOperation(value = "校验记录开票")
    @RequestMapping(path = "/api/cs/oc/oms/v1/checkRecordInvoicing", method = {RequestMethod.POST, RequestMethod.GET})
    public JSONObject checkRecordOrderInvoicing(HttpServletRequest request, @RequestParam(value = "param")
            String param) throws Exception {

        // 校验
        if (param == null || param.trim().length() < 8) {
            ValueHolderV14 v = new ValueHolderV14();
            v.setMessage(Resources.getMessage("参数不正确"));
            v.setCode(ResultCode.FAIL);
            log.error("###" + this.getClass().getName() + "###参数不正确");
            return v.toJSONObject();
        }

        // 解析
        JSONObject paramObj = JSON.parseObject(param);
        User loginUser = r3PrimWebAuthService.getLoginPrimWebUser(request);
        // User loginUser = this.getRootUser();

        // 服务

        ValueHolderV14 vh = ocBorderInvoiceCmd.checkRecordInvoice(paramObj, loginUser);
        String result = JSON.toJSONStringWithDateFormat(vh.toJSONObject(),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue);
        return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
    }

    /**
     * 记录开票
     *
     * @param request htpRequest
     * @param param   String
     * @return JSONObject
     * @throws Exception ex
     */
    @ApiOperation(value = "记录开票")
    @RequestMapping(path = "/api/cs/oc/oms/v1/recordInvoicing", method = {RequestMethod.POST, RequestMethod.GET})
    public JSONObject recordOrderInvoicing(HttpServletRequest request, @RequestParam(value = "param")
            String param) throws Exception {

        User loginUser = r3PrimWebAuthService.getLoginPrimWebUser(request);
        // User loginUser = this.getRootUser();
        // 校验
        if (param == null || loginUser == null) {
            log.error("###" + this.getClass().getName() + "###参数不正确");
        }
        // 解析
        SaveRecordInvoiceRequest ocBOrderInvoiceInform;
        try {
            ocBOrderInvoiceInform = JSON.parseObject(param, SaveRecordInvoiceRequest.class);
        } catch (Exception e) {
            ValueHolderV14 vh1 = new ValueHolderV14();
            vh1.setCode(ResultCode.FAIL);
            vh1.setMessage("参数解析异常:" + e);
            return vh1.toJSONObject();
        }

        // 服务

        ValueHolderV14 vh = ocBorderInvoiceCmd.recordOrderInvoicing(ocBOrderInvoiceInform, loginUser);
        String result = JSON.toJSONStringWithDateFormat(vh.toJSONObject(),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue);

        return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
    }


    /**
     * 参数校验
     *
     * @param saveInvoiceRequest SaveInvoiceRequest
     * @return bool
     */
    private boolean validateParam(SaveInvoiceRequest saveInvoiceRequest, User usr) {
        if (usr == null) {
            return false;
        }
        if (saveInvoiceRequest == null) {
            return false;
        }
        Long[] ids = saveInvoiceRequest.getIds();
        if (ids == null || ids.length < 1) {
            return false;
        }
        return true;
    }

}
