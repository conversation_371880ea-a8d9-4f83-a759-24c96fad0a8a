package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

@TableName(value = "oc_t_order_makeup")
@Data
public class OcTOrderMakeup extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "ORDER_ID")
    private Long orderId;

    @JSONField(name = "ORDER_NO")
    private String orderNo;

    @JSONField(name = "ORDER_TYPE")
    private Integer orderType;

    @JSONField(name = "MAKEUP_TYPE")
    private Integer makeupType;

    @JSONField(name = "CHANNEL_TYPE")
    private Integer channelType;

    @J<PERSON><PERSON>ield(name = "MAKEUP_STATUS")
    private Integer makeupStatus;

    @J<PERSON>NField(name = "MAKEUP_REMARKS")
    private String makeupRemarks;

    @<PERSON><PERSON><PERSON>ield(name = "TIMES")
    private Long times;

    @J<PERSON>NField(name = "<PERSON>WNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;
}