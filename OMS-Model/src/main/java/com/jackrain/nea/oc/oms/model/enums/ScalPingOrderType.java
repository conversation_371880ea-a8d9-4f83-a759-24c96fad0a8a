package com.jackrain.nea.oc.oms.model.enums;

/**
 * 刷单类型枚举
 *
 * @author: heliu
 * @since: 2019-03-21
 * create at : 2019-03-21 12:09  BACKFLOWSTATUS
 */
public enum ScalPingOrderType {

    /**
     * 刷单自动发货
     */
    SDZDFH,

    /**
     * 替换商品发货
     */
    THSPFH,

    /**
     * 刷单外包发货
     */
    SDWBFH;

    public int toInteger() {
        if (this == ScalPingOrderType.SDZDFH) {
            return 1;
        } else if (this == ScalPingOrderType.THSPFH) {
            return 2;
        } else if (this == ScalPingOrderType.SDWBFH) {
            return 3;
        } else {
            return 0;
        }
    }

}
