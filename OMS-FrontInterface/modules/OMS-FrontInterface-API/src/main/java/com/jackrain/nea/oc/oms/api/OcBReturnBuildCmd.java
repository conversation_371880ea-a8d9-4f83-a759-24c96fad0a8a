package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.oc.oms.model.relation.OcBReturnOrderRelation;
import com.jackrain.nea.sys.domain.ValueHolderV14;

/**
 * @program: R3-OC-OMS-V1.4
 * @author: Lijp
 * @create: 2019-03-28 14:30
 */
public interface OcBReturnBuildCmd {

    /**
     * 生成换货订单服务
     * 根据退换货单的数据生成一张新的全渠道订单
     *
     * @param ocBReturnOrderRelation
     * @return
     */
    ValueHolderV14 orderSave(OcBReturnOrderRelation ocBReturnOrderRelation);
}
