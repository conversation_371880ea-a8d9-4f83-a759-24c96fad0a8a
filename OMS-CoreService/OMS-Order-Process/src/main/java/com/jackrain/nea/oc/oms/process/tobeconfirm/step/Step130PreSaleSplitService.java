package com.jackrain.nea.oc.oms.process.tobeconfirm.step;

import com.google.common.base.Throwables;
import com.jackrain.nea.hub.enums.OrderStatusEnum;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.config.OmsSystemConfig;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.resources.OrderOccupyStatus;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.process.tobeconfirm.util.TobeConfirmedUtil;
import com.jackrain.nea.oc.oms.services.OmsOccupyTaskService;
import com.jackrain.nea.oc.oms.services.OmsOrderItemService;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.services.advance.OmsOrderAdvanceSplit;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.oc.oms.util.SplitMessageUtil;
import com.jackrain.nea.oc.oms.util.SplitOrderUtils;
import com.jackrain.nea.sg.service.SgOccupiedInventoryService;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 调用占用库存策略，订单占用库存，更新占单状态为4，如果都占库存，并更新订单状态为1，如果占用不完整，则更新状态为2
 *
 * @author: 易邵峰
 * @since: 2019-01-20
 * create at : 2019-01-20 02:16
 */
@Step(order = 130, description = "调用占用库存策略")
@Slf4j
@Component
public class Step130PreSaleSplitService extends BaseTobeConfirmedProcessStep implements IOmsOrderProcessStep<OcBOrderRelation> {

    @Autowired
    private OmsOrderService omsOrderService;
    @Autowired
    private OmsOrderAdvanceSplit omsOrderAdvanceSplit;

    @Override
    public ProcessStepResult<OcBOrderRelation> startProcess(OcBOrderRelation orderInfo,
                                                            ProcessStepResult preStepResult,
                                                            boolean isAutoMakeup, User operateUser) {
        try {
            OcBOrder ocBOrder = orderInfo.getOrderInfo();
            if (TobeConfirmedUtil.checkIsJcOrder(orderInfo)
                    || orderInfo.getOrderInfo().getPlatform().equals(PlatFormEnum.ALIBABAASCP.getCode())) {
                return new ProcessStepResult<>(StepStatus.SUCCESS);
            }
            if (!TobeConfirmedUtil.checkIsJcOrder(orderInfo)) {
                //判断订单是否是预售标，不是的直接走 否则进行预售拆单
                boolean preSaleStatus = Optional.ofNullable(ocBOrder.getDouble11PresaleStatus()).orElse(0) == 1;
                if (preSaleStatus) {
                    omsOrderAdvanceSplit.orderAdvanceSplit(orderInfo, operateUser);
                } else {
                    return new ProcessStepResult<>(StepStatus.SUCCESS);
                }
            }
            return new ProcessStepResult<>(StepStatus.SUCCESS);
        } catch (Exception ex) {
            OcBOrder errorOrderInfo = new OcBOrder();
            String operateMessage = "订单OrderId" + orderInfo.getOrderId() + "预售拆单服务异常,异常信息-->" + ex.getMessage();
            log.error(LogUtil.format("预售拆单服务异常,异常信息:{}", "预售拆单服务异常", orderInfo.getOrderId()), Throwables.getStackTraceAsString(ex));
            errorOrderInfo.setSysremark(SplitMessageUtil.splitMesssage(operateMessage));
            errorOrderInfo.setId(orderInfo.getOrderId());
            errorOrderInfo.setOccupyStatus(OrderOccupyStatus.STATUS_50);
            omsOrderService.updateOrderInfo(errorOrderInfo);
            omsToBeConfirmedTaskService.updateToBeConfirmedTaskByOrderId(orderInfo.getOrderId());
            return new ProcessStepResult<>(StepStatus.FAILED, operateMessage);
        }
    }
}
