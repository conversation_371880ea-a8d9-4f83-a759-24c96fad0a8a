package com.jackrain.nea.ac.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.oc.oms.model.constant.AcConstant;
import com.jackrain.nea.ac.model.AcFPayableAdjustmentDO;
import com.jackrain.nea.ac.model.AcFPayableAdjustmentItemDO;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.ac.AcFPayableAdjustmentItemMapper;
import com.jackrain.nea.oc.oms.mapper.ac.AcFPayableAdjustmentMapper;
import com.jackrain.nea.oc.oms.model.enums.ac.OperatorLogTypeEnum;
import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.AcBeanUtils;
import com.jackrain.nea.util.AcPayableAdjustmentPushESUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class PayableAdjustmentFiAuditService extends CommandAdapter {
    @Resource
    private AcFPayableAdjustmentMapper acFPayableAdjustmentMapper;

    @Resource
    private AcFPayableAdjustmentItemMapper acFPayableAdjustmentItemMapper;

    @Resource
    private PayableAdjustmentSaveService payableAdjustmentSaveService;

    @Resource
    private CpRpcService cpRpcService;

//    @Resource
//    private AcScRpcService acScRpcService;

    /**
     * 除法
     *
     * @param dec1 数字1
     * @param dec2 数字2
     * @param scale 位数
     * @param roundingMode 四舍五入规则
     * @return dec1 / dec2
     */
    public static BigDecimal divideBigDecimal(final BigDecimal dec1, final BigDecimal dec2,
                                              final int scale, final int roundingMode) {
        if (dec2 == null || BigDecimal.ZERO.compareTo(dec2) == 0) {
            return BigDecimal.ZERO;
        }
        return (dec1 != null ? dec1 : BigDecimal.ZERO).divide(dec2, scale, roundingMode);
    }

    private void checkStatus(AcFPayableAdjustmentDO acFPayableAdjustmentDO, Long objId) {
        int iStatus = acFPayableAdjustmentDO.getBillStatus();
        if (iStatus != AcConstant.CON_BILL_STATUS_03) {
            throw new NDSException("单据处于已业审状态才能进行财审！");
        }
        AssertUtil.notNull(acFPayableAdjustmentDO.getResponsibleParty(),"当前单据责任方为空，不允许财审!");

        List<AcFPayableAdjustmentItemDO> acFPayableAdjustmentItemDOList =
                acFPayableAdjustmentItemMapper.listByItemId(objId);
        if (acFPayableAdjustmentItemDOList == null || acFPayableAdjustmentItemDOList.size() <= 0) {
            throw new NDSException("明细没有记录，不允许财审！");
        }
    }

    /**
     * 财审
     *
     * @param querySession 参数封装
     * @return 返回状态
     * @throws NDSException 异常信息
     */
    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        ValueHolder valueHolder = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);

        if (param != null) {
            JSONArray itemArray = AcBeanUtils.makeAuditJsonArray(param);
            JSONArray errorArray = new JSONArray();
            if (itemArray.size() > 0) {
                for (int i = 0; i < itemArray.size(); i++) {
                    Long id = itemArray.getLong(i);
                    try {
                        auditFunction(id,param, querySession);
                    } catch (Exception e) {
                        JSONObject errJo = new JSONObject();
                        errJo.put("objid", id);
                        errJo.put("message", e.getMessage());
                        errorArray.add(errJo);
                    }
                }
            } else {
                throw new NDSException("未找到需要财审的记录！");
            }
            valueHolder = AcBeanUtils.getProcessValueHolder(itemArray, errorArray, "财审");
        } else {
            throw new NDSException("参数为空！");
        }
        return valueHolder;
    }

    @Transactional(rollbackFor = {Exception.class})
    public void auditFunction(Long objId,JSONObject param, QuerySession querySession) {
        AcFPayableAdjustmentDO acFPayableAdjustmentDO = acFPayableAdjustmentMapper.selectById(objId);
        String isReceivePayment = param.getString("IS_RECEIVE_PAYMENT");
        String remark = param.getString("REMARK");
        Date date = new Date();

        if (acFPayableAdjustmentDO != null) {
            checkStatus(acFPayableAdjustmentDO, objId);

            //BILL_STATUS    1:未审核 2:已客审 3:已业审 4:财审 5:已作废
            acFPayableAdjustmentDO.setBillStatus(AcConstant.CON_BILL_STATUS_04);
            acFPayableAdjustmentDO.setIsReceivePayment(isReceivePayment);
            acFPayableAdjustmentDO.setRemark(remark);
            acFPayableAdjustmentDO.setFinancialTrialId(Long.valueOf(querySession.getUser().getId()));

            acFPayableAdjustmentDO.setFinancialTrialTime(date);

            acFPayableAdjustmentDO.setFinancialTrialName(querySession.getUser().getName());
            acFPayableAdjustmentDO.setFinancialTrialEname(querySession.getUser().getEname());

            int iResult = acFPayableAdjustmentMapper.updateById(acFPayableAdjustmentDO);
            if (iResult < 0) {
                throw new NDSException("单据编码:" + acFPayableAdjustmentDO.getBillNo() + ",财审失败！");
            }

            //应付款调整单-操作类型=财审
            ValueHolderV14 v14 = payableAdjustmentSaveService.insertLogFun(querySession.getUser(), objId,
                    OperatorLogTypeEnum.OPERATOR_CS.getVal(),
                    OperatorLogTypeEnum.OPERATOR_CS.getText(), date);
            //推送ES
            AcPayableAdjustmentPushESUtil.pushOrder(objId);
            if (v14.getCode() == ResultCode.FAIL) {
                throw new NDSException("应付款调整单=>新增操作日志-财审失败!");
            }

//            /**
//             * 调用【结算中心】接口生成代销核算单。
//             * 当应付款调整单的单据类型为 丢单赔付-补发、丢单赔付-仅退款 才生成
//             */
//            int billType = acFPayableAdjustmentDO.getBillType() == null ? 0 : acFPayableAdjustmentDO.getBillType();
//            if (billType == PayBillTypeEnum.PAY_BF.getVal() || billType == PayBillTypeEnum.PAY_TK.getVal()) {
//                QueryWrapper<AcFPayableAdjustmentItemDO> itemQueryWrapper = new QueryWrapper<>();
//                itemQueryWrapper.eq("AC_F_PAYABLE_ADJUSTMENT_ID", objId);
//                List<AcFPayableAdjustmentItemDO> payableAdjustmentItemDOList = acFPayableAdjustmentItemMapper.selectList(itemQueryWrapper);
//
//                //如果未找到明细
//                if (payableAdjustmentItemDOList == null || payableAdjustmentItemDOList.size() <= 0) {
//                    throw new NDSException("未找到对应的明细,财审失败!");
//                }
//
//                //获取系统参数
//                String acDropSettleStore = AdParamUtil.getParam(AcConstant.AC_DROP_SETTLE_STORE);
//                if (StringUtil.isEmpty(acDropSettleStore)) {
//                    throw new NDSException("系统参数：丢件结算店仓。未维护。客审失败!");
//                }
//
//                //系统参数转换成Long类型
//                int acDropSettleStoreInt = 0;
//                try {
//                    acDropSettleStoreInt = Integer.parseInt(acDropSettleStore);
//                } catch(NumberFormatException e) {
//
//                }
//
//                List<CpCStore> cpCStoreList = cpRpcService.selectStoreById(acDropSettleStoreInt);
//                if (cpCStoreList == null) {
//                    throw new NDSException("调用逻辑仓接口【cpStoreQueryCmd.queryStoreInfoByIds】失败!");
//                }
//
//                if (cpCStoreList.size() <= 0) {
//                    throw new NDSException("查询逻辑仓数据不存在!");
//                }
//
//                Long acDropSettleStoreLong = Long.valueOf(acDropSettleStoreInt);
//                String ecode = cpCStoreList.get(0).getEcode();
//                String ename = cpCStoreList.get(0).getEname();
//                Long customerId = cpCStoreList.get(0).getCpCCustomerId();
//                String customerEcode = cpCStoreList.get(0).getCpCCustomerEcode();
//                String customerEname = cpCStoreList.get(0).getCpCCustomerEname();
//
//                if (StringUtils.isEmpty(acFPayableAdjustmentDO.getReserveVarchar01())){
//                    //如果来源单据类型为空 默认给 零售发货单
//                    acFPayableAdjustmentDO.setReserveVarchar01(AcConstant.PAYABLE_SOURCE_TYPE_1);
//                }
//                //来源单据类型为  零售发货单  调用生成 代销核算单
//                if (AcConstant.PAYABLE_SOURCE_TYPE_1.equals(acFPayableAdjustmentDO.getReserveVarchar01())){
//                    AcScSaleAccountsSaveRequest request = setSaleAccountsSaveRequestList(acFPayableAdjustmentDO,
//                            payableAdjustmentItemDOList, querySession.getUser(), acDropSettleStoreLong, ecode, ename, customerId,
//                            customerEcode, customerEname);
//                    ValueHolderV14 valueHolderV14 = acScRpcService.saveTableService(request);
//
//                    if (valueHolderV14 == null || ResultCode.FAIL == valueHolderV14.getCode()) {
//                        throw new NDSException("生成代销核算单失败！原因:" + valueHolderV14.getMessage());
//                    }
//                } else if (AcConstant.PAYABLE_SOURCE_TYPE_2.equals(acFPayableAdjustmentDO.getReserveVarchar01())){
//                    //来源单据类型为  零售退货单  调用生成 代销退货核算单
//                    AcScSaleReturnSaveRequest request = setSaleReturnSaveRequestList(acFPayableAdjustmentDO,
//                            payableAdjustmentItemDOList, querySession.getUser(), acDropSettleStoreLong, ecode, ename, customerId,
//                            customerEcode, customerEname);
//                    ValueHolderV14 valueHolderV14 = acScRpcService.saveSReturnTableService(request);
//
//                    if (valueHolderV14 == null || ResultCode.FAIL == valueHolderV14.getCode()) {
//                        throw new NDSException("生成代销退货核算单失败！原因:" + valueHolderV14.getMessage());
//                    }
//                }
//            }
        } else {
            throw new NDSException("当前记录不存在！");
        }
    }

//    private AcScSaleAccountsSaveRequest setSaleAccountsSaveRequestList(AcFPayableAdjustmentDO acFPayableAdjustmentDO,
//                                                                       List<AcFPayableAdjustmentItemDO> itemList, User user, Long acDropSettleStoreLong, String ecode, String ename, Long customerId,
//                                                                       String customerEcode, String customerEname) {
//
//        AcScSaleAccountsSaveRequest request = new AcScSaleAccountsSaveRequest();
//        //objId 用于判断新增/修改  objId < 0 新增
//        request.setObjId(-1L);
//        //当前操作用户
//        request.setLoginUser(user);
//        //转换代销核算单主表
//        AcFSaleAccounts saleAccounts = mainDataFun(acFPayableAdjustmentDO, acDropSettleStoreLong, ecode, ename, customerId,
//                customerEcode, customerEname);
//        request.setMainData(saleAccounts);
//        //转换代销核算单明细
//        List<AcFSaleAccountsItem> saleAccountsItemList = itemDataFun(itemList);
//        request.setItems(saleAccountsItemList);
//
//        return request;
//    }
//
//    private AcFSaleAccounts mainDataFun(AcFPayableAdjustmentDO mainDO, Long acDropSettleStoreLong, String ecode, String ename, Long customerId,
//                                        String customerEcode, String customerEname) {
//        AcFSaleAccounts saleAccounts = new AcFSaleAccounts();
//
//        //日期=当前日期
//        saleAccounts.setBillDate(new Date());
//
//        //销售类型 NOR:正常销售 ORD:销售
//        saleAccounts.setSaleType("NOR");
//
//        //核算类型 1:正常核算 2:线上代销  3:线下代销  4:丢件赔偿
//        //默认带过去的是 丢件赔偿
//        saleAccounts.setAccountType(4);
//
//        //发货逻辑仓
//        saleAccounts.setCpCOrigId(acDropSettleStoreLong);
//        saleAccounts.setCpCOrigEcode(ecode);
//        saleAccounts.setCpCOrigEname(ename);
//
//        //发货经销商
//        saleAccounts.setCpCCustomerIdSend(customerId);
//        saleAccounts.setCpCCustomerEcodeSend(customerEcode);
//        saleAccounts.setCpCCustomerEnameSend(customerEname);
//
//        //收货逻辑仓
////        saleAccounts.setCpCDestId(mainDO.getCpCPhyWarehouseId());
////        saleAccounts.setCpCDestEcode(mainDO.getCpCPhyWarehouseEcode());
////        saleAccounts.setCpCDestEname(mainDO.getCpCPhyWarehouseEname());
//
//        //收货经销商=【应付款调整单】的实体仓 CJM-2020-02-11
//        saleAccounts.setCpCCustomerId(mainDO.getCpCPhyWarehouseId());
//        saleAccounts.setCpCCustomerEcode(mainDO.getCpCPhyWarehouseEcode());
//        saleAccounts.setCpCCustomerEname(mainDO.getCpCPhyWarehouseEname());
//
//        //出库日期 out_time
//        saleAccounts.setOutTime(mainDO.getSourceOutsourceDate());
//
//        //物流公司
//        saleAccounts.setCpCLogisticsId(mainDO.getCpCLogisticsId());
//        saleAccounts.setCpCLogisticsEcode(mainDO.getCpCLogisticsEcode());
//        saleAccounts.setCpCLogisticsEname(mainDO.getCpCLogisticsEname());
//
//        //备注
//        saleAccounts.setRemark(mainDO.getRemark());
//
//        //来源单号id
//        saleAccounts.setSourceBillId(mainDO.getId());
//
//        //来源单号
//        saleAccounts.setSourceBillNo(mainDO.getBillNo());
//
//        //单据状态 1:未审核 2:已审核 3:已作废
//        saleAccounts.setStatus(1);
//
//        //代销运费 默认为0
//        saleAccounts.setAmtSellFee(BigDecimal.ZERO);
//
//        //平台单号
//        saleAccounts.setSourceCode(mainDO.getTid());
//
//        return saleAccounts;
//    }
//
//    private List<AcFSaleAccountsItem> itemDataFun(List<AcFPayableAdjustmentItemDO> payableAdjustmentItemDOList) {
//
//        //循环应付款调整单明细 组装生成代销核算单的明细
//        List<AcFSaleAccountsItem> itemDOList = new ArrayList<>();
//        for (AcFPayableAdjustmentItemDO itemDO : payableAdjustmentItemDOList) {
//
//            AcFSaleAccountsItem acFSaleAccountsItem = new AcFSaleAccountsItem();
//            //id 由于是新增行，所以默认都是-1
//            acFSaleAccountsItem.setId(-1L);
//            //条码
//            acFSaleAccountsItem.setPsCSkuId(itemDO.getPsCSkuId());
//            acFSaleAccountsItem.setPsCSkuEcode(itemDO.getPsCSkuEcode());
//            //商品
//            Long psCProId = itemDO.getPsCProId() == null ? 0L : itemDO.getPsCProId();
//            acFSaleAccountsItem.setPsCProId(psCProId);
//            acFSaleAccountsItem.setPsCProEcode(itemDO.getPsCProEcode());
//            acFSaleAccountsItem.setPsCProEname(itemDO.getPsCProEname());
//            //颜色
//            acFSaleAccountsItem.setPsCClrId(itemDO.getPsCClrId());
//            acFSaleAccountsItem.setPsCClrEcode(itemDO.getPsCClrEcode());
//            acFSaleAccountsItem.setPsCClrEname(itemDO.getPsCClrEname());
//            //尺寸
//            acFSaleAccountsItem.setPsCSizeId(itemDO.getPsCSizeId());
//            acFSaleAccountsItem.setPsCSizeEcode(itemDO.getPsCSizeEcode());
//            acFSaleAccountsItem.setPsCSizeEname(itemDO.getPsCSizeEname());
//
//            //吊牌价 = standard_price
//            acFSaleAccountsItem.setPriceList(itemDO.getStandardPrice());
//            //是否赠品
//            acFSaleAccountsItem.setIsGift(0);
//
//            //销售数量
//            acFSaleAccountsItem.setQty(itemDO.getQty());
//            //出库数量
//            acFSaleAccountsItem.setQtyOut(itemDO.getQty());
//
//            //销售价 = 原单的实际成交价 true_price
//            acFSaleAccountsItem.setPrice(itemDO.getTruePrice());
//            //审核价=应付单价
//            acFSaleAccountsItem.setAmtCheck(divideBigDecimal(itemDO.getPayablePrice(), itemDO.getQty(),2, 4));
//            //审核金额=应付金额
//            acFSaleAccountsItem.setAmtCheckPrice(itemDO.getPayablePrice());
//
//            //备注
//            acFSaleAccountsItem.setRemark("");
//
//            //国标码
//            acFSaleAccountsItem.setGbcode(itemDO.getGbcode());
//
//            //是否可用，创建日期，修改日期
//            acFSaleAccountsItem.setIsactive("Y");
//            Date date = new Date();
//            acFSaleAccountsItem.setCreationdate(date);
//            acFSaleAccountsItem.setModifieddate(date);
//
//            itemDOList.add(acFSaleAccountsItem);
//        }
//
//        return itemDOList;
//    }
//
//
//    /**
//     * 代销退货核算单
//     * @return
//     */
//    private AcScSaleReturnSaveRequest setSaleReturnSaveRequestList(AcFPayableAdjustmentDO acFPayableAdjustmentDO,
//                                                                   List<AcFPayableAdjustmentItemDO> itemList, User user, Long acDropSettleStoreLong, String ecode, String ename, Long customerId,
//                                                                   String customerEcode, String customerEname) {
//
//        AcScSaleReturnSaveRequest request = new AcScSaleReturnSaveRequest();
//        //objId 用于判断新增/修改  objId < 0 新增
//        request.setObjId(-1L);
//        //当前操作用户
//        request.setLoginUser(user);
//        //转换代销退货核算单主表
//        AcFSaleReturn saleReturn = mainDataReturn(acFPayableAdjustmentDO, acDropSettleStoreLong, ecode, ename, customerId,
//                customerEcode, customerEname);
//        request.setMainData(saleReturn);
//        //转换代销退货核算单明细
//        List<AcFSaleReturnItem> saleReturnItems = itemDataReturn(itemList);
//        request.setItems(saleReturnItems);
//
//        return request;
//    }
//
//    private AcFSaleReturn mainDataReturn(AcFPayableAdjustmentDO mainDO, Long acDropSettleStoreLong, String ecode, String ename, Long customerId,
//                                         String customerEcode, String customerEname) {
//
//        AcFSaleReturn saleReturn =new AcFSaleReturn();
//        //日期=当前日期
//        saleReturn.setBillDate(new Date());
//        //销售类型 NOR:正常销售 ORD:销售
//        //saleReturn.setSaleType("NOR");
//
//        //核算类型 1:正常核算 2:线上代销  3:线下代销  4:丢件赔偿
//        //默认带过去的是 丢件赔偿
//        saleReturn.setAccountType(4);
//        //发货店仓
//        //saleReturn.setCpCOrigId(null);
//        //saleReturn.setCpCOrigEcode(ecode);
//        //saleReturn.setCpCOrigEname(ename);
//        //发货经销商
//        saleReturn.setCpCCustomerIdSend(mainDO.getCpCPhyWarehouseId());
//        saleReturn.setCpCCustomerEcodeSend(mainDO.getCpCPhyWarehouseEcode());
//        saleReturn.setCpCCustomerEnameSend(mainDO.getCpCPhyWarehouseEname());
//        //收货逻辑仓
//        saleReturn.setCpCDestId(acDropSettleStoreLong);
//        saleReturn.setCpCDestEcode(ecode);
//        saleReturn.setCpCDestEname(ename);
//        //收货经销商=【应付款调整单】的实体仓 CJM-2020-02-11
//        saleReturn.setCpCCustomerId(customerId);
//        saleReturn.setCpCCustomerEcode(customerEcode);
//        saleReturn.setCpCCustomerEname(customerEname);
//
//        //出库日期 out_time
//        //saleReturn.setOutTime(mainDO.getSourceOutsourceDate());
//        saleReturn.setInTime(mainDO.getSourceOutsourceDate());
//        //备注
//        saleReturn.setRemark(mainDO.getRemark());
//        //来源单号id
//        saleReturn.setSourceBillId(mainDO.getId());
//        //来源单号
//        saleReturn.setSourceBillNo(mainDO.getBillNo());
//        //单据状态 1:未审核 2:已审核 3:已作废
//        saleReturn.setStatus(1);
//        //代销运费 默认为0
//        saleReturn.setAmtSellFee(BigDecimal.ZERO);
//        return saleReturn;
//    }
//
//
//    private List<AcFSaleReturnItem> itemDataReturn(List<AcFPayableAdjustmentItemDO> payableAdjustmentItemDOList) {
//
//        //循环应付款调整单明细 组装生成代销核算单的明细
//        List<AcFSaleReturnItem> itemDOList = new ArrayList<>();
//        for (AcFPayableAdjustmentItemDO itemDO : payableAdjustmentItemDOList) {
//
//            AcFSaleReturnItem returnItem = new AcFSaleReturnItem();
//            //id 由于是新增行，所以默认都是-1
//            returnItem.setId(-1L);
//            //条码
//            returnItem.setPsCSkuId(itemDO.getPsCSkuId());
//            returnItem.setPsCSkuEcode(itemDO.getPsCSkuEcode());
//            //商品
//            Long psCProId = itemDO.getPsCProId() == null ? 0L : itemDO.getPsCProId();
//            returnItem.setPsCProId(psCProId);
//            returnItem.setPsCProEcode(itemDO.getPsCProEcode());
//            returnItem.setPsCProEname(itemDO.getPsCProEname());
//            //颜色
//            returnItem.setPsCClrId(itemDO.getPsCClrId());
//            returnItem.setPsCClrEcode(itemDO.getPsCClrEcode());
//            returnItem.setPsCClrEname(itemDO.getPsCClrEname());
//            //尺寸
//            returnItem.setPsCSizeId(itemDO.getPsCSizeId());
//            returnItem.setPsCSizeEcode(itemDO.getPsCSizeEcode());
//            returnItem.setPsCSizeEname(itemDO.getPsCSizeEname());
//            //吊牌价 = standard_price
//            returnItem.setPriceList(itemDO.getStandardPrice());
//            //销售数量
//            returnItem.setQty(itemDO.getQty());
//            //出库数量
//            returnItem.setQtyIn(itemDO.getQty());
//            //吊牌价
//            returnItem.setPriceList(itemDO.getStandardPrice());
//            //销售价 = 原单的实际成交价 true_price
//            returnItem.setAmt(itemDO.getTruePrice());
//            //审核价=应付单价
//            returnItem.setAmtCheck(divideBigDecimal(itemDO.getPayablePrice(), itemDO.getQty(),2, 4));
//            //审核金额=应付金额
//            returnItem.setAmtCheckPrice(itemDO.getPayablePrice());
//            //销售金额
//            returnItem.setAmtPrice(itemDO.getTruePrice());
//            //备注
//            returnItem.setRemark("");
//            //国标码
//            returnItem.setGbcode(itemDO.getGbcode());
//            //是否可用，创建日期，修改日期
//            returnItem.setIsactive("Y");
//            Date date = new Date();
//            returnItem.setCreationdate(date);
//            returnItem.setModifieddate(date);
//
//            itemDOList.add(returnItem);
//        }
//
//        return itemDOList;
//    }

}
