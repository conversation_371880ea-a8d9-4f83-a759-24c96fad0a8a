package com.jackrain.nea.oc.oms.process.transfer.impl.normal.jingdong;

import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.MakeupOrderType;
import com.jackrain.nea.oc.oms.model.relation.IpJingdongOrderRelation;
import com.jackrain.nea.oc.oms.process.AbstractOrderProcess;
import com.jackrain.nea.oc.oms.util.LockOrderType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> 孙俊磊
 * @since : 2019-04-24
 * create at : 2019-04-24 3:52 PM
 * 京东中间表转换到全渠道订单服务
 */
@Component
public class JingdongTransferOrderProcessImpl extends AbstractOrderProcess<IpJingdongOrderRelation> {
    public JingdongTransferOrderProcessImpl() {
        super();
    }

    @Override
    protected String getChildPackageName() {
        return "jingdong";
    }

    @Override
    protected long getProcessOrderId(IpJingdongOrderRelation orderInfo) {
        return orderInfo.getOrderId();
    }

    @Override
    protected String getProcessOrderNo(IpJingdongOrderRelation orderInfo) {
        return orderInfo.getOrderNo().toString();
    }

    @Override
    protected long getLongProcessOrderNo(IpJingdongOrderRelation orderInfo) {
        return orderInfo.getOrderNo();
    }

    @Override
    protected LockOrderType getCurrentLockOrderType() {
        return LockOrderType.TRANSFER_JINGDONG_ORDER;
    }

    @Override
    protected ChannelType getCurrentChannelType() {
        return ChannelType.JINGDONG;
    }

    @Override
    protected MakeupOrderType getCurrentMakeupOrderType() {
        return MakeupOrderType.TRANSFER_ORDER;
    }

    /**
     * 京东订单接口，平台单号
     *
     * @param orderInfo 订单单据
     * @return
     */
    @Override
    protected String getSourceTid(IpJingdongOrderRelation orderInfo) {
        return String.valueOf(orderInfo.getOrderNo());
    }
}
