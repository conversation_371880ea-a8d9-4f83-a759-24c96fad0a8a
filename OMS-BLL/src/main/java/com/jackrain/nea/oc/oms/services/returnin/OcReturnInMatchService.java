package com.jackrain.nea.oc.oms.services.returnin;

import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.Enum.ThirdWmsTypeEnum;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.model.enums.IsWrongReceive;
import com.jackrain.nea.oc.oms.model.enums.MatchProcessState;
import com.jackrain.nea.oc.oms.model.enums.ReturnInBilStatus;
import com.jackrain.nea.oc.oms.model.enums.ReturnInType;
import com.jackrain.nea.oc.oms.model.relation.RefundInRelation;
import com.jackrain.nea.oc.oms.model.table.OcBRefundIn;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInActualItem;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInProductItem;
import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ThreadLocalUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.event.Level;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2022/7/20
 */
@Slf4j
@Component
public class OcReturnInMatchService {

    @Autowired
    private OcReturnInSupport returnInService;

    @Autowired
    private OcRefundInMatchOrderService matchOrderService;

    @Autowired
    private OcRefundInMatchReturnService matchReturnService;

    @Autowired
    private OcReturnInNameLessMatchService nameLessMatchService;

    @Autowired
    private OcReturnInAdjustService ocReturnInAdjustService;

    @Autowired
    private OcReturnInB2BStockIn ocReturnInB2BStockIn;

    @Autowired
    private CpRpcService cpRpcService;


    private Map<MatchProcessState, BiFunction<MatchProcessState, RefundInRelation, User>> syncFunctions;

    /**
     * 匹配
     *
     * @param id
     * @param usr
     * @return
     */
    public ValueHolderV14 match(Long id, User usr) {
        ValueHolderV14 vh = new ValueHolderV14(ResultCode.SUCCESS, "success");
        String wmsBillNo = null;
        String logKey = "ReturnInMatch." + id;
        long l = System.currentTimeMillis();
        try {
            validationParameter(id, usr);
            RefundInRelation refundInRelation = getRefundInRelation(id);
            wmsBillNo = refundInRelation.getRefundIn().getWmsBillNo();
            logStep("4.matchProcessing");
            matchProcessing(refundInRelation, MatchProcessState.INIT);
        } catch (Exception e) {
            handleException(id, e, vh);
        } finally {
            logStep("CsmT:" + (System.currentTimeMillis() - l));
            OcReturnInSupport.clearResources(Level.INFO, logKey, wmsBillNo);
        }
        return vh;
    }

    /**
     * check
     *
     * @param id
     * @param user
     */
    private void validationParameter(Long id, User user) {
        logStep("1.Start.Param.id:%d", id);
        AssertUtil.notNull(user, "用户为空");
        AssertUtil.notNull(id, Resources.getMessage("入库单编号为空", user.getLocale()));
        ThreadLocalUtil.users.set(user);
        logStep("2.pass.check.Parameter");
    }

    /**
     * Query Data
     *
     * @param id param
     * @return refundIn , refundInProductItem
     */
    private RefundInRelation getRefundInRelation(Long id) {

        OcBRefundIn refundIn = returnInService.getRefundIn(id);
        AssertUtil.notNull(refundIn, "未查询到入库结果单");

        List<OcBRefundInProductItem> items = returnInService.getRefundInProducts(id);
        AssertUtil.notEmpty(items, "未查询到入库结果单商品明细");

        Integer inType = refundIn.getInType();
        AssertUtil.notNull(inType, "入库类型为空");

        RefundInRelation refundRlt = new RefundInRelation();
        Integer isWrongReceive = refundIn.getIsWrongReceive();
        if (IsWrongReceive.YES.val().equals(isWrongReceive)) {
            List<OcBRefundInActualItem> actualItems = returnInService.getActualInItems(id);
            AssertUtil.notEmpty(actualItems, "错收入库结果单, 实物明细为空");
            refundRlt.setActualItems(actualItems);
        }

        refundRlt.setRefundIn(refundIn);
        refundRlt.setItems(items);
        //    logStep("3.Query.Bill.Result:" + JSON.toJSONString(refundRlt));
        return refundRlt;
    }

    private void matchProcessing(RefundInRelation inRelation, MatchProcessState state) {
        logStep(state.toString());
        switch (state) {
            case INIT:
                MatchProcessState routeState = statisticsItems.apply(inRelation);
                matchProcessing(inRelation, routeState);
                break;
            case NORM2B:
                MatchProcessState b2bMatchProcessState = b2BProcessing(inRelation);
                matchProcessing(inRelation, b2bMatchProcessState);
                break;
            case NORM2C:
                MatchProcessState matchProcessState = normalProcessing(inRelation);
                matchProcessing(inRelation, matchProcessState);
                break;
            case NAMELESS:
                MatchProcessState matchProcessState3 = namelessMatchProcessing(inRelation);
                matchProcessing(inRelation, matchProcessState3);
                break;
            case ADJUST:
                MatchProcessState matchProcessState1 = ocReturnInAdjustService.generateAdjustBil(inRelation);
                matchProcessing(inRelation, matchProcessState1);
                break;
            case MINUS:
                MatchProcessState matchProcessState2 = ocReturnInAdjustService.generateMinusAdjustBil(inRelation);
                matchProcessing(inRelation, matchProcessState2);
                break;
            case FINISH:
                afterMatchProcessing.accept(inRelation);
            case UNNECESSARY:
            default:
                break;
        }
    }

    /**
     * statistics
     *
     * @param inRelation
     */
    private Function<RefundInRelation, MatchProcessState> statisticsItems = (inRelation) -> {
        OcBRefundIn refundIn = inRelation.getRefundIn();
        Integer billStatus = refundIn.getBillStatus();
        if (ReturnInBilStatus.COMPLETE.val().equals(billStatus)) {
            return MatchProcessState.UNNECESSARY;
        }
        //富勒查询仓库类型
        if (inRelation.getRefundIn().getCpCPhyWarehouseId() != null) {
            CpCPhyWarehouse warehouse = cpRpcService.queryByWarehouseId(inRelation.getRefundIn().getCpCPhyWarehouseId());
            if (ThirdWmsTypeEnum.FLWMS.getCode().equals(warehouse.getWmsType())) {
                inRelation.setFluxWms(true);
            }
        }
        List<OcBRefundInProductItem> items = inRelation.getItems();
        for (OcBRefundInProductItem item : items) {
            if (inRelation.isMatched(item)) {
                inRelation.collectMatched(item);
                if (inRelation.isNeedMinusAdjust(item)) {
                    inRelation.collectMinusAdjust(item);
                }
                continue;
            }
            if (inRelation.isUnMatch(item)) {
                inRelation.collectUnMatch(item);
            }
        }
        if (inRelation.branchNorm2CMatch()) {
            return MatchProcessState.NORM2C;
        }
        if (inRelation.branchNorm2BMatch()) {
            return MatchProcessState.NORM2B;
        }
        if (inRelation.branchNameless()) {
            return MatchProcessState.NAMELESS;
        }
        if (inRelation.branchMinusAdjust()) {
            return MatchProcessState.MINUS;
        }
        return MatchProcessState.ADJUST;
    };

    /**
     * B2B 匹配入库
     *
     * @param inRelation
     * @return
     */
    private MatchProcessState b2BProcessing(RefundInRelation inRelation) {
        logStep("B2BProcessing");
        // b2b match Return
        ocReturnInB2BStockIn.matchReturnProcessor(inRelation);
        return MatchProcessState.FINISH;
    }

    /**
     * B2C 正常入库
     *
     * @param inRelation
     * @return
     */
    private MatchProcessState normalProcessing(RefundInRelation inRelation) {
        logStep("normalProcessing");
        // match Return
        matchReturnService.normalMatchReturnProcessor(inRelation);

        List<OcBRefundInProductItem> unMatchItems = inRelation.getUnMatchItems();
        if (CollectionUtils.isEmpty(unMatchItems)) {
            logStep("normalProcessing.match.return result whole");
            return MatchProcessState.MINUS;
        }
        // match by logistics no
        matchOrderService.normalMatchOrderProcessor(inRelation, true);
        if (CollectionUtils.isEmpty(unMatchItems)) {
            logStep("normalProcessing.match.order result whole");
            return MatchProcessState.MINUS;
        }
        return MatchProcessState.ADJUST;
    }

    /**
     * B2C 无名件入库( 异常包裹登记)
     *
     * @param inRelation
     * @return
     */
    private MatchProcessState namelessMatchProcessing(RefundInRelation inRelation) {
        logStep("namelessMatchProcessing.start");
        for (OcBRefundInProductItem item : inRelation.getItems()) {
            boolean adjust = inRelation.isAdjust(item);
            if (adjust) {
                inRelation.setHasAdjustedEle(true);
                logStep("namelessMatchProcessing.has adjust ele");
                break;
            }
        }
        MatchProcessState matchProcessState = nameLessMatchService.namelessMatchProcessing(inRelation);
        logStep("namelessMatchProcessing.end." + matchProcessState.toString());
        return matchProcessState;
    }

    /**
     * 最终状态计算
     */
    private Consumer<RefundInRelation> afterMatchProcessing = inRelation -> {
        logStep("afterMatchProcessing");
        OcBRefundIn refundIn = inRelation.getRefundIn();
        Integer billStatus = refundIn.getBillStatus();
        boolean isCompleted = ReturnInBilStatus.COMPLETE.val().equals(billStatus);
        if (isCompleted) {
            logStep("afterMatchProcessing.refund.completed return");
            return;
        }
        boolean hasMatched = CollectionUtils.isNotEmpty(inRelation.getMatchedItems());
        boolean emptyAdjust = CollectionUtils.isEmpty(inRelation.getUnAdjustments());
        boolean emptyMinusAdjust = CollectionUtils.isEmpty(inRelation.getUnMinusAdjustments());
        boolean emptyUnMatch = CollectionUtils.isEmpty(inRelation.getUnMatchItems());

        if (ReturnInType.NORM2C == inRelation.getReturnInType()) {
            if (hasMatched && emptyAdjust && emptyMinusAdjust && emptyUnMatch) {
                OcBRefundIn newRefundIn = new OcBRefundIn();
                newRefundIn.setId(refundIn.getId());
                newRefundIn.setBillStatus(ReturnInBilStatus.COMPLETE.val());
                returnInService.updateRefundInBil(newRefundIn);
                logStep("afterMatchProcessing.refund.inType is normal,update bill completed");
                return;
            }
        }
        if (!emptyUnMatch || !emptyAdjust || !emptyMinusAdjust) {
            return;
        }
        OcBRefundIn newRefundIn = new OcBRefundIn();
        newRefundIn.setId(refundIn.getId());
        newRefundIn.setBillStatus(ReturnInBilStatus.COMPLETE.val());
        returnInService.updateRefundInBil(newRefundIn);
        logStep("update bill completed");
    };

    /**
     * exception
     *
     * @param id id
     * @param e  id
     * @param vh id
     */
    private void handleException(Long id, Exception e, ValueHolderV14 vh) {
        boolean isNdsExp = e instanceof NDSException;
        String apply = OcReturnInSupport.expMsgFun.apply(e);
        vh.setCode(ResultCode.FAIL);
        vh.setMessage(apply);
        if (!isNdsExp) {
            logErrorAtOnce("入库匹配异常", e, "ReturnInMatch." + id);
        }
        logStep("99.exception." + apply);
    }

    private void logStep(String express, Object... obs) {
        ThreadLocalUtil.logStepMsg.get().add(String.format(express, obs));
    }

    /**
     * print error log
     *
     * @param expression expression
     * @param e          Exception
     * @param keys       params
     */
    private void logErrorAtOnce(String expression, Exception e, Object... keys) {
        log.error(LogUtil.format(expression + ". message:{}", keys), Throwables.getStackTraceAsString(e));
    }


    @PostConstruct
    private void iniHandleFunction() {

    }

}
