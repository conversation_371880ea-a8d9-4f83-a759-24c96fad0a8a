package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.oc.oms.model.result.GetOrderResult;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @Author: wangqiang
 * @Date: 2019-03-07  11:21
 * @Version 1.0
 */
@Mapper
public interface GetDetailMapper {
    /**
     * 查询订单信息
     *
     * @param id
     * @return
     */
    @Select("SELECT * FROM oc_b_order WHERE id =#{id}")
    GetOrderResult queryOrederInfo(@Param("id") Long id);

    @Select("<script> "
            + "SELECT * FROM oc_b_order WHERE id "
            + "in <foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<GetOrderResult> selectOrderListByIds(@Param("ids") List<Long> ids);

    /**
     * 检查 数据是否存在
     *
     * @param id 订单ID
     * @return
     */
    @Select("SELECT COUNT(*) FROM oc_b_order WHERE id =#{id}")
    Integer queryCountNum(@Param("id") Long id);

    @Select("SELECT * FROM oc_b_order_item WHERE oc_b_order_id=#{orderId} and isactive='Y'")
    List<OcBOrderItem> selectOrderItems(long orderId);

    @Select("SELECT * FROM oc_b_return_type WHERE ename = #{ename}")
    OcBReturnType selectReturnTypeByEname(@Param("code") String ename);
}
