package com.jackrain.nea.oc.controller.convert;

import com.google.common.base.Converter;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderItemExtend;
import com.jackrain.nea.oc.oms.vo.OcBOrderImpVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Objects;
import java.util.Optional;


@Component
@Slf4j
public class OrderExtendItemConverter extends Converter<OcBOrderImpVO, OcBOrderItemExtend> {


    @Override
    protected OcBOrderItemExtend doForward(OcBOrderImpVO ocBOrderImpVo) {
        OcBOrderItemExtend bOrderItemExtend = new OcBOrderItemExtend();
        BeanUtils.copyProperties(ocBOrderImpVo, bOrderItemExtend);
        bOrderItemExtend.setBK(Optional.ofNullable(ocBOrderImpVo.getSourceCode()).orElse(""));
        bOrderItemExtend.setTid(Optional.ofNullable(ocBOrderImpVo.getSourceCode()).orElse(""));
        bOrderItemExtend.setRealAmt(Objects.isNull(ocBOrderImpVo.getPriceActual()) ? BigDecimal.ZERO :
                ocBOrderImpVo.getPriceActual().multiply(Objects.isNull(ocBOrderImpVo.getQty()) ? new BigDecimal("1") : ocBOrderImpVo.getQty()));
        // 平台售价(平台售价未传时取成交单价作为平台售价)
        if(ocBOrderImpVo.getPlatformPrice() == null){
            bOrderItemExtend.setPrice(ocBOrderImpVo.getPriceActual());
        }else {
            bOrderItemExtend.setPrice(ocBOrderImpVo.getPlatformPrice());
        }
        //导入赠品 为是时 平台售价为0
        if (1==ocBOrderImpVo.getIsGift()){
            bOrderItemExtend.setPrice(BigDecimal.ZERO);

        }
        return bOrderItemExtend;
    }

    @Override
    protected OcBOrderImpVO doBackward(OcBOrderItemExtend bOrderItemExtend) {
        OcBOrderImpVO ocBOrderImpVo = new OcBOrderImpVO();
        BeanUtils.copyProperties(bOrderItemExtend, ocBOrderImpVo);
        return ocBOrderImpVo;
    }


}
