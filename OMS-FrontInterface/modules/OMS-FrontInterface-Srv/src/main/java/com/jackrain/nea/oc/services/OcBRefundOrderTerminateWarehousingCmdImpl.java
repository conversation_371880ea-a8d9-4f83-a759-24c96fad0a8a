package com.jackrain.nea.oc.services;

import com.jackrain.nea.oc.oms.api.OcBRefundOrderTerminateWarehousingCmd;
import com.jackrain.nea.oc.oms.model.request.TerminateWarehousingRequest;
import com.jackrain.nea.oc.oms.services.returnorder.OcBRefundOrderTerminateWarehousingService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR> lin yu
 * @date : 2022/8/24 下午1:46
 * @describe :
 */

@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBRefundOrderTerminateWarehousingCmdImpl implements OcBRefundOrderTerminateWarehousingCmd {

    @Autowired
    private OcBRefundOrderTerminateWarehousingService service;

    @Override
    public ValueHolderV14<String> terminateWarehousing(TerminateWarehousingRequest request) {
        return service.terminateWarehousing(request);
    }
}
