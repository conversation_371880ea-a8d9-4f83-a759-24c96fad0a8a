package com.jackrain.nea.oc.oms.model.enums;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 订单卡单原因
 * <p>
 * 枚举值参考：
 * com.jackrain.nea.oc.oms.services.OcBOrderHoldService#getReason(java.lang.String)
 *
 * <AUTHOR>
 */
public enum OrderDetentionEnum {

    CUSTOMER_CARD_OPERATE_REQUIRE(200, "客服卡单-运营要求"),

    CUSTOMER_CARD_EPIDEMIC_SITUATION_STOP(201, "客服卡单-疫情停发"),

    CUSTOMER_CARD_NOTICE_OF_DELAY(202, "客服卡单-延期等通知"),

    DOCUMENT_REVIEW_CARD_SITUATION_STOP(203, "审单卡单-疫情停发"),

    SALES_MANAGEMENT_OPERATE_REQUIRE(204, "销管卡单-运营要求"),

    DOCUMENT_REVIEW_CARD_OPERATE_REQUIRE(205, "审单卡单-运营要求"),

    DOCUMENT_REVIEW_CARD_ABNORMAL(206, "审单卡单-异常单卡单"),

    TMALL_CYCLE_BUY_CARD(207, "天猫周期购卡单"),

    TMALL_CYCLE_BUY_MODIFY_DATE_CARD(208, "天猫周期购修改配送时间后重新卡单"),

    BLACKLIST_CARD(209, "刷单用户黑名单卡单"),

    JD_SELF_FLOW_CARD(210, "京东自流转订单整单卡单"),

    LOW_TEMPERATURE_CARD(211, "低温白奶散单卡单"),

    COMMON_SKU_CARD(212, "普通商品卡单"),
    ;


    Integer key;
    String val;

    OrderDetentionEnum(Integer k, String v) {
        this.key = k;
        this.val = v;
    }

    public Integer getKey() {
        return key;
    }

    public String getVal() {
        return val;
    }

    public static String getValueByKey(Integer key) {

        String s = "";
        if (key == null) {
            return s;
        }
        for (OrderDetentionEnum e : OrderDetentionEnum.values()) {
            if (e.getKey().equals(key)) {
                s = e.getVal();
                break;
            }
        }
        return s;
    }

    public static Map<Integer, String> getMap() {
        Map<Integer, String> m = new HashMap<>();
        for (OrderDetentionEnum o : OrderDetentionEnum.values()) {
            m.put(o.getKey(), o.getVal());
        }
        m.put(null, "");
        return m;
    }

    public static Map<Integer, String> toMap() {
        return Arrays.stream(OrderDetentionEnum.values())
                .collect(Collectors.toMap(OrderDetentionEnum::getKey, OrderDetentionEnum::getVal));
    }

}
