package com.jackrain.nea.oc.oms.api;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

/**
 * @program: R3-OC-OMS-V1.4
 * @author: Liqb
 * @Desc 实缺处理:标记业务逻辑单已处理
 * @create: 2019-07-18 10:00
 */
public interface ActualLackProcessingOrdersMarkCmd {
    /**
     * 标记业务逻辑单已处理
     *
     * @param obj
     * @param user
     * @return
     * @throws NDSException
     */
    ValueHolderV14 execute(JSONObject obj, User user) throws NDSException;
}
