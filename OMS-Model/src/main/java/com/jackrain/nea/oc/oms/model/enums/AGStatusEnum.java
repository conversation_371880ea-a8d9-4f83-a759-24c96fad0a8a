package com.jackrain.nea.oc.oms.model.enums;


import com.jackrain.nea.oc.oms.model.result.QueryOrderCheckBoxResult;

import java.util.ArrayList;
import java.util.List;

/**
 * AG状态
 *
 * @author: 周琳胜
 * create at: 2019/3/25 13:20
 */
public enum AGStatusEnum {

    INIT("未传", 0),
    SUCCESS("已传", 1),
    FAIL("失败", 2),
    NOT("不传", 3);


    String key;
    Integer val;

    AGStatusEnum(String k, Integer v) {
        this.key = k;
        this.val = v;
    }

    public String getKey() {
        return key;
    }

    public Integer getVal() {
        return val;
    }

    /**
     * 转化成QueryOrderCheckBoxResult
     *
     * @return list<QueryOrderCheckBoxResult>
     */
    public static List<QueryOrderCheckBoxResult> toQueryOrderCheckBoxResult() {
        List<QueryOrderCheckBoxResult> list = new ArrayList<>();
        for (AGStatusEnum e : AGStatusEnum.values()) {
            QueryOrderCheckBoxResult o = new QueryOrderCheckBoxResult();
            o.setLabel(e.getKey());
            o.setValue(String.valueOf(e.getVal()));
            list.add(o);
        }
        return list;
    }

}


