package com.jackrain.nea.oc.oms.process.transfer.impl.exchange.taobao;

import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.MakeupOrderType;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoExchangeRelation;
import com.jackrain.nea.oc.oms.process.AbstractOrderProcess;
import com.jackrain.nea.oc.oms.util.LockOrderType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> 孙勇生
 * create at:  19/3/7  18:10
 * @description: 中间表转换到退换货订单服务
 */
@Component
public class TaobaoTransferExchangeProcessImpl1 extends AbstractOrderProcess<IpTaobaoExchangeRelation> {
    public TaobaoTransferExchangeProcessImpl1() {
        super();
    }

    @Override
    protected String getChildPackageName() {
        return "taobao";
    }

    @Override
    protected long getProcessOrderId(IpTaobaoExchangeRelation orderInfo) {
        return orderInfo.getOrderId();
    }

    @Override
    protected String getProcessOrderNo(IpTaobaoExchangeRelation orderInfo) {
        return orderInfo.getOrderNo();
    }


    @Override
    protected LockOrderType getCurrentLockOrderType() {
        return LockOrderType.TRANSFER_TAOBAO_EXCHANGE;
    }

    @Override
    protected ChannelType getCurrentChannelType() {
        return ChannelType.TAOBAO;
    }

    @Override
    protected MakeupOrderType getCurrentMakeupOrderType() {
        return MakeupOrderType.TRANSFER_ORDER;
    }
}
