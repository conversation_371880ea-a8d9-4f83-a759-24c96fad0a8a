package com.jackrain.nea.oc.oms.matcher.vo;

import lombok.*;

/**
 * Description： 输入参数
 * Author: RESET
 * Date: Created in 2020/7/9 13:23
 * Modified By:
 */
@Data
@AllArgsConstructor(access = AccessLevel.PUBLIC)
@NoArgsConstructor(access = AccessLevel.PUBLIC)
@Builder(toBuilder = true)
public class ParamInputVO {

    // 标题
    String originalTitle;
    // ID
    String originalId;
    // 备注
    String originalRemark;
    //SPU
    String originalSpu;

    // 规则类型
    Integer ruleType;
    // 规则内容
    String ruleContext;

}
