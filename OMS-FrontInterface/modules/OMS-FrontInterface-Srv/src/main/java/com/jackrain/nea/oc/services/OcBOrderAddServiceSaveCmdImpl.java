package com.jackrain.nea.oc.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBOrderAddServiceSaveCmd;
import com.jackrain.nea.oc.oms.services.OcBOrderAddServiceService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName OcBOrderAddServiceSaveCmdImpl
 * @Description 增值服务编辑、保存
 * <AUTHOR>
 * @Date 2023/11/1 16:36
 * @Version 1.0
 */

@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBOrderAddServiceSaveCmdImpl extends CommandAdapter implements OcBOrderAddServiceSaveCmd {

    @Autowired
    private OcBOrderAddServiceService addServiceService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return addServiceService.saveOrUpdate(session);
    }
}
