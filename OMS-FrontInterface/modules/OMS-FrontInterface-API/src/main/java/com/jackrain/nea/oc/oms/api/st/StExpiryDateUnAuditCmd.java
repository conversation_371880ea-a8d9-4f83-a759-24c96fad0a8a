package com.jackrain.nea.oc.oms.api.st;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.sys.Command;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;

/**
 * @Author: 黄世新
 * @Date: 2022/6/15 上午11:14
 * @Version 1.0
 */
public interface StExpiryDateUnAuditCmd extends Command {
    ValueHolderV14 unAuditNotClearRedis(JSONObject param, User user);
}
