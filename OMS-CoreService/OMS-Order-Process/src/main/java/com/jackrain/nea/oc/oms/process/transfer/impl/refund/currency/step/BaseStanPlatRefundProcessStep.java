package com.jackrain.nea.oc.oms.process.transfer.impl.refund.currency.step;

import com.burgeon.mq.core.DefaultProducerSend;
import com.jackrain.nea.oc.oms.config.TransferOrderMqConfig;
import com.jackrain.nea.oc.oms.mapper.IpBStandplatOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.services.*;
import com.jackrain.nea.oc.oms.util.StandplatRefundOrderTransferUtil;
import com.jackrain.nea.rpc.StRpcService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 通用商品退单 转化 的处理类
 *
 * @author: 夏继超
 * @since: 2019/7/14
 * create at : 2019/7/14 20:47
 */
public class BaseStanPlatRefundProcessStep {
    @Autowired
    protected IpStandplatRefundService ipStandplatRefundService;
    @Autowired
    protected OmsRefundOrderService omsRefundOrderService;
    @Autowired
    protected OmsOrderService omsOrderService;
    @Autowired
    protected OmsBeforeStandPlatReturnService omsBeforeStandPlatReturnService;
    @Autowired
    protected OmsStandPlatRefundOrderService omsStandPlatRefundOrderService;
    @Autowired
    protected OmsStandPlatAfterRefundOnlyService omsStandPlatAfterRefundOnlyService;
    @Autowired
    protected OmsReturnOrderService omsReturnOrderService;
    @Autowired
    protected StandplatRefundOrderTransferUtil standplatRefundOrderTransferUtil;

    @Autowired
    protected OmsOrderStandPlatAutoRefundService standPlatAutoRefundService;
    @Autowired
    protected OmsOrderLogService omsOrderLogService;
    @Autowired
    protected OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    protected IpBStandplatOrderMapper ipStandplatOrderMapper;

    @Autowired
    protected OcBOrderMapper ocBOrderMapper;

    @Autowired
    protected StRpcService stRpcService;

//    @Autowired
//    protected R3MqSendHelper r3MqSendHelper;

    @Autowired
    protected DefaultProducerSend defaultProducerSend;

    @Autowired
    protected TransferOrderMqConfig transferOrderMqConfig;

    @Autowired
    protected OcBReturnOrderMapper ocBReturnOrderMapper;

    @Autowired
    protected Exchange2RefundService exchange2RefundService;

    @Autowired
    protected OcBOrderOffService ocBOrderOffService;

    @Autowired
    protected OcBOrderTheAuditService ocBOrderTheAuditService;

    @Autowired
    protected OmsAfterSaleReissueService omsAfterSaleReissueService;

}
