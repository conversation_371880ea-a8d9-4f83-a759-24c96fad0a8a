package com.jackrain.nea.oc.oms.task.orderwms;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.st.service.StCExpiryDateLabelService;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/***
 * 订单传wms服务
 */
@Slf4j
@Component
public class AutoCalculateDateLabelDailyTask extends BaseR3Task implements IR3Task {

    @Autowired
    private StCExpiryDateLabelService stCExpiryDateLabelService;

    @Override
    @XxlJob("AutoCalculateDateLabelDailyTask")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult result = new RunTaskResult();
        result.setSuccess(true);
        result.setMessage("执行成功！");
        try {
            User rootUser = SystemUserResource.getRootUser();
            stCExpiryDateLabelService.calculateEffectiveLabel(rootUser, true);
        } catch (Exception ex) {
            log.error(LogUtil.format("AutoCalculateDateLabelDailyTask.execute error:{}",
                    "AutoCalculateDateLabelDailyTask.execute"), Throwables.getStackTraceAsString(ex));
            result.setSuccess(false);
            result.setMessage(ex.getMessage());
        }
        return result;
    }
}