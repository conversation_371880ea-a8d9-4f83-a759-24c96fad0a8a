package com.jackrain.nea.oc.oms.model.enums;

/**
 * 操作类型
 * <p>
 * 2020-11-11易邵峰检查
 *
 * @author: 易邵峰
 * @since: 2019-03-06
 * create at : 2019-03-06 10:21
 */
public enum OperateType {

    /**
     * 中间表转单操作
     */
    TRANSFER_ORDER,

    /**
     * 待转换订单操作
     */
    TOBE_CONFIRMED,

    /**
     * 审单操作
     */
    AUDIT_ORDER,

    /**
     * 拆单操作
     */
    SPLIT_ORDER;


    @Override
    public String toString() {
        return String.valueOf(this.toInteger());
    }

    public int toInteger() {
        if (this == OperateType.TRANSFER_ORDER) {
            return 1;
        } else if (this == OperateType.TOBE_CONFIRMED) {
            return 2;
        } else if (this == OperateType.AUDIT_ORDER) {
            return 3;
        } else if (this == OperateType.SPLIT_ORDER) {
            return 4;
        } else {
            return -1;
        }
    }

    public String toDescription() {
        if (this == OperateType.TRANSFER_ORDER) {
            return "TRANSFER_ORDER";
        } else if (this == OperateType.TOBE_CONFIRMED) {
            return "TOBE_CONFIRMED";
        } else if (this == OperateType.AUDIT_ORDER) {
            return "AUDIT_ORDER";
        } else if (this == OperateType.SPLIT_ORDER) {
            return "SPLIT_ORDER";
        } else {
            return "UNKNOWN";
        }
    }
}
