package com.jackrain.nea.oc.services;

import com.jackrain.nea.oc.oms.api.OcBorderShortageNoticeCmd;
import com.jackrain.nea.oc.oms.services.OcBorderShortageNoticeService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/17 下午8:54
 * @description 猫超缺货回传cmd
 **/
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBorderShortageNoticeImpl implements OcBorderShortageNoticeCmd {
    @Autowired
    private OcBorderShortageNoticeService ocBorderShortageNoticeService;

    @Override
    public ValueHolderV14 shortageNotice(List<Long> orderIdList, User user) {
        return ocBorderShortageNoticeService.shortageNotice(orderIdList, user);
    }
}