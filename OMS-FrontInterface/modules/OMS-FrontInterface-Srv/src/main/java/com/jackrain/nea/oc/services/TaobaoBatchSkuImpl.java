package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.api.TaobaoBatchSkuCmd;
import com.jackrain.nea.oc.oms.services.TaobaoBatchSkuService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName : TaobaoBatchSkuImpl  
 * @Description : 
 * <AUTHOR>  YCH
 * @Date: 2021-12-08 15:06  
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class TaobaoBatchSkuImpl implements TaobaoBatchSkuCmd {
    @Autowired
    private TaobaoBatchSkuService taobaoBatchSkuService;
    @Override
    public ValueHolderV14 execute(JSONObject obj, User user) {

        return taobaoBatchSkuService.handle(obj,user);
    }
}
