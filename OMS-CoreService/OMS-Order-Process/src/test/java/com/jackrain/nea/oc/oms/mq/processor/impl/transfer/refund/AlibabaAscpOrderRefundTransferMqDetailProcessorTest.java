package com.jackrain.nea.oc.oms.mq.processor.impl.transfer.refund;

import com.alibaba.fastjson.JSON;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.relation.IpAlibabaAscpOrderRelation;
import com.jackrain.nea.oc.oms.model.request.TransferOrderRequest;
import com.jackrain.nea.oc.oms.model.result.TransferOrderResult;
import com.jackrain.nea.oc.oms.process.MultiThreadOrderProcessor;
import com.jackrain.nea.oc.oms.process.transfer.impl.normal.alibabaAscp.AlibabaAscpTransferOrderProcessImpl;
import com.jackrain.nea.oc.oms.services.IpAlibabaAscpOrderService;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.ProcessStepResultList;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Profile;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest()
@Profile(value = "dev")
public class AlibabaAscpOrderRefundTransferMqDetailProcessorTest {

    @Autowired
    private AlibabaAscpOrderRefundTransferMqDetailProcessor transferMqRefundDetailProcessor;
    @Autowired
    protected MultiThreadOrderProcessor threadOrderProcessor;
    @Autowired
    private IpAlibabaAscpOrderService alibabaAscpOrderService;
    @Autowired
    private AlibabaAscpTransferOrderProcessImpl orderProcess;

    @Test
    public void getProcessStepResultList() {
        OperateOrderMqInfo operateOrderMqInfo = new OperateOrderMqInfo();
//        List<String> ids = Arrays.asList("scp0001", "scp0002");
        List<String> ids = Arrays.asList("LP00410026384540");
        String orderIds = ids.stream().collect(Collectors.joining(","));
        operateOrderMqInfo.setOrderIds(orderIds);
        ProcessStepResultList processStepResultList = transferMqRefundDetailProcessor.getProcessStepResultList(operateOrderMqInfo);
        System.out.println(JSON.toJSONString(processStepResultList));
    }

    @Test
    public void getOrderProcess() {
        ValueHolderV14<TransferOrderResult> resultValueHolderV14 = new ValueHolderV14<>();

        TransferOrderRequest transferOrderRequest = new TransferOrderRequest();

        List<String> strings = new ArrayList<>();
        strings.add("LP00409392584882");

        transferOrderRequest.setOrderNoList(strings);
        transferOrderRequest.setChannelType(ChannelType.ALIBABAASCP);

        try {
            boolean hasError = false;
            int failedNumber = 0;
            int successNumber = 0;
            List<ProcessStepResult> errorStepResultList = new ArrayList<>();
            for (String orderNo : transferOrderRequest.getOrderNoList()) {
//                if (log.isDebugEnabled()) {
//                    log.debug("AlibabaAscpTransferOrderCmdImpl.startTransferOrder OrderNo=" + orderNo + ";ChannelType="
//                            + transferOrderRequest.getChannelType());
//                }
                if (transferOrderRequest.getChannelType() == ChannelType.ALIBABAASCP) {
                    IpAlibabaAscpOrderRelation orderRelation = alibabaAscpOrderService.selectAlibabaAscpOrder(orderNo);
                    if (orderRelation != null) {
//                        if (log.isDebugEnabled()) {
//                            log.debug(this.getClass().getName()+" 猫超直发单据转换查询数据:{}", JSONObject.toJSONString(orderRelation));
//                        }
                        ProcessStepResultList processStepResultList = orderProcess.start(orderRelation, false, transferOrderRequest.getOperateUser());
                        if (!processStepResultList.isProcessSuccess()) {
                            hasError = true;
                            errorStepResultList.add(processStepResultList.getLastFailedProcessStepResult());
                            failedNumber++;
                        } else {
                            successNumber++;
                        }
                    } else {
                        hasError = false;
                        failedNumber++;
                    }
                }
            }

            if (hasError) {
                resultValueHolderV14.setCode(ResultCode.FAIL);
                StringBuilder sbMessage = new StringBuilder();
                sbMessage.append(String.format("转换成功%s条；转换失败%s条；失败原因：\r\n", successNumber, failedNumber));
                for (ProcessStepResult stepResult : errorStepResultList) {
                    if (stepResult != null) {
                        sbMessage.append(stepResult.getMessage());
                        sbMessage.append("\r\n");
                    }
                }
                resultValueHolderV14.setMessage(sbMessage.toString());
            } else {
                resultValueHolderV14.setCode(ResultCode.SUCCESS);
                resultValueHolderV14.setMessage("转换全部成功。");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            resultValueHolderV14.setCode(ResultCode.FAIL);
            resultValueHolderV14.setMessage("转换异常：" + ex.getMessage());
            //log.error("AlibabaAscpTransferOrderCmdImpl.startTransferOrder", ex);
        }
    }
}