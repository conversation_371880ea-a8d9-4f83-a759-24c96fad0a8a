//package com.jackrain.nea.oc.oms.task.jitxorder;
//
//import com.google.common.base.Throwables;
//import com.jackrain.nea.oc.oms.model.table.IpBJitxResetShipWorkflow;
//import com.jackrain.nea.oc.oms.services.IpBJitxResetShipWorkflowService;
//import com.jackrain.nea.resource.SystemTableNames;
//import com.jackrain.nea.rpc.IpRpcService;
//import com.jackrain.nea.web.face.User;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//
//import java.util.List;
//
///**
// * description：jitx 发货重置工单
// *
// * <AUTHOR>
// * @date 2021/10/18
// */
//@Slf4j
//public abstract class AbstractJitxResetShipTask extends  {
//
//    /**
//     * 最大失败次数
//     */
//    @Value("${oms.oc.order.jitx.resetShip.max.failNumber:5}")
//    private Integer failNumber;
//
//    @Autowired
//    protected IpRpcService ipRpcService;
//
//    @Override
//    protected String taskTableName() {
//        return SystemTableNames.IP_B_JITX_RESET_SHIP_WORKFLOW;
//    }
//
//    protected abstract String executeSqlWhere();
//
//    protected String whereFailNumber() {
//        return String.format(" and FAIL_NUMBER <= %d", failNumber);
//    }
//
//    protected abstract String executeSqlOrder();
//
//    protected abstract Integer getPullNum();
//
//    protected abstract void exceute(List<IpBJitxResetShipWorkflow> shipWorkflows, User operateUser);
//
//    @Autowired
//    protected IpBJitxResetShipWorkflowService ipBJitxResetShipWorkflowService;
//
//    @Override
//    protected void executeRunnable(String nodeName, String tableName, User operateUser) {
//        if (log.isDebugEnabled()) {
//            log.debug("JITX订单发货重置工单任务，where：{}，nodeName：{}，tableName：{}",
//                    executeSqlWhere(), nodeName, tableName);
//        }
//        try {
//            List<IpBJitxResetShipWorkflow> workflowList = ipBJitxResetShipWorkflowService.selectByNode(
//                    nodeName, tableName, executeSqlWhere() + whereFailNumber(),
//                    executeSqlOrder(), getPullNum());
//            if (CollectionUtils.isEmpty(workflowList)) {
//                log.info("JITX订单发货重置表查询为空nodeName：{}, taskTableName：{}, pullNum：{}",
//                        new Object[]{nodeName, tableName, getPullNum()});
//                return;
//            }
//            exceute(workflowList, operateUser);
//        } catch (Exception e) {
//            log.info("AbstractJitxResetShipask executeRunnable tableName：{}，error：{}",
//                    tableName, Throwables.getStackTraceAsString(e));
//        }
//    }
//}
