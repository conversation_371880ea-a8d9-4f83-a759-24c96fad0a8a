package com.jackrain.nea.oc.oms.services;

import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.api.OmsOrderOutCmd;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.request.OrderOutRequest;
import com.jackrain.nea.oc.oms.model.result.OrderOutResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * @author: 胡林洋
 * @since: 2019-04-28
 * create at : 2019-04-28 19:50
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", group = "oc-core", version = "1.0")
public class OrderOutCmdImpl implements OmsOrderOutCmd {

    @Autowired
    private OmsOrderService omsOrderService;
    @Autowired
    private OmsOrderOutService omsOrderOutService;

    @Override
    public ValueHolderV14<OrderOutResult> startOrderOut(OrderOutRequest orderOutRequest) {
        ValueHolderV14<OrderOutResult> resultValueHolderV14 = new ValueHolderV14<>();
        try {
            User user = null;
            Long orderId = orderOutRequest.getOrderId();
            int type = 2;
            OcBOrderRelation orderRelation = omsOrderService.selectOmsOrderInfo(orderId);
            if (orderRelation != null) {
                Date date = new Date();
                resultValueHolderV14 = omsOrderOutService.orderOutOfStock(orderId, type, user, date);
            }
            resultValueHolderV14.setCode(0);
            resultValueHolderV14.setMessage("订单出库完成");
        } catch (Exception ex) {
            resultValueHolderV14.setCode(-1);
            resultValueHolderV14.setMessage("订单出库流程异常：" + ex.getMessage());
            log.error(LogUtil.format("订单出库流程异常,异常信息:{}", "订单出库流程异常"), Throwables.getStackTraceAsString(ex));
        }
        return resultValueHolderV14;
    }
}