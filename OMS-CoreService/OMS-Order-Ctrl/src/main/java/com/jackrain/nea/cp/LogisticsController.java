package com.jackrain.nea.cp;

import com.jackrain.nea.cpext.model.LogisticsInfo;
import com.jackrain.nea.rpc.CpRpcService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: 易邵峰
 * @since: 2019-04-18
 * create at : 2019-04-18 16:15
 */
@RestController
@RequestMapping("/p")
public class LogisticsController {
    @Autowired
    private CpRpcService cpRpcService;

    /**
     * 查找区域信息
     *
     * @param code 物流公司编号
     * @return 区域信息
     */
    @RequestMapping("/selectLogistics")
    public LogisticsInfo selectLogistics(@RequestParam("code") String code) {
        return cpRpcService.selectLogisticsInfo(code);
    }

}
