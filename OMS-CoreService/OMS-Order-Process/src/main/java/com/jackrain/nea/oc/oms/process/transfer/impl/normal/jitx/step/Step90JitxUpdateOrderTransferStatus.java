package com.jackrain.nea.oc.oms.process.transfer.impl.normal.jitx.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpJitxOrderRelation;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 释放当前行，更新转换状态为2
 *
 * @author: 黄超
 * @since: 2019-06-25
 * create at : 2019-06-25 19:00
 */
@Step(order = 90, description = "释放当前行，更新转换状态为2")
@Component
@Slf4j
public class Step90JitxUpdateOrderTransferStatus extends BaseJitxOrderProcessStep
        implements IOmsOrderProcessStep<IpJitxOrderRelation> {

    @Override
    public ProcessStepResult<IpJitxOrderRelation> startProcess(IpJitxOrderRelation orderInfo,
                                                               ProcessStepResult preStepResult,
                                                               boolean isAutoMakeup, User operateUser) {
        this.ipJitxOrderService.updateJitxOrderTransStatus(orderInfo.getOrderNo(),
                TransferOrderStatus.TRANSFERRED,
                orderInfo.getRemarks() != null ? orderInfo.getRemarks() : "转单成功");
        return new ProcessStepResult<>(StepStatus.SUCCESS);
    }
}
