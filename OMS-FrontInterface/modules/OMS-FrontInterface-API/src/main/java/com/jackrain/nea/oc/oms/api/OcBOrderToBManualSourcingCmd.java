package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.oc.oms.model.request.OcBOrderToBManualSourcingBatchRequest;
import com.jackrain.nea.oc.oms.model.request.OcBOrderToBManualSourcingRequest;
import com.jackrain.nea.oc.oms.model.result.OcBOrderToBManualSourcingBatchResult;
import com.jackrain.nea.oc.oms.model.result.OcBOrderToBManualSourcingResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;

import java.util.List;

/**
 * <AUTHOR> lin yu
 * @date : 2022/8/4 下午1:29
 * @describe :
 */
public interface OcBOrderToBManualSourcingCmd {

    ValueHolderV14<OcBOrderToBManualSourcingResult> confirm(OcBOrderToBManualSourcingRequest request);

    ValueHolderV14<OcBOrderToBManualSourcingResult> dataQuery(OcBOrderToBManualSourcingRequest request);

    ValueHolderV14<List<OcBOrderToBManualSourcingBatchResult>> batchConfirm(OcBOrderToBManualSourcingBatchRequest request);

}
