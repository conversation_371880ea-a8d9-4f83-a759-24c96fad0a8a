package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jackrain.nea.oc.oms.api.MiniptOrderCmd;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.services.OcCancelChangingOrRefundService;
import com.jackrain.nea.oc.oms.services.OmsOrderWholeCancelService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @ClassName MiniptOrderCmdImpl
 * @Description
 * @Date 2022/8/25 下午4:41
 * @Created by wuhang
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class MiniptOrderCmdImpl implements MiniptOrderCmd {

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;

    @Autowired
    private OmsOrderWholeCancelService omsOrderWholeCancelService;

    @Autowired
    private OcCancelChangingOrRefundService orRefundService;


    @Override
    public ValueHolderV14 cancelOrderForward(String sourceCode, User user, String msg) {
        Long id = ocBOrderMapper.queryByTid(sourceCode);
        if(Objects.isNull(id) || id <= 0){
            ValueHolderV14 response = new ValueHolderV14();
            response.setCode(-1);
            response.setMessage("0获取订单失败:" + sourceCode);
            return response;
        }
        return omsOrderWholeCancelService.orderWholeCancelService(id,user,msg);
    }

    // 小平台的订单逆向取消暂时走 OcCancelChangingOrRefundCmd.qmRefundService
    @Override
    public ValueHolderV14 cancelOrderReverse(String tid, User user) {
        List<OcBReturnOrder> ocReturnOrders = ocBReturnOrderMapper.selectList(new LambdaQueryWrapper<OcBReturnOrder>()
                .eq(OcBReturnOrder::getTid, tid));
        if (CollectionUtils.isNotEmpty(ocReturnOrders)) {
            List<Long> ids = ocReturnOrders.stream().map(OcBReturnOrder::getId).collect(Collectors.toList());
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("ids", ids);
            return orRefundService.orRefundService(jsonObject, user, Boolean.FALSE);
        }else{
            ValueHolderV14 response = new ValueHolderV14();
            response.setCode(-1);
            response.setMessage("1获取订单失败:" + tid);
            return response;
        }
    }
}
