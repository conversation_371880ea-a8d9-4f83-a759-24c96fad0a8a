package com.jackrain.nea.oc.oms.process.transfer.impl.exchange.standplat.step;

import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.services.*;
import com.jackrain.nea.oc.oms.services.refund.OmsReturnUtil;
import com.jackrain.nea.oc.oms.util.StandplatExchangeOrderTransferUtil;
import com.jackrain.nea.oc.oms.util.StandplatRefundOrderTransferUtil;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.StRpcService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Author: 黄世新
 * @Date: 2020/11/30 8:51 下午
 * @Version 1.0
 */
public abstract class BaseStandplatExchangeProcessStep {

    @Autowired
    protected IpStandplatRefundService ipStandplatRefundService;
    @Autowired
    protected OmsStandplatExchangeService standplatExchangeService;
    @Autowired
    protected StandplatExchangeOrderTransferUtil standplatExchangeOrderTransferUtil;
    @Autowired
    protected OmsReturnOrderService omsReturnOrderService;

    @Autowired
    protected OmsRefundOrderService omsRefundOrderService;

    @Autowired
    protected CpRpcService cpRpcService;

    @Autowired
    protected OcBOrderMapper ocBOrderMapper;
    @Autowired
    protected OcBOrderItemMapper ocBOrderItemMapper;

    @Autowired
    protected OmsStandPlatRefundOrderService omsStandPlatRefundOrderService;

}
