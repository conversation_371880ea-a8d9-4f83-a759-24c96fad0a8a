package com.jackrain.nea.oc.oms.matcher.live.impl;

import com.jackrain.nea.oc.oms.matcher.live.ILiveMatcher;
import com.jackrain.nea.oc.oms.matcher.live.LiveStrategyTypeEnum;
import com.jackrain.nea.oc.oms.matcher.vo.ParamInputVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * @program: r3-oc-oms
 * @description: 依据SPU解析匹配
 * @author: liuwj
 * @create: 2021-06-04 16:04
 **/
@Component
@Slf4j
public class LiveMatcherBySpu implements ILiveMatcher {

    @Override
    public boolean doMatch(ParamInputVO inputVO) {
        String ruleContext = inputVO.getRuleContext();
        String targetText = inputVO.getOriginalSpu();
        if (Objects.nonNull(ruleContext) && Objects.nonNull(targetText)) {
            return targetText.indexOf(ruleContext) >= 0;
        }
        return false;
    }

    @Override
    public Integer getLiveStrategyType()  {
        return LiveStrategyTypeEnum.SPU.getValue();
    }
}
