package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.model.request.OrderICheckRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

/**
 * 订单反审核
 *
 * @date 2019/3/12
 * @author: ming.fz
 */
public interface OcBOrderTheAuditCmd {

    ValueHolderV14 orderTheAudit(OrderICheckRequest param, User user) throws NDSException;
}
