package com.jackrain.nea.oc.oms.es;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.AlibabaAscpOrderExt;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/11 1:26 下午
 * @description
 */
public class ES4IpAlibabaAscpCancelOrder {


    /**
     * 猫超直发取消订单补偿服务
     * 根据订单转换状态查询履约单号
     *
     * @param pageIndex 页码
     * @param pageSize  每页大小
     *  TRANS 转换状态
     *  BIZ_ORDER_CODE 履约单号
     */
    public static JSONObject findBizOrderCodeByTrans(int pageIndex, int pageSize) {
        int startIndex = pageIndex * pageSize;
        if (startIndex < 0) {
            startIndex = 0;
        }
        JSONObject orderKes = new JSONObject();
        orderKes.put("name", "TRANSDATE");
        orderKes.put("asc", true);
        JSONArray orderKeys = new JSONArray();
        orderKeys.add(orderKes);

        JSONObject whereKeys = new JSONObject();
        String[] returnFileds = {"BIZ_ORDER_CODE"};
        whereKeys.put("ISTRANS", TransferOrderStatus.NOT_TRANSFER.toInteger());
        JSONObject search = ElasticSearchUtil.search(AlibabaAscpOrderExt.TABLE_NAME_IP_B_ALIBABA_ASCP_ORDER_CANCEL,
                AlibabaAscpOrderExt.TABLE_NAME_IP_B_ALIBABA_ASCP_ORDER_CANCEL, whereKeys, null,
                orderKeys, pageSize, startIndex, returnFileds);
        return search;
    }
}
