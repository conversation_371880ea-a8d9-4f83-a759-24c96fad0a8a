package com.jackrain.nea.oc.services;

import com.jackrain.nea.oc.oms.api.ModifyOrderIsWriteoffCmd;
import com.jackrain.nea.oc.oms.services.ModifyOrderIsWriteoffService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: 李杰
 * @since: 2019/4/22
 * create at : 2019/4/22 9:10
 */
@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class ModifyOrderIsWriteoffCmdImpl implements ModifyOrderIsWriteoffCmd {
    @Autowired
    private ModifyOrderIsWriteoffService modifyOrderIsWriteoffService;

    @Override
    public ValueHolderV14 updateIsWriteoff(List<Long> ids) {
        return modifyOrderIsWriteoffService.updateIsWriteoff(ids);
    }
}
