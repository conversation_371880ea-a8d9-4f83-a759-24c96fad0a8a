package com.jackrain.nea.oc.services.directreport;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.base.Throwables;
import com.jackrain.nea.ac.utils.ValueHolderUtils;
import com.jackrain.nea.annotation.OmsOperationLog;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.directreport.OcBDirectReportOrderSubmitCmd;
import com.jackrain.nea.oc.oms.services.directreport.OcBDirectReportOrderSubmitService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 直发预占-审核
 *
 * <AUTHOR>
 * @since 2024-11-28 15:47
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBDirectReportOrderSubmitCmdImpl extends CommandAdapter implements OcBDirectReportOrderSubmitCmd {
    @Resource
    private OcBDirectReportOrderSubmitService ocBDirectReportOrderSubmitService;

    @Override
    @OmsOperationLog(operationType = "AUDIT", mainTableName = "OC_B_DIRECT_REPORT_ORDER", itemsTableName = "OC_B_DIRECT_REPORT_ORDER_ITEM")
    public ValueHolder execute(QuerySession session) throws NDSException {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        log.info(LogUtil.format("直发预占审核-参数：{}",
                "OcBDirectReportOrderSubmitCmdImpl.execute"), JSON.toJSONString(param));

        Long objId = param.getLong("objid");
        try {
            ocBDirectReportOrderSubmitService.submit(objId, session.getUser());
        } catch (NDSException e) {
            log.error(LogUtil.format("直发预占审核-异常：{}",
                    "OcBDirectReportOrderSubmitCmdImpl.execute"), Throwables.getStackTraceAsString(e));
            return ValueHolderUtils.getFailValueHolder(e.getMessage());
        }

        return ValueHolderUtils.getSuccessValueHolder("审核成功");
    }
    
}
