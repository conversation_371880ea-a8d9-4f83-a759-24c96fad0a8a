package com.jackrain.nea.oc.oms.services.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.parser.Feature;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jackrain.nea.cpext.utils.ValueHolderUtils;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.task.OcBRefundInTaskLogMapper;
import com.jackrain.nea.oc.oms.mapper.task.OcBRefundInTaskMapper;
import com.jackrain.nea.oc.oms.model.enums.OcBRefundInTaskStatusEnum;
import com.jackrain.nea.oc.oms.model.table.task.OcBRefundInTask;
import com.jackrain.nea.oc.oms.model.table.task.OcBRefundInTaskLog;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Field;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020/12/10 17:32
 * @desc
 */
@Service
@Slf4j
public class OcBRefundInTaskService extends CommandAdapter {

    private final String objId = "objid";
    private final String before = "beforevalue";
    private final String after = "aftervalue";
    private final String table = "OC_B_REFUND_IN_TASK";
    private final String OC_B_REFUND_IN_TASK_LOG = "oc_b_refund_in_task_log";
    //  redis 前缀
    public final static String REDIS_KEY_PREFIX = "refund_in_make_up:";

    @Autowired
    private OcBRefundInTaskMapper refundInTaskMapper;

    @Autowired
    private OcBRefundInTaskLogMapper logMapper;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);
        if (Objects.isNull(param)) {
            return ValueHolderUtils.fail("参数异常!");
        }
        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        if (Objects.isNull(fixColumn)) {
            return ValueHolderUtils.fail("参数异常,fixColumn为空!");
        }
        JSONObject jsonObject = fixColumn.getJSONObject(table);
        if (MapUtils.isEmpty(jsonObject)) {
            return ValueHolderUtils.fail("参数异常,ocBRefundInTask为空!");
        }
        OcBRefundInTask ocBRefundInTask = JSONObject.parseObject(jsonObject.toJSONString(), OcBRefundInTask.class);
        if (Objects.isNull(ocBRefundInTask)) {
            return ValueHolderUtils.fail("参数异常,fixColumn转换异常!");
        }
        Long id = param.getLong(objId);
        // 不允许也不可能页面新增
        if (Objects.isNull(id) || id <= 0) {
            return ValueHolderUtils.fail("id异常");
        }
        OcBRefundInTask old = refundInTaskMapper.selectById(id);
        if (Objects.equals(old.getBillStatus(), OcBRefundInTaskStatusEnum.SUCCESS.getVal())) {
            return ValueHolderUtils.fail("状态为已回传成功,不允许修改!");
        }
        ocBRefundInTask.setId(id);
        return this.getInstance().save(param, ocBRefundInTask, session.getUser());
    }

    /**
     * 保存
     *
     * @param param 入参
     * @param task  转换后的task信息
     * @return
     */
    @Transactional(rollbackFor = Throwable.class)
    public ValueHolder save(JSONObject param, OcBRefundInTask task, User user) {
        refundInTaskMapper.updateById(task);
        // 修改之前
        JSONObject before = param.getJSONObject(this.before).getJSONObject(table);
        // 修改之后
        JSONObject after = param.getJSONObject(this.after).getJSONObject(table);
        // size
        int size = after.size();
        // 校验之前之后的size .为空就报错
        if (MapUtils.isEmpty(before) || MapUtils.isEmpty(after) || before.size() != size) {
            return ValueHolderUtils.fail("操作前后,数据异常!");
        }
        // 可以从redis里面获取配置
        String fieldStr = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get("business_system:" + REDIS_KEY_PREFIX + "log_field");
        JSONObject fields = null;
        try {
            fields = JSONObject.parseObject(fieldStr);
        } catch (Exception e) {
            log.error(LogUtil.format("redis业务系统参数(refund_in_make_up:log_field)配置异常:{}"), Throwables.getStackTraceAsString(e));
            return ValueHolderUtils.fail("redis参数配置异常!");
        }
        boolean isChooseRedis = MapUtils.isNotEmpty(fields);
        List<OcBRefundInTaskLog> logs = Lists.newArrayListWithExpectedSize(size);
        for (Map.Entry<String, Object> entry : after.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            OcBRefundInTaskLog taskLog = new OcBRefundInTaskLog();
            if (isChooseRedis) {
                if (fields.containsKey(key)) {
                    taskLog.setModcontent(fields.getString(key));
                    taskLog.setBeforeModification(String.valueOf(before.get(key)));
                    taskLog.setAfterModification(String.valueOf(value));
                }
            } else {
                Map<String, String> fieldInfo = getFieldInfo(OcBRefundInTask.class);
                taskLog.setBeforeModification(String.valueOf(before.get(key)));
                taskLog.setAfterModification(String.valueOf(value));
                taskLog.setModcontent(fieldInfo.get(key));
            }
            taskLog.setId(ModelUtil.getSequence(OC_B_REFUND_IN_TASK_LOG));
            taskLog.setRefundTaskId(task.getId());
            BaseModelUtil.initialBaseModelSystemField(taskLog, user);
            logs.add(taskLog);
        }
        if (!logs.isEmpty()) {
            logMapper.batchInsert(logs);
        }
        return ValueHolderUtils.success();
    }


    /**
     * 获取 字意 和 字名
     *
     * @param clazz
     * @return
     */
    public static Map<String, String> getFieldInfo(Class<?> clazz) {
        Field[] fields = clazz.getDeclaredFields();
        Map<String, String> result = Maps.newHashMapWithExpectedSize(fields.length);
        for (Field field : fields) {
            ApiModelProperty annotation = field.getAnnotation(ApiModelProperty.class);
            JSONField jsonField = field.getAnnotation(JSONField.class);
            if (annotation != null) {
                result.put(jsonField.name(), annotation.value());
            }
        }
        return result;
    }

    public OcBRefundInTaskService getInstance() {
        return ApplicationContextHandle.getBean(this.getClass());
    }
}
