package com.jackrain.nea.oc.oms.nums;

import lombok.Getter;

/**
 * @ClassName KuaiDi100StateEnum
 * @Description 快递100枚举
 * <AUTHOR>
 * @Date 2024/4/23 10:22
 * @Version 1.0
 */
public enum KuaiDi100StateEnum {

    LAN_SHOU("1", "揽收"),
    ZAI_TU("0", "在途"),
    PAI_JIAN("5", "派件"),
    QIAN_SHOU("3", "签收"),
    TUI_HUI("6", "退回"),
    TUI_QIAN("4", "退签"),
    ZHUAN_TOU("7", "转投"),
    YI_NAN("2", "疑难"),
    QING_GUAN("8", "清关"),
    JU_QIAN("14", "拒签"),

    ;
    @Getter
    private final String key;
    @Getter
    private final String desc;

    KuaiDi100StateEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }
}
