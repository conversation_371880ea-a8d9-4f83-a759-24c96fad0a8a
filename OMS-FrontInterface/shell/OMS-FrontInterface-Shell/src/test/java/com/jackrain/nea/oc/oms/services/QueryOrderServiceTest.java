package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.OMSFrontInterfaceMainApplication;
import com.jackrain.nea.oc.request.OcBOrderRequest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @create 2020-12-30 14:07
 * @desc 订单查询
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = OMSFrontInterfaceMainApplication.class)
@Slf4j
public class QueryOrderServiceTest {

    @Autowired
    private QueryOrderService queryOrderService;

    @Test
    public void queryOrderList() {
        OcBOrderRequest ocBOrderRequest = new OcBOrderRequest();
        ocBOrderRequest.setSourceCode("E20200802162231060100113");
        queryOrderService.queryOrderList(ocBOrderRequest);
    }

}