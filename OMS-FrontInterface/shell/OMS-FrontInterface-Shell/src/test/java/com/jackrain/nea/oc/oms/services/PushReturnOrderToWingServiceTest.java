package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.OMSFrontInterfaceMainApplication;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.rpc.IpRpcService;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @create 2020-12-28
 * @desc
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = OMSFrontInterfaceMainApplication.class)
@Slf4j
public class PushReturnOrderToWingServiceTest {

    @Autowired
    private IpRpcService ipRpcService;

    @Autowired
    private OcBReturnOrderMapper returnOrderMapper;

    @Test
    public void test() {
        OcBReturnOrder returnOrder = returnOrderMapper.selectByid(25L);
        //ipRpcService.pushReturnOrderToWms(Lists.newArrayList(returnOrder));
    }

}
