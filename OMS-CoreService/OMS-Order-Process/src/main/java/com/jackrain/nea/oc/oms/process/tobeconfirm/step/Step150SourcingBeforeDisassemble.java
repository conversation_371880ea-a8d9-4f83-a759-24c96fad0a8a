package com.jackrain.nea.oc.oms.process.tobeconfirm.step;

import cn.hutool.core.util.ObjectUtil;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.gsi.GSI4Order;
import com.jackrain.nea.oc.oms.mapper.OcBOccupyTaskMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.StCHoldOrderReasonMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderBusinessTypeCodeEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderParam;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.resources.OrderOccupyStatus;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.StCHoldOrderReason;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.process.tobeconfirm.util.TobeConfirmedUtil;
import com.jackrain.nea.oc.oms.services.BusinessSystemParamService;
import com.jackrain.nea.oc.oms.services.OmsOccupyTaskService;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.services.SplitBeforeSourcingStService;
import com.jackrain.nea.oc.oms.services.advance.AdvanceConstant;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.oc.oms.util.SplitMessageUtil;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.table.StCCustomLabelDO;
import com.jackrain.nea.util.OmsBusinessTypeUtil;
import com.jackrain.nea.util.OmsOrderUtil;
import com.jackrain.nea.util.SendToBOrderMessageUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import static com.jackrain.nea.oc.oms.services.returnin.OcReturnInCommService.B2B_PLAT_TAG;
import static com.jackrain.nea.oc.oms.services.returnin.OcReturnInCommService.B2B_PLAT_TAG2;

/**
 * 寻源前拆单逻辑
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/08/02
 */
@Step(order = 150, description = "寻源前拆单逻辑")
@Slf4j
public class Step150SourcingBeforeDisassemble extends BaseTobeConfirmedProcessStep implements IOmsOrderProcessStep<OcBOrderRelation> {

    @Autowired
    private SplitBeforeSourcingStService splitBeforeSourcingStService;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private PsRpcService psRpcService;
    @Autowired
    private StCHoldOrderReasonMapper stCHoldOrderReasonMapper;
    @Autowired
    private OcBOccupyTaskMapper ocBOccupyTaskMapper;
    @Autowired
    private OmsOccupyTaskService omsOccupyTaskService;
    @Autowired
    private SendToBOrderMessageUtil sendToBOrderMessageUtil;
    @Autowired
    private StRpcService stRpcService;
    @Autowired
    private BusinessSystemParamService businessSystemParamService;


    @Override
    public ProcessStepResult<OcBOrderRelation> startProcess(OcBOrderRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        OcBOrder ocBOrder = orderInfo.getOrderInfo();
        String sourceCode = ocBOrder.getSourceCode();
        List<Long> cardByGiftNotSplit = orderInfo.getCardByGiftNotSplit();
        List<Long> cardByGiftRelation = orderInfo.getCardByGiftRelation();
        String lockRedisKey = "oc:oms:order:tid:" + sourceCode;
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisLock.tryLock(0, TimeUnit.MILLISECONDS)) {
                //上锁
                //二级索引查询
                List<Long> esIdList = GSI4Order.getIdListBySourceCode(sourceCode);
                Set<Long> longs = new HashSet<>(esIdList);
                esIdList = new ArrayList<>(longs);
                List<OcBOrder> ocBOrderList = ocBOrderMapper.selectByIdsListByStatus50AndOccupy170(esIdList);

                ProcessStepResult result = new ProcessStepResult<>(StepStatus.FINISHED);
                log.info(LogUtil.format("Step150SourcingBeforeDisassemble.startProcess.ocBOrder.tid={}",
                        "Step150SourcingBeforeDisassemble.startProcess", sourceCode));
                //这个地方订单不可能为空
                for (OcBOrder order : ocBOrderList) {
                    // 如果是周期购订单，执行生成周期购子订单
                    if (TobeConfirmedUtil.checkIsCyclePurchaseOrder(order)) {
                        result = new ProcessStepResult<>(StepStatus.SUCCESS);
                        continue;
                    }

                    // 如果是旺店通订单 并且是平台推送过来的订单 则插入操作日志 并且结束
                    if (OmsOrderUtil.wdtPlatformSend(order)) {
                        // 增加操作日志
                        omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(),
                                OrderLogTypeEnum.STRATEGY_SPLIT.getKey(), "旺店通平台下推的订单 不执行寻源前拆单策略", null, null, operateUser);
                        OcBOrder updateOrder = new OcBOrder();
                        updateOrder.setId(order.getId());
                        updateOrder.setSysremark("");
                        updateOrder.setOrderStatus(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger());
                        updateOrder.setModifieddate(new Date());
                        ocBOrderMapper.updateById(updateOrder);
                        if (order.getIsDetention() != null && order.getIsDetention() == 1) {
                            Date detentionReleaseDate = order.getDetentionReleaseDate();
                            if (detentionReleaseDate != null) {
                                omsOccupyTaskService.addOcBOccupyTask(order, detentionReleaseDate);
                            }
                        } else {
                            omsOccupyTaskService.addOcBOccupyTask(order, null);
                        }
                        result = new ProcessStepResult<>(StepStatus.FINISHED);
                        continue;
                    }
                    //来源平台=DMS&SAP的订单的toB订单时（RYCK16、RYCK17、RYCK18）发送钉钉消息
                    List<OcBOrderItem> orderItems = ocBOrderItemMapper.selectOrderItemListOccupy(order.getId());
                    boolean isB2b = StringUtils.equalsIgnoreCase(B2B_PLAT_TAG, ocBOrder.getGwSourceGroup()) ||
                            StringUtils.equalsIgnoreCase(B2B_PLAT_TAG2, ocBOrder.getGwSourceGroup());
                    boolean isTobBusiness = OrderBusinessTypeCodeEnum.SAP_CONSIGN_SALE.getCode().equals(ocBOrder.getBusinessTypeCode()) ||
                            OrderBusinessTypeCodeEnum.SAP_STANDARD_SALE.getCode().equals(ocBOrder.getBusinessTypeCode()) ||
                            OrderBusinessTypeCodeEnum.SAP_RAW_MATERIAL_SALE.getCode().equals(ocBOrder.getBusinessTypeCode());
                    //来源平台=DMS&SAP&&业务类型为RYCK16、RYCK17、RYCK18才发送
                    if (isB2b && isTobBusiness) {
                        sendToBOrderMessageUtil.sendMsgByPlaceOrder(order, orderItems);
                    }
                    // 判断当前订单是不是已经卡单了 如果没卡单 则走下面的逻辑
                    // tob 订单不走这一块
                    if (toCOrderCard(operateUser, ocBOrder, order, orderItems)) continue;

                    OcBOrderParam param = new OcBOrderParam();
                    param.setOcBOrder(order);
                    param.setOrderItemList(orderItems);
                    splitBeforeSourcingStService.splitBeforeSourcingStrategy(param, operateUser, cardByGiftNotSplit, cardByGiftRelation, true, true);
                }
                return result;
            } else {
                omsToBeConfirmedTaskService.updateToBeConfirmedTaskByOrderId(orderInfo.getOrderId());
                return new ProcessStepResult<>(StepStatus.FINISHED, "策略拆单,tid加锁失败");
            }
        } catch (Exception e) {
            log.error(LogUtil.format("策略拆单失败:{}", ocBOrder.getId(), "策略拆单失败"), Throwables.getStackTraceAsString(e));
            OcBOrder errorOrderInfo = new OcBOrder();
            errorOrderInfo.setId(orderInfo.getOrderId());
            errorOrderInfo.setSysremark(SplitMessageUtil.splitMesssage("策略拆单失败"));
            errorOrderInfo.setOccupyStatus(OrderOccupyStatus.STATUS_170);
            omsOrderService.updateOrderInfo(errorOrderInfo);
            omsToBeConfirmedTaskService.updateToBeConfirmedTaskByOrderId(orderInfo.getOrderId());
            return new ProcessStepResult<>(StepStatus.FINISHED, "策略拆单失败");
        } finally {
            redisLock.unlock();
        }
    }

    private boolean toCOrderCard(User operateUser, OcBOrder ocBOrder, OcBOrder order, List<OcBOrderItem> orderItems) {
        boolean toBOrder = OmsBusinessTypeUtil.isToBOrder(order);
        if (!toBOrder) {
            if (ObjectUtil.isNull(order.getIsDetention()) || ObjectUtil.equal(0, order.getIsDetention())) {
                BigDecimal totalStandWeight = BigDecimal.ZERO;
                BigDecimal totalQty = BigDecimal.ZERO;
                boolean noBeforeSourcingSplit = false;
                // 卡单原因
                String detentionReason = "";
                // 校验订单的明细是不是包含了100件衍生品 则整单卡单并且跳过寻源前拆单
                for (OcBOrderItem ocBOrderItem : orderItems) {
                    if (ObjectUtil.isNotNull(ocBOrderItem.getProType()) && ObjectUtil.equal(4L, ocBOrderItem.getProType())) {
                        continue;
                    }
                    // 判断明细是否包含了衍生品
                    ProductSku productSku = psRpcService.selectProductSku(ocBOrderItem.getPsCSkuEcode());
                    // 判断商品的物料类型
                    if (productSku != null && productSku.getProAttributeMap() != null && productSku.getProAttributeMap().get("M_DIM4_ID") != null) {
                        String ename = productSku.getProAttributeMap().get("M_DIM4_ID").getEname();
                        // 判断编码是不是 90200，或=Z006的
                        if (StringUtils.isNotEmpty(ename) && (ObjectUtil.equal(ename, "衍生品-入仓") || ObjectUtil.equal(ename, "衍生品-代发"))) {
                            // 说明是衍生品
                            totalQty = totalQty.add(ocBOrderItem.getQty());
                        }
                    }
                    // 增加重量
                    totalStandWeight = totalStandWeight.add(ocBOrderItem.getStandardWeight().multiply(ocBOrderItem.getQty()));
                }

                if (OrderBusinessTypeCodeEnum.SAP_FREE.getCode().equals(order.getBusinessTypeCode())
                        && StringUtils.equalsIgnoreCase("39", order.getGwSourceGroup())) {
                    StCCustomLabelDO stCCustomLabelDO = stRpcService.queryByEname("toB免费订单");
                    if (stCCustomLabelDO != null) {
                        OcBOrder updateModel = new OcBOrder();
                        updateModel.setId(order.getId());
                        updateModel.setModifieddate(new Date());
                        updateModel.setStCCustomLabelId(stCCustomLabelDO.getId().toString());
                        updateModel.setStCCustomLabelEname(stCCustomLabelDO.getEname());
                        ocBOrderMapper.updateById(updateModel);
                    }
                }

                if (totalQty.compareTo(new BigDecimal(100)) >= 0) {
                    detentionReason = "衍生品超过100件";
                    noBeforeSourcingSplit = true;
                }
                if (totalStandWeight.compareTo(new BigDecimal(500)) >= 0) {
                    detentionReason = "订单重量超过500KG";
                    noBeforeSourcingSplit = true;
                }
                if (noBeforeSourcingSplit) {
                    OcBOrder updateOcBOrder = new OcBOrder();
                    List<StCHoldOrderReason> stCHoldOrderReasonList = stCHoldOrderReasonMapper.selectByTypeAndReason(1, detentionReason);
                    if (CollectionUtils.isNotEmpty(stCHoldOrderReasonList)) {
                        StCHoldOrderReason stCHoldOrderReason = stCHoldOrderReasonList.get(0);
                        updateOcBOrder.setDetentionReason(stCHoldOrderReason.getReason());
                        updateOcBOrder.setDetentionReasonId(Math.toIntExact(stCHoldOrderReason.getId()));
                    } else {
                        updateOcBOrder.setDetentionReason(detentionReason);
                    }
                    updateOcBOrder.setId(order.getId());
                    updateOcBOrder.setIsDetention(AdvanceConstant.DETENTION_STATUS_1);
                    updateOcBOrder.setSysremark(detentionReason);
                    updateOcBOrder.setOrderStatus(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger());
                    updateOcBOrder.setModifieddate(new Date());
                    ocBOrderMapper.updateById(updateOcBOrder);
                    omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.ADVANCE_DETENTION.getKey(), detentionReason, "", null, operateUser);
                    ocBOccupyTaskMapper.deleteOcBOccupyTaskByOrderId(order.getId());
                    return true;
                } else {
                    OcBOrder updateModel = new OcBOrder();
                    updateModel.setId(order.getId());
                    updateModel.setModifieddate(new Date());
                    if (OrderBusinessTypeCodeEnum.SAP_FREE.getCode().equals(order.getBusinessTypeCode())
                            && StringUtils.equalsIgnoreCase("39", order.getGwSourceGroup())) {
                        // 获取业务系统参数里面的信息
                        List<String> dmsFreeOrder = businessSystemParamService.getDmsFreeOrder();
                        boolean cardOrder = false;
                        // 获取orderItems中的sku编码
                        for (OcBOrderItem ocBOrderItem : orderItems) {
                            String skuEcode = ocBOrderItem.getPsCSkuEcode();
                            if (dmsFreeOrder.contains(skuEcode)) {
                                cardOrder = true;
                            }
                        }
                        if (cardOrder) {
                            detentionReason = "DMS免费订单衍生品卡单";
                            List<StCHoldOrderReason> stCHoldOrderReasonList = stCHoldOrderReasonMapper.selectByTypeAndReason(1, "DMS免费订单衍生品卡单");
                            if (CollectionUtils.isNotEmpty(stCHoldOrderReasonList)) {
                                StCHoldOrderReason stCHoldOrderReason = stCHoldOrderReasonList.get(0);
                                updateModel.setDetentionReason(stCHoldOrderReason.getReason());
                                updateModel.setDetentionReasonId(Math.toIntExact(stCHoldOrderReason.getId()));
                            } else {
                                updateModel.setDetentionReason(detentionReason);
                            }
                            updateModel.setId(order.getId());
                            updateModel.setIsDetention(AdvanceConstant.DETENTION_STATUS_1);
                            updateModel.setSysremark(detentionReason);
                            updateModel.setOrderStatus(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger());
                            updateModel.setModifieddate(new Date());
                            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.ADVANCE_DETENTION.getKey(), detentionReason, "", null, operateUser);
                            ocBOccupyTaskMapper.deleteOcBOccupyTaskByOrderId(order.getId());
                            ocBOrderMapper.updateById(updateModel);
                            return true;
                        }
                        ocBOrderMapper.updateById(updateModel);
                    }
                }
            }
        }
        return false;
    }


}
