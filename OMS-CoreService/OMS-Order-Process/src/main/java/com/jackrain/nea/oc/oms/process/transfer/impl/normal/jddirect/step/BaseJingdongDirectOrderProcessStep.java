package com.jackrain.nea.oc.oms.process.transfer.impl.normal.jddirect.step;

import com.jackrain.nea.oc.oms.services.IpJingdongDirectService;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.util.JdDirectOrderTransferUtil;
import com.jackrain.nea.rpc.PsRpcService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Author: 黄世新
 * @Date: 2022/3/28 下午3:16
 * @Version 1.0
 */

public class BaseJingdongDirectOrderProcessStep {
    //0无退款
    protected static final Integer REFUND_0 = 0;
    //1售前退款
    protected static final Integer REFUND_1 = 1;
    //2售后退款
    protected static final Integer REFUND_2 = 2;

    @Autowired
    protected IpJingdongDirectService ipJingdongDirectService;
    @Autowired
    protected OmsOrderService omsOrderService;
    @Autowired
    protected PsRpcService psRpcService;
    @Autowired
    protected JdDirectOrderTransferUtil jdDirectOrderTransferUtil;

}
