package com.jackrain.nea.oc.oms.services.advance;

import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.ps.model.SkuType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.security.Key;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @program: r3-oc-oms
 * @description: 拆单工具类
 * @author: liuwj
 * @create: 2021-07-08 14:14
 **/
@Slf4j
@Component
public class OmsOrderSpiltUtill {
    /**
     * <AUTHOR>
     * @Date 14:28 2021/7/8
     * @Description 获取所有赠品（包含无挂靠）
     */
    public List<OcBOrderItem>  getGiftList(List<OcBOrderItem> ocBOrderItemList){
        return ocBOrderItemList.stream().filter(v -> Optional.ofNullable(v.getIsGift()).orElse(0).intValue() == 1).collect(Collectors.toList());
    }

    /**
     * <AUTHOR>
     * @Date 14:28 2021/7/8
     * @Description 获取所有无挂靠的赠品
     */
    public List<OcBOrderItem>  getNoGiftRelationList(List<OcBOrderItem> ocBOrderItemList){
        return ocBOrderItemList.stream().filter(v -> Optional.ofNullable(v.getIsGift()).orElse(0).intValue() == 1 && StringUtils.isEmpty(v.getGiftRelation())).collect(Collectors.toList());
    }

    /**
     * <AUTHOR>
     * @Date 17:22 2021/8/6
     * @Description 有挂靠的赠品 找不到挂的人 当做无挂靠的做
     */
    public List<OcBOrderItem>  getNotFondRelationnList (List<OcBOrderItem> ocBOrderItemList){
        List<OcBOrderItem> notFondRelationnList = new ArrayList<>();
        List<OcBOrderItem> giftRelationList =ocBOrderItemList.stream().filter(v -> Optional.ofNullable(v.getIsGift()).orElse(0).intValue() == 1 && StringUtils.isNotEmpty(v.getGiftRelation())).collect(Collectors.toList());
        Map<String, List<OcBOrderItem>> map=getGiftListMap(ocBOrderItemList);
        if (CollectionUtils.isEmpty(giftRelationList) || map ==null){
            return null;
        }
        for (OcBOrderItem ocBOrderItem : giftRelationList) {
            if (CollectionUtils.isEmpty(map.get(getPsCSkuEcodeKey(ocBOrderItem)) )){
                notFondRelationnList.add(ocBOrderItem);
            }
        }
        return  notFondRelationnList;
    }

    /**
     * <AUTHOR>
     * @Date 14:43 2021/7/13
     * @Description  获取明细包含组合商品虚拟条码 (不含赠品和组合商品真实条码)
     */
    public List<OcBOrderItem> getOcBOrderItem(List<OcBOrderItem> ocBOrderItemList){
        return ocBOrderItemList.stream().filter(v -> checkFilteItem(v)).collect(Collectors.toList());
    }
    /**
     * <AUTHOR>
     * @Date 21:40 2021/7/13
     * @Description  根据组合商品的虚拟sku真实条码
     */
    public List<OcBOrderItem> getisGroupBySku(List<OcBOrderItem> ocBOrderItemList,String sku){
        Map<String,List<OcBOrderItem>> listMap= getIsGroupItemMap(ocBOrderItemList);
        if (listMap != null ){
            return listMap.get(sku);
        }
        return null;
    }

    /**
     * <AUTHOR>
     * @Date 21:40 2021/7/13
     * @Description  根据挂靠关系查询挂靠赠品
     */
    public List<OcBOrderItem> getGiftRelationList(List<OcBOrderItem> ocBOrderItemList,String giftRelation){
        Map<String,List<OcBOrderItem>> listMap= getGiftListMap(ocBOrderItemList);
        if (listMap != null ){
            return listMap.get(giftRelation);
        }
        return null;
    }

    /**
     * <AUTHOR>
     * @Date 21:58 2021/7/13
     * @Description 所有挂靠赠品map
     */
    public Map<String, List<OcBOrderItem>> getGiftListMap(List<OcBOrderItem> ocBOrderItemList) {
        List<OcBOrderItem> orderItemList= ocBOrderItemList.stream().filter(v -> Optional.ofNullable(v.getIsGift()).orElse(0).intValue() == 1 && StringUtils.isNotEmpty(v.getGiftRelation())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(orderItemList) ){
            return orderItemList.stream().collect(Collectors.groupingBy(i-> getRelationKey(i)));
        }
        return null;
    }
    /**
     * <AUTHOR>
     * @Date 22:56 2021/8/5
     * @Description
     */
    public String getRelationKey(OcBOrderItem ocBOrderItem) {
        StringBuilder sb = new StringBuilder();
        if (ocBOrderItem.getTid() !=null){
            sb.append(ocBOrderItem.getTid()+"_");
        }
        if (ocBOrderItem.getOoid() !=null){
            sb.append(ocBOrderItem.getOoid()+"_");
        }
        if (ocBOrderItem.getGiftRelation() !=null){
            sb.append(ocBOrderItem.getGiftRelation());
        }
        return sb.toString();
    }


    /**
     * <AUTHOR>
     * @Date 22:56 2021/8/5
     * @Description
     */
    public String getPsCSkuEcodeKey(OcBOrderItem ocBOrderItem) {
        StringBuilder sb = new StringBuilder();
        if (ocBOrderItem.getTid() !=null){
            sb.append(ocBOrderItem.getTid()+"_");
        }
        if (ocBOrderItem.getOoid() !=null){
            sb.append(ocBOrderItem.getOoid()+"_");
        }
        if (ocBOrderItem.getPsCSkuEcode() !=null){
            sb.append(ocBOrderItem.getPsCSkuEcode());
        }
        return sb.toString();
    }
    /**
     * <AUTHOR>
     * @Date 21:40 2021/7/13
     * @Description  组合商品真实明细集合
     */
    public Map<String,List<OcBOrderItem>> getIsGroupItemMap(List<OcBOrderItem> ocBOrderItemList){
        List<OcBOrderItem> ocBOrderItemList1=ocBOrderItemList.stream().filter(v ->  v.getProType() != null && v.getProType() == SkuType.COMBINE_PRODUCT).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(ocBOrderItemList1)){
            return ocBOrderItemList1.stream().collect(Collectors.groupingBy(i->i.getGiftbagSku()));
        }
        return null;
    }
    /**
     * <AUTHOR>
     * @Date 21:39 2021/7/13
     * @Description 过滤掉赠品和组合商品（只留虚拟条码）
     */
    public boolean checkFilteItem(OcBOrderItem v) {
        boolean isGift = Optional.ofNullable(v.getIsGift()).orElse(0).intValue() == 1;
        boolean isCombine = v.getProType() != null && v.getProType() == SkuType.COMBINE_PRODUCT;
       return !isGift && !isCombine;
    }

}
