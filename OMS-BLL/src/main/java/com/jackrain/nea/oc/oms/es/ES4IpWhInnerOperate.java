package com.jackrain.nea.oc.oms.es;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 仓库仓内操作中间表
 *
 * <AUTHOR>
 * @date 2020/11/11 11:19 上午
 */
public class ES4IpWhInnerOperate {

    private ES4IpWhInnerOperate() {
    }

    /**
     * 业务：WMS回传包裹信息，定时任务读取包裹信息
     *
     * 根据转换状态去查询运单号
     *
     * @param pageSize 页面数据大小
     * @return List express_code 运单号
     */
    public static List<String> findExpressCodesByTransStatus(Integer pageSize) {
        List<String> expressCodes = null;
        JSONObject whereKey = new JSONObject();
        whereKey.put("TRANS_STATUS", TransferOrderStatus.NOT_TRANSFER.toInteger());
        JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.IP_B_WH_INNER_OPERATE,
                OcElasticSearchIndexResources.IP_B_WH_INNER_OPERATE, whereKey,
                null, null, pageSize, 0, new String[]{"EXPRESS_CODE"});
        JSONArray data = search.getJSONArray("data");
        if (CollectionUtils.isNotEmpty(data)) {
            expressCodes = data.stream()
                    .map(a -> ((JSONObject) a).getString("EXPRESS_CODE")).collect(Collectors.toList());
        }
        return expressCodes;
    }

    /**
     * 业务：按钮 标记为已转换服务
     * 根据 id 查询 物流单号
     *
     * @param id 仓库仓内操作表id
     * @return expressCode 运单号
     */
    public static String findExpressCodeById(Object id) {
        String expressCode = "";
        JSONObject whereKey = new JSONObject();
        whereKey.put("ID", id);
        JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.IP_B_WH_INNER_OPERATE,
                OcElasticSearchIndexResources.IP_B_WH_INNER_OPERATE, whereKey,
                null, null, 50, 0, new String[]{"EXPRESS_CODE"});
        JSONArray data = search.getJSONArray("data");
        if (CollectionUtils.isNotEmpty(data)) {
            expressCode = data.stream().map(a -> (
                    (JSONObject) a).getString("EXPRESS_CODE"))
                    .collect(Collectors.toList()).get(0);
        }
        return expressCode;
    }
}
