package com.jackrain.nea.oc.oms.process.transfer.impl.normal.taobaofx.step;

import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoFxOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoFxOrder;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoFxOrderItemExt;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.ps.services.PsGetCommodityInformationService;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> 周琳胜
 * @since : 2019-07-11
 * create at : 2019-07-11 16:24
 * 判断平台订单商品明细中条码是否在系统中【条码档案】中是否存在
 */
@Step(order = 40, description = "判断平台订单商品明细中条码是否在系统中【条码档案】中是否存在")
@Slf4j
@Component
public class Step40CheckItemInSkuExist extends BaseTaobaoFxOrderProcessStep implements IOmsOrderProcessStep<IpTaobaoFxOrderRelation> {

    @Autowired
    private PsGetCommodityInformationService psGetCommodityInformationService;

    @Override
    public ProcessStepResult<IpTaobaoFxOrderRelation> startProcess(IpTaobaoFxOrderRelation orderInfo,
                                                                   ProcessStepResult preStepResult,
                                                                   boolean isAutoMakeup, User operateUser) {
        log.debug("TaobaoFxTransferOrder.step04" + orderInfo.toString());
        IpBTaobaoFxOrder order = orderInfo.getIpBTaobaoFxOrder();
        boolean hasErrorInfo = false;
        StringBuilder sbErrorInfo = new StringBuilder();
        List<IpBTaobaoFxOrderItemExt> IpBTaobaoFxOrderItemExtList = orderInfo.getTaobaoFxOrderItems();
        for (IpBTaobaoFxOrderItemExt taobaoFxOrderItem : IpBTaobaoFxOrderItemExtList) {
            String skuId = taobaoFxOrderItem.getSkuOuterId();
            if (StringUtils.isEmpty(skuId)) {
                skuId = taobaoFxOrderItem.getItemOuterId();
            }
            if (StringUtils.isEmpty(skuId)) {
                hasErrorInfo = true;
                sbErrorInfo.append("商品skuId为空;");
                sbErrorInfo.append("\r\n");
                break;
            } else {
                ProductSku skuInfo = psRpcService.selectProductSku(skuId);
                log.info("TaobaoFxTransferOrder:查询商品信息：" + skuInfo);
                if (skuInfo == null) {
                    hasErrorInfo = true;
                    String msg = Resources.getMessage("不存在;");
                    // 显示商品货号数据
                    sbErrorInfo.append(taobaoFxOrderItem.getSkuOuterId() + "--");
                    sbErrorInfo.append(taobaoFxOrderItem.getItemOuterId());
                    sbErrorInfo.append(msg);
                    sbErrorInfo.append("\r\n");
                    break;
                } else if (YesNoEnum.N.getKey().equals(skuInfo.getIsactive())) {
                    hasErrorInfo = true;
                    String msg = Resources.getMessage("已作废;");
                    // 显示商品货号数据
                    sbErrorInfo.append(taobaoFxOrderItem.getSkuOuterId() + "--");
                    sbErrorInfo.append(taobaoFxOrderItem.getItemOuterId());
                    sbErrorInfo.append(msg);
                    sbErrorInfo.append("\r\n");
                    break;
                } else {
                    taobaoFxOrderItem.setProdSku(skuInfo);
                }
            }
        }
        if (hasErrorInfo) {
            String errorMessage = "商品数据不存在或已作废，退出转单操作";
            IpBTaobaoFxOrder ipBTaobaoFxOrder = new IpBTaobaoFxOrder();
            ipBTaobaoFxOrder.setId(orderInfo.getOrderId());
            ipBTaobaoFxOrder.setIstrans(4); // 转换失败
            ipBTaobaoFxOrder.setSysremark("商品条码" + sbErrorInfo + "在本地商品中不存在。");
            if (null == order.getTransCount()) {
                ipBTaobaoFxOrder.setTransCount(1L);
            } else {
                ipBTaobaoFxOrder.setTransCount(order.getTransCount() + 1L);
            }
            ipTaobaoFxService.updateTransferStatus(ipBTaobaoFxOrder);
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        } else {
            return new ProcessStepResult<>(StepStatus.SUCCESS, "检查商品是否存在成功，进入下一阶段");
        }
    }

}
