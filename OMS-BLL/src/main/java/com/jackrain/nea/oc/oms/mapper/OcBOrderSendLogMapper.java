package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.retail.OcBOrderSendLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Mapper
public interface OcBOrderSendLogMapper extends ExtentionMapper<OcBOrderSendLog> {


    @Select("SELECT CP_C_PHY_WAREHOUSE_ID FROM oc_b_order_send_log WHERE OC_B_ORDER_ID = #{orderId}")
    List<Long> selectOcBOrderSendLogList(@Param("orderId") Long orderId);


    @Select("SELECT count(1) FROM oc_b_order_send_log WHERE OC_B_ORDER_ID = #{orderId} and CP_C_PHY_WAREHOUSE_ID = #{warehouseId}")
    int selectOcBOrderSendLogListCount(@Param("orderId") Long orderId, @Param("warehouseId") Long warehouseId);

    /**
     * 根据订单号查询count
     *
     * @param orderId 订单ID
     * @return count
     */
    @Select("SELECT count(*) FROM oc_b_order_send_log WHERE OC_B_ORDER_ID = #{orderId}")
    int countByOrderId(@Param("orderId") Long orderId);

}