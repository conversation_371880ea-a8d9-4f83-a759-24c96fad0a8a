package com.jackrain.nea.oc.services;

import com.jackrain.nea.oc.oms.api.OmsOrderAddCmd;
import com.jackrain.nea.oc.oms.model.request.OmsOcBOrderRequest;
import com.jackrain.nea.oc.oms.services.OmsOrderAddService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: 黄世新
 * @Date: 2019/11/4 2:17 下午
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OmsOrderAddCmdImpl implements OmsOrderAddCmd {

    @Autowired
    private OmsOrderAddService omsOrderAddService;

    @Override
    public ValueHolderV14 omsOrderAdd(OmsOcBOrderRequest request) {
        return omsOrderAddService.omsOrderAdd(request);
    }
}
