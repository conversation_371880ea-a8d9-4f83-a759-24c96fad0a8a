package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.api.OrderInterceptionCmd;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.services.OrderInterceptionService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;


/**
 * @author: 夏继超
 * @since: 2019/3/11
 * create at : 2019/3/11 11:02
 */
@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OrderInterceptionCmdImpl implements OrderInterceptionCmd {
    @Autowired
    OrderInterceptionService orderInterceptionService;
    @Autowired
    OcBReturnOrderMapper returnOrderMapper;

    @Override
    public ValueHolder orderInterception(JSONObject object, User loginUser) {
        return orderInterceptionService.orderInterception(object, loginUser);
    }

    @Override
    public ValueHolder cancelInterception(JSONObject object, User loginUser) {
//        return orderInterceptionService.cancelInterception(object, loginUser);
        return orderInterceptionService.mainCancelInterception(object, loginUser);
    }


    /**
     * 配送拦截
     *
     * @param obj
     * @param user
     * @return
     */
    @Override
    public ValueHolderV14 distributionInterception(JSONObject obj, User user) {
        ValueHolderV14 vh = new ValueHolderV14();

        if (obj.isEmpty() || !obj.containsKey("ids")) {
            vh.setMessage("传入的参数不能为空");
            vh.setCode(ResultCode.FAIL);
            return vh;
        }
        JSONArray ids = obj.getJSONArray("ids");
        if (ids.size() > 0) {
            //c)若为“等待售后确认”“完成”“取消”状态下的订单，选中要操作的数据行，此按钮显示为灰色，鼠标悬浮在按钮时提示“当前选中行，无法使用此按钮”；
            ArrayList<Integer> returnStatus = new ArrayList();
            returnStatus.add(ReturnStatusEnum.CANCLE.getVal());
            returnStatus.add(ReturnStatusEnum.COMPLETION.getVal());
            returnStatus.add(ReturnStatusEnum.WAIT_AFTERSALE_ENSURE.getVal());
            Integer integer = returnOrderMapper.selectCount(new QueryWrapper<OcBReturnOrder>().in("id", ids).in("RETURN_STATUS", returnStatus));
            if (integer != 0) {
                vh.setMessage("勾选的退单存在“等待售后确认”“完成”“取消”状态下的，不允许操作");
                vh.setCode(ResultCode.FAIL);
                return vh;
            }
            vh = orderInterceptionService.distributionInterception(ids, user);
        } else {
            vh.setMessage("请至少选中一项");
            vh.setCode(ResultCode.FAIL);
            return vh;
        }
        return vh;
    }

}
