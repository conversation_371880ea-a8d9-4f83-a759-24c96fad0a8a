package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@TableName(value = "ip_b_taobao_refund")
@Data
@Document(index = "ip_b_taobao_refund", type = "ip_b_taobao_refund")
public class IpBTaobaoRefund extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "SHIPPING_TYPE")
    @Field(type = FieldType.Keyword)
    private String shippingType;

    @JSONField(name = "CS_STATUS")
    @Field(type = FieldType.Long)
    private Long csStatus;

    @JSONField(name = "ADVANCE_STATUS")
    @Field(type = FieldType.Long)
    private Long advanceStatus;

    @JSONField(name = "SPLIT_TAOBAO_FEE")
    @Field(type = FieldType.Double)
    private BigDecimal splitTaobaoFee;

    @JSONField(name = "SPLIT_SELLER_FEE")
    @Field(type = FieldType.Double)
    private BigDecimal splitSellerFee;

    @JSONField(name = "REFUND_ID")
    @Field(type = FieldType.Keyword)
    private String refundId;

    @JSONField(name = "TID")
    @Field(type = FieldType.Long)
    private Long tid;

    @JSONField(name = "OID")
    @Field(type = FieldType.Long)
    private Long oid;

    @JSONField(name = "ALIPAY_NO")
    @Field(type = FieldType.Keyword)
    private String alipayNo;

    @JSONField(name = "TOTAL_FEE")
    @Field(type = FieldType.Double)
    private BigDecimal totalFee;

    @JSONField(name = "BUYER_NICK")
    @Field(type = FieldType.Keyword)
    private String buyerNick;

    @JSONField(name = "SELLER_NICK")
    @Field(type = FieldType.Keyword)
    private String sellerNick;

    @JSONField(name = "CREATED")
    @Field(type = FieldType.Long)
    private Date created;

    @JSONField(name = "MODIFIED")
    @Field(type = FieldType.Long)
    private Date modified;

    @JSONField(name = "ORDER_STATUS")
    @Field(type = FieldType.Keyword)
    private String orderStatus;

    @JSONField(name = "STATUS")
    @Field(type = FieldType.Keyword)
    private String status;

    @JSONField(name = "GOOD_STATUS")
    @Field(type = FieldType.Keyword)
    private String goodStatus;

    @JSONField(name = "HAS_GOOD_RETURN")
    @Field(type = FieldType.Integer)
    private Integer hasGoodReturn;

    @JSONField(name = "REFUND_FEE")
    @Field(type = FieldType.Double)
    private BigDecimal refundFee;

    @JSONField(name = "PAYMENT")
    @Field(type = FieldType.Double)
    private BigDecimal payment;

    @JSONField(name = "REASON")
    @Field(type = FieldType.Keyword)
    private String reason;

    @JSONField(name = "REFUNDDESC")
    @Field(type = FieldType.Keyword)
    private String refunddesc;

    @JSONField(name = "TITLE")
    @Field(type = FieldType.Keyword)
    private String title;

    @JSONField(name = "PRICE")
    @Field(type = FieldType.Double)
    private BigDecimal price;

    @JSONField(name = "NUM")
    @Field(type = FieldType.Long)
    private Long num;

    @JSONField(name = "GOOD_RETURN_TIME")
    @Field(type = FieldType.Long)
    private Date goodReturnTime;

    @JSONField(name = "COMPANY_NAME")
    @Field(type = FieldType.Keyword)
    private String companyName;

    @JSONField(name = "SID")
    @Field(type = FieldType.Keyword)
    private String sid;

    @JSONField(name = "ADDRESS")
    @Field(type = FieldType.Keyword)
    private String address;

    @JSONField(name = "NUM_IID")
    @Field(type = FieldType.Long)
    private Long numIid;

    @JSONField(name = "ISTRANS")
    @Field(type = FieldType.Keyword)
    private String istrans;

    @JSONField(name = "CP_C_SHOP_ID")
    @Field(type = FieldType.Long)
    private Long cpCShopId;

    @JSONField(name = "INSERTDATE")
    @Field(type = FieldType.Long)
    private Date insertdate;

    @JSONField(name = "TRANSDATE")
    @Field(type = FieldType.Long)
    private Date transdate;

    @JSONField(name = "SYSREMARK")
    @Field(type = FieldType.Keyword)
    private String sysremark;

    @JSONField(name = "IP_B_TAOBAO_ORDER_ITEM_ID")
    @Field(type = FieldType.Long)
    private Long ipBTaobaoOrderItemId;

    @JSONField(name = "ISRELATED")
    @Field(type = FieldType.Keyword)
    private String isrelated;

    @JSONField(name = "SKU")
    @Field(type = FieldType.Keyword)
    private String sku;

    @JSONField(name = "SYSREMARK2")
    @Field(type = FieldType.Keyword)
    private String sysremark2;

    @JSONField(name = "ISTRANS2")
    @Field(type = FieldType.Keyword)
    private String istrans2;

    @JSONField(name = "TRANSDATE2")
    @Field(type = FieldType.Long)
    private Date transdate2;

    @JSONField(name = "REFUND_PHASE")
    @Field(type = FieldType.Keyword)
    private String refundPhase;

    @JSONField(name = "REFUND_VERSION")
    @Field(type = FieldType.Long)
    private Long refundVersion;

    @JSONField(name = "OPERATION_CONTRAINT")
    @Field(type = FieldType.Keyword)
    private String operationContraint;

    @JSONField(name = "ATTRIBUTE")
    @Field(type = FieldType.Keyword)
    private String attribute;

    @JSONField(name = "OUTER_ID")
    @Field(type = FieldType.Keyword)
    private String outerId;

    @JSONField(name = "RETURNDESC")
    @Field(type = FieldType.Keyword)
    private String returndesc;

    @JSONField(name = "REMIND_TYPE")
    @Field(type = FieldType.Long)
    private Long remindType;

    @JSONField(name = "EXIST_TIMEOUT")
    @Field(type = FieldType.Keyword)
    private String existTimeout;

    @JSONField(name = "TIMEOUT")
    @Field(type = FieldType.Long)
    private Date timeout;

    @JSONField(name = "UPD_FLAG")
    @Field(type = FieldType.Integer)
    private Integer updFlag;

    @JSONField(name = "ISGENINCIDENTAL")
    @Field(type = FieldType.Keyword)
    private Integer isgenincidental;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;

    @JSONField(name = "TRANS_COUNT")
    @Field(type = FieldType.Long)
    private Long transCount;

    /**
     * 转换失败原因
     */
    @JSONField(name = "TRANS_FAIL_REASON")
    @Field(type = FieldType.Integer)
    private Integer transFailReason;

    /**
     * 当前申请的期数起始值
     */
    @JSONField(name = "CYCLE_PERIOD")
    @Field(type = FieldType.Integer)
    private Long cyclePeriod;

    /**
     * 总期数
     */
    @JSONField(name = "CYCLE_TPC")
    @Field(type = FieldType.Integer)
    private Long cycleTpc;

    /**
     * 当前申请了多少期
     */
    @JSONField(name = "CYCLE_PERIOD_COUNT")
    @Field(type = FieldType.Integer)
    private Long cyclePeriodCount;
}