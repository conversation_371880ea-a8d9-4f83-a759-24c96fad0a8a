package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.oc.oms.api.st.OmsManualExpiryDateCmd;
import com.jackrain.nea.oc.oms.dto.ExpiryDateItem;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2022/6/21 下午5:13
 * @Version 1.0
 */
@Api(value = "OmsExpiryDateCtrl", tags = "指定效期")
@Slf4j
@RestController
public class OmsExpiryDateCtrl {

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @Autowired
    private OmsManualExpiryDateCmd omsManualExpiryDateCmd;



    @RequestMapping("api/cs/oc/oms/v1/selectExpiryDate")
    public ValueHolderV14 selectExpiryDate(HttpServletRequest request,
                                           @RequestBody JSONObject obj) {
        log.info(LogUtil.format("手工指定效期查询:{}",
                "OmsExpiryDateCtrl.selectExpiryDate"), obj);
        ValueHolderV14 holder = new ValueHolderV14();
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        if (user == null) {
            holder.setCode(-1);
            holder.setMessage("用户不能为空!");
            return holder;
        }
        JSONArray ids = obj.getJSONArray("ids");
        if (ids == null) {
            holder.setCode(-1);
            holder.setMessage("参数有误");
            return holder;
        }
        List<Long> longs = JSON.parseArray(ids.toJSONString(), Long.class);
        return omsManualExpiryDateCmd.selectOmsOrderItem(longs);


    }


    @RequestMapping("api/cs/oc/oms/v1/confirmExpiryDate")
    public ValueHolderV14 executeOmsOrderItem(HttpServletRequest request,
                                              @RequestBody List<ExpiryDateItem> obj) {
        log.info(LogUtil.format("手工指定效期入参:{}",
                "OmsExpiryDateCtrl.executeOmsOrderItem"), JSONObject.toJSONString(obj));
        ValueHolderV14 holder = new ValueHolderV14();
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        if (user == null) {
            holder.setCode(-1);
            holder.setMessage("用户不能为空!");
            return holder;
        }
        if (CollectionUtils.isEmpty(obj)) {
            holder.setCode(-1);
            holder.setMessage("参数有误!");
            return holder;
        }
        return omsManualExpiryDateCmd.executeOmsOrderItem(obj, user);
    }

    /**
     * 自动指定效期
     * @param request
     * @param obj
     * @return
     */
    @RequestMapping("api/cs/oc/oms/v1/autoExpiryDate")
    public ValueHolderV14 autoExpiryDate(HttpServletRequest request,
                                       @RequestBody JSONObject obj){
        ValueHolderV14 holder = new ValueHolderV14();
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        if (user == null) {
            holder.setCode(-1);
            holder.setMessage("用户不能为空!");
            return holder;
        }
        JSONArray ids = obj.getJSONArray("ids");
        if (ids == null) {
            holder.setCode(-1);
            holder.setMessage("参数有误");
            return holder;
        }
        List<Long> idList = JSON.parseArray(ids.toJSONString(), Long.class);
        return omsManualExpiryDateCmd.autoExpiryDate(idList, user);
    }

}
