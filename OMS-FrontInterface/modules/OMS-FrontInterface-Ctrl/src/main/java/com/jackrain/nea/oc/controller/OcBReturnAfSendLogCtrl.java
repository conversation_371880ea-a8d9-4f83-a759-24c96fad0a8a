package com.jackrain.nea.oc.controller;

import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBReturnAfSendLogCmd;
import com.jackrain.nea.oc.oms.model.table.OcBReturnAfSendLog;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @Description: 额外退款日志查询
 * @author: 江家雷
 * @since: 2020/8/14
 * create at : 2020/8/14 17:23
 */
@Api(value = "OcBReturnAfSendLogCtrl", tags = "额外退款日志查询")
@Slf4j
@RestController
public class OcBReturnAfSendLogCtrl {

    @Autowired
    private OcBReturnAfSendLogCmd ocBReturnAfSendLogCmd;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @RequestMapping("/api/cs/oc/oms/v1/getOcBReturnAfSendLog")
    public ValueHolderV14<List<OcBReturnAfSendLog>> getOcBReturnAfSendLog(HttpServletRequest request, Long ocBReturnAfSendId) {
        ValueHolderV14 v14 = new ValueHolderV14();//获取用户登录信息
        if (ocBReturnAfSendId == null) {
            throw new NDSException(Resources.getMessage("参数错误!"));
        }
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        if (user == null) {
            throw new NDSException(Resources.getMessage("用户没有权限!"));
        }
        try {
            List<OcBReturnAfSendLog> list = ocBReturnAfSendLogCmd.getOcBReturnAfSendLog(ocBReturnAfSendId);
            v14.setData(list);
            v14.setCode(ResultCode.SUCCESS);
            v14.setMessage("查询成功");
        } catch (Exception e) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage(e.getMessage());
        }
        return v14;
    }

}