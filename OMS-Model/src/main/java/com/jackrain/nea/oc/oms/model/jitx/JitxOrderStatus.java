package com.jackrain.nea.oc.oms.model.jitx;

/**
 * 唯品会JITX订单状态
 *
 * @author: 黄超
 * @since: 2019-06-26
 * create at : 2019-06-26 9:00
 */
public class JitxOrderStatus {

    private JitxOrderStatus() {

    }

    /**
     * 10：订单已审核
     */
    public static final String ORDER_ALREADY_AUDITED = "10";
    /**
     * 22:订单已发货
     */
    public static final String ORDER_ALREADY_SEND = "22";
    /**
     * 23:订单已揽收
     **/
    public static final String ORDER_ALREADY_COLLECTED = "23";
    /**
     * 97_22:已发货取消
     **/
    public static final String ORDER_SEND_REFUND = "97_22";
    /**
     * 97_10:未发货取消
     **/
    public static final String ORDER_UNSEND_REFUND = "97_10";
    /**
     * 97_23:已揽收取消
     **/
    public static final String ORDER_COLLECTED_REFUND = "97_23";

    public static final String OTHER_STATUS = "";
}
