package com.jackrain.nea.oc.oms.process.transfer.impl.refund.alibaba.ascp.refund.step;

import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderRefundMapper;
import com.jackrain.nea.oc.oms.services.IpBAlibabaAscpOrderRefundService;
import com.jackrain.nea.oc.oms.services.OmsRefundOrderService;
import com.jackrain.nea.oc.oms.services.OmsReturnOrderService;
import com.jackrain.nea.rpc.CpRpcService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Descroption 猫超直发退款单转单处理阶段抽象基类
 * <AUTHOR>
 * @Date 2020/09/05 13:42
 */
public abstract class BaseAlibabaAscpOrderRefundProcessStep {
    @Autowired
    protected IpBAlibabaAscpOrderRefundService orderRefundService;

    @Autowired
    protected OmsRefundOrderService omsRefundOrderService;

    @Autowired
    protected OmsReturnOrderService omsReturnOrderService;

    @Autowired
    protected CpRpcService cpRpcService;

    @Autowired
    protected OcBReturnOrderMapper ocBReturnOrderMapper;

    @Autowired
    protected OcBReturnOrderRefundMapper ocBReturnOrderRefundMapper;
}
