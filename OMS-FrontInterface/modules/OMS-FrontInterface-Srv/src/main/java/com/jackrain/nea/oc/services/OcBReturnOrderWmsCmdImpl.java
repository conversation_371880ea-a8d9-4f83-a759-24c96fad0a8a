package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.store.model.request.in.SgBStoInNoticesBillSaveRequest;
import com.burgeon.r3.sg.store.model.result.in.SgBStoInNoticesBillSaveResult;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.api.OcBReturnOrderWmsCmd;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderLogMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderRefundMapper;
import com.jackrain.nea.oc.oms.model.enums.*;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderLog;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.oc.oms.nums.ReturnOrderNodeEnum;
import com.jackrain.nea.oc.oms.services.OcBReturnOrderNodeRecordService;
import com.jackrain.nea.oc.oms.services.OmsReturnOrderConfirmService;
import com.jackrain.nea.oc.oms.services.ReturnOrderAuditService;
import com.jackrain.nea.oc.oms.services.refund.OcBReturnOrderLogService;
import com.jackrain.nea.oc.oms.services.returnin.OcRefundInGenerateStorageBillService;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.ValueHolderV14Utils;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: YCH
 * @Date: 2022/4/1 18:46
 */
@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBReturnOrderWmsCmdImpl implements OcBReturnOrderWmsCmd {

    @Autowired
    private OcBReturnOrderLogService ocBReturnOrderLogService;

    @Autowired
    OcBReturnOrderMapper ocBReturnOrderMapper;

    @Autowired
    OcBReturnOrderLogMapper logMapper;

    @Autowired
    IpRpcService ipRpcService;

    @Autowired
    private OcBReturnOrderNodeRecordService nodeRecordService;

    @Autowired
    private BllRedisLockOrderUtil redisUtil;
    @Autowired
    private OcRefundInGenerateStorageBillService ocRefundInGenerateStorageBillService;
    @Autowired
    private OcBReturnOrderRefundMapper ocBReturnOrderRefundMapper;
    @Autowired
    private SgRpcService sgRpcService;
    @Autowired
    private OmsReturnOrderConfirmService omsReturnOrderConfirmService;
    @Autowired
    private ReturnOrderAuditService returnOrderAuditService;


    @Override
    public ValueHolderV14 cancelReturnOrder(List<Long> ids, User user) throws NDSException {
        //非取消的退单
        List<OcBReturnOrder> ocBReturnOrders = ocBReturnOrderMapper.selectOcBReturnOrderByOrder(ids);
        ValueHolderV14 v14 = new ValueHolderV14();
        int errorNum = 0;
        List<Long> returnOrderIds = new ArrayList<>();
        for (OcBReturnOrder ocBReturnOrder : ocBReturnOrders) {
            //只有传wms成功地才可以取消
            if (WmsWithdrawalState.YES.toInteger().equals(ocBReturnOrder.getIsTowms())) {
                if (ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal().equals(ocBReturnOrder.getReturnStatus())) {
                    if (!OcBorderListEnums.WmsCanceStatusEnum.RECALL_SUCCESS.getVal().equals(ocBReturnOrder.getWmsCancelStatus())) {
                        try {
                            v14 = ipRpcService.cancelReturnOrder(ocBReturnOrder, user);
                        } catch (Exception e) {
                            log.error("调用从wms撤回服务失败", e);
                            v14.setCode(ResultCode.FAIL);
                            v14.setMessage("撤回失败" + e.getMessage());
                            errorNum++;
                        }
                        if (v14.isOK()) {
                            this.updateCancelWmsStatus(ocBReturnOrder, 1, user, v14.getMessage());
                            returnOrderIds.add(ocBReturnOrder.getId());
                        } else {
                            errorNum++;
                            //则返回撤回失败信息，并添加到系统备注中，程序结束。
                            this.updateCancelWmsStatus(ocBReturnOrder, 0, user, v14.getMessage());
                        }
                    } else {
                        v14.setCode(ResultCode.SUCCESS);
                        v14.setMessage("撤回成功");
                    }
                } else {
                    this.insertReturnOrederLog("手动WMS撤回失败", "只有【等待退货入库】状态才可以操作，请检查后重试！", null, user, ocBReturnOrder.getId());
                    errorNum++;
                }
            } else {
                this.insertReturnOrederLog("手动WMS撤回失败", "WMS撤回失败,单据不为传wms成功！", null, user, ocBReturnOrder.getId());
                errorNum++;
            }
        }
        if (CollectionUtils.isNotEmpty(returnOrderIds)) {
            nodeRecordService.batchInsertNotExist(ReturnOrderNodeEnum.CANCEL_WMS_TIME, new Date(),
                    returnOrderIds, SystemUserResource.getRootUser());
        }

        if (errorNum == 0) {
            v14.setMessage("wms撤回成功！");

        } else {
            v14.setMessage(String.format("执行成功记录数：%s，执行失败记录数：%s，失败原因请见详情页", ids.size() - errorNum, errorNum));
            v14.setCode(ResultCode.SUCCESS);
        }
        return v14;
    }

    public void insertReturnOrederLog(String type, String message, String param, User user, Long returnID) {
        OcBReturnOrderLog log = new OcBReturnOrderLog();
        log.setAdClientId(Long.valueOf(Integer.valueOf(user.getClientId()).toString()));
        log.setAdOrgId(Long.valueOf(Integer.valueOf(user.getOrgId()).toString()));
        log.setOwnername(user.getName());
        log.setOwnerename(user.getEname());
        log.setModifiername(user.getName());
        log.setModifierename(user.getEname());
        log.setId(ModelUtil.getSequence("oc_b_return_order_log"));
        log.setIpAddress(user.getLastloginip());
        log.setLogParam(param);
        log.setLogMessage(message);
        log.setLogType(type);
        log.setUserName(user.getName());
        log.setOcBReturnOrderId(returnID);
        log.setCreationdate(new Date());
        log.setModifieddate(new Date());
        logMapper.insert(log);
    }

    public ValueHolderV14<Object> updateCancelWmsStatus(OcBReturnOrder returnOrderInfo, Integer tag, User user,
                                                        String cancelWmsMessage) {
        ValueHolderV14<Object> vh = new ValueHolderV14<>();
        //更新主表
        //A.“WMS撤回状态”：已撤回
        // B.“修改人”：当前操作人
        //C.“修改时间”：当前时间
        OcBReturnOrder order = new OcBReturnOrder();
        QueryWrapper<OcBReturnOrder> wrapper = new QueryWrapper<>();
        wrapper.eq("id", returnOrderInfo.getId());
        if (tag == 1) {
            order.setWmsCancelStatus(OcBorderListEnums.WmsCanceStatusEnum.RECALL_SUCCESS.getVal());
            returnOrderInfo.setWmsCancelStatus(OcBorderListEnums.WmsCanceStatusEnum.RECALL_SUCCESS.getVal());
        } else {
            order.setWmsCancelStatus(OcBorderListEnums.WmsCanceStatusEnum.RECALL_FAIL.getVal());
            returnOrderInfo.setWmsCancelStatus(OcBorderListEnums.WmsCanceStatusEnum.RECALL_FAIL.getVal());
        }
        order.setModifierid(Long.valueOf(user.getId()));
        order.setModifieddate(new Date());
        returnOrderInfo.setModifierid(Long.valueOf(user.getId()));
        returnOrderInfo.setModifieddate(new Date());
        try {
            if (tag == 0) {
                order.setRemark("手动撤回失败信息->" + cancelWmsMessage);
                returnOrderInfo.setRemark("手动撤回失败信息");
                ocBReturnOrderMapper.update(order, wrapper);
            } else {
                order.setIsTodrp(0);
                order.setToDrpStatus(ToDRPStatusEnum.NOT.getCode());
                String billNo = returnOrderInfo.getBillNo();
                String[] rs = billNo.split("R");
                String key = "wms:returnOrder:" + rs[0];
                Boolean hasKey = RedisOpsUtil.getObjRedisTemplate().hasKey(key);
                if (hasKey != null && hasKey) {
                    Integer num = (Integer) RedisOpsUtil.getObjRedisTemplate().opsForValue().get(key);
                    num++;
                    order.setBillNo(rs[0] + "R" + num);
                    RedisOpsUtil.getObjRedisTemplate().opsForValue().set(key, num);
                } else {
                    order.setBillNo(returnOrderInfo.getBillNo() + "R1");
                    RedisOpsUtil.getObjRedisTemplate().opsForValue().set(key, 1);
                }
                order.setRemark("手动撤回成功信息");
                returnOrderInfo.setRemark("手动撤回成功信息");
                ocBReturnOrderMapper.update(order, wrapper);
            }
            //调用退换货日志服务
            if (tag == 1) {
                this.insertReturnOrederLog("手动WMS撤回成功", "手动WMS撤回成功 ", null, user, returnOrderInfo.getId());
            } else {
                this.insertReturnOrederLog("手动WMS撤回失败", "手动WMS撤回失败-> " + cancelWmsMessage, null, user, returnOrderInfo.getId());
            }
        } catch (Exception e) {
            log.debug("调用服务失败" + e.getMessage());
            vh.setCode(-1);
            vh.setMessage("跟新主表失败或者调用日志服务失败");
            return vh;
        }
        return vh;
    }

    @Override
    public ValueHolderV14 confirmReturnOrder(List<Long> ids, User user) throws NDSException {
        //List<OcBReturnOrder> ocBReturnOrders = ocBReturnOrderMapper.selectActiveReturnOrderListByIds(ids);
        //Map<Long, OcBReturnOrder> returnOrderMap = ocBReturnOrders.stream().collect(Collectors.toMap(OcBReturnOrder::getId, Function.identity()));

        Map<Long, Object> errMap = new HashMap<>();
        List<Long> updateIdList = Lists.newArrayList();
        List<RedisReentrantLock> lockList = Lists.newArrayList();

        try {
            for (Long id : ids) {

                String lockRedisKey = BllRedisKeyResources.buildLockReturnOrderKey(id);
                RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
                if (!redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                    errMap.put(id, "当前单据正在操作中！");
                    continue;
                }
                lockList.add(redisLock);

                OcBReturnOrder returnOrder = ocBReturnOrderMapper.selectById(id);
                if (Objects.isNull(returnOrder)) {
                    errMap.put(id, "当前记录不存在！");
                    continue;
                }

                if (ReturnStatusEnum.CANCLE.getVal().equals(returnOrder.getReturnStatus())
                        || ReturnOrderConfirmStatusEnum.CONFIRM.getKey().equals(returnOrder.getConfirmStatus())
                ) {
                    errMap.put(id, "当前记录已取消或已确认，不允许重复确认！");
                    continue;
                }

                if (Objects.isNull(returnOrder.getCpCPhyWarehouseInId())) {
                    errMap.put(id, "当前单据没有选择入库实体仓库，不允许确认！");
                    continue;
                }
                //创建入库通知单
                try{
                    List<OcBReturnOrderRefund> returnOrderRefunds = ocBReturnOrderRefundMapper.queryRtnOrderRefundByoId(id,"Y");
                    omsReturnOrderConfirmService.createInNotices(returnOrder,returnOrderRefunds,user);
                }catch(Exception e){
                    log.error(LogUtil.format("创建入库通知单失败，ID："+id,"退换货单确认"),e);
                    errMap.put(id, e.getMessage());
                    continue;
                }
                updateIdList.add(id);
                returnOrderAuditService.recordReturnOrderLog(id, "退货单确认", "退换货单确认完成", false, SystemUserResource.getRootUser());
            }
            if (CollectionUtils.isNotEmpty(updateIdList)) {
                //batchUpdateConfirmStatus(updateIdList, user);
                //ocBReturnOrderLogService.batchSaveOrderLogInfo(updateIdList, "退换货单确认", "退换货单确认成功", user);
                nodeRecordService.batchInsertNotExist(ReturnOrderNodeEnum.CONFIRM_TIME, new Date(), updateIdList, SystemUserResource.getRootUser());
            }

        } catch (Exception e) {
            log.error(LogUtil.format("退单传WMS任务异常={}"), Throwables.getStackTraceAsString(e));
            return new ValueHolderV14(ResultCode.FAIL, e.getMessage());
        } finally {
            //批量解锁
            try {
                lockList.forEach(RedisReentrantLock::unlock);
            } catch (Exception e) {
                log.error(LogUtil.format("退单传WMS定时任务释放锁失败:{}"), Throwables.getStackTraceAsString(e));
            }
        }
        return ValueHolderV14Utils.getExcuteValueHolder(ids.size(), errMap);
    }

    public void batchUpdateConfirmStatus(List<Long> updateIdList, User user) {
        List<List<Long>> partition = Lists.partition(updateIdList, 200);
        for (List<Long> subIdList : partition) {
            LambdaQueryWrapper<OcBReturnOrder> queryWrapper = new LambdaQueryWrapper<>();
            OcBReturnOrder returnOrder = new OcBReturnOrder();
            BaseModelUtil.makeBaseModifyField(returnOrder, user);
            returnOrder.setConfirmStatus(ReturnOrderConfirmStatusEnum.CONFIRM.getKey());
            returnOrder.setConfirmName(user.getName());
            returnOrder.setConfirmId((long) user.getId());
            returnOrder.setConfirmDate(new Date());
            queryWrapper.in(OcBReturnOrder::getId, subIdList);
            ocBReturnOrderMapper.update(returnOrder, queryWrapper);
        }
    }

    /**
     *  查询退货物流单号
     * @param ids
     * @return
     */
    public ValueHolderV14 queryLogisticsCode(List<Long> ids) {
        try {
            if (CollectionUtils.isEmpty(ids)) {
                throw new NDSException("请选择记录！");
            }
            List<JSONObject> jsonObjects = ocBReturnOrderMapper.selectActiveReturnOrderLogisticsCodeListByIds(ids);
            ValueHolderV14 vh = ValueHolderV14Utils.getSuccessValueHolder("查询退货物流单号成功");
            vh.setData(jsonObjects);
            return vh;
        }catch (Exception e){
            log.error("查询失败",e);
            return ValueHolderV14Utils.getFailValueHolder(e.getMessage());
        }
    }
}
