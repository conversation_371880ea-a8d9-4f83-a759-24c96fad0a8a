package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.api.OmsReplaceComposeCmd;
import com.jackrain.nea.oc.oms.services.OmsReplaceComposeService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: 黄世新
 * @Date: 2020/2/25 4:25 下午
 * @Version 1.0
 */

@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OmsReplaceComposeCmdImpl implements OmsReplaceComposeCmd {
    @Autowired
    private OmsReplaceComposeService omsReplaceComposeService;

    @Override
    public ValueHolderV14 replaceComposePro(JSONObject jsonObject, User user) {
        return omsReplaceComposeService.replaceComposePro(jsonObject, user);
    }
}
