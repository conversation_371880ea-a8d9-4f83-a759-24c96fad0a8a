package com.jackrain.nea.oc.oms.model.enums;


import com.jackrain.nea.oc.oms.model.result.QueryOrderCheckBoxResult;

import java.util.ArrayList;
import java.util.List;

/**
 * 是否无原单条码
 *
 * @author: 周琳胜
 * create at: 2019/3/25 13:20
 */
public enum IsWithoutOrigEnum {


    /**
     * 是否无原单条码. 否
     */
    NOT_WITHOUT_ORIG("无", 0),

    /**
     * 是
     */
    IS_WITHOUT_ORIG("有", 1);

    String key;
    Integer val;

    IsWithoutOrigEnum(String k, Integer v) {
        this.key = k;
        this.val = v;
    }

    public String getKey() {
        return key;
    }

    public Integer getVal() {
        return val;
    }

    /**
     * 转化成QueryOrderCheckBoxResult
     *
     * @return list<QueryOrderCheckBoxResult>
     */
    public static List<QueryOrderCheckBoxResult> toQueryOrderCheckBoxResult() {
        List<QueryOrderCheckBoxResult> list = new ArrayList<>();
        for (IsWithoutOrigEnum e : IsWithoutOrigEnum.values()) {
            QueryOrderCheckBoxResult o = new QueryOrderCheckBoxResult();
            o.setLabel(e.getKey());
            o.setValue(String.valueOf(e.getVal()));
            list.add(o);
        }
        return list;
    }

}


