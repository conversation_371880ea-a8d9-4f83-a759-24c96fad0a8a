package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.jackrain.nea.oc.oms.api.OmsAcCycleBuyCmd;
import com.jackrain.nea.oc.oms.model.request.CycleConfirmRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolderV14Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 中台周期购财务数据处理
 *
 * <AUTHOR>
 */
@Service(protocol = "dubbo", validation = "true", group = "oc-core", version = "1.0")
@Slf4j
@Component
public class OmsAcCycleBuyCmdImpl implements OmsAcCycleBuyCmd {

    @Resource
    private CycleBuyInfoService cycleBuyInfoService;

    /**
     * 对账变动后周期购额外信息变动（提货金额等）
     *
     * @param confirmRequests
     * @return
     */
    @Override
    public ValueHolderV14 confirmChange(List<CycleConfirmRequest> confirmRequests) {
        try {
            cycleBuyInfoService.changeByConfirm(confirmRequests, false);
        } catch (Exception e) {
            log.warn("周期购对账变动异常 confirmRequests:{}", JSON.toJSONString(confirmRequests), e);
            return ValueHolderV14Utils.getFailValueHolder(e.getMessage());
        }
        return ValueHolderV14Utils.getSuccessValueHolder("ok");
    }


}
