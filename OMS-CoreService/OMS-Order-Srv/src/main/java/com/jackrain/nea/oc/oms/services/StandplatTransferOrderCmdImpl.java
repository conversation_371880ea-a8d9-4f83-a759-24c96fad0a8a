package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.api.StandplatTransferOrderCmd;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.relation.IpStandplatOrderRelation;
import com.jackrain.nea.oc.oms.model.request.TransferOrderRequest;
import com.jackrain.nea.oc.oms.model.result.TransferOrderResult;
import com.jackrain.nea.oc.oms.process.transfer.impl.normal.standplat.StandPlatformTransferOrderProcessImpl;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.ProcessStepResultList;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 通用转单转换单据Service服务接口
 *
 * @author: ming.fz
 * @since: 2019-07-17
 * create at : 2019-07-17
 */
@Service(protocol = "dubbo", validation = "true", group = "oc-core", version = "1.0")
@Slf4j
@Component
public class StandplatTransferOrderCmdImpl implements StandplatTransferOrderCmd {

    @Autowired
    private StandPlatformTransferOrderProcessImpl orderProcess;

    @Autowired
    private IpStandplatOrderService ipStandplatOrderService;

    @Override
    public ValueHolderV14<TransferOrderResult> startTransferOrder(TransferOrderRequest transferOrderRequest) {
        ValueHolderV14<TransferOrderResult> resultValueHolderV14 = new ValueHolderV14<>();
        try {
            boolean hasError = false;
            int failedNumber = 0;
            int successNumber = 0;
            List<ProcessStepResult> errorStepResultList = new ArrayList<>();
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("通用转单入参.orderRelation:{}"), JSONObject.toJSONString(transferOrderRequest));
            }
            for (String orderNo : transferOrderRequest.getOrderNoList()) {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("StandplatTransferOrderCmdImpl.startTransferOrder OrderNo={};ChannelType={}",
                            orderNo), orderNo, transferOrderRequest.getChannelType());
                }
                if (transferOrderRequest.getChannelType() == ChannelType.STANDPLAT) {
                    IpStandplatOrderRelation orderRelation = ipStandplatOrderService.selectStandplatOrder(orderNo);
                    if (orderRelation != null) {
                        ProcessStepResultList processStepResultList = orderProcess.start(orderRelation,
                                false, transferOrderRequest.getOperateUser());
                        if (log.isDebugEnabled()) {
                            log.debug(LogUtil.format("Standplat Finished Page button Order Transfer.Result: {}", orderNo),
                                    JSONObject.toJSONString(processStepResultList));
                        }
                        if (!processStepResultList.isProcessSuccess()) {
                            hasError = true;
                            errorStepResultList.add(processStepResultList.getLastFailedProcessStepResult());
                            failedNumber++;
                        } else {
                            successNumber++;
                        }
                    } else {
                        hasError = false;
                        failedNumber++;
                    }
                }
            }

            if (hasError) {
                resultValueHolderV14.setCode(ResultCode.FAIL);
                StringBuilder sbMessage = new StringBuilder();
                sbMessage.append(String.format("转换成功%s条；转换失败%s条；失败原因： \r\n", successNumber, failedNumber));
                for (ProcessStepResult stepResult : errorStepResultList) {
                    if (stepResult != null) {
                        sbMessage.append(stepResult.getMessage());
                        sbMessage.append(" \r\n");
                    }
                }
                resultValueHolderV14.setMessage(sbMessage.toString());
            } else {
                resultValueHolderV14.setCode(ResultCode.SUCCESS);
                resultValueHolderV14.setMessage("转换全部成功。 ");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            resultValueHolderV14.setCode(ResultCode.FAIL);
            resultValueHolderV14.setMessage("转换异常：" + ex.getMessage());
            log.error(LogUtil.format("TransferOrderCmdImpl.startTransferOrder.转换异常: {}"), ex);
        }

        return resultValueHolderV14;
    }

}
