package com.jackrain.nea.oc.oms.process;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.es.util.SpecialElasticSearchUtil;
import com.jackrain.nea.ip.service.JdCancelOrderTransferService;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OmsSpiltRuleEnum;
import com.jackrain.nea.oc.oms.model.relation.IpStandplatOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderParam;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.SpiltOrderParam;
import com.jackrain.nea.oc.oms.model.request.OmsReleaseStockRequest;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.process.audit.OrderAuditProcess;
import com.jackrain.nea.oc.oms.process.tobeconfirm.ToBeConfirmedOrderProcess;
import com.jackrain.nea.oc.oms.process.transfer.impl.normal.jingdong.JingdongTransferOrderProcessImpl;
import com.jackrain.nea.oc.oms.process.transfer.impl.normal.standplat.StandPlatformTransferOrderProcessImpl;
import com.jackrain.nea.oc.oms.process.transfer.impl.normal.taobao.TaobaoTransferOrderProcessImpl;
import com.jackrain.nea.oc.oms.sap.SapCommonService;
import com.jackrain.nea.oc.oms.services.*;
import com.jackrain.nea.oc.oms.services.delivery.impl.OmsGiftAfterService;
import com.jackrain.nea.oc.oms.services.patrol.ClearRedisCacheService;
import com.jackrain.nea.oc.oms.spiltorder.OmsOrderManualSplitNewService;
import com.jackrain.nea.oc.oms.spiltorder.OmsOrderSpiltRuleService;
import com.jackrain.nea.oc.oms.spiltorder.OmsSplitWeightService;
import com.jackrain.nea.oc.oms.step.ProcessStepResultList;
import com.jackrain.nea.ps.api.ProSkuListCmd;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.web.face.User;
import org.apache.commons.collections.map.HashedMap;
import org.apache.dubbo.config.annotation.Reference;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Profile;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;

import static com.jackrain.nea.resource.OcElasticSearchIndexResources.OC_B_ORDER_TYPE_NAME;

/**
 * @author: 易邵峰
 * @since: 2019-01-21
 * create at : 2019-01-21 21:28
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest()
@Profile(value = "dev")
public class TransferOrderProcessTest {

    @Autowired
    private OcBOrderMapper orderMapper;

    @Autowired
    private OrderAuditProcess auditProcess;

    @Autowired
    private TaobaoTransferOrderProcessImpl distributeProcess;

    @Autowired
    private ToBeConfirmedOrderProcess unconfirmedOrderProcess;

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private IpTaobaoOrderService taoBaoOrderServiceIp;

    @Autowired
    private OmsOrderService orderService;

    @Autowired
    private OcBOrderLinkService ocBOrderLinkService;

    @Autowired
    private OmsOrderDistributeWarehouseService omsWarehouseService;

    @Autowired
    private SgRpcService sgRpcService;

    @Autowired
    private IpJingdongOrderService ipJingdongOrderService;

    @Autowired
    private JingdongTransferOrderProcessImpl jingdongTransferOrderProcess;

    @Reference(group = "ps", version = "1.0")
    private ProSkuListCmd proSkuListCmd;

    @Autowired
    private JdCancelOrderTransferService jdCancelOrderTransferService;

    @Autowired
    private ToBeConfirmedOrderProcess toBeConfirmedOrderProcess;
    @Autowired
    private ClearRedisCacheService clearRedisCacheService;
    @Autowired
    private IpStandplatOrderService ipStandplatOrderService;
    @Autowired
    private StandPlatformTransferOrderProcessImpl standPlatformTransferOrderProcess;
    @Autowired
    private OmsBusinessTypeDistinguishService omsBusinessTypeDistinguishService;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private OmsOrderMarkStService omsOrderMarkStService;
    @Autowired
    private OmsGiftLateHairService omsGiftLateHairService;
    @Autowired
    private OmsOrderSpiltRuleService omsOrderSpiltRuleService;
    @Autowired
    private OmsGiftAfterService omsGiftAfterService;
    @Autowired
    private OmsAutoHoldNewService omsAutoHoldNewService;
    @Autowired
    private SapCommonService SapCommonService;
    @Autowired
    private OmsOrderDistributeLogisticsService omsOrderDistributeLogisticsService;
    @Autowired
    private SplitBeforeSourcingStService splitBeforeSourcingStService;
    @Autowired
    private OmsExpiryDateStService omsExpiryDateStService;
    @Autowired
    private OmsSplitWeightService omsSplitWeightService;
    @Autowired
    private OmsOrderManualSplitNewService omsOrderManualSplitNewService;
    @Autowired
    private OmsReleaseStockOrderService omsReleaseStockOrderService;




    @Before
    public void initial() {
//        distributeProcess = new TransferWaitDistributeProcess();

    }

    protected static final int DEFAULT_PAGE_SIZE = 200;

    @Test
    public void testToBeConfirmedOrderProcess() {

        OcBOrderRelation orderInfo = orderService.selectOmsOrderInfo(3988017);
        String indexName = OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME;
         /*try {
           SpecialElasticSearchUtil.indexDocument(indexName, OC_B_ORDER_TYPE_NAME,
                    orderInfo.getOrderInfo(), orderInfo.getOrderInfo().getId());
            if (orderInfo.getOrderItemList() != null && orderInfo.getOrderItemList().size() > 0) {
                SpecialElasticSearchUtil.indexDocuments(indexName, OcElasticSearchIndexResources.OC_B_ORDER_ITEM_TYPE_NAME,
                        orderInfo.getOrderItemList(),
                        "OC_B_ORDER_ID");
            }
        } catch (IOException e) {
            e.printStackTrace();
        }*/
    }

    @Test
    public void test1() {
        clearRedisCacheService.clearRedisCache("st:", "1",null);
    }


    @Test
    public void test2() {
        OcBOrderRelation taobaoOrderRelation = this.orderService.selectOmsOrderInfo(2428);
        ProcessStepResultList resultList = toBeConfirmedOrderProcess.start(taobaoOrderRelation,
                false, SystemUserResource.getRootUser());


    }

    @Test
    public void test3() {
        IpStandplatOrderRelation standplatOrderRelation = this.ipStandplatOrderService.selectStandplatOrder("4749121273937581125A");
        ProcessStepResultList resultList = standPlatformTransferOrderProcess.start(standplatOrderRelation,
                false, SystemUserResource.getRootUser());


    }
    @Test
    public void test4() {
        User rootUser = SystemUserResource.getRootUser();
        OcBOrderRelation orderInfo = new OcBOrderRelation();
        OcBOrder order = ocBOrderMapper.selectById(202471163L);
        List<OcBOrderItem> itemList = ocBOrderItemMapper.selectOrderItems(order.getId());
        orderInfo.setOrderInfo(order);
        orderInfo.setOrderItemList(itemList);
        OcBOrderParam param = new OcBOrderParam();
        param.setOcBOrder(order);
        param.setOrderItemList(itemList);
        Map<Long, BigDecimal> weightStrategyMap = new HashedMap();
        weightStrategyMap.put(127L, new BigDecimal(500));
       // omsSplitWeightService.splitWeightService(itemList, weightStrategyMap);
        //omsExpiryDateStService.expiryDateStService(param, rootUser,"测试");
        //omsOrderMarkStService.orderMarkStService(param, SystemUserResource.getRootUser());
        //omsBusinessTypeDistinguishService.businessTypeDistinguish(orderInfo, SystemUserResource.getRootUser());
        //omsGiftAfterService.handelGiftAfter(orderInfo);
        //omsAutoHoldNewService.autoCardOrderService(orderInfo, rootUser);
        //omsAutoHoldNewService.autoHandleHoldOrder(param,rootUser);
        order.setOrderStatus(OmsOrderStatus.ORDER_DEFAULT.toInteger());
        //unconfirmedOrderProcess.start(orderInfo, true, rootUser);
        Map<Integer, Map<Set<Long>, SpiltOrderParam>> spiltRule = new HashMap<>();
        Set<Long> set = new HashSet<>();
        set.add(100049834L);
        set.add(100049841L);
        Set<Long> set1 = new HashSet<>();
        set1.add(100049842L);
        set1.add(100049844L);
        Map<Set<Long>, SpiltOrderParam> paramMap = new HashMap<>();
        paramMap.put(set, new SpiltOrderParam());
        paramMap.put(set1, new SpiltOrderParam());

//        Map<Set<Long>, SpiltOrderParam> paramMap1 = new HashMap<>();
//        Set<Long> set3 = new HashSet<>();
//        set3.add(9115L);
//        set3.add(9116L);
//        set3.add(9117L);
//        Set<Long> set4 = new HashSet<>();
//        set4.add(9118L);
//        paramMap1.put(set3, new SpiltOrderParam());
//        paramMap1.put(set4, new SpiltOrderParam());

        spiltRule.put(OmsSpiltRuleEnum.BUSINESS_TYPE.getCode(), paramMap);
        //spiltRule.put(OmsSpiltRuleEnum.GIFT_AFTER.getCode(), paramMap1);
        orderInfo.setSpiltRule(spiltRule);

        //omsOrderSpiltRuleService.orderSpiltRuleService(orderInfo,rootUser );
//        String aa = "{\"CTRL\":{\"MSGTY\":\"\",\"UNAME\":\"C010123\",\"DATUM\":\"2022-08-30\",\"SYSID\":\"SAP\",\"INFID\":\"\",\"PAGE_NO\":0,\"FUNID\":\"ZOMSINF002\",\"PAGE_SIZE\":0,\"METHOD\":\"\",\"REVID\":\"OMS\",\"TABIX\":0,\"MSAGE\":\"\",\"KEYID\":\"10435691\",\"UZEIT\":\"17:13:39\",\"MD5\":\"28463a671eda97d7df9c3f86bdebf58d\"},\"DATA\":[{\"VBELN\":\"10435691\",\"ITEM\":[{\"MAKTX\":\"认养一头牛纯奶250ml利乐砖线上款12入二提装\",\"POSNR\":10,\"OCDQTY_BU\":10.000,\"MATNR\":\"110101101101\"},{\"MAKTX\":\"认养一头牛纯奶250ml利乐砖线上款12入二提装\",\"POSNR\":20,\"OCDQTY_BU\":10.000,\"MATNR\":\"110101100201\"},{\"MAKTX\":\"认养一头牛纯奶250ml利乐砖线上款12入二提装\",\"POSNR\":30,\"OCDQTY_BU\":2.000,\"MATNR\":\"110101101101\"},{\"MAKTX\":\"认养一头牛纯奶250ml利乐砖线上款12入二提装\",\"POSNR\":40,\"OCDQTY_BU\":2.000,\"MATNR\":\"110101100201\"}],\"ZINFSC\":\"S01\"}]}";
//        JSONObject jsonObject = JSONObject.parseObject(aa);
//        JSONArray dataArray = jsonObject.getJSONArray("DATA");
//        for (int i = 0; i < dataArray.size(); i++) {
//            JSONObject dataJo = dataArray.getJSONObject(i);
//            SapCommonService.cancelOrder(dataJo);
//        }
        //omsOrderDistributeLogisticsService.distributeLogisticsDistributeLogistics(order);
        //splitBeforeSourcingStService.splitBeforeSourcingStrategy(param, rootUser);
        //omsOrderManualSplitNewService.handleSplitOne(order,  rootUser, "测试");
        //omsOrderManualSplitNewService.handleSplitByRow(order,  rootUser, "测试");
        List<OmsReleaseStockRequest> requestList = new ArrayList<>();
        OmsReleaseStockRequest request = new OmsReleaseStockRequest();
        List<Long> orderIds = new ArrayList<>();
        orderIds.add(202471163L);
        request.setOrderIds(orderIds);
        request.setQty(BigDecimal.ONE);
        request.setCpCSkuEcode("110101000076");
        request.setCpCSkuId(1034L);
        requestList.add(request);
        omsReleaseStockOrderService.releaseStockOrderService(requestList, rootUser);
    }
}