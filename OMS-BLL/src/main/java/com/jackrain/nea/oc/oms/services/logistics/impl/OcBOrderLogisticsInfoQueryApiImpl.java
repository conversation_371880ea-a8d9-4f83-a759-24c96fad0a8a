package com.jackrain.nea.oc.oms.services.logistics.impl;

import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.LogisticsTableEnum;
import com.jackrain.nea.oc.oms.model.request.LogisticsInfoQueryRequest;
import com.jackrain.nea.oc.oms.model.result.LogisticsInfoQueryResult;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.services.logistics.AbstractLogisticsInfoQueryApi;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> wangbinyu
 * @since : 2022/6/16
 * description :
 */
@Slf4j
@Component
public class OcBOrderLogisticsInfoQueryApiImpl extends AbstractLogisticsInfoQueryApi {

    @Autowired
    private OcBOrderMapper orderMapper;

    @Override
    protected ValueHolderV14<LogisticsInfoQueryResult> invoke(LogisticsInfoQueryRequest request) {
        ValueHolderV14<LogisticsInfoQueryResult> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "查询成功");
        OcBOrder order = orderMapper.selectById(request.getId());
        if(order == null){
            return new ValueHolderV14<>(ResultCode.FAIL, "当前记录已不存在");
        }
        LogisticsInfoQueryResult queryResult = new LogisticsInfoQueryResult();
        queryResult.setLogisticsId(order.getCpCLogisticsId());

        if(StringUtils.isEmpty(order.getMergeSourceCode())){
            queryResult.setSourceCode(order.getBillNo());
        }else{
            queryResult.setSourceCode(order.getMergeSourceCode());
        }
        if(order.getPayTime() == null){
            queryResult.setPaytime(DateUtil.format(order.getCreationdate(), "yyyy-MM-dd HH:mm:ss"));
        }else{
            queryResult.setPaytime(DateUtil.format(order.getPayTime(), "yyyy-MM-dd HH:mm:ss"));
        }
        queryResult.setExpresscode(order.getExpresscode());
        if(StringUtils.isNotEmpty(order.getOaid())){
            //todo 暂时使用OAID判断加密
            queryResult.setReceiverName("***");
            queryResult.setReceiverMobile("***********");
            queryResult.setReceiverAddress("********************");
        }else{
            queryResult.setReceiverName(order.getReceiverName());
            queryResult.setReceiverMobile(order.getReceiverMobile());
            queryResult.setReceiverAddress(order.getReceiverAddress());
        }
        v14.setData(queryResult);
        return v14;
    }

    @Override
    public LogisticsTableEnum table() {
        return LogisticsTableEnum.OC_B_ORDER;
    }
}
