package com.jackrain.nea.oc.oms.model.table.task;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.util.Date;

@TableName(value = "oc_b_order_sku_split_task")
@Data
public class OcBOrderSkuSplitTask extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "oc_b_order_id")
    private Long ocBOrderId;

    @JSONField(name = "ps_c_sku_id")
    private Long psCSkuId;

    @JSONField(name = "status")
    private int status;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;

    @JSONField(name = "version")
    @Field(type = FieldType.Long)
    private Long version;

    @JSONField(name = "next_time")
    @Field(type = FieldType.Long)
    private Long nextTime;

}