package com.jackrain.nea.oc.oms.model.enums;

/**
 * 订单补偿状态
 *
 * @author: 易邵峰
 * @since: 2019-02-23
 * create at : 2019-02-23 19:09
 */
public enum MakeupOrderStatus {

    /**
     * 等待补偿
     */
    WAIT_MAKEUP,

    /**
     * 异常
     */
    EXCEPTION,

    /**
     * 完成
     */
    FINISHED;

    /**
     * 转换成Integer
     *
     * @return 状态对应的Integer
     */
    public int toInteger() {
        if (this == MakeupOrderStatus.WAIT_MAKEUP) {
            return 0;
        } else if (this == MakeupOrderStatus.EXCEPTION) {
            return 99;
        } else if (this == MakeupOrderStatus.FINISHED) {
            return 1;
        } else {
            return 0;
        }
    }

    /**
     * 将Integer转换成补偿状态
     *
     * @param value Integer值
     * @return 补偿状态
     */
    public static MakeupOrderStatus parseFromInt(int value) {
        if (value == 1) {
            return MakeupOrderStatus.FINISHED;
        } else if (value == 99) {
            return MakeupOrderStatus.EXCEPTION;
        } else {
            return MakeupOrderStatus.WAIT_MAKEUP;
        }
    }


}
