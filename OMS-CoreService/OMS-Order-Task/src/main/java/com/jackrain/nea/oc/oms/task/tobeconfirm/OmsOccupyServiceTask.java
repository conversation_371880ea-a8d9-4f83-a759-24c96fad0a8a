package com.jackrain.nea.oc.oms.task.tobeconfirm;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.oc.oms.mapper.OcBOccupyTaskMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.services.BusinessSystemParamService;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sg.service.SgOccupiedInventoryService;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ListSplitUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yomahub.tlog.core.rpc.TLogLabelBean;
import com.yomahub.tlog.core.rpc.TLogRPCHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2022/7/21 下午1:39
 * @Version 1.0
 * 待寻源定时任务
 */
@Slf4j
@Component
public class OmsOccupyServiceTask implements IR3Task {

    @Autowired
    private OcBOccupyTaskMapper ocBOccupyTaskMapper;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private SgOccupiedInventoryService sgOccupiedInventoryService;
    @Autowired
    private BusinessSystemParamService businessSystemParamService;
    @Autowired
    private ThreadPoolTaskExecutor occupyOrderTaskThreadPoolExecutor;


    private static final String TABLE_NAME = "oc_b_occupy_task";

    @Override
    @XxlJob("OmsOccupyServiceTask")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult result = new RunTaskResult();
        long startTime = System.currentTimeMillis();
        List<Future<Integer>> results = new ArrayList<>();
        boolean isStop = businessSystemParamService.checkAutoOccupyStopTime();
        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
        int pageSize = config.getProperty("lts.OmsOccupyServiceTask.range", 2400);
        List<Long> orderIds = ocBOccupyTaskMapper.selectOcBOccupyTaskList(pageSize, TABLE_NAME, isStop);
        if (CollectionUtils.isEmpty(orderIds)) {
            result.setSuccess(true);
            result.setMessage("待寻源定时任务:无待寻源订单");
            return result;
        }
        List<List<Long>> lists = ListSplitUtil.averageAssign(orderIds, 24);
        for (List<Long> data : lists) {
            results.add(occupyOrderTaskThreadPoolExecutor.submit(new CallableOmsOccupyServiceTaskResult(data)));
        }
        Integer count = 0;
        for (Future<Integer> integerFuture : results) {
            try {
                Integer integer = integerFuture.get();
                count = count + integer;
            } catch (Exception e) {
                log.error(LogUtil.format("待寻源定时任务异常:{}", "待寻源定时任务异常"), Throwables.getStackTraceAsString(e));
            }
        }
        long endTime = System.currentTimeMillis();
        result.setSuccess(true);
        result.setMessage("待寻源定时任务:条数" + count + "耗时:" + (endTime - startTime));
        return result;
    }

    class CallableOmsOccupyServiceTaskResult implements Callable<Integer> {

        private final List<Long> data;

        public CallableOmsOccupyServiceTaskResult(List<Long> data) {
            this.data = data;
        }

        @Override
        public Integer call() throws Exception {
            new TLogRPCHandler().processProviderSide(new TLogLabelBean());
            //停发时间段内执行手动寻源task，不在停发时间段内执行所有寻源task
            if (CollectionUtils.isEmpty(data)) {
                return 0;
            }
            ocBOccupyTaskMapper.updateTaskStatus(data);
            List<OcBOrder> ocBOrderList = ocBOrderMapper.selectByIdsListOccupy(data);
            List<Long> ids = ListUtils.emptyIfNull(ocBOrderList).stream()
                    .map(OcBOrder::getId).collect(Collectors.toList());
            log.info(LogUtil.format("OmsOccupyServiceTask selectByIdsListOccupy orderIds:{},ids:{}",
                    "OmsOccupyServiceTask"), JSONObject.toJSONString(data), JSONObject.toJSONString(ids));
            if (CollectionUtils.isEmpty(ocBOrderList)) {
                return 0;
            }
            User rootUser = SystemUserResource.getRootUser();
            for (OcBOrder order : ocBOrderList) {
                sgOccupiedInventoryService.occupyOrder(order, rootUser);
            }
            return ocBOrderList.size();
        }
    }

}
