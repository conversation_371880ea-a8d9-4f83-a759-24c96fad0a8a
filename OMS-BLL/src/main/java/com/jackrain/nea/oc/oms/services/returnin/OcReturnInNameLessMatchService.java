package com.jackrain.nea.oc.oms.services.returnin;

import cn.hutool.core.util.ObjectUtil;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsStoInNoticeQueryRequest;
import com.burgeon.r3.sg.inf.model.result.oms.SgOmsStoInNoticeQueryResult;
import com.jackrain.nea.oc.oms.es.ES4ReturnOrder;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.IsGenInEnum;
import com.jackrain.nea.oc.oms.model.enums.MatchProcessState;
import com.jackrain.nea.oc.oms.model.enums.OcBorderListEnums;
import com.jackrain.nea.oc.oms.model.enums.ProReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnInBilStatus;
import com.jackrain.nea.oc.oms.model.enums.VirtualInStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.WmsWithdrawalState;
import com.jackrain.nea.oc.oms.model.relation.OcReturnInRelation;
import com.jackrain.nea.oc.oms.model.relation.RefundInRelation;
import com.jackrain.nea.oc.oms.model.table.OcBRefundIn;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInLog;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInProductItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderActual;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderLog;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.util.ThreadLocalUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2022/8/10
 */
@Slf4j
@Component
public class OcReturnInNameLessMatchService {

    @Autowired
    private SgRpcService sgRpcService;

    @Autowired
    private OcReturnInSupport returnInService;

    @Autowired
    private OcReturnInCommService commService;

    @Autowired
    private OcReturnInAdjustService ocReturnInAdjustService;

    @Autowired
    private OcRefundInMatchReturnService matchReturnService;

    @Autowired
    private OcRefundInMatchOrderService matchOrderService;
    @Autowired
    private OcBReturnOrderMapper returnOrderMapper;

    public MatchProcessState namelessMatchProcessing(RefundInRelation inRelation) {
        logStep("namelessMatchProcessing.match.start...");
        OcBRefundIn refundIn = inRelation.getRefundIn();
        String logisticNumber = refundIn.getLogisticNumber();
        if (StringUtils.isBlank(logisticNumber)) {
            return MatchProcessState.ADJUST;
        }
        boolean isVirtualUnIN = VirtualInStatusEnum.NOT.integer().equals(refundIn.getVirtualInStatus());
        if (isVirtualUnIN) {
            logStep("virtual status false");
            return MatchProcessState.ADJUST;
        }
        // match return
        List<Long> list = ES4ReturnOrder.queryIdsByLogisticsCode(logisticNumber);
        boolean isMatchedReturn = matchReturnProcessing(list, inRelation);
        if (isMatchedReturn) {
            logStep("nl.m.r.match return success");
            return MatchProcessState.MINUS;
        }
        // match order
        boolean isMatchedOrder = matchOrderProcessing(inRelation);
        if (isMatchedOrder) {
            logStep("nl.m.r.match order success");
            return MatchProcessState.MINUS;
        }
        return MatchProcessState.ADJUST;

    }

    /**
     * 循环匹配退单
     *
     * @param list
     * @param inRelation
     * @return
     */
    private boolean matchReturnProcessing(List<Long> list, RefundInRelation inRelation) {
        if (CollectionUtils.isEmpty(list)) {
            logStep("nl.m.r.return query es empty");
            return false;
        }
        logStep("nl.m.r.matchReturn.start...");
        for (Long key : list) {
            boolean matchStatus = matchReturn(key, inRelation);
            if (matchStatus) {
                // 匹配退换货单成功之后 重新填充入库通知单单号
                fillInNoticeNo(key);
                return true;
            }
        }
        return false;
    }

    private void fillInNoticeNo(Long returnId) {
        OcBReturnOrder ocBReturnOrder = returnOrderMapper.selectByid(returnId);
        if (ObjectUtil.isNull(ocBReturnOrder)) {
            return;
        }
        if (StringUtils.isNotEmpty(ocBReturnOrder.getStoInNoticesNo())) {
            return;
        }
        SgOmsStoInNoticeQueryRequest request = new SgOmsStoInNoticeQueryRequest();
        request.setReturnOrderBillNo(ocBReturnOrder.getBillNo());
        ValueHolderV14<List<SgOmsStoInNoticeQueryResult>> result = sgRpcService.queryInNotice(request);
        if (!result.isOK() || result.getData().isEmpty()) {
            return;
        }
        SgOmsStoInNoticeQueryResult inNoticeQueryResult = result.getData().get(0);
        OcBReturnOrder newReturn = new OcBReturnOrder();
        newReturn.setStoInNoticesNo(inNoticeQueryResult.getBillNo());
        newReturn.setStoInNoticesId(inNoticeQueryResult.getId());
        newReturn.setId(returnId);
        BaseModelUtil.makeBaseModifyField(newReturn, SystemUserResource.getRootUser());
        returnOrderMapper.updateById(newReturn);
    }

    /**
     * 循环匹配订单
     *
     * @param inRelation
     * @return
     */
    private boolean matchOrderProcessing(RefundInRelation inRelation) {
        if (inRelation.isHasAdjustedEle() || inRelation.isFluxWms()) {
            return matchOrderService.normalMatchOrderProcessor(inRelation, true);
        }
        return matchOrderService.normalMatchOrderProcessor(inRelation, false);
    }

    /**
     * 匹配退单
     *
     * @param returnId
     * @param inRelation
     * @return
     */
    private boolean matchReturn(Long returnId, RefundInRelation inRelation) {
        boolean inResult = false;
        try {
            OcReturnInRelation stockIn = matchReturnService.matchReturnProcessing(returnId, inRelation);
            if (stockIn == null) {
                return false;
            }
            if (!stockIn.isCurrentMatchFinished()) {
                logStep("nl.m.r.stockIn.isCurrentMatchFinished false");
                return false;
            }
            OcBReturnOrder item = stockIn.getItem();
            Integer proReturnStatus = item.getProReturnStatus();
            boolean isValid = proReturnStatus == null || ProReturnStatusEnum.WAIT.getVal().equals(proReturnStatus);
            if (!isValid) {
                logStep("nl.m.r.ProReturnStatus not wait");
                return false;
            }
            boolean isWhole = commService.statisticsProReturnStatus(stockIn.getItem(), stockIn.getSubItems());
            if (!isWhole) {
                logStep("nl.m.r.ProReturnStatus matched not whole");
                return false;
            }
            OcBRefundIn refundIn = inRelation.getRefundIn();
            OcBReturnOrder returnOrder = stockIn.getItem();
            String stoInNoticesNo = returnOrder.getStoInNoticesNo();
            if (StringUtils.isBlank(stoInNoticesNo)) {
                logStep("nl.m.r.no in notice");
                if (inRelation.isHasAdjustedEle() || inRelation.isFluxWms()) {
                    inRelation.setForbidMinus(true);
                    logStep("nl.m.r.has adjust, gen in notice,result,adjust bil start");
                    inResult = generateInNoticeBilAndInResultBil(inRelation, stockIn);
                    logStep("nl.m.r.has adjust, gen in notice,result,adjust bil end");
                    return inResult;
                }
                logStep("nl.m.r.no adjust, start gen notice");
                inResult = generateInNoticeBil(inRelation, stockIn);
                arrangeMatchItems(inRelation, stockIn, inResult);
                return inResult;
            } else {
                logStep("nl.m.r.has in notice");
                if (inRelation.isHasAdjustedEle() || inRelation.isFluxWms()) {
                    isSamePhyWarehouse(refundIn, returnOrder);
                    boolean cancelResult = wmsCancelInNoticeBil(returnOrder, true);
                    if (!cancelResult) {
                        return false;
                    }
                    inRelation.setForbidMinus(true);
                    logStep("nl.m.r.has adjust, gen in notice,result,adjust bil start");
                    inResult = generateInNoticeBilAndInResultBil(inRelation, stockIn);
                    logStep("nl.m.r.has adjust, gen in notice,result,adjust bil end");
                    return inResult;
                }

                boolean samePhyWarehouse = isSamePhyWarehouse(refundIn, returnOrder);
                if (samePhyWarehouse) {
                    logStep("same warehouse,start update bil");
                    inResult = updateMatchStatus(stockIn, refundIn);
                    arrangeMatchItems(inRelation, stockIn, inResult);
                    logStep("same warehouse,update bil end");
                    return inResult;
                }
                Integer isToWms = returnOrder.getIsTowms();
                if (WmsWithdrawalState.PASS.toInteger().equals(isToWms)) {
                    logStep("in pass wms");
                    return false;
                }
                boolean cancelResult = wmsCancelInNoticeBil(returnOrder, false);
                if (!cancelResult) {
                    return false;
                }
                inResult = generateInNoticeBil(inRelation, stockIn);
                arrangeMatchItems(inRelation, stockIn, inResult);
                return inResult;
            }
        } catch (Exception e) {
            logStep(OcReturnInSupport.expMsgFun.apply(e));
        }
        return inResult;
    }

    /**
     * 生成入库通知单
     *
     * @param inRelation
     * @param stockIn
     * @return
     */
    private boolean generateInNoticeBil(RefundInRelation inRelation, OcReturnInRelation stockIn) {
        logStep("start gen notice");
        User user = ThreadLocalUtil.users.get();
        OcBRefundIn refundIn = inRelation.getRefundIn();
        OcBReturnOrder returnOrder = stockIn.getItem();
        List<OcBReturnOrderRefund> subItems = stockIn.getSubMatchItems();
        isSamePhyWarehouse(refundIn, returnOrder);

        OcBReturnOrder newReturn = new OcBReturnOrder();
        newReturn.setId(returnOrder.getId());
        newReturn.setWmsBillNo(refundIn.getWmsBillNo());
        newReturn.setOcBRefundInId(refundIn.getId());
        newReturn.setCpCPhyWarehouseInId(returnOrder.getCpCPhyWarehouseInId());
        commService.assignReturnConfirmInfo(returnOrder, newReturn, user);

        OcBRefundIn newRefundIn = matchOrderService.returnWriteBackRefundIn(refundIn, returnOrder);
        newRefundIn.setBillStatus(ReturnInBilStatus.COMPLETE.val());

        Map<String, BigDecimal> skuQtyMap = new HashMap<>();
        List<OcBRefundInProductItem> newInItems = new ArrayList<>();
        for (OcBRefundInProductItem inItem : stockIn.getSubInMatchItems()) {
            OcBRefundInProductItem item = commService.preUpdateRefundInItem4FistMatch(inItem, newReturn.getId());
            item.setIsGenInOrder(IsGenInEnum.NO.integer());
            newInItems.add(item);
            commService.statisticInQty(skuQtyMap, inItem.getPsCSkuEcode(), inItem.getQty());
        }
        String skuMessage = commService.statisticMatchedSkuMessage(skuQtyMap);
        String message = String.format("匹配退货单[%d]成功, 匹配条码:%s", newReturn.getId(), skuMessage);
        OcBRefundInLog matchInLog = commService.buildRefundInLog("自动匹配退单", message, refundIn.getId(), user);
        String returnMessage = String.format("退货入库结果单[%d]入库匹配,生成入库通知单", refundIn.getId());
        OcBReturnOrderLog returnLog = commService.buildReturnOderLog("生成入库通知单", returnMessage, newReturn.getId(), user);

        List<OcBRefundInLog> refundLogs = new ArrayList();
        refundLogs.add(matchInLog);
        List<OcBReturnOrderLog> returnLogs = new ArrayList();
        returnLogs.add(returnLog);

        boolean genResult = atomicGenNoticeAndUpdateBil(returnOrder, subItems, newReturn, newRefundIn, newInItems, refundLogs, returnLogs);
        if (!genResult) {
            return false;
        }
        logStep("gen notice,update bil finish");
        commService.updateReturnOrderNoticeInfo(returnOrder);
        return true;
    }


    /**
     * gen in notice pass wms
     *
     * @param returnOrder
     * @param subItems
     * @param newReturn
     * @param newRefundIn
     * @param newInItems
     * @param refundLogs
     * @param returnLogs
     * @return
     */
    private boolean atomicGenNoticeAndUpdateBil(OcBReturnOrder returnOrder, List<OcBReturnOrderRefund> subItems,
                                                OcBReturnOrder newReturn, OcBRefundIn newRefundIn,
                                                List<OcBRefundInProductItem> newInItems, List<OcBRefundInLog> refundLogs,
                                                List<OcBReturnOrderLog> returnLogs) {
        User user = ThreadLocalUtil.users.get();
        try {
            commService.nameLessGenInNoticeAndUpdateBil(returnOrder, subItems, newRefundIn, newInItems, newReturn, refundLogs, returnLogs, user);
            return true;
        } catch (Exception ex) {
            logStep(OcReturnInSupport.expMsgFun.apply(ex));
        }
        return false;
    }


    /**
     * 生成入库通知单,结果单
     *
     * @param inRelation
     * @param stockIn
     * @return
     */
    private boolean generateInNoticeBilAndInResultBil(RefundInRelation inRelation, OcReturnInRelation stockIn) {
        User user = ThreadLocalUtil.users.get();
        OcBRefundIn refundIn = inRelation.getRefundIn();
        OcBReturnOrder returnOrder = stockIn.getItem();
        List<OcBReturnOrderRefund> subItems = stockIn.getSubMatchItems();
        isSamePhyWarehouse(refundIn, returnOrder);

        OcBReturnOrder newReturn = new OcBReturnOrder();
        commService.preUpdateReturn4FistMatch(returnOrder, newReturn, refundIn);
        newReturn.setProReturnStatus(ProReturnStatusEnum.WHOLE.getVal());
        newReturn.setReturnAmtList(null);
        newReturn.setReturnAmtActual(null);
        commService.assignReturnConfirmInfo(returnOrder, newReturn, user);

        List<OcBReturnOrderRefund> updateReturnItems = new ArrayList<>();
        for (OcBReturnOrderRefund subItem : subItems) {
            OcBReturnOrderRefund newReturnItem = commService.preUpdateReturnItem4FistMatch(subItem, true);
            updateReturnItems.add(newReturnItem);
        }

        OcBRefundIn newRefundIn = matchOrderService.returnWriteBackRefundIn(refundIn, returnOrder);
        newRefundIn.setBillStatus(ReturnInBilStatus.COMPLETE.val());
        Map<String, BigDecimal> skuQtyMap = new HashMap<>();
        List<OcBRefundInProductItem> newInItems = new ArrayList<>();
        for (OcBRefundInProductItem inItem : stockIn.getSubInMatchItems()) {
            OcBRefundInProductItem item = commService.preUpdateRefundInItem4FistMatch(inItem, returnOrder.getId());
            newInItems.add(item);
            commService.statisticInQty(skuQtyMap, inItem.getPsCSkuEcode(), inItem.getQty());
        }

        List<OcBReturnOrderActual> actualItems = commService.generateActualItems(inRelation, newReturn, user);
        String skuMessage = commService.statisticMatchedSkuMessage(skuQtyMap);
        String message = String.format("匹配退货单[%d]成功, 匹配条码:%s", newReturn.getId(), skuMessage);
        List<OcBRefundInLog> refundLogs = new ArrayList();
        OcBRefundInLog matchInLog = commService.buildRefundInLog("自动匹配退单", message, refundIn.getId(), user);
        refundLogs.add(matchInLog);

        String returnMsg = String.format("入库结果单[%d]入库完成，入库条码:%s", refundIn.getId(), skuMessage);
        OcBReturnOrderLog rLog = commService.buildReturnOderLog("退货单入库", returnMsg, newReturn.getId(), user);

        boolean genResult = generateSpecialNoticeAndResultBil(inRelation, stockIn, newReturn, updateReturnItems,
                newRefundIn, newInItems, actualItems, rLog, refundLogs);
        return genResult;
    }

    private boolean generateSpecialNoticeAndResultBil(RefundInRelation inRelation, OcReturnInRelation stockIn,
                                                      OcBReturnOrder newReturn, List<OcBReturnOrderRefund> updateReturnItems,
                                                      OcBRefundIn newRefundIn, List<OcBRefundInProductItem> newInItems,
                                                      List<OcBReturnOrderActual> actualItems, OcBReturnOrderLog rLog2,
                                                      List<OcBRefundInLog> refundLogs) {
        try {
            OcReturnInNameLessMatchService bean = ApplicationContextHandle.getBean(OcReturnInNameLessMatchService.class);
            bean.combineSpecialAdjustInProcessor(inRelation, stockIn, newReturn, updateReturnItems,
                    newRefundIn, newInItems, actualItems, rLog2, refundLogs);
            logStep("gen.special.in notice,result,minus adjust success");
            return true;
        } catch (Exception ex) {
            logStep(OcReturnInSupport.expMsgFun.apply(ex));
        }
        return false;
    }

    @Transactional(rollbackFor = Exception.class)
    public void combineSpecialAdjustInProcessor(RefundInRelation inRelation, OcReturnInRelation stockIn,
                                                OcBReturnOrder newReturn, List<OcBReturnOrderRefund> updateReturnItems,
                                                OcBRefundIn newRefundIn, List<OcBRefundInProductItem> newInItems,
                                                List<OcBReturnOrderActual> actualItems, OcBReturnOrderLog rLog2,
                                                List<OcBRefundInLog> refundLogs) {
        Date inTime = new Date();
        newReturn.setInTime(inTime);
        OcBReturnOrder item = stockIn.getItem();
        item.setInTime(inTime);
        returnInService.persistReturnNormalMatchResult(newRefundIn, newInItems, newReturn, updateReturnItems,
                actualItems, refundLogs, rLog2);
        arrangeMatchItems(inRelation, stockIn, true);
        ocReturnInAdjustService.generateCustomSpecialAdjust(inRelation, item);
    }

    private boolean updateMatchStatus(OcReturnInRelation stockIn, OcBRefundIn refundIn) {

        OcBReturnOrder returnOrder = stockIn.getItem();
        OcBRefundIn newRefundIn = matchOrderService.returnWriteBackRefundIn(refundIn, returnOrder);
        Long returnId = returnOrder.getId();
        Map<String, BigDecimal> cMap = new HashMap<>();
        List<OcBRefundInProductItem> newInItems = new ArrayList<>();
        for (OcBRefundInProductItem inItem : stockIn.getSubInMatchItems()) {
            OcBRefundInProductItem newInItem = commService.preUpdateRefundInItem4FistMatch(inItem, returnId);
            inItem.setIsGenInOrder(IsGenInEnum.NO.integer());
            newInItem.setIsGenInOrder(IsGenInEnum.NO.integer());
            newInItems.add(newInItem);
            commService.statisticInQty(cMap, inItem.getPsCSkuEcode(), inItem.getQty());
        }
        User user = ThreadLocalUtil.users.get();
        String skuMessage = commService.statisticMatchedSkuMessage(cMap);
        String message = String.format("匹配退货单[%d]成功, 匹配条码:%s", returnId, skuMessage);
        OcBRefundInLog matchInLog = commService.buildRefundInLog("自动匹配退单", message, refundIn.getId(), user);

        String returnMessage = String.format("退货入库结果单[%d]入库匹配, 无名件入库,已存在入库通知单,待正常入库", refundIn.getId());
        OcBReturnOrderLog returnLog = commService.buildReturnOderLog("无名件入库匹配", returnMessage, returnId, user);

        returnInService.persistNameLess(newRefundIn, newInItems, matchInLog, returnLog);
        return true;
    }


    private boolean wmsCancelInNoticeBil(OcBReturnOrder returnOrder, boolean isForbid2Wms) {
        logStep("wms cancel start");
        if (OcBorderListEnums.WmsCanceStatusEnum.RECALL_SUCCESS.getVal().equals(returnOrder.getWmsCancelStatus())) {
            logStep("wms success canceled, unnecessary cancel");
            return true;
        }
        User user = ThreadLocalUtil.users.get();
        ValueHolderV14 cancelResult = sgRpcService.cancelNoticeInBil(returnOrder, user);
        if (!cancelResult.isOK()) {
            logStep("wms cancel notice in fail");
            return false;
        }
        OcBReturnOrder newReturn = new OcBReturnOrder();
        newReturn.setId(returnOrder.getId());
        if (isForbid2Wms) {
            newReturn.setIsTowms(WmsWithdrawalState.NO.toInteger());
        }
        newReturn.setWmsCancelStatus(OcBorderListEnums.WmsCanceStatusEnum.RECALL_SUCCESS.getVal());
        newReturn.setStoInNoticesId(0L);
        newReturn.setStoInNoticesNo("");
        String returnMessage = String.format("WMS入库通知单撤回成功");
        OcBReturnOrderLog returnLog = commService.buildReturnOderLog("WMS撤回", returnMessage, returnOrder.getId(), user);

        commService.updateReturnAndLog(newReturn, returnLog);
        logStep("wms cancel success,update return end");
        return true;
    }

    private boolean isSamePhyWarehouse(OcBRefundIn refundIn, OcBReturnOrder returnOrder) {
        returnOrder.setCpCStoreId(refundIn.getInStoreId());
        returnOrder.setCpCStoreEcode(refundIn.getInStoreEcode());
        returnOrder.setCpCStoreEname(refundIn.getInStoreEname());
        Long warehouseId = refundIn.getCpCPhyWarehouseId();
        Long cpCPhyWarehouseInId = returnOrder.getCpCPhyWarehouseInId();
        logStep(String.format("isSamePhyWarehouse.refund:%d,returnIn:%d", warehouseId, cpCPhyWarehouseInId));
        if (warehouseId.equals(cpCPhyWarehouseInId)) {
            return true;
        }
        returnOrder.setCpCPhyWarehouseInId(warehouseId);
        return false;
    }

    private void arrangeMatchItems(RefundInRelation inRelation, OcReturnInRelation stockIn, boolean inResult) {
        if (inResult) {
            List<OcBRefundInProductItem> subInMatchItems = stockIn.getSubInMatchItems();
            inRelation.popMatchedItem(subInMatchItems);
            inRelation.setCurrentMatchReturnId(stockIn.getItem().getId());
        }
    }

    private void logStep(String express, Object... obs) {
        ThreadLocalUtil.logStepMsg.get().add(String.format(express, obs));
    }

}
