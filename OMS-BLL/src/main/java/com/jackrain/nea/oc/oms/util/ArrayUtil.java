package com.jackrain.nea.oc.oms.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import org.apache.commons.collections.CollectionUtils;

/**
 * @author: chenxiulou
 * @description:批量作废数组
 * @since: 2019-07-20
 * create at : 2019-07-20 18:00
 */
public class ArrayUtil {
    /**
     * @param param
     * @return com.alibaba.fastjson.JSONArray
     * @Descroption 获取作JSON数组
     * @Descroption 生成JSONArray
     * @Author: 陈秀楼
     * @Date 2019/3/8
     */
    public static JSONArray buildJsonArray(JSONObject param) {
        Long objid = param.getLong("objid");
        JSONArray jsonArray = param.getJSONArray("ids");
        if (jsonArray == null) {
            jsonArray = new JSONArray();
        }
        if (objid == null && CollectionUtils.isEmpty(jsonArray)) {
            throw new NDSException("请至少选择1条记录！");
        }
        //非空单对象加入json数组
        if (objid != null && objid > 0) {
            jsonArray = new JSONArray();
            jsonArray.add(objid.toString());
        }
        return jsonArray;
    }

    private ArrayUtil() {
    }
}
