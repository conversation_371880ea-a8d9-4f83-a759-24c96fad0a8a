package com.jackrain.nea.oc.oms.task.transfer;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.es.ES4IpStandPlatOrder;
import com.jackrain.nea.oc.oms.model.relation.IpStandplatOrderRelation;
import com.jackrain.nea.oc.oms.process.transfer.impl.normal.standplat.StandPlatformTransferOrderProcessImpl;
import com.jackrain.nea.oc.oms.services.IpStandplatOrderService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 通用转单定时任务
 *
 * @author: ming.fz
 * @since: 2019-07-2
 * create at : 2019-07-2 10:33
 */
@Component
@Slf4j
public class AutoStandplatTransferTask extends BaseR3Task implements IR3Task {

    @Autowired
    private IpStandplatOrderService ipStandplatOrderService;

    @Autowired
    private StandPlatformTransferOrderProcessImpl standPlatformTransferOrderProcessImpl;

    @Override
    @XxlJob("AutoStandplatTransferTask")
    public RunTaskResult execute(JSONObject params) {
        long start = System.currentTimeMillis();
        RunTaskResult result = new RunTaskResult();
        try {
            List<String> orderNoList = ES4IpStandPlatOrder.findTidByTransStatusAndSysRemark(0, DEFAULT_PAGE_SIZE);
            for (String orderNo : orderNoList) {
                IpStandplatOrderRelation StandplatOrderRelation =
                        this.ipStandplatOrderService.selectStandplatOrder(orderNo);
                if (StandplatOrderRelation != null) {
                    standPlatformTransferOrderProcessImpl.start(StandplatOrderRelation, false, SystemUserResource.getRootUser());
                }
            }
            result.setSuccess(true);
            result.setMessage("耗时：" + (System.currentTimeMillis() - start) + "ms");
        } catch (Exception ex) {
            log.error(LogUtil.format("AutoStandplatTransferTask,异常信息:{}", "AutoStandplatTransferTask"), Throwables.getStackTraceAsString(ex));
            result.setSuccess(false);
            result.setMessage(ex.getMessage());
        }

        return result;

    }
}
