package com.jackrain.nea.oc.oms.nums;

import com.jackrain.nea.oc.oms.model.enums.CardAutoVoidEnum;

/**
 * @ClassName RefundSourceEnum
 * @Description 已发货退款单 单据来源
 * <AUTHOR>
 * @Date 2023/3/21 14:30
 * @Version 1.0
 */
public enum RefundSourceEnum {

    REFUND(1, "退款"),
    REISSUE(2, "补发");

    private Integer code;
    private String msg;

    RefundSourceEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static String getMsgByCode(int val) {
        RefundSourceEnum[] enums = values();
        for (RefundSourceEnum refundSourceEnum : enums) {
            if (val == refundSourceEnum.getCode()) {
                return refundSourceEnum.getMsg();
            }
        }
        return null;
    }

    public String getMsg() {
        return msg;
    }

    public Integer getCode() {
        return code;
    }
}
