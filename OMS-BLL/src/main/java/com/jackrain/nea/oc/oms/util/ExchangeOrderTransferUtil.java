package com.jackrain.nea.oc.oms.util;

import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.cpext.model.ProvinceCityAreaInfo;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.cp.services.RegionNewService;
import com.jackrain.nea.ip.model.IpCTaobaoProductItem;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.model.enums.*;
import com.jackrain.nea.oc.oms.model.relation.Address;
import com.jackrain.nea.oc.oms.model.relation.OcBReturnOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderExchangeRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsTaobaoExchangeRelation;
import com.jackrain.nea.oc.oms.model.table.*;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.services.OcSaveChangingOrRefundingService;
import com.jackrain.nea.oc.oms.services.OmsOrderRecountAmountService;
import com.jackrain.nea.oc.oms.services.OmsRefundOrderService;
import com.jackrain.nea.oc.oms.services.refund.OmsReturnAfterUtil;
import com.jackrain.nea.oc.oms.services.refund.OmsReturnUtil;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.st.service.OmsStCShopStrategyService;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.util.OperateUserUtils;
import com.jackrain.nea.util.OrderAddressConvertUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2020/12/2 1:13 下午
 * @Version 1.0
 * 换货转换
 */
@Slf4j
@Component
public class ExchangeOrderTransferUtil {

    @Autowired
    private RegionNewService regionNewService;
    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private BuildSequenceUtil sequenceUtil;
    @Autowired
    private RegionNewService regionService;
    @Autowired
    private PropertiesConf propertiesConf;
    @Autowired
    private OmsReturnUtil omsReturnUtil;
    @Autowired
    private OmsOrderRecountAmountService omsOrderRecountAmountService;
    @Autowired
    private OmsStCShopStrategyService shopStrategyService;
    @Autowired
    private OcSaveChangingOrRefundingService ocSaveChangingOrRefundingService;

    @Autowired
    private OmsRefundOrderService omsRefundOrderService;

    /**
     * 淘宝换货中间表转换到退换货订单
     *
     * @param ipBTaobaoExchange 换货中间表数据
     * @param ocBOrder          原单数据
     * @return 换货主表数据
     */
    private OcBReturnOrder buildOcBReturnOrderFromTaobaoExchange(IpBTaobaoExchange ipBTaobaoExchange,
                                                                 OcBOrder ocBOrder, User user) {
        OcBReturnOrder returnOrder = new OcBReturnOrder();
        returnOrder.setId(ModelUtil.getSequence(TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER));
        returnOrder.setBillNo(sequenceUtil.buildReturnBillNo()); // 退单编号
        //淘宝平台换货单号
        returnOrder.setTbDisputeId(ipBTaobaoExchange.getDisputeId());
        //退款金额
        returnOrder.setReturnAmtActual(ipBTaobaoExchange.getPrice());
        returnOrder.setBillType(TaobaoReturnOrderExt.BillType.EXCHANGE.getCode());
        //原始订单编号
        returnOrder.setOrigOrderId(ocBOrder.getId());
        //卖家昵称
        returnOrder.setBuyerNick(ipBTaobaoExchange.getBuyerNick());
        //退款创建时间
        returnOrder.setReturnCreateTime(ipBTaobaoExchange.getCreated());
        //最后修改时间
        returnOrder.setLastUpdateTime(ipBTaobaoExchange.getModified());
        //退款状态，默认值:等待退货入ku
        //  TaobaoReturnOrderExt.ReturnStatus.WAIT_RETURN_LIBRARY.getCode()
        returnOrder.setReturnStatus(ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal());
        //退款说明
        returnOrder.setReturnDesc(ipBTaobaoExchange.getReason());
        //物流公司名称
        String buyerLogisticName = ipBTaobaoExchange.getBuyerLogisticName();
        returnOrder.setCpCLogisticsEname(buyerLogisticName);
        omsReturnUtil.setLogisticInfo(returnOrder, buyerLogisticName);
        //物流公司单号
        returnOrder.setLogisticsCode(ipBTaobaoExchange.getBuyerLogisticNo());
        //下载时间
        returnOrder.setCreationdate(new Date());
        //商品应退金额(对应商品明细的金额)
        //returnOrder.setReturnAmtActual(); //这个稍后(已加)
        //退还运费，默认0
        returnOrder.setReturnAmtShip(BigDecimal.ZERO);
        //退还其他费用，默认0
        returnOrder.setReturnAmtOther(BigDecimal.ZERO);
        //换货人姓名
        returnOrder.setReceiveName(ocBOrder.getReceiverName());
        //换货人手机
        returnOrder.setReceiveMobile(ipBTaobaoExchange.getBuyerPhone());
        //订单来源
        returnOrder.setOrdeSource(ocBOrder.getOrderSource());
        //邮编
        returnOrder.setReceiveZip(ocBOrder.getReceiverZip());
        //售后/售中
        returnOrder.setReturnPhase(ipBTaobaoExchange.getRefundPhase());
        //发货仓库
        returnOrder.setCpCPhyWarehouseId(ocBOrder.getCpCPhyWarehouseId());
        //原平台单
        returnOrder.setTid(ocBOrder.getTid());
        //平台
        returnOrder.setPlatform(ocBOrder.getPlatform());
        //订单来源
        returnOrder.setOrdeSource(ocBOrder.getOrderSource());
        //是否传AG默认否
        returnOrder.setIsToag(AGStatusEnum.INIT.getVal());
        //是否生成调拨单，默认0
        returnOrder.setIsTransfer(0);
        //是否生成零售，默认0
        returnOrder.setIsTodrp(0);
        //退单状态，默认20
        //TaobaoReturnOrderExt.ReturnStatus.WAIT_RETURN_LIBRARY.getCode()
        returnOrder.setReturnStatus(ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal());
        //是否手工新增，默认0
        returnOrder.setIsAdd(0);
        //虚拟入库状态，默认0
        returnOrder.setInventedStatus(0);
        returnOrder.setProReturnStatus(ProReturnStatusEnum.WAIT.getVal());
        //是否原退，默认0
        returnOrder.setIsRefund(0);
        //是否确认收货，默认0
        returnOrder.setIsReceiveConfirm(0);
        //WMS撤回状态，默认0
        returnOrder.setWmsCancelStatus(0);
        //强制入库，默认0
        returnOrder.setIsForce(0);
        //是否手工审核，默认0
        returnOrder.setIsManualAudit(0);
        //是否传WMS
        returnOrder.setIsTowms(0);
        //是否入仓成功
        returnOrder.setIsInstorage(0);
        //退款原因
        //returnOrder.setRemark(ipBTaobaoExchange.getReason());
        //店铺名称
        returnOrder.setSellerNick(ipBTaobaoExchange.getSellerNick());
        //店铺标题
        returnOrder.setCpCShopTitle(ocBOrder.getCpCShopTitle());
        returnOrder.setCpCShopId(ocBOrder.getCpCShopId());
        returnOrder.setCpCShopEcode(ocBOrder.getCpCShopEcode());
        returnOrder.setReturnAmtList(BigDecimal.ZERO);

        returnOrder.setOrigSourceCode(ocBOrder.getSourceCode()); //原始平台单号
        this.returnOrderAddress(returnOrder, ipBTaobaoExchange.getBuyerAddress());
        //returnOrder.setReceiveAddress(ipBTaobaoExchange.getBuyerAddress());
        returnOrder.setIsReserved(1);
        //取值为发货实体仓档案中关联的退货待检实体仓仓库
        // @20200707 修改退货仓取值逻辑
        //this.selectReturnCPhyWarehouse(ocBOrder.getCpCPhyWarehouseId(), returnOrder);
        // @20200721 设置个默认值
        returnOrder.setIsNeedToWms(Long.valueOf(OcBorderListEnums.YesOrNoEnum.IS_NO.getVal()));
        this.setCpCPhyWarehouseInIdForExchange(ocBOrder, returnOrder);
        //加入“空运单号延迟推单有效时间”字段 在保存的方法里统一处理 2021-11-10
//        returnOrder.setPushDelayTime(ocSaveChangingOrRefundingService.PushDelayTime(returnOrder));
        //赋值业务类型
        StCBusinessType stCBusinessType = omsRefundOrderService.queryReturnOrderType(ocBOrder);
        returnOrder.setBusinessTypeId(stCBusinessType.getId());
        returnOrder.setBusinessTypeCode(stCBusinessType.getEcode());
        returnOrder.setBusinessTypeName(stCBusinessType.getEname());
        OperateUserUtils.saveOperator(returnOrder, user);
        return returnOrder;
    }


    private List<OcBReturnOrderRefund> buildReturnOrderItemFromExchange(List<OcBOrderItem> orderItems, IpBTaobaoExchange taobaoExchange,
                                                                        OcBReturnOrder ocBReturnOrder, User user) {
        List<OcBReturnOrderRefund> returnOrderRefunds = new ArrayList<>();
        BigDecimal sumQtyRefund = BigDecimal.ZERO;
        //换货数量
        BigDecimal qty = new BigDecimal(taobaoExchange.getQty());
        orderItems = orderItems.stream().filter(p -> p.getProType() != SkuType.NO_SPLIT_COMBINE).collect(Collectors.toList());
        for (OcBOrderItem orderItem : orderItems) {
            BigDecimal qtyReturnApply = orderItem.getQtyReturnApply();
            BigDecimal qty1 = orderItem.getQty();
            qtyReturnApply = qtyReturnApply == null ? BigDecimal.ZERO : qtyReturnApply;
            if (qtyReturnApply.compareTo(qty1) >= 0) {
                continue;
            }
            Long proType = orderItem.getProType();
            if (SkuType.GIFT_PRODUCT != proType && SkuType.COMBINE_PRODUCT != proType) {
                if (qty.compareTo(BigDecimal.ZERO) <= 0) {
                    continue;
                }
            }
            OcBReturnOrderRefund ocBReturnOrderRefund = new OcBReturnOrderRefund();
            ocBReturnOrderRefund.setId(ModelUtil.getSequence(TaobaoReturnOrderExt.TABLENAME_OCRETURNORDERREFUND));
            ocBReturnOrderRefund.setPsCSkuEcode(orderItem.getPsCSkuEcode()); //退货编码
            ocBReturnOrderRefund.setSkuSpec(orderItem.getSkuSpec()); ////商品规格
            ocBReturnOrderRefund.setPrice(orderItem.getPrice()); //商品标准价
            ocBReturnOrderRefund.setAmtAdjust(orderItem.getAdjustAmt());  //调整金额
            ocBReturnOrderRefund.setQtyIn(BigDecimal.ZERO);  //入库数量
            ocBReturnOrderRefund.setQtyCanRefund(orderItem.getQty());//可退数量
            ocBReturnOrderRefund.setIsReturn(0); //退换货标识  默认 0
            ocBReturnOrderRefund.setPsCSkuId(orderItem.getPsCSkuId()); //条码id
            ocBReturnOrderRefund.setBarcode(orderItem.getBarcode()); //国标码
            ocBReturnOrderRefund.setPsCProId(orderItem.getPsCProId()); //商品id
            ocBReturnOrderRefund.setPsCProEcode(orderItem.getPsCProEcode());
            ocBReturnOrderRefund.setPsCSkuEcode(orderItem.getPsCSkuEcode()); //商品编码
            ocBReturnOrderRefund.setPsCProEname(orderItem.getPsCProEname());  //商品名称
            ocBReturnOrderRefund.setOid(orderItem.getOoid());  //子订单id
            ocBReturnOrderRefund.setAmtRefundSingle(orderItem.getRealAmt().divide(orderItem.getQty(), 4, BigDecimal.ROUND_HALF_DOWN));
            //商品标记
            ocBReturnOrderRefund.setProductMark("1");
            ocBReturnOrderRefund.setOcBReturnOrderId(ocBReturnOrder.getId());
            ocBReturnOrderRefund.setPsCSizeEcode(orderItem.getPsCSizeEcode());
            ocBReturnOrderRefund.setPsCSizeEname(orderItem.getPsCSizeEname());
            ocBReturnOrderRefund.setPsCSizeId(orderItem.getPsCSizeId());
            ocBReturnOrderRefund.setSex(orderItem.getSex());
            ocBReturnOrderRefund.setPriceList(orderItem.getPriceList());
            ocBReturnOrderRefund.setPsCClrEcode(orderItem.getPsCClrEcode());
            ocBReturnOrderRefund.setPsCClrEname(orderItem.getPsCClrEname());
            ocBReturnOrderRefund.setPsCClrId(orderItem.getPsCClrId());
            ocBReturnOrderRefund.setTid(orderItem.getTid());


            BigDecimal refundQty = orderItem.getQty();
            if (qty.compareTo(refundQty) <= 0) {
                refundQty = qty;
            }
            if (SkuType.GIFT_PRODUCT == proType || SkuType.COMBINE_PRODUCT == proType) {
                BigDecimal qtyGroup = orderItem.getQtyGroup();
                BigDecimal singleQty = orderItem.getQty().divide(qtyGroup, 0, BigDecimal.ROUND_HALF_DOWN);
                refundQty = singleQty.multiply(qty);

            }
            sumQtyRefund = sumQtyRefund.add(refundQty);
            BigDecimal realAmt = orderItem.getRealAmt(); //成交金额
            BigDecimal amtRefund = realAmt.divide(orderItem.getQty(), 4, BigDecimal.ROUND_HALF_DOWN).multiply(qty);
            ocBReturnOrderRefund.setQtyRefund(refundQty); //申请数量
            ocBReturnOrderRefund.setAmtRefund(amtRefund); //退还金额
            if (SkuType.GIFT_PRODUCT != proType && SkuType.COMBINE_PRODUCT != proType) {
                qty = qty.subtract(refundQty);
            }
            // @20200714 加关联ID
            ocBReturnOrderRefund.setOcBOrderId(orderItem.getOcBOrderId());
            ocBReturnOrderRefund.setOcBOrderItemId(orderItem.getId());
            //计算结算金额和结算单价
            //setPriceAndTotPrice(orderItem, ocBReturnOrderRefund);
            OperateUserUtils.saveOperator(ocBReturnOrderRefund, user);
            returnOrderRefunds.add(ocBReturnOrderRefund);
        }
        ocBReturnOrder.setQtyInstore(sumQtyRefund);
        return returnOrderRefunds;
    }


    /**
     * 构建换货商品的明细信息
     *
     * @return
     */
    private List<OcBReturnOrderExchange> buildExchangeOrderItemFromExchange(ProductSku productSku, IpBTaobaoExchange taobaoExchange,
                                                                            OcBReturnOrder ocBReturnOrder, User user, List<OcBReturnOrderRefund> orderRefundList) {
        List<OcBReturnOrderExchange> exchangeOrderItem = new ArrayList<>();
        //申请退货明细单行实际成交价格之和
        BigDecimal amtRefund = orderRefundList.stream().map(OcBReturnOrderRefund::getAmtRefund).
                reduce(BigDecimal.ZERO, BigDecimal::add);
        Long qty = taobaoExchange.getQty();
        OcBReturnOrderExchange ocBReturnOrderExchange = new OcBReturnOrderExchange();
        ocBReturnOrderExchange.setId(ModelUtil.getSequence(TaobaoReturnOrderExt.TABLENAME_OCRETURNORDEREXCHAGE));
        ocBReturnOrderExchange.setPsCSkuEcode(productSku.getSku()); //退货编码
        ocBReturnOrderExchange.setOcBReturnOrderId(ocBReturnOrder.getId());
        //商品规格
        ocBReturnOrderExchange.setSkuSpec(productSku.getSkuSpec());
        //商品标准价
        ocBReturnOrderExchange.setPrice(productSku.getPrice());  //取得就是吊牌价
        //调整金额
        ocBReturnOrderExchange.setAmtAdjust(BigDecimal.ZERO);
        ocBReturnOrderExchange.setQtyIn(BigDecimal.ZERO);   //入库数量
        ocBReturnOrderExchange.setIsReturn(1); //退换货标识  默认 0
        ocBReturnOrderExchange.setPsCSkuId(productSku.getId());   //条码id
        ocBReturnOrderExchange.setPsCSkuEcode(productSku.getSkuEcode());
        ocBReturnOrderExchange.setBarcode(productSku.getBarcode69()); //国标码
        ocBReturnOrderExchange.setPsCProId(productSku.getProdId());  //商品id
        ocBReturnOrderExchange.setPsCProEcode(productSku.getProdCode()); //商品编码
        ocBReturnOrderExchange.setPsCProEname(productSku.getName()); //商品名称
        ocBReturnOrderExchange.setOid(String.valueOf(taobaoExchange.getBizOrderId()));
        ocBReturnOrderExchange.setQtyExchange(new BigDecimal(qty));
        ocBReturnOrderExchange.setPsCSizeEcode(productSku.getSizeCode());
        ocBReturnOrderExchange.setPsCSizeEname(productSku.getSizeName());
        ocBReturnOrderExchange.setPsCSizeId(productSku.getSizeId());

        ocBReturnOrderExchange.setPsCClrEcode(productSku.getColorCode());
        ocBReturnOrderExchange.setPsCClrEname(productSku.getColorName());
        ocBReturnOrderExchange.setPsCClrId(productSku.getColorId());
        ocBReturnOrderExchange.setPriceList(productSku.getPricelist());
        ocBReturnOrderExchange.setSex(productSku.getSex());
//        IpCTaobaoProductItem exchangeTaobaoProductItem = cpRpcService.selectIpCTaobaoProductItemBySkuId(taobaoExchange.getExchangeSku());
//        if (exchangeTaobaoProductItem != null) {
//            ocBReturnOrderExchange.setAmtRefund(exchangeTaobaoProductItem.getPrice());
//        }
        ocBReturnOrderExchange.setAmtRefund(amtRefund);
        //退货计算结算单价结算金额
        //setExchangePriceAndTotPrice(orderItems.get(0), ocBReturnOrderExchange);
        OperateUserUtils.saveOperator(ocBReturnOrderExchange, user);
        exchangeOrderItem.add(ocBReturnOrderExchange);
        return exchangeOrderItem;
    }

    /**
     * 生成换货订单
     *
     * @param ocBOrder 一个原单信息
     * @return
     */
    private OcBOrder buildOcBOrderFromIpTaobaoOrder(OcBOrder ocBOrder, OcBReturnOrder ocBReturnOrder, User user, IpBTaobaoExchange ipBTaobaoExchange) {
        OcBOrder order = new OcBOrder();
        //id自增长
        order.setId(sequenceUtil.buildOrderSequenceId());
        order.setModifierename(SystemUserResource.ROOT_USER_NAME);
        order.setOwnerename(SystemUserResource.ROOT_USER_NAME);
        order.setVersion(0L);

        BaseModelUtil.initialBaseModelSystemField(order);

        //调整金额
        //自动审核状态
        order.setAutoAuditStatus(0);
        //单据编号
        order.setBillNo(sequenceUtil.buildBillNo());

        //买家留言
        //到付代收金额. 如果是支付方式=到付，则赋值订单金额
        order.setCodAmt(BigDecimal.ZERO);
        //代销结算金额. 默认为0
        order.setConsignAmt(BigDecimal.ZERO);
        //代销运费. 默认为0
        order.setConsignShipAmt(BigDecimal.ZERO);
        //配送费用。如果为空，则赋值0.
        order.setShipAmt(ocBReturnOrder.getShipAmt() == null ? BigDecimal.ZERO : ocBReturnOrder.getShipAmt());
        order.setReceiverMobile(ocBReturnOrder.getReceiveMobile());
        //发货实体仓. 赋值null，后续在分配物流中使用
        order.setCpCPhyWarehouseId(null);
        //下单店铺id.
        order.setCpCShopId(ocBOrder.getCpCShopId());
        order.setCpCShopTitle(ocBOrder.getCpCShopTitle());
        order.setGwSourceGroup(ocBOrder.getGwSourceGroup());
        order.setOrderSourcePlatformEcode(ocBOrder.getOrderSourcePlatformEcode());
        //下单店仓编码. 到平台店铺信息表中获取下单店仓字段ID值；
//        order.setCpCStoreEcode(ocBOrder.getCpCStoreEcode());
        //下单店仓名称. 到平台店铺信息表中获取下单店仓字段ID值；
//        order.setCpCStoreEname(ocBOrder.getCpCStoreEname());
        //下单店仓id. 到平台店铺信息表中获取下单店仓字段ID值；
//        order.setCpCStoreId(ocBOrder.getCpCStoreId());
        //下单店仓id. 到平台店铺信息表中获取下单卖家店铺名称
        order.setCpCShopSellerNick(ocBOrder.getCpCShopSellerNick());
        order.setCpCShopEcode(ocBOrder.getCpCShopEcode());
        //初始平台单号（确定唯一）
        order.setTid(ocBOrder.getTid());
        order.setMergeSourceCode(ocBOrder.getTid());
        order.setIsCalcweight(0);
        //是否组合订单
        //是否生成开票通知。现在赋值为N。占用订单后再进行赋值
        order.setIsGeninvoiceNotice(0);
        // 是否已给物流。占单后再进行赋值
        // order.setIsGiveLogistic(0);
        // 是否有赠品.0.否。计算完赠品策略赋值
        order.setIsHasgift(0);
        //包含预售商品
        //是否退款中
        order.setIsInreturning(0);
        //是否已经挂起
        order.setIsInterecept(0);
        //是否虚拟订单。现在赋值为N
        order.setIsInvented(0);
        //京仓订单
        order.setIsJcorder(0);
        //实缺标记
//        order.setIsLackstock(0);
        //是否合并订单 默认0不合并
        order.setIsMerge(0);
        //是否拆分订单
        order.setIsSplit(0);
        //是否生成调拨零售
        // order.setIsTodrp(0);
        //应收平台金额（京东）
        order.setJdReceiveAmt(BigDecimal.ZERO);
        //京东结算金额
        order.setJdSettleAmt(BigDecimal.ZERO);
        //物流成本.需要计算成本。默认为0
        order.setLogisticsCost(BigDecimal.ZERO);
        //合并单据后生成的订单，对原始数据进行修改
        order.setMergeOrderId(null);
        //订单占单状态
        order.setOccupyStatus(0);
        //操作费.默认为0
        // order.setOperateAmt(BigDecimal.ZERO);
        //下单时间
        order.setOrderDate(new Date());
        //订单优惠金额。
        //订单状态. 默认状态为50
        order.setOrderStatus(OmsOrderStatus.ORDER_DEFAULT.toInteger());
        //订单标签
        order.setOrderTag(null);
        //订单类型
        order.setOrderType(OrderTypeEnum.EXCHANGE.getVal());
        //原始订单号。默认空
        order.setOrigOrderId(ocBOrder.getId());
        //出库状态. WMS后调用,已出库未出库,现在没有用
        order.setOutStatus(null);
        //付款时间
        order.setPayTime(new Date());
        //支付方式（淘宝，天猫没有货到付款类型。PRD中写到COD=货到付款的判断可用忽视）
        order.setPayType(OmsPayType.ON_LINE_PAY.toInteger());
        //平台
        order.setPlatform(PlatFormEnum.TAOBAO.getCode());
        // 双11的预售状态。现在暂时赋值0
        order.setDouble11PresaleStatus(0);
        // 已收金额。如果为空，则赋值0.
        // 买家收货详细地址
        order.setReceiverAddress(ocBReturnOrder.getReceiveAddress());
        //买家所在省
        String provinceName = ocBReturnOrder.getReceiverProvinceName();
        //买家所在市
        String cityName = ocBReturnOrder.getReceiverCityName();
        //买家所在区ID。
        String areaName = ocBReturnOrder.getReceiverAreaName();
        ProvinceCityAreaInfo provinceCityAreaInfo = this.regionService.selectProvinceCityAreaInfo(provinceName,
                cityName, areaName);
        if (provinceCityAreaInfo != null && provinceCityAreaInfo.getProvinceInfo() != null) {
            order.setCpCRegionProvinceId(provinceCityAreaInfo.getProvinceInfo().getId());
            order.setCpCRegionProvinceEcode(provinceCityAreaInfo.getProvinceInfo().getCode());
            order.setCpCRegionProvinceEname(provinceName);
        } else {
            order.setCpCRegionCityId(null);
            order.setCpCRegionAreaEcode(null);
        }

        if (provinceCityAreaInfo != null && provinceCityAreaInfo.getCityInfo() != null) {
            order.setCpCRegionCityId(provinceCityAreaInfo.getCityInfo().getId());
            order.setCpCRegionCityEcode(provinceCityAreaInfo.getCityInfo().getCode());
            order.setCpCRegionCityEname(provinceCityAreaInfo.getCityInfo().getName());
        } else {
            order.setCpCRegionCityId(null);
            order.setCpCRegionAreaEcode(null);
        }
        if (provinceCityAreaInfo != null && provinceCityAreaInfo.getAreaInfo() != null) {
            order.setCpCRegionAreaId(provinceCityAreaInfo.getAreaInfo().getId());
            order.setCpCRegionAreaEcode(provinceCityAreaInfo.getAreaInfo().getCode());
            order.setCpCRegionAreaEname(provinceCityAreaInfo.getAreaInfo().getName());
        } else {
            order.setCpCRegionAreaId(null);
            order.setCpCRegionAreaEcode(null);
        }
        order.setReceiverName(ocBReturnOrder.getReceiveName());
        order.setReceiverPhone(ocBReturnOrder.getReceivePhone());
        //货到付款服务费。如果为空，则赋值0.
        //配送费用。如果为空，则赋值0.
        //平台单号信息
        order.setSourceCode(ocBOrder.getTid());
        //拆分原单单号
        order.setSplitOrderId(null);
        //订单补充信息
        order.setSuffixInfo(ocBReturnOrder.getId() + "-TC");
        //系统备注
        order.setSysremark(null);
        //淘宝店铺编号（星盘使用）
        order.setTbStorecode(null);
        //下单用户
        order.setUserId(null);
        order.setPlatform(ocBOrder.getPlatform());
        order.setUserNick(ocBReturnOrder.getBuyerNick());
        //版本信息
        order.setVersion(0L);
        order.setWeight(BigDecimal.ZERO);
        //wms撤回状态调用WMS撤回是否成功。1=成功；2=失败
        order.setWmsCancelStatus(0);
        //仓储状态（拣货中，已打印，已装箱）
        //order.setWmsStatus(null);
        //是否插入核销流水
        // order.setIsWriteoff(0);
        //出库状态
        order.setOutStatus(1);
        order.setOrigReturnOrderId(ocBReturnOrder.getId());
        order.setIsExchangeNoIn(1L);
        if (isSetOaid()) {
            order.setOaid(ipBTaobaoExchange.getOaid());
        }
        order.setDisputeId(ipBTaobaoExchange.getDisputeId());
        OperateUserUtils.saveOperator(order, user);
        return order;
    }

    /**
     * 换货订单明细
     *
     * @param ocBOrder       换货订单主表信息
     * @param taobaoExchange 换货中间表数据
     * @param prodSku        换货的sku信息
     * @return
     */
    private OcBOrderItem buildOrderItemFromTaobaoOrderItemNew(OcBOrder ocBOrder, IpBTaobaoExchange taobaoExchange,
                                                              ProductSku prodSku, User user) {
        OcBOrderItem item = new OcBOrderItem();
        item.setId(sequenceUtil.buildOrderItemSequenceId());
        item.setModifierename(SystemUserResource.ROOT_USER_NAME);
        item.setOwnerename(SystemUserResource.ROOT_USER_NAME);
        item.setVersion(0L);

        BaseModelUtil.initialBaseModelSystemField(item);

        //活动编号. 默认赋值为null
        item.setActiveId(null);

        //优惠金额.若为组合商品的值，则【淘宝订单中间表】明细表的“优惠金额”*【组合商品】数量*【组合商品】价格比例，

        //退货金额.默认为0
        item.setAmtRefund(BigDecimal.ZERO);
        //使用积分
        item.setBuyerUsedIntegral(0L);
        //分销价格。默认为0
        item.setDistributionPrice(BigDecimal.ZERO);
        //组合名称
        item.setGroupName(null);
        //是否已经占用库存
        item.setIsAllocatestock(0);
        //买家是否已评价
        item.setIsBuyerRate(0);
        //是否是赠品
        item.setIsGift(0);
        //实缺标记
        item.setIsLackstock(0);//商品数字编
        //订单编号
        item.setOcBOrderId(ocBOrder.getId());
        item.setOoid(String.valueOf(taobaoExchange.getBizOrderId()));
        item.setPrice(prodSku.getPrice());
        item.setExchangeBillNo(taobaoExchange.getDisputeId());
        // 成交单价
        //标准价。【淘宝订单中间表】明细表的“标准价”
        // item.setPriceList(taobaoOrderItem.getPrice() == null ? BigDecimal.ZERO : taobaoOrderItem.getPrice());
        //taobaoOrderItem.getPrice() == null ? BigDecimal.ZERO : taobaoOrderItem.getPrice()
        item.setPriceList(prodSku.getPricelist());
        //数量
        item.setQty(BigDecimal.valueOf(taobaoExchange.getQty()));
        //已退数量。默认为0
        item.setQtyRefund(BigDecimal.ZERO);
        //单行实际成交金额. s.price * s.num - s.discount_fee + s.adjust_fee- part_mjz_discount
        item.setRealAmt(prodSku.getPricelist().multiply(item.getQty()));
        //退款状态
        // 如果是退款完成，或者是交易关闭 状态=6
        item.setRefundStatus(0);

        //规格。商品条码. normsdetailnames
        //条码id
        //标准重量。商品条码. weight
        //条码编码。
        //若为组合商品的值，则【淘宝订单中间表】明细表的在【组合商品】中对应的实际商品编码（商品档案中存在，且状态为已启用），则【淘宝订单中间表】明细表的“商品编码”
        initialTaobaoOrderItem(prodSku, item);

        //库位。不用赋值
//        item.setStoreSite(null);

        item.setTid(ocBOrder.getTid());
        item.setIsExchangeItem(1);
        item.setReturnOrderId(ocBOrder.getOrigReturnOrderId());
        OperateUserUtils.saveOperator(item, user);
        return item;
    }


    /**
     * 初始化TaobaoOrderItem内容
     * 2019-07-30 组合福袋商品修改
     *
     * @param prodSku 淘宝中间表数据
     * @param item    需要赋值的taobaoorderItem
     */

    private void initialTaobaoOrderItem(ProductSku prodSku, OcBOrderItem item) {
        if (prodSku != null) {
            item.setPsCProId(prodSku.getProdId());
            // ProECode
            item.setPsCProEcode(prodSku.getProdCode());
            item.setPsCSkuId(prodSku.getId());
            item.setSex(prodSku.getSex());
            //2019-08-30吊牌价改为取商品表数据
            item.setPriceTag(prodSku.getPricelist()); //吊牌价
            item.setPsCClrEcode(prodSku.getColorCode());
            item.setPsCClrEname(prodSku.getColorName());
            item.setPsCClrId(prodSku.getColorId());
            item.setPsCSizeEcode(prodSku.getSizeCode());
            item.setPsCSizeEname(prodSku.getSizeName());
            item.setPsCSizeId(prodSku.getSizeId());
            item.setPsCProMaterieltype(prodSku.getMaterialType());
            item.setStandardWeight(prodSku.getWeight());
            item.setSkuSpec(prodSku.getSkuSpec());
            item.setProType(NumberUtils.toLong(prodSku.getSkuType() + ""));
            item.setPsCSkuPtEcode(prodSku.getSkuEcode());
            item.setSex(prodSku.getSex());
            item.setPsCProEname(prodSku.getName());
            item.setMDim4Id(prodSku.getMDim4Id());
            item.setMDim6Id(prodSku.getMDim6Id());
            if ("Y".equals(prodSku.getIsEnableExpiry())) {
                item.setIsEnableExpiry(1);
            } else {
                item.setIsEnableExpiry(0);
            }
            // 2019-06-16 易邵峰修改：增加参数判断是否需要进行对SKU进行大写转换。目的是为了统一SKU
            String psSkuEcode = prodSku.getSkuEcode();
            if (checkIsNeedTransferSkuUpperCase()) {
                psSkuEcode = StringUtils.upperCase(psSkuEcode);
            }
            if (prodSku.getSkuType() == SkuType.COMBINE_PRODUCT
                    || prodSku.getSkuType() == SkuType.GIFT_PRODUCT) {
                //为福袋或者组合商品
                item.setPsCSkuEcode(psSkuEcode); //虚拟条码
                //item.setPsCProEname(taobaoOrderItem.getTitle()); //虚拟条码商品名称取中间表的名称
                //由于数据库做了对尺寸code和商品code做了非空限制
                item.setPsCSizeEcode(psSkuEcode);
                item.setPsCProEcode(psSkuEcode);
                item.setQtyGroup(item.getQty()); //组合商品数量
                item.setProType(NumberUtils.toLong(SkuType.NO_SPLIT_COMBINE + ""));

            } else {
                item.setPsCSkuEcode(psSkuEcode);
            }
        } else {
            item.setSkuSpec(null);
            item.setStandardWeight(BigDecimal.ZERO);
        }
    }


    /**
     * 是否需要转换成大写
     * 乔丹项目中：SAP系统存储的SKU部分有小写。为了统一，库里存储的全部为大写。因此在转单的时候强制转换成大写。
     *
     * @return true
     */
    private boolean checkIsNeedTransferSkuUpperCase() {
        try {
            String value = propertiesConf.getProperty("r3.oc.oms.transfer.sku.toupper", "true");
            return StringUtils.equalsIgnoreCase(value, "true");
        } catch (Exception ex) {
            return true;
        }
    }


    /**
     * 封装退换货单的省市区及id
     *
     * @param ocBReturnOrder
     * @param address
     */
    public void returnOrderAddress(OcBReturnOrder ocBReturnOrder, String address) {
        if (StringUtils.isNotEmpty(address)) {
            int i1 = StringUtils.ordinalIndexOf(address, "， ", 2);
            if (i1 != -1) {
                address = address.substring(i1 + 1);
            }
        }
        Address address1 = this.addressResolutionNew(address);
        if (address1 != null) {
            String province = address1.getProvince(); //省
            String city = address1.getCity(); //市
            String county = address1.getCounty(); //区
            ocBReturnOrder.setReceiverProvinceName(province);
            ocBReturnOrder.setReceiverCityName(city);
            ocBReturnOrder.setReceiverAreaName(county);
            ocBReturnOrder.setReceiveAddress(address1.getAddr());
            ProvinceCityAreaInfo provinceCityAreaInfo =
                    regionNewService.selectProvinceCityAreaInfo(province, city, county);
            if (provinceCityAreaInfo != null && provinceCityAreaInfo.getProvinceInfo() != null) {
                ocBReturnOrder.setReceiverProvinceId(provinceCityAreaInfo.getProvinceInfo().getId());
            }
            if (provinceCityAreaInfo != null && provinceCityAreaInfo.getCityInfo() != null) {
                ocBReturnOrder.setReceiverCityId(provinceCityAreaInfo.getCityInfo().getId());
            }
            if (provinceCityAreaInfo != null && provinceCityAreaInfo.getAreaInfo() != null) {
                ocBReturnOrder.setReceiverAreaId(provinceCityAreaInfo.getAreaInfo().getId());
            }
        }
    }


    /**
     * 从策略取值
     *
     * @param ocBOrder
     * @param returnOrder
     */
    private void setCpCPhyWarehouseInIdForExchange(OcBOrder ocBOrder, OcBReturnOrder returnOrder) {
        if (Objects.nonNull(ocBOrder) && Objects.nonNull(returnOrder)) {
            // 查询策略
            OmsStCShopStrategyService shopStrategyService = ApplicationContextHandle.getBean(OmsStCShopStrategyService.class);
            StCShopStrategyDO shopStrategy = shopStrategyService.selectOcStCShopStrategy(ocBOrder.getCpCShopId());

            if (Objects.nonNull(shopStrategy)) {
                Integer isMultiReturnWarehouse = shopStrategy.getIsMultiReturnWarehouse();

                // @20200803 bug-prd-淘宝换货入库仓未设置问题：是否有多个退货仓库默认为空引起
                if (Objects.isNull(isMultiReturnWarehouse) || isMultiReturnWarehouse == 0) {
                    //退换仓库默认取店铺策略的换货仓库，取不到取退货仓库，还取不到取默认仓库
                    Long wareId = shopStrategy.getCpCWarehouseExchangeId() == null ? shopStrategy.getCpCWarehouseDefId() : shopStrategy.getCpCWarehouseExchangeId();
                    wareId = wareId == null ? shopStrategy.getDefaultStoreId() : wareId;
                    returnOrder.setCpCPhyWarehouseInId(wareId);
                    this.selectReturnCPhyWarehouse2(shopStrategy.getCpCWarehouseDefId(), returnOrder);
                }
            }
        }
    }

    /**
     * 设置传wms标识
     *
     * @param warehouseId
     * @param returnOrder
     */
    private void selectReturnCPhyWarehouse2(Long warehouseId, OcBReturnOrder returnOrder) {
        if (warehouseId != null) {
            CpCPhyWarehouse cpCPhyWarehouse = cpRpcService.queryByWarehouseId(warehouseId);
            if (cpCPhyWarehouse != null && cpCPhyWarehouse.getWmsControlWarehouse() != null && cpCPhyWarehouse.getWmsControlWarehouse() == 1) {
                returnOrder.setIsNeedToWms(1L);
            }
        }
    }


    /**
     * 拆分换货单地址的省市区
     * 市为空时  拿区
     *
     * @param addr
     * @return
     */
    public Address addressResolutionNew(String addr) {
        //int indexOf = addr.lastIndexOf("^^^");
        //String substring = addr.substring(0, indexOf);
        Address address = new Address();
        String[] split = addr.split("\\^\\^\\^");
        address.setProvince(split[0]);
        String str = split[1];
        System.out.println(str);
        if (StringUtils.isEmpty(str.trim())) {
            address.setCity(split[2]);
        } else {
            address.setCity(str);
        }
        address.setCounty(split[2]);
        address.setAddr(split[3].trim());
        return address;
    }

    /**
     * 构建退换货单
     *
     * @param orderInfo
     * @param user
     * @return
     */
    public OcBReturnOrderRelation buildOcBReturnOrder(OmsTaobaoExchangeRelation orderInfo, User user) {
        OcBReturnOrderRelation relation = new OcBReturnOrderRelation();
        IpBTaobaoExchange ipBTaobaoExchange = orderInfo.getIpBTaobaoExchange();
        ProductSku productSku = orderInfo.getProductSku();
        List<OmsOrderExchangeRelation> originalSingleOrder = orderInfo.getOriginalSingleOrder();
        List<OcBOrderItem> ocBOrderItems = new ArrayList<>();
        OcBOrder ocBOrder = originalSingleOrder.get(0).getOcBOrder();
        for (OmsOrderExchangeRelation orderExchangeRelation : originalSingleOrder) {
            List<OcBOrderItem> ocBOrderItems1 = orderExchangeRelation.getOcBOrderItems();
            ocBOrderItems.addAll(ocBOrderItems1);
        }
        OcBReturnOrder ocBReturnOrder = this.buildOcBReturnOrderFromTaobaoExchange(ipBTaobaoExchange, ocBOrder, user);
        ocBOrderItems = ocBOrderItems.stream().filter(p -> p.getProType() != SkuType.NO_SPLIT_COMBINE).collect(Collectors.toList());
        List<OcBReturnOrderRefund> orderRefundList = this.buildReturnOrderItemFromExchange(ocBOrderItems, ipBTaobaoExchange, ocBReturnOrder, user);
        List<OcBReturnOrderExchange> ocBReturnOrderExchanges = this.buildExchangeOrderItemFromExchange(productSku, ipBTaobaoExchange, ocBReturnOrder, user, orderRefundList);
        String jointTid = OmsReturnAfterUtil.getJointTid(orderRefundList);
        ocBReturnOrder.setTid(jointTid);
        relation.setReturnOrderInfo(ocBReturnOrder);
        relation.setOrderRefundList(orderRefundList);
        relation.setOrderExchangeList(ocBReturnOrderExchanges);
        return relation;
    }


    public void buildExchangeOrder(OcBReturnOrderRelation orderRelation, OmsTaobaoExchangeRelation orderInfo, User user) {
        IpBTaobaoExchange ipBTaobaoExchange = orderInfo.getIpBTaobaoExchange();
        OcBReturnOrder ocBReturnOrder = orderRelation.getReturnOrderInfo();
        ProductSku productSku = orderInfo.getProductSku();
        List<OmsOrderExchangeRelation> originalSingleOrder = orderInfo.getOriginalSingleOrder();
        OcBOrder ocBOrder = originalSingleOrder.get(0).getOcBOrder();
        OcBOrder exchangeOrder = this.buildOcBOrderFromIpTaobaoOrder(ocBOrder, ocBReturnOrder, user, ipBTaobaoExchange);
        OrderAddressConvertUtil.convert(exchangeOrder);
        OcBOrderItem orderItem = this.buildOrderItemFromTaobaoOrderItemNew(exchangeOrder, ipBTaobaoExchange, productSku, user);
        exchangeOrder.setQtyAll(ipBTaobaoExchange.getQty() == null ? BigDecimal.ZERO : BigDecimal.valueOf(ipBTaobaoExchange.getQty()));
        //计算金额
        List<OcBOrderItem> items = new ArrayList<>();
        for (OmsOrderExchangeRelation orderExchangeRelation : originalSingleOrder) {
            List<OcBOrderItem> ocBOrderItems = orderExchangeRelation.getOcBOrderItems();
            Integer isCopyOrder = Optional.ofNullable(orderExchangeRelation.getOcBOrder().getIsCopyOrder()).orElse(0);
            Integer isResetShip = Optional.ofNullable(orderExchangeRelation.getOcBOrder().getIsResetShip()).orElse(0);
            // 过滤掉补发和复制单（补发单和复制单的金额会影响） 20230228
            if (1 != isCopyOrder && 1 != isResetShip) {
                items.addAll(ocBOrderItems);
            }
        }
        items = items.stream().filter(p -> p.getProType() == SkuType.NO_SPLIT_COMBINE || p.getProType() == SkuType.NORMAL_PRODUCT).collect(Collectors.toList());
        // 对等换货的明细，计算数量时必须以原来的数量进行计算: 修复存在对等换货生成的换货单优惠金额不正确的问题 20230228
        BigDecimal qty = items.stream().map(i -> paresEqualExchangeQty(i.getEqualExchangeRatio(),i.getQty())).reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal adjustAmt = items.stream().map(OcBOrderItem::getAdjustAmt).
                reduce(BigDecimal.ZERO, BigDecimal::add).divide(qty, 4, BigDecimal.ROUND_HALF_UP);
        BigDecimal amtDiscount = items.stream().map(OcBOrderItem::getAmtDiscount).
                reduce(BigDecimal.ZERO, BigDecimal::add).divide(qty, 4, BigDecimal.ROUND_HALF_UP);
        BigDecimal orderSplitAmt = items.stream().map(OcBOrderItem::getOrderSplitAmt).
                reduce(BigDecimal.ZERO, BigDecimal::add).divide(qty, 4, BigDecimal.ROUND_HALF_UP);
        BigDecimal realAmt = items.stream().map(OcBOrderItem::getRealAmt).
                reduce(BigDecimal.ZERO, BigDecimal::add).divide(qty, 4, BigDecimal.ROUND_HALF_UP);
        orderItem.setAdjustAmt(adjustAmt.multiply(orderItem.getQty()));
        orderItem.setAmtDiscount(amtDiscount.multiply(orderItem.getQty()));
        orderItem.setOrderSplitAmt(orderSplitAmt.multiply(orderItem.getQty()));
        orderItem.setPrice(items.get(0).getPrice());
        orderItem.setRealAmt(realAmt.multiply(orderItem.getQty()));
        List<OcBOrderItem> ocBOrderItems = new ArrayList<>();
        ocBOrderItems.add(orderItem);
        // 这里计算金额
        omsOrderRecountAmountService.doRecountAmount(exchangeOrder, ocBOrderItems);
        orderRelation.setOcBOrder(exchangeOrder);
        orderRelation.setOcBOrderItems(ocBOrderItems);

    }

    /**
     * 计算对等换货前的数量
     */
    private BigDecimal paresEqualExchangeQty(String ratioStr, BigDecimal qty) {
       if(StringUtils.isBlank(ratioStr)){
           return qty;
       }
        String[] ratio = ratioStr.split(":");
        if (ratio.length > 0) {
            List<String> list = Arrays.asList(ratio);
            //分母
            BigDecimal num1 = new BigDecimal(list.get(0));
            //分子
            BigDecimal num2 = new BigDecimal(list.get(1));
            //获取原数量 分母 * 数量 / 分子
            return num1.multiply(qty).divide(num2,4, BigDecimal.ROUND_HALF_UP);
        }
        return qty;
    }


    /**
     * @description: 是否需要设置oaid
     * <AUTHOR>
     * @date 2022/4/19 11:28
     * @version 1.0
     */
    private boolean isSetOaid() {
        try {
            String value = propertiesConf.getProperty("r3.oc.oms.exchange.set.oaid", "true");
            return StringUtils.equalsIgnoreCase(value, "true");
        } catch (Exception ex) {
            log.error("isSetOaid", ex);
            return true;
        }
    }
}
