package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.oc.oms.api.OcBOrderLogPushCmd;
import com.jackrain.nea.oc.request.OcBOrderLogPushRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @create 2020-08-14
 * @desc 零售发货单操作日志推送
 **/
@Component
@Service(protocol = "dubbo", validation = "true", group = "oc-core", version = "1.0")
public class OcBOrderLogPushCmdImpl implements OcBOrderLogPushCmd {
    @Autowired
    private OrderLogPushService orderLogPushService;

    /**
     * WOS系统推送操作日志
     *
     * @param ocBOrderLogPushRequest
     * @return
     */
    @Override
    public ValueHolderV14 wosPushOperationLog(OcBOrderLogPushRequest ocBOrderLogPushRequest) {
        return orderLogPushService.wosPushOperationLog(ocBOrderLogPushRequest);
    }
}
