package com.jackrain.nea.oc.oms.matcher.parser.impl;

import com.jackrain.nea.oc.oms.matcher.live.LiveMatchManager;
import com.jackrain.nea.oc.oms.matcher.parser.AbstractLiveMatchInfoParser;
import com.jackrain.nea.oc.oms.matcher.vo.OcMatcherInfoVO;
import com.jackrain.nea.oc.oms.matcher.vo.ParamInputVO;
import com.jackrain.nea.oc.oms.matcher.vo.ParamOutputVO;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.relation.IpJingdongOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongOrder;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongOrderItemExt;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.ps.model.ProductSku;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/06/15
 * 京东渠道单据信息提取
 */
@Component
public class LiveMatchInfoParserJingdong extends AbstractLiveMatchInfoParser<IpJingdongOrderRelation> {

    @Override
    protected boolean check(IpJingdongOrderRelation channelOrigOrder, OcBOrder order, List<OcBOrderItem> items) {
        return super.check(channelOrigOrder, order, items)
                && Objects.nonNull(channelOrigOrder.getJingdongOrder());
    }

    /**
     * 明细匹配
     *
     * @param matcherInfoVO
     * @param item
     * @param jdOrder
     * @param originalItem
     */
    private void matchItems(OcMatcherInfoVO<IpJingdongOrderRelation> matcherInfoVO, OcBOrderItem item, IpBJingdongOrder jdOrder, IpBJingdongOrderItemExt originalItem) {
        ParamInputVO inputVO = new ParamInputVO();
        inputVO.setOriginalRemark(jdOrder.getOrderRemark());

        if (Objects.nonNull(originalItem)) {
            inputVO.setOriginalId(String.valueOf(originalItem.getSkuId()));
            inputVO.setOriginalTitle(originalItem.getSkuName());
            ProductSku productSku = originalItem.getProdSku();
            if (productSku !=null){
                //赋值spu
                inputVO.setOriginalSpu(productSku.getProdCode());
            }
        }

        // 每次覆盖这个对象
        matcherInfoVO.setInputVO(inputVO);
        ParamOutputVO outputVO = LiveMatchManager.getInstance().doMatch(matcherInfoVO);

        // 赋值
        if (Objects.nonNull(outputVO) && outputVO.isResult()) {
            setResult(item, outputVO);
        }
    }

    /**
     * 渠道原单解析，得出规则匹配锁需要的信息
     *
     * @param channelOrigOrder
     * @return
     */
    @Override
    public void doParser(IpJingdongOrderRelation channelOrigOrder, OcBOrder order, List<OcBOrderItem> items) {
        if (check(channelOrigOrder, order, items)) {
            IpBJingdongOrder jdOrder = channelOrigOrder.getJingdongOrder();
            List<IpBJingdongOrderItemExt> jdOrderItems = channelOrigOrder.getJingdongOrderItems();

            if (jdOrderItems == null) {
                jdOrderItems = new ArrayList<>();
            }

            // to map
            Map<Long, List<IpBJingdongOrderItemExt>> jdItemMap = jdOrderItems.stream().collect(Collectors.groupingBy(i -> i.getSkuId()));

            OcMatcherInfoVO<IpJingdongOrderRelation> matcherInfoVO = new OcMatcherInfoVO<>();
            // 原单对象
            matcherInfoVO.setOriginalOrderRelation(channelOrigOrder);
            // 店铺ID
            matcherInfoVO.setCpCShopId(jdOrder.getCpCShopId());
            // 下单时间：交易创建时间
            matcherInfoVO.setOrderDate(jdOrder.getOrderStartTime());
            // 支付时间
            matcherInfoVO.setPayTime(jdOrder.getPaymentConfirmTime());

            // 匹配明细
            items.forEach(item -> {
                // 取原单明细
                List<IpBJingdongOrderItemExt> oItems = jdItemMap.get(Long.valueOf(item.getOoid()));
                IpBJingdongOrderItemExt oItem = null;

                if (CollectionUtils.isNotEmpty(oItems)) {
                    oItem = oItems.get(0);
                }

                matchItems(matcherInfoVO, item, jdOrder, oItem);
            });

            setOrderByItem(order, items);
        }
    }

    /**
     * 渠道类型
     *
     * @return
     */
    @Override
    public ChannelType getCurrentChannelType() {
        return ChannelType.JINGDONG;
    }

}
