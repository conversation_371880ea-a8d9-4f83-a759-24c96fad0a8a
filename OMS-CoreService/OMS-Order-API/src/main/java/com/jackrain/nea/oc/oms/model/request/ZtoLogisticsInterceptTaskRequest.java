package com.jackrain.nea.oc.oms.model.request;

import com.jackrain.nea.web.face.User;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/5/9 18:24
 * @Description TODO
 * @Version 1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ZtoLogisticsInterceptTaskRequest implements Serializable {

    private static final long serialVersionUID = 6715345188666403848L;

    /**
     * 物流工单服务类型id列表
     */
    private List<Long> logisticsServiceTypeIds;

    /**
     * 任务类型 "0":发起拦截失败+未拦截的 "1":拦截失败   默认为0
     */
    private String type = "0";


}
