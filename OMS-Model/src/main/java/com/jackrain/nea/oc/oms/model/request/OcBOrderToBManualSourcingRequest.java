package com.jackrain.nea.oc.oms.model.request;

import com.jackrain.nea.oc.oms.model.result.OcBOrderStoOutInfoResult;
import com.jackrain.nea.web.face.User;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> lin yu
 * @date : 2022/8/1 下午3:29
 * @describe :
 */

@Data
public class OcBOrderToBManualSourcingRequest implements Serializable {

    private Integer queryType;

    private Integer confirmType;

    private Long id;

    private Long storeId;

    private String storeCode;

    private User user;

    private Long cpCPhyWarehouseId;

    private String cpCPhyWarehouseCode;

    private Long cpCLogisticsId;

    private String cpCLogisticsEcode;

    private String cpCLogisticsEname;

    private List<OcBOrderStoOutInfoResult> stoOutInfoResultList;

    private String findSourceReason;

    private OcBTobFindSourceData recommendData;

    private OcBTobFindSourceData chooseData;

    @Data

    public static class OcBTobFindSourceData implements Serializable {

        private Integer lineNo;

        private Long cpCPhyWarehouseId;

        private String cpCPhyWarehouseEcode;

        private String cpCPhyWarehouseEname;

        private Long cpCLogisticsId;

        private String cpCLogisticsEcode;

        private String cpCLogisticsEname;

        private BigDecimal totalFee;

        private String isRadiationWarehouse;

        private Integer arrivalDays;

        private BigDecimal unfullcarCost;

        private BigDecimal allocationCost;

        private BigDecimal storageCost;

        private Integer factory;
    }

}
