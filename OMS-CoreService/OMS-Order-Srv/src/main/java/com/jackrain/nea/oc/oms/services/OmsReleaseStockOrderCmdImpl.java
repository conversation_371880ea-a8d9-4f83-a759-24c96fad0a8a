package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.oc.oms.api.OmsReleaseStockOrderCmd;
import com.jackrain.nea.oc.oms.model.request.OmsReleaseStockRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2023/3/23 14:47
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", group = "oc-core", version = "1.0")
public class OmsReleaseStockOrderCmdImpl implements OmsReleaseStockOrderCmd {

    @Autowired
    private OmsReleaseStockOrderService omsReleaseStockOrderService;

    @Override
    public ValueHolderV14 releaseStockOrder(List<OmsReleaseStockRequest> requestList, User user) {
        return omsReleaseStockOrderService.releaseStockOrderService(requestList, user);
    }
}
