package com.jackrain.nea.oc.oms.process.transfer.impl.refund.taobao;

import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.MakeupOrderType;
import com.jackrain.nea.oc.oms.model.relation.OmsTaobaoRefundRelation;
import com.jackrain.nea.oc.oms.process.AbstractOrderProcess;
import com.jackrain.nea.oc.oms.util.LockOrderType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> 孙勇生
 * create at:  19/3/7  18:10
 * @description: 中间表转换到退换货订单服务
 */
@Component
public class TaobaoTransferRefundProcessImpl extends AbstractOrderProcess<OmsTaobaoRefundRelation> {
    public TaobaoTransferRefundProcessImpl() {
        super();
    }

    @Override
    protected String getChildPackageName() {
        return "taobao";
    }

    @Override
    protected long getProcessOrderId(OmsTaobaoRefundRelation orderInfo) {
        return orderInfo.getOrderId();
    }

    @Override
    protected String getProcessOrderNo(OmsTaobaoRefundRelation orderInfo) {
        return orderInfo.getOrderNo();
    }

    @Override
    protected LockOrderType getCurrentLockOrderType() {
        return LockOrderType.TRANSFER_TAOBAO_REFUND;
    }

    @Override
    protected ChannelType getCurrentChannelType() {
        return ChannelType.TAOBAO;
    }

    @Override
    protected MakeupOrderType getCurrentMakeupOrderType() {
        return MakeupOrderType.TRANSFER_ORDER;
    }

    /**
     * 淘宝退单接口，平台单号
     *
     * @param orderInfo 订单单据
     * @return
     */
    @Override
    protected String getSourceTid(OmsTaobaoRefundRelation orderInfo) {
        return orderInfo.getTid();
    }
}
