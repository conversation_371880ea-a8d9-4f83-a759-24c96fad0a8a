package com.jackrain.nea.oc.oms.task.jitxorder;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.burgeon.mq.core.DefaultProducerSend;
import com.burgeon.mq.model.MqSendResult;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.oc.oms.config.TransferOrderMqConfig;
import com.jackrain.nea.oc.oms.constant.MqConstants;
import com.jackrain.nea.oc.oms.es.ES4IpJitXDelivery;
import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.OperateType;
import com.jackrain.nea.oc.oms.model.enums.OrderType;
import com.jackrain.nea.oc.oms.model.jitx.JitxDeliveryStatus;
import com.jackrain.nea.oc.oms.model.table.IpBJitxDelivery;
import com.jackrain.nea.oc.oms.process.jitx.feedback.JitxFeedBackDeliveryProcessImpl;
import com.jackrain.nea.oc.oms.services.IpJitxDeliveryService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 唯品会寻仓反馈补偿任务
 * 1、转换状态SYNSTATUS=0、订单状态STATUS="新建"的单据
 *
 * @author: chenxiulou
 * @since: 2019-06-28
 * create at : 2019-03-15 23:10
 */
@Component
@Slf4j
public class AutoAsynJitxFeedBackTask extends BaseR3Task implements IR3Task {

    @Autowired
    private IpJitxDeliveryService ipJitxDeliveryService;

    @Autowired
    private JitxFeedBackDeliveryProcessImpl jitxFeedBackDeliveryProcess;

//    @Autowired
//    private R3MqSendHelper sendHelper;

    @Autowired
    private DefaultProducerSend defaultProducerSend;

    @Autowired
    private TransferOrderMqConfig orderMqConfig;

    /**
     * apollo 配置类别
     */
    public static final String APPLICATION_GROUP_NAME = "lts";
    /**
     * apollo 配置寻仓订单获取最大条数的配置key
     */
    public static final String LTS_AUTO_ASYN_JITX_FEEDBACK_TRANSFER_TASK_DEFAULT_MAXSIZE = "lts" +
            ".AutoAsynTimeOrderTransferTask.default.maxSize";
    /**
     * apollo 配置每次发送mq时效订单条数配置key
     */
    public static final String LTS_AUTO_ASYN_JITX_FEEDBACK_TRANSFER_TASK_DEFAULT_PAGE_SIZE = "lts" +
            ".AutoAsynTimeOrderTransferTask.default.pageSize";

    @Override
    @XxlJob("AutoAsynJitxFeedBackTask")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult result = new RunTaskResult();
        String errorMessage = null;
        try {
            PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
            Integer defaultMaxSize = config.getProperty(LTS_AUTO_ASYN_JITX_FEEDBACK_TRANSFER_TASK_DEFAULT_MAXSIZE,
                    DEFAULT_PAGE_SIZE);
            Integer defaultPageSize =
                    config.getProperty(LTS_AUTO_ASYN_JITX_FEEDBACK_TRANSFER_TASK_DEFAULT_PAGE_SIZE,
                            DEFAULT_PAGE_SIZE);

            List<String> orderNoList = ES4IpJitXDelivery.findOrderSnBySynStatusOrStatus(
                    0, defaultMaxSize
                    , JitxDeliveryStatus.NEW
            );

            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("OrderNoList={}", JSON.toJSONString(orderNoList)));
            }
            if (CollectionUtils.isEmpty(orderNoList)) {
                errorMessage = this.getClass().getSimpleName() + ".Execute 获取数据为空";
                log.error(LogUtil.format(errorMessage));
                result.setSuccess(true);
                result.setMessage(errorMessage);
                return result;
            }
            orderNoList = orderNoList.stream().distinct().collect(Collectors.toList());
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("OrderNoList={}", JSON.toJSONString(orderNoList)));
            }
            Long totalPage;
            Long totalSize = Long.valueOf(orderNoList.size());
            if (totalSize % defaultPageSize == 0) {
                totalPage = totalSize / defaultPageSize;
            } else {
                totalPage = totalSize / defaultPageSize + 1;
            }
            for (int i = 1; i <= totalPage; i++) {
                List<String> tempOrderNos =
                        orderNoList.stream().skip((totalPage - 1) * defaultPageSize).limit(defaultPageSize).collect(Collectors.toList());
                List<IpBJitxDelivery> ipBJitxDeliveries =
                        this.ipJitxDeliveryService.selectJitxDeliveryByOrderNos(tempOrderNos);
                if (ipBJitxDeliveries.size() == tempOrderNos.size()) {
                    this.sendMQ(ipBJitxDeliveries);
                }
            }
            result.setSuccess(true);
        } catch (Exception ex) {
            log.error(LogUtil.format("Execute Error.异常: {}"), Throwables.getStackTraceAsString(ex));
            result.setSuccess(false);
            result.setMessage(ex.getMessage());
        }
        return result;
    }

    /**
     * 发送MQ消息
     *
     * @param ipBJitxDeliveries 保存的OrderInfo订单信息
     */
    private void sendMQ(List<IpBJitxDelivery> ipBJitxDeliveries) {
        if (CollectionUtils.isNotEmpty(ipBJitxDeliveries)) {
            JSONArray sendArr = new JSONArray();
            for (IpBJitxDelivery item : ipBJitxDeliveries) {
                OperateOrderMqInfo orderMqInfo = new OperateOrderMqInfo();
                orderMqInfo.setChannelType(ChannelType.VIPJITX);
                orderMqInfo.setOperateType(OperateType.TRANSFER_ORDER);
                orderMqInfo.setOrderType(OrderType.FEEDBACK);
                orderMqInfo.setOrderId(item.getId());
                orderMqInfo.setOrderNo(item.getOrderSn());
                JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(orderMqInfo), Feature.OrderedField);
                sendArr.add(jsonObject);
            }
            String messageId = null;
            try {
//                messageId = sendHelper.sendMessage(sendArr
//                        , orderMqConfig.getSendTransferMqTopic()
//                        , orderMqConfig.getSendTransferMqTag());
                MqSendResult result = defaultProducerSend.sendTopic(MqConstants.TOPIC_R3_OC_OMS_CALL_TRANSFER, MqConstants.TAG_R3_OC_OMS_CALL_TRANSFER, sendArr.toJSONString(), null);
                messageId = result.getMessageId();
                log.info(LogUtil.format("mqMessageTopic:{},mqMessageTag:{},messageId:{},messageData:{}"),
                        MqConstants.TOPIC_R3_OC_OMS_CALL_TRANSFER,
                        MqConstants.TAG_R3_OC_OMS_CALL_TRANSFER, messageId,  sendArr);
            } catch (Exception e) {
                log.error(LogUtil.format("Execute,mqMessageTopic:{},mqMessageTag:{},messageData:{} Error{}"),
                        MqConstants.TOPIC_R3_OC_OMS_CALL_TRANSFER,
                        MqConstants.TAG_R3_OC_OMS_CALL_TRANSFER, sendArr, Throwables.getStackTraceAsString(e));
            }
        }
    }
}
