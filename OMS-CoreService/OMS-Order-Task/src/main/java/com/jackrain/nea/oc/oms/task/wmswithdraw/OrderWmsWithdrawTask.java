package com.jackrain.nea.oc.oms.task.wmswithdraw;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.config.PropertiesConf;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderParam;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.services.OrderWmsWithdrawService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.resource.WmsUserResource;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.web.face.User;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2019/6/12 11:09 PM
 * @Version 1.0
 * 订单wms撤回自动任务
 */
@Slf4j
@Component
public class OrderWmsWithdrawTask extends BaseR3Task implements IR3Task {

    @Autowired
    private OmsOrderService omsOrderService;
    @Autowired
    private OrderWmsWithdrawService orderWmsWithdrawService;

    @Override
    @XxlJob("OrderWmsWithdrawTask")
    public RunTaskResult execute(JSONObject params) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("进入订单wms撤回定时任务", "进入订单wms撤回定时任务"));
        }
        long starTime = System.currentTimeMillis();
        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
        Integer pageSize = config.getProperty("lts.OrderWmsWithdrawTask.range", 200);
        RunTaskResult result = new RunTaskResult();
        try {
            List<OcBOrderParam> ocBOrderParams = omsOrderService.selectOrderWmsWithdrawList(0, pageSize);
            User rootUser = WmsUserResource.getWmsUser();
            if (CollectionUtils.isNotEmpty(ocBOrderParams)) {
                orderWmsWithdrawService.OrderWmsWithdraw(ocBOrderParams, rootUser);
            }
            result.setSuccess(true);
            long endTime = System.currentTimeMillis();
            long Time = endTime - starTime;
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("订单wms撤回定时任务耗时：{}",  "订单wms撤回定时任务耗时"), Time);
            }

        } catch (Exception ex) {
            log.error(LogUtil.format("订单wms撤回任务执行失败,异常信息:{}", "订单wms撤回任务执行失败"), Throwables.getStackTraceAsString(ex));
            result.setSuccess(false);
            result.setMessage(ex.getMessage());
        }
        return result;

    }
}
