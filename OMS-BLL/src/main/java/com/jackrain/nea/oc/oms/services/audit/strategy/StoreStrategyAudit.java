package com.jackrain.nea.oc.oms.services.audit.strategy;

import com.jackrain.nea.oc.oms.model.enums.OmsAuditFailedReason;
import com.jackrain.nea.oc.oms.model.enums.OmsMethod;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.services.audit.AuditEnum;
import com.jackrain.nea.oc.oms.services.audit.AuditStrategyHandler;
import com.jackrain.nea.oc.oms.services.audit.OmsOrderAutoAuditService;
import com.jackrain.nea.st.model.StCAutoCheck;
import com.jackrain.nea.st.model.StCAutoCheckRelation;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 店铺审核策略
 *
 * @Auther: 黄志优
 * @Date: 2020/11/3 10:27
 * @Description:
 */
@Service
@Slf4j
public class StoreStrategyAudit implements AuditStrategyHandler {
    @Autowired
    private OmsOrderAutoAuditService omsOrderAutoAuditService;

    @Override
    public boolean doHandle(OcBOrderRelation orderInfo, User operateUser) {
        OmsMethod omsMethod = orderInfo.getOmsMethod();
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("audit.开始校验策略平台数据: {}", orderInfo.getOrderId()), orderInfo.getOrderId());
        }

        //然后付款时间是否在策略生效时间(先查出策略)
        StCAutoCheckRelation autoCheckRelation = omsOrderAutoAuditService.isExitsStrategy(orderInfo);

        orderInfo.setStCAutoCheck(autoCheckRelation.getAutoCheck());
        orderInfo.setAutoCheckAutoTimes(autoCheckRelation.getAutoCheckAutoTimes());
        orderInfo.setAutoCheckExcludeProducts(autoCheckRelation.getAutoCheckExcludeProducts());

        //检查自定义标签
        if (!omsOrderAutoAuditService.checkCustomLabeL(orderInfo)) {
            String message = "订单含自定义标签，自定义标签不符合自动审核条件，审核失败！";
            log.error(" {}.audit.message={}", orderInfo.getOrderId(), message);
            omsOrderAutoAuditService.updateOrderInfo(omsMethod, orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_53);
            orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_53);
            return false;
        }

        //判断订单类型
        if (!omsOrderAutoAuditService.checkOrderType(orderInfo)) {
            String message = "自动审核策略，限制了该订单类型不能自动审核,审核失败!";
            omsOrderAutoAuditService.updateOrderInfo(omsMethod, orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_16);
            orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_16);
            return false;
        }

        /**
         * 判断是否启用手工新增订单自动审核,过滤掉是换货单据
         */
        if (!omsOrderAutoAuditService.checkManualOrder(orderInfo)) {
            String message = "自动审核策略，未启用手工新增订单自动审核,审核失败!";
            omsOrderAutoAuditService.updateOrderInfo(omsMethod, orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_07);
            orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_07);
            return false;
        }

        // 检查付款时间
        if (omsOrderAutoAuditService.checkDateType(orderInfo)) {
            String message = "订单店铺自动审核策略限制了付款时间,该订单的付款时间不在自动审核范围内,审核失败!";
            omsOrderAutoAuditService.updateOrderInfo(omsMethod, orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_05);
            return false;
        }

        //判断订单店铺是否启用全赠品订单自动审核
        if (!omsOrderAutoAuditService.checkOrderGift(orderInfo)) {
            String message = "订单店铺未启用全赠品订单自动审核，审核失败!";
            omsOrderAutoAuditService.updateOrderInfo(omsMethod, orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_06);
            orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_06);
            return false;
        }
        //若该店铺自动审核策略开启了“检查可合并订单”，订单为JITX订单，且存在可合并的订单，则该订单不自动审核
        if (!omsOrderAutoAuditService.checkExistCanMergeJitxOrder(orderInfo)) {
            String message = "订单OrderId=" + orderInfo.getOrderId() + "零售发货单/JITX订单中间表存在可合并的订单还未参与合并,请合并后再审核!";
            omsOrderAutoAuditService.updateOrderInfo(orderInfo.getOmsMethod(), orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_44);
            orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_44);
            return false;
        }
        //判断付款方式  货到付款自动审核
        if (!omsOrderAutoAuditService.checkPayType(orderInfo)) {
            String message = "订单店铺未启用自动审核货到付款订单,审核失败!";
            omsOrderAutoAuditService.updateOrderInfo(omsMethod, orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_18);
            orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_18);

            return false;
        }

        //检查卖家备注
        if (!omsOrderAutoAuditService.checkSellerRemark(orderInfo)) {
            String message = "订单含卖家备注，卖家备注不符合自动审核条件，审核失败！";
            omsOrderAutoAuditService.updateOrderInfo(omsMethod, orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_19);
            orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_19);
            return false;
        }

        //检查买家备注
        if (!omsOrderAutoAuditService.checkBuyerRemark(orderInfo)) {
            String message = "订单含买家备注，买家备注不符合自动审核条件，审核失败！";
            omsOrderAutoAuditService.updateOrderInfo(omsMethod, orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_19);
            orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_19);
            return false;
        }

        //判断订单总额是否【订单自动审核策略】限制金额范围
        if (!omsOrderAutoAuditService.checkOrderAmountScope(orderInfo)) {
            String message = "订单金额不符合自动审核条件,审核失败!";
            omsOrderAutoAuditService.updateOrderInfo(omsMethod, orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_35);
            orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_35);
            return false;
        }

        //判断订单折扣是否【订单自动审核策略】限制金额范围
        if (!omsOrderAutoAuditService.checkOrderDiscountScope(orderInfo)) {
            String message = "订单折扣不符合自动审核条件,审核失败!";
            omsOrderAutoAuditService.updateOrderInfo(omsMethod, orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_39);
            orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_39);
            return false;
        }
        //判断订单折扣是否【单条码数量上限】限制
        if (!omsOrderAutoAuditService.checkSingleSkuNum(orderInfo)) {
            String message = "单条码数量上限不符合自动审核条件,审核失败!";
            omsOrderAutoAuditService.updateOrderInfo(omsMethod, orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_46);
            orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_46);
            return false;
        }
        //判断订单是否满足拆单
        if (!omsOrderAutoAuditService.checkWareHouseSplitOrder(orderInfo)) {
            String message = "订单满足拆单不符合自动审核条件,审核失败!";
            omsOrderAutoAuditService.updateOrderInfo(omsMethod, orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_40);
            orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_40);
            return false;
        }
        //判断订单的收货信息（省、市、区、详细地址）存在【订单自动审核策略】中的“排除收货地址”信息
        if (!omsOrderAutoAuditService.checkReceiverAddress(orderInfo)) {
            String message = "订单收货信息包含不能审核的关键字,审核失败!";
            omsOrderAutoAuditService.updateOrderInfo(omsMethod, orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_22);
            orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_22);
            return false;
        }

        //订单的“物流公司”在【订单自动审核策略】中的“排除物流公司”字段中存在，
        if (!omsOrderAutoAuditService.checkLogistics(orderInfo)) {
            String message = "订单店铺自动审核策略限制了发货物流公司,审核失败!";
            omsOrderAutoAuditService.updateOrderInfo(omsMethod, orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_25);
            orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_25);
            return false;
        }

        //如果存在排除仓库信息中，订单不自动审核
        if (!omsOrderAutoAuditService.checkWarehouse(orderInfo)) {
            String message = "自动审核排除仓库不能审核！";
            omsOrderAutoAuditService.updateOrderInfo(omsMethod, orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_55);
            orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_55);
            return false;
        }

        //自动审核时按照平台条码ID>平台商品ID> 商品条码>商品编码 > 一级分类的优先级顺序判断【零售发货单】商品明细是否包含在商品限制条件中；
        if (!omsOrderAutoAuditService.checkProduct(orderInfo)) {
            String message = "自动审核排除商品不能审核!";
            omsOrderAutoAuditService.updateOrderInfo(omsMethod, orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_54);
            orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_54);
            return false;
        }

        //如果存在排除订单业务信息中，订单不自动审核
        if (!omsOrderAutoAuditService.checkBillType(orderInfo)) {
            String message = "自动审核排除订单业务类型不能审核!";
            omsOrderAutoAuditService.updateOrderInfo(omsMethod, orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_56);
            orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_56);
            return false;
        }


        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("audit.校验策略平台数据完成: {}", orderInfo.getOrderId()), orderInfo.getOrderId());
        }

        return true;
    }

    @Override
    public Integer getSort(String name) {
        return AuditEnum.getValueFromValueTag(name);
    }
}
