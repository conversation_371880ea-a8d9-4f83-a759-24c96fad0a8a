package com.jackrain.nea.oc.oms.model.result;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.oc.oms.model.table.OcBInvoiceNoticeLog;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: huang.zaizai
 * @Date: 2019-07-24 20:00
 * @Version 1.0
 */
//订单主表
@Data
public class OcBInvoiceNoticeLogResult extends OcBInvoiceNoticeLog implements Serializable {

    @JSONField(name = "LOG_TYPE_NAME")
    private String logTypeName;
}
