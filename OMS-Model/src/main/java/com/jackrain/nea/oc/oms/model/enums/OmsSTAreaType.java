package com.jackrain.nea.oc.oms.model.enums;

/**
 * @author: 胡林洋
 * @since: 2019-03-15
 * create at : 2019-03-15 19:46
 */
public enum OmsSTAreaType {

    /**
     * 指定区域类型（物流方案） 0：包含
     */
    CONTAINS,
    /**
     * 指定区域类型（物流方案） 1: 排除
     */
    UN_CONTAINS;

    public int toInteger() {
        if (this == CONTAINS) {
            return 0;
        } else if (this == UN_CONTAINS) {
            return 1;
        } else {
            return 9;
        }
    }

}
