package com.jackrain.nea.oc.oms.process.jitx.feedback.step;

import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.SyncStatus;
import com.jackrain.nea.oc.oms.model.relation.IpJitxDeliveryRelation;
import com.jackrain.nea.oc.oms.model.table.IpBJitxDeliveryItemEx;
import com.jackrain.nea.oc.oms.services.IpJitxDeliveryService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.ps.api.result.PsSkuResult;
import com.jackrain.nea.ps.api.table.ProSku;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 检查订单商品是否在系统中
 *
 * @author: chenxiulou
 * @since: 2019-01-20
 * create at : 2019-01-20 02:48
 */
@Step(order = 80, description = "检查订单商品是否在系统中")
@Slf4j
@Component
public class Step080CheckProductExist extends BaseJitxDeliveryProcessStep
        implements IOmsOrderProcessStep<IpJitxDeliveryRelation> {
    @Autowired
    private PsRpcService psRpcService;
    @Autowired
    private IpJitxDeliveryService ipJitxDeliveryService;

    @Override
    public ProcessStepResult<IpJitxDeliveryRelation> startProcess(IpJitxDeliveryRelation deliveryInfo,
                                                                  ProcessStepResult preStepResult,
                                                                  boolean isAutoMakeup, User operateUser) {
        ProcessStepResult<IpJitxDeliveryRelation> stepResult = new ProcessStepResult<>();
        stepResult.setStatus(StepStatus.FAILED);
        String orderNo = deliveryInfo.getOrderNo();
        int currentSyncStatus = deliveryInfo.getJitxDelivery().getSynstatus().intValue();

        boolean hasErrorInfo = false;
        StringBuilder sbErrorInfo = new StringBuilder();
        List<IpBJitxDeliveryItemEx> jitxDeliveryItemList = deliveryInfo.getJitxDeliveryItemList();
        RedisReentrantLock redisLock = deliveryInfo.getRedisLock();
        try{
            //判断同批次下所有寻仓单的商品信息
            List<IpJitxDeliveryRelation> deliveryRelationList = deliveryInfo.getIpJitxDeliveryRelation();
            for(IpJitxDeliveryRelation relation:deliveryRelationList){
                jitxDeliveryItemList.addAll(relation.getJitxDeliveryItemList());
            }
            for (IpBJitxDeliveryItemEx jitxDeliveryItem : jitxDeliveryItemList) {
                String barcode = jitxDeliveryItem.getBarcode();
                if (StringUtils.isEmpty(barcode)) {
                    hasErrorInfo = true;
                    sbErrorInfo.append("商品barcode为空;");
                    sbErrorInfo.append("\r\n");
                } else {
                    PsSkuResult psSkuResult = psRpcService.selectSkuInfoByforCodes(barcode);
                    if (psSkuResult == null) {
                        hasErrorInfo = true;
                        String msg = Resources.getMessage("在系统中未查询到商品条码;");
                        sbErrorInfo.append(jitxDeliveryItem.getBarcode());
                        sbErrorInfo.append(msg);
                        sbErrorInfo.append("\r\n");
                    } else {
                        List<ProSku> proSkuList = psSkuResult.getProSkus();
                        ProductSku skuInfo = null;
                        if(CollectionUtils.isEmpty(proSkuList)){
                            hasErrorInfo = true;
                            String msg = Resources.getMessage("在系统中未查询到商品条码;");
                            sbErrorInfo.append(barcode);
                            sbErrorInfo.append(msg);
                            sbErrorInfo.append("\r\n");
                        }else if(proSkuList.size() > 1){
                            hasErrorInfo = true;
                            String msg = Resources.getMessage("在系统中查询到多个商品条码;");
                            sbErrorInfo.append(barcode);
                            sbErrorInfo.append(msg);
                            sbErrorInfo.append("\r\n");
                        }else{
                            skuInfo = psRpcService.selectProductSku(proSkuList.get(0).getEcode());
                            if (skuInfo == null) {
                                hasErrorInfo = true;
                                String msg = Resources.getMessage("查询商品信息，不存在;");
                                sbErrorInfo.append(barcode);
                                sbErrorInfo.append(msg);
                                sbErrorInfo.append("\r\n");
                            } else {
                                jitxDeliveryItem.setProdSku(skuInfo);
                            }
                        }
                    }
                }
            }

            if (hasErrorInfo) {
                String errorMessage = "商品数据不存在，不对线上反馈寻仓结果，退出寻仓反馈操作;";
                boolean updateStatusRes = ipJitxDeliveryService.updateJitxSyncStatus(orderNo,
                        SyncStatus.SYNCFAILD, sbErrorInfo.toString() + "不对线上反馈寻仓结果;");
                if (!updateStatusRes) {
                    errorMessage += ";更新状态失败=False";
                }
                stepResult.setStatus(StepStatus.FAILED);
                stepResult.setMessage(errorMessage);
            } else {
                stepResult.setStatus(StepStatus.SUCCESS);
                stepResult.setMessage("商品数据检查成功，进入下一阶段");
            }
        }catch (Exception e){
            log.error(LogUtil.format("寻仓单OrderSn:{}转换失败:{}","Step080CheckProductExist"),
                    orderNo, Throwables.getStackTraceAsString(e));
            ipJitxDeliveryService.updateJitxSyncStatus(orderNo, SyncStatus.UNSYNC, "转换失败"+e.getMessage());
        }finally {
            if(!StepStatus.SUCCESS.equals(stepResult.getStatus())){
                redisLock.unlock();
            }
        }
        return stepResult;
    }
}
