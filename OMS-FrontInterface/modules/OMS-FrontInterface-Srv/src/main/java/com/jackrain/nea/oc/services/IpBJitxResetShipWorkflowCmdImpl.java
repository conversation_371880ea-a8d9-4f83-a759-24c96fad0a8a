package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.IpBJitxResetShipWorkflowCmd;
import com.jackrain.nea.oc.oms.model.constant.R3ParamConstants;
import com.jackrain.nea.oc.oms.services.IpBJitxResetShipWorkflowService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utils.ValueHolderUtils;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR> zhuxing
 * @Date : 2021-10-19 17:37
 * @Description : 重置发货
 **/
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class IpBJitxResetShipWorkflowCmdImpl extends CommandAdapter implements IpBJitxResetShipWorkflowCmd {

    @Autowired
    private IpBJitxResetShipWorkflowService ipBJitxResetShipWorkflowService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        ValueHolder vh = new ValueHolder();
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (param.isEmpty()) {
            throw new NDSException(Resources.getMessage("入参为空!", session.getUser().getLocale()));
        }

        JSONArray ids = new JSONArray();
        if (param.containsKey("ids")) {
            ids = param.getJSONArray("ids");
        }
        Long objId = param.getLong(R3ParamConstants.OBJID);
        if (objId != null) {
            ids.add(objId);
        }
        if (ids == null || ids.size() < 1) {
            throw new NDSException(Resources.getMessage("请选择需要操作的数据！", session.getUser().getLocale()));
        }
        if (ids.size() == 1) {
            Long orderId = ids.getLong(0);
            ValueHolderV14 v14 = ipBJitxResetShipWorkflowService.resetSendDelivery(orderId, session.getUser());
            return ValueHolderUtils.custom(v14.getCode(), v14.getMessage(), v14.getData());
        } else {
            int successNum = 0;
            int failNum = 0;
            for (int i = 0; i < ids.size(); i++) {
                Long orderId = ids.getLong(i);
                try {
                    ipBJitxResetShipWorkflowService.resetSendDelivery(orderId, session.getUser());
                    successNum++;
                } catch (Exception e) {
                    log.error("ID:{}重置发货失败,msg:{}", orderId, e.getMessage());
                    failNum++;
                }
            }
            if (failNum == 0) {
                vh.put("code", 0);
                vh.put("message", "执行成功" + successNum + "条,失败" + failNum + "条");
            } else {
                vh.put("code", -1);
                vh.put("message", "执行成功" + successNum + "条,失败" + failNum + "条");
            }
        }
        return vh;
    }
}
