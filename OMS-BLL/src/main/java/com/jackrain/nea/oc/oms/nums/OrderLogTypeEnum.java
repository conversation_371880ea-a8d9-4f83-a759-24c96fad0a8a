package com.jackrain.nea.oc.oms.nums;

import lombok.Getter;

/**
 * @program: R3-OC-OMS-V1.4
 * @author: Lijp
 * @create: 2019-06-21 10:47
 */
public enum OrderLogTypeEnum {

    ORDER_ADD("1", "订单新增"),
    GIFT_DEL("2", "删除商品"),
    GIFT_ADD("3", "添加商品"),
    GOODS_REPLACE("4", "更换商品"),
    ORDER_INTERCEPTION("5", "订单挂起"),
    INTERCEPTION_CANCLE("6", "取消挂起"),
    MARK_REFUND_COMPLETION("7", "标记退款完成"),
    SAVE("8", "保存(明细修改规格)"),
    BLACKLIST_JOIN("9", "加入黑名单"),
    ORDER_EXAMINE("10", "订单审核"),
    MARK_REFUND_CANCEL("11", "标记退款取消"),
    ORDER_RE_EXAMINE("15", "订单反审核"),
    LOGISTICS_UPDATE("20", "修改物流"),
    ORDER_RESHIP("22", "订单补发"),
    LOSEORDER_COPY("25", "丢单复制"),
    ORDER_COPY("26", "复制订单"),
    INVOICE("30", "开票"),
    WAREHOUSE_UPDATE("40", "修改仓库"),
    ADDRESS_UPDATE("50", "修改地址"),
    ORDER_CANCLE("60", "订单取消"),
    REFUND_ORDER_ADD("70", "新增退单"),
    ORDER_SPLIT("80", "拆分订单"),
    APPOINT_SPLIT("81", "指定商品拆分"),
    ORDER_HOLD_CANCEL("82", "取消Hold单"),
    ORDER_JITX_HOLD("83", "JITX Hold单"),
    ORDER_HOLD("84", "订单Hold单"),
    ORDER_PROM("87", "促销挂起"),
    ORDER_REFUND_HOLD("86", "退款HOLD单"),
    ORDER_REFUND_RELEASE("91", "退款HOLD单释放"),
    UPDATE_ADDRDERSS_HOLD("92", "修改地址Hold单"),
    UPDATE_ADDRDERSS_HOLD_RELEASE("93", "修改地址Hold单释放"),
    EXCHANGE_GOODS_HOLD("94", "换货Hold单"),
    EXCHANGE_GOODS_HOLD_RELEASE("95", "换货Hold单释放"),
    ORDER_MERGE("85", "合并订单"),
    ORDER_MERGE_FAILED("851", "合并订单失败"),
    JD_MANUAL_SPLIT("90", "拆分订单"),
    SELLERMEMO_UPDATE("100", "修改卖家备注"),
    INSIDE_REMARK_UPDATE("105", "修改内部备注"),
    WORKORDER("110", "工单"),
    WMS_CANCLE_SUCCESS("120", "WMS撤回成功"),
    WMS_CANCLE_FAIL("121", "WMS撤回失败"),
    RE_OCCUPANCY("130", "重新占单"),
    SYSTEM_ABOLITION("140", "系统作废"),
    STOCK_OCCUPY_SUCCESS("150", "库存占用"),
    STOCK_OCCUPY_FAIL("151", "库存占用失败"),
    LESS_STOCK_OCCUPY_SUCCESS("152", "库存缺货占用成功"),
    RELEASE_STOCK_SUCCESS("160", "释放库存成功"),
    RELEASE_STOCK_FAIL("161", "释放库存失败"),
    DISTRIBUTION_UPDATE("170", "更新配货"),
    WMS_LACK_GIFT("175", "WMS实缺商品打标"),
    STOCK_SEND("180", "仓库发货"),
    WMS_LOGISTICS_BACK("185", "更新平台快递"),
    PLATFORM_SEND("190", "平台发货"),
    TRANSACTION_COMPLETE("200", "交易完成"),
    REFUND_IN("210", "退货中"),
    RETURN_COMPELETED("215", "入库完成,取消Hold单"),
    AG_SEND_CANCLE("220", "AG取消发货"),
    WAREHOUSE_SERVICE("230", "分仓服务"),
    SUBLOGISTICS_SERVICE("240", "分物流服务"),
    LACK_RE_OCCUPANCY("250", "缺货重新占单"),
    ORDER_TOWMS("260", "订单传WMS"),
    ORDER_TO_POS("265", "订单传POS"),
    FULL_LINKLOG("270", "全链路日志"),
    TB_EXCHANGE_CONVERT("280", "淘宝换货转换"),
    SCALPORDER("290", "刷单订单"),

    COMBINATION_SPLIT("300", "组合商品解析"),
    STANDPALT_lOG_TYPE("310", "通用发货前退单转单日志"),
    RECORD_INVOICE_TYPE("320", "记录开票"),
    DEALER_STRATEGY_DELETE_SKU("330", "经销商自有商品删除"),
    SELLGOODS_STRATEGY_DELETE_SKU("340", "非代销分销商品删除"),
    THEAUDIT_PRICE_OCCUPY("350", "反审核调用线上代销资金占用变动服务"),
    CHECK__PRICE_OCCUPY("360", "审核调用线上代销资金占用变动服务"),
    DELIVERGOODS_PRICE_OCCUPY("370", "仓库发货调用线上代销资金占用变动服务"),
    ORDER_IMPORT("380", "订单导入"),
    ORDER_LOCK("390", "订单锁单"),
    ORDER_LABEL("400", "订单打标"),
    ORDER_MODIFY_OUT_STOCK_TYPE("410", "订单修改出库类型"),
    ORDER_DELIVERY("430", "手工修改为平台发货"),
    JITX_ORDER_UPDATE_WAREHOUSE("440", "JITX订单修改仓库"),

    STORE_MODIFY_ORDER_RECEIVING_STATUS("450", "更新门店接单状态"),
    JINGDONG_EXCHANGEORDER_TRANSFER("460", "京东换货单转单"),
    CREATE_PAYABLE_ADJUSTMENT("470", "生成丢件单"),

    ALIBABA_ASCP_CANCEL_DELIVER_GOODS_REPLY("662", "订单取消发货回告"),

    ALIBABA_ASCP_RETURN_GOODS_REPLY("663", "订单销售退货入库回传"),

    SHORTAGE_NOTICE("661", "缺货回告"),
    WAREHOUSE_DELIVERY_CALL_INTERFACE("662", "仓库发货调用云枢纽"),
    TB_AUDIT_VOLUNTARILY_HOLD("998", "TB自助修改地址"),
    VIEW_PERSONAL_INFO("999", "显示敏感数据"),
    //从700开始
    ADVANCE_PARSE("700", "预售解析"),
    ADVANCE_DETENTION("710", "订单卡单"),
    ADVANCE_DETENTION_SPLIT("720", "卡单拆单"),
    DETENTION_RELEASE("730", "卡单释放"),
    ADVANCE_SPLIT("740", "预售拆单"),
    OCCUPY("750", "寻源占单"),
    LACK_RE_SPLIT("760", "缺货拆单"),
    AGAIN_OCCUPY_STOCK("770", "手动重新寻源"),
    ADVANCE_SINK_STRATEGY("780", "预下沉策略"),
    ORDER_LABEL_STRATEGY("790", "订单打标策略"),
    JITX_SHIFT_STOCK_OCCUPY("800", "JITX订单库存占用(平移)成功"),
    JITX_OUT_STOCK_SPLIT("805", "JITX合包订单实缺拆单"),
    JITX_MERGED_RESHIP("806", "JITX合包订单重置发货"),
    JITX_REDELIVERY_SPLIT("807", "JITX订单发货异常补发"),
    LOCK_STOCK_THEN_REOCCUPY("808", "冻结并重新寻源"),
    JITX_ORDER_INFO_CHANGE("810", "JITX订单转换数据更新"),
    LABLEPARSE("820", "订单打标策略"),
    MATCH_EXPIRY_DATE("830", "匹配商品效期"),
    EQUAL_EXCHANGE("850", "对等换货"),
    BUSINESS_TYPE("860", "业务类型区分"),
    TOB_OCCUPY("870", "TOB寻源占单"),
    STRATEGY_SPLIT("880", "策略拆单"),
    BUSINESS_TYPE_ECODE("890", "修改业务类型"),
    SAVE_SAP_SALE_DATA("910", "出库生成销售数据信息"),
    GENERATE_CYCLE_PURCHASE_SUB_ORDER("920", "生成周期购子订单"),
    DETENTION("930", "手动卡单"),
    AGAIN_DISTRIBUTION_LOGISTICS("950", "重新寻物流"),
    ORDER_EXCEPTION("960", "发货单异常策略执行"),
    INTERCEPT_RETURN("970", "包裹拦截退回"),
    APPOINT_LOGISTICS("980", "手工指定快递"),
    CANCEL_APPOINT_LOGISTICS("990", "取消指定快递"),
    SHORT_STOCK_NO_SPLIT("1000", "缺货不拆"),
    AUTO_APPOINT_LOGISTICS("1010", "自动指定快递"),
    CYCLE_ORDER("1020", "周期购促销"),
    CARPOOL_ORDER("1030", "整车拼车"),
    CANCEL_CARPOOL_ORDER("1040", "取消整车拼车"),
    IMPERFECT_STRATEGY("1050", "残次策略"),
    REMARK_GIFT_STRATEGY("1060", "卖家备注添加赠品"),
    ;

    @Getter
    private final String key;
    @Getter
    private final String name;

    OrderLogTypeEnum(String key, String name) {
        this.key = key;
        this.name = name;
    }

    /**
     * 根据状态值,获取状态名
     *
     * @param key
     * @return String
     */
    public static String enumToStringBykey(String key) {
        String s = "";
        if (key == null) {
            return s;
        }
        for (OrderLogTypeEnum e : OrderLogTypeEnum.values()) {
            if (e.getKey().equals(key)) {
                s = e.getName();
                return s;
            }
        }
        return key;
    }
}