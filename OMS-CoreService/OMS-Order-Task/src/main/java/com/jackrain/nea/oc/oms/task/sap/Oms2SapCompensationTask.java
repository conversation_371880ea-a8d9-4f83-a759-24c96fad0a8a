package com.jackrain.nea.oc.oms.task.sap;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.sap.Oms2SapMapper;
import com.jackrain.nea.oc.oms.sap.Oms2SapStatusEnum;
import com.jackrain.nea.oc.oms.sap.SapTaskTableEnum;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.ExceptionUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * oms 订单传 sap  补偿任务
 * 零售单, 退单, 退款单
 *
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2020/4/26
 */
@Slf4j
@Component
@Deprecated
public class Oms2SapCompensationTask extends BaseR3Task implements IR3Task {

    @Autowired
    private Oms2SapMapper oms2SapMapper;

    /**
     * 零售单索引
     */
    public final String orderIndex = "oc_b_order";

    /**
     * 退货单索引
     */
    public final String returnIndex = "oc_b_return_order";

    /**
     * 退款单es索引
     */
    public final String refundIndex = "oc_b_return_af_send";

    /**
     * 零售单
     */
    private final String orderTbName = "OC_B_ORDER";

    /**
     * 退货单
     */
    private final String returnTbName = "OC_B_RETURN_ORDER";

    /**
     * 退款单
     */
    private final String refundTbName = "OC_B_RETURN_AF_SEND";


    /**
     * 零售sap状态
     */
    private final String orderSapStatus = "TO_SAP_STATUS";

    /**
     * 退货sap状态
     */
    private final String returnSapStatus = "RESERVE_BIGINT08";

    /**
     * 退款sap状态
     */
    private final String refundSapStatus = "SAP_STATUS";


    @Override
    @XxlJob("Oms2SapCompensationTask")
    public RunTaskResult execute(JSONObject params) {
        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
        Integer size = config.getProperty("oms2sap.compensation.task.size", 100);

        if (log.isDebugEnabled()) {
            log.debug("Oms2SapCompensationTask.execute Start...");
        }

        RunTaskResult result = new RunTaskResult();
        long l = System.currentTimeMillis();

        // 1. 零售单

        boolean updateResult = updateOrderSapStatus(size, orderIndex, orderIndex, orderTbName, orderSapStatus, SapTaskTableEnum.ORDER.txt());
        if (log.isDebugEnabled()) {
            log.debug("Oms2SapCompensationTask.execute.Order.Result: {}", updateResult);
        }

        // 2. 退货单
        updateResult = updateOrderSapStatus(size, orderIndex, orderIndex, returnTbName, returnSapStatus, SapTaskTableEnum.RETURN.txt());
        if (log.isDebugEnabled()) {
            log.debug("Oms2SapCompensationTask.execute.Return_Order.Result: {}", updateResult);
        }

        // 3. 退款单
        /*updateResult = updateOrderSapStatus(size, refundIndex, refundIndex, refundTbName, refundSapStatus,
                SapTaskTableEnum.REFUND.txt());
        if (log.isDebugEnabled()) {
            log.debug("Oms2SapCompensationTask.execute.Return_AF_Send.Result: {}", updateResult);
        }*/

        long l0 = System.currentTimeMillis();
        result.setSuccess(updateResult);
        result.setMessage("Oms2SapCompensationTask.execute.Finished; UseTime: " + (l0 - l) + " ms");

        if (log.isDebugEnabled()) {
            log.debug("Oms2SapCompensationTask.execute Finished..." + (l0 - l) + " ms");
        }

        return result;
    }


    /**
     * 更新oms单据, task 表
     *
     * @param size          数量/次
     * @param indexName     es 索引
     * @param typeName      es 类型
     * @param tbName        oms 单据表名
     * @param fieldName     oms 传 sap 字段
     * @param taskOrderName task 表名
     * @return 本次执行结果 ? 成功 : 失败
     */
    private boolean updateOrderSapStatus(Integer size, String indexName, String typeName, String tbName, String fieldName, String taskOrderName) {

        String sendTime = "RESERVE_DECIMAL07";

        String ids = null;
        try {
            ids = searchKeyFromEs(size, indexName, typeName, tbName, fieldName, sendTime);
            if (log.isDebugEnabled()) {
                log.debug("Oms2SapCompensationTask.execute.Find ES Result.OriginTableName-{},Ids-{}", tbName, ids);
            }
            if (StringUtils.isBlank(ids)) {
                return true;
            }

            List<Long> listIds = oms2SapMapper.selectCompensateDynamicOrigOrder(tbName, ids, fieldName, sendTime);
            ids = join2String(listIds);

            if (StringUtils.isNotBlank(ids)) {
                int updateResult = oms2SapMapper.updateDynamicOrigOrderByString(tbName, fieldName, Oms2SapStatusEnum.WAIT.val(), ids);
                if (log.isDebugEnabled()) {
                    log.debug("Oms2SapCompensationTask.execute.Update.OriginTableName-{},Result-{},SAPStatus-{},Ids-{}",
                            tbName, updateResult, Oms2SapStatusEnum.WAIT.val(), ids);
                }
                int taskResult = oms2SapMapper.updateDynamicTaskOrderByString(taskOrderName, Oms2SapStatusEnum.WAIT.val(), ids);
                if (log.isDebugEnabled()) {
                    log.debug("Oms2SapCompensationTask.execute.Update.TaskTableName-{},Result-{},SAPStatus-{}, Ids-{} ",
                            taskOrderName, taskResult, Oms2SapStatusEnum.WAIT.val(), ids);
                }
            } else {
                if (log.isDebugEnabled()) {
                    log.debug("Oms2SapCompensationTask.execute.Find DB Result.OriginTableName-{},Ids-{}", tbName, ids);
                }
            }
        } catch (Exception ex) {
            log.error("Oms2SapCompensationTask.execute Update TableName-{},SAPStatus-{}, Ids-{}, Exp-{} ", tbName,
                    Oms2SapStatusEnum.WAIT.val(), ids, ex.getMessage());
            log.error(ExceptionUtil.getMessage(ex));
            return false;
        }
        return true;
    }

    private String join2String(List<Long> listIds) {
        StringBuilder sb = new StringBuilder();
        int i = 0, l = listIds.size();
        for (; i < l; i++) {
            if (i > 0) {
                sb.append(",");
            }
            sb.append(listIds.get(i));
        }
        return sb.toString();
    }


    /**
     * es 查询
     *
     * @param
     * @return 分库键字符串
     */
    private String searchKeyFromEs(Integer size, String indexName, String typeName, String tbName, String fieldName, String sendTime) {

        final String shardKey = "ID";
        JSONObject esJsn = ES4Order.findJSONObjectByIndexName(size, indexName, typeName, tbName, fieldName, sendTime, shardKey);
        if (esJsn == null) {
            return null;
        }

        StringBuilder sb = new StringBuilder();
        Long totalCount = esJsn.getLong("total");
        if (totalCount == null || totalCount.intValue() < 1) {
            return null;
        }

        JSONArray ary = esJsn.getJSONArray("data");
        if (ary == null) {
            return null;
        }
        int i = 0, l = ary.size();
        for (; i < l; i++) {
            JSONObject o = ary.getJSONObject(i);
            if (o == null) {
                continue;
            }
            String id = o.getString(shardKey);
            if (id == null) {
                continue;
            }
            sb.append(id);
            i++;
            break;
        }
        for (; i < l; i++) {
            JSONObject o = ary.getJSONObject(i);
            if (o == null) {
                continue;
            }
            String id = o.getString(shardKey);
            if (id == null) {
                continue;
            }
            sb.append(",").append(id);
        }
        return sb.toString();
    }


}
