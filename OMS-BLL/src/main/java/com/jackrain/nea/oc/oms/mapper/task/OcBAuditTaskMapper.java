package com.jackrain.nea.oc.oms.mapper.task;

import com.alibaba.fastjson.JSONArray;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.task.OcBAuditTask;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Component;

import java.util.List;


@Mapper
@Component
public interface OcBAuditTaskMapper extends ExtentionMapper<OcBAuditTask> {

    @SelectProvider(type = AuditDrdsSql.class, method = "selectByNodeSql")
    List<OcBAuditTask> selectTaskIdList(@Param(value = "limit") int limit,
                                        @Param(value = "taskTableName") String taskTableName,
                                        @Param(value = "shopIds") String shopIds,
                                        @Param(value = "executeTime") Long executeTime);

    @Update("<script> "
            + "UPDATE OC_B_AUDIT_TASK SET STATUS = 1,modifieddate = now() where STATUS = 0 and order_id in "
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    void updateTaskStatus(@Param("ids") List<Long> orderIds);


    @Update("<script> "
            + "UPDATE OC_B_AUDIT_TASK SET STATUS = 2,modifieddate = now() where order_id in "
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    void updateTaskMergeStatus(@Param("ids") List<Long> orderIds);



    @Select("<script> "
            + "SELECT * FROM OC_B_AUDIT_TASK WHERE order_id "
            + "in <foreach item='item' index='index' collection='orderId' open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<OcBAuditTask> selectTaskIdListByOrderId(@Param("orderId") List<Long> orderId);


    @Update("<script> "
            + "UPDATE OC_B_AUDIT_TASK SET STATUS = 0,modifieddate = now() where order_id in "
            + "<foreach item='item' index='index' collection='orderIds' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    void updateTaskStatusByOrderId(@Param("orderIds") List<Long> orderIds);


    @InsertProvider(type = AuditDrdsSql.class, method = "insertAuditTaskList")
    void insertAuditTaskList(@Param(value = "jsonArray") JSONArray jsonArray);

    @Update("<script> "
            + "UPDATE OC_B_AUDIT_TASK SET modifieddate = now() where order_id in "
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    void updateTaskModitimes(@Param("ids") List<Long> orderIds);

    @Select("<script> "
            + "SELECT order_id,status FROM OC_B_AUDIT_TASK WHERE order_id "
            + "in <foreach item='item' index='index' collection='orderId' open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<OcBAuditTask> selectTaskIdListByOrderIds(@Param("orderId") List<Long> orderId);

    @Select("SELECT * FROM OC_B_AUDIT_TASK WHERE order_id = #{orderId}")
    List<OcBAuditTask> selectTaskListByOrderId(@Param("orderId") Long orderId);


    @Delete("<script> "
            + "delete from OC_B_AUDIT_TASK where order_id =#{orderId} and id in "
            + "<foreach item='item' index='index' collection='id' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    void delTaskStatusByOrderId(@Param("orderId") Long orderId, @Param("id") List<Long> id);

    @Update("UPDATE OC_B_AUDIT_TASK SET STATUS = 0,modifieddate = now(),RETRY_NUMBER= #{retryNumber},NEXT_TIME = #{nextTime} where order_id = #{orderId} limit 1")
    void updateTaskByOrderId(@Param("orderId") Long orderId, @Param("retryNumber") Integer retryNumber, @Param("nextTime") Long nextTime);

}


