package com.jackrain.nea.oc.oms.task.splitorder;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.core.model.ext.SgBStorageInclShare;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.task.OcBOrderSkuSplitTaskMapper;
import com.jackrain.nea.oc.oms.mapper.task.OcBOrderSplitTaskMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.services.SplitOutStockOrderService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * 计算sku库存任务
 * @author: wang shuai
 * @since: 2020/12/7
 */
@Slf4j
@Component
public class CalculateSkuStockTask extends BaseR3Task implements IR3Task {

    @Autowired
    private OcBOrderSplitTaskMapper ocBOrderSplitTaskMapper;
    @Autowired
    private OcBOrderSkuSplitTaskMapper ocBOrderSkuSplitTaskMapper;
    @Autowired
    private SgRpcService sgRpcService;
    @Autowired
    private SplitOutStockOrderService splitOutStockOrderService;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private ThreadPoolTaskExecutor calculateSkuStockThreadPoolExecutor;

    @Override
    @XxlJob("CalculateSkuStockTask")
    public RunTaskResult execute(JSONObject params) {
        Long startTime = System.currentTimeMillis();
        RunTaskResult result = new RunTaskResult();
        Long currentTimeMillis = System.currentTimeMillis();
        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
        Integer limitNum = config.getProperty("lts.orderSkuSplitTask.limit.num", 10);
        Integer partition = config.getProperty("lts.orderSkuSplitTask.partition", 50);
        Integer execTimes = config.getProperty("lts.orderSkuSplitTask.execTimes", 5);
        try {
            List<Long> totalSkuIdList = ocBOrderSkuSplitTaskMapper.selectSkuIdList(0, limitNum, execTimes, currentTimeMillis);
            if (CollectionUtils.isNotEmpty(totalSkuIdList)) {
                List<List<Long>> skuIdPartitionList = Lists.partition(totalSkuIdList, partition);
                List<Future<Boolean>> results = new ArrayList<>();
                for (List<Long> skuIdList : skuIdPartitionList) {
                    try {
                        Future<Boolean> exeResult = calculateSkuStockThreadPoolExecutor.submit(new CallableCalculateSkuStockResult(skuIdList, currentTimeMillis));
                        results.add(exeResult);
                    } catch (Exception e) {
                        log.error("提交线程异常：{}", Throwables.getStackTraceAsString(e));
                    }
                }
                //线程执行结果获取
                for (Future<Boolean> futureResult : results) {
                    try {
                        log.debug(LogUtil.format("------>线程结果:{}",  "CalculateSkuStockTask", futureResult.get().toString()));
                    } catch (Exception e) {
                        log.error(LogUtil.format("多线程获取InterruptedException异常：{}", "CalculateSkuStockTask"), Throwables.getStackTraceAsString(e));
                    }
                }
            }
            result.setSuccess(true);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage("计算sku库存任务处理过程发生异常！");
            log.error(LogUtil.format("计算sku库存任务处理过程发生异常:{}", "计算sku库存任务处理过程发生异常"), Throwables.getStackTraceAsString(e));
        }

        return result;
    }


    /**
     * 开启线程类
     */
    class CallableCalculateSkuStockResult implements Callable<Boolean> {

        private final List<Long> skuIdList;
        private final Long currentTimeMillis;

        public CallableCalculateSkuStockResult(List<Long> skuIdList, Long currentTimeMillis) {
            this.skuIdList = skuIdList;
            this.currentTimeMillis = currentTimeMillis;
        }

        @Override
        public Boolean call() throws Exception {
            String prefix = "[" + this.getClass().getName() + "." + new Exception().getStackTrace()[0].getMethodName() + "].[";
            List<Long> haveStockSkuIdList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(skuIdList)) {
                PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
                // 增加时间 单位秒 默认120s
                Long addTime = config.getProperty("lts.orderSkuSplitTask.addTime", 120L);
                Integer limitSplitTimes = config.getProperty("lts.orderSplitTask.limit.splitTimes", 3);
                /**
                 * 查询sku可用库存
                 */
                List<SgBStorageInclShare> qtyAvailableList = sgRpcService.getQtyAvailableList(skuIdList, SystemUserResource.getRootUser());
                if (CollectionUtils.isNotEmpty(qtyAvailableList)) {
                    haveStockSkuIdList = qtyAvailableList.stream().map(o -> o.getPsCSkuId()).collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
                }
                if (CollectionUtils.isNotEmpty(haveStockSkuIdList)) {
                    List<Long> orderIdList = ocBOrderSkuSplitTaskMapper.selectOrderIdList(0, haveStockSkuIdList);
                    /**
                     * 有库存且可以拆单的订单列表
                     */
                    List<Long> canSplitOrderOfOrderId = new ArrayList<>();
                    /**
                     * 不可以拆单的订单列表
                     */
                    List<Long> canNotSplitOrderOfOrderId = new ArrayList<>();

                    for (Long orderId : orderIdList) {
                        OcBOrder orderDB = ocBOrderMapper.selectByID(orderId);
                        if (orderDB != null && OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(orderDB.getOrderStatus())) {
                            List<OcBOrderItem> orderItemListDB = ocBOrderItemMapper.selectOrderItemListOccupy(orderId);
                            boolean isOutStock = splitOutStockOrderService.calculateOrderAllStoreIsOutStock(orderId, orderDB, orderItemListDB);
                            if (!isOutStock) {
                                canSplitOrderOfOrderId.add(orderId);
                            }
                        } else {
                            canNotSplitOrderOfOrderId.add(orderId);
                        }
                    }

                    if (CollectionUtils.isNotEmpty(canSplitOrderOfOrderId)) {
                        this.processTransactionalByCanSplitOrderOfOrderId(canSplitOrderOfOrderId, limitSplitTimes);
                    }

                    if (CollectionUtils.isNotEmpty(canNotSplitOrderOfOrderId)) {
                        this.processTransactionalByCanNotSplitOrderOfOrderId(canNotSplitOrderOfOrderId);
                    }
                }

                /**
                 * 更新下次执行时间
                 */
                if (CollectionUtils.isNotEmpty(skuIdList)) {
                    long nextTime = currentTimeMillis + (addTime * 1000L);
                    ocBOrderSkuSplitTaskMapper.batchUpdateNextTime(skuIdList, nextTime);
                }
            }
            return true;
        }

        @Transactional(rollbackFor = Exception.class)
        public void processTransactionalByCanSplitOrderOfOrderId(List<Long> orderIdList, int limitSplitTimes) {
            try {
                /**
                 * 更新oc_b_order_split_task
                 *
                 */
                ocBOrderSplitTaskMapper.batchUpdateTwo(orderIdList, 0, 0,limitSplitTimes - 1, limitSplitTimes);
                /**
                 * 更新oc_b_order_sku_split_task
                 */
                List<Integer> oldStatusList = new ArrayList<>();
                oldStatusList.add(0);
                ocBOrderSkuSplitTaskMapper.batchUpdate(orderIdList, 1, oldStatusList);
            } catch (Exception e) {
                log.error("异常：", e);
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            }
        }

        @Transactional(rollbackFor = Exception.class)
        public void processTransactionalByCanNotSplitOrderOfOrderId(List<Long> orderIdList) {
            try {
                /**
                 * 更新oc_b_order_split_task
                 *
                 */
                ocBOrderSplitTaskMapper.batchUpdateFour(2,"0,1"," 当前订单已经非缺货状态，无需继续缺货拆单。", orderIdList);
                /**
                 * 更新oc_b_order_sku_split_task
                 */
                List<Integer> oldStatusList = new ArrayList<>();
                oldStatusList.add(0);
                oldStatusList.add(1);
                ocBOrderSkuSplitTaskMapper.batchUpdate(orderIdList, 2, oldStatusList);
            } catch (Exception e) {
                log.error("异常：", e);
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            }
        }
    }
}