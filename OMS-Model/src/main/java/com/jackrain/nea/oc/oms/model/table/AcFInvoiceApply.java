package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@TableName(value = "ac_f_invoice_apply")
@Data
@Document(index = "ac_f_invoice_apply",type = "ac_f_invoice_apply")
public class AcFInvoiceApply extends BaseModel {
    private static final long serialVersionUID = 7694312441469712985L;
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "TID")
    @Field(type = FieldType.Keyword)
    private String tid;

    @JSONField(name = "CP_C_SHOP_ID")
    @Field(type = FieldType.Long)
    private Long cpCShopId;

    @JSONField(name = "CP_C_SHOP_TITLE")
    @Field(type = FieldType.Keyword)
    private String cpCShopTitle;

    @JSONField(name = "CP_C_SHOP_ECODE")
    @Field(type = FieldType.Keyword)
    private String cpCShopEcode;

    @JSONField(name = "CP_C_PLATFORM_ID")
    @Field(type = FieldType.Long)
    private Long cpCPlatformId;

    @JSONField(name = "CP_C_PLATFORM_ENAME")
    @Field(type = FieldType.Keyword)
    private String cpCPlatformEname;

    @JSONField(name = "CP_C_PLATFORM_ECODE")
    @Field(type = FieldType.Keyword)
    private String cpCPlatformEcode;

    @JSONField(name = "INVOICE_KIND")
    @Field(type = FieldType.Keyword)
    private String invoiceKind;

    @JSONField(name = "APPLY_DATE")
    @Field(type = FieldType.Long)
    private Date applyDate;

    @JSONField(name = "APPLY_BILL_NO")
    @Field(type = FieldType.Keyword)
    private String applyBillNo;

    @JSONField(name = "INVOICE_AMT")
    @Field(type = FieldType.Double)
    private BigDecimal invoiceAmt;

    @JSONField(name = "INVOICE_REMARK")
    @Field(type = FieldType.Keyword)
    private String invoiceRemark;

    @JSONField(name = "INVOICE_TYPE")
    @Field(type = FieldType.Keyword)
    private String invoiceType;

    @JSONField(name = "HEADER_TYPE")
    @Field(type = FieldType.Keyword)
    private String headerType;

    @JSONField(name = "INVOICE_HEADER")
    @Field(type = FieldType.Keyword)
    private String invoiceHeader;

    @JSONField(name = "TAXPAYER_NO")
    @Field(type = FieldType.Keyword)
    private String taxpayerNo;

    @JSONField(name = "INVOICE_ADDRESS")
    @Field(type = FieldType.Keyword)
    private String invoiceAddress;

    @JSONField(name = "MOBILE")
    @Field(type = FieldType.Keyword)
    private String mobile;

    @JSONField(name = "OPENING_BANK")
    @Field(type = FieldType.Keyword)
    private String openingBank;

    @JSONField(name = "BANK_ACCOUNT")
    @Field(type = FieldType.Keyword)
    private String bankAccount;

    @JSONField(name = "RECEIVER")
    @Field(type = FieldType.Keyword)
    private String receiver;

    @JSONField(name = "RECEIVER_PHONE")
    @Field(type = FieldType.Keyword)
    private String receiverPhone;

    @JSONField(name = "RECEIVER_ADDRESS")
    @Field(type = FieldType.Keyword)
    private String receiverAddress;

    @JSONField(name = "EMAIL")
    @Field(type = FieldType.Keyword)
    private String email;

    @JSONField(name = "REMARK")
    @Field(type = FieldType.Keyword)
    private String remark;

    @JSONField(name = "CHANGE_INVOICE_TYPE")
    @Field(type = FieldType.Keyword)
    private String changeInvoiceType;

    @JSONField(name = "TRANS_STATUS")
    @Field(type = FieldType.Keyword)
    private String transStatus;

    @JSONField(name = "TRANS_ID")
    @Field(type = FieldType.Long)
    private Long transId;

    @JSONField(name = "FAIL_COUNT")
    @Field(type = FieldType.Integer)
    private Integer failCount;

    @JSONField(name = "FAIL_REASON")
    @Field(type = FieldType.Keyword)
    private String failReason;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;

    @JSONField(name = "MERGE_INVOICE_FLAG")
    @Field(type = FieldType.Keyword)
    private String mergeInvoiceFlag;

    /** 合并开票时关联发票id,逗号分割*/
    @JSONField(name = "MERGE_ORDER_INVOICE_ID")
    @Field(type = FieldType.Keyword)
    private String mergeOrderInvoiceId;

    @JSONField(name = "NEXT_TIME")
    @Field(type = FieldType.Long)
    private Date nextTime;

    /** 关联红票id */
    @JSONField(name = "RED_TICKET_ID")
    @Field(type = FieldType.Long)
    private Long redTicketId;

    @JSONField(name = "TRANS_DATE")
    @Field(type = FieldType.Long)
    private Date transDate;
}