package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBReturnOrderWarningCmd;
import com.jackrain.nea.oc.request.OcBReturnOrderWarningRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2021/12/15 11:32
 */
@RestController
@Slf4j
@Api(value = "OcBReturnOrderRemarkCtrl", tags = "异常订单预警")
public class OcBReturnOrderWarningController {

    @Autowired
    OcBReturnOrderWarningCmd ocBReturnOrderWarningCmd;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @ApiOperation(value = "异常订单预警")
    @RequestMapping(path = "/api/cs/oc/oms/v1/warning", method = RequestMethod.POST)
    public ValueHolderV14 warning(@RequestBody String param, HttpServletRequest request) {

        ValueHolderV14 vh = new ValueHolderV14();
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        if (log.isDebugEnabled()) {
            log.debug(" OcBReturnOrderWarningController reqeust {} user {}", param,
                    JSONObject.toJSONString(user));
        }
        try {
            List<OcBReturnOrderWarningRequest> requests = JSONObject.parseArray(param, OcBReturnOrderWarningRequest.class);
//            JSONArray array = JSONArray.parseArray(param);
//            List<OcBReturnOrderWarningRequest> requests = new ArrayList<>();
//            for (int i = 0; i < array.size(); i++) {
//                OcBReturnOrderWarningRequest warningRequest = new OcBReturnOrderWarningRequest();
//                JSONObject object = array.getJSONObject(i);
//                JSONArray ids = object.getJSONArray("IDS");
//                Integer warningtype = object.getInteger("WARNINGTYPE");
//                warningRequest.setOrderIds(ids.toJavaList(Long.class));
//                warningRequest.setWarningType(warningtype);
//                requests.add(warningRequest);
//            }
            //List<OcBReturnOrderWarningRequest> requests = array.toJavaList(OcBReturnOrderWarningRequest.class);
            vh = ocBReturnOrderWarningCmd.warningSave(requests);
            return vh;
        } catch (NDSException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return vh;
        }
    }
}
