package com.jackrain.nea.oc.oms.mq.processor.impl.mq;

import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.burgeon.mq.annotation.RocketMqMessageListener;
import com.burgeon.mq.core.BaseMessageListener;
import com.burgeon.mq.enums.MqTypeEnum;
import com.burgeon.mq.error.MqException;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.services.ModifyOrderLogisticsService;
import com.jackrain.nea.oc.oms.services.ModifyWarehouseService;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@RocketMqMessageListener(name = "ModifyOrderLogisticsListenMq", type = MqTypeEnum.DEFAULT)
public class ModifyOrderLogisticsListenMq implements BaseMessageListener {


    @Autowired
    private
    ModifyOrderLogisticsService service;

    @Override
    public void consume(String messageBody, String messageTopic, String messageKey, String messageTag, Object object) {
        try {
            service.modifyOrderLogisticHandler(messageBody, messageKey);
        }catch (Exception e) {
            log.error(LogUtil.format("ModifyOrderLogisticsListenMq.ExpMsg: {}"), Throwables.getStackTraceAsString(e));
            throw new MqException(e);
        }
    }
}
