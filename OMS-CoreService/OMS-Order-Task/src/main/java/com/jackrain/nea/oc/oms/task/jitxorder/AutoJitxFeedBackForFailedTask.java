package com.jackrain.nea.oc.oms.task.jitxorder;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.es.ES4IpJitXDelivery;
import com.jackrain.nea.oc.oms.model.relation.IpJitxDeliveryRelation;
import com.jackrain.nea.oc.oms.process.jitx.feedback.JitxFeedBackDeliveryProcessImpl;
import com.jackrain.nea.oc.oms.services.IpJitxDeliveryService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * JITX寻仓反馈失败补偿任务
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class AutoJitxFeedBackForFailedTask extends BaseR3Task implements IR3Task {
    @Autowired
    private IpJitxDeliveryService ipJitxDeliveryService;

    @Autowired
    private JitxFeedBackDeliveryProcessImpl jitxFeedBackDeliveryProcess;

    @Value("${lts.jitx.feedback.failed.task.size:500}")
    private int pageSize;

    @Override
    @XxlJob("AutoJitxFeedBackForFailedTask")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult result = new RunTaskResult();
        try {
            List<String> orderNoList = ES4IpJitXDelivery.findOrderSnBySynStatus(0, pageSize, true);

            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("AutoJitxFeedBackForFailedTask###OrderNoList={}"), JSONObject.toJSONString(orderNoList));
            }

            List<IpJitxDeliveryRelation> deliveryRelationList = new ArrayList<>();
            for (String orderNo : orderNoList) {
                IpJitxDeliveryRelation jitDeliveryRelation = this.ipJitxDeliveryService.selectJitxDelivery(orderNo);
                if (jitDeliveryRelation == null) {
                    String errorMessage = Resources.getMessage("###AutoJitxFeedBackForFailedTask.Order" +
                            ".NotExist!###OrderNo=" + orderNo);
                    log.error(LogUtil.format(errorMessage));
                } else {
                    deliveryRelationList.add(jitDeliveryRelation);
                }
            }

            threadOrderProcessor.startMultiThreadExecute(this.jitxFeedBackDeliveryProcess, deliveryRelationList);

            result.setSuccess(true);
        } catch (Exception ex) {
            log.error(LogUtil.format("AutoJitxFeedBackForFailedTask.Execute: {}"), Throwables.getStackTraceAsString(ex));
            result.setSuccess(false);
            result.setMessage(ex.getMessage());
        }

        return result;

    }
}
