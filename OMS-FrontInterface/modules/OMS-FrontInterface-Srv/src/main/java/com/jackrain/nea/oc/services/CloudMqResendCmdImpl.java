package com.jackrain.nea.oc.services;

import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.CloudMqResendCmd;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.oc.oms.services.OmsReturnOrderService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * Description： 手工重发业务消息
 * Author: RESET
 * Date: Created in 2020/8/16 18:22
 * Modified By:
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class CloudMqResendCmdImpl implements CloudMqResendCmd {

    @Autowired
    OmsReturnOrderService returnOrderService;

    /**
     * 销退单发送消息
     *
     * @param returnOrderId
     * @param user
     * @return
     * @throws NDSException
     */
    @Override
    public ValueHolderV14 returnOrder2PosMqRetry(Long returnOrderId, User user) throws NDSException {
        ValueHolderV14 vh = new ValueHolderV14();

        if (Objects.nonNull(returnOrderId)) {
            OcBReturnOrder returnOrder = returnOrderService.selectReturnOrderById(returnOrderId);

            if (Objects.nonNull(returnOrder)) {
                // 查找明细
                List<OcBReturnOrderRefund> refunds = returnOrderService.selectRefundsByOcOrderId(returnOrderId);

                if (CollectionUtils.isNotEmpty(refunds)) {
                    boolean r = returnOrderService.send4O2OByRetailReturn(returnOrder, refunds);

                    if (!r) {
                        vh.setCode(ResultCode.FAIL);
                        vh.setMessage(Resources.getMessage("发送失败，请联系IT", user.getLocale()));
                        return vh;
                    }
                } else {
                    vh.setCode(ResultCode.FAIL);
                    vh.setMessage(Resources.getMessage("依据" + returnOrderId + "找不到对应的退单明细", user.getLocale()));
                    return vh;
                }
            } else {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage(Resources.getMessage("依据" + returnOrderId + "找不到对应的退单", user.getLocale()));
                return vh;
            }
        }

        vh.setCode(ResultCode.SUCCESS);
        vh.setMessage(Resources.getMessage("发送成功", user.getLocale()));
        return vh;
    }
}
