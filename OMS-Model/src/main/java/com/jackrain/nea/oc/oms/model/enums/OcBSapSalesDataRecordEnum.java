package com.jackrain.nea.oc.oms.model.enums;

import lombok.Getter;

/**
 * Description: 汇总leixing
 *
 * @Author: guo.kw
 * @Since: 2022/8/30
 * create at: 2022/8/30 10:40
 */
public enum OcBSapSalesDataRecordEnum {
    SUM_TYPE_ZERO("0", "销售汇总"),
    SUM_TYPE_ONE("1", "奶卡汇总"),
    SUM_TYPE_TWO("2", "库存调整单汇总");


    @Getter
    private String code;

    @Getter
    private String message;

    OcBSapSalesDataRecordEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public static String getMessage(String key) {
        String mes = "";
        for (OcBSapSalesDataRecordEnum value : OcBSapSalesDataRecordEnum.values()) {
            if (value.getCode().equals(key)) {
                mes = value.getMessage();
            }
        }
        return mes;
    }
}
