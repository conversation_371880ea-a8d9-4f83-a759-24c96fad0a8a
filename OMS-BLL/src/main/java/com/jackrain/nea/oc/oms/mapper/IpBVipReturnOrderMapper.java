package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.IpBVipReturnOrder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.jdbc.SQL;

/**
 * <AUTHOR>
 * @date 2020/6/20 10:05
 * 唯品会退供单Mapper
 */

@Mapper
public interface IpBVipReturnOrderMapper extends ExtentionMapper<IpBVipReturnOrder> {

    /**
     * 根据退供单号查到退供中间表
     *
     * @param orderNo 退供单号
     * @return 退供单详情
     */
    @Select("SELECT * FROM ip_b_vip_return_order WHERE return_sn=#{orderNo} and (trans_status = 0 or trans_status = 4)")
    IpBVipReturnOrder selectVipReturnOrderByOrderSnAndTransStatus(String orderNo);

    /**
     * 根据退供单号查到退供中间表
     *
     * @param orderNo 退供单号
     * @return 退供单详情
     */
    @Select("SELECT * FROM ip_b_vip_return_order WHERE return_sn=#{orderNo}")
    IpBVipReturnOrder selectVipReturnOrderByOrderSn(String orderNo);

    /**
     * 根据退供单号更新转换状态、转换次数、转换时间、系统备注
     *
     * @param remark      系统备注
     * @param orderNo     退供单号
     * @param transStatus 转换状态
     * @return 返回更新行数
     */
//    @Update({"UPDATE ip_b_vip_return_order SET trans_nums = IFNULL(trans_nums, 0) + 1,
//    trans_status = #{trans_status},trans_time = now(),
//    remark = #{remark} where return_sn = #{return_sn}"})
    @UpdateProvider(type = IpBVipReturnOrderMapper.IpBVipReturnOrderSqlBuilder.class, method = "buildUpdateRemark")
    int updateRemark(@Param("remark") String remark,
                     @Param("return_sn") String orderNo, @Param("trans_status") int transStatus);

    /**
     * 根据退供单号更新转换状态、转换次数、转换时间、系统备注
     *
     * @param remark      系统备注
     * @param orderNo     退供单号
     * @param transStatus 转换状态
     * @return 返回更新行数
     */
//    @Update({"UPDATE ip_b_vip_return_order SET trans_status = #{trans_status},trans_time = now(),remark = #{remark}
//    where return_sn = #{return_sn}"})
    @UpdateProvider(type = IpBVipReturnOrderMapper.IpBVipReturnOrderSqlBuilder.class,
            method = "buildUpdateTransferStatus")
    int updateTransferStatus(@Param("remark") String remark,
                             @Param("return_sn") String orderNo, @Param("trans_status") int transStatus);

    /**
     * 唯品会退供单SQL创建器
     */
    class IpBVipReturnOrderSqlBuilder {
        /**
         * @param remark      备注
         * @param orderNo     退供单号
         * @param transStatus 转换状态
         * @return java.lang.String :注释
         * <AUTHOR>
         * @date 2020/6/29 10:05
         * @description：TODO
         */
        public String buildUpdateRemark(@Param("remark") String remark,
                                        @Param("return_sn") String orderNo,
                                        @Param("trans_status") int transStatus) {

            return new SQL() {
                {
                    UPDATE("ip_b_vip_return_order");
                    SET("trans_nums = IFNULL(trans_nums, 0) + 1");
                    SET("trans_status = #{trans_status}");
                    SET("trans_time = now()");
                    SET("remark = #{remark}");
                    WHERE("return_sn=#{return_sn}");
                }
            }.toString();
        }


        /**
         * @param remark      备注
         * @param orderNo     退供单号
         * @param transStatus 转换状态
         * @return java.lang.String :注释
         * <AUTHOR>
         * @date 2020/6/29 10:06
         * @description：TODO
         */
        public String buildUpdateTransferStatus(@Param("remark") String remark,
                                                @Param("return_sn") String orderNo,
                                                @Param("trans_status") int transStatus) {

            return new SQL() {
                {

                    UPDATE("ip_b_vip_return_order");
                    SET("trans_status = #{trans_status}");
                    SET("trans_time = now()");
                    SET("remark = #{remark}");
                    WHERE("return_sn=#{return_sn}");
                }
            }.toString();
        }
    }
}
