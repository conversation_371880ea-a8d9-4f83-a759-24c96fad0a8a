package com.jackrain.nea.oc.services.qimen;

import com.alibaba.fastjson.JSON;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.ip.model.qimen.QimenPosOrderStatusQueryResult;
import com.jackrain.nea.oc.oms.api.qimen.QimenPosOrderStatusCmd;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OmsQimenPosOrderStatusEnum;
import com.jackrain.nea.oc.oms.model.request.QimenPosOrderStatusRequest;
import com.jackrain.nea.oc.oms.model.result.QimenPosOrderStatusResult;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.services.qimen.PosOrderStatusCallBackService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 奇门POS-订单状态查询接口
 *
 * @Auther: 黄志优
 * @Date: 2020/8/30 13:27
 * @Description:
 */
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
@Slf4j
@Component
public class QimenPosOrderStatusCmdImpl implements QimenPosOrderStatusCmd {

    @Autowired
    PosOrderStatusCallBackService posOrderStatusCallBackService;

    @Override
    public ValueHolderV14<QimenPosOrderStatusResult> queryOrderStatus(QimenPosOrderStatusRequest qimenPosOrderStatusRequest) {
        ValueHolderV14<QimenPosOrderStatusResult> resultValueHolderV14 = new ValueHolderV14<>();
        try {
            String orderBillCode = null;

            if (qimenPosOrderStatusRequest == null
                    || StringUtils.isBlank(qimenPosOrderStatusRequest.getOrderId())) {
                resultValueHolderV14.setCode(ResultCode.FAIL);
                resultValueHolderV14.setMessage("请求参数为空");
                return resultValueHolderV14;
            }

            orderBillCode = qimenPosOrderStatusRequest.getOrderId();

            OcBOrder ocBOrder = posOrderStatusCallBackService.selectOmsOrderInfo(orderBillCode);
            Integer orderStatus = null;
            if (ocBOrder != null && (orderStatus = ocBOrder.getOrderStatus()) != null) {

                OmsQimenPosOrderStatusEnum omsQimenPosOrderStatusEnum = orderStatusConversionPos(orderStatus);

                if (omsQimenPosOrderStatusEnum == null) {
                    resultValueHolderV14.setCode(ResultCode.FAIL);
                    resultValueHolderV14.setMessage("订单状态查询失败");

                    return resultValueHolderV14;
                }

                QimenPosOrderStatusResult result = new QimenPosOrderStatusResult(omsQimenPosOrderStatusEnum.getVal(),
                        ocBOrder.getIsInreturning());
                resultValueHolderV14.setCode(ResultCode.SUCCESS);
                resultValueHolderV14.setMessage("订单状态查询成功");
                resultValueHolderV14.setData(result);
            } else {
                resultValueHolderV14.setCode(ResultCode.FAIL);
                resultValueHolderV14.setMessage("该出库单号【" + orderBillCode + "】未找到订单信息");
            }
        } catch (Exception e) {
            resultValueHolderV14.setCode(ResultCode.FAIL);
            resultValueHolderV14.setMessage("订单状态查询异常");
        }

        return resultValueHolderV14;
    }

    OmsQimenPosOrderStatusEnum orderStatusConversionPos(Integer orderStatus) {
        if (orderStatus == null || orderStatus == 0) {
            return null;
        } else if (OmsOrderStatus.IN_DISTRIBUTION.toInteger().equals(orderStatus)) {
            return OmsQimenPosOrderStatusEnum.IN_DISTRIBUTION;
        } else if (OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger().equals(orderStatus)) {
            return OmsQimenPosOrderStatusEnum.WAREHOUSE_DELIVERY;
        } else if (OmsOrderStatus.PLATFORM_DELIVERY.toInteger().equals(orderStatus)) {
            return OmsQimenPosOrderStatusEnum.PLATFORM_DELIVERY;
        } else if (OmsOrderStatus.CANCELLED.toInteger().equals(orderStatus)
                || OmsOrderStatus.SYS_VOID.toInteger().equals(orderStatus)) {
            return OmsQimenPosOrderStatusEnum.CANCELLED;
        } else {
            return null;
        }
    }

    @Override
    public ValueHolderV14<QimenPosOrderStatusQueryResult> queryOrderStatus(QimenPosOrderStatusRequest request, User user) {
        return posOrderStatusCallBackService.queryOrderStatus(request, user);
    }
}