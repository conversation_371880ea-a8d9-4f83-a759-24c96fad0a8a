package com.jackrain.nea.oc.oms.services.invoice;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.google.common.base.Throwables;
import com.jackrain.nea.ac.service.InvoiceApplyService;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.ac.AcFOrderInvoiceMapper;
import com.jackrain.nea.oc.oms.model.constant.InvoiceConst;
import com.jackrain.nea.oc.oms.model.table.AcFInvoiceApplyItem;
import com.jackrain.nea.oc.oms.model.table.AcFOrderInvoice;
import com.jackrain.nea.oc.oms.nums.OmsParamConstant;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.util.*;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/09/12 13:20
 */
@Slf4j
@Component
public class AcFOrderInvoiceCancelService {
    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    @Autowired
    private AcFOrderInvoiceMapper acOrderInvoiceMapper;

    @Autowired
    private InvoiceLogService invoiceLogService;

    @Autowired
    private InvoiceApplyService invoiceApplyService;

    public ValueHolder execute(QuerySession querySession) throws NDSException {
        SgR3BaseRequest request = R3ParamUtils.parseSaveObject(querySession, SgR3BaseRequest.class);
        request.setR3(true);
        AcFOrderInvoiceCancelService service = ApplicationContextHandle.getBean(this.getClass());
        return service.cancel(request);
    }

    @Transactional(rollbackFor = Exception.class)
    public ValueHolder cancel(SgR3BaseRequest request) {
        List<Long> batchObjIds = R3ParamUtils.getBatchObjIds(request);
        // 存储错误的Map
        Map<Long, Object> errorMap = new HashMap<>(batchObjIds.size());
        for (Long objId : batchObjIds) {
            String lockRedisKey = InvoiceConst.AC_F_ORDER_INVOICE + ":" + request.getObjId();
            RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
            try {
                if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                    AcFOrderInvoice acOrderInvoice = acOrderInvoiceMapper.selectById(objId);
                    if (acOrderInvoice == null) {
                        errorMap.put(objId, "当前记录已不存在！");
                        continue;
                    }
                    if (OmsParamConstant.ONE.equals(acOrderInvoice.getAuditStatus()) || OmsParamConstant.ONE.equals(acOrderInvoice.getCancelStatus())) {
                        errorMap.put(objId, "状态已取消或已审核，不能取消！");
                        continue;
                    }
                    acOrderInvoice.setCancelStatus(OmsParamConstant.ONE);
                    BaseModelUtil.setupUpdateParam(acOrderInvoice, request.getLoginUser());
                    acOrderInvoiceMapper.updateById(acOrderInvoice);
                    // 将发票申请明细的tid添加后缀,防止无法进行二次申请开票
                    Long invoiceApplyId = acOrderInvoice.getInvoiceApplyId();
                    if (Objects.nonNull(invoiceApplyId) && invoiceApplyId.compareTo(0L) > 0) {
                        // 取消后修改申请单明细tid
                        invoiceApplyService.updateInvoiceApplyItemTidSuffix(invoiceApplyId);
                    }

                    invoiceLogService.addUserOrderLog(acOrderInvoice.getId(),"取消","未取消改为已取消",request.getLoginUser());

                } else {
                    errorMap.put(objId, "当前发票处于锁定状态！");
                }
            } catch (Exception e) {
                log.error(LogUtil.format("AcFOrderInvoiceCancelService.cancel.error={}", "error"),
                        Throwables.getStackTraceAsString(e));
                throw new NDSException("发票管理取消异常!");
            } finally {
                redisLock.unlock();
            }
        }
        return R3ParamUtils.getExcuteValueHolder(batchObjIds.size(), errorMap);
    }

}
