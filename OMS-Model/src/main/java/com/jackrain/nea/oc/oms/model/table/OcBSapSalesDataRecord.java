package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.util.Date;

@TableName(value = "oc_b_sap_sales_data_record")
@Data
public class OcBSapSalesDataRecord extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "BILL_TYPE")
    private String billType;

    @JSONField(name = "BILL_NO")
    private String billNo;

    @JSONField(name = "MIDDLEGROUND_BILL_TYPE")
    private Integer middlegroundBillType;

    /** 业务类型名称 */
    @JSONField(name = "MIDDLEGROUND_BILL_TYPE_NAME")
    private String middlegroundBillTypeName;

    /** 业务类型名称 */
    @JSONField(name = "MIDDLEGROUND_BILL_TYPE_CODE")
    private String middlegroundBillTypeCode;

    @JSONField(name = "SAP_BILL_TYPE")
    private String sapBillType;

    @JSONField(name = "SALES_ORGANIZATION")
    private String salesOrganization;

    @JSONField(name = "CP_C_SHOP_ID")
    private Integer cpCShopId;

    @JSONField(name = "CP_C_SHOP_ECODE")
    private String cpCShopEcode;

    @JSONField(name = "CP_C_SHOP_ENAME")
    private String cpCShopEname;

    @JSONField(name = "ORDER_CP_C_SHOP_ID")
    private Long orderCpCShopId;

    @JSONField(name = "ORDER_CP_C_SHOP_ECODE")
    private String orderCpCShopEcode;

    @JSONField(name = "ORDER_CP_C_SHOP_ENAME")
    private String orderCpCShopEname;

    @JSONField(name = "IN_TIME")
    private Date inTime;

    @JSONField(name = "MERGE_CODE")
    private String mergeCode;

    @JSONField(name = "SUM_STATUS")
    private String sumStatus;

    @JSONField(name = "ABNORMAL_REASON")
    private String abnormalReason;

    @JSONField(name = "SUM_TYPE")
    private String sumType;

    @JSONField(name = "COST_CENTER")
    private String costCenter;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    @JSONField(name = "OC_B_SAP_SALES_DATA_GATHER_ID")
    private Long ocBSapSalesDataGatherId;

    @JSONField(name = "FACTORY_CODE")
    private String factoryCode;

}