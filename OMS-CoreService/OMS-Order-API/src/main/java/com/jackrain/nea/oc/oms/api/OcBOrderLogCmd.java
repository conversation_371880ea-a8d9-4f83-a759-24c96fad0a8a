package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.web.face.User;

import java.util.List;

/**
 * @author: DXF
 * @since: 2020/12/7
 * create at : 2020/12/7 10:32
 */
public interface OcBOrderLogCmd {

    void addUserOrderLog(long orderId, String billNo, String logType, String logMessage,
                         String param, String errorMessage, User operateUser);


    void addUserOrderLogList(List<OcBOrder> list, String logType, String logMessage,
                             String param, String errorMessage, User operateUser);
}
