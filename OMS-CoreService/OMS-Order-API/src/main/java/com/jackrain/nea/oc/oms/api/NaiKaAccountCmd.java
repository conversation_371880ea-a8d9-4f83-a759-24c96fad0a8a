package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.oc.oms.model.request.NaiKaAccountInitRequest;
import com.jackrain.nea.oc.oms.model.request.NaiKaAccountRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;

/**
 * @ClassName NaiKaAccountCmd
 * @Description 奶卡对账回传
 * <AUTHOR>
 * @Date 2022/9/5 09:42
 * @Version 1.0
 */
public interface NaiKaAccountCmd {

    /**
     * 奶卡对账回传
     *
     * @param request
     * @return
     */
    ValueHolderV14 naiKaAccount(NaiKaAccountRequest request);

    /**
     * 结算单取消 奶卡金额重置
     *
     * @param naiKaAccountInitRequest
     * @return
     */
    ValueHolderV14 naiKaAccountInit(NaiKaAccountInitRequest naiKaAccountInitRequest);

}
