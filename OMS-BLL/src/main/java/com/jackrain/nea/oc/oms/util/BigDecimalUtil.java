package com.jackrain.nea.oc.oms.util;

import java.math.BigDecimal;

/**
 * @author: 王帅
 * @since: 2020/4/15
 * create at : 2020/4/15 10:11
 */
public class BigDecimalUtil {

    /**
     * 如果传入数据为Null，返回0，否是返回自己
     *
     * @param amt
     * @return
     */
    public static BigDecimal isNullReturnZero(BigDecimal amt) {
        return amt == null ? BigDecimal.ZERO : amt;
    }

    /**
     * 传入参数是否不为零
     *
     * @param amt
     * @return true: 非零 false：是零
     */
    public static boolean isNotZero(BigDecimal amt) {
        return !isZero(amt);
    }

    /**
     * 传入参数是否为零
     *
     * @param amt
     * @return true: 是零 false：非零
     */
    public static boolean isZero(BigDecimal amt) {
        return isNullReturnZero(amt).compareTo(BigDecimal.ZERO) == 0;
    }

    /**
     * 两个数是否相等
     *
     * @param amt1
     * @param amt2
     * @return true: 相等 false：不相等
     */
    public static boolean equals(BigDecimal amt1, BigDecimal amt2) {
        return isNullReturnZero(amt1).compareTo(isNullReturnZero(amt2)) == 0;
    }

    /**
     * 乘法
     */
    public static BigDecimal multiply(BigDecimal amt1, BigDecimal am2) {
        return isNullReturnZero(amt1).multiply(isNullReturnZero(am2));
    }

    /**
     * 减法
     */
    public static BigDecimal subtract(BigDecimal amt1, BigDecimal am2) {
        return isNullReturnZero(amt1).subtract(isNullReturnZero(am2));
    }

    /**
     * 当 amt1 == 0 或者 null 时改成后者
     *
     * @param amt1 amt1
     * @param am2  amt2
     * @return BigDecimal
     */
    public static BigDecimal eqZeroChange(BigDecimal amt1, BigDecimal am2) {
        return isZero(amt1) ? isNullReturnZero(am2) : amt1;
    }


    private BigDecimalUtil() {
    }
}
