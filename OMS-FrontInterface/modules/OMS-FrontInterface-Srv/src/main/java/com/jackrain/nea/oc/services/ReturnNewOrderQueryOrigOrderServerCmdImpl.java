package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.ReturnNewOrderQueryOrigOrderCmd;
import com.jackrain.nea.oc.oms.services.ReturnNewOrderQueryOrigOrderService;
import com.jackrain.nea.oc.oms.services.ReturnOrderSkuDBService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 退换货订单
 *
 * @date 2019/9/16
 * @author: ming.fz
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class ReturnNewOrderQueryOrigOrderServerCmdImpl implements ReturnNewOrderQueryOrigOrderCmd {

    @Autowired
    ReturnNewOrderQueryOrigOrderService returnNewOrderQueryOrigOrderService;

    @Autowired
    ReturnOrderSkuDBService returnOrderSkuDBService;

    @Override
    public ValueHolderV14<String> queryOcBOrder(JSONObject param, User user) throws NDSException {
        return returnNewOrderQueryOrigOrderService.queryOrder(param, user);
    }

    @Override
    public ValueHolderV14<List> updateReturnBOrder(JSONObject param, User user) throws NDSException {
        return returnNewOrderQueryOrigOrderService.updateReturnBOrder(param, user);
    }

    @Override
    public ValueHolderV14<List> skuDb(Long id, User user) throws NDSException {
        return returnOrderSkuDBService.markAsCompleted(id, user);
    }
}