<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.0.6.RELEASE</version>
        <relativePath/>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.burgeon.r3</groupId>
    <artifactId>r3-oc-oms</artifactId>
    <packaging>pom</packaging>
    <version>3.0.0-SNAPSHOT</version>

    <properties>
        <snapshots.id>dayu-maven-snapshots</snapshots.id>
        <snapshots.name>dayu-maven-snapshots</snapshots.name>
        <snapshots.url>https://nexus.dayu.work/repository/bp46534027-snapshots</snapshots.url>

        <project.stable.version>3.0.0-SNAPSHOT</project.stable.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <java.version>1.8</java.version>

        <raincloud.version>1.4.0-SNAPSHOT</raincloud.version>
        <raincloud.dubbo.version>1.4.1-SNAPSHOT</raincloud.dubbo.version>
        <raincloud.edas.version>1.4.1-SNAPSHOT</raincloud.edas.version>
        <r3.version>1.4.0-SNAPSHOT</r3.version>
        <r3.framework.version>3.0.0-SNAPSHOT</r3.framework.version>
        <r3.project.version>3.0.0-SNAPSHOT</r3.project.version>
    </properties>

    <modules>
        <module>OMS-BLL</module>
        <module>OMS-CoreService/OMS-Order-API</module>
        <module>OMS-CoreService/OMS-Order-Core</module>
        <module>OMS-CoreService/OMS-Order-Ctrl</module>
        <module>OMS-CoreService/OMS-Order-Process</module>
        <module>OMS-CoreService/OMS-Order-Shell</module>
        <module>OMS-CoreService/OMS-Order-Srv</module>
        <module>OMS-CoreService/OMS-Order-Task</module>
        <module>OMS-FrontInterface</module>
        <module>OMS-Model</module>
        <module>OMS-CoreService/OMS-Order-SplitEngine</module>
        <module>OMS-Shell/OMS-Order-ToBeConfirm-Shell</module>
        <module>OMS-Shell/OMS-Order-Task-Shell</module>
        <module>OMS-Shell/OMS-Transfer-Shell</module>
        <module>OMS-Shell/OMS-Audit-Shell</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <!-- raincloud -->
            <dependency>
                <groupId>org.syman</groupId>
                <artifactId>raincloud-web</artifactId>
                <version>${raincloud.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>spring-aop</artifactId>
                        <groupId>org.springframework</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>fastjson</artifactId>
                        <groupId>com.alibaba</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>guava</artifactId>
                        <groupId>com.google.guava</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>poi</artifactId>
                        <groupId>org.apache.poi</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>poi-ooxml</artifactId>
                        <groupId>org.apache.poi</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.syman</groupId>
                <artifactId>raincloud-es</artifactId>
                <version>${raincloud.version}</version>
            </dependency>
            <dependency>
                <groupId>org.syman</groupId>
                <artifactId>raincloud-dubbo</artifactId>
                <version>${raincloud.dubbo.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.dubbo</groupId>
                        <artifactId>dubbo-spring-boot-starter</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.dubbo</groupId>
                        <artifactId>dubbo</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.syman</groupId>
                <artifactId>raincloud-jdbc</artifactId>
                <version>${raincloud.version}</version>
            </dependency>
            <dependency>
                <groupId>org.syman</groupId>
                <artifactId>raincloud-util</artifactId>
                <version>${raincloud.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.poi</groupId>
                        <artifactId>poi-ooxml</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.google.guava</groupId>
                        <artifactId>guava</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- r3-framework -->
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>r3-service-api</artifactId>
                <version>${r3.framework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>r3-common-util</artifactId>
                <version>${r3.framework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>r3-common-util-web</artifactId>
                <version>${r3.framework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>r3-model-query</artifactId>
                <version>${r3.framework.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>apollo-client</artifactId>
                        <groupId>com.ctrip.framework.apollo</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>apollo-core</artifactId>
                        <groupId>com.ctrip.framework.apollo</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>raincloud-dubbo</artifactId>
                        <groupId>org.syman</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>r3-model-base</artifactId>
                <version>${r3.framework.version}</version>
            </dependency>
<!--            <dependency>-->
<!--                <groupId>com.burgeon.r3</groupId>-->
<!--                <artifactId>r3-mq</artifactId>-->
<!--                <version>3.1.0-SNAPSHOT</version>-->
<!--                <exclusions>-->
<!--                    <exclusion>-->
<!--                        <artifactId>apollo-core</artifactId>-->
<!--                        <groupId>com.ctrip.framework.apollo</groupId>-->
<!--                    </exclusion>-->
<!--                </exclusions>-->
<!--            </dependency>-->
            <dependency>
                <groupId>com.burgeon.mq</groupId>
                <artifactId>mq-spring-boot-starter</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>r3-sysapi</artifactId>
                <version>${r3.framework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>r3-log</artifactId>
                <version>${r3.framework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>r3-service-util</artifactId>
                <version>${r3.framework.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>apollo-client</artifactId>
                        <groupId>com.ctrip.framework.apollo</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>apollo-core</artifactId>
                        <groupId>com.ctrip.framework.apollo</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>raincloud-dubbo</artifactId>
                        <groupId>org.syman</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--            <dependency>-->
            <!--                <groupId>com.burgeon.r3</groupId>-->
            <!--                <artifactId>r3-service-task</artifactId>-->
            <!--                <version>3.1.0-SNAPSHOT</version>-->
            <!--            </dependency>-->
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>ryytn-xxl-job-spring-boot-starter</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.yomahub</groupId>
                <artifactId>tlog-xxljob-spring-boot-starter</artifactId>
                <version>1.5.2-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>r3-sysapi-ext</artifactId>
                <version>${r3.framework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>r3-service-impl-dubbo</artifactId>
                <version>${r3.framework.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.syman</groupId>
                        <artifactId>r3-web-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.syman</groupId>
                        <artifactId>r3-srv-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.syman</groupId>
                        <artifactId>raincloud-sysapi</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.syman</groupId>
                        <artifactId>raincloud-web-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.syman</groupId>
                        <artifactId>raincloud-model-util</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.syman</groupId>
                        <artifactId>raincloud-model-base</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.syman</groupId>
                        <artifactId>r3-web-shiro</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>R3-Starter-Dubbo</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>

            <!-- r3-project -->
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>r3-retail-api</artifactId>
                <version>${r3.project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>r3-sg-share-api</artifactId>
                <version>${r3.project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>r3-sg-store-api</artifactId>
                <version>${r3.project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>r3-ip-api</artifactId>
                <version>${r3.project.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>r3-oc-oms-vip-model</artifactId>
                        <groupId>com.burgeon.r3</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>r3-sg-basic-api</artifactId>
                <version>${r3.project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>r3-service-core</artifactId>
                <version>3.1.0-SNAPSHOT</version>
                <exclusions>
                    <exclusion>
                        <artifactId>spring-aop</artifactId>
                        <groupId>org.springframework</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>apollo-client</artifactId>
                        <groupId>com.ctrip.framework.apollo</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>apollo-core</artifactId>
                        <groupId>com.ctrip.framework.apollo</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.syman</groupId>
                        <artifactId>raincloud-dubbo</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>r3-ad-util</artifactId>
                <version>${r3.project.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.syman</groupId>
                        <artifactId>raincloud-dubbo</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>r3-ps-api</artifactId>
                <version>${r3.project.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>sysapi</artifactId>
                        <groupId>org.syman</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>r3-ps-ext-api</artifactId>
                <version>${r3.project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>r3-cp-ext-api</artifactId>
                <version>${r3.project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>r3-cp-api</artifactId>
                <version>${r3.project.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>sysapi</artifactId>
                        <groupId>org.syman</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>r3-oc-oms-core-api</artifactId>
                <version>${r3.project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>r3-oc-oms-bll</artifactId>
                <version>${r3.project.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.google.guava</groupId>
                        <artifactId>guava</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>r3-oc-basic-srv</artifactId>
                <version>${r3.project.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>r3-cp-ext-api</artifactId>
                        <groupId>com.burgeon.r3</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>r3-cp-api</artifactId>
                        <groupId>com.burgeon.r3</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>apollo-client</artifactId>
                        <groupId>com.ctrip.framework.apollo</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>apollo-core</artifactId>
                        <groupId>com.ctrip.framework.apollo</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>r3-oc-oms-core</artifactId>
                <version>${r3.project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>r3-oc-oms-core-srv</artifactId>
                <version>${r3.project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>r3-oc-oms-task</artifactId>
                <version>${r3.project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>r3-oc-oms-core-process</artifactId>
                <version>${r3.project.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>dubbo</artifactId>
                        <groupId>org.apache.dubbo</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>dubbo-spring-boot-starter</artifactId>
                        <groupId>org.apache.dubbo</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>apollo-client</artifactId>
                        <groupId>com.ctrip.framework.apollo</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>apollo-core</artifactId>
                        <groupId>com.ctrip.framework.apollo</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>r3-st-api</artifactId>
                <version>${r3.project.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.burgeon.r3</groupId>
                        <artifactId>r3-service-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--唯品会服务-->
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>r3-oc-oms-vip-api</artifactId>
                <version>${r3.project.version}</version>
            </dependency>
            <!--促销-->
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>r3-pm-api</artifactId>
                <version>${r3.project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>r3-oc-oms-model</artifactId>
                <version>${r3.project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>r3-oc-oms-fi-api</artifactId>
                <version>${r3.project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>r3-oc-oms-fi-srv</artifactId>
                <version>${r3.project.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>apollo-core</artifactId>
                        <groupId>com.ctrip.framework.apollo</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>apollo-client</artifactId>
                        <groupId>com.ctrip.framework.apollo</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.burgeon.r3</groupId>
                        <artifactId>r3-service-permission</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.burgeon.r3</groupId>
                        <artifactId>r3-service-util</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.syman</groupId>
                        <artifactId>raincloud-dubbo</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>r3-oc-oms-fi-ctrl</artifactId>
                <version>${r3.project.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.burgeon.r3</groupId>
                        <artifactId>r3-model-query</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.syman</groupId>
                        <artifactId>raincloud-dubbo</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>r3-data-basic-bll</artifactId>
                <version>${r3.project.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.syman</groupId>
                        <artifactId>raincloud-dubbo</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>r3-oc-oms-splitengine</artifactId>
                <version>${r3.project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>r3-sg-channel-api</artifactId>
                <version>${r3.project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>r3-sg-stocksync-api</artifactId>
                <version>${r3.project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>oms-vip-frontinterface-api</artifactId>
                <version>${r3.project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>r3-sg-sourcing-api</artifactId>
                <version>${r3.project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>r3-hub-api</artifactId>
                <version>${r3.project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>r3-sg-interface-api</artifactId>
                <version>${r3.project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>ac-finance-model</artifactId>
                <version>${r3.project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>ac-finance-api</artifactId>
                <version>${r3.project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>r3-common-utils</artifactId>
                <version>${r3.framework.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>elasticsearch-rest-client</artifactId>
                        <groupId>org.elasticsearch.client</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.elasticsearch.client</groupId>
                        <artifactId>elasticsearch-rest-high-level-client</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>r3-zuul-api</artifactId>
                <version>${r3.project.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>guava</artifactId>
                        <groupId>com.google.guava</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.burgeon.r3</groupId>
                <artifactId>r3-system-shutdown</artifactId>
                <version>3.2.0-SNAPSHOT</version>
            </dependency>

            <!-- other -->
            <dependency>
                <groupId>commons-lang</groupId>
                <artifactId>commons-lang</artifactId>
                <version>2.6</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.12.0</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>dingtalk</artifactId>
                <version>1.4.28</version>
            </dependency>
            <dependency>
                <groupId>com.dingtalk.open</groupId>
                <artifactId>taobao-sdk-java-auto</artifactId>
                <version>1.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun.openservices</groupId>
                <artifactId>ons-client</artifactId>
                <version>1.8.8.8.Final</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>3.15.2</version>
            </dependency>
            <dependency>
                <groupId>ognl</groupId>
                <artifactId>ognl</artifactId>
                <version>3.1.12</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-high-level-client</artifactId>
                <version>5.6.12</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>1.2.83</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.28</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.nacos</groupId>
                <artifactId>nacos-client</artifactId>
                <version>1.2.1</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.google.guava</groupId>
                        <artifactId>guava</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
                <version>2.0.1.RELEASE</version>
                <exclusions>
                    <exclusion>
                        <artifactId>nacos-client</artifactId>
                        <groupId>com.alibaba.nacos</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>5.8.20</version>
            </dependency>
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo</artifactId>
                <version>2.7.7</version>
            </dependency>
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-spring-boot-starter</artifactId>
                <version>2.7.7</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
            <version>1.2.13</version>
        </dependency>
    </dependencies>

    <distributionManagement>
        <snapshotRepository>
            <id>rdc-snapshots</id>
            <url>https://packages.aliyun.com/maven/repository/2228469-snapshot-tknOVW/</url>
        </snapshotRepository>
        <repository>
            <id>rdc-releases</id>
            <url>https://packages.aliyun.com/maven/repository/2228469-release-3W5OzY/</url>
        </repository>
    </distributionManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <verbose>true</verbose>
                    <fork>true</fork>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>