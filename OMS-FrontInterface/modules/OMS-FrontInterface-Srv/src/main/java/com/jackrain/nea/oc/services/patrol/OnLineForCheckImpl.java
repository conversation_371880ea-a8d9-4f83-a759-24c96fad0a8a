package com.jackrain.nea.oc.services.patrol;

import com.jackrain.nea.oc.oms.api.patrol.OnLineForCheckCmd;
import com.jackrain.nea.oc.oms.services.patrol.OnLineForCheckService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 线上巡查
 *
 * @author: 夏继超
 * @since: 2019/6/4
 * create at : 2019/6/4 11:31
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OnLineForCheckImpl implements OnLineForCheckCmd {
    @Autowired
    OnLineForCheckService checkService;

    /**
     * 没有明细《占单》       （待分配 ，没有明细）
     *
     * @return
     */
    @Override
    public String onCheck1() {
        return checkService.check1();
    }

    /**
     * 中间表重复《转单》        （TID、orderId 重复 昌源）
     *
     * @return
     */
    @Override
    public String onCheck2() {
        return checkService.check2();
    }

    /**
     * 订单不在等待卖家同意退款状态，非人工拦截处于拦截状态的（明细状态和头表的拦截状态   标记退款完整、退款转换、订单拦截）？？？
     *
     * @return
     */
    @Override
    public String onCheck3() {
        return checkService.check3();
    }
}
