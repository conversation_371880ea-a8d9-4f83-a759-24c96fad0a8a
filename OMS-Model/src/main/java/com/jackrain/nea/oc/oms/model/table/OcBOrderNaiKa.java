package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName OcBOrderNaiKa
 * @Description 奶卡、奶卡提货单明细
 * <AUTHOR>
 * @Date 2022/6/24 10:53
 * @Version 1.0
 */
@TableName(value = "oc_b_order_naika")
@Data
@Document(index = "oc_b_order_naika", type = "oc_b_order_naika")
@NoArgsConstructor(access = AccessLevel.PUBLIC)
@ApiModel(value = "oc_b_order_naika", description = "奶卡订单明细")
public class OcBOrderNaiKa extends BaseModel {

    @ApiModelProperty(value = "奶卡明细id")
    @Field(type = FieldType.Long)
    @JSONField(name = "ID")
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @ApiModelProperty(value = "订单编号")
    @Field(type = FieldType.Long)
    @JSONField(name = "OC_B_ORDER_ID")
    private Long ocBOrderId;

    @ApiModelProperty(value = "订单明细表id")
    @Field(type = FieldType.Long)
    @JSONField(name = "OC_B_ORDER_ITEM_ID")
    private Long ocBOrderItemId;

    @ApiModelProperty(value = "条码id")
    @Field(type = FieldType.Long)
    @JSONField(name = "PS_C_SKU_ID")
    private Long psCSkuId;

    @ApiModelProperty(value = "条码编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PS_C_SKU_ECODE")
    private String psCSkuEcode;

    @ApiModelProperty(value = "规格")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SKU_SPEC")
    private String skuSpec;

    @ApiModelProperty(value = "条码名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PS_C_SKU_ENAME")
    private String psCSkuEname;

    @ApiModelProperty(value = "商品id")
    @Field(type = FieldType.Long)
    @JSONField(name = "PS_C_PRO_ID")
    private Long psCProId;

    @ApiModelProperty(value = "商品货号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PS_C_PRO_ECODE")
    private String psCProEcode;

    @ApiModelProperty(value = "商品名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PS_C_PRO_ENAME")
    private String psCProEname;

    @ApiModelProperty(value = "初始平台单号（确定唯一）")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "TID")
    private String tid;

    @ApiModelProperty(value = "奶卡卡号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CARD_CODE")
    private String cardCode;

    @ApiModelProperty(value = "订单类型")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "BUSINESS_TYPE")
    private String businessType;

    @ApiModelProperty(value = "订单类型编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "BUSINESS_TYPE_CODE")
    private String businessTypeCode;

    @ApiModelProperty(value = "订单类型名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "BUSINESS_TYPE_NAME")
    private String businessTypeName;

    @ApiModelProperty(value = "订单类型ID")
    @Field(type = FieldType.Long)
    @JSONField(name = "BUSINESS_TYPE_ID")
    private Long businessTypeId;

    @ApiModelProperty(value = "奶卡状态")
    @Field(type = FieldType.Integer)
    @JSONField(name = "NAIKA_STATUS")
    private Integer naikaStatus;

    @ApiModelProperty(value = "操作时间")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "OPERATE_TIME")
    private Date operateTime;

    @ApiModelProperty(value = "冲销金额")
    @Field(type = FieldType.Double)
    @JSONField(name = "WIPE_AMT")
    private BigDecimal wipeAmt;

    @ApiModelProperty(value = "解冻失败原因")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "UNFREEZE_ERROR_MSG")
    private String unfreezeErrorMsg;

    @ApiModelProperty(value = "对账传奶卡状态")
    @Field(type = FieldType.Integer)
    @JSONField(name = "ACCOUNT_TO_NAIKA")
    private Integer accountToNaika;

    @ApiModelProperty(value = "对账传奶卡次数")
    @Field(type = FieldType.Integer)
    @JSONField(name = "ACCOUNT_TO_NAIKA_TIMES")
    private Integer accountToNaikaTimes;

    @ApiModelProperty(value = "对账失败原因")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "ACCOUNT_ERROR_MSG")
    private String accountErrorMsg;

    @ApiModelProperty(value = "上账时间")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "UP_TO_BILL_DATE")
    private Date upToBillDate;
}
