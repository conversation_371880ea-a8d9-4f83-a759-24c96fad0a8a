package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.Exchange2RefundCmd;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 换货转退货
 * @date 2021/10/13 20:37
 */
@Api(value = "ExchangeChange2RefundCtrl", description = "换货转退货")
@Slf4j
@RestController
@RequestMapping("/api/cs/oc/oms/v1")
public class ExchangeChange2RefundCtrl {

    @DubboReference(version = "1.0", group = "oms-fi")
    private Exchange2RefundCmd exchange2RefundCmd;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @ApiOperation(value = "换货转退货")
    @RequestMapping(value = "/exchange2Refund", method = RequestMethod.POST)
    public JSONObject exchange2Refund(HttpServletRequest request, @RequestBody JSONObject obj) {
        ValueHolderV14 vh;
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        try {
            vh = exchange2RefundCmd.exchange2RefundCmd(obj, user);
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(),
                    SerializerFeature.WriteMapNullValue));
        } catch (NDSException e) {
            vh = new ValueHolderV14();
            vh.setCode( ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return vh.toJSONObject();
        }
    }
}
