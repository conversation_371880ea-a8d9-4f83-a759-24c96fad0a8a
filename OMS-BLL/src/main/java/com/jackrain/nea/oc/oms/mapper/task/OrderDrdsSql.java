package com.jackrain.nea.oc.oms.mapper.task;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: 黄世新
 * @Date: 2020/9/25 12:44 下午
 * @Version 1.0
 */
@Slf4j
public class OrderDrdsSql {

    /**
     * 自动审核用
     *
     * @param para
     * @return
     */
    public String selectByNodeSqlOrder(Map<String, Object> para) {
        String startTime = (String) para.get("startTime");
        String endTime = (String) para.get("endTime");
        StringBuffer sql = new StringBuffer();
        String nodeName = (String) para.get("nodeName");
        String taskTableName = (String) para.get("taskTableName");

        //TODO 如果是DRDS数据库 必传参节点名称nodeName
        if (StringUtils.isEmpty(nodeName)) {
            return null;
        }
        sql.append("/*!TDDL:NODE=").append(nodeName).append("*/").append("select id,cp_c_shop_id from ")
                .append(taskTableName)
                .append(" where order_status = 1 and is_interecept = 0  and ")
                .append(" (STATUS_PAY_STEP is null or STATUS_PAY_STEP = '' or STATUS_PAY_STEP = 'FRONT_PAID_FINAL_PAID')")
                .append(" and creationdate >= '").append(startTime).append("' and creationdate <= '")
                .append(endTime).append("'");
        return sql.toString();
    }


    public String selectByNodeSqlOrderByShopId(Map<String, Object> para) {
        String holdReleaseTime = (String) para.get("holdReleaseTime");
        StringBuffer sql = new StringBuffer();
        Long shopId = (Long) para.get("shopId");
        String nodeName = (String) para.get("nodeName");
        String taskTableName = (String) para.get("taskTableName");
        StringBuffer limitStr = new StringBuffer(" LIMIT ");
        int limit = para.get("limit") != null ? (int) para.get("limit") : 3000;
        limitStr.append(limit);
        //TODO 如果是DRDS数据库 必传参节点名称nodeName
        if (StringUtils.isEmpty(nodeName)) {
            return null;
        }
        sql.append("/*!TDDL:NODE=").append(nodeName).append("*/").append("select id from ")
                .append(taskTableName)
                .append(" where order_status = 60 and cp_c_shop_id = ").append(shopId)
                .append(" and (pay_time <= '").append(holdReleaseTime).append("' or presale_deposit_time <= '").append(holdReleaseTime).append("')")
                .append(limitStr);
        return sql.toString();
    }
}
