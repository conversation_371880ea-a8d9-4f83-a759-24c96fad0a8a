package com.jackrain.nea.oc.oms.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum PlatFormEnum {

    SYSTEM(1, "系统"),
    TAOBAO(2, "淘宝"),
    TAOBAO_DISTRIBUTION(3, "淘宝分销"),
    JINGDONG(4, "京东"),
    PAIPAI(5, "拍拍"),
    QQNET(6, "QQ网购"),
    DANGDANG(7, "当当"),
    YIHAODIAN(8, "一号店"),
    PLATFORM_V(9, "V+"),
    AMAZON(10, "亚马逊"),
    VANCL(11, "Vancl"),
    SUNING_ONLINE_MARKET(12, "苏宁易购"),
    JUMEIYOUPIN(13, "聚美优品"),
    BOJUN_NEBULA_MOBILE(14, "伯俊星云移动"),
    TENCENT_MICRO_SHOPPING(15, "腾讯微购物"),
    PLATFORM17(17, "伯俊B2C商城"),
    INTIME(18, "银泰"),
    VIP_JIT(19, "唯品会JIT"),
    YOUZAN(20, "有赞"),
    SHANGPIN_DIRECT(21, "尚品网直连"),
    NETEASE_KOALA(22, "网易考拉"),
    PLATFORM23(23, "网易秀品"),
    PLATFORM24(24, "美丽说"),
    MUSHROOM_STREET(25, "蘑菇街"),
    CHUCHU_STREET(26, "楚楚街"),
    PINDUODUO(27, "拼多多"),
    PLATFORM28(28, "卷皮网"),
    PLATFORM31(31, "贝贝网"),
    SECOO(32, "寺库"),
    PLATFORM33(33, "魅力惠"),
    PLATFORM34(34, "商帆"),
    PLATFORM35(35, "阿里巴巴"),
    VIP_OXO(36, "唯品会OXO"),
    WUXIANGYUN(37, "舞象云"),
    SAP(38, "SAP"),
    DMS(39, "DMS"),
    SUNING_SALE(40, "苏宁特卖"),
    HAO_YI_KU(41, "好衣库"),
//    VIP_JITX(50, "唯品会JITX"),
    /**
     * 统一唯品会平台为19
     */
    VIP_JITX(19, "唯品会JITX"),
    //网易严选
    WANGYIYANXUAN(232, "WANGYIYANXUAN"),
    HONEY_BUD(51, "蜜芽"),
    DOU_YIN(57, "抖音"),
    DOUYIN_RETAIL(58, "抖音即时零售"),
    LITTLE_RED_BOOK(60, "小红书"),
    PLATFORM61(61, "折八百"),
    PLATFORM62(62, "云集"),
    BEALEAD(63, "百联"),
    PLATFORM64(64, "寺库LBP"),
    AI_KU_CUN(65, "爱库存"),
    ALIBABAASCP(66, "猫超直发"),
    OFFICIAL_WEBSITE(67, "官网"),
    STD_XXY(68, "私域-向心云"),
    TAOBAO_DEAL(77, "淘宝经销"),
    POS(88, "POS"),
    MALL_SHOP(99, "商城店"),
    ALIEXPRESS(101, "速卖通"),
    VIP_MP(102, "唯品会MP"),
    XIAOMI_YOUPIN(103, "小米有品"),
    //小红书 开放平台
    HONGSHU_OPEN(116, "HUSHU_OPEN"),
    VIP_MP_STANDARD(411, "唯品会MP（通用）"),
    KID_KING(120, "孩子王"),
    STEP_TAO(130, "步淘"),
    KUAISHOU(201, "快手"),
    ALL_THINGS_CHOICE(210, "万物心选"),
    DE_WU(223, "得物（品牌直发）"),
    DARLING_HOUSE(230, "达令家"),
    JD_MALL(237, "京东商仓"),
    // 美one
    MEI_ONE(260, "MEI_ONE"),
    PLATFORM_NONE(0, ""),
    YIKE(401, "译氪"),
    // 京东厂直
    JINGDONG_CZ(500, "京东厂直"),
    WANG_DIAN_TONG(188, "旺店通"),
    CARD_CODE(1000, "奶卡小程序"),
    YANGSC(1002, "洋葱"),
    YUNHUO(1101, "云货优选"),
    DANCHUANG(1003, "单创"),
    NICOMAMA(1004, "年糕妈妈"),
    DXDOCTOR(1005, "丁香妈妈"),
    ALIPAY_MIN_APP(1006, "支付宝小程序"),
    HAOSHIQI(1007, "好食期"),
    BIGVSTORE(1008, "大V店"),
    CREATE_CARD_CODE(1010, "奶卡小程序(创新)"),
    MEITUAN_FLASH_SALES(1150, "美团闪购"),
    HIPAC(1153, "海拍客"),
    KUAITUANTUAN(1160, "快团团"),
    KAI_ER_DE_LE(1170, "凯儿得乐"),
    SHIPH(1180, "视频号"),
    NO_SEND_PLATFORM(8888, "不用同步平台"),
    JINGDONG_DX(242, "京东代销订单"),
    QUNJIELONG(20017, "群接龙"),
    FKXIAOYU(1190, "疯狂小鱼"),
    XIAOXIAOBAO(1200, "小小包"),
    YOUFEN(1210, "有分"),
    DONGFANGFULI(1220, "东方福利网"),
    NASCENT(1230, "南讯"),

    YOUHU(1240, "有互"),
    HEMAOS(1250, "盒马"),
    TMALL_DDD(1260, "天猫食品平台"),

    TUAN_MAI_MAI(1270, "团买买平台"),
    XI_YUN(1280, "希芸平台"),
    DOUCHAO(1290, "抖音超市"),
    ALI_1688(1688, "淘工厂"),
    TAO_CAI_CAI(1300, "淘菜菜"),

    ;
    Integer code;
    String name;

    PlatFormEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 获取平台名称
     *
     * @param code
     * @return
     */
    public static String getName(Integer code) {
        if (code == null) {
            return PLATFORM_NONE.getName();
        }
        for (PlatFormEnum e : PlatFormEnum.values()) {
            if (e.getCode().equals(code)) {
                return e.getName();
            }
        }
        return "";
    }

    public Integer getCode() {
        return code;
    }

    public Long getLongVal(){
        return Long.valueOf(this.getCode());
    }

    public String getName() {
        return name;
    }

    /**
     * 转为map
     *
     * @return map
     */
    public static Map convertAllToHashVal() {
        Map<Integer, String> m = new HashMap<>();
        for (PlatFormEnum e : PlatFormEnum.values()) {
            m.put(e.getCode(), e.getName());
        }
        return m;
    }
}
