package com.jackrain.nea.oc.oms.model.result;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName : PosOrderQtyQueryResult  
 * @Description : 
 * <AUTHOR>  YCH
 * @Date: 2021-09-10 11:14  
 */
@Data
public class PosOrderQtyQueryResult implements Serializable {

    /**
     *  1 整单可退  2 任意可退  3 不可退
     */
    private String returnStatus;

    private List<PosOrderQtyQueryItemResult> posOrderQtyQueryItemResultList;

}
