package com.jackrain.nea.oc.oms.process.transfer.impl.refund.jdcancel.step;

import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpJingdongCancelRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderRelation;
import com.jackrain.nea.oc.oms.model.resources.SysNotesConstant;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongSaRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.services.OmsRefundOrderService;
import com.jackrain.nea.oc.oms.services.refund.JdReturnUtil;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: 判断京东取消订单单据转换状态
 * @author: 郑小龙
 * @date: 2020-06-01 11:23
 **/
@Step(order = 10, description = "判断京东取消订单单据转换状态")
@Slf4j
@Component
public class Step010CheckCancelOrderTransferStatus extends BaseJingdongCancelProcessStep implements IOmsOrderProcessStep<IpJingdongCancelRelation> {

    @Autowired
    private OmsRefundOrderService omsRefundOrderService;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;

    @Override
    public ProcessStepResult<IpJingdongCancelRelation> startProcess(IpJingdongCancelRelation orderInfo,
                                                                    ProcessStepResult preStepResult,
                                                                    boolean isAutoMakeup, User operateUser) {

        if (orderInfo == null || orderInfo.getJingdongSaRefund() == null) {
            return new ProcessStepResult<>(StepStatus.FAILED, "OrderInfo为空或者京东取消订单为空；退出转换");
        }

        //todo  更新订单明细的退款状态
        IpBJingdongSaRefund saRefund = orderInfo.getJingdongSaRefund();
        String orderid = saRefund.getOrderid();
        Integer status = saRefund.getStatus();
        String refundStatus = JdReturnUtil.transTaobaoRefundStatus(status);
        List<OcBOrder> ocBOrder = orderInfo.getOcBOrder();
        if (CollectionUtils.isNotEmpty(ocBOrder)) {
            ocBOrder = ocBOrder.stream().filter(p -> !(OmsOrderStatus.CANCELLED.toInteger().equals(p.getOrderStatus())
                    || OmsOrderStatus.SYS_VOID.toInteger().equals(p.getOrderStatus()))).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(ocBOrder)) {
            saRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(), SysNotesConstant.SYS_REMARK4, saRefund);
            return new ProcessStepResult<>(StepStatus.FINISHED, SysNotesConstant.SYS_REMARK4);
        }
        orderInfo.setOcBOrder(ocBOrder);
        if (StringUtils.isNotEmpty(refundStatus) && CollectionUtils.isNotEmpty(ocBOrder)) {
            List<OmsOrderRelation> omsOrderRelation = new ArrayList<>();
            for (OcBOrder order : ocBOrder) {
                OmsOrderRelation relation = new OmsOrderRelation();
                relation.setOcBOrder(order);
                List<OcBOrderItem> orderItems = ocBOrderItemMapper.selectOrderItemList(order.getId());
                orderItems = orderItems.stream().filter(p -> orderid.equals(p.getTid())).collect(Collectors.toList());
                relation.setOcBOrderItems(orderItems);
                omsOrderRelation.add(relation);
            }
            omsRefundOrderService.updateOcOrderStatusInfo(omsOrderRelation, refundStatus);
        }

        omsRefundOrderService.updateRefundSlip(saRefund.getPopafsrefundapplyid()+"", refundStatus);
        Integer currentStatus = orderInfo.getJingdongSaRefund().getIstrans();
        if (TransferOrderStatus.TRANSFERRED.toInteger() == currentStatus) {
            String operateMessage = Resources.getMessage("单据" + orderInfo.getOrderId() + "状态=已转换，转换完成");
            return new ProcessStepResult<>(StepStatus.FINISHED, operateMessage);
        } else if (TransferOrderStatus.TRANSFERRING.toInteger() == currentStatus) {
            String operateMessage = Resources.getMessage("单据" + orderInfo.getOrderId() + "正在转换中");
            return new ProcessStepResult<>(StepStatus.FINISHED, operateMessage);
        } else {
            String operateMessage = Resources.getMessage("单据" + orderInfo.getOrderId() + "检查状态成功，进入下一阶段");
            return new ProcessStepResult<>(StepStatus.SUCCESS, operateMessage, Step020CheckOriginalStatus.class);
        }
    }


}