package com.jackrain.nea.oc.oms.util;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.stereotype.Repository;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;


/**
 * <AUTHOR> ruan.gz
 * @Description :
 * @Date : 2020/6/11
 **/
@Slf4j
@Repository
public class HttpRestUtil {

    private static RestTemplate restTemplate;

    static {
        restTemplate = new RestTemplate();
    }

    /**
     * post请求
     *
     * @param url
     * @param valueMap
     * @return
     */
    public static String httpPostUrl(String url, MultiValueMap<String, String> valueMap) {
        //设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        HttpEntity<MultiValueMap<String, String>> entity = new HttpEntity<>(valueMap, headers);
        String jsonResposeStr = null;
        try {
            ResponseEntity<String> respose = restTemplate.postForEntity(url, entity, String.class);
            jsonResposeStr = respose.getBody();
        } catch (Exception e) {
            log.error(LogUtil.format("请求异常 返回null 错误信息{}"), Throwables.getStackTraceAsString(e));
        }
        return jsonResposeStr;
    }

    /**
     * post请求
     *
     * @param url
     * @param jsonParam
     * @return
     */
    public static String httpPostUrl(String url, JSONObject jsonParam) {
        //设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<JSONObject> entity = new HttpEntity<>(jsonParam, headers);
        String jsonResposeStr = null;
        try {
            ResponseEntity<String> respose = restTemplate.postForEntity(url, entity, String.class);
            jsonResposeStr = respose.getBody();
        } catch (Exception e) {
            log.info("请求异常 返回null 错误信息{}", e);
        }
        return jsonResposeStr;
    }


    /**
     * post请求
     *
     * @param url
     * @param valueMap
     * @return
     */
    public static String httpPostUrlalitm(String url, MultiValueMap<String, String> valueMap) {
        //设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<MultiValueMap<String, String>> entity = new HttpEntity<>(valueMap, headers);
        String jsonResposeStr = null;
        try {
            ResponseEntity<String> respose = restTemplate.postForEntity(url, entity, String.class);
            jsonResposeStr = respose.getBody();
        } catch (Exception e) {
            log.error(LogUtil.format("请求异常 返回null 错误信息{}"), Throwables.getStackTraceAsString(e));
        }
        return jsonResposeStr;
    }

    /**
     * post请求(无加密)
     *
     * @param url
     * @param valueJson
     * @return
     */
    public static String httpPostUrlWithoutEncrypt(String url, String valueJson) {
        //设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.parseMediaType("application/json; charset=UTF-8"));
        headers.add("Accept", MediaType.APPLICATION_JSON_VALUE);
        HttpEntity<String> entity = new HttpEntity<>(valueJson, headers);
        String jsonResposeStr = null;
        try {
            ResponseEntity<String> respose = restTemplate.postForEntity(url, entity, String.class);
            jsonResposeStr = respose.getBody();
        } catch (Exception e) {
            log.error(LogUtil.format("请求异常 返回null 错误信息{}"), Throwables.getStackTraceAsString(e));
        }
        return jsonResposeStr;
    }

    /**
     * @description: get请求
     * @params: [url]
     * @return: java.lang.String
     * @author: fangchao
     * @date Create In: 下午4:02 2018/12/10
     */
    public static String httpGetUrl(String url) {
        ResponseEntity<String> respose = restTemplate.getForEntity(url, String.class);
        if (HttpStatus.OK.equals(respose.getStatusCode())) {
            return respose.getBody();
        }
        return null;
    }


    /**
     * get请求
     *
     * @param url
     * @param param 参数
     * @return
     */
    public static String httpGetUrl(String url, String param) {
        String jsonResposeStr = null;
        try {
            ResponseEntity<String> respose = restTemplate.exchange(url, HttpMethod.GET, null, String.class, param);
            jsonResposeStr = respose.getBody();
        } catch (Exception e) {
            log.error(LogUtil.format("get请求异常 返回null 错误信息{}"), Throwables.getStackTraceAsString(e));
        }
        return jsonResposeStr;
    }
}
