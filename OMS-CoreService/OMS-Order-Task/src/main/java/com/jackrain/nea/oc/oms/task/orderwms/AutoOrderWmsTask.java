package com.jackrain.nea.oc.oms.task.orderwms;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.services.task.OmsWmsTaskService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sg.service.SgOutStockNoticeService;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.ListSplitUtil;
import com.jackrain.nea.util.SplitListUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/***
 * 订单传wms服务
 */
@Slf4j
@Component
public class AutoOrderWmsTask extends BaseR3Task implements IR3Task {

    @Autowired
    private SgOutStockNoticeService sgOutStockNoticeService;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderItemMapper orderItemMapper;
    @Autowired
    private BllRedisLockOrderUtil redisUtil;
    @Autowired
    private OmsWmsTaskService wmsTaskService;
    @Autowired
    private ThreadPoolTaskExecutor preToWmsTaskThreadPoolExecutor;


    @Override
    @XxlJob("AutoOrderWmsTask")
    public RunTaskResult execute(JSONObject params) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("进入订单传wms定时任务:{}", "进入订单传wms定时任务"));
        }
        RunTaskResult result = new RunTaskResult();
        try {

            long start = System.currentTimeMillis();
            final String taskTableName = "oc_b_to_wms_task";
            List<Future<Boolean>> results = new ArrayList<Future<Boolean>>();
            PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
            Integer pageSize = config.getProperty("lts.AutoOrderWmsTask.range", 200);
            Integer orderStatus = config.getProperty("lts.AutoOrderWmsTask.status", 0);
            List<Long> list = wmsTaskService.selectWmsTaskWithPushDelay(pageSize * 24, taskTableName, orderStatus);
            if (CollectionUtils.isEmpty(list)) {
                result.setSuccess(true);
                result.setMessage("无订单");
                return result;
            }
            List<List<Long>> lists = ListSplitUtil.averageAssign(list, 24);
            for (List<Long> data : lists) {
                results.add(preToWmsTaskThreadPoolExecutor.submit(new CallableWmsTaskWithResult(data)));
            }

            //线程执行结果获取
            for (Future<Boolean> futureResult : results) {
                try {
                    if (log.isDebugEnabled()) {
                        log.debug(LogUtil.format("AutoOrderWmsTask------>线程结果:{}"), futureResult.get().toString());
                    }
                } catch (Exception e) {
                    log.error(LogUtil.format("AutoOrderWmsTask多线程获取InterruptedException异常：{}", "AutoOrderWmsTask"), Throwables.getStackTraceAsString(e));
                }
            }

            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("AutoOrderWmsTask 传wms服务定时任务完成 useTime : {}"), (System.currentTimeMillis() - start));
            }
            result.setSuccess(true);
        } catch (Exception ex) {
            log.error(LogUtil.format("订单传wms任务执行失败,异常！{}",  "AutoOrderWmsTask"), Throwables.getStackTraceAsString(ex));
            result.setSuccess(false);
            result.setMessage(ex.getMessage());
        } finally {
            //   executor.shutdown();
        }
        return result;
    }

    /**
     * 开启线程类
     */
    class CallableWmsTaskWithResult implements Callable<Boolean> {


        private final List<Long> list;

        public CallableWmsTaskWithResult(List<Long> list) {
            this.list = list;
        }

        @Override
        public Boolean call() throws Exception {
            if (CollectionUtils.isEmpty(list)) {
                return true;
            }
            // 分批次推送WMS
            spitData(list);
            return true;
        }

    }

    private void spitData(List<Long> list) {
        User rootUser = SystemUserResource.getRootUser();
        // 读取阿波罗配置
        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
        Integer pointsDataLimit = config.getProperty("lts.AutoOrderWmsTask.push.num", 200);
        Boolean booleanProperty = config.getPropertyBoolean("lts.wms.is_LockUp");

        // 分割list
        List<List<Long>> splitList = SplitListUtil.partition(list, pointsDataLimit);
        if (CollectionUtils.isEmpty(splitList)) {
            return;
        }
        for (List<Long> toList : splitList) {
            long starTime = System.currentTimeMillis();
            List<RedisReentrantLock> lockList = new ArrayList<>();
            List<Long> toNewList = new ArrayList<>();
            try {
                if (booleanProperty) {
                    lockOrderList(toList, lockList, toNewList);
                } else {
                    toNewList = toList;
                }
                if (CollectionUtils.isNotEmpty(toNewList)) {
                    // 订单推送WMS
                    ApplicationContextHandle.getBean(AutoOrderWmsTask.class).pushWms(rootUser, toNewList);
                    log.debug("AutoOrderWmsTask订单传wms完成耗时:{}ms,一共:{}条",
                            System.currentTimeMillis() - starTime, toNewList.size());
                }
            } catch (Exception e) {
                log.error(LogUtil.format("AutoOrderWmsTask订单传WMS失败,订单列表orderList={},异常信息:{}",
                        "AutoOrderWmsTask订单传WMS失败"), JSONObject.toJSONString(toList),Throwables.getStackTraceAsString(e));
            } finally {
                this.unLockOrderList(lockList);
            }
        }
    }

    /**
     * 订单逐个加锁
     * @param toList
     * @param lockList
     * @param toNewList
     */
    private void lockOrderList(List<Long> toList, List<RedisReentrantLock> lockList, List<Long> toNewList) {
        for (Long orderId : toList) {
            String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(orderId);
            RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
            try {
                if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                    lockList.add(redisLock);
                    toNewList.add(orderId);
                } else {
                    log.warn(LogUtil.format("AutoOrderWmsTask.spitData tryLockFail,orderId:{}",
                            "AutoOrderWmsTask.spitData tryLockFail"),orderId);
                }
            } catch (Exception e) {
                log.error(LogUtil.format("AutoOrderWmsTask.spitData tryLockError,orderId:{}",
                        "AutoOrderWmsTask.spitData tryLockError"),orderId);
            }
        }
    }

    //解锁
    private void unLockOrderList(List<RedisReentrantLock> lockList) {
        if (CollectionUtils.isEmpty(lockList)) {
            return;
        }
        for (RedisReentrantLock redisReentrantLock : lockList) {
            redisReentrantLock.unlock();
        }

    }

    @Transactional(rollbackFor = Exception.class)
    public void pushWms(User user, List<Long> toList) {
        //1) 将订单状态修改为传WMS中，状态21
        ocBOrderMapper.updateWmsOrderTo21List(toList);
        // 2）修改task表订单状态（因为又一些单据的状态会发生变化，如果不满足条件的，那么也要设置成1）
        wmsTaskService.batchUpdateOcBToWmsTask(toList, 1);
        // 3）新增出库单并批量传wms
        List<OcBOrder> ocBOrders = ocBOrderMapper.selectByIdsListAndOrderStatus(toList);
        // 2020/10/30 优化：通过过滤后的订单id，来查询明细数据
        List<Long> listIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(ocBOrders)) {
            listIds = ocBOrders.stream()
                    .filter(obj -> obj != null && obj.getId() != null)
                    .map(OcBOrder::getId)
                    .collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(listIds)) {
            return;
        }
        List<OcBOrderItem> orderItems = orderItemMapper.selectOrderItemsByOrderIds(listIds);
        sgOutStockNoticeService.addOutStockNoticeToWms(user, ocBOrders, orderItems);
    }

}