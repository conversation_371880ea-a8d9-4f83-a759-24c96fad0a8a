package com.jackrain.nea.ps.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.services.OmsAddBlacklistService;
import com.jackrain.nea.oc.oms.task.orderwms.AutoOrderWmsTask;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.resource.WmsUserResource;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Profile;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * PS相关的单元测试用例
 *
 * @author: 易邵峰
 * @since: 2019-03-29
 * create at : 2019-03-29 22:43
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(
//        classes = {
//        ProductService.class,
//        RedisOpsUtil.class,
//        PsRpcService.class,
//        PropertiesConf.class,
//        CusRedisTemplate.class
//}
)
@Profile(value = "dev")
public class PsRpcServiceTest {

    @Autowired
    private PsRpcService psRpcService;

    @Autowired
    private AutoOrderWmsTask autoOrderWmsTask;
    @Autowired
    private OmsAddBlacklistService omsAddBlacklistService;

    @Test
    public void testSelectSku() {
        String sku = "11922124870160350";
        ProductSku prodSku = psRpcService.selectProductSku(sku);
        Assert.assertNotNull(prodSku);
    }


    @Test
    public void autoOrderWmsTaskTest() {
        JSONObject jsonObject = new JSONObject();
        RunTaskResult execute = autoOrderWmsTask.execute(jsonObject);
        System.out.println(execute);
    }

    @Test
    public void omsAddBlacklistServiceTest() {
        User rootUser = WmsUserResource.getWmsUser();
        Long id = 378039L;
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("orderId", id);
        jsonObject.put("type", 1);
        ValueHolder holder = omsAddBlacklistService.addBlacklist(jsonObject, rootUser);
        System.out.println("返回结果" + holder);
    }

}
