package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.ReturnReceiptMatchCmd;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * @author: 周琳胜
 * @since: 2019/3/25
 * create at : 2019/3/25 19:40
 */
@Api(value = "ReturnReceiptMatchCtrl", tags = "退货入库单匹配服务")
@Slf4j
@RestController
public class ReturnReceiptMatchCtrl {

    @Autowired
    private ReturnReceiptMatchCmd returnReceiptMatchCmd;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @ApiOperation(value = "退货入库单匹配服务")
    @RequestMapping(value = "api/cs/oc/oms/v1/returnreceiptmatch", method = RequestMethod.POST)
    public JSONObject markRefund(HttpServletRequest request, @RequestBody JSONObject obj) {

        ValueHolderV14 vh = new ValueHolderV14();
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        try {

            vh = returnReceiptMatchCmd.execute(obj, user);
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(),
                    SerializerFeature.WriteMapNullValue));
        } catch (NDSException e) {
            vh = new ValueHolderV14();
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return vh.toJSONObject();
        }
    }

}
