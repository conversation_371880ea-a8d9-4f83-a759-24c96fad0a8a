package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.api.OcBOrderItemUpdateAddServiceCmd;
import com.jackrain.nea.oc.oms.services.OcBOrderItemUpdateAddServiceService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName OcBOrderItemUpdateAddServiceCmdImpl
 * @Description 修改增值服务
 * <AUTHOR>
 * @Date 2023/10/23 11:14
 * @Version 1.0
 */
@Slf4j
@Component
public class OcBOrderItemUpdateAddServiceCmdImpl implements OcBOrderItemUpdateAddServiceCmd {

    @Autowired
    private OcBOrderItemUpdateAddServiceService itemUpdateAddServiceService;

    @Override
    public ValueHolderV14 updateItemAddService(JSONObject object, User user) {
        return itemUpdateAddServiceService.updateItemAddService(object, user);
    }
}
