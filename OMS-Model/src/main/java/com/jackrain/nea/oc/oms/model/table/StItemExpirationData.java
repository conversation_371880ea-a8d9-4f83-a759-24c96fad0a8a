package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @ClassName StItemExpirationData
 * @Description 商品指定效期策略
 * <AUTHOR>
 * @Date 2022/6/9 15:59
 * @Version 1.0
 */
@TableName(value = "st_item_expiration_date")
@Data
@Accessors(chain = true)
public class StItemExpirationData extends BaseModel {

    @JSONField(name = "ID")
    @Field(type = FieldType.Long)
    private Long id;

    /**
     * 品项
     */
    @JSONField(name = "PX")
    private String px;

    /**
     * 商品id
     */
    @JSONField(name = "PS_C_PRO_ID")
    private Long psCProId;

    /**
     * 商品编码
     */
    @JSONField(name = "PS_C_PRO_ECODE")
    private String psCProEcode;

    /**
     * 商品名称
     */
    @JSONField(name = "PS_C_PRO_ENAME")
    private String psCProEname;

    /**
     * 指定类型
     */
    @JSONField(name = "EXPIRATION_TYPE")
    private Integer expirationType;

    /**
     * 开始时间
     */
    @JSONField(name = "EXPIRATION_START_TIME")
    private Date expirationStartTime;

    /**
     * 结束时间
     */
    @JSONField(name = "EXPIRATION_END_TIME")
    private Date expirationEndTime;

    /**
     * 开始天
     */
    @JSONField(name = "EXPIRATION_START_DAY")
    private Integer expirationStartDay;

    /**
     * 结束天
     */
    @JSONField(name = "EXPIRATION_END_DAY")
    private Integer expirationEndDay;
}
