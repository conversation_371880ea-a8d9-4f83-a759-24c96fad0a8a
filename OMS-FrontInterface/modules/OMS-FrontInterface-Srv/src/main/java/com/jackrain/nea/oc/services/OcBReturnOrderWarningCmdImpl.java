package com.jackrain.nea.oc.services;

import com.jackrain.nea.oc.oms.api.OcBReturnOrderWarningCmd;
import com.jackrain.nea.oc.oms.services.OcBReturnOrderWarningService;
import com.jackrain.nea.oc.request.OcBReturnOrderWarningRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2021/12/14 19:33
 */
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBReturnOrderWarningCmdImpl implements OcBReturnOrderWarningCmd {

    @Autowired
    private OcBReturnOrderWarningService service;

    @Override
    public ValueHolderV14 warningSave(List<OcBReturnOrderWarningRequest> request) {
        return service.warning(request);
    }
}
