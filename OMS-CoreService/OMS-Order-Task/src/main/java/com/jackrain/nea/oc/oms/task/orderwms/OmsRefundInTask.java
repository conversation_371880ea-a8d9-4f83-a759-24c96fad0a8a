package com.jackrain.nea.oc.oms.task.orderwms;


import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.services.task.OmsRefundInTaskService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Auther: chenhao
 * @Date: 2022-07-16 10:43
 * @Description:
 */

@Slf4j
@Component
public class OmsRefundInTask extends BaseR3Task implements IR3Task {

    @Autowired
    private OmsRefundInTaskService service;

    @Override
    @XxlJob("OmsRefundInTask")
    public RunTaskResult execute(JSONObject params) {
        ValueHolderV14 result = service.execute();
        RunTaskResult runTaskResult = new RunTaskResult();
        if (result.isOK()) {
            runTaskResult.setSuccess(true);
            runTaskResult.setMessage(result.getMessage());
        } else {
            runTaskResult.setSuccess(false);
            runTaskResult.setMessage(result.getMessage());
        }
        return runTaskResult;

    }
}
