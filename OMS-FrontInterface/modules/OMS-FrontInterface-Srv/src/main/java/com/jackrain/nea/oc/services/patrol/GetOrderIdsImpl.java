package com.jackrain.nea.oc.services.patrol;

import com.jackrain.nea.oc.oms.api.patrol.GetOrderIdsCmd;
import com.jackrain.nea.oc.oms.services.patrol.GetorderIdsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: 李杰
 * @since: 2019/6/4
 * create at : 2019/6/4 16:21
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class GetOrderIdsImpl implements GetOrderIdsCmd {
    @Autowired
    GetorderIdsService getorderIdsService;

    @Override
    public List getIds() {
        return getorderIdsService.getAmtUneqItemsIds();
    }

    @Override
    public List getOrderIds() {
        return getorderIdsService.getReceiveUneqAmtIds();
    }

    @Override
    public List getTidRepeatIds() {
        return getorderIdsService.getTidRepeatIds();
    }
}
