package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import java.util.Date;

import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

@TableName(value = "ip_b_order_tjxt_log")
@Data
@Document(index = "ip_b_order_tjxt_log",type = "ip_b_order_tjxt_log")
public class IpBOrderTjxtLog extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "AD_CLIENT_ID")
    @Field(type = FieldType.Long)
    private Long adClientId;

    @JSONField(name = "AD_ORG_ID")
    @Field(type = FieldType.Long)
    private Long adOrgId;

    @JSONField(name = "IP_B_TAOBAO_ORDER_ID")
    @Field(type = FieldType.Long)
    private Long ipBTaobaoOrderId;

    @JSONField(name = "IP_B_JINGDONG_ORDER_ID")
    @Field(type = FieldType.Long)
    private Long ipBJingdongOrderId;

    @JSONField(name = "IP_B_JITX_ORDER_ID")
    @Field(type = FieldType.Long)
    private Long ipBJitxOrderId;

    @JSONField(name = "IP_B_STANDPLAT_ORDER_ID")
    @Field(type = FieldType.Long)
    private Long ipBStandplatOrderId;

    @JSONField(name = "MODCONTENT")
    @Field(type = FieldType.Keyword)
    private String modcontent;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;

    @JSONField(name = "CREATIONDATE")
    @Field(type = FieldType.Date)
    private Date creationdate;

    @JSONField(name = "MODIFIEDDATE")
    @Field(type = FieldType.Date)
    private Date modifieddate;

    @JSONField(name = "ISACTIVE")
    @Field(type = FieldType.Keyword)
    private String isactive;

    @JSONField(name = "OWNERID")
    @Field(type = FieldType.Long)
    private Long ownerid;

    @JSONField(name = "MODIFIERID")
    @Field(type = FieldType.Long)
    private Long modifierid;

    @JSONField(name = "OWNERNAME")
    @Field(type = FieldType.Keyword)
    private String ownername;

    @JSONField(name = "MODIFIERNAME")
    @Field(type = FieldType.Keyword)
    private String modifiername;

    @JSONField(name = "BMOD")
    @Field(type = FieldType.Keyword)
    private String bmod;

    @JSONField(name = "AMOD")
    @Field(type = FieldType.Keyword)
    private String amod;
}