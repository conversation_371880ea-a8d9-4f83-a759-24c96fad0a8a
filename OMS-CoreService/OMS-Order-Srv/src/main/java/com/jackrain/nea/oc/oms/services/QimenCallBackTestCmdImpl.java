package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.oc.oms.api.QimenCallBackTestCmd;
import com.jackrain.nea.oc.oms.services.refund.RefundOrderWmsBackService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: 秦雄飞
 * @time: 2022/1/13 4:40 下午
 * @description: 奇门模拟回传
 */

@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", group = "oc-core", version = "1.0")
public class QimenCallBackTestCmdImpl implements QimenCallBackTestCmd {

    @Autowired
    private RefundOrderWmsBackService refundOrderWmsBackService;

    /**
     * 奇门模拟退货单回传
     *
     * @param msg
     * @return
     */
    @Override
    public boolean returnOrderCallBack(String msg) {
        return refundOrderWmsBackService.consume(msg);
    }
}
