package com.jackrain.nea.oc.oms.process.transfer.impl.refund.currency.step;

import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.OrderBusinessTypeCodeEnum;
import com.jackrain.nea.oc.oms.model.enums.OrderHoldReasonEnum;
import com.jackrain.nea.oc.oms.model.enums.TransNodeTipEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsStandPlatRefundRelation;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.nums.OcOrderRefundStatusEnum;
import com.jackrain.nea.oc.oms.process.transfer.impl.refund.taobao.util.OrderStatusUtil;
import com.jackrain.nea.oc.oms.refund.util.TransRefundNodeTipUtil;
import com.jackrain.nea.oc.oms.services.OcBOrderHoldService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2020/3/8 12:57 下午
 * @Version 1.0
 */
@Step(order = 51, description = "判断订单是否为奶卡订单")
@Slf4j
@Component
public class Step051CheckStandMilkCardOrder extends BaseStanPlatRefundProcessStep
        implements IOmsOrderProcessStep<OmsStandPlatRefundRelation> {

    @Autowired
    private OcBOrderHoldService ocBOrderHoldService;

    @Override
    public ProcessStepResult<OmsStandPlatRefundRelation> startProcess(OmsStandPlatRefundRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        List<OmsOrderRelation> omsOrderRelations = orderInfo.getOmsOrderRelation();
        IpBStandplatRefund ipBStandplatRefund = orderInfo.getIpBStandplatRefund();
        Integer returnStatus = ipBStandplatRefund.getReturnStatus();
        String status = TaobaoReturnOrderExt.RefundStandPlatStatus.getValueByKey(returnStatus);
        boolean closed = TaobaoReturnOrderExt.RefundStatus.CLOSED.getCode().equals(status);

        //按子订单维度创建发货后退款单（仅退款）
        List<OcBOrderItem> orderItems = new ArrayList<>();
        List<OcBOrder> ocBOrderList = new ArrayList<>();
        for (OmsOrderRelation orderRelation : omsOrderRelations) {
            boolean andVoid = OrderStatusUtil.checkOrderIsCancelAndVoid(orderRelation.getOcBOrder().getOrderStatus());
            if (andVoid) {
                continue;
            }
            orderItems.addAll(orderRelation.getOcBOrderItems());
            ocBOrderList.add(orderRelation.getOcBOrder());
        }
        // fixme
        // 1、判断是不是周期购提货。 如果是周期购提货 需要调整为中台周期购订单来生成。
        // 2、生成的时候 尽量按照奶卡相关的逻辑来生成已发货退款单仅退款
        // 如果订单全都是奶卡类商品，则只生成已发货退款单
        // 主要是为了解决两个问题。 一个是周期购提货单不应该生成售后单。另外一个是 仅退款单 后面会根据金额来判断要不要生成退换货单。所以此处不能走到后面。需要跟奶卡一样的处理
        boolean isMilkCardOrder = false;
        boolean isCyclePurchasePickUp = false;
        try {
            isMilkCardOrder = omsRefundOrderService.checkIsMilkCardOrderNew(ocBOrderList);
            // 判断是否为周期购提货单
            for (OcBOrder order : ocBOrderList) {
                if (OrderBusinessTypeCodeEnum.CYCLE_PURCHASE_ORDER_PICK_UP.getCode().equals(order.getBusinessTypeCode()) ||
                        OrderBusinessTypeCodeEnum.FREE_CYCLE_PURCHASE_ORDER_PICK_UP.getCode().equals(order.getBusinessTypeCode())) {
                    isCyclePurchasePickUp = true;
                    break;
                }
            }
        } catch (Exception e) {
            log.error(LogUtil.format("查询是否为奶卡订单或周期购提货单失败={}",
                    "Step051CheckStandMilkCardOrder"), Throwables.getStackTraceAsString(e));
            ipStandplatRefundService.updateRefundIsTransError(ipBStandplatRefund, e.getMessage());
            String errorMessage = "退单转换异常!" + e.getMessage();
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
        OcBOrder ocBOrder = omsOrderRelations.get(0).getOcBOrder();
        //判断订单是否为奶卡订单或周期购提货单
        if (!isMilkCardOrder && !isCyclePurchasePickUp) {
            return new ProcessStepResult<>(StepStatus.SUCCESS, "不为奶卡订单或周期购提货单,继续下一阶段转换");
        }

        String remark = "生成发货后退款单(仅退款)成功";
        if (closed) {
            //退款关闭，取消已发货退款单
            omsRefundOrderService.closedRefundSlip(ipBStandplatRefund.getReturnNo());
            // 如果有赠品 则执行关闭售后单后的赠品处理逻辑
            if (CollectionUtils.isNotEmpty(orderInfo.getIsGiftOrderRelation())) {
                for (OmsOrderRelation orderRelation : orderInfo.getIsGiftOrderRelation()) {
                    OcBOrder giftOrder = orderRelation.getOcBOrder();
                    if (giftOrder.getIsInreturning() == 1 && giftOrder.getIsInterecept() == 1) {
                        OcBOrder order = new OcBOrder();
                        //是否退款中
                        order.setIsInreturning(0);
                        order.setId(giftOrder.getId());
                        omsOrderService.updateOrderInfo(order);
                        //是否已经拦截 Hold单统一调用 HOLD单方法
                        order.setIsInterecept(0);
                        order.setBillNo(giftOrder.getBillNo());
                        ocBOrderHoldService.holdOrUnHoldOrder(order, OrderHoldReasonEnum.REFUND_HOLD);

                        List<OcBOrderItem> ocBOrderItemAll = orderRelation.getOcBOrderItemAll();
                        // 未拆分的组合商品信息
                        List<Long> updateItemIds = ocBOrderItemAll.stream().map(OcBOrderItem::getId).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(updateItemIds)) {
                            ocBOrderItemMapper.updateOcBOrderItemById(giftOrder.getId(), updateItemIds, OcOrderRefundStatusEnum.NOTREFUND.getVal());
                        }
                        //更新订单明细的退款状态
                        for (OcBOrderItem item : ocBOrderItemAll) {
                            ocBOrderItemMapper.updateOrderItemPtReturnStatusByOrderId(giftOrder.getId(), TaobaoReturnOrderExt.RefundStandPlatStatus.CLOSED.getName(), item.getId());
                        }
                    }
                }
            }
            remark = "取消发货后退款单(仅退款)成功";
        }else {
            if (isCyclePurchasePickUp) {
                for (OmsOrderRelation orderRelation : omsOrderRelations) {
                    // 判断是否为周期购提货单
                    if (OrderBusinessTypeCodeEnum.CYCLE_PURCHASE_ORDER.getCode().equals(orderRelation.getOcBOrder().getBusinessTypeCode()) ||
                            OrderBusinessTypeCodeEnum.FREE_CYCLE_PURCHASE_ORDER.getCode().equals(orderRelation.getOcBOrder().getBusinessTypeCode())) {
                        orderItems.clear();
                        orderItems.addAll(orderRelation.getOcBOrderItems());
                        ocBOrder = orderRelation.getOcBOrder();
                    }
                }
            }
            //根据退款单号查询退款
            omsStandPlatRefundOrderService.foundRefundSlipAfterRefundOnly(orderItems, ocBOrder, ipBStandplatRefund, operateUser, orderInfo);
            omsReturnOrderService.giftsThenSend(omsOrderRelations, orderInfo.getIsGiftOrderRelation(), orderInfo.getIntermediateTableRelation(), operateUser);
            ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                    remark, ipBStandplatRefund);
            return new ProcessStepResult<>(StepStatus.FINISHED, remark);
        }
        TransRefundNodeTipUtil.standardTransTipCAS(ipBStandplatRefund, TransNodeTipEnum.DEFAULT);
        ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                remark, ipBStandplatRefund);
        return new ProcessStepResult<>(StepStatus.FINISHED, remark);
    }
}
