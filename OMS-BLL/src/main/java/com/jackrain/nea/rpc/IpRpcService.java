package com.jackrain.nea.rpc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.ac.model.AcFPayableAdjustmentDO;
import com.jackrain.nea.ac.model.AcFPayableAdjustmentItemDO;
import com.jackrain.nea.ac.service.PayableAdjustmentSaveService;
import com.jackrain.nea.common.ReferenceUtil;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.enums.DrpStoreTypeEnum;
import com.jackrain.nea.cp.result.CpCStore;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.cpext.model.table.CpCPlatform;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.data.basic.model.request.ShopQueryRequest;
import com.jackrain.nea.data.basic.model.request.StoreInfoQueryRequest;
import com.jackrain.nea.data.basic.services.BasicCpQueryService;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ip.api.AddressAnalyzeFrom100Cmd;
import com.jackrain.nea.ip.api.LogisticsSendCmd;
import com.jackrain.nea.ip.api.ModifyOrderAddrCmd;
import com.jackrain.nea.ip.api.OrderDecryptCmd;
import com.jackrain.nea.ip.api.QueryLogisticsInfoFrom100Cmd;
import com.jackrain.nea.ip.api.TradeMemoUpdateCmd;
import com.jackrain.nea.ip.api.ascp.AlibabaAscpInStorageFeedbackCmd;
import com.jackrain.nea.ip.api.ascp.AlibabaAscpOrderCancelFeedbackCmd;
import com.jackrain.nea.ip.api.ascp.AlibabaAscpOutOfStockCallbackCmd;
import com.jackrain.nea.ip.api.ascp.AlibabaAscpShippingBackCmd;
import com.jackrain.nea.ip.api.bn.BnCmd;
import com.jackrain.nea.ip.api.dms.Delivery2DmsCmd;
import com.jackrain.nea.ip.api.dms.DmsCloseBackCmd;
import com.jackrain.nea.ip.api.jingdong.JdDirectLogisticsSendCmd;
import com.jackrain.nea.ip.api.jingdong.JdHufuOrderGetSensitiveDataCmd;
import com.jackrain.nea.ip.api.jingdong.JdOrderServiceCmd;
import com.jackrain.nea.ip.api.jingdong.JdRefundServiceCmd;
import com.jackrain.nea.ip.api.others.OrderDeliveryParcelbackCmd;
import com.jackrain.nea.ip.api.others.OrderRemarkSyncCmd;
import com.jackrain.nea.ip.api.others.ProductDownloadCmd;
import com.jackrain.nea.ip.api.others.StandplatLogisticsSendCmd;
import com.jackrain.nea.ip.api.others.StandplatReturnUpdStatusCmd;
import com.jackrain.nea.ip.api.qimen.QimenOrderCallbackCmd;
import com.jackrain.nea.ip.api.qimen.QimenOrderCancelCmd;
import com.jackrain.nea.ip.api.qimen.QimenPosOrderStatusQueryCmd;
import com.jackrain.nea.ip.api.qimen.QimenPosSaleOrderUpdateCmd;
import com.jackrain.nea.ip.api.qimen.QmReturnOrderCreateCmd;
import com.jackrain.nea.ip.api.sap.SapCloseBackCmd;
import com.jackrain.nea.ip.api.stand.IpBStandModifyAddrCmd;
import com.jackrain.nea.ip.api.taobao.CancelGoodsCmd;
import com.jackrain.nea.ip.api.taobao.ExchangeCmd;
import com.jackrain.nea.ip.api.taobao.LogisticsConsignCmd;
import com.jackrain.nea.ip.api.taobao.LogisticsSendBySplitLineCmd;
import com.jackrain.nea.ip.api.taobao.LogisticsWarehouseUpdateCmd;
import com.jackrain.nea.ip.api.taobao.OrderFullLinkCmd;
import com.jackrain.nea.ip.api.taobao.QianNiuInterceptOrderCallbackCmd;
import com.jackrain.nea.ip.api.taobao.TmallExchangeReturngoodsAgreeCmd;
import com.jackrain.nea.ip.api.vips.JitxVipsSendOutResetCmd;
import com.jackrain.nea.ip.api.vips.VipJitxCreateChangeWarehouseWorkflowCmd;
import com.jackrain.nea.ip.api.vips.VipJitxDeliveryOrderBackCmd;
import com.jackrain.nea.ip.api.vips.VipJitxGetChangeWarehouseWorkflowsCmd;
import com.jackrain.nea.ip.api.vips.VipJitxGetLabelCmd;
import com.jackrain.nea.ip.api.vips.VipJitxGetOrdersByOrderSnCmd;
import com.jackrain.nea.ip.api.vips.VipJitxOrderShipCmd;
import com.jackrain.nea.ip.api.vips.VipJitxRedeliverTransportNoVopCmd;
import com.jackrain.nea.ip.api.wing.OmsToWingCmd;
import com.jackrain.nea.ip.api.yike.QueryYiKeCmd;
import com.jackrain.nea.ip.api.yuchenghe.YuChengHeCmd;
import com.jackrain.nea.ip.common.OrderDecryptDataType;
import com.jackrain.nea.ip.model.CancelGoodsModel;
import com.jackrain.nea.ip.model.ExchangeAgreeModel;
import com.jackrain.nea.ip.model.ExchangeRefuseModel;
import com.jackrain.nea.ip.model.LogisticsSendModel;
import com.jackrain.nea.ip.model.LogisticsWarehouseUpdateModel;
import com.jackrain.nea.ip.model.ModifyOrderAddrModel;
import com.jackrain.nea.ip.model.OrderFullLinkModel;
import com.jackrain.nea.ip.model.QianNiuInterceptOrderCallbackModel;
import com.jackrain.nea.ip.model.TbLogisticsSendBySplitLineModel;
import com.jackrain.nea.ip.model.TradeMemoUpdateModel;
import com.jackrain.nea.ip.model.ascp.ConsignOrderCancelFeedbackModel;
import com.jackrain.nea.ip.model.ascp.ConsignOrderOutOfStockCallbackModel;
import com.jackrain.nea.ip.model.ascp.ConsignOrderShipModel;
import com.jackrain.nea.ip.model.ascp.InStorageFeedbackModel;
import com.jackrain.nea.ip.model.bn.BnTaskListQueryRequest;
import com.jackrain.nea.ip.model.bn.BnTaskListQueryResponse;
import com.jackrain.nea.ip.model.dms.request.Delivery2DmsRequest;
import com.jackrain.nea.ip.model.dms.request.DmsOrderBackRequest;
import com.jackrain.nea.ip.model.jingdong.ReceiveRegisterRequest;
import com.jackrain.nea.ip.model.jingdong.ReceiveRequest;
import com.jackrain.nea.ip.model.jingdong.RefundapplyUpdateWarehouseStatusRequest;
import com.jackrain.nea.ip.model.jingdong.request.JdDecryptRequest;
import com.jackrain.nea.ip.model.jingdong.response.JdDecryptResponse;
import com.jackrain.nea.ip.model.others.JdDirectLogisticsSendRequest;
import com.jackrain.nea.ip.model.others.OrderDeliveryParcelbackModel;
import com.jackrain.nea.ip.model.others.OrderRemarkSyncModel;
import com.jackrain.nea.ip.model.others.StandplatLogisticsSendDataModel;
import com.jackrain.nea.ip.model.others.StandplatLogisticsSendModel;
import com.jackrain.nea.ip.model.others.StandplatLogisticsSendResult;
import com.jackrain.nea.ip.model.qimen.QimenOrderCallbackModel;
import com.jackrain.nea.ip.model.qimen.QimenOrderCancelModel;
import com.jackrain.nea.ip.model.qimen.QimenPosOrderStatusQueryExtRequest;
import com.jackrain.nea.ip.model.qimen.QimenPosOrderStatusQueryResult;
import com.jackrain.nea.ip.model.qimen.QimenPosSaleOrderUpdateRequest;
import com.jackrain.nea.ip.model.qimen.QmReturnOrderCreateModel;
import com.jackrain.nea.ip.model.request.ModifyAddressRequest;
import com.jackrain.nea.ip.model.request.OrderDecryptRequest;
import com.jackrain.nea.ip.model.request.VipJitxCreateChangeWarehouseWorkflowRequest;
import com.jackrain.nea.ip.model.request.VipJitxGetChangeWarehouseWorkflowsRequest;
import com.jackrain.nea.ip.model.request.VipJitxGetLabelRequest;
import com.jackrain.nea.ip.model.request.VipJitxRedeliverTransportNoVopRequest;
import com.jackrain.nea.ip.model.result.AddressAnalyseResp;
import com.jackrain.nea.ip.model.result.LogisticsSendResult;
import com.jackrain.nea.ip.model.result.OrderDecryptResponse;
import com.jackrain.nea.ip.model.result.OrderFullLinkResult;
import com.jackrain.nea.ip.model.result.QimenAsyncMqResult;
import com.jackrain.nea.ip.model.result.QueryTrackResp;
import com.jackrain.nea.ip.model.result.VipJitxCreateChangeWarehouseWorkflowResult;
import com.jackrain.nea.ip.model.result.VipJitxGetChangeWarehouseWorkflowsResult;
import com.jackrain.nea.ip.model.result.VipJitxGetLabelResult;
import com.jackrain.nea.ip.model.sap.SapOrderBackRequest;
import com.jackrain.nea.ip.model.table.TmallExchangeReturngoodsAgreeModel;
import com.jackrain.nea.ip.model.taobao.LogisticsConsignResendModel;
import com.jackrain.nea.ip.model.vips.VipCreateShipResetWorkflowRequest;
import com.jackrain.nea.ip.model.vips.VipCreateShipResetWorkflowResult;
import com.jackrain.nea.ip.model.vips.VipGetShipResetWorkflowRequest;
import com.jackrain.nea.ip.model.vips.VipGetShipResetWorkflowResult;
import com.jackrain.nea.ip.model.vips.VipJitxFeedBackDeliveryResultModel;
import com.jackrain.nea.ip.model.vips.VipJitxGetOrdersByOrderSnModel;
import com.jackrain.nea.ip.model.vips.VipJitxOrderShipModel;
import com.jackrain.nea.ip.model.wing.WingDeliveryModel;
import com.jackrain.nea.ip.model.wing.WingIncidentalsOrderCreateModel;
import com.jackrain.nea.ip.model.wing.WingInvalidOrderModel;
import com.jackrain.nea.ip.model.wing.WingReturnOrderCancelModel;
import com.jackrain.nea.ip.model.wing.WingReturnOrderCreateModel;
import com.jackrain.nea.ip.model.wing.WingReturnResult;
import com.jackrain.nea.ip.model.wing.add.WingOutStoOutNoticesRequest;
import com.jackrain.nea.ip.model.wing.cancel.WingOutStoOutNoticesCancelRequest;
import com.jackrain.nea.ip.model.wing.yy.JitxStoreChangeRequest;
import com.jackrain.nea.ip.model.wing.yy.JitxSxOrderAddRequest;
import com.jackrain.nea.ip.model.wing.yy.JitxSxOrderCancelRequest;
import com.jackrain.nea.ip.model.yike.YiKeCouponModel;
import com.jackrain.nea.ip.model.yuchenghe.YuChengHeRequest;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderRefundMapper;
import com.jackrain.nea.oc.oms.mapper.ac.AcFPayableAdjustmentItemMapper;
import com.jackrain.nea.oc.oms.mapper.ac.AcFPayableAdjustmentMapper;
import com.jackrain.nea.oc.oms.model.constant.OcOmsConstant;
import com.jackrain.nea.oc.oms.model.enums.OcBorderListEnums;
import com.jackrain.nea.oc.oms.model.enums.OcReturnBillTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.enums.QiMenOrderStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.Return2WmsBillEnum;
import com.jackrain.nea.oc.oms.model.enums.ToDRPStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.WmsWithdrawalState;
import com.jackrain.nea.oc.oms.model.enums.ac.OperatorLogTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.ac.ResponsiblePartyEnum;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderLog;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
import com.jackrain.nea.oc.oms.services.FindReturnOrderService;
import com.jackrain.nea.oc.oms.services.OcBReturnOrderNodeRecordService;
import com.jackrain.nea.oc.oms.services.OmsReturnOrderService;
import com.jackrain.nea.oc.oms.services.ReturnOrderLogService;
import com.jackrain.nea.oc.oms.util.ReturnOrderPushWingUtils;
import com.jackrain.nea.oc.oms.util.SplitMessageUtil;
import com.jackrain.nea.oc.oms.util.WmsUserCreateUtil;
import com.jackrain.nea.psext.request.PsToWmsRequest;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ACEnumUtil;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.DateUtil;
import com.jackrain.nea.util.Tools;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.util.ValueHolderV14Utils;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: 孙勇生
 * @since: 2019-04-24
 * 接口中心提供的平台、WMS、AG等RPC接口
 */
@Slf4j
@Component
public class IpRpcService {

    @Value("${wing.order.default.store.code:ECTZ3}")
    private String defaultStoreCode;

    @Value("${wing.order.default.wms.code:DRP}")
    private String defaultWmsCode;

    @Value("${system.gift.default.oid:-1}")
    private String defaultOid;


    @Value("${return.to.wing.fail.default.count:6}")
    private Integer defaultCount;

    @Value("${return.to.wing.sku.use.forcode.enable:true}")
    private boolean useFroCode;

    @Autowired
    private StdRpcService stdRpcService;

    @Autowired
    private BasicCpQueryService basicCpQueryService;


    @Reference(group = "ip", version = "1.4.0")
    private OmsToWingCmd omsToWingCmd;

    @Reference(group = "ip", version = "1.4.0")
    private TradeMemoUpdateCmd tradeMemoUpdateCmd;

    @Reference(group = "ip", version = "1.4.0")
    private QimenOrderCancelCmd qimenOrderCancelCmd;

    @Reference(group = "ip", version = "1.4.0")
    private QmReturnOrderCreateCmd qimenEntryorderCreateCmd;

    @Reference(group = "ip", version = "1.4.0")
    private LogisticsSendCmd logisticsSendCmd;

    @Reference(group = "ip", version = "1.4.0")
    private LogisticsSendBySplitLineCmd logisticsSendBySplitLineCmd;

    @Reference(version = "1.4.0", group = "ip")
    private QimenPosSaleOrderUpdateCmd qimenPosSaleOrderUpdateCmd;

    @Reference(group = "ip", version = "1.4.0")
    private VipJitxOrderShipCmd vipJitxOrderShipCmd;

    @Reference(group = "ip", version = "1.4.0")
    private CancelGoodsCmd cancelGoodsCmd;

    @Reference(group = "ip", version = "1.4.0")
    private JdOrderServiceCmd jdOrderServiceCmd;

    @Reference(group = "ip", version = "1.4.0")
    private VipJitxDeliveryOrderBackCmd jitxDeliveryOrderBackCmd;

    @Reference(group = "ip", version = "1.4.0")
    private StandplatReturnUpdStatusCmd standplatReturnUpdStatusCmd;

    @Reference(group = "ip", version = "1.4.0")
    private QimenOrderCallbackCmd qimenOrderCallbackCmd;

    @Reference(group = "ip", version = "1.4.0")
    private YuChengHeCmd yuChengHeCmd;

    @Reference(group = "ip", version = "1.4.0")
    private LogisticsConsignCmd logisticsConsignCmd;

    @Reference(group = "ip", version = "1.4.0")
    private VipJitxGetOrdersByOrderSnCmd vipJitxGetOrdersByOrderSnCmd;

    @Reference(group = "ip", version = "1.4.0")
    private AlibabaAscpShippingBackCmd alibabaAscpShippingBackCmd;

    @Reference(group = "ip", version = "1.4.0")
    private AlibabaAscpOutOfStockCallbackCmd alibabaAscpOutOfStockCallbackCmd;

    @Reference(group = "ip", version = "1.4.0")
    private StandplatLogisticsSendCmd standplatLogisticsSendCmd;

    @Reference(group = "ip", version = "1.4.0")
    private OrderDecryptCmd orderDecryptCmd;

    @Reference(group = "ip", version = "1.4.0")
    private QueryYiKeCmd queryYiKeCmd;

    @Reference(version = "1.4.0", group = "ip")
    private VipJitxGetLabelCmd vipJitxGetLabelCmd;

    @Reference(version = "1.4.0", group = "ip")
    private SapCloseBackCmd sapCloseBackCmd;

    @Reference(version = "1.4.0", group = "ip")
    private Delivery2DmsCmd delivery2DmsCmd;

    @Reference(version = "1.4.0", group = "ip")
    private QueryLogisticsInfoFrom100Cmd queryLogisticsInfoFrom100Cmd;
    @Reference(version = "1.4.0", group = "ip")
    private AddressAnalyzeFrom100Cmd addressAnalyzeFrom100Cmd;

    @Reference(version = "1.4.0", group = "ip")
    private JdDirectLogisticsSendCmd jdDirectLogisticsSendCmd;

    @Reference(version = "1.4.0", group = "ip")
    private JdHufuOrderGetSensitiveDataCmd jdHufuOrderGetSensitiveDataCmd;

    @Reference(version = "1.4.0", group = "ip")
    private DmsCloseBackCmd dmsCloseBackCmd;

    @Reference(version = "1.4.0", group = "ip")
    private VipJitxRedeliverTransportNoVopCmd vipJitxRedeliverTransportNoVopCmd;


    @Autowired
    private OcBReturnOrderRefundMapper refundMapper;

    @Autowired
    private AcFPayableAdjustmentItemMapper payableAdjustmentItemMapper;

    @Autowired
    private AcFPayableAdjustmentMapper payableAdjustmentMapper;

    @Resource
    private PayableAdjustmentSaveService payableAdjustmentSaveService;

    @Autowired
    private FindReturnOrderService findReturnOrderService;
    @Autowired
    private OcBReturnOrderMapper returnOrderMapper;
    @Autowired
    private OmsReturnOrderService omsReturnOrderService;

    @Autowired
    private ReturnOrderLogService returnOrderLogService;

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private OcBReturnOrderNodeRecordService nodeRecordService;

    @Autowired
    private WmsUserCreateUtil wmsUserCreateUtil;

    @Autowired
    private PsRpcService psRpcService;

    @Autowired
    private SgRpcService sgRpcService;

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    /**
     * 猫超销售退货入库回传接口
     */
    @Reference(version = "1.4.0", group = "ip")
    private AlibabaAscpInStorageFeedbackCmd ascpInStorageFeedbackCmd;

    @Reference(group = "ip", version = "1.4.0")
    private JdRefundServiceCmd jdRefundServiceCmd;

    /**
     * 猫超销售订单取消发货回告接口
     */
    @Reference(version = "1.4.0", group = "ip")
    private AlibabaAscpOrderCancelFeedbackCmd orderCancelFeedbackCmd;

    @Reference(group = "ip", version = "1.4.0")
    private VipJitxGetChangeWarehouseWorkflowsCmd vipJitxGetChangeWarehouseWorkflowsCmd;

    @Reference(group = "ip", version = "1.4.0")
    private QianNiuInterceptOrderCallbackCmd qianNiuInterceptOrderCallbackCmd;

    @Reference(group = "ip", version = "1.4.0")
    private LogisticsWarehouseUpdateCmd logisticsWarehouseUpdateCmd;

    @Reference(group = "ip", version = "1.4.0")
    private VipJitxCreateChangeWarehouseWorkflowCmd vipJitxCreateChangeWarehouseWorkflowCmd;

    @Reference(group = "ip", version = "1.4.0")
    private OrderFullLinkCmd orderFullLinkCmd;

    @Reference(group = "ip", version = "1.4.0")
    private QimenPosOrderStatusQueryCmd qimenPosOrderStatusQueryCmd;

    @Reference(group = "ip", version = "1.4.0")
    private ModifyOrderAddrCmd modifyOrderAddrCmd;

    @Reference(group = "ip", version = "1.4.0")
    private ExchangeCmd exchangeCmd;

    @Reference(group = "ip", version = "1.4.0")
    private JitxVipsSendOutResetCmd jitxVipsSendOutResetCmd;

    @Reference(group = "ip", version = "1.4.0")
    private IpBStandModifyAddrCmd ipBStandModifyAddrCmd;

    @Reference(version = "1.4.0", group = "ip")
    private OrderRemarkSyncCmd orderRemarkSyncCmd;

    @Reference(version = "1.4.0", group = "ip")
    private ProductDownloadCmd productDownloadCmd;

    @Reference(version = "1.4.0", group = "ip")
    private OrderDeliveryParcelbackCmd orderDeliveryParcelbackCmd;

    @Reference(version = "1.4.0", group = "ip")
    private BnCmd bnCmd;

    public ValueHolderV14 jitxDealerOrderCancelFromWing(WingOutStoOutNoticesCancelRequest request) {
        log.info("{},jitx经销商未发货取消入参：{}", this.getClass().getSimpleName(), JSON.toJSONString(request));
        ValueHolderV14<String> v14 = omsToWingCmd.cancelOrders(request);
        log.info("{},jitx经销商未发货取消结果：{}", this.getClass().getSimpleName(), JSON.toJSONString(v14));
        if (v14.isOK()) {
            WingReturnResult result = JSON.parseObject(v14.getData(), WingReturnResult.class);
            if (result.isOk()) {
                return ValueHolderV14Utils.getSuccessValueHolder(String.format("调用WING取消结果：%s", StringUtils.isEmpty(result.getMsg()) ? "成功" : result.getMsg()));
            } else {
                return ValueHolderV14Utils.getFailValueHolder(String.format("调用WING取消结果：%s", StringUtils.isEmpty(result.getMsg()) ? "失败(wing返回的失败信息为空)" : result.getMsg()));
            }
        }
        return v14;
    }

    public ValueHolderV14<String> vipJitxGetLabel(VipJitxGetLabelRequest request) {

        ValueHolderV14<String> valueHolderV14 = new ValueHolderV14<>(ResultCode.FAIL, "获取电子面单失败");
        try {
            if (log.isDebugEnabled()) {
                log.debug(" 获取电子面单，入参：{}", JSON.toJSONString(request));
            }
            ValueHolderV14<List<VipJitxGetLabelResult>> result = vipJitxGetLabelCmd.getLabel(request);
            if (log.isDebugEnabled()) {
                log.debug(" 获取电子面单结果：{}", result);
            }
            if (result.isOK()) {
                valueHolderV14.setData(result.getData().get(0).getOrder_label());
                valueHolderV14.setCode(result.getCode());
                valueHolderV14.setMessage(result.getMessage());
            } else {
                valueHolderV14.setMessage(result.getMessage());
            }
        } catch (Exception e) {
            log.error(" 获取电子面单异常：{}", Throwables.getStackTraceAsString(e));
            valueHolderV14.setMessage(e.getMessage());
        }
        return valueHolderV14;
    }

    public ValueHolderV14 cancelSxJitxOrder(JitxSxOrderCancelRequest request) {
        if (request == null) {
            return ValueHolderV14Utils.getFailValueHolder("参数异常");
        }
        try {
            log.info("{},cancelSxJitxOrder 请求参数:{}", this.getClass().getSimpleName(), JSON.toJSONString(request));
            ValueHolderV14<WingReturnResult> v14 = omsToWingCmd.cancelSxJitxOrder(request);
            log.info("{},cancelSxJitxOrder 请求结果:{}", this.getClass().getSimpleName(), JSON.toJSONString(v14));
            return v14;
        } catch (Exception e) {
            return ValueHolderV14Utils.getFailValueHolder(String.format("请求接口异常:%s", SplitMessageUtil.splitMsgBySize(e.getMessage(), SplitMessageUtil.SIZE_999)));
        }
    }

    public ValueHolderV14 addSxJitxOrder(List<JitxSxOrderAddRequest> request) {
        if (CollectionUtils.isEmpty(request)) {
            return ValueHolderV14Utils.getFailValueHolder("参数异常");
        }
        try {
            log.info("{},addSxJitxOrder 请求参数:{}", this.getClass().getSimpleName(), JSON.toJSONString(request));
            ValueHolderV14<WingReturnResult> v14 = omsToWingCmd.addSxJitxOrder(request);
            log.info("{},addSxJitxOrder 请求结果:{}", this.getClass().getSimpleName(), JSON.toJSONString(v14));
            return v14;
        } catch (Exception e) {
            return ValueHolderV14Utils.getFailValueHolder(String.format("请求接口异常:%s", SplitMessageUtil.splitMsgBySize(e.getMessage(), SplitMessageUtil.SIZE_999)));
        }
    }

    public ValueHolderV14<WingReturnResult> changeStoreJitxOrder(List<JitxStoreChangeRequest> request) {
        ValueHolderV14<WingReturnResult> valueHolderV14 = new ValueHolderV14<>(ResultCode.FAIL, "调用失败");
        if (CollectionUtils.isEmpty(request)) {
            valueHolderV14.setMessage("参数为空");
            return valueHolderV14;
        }
        try {
            log.info("{},changeStoreJitxOrder 请求参数:{}", this.getClass().getSimpleName(), JSON.toJSONString(request));
            valueHolderV14 = omsToWingCmd.changeStoreJitxOrder(request);
            log.info("{},changeStoreJitxOrder 请求结果:{}", this.getClass().getSimpleName(), JSON.toJSONString(valueHolderV14));
        } catch (Exception e) {
            log.error(" 通知YY换仓结果异常：{}", Throwables.getStackTraceAsString(e));
            valueHolderV14.setMessage(e.getMessage());
        }
        return valueHolderV14;
    }

    public ValueHolderV14<VipCreateShipResetWorkflowResult> createShipResetWorkflowVop(VipCreateShipResetWorkflowRequest request) {
        log.info("createShipResetWorkflowVop 创建重置发货工单入参：{}", JSON.toJSONString(request));
        ValueHolderV14<VipCreateShipResetWorkflowResult> v14 = jitxVipsSendOutResetCmd.createShipResetWorkflowVop(request);
        log.info("createShipResetWorkflowVop 创建重置发货工单返回结果：{}", JSON.toJSONString(v14));
        return v14;
    }

    public ValueHolderV14<VipGetShipResetWorkflowResult> getShipResetWorkflowVop(VipGetShipResetWorkflowRequest request) {
        log.info("getShipResetWorkflowVop 查询重置发货工单入参：{}", JSON.toJSONString(request));
        ValueHolderV14<VipGetShipResetWorkflowResult> v14 = jitxVipsSendOutResetCmd.getShipResetWorkflowVop(request);
        log.info("getShipResetWorkflowVop 查询重置发货工单返回结果：{}", JSON.toJSONString(v14));
        return v14;
    }


    public ValueHolderV14 addIncidentals(WingIncidentalsOrderCreateModel model) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("调用杂费单开始:{}", model.getId()));
        }
        return omsToWingCmd.addIncidentals(model);
    }

    public ValueHolderV14 batchAddIncidentals(List<WingIncidentalsOrderCreateModel> modelList) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("调用杂费单开始 modelList:{}", "调用杂费单开始"), JSON.toJSONString(modelList));
        }
        return omsToWingCmd.batchAddIncidentals(modelList);
    }

    /**
     * 通用平台发货RPC调用
     *
     * @param model
     * @return
     */
    public ValueHolderV14<List<StandplatLogisticsSendResult>> sendStandPlatLogistics(StandplatLogisticsSendDataModel model) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("通用平台发货RPC接口调用入参>>>{}","通用平台发货RPC接口调用入参"), JSON.toJSONString(model));
        }
        ValueHolderV14<List<StandplatLogisticsSendResult>> vh = standplatLogisticsSendCmd.sendStandplatLogistics(model);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("通用平台发货RPC接口调用返回结果>>>{}", "通用平台发货RPC接口调用返回结果"), JSON.toJSONString(vh));
        }
        return vh;
    }

    /**
     * 通用平台（抖音）换货发货RPC调用
     *
     * @return
     */
    public ValueHolderV14 sendStandPlatLogisticsExchange(OcBOrder ocBOrder, String companyCode, String returnNo) {
        if (log.isDebugEnabled()) {
            log.debug(" 通用平台抖音换货发货RPC接口调用入参 {}", JSON.toJSONString(ocBOrder));
        }
        ValueHolderV14<List<StandplatLogisticsSendResult>> vh = standplatLogisticsSendCmd.sendStandplatLogisticsExchange(ocBOrder, companyCode, returnNo);
        if (log.isDebugEnabled()) {
            log.debug(" 通用平台发货RPC接口调用返回结果 {}", JSON.toJSONString(vh));
        }
        return vh;
    }

    private JSONObject setWebsitePlatformDeliveryParam(StandplatLogisticsSendDataModel model) {
        JSONObject param = new JSONObject();
        JSONArray array = new JSONArray(model.getLogisticsSendModels().size());
        for (StandplatLogisticsSendModel sendModel : model.getLogisticsSendModels()) {
            JSONObject mainParam = new JSONObject();
            mainParam.put("tid", sendModel.getTid());
            mainParam.put("cp_c_logistics_ecode", sendModel.getCompanyCode());
            mainParam.put("status", "ORDER_STATUS_SHIPPED");
            mainParam.put("logistic_number", sendModel.getOutSid());
            mainParam.put("status-datetime", DateUtil.format(new Date(), DateUtil.dateTimeSecondsFormatter.getPattern()));
            List<StandplatLogisticsSendModel.StandplatLogisticsSendDetails> sendDetails = sendModel.getStandplatLogisticsSendDetails();
            JSONArray itemArray = new JSONArray(sendDetails.size());
            for (StandplatLogisticsSendModel.StandplatLogisticsSendDetails detail : sendDetails) {
                JSONObject itemParam = new JSONObject();
                itemParam.put("ps_c_sku_ecode", detail.getSku());
                itemParam.put("qty", detail.getNum());
                itemArray.add(itemArray);
            }
            mainParam.put("product", itemArray);
            array.add(mainParam);
        }
        param.put("data", array);
        return param;
    }


    /**
     * 修改备注同步平台接口
     *
     * @param tradeMemoUpdateModel
     * @return
     */
    public ValueHolderV14 tradeMemoUpdate(TradeMemoUpdateModel tradeMemoUpdateModel) {
        return tradeMemoUpdateCmd.updateTradeMemo(tradeMemoUpdateModel);
    }

    /**
     * 御城河接口对接
     *
     * @param yuChengHeRequest
     * @param user
     * @return
     */
    public ValueHolderV14 yuChengHeEventExecute(YuChengHeRequest yuChengHeRequest, User user) {
        return yuChengHeCmd.yuChengHeEventExecute(yuChengHeRequest, user);
    }

    /**
     * description：推送退单到wing（wms）
     *
     * <AUTHOR>
     * @date 2021/7/25
     */
    public void pushReturnOrderToWms(List<OcBReturnOrder> returnOrders, List<OcBReturnOrderRefund> ocBReturnOrderRefunds) {
        try {
            if (CollectionUtils.isEmpty(returnOrders)) {
                return;
            }
            List<QmReturnOrderCreateModel> modelList = new ArrayList<>(returnOrders.size());
            Map<String, OcBReturnOrder> returnOrdersMap = returnOrders.stream().collect(Collectors.toMap(OcBReturnOrder::getBillNo, Function.identity(), (v1, v2) -> v1));

            buildReturnOrderCreateRequest(returnOrders, ocBReturnOrderRefunds, modelList);

            if (CollectionUtils.isEmpty(modelList)) {
                return;
            }

            log.info(LogUtil.format("退单传WMS:{}", "退单传WMS入参"), JSON.toJSONString(modelList));

            ValueHolderV14<List<QimenAsyncMqResult>> vh = qimenEntryorderCreateCmd.returnOrderCreate(modelList, "oc_oms", wmsUserCreateUtil.initWmsUser());
            Boolean isAllFalse = !vh.isOK() && (CollectionUtils.isEmpty(vh.getData())
                    || StringUtils.isBlank(vh.getData().get(0).getBillNo()));

            log.info(LogUtil.format("退单传WMS结果:{}", "退单传WMS结果"), JSON.toJSONString(vh));

            AssertUtil.assertException(isAllFalse, this.subStringMsg(vh.getMessage()));

            vh.getData().forEach(d -> {
                if ("false".equals(d.getIsSend())) {
                    String billNo = d.getBillNo();
                    if (StringUtils.isBlank(billNo)) {
                        log.error(LogUtil.format("退单传wms调用奇门billNo为空"));
                        return;
                    }
                    OcBReturnOrder returnOrder = returnOrdersMap.get(billNo);
                    if (Objects.isNull(returnOrder) || Objects.isNull(returnOrder.getId())) {
                        log.error(LogUtil.format("退单传wms调用奇门returnOrderIdNotFoundByBillNo={}", billNo), billNo);
                        return;
                    }
                    String message = this.subStringMsg(d.getMessage());
                    // 批量更新退单状态为失败
                    String ids = StringUtils.join(Collections.singletonList(returnOrder.getId()), ",");
                    returnOrderMapper.updateWmsStatusByIds(ids,
                            WmsWithdrawalState.NO.toInteger(), 0, message);

                    // 批量新增日志
                    omsReturnOrderService.batchSaveAddOrderReturnLog(returnOrders,
                            "退单传WMS", String.format("传WMS失败，原因：%s", message), SystemUserResource.getRootUser());
                }
            });
        } catch (Exception e) {
            throw new NDSException("退单传wms异常");
        }
    }

    private void buildReturnOrderCreateRequest(List<OcBReturnOrder> returnOrders,
                                               List<OcBReturnOrderRefund> allItemList,
                                               List<QmReturnOrderCreateModel> requestList) {
        // 查询实体仓
        List<Long> warehouseIds = returnOrders.stream().map(OcBReturnOrder::getCpCPhyWarehouseInId).distinct().collect(Collectors.toList());
        Map<Long, CpCPhyWarehouse> warehouseMap = cpRpcService.rpcQueryCpCPhyWareHouses(warehouseIds);

        for (OcBReturnOrder returnOrder : returnOrders) {
            List<QmReturnOrderCreateModel.OrderLine> orderLines = new ArrayList<>();
            QmReturnOrderCreateModel model = new QmReturnOrderCreateModel();
            QmReturnOrderCreateModel.SenderInfo senderInfo = new QmReturnOrderCreateModel.SenderInfo();
            QmReturnOrderCreateModel.ReturnOrder entryOrder = new QmReturnOrderCreateModel.ReturnOrder();
            Long cpCPhyWarehouseId = returnOrder.getCpCPhyWarehouseInId();
            if (Objects.isNull(cpCPhyWarehouseId) || Objects.isNull(warehouseMap.get(cpCPhyWarehouseId))) {
                this.updateReturnOrderRemark(returnOrder, "传WMS异常：当前退换货单实体仓信息为空");
                continue;
            }
            CpCPhyWarehouse cpCPhyWarehouse = warehouseMap.get(cpCPhyWarehouseId);
            // 单据编号
            entryOrder.setReturnOrderCode(returnOrder.getBillNo());
            // 仓库编码
            entryOrder.setWarehouseCode(cpCPhyWarehouse.getWmsWarehouseCode());
            // 物流公司编码
            entryOrder.setLogisticsCode(StringUtils.isBlank(returnOrder.getCpCLogisticsEcode()) ? "OTHER" : returnOrder.getCpCLogisticsEcode());
            // 物流公司名称
            entryOrder.setLogisticsName(returnOrder.getCpCLogisticsEname());
            // 退货原因
            entryOrder.setReturnReason(returnOrder.getReturnDesc());
            // WMS物流单号
            //AssertUtil.assertException(StringUtils.isBlank(returnOrder.getExpressCode()), "物流单号不能为空");
            entryOrder.setLogisticsName(returnOrder.getCpCLogisticsEname());
            entryOrder.setExpressCode(returnOrder.getLogisticsCode());

            entryOrder.setShopNick(returnOrder.getCpCShopTitle());
            entryOrder.setBuyerNick(returnOrder.getBuyerNick());
            entryOrder.setSenderInfo(senderInfo);

            senderInfo.setName(returnOrder.getReceiveName());
            senderInfo.setMobile(returnOrder.getReceiveMobile());
            senderInfo.setProvince(returnOrder.getReceiverProvinceName());
            senderInfo.setCity(returnOrder.getReceiverCityName());
            senderInfo.setArea(returnOrder.getReceiverProvinceName());
            senderInfo.setDetailAddress(returnOrder.getReceiveAddress());

            //单据类型(THRK=退货入库;HHRK=换货入库)
            if (TaobaoReturnOrderExt.BillType.REFUND.getCode().equals(returnOrder.getBillType())) {
                entryOrder.setOrderType(Return2WmsBillEnum.RETURN.val());
            } else {
                entryOrder.setOrderType(Return2WmsBillEnum.EXCHANGE.val());
            }

            List<OcBReturnOrderRefund> ocBReturnOrderRefundItems = allItemList.stream().
                    filter(refund -> refund.getOcBReturnOrderId().equals(returnOrder.getId())).collect(Collectors.toList());

            AssertUtil.assertException(CollectionUtils.isEmpty(ocBReturnOrderRefundItems), "退单明细为空");

            if (CollectionUtils.isNotEmpty(ocBReturnOrderRefundItems)) {
                OcBOrder ocBOrder = ocBOrderMapper.selectById(ocBReturnOrderRefundItems.get(0).getOcBOrderId());
                //原订单的出库通知单号
                entryOrder.setPreDeliveryOrderCode(Optional.ofNullable(ocBOrder.getSgBOutBillNo()).orElse(String.valueOf(ocBOrder.getId())));
                //原订单的wms单号
                entryOrder.setPreDeliveryOrderId(ocBOrder.getSgBOutBillNo());
                for (OcBReturnOrderRefund item : ocBReturnOrderRefundItems) {
                    QmReturnOrderCreateModel.OrderLine orderLine = new QmReturnOrderCreateModel.OrderLine();
                    // 货主编码
                    orderLine.setOwnerCode(cpCPhyWarehouse.getOwnerCode());
                    // 商品编码
                    orderLine.setItemCode(item.getPsCSkuEcode());
                    // 应收商品数量
                    orderLine.setPlanQty(item.getQtyRefund().longValue());
                    // 为了多个WMS兼容，修改为默认ZP（有部分WMS仓库不支持TH）
                    orderLine.setInventoryType("ZP");
                    // 单据行号
                    orderLine.setOrderLineNo(String.valueOf(item.getId()));
                    // 原始平台单号
                    orderLine.setSourceOrderCode(returnOrder.getTid());
                    // todo 商品数据推送表 itemid
                    //orderLine.setItemId();
                    orderLines.add(orderLine);
                }
            }
            model.setOrderLines(orderLines);
            model.setCustomerId(cpCPhyWarehouse.getWmsAccount());
            model.setReturnOrder(entryOrder);
            requestList.add(model);
        }
    }

    private int getBusinessType(int platform) {
        int businessType = 0;
        if (PlatFormEnum.VIP_JITX.getCode().equals(platform)) {
            businessType = 3;
        } else if (PlatFormEnum.POS.getCode().equals(platform)) {
            businessType = 1;
        }
        return businessType;
    }

    private void batchInsertReturnOrderLog(List<Long> returnOrderIds, String msg) {
        String logType = "零售退货单传DRP";
        User user = wmsUserCreateUtil.initWmsUser();
        List<OcBReturnOrderLog> logList = new ArrayList<>(returnOrderIds.size());
        for (Long id : returnOrderIds) {
            OcBReturnOrderLog returnOrderLog = returnOrderLogService.buildReturnOrderLog(id, logType, msg, user);
            if (returnOrderLog != null) {
                logList.add(returnOrderLog);
            }
        }
        if (CollectionUtils.isNotEmpty(logList)) {
            returnOrderLogService.batchInsertLog(logList);
        }
    }

    private String subStringMsg(String msg) {
        return msg == null ? StringUtils.EMPTY : (msg.length() > 200 ? msg.substring(0, 200) : msg);
    }

    private void updateToDrpStatusSuccess(List<Long> successIdList) {
        if (CollectionUtils.isEmpty(successIdList)) {
            return;
        }
        LambdaQueryWrapper<OcBReturnOrder> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.in(OcBReturnOrder::getId, successIdList);
        OcBReturnOrder ocBReturnOrder = new OcBReturnOrder();
        ocBReturnOrder.setModifieddate(new Date());
        ocBReturnOrder.setToDrpStatus(ToDRPStatusEnum.SUCCESS.getCode());
        ocBReturnOrder.setToDrpCount(0);
        ocBReturnOrder.setToDrpFailedReason("");
        ocBReturnOrder.setWmsFailreason("");
        ocBReturnOrder.setQtyWmsFail(0L);
        returnOrderMapper.update(ocBReturnOrder, queryWrapper);
        String logMsg = "退货单传DRP成功";
        this.batchInsertReturnOrderLog(successIdList, logMsg);
    }

    private void updateReturnOrderRemark(OcBReturnOrder order, String remark) {
        User user = SystemUserResource.getRootUser();
        OcBReturnOrder returnOrderExt = new OcBReturnOrder();
        returnOrderExt.setId(order.getId());
        //仓库数据异常也标记为传DRP失败
        returnOrderExt.setToDrpStatus(ToDRPStatusEnum.FAIL.getCode());
        Integer toDrpCount = order.getToDrpCount() == null ? 0 : order.getToDrpCount();
        returnOrderExt.setToDrpCount(toDrpCount + 1);
        returnOrderExt.setToDrpFailedReason(remark);
        returnOrderExt.setIsTowms(WmsWithdrawalState.FAIL.toInteger());
        Long wmsFailCount = order.getQtyWmsFail() == null ? 0 : order.getQtyWmsFail();
        returnOrderExt.setQtyWmsFail(wmsFailCount + 1);
        returnOrderExt.setWmsFailreason(remark);
        returnOrderExt.setRemark(remark);
        returnOrderExt.setModifieddate(new Date());
        returnOrderExt.setModifierid(user.getId() + 0L);
        returnOrderExt.setModifiername(user.getName());
        returnOrderExt.setModifierename(user.getEname());
        returnOrderExt.setThirdPartyFailStatus(ReturnOrderPushWingUtils.THIRD_PARTY_FAIL_STATUS_02);
        returnOrderMapper.updateById(returnOrderExt);
    }

    private void insertPayableAdjustmentLog(Long id, String remark) {
        try {
            payableAdjustmentSaveService.insertLogFun(SystemUserResource.getRootUser(), id, OperatorLogTypeEnum.PUSH_TO_WING.getVal(), remark, new Date());
        } catch (Exception e) {
            log.error(LogUtil.format(" 丢件单日志记录异常：{}","丢件单日志记录异常"), Throwables.getStackTraceAsString(e));
        }
    }

    /**
     * description：丢件丢件单推送到wing产生退单
     *
     * <AUTHOR>
     * @date 2021/7/25
     */
    public ValueHolderV14<WingReturnResult> pushPayAbleAdjustmentToWing(List<AcFPayableAdjustmentDO> adjustmentDOS) {
        if (CollectionUtils.isEmpty(adjustmentDOS)) {
            return ValueHolderV14Utils.getFailValueHolder("数据为空");
        }
        //查询关联发货单
        List<String> orderNoList = adjustmentDOS.stream().map(AcFPayableAdjustmentDO::getOrderNo).distinct().collect(Collectors.toList());
        List<OcBOrder> ocBOrders = ocBOrderMapper.selectList(new LambdaQueryWrapper<OcBOrder>().in(OcBOrder::getBillNo, orderNoList));
        Map<String, OcBOrder> ocBOrderMap = ocBOrders.stream().collect(Collectors.toMap(OcBOrder::getBillNo, x -> x));
        //查询店铺
        List<Long> shopIds = adjustmentDOS.stream().map(AcFPayableAdjustmentDO::getCpCShopId).distinct().collect(Collectors.toList());
        ShopQueryRequest shopQueryRequest = new ShopQueryRequest();
        shopQueryRequest.setShopIds(shopIds);
        HashMap<Long, CpShop> shopInfoMap = basicCpQueryService.getShopInfo(shopQueryRequest);
        //查询实体仓
        List<Long> warehouseIds = adjustmentDOS.stream().filter(x -> x.getCpCPhyWarehouseId() != null).map(AcFPayableAdjustmentDO::getCpCPhyWarehouseId).distinct().collect(Collectors.toList());
        Map<Long, CpCPhyWarehouse> warehouseMap = cpRpcService.rpcQueryCpCPhyWareHouses(warehouseIds);
        List<WingReturnOrderCreateModel> modelList = new ArrayList<>(adjustmentDOS.size());
        List<Long> errIdList = new ArrayList<>(adjustmentDOS.size());
        for (AcFPayableAdjustmentDO adjustmentDO : adjustmentDOS) {
            List<WingReturnOrderCreateModel.OrderGoods> orderGoodsList = new ArrayList<>(adjustmentDOS.size());
            WingReturnOrderCreateModel model = new WingReturnOrderCreateModel();
            try {
                Long cpCPhyWarehouseId = adjustmentDO.getCpCPhyWarehouseId();
                if (cpCPhyWarehouseId == null) {
                    this.insertPayableAdjustmentLog(adjustmentDO.getId(), "传WING异常：当前丢件单实体仓信息为空");
                    continue;
                }
                OcBOrder ocBOrder = ocBOrderMap.get(adjustmentDO.getOrderNo());
                //("中台退单号")
                model.setId(adjustmentDO.getBillNo());
                //("原始中台订单号")
                model.setOriginal_id(ocBOrder.getSgBOutBillNo());
                //("原始平台单号")
                model.setOriginal_tid(adjustmentDO.getTid());
                //("物流公司CODE")
                model.setShipperCode(adjustmentDO.getCpCLogisticsEcode());
                //("物流编码")
                model.setLogisticsNumber(adjustmentDO.getLogisticsNo());
                //单据类型 丢件单默认为退货单
                model.setOrderType(TaobaoReturnOrderExt.BillType.REFUND.getCode().toString());
                //订单支付时间
                Date payTime = ocBOrder.getPayTime() == null ? new Date() : ocBOrder.getPayTime();
                model.setPayTime(DateUtil.format(payTime, DateUtil.dateTimeSecondsFormatter.getPattern()));
                //("商家店铺电话")
                CpShop cpShop = shopInfoMap.get(adjustmentDO.getCpCShopId());
                if (cpShop != null) {
                    model.setSellerPhone(cpShop.getSellerPhone());
                    //("下单店铺CODE")
                    model.setShopCode(cpShop.getEcode());
                }
                // 20220217 新增字段
                model.setPlatform(ocBOrder.getPlatform() == null ? "" : ocBOrder.getPlatform().toString());
                //("下单店铺名称")
                model.setShopName(adjustmentDO.getCpCShopTitle());
                //("退货逻辑仓编号")
                model.setWarehouseCode(defaultStoreCode);
                //("退货逻辑仓名称")
                model.setWarehouseName(defaultStoreCode);

                CpCPhyWarehouse warehouse = warehouseMap.get(cpCPhyWarehouseId);
                if (warehouse != null) {
                    //("WMS类型编号")
                    model.setWmsType(defaultWmsCode);
                    //("WMS编号")
                    model.setWmsCode(defaultWmsCode);
                    //("WMS物理仓编号")
                    model.setWmsPass(warehouse.getEcode());
                } else {
                    if (log.isDebugEnabled()) {
                        log.debug(LogUtil.format("未查询到实体仓信息", "未查询到实体仓信息"));
                    }
                    this.insertPayableAdjustmentLog(adjustmentDO.getId(), "传WING异常：未查询到实体仓信息,wareHouseId为：" + cpCPhyWarehouseId);
                    continue;
                }
                String qtyAll = ocBOrder.getQtyAll() == null ? "0" : (ocBOrder.getQtyAll().intValue() + "");
                //("商品数量")
                model.setAllnum(qtyAll);
                // ("寄件货人")
                model.setReceivname(ocBOrder.getReceiverName());
                //("寄件省名称")
                model.setReceivprovincename(ocBOrder.getCpCRegionProvinceEname());
                //("寄件家市名称")
                model.setReceivcityname(ocBOrder.getCpCRegionCityEname());
                //("寄件区名称")
                model.setReceivareaname(ocBOrder.getCpCRegionAreaEname());
                //("寄件人手机")  存放責任方中文描述 20210817 修改
                model.setReceivphone(ResponsiblePartyEnum.getTextByVal(adjustmentDO.getResponsibleParty()));
                //("寄件电话")
                model.setReceivmobile(ocBOrder.getReceiverMobile());
                //("寄件收货详细地址")
                model.setReceivaddress(ocBOrder.getReceiverAddress());
                //("寄件人的邮编")
                model.setReceivzip(ocBOrder.getReceiverZip());
                Date creationDate = ocBOrder.getCreationdate() == null ? new Date() : ocBOrder.getCreationdate();
                model.setCreationDate(DateUtil.format(creationDate, DateUtil.dateTimeSecondsFormatter.getPattern()));
                //赔付原因
                String returnReason = ACEnumUtil.payBillTypeMap.get(adjustmentDO.getBillType());
                model.setPayForType(StringUtils.isEmpty(returnReason) ? "空" : returnReason);
                //总金额
                BigDecimal returnOrderAmt = adjustmentDO.getPayablePrice() == null ? BigDecimal.ZERO : adjustmentDO.getPayablePrice();
                model.setOrderAmount(returnOrderAmt.toString());

                List<AcFPayableAdjustmentItemDO> itemDOList = payableAdjustmentItemMapper.selectListByMainId(adjustmentDO.getId());
                if (CollectionUtils.isEmpty(itemDOList)) {
                    if (log.isDebugEnabled()) {
                        log.debug(LogUtil.format(" 丢件单不存在明细数据：{}", adjustmentDO.getId()));
                    }
                    continue;
                }
                for (AcFPayableAdjustmentItemDO itemDO : itemDOList) {
                    WingReturnOrderCreateModel.OrderGoods orderGoods = new WingReturnOrderCreateModel.OrderGoods();
                    //("中台明细单号(唯一)") id;
                    orderGoods.setId(itemDO.getId().toString());
                    //("中台单号") oid;
                    orderGoods.setOid(itemDO.getId().toString());
                    orderGoods.setTid(adjustmentDO.getTid());
                    //("退货逻辑仓编号") warehouseCode;
                    orderGoods.setWarehouseCode(defaultStoreCode);
                    //("新版SKU") sku;
                    orderGoods.setSku(itemDO.getPsCSkuEcode());
                    //("条码(国际码没有就给条形码)") barcode;
                    if (StringUtils.isEmpty(itemDO.getGbcode())) {
                        orderGoods.setBarcode(itemDO.getPsCSkuEcode());
                    } else {
                        orderGoods.setBarcode(itemDO.getGbcode());
                    }
                    //todo 需确认
                    //("旧版SKU") sku_ls;
                    orderGoods.setSku_ls(itemDO.getPsCSkuEcode());
                    //("款色(新版)") productCode;
                    orderGoods.setProductCode(itemDO.getPsCProEcode());
                    //("颜色编码(新版)") colorCode;
                    orderGoods.setColorCode(itemDO.getPsCClrEcode());
                    //("尺码(新版)") sizeCode;
                    orderGoods.setSizeCode(itemDO.getPsCSizeEcode());
                    //("数量") qty;
                    int qtyRefund = itemDO.getQty() == null ? 0 : itemDO.getQty().intValue();
                    orderGoods.setQty(qtyRefund);
                    //("吊牌价") tag_price;
                    String priceList = itemDO.getStandardPrice() == null ? "" : itemDO.getStandardPrice().toString();
                    orderGoods.setTag_price(priceList);
                    //("退货价(退货件价* 数量)") price;
                    String price = itemDO.getPayablePrice() == null ? "" : itemDO.getPayablePrice().toString();
                    orderGoods.setPrice(price);
                    orderGoodsList.add(orderGoods);
                }
            } catch (Exception e) {
                log.error(LogUtil.format(" 当前数据异常：{}", "error"), Throwables.getStackTraceAsString(e));
                this.insertPayableAdjustmentLog(adjustmentDO.getId(), SplitMessageUtil.splitErrMsgBySize(e.getMessage(), SplitMessageUtil.SIZE_200));
                errIdList.add(adjustmentDO.getId());
                continue;
            }
            model.setOrderGoods(orderGoodsList);
            modelList.add(model);
        }
        if (CollectionUtils.isNotEmpty(errIdList)) {
            log.error(LogUtil.multiFormat(" 字段转换出现异常的数据：{}", errIdList));
        }
        //測試时打印
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format(" 传给wing的退单数据：{}", "传给wing的退单数据"), JSON.toJSONString(modelList));
        }
        ValueHolderV14<WingReturnResult> valueHolderV14 = omsToWingCmd.pushReturnOrder(modelList);
        return valueHolderV14;
    }

    /**
     * 订单取消接口
     * 即 退单从WMS撤回接口
     *
     * @param
     * @return
     */
    public ValueHolderV14 qiMenOrderCancel(QimenOrderCancelModel model) {
        ValueHolderV14 vh = new ValueHolderV14<>();
        try {
            if (log.isDebugEnabled()) {
                log.debug("QmReturnOrderCreateCmd.cancelQimenOrder.param：{}", JSON.toJSONString(model));
            }
            vh = qimenOrderCancelCmd.cancelQimenOrder(model);
            if (log.isDebugEnabled()) {
                log.debug("QmReturnOrderCreateCmd.cancelQimenOrder.result：{}", JSON.toJSONString(vh));
            }
        } catch (Exception e) {
            log.error("QmReturnOrderCreateCmd.cancelQimenOrder.error", e);
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(e.getMessage());
        }
        return vh;
    }

    /**
     * description：调用wing取消退单接口
     *
     * <AUTHOR>
     * @date 2021/7/20
     */
    @Transactional
    public ValueHolderV14 cancelReturnOrder(OcBReturnOrder returnOrder, User user) {
        ValueHolderV14<WingReturnResult> vh = new ValueHolderV14();
        try {
            WingReturnOrderCancelModel model = new WingReturnOrderCancelModel();
            Integer isTowms = returnOrder.getIsTowms();
            Long cpCPhyWarehouseId = returnOrder.getCpCPhyWarehouseInId();
            if (cpCPhyWarehouseId == null) {
                return ValueHolderV14Utils.getFailValueHolder("实体仓为空");
            }
            CpCPhyWarehouse wareHouse = cpRpcService.queryByWarehouseId(cpCPhyWarehouseId);
            if (wareHouse == null) {
                return ValueHolderV14Utils.getFailValueHolder("未查询到对应实体仓信息");
            }
            if (StringUtils.isNotEmpty(returnOrder.getCpCStoreEcode())) {
                //("退货逻辑仓编号")
                model.setWarehouseCode(returnOrder.getCpCStoreEcode());
            } else {
                CpCStore cpCStore = null;
                StoreInfoQueryRequest storeInfoQueryRequest = new StoreInfoQueryRequest();
                storeInfoQueryRequest.setPhyId(cpCPhyWarehouseId);
                HashMap<Long, List<CpCStore>> storeInfoByPhyId = basicCpQueryService.getStoreInfoByPhyId(storeInfoQueryRequest);

                if (storeInfoByPhyId == null || storeInfoByPhyId.isEmpty()) {
                    return ValueHolderV14Utils.getFailValueHolder("未查询到对应逻辑仓信息");
                }
                List<CpCStore> cpCStores = storeInfoByPhyId.get(cpCPhyWarehouseId);
                if (CollectionUtils.isNotEmpty(cpCStores)) {
                    Optional<CpCStore> returnStore = cpCStores.stream().filter(x -> DrpStoreTypeEnum.TYPE_27.getValue().equals(x.getStoretype())).findFirst();
                    if (returnStore.isPresent()) {
                        cpCStore = returnStore.get();
                    } else {
                        cpCStore = cpCStores.get(0);
                    }
                } else {
                    log.debug(LogUtil.format(" 未查询到逻辑仓仓信息"));
                }
                if (cpCStore != null) {
                    if (log.isDebugEnabled()) {
                        log.debug(LogUtil.format(" 查询到逻辑仓cpCStore:{}", cpCStore.getId()), cpCStore);
                    }
                    //("退货逻辑仓编号")
                    model.setWarehouseCode(cpCStore.getEcode());
                }
            }
            //【传WMS成功】，则进行一步处理
//            if (WmsWithdrawalState.YES.toInteger().equals(isTowms)) {
            model.setId(returnOrder.getBillNo());
            model.setWmsType(wareHouse.getWmsType());
            model.setWmsPass(wareHouse.getEcode());
            model.setWmsCode(wareHouse.getWmsWarehouseCode());
            vh = omsToWingCmd.cancelReturnOrder(model);
            if (vh.isOK()) {
                WingReturnResult returnResult = vh.getData();
                if (returnResult.getStatus() == 1) {
                    vh.setCode(ResultCode.SUCCESS);
                } else {
                    vh.setCode(ResultCode.FAIL);
                    vh.setMessage(returnResult.getMsg());
                }

            }
        } catch (Exception e) {
            log.error(LogUtil.format("从WMS撤回发生异常:{}", "WMS撤回"), Throwables.getStackTraceAsString(e));
            vh.setMessage("从WMS撤回发生异常>>>" + e.getMessage());
            vh.setCode(-1);
            return vh;
        }
        return vh;
    }

    /**
     * 从WMS撤回
     *
     * @param bReturnOrder
     * @param status
     */
    private void setWmsCancelStatus(OcBReturnOrder bReturnOrder, int status) {
        if (bReturnOrder == null) {
            return;
        }
        OcBOrder order = new OcBOrder();
        order.setId(bReturnOrder.getOrigOrderId());
        order.setWmsCancelStatus(status);
        order.setModifieddate(new Date());
        ocBOrderMapper.updateById(order);
    }

    /**
     * 云枢纽入库单创建接口
     * 即 .退单传WMS服务接口
     *
     * @param
     * @return
     */
    public ValueHolderV14 qiMenEntryOrderCreate(List<OcBReturnOrder> returnOrders) throws Exception {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("qimenEntryorderCreate退单传wms调用奇门开始:{}", "退单传wms调用奇门开始"), JSON.toJSONString(returnOrders));
        }
        ValueHolderV14<List<QimenAsyncMqResult>> vh = new ValueHolderV14<>();
        if (CollectionUtils.isEmpty(returnOrders)) {
            vh.setCode(-1);
            vh.setMessage("数据为空,无需处理");
            return vh;
        }
        List<QmReturnOrderCreateModel> models = new ArrayList<>();
        //  List<Long> ids = new ArrayList<>();
        //查询库存中心
        List<Long> idList = returnOrders.stream().map(OcBReturnOrder::getOrigOrderId).collect(Collectors.toList());
        // ids.addAll(idList);
        //todo 现在传wms 奇门时 查询出库通知单会异常找不rpc （库存中心 移除出库通知接口）
        List sgOutQueryResults = null;
                //sgRpcService.getOutNotices(idList);
        //查询退换货单明细
        List<Long> returnIdList = returnOrders.stream().map(OcBReturnOrder::getId).collect(Collectors.toList());
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("qimenEntryorderCreate 退单:{}", "退单"), JSON.toJSONString(returnIdList));
        }
        String joinIds = StringUtils.join(returnIdList, ",");
        List<OcBReturnOrderRefund> ocBReturnOrderRefunds = refundMapper.selectReturnRefundByPIds(joinIds);
        for (OcBReturnOrder returnOrder : returnOrders) {
            // OcBReturnOrder returnOrder = returnOrderMapper.selectByid(returnOrderOrg.getId());
            QmReturnOrderCreateModel model = new QmReturnOrderCreateModel();
            QmReturnOrderCreateModel.SenderInfo senderInfo = new QmReturnOrderCreateModel.SenderInfo();
            QmReturnOrderCreateModel.ReturnOrder entryOrder = new QmReturnOrderCreateModel.ReturnOrder();
            Long id = returnOrder.getCpCPhyWarehouseInId();
            CpCPhyWarehouse warehouse = findReturnOrderService.findWareHouse(id);
            List<QmReturnOrderCreateModel.OrderLine> orderLines = new ArrayList<>();
            //入库单号(入库仓库编码)
            // @20200715 修改ReturnOrderCode，由原来的id修改为billNo
            // entryOrder.setReturnOrderCode(returnOrder.getId().toString());
            entryOrder.setReturnOrderCode(returnOrder.getBillNo());
            //仓库编码
            entryOrder.setWarehouseCode(warehouse.getWmsWarehouseCode());
            Long origOrderId = returnOrder.getOrigOrderId();
            //从库存中心取值
            // SgBPhyOutNotices outNotices = sgRpcService.getOutNotices(origOrderId);
            if (CollectionUtils.isNotEmpty(sgOutQueryResults)) {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("qimenEntryorderCreate 库存返回结果:{}"), JSON.toJSONString(sgOutQueryResults));
                }
//                for (SgOutQueryResult sgOutQueryResult : sgOutQueryResults) {
//                    SgBPhyOutNotices sgBPhyOutNotices = sgOutQueryResult.getNotices().getOutNotices();
//                    if (sgBPhyOutNotices.getSourceBillId().equals(origOrderId)) {
//                        if (sgBPhyOutNotices != null) {
//                            //原订单的出库通知单号
//                            entryOrder.setPreDeliveryOrderCode(sgBPhyOutNotices.getBillNo());
//                            //原订单的wms单号
//                            entryOrder.setPreDeliveryOrderId(sgBPhyOutNotices.getWmsBillNo());
//                        } else {
//                            if (null != origOrderId) {
//                                entryOrder.setPreDeliveryOrderCode(origOrderId.toString());
//                            }
//                        }
//                        break;
//                    }
//                }
            } else {
                if (null != origOrderId) {
                    entryOrder.setPreDeliveryOrderCode(origOrderId.toString());
                }
            }
            //去除CP_C_LOGISTICS_ECODE的非空校验,当为空时传OTHER
            entryOrder.setLogisticsCode(StringUtils.isBlank(returnOrder.getCpCLogisticsEcode()) ? "OTHER" : returnOrder.getCpCLogisticsEcode());
            // @******** 传WMS物流单号，如果为空，传"000000"，（关于系统参数控制是否必填的已经在数据筛选的时候处理过）
            entryOrder.setExpressCode(StringUtils.isEmpty(returnOrder.getLogisticsCode()) ? OcOmsConstant.DEFAULT_TO_WMS_LOGISTICS_NO : returnOrder.getLogisticsCode());
            entryOrder.setLogisticsName(returnOrder.getCpCLogisticsEname());
            senderInfo.setName(returnOrder.getReceiveName());
            senderInfo.setMobile(returnOrder.getReceiveMobile());
            senderInfo.setProvince(returnOrder.getReceiverProvinceName());
            senderInfo.setCity(returnOrder.getReceiverCityName());
            senderInfo.setDetailAddress(returnOrder.getReceiveAddress());
            entryOrder.setShopNick(returnOrder.getCpCShopTitle());
            entryOrder.setSellerNick(returnOrder.getSellerNick());
            senderInfo.setArea(returnOrder.getReceiverAreaName());
            entryOrder.setSenderInfo(senderInfo);
            //单据类型(THRK=退货入库;HHRK=换货入库;只传英文编码)
            // @******** bill_type: 1-退货;2-换货
            if (OcReturnBillTypeEnum.RETURN.getVal().equals(returnOrder.getBillType())) {
                entryOrder.setOrderType(Return2WmsBillEnum.RETURN.val());
            } else {
                entryOrder.setOrderType(Return2WmsBillEnum.EXCHANGE.val());
            }

            //获取退货单明细
            //  List<OcBReturnOrderRefund> ocBReturnOrderRefunds = refundMapper.selectByOcOrderId(returnOrder.getId());
            List<OcBReturnOrderRefund> currentRefunds = ocBReturnOrderRefunds.stream().filter(refund -> refund.getOcBReturnOrderId().equals(returnOrder.getId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(currentRefunds)) {
                for (OcBReturnOrderRefund refund : currentRefunds) {
                    QmReturnOrderCreateModel.OrderLine orderLine = new QmReturnOrderCreateModel.OrderLine();
                    orderLine.setOwnerCode(warehouse.getOwnerCode());
                    orderLine.setItemCode(refund.getPsCSkuEcode());
                    orderLine.setItemId(getItemId(warehouse.getWmsAccount(), refund.getPsCSkuEcode()));
                    //********  取值由qtyCanRefund改为qtyRefund
                    orderLine.setPlanQty(refund.getQtyRefund().longValue());
                    // 废弃：******** 传正次品，默认正品1正0次
                    // @******** 已修改为默认传TH @******** 为了多个WMS兼容，修改为默认ZP（有部分WMS仓库不支持TH）
                    orderLine.setInventoryType(getInventoryType(refund.getProductMark()));
                    orderLine.setOrderLineNo(refund.getId() + "");  // 新加上行号
                    orderLine.setSourceOrderCode(returnOrder.getOrigSourceCode());  // 新加 原始平台单号
                    orderLines.add(orderLine);
                }
            } else {
                continue;
            }
            model.setOrderLines(orderLines);
            model.setCustomerId(warehouse.getWmsAccount());
            model.setReturnOrder(entryOrder);
            /**
             * @date 2019.11.22 cs 新增扩展字段传值
             */
            Map extendProps = new HashMap();
            extendProps.put("storageplace", warehouse.getEcode());// 入库仓编码
            extendProps.put("partsQty", returnOrder.getQtyInstore()); // 商品数量
            extendProps.put("operatorName", returnOrder.getOwnername()); // 操作人名称
            extendProps.put("EDISource", "R"); // @******** 增加属性，默认传R：用于给WMS识别是中台传的还是其他平台传的
            extendProps.put("shopName", returnOrder.getCpCShopTitle()); // @******** 增加店铺名称字段
            extendProps.put("shopCode", returnOrder.getCpCShopEcode()); // @******** 增加店铺编码字段
            model.setExtendProps(extendProps);
            models.add(model);
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("qimenEntryorderCreate定时任务传入WMS的数据是：{}"), JSON.toJSONString(models));
        }
        vh = qimenEntryorderCreateCmd.returnOrderCreate(models, "oc_oms", wmsUserCreateUtil.initWmsUser());
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("qimenEntryorderCreate定时任务返回的结果是：{}"), JSON.toJSONString(vh));
        }
        //调奇门接口失败则更新传WMS状态为 传wms失败 更新错误信息及失败次数,成功不做处理等回执处理
        List<QimenAsyncMqResult> data = vh.getData();
        data.forEach(d -> {
            //获取退单ID
            String billNo = d.getBillNo();
            OcBReturnOrder ocBReturnOrder = new OcBReturnOrder();
            if ("false".equals(d.getIsSend())) {
                //防止异常过长造成
                // @20200819 bug#21764 billNo已经不再是单据ID而是单据编码，不能直接转Long
                Long returnOrderId = getReturnOrderIdByBillNo(billNo);
                if (Objects.nonNull(returnOrderId) && returnOrderId.intValue() > 0) {
                    String message = d.getMessage();
                    if (StringUtils.isNotBlank(message) && message.length() > 200) {
                        message = message.substring(0, 200);
                    }
                    ocBReturnOrder.setIsTowms(3);
                    OcBReturnOrder ocBReturnOrderOrg = returnOrderMapper.selectByid(returnOrderId);
                    Long wmsFail = ocBReturnOrderOrg.getQtyWmsFail();
                    if (null == wmsFail) {
                        wmsFail = 1L;
                    } else {
                        wmsFail = wmsFail + 1;
                    }
                    ocBReturnOrder.setQtyWmsFail(wmsFail);
                    ocBReturnOrder.setWmsFailreason(message);
                    try {
                        ocBReturnOrder.setId(returnOrderId);
                        ocBReturnOrder.setModifieddate(new Date());
                        omsReturnOrderService.updateOcBReturnOrder(ocBReturnOrder);
                        //插入失败日志
                        omsReturnOrderService.saveAddOrderReturnLog(returnOrderId,
                                "退货单传WMS失败,原因:" + message, "零售退货单传WMS", wmsUserCreateUtil.initWmsUser());
                    } catch (IOException e) {
                        log.error(LogUtil.format("退单传wms失败，失败原因={},退单=", billNo), Throwables.getStackTraceAsString(e));
                    }
                } else {
                    log.error(LogUtil.format("returnOrderIdNotFoundByBillNo:{}", billNo));
                }
            }
        });

        return vh;
    }

    /**
     * 依据单据编码查询ID
     *
     * @param billNo
     * @return
     */
    private Long getReturnOrderIdByBillNo(String billNo) {
        return omsReturnOrderService.selectReturnOrderIdByBillNoFromEs(billNo);
    }

    /**
     * @param productMark
     * @return
     * @******** 默认传TH @******** 修改为默认传ZP
     */
    private String getInventoryType(String productMark) {
        // @******** 写死ZP
        String inventoryType = "ZP";
//        if (productMark == null || "1".equalsIgnoreCase(productMark)) {
//            inventoryType = "ZP";
//        } else {
//            inventoryType = "CC";
//        }
        return inventoryType;
    }

    private String getItemId(String wmsAccount, String psCSkuEcode) {
        List<PsToWmsRequest> list = new ArrayList<>();
        PsToWmsRequest request = new PsToWmsRequest();
        request.setCustomerId(wmsAccount);
        request.setSku(psCSkuEcode);
        list.add(request);
        HashMap<String, String> hashMap = psRpcService.getItemId(list);
        return hashMap.getOrDefault(psCSkuEcode, null);
    }

    /**
     * 天猫在线换货确认接口
     *
     * @param model
     * @return
     */
    public ValueHolderV14 tmallExchangeReturngoodsAgree(TmallExchangeReturngoodsAgreeModel model) {
        TmallExchangeReturngoodsAgreeCmd tmallExchangeReturngoodsAgreeCmd = (TmallExchangeReturngoodsAgreeCmd) ReferenceUtil.refer(
                ApplicationContextHandle.getApplicationContext(), TmallExchangeReturngoodsAgreeCmd.class.getName(),
                "ip", "1.4.0");
        return tmallExchangeReturngoodsAgreeCmd.tmallExchangeReturngoodsAgree(model);
    }


    /**
     * 订单平台发货，换货发货接口
     * 接口所需参数
     * id,tid,platform,sellerNick,shoptype,isSplit,outSid,companyCode,disputeId,
     * logisticsType,logisticsCompanyName,subTid,ordertype,paytype
     *
     * @param logisticsSendModel 发货参数sendGoodsList
     * @param plaform            平台
     * @return
     */
    public ValueHolderV14<List<LogisticsSendResult>> sendAndExchangeGoods(LogisticsSendModel logisticsSendModel, Long plaform) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("IpRpcService.sendAndExchangeGoods.inputParams={};plaform=", plaform),
                    JSON.toJSONString(logisticsSendModel));
        }
        List<LogisticsSendModel> requestList = new ArrayList<>();
        requestList.add(logisticsSendModel);

        ValueHolderV14<List<LogisticsSendResult>> result = logisticsSendCmd.sendLogistics(requestList, plaform);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("IpRpcService.sendAndExchangeGoods.outputParams={}"), JSON.toJSONString(result));
        }
        return result;
    }

    /**
     * 唯品会平台发货接口（同步 ）
     *
     * @param vipJitxOrderShipModel 发货参数sendGoodsList
     * @return
     */
    public ValueHolderV14 weiPinHuiSendGoods(VipJitxOrderShipModel vipJitxOrderShipModel) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("IpRpcService.唯品会JITX订单平台发货RPC接口请求入参={}"),
                    JSON.toJSONString(vipJitxOrderShipModel));
        }
        ValueHolderV14 vh = vipJitxOrderShipCmd.syncJitxOrderShip(vipJitxOrderShipModel);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("IpRpcService.唯品会JITX订单平台发货RPC接口请求出参={}"), JSON.toJSONString(vh));
        }
        return vh;
    }

    /**
     * 猫超平台发货（同步 ）
     *
     * @param consignOrderShipModel
     * @param sellerNick
     * @return
     */
    public ValueHolderV14 alibabaAscpShippingBack(ConsignOrderShipModel consignOrderShipModel, String sellerNick) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("猫超订单仓库发货调用云枢纽请求参数={}"), JSON.toJSONString(consignOrderShipModel));
        }
        ValueHolderV14 vh = alibabaAscpShippingBackCmd.shippingBack(consignOrderShipModel, sellerNick);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("猫超订单仓库发货调用云枢纽结果={}"), JSON.toJSONString(vh));
        }
        return vh;
    }

    /**
     * 猫超平台缺货回告
     *
     * @param consignOrderShipModel
     * @param sellerNick
     * @return
     */
    public ValueHolderV14 alibabaAscpOutOfStockCallback(ConsignOrderOutOfStockCallbackModel consignOrderShipModel, String sellerNick) {
        ValueHolderV14 vh = alibabaAscpOutOfStockCallbackCmd.outOfStockCallback(consignOrderShipModel, sellerNick);
        return vh;
    }

    /**
     * 取消订单传AG服务(订单取消)
     *
     * @param goodsModel
     */
    public boolean cancelGoodsToAg(CancelGoodsModel goodsModel) {
        log.debug(this.getClass().getName() + " 进入取消订单传AG服务");
        ValueHolderV14 holder = cancelGoodsCmd.cancelGoods(goodsModel);
        log.debug(this.getClass().getName() + " 取消Ag返回参数:{}", holder);
        if (holder == null) {
            return false;
        }
        int code = Tools.getInt(holder.getCode(), -1);
        return code == 0;


    }

    /**
     * 配送拦截服务
     *
     * @param orderCallbackModel
     * @return
     */
    public ValueHolderV14 distributionInterception(QimenOrderCallbackModel orderCallbackModel) {
        ValueHolderV14 vh = new ValueHolderV14();
        if (orderCallbackModel == null) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("传入的参数不能为空");
        } else {
            log.debug(LogUtil.format("调用配送拦截服务入参:") + JSONObject.toJSONString(orderCallbackModel));
            vh = qimenOrderCallbackCmd.orderCallback(orderCallbackModel);
            log.debug(LogUtil.format("调用配送拦截服务出参:") + JSONObject.toJSONString(orderCallbackModel));
        }
        return vh;
    }

    /**
     * 取消订单传AG服务(订单取消)
     *
     * @param goodsModel
     */
    public ValueHolderV14 cancelGoodsToAgRetry(CancelGoodsModel goodsModel) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("IpRpcService.cancelGoodsToAgRetry 进入取消订单传AG服务"));
        }
        ValueHolderV14 holder = cancelGoodsCmd.cancelGoods(goodsModel);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("IpRpcService.cancelGoodsToAgRetry 取消Ag返回参数:{}"), holder);
        }
        return holder;
    }

    /**
     * 京东物流接单接口(青龙接口)
     *
     * @param receiveRequest
     * @param sellerNick
     * @return
     */
    public ValueHolderV14 receive(ReceiveRequest receiveRequest, String sellerNick) {
        ValueHolderV14 vh = jdOrderServiceCmd.receive(receiveRequest, sellerNick);
        return vh;
    }

    /**
     * 构造ReceiveRequest对象
     *
     * @param ocBOrder 订单对象
     * @return ReceiveRequest
     */
    public ReceiveRequest bulidReceiveRequest(OcBOrder ocBOrder) {

        //查询店铺对象
        CpShop cpShop = cpRpcService.selectShopById(ocBOrder.getCpCShopId());
        ReceiveRequest receiveRequest = new ReceiveRequest();
        //销售平台 [必填]
        //receiveRequest.setSalePlat("000010");
        //商家编码 [必填]
        receiveRequest.setCustomerCode(cpShop.getQinglongCode());
        //订单号   [必填]
        receiveRequest.setOrderId(String.valueOf(ocBOrder.getTid()));
        //销售平台订单号 [必填]
        receiveRequest.setThrOrderId(String.valueOf(ocBOrder.getId()));
        //寄件人姓名 [必填]
        receiveRequest.setSenderName(cpShop.getSellerName());
        //寄件人地址 [必填]
        receiveRequest.setSenderAddress(cpShop.getSellerAddress());
        //寄件人电话 [非必填]
        receiveRequest.setSenderTel(cpShop.getSellerPhone());
        //寄件人手机(寄件人电话、手机至少有一个) [必填]
        receiveRequest.setSenderMobile(cpShop.getSellerPhone());
        //寄件人邮编 [非必填]
        receiveRequest.setSenderPostcode(null);
        //收件人名称 [必填]
        receiveRequest.setReceiveName(ocBOrder.getReceiverName());
        //收件人地址 [必填]
        receiveRequest.setReceiveAddress(ocBOrder.getReceiverAddress());
        //收件人省 [非必填]
        receiveRequest.setProvince(ocBOrder.getCpCRegionProvinceEname());
        //收件人市 [非必填]
        receiveRequest.setCity(ocBOrder.getCpCRegionCityEname());
        //收件人县 [非必填]
        receiveRequest.setCounty(ocBOrder.getCpCRegionAreaEname());
        //收件人镇 [非必填]
        receiveRequest.setTown(null);
        //收件人省编码 [非必填]
        receiveRequest.setProvinceId(null);
        //收件人市编码 [非必填]
        receiveRequest.setCityId(null);
        //收件人县编码 [非必填]
        receiveRequest.setCountyId(null);
        //收件人镇编码 [非必填]
        receiveRequest.setTownId(null);
        //站点类型 [非必填]
        receiveRequest.setSiteType(null);
        //站点编码 [非必填]
        receiveRequest.setSiteId(null);
        //站点名称 [非必填]
        receiveRequest.setSiteName(null);
        //收件人电话 [必填]
        receiveRequest.setReceiveTel(ocBOrder.getReceiverPhone());
        //收件人手机号(收件人电话、手机) [必填]
        receiveRequest.setReceiveMobile(ocBOrder.getReceiverMobile());
        //收件人邮编 [非必填]
        receiveRequest.setPostcode(ocBOrder.getReceiverZip());
        //包裹数(大于0，小于1000) [必填]
        receiveRequest.setPackageCount(1);
        //重量(单位：kg，保留小数点后两位) [必填]
        receiveRequest.setWeight(ocBOrder.getWeight().doubleValue());
        //包裹长(单位：cm,保留小数点后两位) [非必填]
        receiveRequest.setVloumLong(null);
        //包裹宽(单位：cm，保留小数点后两位) [非必填]
        receiveRequest.setVloumWidth(null);
        //包裹高(单位：cm，保留小数点后两位) [非必填]
        receiveRequest.setVloumHeight(null);
        //体积(单位：cm3，保留小数点后两位) [必填]
        receiveRequest.setVloumn(1000.00);
        //商品描述  [非必填]
        receiveRequest.setDescription(null);
        //是否代收货款(是：1，否：0。不填或者超出范围)  [非必填]
        receiveRequest.setCollectionValue(ocBOrder.getPayType());
        //代收货款金额(保留小数点后两位)  [非必填]
        receiveRequest.setCollectionMoney(ocBOrder.getCodAmt().doubleValue());
        return receiveRequest;
    }

    /**
     * Jitx寻仓反馈结果（调用陈顺云枢纽Rpc）
     *
     * @param deliveryResultModel
     */
    public ValueHolderV14<JSONArray> feedBackDelivery(VipJitxFeedBackDeliveryResultModel deliveryResultModel) {
        log.debug(this.getClass().getName() + " 调用云枢纽Jitx寻仓反馈结果服务Rpc");
        ValueHolderV14<JSONArray> holder = jitxDeliveryOrderBackCmd.feedBackDeliveryResult(deliveryResultModel);
        log.debug(this.getClass().getName() + " 调用云枢纽Jitx寻仓反馈结果返回参数:{}", holder);
        return holder;
    }

//    /**
//     * 舞象云平台退单状态回传批量
//     */
//    public ValueHolder wuxiangyunRefundCallbackPlatform(List<RPCReturnUpdStatusModel> rpcReturnUpdStatusModels) {
//        log.debug("调用舞象云平台退单状态回传RPC入参：" + rpcReturnUpdStatusModels);
//        ValueHolder valueHolder = standplatReturnUpdStatusCmd.returnUpdStatus(rpcReturnUpdStatusModels);
//        log.debug("调用舞象云平台退单状态回传RPC出参：" + valueHolder.toJSONObject());
//        return valueHolder;
//    }
//
//    /**
//     * 舞象云平台退单状态回传单条
//     */
//    public ValueHolder wuxiangyunRefundCallbackPlatform(RPCReturnUpdStatusModel rpcReturnUpdStatusModel) {
//        log.debug("调用舞象云平台退单状态回传RPC入参：" + rpcReturnUpdStatusModel);
//        ValueHolder valueHolder = standplatReturnUpdStatusCmd.returnUpdStatus(rpcReturnUpdStatusModel);
//        log.debug("调用舞象云平台退单状态回传RPC出参：" + valueHolder.toJSONObject());
//        return valueHolder;
//    }

    public ValueHolderV14 getJitxOrderByOrderSn(String orderSn, User operateUser, Long cpShopId) {
        VipJitxGetOrdersByOrderSnModel vipJitxGetOrdersByOrderSnModel = new VipJitxGetOrdersByOrderSnModel();

        vipJitxGetOrdersByOrderSnModel.setOperateUser(operateUser);
        List<String> orderSns = new ArrayList<>();
        orderSns.add(orderSn);
        vipJitxGetOrdersByOrderSnModel.setOrder_sns(orderSns);

        CpShop cpShop = cpRpcService.selectShopById(cpShopId);
        if (cpShop == null) {
            ValueHolderV14 vh = new ValueHolderV14();
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("通过店铺ID获取店铺信息失败，店铺ID：" + cpShopId);
            return vh;
        }

        vipJitxGetOrdersByOrderSnModel.setSellerNick(cpShop.getSellerNick());
        vipJitxGetOrdersByOrderSnModel.setVendorId(cpShop.getPlatformSupplierId());

        return vipJitxGetOrdersByOrderSnCmd.syncJitxsyncJitxGetOrdersByOrderSn(vipJitxGetOrdersByOrderSnModel);
    }

    /**
     * @param resendModel
     * @return com.jackrain.nea.sys.domain.ValueHolderV14
     * <AUTHOR>
     * @Description 淘宝物流二次回传
     * @Date 14:53 2020/7/6
     **/
    public ValueHolderV14 logisticsConsignResendForTaoBao(LogisticsConsignResendModel resendModel) {
        log.debug(this.getClass().getName() + " 调用云枢纽更新淘宝物流服务Rpc:{}", resendModel);
        ValueHolderV14 holder = null;
        try {
            holder = logisticsConsignCmd.resend(resendModel);
        } catch (Exception e) {
            log.error(LogUtil.format(" 调用云枢纽更新淘宝物流服务Rpc失败:{},data:{}"), Throwables.getStackTraceAsString(e),
                    JSON.toJSONString(resendModel));
        }
        log.debug(LogUtil.format("调用云枢纽更新淘宝物流服务Rpc返回参数:{}"), JSON.toJSONString(holder));
        return holder;
    }

    /**
     * 奇門O2O渠道訂單接口
     *
     * @param deliveryOrder       零售發貨單
     * @param reason              取消原因
     * @param warehouseCustomerId 實體倉庫 customerId
     * @param storeSessionKey     店鋪sessionKey
     * @param user                操作用戶
     * @return 返回操作結果
     */
    public ValueHolderV14 cancelPosDeliveryOrder(OcBOrder deliveryOrder, Integer reason, String warehouseCustomerId, String storeSessionKey, User user) {
        QimenPosSaleOrderUpdateRequest qimenPosSaleOrderUpdateRequest = new QimenPosSaleOrderUpdateRequest();
        qimenPosSaleOrderUpdateRequest.setCanceler(user.getName());
        qimenPosSaleOrderUpdateRequest.setCancleDate(DateUtil.getDateTime());
        qimenPosSaleOrderUpdateRequest.setStatus(QiMenOrderStatusEnum.VOID.getCode().toString());
        qimenPosSaleOrderUpdateRequest.setConfirm("1");
        qimenPosSaleOrderUpdateRequest.setConfirmor(user.getName());
        qimenPosSaleOrderUpdateRequest.setConfirmDate(DateUtil.getDateTime());
        qimenPosSaleOrderUpdateRequest.setFinish("finish");
        qimenPosSaleOrderUpdateRequest.setFinishDate(DateUtil.getDateTime());
        qimenPosSaleOrderUpdateRequest.setOrderBillCode(deliveryOrder.getBillNo());
        qimenPosSaleOrderUpdateRequest.setShippingCode(deliveryOrder.getCpCLogisticsEcode());
        qimenPosSaleOrderUpdateRequest.setShippingSn(deliveryOrder.getExpresscode());
        qimenPosSaleOrderUpdateRequest.setZfMessage(deliveryOrder.getSellerMemo());
        qimenPosSaleOrderUpdateRequest.setZfType(reason.toString());
        qimenPosSaleOrderUpdateRequest.setCustomerid(warehouseCustomerId);
        return qimenPosSaleOrderUpdateCmd.saleOrderUpdate(qimenPosSaleOrderUpdateRequest, storeSessionKey, user);
    }

    /**
     * 猫超销售退货入库回传接口
     *
     * @param model      请求参数
     * @param sellerNick 卖家姓名
     * @return
     */
    public ValueHolderV14 inStorageFeedback(InStorageFeedbackModel model, String sellerNick) {
        return ascpInStorageFeedbackCmd.inStorageFeedback(model, sellerNick);
    }

    /**
     * 修改仓库状态 ?????
     *
     * @param statusRequest request
     * @param sellerNick    卖家姓名
     * @return
     */
    public ValueHolderV14 updateWarehouseStatus(RefundapplyUpdateWarehouseStatusRequest statusRequest, String sellerNick) {
        return jdRefundServiceCmd.updateWarehouseStatus(statusRequest, sellerNick);
    }


    /**
     * b2 执行退单 传 sap
     *
     * @param sapItems
     * @param user
     * @return
     */

    /**
     * 猫超销售订单取消发货回告接口
     *
     * @param model      model
     * @param sellerNick 卖家姓名
     * @return
     */
    public ValueHolderV14 orderCancelFeedback(ConsignOrderCancelFeedbackModel model, String sellerNick) {
        return orderCancelFeedbackCmd.orderCancelFeedback(model, sellerNick);
    }

    /**
     * ？
     *
     * @param request 请求参数
     * @return
     */
    public ValueHolderV14<List<VipJitxGetChangeWarehouseWorkflowsResult>> getChangeWarehouseWorkflows(
            VipJitxGetChangeWarehouseWorkflowsRequest request) {
        ValueHolderV14<List<VipJitxGetChangeWarehouseWorkflowsResult>> changeWarehouseWorkflows = new ValueHolderV14<>();
        try {
            if (log.isDebugEnabled()) {
                log.debug("调用获取jitx订单改仓接口入参：param={}", JSON.toJSONString(request));
            } else {
                log.info("调用获取jitx订单改仓接口入参：param={}", JSON.toJSONString(request));
            }
            changeWarehouseWorkflows = vipJitxGetChangeWarehouseWorkflowsCmd.getChangeWarehouseWorkflows(request);
        } catch (Exception e) {
            log.error("调用获取jitx订单改仓接口异常：{}", Throwables.getStackTraceAsString(e));
            changeWarehouseWorkflows.setCode(ResultCode.FAIL);
            changeWarehouseWorkflows.setMessage(e.getMessage());
        }
        if (log.isDebugEnabled()) {
            log.debug("调用获取jitx订单改仓接口返回参数：param={}", JSON.toJSONString(changeWarehouseWorkflows));
        } else {
            log.info("调用获取jitx订单改仓接口返回参数：param={}", JSON.toJSONString(changeWarehouseWorkflows));
        }
        return changeWarehouseWorkflows;
    }


    /**
     * 京东拆单请求接口
     *
     * @param orderId    订单id
     * @param param      参数
     * @param sellerNick 卖家姓名
     * @return
     */
    public ValueHolderV14<String> requestJdSplit(Long orderId, String param, String sellerNick) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("OmsOrderJdSplitService调用京东平台拆单接口,接口参数param={},orderId/sellerNick：", orderId,
                    sellerNick), param);
        }
        ValueHolderV14<String> retVh = jdOrderServiceCmd.orderSpit(param, sellerNick);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("OmsOrderJdSplitService调用京东平台拆单接口,返回参数param={}，orderId/sellerNick：", orderId,
                    sellerNick), JSON.toJSONString(retVh));
        }
        return retVh;
    }

    /**
     * 调用千牛锁单接口
     *
     * @param model model
     * @return
     */
    public ValueHolder intercaptOrderCallback(QianNiuInterceptOrderCallbackModel model) {
        return qianNiuInterceptOrderCallbackCmd.intercaptOrderCallback(model);
    }

    /**
     * 调用AG入仓服务入参
     *
     * @param returnId  退单id
     * @param cpCShopId 店铺id
     * @param loginUser 操作人
     * @return
     */
    public boolean updateLogisticsWarehouse(String returnId, Long cpCShopId, User loginUser) {
        LogisticsWarehouseUpdateModel model = new LogisticsWarehouseUpdateModel();
        model.setOperateUser(loginUser);
        model.setSessionKey(cpRpcService.getSessionKey(cpCShopId));
        model.setRefundId(Long.valueOf(returnId));
        //固定值1
        model.setWarehouseStatus(1L);
        try {
            log.debug(this.getClass().getSimpleName() + "调用AG入仓服务入参=" + model);

            ValueHolderV14 valueHolderV14 = logisticsWarehouseUpdateCmd.updateLogisticsWarehouse(model);
            if (!valueHolderV14.isOK()) {
                return false;
            }
            log.debug(this.getClass().getSimpleName() + "调用AG入仓服务出参=" + valueHolderV14);

        } catch (Exception ex) {
            log.debug(this.getClass().getSimpleName() + "调用AG入仓服务异常=" + ex);
            return false;
        }
        return true;
    }

    public ValueHolderV14<List<VipJitxCreateChangeWarehouseWorkflowResult>> createChangeWarehouseWorkflow(
            VipJitxCreateChangeWarehouseWorkflowRequest vipRequest) {
        return vipJitxCreateChangeWarehouseWorkflowCmd.createChangeWarehouseWorkflow(vipRequest);
    }

    /**
     * 京东调用SA拆包登记服务
     *
     * @param request    request
     * @param sellerNick 卖家姓名
     * @return
     */
    public ValueHolderV14 receiveRegister(ReceiveRegisterRequest request, String sellerNick) {
        return jdRefundServiceCmd.receiveRegister(request, sellerNick);
    }

    /**
     * 同步链路日志平台接口
     *
     * @param modelList
     * @param user
     * @return
     */
    public ValueHolderV14<List<OrderFullLinkResult>> orderFullLink(List<OrderFullLinkModel> modelList, User user) {
        return orderFullLinkCmd.orderFullLink(modelList, user);
    }

    /**
     * 查询奇门订单状态
     *
     * @param request
     * @param loginUser
     * @return
     */
    public ValueHolderV14<QimenPosOrderStatusQueryResult> orderStatusQuery(QimenPosOrderStatusQueryExtRequest request, User loginUser) {
        return qimenPosOrderStatusQueryCmd.orderStatusQuery(request, loginUser);
    }

    /**
     * 淘宝修改地址
     *
     * @param model
     * @return
     */
    public ValueHolderV14 updateAddr(ModifyOrderAddrModel model) {
        return modifyOrderAddrCmd.updateAddr(model);
    }

    /**
     * 通用修改地址推送平台
     *
     * @param request
     * @return
     */
    public ValueHolderV14 standModifyAddrPushPlatform(ModifyAddressRequest request) {
        return ipBStandModifyAddrCmd.pushPlatform(request);
    }

    /**
     * 天猫换货-同意换货接口
     *
     * @param model model
     * @return
     */
    public ValueHolderV14 agree(ExchangeAgreeModel model) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("oms同意换货入参") + JSON.toJSONString(model));
        }
        ValueHolderV14 agree = exchangeCmd.agree(model);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("oms同意换货出参") + JSON.toJSONString(agree));
        }
        return agree;
    }

    /**
     * 天猫换货-拒绝换货
     *
     * @param model model
     * @return
     */
    public ValueHolderV14 refuse(ExchangeRefuseModel model) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("天猫换货-拒绝换货入参") + JSON.toJSONString(model));
        }
        ValueHolderV14 refuse = exchangeCmd.refuse(model);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("天猫换货-拒绝换货出参") + JSON.toJSONString(refuse));
        }
        return refuse;
    }

    /**
     * 订单信息解密
     *
     * @param order 订单
     */
    public void decrypt(OcBOrder order) {
        if (ObjectUtils.isEmpty(order)) {
            return;
        }
        /*地址是明文，无需调用解密接口*/
        if (OcBorderListEnums.YesOrNoEnum.IS_YES.getVal().equals(order.getIsPlainAddr())) {
            return;
        }

        OrderDecryptRequest request = new OrderDecryptRequest();
        List<Long> ids = new ArrayList<>();
        ids.add(Long.valueOf(order.getPlatform()));
        List<CpCPlatform> platformList = cpRpcService.queryCpCPlatformByIds(ids);
        AssertUtil.assertException(CollectionUtils.isEmpty(platformList), "未找到平台==" + order.getPlatform());

        Integer platform = Integer.valueOf(platformList.get(0).getEcode());
        request.setPlatform(platform);
        request.setSellerNick(order.getCpCShopSellerNick());
        List<OrderDecryptRequest.OrderDecryptItemRequest> itemRequests = new ArrayList<>();

        //根据平台拼接加密参数
        // 淘宝传入oaid；
        //抖音、有赞、京东传入原始密文信息；
        for (OrderDecryptDataType dataType : OrderDecryptDataType.values()) {
            OrderDecryptRequest.OrderDecryptItemRequest itemRequest = new OrderDecryptRequest.OrderDecryptItemRequest();
            itemRequest.setDataType(dataType.getCode());
            itemRequest.setTid(order.getTid());
            if (PlatFormEnum.TAOBAO.getCode().equals(platform)) {
                if (order.getOaid() == null || "".equals(order.getOaid())) {
                    return;
                }
//                淘宝换货单的解密，需要淘宝换货单号+oaid获取
                if(OrderTypeEnum.EXCHANGE.getVal().equals(order.getOrderType()) && order.getDisputeId() != null){
                    itemRequest.setTid(order.getDisputeId()+"");
                }
                itemRequest.setEncryptedData(order.getOaid());
            } else {
                switch (dataType) {
                    case NAME:
                        itemRequest.setEncryptedData(order.getReceiverName());
                        break;
                    case MOBILE:
                        itemRequest.setEncryptedData(order.getReceiverMobile());
                        break;
                    case PHONE:
                        itemRequest.setEncryptedData(order.getReceiverPhone());
                        break;
                    case ADDRESS_DETAIL:
                        itemRequest.setEncryptedData(order.getReceiverAddress());
                        break;
                    default:
                        break;
                }
            }
            itemRequests.add(itemRequest);
        }

        request.setQueryList(itemRequests);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("解密订单数据===order：{}，入参：{}"), JSON.toJSONString(order), JSON.toJSONString(request));
        }
        //解密
        OrderDecryptResponse response = orderDecryptCmd.orderDecrypt(request);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("解密订单数据结果==={}"), JSON.toJSONString(response));
        }
        //转换字段
        if (!ObjectUtils.isEmpty(response) && !ObjectUtils.isEmpty(response.getCode()) &&
                response.getCode().equals(0) && CollectionUtils.isNotEmpty(response.getData())) {
            Map<String, String> resultMap = response.getData().stream().collect(Collectors
                    .toMap(OrderDecryptResponse.OrderDecryptItemResponse::getDataType,
                            item -> Optional.ofNullable(item.getDecryptData()).orElse("")));
            for (OrderDecryptDataType dataType : OrderDecryptDataType.values()) {
                String value = resultMap.get(dataType.getCode());
                if (StringUtils.isEmpty(value)) {
                    continue;
                }
                switch (dataType) {
                    case NAME:
                        order.setReceiverName(value);
                        break;
                    case MOBILE:
                        order.setReceiverMobile(value);
                        break;
                    case PHONE:
                        order.setReceiverPhone(value);
                        break;
                    case ADDRESS_DETAIL:
                        order.setReceiverAddress(value);
                        break;
                    default:
                        break;
                }
            }
        }

    }

    /**
     * description:oms调用wing发货接口
     *
     * @Author: liuwenjin
     * @Date 2021/9/29 12:36 上午
     */
    public ValueHolderV14<WingReturnResult> wingDeliveryOrder(List<WingDeliveryModel> wingDeliveryModelList) {
        if (log.isDebugEnabled()) {
            log.debug("IpRpcService.wingDeliveryOrder.oms调用wing发货接口.入参为:{}", wingDeliveryModelList);
        }
        ValueHolderV14<WingReturnResult> valueHolderV14 = null;
        valueHolderV14 = omsToWingCmd.wingDeliveryOrder(wingDeliveryModelList);
        if (log.isDebugEnabled()) {
            log.debug("IpRpcService.wingDeliveryOrder.oms调用wing发货接口.出参为:{}", JSON.toJSONString(valueHolderV14));
        }
        return valueHolderV14;
    }

    /**
     * description:换货调用wing hold单
     *
     * @Author: liuwenjin
     * @Date 2021/10/20 4:39 下午
     */
    public ValueHolderV14<String> orderExchangeHoldTowing(List<WingOutStoOutNoticesRequest> list) {
        if (log.isDebugEnabled()) {
            log.debug("IpRpcService.orderExchangeHoldTowing.oms调用winghold单.入参为:{}", list);
        }
        ValueHolderV14<String> valueHolderV14 = null;
        valueHolderV14 = omsToWingCmd.orderExchangeHoldTowing(list);
        if (log.isDebugEnabled()) {
            log.debug("IpRpcService.orderExchangeHoldTowing.oms调用winghold单.出参为:{}", JSON.toJSONString(valueHolderV14));
        }
        return valueHolderV14;
    }

    /**
     * description:换货调用wing 取消hold单
     *
     * @Author: liuwenjin
     * @Date 2021/10/20 4:39 下午
     */
    public ValueHolderV14<String> orderToWingExchangeUnHold(List<String> stringList) {
        if (log.isDebugEnabled()) {
            log.debug("IpRpcService.orderToWingExchangeUnHold.换货调用wing 取消hold单.入参为:{}", stringList);
        }
        ValueHolderV14<String> valueHolderV14 = null;
        valueHolderV14 = omsToWingCmd.orderToWingExchangeUnHold(stringList);
        if (log.isDebugEnabled()) {
            log.debug("IpRpcService.orderToWingExchangeUnHold.换货调用wing 取消hold单.出参为:{}", JSON.toJSONString(valueHolderV14));
        }
        return valueHolderV14;
    }

    /**
     * description:查询译氪优惠券的接口
     *
     * @Author: liuwenjin
     * @Date 2021/12/6 4:13 下午
     */
    public YiKeCouponModel queryYiKeCoupon(String couponId) {
        if (log.isDebugEnabled()) {
            log.debug("IpRpcService.queryYiKeCoupon.换货调用ip译氪查询优惠券.入参为:{}", couponId);
        }
        YiKeCouponModel yiKeCouponModel = queryYiKeCmd.queryYiKeCoupon(couponId);
        if (log.isDebugEnabled()) {
            log.debug("IpRpcService.queryYiKeCoupon.换货调用ip译氪查询优惠券.出参为:{}", yiKeCouponModel);
        }
        return yiKeCouponModel;
    }

    /**
     * oms 一件代发缺货反馈调用wing
     *
     * @param model
     * @return
     */
    public ValueHolderV14 wingInvalidOrder(WingInvalidOrderModel model) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("IpRpcService.wingInvalidOrder.Request:{}",
                    "IpRpcService.wingInvalidOrder",
                    model.getOrder_Sn()), JSON.toJSONString(model));
        }
        ValueHolderV14 v14 = omsToWingCmd.wingInvalidOrder(model);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("IpRpcService.wingInvalidOrder.Response:{}",
                    "IpRpcService.wingInvalidOrder",
                    model.getOrder_Sn()), JSON.toJSONString(v14));
        }
        return v14;
    }

    public ValueHolderV14 returnOrderDelivery(List<SapOrderBackRequest> request){
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("IpRpcService.returnOrderDelivery.Request:{}",
                    "IpRpcService.returnOrderDelivery"), JSON.toJSONString(request));
        }
        ValueHolderV14 v14 = sapCloseBackCmd.return2BOrder(request);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("IpRpcService.returnOrderDelivery.Response:{}",
                    "IpRpcService.returnOrderDelivery"), JSON.toJSONString(v14));
        }
        return v14;
    }

    public ValueHolderV14 delivery2Dms(Delivery2DmsRequest delivery2DmsRequest) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("IpRpcService.delivery2Dms.Request:{}",
                    "IpRpcService.delivery2Dms"), JSON.toJSONString(delivery2DmsRequest));
        }
        ValueHolderV14 valueHolderV14 = delivery2DmsCmd.delivery2Dms(delivery2DmsRequest);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("IpRpcService.delivery2Dms.Response:{}",
                    "IpRpcService.delivery2Dms"), JSON.toJSONString(valueHolderV14));
        }
        return valueHolderV14;
    }

    /**
     * sap ag
     *
     * @param request request
     * @return v14
     */
    public ValueHolderV14 sapAg(SapOrderBackRequest request) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("IpRpcService.sapAg.Request:{}",
                    "IpRpcService.sapAg"), JSON.toJSONString(request));
        }
        List<SapOrderBackRequest> requestList = new ArrayList<>();
        requestList.add(request);
        ValueHolderV14 v14 = sapCloseBackCmd.return2BOrder(requestList);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("IpRpcService.sapAg.Response:{}",
                    "IpRpcService.sapAg"), JSON.toJSONString(v14));
        }
        return v14;
    }


    public ValueHolderV14 dmsAg(DmsOrderBackRequest request) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("IpRpcService.dmsAg.Request:{}",
                    "IpRpcService.dmsAg"), JSON.toJSONString(request));
        }
        ValueHolderV14 v14 = dmsCloseBackCmd.return2BOrder(request);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("IpRpcService.dmsAg.Response:{}",
                    "IpRpcService.dmsAg"), JSON.toJSONString(v14));
        }

        return v14;
    }

    public ValueHolderV14 tbLogisticsSendBySplitLine(TbLogisticsSendBySplitLineModel tbLogisticsSendBySplitLineModel) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("IpRpcService.tbLogisticsSendBySplitLine.inputParams={}", "IpRpcService.tbLogisticsSendBySplitLine"),
                    JSON.toJSONString(tbLogisticsSendBySplitLineModel));
        }
        ValueHolderV14 result = logisticsSendBySplitLineCmd.logisticsSendBySplitLine(tbLogisticsSendBySplitLineModel);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("IpRpcService.tbLogisticsSendBySplitLine.outputParams={}", "IpRpcService.tbLogisticsSendBySplitLine"), JSON.toJSONString(result));
        }
        return result;
    }
    public ValueHolderV14<QueryTrackResp> queryLogisticsInfoFrom100(String com, String num, String phone) {

        try {
            log.info("{},queryLogisticsInfoFrom100Cmd.queryLogisticsInfo 请求参数:com={} num={} phone={}", this.getClass().getSimpleName(), com, num, phone);
            ValueHolderV14<QueryTrackResp> v14 = queryLogisticsInfoFrom100Cmd.queryLogisticsInfo(com, num, phone);
            log.info("{},queryLogisticsInfoFrom100Cmd.queryLogisticsInfo 请求结果:{}", this.getClass().getSimpleName(), JSON.toJSONString(v14));
            return v14;
        } catch (Exception e) {
            log.error("IpRpcService.queryLogisticsInfoFrom100 error", e);
            return ValueHolderV14Utils.getFailValueHolder(String.format("请求接口异常:%s", SplitMessageUtil.splitMsgBySize(e.getMessage(), SplitMessageUtil.SIZE_999)));
        }
    }

    public ValueHolderV14<AddressAnalyseResp> addressAnalyse(String address){
        try {
            log.info(LogUtil.format("addressAnalyzeFrom100Cmd.addressAnalyse.address={}", "ddressAnalyzeFrom100Cmd.addressAnalyse"),
                    address);
            ValueHolderV14<AddressAnalyseResp> valueHolderV14 = addressAnalyzeFrom100Cmd.addressAnalyse(address);
            log.info(LogUtil.format("addressAnalyzeFrom100Cmd.addressAnalyse.address={}", "addressAnalyzeFrom100Cmd.addressAnalyse"),
                    JSON.toJSONString(valueHolderV14));
            return valueHolderV14;
        }catch (Exception e){
            log.error("IpRpcService.addressAnalyse error", e);
            return ValueHolderV14Utils.getFailValueHolder(String.format("请求接口异常:%s", SplitMessageUtil.splitMsgBySize(e.getMessage(), SplitMessageUtil.SIZE_999)));
        }
    }

    /**
     * 京东厂直平台发货RPC调用
     *
     * @param requestList
     * @return
     */
    public ValueHolderV14 jingdongDirectDelivery(List<JdDirectLogisticsSendRequest> requestList) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("京东厂直平台发货调用入参>>>{}", "京东厂直平台发货"), JSON.toJSONString(requestList));
        }
        ValueHolderV14 vh = jdDirectLogisticsSendCmd.sendJdDirectLogistics(requestList);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("京东厂直平台发货发货RPC接口调用返回结果>>>{}", "京东厂直平台发货RPC接口调用返回结果"), JSON.toJSONString(vh));
        }
        return vh;
    }

    /**
     * 京东供销解密收货人信息
     *
     * @param jdDecryptRequest 请求数据
     * @return ValueHolderV14
     */
    public  ValueHolderV14<List<JdDecryptResponse>> jdGxDecrypt(JdDecryptRequest jdDecryptRequest) {
        log.info(LogUtil.format("jdDecryptRequest={}", "IpRpcService.jdGxDecrypt"),
                JSONObject.toJSONString(jdDecryptRequest));
        ValueHolderV14<List<JdDecryptResponse>> sensitiveData =
                jdHufuOrderGetSensitiveDataCmd.getSensitiveData(jdDecryptRequest);
        log.info(LogUtil.format("sensitiveData={}", "IpRpcService.jdGxDecrypt"),
                JSONObject.toJSONString(sensitiveData));
        return sensitiveData;
    }

    /**
     * 订单备注同步
     *
     * @param model
     * @return
     */
    public ValueHolderV14 orderRemarkSync(OrderRemarkSyncModel model) {
        ValueHolderV14 vh = orderRemarkSyncCmd.remarkSync(model);
        log.info(LogUtil.format("订单备注同步平台,model:{},vh:{}", "orderRemarkSync"), JSON.toJSONString(model), JSON.toJSONString(vh));
        return vh;
    }

    /**
     * 下载通用商品
     *
     * @param shopId
     * @param startTime
     * @param endTime
     * @return
     */
    public ValueHolder downloadPorduucts(Long shopId, String startTime, String endTime) {
        ValueHolder valueHolder = productDownloadCmd.downloadPlatProducts(shopId, startTime, endTime);
        log.info(LogUtil.format("下载通用商品,shopId:{},startTime:{},endTime:{},result:{}", "downloadPorduucts"), shopId, startTime, endTime, valueHolder);
        return valueHolder;
    }

    /**
     * 多包裹云枢纽
     *
     * @param model
     * @return
     */
    public ValueHolderV14 deliveryParcel(OrderDeliveryParcelbackModel model) {
        ValueHolderV14 vh = orderDeliveryParcelbackCmd.deliveryParcelback(model);
        log.info(LogUtil.format("云枢纽多包裹发货,model:{},vh:{}", "deliveryParcelCloud"), JSON.toJSONString(model), JSON.toJSONString(vh));
        return vh;
    }

    /**
     * content字段为map接口 key value都是string 类型
     *
     * @param content
     * @param projectId
     * @param appId
     * @param userId
     * @return
     */
    public ValueHolderV14 pushTask(Map<String, String> content, String projectId, String appId, String userId) {
        ValueHolderV14 result = bnCmd.pushTask(content, projectId, appId, userId);
        return result;
    }

    public ValueHolderV14 uploadFile(String url, String name, String type) {
        ValueHolderV14 result = bnCmd.uploadFile(url, name, type);
        return result;
    }

    public ValueHolderV14 batchPushTask(List<Map<String, String>> contents, String projectId, String appId, String userId) {
        ValueHolderV14 valueHolderV14 = bnCmd.batchPushTask(contents, projectId, appId, userId);
        return valueHolderV14;
    }

    public ValueHolderV14<BnTaskListQueryResponse> queryTaskList(BnTaskListQueryRequest request){
        ValueHolderV14 valueHolderV14 = bnCmd.queryTaskList(request);
        return valueHolderV14;
    }

    public ValueHolderV14<Void> queryRedeliverTransportLogisticsNo(VipJitxRedeliverTransportNoVopRequest request) {
        return vipJitxRedeliverTransportNoVopCmd.queryRedeliverTransportLogisticsNo(request);
    }
}