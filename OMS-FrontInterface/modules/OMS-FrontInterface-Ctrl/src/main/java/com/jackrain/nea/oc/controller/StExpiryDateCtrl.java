package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.model.enums.AppointDimensionEnum;
import com.jackrain.nea.oc.oms.model.enums.AppointTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.ExpiryDateTypeEnum;
import com.jackrain.nea.oc.oms.vo.StCExpiryDateImpVo;
import com.jackrain.nea.oc.services.st.StExpiryDateImportCmdImpl;
import com.jackrain.nea.oc.services.st.StExpiryDateUnAuditCmdImpl;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.async.AsyncTaskBody;
import com.jackrain.nea.web.common.AsyncTaskManager;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.InputStream;
import java.text.NumberFormat;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

/**
 * @program: r3-oc-oms
 * @description: 效期策略ctrl
 * @author: caomalai
 * @create: 2022-08-11 10:02
 **/
@RestController
@Slf4j
@Api(value = "StExpiryDateCtrl", description = "效期策略")
public class StExpiryDateCtrl {
    public static final String cellStr = "cell_";
    public static final String rowStr = "row_";
    public static final int NUMERIC = 0;
    public static final int FORMULA = 2;
    private static NumberFormat nf = NumberFormat.getInstance();

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;
    @Autowired
    private AsyncTaskManager asyncTaskManager;
    @Autowired
    private StExpiryDateImportCmdImpl stExpiryDateImportCmd;
    @Autowired
    private StExpiryDateUnAuditCmdImpl stExpiryDateUnAuditCmd;
    @Autowired
    private ThreadPoolTaskExecutor commonTaskExecutor;

    @ApiOperation(value = "反审核并修改")
    @RequestMapping(path = "/api/cs/oc/oms/stExpiryDateStrategy/backAndModify", method = RequestMethod.POST)
    public ValueHolderV14 backAndModify(HttpServletRequest request, @RequestBody JSONObject param) {
        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        ValueHolderV14 vh = stExpiryDateUnAuditCmd.unAuditNotClearRedis(param, user);
        return vh;
    }

    @ApiOperation(value = "获取导入模板地址")
    @RequestMapping(path = "/api/cs/oc/oms/stExpiryDateStrategy/template/url", method = RequestMethod.GET)
    public ValueHolderV14 queryImportTemplateUrl(){
        ValueHolderV14 vh = stExpiryDateImportCmd.queryTemplateDownloadUrl();
        return vh;
    }

    @ApiOperation(value = "导入")
    @RequestMapping(value = "/api/cs/oc/oms/stExpiryDateStrategy/import", method = RequestMethod.POST)
    public ValueHolderV14 selectLogData(HttpServletRequest request, @RequestParam(value = "file", required = true) MultipartFile file) {
        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        //插入我的任务里
        AsyncTaskBody asyncTaskBody =new AsyncTaskBody();
        asyncTaskBody.setTaskId(UUID.randomUUID().toString());
        asyncTaskBody.setMenu("商品效期策略头明细导入");
        asyncTaskBody.setTaskType("导入");
        asyncTaskManager.beforeExecute(user, asyncTaskBody);
        return asyncImport(asyncTaskBody,user,file);
    }

    public ValueHolderV14 asyncImport(AsyncTaskBody asyncTaskBody,User user,MultipartFile file){
        ValueHolderV14 holderV14 = new ValueHolderV14();
        Map<Object, Object> retMap = new LinkedHashMap<>();
        commonTaskExecutor.submit(() -> {
            try {
                if (file == null) {
                    throw new NDSException("导入文件不能为空!");
                }
                InputStream inputStream = file.getInputStream();
                Workbook hssfWorkbook = WorkbookFactory.create(inputStream);
                if (hssfWorkbook.getNumberOfSheets() != 1) {
                    throw new NDSException("导入模板不正确");
                }
                List<StCExpiryDateImpVo> dataImpVos = getImportDataList(hssfWorkbook);
                if (CollectionUtils.isEmpty(dataImpVos)) {
                    throw new NDSException("导入数据不能为空!");
                }
                if (dataImpVos.size() > 2000) {
                    throw new NDSException("导入条数请勿超过2000！");
                }

                ValueHolderV14 valueHolder = stExpiryDateImportCmd.importDataList(dataImpVos, user);

                retMap.put("code", valueHolder.getCode());
                retMap.put("data", valueHolder.getData());
                retMap.put("path", valueHolder.getData());
                retMap.put("message", valueHolder.getMessage());
                //任务完成
                asyncTaskBody.setUrl(String.valueOf(valueHolder.getData()));
                asyncTaskBody.setTaskType("导入");
                asyncTaskManager.afterExecute(user, asyncTaskBody, JSON.parseObject(JSON.toJSONString(retMap)));
            } catch (Exception ex) {
                log.error(LogUtil.format("头明细导入失败","商品效期策略"),ex);
                retMap.put("code", ResultCode.FAIL);
                retMap.put("message", "导入异常：" + ex.getMessage());
                asyncTaskManager.afterExecute(user, asyncTaskBody, JSON.parseObject(JSON.toJSONString(retMap)));
            }
        });
        holderV14.setCode(ResultCode.SUCCESS);
        holderV14.setData(asyncTaskBody.getId());
        holderV14.setMessage(Resources.getMessage("商品效期策略头明细导入任务开始！详情请在我的任务查看"));
        return holderV14;
    }


    /**
     * 获取主it 表sheet数据，转换成主表对象
     */
    public List<StCExpiryDateImpVo> getImportDataList(Workbook hssfWorkbook) {
        List<StCExpiryDateImpVo> dataImpVoList = Lists.newArrayList();
        List<Map<String, String>> execlList = Lists.newArrayList();

        try {
            execlList = readExcel(0, hssfWorkbook);
        } catch (Exception e) {

        }
        int index = 0;
        // 数据缺失校验
        StringBuilder checkMessage = new StringBuilder();
        for (Map<String, String> columnMap : execlList) {
            if (index == 0) {
                // 校验excel字段
                if (columnMap.size() != 11
                        || !"类型".equals(columnMap.get(rowStr + index + cellStr + 0))
                        || !"平台店铺".equals(columnMap.get(rowStr + index + cellStr + 1))
                        || !"开始时间".equals(columnMap.get(rowStr + index + cellStr + 2))
                        || !"结束时间".equals(columnMap.get(rowStr + index + cellStr + 3))
                        || !"指定维度".equals(columnMap.get(rowStr + index + cellStr + 4))
                        || !"指定内容".equals(columnMap.get(rowStr + index + cellStr + 5))
                        || !"指定类型".equals(columnMap.get(rowStr + index + cellStr + 6))
                        || !"开始生产日期/天数".equals(columnMap.get(rowStr + index + cellStr + 7))
                        || !"结束生产日期/天数".equals(columnMap.get(rowStr + index + cellStr + 8))
                        || !"订单标签".equals(columnMap.get(rowStr + index + cellStr + 9))
                        || !"优先最便宜快递".equals(columnMap.get(rowStr + index + cellStr + 10))
                ) {

                    return Lists.newArrayList();
                }
            } else {
                if (StringUtils.isNotEmpty(columnMap.get(rowStr + index + cellStr + 0))) {
                    StCExpiryDateImpVo dataImpVo = StCExpiryDateImpVo.importCreate(index, columnMap);
                    //转换枚举值
                    if(StringUtils.isNotBlank(dataImpVo.getExpiryTypeName())){
                        ExpiryDateTypeEnum dateTypeEnum = ExpiryDateTypeEnum.getByDesc(dataImpVo.getExpiryTypeName());
                        if(Objects.nonNull(dateTypeEnum)){
                            dataImpVo.setExpiryType(dateTypeEnum.getKey());
                        }else{
                            checkMessage.append("[填写的类型有误]");
                        }
                    }
                    if(StringUtils.isNotBlank(dataImpVo.getAppointDimensionName())){
                        AppointDimensionEnum appointDimensionEnum = AppointDimensionEnum.getByDesc(dataImpVo.getAppointDimensionName());
                        if(Objects.nonNull(appointDimensionEnum)){
                            dataImpVo.setAppointDimension(appointDimensionEnum.getKey());
                        }else{
                            checkMessage.append("[填写的指定维度有误]");
                        }
                    }
                    if(StringUtils.isNotBlank(dataImpVo.getAppointTypeName())){
                        AppointTypeEnum appointTypeEnum = AppointTypeEnum.getByDesc(dataImpVo.getAppointTypeName());
                        if(Objects.nonNull(appointTypeEnum)){
                            dataImpVo.setAppointType(appointTypeEnum.getKey());
                        }else{
                            checkMessage.append("[填写的指定类型有误]");
                        }
                    }
                    // cheapestExpress 如果不为空 则必须为 "是"或"否"
                    if (StringUtils.isNotBlank(dataImpVo.getCheapestExpress())) {
                        if (!"是".equals(dataImpVo.getCheapestExpress()) && !"否".equals(dataImpVo.getCheapestExpress())) {
                            checkMessage.append("[填写的优先最便宜快递有误]");
                        }
                    }
                    if (StringUtils.isNotEmpty(checkMessage)) {
                        if(StringUtils.isNotBlank(dataImpVo.getDesc())){
                            dataImpVo.setDesc(dataImpVo.getDesc()+checkMessage.toString());
                        }else{
                            dataImpVo.setDesc(checkMessage.toString());
                        }
                        checkMessage.setLength(0);
                    }
                    dataImpVoList.add(dataImpVo);
                }
            }
            index++;
        }
        return dataImpVoList;
    }


    /**
     * 导入方法，
     *
     * @param hssfWorkbook
     * @return
     * @throws Exception
     */
    public List<Map<String, String>> readExcel(Integer sheetIndex, Workbook hssfWorkbook) throws Exception {
        List list = Lists.newArrayList();
        Sheet hssfSheet = hssfWorkbook.getSheetAt(sheetIndex);
        if (Objects.isNull(hssfSheet)) {
            return Lists.newArrayList();
        }

        for (int rowNum = 0; rowNum <= hssfSheet.getLastRowNum(); rowNum++) {
            Map<String, String> map = Maps.newTreeMap();
            Row hssfRow = hssfSheet.getRow(rowNum);
            if (hssfRow != null) {
                Iterator<Cell> cellItr = hssfRow.cellIterator();
                while (cellItr.hasNext()) {
                    Cell cell = cellItr.next();
                    map.put(rowStr + cell.getRowIndex() + cellStr + cell.getColumnIndex(), getCellValue(cell));
                }
            }
            list.add(map);
        }
        return list;
    }

    private static String getCellValue(Cell cell) {
        int cellType = cell.getCellType();
        String cellValue;
        switch (cellType) {
            case NUMERIC:
                cellValue = nf.format(cell.getNumericCellValue());
                if (cellValue.indexOf(",") >= 0) {
                    cellValue = cellValue.replace(",", "");
                }
                break;
            case FORMULA:
                try {
                    cellValue = cell.getStringCellValue();
                } catch (IllegalStateException e) {
                    cellValue = String.valueOf(cell.getNumericCellValue());
                }
                break;

            default:
                cellValue = cell.getStringCellValue();
        }
        return cellValue.trim();
    }
}
