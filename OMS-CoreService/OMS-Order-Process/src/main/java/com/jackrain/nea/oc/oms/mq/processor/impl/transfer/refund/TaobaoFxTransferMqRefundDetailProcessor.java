package com.jackrain.nea.oc.oms.mq.processor.impl.transfer.refund;

import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.ErrorLogType;
import com.jackrain.nea.oc.oms.model.enums.OperateType;
import com.jackrain.nea.oc.oms.model.enums.OrderType;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoFxRefundRelation;
import com.jackrain.nea.oc.oms.mq.processor.IMqOrderDetailProcessor;
import com.jackrain.nea.oc.oms.process.transfer.impl.refund.taobaofx.TaobaoFxTransferRefundProcessImpl;
import com.jackrain.nea.oc.oms.services.IpTaobaoFxRefundService;
import com.jackrain.nea.oc.oms.step.ProcessStepResultList;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR> 周琳胜
 * create at:  19/3/6  20:33
 * @description: 淘宝分销退换货单退款单消息处理器
 */
@Slf4j
public class TaobaoFxTransferMqRefundDetailProcessor implements IMqOrderDetailProcessor {
    @Autowired
    private IpTaobaoFxRefundService ipTaobaoFxRefundService;

    @Autowired
    private TaobaoFxTransferRefundProcessImpl taobaoFxTransferRefundProcess;

    @Override
    public ProcessStepResultList start(OperateOrderMqInfo orderMqInfo) {
        String orderNo = orderMqInfo.getOrderNo();

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("淘宝分销退换货单退款单消息处理器.Start", orderNo));
        }

        IpTaobaoFxRefundRelation taobaoFxRefundRelation = this.ipTaobaoFxRefundService.selectTaobaoFxRefundRelation(orderNo);
        if (taobaoFxRefundRelation == null) {
            String errorMessage = Resources.getMessage
                    ("TaobaoExchange Received OrderMqInfo Not Exist!OrderNo=" + orderNo);
            log.error(LogUtil.format(errorMessage, orderNo));
            return new ProcessStepResultList();
        } else {
            ProcessStepResultList resultList = taobaoFxTransferRefundProcess.start(taobaoFxRefundRelation,
                    false, SystemUserResource.getRootUser());
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("淘宝分销退换货单退款单消息处理器.Result:{}", orderNo),resultList.isProcessSuccess());
            }
            return resultList;
        }
    }

    @Override
    public boolean checkMqIsCanExecute(OperateOrderMqInfo orderMqInfo) {
        return orderMqInfo.getOperateType() == OperateType.TRANSFER_ORDER
                && orderMqInfo.getChannelType() == ChannelType.TAOBAO
                && orderMqInfo.getOrderType() == OrderType.FXREFUND;
    }

}
