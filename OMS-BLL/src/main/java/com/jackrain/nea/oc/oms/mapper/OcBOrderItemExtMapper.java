package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItemExt;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> 孙俊磊
 * @since : 2019-07-23
 * create at : 2019-07-23 3:19 PM
 */
@Mapper
@Component
public interface OcBOrderItemExtMapper extends ExtentionMapper<OcBOrderItemExt> {

    @Select("SELECT * FROM oc_b_order_item_ext WHERE oc_b_order_id=#{orderId} and isactive='Y' ")
    List<OcBOrderItemExt> queryByOrderId(Long orderId);

    @Select("<script> "
            + "SELECT * FROM oc_b_order_item_ext WHERE isactive = 'Y' and oc_b_order_id "
            + "in <foreach item='item' index='index' collection='orderIdList' "
            + "open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<OcBOrderItemExt> queryByOrderIdList(@Param("orderIdList") List<Long> orderIdList);

    @Delete("delete FROM oc_b_order_item_ext WHERE oc_b_order_id=#{orderId} ")
    int deleteByOrderId(Long orderId);

    @Select("SELECT * FROM oc_b_order_item_ext WHERE isactive='Y' and order_item_id=#{itemId} limit 1")
    OcBOrderItemExt queryByOrderItemId(Long itemId);

    @Select("SELECT * FROM oc_b_order_item_ext WHERE isactive='Y' and order_item_id=#{itemId} and oc_b_order_id=#{orderId} limit 1")
    OcBOrderItemExt queryByOrderAndItemId(@Param("itemId") Long itemId, @Param("orderId") Long orderId);

    @Update("UPDATE oc_b_order_item_ext " +
            "SET dist_code_level_one = #{distCodeLevelOne}, " +
            "dist_name_level_one = #{distNameLevelOne}, " +
            "dist_code_level_two = #{distCodeLevelTwo}, " +
            "dist_name_level_two = #{distNameLevelTwo}, " +
            "dist_code_level_three = #{distCodeLevelThree}, " +
            "dist_name_level_three = #{distNameLevelThree}, " +
            "modifieddate = #{modifieddate} " +
            "WHERE oc_b_order_id=#{ocBOrderId} AND id = #{id}")
    void updateItemExtCustom(OcBOrderItemExt updateItemExt);

}
