FROM --platform=linux/amd64 registry.cn-hangzhou.aliyuncs.com/ryytn-r3-uat/openjdk:8u351-skywalking-without-kafka

RUN echo "start add jar"
ADD OMS-CoreService/OMS-Order-Shell/target/r3-oc-oms-core-shell-3.0.0-SNAPSHOT.jar /acs/user/src/
RUN echo "end add jar"

RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && echo 'Asia/Shanghai' >/etc/timezone \
;yum -y install net-tools telnet tcpdump iproute less kde-l10n-Chinese \
;yum -y reinstall glibc-common \
;localedef -c -f UTF-8 -i zh_CN zh_CN.utf8 \
;export LANG=zh_CN.UTF-8 && echo 'LANG="zh_CN.UTF-8"' > /etc/locale.conf \
;echo "export LANG=zh_CN.UTF-8" >> /etc/profile && source /etc/profile \
;yum clean all \
;echo 'alias log="cd /app/log"' >> ~/.bashrc

EXPOSE 8080
ENV APP_NAME=r3-oc-oms-core-shell-3.0.0-SNAPSHOT

ENV JAVA_OPTS=""
ENTRYPOINT [ "sh", "-c", "java ${JAVA_OPTS} -Djava.security.egd=file:/dev/./urandom -jar /acs/user/src/${APP_NAME}.jar" ]