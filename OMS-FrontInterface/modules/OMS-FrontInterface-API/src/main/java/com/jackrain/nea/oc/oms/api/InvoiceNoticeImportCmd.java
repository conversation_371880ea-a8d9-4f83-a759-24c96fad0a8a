package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.oc.oms.extmodel.ExtOcBInvoiceNotice;
import com.jackrain.nea.sys.Command;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

import java.util.List;

/**
 * @author:洪艺安
 * @since: 2019/7/27
 * @create at : 2019/7/27 14:09
 */
public interface InvoiceNoticeImportCmd extends Command {
    /**
     * 下载开票通知导入模板接口
     *
     * @return
     */
    public ValueHolderV14 downloadTemp();

    /**
     * 导入开票通知
     *
     * @return
     */
    public ValueHolderV14 importInvoiceNotice(List<ExtOcBInvoiceNotice> extInvoiceNoticeList, User user);

}
