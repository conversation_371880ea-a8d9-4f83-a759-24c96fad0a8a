package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.oc.oms.model.relation.UserPermission;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

/**
 * @author: 李龙飞
 * @create: 2019-05-14 13:28
 **/
public interface OcBOrderExportCmd {

    /**
     * 返回导出结果
     */
    public ValueHolderV14 exportList(String jsonStr, User loginUser);

    /**
     * 下载模板
     */
    public ValueHolderV14 downloadTemp();
    /**
     * 下载批量修改地址模板
     */
    public ValueHolderV14 downloadAddressTemp();

    public ValueHolderV14 exportListext(String jsonStr, User loginUser, UserPermission usrPem, Boolean withTag);

    /**
     * 修改备注下载模板
     *
     * @return
     */
    ValueHolderV14 downloadUpdateRemarkTemp();

    /**
     * 退货入库单备注导入
     *
     * @return
     */
    ValueHolderV14 downloadRefundInRemarkTemp();
}
