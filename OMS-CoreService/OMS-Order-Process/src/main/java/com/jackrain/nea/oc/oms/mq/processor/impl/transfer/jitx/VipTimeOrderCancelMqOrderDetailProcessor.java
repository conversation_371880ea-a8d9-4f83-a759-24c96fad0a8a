package com.jackrain.nea.oc.oms.mq.processor.impl.transfer.jitx;

import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.ErrorLogType;
import com.jackrain.nea.oc.oms.model.enums.OperateType;
import com.jackrain.nea.oc.oms.model.enums.OrderType;
import com.jackrain.nea.oc.oms.model.relation.IpBCancelTimeOrderVipRelation;
import com.jackrain.nea.oc.oms.mq.processor.IMqOrderDetailProcessor;
import com.jackrain.nea.oc.oms.process.jitx.timeorder.cancel.VipTimeOrderCancelProcessImpl;
import com.jackrain.nea.oc.oms.services.IpVipTimeOrderCancelService;
import com.jackrain.nea.oc.oms.step.ProcessStepResultList;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @author: chenxiulou
 * @since: 2019-06-25
 * create at : 2019-06-25 18:00
 */
@Slf4j
public class VipTimeOrderCancelMqOrderDetailProcessor implements IMqOrderDetailProcessor {

    @Autowired
    private IpVipTimeOrderCancelService vipTimeOrderCancelService;

    @Autowired
    private VipTimeOrderCancelProcessImpl timeOrderCancelProcess;

    @Override
    public ProcessStepResultList start(OperateOrderMqInfo orderMqInfo) {
        String orderNo = orderMqInfo.getOrderNo();

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Start MqOrder TimeOrderCancel", orderNo));
        }

        IpBCancelTimeOrderVipRelation cancelTimeOrderVipRelation = this.vipTimeOrderCancelService.selectCancelTimeOrder(orderNo);
        if (cancelTimeOrderVipRelation == null || cancelTimeOrderVipRelation.getCancelTimeOrderVip() == null) {
            String errorMessage = Resources.getMessage("Received OrderMqInfo Not Exist!OrderNo=" + orderNo);
            log.error(LogUtil.format(errorMessage));
            return new ProcessStepResultList();
        } else {
            ProcessStepResultList resultList = timeOrderCancelProcess.start(cancelTimeOrderVipRelation,
                    false, SystemUserResource.getRootUser());
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("Finished.MqOrder.CancelTimeOrder,Result= {}", orderNo), resultList);
            }
            return resultList;
        }
    }

    @Override
    public boolean checkMqIsCanExecute(OperateOrderMqInfo orderMqInfo) {
        return orderMqInfo.getOperateType() == OperateType.TRANSFER_ORDER
                && orderMqInfo.getChannelType() == ChannelType.VIPJITX
                && orderMqInfo.getOrderType() == OrderType.CANCELTIMEORDER;
    }
}
