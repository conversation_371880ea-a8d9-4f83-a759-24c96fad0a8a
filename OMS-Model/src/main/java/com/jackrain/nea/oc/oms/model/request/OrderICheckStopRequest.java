package com.jackrain.nea.oc.oms.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: ming.fz
 * @Date: 2019-03-12
 * @Version 1.0
 */
@Data
public class OrderICheckStopRequest implements Serializable{
    @J<PERSON>NField(name = "IDS")
    private Long[] ids;
    @J<PERSON><PERSON>ield(name = "TYPE")
    private Long type;
    @JSONField(name = "ISCHECK")
    private Long isCheck;
    @JSONField(name = "MANDATORY_AUDIT")
    private Boolean mandatoryAudit = false;

}
