package com.jackrain.nea.oc.oms.process.jitx.timeorder.normal.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.mapper.IpBTimeOrderVipMapper;
import com.jackrain.nea.oc.oms.mapper.IpBTimeOrderVipOccupyItemMapper;
import com.jackrain.nea.oc.oms.model.enums.TimeOrderVipStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpVipTimeOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTimeOrderVip;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @Description 更新订单转换状态
 **/
//@Step(order = 60, description = "更新订单转换状态")
@Slf4j
@Component
public class Step060UpdateTransferStatus extends BaseVipTimeOrderProcessStep
        implements IOmsOrderProcessStep<IpVipTimeOrderRelation> {

    @Override
    public ProcessStepResult<IpVipTimeOrderRelation> startProcess(IpVipTimeOrderRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        IpBTimeOrderVip ipBTimeOrderVip = orderInfo.getIpBTimeOrderVip();
        /**更新时效订单主表单据状态为  占单中,转换状态为  转换中*/
        ipVipTimeOrderService.updateTimeOrderData(ipBTimeOrderVip,TimeOrderVipStatusEnum.IN_SINGLE.getValue(),TransferOrderStatus.TRANSFERRING.toInteger(),null,"转换中！",true);
        return new ProcessStepResult<>(StepStatus.SUCCESS, "转单中！");
    }
}
