package com.jackrain.nea.oc.services;

import com.jackrain.nea.oc.model.SgBPhyInResultExt;
import com.jackrain.nea.oc.model.SgBPhyInResultItemExt;
import com.jackrain.nea.oc.oms.api.OcBRetailReturnUpdateRefundInCmd;
import com.jackrain.nea.oc.oms.services.OcBRetailReturnUpdateRefundInService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 零售退货单更新入库结果服务
 *
 * @author: xiWen.z
 * create at: 2019/5/9 0009
 */
@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBRetailReturnUpdateRefundInCmdImpl implements OcBRetailReturnUpdateRefundInCmd {

    @Autowired
    private OcBRetailReturnUpdateRefundInService ocBRetailReturnUpdateRefundInService;

    /**
     * @param sgPir     结果单
     * @param inRstList 明细
     * @param usr       用户
     * @return VH
     */
    @Override
    public ValueHolderV14 retailOrderUpdateRefundIn(SgBPhyInResultExt sgPir, List<SgBPhyInResultItemExt> inRstList,
                                                    User usr) {
        return ocBRetailReturnUpdateRefundInService.updateWareHousingResultService(sgPir, inRstList, usr);
    }
}
