package com.jackrain.nea.oc.oms.mapper.ac;

import com.jackrain.nea.ac.model.AcFPayableAdjustmentLogDO;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface AcFPayableAdjustmentLogMapper extends ExtentionMapper<AcFPayableAdjustmentLogDO> {
    @Select("select * from ac_f_payable_adjustment_log where ac_f_payable_adjustment_id = #{id}")
    List<AcFPayableAdjustmentLogDO> listByLogId(@Param("id") Long id);
}