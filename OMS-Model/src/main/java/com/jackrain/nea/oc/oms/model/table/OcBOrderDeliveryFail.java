package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @ClassName OcBOrderDeliveryFail
 * @Description 调用平台发货失败记录表
 * <AUTHOR>
 * @Date 2023/3/14 15:15
 * @Version 1.0
 */

@TableName(value = "oc_b_order_delivery_fail")
@Data
@Document(index = "oc_b_order_delivery_fail", type = "oc_b_order_delivery_fail")
public class OcBOrderDeliveryFail extends BaseModel {

    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @ApiModelProperty(value = "下单店铺id")
    @Field(type = FieldType.Long)
    @JSONField(name = "CP_C_SHOP_ID")
    private Long cpCShopId;

    @ApiModelProperty(value = "店铺编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CP_C_SHOP_ECODE")
    private String cpCShopEcode;

    @ApiModelProperty(value = "下单店铺标题")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CP_C_SHOP_TITLE")
    private String cpCShopTitle;

    @ApiModelProperty(value = "物流公司id")
    @Field(type = FieldType.Long)
    @JSONField(name = "CP_C_LOGISTICS_ID")
    private Long cpCLogisticsId;

    @ApiModelProperty(value = "物流公司编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CP_C_LOGISTICS_ECODE")
    private String cpCLogisticsEcode;

    @ApiModelProperty(value = "物流公司名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CP_C_LOGISTICS_ENAME")
    private String cpCLogisticsEname;

    @ApiModelProperty(value = "物流单号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "LOGISTIC_NUMBER")
    private String logisticNumber;

    @ApiModelProperty(value = "零售发货单ID")
    @JSONField(name = "OC_B_ORDER_ID")
    @Field(type = FieldType.Long)
    private Long ocBOrderId;

    @ApiModelProperty(value = "初始平台单号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "TID")
    private String tid;

    @ApiModelProperty(value = "下次执行时间")
    @Field(type = FieldType.Long)
    @JSONField(name = "NEXT_TIME")
    private Date nextTime;

    @ApiModelProperty(value = "重试次数")
    @Field(type = FieldType.Integer)
    @JSONField(name = "RETRY_NUMBER")
    private Integer retryNumber;

    @ApiModelProperty(value = "平台ID")
    @Field(type = FieldType.Integer)
    @JSONField(name = "PLATFORM")
    private Integer platform;

    /**
     * 0 失败 1成功
     */
    @ApiModelProperty(value = "状态")
    @Field(type = FieldType.Integer)
    @JSONField(name = "STATUS")
    private Integer status;
}
