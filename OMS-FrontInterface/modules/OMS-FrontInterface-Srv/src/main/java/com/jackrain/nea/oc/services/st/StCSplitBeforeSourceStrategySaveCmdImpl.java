package com.jackrain.nea.oc.services.st;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.annotation.OmsOperationLog;
import com.jackrain.nea.cpext.utils.ValueHolderUtils;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.st.StCSplitBeforeSourceStrategySaveCmd;
import com.jackrain.nea.st.service.StCSplitBeforeSourceStrategyService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @program: r3-oc-oms
 * @description: 寻源前拆单策略
 * @author: caomalai
 * @create: 2022-07-29 14:00
 **/
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class StCSplitBeforeSourceStrategySaveCmdImpl extends CommandAdapter implements StCSplitBeforeSourceStrategySaveCmd {
    @Autowired
    private StCSplitBeforeSourceStrategyService strategyService;

    @Override
    @OmsOperationLog(mainTableName = "ST_C_SPLIT_BEFORE_SOURCE_STRATEGY",itemsTableName = "ST_C_SPLIT_BEFORE_SOURCE_STRATEGY_CATEGORY_ITEM,ST_C_SPLIT_BEFORE_SOURCE_STRATEGY_WEIGHT_ITEM,ST_C_SPLIT_BEFORE_SOURCE_STRATEGY_SKU_ITEM")
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("StCSplitBeforeSourceStrategySaveCmdImpl param：{}","寻源前拆单策略") , param);
        }
        User user = querySession.getUser();
        Long objid = null;
        String tableName = param.getString("table");
        try{
            objid = strategyService.save(param,user);
        }catch (Exception e){
            if(log.isErrorEnabled()){
                log.error(LogUtil.format("新增异常"),e);
            }
            return ValueHolderUtils.fail(e.getMessage());
        }
        return ValueHolderUtils.success("新增成功！",ValueHolderUtils.createAddErrorData(tableName, objid, null));
    }

}
