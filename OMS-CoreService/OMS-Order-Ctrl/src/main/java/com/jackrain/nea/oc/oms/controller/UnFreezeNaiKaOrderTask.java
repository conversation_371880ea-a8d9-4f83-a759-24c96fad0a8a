//package com.jackrain.nea.oc.oms.controller;
//
//import cn.hutool.core.util.ObjectUtil;
//import com.alibaba.dubbo.config.annotation.Reference;
//import com.alibaba.fastjson.JSONObject;
//import com.google.common.base.Throwables;
//import com.jackrain.nea.constants.ResultCode;
//import com.jackrain.nea.hub.api.naika.NaiKaOrderCmd;
//import com.jackrain.nea.hub.request.naika.NaiKaThawRequest;
//import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
//import com.jackrain.nea.oc.oms.mapper.OcBOrderNaiKaMapper;
//import com.jackrain.nea.oc.oms.mapper.OcBOrderNaiKaUnfreezeMapper;
//import com.jackrain.nea.oc.oms.mapperservice.OcBOrderNaiKaUnfreezeMapperService;
//import com.jackrain.nea.oc.oms.model.enums.UnFreezeEnum;
//import com.jackrain.nea.oc.oms.model.enums.naika.OmsOrderNaiKaStatusEnum;
//import com.jackrain.nea.oc.oms.model.table.OcBOrder;
//import com.jackrain.nea.oc.oms.model.table.OcBOrderNaiKa;
//import com.jackrain.nea.oc.oms.model.table.OcBOrderNaikaUnfreeze;
//import com.jackrain.nea.rpc.CpRpcService;
//import com.jackrain.nea.sys.domain.ValueHolderV14;
//import com.jackrain.nea.utility.LogUtil;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
//import org.springframework.stereotype.Component;
//
//import java.util.ArrayList;
//import java.util.Date;
//import java.util.HashSet;
//import java.util.List;
//import java.util.Map;
//import java.util.Set;
//import java.util.concurrent.Callable;
//import java.util.concurrent.Future;
//import java.util.stream.Collectors;
//
///**
// * @ClassName UnFreezeNaiKaOrderTask
// * @Description TODO
// * <AUTHOR>
// * @Date 2022/6/29 17:23
// * @Version 1.0
// */
//@Component
//@Slf4j
//public class UnFreezeNaiKaOrderTask {
//
//    @Autowired
//    private OcBOrderNaiKaMapper ocBOrderNaiKaMapper;
//    @Autowired
//    private OcBOrderMapper ocBOrderMapper;
//    @Autowired
//    private OcBOrderNaiKaUnfreezeMapper naiKaUnfreezeMapper;
//    @Autowired
//    private OcBOrderNaiKaUnfreezeMapperService naiKaUnfreezeMapperService;
//    @Reference(group = "hub", version = "1.0")
//    private NaiKaOrderCmd naiKaOrderCmd;
//    @Autowired
//    private CpRpcService cpRpcService;
//    @Autowired
//    private ThreadPoolTaskExecutor naikaUnfreezeThreadPoolExecutor;
//
//    public void execute(JSONObject params) {
//        try {
//            long start = System.currentTimeMillis();
//            final String taskTableName = "oc_b_order_naika_unfreeze";
//            Map<String, String> topMap = null;
//            Set<String> nodes = topMap.keySet();
//            if (CollectionUtils.isEmpty(nodes)) {
//                return;
//            }
//            List<Future<Boolean>> results = new ArrayList<Future<Boolean>>();
//
//            for (String nodeName : nodes) {
//                results.add(naikaUnfreezeThreadPoolExecutor.submit(new CallableNaiKaUnFreezeTaskWithResult(nodeName, topMap.get(nodeName))));
//            }
//            //线程执行结果获取
//            for (Future<Boolean> futureResult : results) {
//                try {
//                    if (log.isDebugEnabled()) {
//                        log.debug(LogUtil.format("UnFreezeNaiKaOrderTask------>线程结果:{}"), futureResult.get().toString());
//                    }
//                } catch (Exception e) {
//                    log.error(LogUtil.format("UnFreezeNaiKaOrderTask多线程获取InterruptedException异常：{}", "UnFreezeNaiKaOrderTask"), Throwables.getStackTraceAsString(e));
//                }
//            }
//            if (log.isDebugEnabled()) {
//                log.debug(LogUtil.format("UnFreezeNaiKaOrderTask 解冻奶卡任务完成 useTime : {}"), (System.currentTimeMillis() - start));
//            }
//        } catch (Exception e) {
//            log.error(LogUtil.format("解冻奶卡任务执行失败,异常！{}", "UnFreezeNaiKaOrderTask"), Throwables.getStackTraceAsString(e));
//
//        }
//        return;
//    }
//
//    class CallableNaiKaUnFreezeTaskWithResult implements Callable<Boolean> {
//
//        private final String nodeName;
//
//        private final String taskTableName;
//
//        public CallableNaiKaUnFreezeTaskWithResult(String nodeName, String taskTableName) {
//            this.nodeName = nodeName;
//            this.taskTableName = taskTableName;
//        }
//
//        @Override
//        public Boolean call() throws Exception {
//            Integer pageSize = 100;
//            List<OcBOrderNaikaUnfreeze> naikaUnfreezeList = null;
//            if (CollectionUtils.isEmpty(naikaUnfreezeList)) {
//                return true;
//            }
//            for (OcBOrderNaikaUnfreeze naikaUnfreeze : naikaUnfreezeList) {
//                if (naikaUnfreeze.getUnfreezeTimes() > 5) {
//                    continue;
//                }
//                OcBOrderNaikaUnfreeze updateNaikaUnfreeze = new OcBOrderNaikaUnfreeze();
//                updateNaikaUnfreeze.setOcBOrderId(naikaUnfreeze.getOcBOrderId());
//                List<OcBOrderNaikaUnfreeze> unfreezeList = naiKaUnfreezeMapper.selectUnFreezeNaiKaOrderByOrderId(naikaUnfreeze.getOcBOrderId());
//                List<OcBOrderNaikaUnfreeze> unfreezeSuccessList = new ArrayList<>();
//                if (CollectionUtils.isNotEmpty(unfreezeList)) {
//                    // 过滤 如果之前有已经解冻完成的 则将此单也设置为完成
//                    unfreezeSuccessList = unfreezeList.stream().filter(s -> s.getUnfreezeStatus().equals(UnFreezeEnum.UN_FREEZE_SUCCESS.getStatus())).collect(Collectors.toList());
//                    if (CollectionUtils.isNotEmpty(unfreezeSuccessList)) {
//                        updateNaikaUnfreeze.setUnfreezeStatus(UnFreezeEnum.UN_FREEZE_SUCCESS.getStatus());
//                        updateNaikaUnfreeze.setModifieddate(new Date());
//                        updateNaikaUnfreeze.setId(naikaUnfreeze.getId());
//                        naiKaUnfreezeMapperService.updateById(updateNaikaUnfreeze);
//                        continue;
//                    }
//                }
//
//                // 根据零售发货单单id 查询零售发货单数据
//                OcBOrder ocBOrder = ocBOrderMapper.get4NaiKaOrder(naikaUnfreeze.getOcBOrderId());
//                List<OcBOrderNaiKa> ocBOrderNaiKaList = ocBOrderNaiKaMapper.selectNaiKaByOcBOrderId(naikaUnfreeze.getOcBOrderId());
//                if (CollectionUtils.isEmpty(ocBOrderNaiKaList)) {
//                    updateNaikaUnfreeze.setUnfreezeStatus(UnFreezeEnum.UN_FREEZE_SUCCESS.getStatus());
//                    updateNaikaUnfreeze.setModifieddate(new Date());
//                    updateNaikaUnfreeze.setId(naikaUnfreeze.getId());
//                    naiKaUnfreezeMapperService.updateById(updateNaikaUnfreeze);
//                    continue;
//                }
//                // 取出所有卡号
//                Set<String> cardCodeList = new HashSet<>();
//                for (OcBOrderNaiKa ocBOrderNaiKa : ocBOrderNaiKaList) {
//                    if (ObjectUtil.equals(ocBOrderNaiKa.getNaikaStatus(), OmsOrderNaiKaStatusEnum.FREEZE.getStatus())
//                            || ObjectUtil.equals(ocBOrderNaiKa.getNaikaStatus(), OmsOrderNaiKaStatusEnum.FREEZE_FAILED.getStatus())) {
//                        cardCodeList.add(ocBOrderNaiKa.getCardCode());
//                    }
//                }
//                if (CollectionUtils.isEmpty(cardCodeList)) {
//                    updateNaikaUnfreeze.setUnfreezeStatus(UnFreezeEnum.UN_FREEZE_SUCCESS.getStatus());
//                    updateNaikaUnfreeze.setUnfreezeTimes(naikaUnfreeze.getUnfreezeTimes() + 1);
//                    updateNaikaUnfreeze.setModifieddate(new Date());
//                    updateNaikaUnfreeze.setId(naikaUnfreeze.getId());
//                    naiKaUnfreezeMapperService.updateById(updateNaikaUnfreeze);
//                    continue;
//                }
//                NaiKaThawRequest naiKaThawRequest = new NaiKaThawRequest();
//                naiKaThawRequest.setBillNo(ocBOrder.getBillNo());
//                naiKaThawRequest.setSourceCode(ocBOrder.getSourceCode());
//                naiKaThawRequest.setCardList(new ArrayList<>(cardCodeList));
//                naiKaThawRequest.setShopCode(ocBOrder.getCpCShopEcode());
//                if (ocBOrder.getIsResetShip() == null || ObjectUtil.equals(0, ocBOrder.getIsResetShip())) {
//                    naiKaThawRequest.setType(1);
//                } else {
//                    naiKaThawRequest.setType(2);
//                }
//                try {
//                    // fixme 等待更新
//                    ValueHolderV14 valueHolderV14 = null;
//                    // 根据结果 对奶卡解冻表数据进行处理
//                    if (ObjectUtil.equals(ResultCode.FAIL, valueHolderV14.getCode())) {
//                        // 执行失败 则对解冻次数以及状态进行修改
//                        updateNaikaUnfreeze.setUnfreezeStatus(UnFreezeEnum.UN_FREEZE_FAIL.getStatus());
//                        updateNaikaUnfreeze.setUnfreezeTimes(naikaUnfreeze.getUnfreezeTimes() + 1);
//                        updateNaikaUnfreeze.setModifieddate(new Date());
//                        updateNaikaUnfreeze.setId(naikaUnfreeze.getId());
//                        naiKaUnfreezeMapperService.updateById(updateNaikaUnfreeze);
//                        updateNaiKaStatus(new ArrayList<>(cardCodeList), ocBOrder.getId(), OmsOrderNaiKaStatusEnum.FREEZE_FAILED.getStatus());
//                    } else {
//                        // 执行成功 则对解冻次数以及状态进行修改
//                        updateNaikaUnfreeze.setUnfreezeStatus(UnFreezeEnum.UN_FREEZE_SUCCESS.getStatus());
//                        updateNaikaUnfreeze.setModifieddate(new Date());
//                        updateNaikaUnfreeze.setId(naikaUnfreeze.getId());
//                        naiKaUnfreezeMapperService.updateById(updateNaikaUnfreeze);
//                        updateNaiKaStatus(new ArrayList<>(cardCodeList), ocBOrder.getId(), OmsOrderNaiKaStatusEnum.FREEZE_SUCCESS.getStatus());
//                    }
//                } catch (Exception e) {
//                    updateNaikaUnfreeze.setUnfreezeStatus(UnFreezeEnum.UN_FREEZE_SUCCESS.getStatus());
//                    updateNaikaUnfreeze.setUnfreezeTimes(naikaUnfreeze.getUnfreezeTimes() + 1);
//                    updateNaikaUnfreeze.setModifieddate(new Date());
//                    updateNaikaUnfreeze.setId(naikaUnfreeze.getId());
//                    naiKaUnfreezeMapperService.updateById(updateNaikaUnfreeze);
//                    updateNaiKaStatus(new ArrayList<>(cardCodeList), ocBOrder.getId(), OmsOrderNaiKaStatusEnum.FREEZE_FAILED.getStatus());
//                }
//            }
//            return true;
//        }
//    }
//
//    // 更新奶卡表数据
//    private void updateNaiKaStatus(List<String> cardCodeList, Long ocBOrderId, Integer status) {
//        for (String cardCode : cardCodeList) {
//            List<OcBOrderNaiKa> ocBOrderNaiKaList = ocBOrderNaiKaMapper.selectNaiKaList(ocBOrderId, cardCode);
//            if (CollectionUtils.isEmpty(ocBOrderNaiKaList)) {
//                continue;
//            }
//            for (OcBOrderNaiKa ocBOrderNaiKa : ocBOrderNaiKaList) {
//                OcBOrderNaiKa updateOcBOrderNaiKa = new OcBOrderNaiKa();
//                updateOcBOrderNaiKa.setNaikaStatus(status);
//                updateOcBOrderNaiKa.setId(ocBOrderNaiKa.getId());
//                updateOcBOrderNaiKa.setOcBOrderId(ocBOrderNaiKa.getOcBOrderId());
//                updateOcBOrderNaiKa.setModifieddate(new Date());
//                ocBOrderNaiKaMapper.updateById(updateOcBOrderNaiKa);
//            }
//        }
//    }
//}
