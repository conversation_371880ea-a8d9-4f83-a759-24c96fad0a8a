package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.api.InvoiceNoticeExportCmd;
import com.jackrain.nea.oc.oms.services.InvoiceNoticeExportService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author:洪艺安
 * @since: 2019/7/27
 * @create at : 2019/7/27 22:25
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class InvoiceNoticeExportCmdImpl extends CommandAdapter implements InvoiceNoticeExportCmd {
    @Autowired
    private InvoiceNoticeExportService invoiceNoticeExportService;

    @Override
    public ValueHolderV14 exportInvoiceNotice(JSONObject obj, User user) {
        return invoiceNoticeExportService.exportInvoiceNotice(obj, user);
    }
}
