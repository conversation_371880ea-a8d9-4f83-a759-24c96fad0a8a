package com.jackrain.nea.oc.oms.task.refundorder;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.xxl.job.starter.helper.R3XxlJobParamHelper;
import com.google.common.collect.Lists;
import com.jackrain.nea.config.BusinessSystem;
import com.jackrain.nea.oc.oms.mapper.IOrderRefundInTaskMapper;
import com.jackrain.nea.oc.oms.model.enums.OcBRefundInStatusEnum;
import com.jackrain.nea.oc.oms.model.table.task.OcBRefundInTask;
import com.jackrain.nea.oc.oms.services.RefundOrderToWmsBackService;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
@Deprecated
public class OmsRefundInTransferTask implements IR3Task {

    private final BusinessSystem businessSystem;

    private final IOrderRefundInTaskMapper refundInTaskMapper;

    private final BllRedisLockOrderUtil redisUtil;

    private final RefundOrderToWmsBackService refundOrderToWmsBackService;

    public final List<Integer> status = Arrays.asList(OcBRefundInStatusEnum.RefundInTaskStatusEnum.INIT.getCode(),
            OcBRefundInStatusEnum.RefundInTaskStatusEnum.FAIL.getCode());

    @Value("${r3.oms.refund.in.task.max.compensate.num:5}")
    private Integer compensate;

    @Override
//    @XxlJob("OmsRefundInTransferTask")
    public RunTaskResult execute(JSONObject params) {
        params = R3XxlJobParamHelper.xxlParam2R3Json();
        RunTaskResult result = new RunTaskResult();
        if (log.isDebugEnabled()) {
            log.debug("OmsRefundInTransferTask.start.params:{}", JSONObject.toJSONString(params));
        }
        try {
            String joinStr = StringUtils.join(status, ',');

            createRefundInOrder(businessSystem.getReturnOrderTransferWmsNum(), compensate, joinStr);

            result.setSuccess(true);
        } catch (Exception ex) {
            log.error("OmsRefundInTransferTask.Execute Error", ex);
            result.setSuccess(false);
            result.setMessage(ex.getMessage());
        }
        if (log.isDebugEnabled()) {
            log.debug("OmsRefundInTransferTask.end.result:{}", JSON.toJSONString(result));
        }
        return result;
    }

    public void createRefundInOrder(Integer pageSize, Integer count, String statusStr) {
        if (log.isDebugEnabled()) {
            log.debug("OmsRefundInTransferTask.createRefundInOrder.pageSize:{},count:{},statusStr:{}", pageSize, count, statusStr);
        }

        List<OcBRefundInTask> ocBRefundInTasks = refundInTaskMapper.selectRefundInTaskBySize(pageSize, count, statusStr);

        List<OcBRefundInTask> lockOkOrderList = Lists.newArrayList();
        List<RedisReentrantLock> locks = Lists.newArrayList();
        try {
            for (OcBRefundInTask refundInTask : ocBRefundInTasks) {
                RedisReentrantLock reentrantLock = RedisMasterUtils.getReentrantLock(BllRedisKeyResources.buildLockReturnOrderKey(refundInTask.getId()));
                if (reentrantLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                    lockOkOrderList.add(refundInTask);
                } else {
                    log.error("退货入库回传中间表定时任务上锁失败，单据正在操作中{}", refundInTask.getId());
                }
                locks.add(reentrantLock);
            }
            if (CollectionUtils.isEmpty(ocBRefundInTasks)) {
                return;
            }
            List<Long> idList = lockOkOrderList.stream().map(OcBRefundInTask::getId).collect(Collectors.toList());

            String ids = StringUtils.join(idList, ",");
            List<OcBRefundInTask> refundInTaskList = refundInTaskMapper.selectRefundInTaskListByIds(ids);

            refundOrderToWmsBackService.returnOrderWmsInResultBack(refundInTaskList);
        } catch (Exception e) {
            log.error("退货入库回传中间表定时任务异常", e);
        } finally {
            try {
                locks.forEach(RedisReentrantLock::unlock);
            } catch (Exception e) {
                log.error("退货入库回传中间表定时任务异常释放锁失败!" + e.getMessage());
            }
        }
    }
}
