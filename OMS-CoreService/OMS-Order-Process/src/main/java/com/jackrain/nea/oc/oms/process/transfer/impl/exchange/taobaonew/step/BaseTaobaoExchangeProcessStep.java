package com.jackrain.nea.oc.oms.process.transfer.impl.exchange.taobaonew.step;

import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.services.IpTaobaoExchangeService;
import com.jackrain.nea.oc.oms.services.OmsRefundOrderService;
import com.jackrain.nea.oc.oms.services.OmsReturnOrderService;
import com.jackrain.nea.oc.oms.services.OmsTaobaoExchangeService;
import com.jackrain.nea.oc.oms.services.refund.OmsReturnUtil;
import com.jackrain.nea.oc.oms.util.ExchangeOrderTransferUtil;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.StRpcService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Author: 黄世新
 * @Date: 2020/11/30 8:51 下午
 * @Version 1.0
 */
public abstract class BaseTaobaoExchangeProcessStep {

    @Autowired
    protected IpTaobaoExchangeService ipTaobaoExchangeService;
    @Autowired
    protected OmsTaobaoExchangeService omsTaobaoExchangeService;
    @Autowired
    protected ExchangeOrderTransferUtil exchangeOrderTransferUtil;
    @Autowired
    protected OmsReturnOrderService omsReturnOrderService;
    @Autowired
    protected OmsReturnUtil omsReturnUtil;
    @Autowired
    protected OmsRefundOrderService omsRefundOrderService;
    @Autowired
    protected StRpcService stRpcService;
    @Autowired
    protected CpRpcService cpRpcService;
    @Autowired
    protected OcBOrderMapper ocBOrderMapper;
    @Autowired
    protected OcBOrderItemMapper ocBOrderItemMapper;
}
