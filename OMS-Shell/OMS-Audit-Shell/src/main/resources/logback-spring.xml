<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 定义日志文件 输入位置 -->
    <property name="log_dir" value="./app/log"/>
    <!-- 配置日志清理时间 -->
    <property name="maxHistory" value="5"/>
    <!-- 配置日志文件限制 -->
    <property name="totalSizeCap" value="1GB"/>
    <!-- 设置单个日志文件的大小限制 -->
    <property name="maxFileSize" value="300MB"/>
    <!-- 日志打印格式-->
    <property name="PATTERN"
              value="[%date{yyyy-MM-dd HH:mm:ss.SSS}] [%thread] [%-5level] [%logger{50}#%line] %msg%n"/>

    <!-- ConsoleAppender -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <!-- 对日志进行格式化 -->
        <encoder class="com.yomahub.tlog.core.enhance.logback.AspectLogbackEncoder">
            <pattern>${PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <!-- Only log level WARN and above -->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>DEBUG</level>
        </filter>

        <filter class="ch.qos.logback.core.filter.EvaluatorFilter">
            <evaluator>
                <expression>
                    marker != null &amp;&amp;
                    "MYBATIS".equals(marker.getName()) &amp;&amp;
                    message.contains("Preparing:")
                </expression>
            </evaluator>
            <onMatch>DENY</onMatch>
            <onMismatch>NEUTRAL</onMismatch>
        </filter>
    </appender>

    <!-- root -->
    <root level="INFO">
        <appender-ref ref="STDOUT"/>
    </root>

    <!-- 多个使用逗号隔开. -->
    <springProfile name="dev, test, prod">
        <logger name="org.apache.dubbo" level="INFO"/>

        <logger name="com.alibaba.dubbo" level="WARN"/>
        <logger name="org.springframework" level="WARN"/>
        <logger name="com.jackrain.nea.vp.job" level="WARN"/>
        <logger name="com.jackrain.nea.config" level="WARN"/>

        <logger name="RocketmqCommon" level="ERROR"/>
        <logger name="RocketmqRemoting" level="ERROR"/>
        <logger name="RocketmqClient" level="ERROR"/>
        <logger name="com.github.ltsopensource" level="ERROR"/>

        <logger name="com.jackrain" level="DEBUG"/>
        <logger name="com.burgeon" level="DEBUG"/>

        <logger name="com.jackrain.nea.web.common.ObjectSingle" level="OFF"/>
        <logger name="com.jackrain.nea.jdbc.datasource" level="OFF"/>
    </springProfile>

</configuration>