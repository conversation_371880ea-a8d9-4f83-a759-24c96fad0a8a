package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.StCSticker;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

@Mapper
public interface StCStickerMapper extends ExtentionMapper<StCSticker> {


    @Select("SELECT * FROM st_c_sticker WHERE shop_id = #{shopId} and status = 2 and end_time >=#{date}")
    List<StCSticker> selectStCStickerByStatus(@Param("date") Date date, @Param("shopId") Long shopId);


    @Select("SELECT * FROM st_c_sticker WHERE shop_id = #{shopId} and status = 2 ")
    List<StCSticker> selectStCStickerList(@Param("shopId") Long shopId);

}