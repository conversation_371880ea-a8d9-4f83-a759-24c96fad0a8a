package com.jackrain.nea.ac.service;

import com.google.common.collect.Lists;
import com.jackrain.nea.ac.model.AcFPayableAdjustmentExcel;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.model.constant.AcConstant;
import com.jackrain.nea.oc.oms.util.ExportUtil;
import com.jackrain.nea.oc.oms.util.ImportUtil;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.UserImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * @author:洪艺安
 * @since: 2019/7/11
 * @create at : 2019/7/11 9:14
 */
@Component
@Slf4j
public class PayableAdjustmentImportService extends CommandAdapter {

    @Autowired
    private ExportUtil exportUtil;
    @Autowired
    private ImportUtil importUtil;

    @Value("${r3.oss.endpoint}")
    private String endpoint;

    @Value("${r3.oss.accessKey}")
    private String accessKeyId;

    @Value("${r3.oss.secretKey}")
    private String accessKeySecret;

    @Value("${r3.oss.bucketName}")
    private String bucketName;

    @Value("${r3.oss.timeout}")
    private String timeout;

    public ValueHolderV14 downloadTemp() {
        ValueHolderV14 vh = new ValueHolderV14(ResultCode.SUCCESS, "应付款调整单导入模板下载成功！");
        exportUtil.setEndpoint(this.endpoint);
        exportUtil.setAccessKeyId(this.accessKeyId);
        exportUtil.setAccessKeySecret(this.accessKeySecret);
        exportUtil.setBucketName(this.bucketName);
        if (StringUtils.isEmpty(timeout)) {
            exportUtil.setTimeout("1800000");
        }
        /**
         *  拼接Excel主表sheet表头字段
         * */
        String mainNames[] = {"平台单号", "单据类型", "调整类型", "店铺名称", "实体仓", "赔付快递公司",
                "快递单号", "总应付金额", "支付方式", "备注", "来源单据编号", "顾客电话", "顾客姓名", "支付宝号", "会员昵称", "付款时间"};
        User user = new UserImpl();
        ((UserImpl) user).setName("");
        List mainList = Lists.newArrayList(mainNames);
        //生成Excel
        XSSFWorkbook hssfWorkbook = new XSSFWorkbook();
        exportUtil.executeSheet(hssfWorkbook, "应付款调整单数据", "", mainList, Lists.newArrayList(), Lists.newArrayList(), false);
        String putMsg = exportUtil.saveFileAndPutOss(hssfWorkbook, "应付款调整单导入模板", user, "OSS-Bucket/EXPORT/AcFPayableAdjustment/");
        vh.setData(putMsg);
        return vh;
    }

    public ValueHolderV14 importPayableAdjustment(MultipartFile file, User user) {
        //1.传入数据校验
        ValueHolderV14 vh = new ValueHolderV14();
        if (file == null) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("请求参数不能为空!");
            return vh;
        }
        InputStream inputStream = null;
        try {
            inputStream = file.getInputStream();
        } catch (IOException e) {
            log.error(LogUtil.format("应付款调整单导入文件装换成流失败！错误信息为：" + e.getMessage(), "应付款调整单导入"));
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("文件转换成流失败!");
            return vh;
        }
        //2.解析Excel并组装传入Json
        Workbook workbook = null;
        try {
            workbook = getWorkbookForImportFile(inputStream, file);
        } catch (IOException e) {
            log.error(LogUtil.format("应付款调整单解析Excel失败！错误信息为：" + e.getMessage(), "应付款调整单解析"));
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("文件解析Excel失败!");
            return vh;
        }
        List<AcFPayableAdjustmentExcel> payableAdjustmentList = getPayableAdjustmentExcelList(workbook);
        if (CollectionUtils.isEmpty(payableAdjustmentList)) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("导入模板主表数据不能为空!");
            return vh;
        }
        //TODO:3.调用保存服务
        return vh;
    }

    /**
     * @param inputStream
     * @param file
     * @return org.apache.poi.ss.usermodel.Workbook
     * @Description 分版本处理对应excel文件
     * <AUTHOR>
     * @date 2019/7/11 15:38
     */
    private Workbook getWorkbookForImportFile(InputStream inputStream, MultipartFile file) throws IOException {
        Workbook workbook = null;
        String fileName = file.getName();
        if (fileName.toLowerCase().endsWith("xls")) {
            workbook = new HSSFWorkbook(inputStream);
        } else {
            workbook = new XSSFWorkbook(inputStream);
        }
        return workbook;
    }

    /**
     * @param workbook
     * @return java.util.List<com.jackrain.nea.ac.model.table.AcFPayableAdjustmentExcel>
     * @Description 获取主表sheet数据，转换成主表对象
     * <AUTHOR>
     * @date 2019/7/11 18:32
     */
    public List<AcFPayableAdjustmentExcel> getPayableAdjustmentExcelList(Workbook workbook) {
        List<AcFPayableAdjustmentExcel> payableAdjustList = Lists.newArrayList();
        List<Map<String, String>> execlList = null;
        try {
            execlList = importUtil.readExcel(0, workbook);
        } catch (Exception e) {
            e.printStackTrace();
        }
        int index = 0;
        for (Map<String, String> columnMap : execlList) {
            if (index == 0) {
                // 校验excel字段
                if (columnMap.size() != 18
                        || !"平台单号".equals(columnMap.get(AcConstant.PREFIX_ROW + index + AcConstant.PREFIX_CELL + 0))
                        || !"单据类型".equals(columnMap.get(AcConstant.PREFIX_ROW + index + AcConstant.PREFIX_CELL + 1))
                        || !"调整类型".equals(columnMap.get(AcConstant.PREFIX_ROW + index + AcConstant.PREFIX_CELL + 2))
                        || !"店铺名称".equals(columnMap.get(AcConstant.PREFIX_ROW + index + AcConstant.PREFIX_CELL + 3))
                        || !"实体仓".equals(columnMap.get(AcConstant.PREFIX_ROW + index + AcConstant.PREFIX_CELL + 4))
                        || !"赔付快递公司".equals(columnMap.get(AcConstant.PREFIX_ROW + index + AcConstant.PREFIX_CELL + 5))
                        || !"快递单号".equals(columnMap.get(AcConstant.PREFIX_ROW + index + AcConstant.PREFIX_CELL + 6))
                        || !"总应付金额".equals(columnMap.get(AcConstant.PREFIX_ROW + index + AcConstant.PREFIX_CELL + 7))
                        || !"支付方式".equals(columnMap.get(AcConstant.PREFIX_ROW + index + AcConstant.PREFIX_CELL + 8))
                        || !"备注".equals(columnMap.get(AcConstant.PREFIX_ROW + index + AcConstant.PREFIX_CELL + 9))
                        || !"来源单据编号".equals(columnMap.get(AcConstant.PREFIX_ROW + index + AcConstant.PREFIX_CELL + 10))
                        || !"顾客电话".equals(columnMap.get(AcConstant.PREFIX_ROW + index + AcConstant.PREFIX_CELL + 11))
                        || !"顾客姓名".equals(columnMap.get(AcConstant.PREFIX_ROW + index + AcConstant.PREFIX_CELL + 12))
                        || !"支付宝号".equals(columnMap.get(AcConstant.PREFIX_ROW + index + AcConstant.PREFIX_CELL + 13))
                        || !"会员昵称".equals(columnMap.get(AcConstant.PREFIX_ROW + index + AcConstant.PREFIX_CELL + 14))
                        || !"付款时间".equals(columnMap.get(AcConstant.PREFIX_ROW + index + AcConstant.PREFIX_CELL + 15))) {
                    return Lists.newArrayList();
                }
            } else {
                if (StringUtils.isNotEmpty(columnMap.get(AcConstant.PREFIX_ROW + index + AcConstant.PREFIX_CELL + 1))) {
                    // 组装待配货记录
                    payableAdjustList.add(importModelCreate(index, columnMap));
                }
            }
            index++;
        }
        return payableAdjustList;
    }

    /**
     * 导入生成模型
     *
     * @return
     */
    private AcFPayableAdjustmentExcel importModelCreate(int index, Map<String, String> columnMap) {
        //TODO:待业务确认后完善
        AcFPayableAdjustmentExcel payableAdjustmentExcel = new AcFPayableAdjustmentExcel();
        payableAdjustmentExcel.setBillStatus(new Integer(columnMap.get(AcConstant.PREFIX_ROW + index + AcConstant.PREFIX_CELL + 0)));
        return payableAdjustmentExcel;
    }

}
