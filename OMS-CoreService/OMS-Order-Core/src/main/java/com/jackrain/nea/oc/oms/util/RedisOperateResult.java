package com.jackrain.nea.oc.oms.util;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Redis操作结果对象类
 *
 * @author: 易邵峰
 * @since: 2019-01-28
 * create at : 2019-01-28 09:25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RedisOperateResult {
    /**
     * 是否成功
     */
    private boolean isSuccess;

    /**
     * Redis Key值
     */
    private String redisKeyName;

    /**
     * Redis操作Message值
     */
    private String message;

}
