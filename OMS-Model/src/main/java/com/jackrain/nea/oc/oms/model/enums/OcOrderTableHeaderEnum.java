package com.jackrain.nea.oc.oms.model.enums;

import com.jackrain.nea.oc.oms.model.result.QueryOrderTableHeaderResult;

import java.util.ArrayList;
import java.util.List;

/**
 * 订单列表-表头
 *
 * @author: xiwen.z
 * create at: 2019/3/13 0013
 */
public enum OcOrderTableHeaderEnum {

    FLAG("ORDER_FLAG", "旗帜", 10),
    TAG("ORDER_TAG", "订单标识", 20),
    ORDESTATUS("orderStatusName", "订单状态", 30),
    WMSCANCELSTATUS("wmsCancelStatusName", "wms撤回状态", 40),
    SHOPTITLE("CP_C_SHOP_TITLE", "下单店铺", 50),
    SOURCECODE("SOURCE_CODE", "平台单号", 60),
    ID("ID", "订单编号", 70),
    PAYTYPE("payTypeName", "付款方式", 80),
    PLATFORM("platFormName", "平台", 90),
    RETURNSTATUS("returnStatusName", "退货状态", 100),
    WAREHOUSE("CP_C_PHY_WAREHOUSE_ENAME", "发货仓库", 110),
    LOGISTICSENAME("CP_C_LOGISTICS_ENAME", "物流公司", 120),
    LOGISTICSECODE("EXPRESSCODE", "物流单号", 130),
    RECEIVERADDRESS("RECEIVER_ADDRESS", "收货信息", 140),
    GOODSINFO("queryOrderItemResultList", "商品信息", 150),
    SYSTEMREMARK("SYSREMARK", "系统备注", 160),
    PAYTIME("PAY_TIME", "付款时间", 170),
    CREATIONDATE("CREATIONDATE", "创建时间", 180),
    OWNERNAME("OWNERNAME", "创建人", 190),
    AUDITTIME("AUDIT_TIME", "审核时间", 195),
    DISTRIBUTIONTIME("DISTRIBUTION_TIME", "配货时间", 200),
    SCAN_TIME("SCAN_TIME", "出库时间", 205),
    BUYERMESSAGE("BUYER_MESSAGE", "买家留言", 210),
    SELLERMEMO("SELLER_MEMO", "卖家备注", 220),
    INVOICEHEADER("INVOICE_HEADER", "开票抬头", 230),
    INVOICECONTENT("INVOICE_CONTENT", "开票内容", 240),
    // WEIGHT("WEIGHT", "商品重量", 250),
    QTY_ALL("QTY_ALL", "商品总数", 260),
    SUFFIX_INFO("SUFFIX_INFO", "订单补充信息", 270),
    BILLNO("BILL_NO", "单据编号", 275),
    USERNICK("USER_NICK", "买家昵称", 280),
    ORDERTYPE("orderTypeName", "订单类型", 290),
    PRODUCTAMOUNT("PRODUCT_AMT", "商品总额", 300),
    PRODUCTDISCOUNTAMT("PRODUCT_DISCOUNT_AMT", "商品优惠金额", 310),
    ORDERDISCOUNTAMT("ORDER_DISCOUNT_AMT", "订单优惠金额", 320),
    ADJUSTAMT("ADJUST_AMT", "调整金额", 330),
    SHIPAMT("SHIP_AMT", "配送费用", 340),
    SERVICEAMT("SERVICE_AMT", "服务费", 350),
    ORDERAMT("ORDER_AMT", "订单总额", 360),
    RECEIVEDAMT("RECEIVED_AMT", "已收金额", 370),
    CONSIGNAMT("CONSIGN_AMT", "代销结算金额", 380),
    CONSIGNSHIPAMT("CONSIGN_SHIP_AMT", "代销运费", 390),
    RECEIVAMT("AMT_RECEIVE", "应收金额", 400),
    CODAMT("COD_AMT", "到付代收金额", 410),
    JDRECEIVEAMT("JD_RECEIVE_AMT", "应收平台金额（京东）", 420),
    JDSETTLEAMT("JD_SETTLE_AMT", "京东结算金额", 430);
    //AUDIT("审核人"),
   /* AUTOAUDITSTATUS("autoAuditStatusName", "自动审核状态", 440),
    MERGESOURCECODE("MERGE_SOURCE_CODE", "合并后发货的订单号", 450),
    NEWORDER("MERGE_ORDER_ID", "合并新单号", 460),
    SPLITORDER("SPLIT_ORDER_ID", "拆分原单单号", 470),

    ORDERFROM("ORDER_SOURCE", "订单来源", 490),
    ORDERDATE("ORDER_DATE", "下单时间", 500),
    STORENAME("CP_C_STORE_ENAME", "下单店仓", 510),
    RECEIVEREMAIL("RECEIVER_EMAIL", "买家邮件地址", 520),
    RECEIVERZIP("RECEIVER_ZIP", "收货人的邮编", 530),
    ORIORDER("ORIG_ORDER_ID", "原始订单号", 540),
    ORIORDERRETURN("ORIG_RETURN_ORDER_ID", "原始退货单号", 550),
    ISTODRP("isToDrpName", "是否生成调拨零售", 560),
    OPERATEAMT("OPERATE_AMT", "操作费", 570),
    LOGISTICSCOST("LOGISTICS_COST", "物流成本", 580),
    ISGENVOICENOTICE("isGeninvoiceNoticeName", "是否生成开票通知", 590),
    ENDTIME("END_TIME", "交易结束时间", 600),
    SENDTIME("SEND_TIME", "预计发货时间", 610);
    */

    //OCCUPYSTATUS("occupyStatusName", "订单占单状态", 660);

    String key;
    String title;
    int sort;

    OcOrderTableHeaderEnum(String k, String tx, int n) {
        this.key = k;
        this.title = tx;
        this.sort = n;
    }

    public String getTitle() {
        return title;
    }

    public int getSort() {
        return sort;
    }

    public String getKey() {
        return key;
    }

    /**
     * 转换为QueryOrderTableHeaderResult
     *
     * @return list
     */
    public static List<QueryOrderTableHeaderResult> convertToModel() {
        List<QueryOrderTableHeaderResult> list = new ArrayList<>();
        for (OcOrderTableHeaderEnum o : OcOrderTableHeaderEnum.values()) {
            QueryOrderTableHeaderResult qothr = new QueryOrderTableHeaderResult();
            qothr.setKey(o.getKey());
            qothr.setTitle(o.getTitle());
            qothr.setSort(o.getSort());
            list.add(qothr);
        }
        return list;
    }
}
