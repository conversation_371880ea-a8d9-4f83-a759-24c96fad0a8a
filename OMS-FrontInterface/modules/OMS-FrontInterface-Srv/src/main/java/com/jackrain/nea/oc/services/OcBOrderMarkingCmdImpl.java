package com.jackrain.nea.oc.services;

import com.jackrain.nea.oc.oms.api.OcBOrderMarkingCmd;
import com.jackrain.nea.oc.oms.model.request.OrderMarkingRequest;
import com.jackrain.nea.oc.oms.services.OcBOrderMarkingService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 订单打标
 *
 * @date 2022/6/14
 * @author: ming.fz
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBOrderMarkingCmdImpl implements OcBOrderMarkingCmd {

    @Autowired
    OcBOrderMarkingService service;

    @Override
    public ValueHolderV14 orderMarking(OrderMarkingRequest param, User user) {
        return service.orderMarking(param, user);
    }

    @Override
    public ValueHolderV14 clearMarking(OrderMarkingRequest param, User user) {
        return service.clearMarking(param, user);
    }
}