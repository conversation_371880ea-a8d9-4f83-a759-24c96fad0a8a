package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.burgeon.mq.core.DefaultProducerSend;
import com.burgeon.mq.model.MqSendResult;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.ac.model.result.AcStExpressQueryResult;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpLogistics;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.carpoolOrder.OmsOrderCarpoolOrderService;
import com.jackrain.nea.oc.oms.config.OmsModifyOrderLogisticsConfig;
import com.jackrain.nea.oc.oms.constant.Mq5Constants;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OcOrderCheckBoxEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsPayType;
import com.jackrain.nea.oc.oms.model.enums.OmsWmsCancelStatus;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcModifyOrderLogisticsModel;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.UserImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;


@Slf4j
@Component
public class ModifyOrderLogisticsService {


//    @Autowired
//    private R3MqSendHelper r3MqSendHelper;
    @Autowired
    private DefaultProducerSend defaultProducerSend;


    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private OmsModifyOrderLogisticsConfig logisticsConfig;

    @Autowired
    private OcBOrderMapper orderMapper;

    @Autowired
    UpdateOrderInfoService updateOrderInfoService;

    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Autowired
    private OmsOrderDistributeLogisticsService logisticsService;
    @Resource
    private OmsOrderCarpoolOrderService omsOrderCarpoolOrderService;


    /**
     * description:发送mq
     *
     * @Author: liuwenjin
     * @Date 2022/10/6 17:04
     */
    public ValueHolderV14 modifyOrderLogistics(List<Long> ids, Long cLogisticsId, User loginUser) {
        ValueHolderV14 vh;
        try {
            // 1. check
            AssertUtil.notEmpty(ids, "请选择需要修改的单据");
            AssertUtil.notNull(cLogisticsId, "请选择物流公司");
            // 2. query check
            CpLogistics logistic = getLogistic(cLogisticsId);
            AssertUtil.notNull(logistic, "当前物流公司，请重新录入");
            // 3. send message
            ValueHolderV14 sendResult = sendMessageProcessor(ids, logistic, loginUser);
            return sendResult;
        } catch (Exception e) {
            vh = new ValueHolderV14(ResultCode.FAIL, "");
            if (e instanceof NDSException) {
                vh.setMessage(optimizeExpMsg.apply(e));
            } else {
                vh.setMessage("修改物流异常");
                log.error(LogUtil.format("modifyOrderLogistics.exp:{}", "modifyOrderLogistics"), Throwables.getStackTraceAsString(e));
            }
        }
        return vh;
    }
    /**
     * description:mq发送类
     * @Author:  liuwenjin
     * @Date 2022/10/6 19:39
     */
    private ValueHolderV14 sendMessageProcessor(List<Long> ids, CpLogistics logistic, User loginUser) {
        OcModifyOrderLogisticsModel model = new OcModifyOrderLogisticsModel();
        model.setTopic(Mq5Constants.TOPIC_R3_OC_OMS_MODIFY_LOGISTICS);
        model.setTag(Mq5Constants.TAG_R3_OC_OMS_MODIFY_LOGISTICS);
        model.setLogisticsId(logistic.getId());
        model.setLogisticsCode(logistic.getEcode());
        model.setLogisticsName(logistic.getEname());
        model.setUser((UserImpl) loginUser);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("sendMessageProcessor.{}", "ModifyOrderLogisticsService"), JSON.toJSONString(model));
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("sendMessageProcessor.{}", "ModifyOrderLogisticsService"), JSON.toJSONString(model));
        }
        ValueHolderV14 vh = new ValueHolderV14(ResultCode.SUCCESS, "");
        int exp = 0;
        int success = 0;
        int size = ids.size();
        List<Long> expList = new ArrayList<>();
        for (Long key : ids) {
            model.setOrderId(key);
            boolean isSendSuccess = sendMessage(model);
            if (isSendSuccess) {
                success++;
            } else {
                exp++;
                expList.add(key);
            }
        }
        if (exp == size) {
            vh.setCode(ResultCode.FAIL);
            vh.setData(expList);
            vh.setMessage("消息发送失败");
            return vh;
        }
        if (success == size) {
            vh.setMessage("消息发送成功,请稍后查看结果");
            return vh;
        }
        vh.setMessage("消息发送,成功:" + success + "条, 失败:" + exp + "条");
        vh.setData(expList);
        return vh;

    }
    /**
     * description:发送mq
     * @Author:  liuwenjin
     * @Date 2022/10/6 17:36
     */
    private boolean sendMessage(OcModifyOrderLogisticsModel model) {
        try {
            Long id = model.getOrderId();
            MqSendResult result = defaultProducerSend.sendTopic(model.getTopic(), model.getTag(), JSON.toJSONString(model), null);
//            String msgId = r3MqSendHelper.sendMessage(JSON.toJSONString(model), model.getTopic(), model.getTag());
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("modifyWarehouse.{}, msgId:{}", "ModifyOrderLogisticsService", id), id, result.getMessageId());
            }
            return true;
        } catch (Exception e) {
            log.error(LogUtil.format("sendMessage.Producer.SendMQ.SendException: {}"
                    , model.getTopic() + ":" + model.getTag()), Throwables.getStackTraceAsString(e));
        }
        return false;
    }

    /**
     * description:查找物流
     * @Author:  liuwenjin
     * @Date 2022/10/6 17:10
     */
    private CpLogistics getLogistic(Long cLogisticsId) {
        //调用接口查询物流信息
        CpLogistics cpLogistics = null;
        try {
            cpLogistics = cpRpcService.checkCpLogistic(cLogisticsId);
        } catch (Exception e) {
            log.error(LogUtil.format("调用cp物流出错 错误信息{}"), Throwables.getStackTraceAsString(e));
        }
        return cpLogistics;
    }

    /**
     * description:消费类
     * @Author:  liuwenjin
     * @Date 2022/10/6 19:39
     */
    public void modifyOrderLogisticHandler(String messageBody, String messageKey) {
        /* message parser */
        OcModifyOrderLogisticsModel model = parseMessage(messageBody, messageKey);
        if (model == null) {
            return;
        }
        // modify
        modifyLogisticProcessing(model);
    }
    /**
     * description:修改物流消费
     * @Author:  liuwenjin
     * @Date 2022/10/6 19:40
     */
    private void modifyLogisticProcessing(OcModifyOrderLogisticsModel model) {
        User user = model.getUser();
        Long id = model.getOrderId();
        Long logisticsId = model.getLogisticsId();
        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(id);
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        String billNo = "";
        try {
            AssertUtil.notNull(user, "用户信息为空");
            AssertUtil.isTrue(logisticsId != null && model.getLogisticsCode() != null && model.getLogisticsName() != null, "目标物流信息缺失");
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("modifyWarehouse.{},warehouse:{},user:{}", "ModifyOrderLogisticsService"),
                        id, model.getLogisticsId(), user.getEname());
            }
            JSONObject failMessage = new JSONObject();
            CpLogistics cpLogistics = new CpLogistics();
            cpLogistics.setId(model.getLogisticsId());
            cpLogistics.setEcode(model.getLogisticsCode());
            cpLogistics.setEname(model.getLogisticsName());
            List<Object> failList = new ArrayList<>();
            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                OcBOrder ocBorder = orderMapper.selectById(id);
                if (ocBorder != null && !(logisticsId.equals(ocBorder.getCpCLogisticsId()))) {
                    Integer status = ocBorder.getOrderStatus();
                    List<Integer> list = Arrays.asList(OcOrderCheckBoxEnum.CHECKBOX_UNCONFIRMED.getVal(), OcOrderCheckBoxEnum.CHECKBOX_OUT_OF_STOCK.getVal(), OcOrderCheckBoxEnum.CHECKBOX_IN_DISTRIBUTION.getVal());
                    AssertUtil.assertException(!list.contains(status), "当前单据状态,不允许修改发货仓库");
                    billNo = ocBorder.getBillNo();
                    Integer wmsCancelStatus = ocBorder.getWmsCancelStatus();
                    //校验所选物流
                    if (!updateOrderInfoService.checkCpLogistics(ocBorder, logisticsId)) {
                        throw new NDSException(id +"所选物流公司未维护，不允许修改！");
                    }

                    //校验快递费用省是否配置
                    this.expressCheck(model.getLogisticsId(), id, ocBorder.getCpCPhyWarehouseId(), ocBorder.getCpCRegionProvinceId());

                    //若存在，判断订单平台是否为京东且支付方式为“货到付款”，若是，提示“货到付款的京东配送不允许修改物流！”
                    if (ocBorder.getPlatform().equals(PlatFormEnum.JINGDONG.getCode()) && ocBorder.getPayType() == OmsPayType.CASH_ON_DELIVERY.toInteger()) {
                        throw new NDSException(id +"货到付款的京东配送不允许修改物流！");
                        //待审核、缺货，则点击【确认】按钮，则更新主表数据和日志信息
                    } else if (status == OcOrderCheckBoxEnum.CHECKBOX_UNCONFIRMED.getVal() || status == OcOrderCheckBoxEnum.CHECKBOX_OUT_OF_STOCK.getVal()) {
                        //获取修改之前的物流名称
                        String preName = ocBorder.getCpCLogisticsEname();
                        //获取修改之后的物流名称
                        String afterName = model.getLogisticsName();
                        ocBorder.setCpCLogisticsId(logisticsId);
                        ocBorder.setCpCLogisticsEcode(cpLogistics.getEcode());
                        ocBorder.setCpCLogisticsEname(cpLogistics.getEname());
                        // 自动打标：物流改单
                        ocBorder.setIsModifiedOrder(1);
                        //寻源失败标识
                        ocBorder.setIsOccupyStockFail(OcBOrderConst.IS_STATUS_IN);
                        //去除订单异常标签
                        ocBorder.setIsException(OcBOrderConst.IS_STATUS_IN);
                        ocBorder.setExceptionType("");
                        ocBorder.setExceptionExplain("");
                        updateOrderInfoService.updateOrderInfo(id, ocBorder, model.getUser());
                        //更新订单日志
                        omsOrderLogService.addUserOrderLog(id, billNo, OrderLogTypeEnum.LOGISTICS_UPDATE.getKey(), "物流公司：修改前：" + preName + "，修改后：" + afterName + "", "", "", user);
                        //清空拼车单号
                        if (StringUtils.isNotEmpty(ocBorder.getCarpoolNo())) {
                            omsOrderCarpoolOrderService.cancelCarpoolNo(Lists.newArrayList(ocBorder.getCarpoolNo()), user, false);
                        }
                        //配货中
                    } else if (status == OcOrderCheckBoxEnum.CHECKBOX_IN_DISTRIBUTION.getVal()) {
                        //WMS撤回状态为已撤回
                        if (wmsCancelStatus == OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_YES.toInteger()) {
                            try {
                                ApplicationContextHandle.getBean(UpdateOrderInfoService.class).logisticDealWmsCanceled(model.getUser(), failList, cpLogistics, 0, failMessage, ocBorder);
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            //WMS撤回状态为未撤回或撤回失败
                        } else {
                            throw new NDSException(id +"订单在WMS中未取消，不允许修改物流公司，建议先撤回WMS再进行修改物流公司！！");
                        }
                    }
                } else {
                    throw new NDSException(id +"物流公司与当前一致，无需修改！");
                }

            } else {
                throw new NDSException(id +"当前订单其他人在操作，请稍后再试!");
            }
        } catch (Exception e) {
            String expMessage = optimizeExpMsg.apply(e);
            if (e instanceof NDSException) {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("modifyWarehouse.{}, fail:{}", "ModifyOrderLogisticsService"),
                            id, expMessage);
                }
            } else {
                log.error(LogUtil.format("modifyWarehouse.{},exp:{}", "ModifyOrderLogisticsService"),
                        id, Throwables.getStackTraceAsString(e));
            }
            addOrderLog(id, billNo, expMessage, user);
        } finally {
            redisLock.unlock();
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("modifyWarehouse.{}, end", "ModifyOrderLogisticsService"), id);
            }
        }
    }

    public void expressCheck(Long logisticsId, Long id, Long warehouseId, Long provinceId) {
        ValueHolderV14<List<CpLogistics>> holderV14 = cpRpcService.queryLogisticsByIdsIgnoreStatus(Lists.newArrayList(logisticsId));
        if (!holderV14.isOK()) {
            throw new NDSException(id + "查询物流公司信息失败，请稍后重试！");
        }
        List<CpLogistics> logistics = holderV14.getData();
        if (CollectionUtils.isEmpty(logistics)) {
            throw new NDSException(id + "未查询到物流公司信息!");
        }
        CpLogistics logi = logistics.get(0);
        if (Objects.isNull(logi)){
            throw new NDSException(id + ",未查询到物流公司信息!");
        }
        //1-快递
        if (logi.getType() != 1) {
            return;
        }

        List<AcStExpressQueryResult> results;
        try {
            results = logisticsService.queryStExpress(id, new Date(), warehouseId, provinceId, Lists.newArrayList(logisticsId));
        } catch (Exception e) {
            throw new NDSException(id + "查询目的省份报价快递失败！");
        }
        if (CollectionUtils.isEmpty(results)) {
            throw new NDSException(id + "查询目的省份报价快递失败，空数据！");
        }
        if (OcBOrderConst.IS_STATUS_IN.equals(results.get(0).getOrderAddressCheck())) {
            throw new NDSException(id + "目的省份未维护快递报价，不允许操作！");
        }
    }

    /**
     * description:解析消息
     * @Author:  liuwenjin
     * @Date 2022/10/6 19:41
     */
    private OcModifyOrderLogisticsModel parseMessage(String messageBody, String messageKey) {
        try {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("modifyWarehouse.msgId.{},msgKey:{}", "ModifyOrderLogisticsService"), messageKey, messageKey);
            }
            OcModifyOrderLogisticsModel model = JSON.parseObject(messageBody, OcModifyOrderLogisticsModel.class);
            return model;
        } catch (Exception e) {
            log.error(LogUtil.format("modifyWarehouse.msgId.{},msgKey:{},exp:{}",
                    "ModifyOrderLogisticsService"), messageKey, messageKey, Throwables.getStackTraceAsString(e));
        }
        return null;
    }

    /**
     * optimize exception
     */
    private Function<Exception, String> optimizeExpMsg = e -> {
        if (e == null) {
            return "null exception";
        }
        String message = e.getMessage();
        if (message == null) {
            return "null message";
        }
        return message.length() > 200 ? message.substring(0, 200) : message;
    };

    private void addOrderLog(Long id, String bilNo, String message, User user) {
        try {
            omsOrderLogService.addUserOrderLog(
                    id, bilNo, OrderLogTypeEnum.LOGISTICS_UPDATE.getKey(), message, "", "", user);
        } catch (NDSException e) {
            log.error(LogUtil.format("modifyWarehouse.{},exp:{}", "ModifyWarehouseService"),
                    id, Throwables.getStackTraceAsString(e));
        }
    }

}
