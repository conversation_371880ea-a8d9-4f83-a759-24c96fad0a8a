package com.jackrain.nea.oc.oms.model.table.task;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@TableName(value = "oc_b_tobeconfirmed_task")
@Data
public class OcBToBeConfirmedTask implements Serializable {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @J<PERSON><PERSON>ield(name = "ORDER_ID")
    private Long orderId;

    @J<PERSON><PERSON>ield(name = "STATUS")
    private int status;

    @J<PERSON><PERSON>ield(name = "CREATIONDATE")
    private Date creationdate;

    @J<PERSON><PERSON>ield(name = "MODIFIEDDATE")
    private Date modifieddate;

    @JSONField(name = "ISACTIVE")
    private String isactive;
}