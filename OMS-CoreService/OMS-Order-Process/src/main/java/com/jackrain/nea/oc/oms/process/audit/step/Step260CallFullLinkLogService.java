package com.jackrain.nea.oc.oms.process.audit.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.services.OcBOrderLinkService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @author: heliu
 * @since: 2019/4/17
 * create at : 2019/4/17 11:54
 */
@Step(order = 260, description = "判断订单平台类型是否为淘宝,若是,则调用全链路日志")
@Slf4j
public class Step260CallFullLinkLogService extends BaseAuditOrderProcessStep implements IOmsOrderProcessStep<OcBOrderRelation> {

    @Autowired
    private OcBOrderLinkService ocBOrderLinkService;

    @Override
    public ProcessStepResult<OcBOrderRelation> startProcess(OcBOrderRelation orderInfo, ProcessStepResult preStepResult,
                                                            boolean isAutoMakeup, User operateUser) {
//        //判断订单平台类型是否为淘宝（天猫）2，如果是，则异步调用全链路服务
//        if (PlatFormEnum.TAOBAO.getCode() == orderInfo.getOrderInfo().getPlatform()) {
//            OcBOrderLink ocBOrderLink = new OcBOrderLink();
//            ocBOrderLink.setOcBOrderId(orderInfo.getOrderInfo().getId());
//            ocBOrderLink.setTid(orderInfo.getOrderInfo().getTid());
//            ocBOrderLink.setBackflowStatus(BackflowStatus.QIMEN_ERP_CHECK.parseValue());
//            ocBOrderLink.setExtAttribute("");
//            ocBOrderLink.setPlatform(PlatformCode.MAIN.parseValue());
//            //卖家昵称
//            ocBOrderLink.setSellerNick(orderInfo.getOrderInfo().getCpCShopSellerNick());
//            ocBOrderLink.setSyncStatus(SyncStatus.UNSYNC.toInteger());
//            ocBOrderLink.setSyncTime(new Date());
//            ocBOrderLink.setErrorInfo("");
//            ocBOrderLinkService.addOrderFinkLog(ocBOrderLink, operateUser);
//        }
        return new ProcessStepResult<>(StepStatus.FINISHED, "订单审核流程结束");
    }
}