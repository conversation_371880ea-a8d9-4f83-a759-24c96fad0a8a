package com.jackrain.nea.oc.oms.model.enums;


import com.alibaba.fastjson.JSONArray;
import com.jackrain.nea.oc.oms.model.result.QueryOrderCheckBoxResult;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 订单列表-下拉列表状态选项值
 *
 * @author: xiwen.z
 * create at: 2019/3/8 0008
 */
public enum OcOrderCheckBoxEnum {

    CHECKBOX_ALL("全部", 0),
    CHECKBOX_UNCONFIRMED("待审核", 1),
    CHECKBOX_OUT_OF_STOCK("待寻源", 2),
    CHECKBOX_AUDITED("已审核", 3),
    CHECKBOX_IN_DISTRIBUTION("配货中", 4),
    CHECKBOX_WAREHOUSE_DELIVERY("仓库发货", 5),
    CHECKBOX_PLATFORM_DELIVERY("平台发货", 6),
    CHECKBOX_CANCELLED("已取消", 7),
    CHECKBOX_SYSTEM_INVALIDATION("系统作废", 8),
    CHECKBOX_ADVANCE_SALE("预售", 9),
    CHECKBOX_SUBSTITUTION("代发", 10),
    CHECKBOX_LOGISTICS_DELIVERED("物流已送达", 11),
    CHECKBOX_TRANSACTION_COMPLETED("交易完成", 12),
    CHECKBOX_UNPAID("未付款", 13),
    CHECKBOX_PENDING_WMS("待传wms", 21),
    CHECKBOX_OCCUPY_IN("寻源中", 22),
    CHECKBOX_PENDING_ALLOCATED("待分配", 50),
    CHECKBOX_PRE_SALE_DELIVERY("预售待发货", 51),
    CHECKBOX_ADVANCE_SHORTAGE("预售缺货", 52);

    String key;
    int val;

    OcOrderCheckBoxEnum(String k, int v) {
        this.key = k;
        this.val = v;
    }

    public String getKey() {
        return key;
    }

    public int getVal() {
        return val;
    }

    public Integer integerVal() {
        return val;
    }

    /**
     * 转化成QueryOrderCheckBoxResult
     *
     * @return list<QueryOrderCheckBoxResult>
     */
    public static List<QueryOrderCheckBoxResult> toQueryOrderCheckBoxResult() {
        List<QueryOrderCheckBoxResult> list = new ArrayList<>();
        for (OcOrderCheckBoxEnum e : OcOrderCheckBoxEnum.values()) {
            QueryOrderCheckBoxResult o = new QueryOrderCheckBoxResult();
            o.setLabel(e.getKey());
            o.setValue(String.valueOf(e.getVal()));
            list.add(o);
        }
        return list;
    }

    /**
     * 提取全部状态值
     *
     * @return jsonArray
     */
    public static JSONArray joinAllStatusVal() {
        JSONArray jsArys = new JSONArray();
        for (OcOrderCheckBoxEnum e : OcOrderCheckBoxEnum.values()) {
            jsArys.add(e.getVal());
        }
        return jsArys;
    }

    /**
     * 根据状态值,获取状态名
     *
     * @param integer integer
     * @return String
     */
    public static String enumToStringByValue(Integer integer) {
        String s = "";
        if (integer == null) {
            return s;
        }
        for (OcOrderCheckBoxEnum e : OcOrderCheckBoxEnum.values()) {
            if (e.getVal() == integer) {
                s = e.getKey();
                break;
            }
        }
        return s;
    }

    /**
     * 转化为hashMap
     *
     * @return map
     */
    public static Map convertAllToHashVal() {
        Map<Integer, String> m = new HashMap<>();
        for (OcOrderCheckBoxEnum o : OcOrderCheckBoxEnum.values()) {
            m.put(o.getVal(), o.getKey());
        }
        return m;
    }

}


