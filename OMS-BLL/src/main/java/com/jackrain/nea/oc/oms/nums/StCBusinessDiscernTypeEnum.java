package com.jackrain.nea.oc.oms.nums;

import com.jackrain.nea.exception.NDSException;
import lombok.Getter;

import java.util.Objects;

/**
 * @program: r3-oc-oms
 * @description: 业务单据类型
 * @author: caomalai
 * @create: 2022-07-14 15:19
 **/
public enum StCBusinessDiscernTypeEnum {
    PLATFORM(1,"交易平台"),
    MATERIAL_GROUP(2,"物料组"),
    SAP_BILL_TYPE(3,"SAP单据类型"),
    PAY_ACTUAL(4,"商品实付");

    @Getter
    private Integer value;

    @Getter
    private String desc;

    StCBusinessDiscernTypeEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static String getDescByValue(Integer value) {
        StCBusinessDiscernTypeEnum[] values = StCBusinessDiscernTypeEnum.values();
        for (StCBusinessDiscernTypeEnum nodeEnum : values) {
            if (Objects.equals(nodeEnum.value, value)) {
                return nodeEnum.desc;
            }
        }
        throw new NDSException("错误的节点配置:" + value);
    }
}
