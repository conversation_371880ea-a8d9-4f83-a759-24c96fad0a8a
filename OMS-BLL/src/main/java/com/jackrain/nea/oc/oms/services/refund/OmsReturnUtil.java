package com.jackrain.nea.oc.oms.services.refund;

import com.jackrain.nea.cpext.model.LogisticsInfo;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.model.enums.LogTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderHoldReasonEnum;
import com.jackrain.nea.oc.oms.model.resources.SysNotesConstant;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.nums.OcOrderRefundStatusEnum;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.OcBOrderHoldService;
import com.jackrain.nea.oc.oms.services.OcBOrderTheAuditService;
import com.jackrain.nea.oc.oms.services.OmsMarkCancelService;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.Tools;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2020/11/25 1:30 下午
 * @Version 1.0
 * <p>
 * 退款转换
 */
@Slf4j
@Component
public class OmsReturnUtil {

    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private OcBOrderTheAuditService ocBOrderTheAuditService;
    @Autowired
    private OmsMarkCancelService omsMarkCancelService;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private OcBOrderHoldService ocBOrderHoldService;
    @Autowired
    private OmsOrderService omsOrderService;
    @Autowired
    private CpRpcService cpRpcService;

    /**
     * 订单状态处理
     *
     * @param operateUser
     * @param ocBOrder
     */
    public boolean orderHandle(User operateUser, OcBOrder ocBOrder) {

        Integer status = ocBOrder.getOrderStatus();
        if (OmsOrderStatus.TO_BE_ASSIGNED.toInteger().equals(status) || OmsOrderStatus.PENDING_WMS.toInteger().equals(status) || OmsOrderStatus.OCCUPY_IN.toInteger().equals(status)) {
            throw new NDSException("订单状态为待分配、寻源中或者待传wms,等待下次转换");
        } else if (OmsOrderStatus.CHECKED.toInteger().equals(status) || OmsOrderStatus.IN_DISTRIBUTION.toInteger().equals(status)) {
            // 订单状态为已审核或者配货中 调用反审核
            if (this.toExamineOrderLock(ocBOrder, operateUser)) {
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                        OrderLogTypeEnum.ORDER_INTERCEPTION.getKey(), "买家申请退款,反审核成功", null,
                        null, operateUser);
            } else {
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                        OrderLogTypeEnum.ORDER_INTERCEPTION.getKey(), "买家申请退款,反审核失败", null,
                        null, operateUser);
                String remark1 = SysNotesConstant.SYS_REMARK46;
                throw new NDSException(remark1);
            }
        }
        return true;
    }


    /**
     * 已审核订单调用反审核接口
     *
     * @param ocBOrder
     */
    public boolean toExamineOrderLock(OcBOrder ocBOrder, User user) {
        Long id = ocBOrder.getId();
        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(id);
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisLock.tryLock(0, TimeUnit.MILLISECONDS)) {
                ValueHolderV14 holderV14 = new ValueHolderV14();
                boolean isSuccess = ocBOrderTheAuditService.updateOrderInfo(user, holderV14, id, LogTypeEnum.NOT_CAPTURED_SCENE.getType());
                if (isSuccess) {
                    //反审核成功  将订单状态改为 待审核
                    ocBOrder.setOrderStatus(OmsOrderStatus.UNCONFIRMED.toInteger());
                }
                return isSuccess;
            } else {
                return false;
            }
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 调用反审核失败", e);
            return false;
        }finally {
            redisLock.unlock();
        }

    }



    /**
     * 已审核订单调用反审核接口
     *
     * @param ocBOrder
     */
    public boolean toExamineOrder(OcBOrder ocBOrder, User user) {
        try {
            Long id = ocBOrder.getId();
            ValueHolderV14 holderV14 = new ValueHolderV14();
            boolean isSuccess = ocBOrderTheAuditService.updateOrderInfo(user, holderV14, id, LogTypeEnum.NOT_CAPTURED_SCENE.getType());
            if (isSuccess) {
                //反审核成功  将订单状态改为 待审核
                ocBOrder.setOrderStatus(OmsOrderStatus.UNCONFIRMED.toInteger());
            }
            return isSuccess;
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 调用反审核失败", e);
            return false;
        }

    }

    /**
     * 标记退款完成服务
     *
     * @param orderId
     * @param itemId
     * @param user
     * @return
     */
    public boolean signRefundNew(Long orderId, List<Long> itemId, User user, OrderHoldReasonEnum reasonEnum) {
        ValueHolderV14 holderV14 = omsMarkCancelService.markCancel(orderId, itemId, user, reasonEnum, Boolean.FALSE);
        int code = Tools.getInt(holderV14.getCode(), -1);
        return code == 0;
    }


    /**
     * 订单退款完成后解挂的处理
     *
     * @param ocBOrder
     */
    public void handleRefundComplete(OcBOrder ocBOrder) {
        //判断所有订单是否全部退款完成
        List<OcBOrderItem> orderItems = ocBOrderItemMapper.selectOrderItemListAndReturn(ocBOrder.getId());
        if (CollectionUtils.isEmpty(orderItems)) {
            OcBOrder order = new OcBOrder();
            order.setIsInreturning(0);
            order.setId(ocBOrder.getId());
            omsOrderService.updateOrderInfo(order);
            // 修改hold单状态 使用HOLD单方法修改
            //是否已经拦截
            //修改hold单状态 使用HOLD单方法修改
            order.setIsInterecept(0);
            ocBOrderHoldService.holdOrUnHoldOrder(order, OrderHoldReasonEnum.REFUND_HOLD);
            return;
        }
        //判断订单时候还有申请退款的明细
        orderItems = orderItems.stream().filter(p -> (
                        p.getRefundStatus().equals(OcOrderRefundStatusEnum.WAIT_SELLER_AGREE.getVal())
                )
        ).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderItems)) {
            OcBOrder order = new OcBOrder();
            order.setIsInreturning(0);
            order.setId(ocBOrder.getId());
            omsOrderService.updateOrderInfo(order);
            // 修改hold单状态 使用HOLD单方法修改
            //是否已经拦截
            //修改hold单状态 使用HOLD单方法修改
            order.setIsInterecept(0);
            ocBOrderHoldService.holdOrUnHoldOrder(order, OrderHoldReasonEnum.REFUND_HOLD);

        }
    }


    /**
     * 根据物流公司查看物流信息
     *
     * @param returnOrder
     * @param buyerLogisticName
     */
    public void setLogisticInfo(OcBReturnOrder returnOrder, String buyerLogisticName) {
        if (StringUtils.isNotEmpty(buyerLogisticName)) {
            LogisticsInfo logisticsInfo = cpRpcService.selectLogisticsInfoByName(buyerLogisticName);
            if (logisticsInfo != null) {
                returnOrder.setCpCLogisticsEcode(logisticsInfo.getCode());
                returnOrder.setCpCLogisticsId(logisticsInfo.getId());
            }
        }
    }

    /**
     * 根据平台物流公司编码查看物流信息
     *
     * @param returnOrder
     * @param buyerLogisticName
     */
    public void setLogisticInfoByCarrierCode(OcBReturnOrder returnOrder, String buyerLogisticName) {
        if (StringUtils.isNotEmpty(buyerLogisticName)) {
            LogisticsInfo logisticsInfo = cpRpcService.selectLogisticsInfoByCarrierCode(buyerLogisticName);
            if (logisticsInfo != null) {
                returnOrder.setCpCLogisticsEcode(logisticsInfo.getCode());
                returnOrder.setCpCLogisticsId(logisticsInfo.getId());
                returnOrder.setCpCLogisticsEname(logisticsInfo.getName());
            }
        }
    }
}
