package com.jackrain.nea.oc.oms.task.lockorder;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.xxl.job.starter.helper.R3XxlJobParamHelper;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.services.IpOrderLockQueryService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: chenxiulou
 * @description: 预计解锁时间初始化
 * @since: 2019-09-25
 * create at : 2019-09-25 9:55
 */
@Slf4j
@Component
public class AutoUpdateUnlockTimeTask extends BaseR3Task implements IR3Task {
    @Autowired
    private IpOrderLockQueryService lockQueryService;
    private static final Integer totalConst = 2000;

    @Override
    @XxlJob("AutoUpdateUnlockTimeTask")
    public RunTaskResult execute(JSONObject params) {
        params = R3XxlJobParamHelper.xxlParam2R3Json();
        RunTaskResult result = new RunTaskResult();
        log.debug(LogUtil.format("定时任务AutoUpdateUnlockTimeTask开始执行！"));
        int total = totalConst;
        try {
            total = params.getInteger("total");
        } catch (Exception ex) {
            log.error(LogUtil.format("获取Lts参数params：total异常！"));
        }
        try {
            ValueHolderV14 v14 = lockQueryService.dealLockOrderLockTime(total);
            result.setSuccess(true);
            result.setMessage(v14.getMessage());
        } catch (Exception ex) {
            //发生异常 所有数据返回最开始的状态
            log.error(LogUtil.format("AutoRefundInTimeOutTask.Execute.异常: {}"), Throwables.getStackTraceAsString(ex));
            result.setSuccess(false);
            result.setMessage(ex.getMessage());

        }
        return result;
    }

}
