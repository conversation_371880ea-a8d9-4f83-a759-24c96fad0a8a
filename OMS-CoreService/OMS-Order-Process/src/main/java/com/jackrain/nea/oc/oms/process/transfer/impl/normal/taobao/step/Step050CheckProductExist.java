package com.jackrain.nea.oc.oms.process.transfer.impl.normal.taobao.step;

import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.AbnormalTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoOrder;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoOrderItemEx;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 检查订单商品是否在系统中
 *
 * @author: 易邵峰
 * @since: 2019-01-20
 * create at : 2019-01-20 02:48
 */
@Step(order = 50, description = "检查订单商品是否在系统中")
@Slf4j
@Component
public class Step050CheckProductExist extends BaseTaobaoOrderProcessStep
        implements IOmsOrderProcessStep<IpTaobaoOrderRelation> {

    @Override
    public ProcessStepResult<IpTaobaoOrderRelation> startProcess(IpTaobaoOrderRelation orderInfo,
                                                                 ProcessStepResult preStepResult,
                                                                 boolean isAutoMakeup, User operateUser) {
        String orderNo = orderInfo.getOrderNo();
        boolean hasErrorInfo = false;
        boolean isTmallExpandCard=false;
        StringBuilder sbErrorInfo = new StringBuilder();
        IpBTaobaoOrder taobaoOrder = orderInfo.getTaobaoOrder();
        Long shopId = taobaoOrder.getCpCShopId();

        for (IpBTaobaoOrderItemEx tbOrderItem : orderInfo.getTaobaoOrderItemList()) {
            ProductSku skuInfo = null;

            String skuId = tbOrderItem.getOuterSkuId();
            if (StringUtils.isEmpty(skuId)) {
                skuId = tbOrderItem.getOuterIid();
            }
            if (StringUtils.isEmpty(skuId)) {
                skuId = tbOrderItem.getSkuId() + "";
                if (StringUtils.isEmpty(skuId)) {
                    hasErrorInfo = true;
                    sbErrorInfo.append("商品SkuId为空;");
                    sbErrorInfo.append("\r\n");
                } else {
                    List<ProductSku> productSkus = psRpcService.selectProductSkuByThirdCode(skuId, shopId);
                    if (CollectionUtils.isNotEmpty(productSkus) && productSkus.size() == 1){
                        skuInfo = productSkus.get(0);
                    }
                }
            } else {
                skuInfo = psRpcService.selectProductSku(skuId);
                if (skuInfo == null) {
                    List<ProductSku> productSkus = psRpcService.selectProductSkuByThirdCode(skuId, shopId);
                    if (CollectionUtils.isNotEmpty(productSkus) && productSkus.size() == 1){
                        skuInfo = productSkus.get(0);
                    }
                }
            }
            if (skuInfo == null) {
                hasErrorInfo = true;
                String msg = Resources.getMessage("不存在;");
                sbErrorInfo.append(tbOrderItem.getOuterSkuId()).append("--");
                sbErrorInfo.append(tbOrderItem.getOuterIid());
                sbErrorInfo.append(msg);
                sbErrorInfo.append("\r\n");
            } else {
                if ("Y".equals(skuInfo.getTmallExpandCard())) {
                    isTmallExpandCard = true;
                } else {
                    tbOrderItem.setProdSku(skuInfo);
                }

            }
        }

        if (hasErrorInfo) {
            String errorMessage = "商品数据不存在，退出转单操作";
            boolean updateStatusRes = ipTaoBaoOrderService.updateTaobaoOrderTransAndAbnormal(orderNo,
                    TransferOrderStatus.TRANSFEREXCEPTION, sbErrorInfo.toString(), AbnormalTypeEnum.SKU_ABNORMAL.getKey());
            if (!updateStatusRes) {
                errorMessage += ";更新状态失败=False";
            }
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        } else if(isTmallExpandCard){
            String operateMessage = "该订单为购买购物金的订单，直接更新为已转换!";
            boolean updateStatusRes = ipTaoBaoOrderService.updateTaobaoOrderTransStatus(orderNo,
                    TransferOrderStatus.TRANSFERRED,operateMessage);
            if (!updateStatusRes) {
                operateMessage += ";更新状态失败=False";
            }
            return new ProcessStepResult<>(StepStatus.FINISHED, operateMessage);
        }else {
            return new ProcessStepResult<>(StepStatus.SUCCESS, null, "检查商品是否存在成功，进入下一阶段",
                    Step060SaveOmsOrder.class);
        }
    }
}
