package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.OcBSapSalesDataRecordItemRequest;
import com.jackrain.nea.oc.oms.model.table.OcBSapSalesDataRecordItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface OcBSapSalesDataRecordItemMapper extends ExtentionMapper<OcBSapSalesDataRecordItem> {

    //批量查询明细
    @Select("<script>\n" +
            "select a.id,\n" +
            "       a.oc_b_sap_sales_data_record_id,\n" +
            "       a.SKU,\n" +
            "       a.LINE_TYPE,\n" +
            "       a.LINE_CATEGORY,\n"+
            "       a.QTY,\n" +
            "       a.UNIT,\n" +
            "       a.CP_C_STORE_ID,\n" +
            "       a.CP_C_STORE_ECODE,\n" +
            "       a.CP_C_STORE_ENAME,\n" +
            "       a.FACTORY_CODE,\n" +
            "       a.AMT,\n" +
            "       a.PRO_TYPE,\n" +
            "       a.BATCH,\n" +
            "       a.CYCLE_QTY,\n" +
            "       a.RESIDUE_QTY,\n" +
            "       a.PRICE_COST,\n" +
            "       b.MERGE_CODE,\n" +
            "       b.SUM_TYPE" +
            " from oc_b_sap_sales_data_record_item a, oc_b_sap_sales_data_record b where b.isactive = 'Y'\n" +
            " and a.isactive = 'Y' and a.oc_b_sap_sales_data_record_id = b.id" +
            " and b.id in\n" +
            " <foreach collection = 'ids' item = 'item' open = '(' separator = ',' close = ')'>" +
            " #{item}\n" +
            " </foreach>\n" +
            "</script>")
    List<OcBSapSalesDataRecordItemRequest> batchSelectItem(@Param("ids")List<Long> ids);

    @Select("SELECT * FROM oc_b_sap_sales_data_record_item WHERE oc_b_sap_sales_data_record_id = #{recordId}")
    List<OcBSapSalesDataRecordItem> selectByRecordId(@Param("recordId") Long recordId);
}