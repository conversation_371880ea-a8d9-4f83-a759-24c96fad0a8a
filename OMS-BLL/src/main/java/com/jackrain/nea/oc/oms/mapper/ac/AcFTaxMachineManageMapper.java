package com.jackrain.nea.oc.oms.mapper.ac;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.AcFTaxMachineManage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * @ClassName AcFTaxMachineManageMapper
 * @Description
 * @Date 2022/9/13 上午10:28
 * @Created by wuhang
 */
@Mapper
public interface AcFTaxMachineManageMapper extends ExtentionMapper<AcFTaxMachineManage> {

    @Select("select * from ac_f_tax_machine_manage where user_id = #{userId}")
    AcFTaxMachineManage queryByUserId(@Param("userId") Long userId);
}
