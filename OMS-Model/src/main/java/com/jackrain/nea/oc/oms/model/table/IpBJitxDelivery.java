package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.util.Date;

@TableName(value = "ip_b_jitx_delivery")
@Data
@Document(index = "ip_b_jitx_delivery", type = "ip_b_jitx_delivery")
public class IpBJitxDelivery extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "ORDER_SN")
    @Field(type = FieldType.Keyword)
    private String orderSn;

    @JSONField(name = "AVAILABLE_WAREHOUSES")
    @Field(type = FieldType.Keyword)
    private String availableWarehouses;

    @JSONField(name = "BUYER_ADDRESS")
    @Field(type = FieldType.Keyword)
    private String buyerAddress;

    @JSONField(name = "VENDOR_ID")
    @Field(type = FieldType.Keyword)
    private String vendorId;

    @JSONField(name = "STATUS")
    @Field(type = FieldType.Keyword)
    private String status;

    @JSONField(name = "STATUS_REMARK")
    @Field(type = FieldType.Keyword)
    private String statusRemark;

    @JSONField(name = "UPDATE_TIME")
    @Field(type = FieldType.Long)
    private Date updateTime;

    @JSONField(name = "SELLERNICK")
    @Field(type = FieldType.Keyword)
    private String sellernick;

    @JSONField(name = "CP_C_SHOP_ID")
    @Field(type = FieldType.Long)
    private Long cpCShopId;

    @JSONField(name = "MATCHTIME")
    @Field(type = FieldType.Long)
    private Date matchtime;

    @JSONField(name = "SYSREMARK")
    @Field(type = FieldType.Keyword)
    private String sysremark;

    @JSONField(name = "FEEDBACK_STATE")
    @Field(type = FieldType.Keyword)
    private String feedbackState;

    @JSONField(name = "WAREHOUSE")
    @Field(type = FieldType.Keyword)
    private String warehouse;

    @JSONField(name = "SYNSTATUS")
    @Field(type = FieldType.Long)
    private Long synstatus;

    @JSONField(name = "FEEDBACKMSGKEY")
    @Field(type = FieldType.Keyword)
    private String feedbackmsgkey;

    @JSONField(name = "IS_STORE_DELIVERY")
    @Field(type = FieldType.Integer)
    private Integer isStoreDelivery;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;
}