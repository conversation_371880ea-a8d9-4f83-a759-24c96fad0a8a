package com.jackrain.nea.ac.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.jackrain.nea.ac.model.*;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.mapper.ac.AcFPayableAdjustmentItemMapper;
import com.jackrain.nea.oc.oms.mapper.ac.AcFPayableAdjustmentMapper;
import com.jackrain.nea.oc.oms.util.ExportUtil;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ACEnumUtil;
import com.jackrain.nea.web.async.AsyncTaskBody;
import com.jackrain.nea.web.common.AsyncTaskManager;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * @author:洪艺安
 * @since: 2019/7/12
 * @create at : 2019/7/12 10:37
 */
@Component
@Slf4j
public class PayableAdjustmentExportService extends CommandAdapter {
    @Autowired
    private ExportUtil exportUtil;
    @Autowired
    private AcFPayableAdjustmentMapper acFPayableAdjustmentMapper;
    @Autowired
    private AcFPayableAdjustmentItemMapper acFPayableAdjustmentItemMapper;

    @Value("${r3.oss.endpoint}")
    private String endpoint;

    @Value("${r3.oss.accessKey}")
    private String accessKeyId;

    @Value("${r3.oss.secretKey}")
    private String accessKeySecret;

    @Value("${r3.oss.bucketName}")
    private String bucketName;

    @Value("${r3.oss.timeout}")
    private String timeout;

    @Autowired
    private AsyncTaskManager asyncTaskManager;
    @Autowired
    private ThreadPoolTaskExecutor commonTaskExecutor;

    public ValueHolderV14 exportPayableAdjustment(JSONObject obj, User user) {
        //插入我的任务里
        AsyncTaskBody asyncTaskBody = new AsyncTaskBody();
        asyncTaskBody.setTaskId(UUID.randomUUID().toString());
        asyncTaskBody.setMenu("丢件单导出");
        asyncTaskBody.setTaskType("导出");
        ValueHolderV14 vh = new ValueHolderV14();
        //任务开始
        asyncTaskManager.beforeExecute(user, asyncTaskBody);

        commonTaskExecutor.submit(() -> {
            //主线任务
            ValueHolderV14 v14 = asyncExport(obj, user);
            Map<Object, Object> retMap = new LinkedHashMap<>();
            retMap.put("code", v14.getCode());
            retMap.put("data", v14.getData());
            retMap.put("message", v14.getMessage());
            log.info(" 丢件单导出主线任务返回结果为:{}", JSON.toJSONString(v14));
            //任务完成
            asyncTaskBody.setUrl(String.valueOf(v14.getData()));
            asyncTaskManager.afterExecute(user, asyncTaskBody, JSON.parseObject(JSON.toJSONString(retMap)));
        });
        vh.setCode(ResultCode.SUCCESS);
        vh.setData(asyncTaskBody.getId());
        vh.setMessage(Resources.getMessage("执行成功，丢件单导出任务开始！"));
        return vh;

    }

    public ValueHolderV14 asyncExport(JSONObject obj, User user){
        ValueHolderV14 vh = new ValueHolderV14(ResultCode.SUCCESS, "应付款调整单导出成功！");

        ExportPayableAdjustmentResult result = getPayableAdjustmentData(obj).getData();
        List<AcFPayableAdjustmentExcel> mainExcelList = result.getAcFPayableAdjustmentList();
        List<AcFPayableAdjustmentItemExcel> itemExcelList = result.getAcFPayableAdjustmentItemList();
        exportUtil.setEndpoint(this.endpoint);
        exportUtil.setAccessKeyId(this.accessKeyId);
        exportUtil.setAccessKeySecret(this.accessKeySecret);
        exportUtil.setBucketName(this.bucketName);
        if(StringUtils.isEmpty(timeout)){
            //如果获取不到apllo配置参数，设置默认过期时间为30分钟
            timeout = "1800000";
        }
        exportUtil.setTimeout(this.timeout);

        /**
         *  拼接Excel主表sheet表头字段和列表
         * */
        String mainNames[] = {"单据状态", "单据编号", "平台单号", "单据类型", "调整类型", "渠道类型", "店铺名称", "实体仓", "赔付快递公司",
                "快递单号", "总应付金额", "支付方式", "备注", "来源单据编号", "顾客电话", "顾客姓名", "支付宝号", "会员昵称", "付款时间",
                "创建时间", "创建人", "修改时间", "修改人", "客审时间", "客审人", "财审时间", "财审人", "作废时间", "作废人"};
        String orderKeys[] = {"billStatusName", "billNo", "tid", "billTypeName", "adjustTypeName", "channelTypeName", "cpCShopTitle", "cpCPhyWarehouseEname", "cpCLogisticsEname",
                "logisticsNo", "payablePrice", "payTypeName", "remark", "orderNo", "customerTel", "customerName", "alipayAccount", "customerNick", "payTime",
                "creationdate", "ownerename", "modifieddate", "modifierename", "guestTrialTime", "guestTrialEname", "financialTrialTime", "financialTrialEname", "delTime", "delename"};

        /**
         *  拼接Excel调整单明细表sheet表头字段和列表
         * */
        String iteamNames[] = {"平台单号" ,"商品编码", "商品名称", "颜色", "尺寸", "商品条码", "国标码", "数量", "标准价", "实际成交价", "应付金额"};
        String iteamKeys[] = {"tid", "psCProEcode", "psCProEname", "psCClrEname", "psCSizeEname", "psCSkuEcode", "gbcode", "qty", "standardPrice", "truePrice", "payablePrice"};
        List mainName = Lists.newArrayList(mainNames);
        List mainKey = Lists.newArrayList(orderKeys);
        List itemName = Lists.newArrayList(iteamNames);
        List itemKey = Lists.newArrayList(iteamKeys);
        XSSFWorkbook hssfWorkbook = new XSSFWorkbook();
        exportUtil.executeSheet(hssfWorkbook, "应付款调整单主表", "", mainName, mainKey, mainExcelList, false);
        exportUtil.executeSheet(hssfWorkbook, "应付款调整单明细", "", itemName, itemKey, itemExcelList, false);
        String putMsg = exportUtil.saveFileAndPutOss(hssfWorkbook, "应付款调整单导出", user, "OSS-Bucket/EXPORT/AcFPayableAdjustment/");
        if(StringUtils.isEmpty(putMsg)){
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("应付款调整单导出失败");
        }
        vh.setData(putMsg);
        return vh;
    }


    public ValueHolderV14<ExportPayableAdjustmentResult> getPayableAdjustmentData(JSONObject param) {
        ValueHolderV14 vh = new ValueHolderV14();
        List<AcFPayableAdjustmentExcel> mainExcelList = Lists.newArrayList();
        List<AcFPayableAdjustmentItemExcel> itemExcelList = Lists.newArrayList();

        List<Long> idList = (List) param.get("idList");

        //创建时间-开始
        Date creationdateStart = "".equals(param.getString("CREATIONDATE_START")) ? null : param.getDate("CREATIONDATE_START");

        //创建时间-结束
        Date creationdateEnd = "".equals(param.getString("CREATIONDATE_END")) ? null : param.getDate("CREATIONDATE_END");

        //客审时间-开始
        Date guestTrialTimeStart = "".equals(param.getString("GUEST_TRIAL_TIME_START")) ? null : param.getDate("GUEST_TRIAL_TIME_START");

        //客审时间-结束
        Date guestTrialTimeEnd = "".equals(param.getString("GUEST_TRIAL_TIME_END")) ? null : param.getDate("GUEST_TRIAL_TIME_END");

        //财审时间-开始
        Date financialTrialTimeStart = "".equals(param.getString("FINANCIAL_TRIAL_TIME_START")) ? null : param.getDate("FINANCIAL_TRIAL_TIME_START");

        //财审时间-结束
        Date financialTrialTimeEnd = "".equals(param.getString("FINANCIAL_TRIAL_TIME_END")) ? null : param.getDate("FINANCIAL_TRIAL_TIME_END");

        //单据编号
        String billNo = "".equals(param.getString("BILL_NO")) ? null:param.getString("BILL_NO");
        String[] billNoList = null;
        if (billNo != null) {
            billNoList = billNo.split(",");
        }

        //平台单号
        String tid = "".equals(param.getString("TID")) ? null:param.getString("TID");
        String[] tidList = null;
        if (tid != null) {
            tidList = tid.split(",");
        }

        //国标码
        String gbcode = "".equals(param.getString("GBCODE")) ? null:param.getString("GBCODE");
        String[] gbcodeList = null;
        if (gbcode != null) {
            gbcodeList = gbcode.split(",");
        }

        //单据类型
        JSONArray billType = param.getJSONArray("BILL_TYPE");
        //将array数组转换成字符串
        List<Long> billTypeList = null;
        String billTypeStr = JSONObject.toJSONString(billType, SerializerFeature.WriteClassName);
        if (!"".equals(billTypeStr) && billTypeStr.indexOf("bSelect-all") == -1) {
            //把字符串转换成集合
            billTypeList = JSONObject.parseArray(billTypeStr, Long.class);
        }

        //单据状态
        JSONArray billStatus = param.getJSONArray("BILL_STATUS");
        //将array数组转换成字符串
        List<Long> billStatusList = null;
        String billStatusStr = JSONObject.toJSONString(billStatus, SerializerFeature.WriteClassName);
        if (!"".equals(billStatusStr) && billStatusStr.indexOf("bSelect-all") == -1) {
            //把字符串转换成集合
            billStatusList = JSONObject.parseArray(billStatusStr, Long.class);
        }

        //调整类型
        JSONArray adjustType = param.getJSONArray("ADJUST_TYPE");
        //将array数组转换成字符串
        List<Long> adjustTypeList = null;
        String adjustTypeStr = JSONObject.toJSONString(adjustType, SerializerFeature.WriteClassName);
        if (!"".equals(adjustTypeStr) && adjustTypeStr.indexOf("bSelect-all") == -1) {
            //把字符串转换成集合
            adjustTypeList = JSONObject.parseArray(adjustTypeStr, Long.class);
        }

        //渠道类型
        JSONArray channelType = param.getJSONArray("RESERVE_BIGINT01");
        //将array数组转换成字符串
        List<Long> channelTypeList = null;
        String channelTypeStr = JSONObject.toJSONString(channelType, SerializerFeature.WriteClassName);
        if (!"".equals(channelTypeStr) && channelTypeStr.indexOf("bSelect-all") == -1) {
            //将字符串转换成集合
            channelTypeList = JSONObject.parseArray(channelTypeStr, Long.class);
        }

        //系统订单编号
        String orderNo = "".equals(param.getString("ORDER_NO")) ? null:param.getString("ORDER_NO");


        //实体仓id
        Long cpCPhyWarehouseId = param.getLong("CP_C_PHY_WAREHOUSE_ID");

        //实体仓名称
        String cpCPhyWarehouseEname = param.getString("CP_C_PHY_WAREHOUSE_ENAME");

        //快递公司ID
        Long cpCLogisticsId = param.getLong("CP_C_LOGISTICS_ID");

        //快递公司名称
        String cpCLogisticsEname = param.getString("CP_C_LOGISTICS_ENAME");

        //条码id
        Long psCSkuId = param.getLong("PS_C_SKU_ID");

        //条码编码
        String psCSkuEcode = param.getString("PS_C_SKU_ECODE");

        if (idList != null && idList.size() > 0) {
            QueryWrapper<AcFPayableAdjustmentDO> orderWrapper = new QueryWrapper<>();
            orderWrapper.in("ID", idList);
            List<AcFPayableAdjustmentDO> adjustmentDOList = acFPayableAdjustmentMapper.selectList(orderWrapper);
            //转换主表对象返回
            if (CollectionUtils.isNotEmpty(adjustmentDOList)) {
                for (AcFPayableAdjustmentDO acFPayableAdjustmentDO : adjustmentDOList) {
                    AcFPayableAdjustmentExcel acFPayableAdjustmentExcel = new AcFPayableAdjustmentExcel();
                    BeanUtils.copyProperties(acFPayableAdjustmentDO, acFPayableAdjustmentExcel);
                    acFPayableAdjustmentExcel.setBillTypeName(ACEnumUtil.payBillTypeMap.get(acFPayableAdjustmentDO.getBillType()));
                    acFPayableAdjustmentExcel.setBillStatusName(ACEnumUtil.payBillStatusMap.get(acFPayableAdjustmentDO.getBillStatus()));
                    acFPayableAdjustmentExcel.setPayTypeName(ACEnumUtil.payTypeMap.get(acFPayableAdjustmentDO.getPayType()));
                    acFPayableAdjustmentExcel.setAdjustTypeName(ACEnumUtil.adjustTypeMap.get(acFPayableAdjustmentDO.getAdjustType()));
                    acFPayableAdjustmentExcel.setChannelTypeName(ACEnumUtil.channelTypeMap.get(acFPayableAdjustmentDO.getReserveBigint01()));
                    mainExcelList.add(acFPayableAdjustmentExcel);
                    //组装明细数据
                    QueryWrapper<AcFPayableAdjustmentItemDO> itemWrapper = new QueryWrapper<>();
                    itemWrapper.eq("AC_F_PAYABLE_ADJUSTMENT_ID", acFPayableAdjustmentDO.getId());
                    List<AcFPayableAdjustmentItemDO> itemList = acFPayableAdjustmentItemMapper.selectList(itemWrapper);
                    if (CollectionUtils.isNotEmpty(itemList)) {
                        for (AcFPayableAdjustmentItemDO item : itemList) {
                            AcFPayableAdjustmentItemExcel itemExcel = new AcFPayableAdjustmentItemExcel();
                            BeanUtils.copyProperties(item, itemExcel);
                            itemExcel.setTid(acFPayableAdjustmentDO.getTid());
                            itemExcelList.add(itemExcel);
                        }
                    }
                }
            }
        } else {
            //查询符合的主表
            List<AcFPayableAdjustmentExDO> payableAdjustmentList = acFPayableAdjustmentMapper.selectAcFPayableAdjustmentListByWhere(billNoList, tidList,
                    billTypeList, billStatusList, adjustTypeList, orderNo, cpCPhyWarehouseId, cpCLogisticsId,
                    psCSkuId, gbcodeList, creationdateStart, creationdateEnd, guestTrialTimeStart, guestTrialTimeEnd,
                    financialTrialTimeStart, financialTrialTimeEnd, channelTypeList,null, null);
            //转换主表对象返回
            if (CollectionUtils.isNotEmpty(payableAdjustmentList)) {
                for (AcFPayableAdjustmentDO acFPayableAdjustmentDO : payableAdjustmentList) {
                    AcFPayableAdjustmentExcel acFPayableAdjustmentExcel = new AcFPayableAdjustmentExcel();
                    BeanUtils.copyProperties(acFPayableAdjustmentDO, acFPayableAdjustmentExcel);
                    acFPayableAdjustmentExcel.setBillTypeName(ACEnumUtil.payBillTypeMap.get(acFPayableAdjustmentDO.getBillType()));
                    acFPayableAdjustmentExcel.setBillStatusName(ACEnumUtil.payBillStatusMap.get(acFPayableAdjustmentDO.getBillStatus()));
                    acFPayableAdjustmentExcel.setPayTypeName(ACEnumUtil.payTypeMap.get(acFPayableAdjustmentDO.getPayType()));
                    acFPayableAdjustmentExcel.setAdjustTypeName(ACEnumUtil.adjustTypeMap.get(acFPayableAdjustmentDO.getAdjustType()));
                    acFPayableAdjustmentExcel.setChannelTypeName(ACEnumUtil.channelTypeMap.get(acFPayableAdjustmentDO.getReserveBigint01()));
                    mainExcelList.add(acFPayableAdjustmentExcel);
                    //组装明细数据
                    QueryWrapper<AcFPayableAdjustmentItemDO> itemWrapper = new QueryWrapper<>();
                    itemWrapper.eq("AC_F_PAYABLE_ADJUSTMENT_ID", acFPayableAdjustmentDO.getId());
                    List<AcFPayableAdjustmentItemDO> itemList = acFPayableAdjustmentItemMapper.selectList(itemWrapper);
                    if (CollectionUtils.isNotEmpty(itemList)) {
                        for (AcFPayableAdjustmentItemDO item : itemList) {
                            AcFPayableAdjustmentItemExcel itemExcel = new AcFPayableAdjustmentItemExcel();
                            BeanUtils.copyProperties(item, itemExcel);
                            itemExcel.setTid(acFPayableAdjustmentDO.getTid());
                            itemExcelList.add(itemExcel);
                        }
                    }
                }
            }
        }
        //拼接返回对象
        ExportPayableAdjustmentResult exportResult = new ExportPayableAdjustmentResult();
        exportResult.setAcFPayableAdjustmentList(mainExcelList);
        exportResult.setAcFPayableAdjustmentItemList(itemExcelList);
        vh.setData(exportResult);
        return vh;
    }
}
