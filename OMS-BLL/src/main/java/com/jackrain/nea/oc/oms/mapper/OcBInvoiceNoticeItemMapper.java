package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.OcBInvoiceNoticeItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface OcBInvoiceNoticeItemMapper extends ExtentionMapper<OcBInvoiceNoticeItem> {

    @Select("select * from oc_b_invoice_notice_item where oc_b_invoice_notice_id = #{mainid}")
    List<OcBInvoiceNoticeItem> listByMainid(@Param("mainid") Long mainid);
}