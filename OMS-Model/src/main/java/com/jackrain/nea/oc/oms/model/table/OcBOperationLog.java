package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

@TableName(value = "oc_b_operation_log")
@Data
public class OcBOperationLog extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "TABLE_NAME")
    private String tableName;

    @JSONField(name = "OPERATION_TYPE")
    private String operationType;

    @JSO<PERSON>ield(name = "UPDATE_ID")
    private Long updateId;

    @JSO<PERSON>ield(name = "UPDATE_MODEL_NAME")
    private String updateModelName;

    @J<PERSON><PERSON><PERSON>(name = "MOD_CONTENT")
    private String modContent;

    @<PERSON><PERSON><PERSON>ield(name = "BEFORE_DATA")
    private String beforeData;

    @J<PERSON><PERSON>ield(name = "AFTER_DATA")
    private String afterData;
}