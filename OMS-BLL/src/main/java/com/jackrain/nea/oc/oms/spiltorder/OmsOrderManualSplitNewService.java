package com.jackrain.nea.oc.oms.spiltorder;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsShareOutItemRequest;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsShareOutRequest;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.dto.DmsOrderManualSplitDTO;
import com.jackrain.nea.oc.oms.dto.DmsOrderManualSplitTempDTO;
import com.jackrain.nea.oc.oms.dto.OmsManualSplitItem;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.DmsGiftAttrEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderIsInterceptEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OmsPayType;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.taobao.TaoBaoOrderStatus;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
import com.jackrain.nea.oc.oms.services.BoxStrategyService;
import com.jackrain.nea.oc.oms.services.OcBOrderItemExtService;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.services.SplitBeforeSourcingStService;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.st.service.OmsStCShopStrategyService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.OmsBusinessTypeUtil;
import com.jackrain.nea.util.OmsOrderUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.async.AsyncTaskBody;
import com.jackrain.nea.web.common.AsyncTaskManager;
import com.jackrain.nea.web.face.User;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2022/9/18 下午1:48
 * @Version 1.0
 * 手动拆单的业务类
 */
@Slf4j
@Component
public class OmsOrderManualSplitNewService {
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OmsOrderService omsOrderService;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private BllRedisLockOrderUtil redisUtil;
    @Autowired
    private SplitBeforeSourcingStService splitBeforeSourcingStService;
    @Autowired
    private AsyncTaskManager asyncTaskManager;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private ThreadPoolTaskExecutor commonTaskExecutor;
    @Autowired
    private ThreadPoolTaskExecutor splitOnePollExecutor;
    @Autowired
    private OcBOrderItemExtService ocBOrderItemExtService;
    @Autowired
    private ThreadPoolTaskExecutor splitBoxPollExecutor;
    @Autowired
    private OmsOrderManualSplitNewService omsOrderManualSplitNewService;
    @Autowired
    private OmsStCShopStrategyService omsStCShopStrategyService;
    @Autowired
    private BoxStrategyService boxStrategyService;
    @Autowired
    SgRpcService sgRpcService;

    /**
     * 单个拆单查询
     *
     * @param obj
     * @return
     */
    public ValueHolderV14 selectOmsOrderItem(JSONObject obj, User user) {
        //查询待确认和缺货状态单据
        ValueHolderV14 holder = new ValueHolderV14();
        Long id = obj.getLong("orderId");
        OcBOrder ocBorderDto = ocBOrderMapper.selectById(id);
        try {
            this.check(ocBorderDto);
            List<OcBOrderItem> orderItems = ocBOrderItemMapper.selectOrderItemListAndReturn(id);
            //过滤掉贴纸赠品
            orderItems = orderItems.stream().filter(s ->
                    !Objects.equals(s.getStickerGift(), YesNoEnum.Y.getVal())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(orderItems)) {
                AssertUtil.isTrue(true, "明细为空");
            }
            if (orderItems.size() == 1 && orderItems.get(0).getQty().compareTo(BigDecimal.ONE) == 0) {
                AssertUtil.isTrue(true, "只有一条明细并且数量为1,无法拆分");
            }
            List<OmsManualSplitItem> itemList = new ArrayList<>();
            for (OcBOrderItem orderItem : orderItems) {
                OmsManualSplitItem item = new OmsManualSplitItem();
                item.setIsGift(orderItem.getIsGift());
                item.setQty(orderItem.getQty());
                item.setItemId(orderItem.getId());
                item.setOrderId(orderItem.getOcBOrderId());
                item.setProName(orderItem.getPsCProEname());
                item.setSkuCode(orderItem.getPsCSkuEcode());
                item.setTid(orderItem.getTid());
                item.setExpiryDateRange(orderItem.getExpiryDateRange());
                if (orderItem.getProType() == SkuType.COMBINE_PRODUCT) {
                    String canSplit = orderItem.getCanSplit();
                    if ("N".equals(canSplit)) {
                        item.setIsSplit("否");
                    }
                }
                Integer isGift = orderItem.getIsGift();
                if (isGift != null && isGift == 1 && orderItem.getProType() == SkuType.NORMAL_PRODUCT) {
                    Integer isGiftSplit = orderItem.getIsGiftSplit();
                    if (isGiftSplit != null && isGiftSplit == 2) {
                        item.setIsSplit("否");
                    }
                }
                item.setBusinessTypeCode(ocBorderDto.getBusinessTypeCode());
                if (StringUtils.isNotEmpty(ocBorderDto.getGwSourceGroup())) {
                    item.setPlatform(Integer.valueOf(ocBorderDto.getGwSourceGroup()));
                } else {
                    item.setPlatform(ocBorderDto.getPlatform());
                }
                item.setSaleProductAttr(ocBorderDto.getSaleProductAttr());
                itemList.add(item);
            }
            holder.setCode(0);
            holder.setMessage("查询成功");
            holder.setData(itemList);
        } catch (Exception e) {
            holder.setCode(-1);
            holder.setMessage(e.getMessage());
        }
        return holder;
    }


    /**
     * 确认拆单
     *
     * @param object
     * @param user
     * @return
     */
    public ValueHolderV14 confirmSplitOrder(JSONObject object, User user, boolean isLock, List<Long> splitItemIds) {
        //单个拆单id是一样的
        ValueHolderV14 holder = new ValueHolderV14();
        Long orderId = object.getLong("orderId");
        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(orderId);
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (isLock) {
                if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                    splitOrder(object, user, holder, orderId, splitItemIds);
                } else {
                    AssertUtil.isTrue(false, "当前订单正在被其他人操作,请稍后再试");
                }
            } else {
                splitOrder(object, user, holder, orderId, splitItemIds);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("手动单个拆单失败:{}", "手动单个拆单", orderId), Throwables.getStackTraceAsString(e));
            holder.setCode(-1);
            holder.setMessage("订单拆分失败,原因:" + e.getMessage());
        } finally {
            if (isLock) {
                redisLock.unlock();
            }
        }
        return holder;
    }

    private void splitOrder(JSONObject object, User user, ValueHolderV14 holder, Long orderId, List<Long> splitItemIds) {
        OcBOrder order = ocBOrderMapper.selectById(orderId);
        this.check(order);
        List<OcBOrderItem> orderItems = ocBOrderItemMapper.selectOrderItemListOccupy(orderId);
        //过滤掉贴纸赠品
        orderItems = orderItems.stream().filter(s ->
                !Objects.equals(s.getStickerGift(), YesNoEnum.Y.getVal())).collect(Collectors.toList());
        List<OcBOrderItem> items = orderItems.stream().filter(p -> p.getProType() != SkuType.NO_SPLIT_COMBINE).collect(Collectors.toList());

        Map<Long, OcBOrderItem> orderItemMap = items.stream().collect(Collectors.toMap(OcBOrderItem::getId, Function.identity(), (key1, key2) -> key2));

        JSONArray item = object.getJSONArray("item");
        List<List<OcBOrderItem>> listList = new ArrayList<>();
        BigDecimal qtyCount = BigDecimal.ZERO;
        Map<String, Long> warehouseIdMap = new HashMap<>();
        for (int i = 0; i < item.size(); i++) {
            JSONArray jsonArray = item.getJSONArray(i);
            List<OmsManualSplitItem> itemList = JSONObject.parseArray(jsonArray.toJSONString(), OmsManualSplitItem.class);
            List<OcBOrderItem> ocBOrderItems = new ArrayList<>();
            List<OmsManualSplitItem> collect = itemList.stream().filter(p -> p.getWarehouseId() != null).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect) && collect.size() != itemList.size()) {
                AssertUtil.isTrue(false, "拆单明细所选实体仓不一致");
            }
            if (CollectionUtils.isNotEmpty(collect)) {
                Set<Long> collect1 = collect.stream().map(OmsManualSplitItem::getWarehouseId).collect(Collectors.toSet());
                if (collect1.size() != 1) {
                    AssertUtil.isTrue(false, "拆单明细所选实体仓不一致");
                }
            }
            String uuid = UUID.randomUUID().toString();
            if (CollectionUtils.isEmpty(itemList)) {
                continue;
            }
            for (OmsManualSplitItem omsManualSplitItem : itemList) {
                BigDecimal qty = omsManualSplitItem.getQty();
                if (BigDecimal.ZERO.compareTo(qty) >= 0) {
                    continue;
                }
                OcBOrderItem item1 = orderItemMap.get(omsManualSplitItem.getItemId());
                if (item1 == null) {
                    continue;
                }
                OcBOrderItem orderItem = new OcBOrderItem();
                BeanUtils.copyProperties(item1, orderItem);
                orderItem.setQty(qty);
                orderItem.setReserveVarchar05(uuid);
                ocBOrderItems.add(orderItem);
                warehouseIdMap.put(uuid, omsManualSplitItem.getWarehouseId());
                qtyCount = qtyCount.add(omsManualSplitItem.getQty());
            }
            if (CollectionUtils.isNotEmpty(ocBOrderItems)) {
                listList.add(ocBOrderItems);
            }
        }
        //判断数量与原单是否一样
        BigDecimal qty = items.stream().map(OcBOrderItem::getQty).
                reduce(BigDecimal.ZERO, BigDecimal::add);
        if (qtyCount.compareTo(qty) != 0) {
            AssertUtil.isTrue(false, "拆分数量与原单不一致");
        }
        if (listList.size() == 1) {
            AssertUtil.isTrue(false, "拆分之后与原单一致");
        }
        boolean flag = splitBeforeSourcingStService.insertNewOrders(listList, order, orderItems, user, warehouseIdMap,
                "手动单个拆单,作废原单", null, null, false, splitItemIds, Maps.newHashMap());
        if (flag) {
            holder.setCode(0);
            holder.setMessage("订单拆分成功");
            ocBOrderItemExtService.deleteByOrderId(order.getId());
        } else {
            holder.setCode(-1);
            holder.setMessage("订单拆分失败");
        }
    }

    private void check(OcBOrder order) {
        AssertUtil.assertException(order == null, "订单不存在！");
        AssertUtil.assertException(OmsOrderUtil.wdtPlatformSend(order), "旺店通下推订单 不支持手动拆单");

        //如果是经销商OMS订单转成的零售发货单，不允许拆单
        boolean isCheckOrderIssuing = omsOrderService.checkOrderIssuing(order.getId(), order.getCpCShopId());
        AssertUtil.assertException(isCheckOrderIssuing, "订单对应平台店铺的店铺渠道为一件代发经销平台，无法拆分!");

        AssertUtil.assertException(!OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(order.getOrderStatus())
                        && !OmsOrderStatus.UNCONFIRMED.toInteger().equals(order.getOrderStatus()),
                "订单非待审核或缺货状态，不符合拆单条件，无法拆分!");
        //订单是否被拦截
        AssertUtil.assertException(OmsOrderIsInterceptEnum.YES.getVal().equals(order.getIsInterecept()),
                "订单HOLD单状态，不符合拆单条件，无法拆分!");
        AssertUtil.assertException(PlatFormEnum.VIP_JITX.getCode().equals(order.getPlatform()),
                "订单为JITX订单，不符合拆单条件，无法拆分!");

        //订单是否在退款中
        AssertUtil.assertException(order.getIsInreturning() == 1, "订单退款中，不符合拆单条件，无法拆分!");

        // 货到付款订单不允许拆单
        AssertUtil.assertException(order.getPayType() != null &&
                        OmsPayType.CASH_ON_DELIVERY.toInteger() == order.getPayType(),
                "订单为货到付款订单,不符合拆单条件，无法拆分!");

        //预售状态为预售尾款未付的订单不允许手工拆单
        String statusPayStep = order.getStatusPayStep();
        AssertUtil.assertException(OrderTypeEnum.TBA_PRE_SALE.getVal().equals(order.getOrderType())
                        && StringUtils.isNotEmpty(statusPayStep) && !TaoBaoOrderStatus.FRONT_PAID_FINAL_PAID.equals(statusPayStep),
                "订单为预售状态为预售尾款未付订单,不符合拆单条件，无法拆分!");
    }


    /**
     * 按行拆 (组合商品  赠品需要判断是否可拆)
     *
     * @param orderIds
     * @param user
     * @return
     */
    public ValueHolderV14 orderSplitByRow(List<Long> orderIds, User user, String splitReason) {
        ValueHolderV14 holder = new ValueHolderV14();
//        List<Long> orderIdList = ocBOrderMapper.selectByIdsAndGwSourceGroup(orderIds, PlatFormEnum.DMS.getCode());
//        if (CollectionUtils.isNotEmpty(orderIdList)) {
//            // 查询订单明细 找到未退款的明细 并且取出reserveBigint字段值不为空的数据
//            List<OcBOrderItem> ocBOrderItems = ocBOrderItemMapper.selectOrderItemsByIds(orderIdList);
//            if (CollectionUtils.isNotEmpty(ocBOrderItems)) {
//                // 找到未退款的明细 并且取出reserveBigint字段值不为空的数据
//                List<OcBOrderItem> ocBOrderItems1 = ocBOrderItems.stream().filter(item -> item.getReserveBigint01() != null).collect(Collectors.toList());
//                if (CollectionUtils.isNotEmpty(ocBOrderItems1)) {
//                    // 判断是否包含了
//                    for (OcBOrderItem item : ocBOrderItems1) {
//                        if (!Objects.equals(item.getReserveBigint01(), DmsGiftAttrEnum.OUR_PRODUCT.getVal())) {
//                            // 抛异常
//                            holder.setCode(ResultCode.FAIL);
//                            holder.setMessage("DMS赠品订单不允许按行拆单，订单编号:" + item.getOcBOrderId());
//                            return holder;
//                        }
//                    }
//                }
//            }
//        }
        AsyncTaskBody asyncTaskBody = new AsyncTaskBody();
        asyncTaskBody.setTaskId(UUID.randomUUID().toString());
        asyncTaskBody.setMenu("手动按行拆单");
        asyncTaskBody.setTaskType("手动按行拆单");
        asyncTaskManager.beforeExecute(user, asyncTaskBody);
        commonTaskExecutor.submit(() -> {
            Integer failCount = 0;
            for (Long orderId : orderIds) {
                String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(orderId);
                RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
                try {
                    if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                        OcBOrder order = ocBOrderMapper.selectById(orderId);
                        this.check(order);
                        this.handleSplitByRow(order, user, splitReason);
                    } else {
                        AssertUtil.isTrue(false, "当前订单正在被其他人操作,请稍后再试");
                    }
                } catch (Exception e) {
                    log.error(LogUtil.format("订单按行拆分失败:{}", "订单按行拆分失败", orderId), Throwables.getStackTraceAsString(e));
                    failCount++;
                } finally {
                    redisLock.unlock();
                }
            }
            ValueHolderV14 holderV14 = new ValueHolderV14();
            holderV14.setCode(0);
            holderV14.setMessage("手动按行拆单共:" + orderIds.size() + "条,成功:" + (orderIds.size() - failCount) + "条,失败:" + failCount + "条");
            asyncTaskManager.afterExecute(user, asyncTaskBody, holderV14.toJSONObject());
        });
        holder.setCode(0);
        holder.setData(asyncTaskBody.getId());
        holder.setMessage("手动按行拆单任务开始！详情请在我的任务查看");
        return holder;
    }


    /**
     * 按行拆
     *
     * @param order
     * @param user
     * @param splitReason
     */
    public void handleSplitByRow(OcBOrder order, User user, String splitReason) {
        List<OcBOrderItem> ocBOrderItemList = ocBOrderItemMapper.selectOrderItemListOccupy(order.getId());
        //过滤掉贴纸赠品
        ocBOrderItemList = ocBOrderItemList.stream().filter(s ->
                !Objects.equals(s.getStickerGift(), YesNoEnum.Y.getVal())).collect(Collectors.toList());
        List<OcBOrderItem> orderItems = ocBOrderItemList.stream().filter(p -> p.getProType() != SkuType.NO_SPLIT_COMBINE).collect(Collectors.toList());

        //dms拆单特殊逻辑
        if (OmsBusinessTypeUtil.isToBOrder(order) && PlatFormEnum.DMS.getCode().toString().equals(order.getGwSourceGroup())) {
            List<OcBOrderItem> ocBOrderItems = ocBOrderItemList.stream().filter(p -> p.getReserveBigint01() != null).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(ocBOrderItems)) {
                List<OcBOrderItem> productList = ocBOrderItems.stream().filter(p -> DmsGiftAttrEnum.OUR_PRODUCT.getVal().equals(p.getReserveBigint01())).collect(Collectors.toList());
                if (productList.size() != ocBOrderItems.size()) {
                    //若订单只有一种SKU但是多行，则拆单失败
                    Set<String> skuCodes = orderItems.stream().map(OcBOrderItem::getPsCSkuEcode).collect(Collectors.toSet());
                    if (skuCodes.size() <= 1) {
                        //订单日志
                        omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(), OrderLogTypeEnum.ORDER_SPLIT.getKey(), "订单中仅包含一种商品，无法进行拆单！", "", "", user);
                        AssertUtil.isTrue(false, "订单中仅包含一种商品，无法进行拆单！订单编号:" + order.getId());
                    }

                    //特殊拆单逻辑
                    handleSplitByRowDms(order, user, splitReason, ocBOrderItemList);
                    return;
                }
            }
        }

        List<List<OcBOrderItem>> splitItem = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(orderItems)) {
            List<OcBOrderItem> itemList = orderItems.stream().filter(p -> p.getProType() != SkuType.NO_SPLIT_COMBINE).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(itemList) && itemList.size() == 1) {
                AssertUtil.isTrue(false, "当前订单只有一行明细,无法拆分");
            }
            List<OcBOrderItem> group4Item = ocBOrderItemList.stream().filter(p -> p.getProType() == SkuType.NO_SPLIT_COMBINE).collect(Collectors.toList());
            Map<String, List<OcBOrderItem>> group2Map = new HashMap<>();
            if (CollectionUtils.isNotEmpty(group4Item)) {
                List<OcBOrderItem> group2Item = orderItems.stream().filter(p -> p.getProType() == SkuType.COMBINE_PRODUCT || p.getProType() == SkuType.GIFT_PRODUCT).collect(Collectors.toList());
                group2Map = group2Item.stream().collect(Collectors.groupingBy(OcBOrderItem::getGroupGoodsMark));
            }
            List<OcBOrderItem> giftItem = orderItems.stream().filter(p -> p.getIsGift() != null && p.getIsGift() == 1 && p.getProType() == SkuType.NORMAL_PRODUCT).collect(Collectors.toList());
            List<OcBOrderItem> normalItem = orderItems.stream().filter(p -> (p.getIsGift() == null || p.getIsGift() == 0) && p.getProType() == SkuType.NORMAL_PRODUCT).collect(Collectors.toList());
            List<OcBOrderItem> relationGift = giftItem.stream().filter(p -> StringUtils.isNotEmpty(p.getGiftRelation()) && p.getIsGiftSplit() != null && p.getIsGiftSplit() == 1).collect(Collectors.toList());
            List<OcBOrderItem> gift = giftItem.stream().filter(p -> StringUtils.isEmpty(p.getGiftRelation()) || p.getIsGiftSplit() == null || p.getIsGiftSplit() != 1).collect(Collectors.toList());
            Map<String, List<OcBOrderItem>> relationGiftMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(relationGift)) {
                relationGiftMap = relationGift.stream().collect(Collectors.groupingBy(OcBOrderItem::getGiftRelation));
            }
            List<OcBOrderItem> noSplitItem = new ArrayList<>();
            this.handGroup(splitItem, group2Map, relationGiftMap, noSplitItem);
            if (CollectionUtils.isNotEmpty(normalItem)) {
                for (OcBOrderItem orderItem : normalItem) {
                    List<OcBOrderItem> ocBOrderItems = new ArrayList<>();
                    ocBOrderItems.add(orderItem);
                    String giftRelation = orderItem.getGiftRelation();
                    if (StringUtils.isNotEmpty(giftRelation)) {
                        List<OcBOrderItem> itemList1 = relationGiftMap.get(giftRelation);
                        if (CollectionUtils.isNotEmpty(itemList1)) {
                            ocBOrderItems.addAll(itemList1);
                            relationGiftMap.remove(giftRelation);
                        }
                    }
                    splitItem.add(ocBOrderItems);
                }
            }
            if (CollectionUtils.isNotEmpty(gift)) {
                for (OcBOrderItem orderItem : gift) {
                    List<OcBOrderItem> ocBOrderItems = new ArrayList<>();
                    ocBOrderItems.add(orderItem);
                    splitItem.add(ocBOrderItems);
                }
            }
            if (CollectionUtils.isNotEmpty(noSplitItem)) {
                splitItem.add(noSplitItem);
            }
        }
        if (splitItem.size() == 1) {
            AssertUtil.isTrue(false, "订单只有一单,无法拆分");
        }
        List<List<OcBOrderItem>> splitItemNew = new ArrayList<>();
        for (List<OcBOrderItem> ocBOrderItems : splitItem) {
            List<OcBOrderItem> bOrderItems = new ArrayList<>();
            for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
                OcBOrderItem orderItem = new OcBOrderItem();
                BeanUtils.copyProperties(ocBOrderItem, orderItem);
                bOrderItems.add(orderItem);
            }
            splitItemNew.add(bOrderItems);
        }
        boolean b = splitBeforeSourcingStService.insertNewOrders(splitItemNew, order, ocBOrderItemList, user,
                null, "手动按行拆单,作废原单,原因:" + splitReason, null, null, false, Lists.newArrayList(), Maps.newHashMap());
        AssertUtil.isTrue(b, "订单按行拆分失败");
        ocBOrderItemExtService.deleteByOrderId(order.getId());
    }

    private void handGroup(List<List<OcBOrderItem>> splitItem, Map<String, List<OcBOrderItem>> group2Map,
                           Map<String, List<OcBOrderItem>> relationGiftMap, List<OcBOrderItem> noSplitItem) {
        if (!group2Map.isEmpty()) {
            for (String s : group2Map.keySet()) {
                List<OcBOrderItem> orderItems1 = group2Map.get(s);
                if (CollectionUtils.isNotEmpty(orderItems1)) {
                    String canSplit = orderItems1.get(0).getCanSplit();
                    if ("Y".equals(canSplit)) {
                        for (OcBOrderItem orderItem : orderItems1) {
                            List<OcBOrderItem> itemList = new ArrayList<>();
                            itemList.add(orderItem);
                            String giftRelation = orderItem.getGiftRelation();
                            if (StringUtils.isNotEmpty(giftRelation)) {
                                List<OcBOrderItem> itemList1 = relationGiftMap.get(giftRelation);
                                if (CollectionUtils.isNotEmpty(itemList1)) {
                                    itemList.addAll(itemList1);
                                    relationGiftMap.remove(giftRelation);
                                }
                            }
                            splitItem.add(itemList);
                        }
                    } else {
                        String giftRelation = orderItems1.get(0).getGiftRelation();
                        if (StringUtils.isNotEmpty(giftRelation)) {
                            List<OcBOrderItem> itemList1 = relationGiftMap.get(giftRelation);
                            if (CollectionUtils.isNotEmpty(itemList1)) {
                                orderItems1.addAll(itemList1);
                                relationGiftMap.remove(giftRelation);
                            }
                        }
                        noSplitItem.addAll(orderItems1);
                    }
                }
            }
        }
    }


    private void handGroupOne(List<OcBOrderItem> splitItem, Map<String, List<OcBOrderItem>> group2Map,
                              Map<String, List<OcBOrderItem>> relationGiftMap, List<OcBOrderItem> noSplitItem) {
        if (!group2Map.isEmpty()) {
            for (String s : group2Map.keySet()) {
                List<OcBOrderItem> orderItems1 = group2Map.get(s);
                if (CollectionUtils.isNotEmpty(orderItems1)) {
                    String canSplit = orderItems1.get(0).getCanSplit();
                    if ("Y".equals(canSplit)) {
                        for (OcBOrderItem orderItem : orderItems1) {
                            List<OcBOrderItem> itemList = new ArrayList<>();
                            itemList.add(orderItem);
                            splitItem.add(orderItem);
                        }
                    } else {
                        String giftRelation = orderItems1.get(0).getGiftRelation();
                        if (StringUtils.isNotEmpty(giftRelation)) {
                            List<OcBOrderItem> itemList1 = relationGiftMap.get(giftRelation);
                            if (CollectionUtils.isNotEmpty(itemList1)) {
                                orderItems1.addAll(itemList1);
                                relationGiftMap.remove(giftRelation);
                            }
                        }
                        noSplitItem.addAll(orderItems1);
                    }
                }
            }
        }
    }

    /**
     * 指定商品拆单
     *
     * @param orderIds
     * @param skuCode
     * @param user
     * @param splitNum
     * @return
     */
    public ValueHolderV14 appointSplitOrder(List<Long> orderIds, String skuCode, User user, String splitNum) {
        ValueHolderV14 holder = new ValueHolderV14();
//        List<Long> orderIdList = ocBOrderMapper.selectByIdsAndGwSourceGroup(orderIds, PlatFormEnum.DMS.getCode());
//        if (CollectionUtils.isNotEmpty(orderIdList)) {
//            // 查询订单明细 找到未退款的明细 并且取出reserveBigint字段值不为空的数据
//            List<OcBOrderItem> ocBOrderItems = ocBOrderItemMapper.selectOrderItemsByIds(orderIdList);
//            if (CollectionUtils.isNotEmpty(ocBOrderItems)) {
//                // 找到未退款的明细 并且取出reserveBigint字段值不为空的数据
//                List<OcBOrderItem> ocBOrderItems1 = ocBOrderItems.stream().filter(item -> item.getReserveBigint01() != null).collect(Collectors.toList());
//                if (CollectionUtils.isNotEmpty(ocBOrderItems1)) {
//                    // 判断是否包含了
//                    for (OcBOrderItem item : ocBOrderItems1) {
//                        if (!Objects.equals(item.getReserveBigint01(), DmsGiftAttrEnum.OUR_PRODUCT.getVal())) {
//                            // 抛异常
//                            holder.setCode(ResultCode.FAIL);
//                            holder.setMessage("DMS赠品订单不允许指定商品拆单，订单编号:" + item.getOcBOrderId());
//                            return holder;
//                        }
//                    }
//                }
//            }
//        }
        AsyncTaskBody asyncTaskBody = new AsyncTaskBody();
        asyncTaskBody.setTaskId(UUID.randomUUID().toString());
        asyncTaskBody.setMenu("指定商品拆单" + skuCode);
        asyncTaskBody.setTaskType("指定商品拆单" + skuCode);
        asyncTaskManager.beforeExecute(user, asyncTaskBody);
        commonTaskExecutor.submit(() -> {
            Integer failCount = 0;
            for (Long orderId : orderIds) {
                String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(orderId);
                RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
                try {
                    if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                        OcBOrder order = ocBOrderMapper.selectById(orderId);
                        this.check(order);
                        this.appointSplit(order, skuCode, user, splitNum);
                    } else {
                        AssertUtil.isTrue(false, "当前订单正在被其他人操作,请稍后再试");
                    }
                } catch (Exception e) {
                    log.error(LogUtil.format("指定商品拆单失败:{}", "指定商品拆单", orderId), Throwables.getStackTraceAsString(e));
                    failCount++;
                } finally {
                    redisLock.unlock();
                }
            }
            ValueHolderV14 holderV14 = new ValueHolderV14();
            holderV14.setCode(0);
            holderV14.setMessage("指定商品" + skuCode + "拆单共:" + orderIds.size() + "条,成功:" + (orderIds.size() - failCount) + "条,失败:" + failCount + "条");
            asyncTaskManager.afterExecute(user, asyncTaskBody, holderV14.toJSONObject());
        });
        holder.setCode(0);
        holder.setData(asyncTaskBody.getId());
        holder.setMessage("指定商品拆单任务开始！详情请在我的任务查看");
        return holder;
    }


    /**
     * 指定商品拆
     *
     * @param order
     * @param skuCode
     * @param user
     * @param splitNum
     */
    public void appointSplit(OcBOrder order, String skuCode, User user, String splitNum) {
        List<OcBOrderItem> orderItems = ocBOrderItemMapper.selectOrderItemListOccupy(order.getId());
        //过滤掉贴纸赠品
        orderItems = orderItems.stream().filter(s ->
                !Objects.equals(s.getStickerGift(), YesNoEnum.Y.getVal())).collect(Collectors.toList());
        List<OcBOrderItem> ocBOrderItems = orderItems.stream().filter(p -> p.getProType() != SkuType.NO_SPLIT_COMBINE).collect(Collectors.toList());
        List<OcBOrderItem> itemList = ocBOrderItems.stream().filter(p -> p.getPsCSkuEcode().equals(skuCode)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(itemList)) {
            omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(),
                    OrderLogTypeEnum.ORDER_SPLIT.getKey(), "指定商品:" + skuCode + ",拆单,该订单不存在此sku,无法拆分", null, null, user);
            return;
        }
        List<OcBOrderItem> bOrderItems = ocBOrderItems.stream().filter(p -> !p.getPsCSkuEcode().equals(skuCode)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(bOrderItems) && !PlatFormEnum.DMS.getCode().toString().equals(order.getGwSourceGroup())) {
            omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(),
                    OrderLogTypeEnum.ORDER_SPLIT.getKey(), "指定商品:" + skuCode + ",拆单,该订单只有此sku,无法拆分", null, null, user);
            return;
        }
        if (CollectionUtils.isEmpty(bOrderItems) && PlatFormEnum.DMS.getCode().toString().equals(order.getGwSourceGroup()) && StringUtils.isBlank(splitNum)) {
            omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(),
                    OrderLogTypeEnum.ORDER_SPLIT.getKey(), "指定商品:" + skuCode + ",拆单,该订单只有此sku,且不按数量拆,无法拆分", null, null, user);
            return;
        }

        //tob dms 按sku拆单逻辑
        if (OmsBusinessTypeUtil.isToBOrder(order) && PlatFormEnum.DMS.getCode().toString().equals(order.getGwSourceGroup())) {
            if (appointSplitDms(order, skuCode, user, splitNum, orderItems, itemList, bOrderItems)) {
                return;
            }
        }

        List<List<OcBOrderItem>> splitItem = new ArrayList<>();
        splitItem.add(bOrderItems);
        splitItem.add(itemList);
        List<List<OcBOrderItem>> splitItemNew = new ArrayList<>();
        for (List<OcBOrderItem> orderItemList : splitItem) {
            List<OcBOrderItem> orderItems1 = new ArrayList<>();
            for (OcBOrderItem ocBOrderItem : orderItemList) {
                OcBOrderItem orderItem = new OcBOrderItem();
                BeanUtils.copyProperties(ocBOrderItem, orderItem);
                orderItems1.add(orderItem);
            }
            splitItemNew.add(orderItems1);
        }
        boolean b = splitBeforeSourcingStService.insertNewOrders(splitItemNew, order, orderItems, user, null,
                "手动指定商品:" + skuCode + "拆单,作废原单", null, null, false, Lists.newArrayList(), Maps.newHashMap());
        AssertUtil.isTrue(b, "指定商品拆分失败");
    }

    /**
     * tob按指定商品拆单
     *
     * @param order
     * @param skuCode
     * @param user
     * @param splitNum
     * @param orderItems
     * @param itemList
     * @param bOrderItems
     * @return
     */
    private boolean appointSplitDms(OcBOrder order, String skuCode, User user, String splitNum, List<OcBOrderItem> orderItems,
                                    List<OcBOrderItem> itemList, List<OcBOrderItem> bOrderItems) {
        if (StringUtils.isBlank(splitNum)) {
            return false;
        }

        //只能是大于0的正整数
        if (!NumberUtils.isDigits(splitNum) || Integer.parseInt(splitNum) <= 0) {
            omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(),
                    OrderLogTypeEnum.ORDER_SPLIT.getKey(), "指定商品:" + skuCode + ",拆单,拆分数量错误,无法拆分", null, null, user);
            return true;
        }
        if (itemList.size() > 1) {
            omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(),
                    OrderLogTypeEnum.ORDER_SPLIT.getKey(), "指定商品:" + skuCode + ",拆单,按数量拆存在多行,无法拆分", null, null, user);
            return true;
        }

        if (new BigDecimal(splitNum).compareTo(itemList.get(0).getQty()) > 0) {
            omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(),
                    OrderLogTypeEnum.ORDER_SPLIT.getKey(), "指定商品:" + skuCode + ",拆单,拆分数量大于可拆数量,无法拆分", null, null, user);
            return true;
        }

        //特殊拆单逻辑
        List<List<OcBOrderItem>> splitItem = new ArrayList<>();
        //非指定sku拆成一单
        if (CollectionUtils.isNotEmpty(bOrderItems)) {
            splitItem.add(bOrderItems);
        }

        //指定sku按照拆分数量分组
        OcBOrderItem item = itemList.get(0);
        BigDecimal splitNumBig = new BigDecimal(splitNum);
        int groupNum = qtyGroup(splitNumBig, item);

        BigDecimal orderQty = item.getQty();
        for (int i = 0; i < groupNum; i++) {
            List<OcBOrderItem> partList = Lists.newArrayList();
            OcBOrderItem orderItem = new OcBOrderItem();
            BeanUtils.copyProperties(item, orderItem);
            if (orderQty.compareTo(splitNumBig) <= 0) {
                orderItem.setQty(orderQty);
            } else {
                orderItem.setQty(splitNumBig);
                orderQty = orderQty.subtract(splitNumBig);
            }
            partList.add(orderItem);
            splitItem.add(partList);
        }

        List<List<OcBOrderItem>> splitItemNew = new ArrayList<>();
        for (List<OcBOrderItem> orderItemList : splitItem) {
            List<OcBOrderItem> orderItems1 = new ArrayList<>();
            for (OcBOrderItem ocBOrderItem : orderItemList) {
                OcBOrderItem orderItem = new OcBOrderItem();
                BeanUtils.copyProperties(ocBOrderItem, orderItem);
                orderItems1.add(orderItem);
            }
            splitItemNew.add(orderItems1);
        }
        boolean b = splitBeforeSourcingStService.insertNewOrders(splitItemNew, order, orderItems, user, null,
                "手动指定商品:" + skuCode + "拆单,作废原单", null, null, false, Lists.newArrayList(), Maps.newHashMap());
        AssertUtil.isTrue(b, "指定商品拆分失败");
        return true;
    }

    /**
     * 按照splitNum一组计算有几个组
     *
     * @param splitNum
     * @param item
     * @return
     */
    private int qtyGroup(BigDecimal splitNum, OcBOrderItem item) {
        BigDecimal orderQty = item.getQty();
        int fullGroups = orderQty.divide(splitNum, 0, RoundingMode.DOWN).intValue();
        BigDecimal remainder = orderQty.remainder(splitNum);
        return fullGroups + (remainder.compareTo(BigDecimal.ZERO) != 0 ? 1 : 0);
    }

    /**
     * 一单一货
     *
     * @param orderIds
     * @param user
     * @return
     */
    public ValueHolderV14 splitOne(List<Long> orderIds, User user, String splitReason) {
        ValueHolderV14 holder = new ValueHolderV14();
        List<Long> orderIdList = ocBOrderMapper.selectByIdsAndGwSourceGroup(orderIds, PlatFormEnum.DMS.getCode());
        if (CollectionUtils.isNotEmpty(orderIdList)) {
            // 查询订单明细 找到未退款的明细 并且取出reserveBigint字段值不为空的数据
            List<OcBOrderItem> ocBOrderItems = ocBOrderItemMapper.selectOrderItemsByIds(orderIdList);
            if (CollectionUtils.isNotEmpty(ocBOrderItems)) {
                // 找到未退款的明细 并且取出reserveBigint字段值不为空的数据
                List<OcBOrderItem> ocBOrderItems1 = ocBOrderItems.stream().filter(item -> item.getReserveBigint01() != null).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(ocBOrderItems1)) {
                    // 判断是否包含了
                    for (OcBOrderItem item : ocBOrderItems1) {
                        if (!Objects.equals(item.getReserveBigint01(), DmsGiftAttrEnum.OUR_PRODUCT.getVal())) {
                            // 抛异常
                            holder.setCode(ResultCode.FAIL);
                            holder.setMessage("DMS赠品订单不允许一单一货拆单，订单编号:" + item.getOcBOrderId());
                            return holder;
                        }
                    }
                }
            }
        }
        AsyncTaskBody asyncTaskBody = new AsyncTaskBody();
        asyncTaskBody.setTaskId(UUID.randomUUID().toString());
        asyncTaskBody.setMenu("一单一货拆单");
        asyncTaskBody.setTaskType("一单一货拆单");
        asyncTaskManager.beforeExecute(user, asyncTaskBody);
        commonTaskExecutor.submit(() -> {
            // 分组
            List<List<Long>> partition = Lists.partition(orderIds, 50);
            List<Future<Integer>> result = new ArrayList<>();
            for (List<Long> longs : partition) {
                Future<Integer> submit = splitOnePollExecutor.submit(new OneSplitTask(longs, user, splitReason));
                result.add(submit);
            }
            AtomicInteger failCount = new AtomicInteger();
            for (Future<Integer> integerFuture : result) {
                try {
                    Integer integer = integerFuture.get();
                    failCount.addAndGet(integer);
                } catch (Exception e) {
                    log.error(LogUtil.format("一单一货拆单失败:{}", "一单一货拆单"), Throwables.getStackTraceAsString(e));
                }
            }
            ValueHolderV14 holderV14 = new ValueHolderV14();
            holderV14.setCode(0);
            holderV14.setMessage("一单一货拆单共:" + orderIds.size() + "条,成功:" + (orderIds.size() - failCount.get()) + "条,失败:" + failCount.get() + "条");
            asyncTaskManager.afterExecute(user, asyncTaskBody, holderV14.toJSONObject());
        });
        holder.setCode(0);
        holder.setData(asyncTaskBody.getId());
        holder.setMessage("一单一货拆单任务开始！详情请在我的任务查看");
        return holder;
    }

    class OneSplitTask implements Callable<Integer> {

        public List<Long> orderIdList;
        public User user;
        public String splitReason;

        public OneSplitTask(List<Long> orderIdList, User user, String splitReason) {
            this.orderIdList = orderIdList;
            this.user = user;
            this.splitReason = splitReason;
        }

        @Override
        public Integer call() throws Exception {
            Integer failCount = 0;
            if (CollectionUtils.isEmpty(orderIdList)) {
                return failCount;
            }
            for (Long orderId : orderIdList) {
                String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(orderId);
                RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
                try {
                    if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                        OcBOrder order = ocBOrderMapper.selectById(orderId);
                        check(order);
                        handleSplitOne(order, user, splitReason);
                    } else {
                        AssertUtil.isTrue(false, "当前订单正在被其他人操作,请稍后再试");
                    }
                } catch (Exception e) {
                    log.error(LogUtil.format("一单一货拆单失败:{}", "一单一货拆单", orderId), Throwables.getStackTraceAsString(e));
                    failCount++;
                } finally {
                    redisLock.unlock();
                }
            }
            return failCount;
        }
    }

    public ValueHolderV14 getNumByItemIdAndQty(JSONObject obj, User user) {
        ValueHolderV14 holder = new ValueHolderV14();
        holder.setCode(ResultCode.FAIL);
        // 重要！！ 因为只要被勾选了一次 下次就不能再勾选 所以不需要前端传具体明细已经被使用的数量。后期如果允许勾选过再勾选 则需要调整
        JSONArray jsonArray = obj.getJSONArray("orderItemIds");
        if (CollectionUtils.isEmpty(jsonArray)) {
            holder.setMessage("明细不能为空");
            return holder;
        }
        BigDecimal totalQty = BigDecimal.valueOf(obj.getInteger("totalQty"));
        if (totalQty.equals(BigDecimal.ZERO)) {
            holder.setMessage("数量不能为空或者0");
            return holder;
        }
        // 先进行校验。先校验业务类型与平台
        // 将jsonArray转换成List<Long>
        List<Long> orderItemIds = jsonArray.toJavaList(Long.class);
        // 根据订单明细列表获取订单明细信息
        List<OcBOrderItem> ocBOrderItemList = ocBOrderItemMapper.selectBatchIds(orderItemIds);
        if (CollectionUtils.isEmpty(ocBOrderItemList)) {
            holder.setMessage("订单明细不能为空");
            return holder;
        }
        OcBOrder ocBOrder = ocBOrderMapper.selectByID(ocBOrderItemList.get(0).getOcBOrderId());
        boolean isToBOrder = OmsBusinessTypeUtil.isToBOrder(ocBOrder);
        Integer platform = Integer.valueOf(ocBOrder.getGwSourceGroup());
        if (!isToBOrder || !PlatFormEnum.DMS.getCode().equals(platform)) {
            holder.setMessage("一单一货拆单业务类型与平台不匹配");
            return holder;
        }

        // 一次只能包含一个sku
        if (ocBOrderItemList.stream().map(OcBOrderItem::getPsCSkuEcode).distinct().count() > 1) {
            holder.setMessage("一单一货拆单明细只能包含一个sku");
            return holder;
        }

        // 定义剩余可拆数量 数据等于totalQty
        BigDecimal remainQty = totalQty;
        // 计算所有ocBOrderItemList 中qty数量总和
        BigDecimal qtySum = ocBOrderItemList.stream().map(OcBOrderItem::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (qtySum.compareTo(totalQty) <= 0) {
            remainQty = qtySum;
        }
        List<DmsOrderManualSplitDTO> dmsOrderManualSplitDTOS = new ArrayList<>();

        // 获取所有的赠品属性 DMS下推的赠品属性都不会为空
        Map<Long, List<OcBOrderItem>> itemMap = ocBOrderItemList.stream().collect(Collectors.groupingBy(OcBOrderItem::getReserveBigint01));
        // 优先级 先分尾差 再分物料 再分 主品＞货补＞合同搭赠>活动搭赠(合同搭赠与活动搭赠不会同时存在)
        List<OcBOrderItem> tailDifferenceItemList = itemMap.get(DmsGiftAttrEnum.TAIL_DIFFERENCE.getVal());
        if (CollectionUtils.isNotEmpty(tailDifferenceItemList)) {
            BigDecimal splitNum = BigDecimal.ZERO;
            // 计算尾差分摊的数量 获取tailDifferenceItemList的所有的数量
            BigDecimal tailDifferenceQty = tailDifferenceItemList.stream().map(OcBOrderItem::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (tailDifferenceQty.compareTo(remainQty) > 0) {
                splitNum = remainQty;
                // 如果尾差数量大于总数量 则直接可以尾差数量
                remainQty = BigDecimal.ZERO;
            } else if (tailDifferenceQty.compareTo(remainQty) == 0) {
                // 尾差的数量正好等于总数量 则直接可以尾差数量
                splitNum = remainQty;
                remainQty = BigDecimal.ZERO;
            } else {
                remainQty = remainQty.subtract(tailDifferenceQty);
            }
            if (remainQty.equals(BigDecimal.ZERO)) {
                // 直接返回
                for (OcBOrderItem item : tailDifferenceItemList) {
                    DmsOrderManualSplitDTO dmsOrderManualSplitDTO = new DmsOrderManualSplitDTO();
                    dmsOrderManualSplitDTO.setSplitQty(splitNum.intValue());
                    dmsOrderManualSplitDTO.setOrderItemId(item.getId());
                    dmsOrderManualSplitDTOS.add(dmsOrderManualSplitDTO);
                }
                holder.setCode(ResultCode.SUCCESS);
                holder.setData(dmsOrderManualSplitDTOS);
                return holder;
            } else {
                for (OcBOrderItem item : tailDifferenceItemList) {
                    DmsOrderManualSplitDTO dmsOrderManualSplitDTO = new DmsOrderManualSplitDTO();
                    dmsOrderManualSplitDTO.setSplitQty(item.getQty().intValue());
                    dmsOrderManualSplitDTO.setOrderItemId(item.getId());
                    dmsOrderManualSplitDTOS.add(dmsOrderManualSplitDTO);
                }
            }
        }
        // 物料
        List<OcBOrderItem> materialItemList = itemMap.get(DmsGiftAttrEnum.MATERIAL.getVal());
        if (CollectionUtils.isNotEmpty(materialItemList)) {
            BigDecimal splitNum = BigDecimal.ZERO;
            // 计算尾差分摊的数量 获取materialItemList的所有的数量
            BigDecimal materialQty = materialItemList.stream().map(OcBOrderItem::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (materialQty.compareTo(remainQty) > 0) {
                // 如果尾差数量大于总数量 则直接可以尾差数量
                splitNum = remainQty;
                remainQty = BigDecimal.ZERO;
            } else if (materialQty.compareTo(remainQty) == 0) {
                // 尾差的数量正好等于总数量 则直接可以尾差数量
                splitNum = remainQty;
                remainQty = BigDecimal.ZERO;
            } else {
                remainQty = remainQty.subtract(materialQty);
            }
            if (remainQty.equals(BigDecimal.ZERO)) {
                // 直接返回
                for (OcBOrderItem item : materialItemList) {
                    DmsOrderManualSplitDTO dmsOrderManualSplitDTO = new DmsOrderManualSplitDTO();
                    dmsOrderManualSplitDTO.setSplitQty(splitNum.intValue());
                    dmsOrderManualSplitDTO.setOrderItemId(item.getId());
                    dmsOrderManualSplitDTOS.add(dmsOrderManualSplitDTO);
                }
                holder.setCode(ResultCode.SUCCESS);
                holder.setData(dmsOrderManualSplitDTOS);
                return holder;
            } else {
                for (OcBOrderItem item : materialItemList) {
                    DmsOrderManualSplitDTO dmsOrderManualSplitDTO = new DmsOrderManualSplitDTO();
                    dmsOrderManualSplitDTO.setSplitQty(item.getQty().intValue());
                    dmsOrderManualSplitDTO.setOrderItemId(item.getId());
                    dmsOrderManualSplitDTOS.add(dmsOrderManualSplitDTO);
                }
            }
        }

        // ocBOrderItemList去除tailDifferenceItemList与materialItemList 得到剩余的集合
        if (CollectionUtils.isNotEmpty(tailDifferenceItemList)) {
            ocBOrderItemList.removeAll(tailDifferenceItemList);
        }
        if (CollectionUtils.isNotEmpty(materialItemList)) {
            ocBOrderItemList.removeAll(materialItemList);
        }
        if (CollectionUtils.isEmpty(ocBOrderItemList)) {
            holder.setCode(ResultCode.SUCCESS);
            holder.setData(dmsOrderManualSplitDTOS);
            return holder;
        }
        // 如果还有剩余的 就来计算比例
        // 只有一个
        if (ocBOrderItemList.size() == 1) {
            BigDecimal splitNum = BigDecimal.ZERO;
            OcBOrderItem ocBOrderItem = ocBOrderItemList.get(0);
            if (ocBOrderItem.getQty().compareTo(remainQty) > 0) {
                // 如果尾差数量大于总数量 则直接可以尾差数量
                splitNum = remainQty;
                remainQty = BigDecimal.ZERO;
            } else if (ocBOrderItem.getQty().compareTo(remainQty) == 0) {
                // 尾差的数量正好等于总数量 则直接可以尾差数量
                splitNum = remainQty;
                remainQty = BigDecimal.ZERO;
            } else {
                remainQty = remainQty.subtract(ocBOrderItem.getQty());
            }
            if (remainQty.equals(BigDecimal.ZERO)) {
                // 直接返回
                DmsOrderManualSplitDTO dmsOrderManualSplitDTO = new DmsOrderManualSplitDTO();
                dmsOrderManualSplitDTO.setSplitQty(splitNum.intValue());
                dmsOrderManualSplitDTO.setOrderItemId(ocBOrderItem.getId());
                dmsOrderManualSplitDTOS.add(dmsOrderManualSplitDTO);
                holder.setCode(ResultCode.SUCCESS);
                holder.setData(dmsOrderManualSplitDTOS);
                return holder;
            } else {
                // 直接返回
                DmsOrderManualSplitDTO dmsOrderManualSplitDTO = new DmsOrderManualSplitDTO();
                dmsOrderManualSplitDTO.setSplitQty(ocBOrderItem.getQty().intValue());
                dmsOrderManualSplitDTO.setOrderItemId(ocBOrderItem.getId());
                dmsOrderManualSplitDTOS.add(dmsOrderManualSplitDTO);
                // 虽然有剩余的 但是已经计算完了 没有明细了 只能先返回
                holder.setCode(ResultCode.SUCCESS);
                holder.setData(dmsOrderManualSplitDTOS);
                return holder;
            }
        } else {
            // 开始计算比例
            BigDecimal totalItemQty = ocBOrderItemList.stream().map(OcBOrderItem::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (totalItemQty.compareTo(remainQty) <= 0) {
                for (OcBOrderItem item : ocBOrderItemList) {
                    DmsOrderManualSplitDTO dmsOrderManualSplitDTO = new DmsOrderManualSplitDTO();
                    dmsOrderManualSplitDTO.setSplitQty(item.getQty().intValue());
                    dmsOrderManualSplitDTO.setOrderItemId(item.getId());
                    dmsOrderManualSplitDTOS.add(dmsOrderManualSplitDTO);
                }
                holder.setCode(ResultCode.SUCCESS);
                holder.setData(dmsOrderManualSplitDTOS);
                return holder;
            }
            // 比例的计算
            BigDecimal ratio = remainQty.divide(totalItemQty, 4, BigDecimal.ROUND_HALF_DOWN);
            // 先根据比例 算出来每个明细需要扣除的数量 然后余数在分给剩余数量最多的那个明细
            BigDecimal remainTotal = BigDecimal.ZERO;
            List<DmsOrderManualSplitTempDTO> dmsOrderManualSplitTempDTOS = new ArrayList<>();
            for (OcBOrderItem item : ocBOrderItemList) {
                BigDecimal qty = item.getQty().multiply(ratio).setScale(0, BigDecimal.ROUND_DOWN);
                DmsOrderManualSplitTempDTO tempDTO = new DmsOrderManualSplitTempDTO();
                tempDTO.setOrderItemId(item.getId());
                tempDTO.setSplitQty(qty.intValue());
                tempDTO.setRemainingQty(item.getQty().subtract(qty).intValue());
                remainTotal = remainTotal.add(qty);
                dmsOrderManualSplitTempDTOS.add(tempDTO);
            }
            // 按比例算之后 还剩余的
            remainQty = remainQty.subtract(remainTotal);
            if (remainQty.compareTo(BigDecimal.ZERO) == 0) {
                for (DmsOrderManualSplitTempDTO item : dmsOrderManualSplitTempDTOS) {
                    DmsOrderManualSplitDTO dmsOrderManualSplitDTO = new DmsOrderManualSplitDTO();
                    dmsOrderManualSplitDTO.setSplitQty(item.getSplitQty());
                    dmsOrderManualSplitDTO.setOrderItemId(item.getOrderItemId());
                    dmsOrderManualSplitDTOS.add(dmsOrderManualSplitDTO);
                }
                holder.setCode(ResultCode.SUCCESS);
                holder.setData(dmsOrderManualSplitDTOS);
                return holder;
            }
            // 如果按比例算完之后 还有需要拆的 那就按照剩余可拆的数量进行排序
            dmsOrderManualSplitTempDTOS.sort(Comparator.comparing(DmsOrderManualSplitTempDTO::getRemainingQty).reversed());
            for (DmsOrderManualSplitTempDTO item : dmsOrderManualSplitTempDTOS) {
                if (remainQty.compareTo(BigDecimal.valueOf(item.getRemainingQty())) <= 0) {
                    item.setSplitQty(item.getSplitQty() + remainQty.intValue());
                    break;
                }
                remainQty = remainQty.subtract(BigDecimal.valueOf(item.getRemainingQty()));
                item.setSplitQty(item.getSplitQty() + item.getRemainingQty());
            }
            for (DmsOrderManualSplitTempDTO item : dmsOrderManualSplitTempDTOS) {
                DmsOrderManualSplitDTO dmsOrderManualSplitDTO = new DmsOrderManualSplitDTO();
                dmsOrderManualSplitDTO.setSplitQty(item.getSplitQty());
                dmsOrderManualSplitDTO.setOrderItemId(item.getOrderItemId());
                dmsOrderManualSplitDTOS.add(dmsOrderManualSplitDTO);
            }
            holder.setCode(ResultCode.SUCCESS);
            holder.setData(dmsOrderManualSplitDTOS);
            return holder;
        }
    }

    /**
     * 一单一货
     *
     * @param order
     * @param user
     */
    public void handleSplitOne(OcBOrder order, User user, String splitReason) {

        List<OcBOrderItem> ocBOrderItemList = ocBOrderItemMapper.selectOrderItemListOccupy(order.getId());
        //过滤掉贴纸赠品
        ocBOrderItemList = ocBOrderItemList.stream().filter(s ->
                !Objects.equals(s.getStickerGift(), YesNoEnum.Y.getVal())).collect(Collectors.toList());
        List<OcBOrderItem> orderItems = ocBOrderItemList.stream().filter(p -> p.getProType() != SkuType.NO_SPLIT_COMBINE).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(orderItems) && orderItems.size() == 1 && orderItems.get(0).getQty().compareTo(BigDecimal.ONE) == 0) {
            AssertUtil.isTrue(false, "一单一货拆单明细只有一条并且只有一个数量");
        }
        List<List<OcBOrderItem>> splitItem = new ArrayList<>();
        List<OcBOrderItem> group4Item = ocBOrderItemList.stream().filter(p -> p.getProType() == SkuType.NO_SPLIT_COMBINE).collect(Collectors.toList());
        Map<String, List<OcBOrderItem>> group2Map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(group4Item)) {
            List<OcBOrderItem> group2Item = orderItems.stream().filter(p -> p.getProType() == SkuType.COMBINE_PRODUCT || p.getProType() == SkuType.GIFT_PRODUCT).collect(Collectors.toList());
            group2Map = group2Item.stream().collect(Collectors.groupingBy(OcBOrderItem::getGroupGoodsMark));
        }
        List<OcBOrderItem> giftItem = orderItems.stream().filter(p -> p.getIsGift() != null && p.getIsGift() == 1 && p.getProType() == SkuType.NORMAL_PRODUCT).collect(Collectors.toList());
        List<OcBOrderItem> normalItem = orderItems.stream().filter(p -> (p.getIsGift() == null || p.getIsGift() == 0) && p.getProType() == SkuType.NORMAL_PRODUCT).collect(Collectors.toList());
        List<OcBOrderItem> relationGift = giftItem.stream().filter(p -> StringUtils.isNotEmpty(p.getGiftRelation()) && p.getIsGiftSplit() != null && p.getIsGiftSplit() == 1).collect(Collectors.toList());
        List<OcBOrderItem> gift = giftItem.stream().filter(p -> (p.getIsGiftSplit() == null || p.getIsGiftSplit() != 1)).collect(Collectors.toList());
        List<OcBOrderItem> noSplitGift = giftItem.stream().filter(p -> StringUtils.isEmpty(p.getGiftRelation()) && p.getIsGiftSplit() != null && p.getIsGiftSplit() == 1).collect(Collectors.toList());

        Map<String, List<OcBOrderItem>> relationGiftMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(relationGift)) {
            relationGiftMap = relationGift.stream().collect(Collectors.groupingBy(OcBOrderItem::getGiftRelation));
        }
        List<OcBOrderItem> noSplitItem = new ArrayList<>();
        this.handGroupOne(normalItem, group2Map, relationGiftMap, noSplitItem);
        if (CollectionUtils.isNotEmpty(gift)) {
            normalItem.addAll(gift);
        }
        if (CollectionUtils.isEmpty(normalItem)) {
            AssertUtil.isTrue(false, "一单一货拆单明细不可拆单");

        }
        if (CollectionUtils.isNotEmpty(noSplitGift)) {
            noSplitItem.addAll(noSplitGift);
        }
        for (OcBOrderItem orderItem : normalItem) {
            BigDecimal qty = orderItem.getQty();
            for (int i = 0; i < qty.intValue(); i++) {
                List<OcBOrderItem> ocBOrderItems = new ArrayList<>();
                OcBOrderItem item = new OcBOrderItem();
                BeanUtils.copyProperties(orderItem, item);
                item.setQty(BigDecimal.ONE);
                String giftRelation = orderItem.getGiftRelation();
                if (StringUtils.isNotEmpty(giftRelation)) {
                    List<OcBOrderItem> itemList1 = relationGiftMap.get(giftRelation);
                    if (CollectionUtils.isNotEmpty(itemList1)) {
                        ocBOrderItems.addAll(itemList1);
                        relationGiftMap.remove(giftRelation);
                    }
                }
                ocBOrderItems.add(item);
                splitItem.add(ocBOrderItems);
            }
        }
        if (CollectionUtils.isNotEmpty(noSplitItem)) {
            List<OcBOrderItem> collect = noSplitItem.stream().filter(p -> p.getIsGift() == null || p.getIsGift() == 0).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                splitItem.add(noSplitItem);
            } else {
                List<OcBOrderItem> bOrderItems = splitItem.get(0);
                if (CollectionUtils.isNotEmpty(bOrderItems)) {
                    bOrderItems.addAll(noSplitItem);
                }
            }
        }
        if (splitItem.size() == 1) {
            AssertUtil.isTrue(false, "一单一货拆单明细不可拆单");
        }

        List<List<OcBOrderItem>> splitItemNew = new ArrayList<>();
        for (List<OcBOrderItem> ocBOrderItems : splitItem) {
            List<OcBOrderItem> bOrderItems = new ArrayList<>();
            for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
                OcBOrderItem orderItem = new OcBOrderItem();
                BeanUtils.copyProperties(ocBOrderItem, orderItem);
                bOrderItems.add(orderItem);
            }
            splitItemNew.add(bOrderItems);
        }
        boolean b = splitBeforeSourcingStService.insertNewOrders(splitItemNew, order, ocBOrderItemList, user, null,
                "一单一货拆单,作废原单,原因:" + splitReason, null, null, false, Lists.newArrayList(), Maps.newHashMap());
        AssertUtil.isTrue(b, "一单一货拆分失败");
        ocBOrderItemExtService.deleteByOrderId(order.getId());
    }

    public ValueHolderV14 splitOrderByBoxStrategy(JSONObject obj, User user) throws NDSException {
        JSONArray ids = obj.getJSONArray("ids");
        if (ids == null || ids.size() == 0) {
            throw new NDSException(Resources.getMessage("请求参数不能为空!", user.getLocale()));
        }
        if (ids.size() > 2000) {
            throw new NDSException(Resources.getMessage("一次最多允许2000条!", user.getLocale()));
        }
        //
        int corePoolSize = 40;
        int batchSize = Math.max(1, ids.size() / corePoolSize);
        List<List<Object>> batchSplitBoxList = Lists.partition(ids, batchSize);
        List<Future<OmsOrderManualSplitNewService.BatchSplitBoxResult>> failCountList = new ArrayList<>();
        for (List<Object> batchSplitBox : batchSplitBoxList) {
            failCountList.add(splitBoxPollExecutor.submit(
                    new OmsOrderManualSplitNewService.BatchSplitBox(batchSplitBox, user)));
        }

        AtomicInteger failureCount = new AtomicInteger(0);
        JSONArray errorMessage = new JSONArray();
        for (Future<OmsOrderManualSplitNewService.BatchSplitBoxResult> failCount : failCountList) {
            try {
                OmsOrderManualSplitNewService.BatchSplitBoxResult splitBoxResult = failCount.get();
                failureCount.addAndGet(splitBoxResult.getFailNum());
                errorMessage.addAll(splitBoxResult.getErrorMessage());
            } catch (InterruptedException e) {
                log.error(LogUtil.format("箱型拆单InterruptedException异常：{}"), Throwables.getStackTraceAsString(e));
            } catch (ExecutionException e) {
                log.error(LogUtil.format("箱型拆单ExecutionException异常：{}"), Throwables.getStackTraceAsString(e));
            }
        }

        ValueHolderV14 vh = new ValueHolderV14();
        vh.setData(errorMessage);
        if (failureCount.get() == 0) {
            vh.setCode(ResultCode.SUCCESS);
            vh.setMessage(Resources.getMessage("箱型拆单成功", user.getLocale()));
        } else {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage("箱型拆单成功" + (ids.size() - failureCount.get()) +
                    "条，失败了" + failureCount.get() + "条", user.getLocale()));
        }
        return vh;
    }

    class BatchSplitBox implements Callable<OmsOrderManualSplitNewService.BatchSplitBoxResult> {

        User user;
        List<Object> ids;

        public BatchSplitBox(List<Object> ids, User user) {
            this.user = user;
            this.ids = ids;
        }

        @Override
        public OmsOrderManualSplitNewService.BatchSplitBoxResult call() throws Exception {
            OmsOrderManualSplitNewService.BatchSplitBoxResult splitBoxResult =
                    new BatchSplitBoxResult();
            JSONArray errorMessage = new JSONArray();
            Integer fail = 0;
            for (int i = 0; i < ids.size(); i++) {
                JSONObject jsonObject = new JSONObject();
                Long orderId = Long.valueOf(ids.get(i).toString());
                try {
                    omsOrderManualSplitNewService.splitBoxOrder(orderId, user);
                } catch (Exception e) {
                    log.info(LogUtil.format("箱型拆单，orderId:{},失败原因：{}", orderId), orderId, Throwables.getStackTraceAsString(e));
                    jsonObject.put("code", -1);
                    jsonObject.put("message", e.getMessage());
                    jsonObject.put("objid", ids.get(i));
                    errorMessage.add(jsonObject);
                    fail++;
                    String message = e.getMessage();
                    if (e.getMessage() != null && message.length() > 1000) {
                        message = message.substring(0, 1000);
                    }
                    omsOrderLogService.addUserOrderLog(orderId, null,
                            OrderLogTypeEnum.STRATEGY_SPLIT.getKey(), message, null, null, user);
                }
            }
            splitBoxResult.setFailNum(fail);
            splitBoxResult.setErrorMessage(errorMessage);
            return splitBoxResult;
        }
    }

    @Data
    static class BatchSplitBoxResult {
        private JSONArray errorMessage;
        private Integer failNum;
    }

    public void splitBoxOrder(Long orderId, User user) {
        log.info(LogUtil.format("OmsOrderManualSplitNewService.splitBoxOrder orderId:{}",
                "OmsOrderManualSplitNewService.splitBoxOrder"), orderId);
        // 给订单加锁
        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(orderId);
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                OcBOrder ocBOrder = ocBOrderMapper.selectByID(orderId);
                if (ocBOrder == null) {
                    throw new NDSException("订单不存在");
                }
                if (!(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(ocBOrder.getOrderStatus())
                        || OmsOrderStatus.UNCONFIRMED.toInteger().equals(ocBOrder.getOrderStatus()))) {
                    throw new NDSException("仅待寻源或待审核状态允许执行箱型拆单");
                }
                //执行箱型拆单
                StCShopStrategyDO stCShopStrategyDO =
                        omsStCShopStrategyService.selectOcStCShopStrategy(ocBOrder.getCpCShopId());
                if (!YesNoEnum.Y.getKey().equals(stCShopStrategyDO.getCanSplit())) {
                    throw new NDSException("店铺策略未开启拆单,不拆");
                }
                // 走一遍箱型拆单
                if (!YesNoEnum.ONE.getKey().equals(stCShopStrategyDO.getIsBoxSplit())) {
                    throw new NDSException("店铺策略未开启箱型拆单,不拆");
                }
                //查询订单明细
                List<OcBOrderItem> orderItems = ocBOrderItemMapper.selectOrderItemListOccupy(orderId);
                //过滤掉贴纸赠品
                orderItems = orderItems.stream().filter(s ->
                        !Objects.equals(s.getStickerGift(), YesNoEnum.Y.getVal())).collect(Collectors.toList());
                if (OmsOrderStatus.UNCONFIRMED.toInteger().equals(ocBOrder.getOrderStatus())) {
                    //封装数据
                    SgOmsShareOutRequest request = buildSgOmsShareOutRequest(ocBOrder, orderItems, user);
                    log.info("调用sg取消库存封装数据为：{}", JSON.toJSONString(request));
                    ValueHolderV14 sgValueHolder = sgRpcService.voidSgOmsShareOut(request, ocBOrder, orderItems);
                    AssertUtil.assertException(!sgValueHolder.isOK(), "释放库存失败");
                    log.info("调用sg取消库存返回接口数据为：{}", JSON.toJSONString(sgValueHolder));
                    //修改订单状态为待分配，重新占单
                    updateStatus(ocBOrder);
                }
                //过滤未拆分组合商品
                List<OcBOrderItem> orderItemList = orderItems.stream()
                        .filter(p -> p.getProType() != SkuType.NO_SPLIT_COMBINE).collect(Collectors.toList());
                String boxStrategyLogMessage = "";
                Collection<List<OcBOrderItem>> addSplitOrderItemsPool = new ArrayList<>();
                // 针对ocBOrderItemList 查询是否有命中到策略
                boxStrategyLogMessage = boxStrategyService.boxStrategyMatch(addSplitOrderItemsPool, orderItemList, boxStrategyLogMessage);
                if (CollectionUtils.isNotEmpty(addSplitOrderItemsPool) && addSplitOrderItemsPool.size() > 1) {
                    splitBeforeSourcingStService.insertNewOrders(addSplitOrderItemsPool, ocBOrder, orderItemList, user,
                            null, boxStrategyLogMessage, null, null, false, Lists.newArrayList(), Maps.newHashMap());
                } else {
                    throw new NDSException(Resources.getMessage("订单明细未匹配到箱型拆单策略", user.getLocale()));
                }
            } else {
                throw new NDSException(Resources.getMessage(" 当前订单其他人在操作，请稍后再试!", user.getLocale()));
            }
        } catch (Exception e) {
            log.info(LogUtil.format("OmsOrderManualSplitNewService.splitBoxOrder orderId:{},error:{}",
                    "OmsOrderManualSplitNewService.splitBoxOrder"), orderId, Throwables.getStackTraceAsString(e));
            throw new NDSException(e.getMessage());
        } finally {
            redisLock.unlock();
        }
    }

    /**
     * description:修改订单状态为
     *
     * @Author: liuwenjin
     * @Date 4:46 下午
     */
    private void updateStatus(OcBOrder ocBOrder) {
        ocBOrderMapper.updateWarehouse(ocBOrder.getId());
    }

    /**
     * <AUTHOR>
     * @Date 14:31 2021/7/30
     * @Description 封装sg、所用的数据
     */
    public SgOmsShareOutRequest buildSgOmsShareOutRequest(OcBOrder orderInfo, List<OcBOrderItem> ocBOrderItemList, User user) {
        SgOmsShareOutRequest request = new SgOmsShareOutRequest();
        request.setSourceBillId(orderInfo.getId());
        request.setSourceBillNo(orderInfo.getBillNo());
        request.setSourceBillType(SgConstantsIF.BILL_TYPE_RETAIL);
        request.setLoginUser(user);
        List<SgOmsShareOutItemRequest> itemRequestList = new ArrayList<>();
        for (OcBOrderItem ocBOrderItem : ocBOrderItemList) {
            SgOmsShareOutItemRequest sgOmsShareOutItemRequest = new SgOmsShareOutItemRequest();
            sgOmsShareOutItemRequest.setPsCSkuEcode(ocBOrderItem.getPsCSkuEcode());
            sgOmsShareOutItemRequest.setPsCSkuId(ocBOrderItem.getPsCSkuId());
            sgOmsShareOutItemRequest.setQtyPreout(ocBOrderItem.getQty());
            sgOmsShareOutItemRequest.setPsCSkuEcode(ocBOrderItem.getPsCSkuEcode());
            itemRequestList.add(sgOmsShareOutItemRequest);
        }
        request.setItemRequestList(itemRequestList);

        return request;
    }

    /**
     * 按行拆单-dms
     *
     * @param order
     * @param user
     * @param splitReason
     * @param ocBOrderItemList
     */
    public void handleSplitByRowDms(OcBOrder order, User user, String splitReason, List<OcBOrderItem> ocBOrderItemList) {
        List<OcBOrderItem> orderItems = ocBOrderItemList.stream().filter(p -> p.getProType() != SkuType.NO_SPLIT_COMBINE).collect(Collectors.toList());
        List<List<OcBOrderItem>> splitItem = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(orderItems)) {
            List<OcBOrderItem> group4Item = ocBOrderItemList.stream().filter(p -> p.getProType() == SkuType.NO_SPLIT_COMBINE).collect(Collectors.toList());
            Map<String, List<OcBOrderItem>> group2Map = new HashMap<>();
            if (CollectionUtils.isNotEmpty(group4Item)) {
                List<OcBOrderItem> group2Item = orderItems.stream().filter(p -> p.getProType() == SkuType.COMBINE_PRODUCT || p.getProType() == SkuType.GIFT_PRODUCT).collect(Collectors.toList());
                group2Map = group2Item.stream().collect(Collectors.groupingBy(OcBOrderItem::getGroupGoodsMark));
            }
            List<OcBOrderItem> normalItem = orderItems.stream().filter(p -> p.getProType() == SkuType.NORMAL_PRODUCT).collect(Collectors.toList());
            this.handGroup(splitItem, group2Map, new HashMap<>(), new ArrayList<>());
            if (CollectionUtils.isNotEmpty(normalItem)) {
                for (OcBOrderItem orderItem : normalItem) {
                    List<OcBOrderItem> ocBOrderItems = new ArrayList<>();
                    ocBOrderItems.add(orderItem);
                    splitItem.add(ocBOrderItems);
                }
            }
        }
        if (splitItem.size() == 1) {
            AssertUtil.isTrue(false, "订单只有一单,无法拆分");
        }

        Map<String, List<OcBOrderItem>> map = Maps.newHashMap();

        for (List<OcBOrderItem> ocBOrderItems : splitItem) {
            List<OcBOrderItem> bOrderItems = new ArrayList<>();
            for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
                String psCSkuEcode = ocBOrderItem.getPsCSkuEcode();
                OcBOrderItem orderItem = new OcBOrderItem();
                BeanUtils.copyProperties(ocBOrderItem, orderItem);
                if (map.containsKey(psCSkuEcode)) {
                    List<OcBOrderItem> items = map.get(psCSkuEcode);
                    items.add(orderItem);
                } else {
                    map.put(psCSkuEcode, Lists.newArrayList(orderItem));
                }

                bOrderItems.add(orderItem);
            }
        }
        boolean b = splitBeforeSourcingStService.insertNewOrders(map.values(), order, ocBOrderItemList, user, null,
                "手动按行拆单,作废原单,原因:" + splitReason, null, null, false, Lists.newArrayList(), Maps.newHashMap());
        AssertUtil.isTrue(b, "订单按行拆分失败");
        ocBOrderItemExtService.deleteByOrderId(order.getId());
    }

}
