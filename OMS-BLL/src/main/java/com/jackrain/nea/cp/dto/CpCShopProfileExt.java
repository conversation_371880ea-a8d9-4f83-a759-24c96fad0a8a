package com.jackrain.nea.cp.dto;

import com.jackrain.nea.cpext.model.table.CpCShopProfile;
import com.jackrain.nea.ps.model.ProductSku;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName CpCShopProfileExt
 * @Description 店铺品项明细拓展表
 * <AUTHOR>
 * @Date 2024/7/29 10:37
 * @Version 1.0
 */
@Data
public class CpCShopProfileExt implements Serializable {
    private static final long serialVersionUID = -8776018136411261356L;

    private CpCShopProfile cpCShopProfile;

    private ProductSku productSku;

    private String categoryCode;

    private String categoryName;
}
