package com.jackrain.nea.oc.oms.task.refundorder;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.es.ES4IpAlibabaAscpOrderRefund;
import com.jackrain.nea.oc.oms.model.relation.IpBAlibabaAscpOrderRefundRelation;
import com.jackrain.nea.oc.oms.process.transfer.impl.refund.alibaba.ascp.refund.AlibabaAscpOrderRefundTransferProcessImpl;
import com.jackrain.nea.oc.oms.services.IpBAlibabaAscpOrderRefundService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @Descroption 猫超直发退货转换
 * <AUTHOR>
 * @Date 2019/4/24 19:04
 */

@Slf4j
@Component
public class AutoAlibabaAscpOrderRefundTask extends BaseR3Task implements IR3Task {

    @Autowired
    private IpBAlibabaAscpOrderRefundService orderRefundService;
    @Autowired
    private AlibabaAscpOrderRefundTransferProcessImpl refundTransferProcess;

    @Override
    @XxlJob("AutoAlibabaAscpOrderRefundTask")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult result = new RunTaskResult();
        try {
            //1.遍历满足条件的订单数据
            List<String> list = ES4IpAlibabaAscpOrderRefund.findBizOrderCodeByTransStatus(0, DEFAULT_PAGE_SIZE);
            List<IpBAlibabaAscpOrderRefundRelation> relations = new ArrayList<>();
            for (String refundId : list) {
                if (StringUtils.isBlank(refundId)) {
                    continue;
                }
                IpBAlibabaAscpOrderRefundRelation ipBAlibabaAscpOrderRefundRelation =
                        orderRefundService.getAlibabaAscpRefundRelation(refundId);
                if (ipBAlibabaAscpOrderRefundRelation != null) {
                    relations.add(ipBAlibabaAscpOrderRefundRelation);
                }
            }
            //2.多线程处理订单数据
            threadOrderProcessor.startMultiThreadExecute(this.refundTransferProcess, relations);
            result.setSuccess(true);
        } catch (Exception e) {
            log.error(LogUtil.format("AutoAlibabaAscpOrderRefundTask.异常：{}", "猫超直发退货转换"), Throwables.getStackTraceAsString(e));
            result.setSuccess(false);
            result.setMessage(e.getMessage());
        }
        return result;
    }
}
