package com.jackrain.nea.oc.oms.process.transfer.impl.normal.taobao.step;

import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoOrder;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoOrderItemEx;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.taobao.TaoBaoOrderStatus;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.services.OmsOrderItemService;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.services.OmsTransferModifyAddrService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.third.enums.OmsBillTypeEnum;
import com.jackrain.nea.third.service.OmsToThirdService;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 卖家备注信息是否一致，不一致更新卖家备注信息
 *
 * @author: 易邵峰
 * @since: 2019-01-20
 * create at : 2019-01-20 02:47
 */
@Step(order = 70, description = "卖家备注信息是否一致，不一致更新卖家备注信息")
@Component
@Slf4j
public class Step070UpdateSellerRemark extends BaseTaobaoOrderProcessStep implements IOmsOrderProcessStep<IpTaobaoOrderRelation> {

    @Autowired
    private OmsOrderService orderService;
    @Autowired
    private OmsOrderItemService omsOrderItemService;
    @Autowired
    private OmsTransferModifyAddrService omsTransferModifyAddrService;

    @SuppressWarnings("unchecked")
    @Override
    public ProcessStepResult<IpTaobaoOrderRelation> startProcess(IpTaobaoOrderRelation orderInfo,
                                                                 ProcessStepResult preStepResult,
                                                                 boolean isAutoMakeup, User operateUser) {
        ProcessStepResult<IpTaobaoOrderRelation> stepResult = new ProcessStepResult<>();
        IpBTaobaoOrder taobaoOrder = orderInfo.getTaobaoOrder();
        String orderStatus = taobaoOrder.getStatus();
        String orderNo = orderInfo.getOrderNo();
        boolean status = TaoBaoOrderStatus.TRADE_CLOSED.equalsIgnoreCase(orderStatus)
                || TaoBaoOrderStatus.TRADE_CLOSED_BY_TAOBAO.equalsIgnoreCase(orderStatus)
                || TaoBaoOrderStatus.TRADE_FINISHED.equalsIgnoreCase(orderStatus);
        if (preStepResult.getNextStepOperateObj() != null) {
            try {
                List<OcBOrder> afterTransferOrderList = (List<OcBOrder>) preStepResult.getNextStepOperateObj();
                for (OcBOrder afterTransferOrder : afterTransferOrderList) {
                    if (afterTransferOrder == null) {
                        continue;
                    }
                    boolean isAddThird = (OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger().equals(afterTransferOrder.getOrderStatus())
                            || OmsOrderStatus.PLATFORM_DELIVERY.toInteger().equals(afterTransferOrder.getOrderStatus())) && status;
                    if (isAddThird) {
                        OmsToThirdService.addOmsToThirdPlatformStatus(afterTransferOrder.getTid(), OmsBillTypeEnum.UPDATE_PLATFORM_STATUS, "AC",orderStatus, operateUser);
                    }

                    String beforeMemo = "";
                    // merge ys code 202007-2
                    String beforeStatus = "";
                    if (orderInfo.getTaobaoOrder() != null) {
                        beforeMemo = orderInfo.getTaobaoOrder().getSellerMemo();
                        beforeStatus = orderInfo.getTaobaoOrder().getStatus();
                    }
                    String transferMemo = afterTransferOrder.getSellerMemo();
                    String transferStatus = afterTransferOrder.getPlatformStatus();
//                    if (TaoBaoOrderStatus.WAIT_BUYER_CONFIRM_GOODS.equals(beforeStatus) || TaoBaoOrderStatus.SELLER_CONSIGNED_PART.equals(beforeStatus)) {
//                        //更新订单明细的发货状态
//                        this.omsOrderItemService.updateOrderItemDeliveryStatus(orderInfo.getTaobaoOrderItemList(), afterTransferOrder.getId());
//                    }
                    if (!StringUtils.equalsIgnoreCase(beforeMemo, transferMemo)
                            || !StringUtils.equalsIgnoreCase(beforeStatus, transferStatus)) {
                        OcBOrder ocBOrder = new OcBOrder();
                        ocBOrder.setId(afterTransferOrder.getId());
                        if(StringUtils.isNotEmpty(beforeMemo) &&beforeMemo.length()>1000){
                            //卖家备注
                            ocBOrder.setSellerMemo(beforeMemo.substring(beforeMemo.length()-1000,beforeMemo.length()-1));
                        }else {
                            //卖家备注
                            ocBOrder.setSellerMemo(beforeMemo);
                        }
                        ocBOrder.setPlatformStatus(beforeStatus);
                        //修改预下沉状态
                        ocBOrder.setSuggestPresinkStatus(afterTransferOrder.getSuggestPresinkStatus());
                        ocBOrder.setActualPresinkStatus(afterTransferOrder.getActualPresinkStatus());
                        this.orderService.updateOrderInfo(ocBOrder);
//                        List<IpBTaobaoOrderItemEx> ipBTaobaoOrderItemExs = orderInfo.getTaobaoOrderItemList();
//                        if (CollectionUtils.isNotEmpty(ipBTaobaoOrderItemExs)) {
//                            for (IpBTaobaoOrderItemEx ipBTaobaoOrderItemEx : ipBTaobaoOrderItemExs) {
//                                String statusSub = ipBTaobaoOrderItemEx.getStatus();
//                                String oid = ipBTaobaoOrderItemEx.getOid().toString();
//                                if (TaobaoReturnOrderExt.SuborderStatus.TRADE_CLOSED.getCode().equals(statusSub)) {
//                                    boolean flag = orderService.handlePlatformGift(afterTransferOrder, oid, operateUser);
//                                    //如果失败  则将订单
//                                    if (!flag) {
//                                        return new ProcessStepResult<>(StepStatus.FAILED, "处理平台赠品退款失败!");
//                                    }
//                                }
//                            }
//                        }
                    }

                }
                String s = omsTransferModifyAddrService.transferModifyAddrService(afterTransferOrderList, orderInfo, operateUser, "taobao");
                if (StringUtils.isNotEmpty(s)){
                    this.ipTaoBaoOrderService.updateTaobaoOrderTransStatus(orderNo,
                            TransferOrderStatus.TRANSFERRED, s);
                    return new ProcessStepResult<>(StepStatus.FINISHED,"转单修改地址");
                }

            } catch (Exception ex) {
                ex.printStackTrace();
                log.error(LogUtil.format("Step070UpdateSellerRemark:{}", "Step070UpdateSellerRemark"), Throwables.getStackTraceAsString(ex));
            }

            stepResult.setMessage("更新卖家备注信息成功，进入下一阶段");
        } else {
            stepResult.setMessage("未进行更新卖家备注信息，进入下一阶段");
        }
        stepResult.setNextStepClass(Step080UpdateOrderTransferStatus.class);
        stepResult.setStatus(StepStatus.SUCCESS);
        return stepResult;
    }
}
