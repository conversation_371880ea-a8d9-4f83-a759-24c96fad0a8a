package com.jackrain.nea.oc.oms.services.task;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.ip.api.Invoice.InvoiceApplyDownloadCmd;
import com.jackrain.nea.ip.model.Invoice.JdInvoiceApplyDownloadRequest;
import com.jackrain.nea.ip.model.result.JdInvoiceApplyDownloadResult;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.table.AcFInvoiceApply;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.services.invoice.InvoiceApplySaveService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Auther: chenhao
 * @Date: 2022-10-13 10:27
 * @Description:
 */

@Slf4j
@Component
public class JdInvoiceTaskService {

    @Reference(group = "ip", version = "1.4.0")
    private InvoiceApplyDownloadCmd inTaskCmd;

    @Autowired
    private OcBOrderMapper orderMapper;

    private static final SimpleDateFormat FORMAT = new SimpleDateFormat("yyyy-MM-dd");

    @Autowired
    private InvoiceApplySaveService saveService;

    public ValueHolderV14 getJdInvoice() {

        log.info(LogUtil.format("JdInvoiceTaskService.getJdInvoice start", "JdInvoiceTaskService"));

        try {
            //查es获取到id，只查500 "2022-09-13" 系统上线时间
            List<Long> byIsInvoice = ES4Order.findByIsInvoice(FORMAT.parse("2022-09-13").getTime(), System.currentTimeMillis());
            if (CollectionUtils.isEmpty(byIsInvoice)) {
                return new ValueHolderV14(ResultCode.FAIL, "查询订单异常，未查询到数据！");
            }

            List<Integer> orderStatusList = new ArrayList<>();
            orderStatusList.add(OmsOrderStatus.CANCELLED.toInteger());
            orderStatusList.add(OmsOrderStatus.SYS_VOID.toInteger());

            List<OcBOrder> ocOrders = orderMapper.selectList(new LambdaQueryWrapper<OcBOrder>()
                    .in(OcBOrder::getId, byIsInvoice)
                    .notIn(OcBOrder::getOrderStatus, orderStatusList));

            if (CollectionUtils.isEmpty(ocOrders)) {
                return new ValueHolderV14(ResultCode.FAIL, "查询订单异常，未查询到发货单数据！");
            }

            log.info(LogUtil.format("JdInvoiceTaskService.getJdInvoice ocOrders:{}",
                    "JdInvoiceTaskService.ocOrders"), ocOrders.size());

            Map<Long, AcFInvoiceApply> invoiceApplyMap = new HashMap<>(16);

            //店铺分组 平台京东
            Map<Long, List<OcBOrder>> shopGroupingByMap = ocOrders.stream().collect(Collectors.groupingBy(OcBOrder::getCpCShopId));

            for (Long key : shopGroupingByMap.keySet()) {
                List<OcBOrder> ocOrderList = shopGroupingByMap.get(key);
                //去重复
                Map<Long, String> tidMap = new HashMap<>(16);
                Map<String, List<OcBOrder>> orderMap = ocOrderList.stream().collect(Collectors.groupingBy(OcBOrder::getTid));

                for (String orderMapKey : orderMap.keySet()) {
                    List<OcBOrder> orderList = orderMap.get(orderMapKey);
                    if (CollectionUtils.isNotEmpty(orderList)) {
                        tidMap.put(orderList.get(0).getId(), orderMapKey);
                    }
                }


                JdInvoiceApplyDownloadRequest jdInvoiceApplyDownloadRequest = new JdInvoiceApplyDownloadRequest();
                jdInvoiceApplyDownloadRequest.setShopId(key);
                jdInvoiceApplyDownloadRequest.setPlatformId(ocOrderList.get(0).getPlatform().longValue());
                jdInvoiceApplyDownloadRequest.setTidMap(tidMap);

                log.info(LogUtil.format("JdInvoiceTaskService.getJdInvoice jdInvoiceApplyDownloadRequest:{}",
                        "JdInvoiceTaskService.jdInvoiceApplyDownloadRequest"), JSONObject.toJSONString(jdInvoiceApplyDownloadRequest));

                ValueHolderV14<JdInvoiceApplyDownloadResult> v14 = inTaskCmd.JdDownloadInvoiceApply(jdInvoiceApplyDownloadRequest);

                log.info(LogUtil.format("JdInvoiceTaskService.getJdInvoice ValueHolderV14:{}",
                        "JdInvoiceTaskService.ValueHolderV14"), JSONObject.toJSONString(v14));

                if (v14.isOK()) {
                    JdInvoiceApplyDownloadResult data = v14.getData();
                    Map<Long, AcFInvoiceApply> requestMap = data.getRequestMap();
                    if (MapUtils.isNotEmpty(requestMap)) {
                        invoiceApplyMap.putAll(requestMap);
                    }
                }
            }

            if (MapUtils.isNotEmpty(invoiceApplyMap)) {
                ArrayList<AcFInvoiceApply> acInvoiceApplies = new ArrayList<>(invoiceApplyMap.values());
                ValueHolderV14 save = saveService.save(acInvoiceApplies);
                if (save.isOK()) {
                    List<String> tidList = acInvoiceApplies.stream().distinct().map(AcFInvoiceApply::getTid).collect(Collectors.toList());
                    OcBOrder update = new OcBOrder();
                    update.setReserveBigint05(1L);
                    orderMapper.update(update, new LambdaUpdateWrapper<OcBOrder>()
                            .set(OcBOrder::getReserveBigint05, 1L)
                            .in(OcBOrder::getTid, tidList));
                }
            }

        } catch (Exception e) {
            log.error("JD保存发票申请异常:{}", Throwables.getStackTraceAsString(e));
            return new ValueHolderV14(ResultCode.FAIL, "JD保存发票申请异常:" + e.getMessage());
        }


        return new ValueHolderV14(ResultCode.SUCCESS, "JD发票下载成功！");
    }


}
