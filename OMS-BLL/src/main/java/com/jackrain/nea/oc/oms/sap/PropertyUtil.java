package com.jackrain.nea.oc.oms.sap;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Desc :
 * <AUTHOR> xiwen
 * @Date : 2020/3/20
 */
public class PropertyUtil {

    private static Pattern humpPattern = Pattern.compile("[A-Z]");

    public static String hump2Line(String str) {
        Matcher matcher = humpPattern.matcher(str);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            matcher.appendReplacement(sb, "_" + matcher.group(0).toLowerCase());
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

}
