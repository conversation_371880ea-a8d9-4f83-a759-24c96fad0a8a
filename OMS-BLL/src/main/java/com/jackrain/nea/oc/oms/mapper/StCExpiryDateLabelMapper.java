package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.StCExpiryDateLabel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
public interface StCExpiryDateLabelMapper extends ExtentionMapper<StCExpiryDateLabel> {

    /**
     * 按照新开始生产日期倒序，新结束生产日期倒序查询所有有效汇波策略
     *
     * @return 所有汇波策略集合
     */
    @Select("select * from st_c_expiry_date_label where isactive = 'Y' order by new_start_date_day desc ,new_end_date_day desc")
    List<StCExpiryDateLabel> selectAllByRule();


    @Update({
            "<script>",
            "UPDATE st_c_expiry_date_label",
            "SET",
            "  bill_no = CASE id",
            "    <foreach collection='params' item='param' separator=' '>",
            "      <if test='param.billNo != null'>WHEN #{param.id} THEN #{param.billNo}</if>",
            "    </foreach>",
            "    ELSE bill_no END,",
            "  appoint_type = CASE id",
            "    <foreach collection='params' item='param' separator=' '>",
            "      <if test='param.appointType != null'>WHEN #{param.id} THEN #{param.appointType}</if>",
            "    </foreach>",
            "    ELSE appoint_type END,",
            "  start_date_day = CASE id",
            "    <foreach collection='params' item='param' separator=' '>",
            "      <if test='param.startDateDay != null'>WHEN #{param.id} THEN #{param.startDateDay}</if>",
            "    </foreach>",
            "    ELSE start_date_day END,",
            "  end_date_day = CASE id",
            "    <foreach collection='params' item='param' separator=' '>",
            "      <if test='param.endDateDay != null'>WHEN #{param.id} THEN #{param.endDateDay}</if>",
            "    </foreach>",
            "    ELSE end_date_day END,",
            "  order_label = CASE id",
            "    <foreach collection='params' item='param' separator=' '>",
            "      <if test='param.orderLabel != null'>WHEN #{param.id} THEN #{param.orderLabel}</if>",
            "    </foreach>",
            "    ELSE order_label END,",
            "  new_start_date_day = CASE id",
            "    <foreach collection='params' item='param' separator=' '>",
            "      <if test='param.newStartDateDay != null'>WHEN #{param.id} THEN #{param.newStartDateDay}</if>",
            "    </foreach>",
            "    ELSE new_start_date_day END,",
            "  new_end_date_day = CASE id",
            "    <foreach collection='params' item='param' separator=' '>",
            "      <if test='param.newEndDateDay != null'>WHEN #{param.id} THEN #{param.newEndDateDay}</if>",
            "    </foreach>",
            "    ELSE new_end_date_day END,",
            "  item_id = CASE id",
            "    <foreach collection='params' item='param' separator=' '>",
            "      <if test='param.itemId != null'>WHEN #{param.id} THEN #{param.itemId}</if>",
            "    </foreach>",
            "    ELSE item_id END,",
            "  main_id = CASE id",
            "    <foreach collection='params' item='param' separator=' '>",
            "      <if test='param.mainId != null'>WHEN #{param.id} THEN #{param.mainId}</if>",
            "    </foreach>",
            "    ELSE main_id END,",
            "  modifieddate = CASE id",
            "    <foreach collection='params' item='param' separator=' '>",
            "      <if test='param.modifiedDate != null'>WHEN #{param.id} THEN #{param.modifieddate}</if>",
            "    </foreach>",
            "    ELSE modifieddate END,",
            "  modifierid = CASE id",
            "    <foreach collection='params' item='param' separator=' '>",
            "      <if test='param.modifierId != null'>WHEN #{param.id} THEN #{param.modifierid}</if>",
            "    </foreach>",
            "    ELSE modifierid END,",
            "  modifiername = CASE id",
            "    <foreach collection='params' item='param' separator=' '>",
            "      <if test='param.modifierName != null'>WHEN #{param.id} THEN #{param.modifiername}</if>",
            "    </foreach>",
            "    ELSE modifiername END,",
            "  isactive = CASE id",
            "    <foreach collection='params' item='param' separator=' '>",
            "      <if test='param.isActive != null'>WHEN #{param.id} THEN #{param.isactive}</if>",
            "    </foreach>",
            "    ELSE isactive END",
            "WHERE id IN",
            "  <foreach collection='params' item='param' open='(' separator=',' close=')'>",
            "    #{param.id}",
            "  </foreach>",
            "</script>"
    })
    Integer batchUpdateById(@Param("params") List<StCExpiryDateLabel> params);
}