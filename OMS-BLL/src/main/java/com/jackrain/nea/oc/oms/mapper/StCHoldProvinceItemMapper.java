package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.StCHoldProvinceItemDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface StCHoldProvinceItemMapper extends ExtentionMapper<StCHoldProvinceItemDO> {


    @Select("<script> "
            + "SELECT * FROM ST_C_HOLD_PROVINCE_ITEM WHERE HOLD_ORDER_ID "
            + "in <foreach item='item' index='index' collection='mainIds' open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<StCHoldProvinceItemDO> selectStCHoldProvinceItemByMainIds(@Param("mainIds") List<Long> mainIds);

}
