package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.api.GetDetailCmd;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.result.GetOrderResult;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnType;
import com.jackrain.nea.oc.oms.model.table.OcBReturnTypeItem;
import com.jackrain.nea.oc.oms.services.GetDetailService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: wangqiang
 * @Date: 2019-03-07 10:42
 * @Version 1.0
 */


@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")

public class GetDetailCmdImpI extends CommandAdapter implements GetDetailCmd {
    @Autowired
    GetDetailService getDetailService;
    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;

    @Override
    public ValueHolder getDetail(JSONObject obj, User user) {
        ValueHolder detail = getDetailService.getDetail(obj, user);

        Long returnId = obj.getLong("SAP_ID");
        if(Objects.nonNull(returnId)) {
            OcBReturnOrder ocBReturnOrder = ocBReturnOrderMapper.selectById(returnId);
            GetOrderResult data = (GetOrderResult) (detail.getData().get("data"));
            if (data == null) {
                data = new GetOrderResult();
            }
            data.setReceiverMobile(ocBReturnOrder.getReceiveMobile());
            data.setReceiverPhone(ocBReturnOrder.getReceivePhone());
            data.setReceiverZip(ocBReturnOrder.getReceiveZip());
            data.setShipAmt(ocBReturnOrder.getShipAmt());
            data.setReceiverAddress(ocBReturnOrder.getReceiveAddress());
            data.setReceiverName(ocBReturnOrder.getReceiveName());
            data.setCpCRegionCityId(ocBReturnOrder.getReceiverCityId());
            data.setCpCRegionAreaId(ocBReturnOrder.getReceiverAreaId());
            data.setCpCRegionProvinceId(ocBReturnOrder.getReceiverProvinceId());
        }
        return detail;
    }

    @Override
    public ValueHolderV14<List<GetOrderResult>> getDetailList(List<JSONObject> jsonObjects, User user) {
        return getDetailService.getDetailList(jsonObjects, user);
    }

    @Override
    public ValueHolderV14<List<OcBOrderItem>> getOrderItem(Long mainId) {
        return getDetailService.getOrderItem(mainId);
    }

    @Override
    public ValueHolderV14<Map<OcBReturnType, List<OcBReturnTypeItem>>> selectTurnTypeByEname(String ename) {
        return getDetailService.getTurnTypeList(ename);
    }
}
