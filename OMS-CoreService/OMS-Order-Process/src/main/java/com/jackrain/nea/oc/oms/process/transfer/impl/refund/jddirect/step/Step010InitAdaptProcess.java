package com.jackrain.nea.oc.oms.process.transfer.impl.refund.jddirect.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.JDDApprovalState;
import com.jackrain.nea.oc.oms.model.enums.StepExeState;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OmsJDDirectCancelRelation;
import com.jackrain.nea.oc.oms.model.relation.StepExecInfo;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongDirectRefund;
import com.jackrain.nea.oc.oms.process.transfer.impl.refund.jddirect.JDDirectTransferSupply;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.util.OmsTransferSupply;
import com.jackrain.nea.web.face.User;
import org.springframework.stereotype.Component;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2022/3/26
 */
@Component
@Step(order = 10, description = "check and query")
public class Step010InitAdaptProcess extends AbstractJDDirectCancelProcessStep {


    @Override
    public ProcessStepResult<OmsJDDirectCancelRelation> startProcess(OmsJDDirectCancelRelation omsRelation,
                                                                     ProcessStepResult preStepResult,
                                                                     boolean isAutoMakeup, User operateUser) {
        // 0. init
        StepExecInfo stepExecInfo = OmsTransferSupply
                .initStepExecInfo(omsRelation.getIpOrderId(), omsRelation.getIpRefundNo())
                .assignUserInfo(operateUser).pointRelation(omsRelation);

        // 1. query
        omsJDDirectCancelService.getOmsJDDCancelRelation(omsRelation);
        StepExeState stepExeState = stepExecInfo.getStepExeState();
        if (StepExeState.UPDATE == stepExeState || StepExeState.FINAL == stepExeState) {
            return JDDirectTransferSupply.getTransStep(stepExeState);
        }

        // 2. status process
        IpBJingdongDirectRefund ipRefund = omsRelation.getIpRefund();
        Integer approvalStateValue = ipRefund.getApprovalState();
        stepExecInfo.setRelationShardKey(ipRefund.getCustomOrderId());
        return handleProcessStepResult(approvalStateValue, stepExecInfo);
    }

    /**
     * @param state
     * @return
     */
    private ProcessStepResult<OmsJDDirectCancelRelation> handleProcessStepResult(Integer state, StepExecInfo execInfo) {
        JDDApprovalState approvalState = JDDApprovalState.convert2Enum(state);
        ProcessStepResult<OmsJDDirectCancelRelation> stepResult;
        switch (approvalState) {
            case UN:
                stepResult = JDDirectTransferSupply.getAppointStepResult(Step030UnAuditProcess.class);
                break;
            case PASS:
                stepResult = JDDirectTransferSupply.getAppointStepResult(Step040AuditPassProcess.class);
                break;
            case NO:
                stepResult = JDDirectTransferSupply.getAppointStepResult(Step050AuditNoPassProcess.class);
                break;
            case UNDEFINED:
            default:
                execInfo.markStepInfo(StepExeState.UPDATE,
                        TransferOrderStatus.TRANSFERRED, "未知退款单审核状态");
                stepResult = JDDirectTransferSupply.getAppointStepResult(Step099UpdateBills.class);
                break;
        }
        return stepResult;
    }


}
