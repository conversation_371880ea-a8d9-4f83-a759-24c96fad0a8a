package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.oc.oms.model.request.TransferOrderRequest;
import com.jackrain.nea.oc.oms.model.result.TransferOrderResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;

/**
 * @author: ming.fz
 * @since: 2019-07-18
 * create at : 2019-07-18
 */
public interface StandplatTransferOrderCmd {

    ValueHolderV14<TransferOrderResult> startTransferOrder(TransferOrderRequest transferOrderRequest);
}
