package com.jackrain.nea.oc.oms.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @ClassName StCPreorderItemStrategyVO
 * @Description 订单预导入
 * <AUTHOR>
 * @Date 2023/2/3 18:07
 * @Version 1.0
 */
@Data
public class StCPreorderItemStrategyVO implements Serializable {

    private Long id;

    private Long preOrderModelStrategyId;

    private String itemName;

    private String skuEcode;

    private BigDecimal priceActual;
}
