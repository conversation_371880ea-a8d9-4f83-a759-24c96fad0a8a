package com.jackrain.nea.oc.oms.process.transfer.impl.normal.jingdong.step;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.mq.core.DefaultProducerSend;
import com.burgeon.mq.model.MqSendResult;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.log.LogCat;
import com.jackrain.nea.log.LogEvent;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.config.ToBeConfirmedOrderMqConfig;
import com.jackrain.nea.oc.oms.constant.Mq5Constants;
import com.jackrain.nea.oc.oms.matcher.MatchStrategyManager;
import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
import com.jackrain.nea.oc.oms.model.enums.AbnormalTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OperateType;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.jingdong.JingdongOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpJingdongOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.nums.OcBOrderNodeEnum;
import com.jackrain.nea.oc.oms.services.OcBOrderNodeRecordService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.resource.BllSystemParameterKeyResources;
import com.jackrain.nea.util.BllCommonUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> 孙俊磊
 * @since : 2019-04-25
 * create at : 2019-04-25 4:38 PM
 * 京东中间表转换全渠道订单表
 */
@Step(order = 70, description = "京东中间表转换全渠道订单表")
@Slf4j
@Component
public class Step70SaveOmsOrder extends BaseJingdongOrderProcessStep implements IOmsOrderProcessStep<IpJingdongOrderRelation> {

//    @Autowired
//    private R3MqSendHelper sendHelper;

    @Autowired
    private DefaultProducerSend defaultProducerSend;

    @Autowired
    private ToBeConfirmedOrderMqConfig orderMqConfig;

    @Autowired
    private OcBOrderNodeRecordService ocBOrderNodeRecordService;

    @Override
    public ProcessStepResult<IpJingdongOrderRelation> startProcess(IpJingdongOrderRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        /**
         * 转换主表，匹配省市区
         * 转换明细的时候先用outer_sku_id查询组合商品（组合商品分组合商品和福袋）
         * 查到：用查到的组合商品明细转换
         * 查不到：查条码档案转换
         * 转换其他子表
         * 转换成功，发MQ,更改京东中间表状态
         * 结束
         */
        IpBJingdongOrder order = orderInfo.getJingdongOrder();
        String orderState = order.getOrderState();
        boolean isHistoryOrder = StringUtils.equalsIgnoreCase(JingdongOrderStatus.WAIT_GOODS_RECEIVE_CONFIRM, orderState)
                || StringUtils.equalsIgnoreCase(JingdongOrderStatus.FINISHED_L, orderState);

        IpBJingdongOrder jingdongOrder = null;

        //仅用来更新
        IpBJingdongOrder updateOrder = new IpBJingdongOrder();
        Long orderId = order.getOrderId();
        try {
            IpJingdongOrderRelation relation = orderService.convertJdOrderToOrder(orderInfo, isHistoryOrder);
            if (relation == null) {
                throw new NDSException("平台订单生成系统订单结果为空");
            }
            jingdongOrder = relation.getJingdongOrder();
            if (jingdongOrder == null) {
                throw new NDSException("平台订单生成系统主单结果为空");
            }
            // @20200616 策略解析匹配
            log.info("策略解析匹配");
            strategyMatch(relation);
            // @20200710 打标
            //TaggerManager.get().doTag(relation.getOcBOrder(), relation.getOcBOrderItems());

            orderService.saveOrderInfo(relation, isHistoryOrder);
            if (BllCommonUtil.isOpen(null, BllSystemParameterKeyResources.OMS_TRANSFER_AUTO_MQ)) {
                sendMq(relation.getOcBOrder());
            }
            ocBOrderNodeRecordService.insertByNode(OcBOrderNodeEnum.ORDER_CREAT,new Date(),relation.getOcBOrder().getId(),operateUser);

            //更新转单状态,已转换
            ipJingdongOrderService.updateIpJdOrderAfterTransSuccess(orderId);
            return new ProcessStepResult<>(StepStatus.SUCCESS, "转单成功");
        } catch (NDSException ndsException) {
            ipJingdongOrderService.updateIpJingdongExchangeOrderInfo(TransferOrderStatus.TRANSFEREXCEPTION.toInteger(), orderId, "转单异常", AbnormalTypeEnum.MATE_ABNORMAL.getKey());
            return new ProcessStepResult<>(StepStatus.FAILED, "转单异常");
        } catch (Exception ex) {
            //转单失败，更新为未转换
            log.error(LogUtil.format("Step70SaveOmsOrder:{}", "Step70SaveOmsOrder"), Throwables.getStackTraceAsString(ex));
            updateOrder.setIstrans(TransferOrderStatus.NOT_TRANSFER.toInteger());
            updateOrder.setTransdate(new Date());
            String msg = "转换异常，原因："+ ex.getMessage();
            if (jingdongOrder == null) {
                ipJingdongOrderService.updateIpJingdongExchangeOrderInfo(TransferOrderStatus.NOT_TRANSFER.toInteger(), orderId, msg, null);
            } else {
                updateOrder.setTransCount(jingdongOrder.getTransCount() + 1L);
                updateOrder.setSysremark(msg);
                ipJingdongOrderService.updateIpJingdongOrderInfo(updateOrder, orderId);
            }
            return new ProcessStepResult<>(StepStatus.FAILED, msg);
        }
    }

    /**
     * 策略解析匹配
     *
     * @param relation
     */
    private void strategyMatch(IpJingdongOrderRelation relation) {
        if (Objects.nonNull(relation)) {
            MatchStrategyManager.get().match(relation, this.getCurrentChannelType(), relation.getOcBOrder(), relation.getOcBOrderItems());
        }
    }

    /**
     * 发送MQ消息，京东转换后全渠道新增订单的信息
     *
     * @param ocBOrder 全渠道订单实体
     */
    private void sendMq(OcBOrder ocBOrder) {
        LogEvent eventMQ = LogCat.newEvent(Step70SaveOmsOrder.class.getSimpleName(), "StartSendMQ");
        if (ocBOrder != null) {
            long saveOrderId = ocBOrder.getId();
            String billNo = ocBOrder.getBillNo();
            String msgKey = "JD_TR_" + saveOrderId + "_" + billNo;
            OperateOrderMqInfo orderMqInfo = new OperateOrderMqInfo();
            orderMqInfo.setChannelType(this.getCurrentChannelType());
            orderMqInfo.setOperateType(OperateType.TOBE_CONFIRMED);
            orderMqInfo.setOrderId(saveOrderId);
            orderMqInfo.setOrderNo(billNo);
            List<OperateOrderMqInfo> mqInfoList = new ArrayList<>();
            mqInfoList.add(orderMqInfo);
            String jsonValue = JSONObject.toJSONString(mqInfoList);

            String messageId = null;
            try {
//                messageId = sendHelper.sendMessage(jsonValue, orderMqConfig.getSendToBeConfirmMqTopic(),
//                        orderMqConfig.getSendToBeConfirmTag(),
//                        msgKey);
                MqSendResult result = defaultProducerSend.sendTopic(Mq5Constants.TOPIC_R3_OC_OMS_CALL_TOBECONFIRMED, Mq5Constants.TAG_R3_OC_OMS_CALL_TOBECONFIRMED, jsonValue, msgKey);
                messageId = result.getMessageId();
            } catch (Exception e) {
                e.printStackTrace();
            }

            eventMQ.addData("MQId", messageId);
            eventMQ.addData("MQKey", msgKey);
        }
        eventMQ.complete();
    }

}
