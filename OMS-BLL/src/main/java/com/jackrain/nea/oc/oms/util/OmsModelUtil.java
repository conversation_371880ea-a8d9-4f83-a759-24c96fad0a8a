package com.jackrain.nea.oc.oms.util;

import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.web.face.User;

import java.util.Date;

/**
 * Description： model处理工具类
 * Author: RESET
 * Date: Created in 2020/8/24 21:32
 * Modified By:
 */
public class OmsModelUtil {

    /**
     * 新增时设置默认值
     *
     * @param sourceModel
     * @param loginUser
     * @param <T>
     * @return
     */
    public static <T extends BaseModel> T setDefault4Add(T sourceModel, User loginUser) {
        Date systemDate = new Date();
        Long loginUserId = loginUser.getId() == null ? null : loginUser.getId().longValue();

        sourceModel.setAdClientId(Long.valueOf(loginUser.getClientId()));
        sourceModel.setAdOrgId(Long.valueOf(loginUser.getOrgId()));
        sourceModel.setIsactive(OcBOrderConst.IS_ACTIVE_YES);
        sourceModel.setOwnerid(loginUserId);
        sourceModel.setOwnername(loginUser.getName());
        sourceModel.setCreationdate(systemDate);
        sourceModel.setModifierid(loginUserId);
        sourceModel.setModifiername(loginUser.getName());
        sourceModel.setModifieddate(systemDate);

        return sourceModel;
    }

    /**
     * 更新时设默认值
     *
     * @param sourceModel
     * @param loginUser
     * @param <T>
     * @return
     */
    public static <T extends BaseModel> T setDefault4Upd(T sourceModel, User loginUser) {
        Date systemDate = new Date();
        Long loginUserId = loginUser.getId() == null ? null : loginUser.getId().longValue();

        sourceModel.setModifierid(loginUserId);
        sourceModel.setModifiername(loginUser.getName());
        sourceModel.setModifieddate(systemDate);

        return sourceModel;
    }

}
