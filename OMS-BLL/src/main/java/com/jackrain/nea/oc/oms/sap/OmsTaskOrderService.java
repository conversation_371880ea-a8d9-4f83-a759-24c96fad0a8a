package com.jackrain.nea.oc.oms.sap;

import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.function.BiFunction;
import java.util.function.Predicate;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2020/4/18
 */
@Slf4j
@Component
public class OmsTaskOrderService {

    @Autowired
    protected Oms2SapMapper oms2SapMapper;

    private final String nameSpaceKey = "application";
    private final String taskOrderKey = "oms.task.order.names";

    public ValueHolderV14 modifyTaskOrderStatus(List<Long> shardKeys, String tableName, Integer status) {

        // 1. get param
        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
        String tbNames = config.getProperty(taskOrderKey, null);

        // 2. validate param
        if (!(assertNotNull.and(assertLength).test(tbNames))) {
            return errorValue.apply(tbNames, "Properties Config Was Error In Apollo");
        }

        if (!(assertNotNull.and(assertLength).test(tableName))) {
            return errorValue.apply(tableName, "Param TableName Invalid");
        }

        if (!tbNames.contains(tableName)) {
            return errorValue.apply(tableName, "Param TableName Invalid That UnConfig In Apollo");
        }

        if (!(assertNotNull.and(assertSize).test(shardKeys))) {
            return errorValue.apply(shardKeys, "Param ShardKey Invalid");
        }

        if (!(assertNotNull.test(status))) {
            return errorValue.apply(status, "Param status Invalid");
        }

        // 3. update task order status
        int updateResult = oms2SapMapper.updateDynamicTaskOrder(tableName, status, shardKeys);
        return successValue.apply(updateResult, "Modify Success");

    }


    private Predicate assertNotNull = o -> o != null;

    private Predicate<String> assertLength = s -> s.length() > 11;

    private Predicate<Collection> assertSize = c -> c.size() > 0;

    private BiFunction<Object, String, ValueHolderV14> errorValue = (o, s) -> {
        ValueHolderV14 vh = new ValueHolderV14();
        vh.setCode(-1);
        vh.setData(o);
        vh.setMessage(s);
        return vh;
    };

    private BiFunction<Object, String, ValueHolderV14> successValue = (o, s) -> {
        ValueHolderV14 vh = new ValueHolderV14();
        vh.setCode(0);
        vh.setData(o);
        vh.setMessage(s);
        return vh;
    };


}
