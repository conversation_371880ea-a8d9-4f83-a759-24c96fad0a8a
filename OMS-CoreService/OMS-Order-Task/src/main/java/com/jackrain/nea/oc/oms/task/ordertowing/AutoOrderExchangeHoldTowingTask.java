//package com.jackrain.nea.oc.oms.task.ordertowing;
//
//import com.alibaba.fastjson.JSONObject;
//import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutItem;
//import com.burgeon.r3.sg.store.model.result.out.SgBStoOutQueryResult;
//import com.google.common.base.Throwables;
//import com.jackrain.nea.config.PropertiesConf;
//import com.jackrain.nea.ip.model.wing.add.WingOutStoOutNoticesOrderGoodsRequest;
//import com.jackrain.nea.ip.model.wing.add.WingOutStoOutNoticesRequest;
//import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
//import com.jackrain.nea.oc.oms.mapper.OrderExchangeHoldTowingTaskMapper;
//import com.jackrain.nea.oc.oms.model.table.OcBOrder;
//import com.jackrain.nea.oc.oms.nums.ExchangeHoldTowingcConstant;
//import com.jackrain.nea.oc.oms.services.OrderExchangeHoldTowingTaskService;
//import com.jackrain.nea.oc.oms.task.BaseR3Task;
//import com.jackrain.nea.r3.mq.util.R3MqSendHelper;
//import com.jackrain.nea.rpc.SgRpcService;
//import com.jackrain.nea.task.IR3Task;
//import com.jackrain.nea.task.RunTaskResult;
//import com.jackrain.nea.util.ApplicationContextHandle;
//import com.jackrain.nea.util.DateUtil;
//import com.jackrain.nea.util.RuntimeCompute;
//import com.jackrain.nea.utility.LogUtil;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.apache.commons.collections.MapUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.math.BigDecimal;
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Map;
//import java.util.stream.Collectors;
//
//
///**
// * description: 换货hold单传给wing占单
// * @Author:  liuwenjin
// * @Date 2021/10/19 10:28 下午
// */
//@Slf4j
//@Component
//public class AutoOrderExchangeHoldTowingTask extends BaseR3Task implements IR3Task {
//
//    @Autowired
//    private OrderExchangeHoldTowingTaskService orderExchangeHoldTowingTaskService;
//
//    @Autowired
//    private OrderExchangeHoldTowingTaskMapper orderExchangeHoldTowingTaskMapper;
//
//    @Autowired
//    private OcBOrderMapper ocBOrderMapper;
//
//    @Autowired
//    private SgRpcService sgRpcService;
//
//    @Autowired
//    private R3MqSendHelper r3MqSendHelper;
//
//    @Override
//    public RunTaskResult execute(JSONObject params) {
//        RunTaskResult result = new RunTaskResult();
//        RuntimeCompute runtimeCompute = new RuntimeCompute();
//        runtimeCompute.startRuntime();
//        result.setSuccess(true);
//        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
//        //默认查询总条数
//        Integer totalCount = config.getProperty("lts.ordertowing.exchangehold.totalcount", DEFAULT_PAGE_SIZE);
//        //拉取数据未成功的数据，默认200条修改时间正序  0未转换 1 是转换中  3 失败  4成功
//        List<Long> orderList = orderExchangeHoldTowingTaskMapper.queyList(totalCount);
//        if (CollectionUtils.isEmpty(orderList)){
//            double usedTime =runtimeCompute.endRuntime();
//            result.setSuccess(true);
//            result.setMessage("换货hold单传给wing任务执行成功!UsedTime=" + usedTime);
//            return result;
//        }
//        try {
//            List<OcBOrder> ocBOrderList = ocBOrderMapper.selectAuditOrderInfoListNotHold(orderList);
//            if (CollectionUtils.isEmpty(ocBOrderList)) {
//                result.setSuccess(true);
//                result.setMessage("未从数据库查询到有效的数据");
//                return result;
//            }
//            //调用库存的接口查询逻辑仓 sgQuerySgBStoOutByIds
//            List<SgBStoOutQueryResult> sgBStoOutQueryResultList= sgRpcService.sgQuerySgBStoOutByIds(orderList);
//            if (CollectionUtils.isEmpty(sgBStoOutQueryResultList)){
//                result.setSuccess(true);
//                result.setMessage("调用库存接口查询逻辑仓信息为空，定时任务结束");
//                return result;
//            }
//            log.info(LogUtil.format("调用库存接口查询逻辑仓信息为:{}",  "换货hold单传给wing占单"), JSONObject.toJSONString(sgBStoOutQueryResultList));
//            //转成 map  key：id  val：item
//            Map<Long,List<SgBStoOutItem>> sgBStoOutQueryMap = sgBStoOutQueryResultList.stream()
//                    .collect(Collectors.toMap(o->o.getMain().getSourceBillId(), o -> o.getItems(), (v, v1) -> v));
//            List<WingOutStoOutNoticesRequest> list = new ArrayList<>();
//            List<Long> ids= new ArrayList<>();
//            List<String> omList=new ArrayList<>();
//            if (CollectionUtils.isNotEmpty(ocBOrderList) && MapUtils.isNotEmpty(sgBStoOutQueryMap)){
//                for (OcBOrder order : ocBOrderList) {
//                    Long orderId = order.getId();
//                    if (CollectionUtils.isEmpty(sgBStoOutQueryMap.get(orderId))){
//                        continue;
//                    }
//                    ids.add(orderId);
//                    omList.add(order.getBillNo());
//                    List<SgBStoOutItem> sgBStoOutItemList = sgBStoOutQueryMap.get(orderId);
//                    //构建传wing参数
//                    WingOutStoOutNoticesRequest wingOutStoOutNoticesRequest = setProperties(order,sgBStoOutItemList);
//                    list.add(wingOutStoOutNoticesRequest);
//                }
//                if (CollectionUtils.isNotEmpty(list)){
//                    if (log.isDebugEnabled()){
//                        log.debug(LogUtil.format("AutoOrderExchangeHoldTowingTask.发生mq数据为{}",  "换货hold单传给wing占单"), JSONObject.toJSONString(list));
//                    }
//                    //发送mq
//                    sendMq(list,ids,omList);
//                }
//            }
//        }catch (Exception e){
//            log.error(LogUtil.format("AutoOrderExchangeHoldTowingTask.换货hold单传给wing任务失败 Error:{}",  "换货hold单传给wing占单"), Throwables.getStackTraceAsString(e));
//        }finally {
//            log.debug(LogUtil.format("SAutoOrderExchangeHoldTowingTask.不管定时任务执行成功失败都修改时间,orderList:{}",  "换货hold单传给wing占单"), orderList);
//            orderExchangeHoldTowingTaskService.updateOrderExchangeHoldTowing(orderList);
//        }
//        return result;
//    }
//    /**
//     * description:换货hold发送mq
//     * @Author:  liuwenjin
//     * @Date 2021/10/26 2:24 下午
//     */
//    private void sendMq(List<WingOutStoOutNoticesRequest> list,List<Long> ids,List<String> omList) {
//        try {
//            //获取Topic
//            PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
//            String topic = config.getProperty("r3.oc.oms.ordertowing.exchangehold.mq.topic", "BJ_DEV_R3_OC_OMS_EXCHANGE_HOLD");
//            String tag = config.getProperty("r3.oc.oms.ordertowing.exchangehold.mq.tag", "OperateExchangehold");
//            JSONObject jsonObject =new JSONObject();
//            jsonObject.put("data",list);
//            jsonObject.put("billNoList",omList);
//            String message = r3MqSendHelper.sendMessage(jsonObject, topic, tag);
//            log.info(LogUtil.format("换货hold发送mq返回结果为{}",  "换货hold单传给wing占单"), message);
//            if (log.isDebugEnabled()){
//                log.debug(LogUtil.format("AutoOrderExchangeHoldTowingTask.调用wing换货hold单发送成功{}",  "换货hold单传给wing占单"), ids);
//            }
//            //全部修改成传wing中中间状态
//            String remake = "调用wing换货hold单传Mq成功！";
//            orderExchangeHoldTowingTaskService.updateOrderExchangeHoldTowingStatus(ids,ExchangeHoldTowingcConstant.STATUS_1,remake);
//        } catch (Exception e) {
//            log.error(LogUtil.format("发送MQ异常!,异常！{}",  "换货hold单传给wing占单"), Throwables.getStackTraceAsString(e));
//        }
//    }
//
//    /**
//     * description: 赋值参数
//     * @Author:  liuwenjin
//     * @Date 2021/10/20 2:56 下午
//     */
//    private WingOutStoOutNoticesRequest setProperties(OcBOrder order, List<SgBStoOutItem> sgBStoOutItemList) {
//        WingOutStoOutNoticesRequest wingOutStoOutNoticesRequest = new  WingOutStoOutNoticesRequest();
//        //Long orderId = order.getId();
//        wingOutStoOutNoticesRequest.setId(order.getBillNo());
//        wingOutStoOutNoticesRequest.setTid(order.getTid());
//        wingOutStoOutNoticesRequest.setShopCode(order.getCpCShopEcode());
//        wingOutStoOutNoticesRequest.setShopName(order.getCpCShopTitle());
//        wingOutStoOutNoticesRequest.setPaytime(DateUtil.format(order.getPayTime(), DateUtil.dateTimeSecondsFormatter.getPattern()));
//        wingOutStoOutNoticesRequest.setAudittime(DateUtil.format(order.getAuditTime(), DateUtil.dateTimeSecondsFormatter.getPattern()));
//        wingOutStoOutNoticesRequest.setProductAmount(order.getProductAmt() == null ? "" : order.getProductAmt().toString());
//        wingOutStoOutNoticesRequest.setOrderAmount(order.getOrderAmt() == null ? "" : order.getOrderAmt().toString());
//        wingOutStoOutNoticesRequest.setShipAmount(order.getShipAmt() == null ? "" : order.getShipAmt().toString());
//        wingOutStoOutNoticesRequest.setReceivedAmount(order.getReceivedAmt() == null ? "" : order.getReceivedAmt().toString());
//        wingOutStoOutNoticesRequest.setUsernick(order.getUserNick());
//        wingOutStoOutNoticesRequest.setReceivname(order.getReceiverName());
//        wingOutStoOutNoticesRequest.setReceivprovincename(order.getCpCRegionProvinceEname());
//        wingOutStoOutNoticesRequest.setReceivcityname(order.getCpCRegionCityEname());
//        wingOutStoOutNoticesRequest.setReceivareaname(order.getCpCRegionAreaEname());
//        wingOutStoOutNoticesRequest.setReceivmobile(order.getReceiverMobile());
//        wingOutStoOutNoticesRequest.setOaid(order.getOaid());
//        wingOutStoOutNoticesRequest.setOrder_label("");
//        wingOutStoOutNoticesRequest.setReceivphone(order.getReceiverPhone());
//        wingOutStoOutNoticesRequest.setReceivaddress(order.getReceiverAddress() == null ? "" : order.getReceiverAddress().replaceAll("\\\\", "").replaceAll("\"", "“"));
//        wingOutStoOutNoticesRequest.setReceivzip(order.getReceiverZip());
//        // 构建wing参数明细
//        List<WingOutStoOutNoticesOrderGoodsRequest> listItem = new ArrayList<>();
//       // List<OcBOrderItem> itemList = orderItemMapper.selectOrderItemListOccupy(orderId);
//        BigDecimal allNum = BigDecimal.ZERO;
//        for (SgBStoOutItem sgBStoOutItem : sgBStoOutItemList) {
//            WingOutStoOutNoticesOrderGoodsRequest requestItem = new WingOutStoOutNoticesOrderGoodsRequest();
//            allNum = allNum.add(sgBStoOutItem.getQty() == null ? BigDecimal.ZERO : sgBStoOutItem.getQty());
//            requestItem.setId(sgBStoOutItem.getSourceBillItemId().toString());
//            requestItem.setOid(order.getBillNo());
//            requestItem.setTid(sgBStoOutItem.getTid());
//            requestItem.setWarehouseCode(sgBStoOutItem.getCpCStoreEcode());
//            requestItem.setSku(sgBStoOutItem.getPsCSkuEcode());
//            requestItem.setQty(sgBStoOutItem.getQty().intValue());
//            requestItem.setTag_price(sgBStoOutItem.getPriceList() == null ? "" : sgBStoOutItem.getPriceList().toString());
//            //requestItem.setPrice();
//            if (StringUtils.isEmpty(wingOutStoOutNoticesRequest.getWarehouseCode())) {
//                wingOutStoOutNoticesRequest.setWarehouseCode(sgBStoOutItem.getCpCStoreEcode());
//                wingOutStoOutNoticesRequest.setWarehouseName(sgBStoOutItem.getCpCStoreEname());
//            }
//            requestItem.setPrice("0");
//            listItem.add(requestItem);
//        }
//        wingOutStoOutNoticesRequest.setAllnum(allNum.intValue());
//        wingOutStoOutNoticesRequest.setOrderGoods(listItem);
//        return wingOutStoOutNoticesRequest;
//    }
//}
