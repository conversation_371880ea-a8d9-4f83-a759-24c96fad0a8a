package com.jackrain.nea.oc.oms.process.transfer.impl.refund.currency.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.TransNodeTipEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsStandPlatRefundRelation;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.process.transfer.impl.refund.taobao.util.OrderStatusUtil;
import com.jackrain.nea.oc.oms.refund.util.TransRefundNodeTipUtil;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2020/3/8 12:57 下午
 * @Version 1.0
 */
@Step(order = 50, description = "判断订单是否为虚拟订单")
@Slf4j
@Component
public class Step050CheckFictitiousStandPlatOrder extends BaseStanPlatRefundProcessStep
        implements IOmsOrderProcessStep<OmsStandPlatRefundRelation> {
    @Override
    public ProcessStepResult<OmsStandPlatRefundRelation> startProcess(OmsStandPlatRefundRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        List<OmsOrderRelation> omsOrderRelations = orderInfo.getOmsOrderRelation();
        IpBStandplatRefund ipBStandplatRefund = orderInfo.getIpBStandplatRefund();
        Integer returnStatus = ipBStandplatRefund.getReturnStatus();
        String status = TaobaoReturnOrderExt.RefundStandPlatStatus.getValueByKey(returnStatus);
        boolean closed = TaobaoReturnOrderExt.RefundStatus.CLOSED.getCode().equals(status);
        //判断订单是否为虚拟订单
        OcBOrder ocBOrder = omsOrderRelations.get(0).getOcBOrder();
        // 获取所有的订单 都需要判断下是否是虚拟订单
        for (OmsOrderRelation orderRelation : omsOrderRelations) {
            if (!OrderTypeEnum.DIFFPRICE.getVal().equals(orderRelation.getOcBOrder().getOrderType())) {
                //虚拟订单
                return new ProcessStepResult<>(StepStatus.SUCCESS, "不为虚拟订单,继续下一阶段转换");
            }
        }
        String remark = "生成发货后退款单(仅退款)成功";
        if (closed) {
            //退款关闭，取消已发货退款单
            omsRefundOrderService.closedRefundSlip(ipBStandplatRefund.getReturnNo());
            remark = "取消发货后退款单(仅退款)成功";
        } else {
            //按子订单维度创建发货后退款单（仅退款）
            List<OcBOrderItem> orderItems = new ArrayList<>();
            for (OmsOrderRelation orderRelation : omsOrderRelations) {
                boolean andVoid = OrderStatusUtil.checkOrderIsCancelAndVoid(orderRelation.getOcBOrder().getOrderStatus());
                if (andVoid) {
                    continue;
                }
                orderItems.addAll(orderRelation.getOcBOrderItems());
            }
            //根据退款单号查询退款
            omsStandPlatRefundOrderService.foundRefundSlipAfterRefundOnly(orderItems, ocBOrder, ipBStandplatRefund, operateUser, orderInfo);
            omsReturnOrderService.giftsThenSend(omsOrderRelations, orderInfo.getIsGiftOrderRelation(), orderInfo.getIntermediateTableRelation(), operateUser);
        }
        TransRefundNodeTipUtil.standardTransTipCAS(ipBStandplatRefund, TransNodeTipEnum.DEFAULT);
        ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                remark, ipBStandplatRefund);
        return new ProcessStepResult<>(StepStatus.FINISHED, remark);
    }
}
