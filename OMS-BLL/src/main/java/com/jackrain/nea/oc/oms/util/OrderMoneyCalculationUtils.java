package com.jackrain.nea.oc.oms.util;

import com.jackrain.nea.oc.oms.model.OrderMoney;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.ps.model.SkuType;

import java.math.BigDecimal;
import java.util.List;

import static com.jackrain.nea.oc.oms.util.BigDecimalUtil.isNullReturnZero;

/**
 * 订单相关金额计算
 *
 * @author: 王帅
 * @since: 2020/4/15
 * create at : 2020/4/15 9:59
 */
public class OrderMoneyCalculationUtils {

    /**
     * 通过订单明细（orderItemList），计算订单的相关金额（只算protype == 4 或 protype == 0）
     *
     * @param orderItemList
     */
    public static OrderMoney calculateOrderAmtByOrderItemList(List<OcBOrderItem> orderItemList) {
        OrderMoney orderMoney = new OrderMoney();
        // 商品优惠金额
        BigDecimal amtDiscountTotal = BigDecimal.ZERO;
        // 平摊金额之和
        BigDecimal orderSplitAmtTotal = BigDecimal.ZERO;
        // 调整金额之和
        BigDecimal adjustTotal = BigDecimal.ZERO;
        // 商品金额
        BigDecimal productAmtTotal = BigDecimal.ZERO;
        // 订单总额
        BigDecimal orderAmtTotal = BigDecimal.ZERO;

        for (OcBOrderItem ocBOrderItem : orderItemList) {
            // protype == 4 或 protype == 0
            if (ocBOrderItem.getProType() == SkuType.NO_SPLIT_COMBINE || ocBOrderItem.getProType() == SkuType.NORMAL_PRODUCT) {
                BigDecimal qty = isNullReturnZero(ocBOrderItem.getQty());
                // 商品金额 = 原价 * 数量
                productAmtTotal = productAmtTotal.add(isNullReturnZero(ocBOrderItem.getPrice()).multiply(qty)).setScale(2, BigDecimal.ROUND_HALF_UP);
                // 优惠金额计算
                amtDiscountTotal = amtDiscountTotal.add(isNullReturnZero(ocBOrderItem.getAmtDiscount()));
                // 平摊金额求和
                orderSplitAmtTotal = orderSplitAmtTotal.add(isNullReturnZero(ocBOrderItem.getOrderSplitAmt()));
                // 调整金额之和
                adjustTotal = adjustTotal.add(isNullReturnZero(ocBOrderItem.getAdjustAmt()));
                // 订单总额 = 订单明细成交金额之和
                orderAmtTotal = orderAmtTotal.add(isNullReturnZero(ocBOrderItem.getRealAmt()));
            }
        }
        /* 设定返回值*/
        orderMoney.setProductAmt(productAmtTotal);
        orderMoney.setOrderDiscountAmt(orderSplitAmtTotal);
        orderMoney.setAdjustAmt(adjustTotal);
        orderMoney.setProductDiscountAmt(amtDiscountTotal);
        orderMoney.setOrderAmt(orderAmtTotal);
        return orderMoney;
    }
}
