package com.jackrain.nea.oc.oms.mapper.task;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.enums.OcBRefundInTaskStatusEnum;
import com.jackrain.nea.oc.oms.model.table.task.OcBRefundInTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.apache.ibatis.annotations.UpdateProvider;

import java.util.List;
import java.util.Objects;


/**
 * 退货入库补偿任务表
 */
@Mapper
public interface OcBRefundInTaskMapper extends ExtentionMapper<OcBRefundInTask> {

    /**
     * 初始次数
     */
    int INIT_COUNT = 0;
    /**
     * 递增次数
     */
    int INCREMENT_COUNT = 1;
    /**
     * 最大次数
     */
    int MAX_COUNT = 5;


    /**
     * 分页查询退货入库补偿任务表,  状态为  初始和处理失败  且状态为可用, 按创建时间升序排列
     *
     * @param pageSize  pageSize
     * @param count     count
     * @param statusStr 状态
     * @return
     */
    @Select(" select * from oc_b_refund_in_task where failed_count < #{count} and bill_status in(${statusStr}) " +
            " and isactive ='Y' order by creationdate asc limit 0,#{pageSize} ")
    List<OcBRefundInTask> selectPageBySize(@Param("pageSize") Integer pageSize, @Param("count") Integer count
            , @Param("statusStr") String statusStr);

    /**
     * 按照退单 returnBillNo 更新状态
     *
     * @param wmsBillNo    wmsBillNo
     * @param returnBillNo returnBillNo
     * @param status       status
     * @param failReason   failReason
     * @return
     */
    @UpdateProvider(type = OcBRefundInTaskMapper.Sql.class, method = "updateMakeUpStatus")
    Integer updateMakeUpStatus(@Param("wmsBillNo") String wmsBillNo, @Param("returnBillNo") String returnBillNo
            , @Param("status") Integer status, @Param("failReason") String failReason);


    /**
     * 根据wmsBillNo查询中间表数据
     *
     * @param wmsBillNo wmsBillNo
     * @return
     */
    @Select(" select * from oc_b_refund_in_task where wms_bill_no = #{wmsBillNo} and isactive = #{active} ")
    List<OcBRefundInTask> selectByWmsBillNo(@Param("wmsBillNo") String wmsBillNo, @Param("active") String active);

    /**
     * 根据wms编号更新wms回传次数
     *
     * @param wmsBillNo
     * @return
     */
    @Update(" update oc_b_refund_in_task set  WMS_BACK_COUNT = IFNULL(WMS_BACK_COUNT,0) +1 ,modifieddate =now() " +
            " where wms_bill_no = #{wmsBillNo} ")
    int updateWmsCallBackCount(@Param("wmsBillNo") String wmsBillNo);

    class Sql {
        public String updateMakeUpStatus(@Param("wmsBillNo") String wmsBillNo, @Param("returnBillNo") String returnBillNo
                , @Param("status") Integer status
                , @Param("failReason") String failReason) {
            StringBuilder sql = new StringBuilder(" update oc_b_refund_in_task ");
            sql.append(" set bill_status = ").append(status);
            sql.append(" ,failed_reason = '").append(failReason).append("'");
            sql.append(" ,modifieddate = now() ");
            if (Objects.equals(status, OcBRefundInTaskStatusEnum.RESOLVE_FAIL.getVal())) {
                // 假如是失败
                sql.append(" ,failed_count =  ").append(MAX_COUNT);
            } else if (!Objects.equals(status, OcBRefundInTaskStatusEnum.SUCCESS.getVal())) {
                // 假如是失败
                sql.append(" ,failed_count = failed_count+1 ");
            }
            sql.append(" where return_bill_no = '").append(returnBillNo).append("'");
            sql.append(" and  wms_bill_no = '").append(wmsBillNo).append("'");
            return sql.toString();
        }
    }
}


