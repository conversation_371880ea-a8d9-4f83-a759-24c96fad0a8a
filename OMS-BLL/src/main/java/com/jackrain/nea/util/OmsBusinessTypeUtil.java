package com.jackrain.nea.util;

import com.jackrain.nea.oc.oms.model.enums.OrderBusinessTypeCodeEnum;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * @Author: 黄世新
 * @Date: 2022/9/23 下午2:01
 * @Version 1.0
 */
public class OmsBusinessTypeUtil {

    /**
     * SAP下发的TOC类型单据是否执行以下策略
     * （
     * SAP免费订单-TOC，编码RYCK19
     * SAP员工内购订单-TOC，编码RYCK20
     * SAP员工奶卡内购，编码RYCK23
     * SAP奶卡线下销售，编码RYCK24
     * SAP奶卡赠送出库，编码RYCK03
     * ）
     */
    public static boolean isSapToCOrder(OcBOrder order) {
        if (order == null) {
            return false;
        }
        String businessTypeCode = order.getBusinessTypeCode();
        if (StringUtils.isEmpty(businessTypeCode)) {
            return false;
        }
        return OrderBusinessTypeCodeEnum.SAP_FREE.getCode().equals(businessTypeCode)
                || OrderBusinessTypeCodeEnum.SAP_INSIDE.getCode().equals(businessTypeCode)
                || OrderBusinessTypeCodeEnum.SAP_MILK_CARD.getCode().equals(businessTypeCode)
                || OrderBusinessTypeCodeEnum.SAP_UNLINE_MILK_CARD.getCode().equals(businessTypeCode)
                || OrderBusinessTypeCodeEnum.SAP_GIVE.getCode().equals(businessTypeCode);
    }

    public static boolean isUnHoldToCOrder(OcBOrder order) {
        if (order == null) {
            return false;
        }
        String businessTypeCode = order.getBusinessTypeCode();
        if (StringUtils.isEmpty(businessTypeCode)) {
            return false;
        }
        return OrderBusinessTypeCodeEnum.SAP_INSIDE.getCode().equals(businessTypeCode)
                || OrderBusinessTypeCodeEnum.SAP_MILK_CARD.getCode().equals(businessTypeCode)
                || OrderBusinessTypeCodeEnum.SAP_UNLINE_MILK_CARD.getCode().equals(businessTypeCode)
                || OrderBusinessTypeCodeEnum.SAP_GIVE.getCode().equals(businessTypeCode);
    }

    /**
     * SAP寄售补货订单-TOB，编码RYCK16
     * SAP标准销售订单-TOB，编码RYCK17
     * SAP原材料销售订单-TOB，编码RYCK18）
     *
     * @param order
     * @return
     */
    public static boolean isToBOrder(OcBOrder order) {
        if (order == null) {
            return false;
        }
        String businessTypeCode = order.getBusinessTypeCode();
        if (StringUtils.isEmpty(businessTypeCode)) {
            return false;
        }
       return OrderBusinessTypeCodeEnum.SAP_CONSIGN_SALE.getCode().equals(businessTypeCode)
                || OrderBusinessTypeCodeEnum.SAP_STANDARD_SALE.getCode().equals(businessTypeCode)
                || OrderBusinessTypeCodeEnum.SAP_RAW_MATERIAL_SALE.getCode().equals(businessTypeCode);
    }



    public static boolean isToCOrderPt(OcBOrder order) {
        if (order == null) {
            return false;
        }
        String businessType = order.getBusinessType();
        if (StringUtils.isEmpty(businessType)) {
            return false;
        }
        return PtBusinessType.ZS03.getCode().equals(businessType)
                || PtBusinessType.ZS04.getCode().equals(businessType)
                || PtBusinessType.ZS05.getCode().equals(businessType);
    }


    public static boolean isToBOrderPt(OcBOrder order) {
        if (order == null) {
            return false;
        }
        String businessType = order.getBusinessType();
        if (StringUtils.isEmpty(businessType)) {
            return false;
        }
        return PtBusinessType.ZS01.getCode().equals(businessType)
                || PtBusinessType.ZS02.getCode().equals(businessType)
                || PtBusinessType.ZKB.getCode().equals(businessType);
    }


    /**
     * 平台业务类型枚举
     */
    @AllArgsConstructor
    @Getter
    public enum PtBusinessType{

        ZS01("ZS01", "tob类型"),
        ZS02("ZS02", "tob类型"),
        ZKB("ZKB", "tob类型"),
        ZS03("ZS03", "toc类型"),
        ZS04("ZS04", "toc类型"),
        ZS05("ZS05", "toc类型");

        private String code;

        private String message;

    }



}
