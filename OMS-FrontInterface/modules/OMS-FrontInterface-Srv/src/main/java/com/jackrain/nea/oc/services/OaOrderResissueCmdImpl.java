package com.jackrain.nea.oc.services;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.api.OaOrderResissueCmd;
import com.jackrain.nea.oc.oms.services.OaOrderResissueService;
import com.jackrain.nea.oc.oms.vo.OaOrderResissueVO;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName OaOrderResissueCmdImpl
 * @Description OA补发
 * <AUTHOR>
 * @Date 2024/10/12 14:51
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OaOrderResissueCmdImpl implements OaOrderResissueCmd {

    @Autowired
    private OaOrderResissueService oaOrderResissueService;

    @Override
    public ValueHolderV14 resissue(JSONObject jsonObject) {
        ValueHolderV14 valueHolderV14 = new ValueHolderV14<>();
        valueHolderV14.setCode(ResultCode.SUCCESS);
        JSONArray jsonArray = JSONArray.parseArray(jsonObject.getString("data"));
        // 校验行数 不能超过1000行
        if (jsonArray.size() > 1000) {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("补发的行数不能超过1000");
            return valueHolderV14;
        }
        List<OaOrderResissueVO> oaOrderResissueVOList = new ArrayList<>();
        for (Object o : jsonArray) {
            OaOrderResissueVO oaOrderResissueVO = new OaOrderResissueVO();
            JSONObject object = JSONObject.parseObject(JSONUtil.toJsonStr(o));
            oaOrderResissueVO.setTid(object.getString("tid"));
            oaOrderResissueVO.setPsCSkuEcode(object.getString("psCSkuEcode"));
            oaOrderResissueVO.setQty(object.getInteger("qty"));
            oaOrderResissueVO.setOwnername(object.getString("ownername"));
            oaOrderResissueVOList.add(oaOrderResissueVO);
        }
        return oaOrderResissueService.oaOrderResissue(oaOrderResissueVOList);
    }
}
