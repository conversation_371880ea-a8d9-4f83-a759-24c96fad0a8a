package com.jackrain.nea.oc.oms.process.tobeconfirm.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.process.tobeconfirm.util.TobeConfirmedUtil;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.oc.oms.util.SplitMessageUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

/**
 * 判断是否是待分配
 * <p>
 * 判断此订单是否为待分配状态。如果是非待分配状态则结束，现在允许缺货重新占单
 *
 * @author: 易邵峰
 * @since: 2019-01-18
 * create at : 2019-01-18 14:15
 */
@Step(order = 10, description = "判断此订单是否为待分配状态")
@Slf4j
public class Step010CheckOrderStatus extends BaseTobeConfirmedProcessStep implements IOmsOrderProcessStep<OcBOrderRelation> {

    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OmsOrderService omsOrderService;

    @Override
    public ProcessStepResult<OcBOrderRelation> startProcess(OcBOrderRelation orderInfo, ProcessStepResult preStepResult,
                                                            boolean isAutoMakeup, User operateUser) {
        // 重新从数据库查询订单信息,防止锁单前的数据已经被修改
        OcBOrder order = ocBOrderMapper.selectById(orderInfo.getOrderInfo().getId());
        orderInfo.setOrderInfo(order);
        if (orderInfo.getOrderInfo() == null) {
            return new ProcessStepResult<>(StepStatus.FINISHED, "无效对象。OrderId=" + orderInfo.getOrderId());
        } else {
            if (TobeConfirmedUtil.checkCanDistributeOrder(orderInfo) && orderInfo.getOrderInfo().getCpCShopId() != null) {
                //判断订单是否是待分配 或者缺货 && 店铺ID必须不为空
                String message = "订单OrderId=" + orderInfo.getOrderId() + "订单状态等于待分配,或者缺货状态.OrderStatus="
                        + orderInfo.getOrderInfo().getOrderStatus() + "开始执行占单服务流程!";
                return new ProcessStepResult<>(StepStatus.SUCCESS, message);
            } else if (orderInfo.getOrderInfo().getCpCShopId() == null) {
                OcBOrder ocbOrderInfo = new OcBOrder();
                String errorMessage = "订单OrderId=" + orderInfo.getOrderId() + "订单[下单店铺为空],退出占单服务流程!";
                log.error(errorMessage);
                ocbOrderInfo.setId(orderInfo.getOrderId());
                ocbOrderInfo.setSysremark(SplitMessageUtil.splitMesssage(errorMessage));
                ocbOrderInfo.setModifieddate(new Date());
                omsOrderService.updateOrderInfo(ocbOrderInfo);
                return new ProcessStepResult<>(StepStatus.FINISHED, errorMessage);
            } else {
                log.info(LogUtil.format("TobeConfirmed.Step10订单占单失败,订单状态={}","Step10订单占单失败",orderInfo.getOrderId()), orderInfo.getOrderInfo().getOrderStatus());
                return new ProcessStepResult<>(StepStatus.FINISHED, "订单OrderId=" + orderInfo.getOrderId() + "订单[订单状态不正确],退出占单服务流程!");
            }
        }
    }
}
