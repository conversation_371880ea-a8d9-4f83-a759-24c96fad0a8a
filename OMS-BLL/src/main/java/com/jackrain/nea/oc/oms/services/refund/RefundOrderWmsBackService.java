package com.jackrain.nea.oc.oms.services.refund;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.dto.ReturnOrderConfirmDTO;
import com.jackrain.nea.oc.oms.mapper.IOrderRefundInTaskMapper;
import com.jackrain.nea.oc.oms.model.constant.AcConstant;
import com.jackrain.nea.oc.oms.model.enums.OcBRefundInStatusEnum;
import com.jackrain.nea.oc.oms.model.table.task.OcBRefundInTask;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.utility.LogUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: 秦雄飞
 * @time: 2021/12/31 10:29 上午
 * @description:
 */

@Slf4j
@Component
@RequiredArgsConstructor
public class RefundOrderWmsBackService {

    private final IOrderRefundInTaskMapper refundInTaskMapper;

    public boolean consume(String msg) {

        if (log.isDebugEnabled()) {
            log.debug("refundOrderToWmsBackService.consume.msg:{}", msg);
        }

        OcBRefundInTask refundInTask = new OcBRefundInTask();
        try {
            ReturnOrderConfirmDTO returnOrderConfirmDTO = JSON.parseObject(msg, ReturnOrderConfirmDTO.class);
            ReturnOrderConfirmDTO.Request request = returnOrderConfirmDTO.getRequest();

            // 回传Wms编号
            refundInTask.setWmsBillNo(request.getReturnOrder().getReturnOrderId());
            // 单据类型
            refundInTask.setOrderType(request.getReturnOrder().getOrderType());
            // 退单单据编号
            refundInTask.setReturnBillNo(request.getReturnOrder().getReturnOrderCode());

        } catch (Exception e) {
            log.error(LogUtil.format("RefundOrderToWmsBackService.consume.error1:{}"), Throwables.getStackTraceAsString(e));
        }

        try {
            if (StringUtils.isNotBlank(refundInTask.getWmsBillNo())) {
                List<OcBRefundInTask> ocBRefundInTasks = refundInTaskMapper.selectRefundInTaskByWmsBillNo(refundInTask.getWmsBillNo());

                if (CollectionUtils.isNotEmpty(ocBRefundInTasks)) {
                    log.error("RefundOrderToWmsBackService.consume.wms编号重复:{}", refundInTask.getWmsBillNo());
                    return true;
                }
            }
            refundInTask.setId(ModelUtil.getSequence(AcConstant.OC_B_REFUND_IN_TASK));
            refundInTask.setMsg(msg);
            refundInTask.setBillStatus(OcBRefundInStatusEnum.RefundInTaskStatusEnum.INIT.getCode());
            refundInTask.setFailedCount(0);
            BaseModelUtil.initialBaseModelSystemField(refundInTask, SystemUserResource.getRootUser());

            refundInTaskMapper.insert(refundInTask);
        } catch (Exception e) {
            log.error(LogUtil.format("RefundOrderToWmsBackService.consume.error2:{}"), Throwables.getStackTraceAsString(e));
            return false;
        }
        return true;
    }
}