package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import io.swagger.annotations.ApiModel;
import lombok.AccessLevel;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName OcBOrderBnTask
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/25 14:10
 * @Version 1.0
 */
@TableName(value = "oc_b_order_bn_task")
@Data
@Document(index = "oc_b_order_bn_task", type = "oc_b_order_bn_task")
@NoArgsConstructor(access = AccessLevel.PUBLIC)
@ApiModel(value = "oc_b_order_bn_task", description = "班牛工单")
public class OcBOrderBnTask extends BaseModel {
    private static final long serialVersionUID = 1799447973369737986L;

    @Field(type = FieldType.Long)
    @JSONField(name = "ID")
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "BILL_NO")
    private String billNo;

    @JSONField(name = "TID")
    private String tid;

    @JSONField(name = "OC_B_ORDER_ID")
    private Long ocBOrderId;

    @JSONField(name = "PARAM")
    private String param;

}
