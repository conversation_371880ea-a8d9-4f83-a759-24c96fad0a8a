package com.jackrain.nea.oc.oms.matcher.live;

import com.jackrain.nea.oc.oms.matcher.vo.ParamInputVO;

/**
 * Description： TODO
 * Author: RESET
 * Date: Created in 2020/6/15 21:26
 * Modified By:
 */
public interface ILiveMatcher {

    /**
     * 解析匹配
     *
     * @param inputVO
     */
    public boolean doMatch(ParamInputVO inputVO);

    /**
     * 策略类型
     *
     * @return
     */
    public Integer getLiveStrategyType();

}
