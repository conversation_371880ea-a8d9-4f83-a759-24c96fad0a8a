package com.jackrain.nea.oc.oms.model.result;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 订单查询-分页
 *
 * @author: xiwen.z
 * create at: 2019/3/13 0013
 */
@Data
public class QueryOrderListResult implements Serializable {
    /**
     * 订单数据
     */
    private List<QueryOrderResult> queryOrderResultList;
    /**
     * 当前页
     */
    private Integer pageNum;
    /**
     * 每页条数
     */
    private Integer pageSize;
    /**
     * 总页数
     */
    private Integer totalNum;
    /**
     * 总记录数
     */
    private Long totalSize;
    /**
     * 旗帜
     */
    private List<QueryOrderFlagResult> orderFlagList;

    /**
     * 满足条件的id 集合
     */
    private List<Long> ids;

}
