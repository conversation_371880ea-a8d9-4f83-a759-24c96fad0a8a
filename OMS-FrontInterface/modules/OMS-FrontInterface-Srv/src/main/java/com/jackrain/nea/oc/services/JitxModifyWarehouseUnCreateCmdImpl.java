package com.jackrain.nea.oc.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.JitxModifyWarehouseUnCreateCmd;
import com.jackrain.nea.oc.oms.services.OcBJitxModifyWarehouseLogFiService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @create 2020-12-30
 * @desc JITX订单改仓中间表标记为未创建
 **/
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class JitxModifyWarehouseUnCreateCmdImpl extends CommandAdapter implements JitxModifyWarehouseUnCreateCmd {

    @Autowired
    private OcBJitxModifyWarehouseLogFiService ocBJitxModifyWarehouseLogFiService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return ocBJitxModifyWarehouseLogFiService.markUnCreate(session);
    }
}
