package com.jackrain.nea.resource;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2020/12/22
 */
public class RedisKeyConst {

    /**
     * st.店铺查找店铺价格方案
     */
    public static final String SHOP_PRICE_STRATEGY = "st:shop:price:strategy:shopid:";

    /**
     * st.店铺查找店铺价格策略信息
     */
    public static final String SHOP_PRICE_STRATEGY_INFO = "st:shop:price:strategyInfo:shopid:";

    /**
     * st.店铺查找派单方案以及规则
     */
    public static final String SHOP_SEND_PLAN_AND_RULE = "st:shop:send:plan:and:rule:shopid:";

    /**
     * 查询sku ECODE信息
     */
    public static final String PS_PRODUCT_SKU = "ps:product:sku:";

    /**
     * 查询skuid信息
     */
    public static final String PS_PRODUCT_SKUID = "ps:product:skuId:";

    /**
     * 平台条码与系统条码映射
     */
    public static final String PS_PRODUCT_SKUFORCODE = "ps:product:skuforcode:";

    /**
     * st.根据店铺id查找库存同步策略: 查找店铺供货逻辑仓优先级
     */
    public static final String SHOP_SYNC_STOCK_STRATEGY = "st:sync:stock:strategyitem:shopid:";

    /**
     * st.查询开启的订单推单延时策略
     */
    public static final String SHOP_ORDER_PUSH_DELAY = "st:order:push:delay:shopid:";

    /**
     * st.查询开启的订单推单延时策略
     */
    public static final String COOPERATION_NO = "st:order:shop:cooperationno:";

    /**
     * st.根据实体仓id,查询仓库拆单策略
     */
    public static final String PHY_WAREHOUSE_SPLIT_ORDER = "st:order:split:warehouseid:";

    /**
     * st.根据店铺ID查询有效的Hold策略
     */
    public static final String SHOP_HOLD_ORDER_ST = "st:order:hold:shopid:";


    /**
     * st.查找分仓比例的规则
     */
    public static final String ST_SEND_RULE_KEY = "st:send:rule:hashkey";

    /**
     * st.查询店铺档案是否开启AG
     */
    public static final String ST_LIVE_STRATEGY_KEY = "st:liveStrategy:";


    /*********************cp 组织中心key常量start *********************/
    /**
     * cp.所有平台信息
     */
    public static final String CP_PLATFORM_ALL = "cp:platform:all";

    /**
     * cp.物流公司名称
     */
    public static final String CP_LOGISTICS_OBJ = "cp:logistics:obj:";


    /**
     * cp.实体仓编码
     */
    public static final String CP_CPCWAREHOUSE_BYECODE_ECODE_JSON = "cp:cpcwarehouse:byecode:ecode:json:";

    /**
     * cp.门店信息
     */
    public static final String CP_SALESROOM_SESSIONKEY = "cp:salesroom:sessionkey:";

    /**
     * 物流公司
     */
    public static final String CP_LOGISTICS_BYID = "cp:logistics:byId:";

    /**
     * cp.所有物流公司
     */
    public static final String CP_LOGISTICSLIST = "cp:LogisticsList";


    public static final String CP_CPREGION_BYID = "cp:cpRegion:byId:";


    /**
     * cp.实体仓对应的逻辑仓集合
     */
    public static final String CP_CPSTORE_BYWAREHOUSEID = "cp:cpStore:byWarehouseId:";

    /**
     * cp.店铺
     */
    public static final String CP_SHOP = "cp:shop:";


    /**
     * cp.物流公司
     */
    public static final String CP_LOGISTICS = "cp:logistics:";

    /**
     * cp.实体仓
     */
    public static final String CP_PHYSICAL_WAREHOUSE_ID = "cp:physical:warehouse:id:";


    /**
     * cp.平台编码获取明细
     */
    public static final String CP_PLATFORM = "cp:platform:";

    /**
     * cp.根据逻辑仓ID查询实体仓ID
     */
    public static final String CP_LOGICAL_WAREHOUSE_KEY = "cp:logical:warehouse:hashkey";

    /**
     * cp.根据实体仓ID批量查询实体仓信息
     */
    public static final String CP_PHY_WAREHOUSE_KEY = "cp:phy:warehouse:hashkey";

    /**
     * 根据实体仓ID获取实体仓对象
     */
    public static final String CP_PHY_WAREHOUSE_ID_KEY = "cp:phy:warehouse:id:";

    /**
     * cp.根据逻辑仓ID 查询逻辑仓信息
     */
    public static final String CP_C_STORE_KEY = "cp:logical:store:id:";
    /*********************cp 组织中心key常量end *********************/

    /**
     * st.根据店铺ID查询有效的Hold策略
     */
    public static final String SHOP_DETENTION_ORDER_ST = "st:order:Detention:shopid:";

    /**
     * 唯品会地址编码
     */
    public static final String CP_VIP_ADDRESS_CODE_KEY = "cp:vip:addresscode:";

    /**
     * st.根据仓库id查询有效的定金预售预下沉策略
     */
    public static final String ST_DEPOSIT_PRE_SALE_SINK_KEY = "st:advance:sink:warehouseid:";

    /**
     * 根据店铺ID查询有效的订单打标策略
     */
    public static final String ST_C_ORDER_LABEL_KEY = "st:advance:label:shopid:";


    /**
     * 根据实体仓id查询key
     */
    public static final String ST_C_ORDER_EXPRESS_ALLOCATION_KEY = "st:express:allocation:";

    /**
     * 贴纸策略缓存key前缀
     */
    public static final String STICKER_CACHE_KEY_PREFIX = "oms:sticker:shop:";

    /**
     * 来源平台Key
     */
    public static final String CP_PLATFORM_KEY = "cp:CP_C_PLATFORM_KEY";

    public static final String CP_C_SOURCE_PLATFORM_KEY = "CP:CP_C_SOURCE_PLATFORM_KEY";

    public static final String CP_C_PLAT_LOGISTICS_KEY = "CP:CP_C_PLAT_LOGISTICS_KEY";

    /**
     * cp.主播
     */
    public static final String ARCHIVES_KEY = "cp:archives:";

    /**
     * ip.快递100 物流key
     */
    public static final String LOGISTICS_QUERY_KEY = "ip:logistics:query:";


    /**
     * st.根据ID查询有效的Hold策略原因
     */
    public static final String HOLD_ORDER_REASON_KEY = "st:order:holdReason:id:";

    /**
     * 查询sku ECODE信息
     */
    public static final String PS_JD_PRODUCT_SKU = "ps:jd:product:sku:wareid:";

}
