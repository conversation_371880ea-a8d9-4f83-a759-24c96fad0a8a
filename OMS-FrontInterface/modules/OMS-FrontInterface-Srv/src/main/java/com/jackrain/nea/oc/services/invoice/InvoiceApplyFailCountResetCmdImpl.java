package com.jackrain.nea.oc.services.invoice;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.ac.service.InvoiceApplyService;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.invoice.InvoiceApplyFailCountResetCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @ClassName InvoiceApplyFailCountResetCmdImpl
 * @Description 申请表失败次数重置
 * @Date 2022/9/19 上午10:07
 * @Created by wuhang
 */
public class InvoiceApplyFailCountResetCmdImpl extends CommandAdapter implements InvoiceApplyFailCountResetCmd {

    @Autowired
    private InvoiceApplyService service;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        ValueHolder vh = new ValueHolder();
        User user = querySession.getUser();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        JSONArray ids = param.getJSONArray("ids");
        if (ids.isEmpty()) {
            vh.put("code", ResultCode.FAIL);
            vh.put("message", "请勾选一条数据来操作");
            return vh;
        }
        return service.failCountReset(param, user);
    }
}
