package com.jackrain.nea.oc.oms.model.result;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @author: xiwen.z
 * create at: 2019/3/13 0013
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryOrderTagResult implements Serializable {
    // 值
    private String val;
    // 字段
    private String key;
    // 颜色
    private String clr;
    // 文本
    private String text;
    // 序号
    private int sort;

}
