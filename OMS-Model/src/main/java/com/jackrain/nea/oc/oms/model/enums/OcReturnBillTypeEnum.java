package com.jackrain.nea.oc.oms.model.enums;

/**
 * 退单类型选项值
 *
 * @author: 周琳胜
 * create at: 2019/3/20 13:20
 */
public enum OcReturnBillTypeEnum {

    /**
     * 退货
     */
    RETURN(1, "退货单"),

    /**
     * 换货
     */
    EXCHANGE(2, "退换货单"),

    /**
     * 补寄
     */
    REISSUE(3, "补寄");

    /**
     * 类型值
     */
    Integer val;

    /**
     * 类型描述
     */
    String text;

    OcReturnBillTypeEnum(Integer v, String k) {
        this.val = v;
        this.text = k;
    }

    public String getText() {
        return text;
    }

    public Integer getVal() {
        return val;
    }


}


