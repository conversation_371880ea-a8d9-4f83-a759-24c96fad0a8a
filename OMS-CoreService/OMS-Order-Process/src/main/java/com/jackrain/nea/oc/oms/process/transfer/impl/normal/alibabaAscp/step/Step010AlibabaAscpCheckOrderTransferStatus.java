package com.jackrain.nea.oc.oms.process.transfer.impl.normal.alibabaAscp.step;

import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpAlibabaAscpOrderRelation;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;


/**
 * @author: 秦雄飞
 * @date 2020/9/3 下午4:32
 */
@Step(order = 10, description = "判断猫超订单单据转换状态")
@Slf4j
@Component
public class Step010AlibabaAscpCheckOrderTransferStatus extends BaseAlibabaAscpOrderProcessStep
        implements IOmsOrderProcessStep<IpAlibabaAscpOrderRelation> {

    @Override
    public ProcessStepResult<IpAlibabaAscpOrderRelation> startProcess(IpAlibabaAscpOrderRelation orderInfo,
                                                                      ProcessStepResult preStepResult,
                                                                      boolean isAutoMakeup, User operateUser) {
        if (orderInfo == null || orderInfo.getAlibabaAscpOrder() == null) {
            return new ProcessStepResult<>(StepStatus.FAILED, "orderInfo或orderNo为空退出转换");
        }

        if (CollectionUtils.isEmpty(orderInfo.getAlibabaAscpOrderItemExList())) {
            String orderNo = orderInfo.getOrderNo();
            String message = "订单明细为空转换失败!";
            ipAlibabaAscpOrderService.updateAlibabaAscpOrderTransStatus(orderNo,
                    TransferOrderStatus.TRANSFERFAIL, message);
            return new ProcessStepResult<>(StepStatus.FAILED, "orderInfo.getAlibabaAscpOrderItemExList为空；退出转换");
        }

        int currentStatus = TransferOrderStatus.NOT_TRANSFER.toInteger();
        if (orderInfo.getAlibabaAscpOrder().getIsTrans() != null) {
            currentStatus = orderInfo.getAlibabaAscpOrder().getIsTrans();
        }

        if (TransferOrderStatus.TRANSFERRED.toInteger() == currentStatus) {
            String operateMessage = Resources.getMessage("单据" + orderInfo.getOrderId() + "状态=已转换，转换完成");
            return new ProcessStepResult<>(StepStatus.FINISHED, operateMessage);
        } else if (TransferOrderStatus.TRANSFERRING.toInteger() == currentStatus) {
            String operateMessage = Resources.getMessage("单据" + orderInfo.getOrderId() + "正在转换中");
            return new ProcessStepResult<>(StepStatus.FINISHED, operateMessage);
        } else {
            String operateMessage = Resources.getMessage("单据" + orderInfo.getOrderId() + "检查状态成功，进入下一阶段");
            return new ProcessStepResult<>(StepStatus.SUCCESS, operateMessage);
        }
    }
}
