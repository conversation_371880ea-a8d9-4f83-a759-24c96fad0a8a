package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.IpBVipReturnOrderItem;
import com.jackrain.nea.oc.oms.model.table.IpBVipReturnOrderItemEx;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface IpBVipReturnOrderItemMapper extends ExtentionMapper<IpBVipReturnOrderItem> {

    /**
     * 根据外键查询退供单中间表子表数据
     *
     * @param id 主表ID
     * @return 明细集合
     */
    @Select("select * from ip_b_vip_return_order_item where ip_b_vip_return_order_id = #{id}")
    List<IpBVipReturnOrderItemEx> selectVipReturnOrderItemByForeignId(Long id);
}