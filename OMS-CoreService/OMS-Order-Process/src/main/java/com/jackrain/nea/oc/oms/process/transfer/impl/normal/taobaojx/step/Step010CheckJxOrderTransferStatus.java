package com.jackrain.nea.oc.oms.process.transfer.impl.normal.taobaojx.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoJxOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoJxOrder;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description 判断淘宝经销订单单据转换状态
 **/
@Step(order = 10, description = "判断淘宝经销订单单据转换状态")
@Slf4j
@Component
public class Step010CheckJxOrderTransferStatus extends BaseTaobaoJxOrderProcessStep
        implements IOmsOrderProcessStep<IpTaobaoJxOrderRelation> {
    @Override
    public ProcessStepResult<IpTaobaoJxOrderRelation> startProcess(
            IpTaobaoJxOrderRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        try {
            if (null == orderInfo || null == orderInfo.getTaobaoJxOrder()) {
                log.error(this.getClass().getName() + " 日志服务:OrderInfo为空或者Order.TaobaoJxOrder为空");
                return new ProcessStepResult<>(StepStatus.FAILED, "OrderInfo为空或者Order.TaobaoJxOrder为空；退出转换！");
            }
            //1.状态非未转换，则不执行逻辑
            IpBTaobaoJxOrder taobaoJxOrder = orderInfo.getTaobaoJxOrder();
            if (TransferOrderStatus.NOT_TRANSFER.toInteger() != taobaoJxOrder.getIstrans()) {
                ipTaobaoJxOrderService.updateIsTrans(TransferOrderStatus.parse(taobaoJxOrder.getIstrans().toString()),
                        "当前状态非转换状态，不允许单据转换！", orderInfo.getTaobaoJxOrder());
            }
            return new ProcessStepResult<>(StepStatus.SUCCESS, "单据" + orderInfo.getOrderNo() + "检查状态成功，进入下一阶段！");
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 淘宝经销订单转换异常！", e);
            //修改中间表状态及系统备注
            if (orderInfo != null) {
                ipTaobaoJxOrderService.updateIsTransError(orderInfo.getTaobaoJxOrder(), e.getMessage());
            }
            String errorMessage = "淘宝经销订单转换异常！" + e.getMessage();
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
    }
}
