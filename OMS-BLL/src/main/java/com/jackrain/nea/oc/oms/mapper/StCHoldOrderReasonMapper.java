package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.StCHoldOrderReason;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface StCHoldOrderReasonMapper extends ExtentionMapper<StCHoldOrderReason> {

    @Select("<script> "
            + "SELECT COUNT(1) FROM ST_C_HOLD_ORDER_REASON WHERE TYPE = #{type} AND REASON = #{reason} AND ISACTIVE = 'Y' "
            + "</script>")
    int selectExistCount(@Param("type") Integer type, @Param("reason") String reason);

    @Select("<script> "
            + "SELECT * FROM ST_C_HOLD_ORDER_REASON WHERE ISACTIVE = 'Y' "
            + "</script>")
    List<StCHoldOrderReason> selectAllHoldOrder();

    /**
     * 根据类型和原因查询有效的原因（理论上只会有一条返回值）
     *
     * @param type
     * @param reason
     * @return
     */
    @Select("<script> SELECT * FROM st_c_hold_order_reason WHERE type = #{type} AND reason = #{reason} AND isactive = 'Y' </script>")
    List<StCHoldOrderReason> selectByTypeAndReason(@Param("type") Integer type, @Param("reason") String reason);
}
