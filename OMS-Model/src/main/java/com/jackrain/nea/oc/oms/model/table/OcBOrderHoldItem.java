package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@TableName(value = "oc_b_order_hold_item")
@Data
@Document(index = "oc_b_order_hold_item", type = "oc_b_order_hold_item")
public class OcBOrderHoldItem extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "OC_B_ORDER_ID")
    @Field(type = FieldType.Long)
    private Long ocBOrderId;

    @JSONField(name = "st_c_hold_order_id")
    @Field(type = FieldType.Long)
    private Long stCHoldOrderId;

    @JSONField(name = "hold_order_reason")
    @Field(type = FieldType.Integer)
    private Integer holdOrderReason;

    @JSONField(name = "hold_order_reason_msg")
    @Field(type = FieldType.Keyword)
    private String holdOrderReasonMsg;

    @JSONField(name = "hold_status")
    private String holdStatus;

    @JSONField(name = "release_time")
    private Date releaseTime;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;

    /**
     * 策略的name
     */
    @Field(type = FieldType.Keyword)
    @JSONField(name = "ST_C_HOLD_ORDER_ENAME")
    private String stCHoldOrderEname;
}