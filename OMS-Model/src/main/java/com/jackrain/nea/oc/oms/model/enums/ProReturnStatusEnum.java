package com.jackrain.nea.oc.oms.model.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 退货状态
 *
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2020/4/1
 */
public enum ProReturnStatusEnum {

    WAIT(0, "待入库"),
    PORTION(1, "部分入库"),
    WHOLE(2, "全部入库");

    Integer val;
    String txt;

    ProReturnStatusEnum(Integer val, String txt) {
        this.val = val;
        this.txt = txt;
    }

    public static Map<Integer, String> getKeyValueMap() {
        Map<Integer, String> map = new HashMap<>();
        for (ProReturnStatusEnum proEnum : ProReturnStatusEnum.values()) {
            map.put(proEnum.getVal(), proEnum.getTxt());
        }
        map.put(null, "");
        return map;
    }

    public Integer getVal() {
        return val;
    }

    public String getTxt() {
        return txt;
    }


}
