package com.jackrain.nea.oc.oms.services;

import com.alibaba.dubbo.config.annotation.Service;
import com.jackrain.nea.annotation.OmsOperationLog;
import com.jackrain.nea.cpext.utils.ValueHolderUtils;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.StCCycleStrategyActiveCmd;
import com.jackrain.nea.oc.oms.mapper.StCCycleItemStrategyMapper;
import com.jackrain.nea.oc.oms.mapper.StCCycleRuleStrategyMapper;
import com.jackrain.nea.oc.oms.mapper.StCCycleStrategyMapper;
import com.jackrain.nea.oc.oms.model.table.StCCycleItemStrategy;
import com.jackrain.nea.oc.oms.model.table.StCCycleRuleStrategy;
import com.jackrain.nea.oc.oms.model.table.StCCycleStrategy;
import com.jackrain.nea.st.model.StCCycleStrategyRelation;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;

/**
 * @ClassName StCCycleStrategyActiveCmdImpl
 * @Description 启用
 * <AUTHOR>
 * @Date 2024/8/20 16:21
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oc-core")
public class StCCycleStrategyActiveCmdImpl extends CommonCommandAdapter implements StCCycleStrategyActiveCmd {

    @Autowired
    private StCCycleStrategyMapper stCCycleStrategyMapper;
    @Autowired
    private StCCycleRuleStrategyMapper ruleStrategyMapper;
    @Autowired
    private StCCycleItemStrategyMapper itemStrategyMapper;
    @Autowired
    private StCCycleStrategyServiceImpl stCCycleStrategyService;

    @Override
    @OmsOperationLog(operationType = "OPEN", mainTableName = "ST_C_CYCLE_STRATEGY", itemsTableName = "ST_C_CYCLE_RULE_STRATEGY,ST_C_CYCLE_ITEM_STRATEGY")
    public ValueHolder execute(QuerySession session) throws NDSException {
        List<Long> objIds = getObjIds(session);

        if (CollectionUtils.isNotEmpty(objIds)) {
            for (Long objId : objIds) {
                StCCycleStrategy strategy = stCCycleStrategyMapper.selectById(objId);
                if (strategy == null) {
                    continue;
                }
                List<StCCycleRuleStrategy> ruleStrategyList = ruleStrategyMapper.selectByStrategyIdWithOutActive(objId);
                List<StCCycleItemStrategy> itemStrategyList = itemStrategyMapper.selectByStrategyIdWithOutActive(objId);
                if (CollectionUtils.isEmpty(ruleStrategyList) || CollectionUtils.isEmpty(itemStrategyList)) {
                    return ValueHolderUtils.fail("策略" + strategy.getStrategyCode() + "启用失败!失败原因:规则或商品策略未维护");
                }
                StCCycleStrategyRelation strategyRelation = new StCCycleStrategyRelation();
                strategyRelation.setStCCycleStrategy(strategy);
                strategyRelation.setStCCycleItemStrategy(itemStrategyList);
                strategyRelation.setStCCycleRuleStrategy(ruleStrategyList);
                try {
                    stCCycleStrategyService.checkRule(strategyRelation);
                } catch (Exception e) {
                    return ValueHolderUtils.fail("策略" + strategy.getStrategyCode() + "启用失败!失败原因:" + e.getMessage());
                }
                // 先全部校验一下
                stCCycleStrategyMapper.activeStrategy(objId);
            }
        }
        return ValueHolderUtils.success("启用成功");
    }

    @Override
    public ValueHolder execute(HashMap map) throws NDSException {
        return null;
    }
}
