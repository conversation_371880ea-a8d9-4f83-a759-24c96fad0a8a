package com.jackrain.nea.oc.services;

import com.jackrain.nea.oc.oms.api.PosOrderQtyQueryCmd;
import com.jackrain.nea.oc.oms.model.result.PosOrderQtyQueryItemResult;
import com.jackrain.nea.oc.oms.model.result.PosOrderQtyQueryResult;
import com.jackrain.nea.oc.oms.services.PosOrderQtyQueryService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @ClassName : PosOrderQtyQueryCmdImpl  
 * @Description : 
 * <AUTHOR>  YCH
 * @Date: 2021-09-08 12:04  
 */
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
@Slf4j
@Component
public class PosOrderQtyQueryCmdImpl implements PosOrderQtyQueryCmd {

    @Autowired
    private PosOrderQtyQueryService posOrderQtyQueryService;
    @Override
    public ValueHolderV14<PosOrderQtyQueryResult> queryOrderQtyOut(String tid) {
        return posOrderQtyQueryService.posOrderQtyQuery(tid);
    }
}
