package com.jackrain.nea.oc.oms.model.enums;

public enum QiMenOrderStatusEnum {

    CONFIRM(1), PICK_UP(2), VOID(3), FINISH(4), OTHER(5);

    private Integer code;


    QiMenOrderStatusEnum(Integer code) {
        this.code = code;
    }

    public QiMenOrderStatusEnum form(Integer code) {
        for (QiMenOrderStatusEnum e : values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }
}
