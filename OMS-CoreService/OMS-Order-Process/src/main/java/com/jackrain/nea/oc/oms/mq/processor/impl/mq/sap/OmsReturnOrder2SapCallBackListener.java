package com.jackrain.nea.oc.oms.mq.processor.impl.mq.sap;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.burgeon.mq.annotation.RocketMqMessageListener;
import com.burgeon.mq.core.BaseMessageListener;
import com.burgeon.mq.enums.MqTypeEnum;
import com.burgeon.mq.error.MqException;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.model.relation.OmsOrder2SapBackRelation;
import com.jackrain.nea.oc.oms.sap.Oms2SapMapper;
import com.jackrain.nea.oc.oms.sap.Oms2SapStatusEnum;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * B2C订单退-SAP回传, MQ 回执
 *
 * @author: xiWen.z
 * create at: 2019/12/19 0019
 */
@Slf4j
@RocketMqMessageListener(name = "OmsReturnOrder2SapCallBackListener", type = MqTypeEnum.DEFAULT)
public class OmsReturnOrder2SapCallBackListener implements BaseMessageListener {

    @Autowired
    private Oms2SapMapper oms2SapMapper;

    private String returnTag = "return_order";
    //private String rfnTag = "sap_refund_to_oms";

    @Override
    public void consume(String messageBody, String messageTopic, String messageKey, String messageTag, Object obj) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("OmsReturnOrder2SapCallBackListener.consume.msgBody:{},msgKey:{}, msgTopic:{}",
                    messageKey),  messageTag, messageKey, messageTopic);
        }
        String msgBody = null;
        try {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("接收处理后的msgBody:{}",  messageKey), msgBody);
            }
            List<Long> okIds = new ArrayList<>();
            List<Long> noIds = new ArrayList<>();
            //解析接收到的msgBody，对成功和失败的订单ID集合分别进行处理
            if (returnTag.equals(messageTag)) {
                JSONObject object = JSONObject.parseObject(msgBody);
                JSONArray resultArry = object.getJSONArray("data");
                if (CollectionUtils.isNotEmpty(resultArry)) {
                    for (int i = 0; i < resultArry.size(); i++) {
                        String code = resultArry.getJSONObject(i).get("code").toString();
                        Long orderId = Long.valueOf(String.valueOf(resultArry.getJSONObject(i).get("id")));
                        if (null != orderId) {
                            if ("0".equals(code)) {
                                okIds.add(orderId);
                            } else {
                                noIds.add(orderId);
                            }
                        }
                    }
                }
            }
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("解析msgBody,传SAP成功:{} 条，失败：{} 条",  messageKey), okIds.size(), noIds.size());
            }

            //消费MQ 传SAP成功、失败的数据，更新订单状态、传失败次数
            ValueHolderV14 vh = updateOrderInfo(okIds, noIds, "OC_B_RETURN_ORDER", "RESERVE_BIGINT02");
            //vh = updateOrderInfo(response, "OC_B_RETURN_AF_SEND", "SAP_STATUS");
            if (vh != null && vh.isOK()) {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("更新成功", messageKey));
                }
            } else {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("更新失败", messageKey));
                }
                throw new MqException("更新失败重试");
            }

        } catch (Exception e) {
            log.error(LogUtil.format("ReceiveCallBackMsgExp, ExpMsg: {}"), Throwables.getStackTraceAsString(e));
        }
    }

    /**
     * validate
     *
     * @param msgBody
     * @return jsn
     */
    private OmsOrder2SapBackRelation checkMsg(String msgBody) {
        OmsOrder2SapBackRelation response = null;
        if (msgBody == null) {
            log.error(LogUtil.format("DeSerialize msgBody Result ->　Null"));
            return null;
        }
        try {
            response = JSON.parseObject(msgBody, OmsOrder2SapBackRelation.class);
            if (response == null) {
                log.error(LogUtil.format("msgBody　Parse To JSONObject Result ->　Null"));
            }
        } catch (Exception e) {
            log.error(LogUtil.format("msgBody parse Exp, ExpMsg: {}"), Throwables.getStackTraceAsString(e));
        }
        return response;
    }


    /**
     * @param okIds
     * @param noIds
     * @param origTable
     * @param status
     * @return
     */
    public ValueHolderV14 updateOrderInfo(List<Long> okIds, List<Long> noIds, String origTable, String status) {
        //消息体处理
        ValueHolderV14 vh = new ValueHolderV14();
        try {
            if (CollectionUtils.isNotEmpty(okIds)) {
                int i = oms2SapMapper.updateDynamicOrigOrder(origTable, status, Oms2SapStatusEnum.FINISH.val(), okIds);
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("传SAP成功：{},消费MQ更新OC_B_RETURN_ORDER状态为-完成，共：{} 条"), i, okIds.size());
                }
            }
            if (CollectionUtils.isNotEmpty(noIds)) {
                int k = oms2SapMapper.updateDynamicOrigOrderCallBack(origTable, status, Oms2SapStatusEnum.FAILED.val(),
                        noIds, "RESERVE_DECIMAL07");
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("传SAP成功：{},消费MQ更新OC_B_RETURN_ORDER状态为-失败，失败次数加1，共：{} 条"), k, noIds.size());
                }
            }
            vh.setCode(ResultCode.SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            vh.setCode(ResultCode.FAIL);
        }
        return vh;
    }


}
