package com.jackrain.nea.oc.services.invoice;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.ac.service.OrderInvoiceService;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.invoice.AcFOrderInvoiceAuditFallbackCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName AcFOrderInvoiceAuditFallbackCmdImpl
 * @Description
 * @Date 2022/12/2 10:16
 * @Created by wuhang
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class AcFOrderInvoiceAuditFallbackCmdImpl extends CommandAdapter implements AcFOrderInvoiceAuditFallbackCmd {
    @Autowired
    private OrderInvoiceService service;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        return service.auditFallback(querySession);
    }
}
