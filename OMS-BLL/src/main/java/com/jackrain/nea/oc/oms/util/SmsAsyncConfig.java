//package com.jackrain.nea.oc.oms.util;
//
//import com.google.common.base.Throwables;
//import com.jackrain.nea.utility.LogUtil;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.aop.interceptor.AsyncUncaughtExceptionHandler;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.scheduling.annotation.AsyncConfigurer;
//import org.springframework.scheduling.annotation.EnableAsync;
//import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
//
//import java.lang.reflect.Method;
//import java.util.concurrent.Executor;
//import java.util.concurrent.ThreadPoolExecutor;
//
///**
// * @Descroption 短信策略异步监听线程池
// * <AUTHOR>
// * @Date 2020/9/24
// */
//@EnableAsync
//@Configuration
//@Slf4j
//public class SmsAsyncConfig implements AsyncConfigurer {
//
//    /**
//     * 基本线程池常量定义
//     */
//    @Value("${send.msg.core.pool:8}")
//    private int corePoolSize;
//    @Value("${send.msg.max.pool:10}")
//    private int maxPoolSize;
//    @Value("${send.msg.alive.seconds:60}")
//    private int aliveSeconds;
//    @Value("${send.msg.queue.capacity:200}")
//    private int queueCapacity;
//
//    @Override
//    @Bean("smsAsyncTaskExector")
//    public Executor getAsyncExecutor() {
//        //通过Runtime方法来获取当前服务器cpu内核，根据cpu内核来创建核心线程数和最大线程数
////        int threadCount = Runtime.getRuntime().availableProcessors();
//        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
//        executor.setCorePoolSize(corePoolSize);
//        executor.setMaxPoolSize(maxPoolSize);
//        executor.setKeepAliveSeconds(aliveSeconds);
//        executor.setQueueCapacity(queueCapacity);
//        executor.setThreadNamePrefix("smsAsyncTaskExecutor-");
//        //直接在execute方法的调用线程中运行被拒绝的任务
//        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
//        return executor;
//    }
//
//    @Override
//    public AsyncUncaughtExceptionHandler getAsyncUncaughtExceptionHandler() {
//        return (Throwable ex, Method method, Object... params) ->
//        log.error(LogUtil.format("smsEvent_Exception,短信策略异步监听线程池执行异常,method={},param={},e={}"), method, params,Throwables.getStackTraceAsString(ex));
//    }
//}
