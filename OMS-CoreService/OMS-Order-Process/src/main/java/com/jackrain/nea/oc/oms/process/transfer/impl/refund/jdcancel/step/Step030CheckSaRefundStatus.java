package com.jackrain.nea.oc.oms.process.transfer.impl.refund.jdcancel.step;

import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.relation.IpJingdongCancelRelation;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongSaRefund;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @description: 判断京东取消订单审核状态
 * @author: 郑小龙
 * @date: 2020-06-01 16:25
 **/
@Step(order = 30, description = "判断京东取消订单审核状态")
@Slf4j
@Component
public class Step030CheckSaRefundStatus extends BaseJingdongCancelProcessStep
        implements IOmsOrderProcessStep<IpJingdongCancelRelation> {

    @Override
    public ProcessStepResult<IpJingdongCancelRelation> startProcess(IpJingdongCancelRelation orderInfo,
                                                                    ProcessStepResult preStepResult,
                                                                    boolean isAutoMakeup, User operateUser) {
        IpBJingdongSaRefund saRefund = orderInfo.getJingdongSaRefund();
        log.debug(this.getClass().getName() + " 判断京东取消订单审核状态，京东取消订单退款单id：{}", saRefund.getPopafsrefundapplyid());
        long starTime = System.currentTimeMillis();
        try {
            if (saRefund.getStatus() == null) {
                //修改中间表状态及系统备注
                saRefundService.updateSaRefundIsTransError(saRefund, "京东取消订单审核状态为空!");
                return new ProcessStepResult<>(StepStatus.FAILED, "京东取消订单审核状态为空！");
            }
            //京东取消接口转单处理逻辑
            saRefundService.jdCancelOrderRefund(orderInfo, operateUser);
            long endTime = System.currentTimeMillis();
            long Time = endTime - starTime;
            if (log.isDebugEnabled()) {
                log.debug(this.getClass().getName() + " 京东取消接口转单处理逻辑耗时:{}ms", Time);
            }
            return new ProcessStepResult<>(StepStatus.FINISHED, "单据" + saRefund.getPopafsrefundapplyid() + "转换完成");
        } catch (Exception e) {
            log.error(LogUtil.format("京东取消订单转换异常信息:{}", "京东取消订单转换异常"), Throwables.getStackTraceAsString(e));
            //修改中间表状态及系统备注
            saRefundService.updateSaRefundIsTransError(saRefund, e.getMessage());
            String errorMessage = "退单转换异常!" + e.getMessage();
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
    }
}
