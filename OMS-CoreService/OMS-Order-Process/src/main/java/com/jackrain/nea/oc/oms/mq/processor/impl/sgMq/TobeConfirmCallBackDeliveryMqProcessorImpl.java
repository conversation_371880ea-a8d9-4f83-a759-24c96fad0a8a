package com.jackrain.nea.oc.oms.mq.processor.impl.sgMq;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.mq.error.MqException;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyOmsResult;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.util.OmsOrderSplitReasonUtil;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * @program: r3-oc-oms
 * @description: 寻仓单占单回执mq监听mq
 * @author: zhuxing
 * @create: 2021-09-14 20:18
 **/

// fixme tag:OperateTobeConfirmCallBack_Vip_Delivery
@Slf4j
@Component
//@RocketMqMessageListener(name = "TobeConfirmCallBackDeliveryMqProcessorImpl", type = MqTypeEnum.DEFAULT)
@Transactional(rollbackFor = Exception.class)
public class TobeConfirmCallBackDeliveryMqProcessorImpl {

    @Autowired
    OmsOrderSplitReasonUtil omsOrderSplitReasonUtil;

    @Autowired
    SgRpcService sgRpcService;

    @Autowired
    TobeConfirmCallBackMqService tobeConfirmCallBackMqService;

    @Autowired
    private TobeConfirmCallBackVipTimeOrderMqService tobeConfirmCallBackVipTimeOrderMqService;

    @Autowired
    private TobeConfirmCallBackDeliveryVirtualOrderMqService tobeConfirmCallBackDeliveryVirtualOrderMqService;

    @Transactional(rollbackFor = Exception.class)
    public void consume(String messageBody, String messageTopic, String messageKey, String messageTag, Object object) {
        log.info(LogUtil.format("寻仓单寻源占单返回监听messageTopic={} messageKey = {} messageBody = {} messageTag = {}",
                "TobeConfirmCallBackDeliveryMqProcessorImpl",messageTopic, messageKey),messageTopic, messageKey, messageBody, messageTag);
        try {
            JSONObject messageJson = JSONObject.parseObject(messageBody);
            SgFindSourceStrategyOmsResult sgFindSourceStrategyOmsResult = JSONObject.toJavaObject(messageJson,SgFindSourceStrategyOmsResult.class);

            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("寻仓单寻源占单返回监听sgFindSourceStrategyOmsResult:{}",
                        "TobeConfirmCallBackDeliveryMqProcessorImpl",sgFindSourceStrategyOmsResult.getSourceBillNo()),sgFindSourceStrategyOmsResult);
            }
            if(sgFindSourceStrategyOmsResult.getInventedOccupy() != null && sgFindSourceStrategyOmsResult.getInventedOccupy()){
                //虚拟寻源结果
                tobeConfirmCallBackDeliveryVirtualOrderMqService.tobeConfirmDeliveryVirtualCallBackService(sgFindSourceStrategyOmsResult);
            }else{
                //唯品会寻仓单
                tobeConfirmCallBackVipTimeOrderMqService.tobeConfirmDeliveryCallBackService(sgFindSourceStrategyOmsResult);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("TobeConfirmCallBackDeliveryMqProcessorImpl.consume.ExpMsg: {}"), Throwables.getStackTraceAsString(e));
            throw new MqException(e);
        }
    }

}
