package com.jackrain.nea.oc.oms.api.sap;

import com.jackrain.nea.cpext.model.ProvinceCityAreaInfo;
import com.jackrain.nea.oc.oms.model.table.StCBusinessType;
import com.jackrain.nea.sys.Command;
import com.jackrain.nea.sys.domain.ValueHolderV14;

/**
 *
 * @Desc :
 * <AUTHOR>
 * @Date : 2022/9/5
 */
public interface SalesDataGatherBackSapR3Cmd extends Command {

    ValueHolderV14 queryOcBOrderInfo(String billNo) throws Exception;


    StCBusinessType queryStCBusinessType(Long id);

    ProvinceCityAreaInfo selectByProvinceAndCityAndArea(String provinceName, String cityName, String areaName);

}
