package com.jackrain.nea.oc.oms.task.hang;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.oc.oms.extmodel.SqlHandleModel;
import com.jackrain.nea.oc.oms.services.AutoReleaseHangTaskService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;

/**
 * 自动解挂 -> 自动释放hold单
 *
 * @author: 秦俊龙
 * @since: 2019-03-23
 * create at : 2019-03-23 12:19
 */
@Slf4j
@Component
public class AutoReleaseHangTask extends BaseR3Task implements IR3Task {

    @Autowired
    private AutoReleaseHangTaskService autoReleaseHangTaskService;
    @Autowired
    private ThreadPoolTaskExecutor autoReleaseHangTaskThreadPoolExecutor;


    @Override
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult result = new RunTaskResult();

        try {
            final String taskTableName = "oc_b_auto_release_hang_task";
            Map<String, String> topMap = null;
            Set<String> nodes = topMap.keySet();
            if (CollectionUtils.isEmpty(nodes)) {
                result.setSuccess(false);
                result.setMessage("请检查环境，node获取不到！！");
                return result;
            }
            List<Future<Integer>> results = new ArrayList<>();

            List<SqlHandleModel> parameters = this.getParameters();
            for (String node : nodes) {
                results.add(autoReleaseHangTaskThreadPoolExecutor.submit(new CallableAutoReleaseHangTaskWithResult(node, topMap.get(node), parameters)));
            }

            //线程执行结果获取
            int count = 0;
            for (Future<Integer> futureResult : results) {
                try {
                    count = count + futureResult.get();
                    log.info("AutoReleaseHangTask------>线程结果:" + futureResult.get().toString());
                } catch (InterruptedException e) {
                    log.error("AutoReleaseHangTask:" + e.getMessage(), e);
                } catch (ExecutionException e) {
                    log.error("AutoReleaseHangTask多线程获取ExecutionException异常:" + e.getMessage(), e);
                }
            }

            result.setSuccess(true);
            result.setMessage("发送总数" + count);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error("AutoReleaseHangTask.Execute Error", ex);
            result.setSuccess(false);
            result.setMessage(ex.getMessage());
        }
        if (log.isInfoEnabled()) {
            log.debug("Finish AutoReleaseHangTask");
        }
        return result;
    }

    /**
     * 获取查询参数
     *
     * @return
     */
    private List<SqlHandleModel> getParameters() {
        return Lists.newArrayList(new SqlHandleModel("status", "=", 0), new SqlHandleModel("auto_release_time", "<=", DateUtil.dateTimeSecondsFormatter.format(new Date())));
    }

    /**
     * 开启线程类
     */
    class CallableAutoReleaseHangTaskWithResult implements Callable<Integer> {
        private final String nodeName;

        private final String taskTableName;

        private final List<SqlHandleModel> models;

        public CallableAutoReleaseHangTaskWithResult(String nodeName, String taskTableName, List<SqlHandleModel> models) {
            this.nodeName = nodeName;
            this.taskTableName = taskTableName;
            this.models = models;
        }

        @Override
        public Integer call() throws Exception {
            PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
            int pageSize = config.getProperty("lts.auto.release.hang.range", 200);
            List<Long> orderIds = autoReleaseHangTaskService.selectCanExecuteData(nodeName, taskTableName, pageSize, models);
            if (orderIds.isEmpty()) {
                return 0;
            }
            autoReleaseHangTaskService.handle(orderIds);
            return 1;
        }
    }


}
