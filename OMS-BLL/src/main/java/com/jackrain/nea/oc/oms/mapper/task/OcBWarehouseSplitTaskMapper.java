package com.jackrain.nea.oc.oms.mapper.task;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.task.OcBWarehouseSplitTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>
 * 仓库拆单任务表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-02
 */
@Mapper
@Component
public interface OcBWarehouseSplitTaskMapper extends ExtentionMapper<com.jackrain.nea.oc.oms.model.table.task.OcBWarehouseSplitTask> {

    @SelectProvider(type = OcBWarehouseSplitTaskSql.class, method = "selectByNodeSql")
    List<Long> selectOcBWarehouseSplitTask(@Param(value = "nodeName") String nodeName, @Param(value = "limit") int limit,
                                @Param(value = "taskTableName") String taskTableName, @Param("status") int status);

    @Select("select * from OC_B_WAREHOUSE_SPLIT_TASK where order_id = #{orderId} limit 1")
    OcBWarehouseSplitTask selectOcBWarehouseSplitTaskByOrderId(@Param("orderId") Long orderId);

    @Update("<script> "
            + "UPDATE OC_B_WAREHOUSE_SPLIT_TASK SET STATUS = #{status},modifieddate = now() where order_id in "
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    void batchUpdateOcBWarehouseSplitTaskByOrderIds(@Param("ids") List<Long> orderIds, @Param("status")Integer status);

    @SelectProvider(type = OcBWarehouseSplitTaskSql.class, method = "selectByNodeSqlWithPushDelay")
    List<Long> selectWmsTaskWithPushDelay(@Param(value = "limit") int limit, @Param(value = "taskTableName") String taskTableName,
                                          @Param("status") int status);

    @SelectProvider(type = OcBWarehouseSplitTaskSql.class, method = "selectByNodeSqlWithPushDelayNull")
    List<Long> selectByNodeSqlWithPushDelayNull(@Param(value = "limit") int limit,
                                                @Param(value = "taskTableName") String taskTableName, @Param("status") int status);


}
