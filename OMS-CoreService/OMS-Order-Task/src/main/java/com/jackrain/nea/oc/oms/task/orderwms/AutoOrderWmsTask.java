package com.jackrain.nea.oc.oms.task.orderwms;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.ip.model.request.DeWuOrderAcceptRequest;
import com.jackrain.nea.ip.model.request.DeWuOrderModifyRequest;
import com.jackrain.nea.oc.oms.mapper.OcBDewuOrderWarehouseInfoMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.table.OcBDewuOrderWarehouseInfo;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.services.task.OmsWmsTaskService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.sg.service.SgOutStockNoticeService;
import com.jackrain.nea.st.model.table.StCDewuWarehouseConfig;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.ListSplitUtil;
import com.jackrain.nea.util.SplitListUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/***
 * 订单传wms服务
 */
@Slf4j
@Component
public class AutoOrderWmsTask extends BaseR3Task implements IR3Task {

    @Autowired
    private SgOutStockNoticeService sgOutStockNoticeService;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderItemMapper orderItemMapper;
    @Autowired
    private BllRedisLockOrderUtil redisUtil;
    @Autowired
    private OmsWmsTaskService wmsTaskService;
    @Autowired
    private ThreadPoolTaskExecutor preToWmsTaskThreadPoolExecutor;
    @Autowired
    private OcBDewuOrderWarehouseInfoMapper ocBDewuOrderWarehouseInfoMapper;
    @Autowired
    private ThreadPoolTaskExecutor commonTaskExecutor;
    @Autowired
    private StRpcService stRpcService;
    @Autowired
    private IpRpcService ipRpcService;
    @Autowired
    private CpRpcService cpRpcService;


    @Override
    @XxlJob("AutoOrderWmsTask")
    public RunTaskResult execute(JSONObject params) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("进入订单传wms定时任务:{}", "进入订单传wms定时任务"));
        }
        RunTaskResult result = new RunTaskResult();
        try {

            long start = System.currentTimeMillis();
            final String taskTableName = "oc_b_to_wms_task";
            List<Future<Boolean>> results = new ArrayList<Future<Boolean>>();
            PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
            Integer pageSize = config.getProperty("lts.AutoOrderWmsTask.range", 200);
            Integer orderStatus = config.getProperty("lts.AutoOrderWmsTask.status", 0);
            List<Long> list = wmsTaskService.selectWmsTaskWithPushDelay(pageSize * 24, taskTableName, orderStatus);
            if (CollectionUtils.isEmpty(list)) {
                result.setSuccess(true);
                result.setMessage("无订单");
                return result;
            }
            List<List<Long>> lists = ListSplitUtil.averageAssign(list, 24);
            for (List<Long> data : lists) {
                results.add(preToWmsTaskThreadPoolExecutor.submit(new CallableWmsTaskWithResult(data)));
            }

            //线程执行结果获取
            for (Future<Boolean> futureResult : results) {
                try {
                    if (log.isDebugEnabled()) {
                        log.debug(LogUtil.format("AutoOrderWmsTask------>线程结果:{}"), futureResult.get().toString());
                    }
                } catch (Exception e) {
                    log.error(LogUtil.format("AutoOrderWmsTask多线程获取InterruptedException异常：{}", "AutoOrderWmsTask"), Throwables.getStackTraceAsString(e));
                }
            }

            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("AutoOrderWmsTask 传wms服务定时任务完成 useTime : {}"), (System.currentTimeMillis() - start));
            }
            result.setSuccess(true);
        } catch (Exception ex) {
            log.error(LogUtil.format("订单传wms任务执行失败,异常！{}",  "AutoOrderWmsTask"), Throwables.getStackTraceAsString(ex));
            result.setSuccess(false);
            result.setMessage(ex.getMessage());
        } finally {
            //   executor.shutdown();
        }
        return result;
    }

    /**
     * 开启线程类
     */
    class CallableWmsTaskWithResult implements Callable<Boolean> {


        private final List<Long> list;

        public CallableWmsTaskWithResult(List<Long> list) {
            this.list = list;
        }

        @Override
        public Boolean call() throws Exception {
            if (CollectionUtils.isEmpty(list)) {
                return true;
            }
            // 分批次推送WMS
            spitData(list);
            return true;
        }

    }

    private void spitData(List<Long> list) {
        User rootUser = SystemUserResource.getRootUser();
        // 读取阿波罗配置
        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
        Integer pointsDataLimit = config.getProperty("lts.AutoOrderWmsTask.push.num", 200);
        Boolean booleanProperty = config.getPropertyBoolean("lts.wms.is_LockUp");

        // 分割list
        List<List<Long>> splitList = SplitListUtil.partition(list, pointsDataLimit);
        if (CollectionUtils.isEmpty(splitList)) {
            return;
        }
        for (List<Long> toList : splitList) {
            long starTime = System.currentTimeMillis();
            List<RedisReentrantLock> lockList = new ArrayList<>();
            List<Long> toNewList = new ArrayList<>();
            try {
                if (booleanProperty) {
                    lockOrderList(toList, lockList, toNewList);
                } else {
                    toNewList = toList;
                }
                if (CollectionUtils.isNotEmpty(toNewList)) {
                    // 订单推送WMS
                    ApplicationContextHandle.getBean(AutoOrderWmsTask.class).pushWms(rootUser, toNewList);
                    log.debug("AutoOrderWmsTask订单传wms完成耗时:{}ms,一共:{}条",
                            System.currentTimeMillis() - starTime, toNewList.size());
                }
            } catch (Exception e) {
                log.error(LogUtil.format("AutoOrderWmsTask订单传WMS失败,订单列表orderList={},异常信息:{}",
                        "AutoOrderWmsTask订单传WMS失败"), JSONObject.toJSONString(toList),Throwables.getStackTraceAsString(e));
            } finally {
                this.unLockOrderList(lockList);
            }
        }
    }

    /**
     * 订单逐个加锁
     * @param toList
     * @param lockList
     * @param toNewList
     */
    private void lockOrderList(List<Long> toList, List<RedisReentrantLock> lockList, List<Long> toNewList) {
        for (Long orderId : toList) {
            String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(orderId);
            RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
            try {
                if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                    lockList.add(redisLock);
                    toNewList.add(orderId);
                } else {
                    log.warn(LogUtil.format("AutoOrderWmsTask.spitData tryLockFail,orderId:{}",
                            "AutoOrderWmsTask.spitData tryLockFail"),orderId);
                }
            } catch (Exception e) {
                log.error(LogUtil.format("AutoOrderWmsTask.spitData tryLockError,orderId:{}",
                        "AutoOrderWmsTask.spitData tryLockError"),orderId);
            }
        }
    }

    //解锁
    private void unLockOrderList(List<RedisReentrantLock> lockList) {
        if (CollectionUtils.isEmpty(lockList)) {
            return;
        }
        for (RedisReentrantLock redisReentrantLock : lockList) {
            redisReentrantLock.unlock();
        }

    }


    @Transactional(rollbackFor = Exception.class)
    public void pushWms(User user, List<Long> toList) {
        //1) 将订单状态修改为传WMS中，状态21
        ocBOrderMapper.updateWmsOrderTo21List(toList);
        // 2）修改task表订单状态（因为又一些单据的状态会发生变化，如果不满足条件的，那么也要设置成1）
        wmsTaskService.batchUpdateOcBToWmsTask(toList, 1);
        // 3）新增出库单并批量传wms
        List<OcBOrder> ocBOrders = ocBOrderMapper.selectByIdsListAndOrderStatus(toList);
        // 2020/10/30 优化：通过过滤后的订单id，来查询明细数据
        List<Long> listIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(ocBOrders)) {
            listIds = ocBOrders.stream()
                    .filter(obj -> obj != null && obj.getId() != null)
                    .map(OcBOrder::getId)
                    .collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(listIds)) {
            return;
        }

        // 异步处理得物平台订单
        try {
            asyncProcessDewuOrders(ocBOrders);
        } catch (Exception e) {
            log.error(LogUtil.format("处理得物平台订单异常，不影响主流程，异常信息：{}", "处理得物平台订单异常"), Throwables.getStackTraceAsString(e));
        }

        List<OcBOrderItem> orderItems = orderItemMapper.selectOrderItemsByOrderIds(listIds);
        sgOutStockNoticeService.addOutStockNoticeToWms(user, ocBOrders, orderItems);
    }

    /**
     * 异步处理得物平台订单
     * @param ocBOrders 订单列表
     */
    private void asyncProcessDewuOrders(List<OcBOrder> ocBOrders) {
        if (CollectionUtils.isEmpty(ocBOrders)) {
            return;
        }

        // 使用线程池异步处理
        commonTaskExecutor.execute(() -> {
            try {
                processDewuOrders(ocBOrders);
            } catch (Exception e) {
                log.error(LogUtil.format("异步处理得物平台订单异常，异常信息：{}", "异步处理得物平台订单异常"), Throwables.getStackTraceAsString(e));
            }
        });
    }

    /**
     * 处理得物平台订单
     * @param ocBOrders 订单列表
     */
    private void processDewuOrders(List<OcBOrder> ocBOrders) {
        for (OcBOrder ocBOrder : ocBOrders) {
            try {
                // 判断是否是得物平台订单
                if (ocBOrder.getPlatform() != null && ocBOrder.getPlatform().equals(PlatFormEnum.DE_WU.getCode())) {
                    String tid = ocBOrder.getTid();
                    CpShop cpShop = cpRpcService.selectShopById(ocBOrder.getCpCShopId());
                    // 查询是否存在得物订单仓库信息记录
                    OcBDewuOrderWarehouseInfo dewuOrderWarehouseInfo = ocBDewuOrderWarehouseInfoMapper.selectOne(
                            new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<OcBDewuOrderWarehouseInfo>()
                                    .eq("TID", tid));
                    List<StCDewuWarehouseConfig> warehouseConfigList =  stRpcService.queryDewuWarehouseConfigByCode(ocBOrder.getCpCPhyWarehouseEcode());
                    if (CollectionUtils.isEmpty(warehouseConfigList)){
                        log.error(LogUtil.format("订单:{}仓库编码:{}在得物平台没有找到对应的仓库编码",
                                "订单:{}仓库编码:{}在得物平台没有找到对应的仓库编码"), ocBOrder.getId(), ocBOrder.getCpCPhyWarehouseEcode());
                        continue;
                    }
                    if (dewuOrderWarehouseInfo != null) {
                        // 存在记录，判断是否是同一个订单
                        if (dewuOrderWarehouseInfo.getOcBOrderId() != null &&
                                dewuOrderWarehouseInfo.getOcBOrderId().equals(ocBOrder.getId())) {
                            StCDewuWarehouseConfig stCDewuWarehouseConfig = warehouseConfigList.get(0);
                            if (dewuOrderWarehouseInfo.getAddressId().equals(stCDewuWarehouseConfig.getAddressId())){
                                log.info(LogUtil.format("得物订单得物地址ID未修改，不处理，订单ID:{}, 平台单号:{}", "得物订单不处理"),
                                        ocBOrder.getId(), tid);
                                continue;
                            }
                            // 是同一个订单，更新记录
                            dewuOrderWarehouseInfo.setWarehouseCode(ocBOrder.getCpCPhyWarehouseEcode());
                            dewuOrderWarehouseInfo.setAddressId(stCDewuWarehouseConfig.getAddressId());
                            dewuOrderWarehouseInfo.setModifieddate(new Date());
                            // 调用得物改仓接口
                            try {
                                // 构建改仓请求参数
                                DeWuOrderModifyRequest modifyParams = new DeWuOrderModifyRequest();
                                modifyParams.setTid(tid);
                                modifyParams.setAddressId(stCDewuWarehouseConfig.getAddressId());
                                modifyParams.setPlatform(ocBOrder.getPlatform().toString());
                                modifyParams.setSellerNick(cpShop.getSellerNick());
                                // 调用改仓接口
                                ValueHolderV14 valueHolderV14 = ipRpcService.modifyOrder(modifyParams);
                                if (!valueHolderV14.isOK()) {
                                    log.error(LogUtil.format("得物订单改仓处理失败，订单ID:{}, 平台单号:{}, 仓库编码:{}, 异常信息:{}", "得物订单改仓异常"),
                                            ocBOrder.getId(), tid, ocBOrder.getCpCPhyWarehouseEcode(), valueHolderV14.getMessage());
                                }else {
                                    log.info(LogUtil.format("得物订单改仓处理成功，订单ID:{}, 平台单号:{}, 仓库编码:{}, 地址ID:{}", "得物订单改仓"),
                                            ocBOrder.getId(), tid, ocBOrder.getCpCPhyWarehouseEcode(), stCDewuWarehouseConfig.getAddressId());
                                    ocBDewuOrderWarehouseInfoMapper.updateById(dewuOrderWarehouseInfo);
                                }
                            } catch (Exception e) {
                                log.error(LogUtil.format("得物订单改仓处理失败，订单ID:{}, 平台单号:{}, 仓库编码:{}, 异常信息:{}", "得物订单改仓异常"),
                                        ocBOrder.getId(), tid, ocBOrder.getCpCPhyWarehouseEcode(), Throwables.getStackTraceAsString(e));
                            }
                        } else {
                            // 不是同一个订单，不做处理
                            log.info(LogUtil.format("得物订单已存在其他订单记录，不处理，订单ID:{}, 平台单号:{}", "得物订单不处理"),
                                    ocBOrder.getId(), tid);
                        }
                    } else {
                        // 不存在记录，创建新记录
                        OcBDewuOrderWarehouseInfo newInfo = new OcBDewuOrderWarehouseInfo();
                        newInfo.setTid(tid);
                        newInfo.setOcBOrderId(ocBOrder.getId());
                        newInfo.setWarehouseCode(ocBOrder.getCpCPhyWarehouseEcode());
                        // 设置基础字段
                        newInfo.setCreationdate(new Date());
                        newInfo.setModifieddate(new Date());
                        newInfo.setIsactive("Y");
                        StCDewuWarehouseConfig stCDewuWarehouseConfig = warehouseConfigList.get(0);
                        newInfo.setAddressId(stCDewuWarehouseConfig.getAddressId());
                        // 调用得物接单接口
                        try {
                            // 构建接单请求参数
                            DeWuOrderAcceptRequest acceptParams = new DeWuOrderAcceptRequest();
                            acceptParams.setTid(tid);
                            acceptParams.setAddressId(stCDewuWarehouseConfig.getAddressId());
                            acceptParams.setPlatform(ocBOrder.getPlatform().toString());
                            acceptParams.setSellerNick(cpShop.getSellerNick());
                            acceptParams.setPerformanceType("3");
                            // 调用接单接口
                            ValueHolderV14 valueHolderV14 = ipRpcService.acceptOrder(acceptParams);
                            if (!valueHolderV14.isOK()) {
                                log.error(LogUtil.format("得物订单接单处理失败，订单ID:{}, 平台单号:{}, 仓库编码:{}, 异常信息:{}", "得物订单接单异常"),
                                        ocBOrder.getId(), tid, ocBOrder.getCpCPhyWarehouseEcode(), valueHolderV14.getMessage());
                            }else {
                                log.info(LogUtil.format("得物订单接单处理成功，订单ID:{}, 平台单号:{}, 仓库编码:{}, 地址ID:{}", "得物订单接单"),
                                        ocBOrder.getId(), tid, ocBOrder.getCpCPhyWarehouseEcode(), stCDewuWarehouseConfig.getAddressId());
                                newInfo.setId(com.jackrain.nea.model.util.ModelUtil.getSequence("oc_b_dewu_order_warehouse_info"));
                                ocBDewuOrderWarehouseInfoMapper.insert(newInfo);
                            }
                        } catch (Exception e) {
                            log.error(LogUtil.format("得物订单接单处理失败，订单ID:{}, 平台单号:{}, 仓库编码:{}, 异常信息:{}", "得物订单接单异常"),
                                    ocBOrder.getId(), tid, ocBOrder.getCpCPhyWarehouseEcode(), Throwables.getStackTraceAsString(e));
                        }
                    }
                }
            } catch (Exception e) {
                // 单个订单处理异常不影响其他订单
                log.error(LogUtil.format("处理得物平台订单异常，订单ID:{}, 异常信息：{}", "处理得物平台订单异常"),
                        ocBOrder.getId(), Throwables.getStackTraceAsString(e));
            }
        }
    }
}