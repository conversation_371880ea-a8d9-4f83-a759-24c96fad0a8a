package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.OmsOrderProcessApplication;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.st.model.table.StCVipcomJitxWarehouse;
import com.jackrain.nea.st.service.VipcomJitxWarehouseService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * Description:
 *
 * <AUTHOR> sunies
 * @since : 2020-11-08
 * create at : 2020-11-08 23:02
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = OmsOrderProcessApplication.class)
@Slf4j
public class OmsOrderPlatformDeliveryServiceTest {

    @Resource
    private OmsOrderPlatformDeliveryService omsOrderPlatformDeliveryService;
    @Resource
    private OcBOrderMapper ocBOrderMapper;
    @MockBean
    private VipcomJitxWarehouseService vipcomJitxWarehouseService;
    @MockBean
    private CpRpcService cpRpcService;

    @Test
    public void weiPinHuiPlaformSendGoods() {
        Long orderId = 608640L;
        OcBOrder ocBOrder = ocBOrderMapper.selectById(orderId);
        StCVipcomJitxWarehouse stCVipcomJitxWarehouse = new StCVipcomJitxWarehouse();
        stCVipcomJitxWarehouse.setVipcomWarehouseEcode("001");
        when(vipcomJitxWarehouseService.queryJitxCapacity(anyLong(), anyLong(), anyString())).thenReturn(stCVipcomJitxWarehouse);
        CpShop cpShop = new CpShop();
        cpShop.setPlatformSupplierId("5400");
        when(cpRpcService.selectShopById(anyLong())).thenReturn(cpShop);
        omsOrderPlatformDeliveryService.weiPinHuiPlaformSendGoods(ocBOrder, SystemUserResource.getRootUser());
    }
}