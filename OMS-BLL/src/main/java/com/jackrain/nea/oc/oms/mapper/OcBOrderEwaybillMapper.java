package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrderEwaybill;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface OcBOrderEwaybillMapper extends ExtentionMapper<OcBOrderEwaybill> {

    @Select("SELECT * FROM oc_b_order_ewaybill WHERE oc_b_order_id = #{orderId}")
    OcBOrderEwaybill selectByOrderId(@Param("orderId") Long orderId);
}