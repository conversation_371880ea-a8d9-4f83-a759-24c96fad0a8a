package com.jackrain.nea.oc.controller.invoice;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.jackrain.nea.ac.service.OrderInvoiceService;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.oc.oms.model.result.InvoiceSaveCheckReceiverResult;
import com.jackrain.nea.oc.oms.services.invoice.AcFOrderInvoiceSaveService;
import com.jackrain.nea.oc.oms.services.invoice.AcFOrderInvoiceViewService;
import com.jackrain.nea.oc.request.InvoiceInfoModifyRequest;
import com.jackrain.nea.oc.request.OrderInvoiceChangeRequest;
import com.jackrain.nea.oc.request.OrderInvoiceModifyAmountRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

/**
 * @ClassName OrderInvoiceController
 * @Description 订单发票相关接口
 * @Date 2022/9/8 上午11:13
 * @Created by wuhang
 */
@RestController
@RequestMapping("/api/cs/oc/oms")
public class OrderInvoiceController {

    @Autowired
    private OrderInvoiceService service;

    @Autowired
    private AcFOrderInvoiceViewService invoiceViewService;
    @Autowired
    private AcFOrderInvoiceSaveService invoiceSaveService;
    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    /**
     * 红冲
     * @param param
     * @return
     */
//    @PostMapping("/invoice/redRush")
//    public ValueHolderV14 redRush(@RequestBody JSONObject param){
//        User user = SystemUserResource.getRootUser();
//        return service.redRush(param,user);
//    }
//
//    /**
//     * 重新申请开票
//     * @param param
//     * @return
//     */
//    @PostMapping("/invoice/reInvoice")
//    public ValueHolderV14 reInvoice(@RequestBody JSONObject param){
//        return service.reInvoice(param);
//    }
//
//    /**
//     * 合并开票
//     * @param param
//     * @return
//     */
//    @PostMapping("/invoice/mergeInvoice")
//    public ValueHolderV14 mergeInvoice(@RequestBody JSONObject param){
//        return service.mergeInvoice(param);
//    }


    /**
     * 获取发票信息
     * @return
     */
    @PostMapping("/invoice/{id}")
    public ValueHolderV14 getById(@PathVariable("id") Long id){
        return service.getByid(id);
    }

    /**
     * 换票
     * @param param
     * @return
     */
    @PostMapping("/invoice/changeInvoice")
    public ValueHolderV14 changeInvoice(@RequestBody @Valid OrderInvoiceChangeRequest param){
        return service.changeInvoice(param);
    }

    /**
     * 根据发票表id查询开票明细
     * @param id
     * @return
     */
    @GetMapping("/invoice/itemList")
    public ValueHolderV14 getItemListByInvoiceId(@RequestParam("id") Long id){
        return service.getItemListByInvoiceId(id);
    }

    /**
     * 修改金额
     * @param req
     * @return
     */
    @PostMapping("/invoice/modAmount")
    public ValueHolderV14 modAmount(@RequestBody OrderInvoiceModifyAmountRequest req){
        return service.modAmount(req);
    }

    @PostMapping("/invoice/revoke")
    public ValueHolderV14 revoke(@RequestBody JSONObject param){
        return service.revoke(param);
    }

    @GetMapping("/invoice/change")
    public ValueHolderV14 change(){
        return service.change();
    }

    @GetMapping("/invoice/invoice")
    public ValueHolderV14 invoice(){
        return service.invoice();
    }

    @GetMapping("/invoice/getResult")
    public ValueHolderV14 getResult(){
        return service.getResult();
    }

    /**
     * 查看发票
     */
    @PostMapping("/invoice/view")
    public ValueHolderV14 view(@RequestBody SgR3BaseRequest request){
        return invoiceViewService.view(request);
    }


    @PostMapping("/invoice/queryTidFuzzy")
    public ValueHolderV14 queryTid(@RequestBody JSONObject fuzzyParam){
        return invoiceSaveService.queryTidFuzzy(fuzzyParam);
    }

    @PostMapping("/invoice/checkParam")
    public ValueHolderV14<InvoiceSaveCheckReceiverResult> checkTid(@RequestBody JSONObject param){
        return invoiceSaveService.checkTid(param);
    }

    @PostMapping("/invoice/saveApplyInvoice")
    public ValueHolderV14<SgR3BaseResult> saveApplyInvoice(HttpServletRequest request, @RequestBody JSONObject param){
        User user =r3PrimWebAuthService.getLoginPrimWebUser(request);
        if (user == null) {
            user = R3SystemUserResource.getSystemRootUser();
        }
        return invoiceSaveService.saveApplyInvoice(param, user);
    }

    @PostMapping("/invoice/checkOrderParam")
    public ValueHolderV14<List<InvoiceSaveCheckReceiverResult>>checkOrderParam(@RequestBody JSONObject param){
        return invoiceSaveService.checkOrderParam(param);
    }

    /**
     * 修改开票信息
     * @param param
     * @return
     */
    @PostMapping("/invoice/info/modify")
    public ValueHolderV14 invoiceInfoModify(@RequestBody @Valid InvoiceInfoModifyRequest param){
        return service.invoiceInfoModify(param);
    }

}
