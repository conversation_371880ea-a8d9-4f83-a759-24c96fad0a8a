package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;


@TableName(value = "ip_b_jingdong_direct_item")
@Data
public class IpBJingdongDirectItem extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "SKU_ID")
    private String skuId;

    @JSONField(name = "UPC")
    private String upc;

    @JSONField(name = "COMMODITY_NAME")
    private String commodityName;

    @JSONField(name = "COMMODITY_NUM")
    private Integer commodityNum;

    @JSONField(name = "JD_PRICE")
    private BigDecimal jdPrice;

    @JSONField(name = "DISCOUNT")
    private BigDecimal discount;

    @JSONField(name = "COST")
    private BigDecimal cost;

    @JSONField(name = "IP_B_JINGDONG_DIRECT_ID")
    private Long ipBJingdongDirectId;

    @JSONField(name = "AD_ORG_ID")
    private Long adOrgId;

    @JSONField(name = "ISACTIVE")
    private String isactive;

    @JSONField(name = "AD_CLIENT_ID")
    private Long adClientId;

    @JSONField(name = "OWNERID")
    private Long ownerid;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "OWNERNAME")
    private String ownername;

    @JSONField(name = "CREATIONDATE")
    private Date creationdate;

    @JSONField(name = "MODIFIERID")
    private Long modifierid;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    @JSONField(name = "MODIFIERNAME")
    private String modifiername;

    @JSONField(name = "MODIFIEDDATE")
    private Date modifieddate;

    @JSONField(name = "ISMATCH")
    private Integer ismatch;
}
