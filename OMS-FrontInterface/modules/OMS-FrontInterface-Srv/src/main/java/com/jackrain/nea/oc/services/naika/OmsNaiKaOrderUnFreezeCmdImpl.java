package com.jackrain.nea.oc.services.naika;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.api.naika.OmsNaiKaOrderUnFreezeCmd;
import com.jackrain.nea.oc.oms.mapper.OcBOrderNaiKaMapper;
import com.jackrain.nea.oc.oms.model.constant.OcCommonConstant;
import com.jackrain.nea.oc.oms.model.table.OcBOrderNaiKa;
import com.jackrain.nea.oc.oms.services.naika.OmsNaiKaOrderUnFreezeService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName OmsNaiKaOrderUnFreezeCmdImpl
 * @Description 奶卡解冻-详情
 * <AUTHOR>
 * @Date 2022/7/27 20:40
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OmsNaiKaOrderUnFreezeCmdImpl extends CommandAdapter implements OmsNaiKaOrderUnFreezeCmd {

    @Autowired
    private OmsNaiKaOrderUnFreezeService unFreezeService;
    @Autowired
    private OcBOrderNaiKaMapper ocBOrderNaiKaMapper;

    @Override
    public ValueHolder execute(QuerySession querySession) {
        ValueHolder vh = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(event.getParameterValue("param").toString());
        log.info("OmsNaiKaOrderUnFreezeCmdImpl.execute.param:{}", param.toJSONString());
        Long id = param.getLong(OcCommonConstant.OBJ_ID);
        JSONObject subParam = JSON.parseObject(param.get("subParam").toString());

        // 判断表名
        String tableName = (String) subParam.get("table");
        if (ObjectUtil.notEqual(tableName, "OC_B_ORDER_NAIKA")) {
            vh.put("code", ResultCode.FAIL);
            vh.put("message", "明细表信息有误");
            return vh;
        }
        JSONArray jsonArray = JSON.parseArray(subParam.get("idArr").toString());
        List<Long> naiKaOrderIds = new ArrayList<>();
        for (Object object : jsonArray) {
            naiKaOrderIds.add(Long.valueOf(object.toString()));
        }
        List<OcBOrderNaiKa> ocBOrderNaiKaList = ocBOrderNaiKaMapper.selectNaiKaByIdList(naiKaOrderIds);
        if (CollectionUtil.isEmpty(ocBOrderNaiKaList)) {
            vh.put("code", ResultCode.FAIL);
            vh.put("message", "奶卡信息为空");
            return vh;
        }
        return unFreezeService.naiKaOrderUnFreeze(id, "", ocBOrderNaiKaList);
    }
}
