package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.api.OrderPodHandleCmd;
import com.jackrain.nea.oc.oms.sap.SapCommonService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2020/8/13
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OrderPodHandleCmdImpl implements OrderPodHandleCmd {


    @Autowired
    private SapCommonService sapCommonService;

    @Override
    public ValueHolderV14 podHandle(JSONObject params) {
        return sapCommonService.podHandle(params);
    }

    @Override
    public ValueHolderV14 cancelOrder(JSONObject params) {
        return sapCommonService.cancelOrder(params);
    }

    @Override
    public ValueHolderV14 cancelReturn(JSONObject params) {
        return sapCommonService.cancelReturn(params);
    }

    @Override
    public ValueHolderV14 cancel411TReturn(JSONObject params) {
        return sapCommonService.cancel411TReturn(params);
    }
}
