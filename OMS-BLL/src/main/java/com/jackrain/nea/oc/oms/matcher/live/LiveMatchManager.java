package com.jackrain.nea.oc.oms.matcher.live;

import com.jackrain.nea.cpext.api.CpCMainbodyQueryCmd;
import com.jackrain.nea.cpext.model.table.CpCMainbody;
import com.jackrain.nea.oc.oms.matcher.vo.OcMatcherInfoVO;
import com.jackrain.nea.oc.oms.matcher.vo.ParamInputVO;
import com.jackrain.nea.oc.oms.matcher.vo.ParamOutputVO;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.result.StCLiveCastStrategyAllResult;
import com.jackrain.nea.st.model.table.StCLiveCastStrategyDO;
import com.jackrain.nea.st.model.table.StCLiveCastStrategyItemDO;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Description： 匹配管理器，提供直播策略匹配各种需求调用入口
 * Author: RESET
 * Date: Created in 2020/6/15 20:36
 * Modified By:
 */
@Component
@Slf4j
public class LiveMatchManager {

    @Autowired
    StRpcService stRpcService;

    @Reference(version = "1.0", group = "cp-ext")
    CpCMainbodyQueryCmd cpCMainbodyQueryCmd;

    /**
     * getLiveStrategies 查询直播解析策略列表
     *
     * @param cpCShopId
     * @return
     */
    public List<StCLiveCastStrategyAllResult> getLiveStrategy(Long cpCShopId, Date orderDate, Date payTime) {
        List<StCLiveCastStrategyAllResult> liveCastStrategyList = new ArrayList<>();
        List<StCLiveCastStrategyAllResult> resultList = stRpcService.queryLiveCastStrategy(cpCShopId, orderDate,
                payTime);
        if (!CollectionUtils.isEmpty(resultList)) {
            for (StCLiveCastStrategyAllResult result : resultList) {
                StCLiveCastStrategyDO liveCastStrategy = result.getLiveCastStrategyDO();
                if (liveCastStrategy == null || liveCastStrategy.getBillTimeType() == null
                        || liveCastStrategy.getStartTime() == null || liveCastStrategy.getEndTime() == null) {
                    continue;
                }
                String billTimeType = liveCastStrategy.getBillTimeType();
                // 下单时间
                if ("1".equals(billTimeType) && orderDate != null && orderDate.after(liveCastStrategy.getStartTime())
                        && orderDate.before(liveCastStrategy.getEndTime())) {
                    liveCastStrategyList.add(result);
                    continue;
                }
                // 支付时间
                if ("2".equals(billTimeType) && payTime != null && payTime.after(liveCastStrategy.getStartTime())
                        && payTime.before(liveCastStrategy.getEndTime())) {
                    liveCastStrategyList.add(result);
                }
            }
        }
        return liveCastStrategyList;
    }

    /**
     * 查列表
     *
     * @param matcherInfoVO
     * @return
     */
    private List<StCLiveCastStrategyAllResult> getLiveStrategy(OcMatcherInfoVO matcherInfoVO) {
        if (Objects.nonNull(matcherInfoVO)) {
            Long cpCShopId = matcherInfoVO.getCpCShopId();
            Date orderDate = matcherInfoVO.getOrderDate();
            Date payTime = matcherInfoVO.getPayTime();

            List<StCLiveCastStrategyAllResult> results = getLiveStrategy(cpCShopId, orderDate, payTime);
            matcherInfoVO.setStrategies(results);
            return results;
        }

        return null;
    }

    /**
     * 复制一份
     *
     * @param src
     * @return
     */
    private ParamInputVO copyParamInputVO(ParamInputVO src) {
        if (Objects.nonNull(src)) {
            return ParamInputVO.builder()
                    .originalId(src.getOriginalId())
                    .originalTitle(src.getOriginalTitle())
                    .originalRemark(src.getOriginalRemark())
                    .originalSpu(src.getOriginalSpu())
                    .ruleType(src.getRuleType())
                    .ruleContext(src.getRuleContext())
                    .build();
        }

        return new ParamInputVO();
    }

    /**
     * 匹配逻辑
     *
     * @param matcherInfoVO
     */
    public ParamOutputVO doMatch(OcMatcherInfoVO matcherInfoVO) {
        List<StCLiveCastStrategyAllResult> strategies = new ArrayList<StCLiveCastStrategyAllResult>();
        ParamOutputVO outputVO = new ParamOutputVO();
        if (matcherInfoVO != null) {
            // 取直播解析策略
            strategies = getLiveStrategy(matcherInfoVO);
            outputVO = ParamOutputVO.builder().result(false).build();
            matcherInfoVO.setOutputVO(outputVO);
        }

        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(strategies)) {
            // 遍历每一个策略
            boolean match = false;

            for (StCLiveCastStrategyAllResult strategy : strategies) {
                List<StCLiveCastStrategyItemDO> liveCastStrategyItemDOList = strategy.getLiveCastStrategyItemDOList();
                StCLiveCastStrategyDO liveCastStrategyDO = strategy.getLiveCastStrategyDO();

                if (Objects.nonNull(liveCastStrategyDO) && !CollectionUtils.isEmpty(liveCastStrategyItemDOList)) {
                    for (StCLiveCastStrategyItemDO strategyItemDO : liveCastStrategyItemDOList) {
                        ILiveMatcher liveMatcher = LiveMatcherFactory.getInstance().getLiveMatcher(strategyItemDO.getRuleType());

                        // 进行匹配
                        matcherInfoVO = matcherInfoVO == null ? new OcMatcherInfoVO() : matcherInfoVO;
                        ParamInputVO inputVO = copyParamInputVO(matcherInfoVO.getInputVO());
                        inputVO.setRuleType(strategyItemDO.getRuleType());
                        inputVO.setRuleContext(strategyItemDO.getRuleContext());

                        boolean result = liveMatcher.doMatch(inputVO);

                        if (result) {
                            outputVO.setAnchorId(liveCastStrategyDO.getAnchorId());
                            outputVO.setAnchorName(liveCastStrategyDO.getAnchorNickName());
                            outputVO.setLivePlatform(liveCastStrategyDO.getLivePlatform());
                            //新逻辑赋值
                            Long acFManageId = liveCastStrategyDO.getAcFManageId();
                            if (null != liveCastStrategyDO.getAcFManageId()) {
                                outputVO.setAcFManageId(acFManageId);
                                ValueHolderV14 v14 = cpCMainbodyQueryCmd.cpCMainbodyQueryByID(acFManageId);
                                CpCMainbody cpCMainbody = (CpCMainbody) v14.getData();
                                outputVO.setAcFManageEcode(cpCMainbody.getEcode());
                                outputVO.setAcFManageEname(cpCMainbody.getEname());
                            }
                            Long cooperateId = liveCastStrategyDO.getCooperateId();
                            if (null != cooperateId) {
                                outputVO.setCooperateId(cooperateId);
                                ValueHolderV14 v14 = cpCMainbodyQueryCmd.cpCMainbodyQueryByID(cooperateId);
                                CpCMainbody cpCMainbody = (CpCMainbody) v14.getData();
                                outputVO.setCooperateEcode(cpCMainbody.getEcode());
                                outputVO.setCooperateEname(cpCMainbody.getEname());
                            }
                            outputVO.setLiveEvents(liveCastStrategyDO.getLiveEvents());
                            outputVO.setResult(true);

                            match = true;
                            break;
                        }
                    }
                }

                if (match) {
                    // 匹配到就不再匹配
                    break;
                }
            }
        }

        return outputVO;
    }

    /**
     * 重整主单直播标识
     *
     * @param order
     * @param orderItems
     */
    public void cleanUp(OcBOrder order, List<OcBOrderItem> orderItems) {
        // 标准单据重刷直播信息
        if (!CollectionUtils.isEmpty(orderItems)) {
            Map<String, List<OcBOrderItem>> liveKeyMap = orderItems.stream()
                    .filter(f -> Objects.isNull(f.getRefundStatus()) || f.getRefundStatus() != 6)
                    .filter(f -> checkKeyField(f))
                    .collect(Collectors.groupingBy(i -> getKey(i)));

            if (liveKeyMap == null || liveKeyMap.isEmpty()) {
                // 明细无直播信息
                clearLiveValue(order);
            } else {
                if (liveKeyMap.keySet().size() > 1) {
                    // 有两个不同的
                    clearLiveValue(order);
                    order.setLiveFlag(LiveFlagEnum.Y.getValue());
                } else {
                    // 仅有的唯一的一个
                    OcBOrderItem item = liveKeyMap.entrySet().iterator().next().getValue().get(0);
                    setLiveValue(order, item);
                }
            }
        }
    }

    /**
     * 检查关键字段
     *
     * @param item
     * @return
     */
    private boolean checkKeyField(OcBOrderItem item) {
        return Objects.nonNull(item) && Objects.nonNull(item.getLivePlatform()) && Objects.nonNull(item.getAnchorId()) && Objects.nonNull(item.getAcFManageId());
    }

    /**
     * 分组key
     *
     * @param orderItem
     * @return
     */
    private String getKey(OcBOrderItem orderItem) {

        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(orderItem.getAcFManageId());
        if (orderItem.getCooperateId() != null) {
            stringBuilder.append("-").append(orderItem.getCooperateId());
        }
        if (orderItem.getLiveEvents() != null) {
            stringBuilder.append("-").append(orderItem.getLiveEvents());
        }
        return stringBuilder.toString();
    }

    /**
     * clear 清理直播相关值
     *
     * @param order
     */
    private void clearLiveValue(OcBOrder order) {
        if (Objects.nonNull(order) && !(Objects.equals(PlatFormEnum.SHIPH.getCode(),
                order.getPlatform()))) {
            order.setAnchorId(null);
            order.setAnchorName(null);
            order.setLiveFlag(LiveFlagEnum.N.getValue());
        }
    }

    /**
     * set 设置直播相关值
     *
     * @param order
     * @param item
     */
    private void setLiveValue(OcBOrder order, OcBOrderItem item) {
        if (Objects.nonNull(order) && Objects.nonNull(item)) {
            //order.setLivePlatform(item.getLivePlatform());
            order.setAnchorId(item.getAnchorId());
            order.setAnchorName(item.getAnchorName());
            order.setLiveFlag(LiveFlagEnum.Y.getValue());
            //直播主体，配合主体，直播场次赋值
            order.setAcFManageId(item.getAcFManageId());
//            order.setAcFManageEcode(item.getAcFManageEcode());
//            order.setAcFManageEname(item.getAcFManageEname());
//            order.setCooperateId(item.getCooperateId());
//            order.setCooperateEcode(item.getAcFManageEcode());
//            order.setCooperateEname(item.getCooperateEname());
            order.setLiveEvents(item.getLiveEvents());
        }
    }

    /**
     * 复制赋值
     *
     * @param item
     * @param liveCastStrategyDO
     */
    private void setLiveValue(OcBOrderItem item, StCLiveCastStrategyDO liveCastStrategyDO) {
        if (Objects.nonNull(item) && Objects.nonNull(liveCastStrategyDO)) {
            item.setLivePlatform(liveCastStrategyDO.getLivePlatform());
            item.setAnchorId(liveCastStrategyDO.getAnchorId());
            item.setAnchorName(liveCastStrategyDO.getAnchorNickName());
            item.setLiveFlag(LiveFlagEnum.Y.getValue());
        }
    }

    /**
     * 取个实例
     *
     * @return
     */
    public static LiveMatchManager getInstance() {
        return ApplicationContextHandle.getBean(LiveMatchManager.class);
    }

}
