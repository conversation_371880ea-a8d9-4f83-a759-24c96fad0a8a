package com.jackrain.nea.oc.oms.services;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.ip.model.LogisticsSendModel;
import com.jackrain.nea.ip.model.TbLogisticsSendBySplitLineModel;
import com.jackrain.nea.ip.model.ascp.ConsignOrderShipModel;
import com.jackrain.nea.ip.model.result.LogisticsSendResult;
import com.jackrain.nea.ip.model.vips.VipJitxOrderShipModel;
import com.jackrain.nea.oc.oms.es.ES4ReturnOrder;
import com.jackrain.nea.oc.oms.mapper.IpBJitxOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.IpBJitxOrderMapper;
import com.jackrain.nea.oc.oms.mapper.IpBStandplatOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderDeliveryMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.IsActiveEnum;
import com.jackrain.nea.oc.oms.model.enums.OcReturnBillTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.jitx.JitxOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpAlibabaAscpOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBAlibabaAscpOrder;
import com.jackrain.nea.oc.oms.model.table.IpBJitxOrder;
import com.jackrain.nea.oc.oms.model.table.IpBJitxOrderItem;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderDelivery;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.taobao.TaoBaoOrderStatus;
import com.jackrain.nea.oc.oms.nums.InreturningStatus;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.st.model.table.StCVipcomJitxWarehouse;
import com.jackrain.nea.st.service.VipcomJitxWarehouseService;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolderV14Utils;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Description:平台发货服务类
 *
 * <AUTHOR> sunies
 * @since : 2020-11-03
 * create at : 2020-11-03 20:02
 */
@Slf4j
@Component
public class OrderPlatformDeliveryService {

    @Value("${jitx.order.platform.deliver.mock.enable:false}")
    private boolean paltformDeliveryMockEnable;


    @Autowired
    private IpRpcService ipRpcService;
    @Autowired
    private VipcomJitxWarehouseService jitxWarehouseService;

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private OcBOrderDeliveryMapper ocBOrderDeliveryMapper;

    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;

    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;

    @Autowired
    private IpAlibabaAscpOrderService ipAlibabaAscpOrderService;

    @Autowired
    private VipcomJitxWarehouseService vipcomJitxWarehouseService;

    @Autowired
    private OcCancelChangingOrRefundService ocCancelChangingOrRefundService;

    @Autowired
    private IpBJitxOrderMapper ipBJitxOrderMapper;

    @Autowired
    private IpBJitxOrderItemMapper ipBJitxOrderItemMapper;

    @Autowired
    private OcbCancelOrderMergeService ocbCancelOrderMergeService;

    @Autowired
    private IpBStandplatOrderMapper ipBStandplatOrderMapper;


    /**
     * 获取发货信息列表
     *
     * @param ocBOrder
     * @param skuIds
     * @return
     */
    public Map<String, List<OcBOrderDelivery>> getExpressCodeFromOrderDelivery(OcBOrder ocBOrder, Collection<Long> skuIds) {
        Map<String, List<OcBOrderDelivery>> orderDeliveryGroup = Maps.newHashMap();
        if (OrderTypeEnum.DIFFPRICE.getVal().equals(ocBOrder.getOrderType())) {
            orderDeliveryGroup.put(ocBOrder.getBillNo(), Lists.newArrayList());
        } else {
            List<OcBOrderDelivery> orderDelivery = ocBOrderDeliveryMapper.getExpressCodeFromOrderDelivery(ocBOrder.getId(), skuIds);
            if (skuIds.size() == 1 && orderDelivery.size() > 1) {
                orderDelivery = orderDelivery.subList(0, 1);
            }
            /**
             * ☆4、分组结果确定是多物流还是单物流发货
             */
            orderDeliveryGroup = orderDelivery.stream().collect(Collectors.groupingBy(OcBOrderDelivery::getLogisticNumber));
        }
        return orderDeliveryGroup;
    }

    /**
     * 平台发货接口 （淘宝、天猫换货、淘宝分销、淘宝经销、京东（包含换货））
     *
     * @param tbLogisticsModel 云枢纽接口入参
     * @param orderInfo        订单主表信息
     * @return
     */
    public ValueHolderV14<List<LogisticsSendResult>> packageNormalInterfaceParam(LogisticsSendModel tbLogisticsModel, OcBOrder orderInfo) {
        Long platForm = Long.valueOf(orderInfo.getPlatform());
        tbLogisticsModel.setId(orderInfo.getId());
        tbLogisticsModel.setPlatform(platForm);
        tbLogisticsModel.setSellerNick(orderInfo.getCpCShopSellerNick());
        tbLogisticsModel.setPaytype(Long.valueOf(orderInfo.getPayType()));
        tbLogisticsModel.setSeqNo(OcBOrderConst.IS_STATUS_IY.equals(orderInfo.getIsCycle()) ? "seqNo=" + orderInfo.getCurrentCycleNumber() : "");
        return ipRpcService.sendAndExchangeGoods(tbLogisticsModel, platForm);
    }

    public ValueHolderV14 packageTbSplitLineInterfaceParam(TbLogisticsSendBySplitLineModel tbLogisticsSendBySplitLineModel, OcBOrder orderInfo) {
        tbLogisticsSendBySplitLineModel.setSellerNick(orderInfo.getCpCShopSellerNick());
        tbLogisticsSendBySplitLineModel.setSession(cpRpcService.getSessionKey(orderInfo.getCpCShopId()));
        return ipRpcService.tbLogisticsSendBySplitLine(tbLogisticsSendBySplitLineModel);
    }

    /**
     * 查询物流公司档案平台维护的物流编码
     *
     * @param logisticsId
     * @param platformId
     * @return
     */
    public String getLogisticCode(Long logisticsId, Long platformId) {
        return cpRpcService.getPlatformLogisticEcode(logisticsId, platformId);
    }

    /**
     * ooid为空，更新为平台发货
     *
     * @param ocBOrder
     * @return
     */
    public boolean updateOrderAfterPlatDeliverySuccess(OcBOrder ocBOrder) {
        String msg = "订单无需平台发货，标记平台发货";
        omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.PLATFORM_SEND.getKey(), msg, null, msg, SystemUserResource.getRootUser());
        OcBOrder order = new OcBOrder();
        order.setId(ocBOrder.getId());
        order.setOrderStatus(OmsOrderStatus.PLATFORM_DELIVERY.toInteger());
        order.setIsForce(0L);
        order.setForceSendFailReason("平台发货标记为失败:无子交易号或不存在未发货的子交易号，标记平台发货");
        order.setModifieddate(new Date());
        return ocBOrderMapper.updateById(order) > 0;
    }


    /**
     * 线程休眠
     *
     * @param isSleep
     * @param time
     */
    public static void threadSleep(boolean isSleep, long time) {
        try {
            if (isSleep) {
                Thread.sleep(time);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("线程休眠异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
        }
    }

    /**
     * 猫超仓库发货调用云枢纽接口
     *
     * @param ocBOrderRelation 订单对象
     * @return
     */
    public ValueHolderV14 alibabaAscpShippingBack(OcBOrderRelation ocBOrderRelation) {

        ConsignOrderShipModel consignOrderShipModel = new ConsignOrderShipModel();
        OcBOrder ocBOrder = ocBOrderRelation.getOrderInfo();
        List<OcBOrderItem> ocBOrderItemList = ocBOrderRelation.getOrderItemList();

        IpAlibabaAscpOrderRelation ipAlibabaAscpOrderRelation = ipAlibabaAscpOrderService.selectAlibabaAscpOrder(ocBOrder.getTid());
        IpBAlibabaAscpOrder alibabaAscpOrder = ipAlibabaAscpOrderRelation.getAlibabaAscpOrder();

        consignOrderShipModel.setWholeSheetConsigned(0 == ocBOrder.getIsMultiPack());

        //逻辑仓信息
        consignOrderShipModel.setStoreCode(alibabaAscpOrder.getStoreCode());
//        consignOrderShipModel.setStoreName(ocBOrder.getCpCStoreEname());
        consignOrderShipModel.setBizOrderCode(ocBOrder.getSourceCode());
        consignOrderShipModel.setOutBizId(ocBOrder.getBillNo());

        //根据店铺id,查询店铺信息
        CpShop cpShop = cpRpcService.selectShopById(ocBOrder.getCpCShopId());
        consignOrderShipModel.setSupplierId(cpShop.getPlatformSupplierId());

        List<ConsignOrderShipModel.Orderitems> orderitemsList = new ArrayList();

        ocBOrderItemList.stream().forEach(item -> {
            ConsignOrderShipModel.Orderitems items = new ConsignOrderShipModel.Orderitems();
            items.setScItemId(item.getSkuNumiid());
            items.setItemQuantity(item.getQty().longValue());
            items.setLackQuantity(0L);
            items.setSubOrderCode(item.getOoid());
            orderitemsList.add(items);
        });
        consignOrderShipModel.setOrderItems(orderitemsList);

        //发货信息
        ConsignOrderShipModel.Senderinfo senderinfo = new ConsignOrderShipModel.Senderinfo();
        senderinfo.setSenderName(cpShop.getSellerName());
        senderinfo.setSenderZipCode(cpShop.getSellerZip());
        senderinfo.setSenderCountry(StringUtils.isBlank(alibabaAscpOrder.getSenderCountry()) ? "中国" : alibabaAscpOrder.getSenderCountry());
        senderinfo.setSenderProvince(cpShop.getSellerProvince());
        senderinfo.setSenderCity(cpShop.getSellerCity());
        senderinfo.setSenderArea(cpShop.getSellerArea());
        senderinfo.setSenderTown("无");
        senderinfo.setSenderPhone(cpShop.getSellerPhone());
        senderinfo.setSenderMobile(cpShop.getSellerPhone());
        senderinfo.setSenderAddress(cpShop.getSellerAddress());
        consignOrderShipModel.setSenderInfo(senderinfo);

        //包裹列表
        List<ConsignOrderShipModel.Tmsorders> tmsOrdersList = new ArrayList<>();
        ConsignOrderShipModel.Tmsorders tmsOrders = new ConsignOrderShipModel.Tmsorders();
        tmsOrders.setTmsServiceName(ocBOrder.getCpCLogisticsEname());
        tmsOrders.setTmsServiceCode(ocBOrder.getCpCLogisticsEcode());
        tmsOrders.setTmsOrderCode(ocBOrder.getExpresscode());
        tmsOrdersList.add(tmsOrders);

        //包裹明细列表
        List<ConsignOrderShipModel.Tmsitems> tmsItems = new ArrayList<>();

        ocBOrderItemList.stream().forEach(item -> {
            ConsignOrderShipModel.Tmsitems im = new ConsignOrderShipModel.Tmsitems();
            im.setScItemId(item.getSkuNumiid());
            im.setItemQuantity(item.getQty().longValue());
            im.setLackQuantity(0L);
            im.setSubOrderCode(item.getOoid());
            tmsItems.add(im);
        });

        tmsOrders.setTmsItems(tmsItems);
        consignOrderShipModel.setTmsOrders(tmsOrdersList);
        return ipRpcService.alibabaAscpShippingBack(consignOrderShipModel, ocBOrder.getCpCShopSellerNick());
    }


    /**
     * 封装调用陈顺唯品会平台发货接口
     *
     * @param ocBOrder      订单对象
     * @param user          user对象
     * @param isFailedSplit 合包订单平台发货失败是否取消合包
     * @return
     */
    public ValueHolderV14 weiPinHuiPlaformSendGoods(OcBOrder ocBOrder, User user, Boolean isFailedSplit) {
        if (paltformDeliveryMockEnable) {
            return ValueHolderV14Utils.getSuccessValueHolder("mock发货成功");
        }
        ValueHolderV14 result = new ValueHolderV14();
        IpBJitxOrder ipBJitxOrder = ipBJitxOrderMapper.selectJitxOrderByOrderSn(ocBOrder.getTid());
        if (JitxOrderStatus.ORDER_ALREADY_COLLECTED.equals(ipBJitxOrder.getOrderStatus())) {
            result.setCode(ResultCode.SUCCESS);
            result.setMessage("单据状态为已揽收，标记为平台发货");
        } else {
            VipJitxOrderShipModel vipJitxOrderShipModel = new VipJitxOrderShipModel();
            List<VipJitxOrderShipModel.Ship> shipList = new ArrayList();
            VipJitxOrderShipModel.Ship ship = new VipJitxOrderShipModel.Ship();
            List<VipJitxOrderShipModel.Ship.Package> pakageList = new ArrayList();
            Date scanTime = ocBOrder.getScanTime() == null ? new Date() : ocBOrder.getScanTime();
            //获取订单的出库时间，【实际打包时间】
            Long oqc_date = scanTime.getTime() / 1000;

            List<IpBJitxOrderItem> jitxOrderItems = ipBJitxOrderItemMapper.selectList(new QueryWrapper<IpBJitxOrderItem>().lambda()
                    .eq(IpBJitxOrderItem::getIpBJitxOrderId, ipBJitxOrder.getId())
                    .eq(BaseModel::getIsactive, IsActiveEnum.Y.getKey()));
            if (CollectionUtils.isEmpty(jitxOrderItems)) {
                log.error("零售发货单 ID:{} 平台发货，查询JITX订单中间表明细为空！", ocBOrder.getId());
                result.setCode(ResultCode.FAIL);
                result.setMessage("查询JITX订单中间表明细为空");
                return result;
            }

            //根据订单ID查询零售发货单明细
//            List<OcBOrderItem> orderItemList = ocBOrderItemMapper.selectList(Wrappers.<OcBOrderItem>lambdaQuery()
//                    .eq(OcBOrderItem::getOcBOrderId, ocBOrder.getId())
//                    .eq(OcBOrderItem::getRefundStatus, OcOrderRefundStatusEnum.NOTREFUND.getVal())
//                    .ne(OcBOrderItem::getProType, SkuType.NO_SPLIT_COMBINE)
//                    .eq(OcBOrderItem::getIsactive, IsActiveEnum.Y.getKey()));
//            if (CollectionUtils.isEmpty(orderItemList)) {
//                log.error("零售发货单 ID:{} 平台发货，查询有效明细为空！", ocBOrder.getId());
//                result.setCode(ResultCode.FAIL);
//                result.setMessage("查询零售发货单有效明细为空");
//                return result;
//            }
//
//            List<OcBOrderItem> newOrderItems = transformVipIpItemParam(orderItemList, ocBOrder.getId());

            List<VipJitxOrderShipModel.Ship.Package.PackageDetail> packageDetailList = new ArrayList();
            for (int i = 0; i < jitxOrderItems.size(); i++) {
                VipJitxOrderShipModel.Ship.Package.PackageDetail packageDetail = new VipJitxOrderShipModel.Ship.Package.PackageDetail();
                packageDetail.setBarcode(jitxOrderItems.get(i).getBarcode());
                packageDetail.setQuantity(jitxOrderItems.get(i).getQuantity().intValue());
                packageDetailList.add(packageDetail);
            }

//            List<String> tidList = new ArrayList<>();
//            for (int i = 0; i < newOrderItems.size(); i++) {
//                OcBOrderItem item = newOrderItems.get(i);
//                VipJitxOrderShipModel.Ship.Package.PackageDetail packageDetail = new VipJitxOrderShipModel.Ship.Package.PackageDetail();
//                packageDetail.setBarcode(item.getBarcode());
//                packageDetail.setQuantity(item.getQty().intValue());
////                if (StringUtils.isNotEmpty(ocBOrder.getMergedCode()) && newOrderItems.size() > 1) {
////                    packageDetail.setTradeId(item.getTid());
////                }
//                packageDetailList.add(packageDetail);
//                /**明细平台单号集合*/
//                tidList.add(item.getTid());
//            }

            /**物流单号*/
            String expresscode = ocBOrder.getExpresscode();

            VipJitxOrderShipModel.Ship.Package pakage = new VipJitxOrderShipModel.Ship.Package();
            pakage.setBox_no(1);
            pakage.setOqc_date(oqc_date);
            pakage.setPackage_no(expresscode);
            pakage.setTransport_no(expresscode);
            pakage.setDetails(packageDetailList);
            pakageList.add(pakage);

            //陈秀楼确认后，Order_sn取值修改为订单信息的SourceCode
            ship.setOrder_sn(ocBOrder.getSourceCode());
            ship.setTotal_package(1);
            ship.setPackages(pakageList);
            // 斯凯奇 平台发货时，判断该JITX订单是否存在“合”标识，若存在，则取“JITX合包发货平台单号”
            //字段的值作为平台单号的入参
            if (YesNoEnum.Y.getVal().equals(ocBOrder.getIsMerge())) {
                ship.setOrder_sn(ocBOrder.getJitxMergedDeliverySn());
            }
            /*JITX不会有拆合单*/
            // 对tidList 进行去重
//            if (StringUtils.isNotEmpty(ocBOrder.getMergedCode()) && tidList.size() > 1) {
//                //子订单集合
////                tidList = tidList.stream().distinct().collect(Collectors.toList());
////                ship.setMergedOrderSns(tidList);
//
//                //合包订单发货前校验
//                int resultInt = cancelMergeOrder(isFailedSplit, ocBOrder, user, null);
//                if (-1 == resultInt) {
//                    log.warn(" 零售发货单平台发货ID:{}，不满足审核条件！", ocBOrder.getId());
//                    result.setCode(ResultCode.FAIL);
//                    result.setMessage("不满足审核条件");
//                    return result;
//                } else if (0 == resultInt) {
//                    log.warn(" 零售发货单平台发货ID:{}，已发货直接审核！", ocBOrder.getId());
//                    result.setCode(ResultCode.SUCCESS);
//                    result.setMessage("发货成功");
//                    return result;
//                }
//            }
            //根据实体仓id
            //斯凯奇项目 by caomeng at 20210521 todo
            //在调用平台发货接口时，如果「JITX要求发货仓」不为空，则发货仓库 delivery_warehouse使用JITX订单的「JITX要求发货仓」，如果为空，则使用零售发货单的「发货仓库」。
//            Long wareHouseId = ocBOrder.getCpCPhyWarehouseId();
//            if (ocBOrder.getJitxRequiresDeliveryWarehouseId() != null) {
//                wareHouseId = ocBOrder.getJitxRequiresDeliveryWarehouseId();
//            }

            //发货仓库
            ship.setDelivery_warehouse(ipBJitxOrder.getDeliveryWarehouse());
            shipList.add(ship);
            vipJitxOrderShipModel.setOperateUser(user);
            vipJitxOrderShipModel.setShips(shipList);
            vipJitxOrderShipModel.setSellerNick(ocBOrder.getCpCShopSellerNick());
            //取经销商id
            //1.根据店铺id,查询店铺信息
            CpShop cpShop = cpRpcService.selectShopById(ocBOrder.getCpCShopId());
            String vendorId = cpShop.getPlatformSupplierId();
            vipJitxOrderShipModel.setVendorId(vendorId);
            result = ipRpcService.weiPinHuiSendGoods(vipJitxOrderShipModel);
        }
        try {
            String msg = result.getMessage();
            JSONObject retData = (JSONObject) result.getData();
            if (retData != null) {
                JSONArray failArray = retData.getJSONArray("failed_list");
                int successNum = retData.getIntValue("success_num");
                if (failArray != null && !failArray.isEmpty()) {
                    JSONObject tmpJson = failArray.getJSONObject(0);
                    msg = tmpJson.getString("msg");
                    int jitxResult = cancelMergeOrder(isFailedSplit, ocBOrder, user, msg);
                    if (0 == jitxResult) {
                        result.setCode(ResultCode.SUCCESS);
                    }
                } else if (successNum < 1) {
                    log.error(" 平台发货返回成功记录为0：,{}", retData.toJSONString());
                    msg = "平台发货失败，平台发货返回成功记录数为0，失败原因为空";
                    result.setCode(ResultCode.FAIL);
                    result.setMessage("平台发货返回成功记录为0：" + retData.toJSONString());
                }
            }
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.PLATFORM_SEND.getKey(),
                    String.format(ocBOrder.getId() + "JITX平台发货结果为：%s", msg), null, msg, SystemUserResource.getRootUser());
        } catch (Exception e) {
            log.error("{},唯品会平台发货日志记录异常：{}", this.getClass().getSimpleName(), Throwables.getStackTraceAsString(e));
        }
        return result;
    }


    /**
     * 如果存在福袋、组合商品将真实商品明细转换为平台的虚拟明细
     *
     * @param orderItems
     * @param id
     * @return
     */
    private List<OcBOrderItem> transformVipIpItemParam(List<OcBOrderItem> orderItems, Long id) {
        List<OcBOrderItem> newOrderItems = new ArrayList<>();
        List<OcBOrderItem> transformOrderItems = new ArrayList<>();
        for (OcBOrderItem item : orderItems) {
            Long proType = item.getProType();
            if (proType != null
                    && (proType.intValue() == SkuType.COMBINE_PRODUCT || proType.intValue() == SkuType.GIFT_PRODUCT)) {
                transformOrderItems.add(item);
            } else {
                newOrderItems.add(item);
            }
        }
        if (CollectionUtils.isEmpty(transformOrderItems)) {
            //需要转换的明细为空时，直接返回原明细
            return orderItems;
        }
        List<String> ooidList = transformOrderItems.stream()
                .filter(obj -> StringUtils.isNotBlank(obj.getOoid()))
                .map(obj -> obj.getOoid())
                .distinct().collect(Collectors.toList());
        List<OcBOrderItem> specialItems = ocBOrderItemMapper.selectList(new LambdaQueryWrapper<OcBOrderItem>()
                .eq(OcBOrderItem::getOcBOrderId, id)
                .eq(OcBOrderItem::getProType, Long.valueOf(SkuType.NO_SPLIT_COMBINE))
                .in(CollectionUtils.isNotEmpty(ooidList), OcBOrderItem::getOoid, ooidList));
        if (CollectionUtils.isNotEmpty(specialItems)) {
            newOrderItems.addAll(specialItems);
        }
        return newOrderItems;
    }


    private int cancelMergeOrder(Boolean isFailedSplit, OcBOrder ocBOrder, User user, String msg) {
        try {
            String mergeSourceCode = ocBOrder.getMergeSourceCode();
            if (ObjectUtils.isEmpty(mergeSourceCode) || !mergeSourceCode.contains(",") || !Boolean.TRUE.equals(isFailedSplit)) {
                return 1;
            }

            //查询所有子订单在中间表平台状态为已发货的
            List<IpBJitxOrder> jitxOrderList = ipBJitxOrderMapper.selectList(new LambdaQueryWrapper<IpBJitxOrder>()
                    .in(IpBJitxOrder::getOrderSn, Arrays.asList(mergeSourceCode.split(","))));

            if (CollectionUtils.isEmpty(jitxOrderList)) {
                log.warn(" 未找到JITX中间表记录，tid:{}", ocBOrder.getMergedCode());
                return 1;
            }

            //虚拟发货前
            if (ObjectUtils.isEmpty(msg)) {
                //全部发货直接审核通过，不用虚拟发货
                if (jitxOrderList.stream().allMatch(o -> JitxOrderStatus.ORDER_ALREADY_SEND.equals(o.getOrderStatus()))) {
                    omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.ORDER_EXAMINE.getKey(),
                            "合包订单全部【已发货】，审核成功", "", "", user);
                    return 0;
                }

                //部分发货，审核失败等待下次审核
                if (jitxOrderList.stream().anyMatch(o -> JitxOrderStatus.ORDER_ALREADY_SEND.equals(o.getOrderStatus()))) {
                    omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.ORDER_EXAMINE.getKey(),
                            "合包订单中有部分【已发货】订单，审核失败等待下次审核", "", "", user);
                    return -1;
                }

                //存在取消状态调用取消合包。审核失败
                if (jitxOrderList.stream().anyMatch(o -> JitxOrderStatus.ORDER_SEND_REFUND.equals(o.getOrderStatus()) ||
                        JitxOrderStatus.ORDER_UNSEND_REFUND.equals(o.getOrderStatus()))) {
                    omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.ORDER_EXAMINE.getKey(),
                            "合包订单中有【未发货取消】【已发货取消】订单，审核失败", "", "", user);
                    ocbCancelOrderMergeService.cancelMergeOrder(user, Collections.singletonList(ocBOrder.getId()));
                    return -1;
                }

            } else {

                //包含未发货取消关键字，且中间表状态包含已取消订单
                if (msg.contains("未发货取消") || (msg.contains("已发货取消") && jitxOrderList.stream()
                        .allMatch(o -> JitxOrderStatus.ORDER_SEND_REFUND.equals(o.getOrderStatus())))) {
                    omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.ORDER_EXAMINE.getKey(),
                            "合包订单中有【未发货取消】【已发货取消】订单，平台发货失败，审核失败", "", "", user);
                    ocbCancelOrderMergeService.cancelMergeOrder(user, Collections.singletonList(ocBOrder.getId()));
                    return -1;
                }

                //接口返回已发货取消，状态非全部已发货取消，直接审核失败等待下次审核
                if (msg.contains("已发货取消")) {
                    omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.ORDER_EXAMINE.getKey(),
                            "合包订单中有【已发货取消】订单，审核失败", "", "", user);
                    return -1;
                }

                if (msg.contains("已发货")) {
                    if (jitxOrderList.stream().allMatch(o -> JitxOrderStatus.ORDER_ALREADY_SEND.equals(o.getOrderStatus()))) {
                        omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.ORDER_EXAMINE.getKey(),
                                "平台发货失败，提示合包订单已发货，审核成功", "", "", user);
                        return 0;
                    } else {
                        omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.ORDER_EXAMINE.getKey(),
                                "合包订单中有部分【已发货】订单，平台发货失败，等待下次审核", "", "", user);
                        return -1;
                    }
                }

            }
        } catch (Exception e) {
            log.error(" 平台发货失败，取消合包异常：{}", Throwables.getStackTraceAsString(e));
            return -1;
        }
        return 1;
    }

    /**
     * 同步调用云枢纽封装的唯品会平台发货接口
     *
     * @param ocBOrder
     * @return
     */
    @Deprecated
    public ValueHolderV14 buildParamAndInvoke(OcBOrder ocBOrder) {
        VipJitxOrderShipModel vipJitxOrderShipModel = new VipJitxOrderShipModel();
        VipJitxOrderShipModel.Ship ship = new VipJitxOrderShipModel.Ship();
        List<VipJitxOrderShipModel.Ship.Package> packageList = new ArrayList();
        VipJitxOrderShipModel.Ship.Package packageModel = new VipJitxOrderShipModel.Ship.Package();
        List<VipJitxOrderShipModel.Ship.Package.PackageDetail> packageDetailList = new ArrayList();
        Long scanTime = ocBOrder.getScanTime().getTime() / 1000;
        /**
         * ☆获取发货信息列表
         */
        List<OcBOrderDelivery> ocBOrderDeliveryList = ocBOrderDeliveryMapper.selectOrderDeliveryByOrderId(ocBOrder.getId());
        for (int i = 0; i < ocBOrderDeliveryList.size(); i++) {
            OcBOrderDelivery orderDelivery = ocBOrderDeliveryList.get(i);
            String expressCode = orderDelivery.getLogisticNumber();
            VipJitxOrderShipModel.Ship.Package.PackageDetail packageDetail = new VipJitxOrderShipModel.Ship.Package.PackageDetail();
            packageDetail.setBarcode(orderDelivery.getGbcode());
            packageDetail.setQuantity(orderDelivery.getQty().intValue());
            packageDetailList.add(packageDetail);
            //包裹数量，无多包裹业务，直接给1
            packageModel.setBox_no(1);
            packageModel.setOqc_date(scanTime);
            packageModel.setPackage_no(expressCode);
            packageModel.setTransport_no(expressCode);
            packageModel.setDetails(packageDetailList);
            packageList.add(packageModel);
        }
        ship.setOrder_sn(ocBOrder.getSourceCode());
        ship.setTotal_package(1);
        ship.setPackages(packageList);
        /**
         * ☆获取唯品会仓库
         */
        StCVipcomJitxWarehouse stCVipcomJitxWarehouse = vipcomJitxWarehouseService.queryJitxCapacity(ocBOrder.getCpCShopId(), ocBOrder.getCpCPhyWarehouseId(), null);
        ship.setDelivery_warehouse(Optional.ofNullable(stCVipcomJitxWarehouse).map(StCVipcomJitxWarehouse::getVipcomWarehouseEcode).orElse(null));
        vipJitxOrderShipModel.setOperateUser(SystemUserResource.getRootUser());
        vipJitxOrderShipModel.setShips(Arrays.asList(ship));
        vipJitxOrderShipModel.setSellerNick(ocBOrder.getCpCShopSellerNick());
        /**
         * ☆获取店铺信息
         */
        CpShop cpShop = cpRpcService.selectShopById(ocBOrder.getCpCShopId());
        String vendorId = cpShop.getPlatformSupplierId();
        vipJitxOrderShipModel.setVendorId(vendorId);
        /**
         * ☆请求云枢纽接口
         */
        ValueHolderV14 result = ipRpcService.weiPinHuiSendGoods(vipJitxOrderShipModel);
        omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.PLATFORM_SEND.getKey(), ocBOrder.getId() + "JITX平台发货结果为：" + result.getMessage(), null, result.getMessage(), SystemUserResource.getRootUser());
        return result;
    }

    /**
     * ☆更新订单状态为平台发货
     * jitx和拼多多和苏宁调用时,如果是退款中，还需取消等待退货入库的退单
     *
     * @param ocBOrder
     * @return
     */
    public boolean updateOrderAndVoidReturnOrder(OcBOrder ocBOrder) {
        if (ocBOrder.getIsInreturning().equals(InreturningStatus.INRETURNING)) {
            try {
                JSONObject whereKeys = new JSONObject();
                whereKeys.put("ORIG_ORDER_ID", ocBOrder.getId());
                whereKeys.put("RETURN_STATUS", ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal());
                JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_RETURN_ORDER_INDEX_NAME,
                        OcElasticSearchIndexResources.OC_B_RETURN_ORDER_TYPE_NAME, whereKeys, null, null, 100, 0, new String[]{"ID"});
                JSONArray returnIds = search.getJSONArray("data");
                if (CollectionUtils.isNotEmpty(returnIds)) {
                    for (int i = 0; i < returnIds.size(); i++) {
                        JSONObject map = (JSONObject) returnIds.get(i);
                        OcBReturnOrder ocBReturnOrder = ocBReturnOrderMapper.getRefundOrder(map.getLong("ID"), ocBOrder.getId(), ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal());
                        Optional.ofNullable(ocBReturnOrder).ifPresent(s -> {
                            ocBReturnOrder.setReturnStatus(ReturnStatusEnum.CANCLE.getVal());
                            ocBReturnOrderMapper.updateById(ocBReturnOrder);
                        });
                    }
                }
            } catch (Exception e) {
                log.error(LogUtil.format("唯品会平台发货成功后作废退单异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            }
        }
        return ocBOrderMapper.updateOrderAfterPlatDeliverySuccess(ocBOrder.getId()) > 0;
    }

    /**
     * ljp add
     * 打上平台发货成功或失败的标记
     *
     * @param ocBOrder
     * @return
     */
    public void updateOrder(OcBOrder ocBOrder) {
        omsOrderService.updateOrderInfo(ocBOrder);
    }

    /**
     * 平台发货失败更新
     *
     * @param expressCode
     * @param msg
     * @param ocBOrder
     */
    @Transactional(rollbackFor = Exception.class)
    public void failUpdate(String expressCode, String msg, OcBOrder ocBOrder) {
        OcBOrder order = new OcBOrder();
        order.setId(ocBOrder.getId());
        order.setIsForce(0L);
        order.setForceSendFailReason(msg);
        order.setSysremark(msg);
        order.setMakeupFailNum(ocBOrder.getMakeupFailNum() + 1);
        this.updateOrder(order);
        ocBOrderItemMapper.updateFailTimesByOrderid(ocBOrder.getId());
        ocBOrderDeliveryMapper.updateSendGoodsStatusByExpressCode(ocBOrder.getId(), expressCode, 0L);
    }


    /**
     * 作废退单
     *
     * @param ocBOrder
     */
    public void updateReturnOrder(OcBOrder ocBOrder) {
        //去es查询出退单的数据 如果存在并且状态为待退换入库
        if (!ocBOrder.getIsInreturning().equals(InreturningStatus.INRETURNING)) {
            return;
        }
        JSONArray aryIds = ES4ReturnOrder.QueryReturnOrdersByOrderId(ocBOrder);
        if (aryIds == null) {
            return;
        }
        for (int i = 0; i < aryIds.size(); i++) {
            //  Map<String, Long> map = (Map<String, Long>) aryIds.get(i);
            //OcBReturnOrder ocBReturnOrder = ocBReturnOrderMapper.getRefundOrder(map.get("ID"), ocBOrder.getId(),
            //      ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal());
            //作废退单
            //  ocBReturnOrder.setReturnStatus(ReturnStatusEnum.CANCLE.getVal());
            // if (null != ocBReturnOrder) {
            // ocBReturnOrderMapper.updateById(ocBReturnOrder);//作废退单
            JSONObject jsonObject = new JSONObject();
            JSONArray jsonArray = new JSONArray();
            jsonArray.add(aryIds.get(i));
            jsonObject.put("ids", jsonArray);
            ValueHolderV14 holderV14 = ocCancelChangingOrRefundService.orRefundService(jsonObject, SystemUserResource.getRootUser(), Boolean.FALSE);
            if (holderV14.getCode() == -1) {
                log.error(LogUtil.format("updateReturnOrder 取消退单失败,退单id{}. 失败原因{}"), aryIds.get(i), holderV14.getMessage());
            }
        }
    }

    /**
     * 更新发货信息列表的发货状态
     *
     * @param orderId
     * @param expressCode
     * @param status
     */
    public void updateSendGoodsStatusByExpressCode(Long orderId, String expressCode, Long status) {
        ocBOrderDeliveryMapper.updateSendGoodsStatusByExpressCode(orderId, expressCode, status);
    }

    /**
     * 订单如果是 复制单 或者补发单 或者平台为8888  则不同步平台，直接平台发货
     *
     * @param orderInfo
     * @return
     */
    public boolean orderDeliveryOfNoSend(OcBOrder orderInfo) {
        Long orderId = orderInfo.getId();
        String billNo = orderInfo.getBillNo();
        Integer platform = orderInfo.getPlatform();

        //是复制订单
        Integer isCopyOrder = Optional.ofNullable(orderInfo.getIsCopyOrder()).orElse(0);
        //是补发单
        Integer orderType = orderInfo.getOrderType();
        //是否是手动新增订单
        String orderSource = orderInfo.getOrderSource();
        boolean isFilter = isFilterPingDDReissueOrder(orderInfo);
        if (isFilter) {
            return false;
        }
        //如果是京东自营订单 通过平台单号发现已经有拆标并且已经平台发货，那就直接标记平台发货
        boolean isJingDingDx = checkiIsSendOut(orderInfo);

        //好衣库补发单不直接平台发货
        if (OrderTypeEnum.REISSUE.getVal().equals(orderType) && PlatFormEnum.HAO_YI_KU.getCode().equals(platform)) {
            return false;
        }

//        // 如果是补发 并且是抖音的话 则需要调用平台发货
//        if (OrderTypeEnum.REISSUE.getVal().equals(orderType) && PlatFormEnum.DOU_YIN.getCode().equals(platform)) {
//            return false;
//        }

        if (1 == isCopyOrder
                || OrderTypeEnum.REISSUE.getVal().equals(orderType)
                || PlatFormEnum.NO_SEND_PLATFORM.getCode().equals(platform)
                || "手工新增".equals(orderSource)
                || isJingDingDx) {
            //更新发货状态，插入日志
            String logMsg = "OrderId=" + orderId + "为复制单或补发单或者手工订单,或者平台为8888，直接标记平台发货";
            if (isJingDingDx) {
                logMsg = "OrderId=" + orderId + "为京东自营订单，且平台单号[" + orderInfo.getTid() + "]已经有订单平台发货，直接标记平台发货";
            }
            omsOrderLogService.addUserOrderLog(orderId, billNo, OrderLogTypeEnum.PLATFORM_SEND.getKey(), logMsg, "",
                    null, null);
            OcBOrder update = new OcBOrder();
            update.setOrderStatus(OmsOrderStatus.PLATFORM_DELIVERY.toInteger());
            update.setId(orderId);
            ocBOrderMapper.updateById(update);
            return true;
        }

        if (PlatFormEnum.DOU_YIN.getCode().equals(platform)) {
            IpBStandplatOrder ipBStandplatOrder = ipBStandplatOrderMapper.selectStandplatOrderByTid(orderInfo.getTid());
            if (TaoBaoOrderStatus.REFUND_FINISHED.equals(ipBStandplatOrder.getStatus())
                    || TaoBaoOrderStatus.TRADE_CANCELED.equals(ipBStandplatOrder.getStatus())
                    || TaoBaoOrderStatus.TRADE_FINISHED.equals(ipBStandplatOrder.getStatus())) {
                OcBOrder updateOrder = new OcBOrder();
                updateOrder.setId(orderId);
                updateOrder.setOrderStatus(OmsOrderStatus.PLATFORM_DELIVERY.toInteger());
                updateOrder.setIsForce(1L);
                updateOrder.setPlatformDeliveryTime(new Date());
                updateOrder.setSysremark("根据订单中间表状态,标记平台发货完成");
                updateOrder.setModifieddate(new Date());
                ocBOrderMapper.updateById(updateOrder);
                String logMsg = "OrderId=" + orderId + "根据订单中间表状态,标记平台发货完成";
                omsOrderLogService.addUserOrderLog(orderId, billNo, OrderLogTypeEnum.PLATFORM_SEND.getKey(), logMsg, "",
                        null, null);
                return true;
            }
        }
        return false;
    }

    /**
     * 拼多多补寄,平台发货
     *
     * @param order 补寄订单
     * @return
     */
    private boolean isFilterPingDDReissueOrder(OcBOrder order) {
        boolean isJudgePddReissueOrder = OrderTypeEnum.REISSUE.getVal().equals(order.getOrderType())
                && PlatFormEnum.PINDUODUO.getCode().equals(order.getPlatform());
        if (!isJudgePddReissueOrder) {
            return false;
        }
        Long origReturnOrderId = order.getOrigReturnOrderId();
        if (Objects.isNull(origReturnOrderId)) {
            return false;
        }
        OcBReturnOrder returnOrder = ocBReturnOrderMapper.selectByid(origReturnOrderId);
        if (Objects.isNull(returnOrder)) {
            return false;
        }
        boolean isReturnReissue = OcReturnBillTypeEnum.REISSUE.getVal().equals(returnOrder.getBillType());
        return isReturnReissue;
    }

    /**
     * description:判断是不是代销订单是不是有单子已经平台发货
     *
     * @Author: liuwenjin
     * @Date 2022/12/16 20:29
     */
    private boolean checkiIsSendOut(OcBOrder orderInfo) {
        if (PlatFormEnum.JINGDONG_DX.getCode().equals(orderInfo.getPlatform().intValue())) {
            String tid = orderInfo.getTid();
            // TID +拆+平台发货+!=合
            List<OcBOrder> ocBOrders = ocBOrderMapper.selectDeliveryByTid(tid);
            //有值就全部更新成平台发货
            return CollectionUtils.isNotEmpty(ocBOrders);
        }
        return false;
    }

    /**
     * 虚拟发货，直接标记平台发货
     *
     * @param orderInfo orderInfo
     */
    public void orderDeliveryByNoSend(OcBOrder orderInfo) {
        Long orderId = orderInfo.getId();
        String billNo = orderInfo.getBillNo();
        Integer platform = orderInfo.getPlatform();

        //更新发货状态，插入日志
        String logMsg = "OrderId=" + orderId + "为虚拟发货，直接标记平台发货";
        omsOrderLogService.addUserOrderLog(orderId, billNo, OrderLogTypeEnum.PLATFORM_SEND.getKey(), logMsg, "",
                null, null);
        OcBOrder update = new OcBOrder();
        update.setOrderStatus(OmsOrderStatus.PLATFORM_DELIVERY.toInteger());
        update.setId(orderId);
        ocBOrderMapper.updateById(update);

        //toc订单，直接平台发货的，更新实发数量为数量字段值
        ocBOrderItemMapper.updateRealNumSourceQtyByOrderIds(Lists.newArrayList(orderId));
    }
}
