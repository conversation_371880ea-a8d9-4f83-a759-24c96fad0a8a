package com.jackrain.nea.ac.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.ac.model.AcFPayableAdjustmentDO;
import com.jackrain.nea.ac.model.AcFPayableAdjustmentExDO;
import com.jackrain.nea.ac.model.PayablePageRequest;
import com.jackrain.nea.common.ReferenceUtil;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBOrderPermissionCmd;
import com.jackrain.nea.oc.oms.mapper.ac.AcFPayableAdjustmentMapper;
import com.jackrain.nea.oc.oms.model.constant.AcConstant;
import com.jackrain.nea.oc.oms.model.enums.ac.ResponsiblePartyEnum;
import com.jackrain.nea.oc.oms.model.relation.BasePermission;
import com.jackrain.nea.oc.oms.model.relation.UserPermission;
import com.jackrain.nea.oc.oms.permission.OcOrderAuthorityMgtService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ACEnumUtil;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.CommonEsSearchUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.utils.ValueHolderUtils;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: 陈俊明
 * @since: 2019-07-09
 * @create at : 2019-07-09 21:31
 */
@Component
@Slf4j
@Transactional
public class PayableAdjustmentSelectListService extends CommandAdapter {
    @Autowired
    private AcFPayableAdjustmentMapper mainMapper;

    @Autowired
    OcOrderAuthorityMgtService ocOrderAuthorityMgtService;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {

        log.error(LogUtil.format("PayableAdjustmentSelectListService.PayableAdjustmentSelectListService ",
                "PayableAdjustmentSelectListService"));

        ValueHolder valueHolder = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject paramYs = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);
        User user = querySession.getUser();
        UserPermission usrPem = null;
        try {
            OcBOrderPermissionCmd ocBOrderPermissionCmd = (OcBOrderPermissionCmd) (ReferenceUtil.refer(
                    ApplicationContextHandle.getApplicationContext(), OcBOrderPermissionCmd.class.getName(),
                    "oms-fi", "1.0"));
            usrPem = ocBOrderPermissionCmd.getCurrentUserPermission(null, AcConstant.TAB_AC_F_PAYABLE_ADJUSTMENT, user);

        } catch (Exception e) {
            log.error(LogUtil.format("UserPermissionHelper.currentUserPermission:{},{}",
                    "currentUserPermission"), AcConstant.TAB_AC_F_PAYABLE_ADJUSTMENT, Throwables.getStackTraceAsString(e));
        }

        if (usrPem == null) {
            return ValueHolderUtils.getFailValueHolder("未获取到用户权限");
        }
        JSONObject wherekeys = new JSONObject();
        JSONObject filter = new JSONObject();
        //whereInfo
        JSONObject param = paramYs.getJSONObject("whereInfo");
        //创建时间-开始
        Date creationdateStart = param.getDate("CREATIONDATE_START");

        //创建时间-结束
        Date creationdateEnd = param.getDate("CREATIONDATE_END");
        if (creationdateStart != null) {
            filter.put("CREATIONDATE", creationdateStart.getTime() + "~" + creationdateEnd.getTime());
        }
        //客审时间-开始
        Date guestTrialTimeStart = param.getDate("GUEST_TRIAL_TIME_START");

        //客审时间-结束
        Date guestTrialTimeEnd = param.getDate("GUEST_TRIAL_TIME_END");
        if (guestTrialTimeStart != null) {
            filter.put("GUEST_TRIAL_TIME", guestTrialTimeStart.getTime() + "~" + guestTrialTimeEnd.getTime());
        }
        //财审时间-开始
        Date financialTrialTimeStart = param.getDate("FINANCIAL_TRIAL_TIME_START");

        //财审时间-结束
        Date financialTrialTimeEnd = param.getDate("FINANCIAL_TRIAL_TIME_END");
        if (financialTrialTimeStart != null) {
            filter.put("FINANCIAL_TRIAL_TIME", financialTrialTimeStart.getTime() + "~" + financialTrialTimeEnd.getTime());
        }
        JSONObject childKey = new JSONObject();
        Set<String> keys = param.keySet();
        List<String> list = keys.stream()
                .filter(key -> param.get(key) instanceof String ? StringUtils.isNotEmpty(param.get(key).toString()) : !CollectionUtils.isEmpty((List) param.get(key)))
                .collect(Collectors.toList());
        for (String s : list) {
            if ("BILL_NO".equalsIgnoreCase(s)
                    || "TID".equalsIgnoreCase(s)
                    || "ORDER_NO".equalsIgnoreCase(s)
                    || "RESPONSIBLE_PERSON".equalsIgnoreCase(s)
            ) {
                String result = param.getString(s);
                String[] split = result.trim().split(",");
                if (split.length > 1) {
                    JSONArray array = new JSONArray();
                    for (String s1 : split) {
                        array.add(s1);
                    }
                    wherekeys.put(s, array);
                } else {
//                    wherekeys.put(s, String.format("*%s", result));
                    wherekeys.put(s, result);
                }
            } else if ("BILL_TYPE".equalsIgnoreCase(s)
                    || "BILL_STATUS".equalsIgnoreCase(s)
                    || "ADJUST_TYPE".equalsIgnoreCase(s)
                    || "RESERVE_BIGINT01".equalsIgnoreCase(s)
                    || "RESPONSIBLE_PARTY".equalsIgnoreCase(s)
                    || "CP_C_SHOP_ID".equalsIgnoreCase(s)
            ) {
                wherekeys.put(s, param.getJSONArray(s));
            } else if ("CP_C_PHY_WAREHOUSE_ID".equalsIgnoreCase(s)
                    || "CP_C_PHY_WAREHOUSE_ENAME".equalsIgnoreCase(s)
                    || "CP_C_LOGISTICS_ID".equalsIgnoreCase(s)
                    || "CP_C_LOGISTICS_ENAME".equalsIgnoreCase(s)
                    || "IS_RECEIVE_PAYMENT".equalsIgnoreCase(s)
            ) {
                wherekeys.put(s, param.get(s));
            } else if ("GBCODE".equalsIgnoreCase(s)) {
                String result = param.getString(s);
                String[] split = result.trim().split(",");
                JSONArray array = new JSONArray();
                for (String s1 : split) {
                    array.add(s1);
                }
                childKey.put(s, array);
            } else if ("PS_C_SKU_ID".equalsIgnoreCase(s) || "PS_C_SKU_ECODE".equalsIgnoreCase(s)) {
                childKey.put(s, param.get(s));
            }
        }

        //查询的页数 pageNum
        int start = paramYs.getInteger("pageNum") == null ? 1 : paramYs.getInteger("pageNum");

        //查询每页的行数 pageSize
        int count = paramYs.getInteger("pageSize") == null ? 15 : paramYs.getInteger("pageSize");
        JSONArray orderKeys = new JSONArray();
        JSONObject order = new JSONObject();
        order.put("asc", false);
        order.put("name", "MODIFIEDDATE");
        orderKeys.add(order);
        String[] arr = {"ID"};
        if (CollectionUtils.isEmpty(wherekeys)) {
            wherekeys.put("AD_CLIENT_ID", 37);
            wherekeys.put("AD_ORG_ID", 27);
        }
        filterSearchCondition(usrPem, wherekeys);
        JSONObject search = ElasticSearchUtil.search(AcConstant.INDEX_AC_F_PAYABLE_ADJUSTMENT, AcConstant.TYPE_AC_F_PAYABLE_ADJUSTMENT, AcConstant.TYPE_AC_F_PAYABLE_ADJUSTMENT_ITEM,
                wherekeys, filter, orderKeys, childKey, count, (start - 1) * count, arr);
        List<Long> esIds = CommonEsSearchUtil.getEsIds("ID", search);
        if (CollectionUtils.isEmpty(esIds)) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("totalSize", 0);

            PayablePageRequest payablePageRequest = new PayablePageRequest();
            payablePageRequest.setPayableAdjustmentList(new ArrayList<>());
            payablePageRequest.setPage(jsonObject);

            valueHolder.put("code", 0);
            valueHolder.put("message", "查询成功");
            valueHolder.put("data", payablePageRequest);
            return valueHolder;
        }

        //按页查询出数据
//        List<AcFPayableAdjustmentExDO> mainDO = mainMapper.selectAcFPayableAdjustmentListByWhere(billNoList, tidList,
//                billTypeList, billStatusList, adjustTypeList, orderNo, cpCPhyWarehouseId, cpCLogisticsId,
//                psCSkuId, gbcodeList, creationdateStart, creationdateEnd, guestTrialTimeStart, guestTrialTimeEnd,
//                financialTrialTimeStart, financialTrialTimeEnd, channelTypeList, pageSize, offset);

        List<AcFPayableAdjustmentDO> acFPayableAdjustmentDOList = mainMapper.selectList(new LambdaQueryWrapper<AcFPayableAdjustmentDO>().in(AcFPayableAdjustmentDO::getId, esIds));
        List<AcFPayableAdjustmentExDO> mainDO = new ArrayList<>(acFPayableAdjustmentDOList.size());
        for (AcFPayableAdjustmentDO acFPayableAdjustmentDO : acFPayableAdjustmentDOList) {
            AcFPayableAdjustmentExDO exDO = new AcFPayableAdjustmentExDO();
            BeanUtils.copyProperties(acFPayableAdjustmentDO, exDO);
            exDO.setPayTypeName(ACEnumUtil.payTypeMap.get(acFPayableAdjustmentDO.getPayType()));
            exDO.setBillTypeName(ACEnumUtil.payBillTypeMap.get(acFPayableAdjustmentDO.getBillType()));
            exDO.setBillStatusName(ACEnumUtil.payBillStatusMap.get(acFPayableAdjustmentDO.getBillStatus()));
            exDO.setAdjustTypeName(ACEnumUtil.adjustTypeMap.get(acFPayableAdjustmentDO.getAdjustType()));
            exDO.setChannelTypeName(ACEnumUtil.channelTypeMap.get(acFPayableAdjustmentDO.getReserveBigint01()));
            if (acFPayableAdjustmentDO.getResponsibleParty() != null) {
                exDO.setResponsiblePartyName(ResponsiblePartyEnum.getTextByVal(acFPayableAdjustmentDO.getResponsibleParty()));
            }
            mainDO.add(exDO);
        }

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("totalSize", search.getInteger("total"));

        PayablePageRequest payablePageRequest = new PayablePageRequest();
        payablePageRequest.setPayableAdjustmentList(mainDO);
        payablePageRequest.setPage(jsonObject);

        valueHolder.put("code", 0);
        valueHolder.put("message", "查询成功");
        valueHolder.put("data", payablePageRequest);

        return valueHolder;
    }

    /**
     * permission grant
     *
     * @param pem  UserPermission
     * @param wKey JSONObject
     */
    private void filterSearchCondition(UserPermission pem, JSONObject wKey) {
        keyLabel:
        if (pem != null) {
            Map<String, BasePermission> baseMap = pem.getBasePermission();
            if (baseMap == null) {
                break keyLabel;
            }
            BasePermission basePem = baseMap.get(AcConstant.TAB_AC_F_PAYABLE_ADJUSTMENT);
            if (basePem == null) {
                return;
            }
            List<String> cdtList = new ArrayList<>();
            cdtList.add("CP_C_SHOP_ID");
            ocOrderAuthorityMgtService.recombinationSearchCdt(cdtList, basePem, wKey);
        }
    }
}
