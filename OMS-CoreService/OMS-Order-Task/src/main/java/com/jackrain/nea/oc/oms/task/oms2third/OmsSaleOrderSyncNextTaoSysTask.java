/*
package com.jackrain.nea.oc.oms.task.oms2third;

import com.alibaba.fastjson.JSON;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.relation.TaskParam;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.sap.SapTaskTableEnum;
import com.jackrain.nea.oc.oms.services.SaleOrderSyncNextTaoService;
import com.jackrain.nea.oc.request.o2o.SaleOrderRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

*/
/**
 * @Desc : 销售订单同步
 * <AUTHOR> xiWen
 * @Date : 2020/8/22
 *//*

@Component
public class OmsSaleOrderSyncNextTaoSysTask extends AbsOmsSyncThirdSysTask<SaleOrderRequest> {


    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;

    @Autowired
    private SaleOrderSyncNextTaoService saleOrderSyncNextTaoService;

    private String statusKey = "omsSyncThirdSysTask.nextTao.order.query.status";
    private String eachSizeKey = "omsSyncThirdSysTask.nextTao.order.query.eachSize";

    @Override
    protected String getTag() {
        return omsSyncThirdSysConfig.getOms2QiMenTag();
    }

    @Override
    protected String getTopic() {
        return omsSyncThirdSysConfig.getOms2QiMenTopic();
    }

    @Override
    protected String getTaskTableName() {
        return SapTaskTableEnum.ORDER.txt();
    }

    @Override
    protected String getTaskStatus() {
        return "NEXT_TAO_STATUS";
    }

    @Override
    protected String getTaskType() {
        return "IS_NEXT_TAO";
    }

    @Override
    protected int getTaskTypeVal() {
        return 1;
    }

    @Override
    protected String getOriginCol() {
        return "SEND_NEXT_TAO_STATUS";
    }

    @Override
    protected String getOriginTableName() {
        return "OC_B_ORDER";
    }

    @Override
    protected String getThreadPoolName() {
        return "R3_OMS_NEXT_TAO_ORDER_TASK_THREAD_POOL_%d";
    }


    @Override
    protected int getTaskStatusVal() {
        Config config = ConfigService.getConfig(nameSpaceKey);
        Integer orderStatus = config.getIntProperty(statusKey, 0);
        return orderStatus;
    }

    @Override
    protected int getTaskEachSize() {
        Config config = ConfigService.getConfig(nameSpaceKey);
        Integer taskSize = config.getIntProperty(eachSizeKey, 1000);
        return taskSize;
    }

    @Override
    protected int getBatchEachSize() {
        return 200;
    }

    @Override
    protected List<SaleOrderRequest> getSyncData(TaskParam taskParam) {

        String ids = joinList2String(taskParam.getKeys());
        taskParam.setKeyStrings(ids);
        List<OcBOrder> ocBorders = ocBOrderMapper.selectList4SyncThirdSys(taskParam);
        if (unExpect.test(ocBorders)) {
            return null;
        }

        List<OcBOrderItem> items = ocBOrderItemMapper.selectItems4SyncThirdSys(taskParam);
        if (unExpect.test(items)) {
            return null;
        }

        orderIdFun = o -> ((OcBOrder) o).getId();
        statisticsCsm(ocBorders, taskParam.getValidKeys());

        return saleOrderSyncNextTaoService.saleOrderTransfer(ocBorders, items);
    }

    @Override
    protected String convert2String(List list) {
        OmsSyncNextTaoRequest omsSyncNextTaoRequest = new OmsSyncNextTaoRequest();
        omsSyncNextTaoRequest.setMethod("sm.salesorder.add");
        omsSyncNextTaoRequest.setCenterName("");
        omsSyncNextTaoRequest.setData(list);

        return JSON.toJSONString(omsSyncNextTaoRequest);
    }
}
*/
