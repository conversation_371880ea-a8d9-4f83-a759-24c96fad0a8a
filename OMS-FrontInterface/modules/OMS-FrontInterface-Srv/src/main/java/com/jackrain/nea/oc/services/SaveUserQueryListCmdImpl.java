package com.jackrain.nea.oc.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.SaveUserQueryListCmd;
import com.jackrain.nea.oc.oms.model.relation.UserConfig;
import com.jackrain.nea.oc.oms.services.SaveUserQueryListService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class SaveUserQueryListCmdImpl extends CommandAdapter implements SaveUserQueryListCmd {

    @Autowired
    private SaveUserQueryListService saveUserQueryListService;

    @Override
    public ValueHolder saveTableQuery(String tableName, List<UserConfig> userConfigList, User userWeb) throws NDSException {
        return saveUserQueryListService.saveTableQuery(tableName, userConfigList, userWeb);
    }

}
