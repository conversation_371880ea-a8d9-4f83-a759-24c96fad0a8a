package com.jackrain.nea.oc.oms.model.result;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @program: ryytn-oc-oms-v3.0
 * @description:
 * @author: haiyang
 * @create: 2024-01-15 14:43
 **/
@Data
public class PlatformGiftOrderResult implements Serializable {
    private static final long serialVersionUID = 1096694134695389963L;

    /**
     * 平台单号
     */
    private String platformOrderNo;

    /**
     * 商品总额
     */
    private BigDecimal productAmt;

    /**
     * 省
     */
    private String cpCRegionProvinceEname;

    /**
     * 市
     */
    private String cpCRegionCityEname;

    /**
     * 区
     */
    private String cpCRegionAreaEname;

    /**
     * 店铺名称
     */
    private String cpCShopTitle;


    /**
     * 赠品明细
     */
    private List<GiftOrderItemResult> giftOrderItems;

    @Data
    public static class GiftOrderItemResult implements Serializable{

        private static final long serialVersionUID = -205114506199157390L;

        /**
         * sku编码
         */
        private String skuCode;

        /**
         * 数量
         */
        private Integer quantity;
    }

}
