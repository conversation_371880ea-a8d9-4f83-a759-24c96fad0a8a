package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBOrderSplitCmd;
import com.jackrain.nea.oc.oms.model.relation.UserPermission;
import com.jackrain.nea.oc.oms.model.result.OrderHoldResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.async.AsyncTaskBody;
import com.jackrain.nea.web.common.AsyncTaskManager;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.UUID;

/**
 * @author: heliu
 * @since: 2019/4/10
 * create at : 2019/4/10 11:30
 */
@Api(value = "OcBorderSplitOrderCtrl", tags = "手动拆分订单接口")
@Slf4j
@RestController
public class OcBorderSplitOrderCtrl {

    @Autowired
    private OcBOrderSplitCmd ocBOrderSplitCmd;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @Autowired
    private AsyncTaskManager asyncTaskManager;

    @Autowired
    private ThreadPoolTaskExecutor commonTaskExecutor;

    /**
     * 初始化查询接口
     *
     * @param request 请求对象
     * @param obj     请求参数
     * @return json对象
     */
    @ApiOperation(value = "手工拆单查询接口")
    @PostMapping(value = "/api/cs/oc/oms/v1/querySkuListAndStorageInfo")
    public JSONObject querySkuListAndStorageInfo(HttpServletRequest request,
                                                 @RequestBody JSONObject obj) {
        ValueHolderV14 vh;
        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        try {
            vh = new ValueHolderV14();
            UserPermission usrPem = UserPermissionCtrlHelper.currentUserPermission(user, "OC_B_ORDER");
            if (usrPem == null) {
                vh = new ValueHolderV14<>();
                vh.setMessage("未获取到用户权限");
                vh.setCode(ResultCode.FAIL);
                return vh.toJSONObject();
            }
            vh = ocBOrderSplitCmd.querySkuListAndStorageInfo(obj, user);
            vh.setCode(vh.getCode());
            vh.setMessage(Resources.getMessage(vh.getMessage()));
            //记录日志信息。Finish 标记结束
            return vh.toJSONObject();
        } catch (NDSException e) {
            vh = new ValueHolderV14();
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return vh.toJSONObject();
        }
    }


    /**
     * 手动拆单信息保存接口
     * /api/cs/oc/oms/v1/saveSplitOrderInfo
     *
     * @param request 请求对象
     * @param param   请求参数
     * @return json对象
     */
    @ApiOperation(value = "手动拆单信息保存接口")
    @RequestMapping(value = "/api/cs/oc/oms/v1/saveSplitOrderInfo", method = RequestMethod.POST)
    public JSONObject saveSplitOrderInfo(HttpServletRequest request, @RequestBody String param) {
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        ValueHolderV14 holderV14 = new ValueHolderV14();
        try {
            UserPermission usrPem = UserPermissionCtrlHelper.currentUserPermission(user, "OC_B_ORDER");
            if (usrPem == null) {
                holderV14 = new ValueHolderV14<>();
                holderV14.setMessage("未获取到用户权限");
                holderV14.setCode(ResultCode.FAIL);
                return holderV14.toJSONObject();
            }
            holderV14 = ocBOrderSplitCmd.saveSplitOrderInfo(param, user);
            holderV14.setCode(holderV14.getCode());
            holderV14.setMessage(Resources.getMessage(holderV14.getMessage()));
            return holderV14.toJSONObject();
        } catch (Exception ex) {
            holderV14.setCode(-1);
            holderV14.setMessage("订单手动拆单信息保存异常：" + ex.getMessage());
            return holderV14.toJSONObject();
        }
    }

    /**
     * 手动拆单信息保存接口
     * /api/cs/oc/oms/v1/saveSplitOrderInfo
     *
     * @param request 请求对象
     * @param param   请求参数
     * @return json对象
     */
    @ApiOperation(value = "手动拆单信息保存接口")
    @RequestMapping(value = "/api/cs/oc/oms/v1/confirmSplitOrder", method = RequestMethod.POST)
    public JSONObject confirmSplitOrder(HttpServletRequest request, @RequestBody JSONObject param) {
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        ValueHolderV14 holderV14 = new ValueHolderV14();
        try {
            UserPermission usrPem = UserPermissionCtrlHelper.currentUserPermission(user, "OC_B_ORDER");
            if (usrPem == null) {
                holderV14 = new ValueHolderV14<>();
                holderV14.setMessage("未获取到用户权限");
                holderV14.setCode(ResultCode.FAIL);
                return holderV14.toJSONObject();
            }
            holderV14 = ocBOrderSplitCmd.confirmSplitOrder(param, user);
            holderV14.setCode(holderV14.getCode());
            holderV14.setMessage(Resources.getMessage(holderV14.getMessage()));
            return holderV14.toJSONObject();
        } catch (Exception ex) {
            holderV14.setCode(-1);
            holderV14.setMessage("订单手动拆单信息保存异常：" + ex.getMessage());
            return holderV14.toJSONObject();
        }
    }


    @ApiOperation(value = "手动拆单信息保存接口")
    @RequestMapping(value = "/api/cs/oc/oms/v1/confirmSplitOrderByRow", method = RequestMethod.POST)
    public JSONObject confirmSplitOrderByRow(HttpServletRequest request, @RequestBody JSONObject param) {
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        ValueHolderV14 holderV14 = new ValueHolderV14();
        try {
            UserPermission usrPem = UserPermissionCtrlHelper.currentUserPermission(user, "OC_B_ORDER");
            if (usrPem == null) {
                holderV14 = new ValueHolderV14<>();
                holderV14.setMessage("未获取到用户权限");
                holderV14.setCode(ResultCode.FAIL);
                return holderV14.toJSONObject();
            }
            JSONArray ids = param.getJSONArray("ids");
            if (ids == null) {
                holderV14.setCode(-1);
                holderV14.setMessage("参数有误");
                return holderV14.toJSONObject();
            }
            String splitReason = param.getString("splitReason");
            List<Long> idList = JSON.parseArray(ids.toJSONString(), Long.class);
            holderV14 = ocBOrderSplitCmd.confirmSplitOrderByRow(idList, user,splitReason);
            holderV14.setCode(holderV14.getCode());
            holderV14.setMessage(Resources.getMessage(holderV14.getMessage()));
            return holderV14.toJSONObject();
        } catch (Exception ex) {
            holderV14.setCode(-1);
            holderV14.setMessage("订单手动按行拆单信息保存异常：" + ex.getMessage());
            return holderV14.toJSONObject();
        }
    }


    @ApiOperation(value = "手动拆单信息保存接口")
    @RequestMapping(value = "/api/cs/oc/oms/v1/confirmSplitOrderByOne", method = RequestMethod.POST)
    public JSONObject confirmSplitOrderByOne(HttpServletRequest request, @RequestBody JSONObject param) {
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        ValueHolderV14 holderV14 = new ValueHolderV14();
        try {
            UserPermission usrPem = UserPermissionCtrlHelper.currentUserPermission(user, "OC_B_ORDER");
            if (usrPem == null) {
                holderV14 = new ValueHolderV14<>();
                holderV14.setMessage("未获取到用户权限");
                holderV14.setCode(ResultCode.FAIL);
                return holderV14.toJSONObject();
            }
            JSONArray ids = param.getJSONArray("ids");
            if (ids == null) {
                holderV14.setCode(-1);
                holderV14.setMessage("参数有误");
                return holderV14.toJSONObject();
            }
            String splitReason = param.getString("splitReason");
            List<Long> idList = JSON.parseArray(ids.toJSONString(), Long.class);
            holderV14 = ocBOrderSplitCmd.confirmSplitOrderByOne(idList, user, splitReason);
            holderV14.setCode(holderV14.getCode());
            holderV14.setMessage(Resources.getMessage(holderV14.getMessage()));
            return holderV14.toJSONObject();
        } catch (Exception ex) {
            holderV14.setCode(-1);
            holderV14.setMessage("订单手动按行拆单信息保存异常：" + ex.getMessage());
            return holderV14.toJSONObject();
        }
    }

    @ApiOperation(value = "根据订单明细id列表与数量 计算不同的明细id对应的数量")
    @RequestMapping(value = "/api/cs/oc/oms/v1/getNumByItemIdAndQty", method = RequestMethod.POST)
    public ValueHolderV14 getNumByItemIdAndQty(HttpServletRequest request, @RequestBody JSONObject param) {
        return ocBOrderSplitCmd.getNumByItemIdAndQty(param, r3PrimWebAuthService.getLoginPrimWebUser(request));
    }

    @ApiOperation(value = "手工箱型拆单")
    @RequestMapping(value = "/api/cs/oc/oms/v1/splitOrderByBoxStrategy", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolderV14 splitOrderByBoxStrategy(HttpServletRequest request,
                                                  @RequestBody JSONObject obj) {
        //todo
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        //新增我的任务
        AsyncTaskBody asyncTaskBody = new AsyncTaskBody();
        asyncTaskBody.setTaskId(UUID.randomUUID().toString());
        asyncTaskBody.setMenu("零售发货单");
        asyncTaskBody.setTaskType("箱型拆单");

        //任务开始
        asyncTaskManager.beforeExecute(user, asyncTaskBody);

        commonTaskExecutor.submit(() -> {

            //记录日志信息
            ValueHolderV14<OrderHoldResult> valueHolder = new ValueHolderV14<>();

            try {
                ValueHolderV14 v14 = ocBOrderSplitCmd.splitOrderByBoxStrategy(obj, user);
                //记录日志信息
                asyncTaskManager.afterExecute(user, asyncTaskBody, v14.toJSONObject());

            } catch (NDSException e) {
                valueHolder.setCode(ResultCode.FAIL);
                valueHolder.setMessage(Resources.getMessage("异常信息：" + Throwables.getStackTraceAsString(e)));

                //记录日志信息
                asyncTaskManager.afterExecute(user, asyncTaskBody, valueHolder.toJSONObject());
            }

        });
        return new ValueHolderV14(asyncTaskBody.getId(), ResultCode.SUCCESS,
                Resources.getMessage("零售发货单-箱型拆单任务开始！详情请在我的任务查看"));
    }

}