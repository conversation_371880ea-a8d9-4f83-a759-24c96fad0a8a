package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrderBnTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @ClassName OcBOrderBnTaskMapper
 * @Description 班牛
 * <AUTHOR>
 * @Date 2024/12/25 14:31
 * @Version 1.0
 */
@Mapper
@Component
public interface OcBOrderBnTaskMapper extends ExtentionMapper<OcBOrderBnTask> {

    @Select("SELECT * FROM OC_B_ORDER_BN_TASK WHERE OC_B_ORDER_ID = #{orderId} ")
    List<OcBOrderBnTask> selectByOrderId(Long orderId);
}
