package com.jackrain.nea.oc.oms.model.request;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.model.perm.PermissionAlias;
import com.jackrain.nea.oc.oms.model.perm.PermissionModel;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * Query Condition
 *
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2020/5/2
 */
@Data
@Accessors(chain = true)
public class OrderQueryRequest {


    /**
     * search es by keys
     */
    private JSONObject where;

    /**
     * search es filter keys
     */
    private JSONObject filter;

    /**
     * search es by child keys
     */
    private JSONObject child;

    /**
     * search es by sort
     */
    private JSONArray order;

    /**
     * return fields
     */
    private String[] field;

    /**
     * current page index
     */
    private long index;

    /**
     * page size
     */
    private int size;

    /**
     * total count
     */
    private long totalCount;

    /**
     * total page
     */
    private long totalPage;

    /**
     * search start index
     */
    private int start;

    /**
     * permission identifier
     */
    private PermissionModel permissionModel;

    /**
     * permission alia
     */
    private PermissionAlias permissionAlias;

    /**
     * all condition
     */
    private JSONArray whereKeys;

    private JSONObject existKeys;

    private JSONObject notExistKeys;

}
