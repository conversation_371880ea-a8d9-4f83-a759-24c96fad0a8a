package com.jackrain.nea.oc.oms.mq.processor.impl.transfer.normal;

import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.OperateType;
import com.jackrain.nea.oc.oms.model.enums.OrderType;
import com.jackrain.nea.oc.oms.model.relation.IpStandplatOrderRelation;
import com.jackrain.nea.oc.oms.mq.processor.IMqOrderDetailProcessor;
import com.jackrain.nea.oc.oms.process.transfer.impl.normal.standplat.StandPlatformTransferOrderProcessImpl;
import com.jackrain.nea.oc.oms.services.IpStandplatOrderService;
import com.jackrain.nea.oc.oms.step.ProcessStepResultList;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 通用转单消息处理器
 * <p>
 * 2020-11-11易邵峰检查
 *
 * @author: ming.fz
 * @since: 2019-07-22
 * create at : 2019-07-22
 */
@Slf4j
@Component
public class StandPlatformTransferMqOrderDetailProcessor implements IMqOrderDetailProcessor {

    @Autowired
    private IpStandplatOrderService ipStandplatOrderService;

    @Autowired
    private StandPlatformTransferOrderProcessImpl standPlatformTransferOrderProcess;

    @Override
    public ProcessStepResultList start(OperateOrderMqInfo orderMqInfo) {
        String orderNo = orderMqInfo.getOrderNo();

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("StandPlatformTransferMqOrderDetailProcessor.Start", orderNo));
        }

        IpStandplatOrderRelation standplatOrderRelation = this.ipStandplatOrderService.selectStandplatOrder(orderNo);
        if (standplatOrderRelation == null || standplatOrderRelation.getStandplatOrder() == null) {
            log.error(LogUtil.format("StandPlatformTransferMqOrderDetailProcessor.Error.Not.Exist", orderNo));
            return new ProcessStepResultList();
        } else {
            ProcessStepResultList resultList = standPlatformTransferOrderProcess.start(standplatOrderRelation,
                    false, SystemUserResource.getRootUser());
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("StandPlatformTransferMqOrderDetailProcessor.Finished,Result={}", orderNo), resultList);
            }
            return resultList;
        }
    }

    @Override
    public boolean checkMqIsCanExecute(OperateOrderMqInfo orderMqInfo) {
        return orderMqInfo.getOperateType() == OperateType.TRANSFER_ORDER
                && orderMqInfo.getChannelType() == ChannelType.STANDPLAT
                && orderMqInfo.getOrderType() == OrderType.NORMAL;
    }
}
