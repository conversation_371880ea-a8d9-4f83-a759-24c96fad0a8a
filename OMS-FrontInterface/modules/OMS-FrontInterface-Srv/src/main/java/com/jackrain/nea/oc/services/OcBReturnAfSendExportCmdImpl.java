package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.api.OcBReturnAfSendExportCmd;
import com.jackrain.nea.oc.oms.util.ExportUtil;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @ClassName : OcBReturnAfSendExportCmdImpl  
 * @Description : 
 * <AUTHOR>  YCH
 * @Date: 2021-08-30 19:36  
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBReturnAfSendExportCmdImpl implements OcBReturnAfSendExportCmd {
    @Autowired
    private ExportUtil exportUtil;

    @Value("${r3.oss.endpoint}")
    private String endpoint;

    @Value("${r3.oss.accessKey}")
    private String accessKeyId;

    @Value("${r3.oss.secretKey}")
    private String accessKeySecret;

    @Value("${r3.oss.bucketName}")
    private String bucketName;

    @Value("${r3.oss.timeout}")
    private String timeout;
    @Override
    public ValueHolderV14 downloadTemp() {
        ValueHolderV14 holderV14 = new ValueHolderV14(ResultCode.SUCCESS, "订单管理导入模板下载成功！");
        exportUtil.setEndpoint(this.endpoint);
        exportUtil.setAccessKeyId(this.accessKeyId);
        exportUtil.setAccessKeySecret(this.accessKeySecret);
        exportUtil.setBucketName(this.bucketName);
        if (StringUtils.isEmpty(timeout)) {
            //如果获取不到apllo配置参数，设置默认过期时间为30分钟
            timeout = "1800000";
        }
        exportUtil.setTimeout(timeout);
        /**
         *  拼接Excel主表sheet表头字段
         * */
        String orderNames[] = {"单据日期", "原始订单编号", "退款原因", "支付方式",
                "支付账号", "退款分类", "退款描述", "收款人姓名", "备注", "卖家备注",
                "SKU编码", "数量", "退款金额", "运费"};
        User user = SystemUserResource.getRootUser();
        List orderN = Lists.newArrayList(orderNames);
        //生成Excel
        XSSFWorkbook hssfWorkbook = new XSSFWorkbook();
        String sdd = "";
        hssfWorkbook = exportUtil.executeAfSendSheet("额外退款单主子表",  orderN);
        sdd = exportUtil.saveFileAndPutOss(hssfWorkbook, "额外退款单导入模板", user, "OSS-Bucket/EXPORT/OC_B_RETURN_AF_SEND_MANUAL/");
        holderV14.setData(sdd);
        return holderV14;
    }
}
