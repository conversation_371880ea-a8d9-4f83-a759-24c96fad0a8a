package com.jackrain.nea.oc.oms.process.transfer.impl.normal.jitx.step;

import cn.hutool.core.util.ObjectUtil;
import com.google.common.base.Throwables;
import com.jackrain.nea.cpext.model.LogisticsInfo;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.relation.IpJitxOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBJitxOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：2021/1/26 4:00 下午
 * description ：
 * @ Modified By：
 */
@Step(order = 65, description = "更新订单信息")
@Component
@Slf4j
public class Step65JitxOrderUpdate extends BaseJitxOrderProcessStep
        implements IOmsOrderProcessStep<IpJitxOrderRelation> {

    @Override
    public ProcessStepResult<IpJitxOrderRelation> startProcess(IpJitxOrderRelation orderInfo,
                                                               ProcessStepResult preStepResult,
                                                               boolean isAutoMakeup,
                                                               User operateUser) {
        ProcessStepResult<IpJitxOrderRelation> stepResult = new ProcessStepResult<>();

        if (orderInfo == null || orderInfo.getJitxOrder() == null) {
            return new ProcessStepResult<>(StepStatus.FAILED, "orderInfo is null or orderInfo.JitxOrder is null");
        }
        List<OcBOrder> transferOrderList = (List<OcBOrder>) preStepResult.getNextStepOperateObj();
        if (transferOrderList == null) {
            return new ProcessStepResult<>(StepStatus.FAILED, "Step65JitxOrderUpdate.preStepResult.getNextStepOperateObj is null");
        }
        try {
            IpBJitxOrder jitxOrder = orderInfo.getJitxOrder();
            OcBOrder order = transferOrderList.get(0);
            //只有平台单号一致才更新，防止覆盖JITX合包后的信息
            if(order.getTid().equals(jitxOrder.getOrderSn())) {
                order.setExpresscode(jitxOrder.getTransportNo());
                order.setIsForbiddenDelivery(jitxOrder.getIsForbiddenDelivery());
                order.setIsInterecept(jitxOrder.getIsForbiddenDelivery());
                order.setMergedCode(jitxOrder.getMergedCode());
                order.setMergedSn(jitxOrder.getMergedSn());
                order.setJitxRequiresMerge(YesNoEnum.N.getVal() + "");
                if (StringUtils.isNotEmpty(jitxOrder.getMergedCode())) {
                    order.setJitxRequiresMerge(YesNoEnum.Y.getVal() + "");
                }

                ipJitxOrderService.updateJitxOrder(jitxOrder, order);
            }
            List<OcBOrder> orderInfoList = new ArrayList<>();
            orderInfoList.add(order);
            stepResult.setNextStepOperateObj(orderInfoList);
            stepResult.setNextStepClass(Step70JitxOrderCancleWms.class);
            stepResult.setStatus(StepStatus.SUCCESS);
            return stepResult;
        } catch (Exception e) {
            log.error(LogUtil.format("Step70SaveOmsOrder:{}", "Step70SaveOmsOrder"), Throwables.getStackTraceAsString(e));
            return new ProcessStepResult<>(StepStatus.FAILED, "Step65JitxOrderUpdate.updateJitxOrder.error，" + e.getMessage());
        }
    }
}
