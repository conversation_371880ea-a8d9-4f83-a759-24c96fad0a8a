package com.jackrain.nea.oc.oms.model.enums;

/**
 * 唯品会时效订单状态
 *
 * @author: huang.zai<PERSON>
 * create at: 2019/8/19
 */
public enum TimeOrderVipStatusEnum {

    /**
     * 待占单（已创建）
     */
    CREATED("待占单", 1),

    /**
     * 占单成功
     */
    OCCUPIED("占单成功", 2),

    /**
     * 缺货
     */
    OUT_STOCK("缺货", 3),

    /**
     * 已完成(已匹配成功)
     */
    MATCHED("已完成", 4),

    /**
     * 已取消
     */
    CANCELLED("已取消", 5),

    /**
     * 占单中
     */
    IN_SINGLE("占单中", 6),

    /**
     * 寻仓中
     */
    IN_SEEKING_STORE("寻仓中", 7),

    /**
     * 寻仓成功
     */
    SEEKING_STORE_SUCCESS("寻仓成功", 8);

    String key;
    Integer value;

    TimeOrderVipStatusEnum(String key, Integer value) {
        this.value = value;
        this.key = key;
    }

    public Integer getValue() {
        return value;
    }

    public String getKey() {
        return key;
    }

    public static Integer getValue(String key) {
        if (key == null) {
            return null;
        }
        for (TimeOrderVipStatusEnum orderVipStatusEnum : TimeOrderVipStatusEnum.values()) {
            if (orderVipStatusEnum.getKey().equals(key)) {
                return orderVipStatusEnum.getValue();
            }
        }
        return null;
    }

}