package com.jackrain.nea.oc.oms.mq.processor.aop.parser.impl;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.impl.util.MsgConvertUtil;
import com.jackrain.nea.oc.oms.model.table.OcBMessageConsumeLog;
import com.jackrain.nea.oc.oms.mq.processor.aop.parser.MetaDataOrderTypeEnum;
import com.jackrain.nea.oc.oms.mq.processor.aop.parser.QiMenMethodEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * Description： 退单回传
 * Author: RESET
 * Date: Created in 2020/8/26 1:53
 * Modified By:
 */
@Component
@Slf4j
public class MethodItemlackReportParser extends AbstractMessageParser {

    /**
     * 解析器
     *
     * @param message
     * @return
     */
    @Override
    public OcBMessageConsumeLog parse(Message message) throws Exception {
        if (Objects.nonNull(message)) {
            String messageBody = MsgConvertUtil.objectDeserialize(message.getBody()).toString();

            OcBMessageConsumeLog consumeLog = buildLogByMqInfo(message);
            consumeLog.setBody(messageBody);
            // 解析单据类型编码
            parseBody(messageBody, consumeLog);

            return consumeLog;
        }

        return null;
    }

    /**
     * 解析器
     *
     * @param messageBody
     * @return
     */
    private void parseBody(String messageBody, OcBMessageConsumeLog consumeLog) {
        JSONObject object = JSONObject.parseObject(messageBody);
        String method = object.getString("method");

        JSONObject request = object.getJSONObject("request");
        //出库通知单编码
        String deliveryOrderCode = request.getString("deliveryOrderCode");

        consumeLog.setBillNo(deliveryOrderCode);
        consumeLog.setBillType(MetaDataOrderTypeEnum.OUT_NOTICE.getValue());
        consumeLog.setMethod(method);
    }

    /**
     * 标准单据类型
     *
     * @return
     */
    @Override
    public String getMethod() {
        return QiMenMethodEnum.RETURNORDER_CONFIRM.getCode();
    }

}
