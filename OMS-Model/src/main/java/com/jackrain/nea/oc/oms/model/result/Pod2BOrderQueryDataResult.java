package com.jackrain.nea.oc.oms.model.result;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName Pod2BOrderQueryDataResult
 * @Description 结果
 * <AUTHOR>
 * @Date 2024/8/29 16:47
 * @Version 1.0
 */
@Data
public class Pod2BOrderQueryDataResult implements Serializable {
    private static final long serialVersionUID = -4792182322916424651L;

    /**
     * 平台单号
     */
    private String tid;

    /**
     * 单据编号
     */
    private String billNo;

    /**
     * 出库通知单号
     */
    private String onNo;

    /**
     * 订单状态
     */
    private String status;

    /**
     * 发货仓库
     */

    private String warehouseCode;

    /**
     * 物流公司
     */
    private String logisticsCode;

    /**
     * 物流公司
     */
    private String logisticsName;

    /**
     * 商品编码
     */
    private String skuCode;

    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 数量
     */
    private Integer qty;

    /**
     * 重量
     */
    private BigDecimal weight;

    /**
     * 总重量
     */
    private BigDecimal totalWeight;

    /**
     * 体积
     */
    private BigDecimal volume;

    /**
     * 总体积
     */
    private BigDecimal totalVolume;

    /**
     * 买家姓名
     */
    private String buyerName;

    /**
     * 买家电话
     */
    private String buyerPhone;

    /**
     * 省
     */
    private String buyerProvince;

    /**
     * 市
     */
    private String buyerCity;

    /**
     * 区
     */
    private String buyerArea;

    /**
     * 地址
     */
    private String buyerAddress;

    /**
     * 买家备注
     */
    private String buyerRemark;

    /**
     * 卖家备注
     */
    private String sellerRemark;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 销售部门
     */
    private String saleDepartment;

    /**
     * 销售部门
     */
    private String saleDepartmentName;

    /**
     * 审核人
     */
    private String auditor;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 配货时间
     */
    private Date scanTime;

    /**
     * 拼车单号
     */
    private String carpoolNo;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 总重量-订单
     */
    private BigDecimal orderTotalWeight;

    /**
     * 总体积-订单
     */
    private BigDecimal orderTotalVolume;

    /**
     * 状态名称-导出用
     */
    private String statusName;
}
