package com.jackrain.nea.oc.oms.nums;

import lombok.Getter;

import java.util.Objects;

/**
 * Description： 单据来源类型
 * Author: RESET
 * Date: Created in 2020/6/15 20:24
 * Modified By:
 */
public enum RefundOrderSourceTypeEnum {

    // 匹配策略类型
    MANUAL(1, "manual", "手动"),
    AUTO(2, "auto", "自动");

    @Getter
    private Integer value;
    @Getter
    private String code;
    @Getter
    private String description;

    RefundOrderSourceTypeEnum(Integer value, String code, String description) {
        this.value = value;
        this.code = code;
        this.description = description;
    }

    @Override
    public String toString() {
        return String.valueOf(value);
    }

    public static RefundOrderSourceTypeEnum fromValue(Integer v) {
        for (RefundOrderSourceTypeEnum c : RefundOrderSourceTypeEnum.values()) {
            if (Objects.equals(v, c.value)) {
                return c;
            }
        }
        throw new IllegalArgumentException(String.valueOf(v));
    }

}
