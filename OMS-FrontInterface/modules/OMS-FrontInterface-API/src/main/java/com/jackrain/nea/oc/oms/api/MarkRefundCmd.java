package com.jackrain.nea.oc.oms.api;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;

import java.util.List;

/**
 * @author: 周琳胜
 * @since: 2019/3/11
 * create at : 2019/3/11 11:15
 */

/**
 * 标记退款完成
 */
public interface MarkRefundCmd {

    ValueHolderV14 execute(JSONObject obj, User user) throws NDSException;

    ValueHolder markRefundCancel(Long orderId, List<Long> itemIds, User user);

    /**
     * 奶卡订单取消
     *
     * @param tid
     * @return
     */
    @Deprecated
    ValueHolderV14 naiKaOrderCancel(String tid);

    /**
     * 奶卡订单部分取消
     *
     * @param tid
     * @param oid 子单号 集合
     * @return
     */
    ValueHolderV14 naiKaPartOrderCancel(String tid, List<String> oid);
}
