package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.oc.oms.api.OmsTaskOrderCmd;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.ExceptionUtil;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2020/4/18
 */
@Slf4j
@RestController
@Api(value = "OmsTaskOrderCtrl", tags = "task order control")
public class OmsTaskOrderCtrl {


    @Autowired
    private OmsTaskOrderCmd omsTaskOrderCmd;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @RequestMapping(value = "/api/cs/oc/oms/v1/oms/v1/taskOrderControl", method = {RequestMethod.POST, RequestMethod.GET})
    public JSONObject taskOrderControl(HttpServletRequest request, @RequestBody JSONObject jsn) {


        if (jsn == null) {
            return valueResult(false, "Param Was Null");
        }

        String operator = jsn.getString("operator");
        if (operator == null) {
            return valueResult(false, "Param Operator Was Null");
        }

        String shardKeys = jsn.getString("shardKeys");
        if (shardKeys == null) {
            return valueResult(false, "Param ShardKeys Was Null");
        }

        User loginPrimWebUser = r3PrimWebAuthService.getLoginPrimWebUser(request);
        if (loginPrimWebUser == null) {
            return valueResult(false, "User Was Mission");
        }

        try {
            ValueHolderV14 vh = null;
            switch (operator) {
                case "modifyStatus":
                    List<Long> longs = JSON.parseArray(shardKeys, Long.class);
                    vh = omsTaskOrderCmd.modifyTaskOrderStatus(longs, jsn.getString("tableName"), jsn.getInteger("status"));
                    break;
                default:
                    return valueResult(false, "UnKnow Operator Sign");
            }
            if (vh != null) {
                return vh.toJSONObject();
            }
            return valueResult(false, "Null Response");
        } catch (Exception e) {
            return valueResult(false, "Exception: " + ExceptionUtil.getMessage(e));
        }
    }


    private JSONObject valueResult(boolean flag, String msg) {
        ValueHolderV14 vh = new ValueHolderV14();
        if (flag) {
            vh.setCode(0);
            vh.setMessage(msg);
        } else {
            vh.setCode(-1);
            vh.setMessage(msg);
        }
        return vh.toJSONObject();
    }


}
