package com.jackrain.nea.oc.oms.services;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.result.ReginQueryResult;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ip.model.ModifyOrderAddrModel;
import com.jackrain.nea.oc.oms.mapper.IpBTaobaoOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderHoldItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.task.OcBAuditTaskMapper;
import com.jackrain.nea.oc.oms.model.enums.LogTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OcOrderCheckBoxEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OmsWmsCancelStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderHoldReasonEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.order.address.ReceiverAddressDto;
import com.jackrain.nea.oc.oms.model.order.address.WarehouseAndLogisticsDto;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderHoldConst;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderHoldItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.task.OcBAuditTask;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.MD5Util;
import com.jackrain.nea.util.OrderAddressConvertUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * @author: 孙俊磊
 * @since: 2019-03-13
 * create at:  2019-03-13 16:11
 * 修改收货地址
 */
@Slf4j
@Component
public class OcBorderUpdateService {

    @Autowired
    OmsOrderService omsOrderService;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private OmsOrderCancellationService omsOrderCancellationService;
    @Autowired
    private OmsOrderSplitService omsOrderSplitService;

    @Autowired
    private SgRpcService sgRpcService;
    @Autowired
    private IpRpcService ipRpcService;
    @Autowired
    private IpBTaobaoOrderMapper ipBTaobaoOrderMapper;
    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private BllRedisLockOrderUtil redisUtil;
    @Autowired
    private OmsOrderDistributeWarehouseService omsWarehousRuleService;
    @Autowired
    private UpdateOrderInfoService updateOrderInfoService;

    @Autowired
    private OcBOrderTheAuditService ocBOrderTheAuditService;

    @Autowired
    private OcBAuditTaskMapper auditTaskMapper;

    @Autowired
    private OcBOrderHoldService ocBOrderHoldService;
    @Autowired
    private OcBOrderHoldItemMapper ocBOrderHoldItemMapper;
    @Autowired
    private SplitOutStockOrderService splitOutStockOrderService;

    @Autowired
    private OcBOrderMapper orderMapper;
    @Autowired
    private SaveBillService saveBillService;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private OmsOccupyTaskService omsOccupyTaskService;

    @Autowired
    private OmsBusinessTypeStService omsBusinessTypeStService;

    /**
     * 最终发货地址以我们平台为主，淘宝平台同步地址失败很正常
     * 但是还是要加上事务，否则 新单占用库存失败，还是订单作废失败。
     * 都会导致复制新单不可逆的生成。那样失败一次生成一个新单。
     *
     * @param jsonObject 入参
     * @param loginUser  loginUser
     * @return ValueHolderV14
     */

    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 updateReceiveAddress(JSONObject jsonObject, User loginUser, Boolean isRecoil) {
        ValueHolderV14 holderV14 = new ValueHolderV14();
        if (jsonObject != null) {
            Long id = jsonObject.getLong("id");
            JSONObject updateInfo = jsonObject.getJSONObject("updateInfo");
            //redis锁单
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("OcBorderUpdateService_updateReceiveAddress 入参id=", id));
            }
            String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(id);
            RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
            try {
                if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                    if (log.isDebugEnabled()) {
                        log.debug(LogUtil.format("OcBorderUpdateService_updateReceiveAddress 锁单id=", id));
                    }
                    OcBOrder ocBorderDto = ocBOrderMapper.selectById(id);
                    //无明细直接判断 订单状态为待审核、缺货状态，则执行占用库存服务
                    Integer orderStatus = ocBorderDto.getOrderStatus();
                    Integer wmsCancelStatus = ocBorderDto.getWmsCancelStatus();
                    String receiverAddressBefore = ocBorderDto.getCpCRegionProvinceEname() + ocBorderDto.getCpCRegionCityEname() + ocBorderDto.getCpCRegionAreaEname() + ocBorderDto.getReceiverAddress();
                    //修改收货地址
                    if (updateInfo != null) {
                        if (!isRecoil && ocBorderDto.getIsMerge() == 1) {
                            //已合并订单不修改
                            holderV14.setMessage("已合并订单不修改！");
                            holderV14.setCode(-1);
                            if (log.isDebugEnabled()) {
                                log.debug(LogUtil.format("合并订单不允许修改地址，orderId=", ocBorderDto.getId()));
                            }
                            return holderV14;
                        }
                        if (orderStatus == OcOrderCheckBoxEnum.CHECKBOX_AUDITED.getVal()) {
                            //反审核订单成功后修改地址
                            if (toExamineOrder(ocBorderDto, loginUser, isRecoil ? LogTypeEnum.UPDATE_ADDRESS_REVERSE_AUDIT.getType() : LogTypeEnum.AUTOMATIC_UPDATE_ADDRESS_REVERSE_AUDIT.getType())) {
                                return updateAddress(ocBorderDto, receiverAddressBefore, updateInfo, loginUser);
                            }
                            holderV14.setMessage("订单反审核失败，不允许修改地址信息！");
                            if (log.isDebugEnabled()) {
                                log.debug(LogUtil.format("订单反审核失败，不允许修改地址信息,orderId=", ocBorderDto.getId()));
                            }
                            holderV14.setCode(-1);
                            return holderV14;
                        } else if (orderStatus == OcOrderCheckBoxEnum.CHECKBOX_UNCONFIRMED.getVal() || orderStatus == OcOrderCheckBoxEnum.CHECKBOX_OUT_OF_STOCK.getVal()) {
                            //若订单状态为“待审核”、“缺货”
                            return updateAddress(ocBorderDto, receiverAddressBefore, updateInfo, loginUser);
                        } else if (orderStatus == OcOrderCheckBoxEnum.CHECKBOX_IN_DISTRIBUTION.getVal()) {
                            //若订单状态为配货中且WMS撤回状态为“已撤回”
                            if (wmsCancelStatus == null) {
                                holderV14.setCode(-1);
                                holderV14.setMessage("修改地址失败：WMS撤回状态异常!");
                                if (log.isDebugEnabled()) {
                                    log.debug(LogUtil.format("修改地址失败：WMS撤回状态异常,orderId=", ocBorderDto.getId()));
                                }
                                return holderV14;
                            }
                            //先调服务，再调修改地址
                            if (OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_YES.toInteger() == wmsCancelStatus) {
                                OcBOrder newOcBOrder = copyOrderAndLogRecord(ocBorderDto, loginUser);
                                //应该更新复制后的新的订单
                                return updateAddress(newOcBOrder, receiverAddressBefore, updateInfo, loginUser);
                            } else {
                                holderV14.setCode(-1);
                                if (log.isDebugEnabled()) {
                                    log.debug(LogUtil.format("订单在WMS中未取消，不允许修改收货地址，建议先撤回WMS再进行修改收货地址,orderId=",
                                            ocBorderDto.getId()));
                                }
                                holderV14.setMessage("订单在WMS中未取消，不允许修改收货地址，建议先撤回WMS再进行修改收货地址！");
                                return holderV14;
                            }
                        } else {
                            holderV14.setCode(-1);
                            if (log.isDebugEnabled()) {
                                log.debug(LogUtil.format("修改地址失败：订单状态异常,orderId=", ocBorderDto.getId()));
                            }
                            holderV14.setMessage("修改地址失败：订单状态异常");
                            return holderV14;
                        }
                    }
                } else {
                    throw new NDSException(Resources.getMessage(" 当前订单其他人在操作，请稍后再试!", loginUser.getLocale()));
                }
            } catch (Exception ex) {
                if (log.isDebugEnabled()) {
                    log.error(LogUtil.format("updateReceiveAddress修改地址服务异常,error:{}"),
                            Throwables.getStackTraceAsString(ex));
                }
                throw new NDSException("修改地址服务异常" + ex);
            } finally {
                redisLock.unlock();
            }

        }

        holderV14.setMessage("修改地址失败");
        holderV14.setCode(-1);
        return holderV14;

    }

    // 修改地址参考一商代码
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 updateReceiveAddressNew(ReceiverAddressDto addressDto, User user, boolean isRollback, boolean isRecoil) {
        Long id = addressDto.getId();
        if (id == null) {
            return new ValueHolderV14(ResultCode.FAIL, "全渠道订单表的id不能为空");
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("修改地址开始 订单id=", id));
        }
        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(id);
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        String tid = Strings.EMPTY;
        try {
            if (!redisLock.tryLock(1000, TimeUnit.MILLISECONDS)) {
                throw new NDSException(Resources.getMessage(String.format("修改地址 当前订单其他人在操作 请稍后再试 订单id %s", id), user.getLocale()));
            }
            OcBOrder beforeOrderDb = ocBOrderMapper.selectById(id);

            String receiverAddressBefore = beforeOrderDb.getCpCRegionProvinceEname() + beforeOrderDb.getCpCRegionCityEname()
                    + beforeOrderDb.getCpCRegionAreaEname() + Optional.ofNullable(beforeOrderDb.getCpCRegionTownEname()).orElse(Strings.EMPTY) + beforeOrderDb.getReceiverAddress();
            if (beforeOrderDb == null) {
                return new ValueHolderV14(ResultCode.FAIL, String.format("修改地址 订单表中查询不到数据 订单id %d", id));
            }
            if (PlatFormEnum.VIP_JITX.getCode().equals(beforeOrderDb.getPlatform())) {
                return new ValueHolderV14(ResultCode.FAIL, "唯品会JITX订单不允许修改地址");
            }
            if (!isRecoil && beforeOrderDb.getIsMerge() == 1) {
                //已合并订单不修改
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("合并订单不允许修改地址,beforeOrderDb.ordeId=", beforeOrderDb.getId()));
                }
                return new ValueHolderV14(ResultCode.FAIL, "已合并订单不修改！");
            }
            tid = beforeOrderDb.getTid();
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("修改地址开始2 tid /订单id =", tid, id));
            }
            int orderStatus = toInt(beforeOrderDb.getOrderStatus());
            if (orderStatus == OcOrderCheckBoxEnum.CHECKBOX_AUDITED.getVal()
                    || orderStatus == OcOrderCheckBoxEnum.CHECKBOX_IN_DISTRIBUTION.getVal()) {
                //反审核订单
                if (!toExamineOrder(beforeOrderDb, user, isRecoil ? LogTypeEnum.UPDATE_ADDRESS_REVERSE_AUDIT.getType() :
                        LogTypeEnum.AUTOMATIC_UPDATE_ADDRESS_REVERSE_AUDIT.getType())) {
                    if (log.isDebugEnabled()) {
                        log.debug(LogUtil.format("订单反审核失败，不允许修改地址信息,beforeOrderDb.getId=", beforeOrderDb.getId()));
                    }
                    return new ValueHolderV14(ResultCode.FAIL, "订单反审核失败，不允许修改地址信息！");
                } else {
                    orderStatus = toInt(beforeOrderDb.getOrderStatus());
                }
            }
            if (orderStatus != OcOrderCheckBoxEnum.CHECKBOX_UNCONFIRMED.getVal()
                    && orderStatus != OcOrderCheckBoxEnum.CHECKBOX_OUT_OF_STOCK.getVal()
                    && orderStatus != OcOrderCheckBoxEnum.CHECKBOX_PENDING_ALLOCATED.getVal()) {
                String msg = String.format("修改地址 订单【%s】，不允许修改地址信息。建议反审核之后，在发货单管理页面修改！ tid %s id %d",
                        OcOrderCheckBoxEnum.enumToStringByValue(orderStatus), tid, id);
                return new ValueHolderV14(ResultCode.FAIL, msg);
            }
            // 定时任务 && 手工修改地址 并且是合单 不推送平台
            boolean isPushPlatform = isRecoil && (beforeOrderDb.getIsMerge() != 1 && (Objects.isNull(beforeOrderDb.getIsSplit()) || beforeOrderDb.getIsSplit() != 1));
            ValueHolderV14 vh = updateAddressAndPushTaobaoBiz(beforeOrderDb, addressDto, user, isRollback, isPushPlatform, receiverAddressBefore);
            if (!vh.isOK()) {
                log.error(LogUtil.format("修改地址失败  message {},tid/id =", tid, id), vh.getMessage());
                return vh;
            }
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("修改地址成功 tid/id = ", tid, id));
            }
            return vh;
        } catch (Exception ex) {
            log.error(LogUtil.format("修改地址异常,message:{},tid/id=", tid, id), Throwables.getStackTraceAsString(ex));
            return new ValueHolderV14(ResultCode.FAIL, ex.getMessage());
        } finally {
            redisLock.unlock();
        }
    }
    /**
     * description:在最外层加锁
     * @Author:  liuwenjin
     * @Date 2023/3/6 19:31
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 updateReceiveAddressNoLock(ReceiverAddressDto addressDto, User user, boolean isRollback, boolean isRecoil) {
        Long id = addressDto.getId();
        if (id == null) {
            return new ValueHolderV14(ResultCode.FAIL, "全渠道订单表的id不能为空");
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("修改地址开始 订单id=", id));
        }
        String tid = Strings.EMPTY;
        try {

            OcBOrder beforeOrderDb = ocBOrderMapper.selectById(id);
            String receiverAddressBefore = beforeOrderDb.getCpCRegionProvinceEname() + beforeOrderDb.getCpCRegionCityEname()
                    + beforeOrderDb.getCpCRegionAreaEname() + Optional.ofNullable(beforeOrderDb.getCpCRegionTownEname()).orElse(Strings.EMPTY) + beforeOrderDb.getReceiverAddress();
            if (beforeOrderDb == null) {
                return new ValueHolderV14(ResultCode.FAIL, String.format("修改地址 订单表中查询不到数据 订单id %d", id));
            }
            if (PlatFormEnum.VIP_JITX.getCode().equals(beforeOrderDb.getPlatform())) {
                return new ValueHolderV14(ResultCode.FAIL, "唯品会JITX订单不允许修改地址");
            }
            if (!isRecoil && beforeOrderDb.getIsMerge() == 1) {
                //已合并订单不修改
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("合并订单不允许修改地址,beforeOrderDb.ordeId=", beforeOrderDb.getId()));
                }
                return new ValueHolderV14(ResultCode.FAIL, "已合并订单不修改！");
            }
            tid = beforeOrderDb.getTid();
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("修改地址开始2 tid /订单id =", tid, id));
            }
            int orderStatus = toInt(beforeOrderDb.getOrderStatus());
            if (orderStatus == OcOrderCheckBoxEnum.CHECKBOX_AUDITED.getVal()
                    || orderStatus == OcOrderCheckBoxEnum.CHECKBOX_IN_DISTRIBUTION.getVal()) {
                //反审核订单
                if (!toExamineOrder(beforeOrderDb, user, isRecoil ? LogTypeEnum.UPDATE_ADDRESS_REVERSE_AUDIT.getType() :
                        LogTypeEnum.AUTOMATIC_UPDATE_ADDRESS_REVERSE_AUDIT.getType())) {
                    if (log.isDebugEnabled()) {
                        log.debug(LogUtil.format("订单反审核失败，不允许修改地址信息,beforeOrderDb.getId=", beforeOrderDb.getId()));
                    }
                    return new ValueHolderV14(ResultCode.FAIL, "订单反审核失败，不允许修改地址信息！");
                } else {
                    orderStatus = toInt(beforeOrderDb.getOrderStatus());
                }
            }
            if (orderStatus != OcOrderCheckBoxEnum.CHECKBOX_UNCONFIRMED.getVal()
                    && orderStatus != OcOrderCheckBoxEnum.CHECKBOX_OUT_OF_STOCK.getVal()
                    && orderStatus != OcOrderCheckBoxEnum.CHECKBOX_PENDING_ALLOCATED.getVal()) {
                String msg = String.format("修改地址 订单【%s】，不允许修改地址信息。建议反审核之后，在发货单管理页面修改！ tid %s id %d",
                        OcOrderCheckBoxEnum.enumToStringByValue(orderStatus), tid, id);
                return new ValueHolderV14(ResultCode.FAIL, msg);
            }
            // 定时任务 && 手工修改地址 并且是合单 不推送平台
            boolean isPushPlatform = isRecoil && (beforeOrderDb.getIsMerge() != 1 && (Objects.isNull(beforeOrderDb.getIsSplit()) || beforeOrderDb.getIsSplit() != 1));
            ValueHolderV14 vh = updateAddressAndPushTaobaoBiz(beforeOrderDb, addressDto, user, isRollback, isPushPlatform, receiverAddressBefore);
            if (!vh.isOK()) {
                log.error(LogUtil.format("修改地址失败  message {},tid/id =", tid, id), vh.getMessage());
                return vh;
            }
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("修改地址成功 tid/id = ", tid, id));
            }
            return vh;
        } catch (Exception ex) {
            log.error(LogUtil.format("修改地址异常,message:{},tid/id=", tid, id), Throwables.getStackTraceAsString(ex));
            return new ValueHolderV14(ResultCode.FAIL, ex.getMessage());
        }
    }

    private ValueHolderV14 updateAddressAndPushTaobaoBiz(OcBOrder beforeOrderDb, ReceiverAddressDto addressDto, User user, boolean isRollback, boolean isPushPlatform, String receiverAddressBefore) throws Exception {
        WarehouseAndLogisticsDto houseDto = null;
        // 修改地址现在不分仓分物流
        log.info(LogUtil.format("OcBorderUpdateService.updateAddressAndPushTaobaoBiz,orderId=",
                "OcBorderUpdateService.updateAddressAndPushTaobaoBiz"), beforeOrderDb.getId());
        //230417徐平需求 -- 更新订单省or市or区后，若订单=待审核&订单业务类型设置自动寻源 则将订单返回待寻源进行重新寻源
        if (OmsOrderStatus.UNCONFIRMED.toInteger().equals(beforeOrderDb.getOrderStatus())
                && omsBusinessTypeStService.isAutoOccupy(beforeOrderDb)) {
            long beforeProvId = toLong(beforeOrderDb.getCpCRegionProvinceId());
            long afterProvId = toLong(addressDto.getCpCRegionProvinceId());
            long beforeCityId = toLong(beforeOrderDb.getCpCRegionCityId());
            long afterCityId = toLong(addressDto.getCpCRegionCityId());
            long beforeAreaId = toLong(beforeOrderDb.getCpCRegionAreaId());
            long afterAreaId = toLong(addressDto.getCpCRegionAreaId());
            if ((beforeProvId != 0L && afterProvId != 0L && beforeProvId != afterProvId)
                    || (beforeCityId != 0L && afterCityId != 0L && beforeCityId != afterCityId)
                    || (beforeAreaId != 0L && afterAreaId != 0L && beforeAreaId != afterAreaId)) {
                //释放库存占用、更新订单状态、重置占单中间表状态、记录操作日志
                List<OcBOrderItem> orderItems = ocBOrderItemMapper.selectOrderItemListOccupy(beforeOrderDb.getId());
                ValueHolderV14 sgValueHolder = sgRpcService.voidSgStockOccupy(beforeOrderDb, orderItems, user);
                if (!sgValueHolder.isOK()) {
                    throw new NDSException(beforeOrderDb.getBillNo() + "修改收货地址失败,释放库存失败");
                }
                beforeOrderDb.setOrderStatus(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger());
                ocBOrderMapper.updateById(beforeOrderDb);
                omsOccupyTaskService.addOrUpdateOccupyTask(beforeOrderDb, 1);
                omsOrderLogService.addUserOrderLog(beforeOrderDb.getId(), beforeOrderDb.getBillNo(),
                        OrderLogTypeEnum.OCCUPY.getKey(), "订单省市区变更重新寻源", "", "", user);
            }
        }
        //邮编补0
        addressDto.setReceiverZip(zip6(addressDto.getReceiverZip()));
        return this.updateAddressAndPushTaobao(beforeOrderDb, addressDto, houseDto, user, isRollback, isPushPlatform, receiverAddressBefore);
    }

    /**
     * 通用修改地址
     *
     * @param jsonObject JSONObject
     * @param loginUser  User
     * @return vh
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 updateReceiveAddressStan(JSONObject jsonObject, User loginUser) {

        ValueHolderV14 holderV14 = new ValueHolderV14();

        Long id = jsonObject.getLong("id");
        JSONObject updateInfo = jsonObject.getJSONObject("updateInfo");
        //redis锁单
        log.debug("OcBorderUpdateService_updateReceiveAddress 入参id=" + id);
        try {
            OcBOrder ocBorderDto = ocBOrderMapper.selectById(id);
            //无明细直接判断 订单状态为待审核、缺货状态，则执行占用库存服务
            String cpCRegionTownEname = ocBorderDto.getCpCRegionTownEname();
            cpCRegionTownEname = StringUtils.isEmpty(cpCRegionTownEname) ? "" : cpCRegionTownEname;
            Integer orderStatus = ocBorderDto.getOrderStatus();
            String receiverAddressBefore = ocBorderDto.getCpCRegionProvinceEname()
                    + ocBorderDto.getCpCRegionCityEname() + ocBorderDto.getCpCRegionAreaEname()
                    + cpCRegionTownEname
                    + ocBorderDto.getReceiverAddress();
            //修改收货地址
            if (updateInfo != null) {
                if (orderStatus == OcOrderCheckBoxEnum.CHECKBOX_AUDITED.getVal()
                        || orderStatus == OcOrderCheckBoxEnum.CHECKBOX_IN_DISTRIBUTION.getVal()
                        || OcBOrderConst.IS_STATUS_IY.equals(ocBorderDto.getIsMerge())) {
                    //若订单状态为“已审核”
                    holderV14.setMessage("当前订单:(" + ocBorderDto.getId() + ")状态不允许修改地址！");
                    holderV14.setCode(-1);
                    return holderV14;
                } else if (orderStatus == OcOrderCheckBoxEnum.CHECKBOX_UNCONFIRMED.getVal()
                        || orderStatus == OcOrderCheckBoxEnum.CHECKBOX_OUT_OF_STOCK.getVal()) {
                    //若订单状态为“待审核”、“缺货”
                    return updateAddress(ocBorderDto, receiverAddressBefore, updateInfo, loginUser);
                } else {
                    holderV14.setCode(-1);
                    holderV14.setMessage("修改地址失败，订单:(" + ocBorderDto.getId() + ")状态异常");
                    return holderV14;
                }
            }
        } catch (Exception ex) {
            log.debug("updateReceiveAddress修改地址服务异常" + ex);
        }
        holderV14.setMessage("订单:(" + id + ")修改地址失败");
        holderV14.setCode(-1);
        return holderV14;
    }

    /**
     * 重新分仓分物流
     *
     * @param beforeOrderDb :
     * @param addressDto    :
     * @param user          :
     */
    private ValueHolderV14<WarehouseAndLogisticsDto> reDistributeWarehouseAndLogistics(OcBOrder beforeOrderDb, ReceiverAddressDto addressDto, User user) {
        OcBOrder updateOrder = new OcBOrder();
        updateOrder.setId(beforeOrderDb.getId());
        if (beforeOrderDb.getAutoAuditStatus() != null) {
            updateOrder.setAutoAuditStatus(beforeOrderDb.getAutoAuditStatus());
            if (beforeOrderDb.getAutoAuditStatus() == 0) {
                log.debug(LogUtil.format("updateAddress订单自动审核任务,beforeOrderDb.getId = ", beforeOrderDb.getId()));
                auditTaskMapper.updateTaskStatusByOrderId(Lists.newArrayList(beforeOrderDb.getId()));
            }
        }
        if (StringUtils.isNotBlank(addressDto.getShipAmt())) {
            updateOrder.setShipAmt(new BigDecimal(addressDto.getShipAmt()));
        }
        updateOrder.setModifierename(user.getEname());
        updateOrder.setModifieddate(new Date());
        updateOrder.setIsModifiedOrder(1);
        //JITX类型订单修改地址，不自动调用寻仓服务
        if (!beforeOrderDb.getPlatform().equals(PlatFormEnum.VIP_JITX.getCode())) {
            OcBOrderRelation orderInfoRelation = omsOrderService.selectOmsOrderInfo(beforeOrderDb.getId());
            if (orderInfoRelation != null) {
                if (orderInfoRelation.getOrderInfo() != null) {
                    orderInfoRelation.getOrderInfo().setCpCRegionProvinceId(addressDto.getCpCRegionProvinceId());
                    orderInfoRelation.getOrderInfo().setCpCRegionProvinceEcode(addressDto.getCpCRegionProvinceEcode());
                    orderInfoRelation.getOrderInfo().setCpCRegionProvinceEname(addressDto.getCpCRegionProvinceEname());
                    // 分物流 会通过id查询数据库 会用到省市区id    先入库
                    orderInfoRelation.getOrderInfo().setCpCRegionCityId(addressDto.getCpCRegionCityId());
                    orderInfoRelation.getOrderInfo().setCpCRegionAreaId(addressDto.getCpCRegionAreaId());
                    // 入库,分物流会通过id查询单据信息
                    orderMapper.updateById(orderInfoRelation.getOrderInfo());
                }
            }
            Long phyWarehouseId = omsWarehousRuleService.doCallDistributeWarehouse(orderInfoRelation, user);
            if (phyWarehouseId == null) {
                return new ValueHolderV14<>(ResultCode.FAIL, "重新分仓失败");
            }
            if (phyWarehouseId != null) {
                //直接调用改仓服务
                List<Long> ids = new ArrayList<>();
                ids.add(beforeOrderDb.getId());
                ids.add(-1L);
                ValueHolderV14 valueHolderV14 = updateOrderInfoService.updateWarehouseForUpdateAddressNew(ids, phyWarehouseId, user);
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("修改地址中调用修改仓库服务出参：{},beforeOrderDb.getId= ", beforeOrderDb.getId()),
                            valueHolderV14.toJSONObject());
                }
                if (valueHolderV14.getCode() == 2) {
                    //库存不足，订单改为缺货状态
                    updateOrder.setOrderStatus(2);
                }
            }
        }
        ocBOrderMapper.updateById(updateOrder);
        /**
         * 订单为缺货时，处理缺货订单，如果订单不是缺货，方法直接返回不做任何处理。
         */
        splitOutStockOrderService.processOutStockOrder(updateOrder.getId(), user);
        // 分仓：逻辑仓
//        ValueHolderV14<WarehouseAndLogisticsDto> vhHouseDto = updateOrderInfoService.buildStore(addressDto.getId(), inParamOrder.getCpCShopId(), phyWarehouseId, user);
//        if (!vhHouseDto.isOK()) {
//            return vhHouseDto;
//        }
//        //分物流
//        OcBOrder inParamOrder2 = new OcBOrder();
//        BeanUtils.copyProperties(beforeOrderDb, inParamOrder2);
//        BeanUtils.copyProperties(addressDto, inParamOrder2);
//        setOrderStore(vhHouseDto.getData(), inParamOrder2);
//        OcBOrderRelation ocBOrderRelation = new OcBOrderRelation();
//        ocBOrderRelation.setOrderInfo(inParamOrder2);
//        ValueHolderV14 logisticsInfo = omsOrderDistributeLogisticsService.orderDistributeLogisticsWholeProcess(beforeOrderDb, ocBOrderRelation, user);
//        if (logisticsInfo.getCode() != ResultCode.SUCCESS) {
//            return new ValueHolderV14<>(-2, "修改地址分仓之后调用分物流服务失败：" + logisticsInfo.getMessage());
//        }
//        OcBOrder ocBOrderTemp = (OcBOrder) logisticsInfo.getData();
//        ocBOrderMapper.updateById(beforeOrderDb);
//
//        vhHouseDto.getData().setCpCLogisticsId(ocBOrderTemp.getCpCLogisticsId());
//        vhHouseDto.getData().setCpCLogisticsEcode(ocBOrderTemp.getCpCLogisticsEcode());
//        vhHouseDto.getData().setCpCLogisticsEname(ocBOrderTemp.getCpCLogisticsEname());
        return new ValueHolderV14<>(ResultCode.SUCCESS, "保存成功！");
    }

    private void setOrderStore(WarehouseAndLogisticsDto source, OcBOrder target) {
        target.setCpCPhyWarehouseId(source.getCpCPhyWarehouseId());
        target.setCpCPhyWarehouseEcode(source.getCpCPhyWarehouseEcode());
        target.setCpCPhyWarehouseEname(source.getCpCPhyWarehouseEname());

        /*target.setCpCStoreId(source.getCpCStoreId());
        target.setCpCStoreEcode(source.getCpCStoreEcode());
        target.setCpCStoreEname(source.getCpCStoreEname());*/

    }


    private int toInt(Integer num) {
        return num == null ? 0 : num;
    }

    private long toLong(Long num) {
        return num == null ? 0L : num;
    }

    private String zip6(String zip) {
        if (StringUtils.isBlank(zip) || zip.length() > 6) {
            return "000000";
        }
        return StringUtils.leftPad(zip, 6, '0');
    }


    /**
     * 最终发货地址以我们平台为主，淘宝平台同步地址失败很正常
     * 但是还是要加上事务，否则 新单占用库存失败，还是订单作废失败。
     * 都会导致复制新单不可逆的生成。那样失败一次生成一个新单。
     *
     * @param jsonObject 入参
     * @param loginUser  loginUser
     * @return ValueHolderV14
     */

    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 jitxUpdateReceiveAddress(JSONObject jsonObject, User loginUser) {
        ValueHolderV14 holderV14 = new ValueHolderV14();
        if (jsonObject != null) {
            Long id = jsonObject.getLong("id");
            JSONObject updateInfo = jsonObject.getJSONObject("updateInfo");
            //redis锁单
            log.debug(LogUtil.format("OcBorderUpdateService_jitxUpdateReceiveAddress 入参id=", id));

            String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(id);
            RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
            try {
                if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                    log.debug(LogUtil.format("OcBorderUpdateService_jitxUpdateReceiveAddress 锁单id=", id));
                    OcBOrder ocBorderDto = ocBOrderMapper.selectById(id);

                    //无明细直接判断 订单状态为待审核、缺货状态，则执行占用库存服务
                    Integer orderStatus = ocBorderDto.getOrderStatus();
                    Integer wmsCancelStatus = ocBorderDto.getWmsCancelStatus();
                    String receiverAddressBefore = ocBorderDto.getCpCRegionProvinceEname() + ocBorderDto.getCpCRegionCityEname() + ocBorderDto.getCpCRegionAreaEname() + ocBorderDto.getReceiverAddress();
                    //修改收货地址
                    if (updateInfo != null) {

                        List<OcBAuditTask> ocBAuditTasks = auditTaskMapper.selectTaskIdListByOrderId(Lists.newArrayList(id));
                        if (CollectionUtils.isNotEmpty(ocBAuditTasks)) {
                            ocBorderDto.setAutoAuditStatus(0);
                        } else {
                            ocBorderDto.setAutoAuditStatus(null);
                        }

                        log.debug(LogUtil.format("OcBorderUpdateService_jitxUpdateReceiveAddress,获取自动审核策略:{}," +
                                        "orderId=", id),
                                JSONObject.toJSONString(ocBAuditTasks));
                        /**
                         * 1、订单状态为"待审核"和"缺货"，允许直接修改地址
                         * 2、订单状态为"已审核"和"配货中且WMS状态已撤回"，允许反审核成功后修改地址
                         */
                        if (orderStatus == OcOrderCheckBoxEnum.CHECKBOX_UNCONFIRMED.getVal()
                                || orderStatus == OcOrderCheckBoxEnum.CHECKBOX_OUT_OF_STOCK.getVal()) {
                            //若订单状态为“待审核”、“缺货”
                            return updateAddress(ocBorderDto, receiverAddressBefore, updateInfo, loginUser);
                        } else if ((orderStatus == OcOrderCheckBoxEnum.CHECKBOX_IN_DISTRIBUTION.getVal()
                                && ocBorderDto.getWmsCancelStatus() == OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_YES.toInteger())
                                || orderStatus == OcOrderCheckBoxEnum.CHECKBOX_AUDITED.getVal()) {
                            //反审核订单成功后修改地址
                            if (toExamineOrder(ocBorderDto, loginUser, LogTypeEnum.NOT_CAPTURED_SCENE.getType())) {
                                return updateAddress(ocBorderDto, receiverAddressBefore, updateInfo, loginUser);
                            }
                            holderV14.setMessage("订单反审核失败，不允许修改地址信息！");
                            log.debug(LogUtil.format("订单反审核失败，不允许修改地址信息,orderId=", ocBorderDto.getId()));
                            holderV14.setCode(-1);
                            return holderV14;
                        } else {
                            holderV14.setCode(-1);
                            log.debug(LogUtil.format("修改地址失败：订单状态异常,orderId=", ocBorderDto.getId()));
                            holderV14.setMessage("修改地址失败：订单状态异常");
                            return holderV14;
                        }
                    }
                } else {
                    throw new NDSException(Resources.getMessage(" 当前订单其他人在操作，请稍后再试!", loginUser.getLocale()));
                }
            } catch (Exception ex) {
                log.debug(LogUtil.format("updateReceiveAddress修改地址服务异常,error:{}"),
                        Throwables.getStackTraceAsString(ex));
                throw new NDSException("修改地址服务异常" + ex);
            } finally {
                redisLock.unlock();
            }

        }

        holderV14.setMessage("修改地址失败");
        holderV14.setCode(-1);
        return holderV14;

    }


    /**
     * 已审核订单调用反审核接口
     *
     * @param ocBOrder
     */
    public boolean toExamineOrder(OcBOrder ocBOrder, User user, Long type) {
        try {
            Long id = ocBOrder.getId();
            ValueHolderV14 holderV14 = new ValueHolderV14();
            boolean isSuccess = ocBOrderTheAuditService.updateOrderInfo(user, holderV14, id, type);
            if (isSuccess) {
                //反审核成功  将订单状态改为 待审核
                ocBOrder.setOrderStatus(OmsOrderStatus.UNCONFIRMED.toInteger());
            }
            return isSuccess;
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 调用反审核失败", e);
            return false;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean toExamineOrderBatch(List<OcBOrder> ocBOrderList, User user, Long type) {
        try {
            for (OcBOrder ocBOrder : ocBOrderList) {
                Long id = ocBOrder.getId();
                ValueHolderV14 holderV14 = new ValueHolderV14();
                boolean isSuccess = ocBOrderTheAuditService.updateOrderInfo(user, holderV14, id, type);
                if (isSuccess) {
                    //反审核成功  将订单状态改为 待审核
                    ocBOrder.setOrderStatus(OmsOrderStatus.UNCONFIRMED.toInteger());
                } else {
                    throw new RuntimeException("组合商品反审核失败!");
                }
            }
            return true;
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 调用反审核失败", e);
            return false;
        }
    }

    private ValueHolderV14 updateAddress(OcBOrder ocBorderDto, String receiverAddressBefore, JSONObject updateInfo, User loginUser) {
        //得物，用户修改地址手机号适配
        if (ObjectUtil.equal(PlatFormEnum.DE_WU.getCode(), ocBorderDto.getPlatform())) {
            //更新名字
            String receiverName = updateInfo.getString("receiver_name");
            //更新手机号
            String receiverMobile = updateInfo.getString("receiver_mobile");
            if (StringUtils.isNotBlank(receiverMobile) && receiverMobile.contains("转")) {
                String[] receiverMobileArr = receiverMobile.split("转");
                String newMobile = receiverMobileArr[0];
                if (StringUtils.isNotBlank(newMobile)) {
                    updateInfo.put("receiver_mobile", newMobile);
                }
                String newName = receiverMobileArr[1];
                if (StringUtils.isNotBlank(newName)) {
                    updateInfo.put("receiver_name", receiverName + newName);
                }
            }
        }

        OcBOrder order = new OcBOrder();
        order.setId(ocBorderDto.getId());
        if (ocBorderDto.getAutoAuditStatus() != null) {
            order.setAutoAuditStatus(ocBorderDto.getAutoAuditStatus());
            if (ocBorderDto.getAutoAuditStatus() == 0) {
                log.debug(LogUtil.format("updateAddress订单自动审核任务,orderId=", ocBorderDto.getId()));
                auditTaskMapper.updateTaskStatusByOrderId(Lists.newArrayList(order.getId()));
            }
        }

        ValueHolderV14 holderV14 = new ValueHolderV14();
        order.setReceiverAddress(updateInfo.getString("receiver_address").replaceAll(",", "::::"));
        order.setCpCRegionProvinceId(updateInfo.getLong("cp_c_region_province_id"));
        order.setCpCRegionCityId(updateInfo.getLong("cp_c_region_city_id"));
        order.setCpCRegionAreaId(updateInfo.getLong("cp_c_region_area_id"));
        if (StringUtils.isNotEmpty(updateInfo.getString("platform_province"))) {
            order.setPlatformProvince(updateInfo.getString("platform_province"));
        }
        if (StringUtils.isNotEmpty(updateInfo.getString("platform_city"))) {
            order.setPlatformCity(updateInfo.getString("platform_city"));
        }
        if (StringUtils.isNotEmpty(updateInfo.getString("platform_area"))) {
            order.setPlatformArea(updateInfo.getString("platform_area"));
        }
        try {
            ValueHolder valueHolder = cpRpcService.getRegionNameByid(updateInfo.getLong("cp_c_region_province_id")
                    , updateInfo.getLong("cp_c_region_city_id"), updateInfo.getLong("cp_c_region_area_id"));
            ReginQueryResult reginQueryResult = (ReginQueryResult) valueHolder.get("data");
            order.setCpCRegionProvinceEname(reginQueryResult.getProvName());
            order.setCpCRegionCityEname(reginQueryResult.getCityName());
            order.setCpCRegionAreaEname(reginQueryResult.getRegionName());
            order.setCpCRegionTownEname(updateInfo.getString("cp_c_region_town_ename"));
        } catch (Exception e) {
            log.error(LogUtil.format("updateReceiveAddress调用更新省市区接口异常：{}"), Throwables.getStackTraceAsString(e));
        }
        //  来统计更新的地方
        String changeMessage = checkeChange(updateInfo, ocBorderDto);
        order.setReceiverName(updateInfo.getString("receiver_name"));
        order.setReceiverMobile(updateInfo.getString("receiver_mobile"));
        order.setReceiverPhone(updateInfo.getString("receiver_phone"));
        order.setReceiverZip(updateInfo.getString("receiver_zip"));
        order.setShipAmt(updateInfo.getBigDecimal("ship_amt"));
        order.setModifierename(loginUser.getEname());
        order.setModifieddate(new Date());
        // 自动打标：是否为改单
        order.setIsModifiedOrder(1);
        String oaid = updateInfo.getString("oaid");
        if (StringUtils.isNotEmpty(oaid)) {
            order.setOaid(oaid);
        }
        if (ObjectUtil.equal(PlatFormEnum.JINGDONG.getCode(), ocBorderDto.getPlatform())
        ) {
            //京东修改地址，oaid跟着变，哪怕为空
            order.setOaid(oaid);
        }
        //拿最新的单子进行下一步操作
        OcBOrder bOrder = ocBOrderMapper.selectById(order.getId());
        // @20200801 这里开始分物流：省没改动（若改动已经调用了上面的分仓分物流逻辑），只改动了市区，则分物流
        if ((Objects.nonNull(updateInfo.getLong("cp_c_region_province_id")) && !updateInfo.getLong("cp_c_region_province_id").equals(ocBorderDto.getCpCRegionProvinceId()))
                || (Objects.nonNull(updateInfo.getLong("cp_c_region_city_id")) && !updateInfo.getLong("cp_c_region_city_id").equals(ocBorderDto.getCpCRegionCityId()))
                || (Objects.nonNull(updateInfo.getLong("cp_c_region_area_id")) && !updateInfo.getLong("cp_c_region_area_id").equals(ocBorderDto.getCpCRegionAreaId()))) {
            Integer orderStatus = bOrder.getOrderStatus();
            List<OcBOrderItem> orderItems = ocBOrderItemMapper.selectOrderItemListOccupy(order.getId());
            if (OmsOrderStatus.UNCONFIRMED.toInteger().equals(orderStatus)) {
                //
                ValueHolderV14 sgValueHolder = sgRpcService.voidSgStockOccupy(bOrder, orderItems, loginUser);
                if (sgValueHolder.getCode() != 0) {
                    holderV14.setCode(-1);
                    holderV14.setMessage(bOrder.getBillNo() + "修改收货地址失败,释放库存失败");
                    return holderV14;
                }
                order.setOrderStatus(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger());
                omsOccupyTaskService.addOrUpdateOccupyTask(bOrder, 1);
                omsOrderLogService.addUserOrderLog(bOrder.getId(), bOrder.getBillNo(),
                        OrderLogTypeEnum.OCCUPY.getKey(), "订单省市区变更重新寻源", "", "", loginUser);
            }
        }
        try {
            MD5Util.encryptOrderInfo4Merge(order);
            ocBOrderMapper.updateById(order);
            //SpecialElasticSearchUtil.indexDocument(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME, OcElasticSearchIndexResources.OC_B_ORDER_TYPE_NAME, ocBOrderMapper.selectById(order.getId()), order.getId());
        } catch (Exception ex) {
            log.error(LogUtil.format("updateReceiveAddress修改收货地址失败,error:{}"), Throwables.getStackTraceAsString(ex));
            throw new NDSException("修改收货地址失败" + ex.getMessage());
        }
        bOrder = ocBOrderMapper.selectById(order.getId());
        String cpCRegionTownEname = bOrder.getCpCRegionTownEname();
        cpCRegionTownEname = StringUtils.isEmpty(cpCRegionTownEname) ? "" : cpCRegionTownEname;
        //调用日志
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("修改前：").append(receiverAddressBefore).append("\n").append("修改后：")
                .append(bOrder.getCpCRegionProvinceEname()).append(bOrder.getCpCRegionCityEname()).append(bOrder.getCpCRegionAreaEname()).append(cpCRegionTownEname).append(bOrder.getReceiverAddress());
        try {
            if (!StringUtils.isEmpty(changeMessage)) {
                omsOrderLogService.addUserOrderLog(bOrder.getId(), bOrder.getBillNo(), OrderLogTypeEnum.ADDRESS_UPDATE.getKey(), changeMessage, "", "", loginUser);
            }
            omsOrderLogService.addUserOrderLog(bOrder.getId(), bOrder.getBillNo(), OrderLogTypeEnum.ADDRESS_UPDATE.getKey(), stringBuilder.toString(), "", "", loginUser);
        } catch (Exception ex) {
            log.error(LogUtil.format("updateReceiveAddress调用日志服务异常,error：{}")
                    , Throwables.getStackTraceAsString(ex));
        }

        holderV14.setCode(0);
        //更新淘宝收货地址
        try {
            updateReceiveAddress(bOrder, loginUser);
            holderV14.setMessage("修改收货地址成功!");
        } catch (Exception ex) {
            log.error(LogUtil.format("updateReceiveAddress平台同步异常,error：{}"), Throwables.getStackTraceAsString(ex));
            holderV14.setMessage("修改收货地址成功!，同步第三方平台失败");
        }
        return holderV14;
    }

    private String checkeChange(JSONObject updateInfo, OcBOrder ocBorderDto) {
        StringBuilder sb = new StringBuilder();
        String receiverName = updateInfo.getString("receiver_name");
        String receiverMobile = updateInfo.getString("receiver_mobile");
        String receiverPhone = updateInfo.getString("receiver_phone");
        String receiverZip = updateInfo.getString("receiver_zip");
        if (ocBorderDto.getReceiverName() == null) {
            if (!StringUtils.isEmpty(receiverName)) {
                sb.append("收货人修改前为：" + ocBorderDto.getReceiverName() + "修改后：" + receiverName);
            }
        } else {
            if (!ocBorderDto.getReceiverName().equals(receiverName)) {
                sb.append("收货人修改前为：" + ocBorderDto.getReceiverName() + "修改后：" + receiverName);
            }
        }
        if (ocBorderDto.getReceiverMobile() == null) {
            if (!StringUtils.isEmpty(receiverMobile)) {
                sb.append("手机号修改前为：" + ocBorderDto.getReceiverMobile() + "修改后：" + receiverMobile);
            }
        } else {
            if (!ocBorderDto.getReceiverMobile().equals(receiverMobile)) {
                sb.append("手机号修改前为：" + ocBorderDto.getReceiverMobile() + "修改后：" + receiverMobile);
            }
        }
        if (ocBorderDto.getReceiverPhone() == null) {
            if (!StringUtils.isEmpty(receiverPhone)) {
                sb.append("电话号修改前为：" + ocBorderDto.getReceiverPhone() + "修改后：" + receiverPhone);
            }
        } else {
            if (!ocBorderDto.getReceiverPhone().equals(receiverPhone)) {
                sb.append("电话号修改前为：" + ocBorderDto.getReceiverPhone() + "修改后：" + receiverPhone);
            }
        }
        if (ocBorderDto.getReceiverZip() == null) {
            if (!StringUtils.isEmpty(receiverZip)) {
                sb.append("邮编修改前为：" + ocBorderDto.getReceiverZip() + "修改后：" + receiverZip);
            }
        } else {
            if (!ocBorderDto.getReceiverZip().equals(receiverZip)) {
                sb.append("邮编修改前为：" + ocBorderDto.getReceiverZip() + "修改后：" + receiverZip);
            }
        }
        String s = sb.toString();
        return s;
    }


    /**
     * 更新成功后，判断平台是否为淘宝，若为淘宝则调用淘宝修改地址接口
     * 若为京东，则调动京东修改地址接口
     * 若为其他，则不调用接口，提示：“保存成功！”
     *
     * @param ocBorderDto 修改后的订单实体
     * @param loginUser   loginUser
     */
    public void updateReceiveAddress(OcBOrder ocBorderDto, User loginUser) {
        try {
            ModifyOrderAddrModel model = new ModifyOrderAddrModel();
            model.setOperateUser(loginUser);
            model.setPlatform(ocBorderDto.getPlatform());
            //如果是淘宝，查淘宝的表
            if (null != ocBorderDto.getPlatform() && PlatFormEnum.TAOBAO.getCode().equals(ocBorderDto.getPlatform())) {
                IpBTaobaoOrder taobaoOrder = ipBTaobaoOrderMapper.selectTaobaoOrderByTid(ocBorderDto.getTid());
                if (taobaoOrder == null) {
                    log.debug(LogUtil.format("updateReceiveAddress全渠道订单对应的实体为空,tid= ", ocBorderDto.getTid()));
                    throw new NDSException("全渠道订单tid= " + ocBorderDto.getTid() + "对应的实体为空");
                }
                //淘宝所需参数
                model.setSessionKey(cpRpcService.getSessionKey(ocBorderDto.getCpCShopId()));
                model.setSellerNick(taobaoOrder.getSellerNick());
                model.setTid(Long.parseLong(ocBorderDto.getTid()));
                model.setReceiverName(ocBorderDto.getReceiverName());
                model.setReceiverPhone(ocBorderDto.getReceiverPhone());
                model.setReceiverMobile(ocBorderDto.getReceiverMobile());
                model.setReceiverState(ocBorderDto.getCpCRegionProvinceEname());
                model.setReceiverCity(ocBorderDto.getCpCRegionCityEname());
                model.setReceiverDistrict(ocBorderDto.getCpCRegionAreaEname());
                model.setReceiverAddress(ocBorderDto.getReceiverAddress().replaceAll(",", "::::"));
                model.setReceiverZip(ocBorderDto.getReceiverZip());

                ValueHolderV14 valueHolderV14 = ipRpcService.updateAddr(model);
                if (!valueHolderV14.isOK()) {
                    log.debug(LogUtil.format("updateReceiveAddress修改地址调用淘宝接口异常") + valueHolderV14.getMessage());
                    throw new NDSException(valueHolderV14.getMessage());
                }

                log.debug(LogUtil.format("updateReceiveAddress修改地址调用淘宝接口成功") + valueHolderV14);
            } else if (PlatFormEnum.JINGDONG.getCode().equals(ocBorderDto.getPlatform())) {
                //京东所需参数
                model.setOrderId(Long.parseLong(ocBorderDto.getSourceCode()));
                model.setProvinceId(ocBorderDto.getCpCRegionProvinceId());
                model.setCityId(ocBorderDto.getCpCRegionCityId());
                model.setCountyId(ocBorderDto.getCpCRegionAreaId());
                model.setDetailAddr(ocBorderDto.getReceiverAddress());
                model.setTownId(null);//默认-1
                model.setCustomerName(ocBorderDto.getReceiverName());
                model.setCustomerPhone(ocBorderDto.getReceiverPhone());

                ValueHolderV14 valueHolderV14 = ipRpcService.updateAddr(model);
                if (!valueHolderV14.isOK()) {
                    log.debug(LogUtil.format("updateReceiveAddress修改地址调用京东接口异常") + valueHolderV14.getMessage());
                    throw new NDSException(valueHolderV14.getMessage());
                }
                log.debug(LogUtil.format("updateReceiveAddress修改地址调用京东接口成功") + valueHolderV14);
            }
        } catch (Exception ex) {
            String platFormName = "";
            if (PlatFormEnum.JINGDONG.getCode().equals(ocBorderDto.getPlatform())) {
                platFormName = "京东";
            } else if (PlatFormEnum.TAOBAO.getCode().equals(ocBorderDto.getPlatform())) {
                platFormName = "淘宝";
            }
            log.debug(LogUtil.format("updateReceiveAddress同步平台异常，异常原因,error:{}"), Throwables.getStackTraceAsString(ex));
            if (ex.getMessage() == null) {
                throw new NDSException("未同步" + platFormName + "平台。");
            } else {
                throw new NDSException("同步" + platFormName + "平台失败，失败原因：" + ex.getMessage());
            }
        }
    }

    /**
     * 若订单状态为“配货中”且WMS撤回状态为“已撤回”，则执行以下逻辑
     * 1)则将原订单复制生成一张新订单，订单状态为缺货
     * 2）且收货人等信息为界面信息，调用日志服务
     * 3)新单调用占用库存服务，调用成功，判断订单是否缺货，如果缺货则任务回滚，提示“XXX订单无库存，不允许修改地址！”如果为待审核则调用订单日志服务，传输以下参数
     * 4)作废原订单
     *
     * @param ocBOrder  订单实体
     * @param loginUser loginUser
     */
    private OcBOrder copyOrderAndLogRecord(OcBOrder ocBOrder, User loginUser) {
        OcBOrderRelation ocBOrderRelation;
        OcBOrder newOrder;
        //复制订单
        try {
            ocBOrderRelation = omsOrderSplitService.getOcBOrderRelation(ocBOrder.getId(), true);
            if (ocBOrderRelation.getOrderInfo() == null) {
                throw new NDSException("复制订单失败！");
            }
            newOrder = ocBOrderRelation.getOrderInfo();
        } catch (Exception ex) {
            log.debug(LogUtil.format("updateReceiveAddress复制订单异常,error:{}"), Throwables.getStackTraceAsString(ex));
            throw new NDSException("复制订单异常！" + ex.getMessage());
        }

        log.debug(LogUtil.format("OcBorderUpdateService复制订单成功，旧orderId/新orderId=",
                ocBOrder.getId(), newOrder.getId()));

        try {
            omsOrderLogService.addUserOrderLog(newOrder.getId(), newOrder.getBillNo(), OrderLogTypeEnum.ORDER_ADD.getKey(), "复制订单成功", "", "", loginUser);
        } catch (Exception ex) {
            log.debug(LogUtil.format("updateReceiveAddress调用日志服务异常,error:{}"), Throwables.getStackTraceAsString(ex));
        }

        /*
         * 新单调用占用库存服务，调用成功，判断订单是否缺货
         * 如果缺货则任务回滚，提示“XXX订单无库存，不允许修改地址！”
         * 如果为待审核则调用订单日志服务，传输以下参数
         */
        log.debug(LogUtil.format("updateReceiveAddress占用库存传输数据SgRpcService.querySearchStockAndModifyAddress" +
                "(ocBOrderRelation)==") + JSON.toJSONString(ocBOrderRelation));
        ValueHolderV14 holderV14 = sgRpcService.querySearchStockAndModifyAddress(ocBOrderRelation, loginUser);

        if (holderV14.isOK()) {
            try {
                omsOrderLogService.addUserOrderLog(newOrder.getId(), newOrder.getBillNo(), OrderLogTypeEnum.STOCK_OCCUPY_SUCCESS.getKey(), "占用库存成功", "", "", loginUser);
            } catch (Exception ex) {
                log.error(LogUtil.format("updateReceiveAddress调用日志服务异常,error:{}"), Throwables.getStackTraceAsString(ex));
            }
        } else if (holderV14.getCode() == 3) {
            //占用库存部分成功，有些为缺货
            try {
                omsOrderLogService.addUserOrderLog(newOrder.getId(), newOrder.getBillNo(), OrderLogTypeEnum.STOCK_OCCUPY_FAIL.getKey(), "因缺少库存，占用库存失败", "", "", loginUser);
            } catch (Exception ex) {
                log.error(LogUtil.format("updateReceiveAddress调用日志服务异常,error:{}"), Throwables.getStackTraceAsString(ex));
            }
        } else {
            log.debug(LogUtil.format("updateReceiveAddress占用库存服务失败") + holderV14.getMessage());
            throw new NDSException("占用库存服务失败" + holderV14.getMessage());
        }

        log.debug(LogUtil.format("OcBorderUpdateService占用库存成功,newOrder.getId=", newOrder.getId()));

        ValueHolderV14 valueHolderV14 = null;
        //作废原单
        try {
            valueHolderV14 = omsOrderCancellationService.doInvoildOutOrder(ocBOrder, loginUser);
            if (!valueHolderV14.isOK()) {
                throw new NDSException("调用作废订单服务失败" + valueHolderV14.getMessage());
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("updateReceiveAddress调用日志doOrderInvalid失败,error:{}", Throwables.getStackTraceAsString(ex)));
            throw new NDSException("调用作废订单服务失败" + valueHolderV14);
        }

        log.debug(LogUtil.format("OcBorderUpdateService作废原单成功已经作废,ocBOrder.getId", ocBOrder.getId()));

        return newOrder;
    }

    /**
     * 淘宝双11.预售修改地址
     *
     * @param jsonObject JSONObject
     * @param loginUser  User
     * @return vh
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 updateReceiveAddress4PreSale(JSONObject jsonObject, User loginUser) {
        ValueHolderV14 holderV14 = new ValueHolderV14();
        if (jsonObject != null) {
            Long id = jsonObject.getLong("id");
            JSONObject updateInfo = jsonObject.getJSONObject("updateInfo");
            //redis锁单
            log.debug(LogUtil.format("OcBorderUpdateService_updateReceiveAddress 入参id=", id));
            try {
                OcBOrder ocBorderDto = ocBOrderMapper.selectById(id);
                //无明细直接判断 订单状态为待审核、缺货状态，则执行占用库存服务
                Integer orderStatus = ocBorderDto.getOrderStatus();
                Integer wmsCancelStatus = ocBorderDto.getWmsCancelStatus();
                String receiverAddressBefore = ocBorderDto.getCpCRegionProvinceEname()
                        + ocBorderDto.getCpCRegionCityEname() + ocBorderDto.getCpCRegionAreaEname()
                        + ocBorderDto.getReceiverAddress();
                //修改收货地址
                if (updateInfo != null) {
                    if (orderStatus == OcOrderCheckBoxEnum.CHECKBOX_AUDITED.getVal()) {
                        //若订单状态为“已审核”
                        holderV14.setMessage("订单已审核，不允许修改地址信息，建议反审核再进行修改！");
                        holderV14.setCode(-1);
                        return holderV14;
                    } else if (orderStatus == OcOrderCheckBoxEnum.CHECKBOX_UNCONFIRMED.getVal()
                            || orderStatus == OcOrderCheckBoxEnum.CHECKBOX_OUT_OF_STOCK.getVal()) {
                        //若订单状态为“待审核”、“缺货”
                        return updateAddress(ocBorderDto, receiverAddressBefore, updateInfo, loginUser);
                    } else if (orderStatus == OcOrderCheckBoxEnum.CHECKBOX_IN_DISTRIBUTION.getVal()) {
                        //若订单状态为配货中且WMS撤回状态为“已撤回”
                        if (wmsCancelStatus == null) {
                            holderV14.setCode(-1);
                            holderV14.setMessage("修改地址失败：WMS撤回状态异常!");
                            return holderV14;
                        }
                        //先调服务，再调修改地址
                        if (OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_YES.toInteger() == wmsCancelStatus) {
                            OcBOrder newOcBOrder = copyOrderAndLogRecord(ocBorderDto, loginUser);
                            //应该更新复制后的新的订单
                            return updateAddress(newOcBOrder, receiverAddressBefore, updateInfo, loginUser);
                        } else {
                            holderV14.setCode(-1);
                            holderV14.setMessage("订单在WMS中未取消，不允许修改收货地址，建议先撤回WMS再进行修改收货地址！");
                            return holderV14;
                        }
                    } else {
                        holderV14.setCode(-1);
                        holderV14.setMessage("修改地址失败：订单状态异常");
                        return holderV14;
                    }
                }
            } catch (Exception ex) {
                log.debug(LogUtil.format("updateReceiveAddress修改地址服务异常,error:{}"), Throwables.getStackTraceAsString(ex));
                throw new NDSException("修改地址服务异常" + ex);
            }
        }
        holderV14.setMessage("修改地址失败");
        holderV14.setCode(-1);
        return holderV14;

    }

    /**
     * 收货人信息解密
     *
     * @param beforeOrderDb 原始订单信息
     * @param addressDto    修改后信息
     */
    private void decryptBuyerInfo(OcBOrder beforeOrderDb, ReceiverAddressDto addressDto) {
        OcBOrder ocBOrder = new OcBOrder();

        //拼接参数
        ocBOrder.setReceiverName(addressDto.getReceiverName());
        ocBOrder.setReceiverMobile(addressDto.getReceiverMobile());
        ocBOrder.setReceiverPhone(addressDto.getReceiverPhone());
        ocBOrder.setReceiverAddress(addressDto.getReceiverAddress());

        //解密
        saveBillService.copyBuyerInfo(ocBOrder, beforeOrderDb);

        addressDto.setReceiverName(ocBOrder.getReceiverName());
        addressDto.setReceiverMobile(ocBOrder.getReceiverMobile());
        addressDto.setReceiverPhone(ocBOrder.getReceiverPhone());
        addressDto.setReceiverAddress(ocBOrder.getReceiverAddress());
        addressDto.setOaid(ocBOrder.getOaid());
    }

    /**
     * 更新地址到数据库，同时上传到淘宝
     *
     * @param beforeOrderDb :
     * @param addressDto    :
     * @param user          :
     * @param isRollback    : 只有发货单管理页面 上传淘宝平台失败的时候 才回滚，其他的都不回滚，只留失败日志
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 updateAddressAndPushTaobao(OcBOrder beforeOrderDb, ReceiverAddressDto addressDto, WarehouseAndLogisticsDto houseDto, User user, boolean isRollback, boolean isPushPlatform, String receiverAddressBefore) throws Exception {

        if (StringUtils.isEmpty(addressDto.getOaid())) {
            //解密收货人信息
            decryptBuyerInfo(beforeOrderDb, addressDto);
        }

        //1.更新数据到表：oc_b_order
        int changRows = omsOrderService.updateForceAddress(beforeOrderDb.getPlatform(), addressDto, houseDto, user);
        if (changRows != 1) {
            throw new NDSException("修改地址更新数据库失败");
        }
        OcBOrder ocBOrderNew = ocBOrderMapper.selectById(beforeOrderDb.getId());
        OrderAddressConvertUtil.convert(ocBOrderNew);
        MD5Util.encryptOrderInfo4Merge(ocBOrderNew);
        // 只更新 订单合单加密信息
        UpdateWrapper<OcBOrder> wrapper = new UpdateWrapper<>();
        wrapper.set("ORDER_ENCRYPTION_CODE", ocBOrderNew.getOrderEncryptionCode());
        wrapper.set("RECEIVER_ADDRESS", ocBOrderNew.getReceiverAddress());
        wrapper.set("IS_MODIFIED_ORDER", 1);
        if (isRollback) {
            wrapper.set("IS_MANUAL_ADDR", 1);
        }
        wrapper.eq("ID", beforeOrderDb.getId());
        ocBOrderMapper.update(null, wrapper);
        // 当OMS定时任务将零售发货单修改地址成功后，将自助修改地址hold标为无效，重新计算HOLD标
        OcBOrderHoldItem item = ocBOrderHoldItemMapper.selectOrderHoldItemByOrderIdAndHoldReason(beforeOrderDb.getId(), OrderHoldReasonEnum.TB_AUDIT_VOLUNTARILY_HOLD.getKey());
        if (item != null && OcBOrderHoldConst.YES.equals(item.getHoldStatus())) {
            OcBOrder ocBorderDto = ocBOrderMapper.selectById(beforeOrderDb.getId());
            if (ocBorderDto.getIsInterecept() == 1) {
                ocBorderDto.setIsInterecept(0);
                ocBOrderHoldService.holdOrUnHoldOrder(ocBorderDto, OrderHoldReasonEnum.TB_AUDIT_VOLUNTARILY_HOLD);
            }
        }
        saveOrderAddressLog(beforeOrderDb.getId(), beforeOrderDb.getBillNo(), beforeOrderDb, addressDto, user, receiverAddressBefore, isRollback);
        saveOrderLogisticLog(beforeOrderDb.getId(), beforeOrderDb.getBillNo(), beforeOrderDb, houseDto, user);
        saveOrderWarehouseLog(beforeOrderDb.getId(), beforeOrderDb.getBillNo(), beforeOrderDb, houseDto, user);
        ValueHolderV14 valueHolderV14 = new ValueHolderV14(ResultCode.SUCCESS, "修改收货地址成功！");
        if (isPushPlatform) {
            try {
                OcBOrder bOrder = ocBOrderMapper.selectById(beforeOrderDb.getId());
                this.updateReceiveAddress(bOrder, user);
            } catch (Exception ex) {
                log.error(LogUtil.format("updateReceiveAddress平台同步异常,error={}"), Throwables.getStackTraceAsString(ex));
                valueHolderV14.setMessage("修改收货地址成功!，同步第三方平台失败");
            }
        }
        return valueHolderV14;
    }

    public void saveOrderAddressLog(Long orderId, String billNo, OcBOrder beforeOrderDb, ReceiverAddressDto addressDto, User user, String receiverAddressBefore, boolean isAutomatic) {
        String changeMessage = addressChange(beforeOrderDb, addressDto, receiverAddressBefore, isAutomatic);
        if (!StringUtils.isEmpty(changeMessage)) {
            omsOrderLogService.addUserOrderLog(orderId, billNo, OrderLogTypeEnum.ADDRESS_UPDATE.getKey(), changeMessage, "", "", user);
        }
    }

    public void saveOrderLogisticLog(Long orderId, String billNo, OcBOrder beforeOrderDb, WarehouseAndLogisticsDto houseDto, User user) {
        if (houseDto == null) {
            return;
        }
        String changeMessage = logisticsChange(beforeOrderDb, houseDto);
        if (StringUtils.isNotEmpty(changeMessage)) {
            omsOrderLogService.addUserOrderLog(orderId, billNo, OrderLogTypeEnum.SUBLOGISTICS_SERVICE.getKey(), changeMessage, "", "", user);
        }
    }

    public void saveOrderWarehouseLog(Long orderId, String billNo, OcBOrder beforeOrderDb, WarehouseAndLogisticsDto houseDto, User user) {
        if (houseDto == null) {
            return;
        }
        String changeMessage = houseChange(beforeOrderDb, houseDto);
        if (StringUtils.isNotEmpty(changeMessage)) {
            omsOrderLogService.addUserOrderLog(orderId, billNo, OrderLogTypeEnum.WAREHOUSE_SERVICE.getKey(), changeMessage, "", "", user);
        }
    }

    private String houseChange(OcBOrder beforeOrder, WarehouseAndLogisticsDto afterHouse) {
        String beforeHouseName = StringUtils.trimToEmpty(beforeOrder.getCpCPhyWarehouseEname());
//        String beforeStoreName = StringUtils.trimToEmpty(beforeOrder.getCpCStoreEname());

        String afterHouseName = StringUtils.trimToEmpty(afterHouse.getCpCPhyWarehouseEname());
        String afterStoreName = StringUtils.trimToEmpty(afterHouse.getCpCStoreEname());

        List<String> changeList = new ArrayList<>();
        if (StringUtils.isNotBlank(beforeHouseName) && !beforeHouseName.equalsIgnoreCase(afterHouseName)) {
            changeList.add(String.format("【实体仓】修改前为：%s id[%s] 修改后：%s id[%s]", beforeHouseName, beforeOrder.getCpCPhyWarehouseId(), afterHouseName, afterHouse.getCpCPhyWarehouseId()));
        }
        /*if (StringUtils.isNotBlank(beforeStoreName) && !beforeStoreName.equalsIgnoreCase(afterStoreName)) {
            changeList.add(String.format("【逻辑仓】修改前为：%s id[%s] 修改后：%s id[%s]", beforeStoreName, beforeOrder.getCpCStoreId(), afterStoreName, afterHouse.getCpCStoreId()));
        }*/
        return StringUtils.join(changeList, ",");
    }

    private String logisticsChange(OcBOrder beforeOrder, WarehouseAndLogisticsDto afterHouse) {
        String beforeLogisticsName = StringUtils.trimToEmpty(beforeOrder.getCpCLogisticsEname());
        String afterLogisticsName = StringUtils.trimToEmpty(afterHouse.getCpCLogisticsEname());

        List<String> changeList = new ArrayList<>();
        if (StringUtils.isNotBlank(beforeLogisticsName) && !beforeLogisticsName.equalsIgnoreCase(afterLogisticsName)) {
            changeList.add(String.format("【物流】修改前为：%s id[%s] 修改后：%s id[%s]", beforeLogisticsName, beforeOrder.getCpCLogisticsId(), afterLogisticsName, afterHouse.getCpCLogisticsId()));
        }
        return StringUtils.join(changeList, ",");
    }

    private String addressChange(OcBOrder beforeOrder, ReceiverAddressDto afterOrder, String receiverAddressBefore, boolean isAutomatic) {
        String beforeReceiverName = StringUtils.trimToEmpty(beforeOrder.getReceiverName());
        String beforeReceiverMobile = StringUtils.trimToEmpty(beforeOrder.getReceiverMobile());
        String beforeReceiverPhone = StringUtils.trimToEmpty(beforeOrder.getReceiverPhone());
        String beforeReceiverZip = StringUtils.trimToEmpty(beforeOrder.getReceiverZip());

        String afterReceiverName = StringUtils.trimToEmpty(afterOrder.getReceiverName());
        String afterReceiverMobile = StringUtils.trimToEmpty(afterOrder.getReceiverMobile());
        String afterReceiverPhone = StringUtils.trimToEmpty(afterOrder.getReceiverPhone());
        String afterReceiverZip = StringUtils.trimToEmpty(afterOrder.getReceiverZip());

        List<String> changeList = new ArrayList<>();
        if (!isAutomatic) {
            changeList.add("自助修改地址");
        }
        //必填项：【收货人、手机号】 如果没有修改，则不用打印日志
        if (StringUtils.isNotBlank(afterReceiverName) && !beforeReceiverName.equalsIgnoreCase(afterReceiverName)) {
            changeList.add(" 【收货人】修改前为：" + beforeReceiverName + " 修改后：" + afterReceiverName);
        }
        if (StringUtils.isNotBlank(afterReceiverMobile) && !beforeReceiverMobile.equalsIgnoreCase(afterReceiverMobile)) {
            changeList.add(" 【手机号】修改前为：" + beforeReceiverMobile + " 修改后：" + afterReceiverMobile);
        }
        if (!beforeReceiverPhone.equalsIgnoreCase(afterReceiverPhone)) {
            changeList.add(" 【电话号】修改前为：" + beforeReceiverPhone + " 修改后：" + afterReceiverPhone);
        }
        if (!beforeReceiverZip.equalsIgnoreCase(afterReceiverZip)) {
            changeList.add(" 【邮编】修改前为：" + beforeReceiverZip + " 修改后：" + afterReceiverZip);
        }
        String afterAddress = getFullAddress(afterOrder.getCpCRegionProvinceEname(), afterOrder.getCpCRegionCityEname(),
                afterOrder.getCpCRegionAreaEname(), afterOrder.getCpCRegionTownEname(), afterOrder.getReceiverAddress(), "");
        changeList.add(" 【地址】修改前为：" + receiverAddressBefore + " 修改后：" + afterAddress);
        return StringUtils.join(changeList, ",");
    }

    /**
     * 省市区合并一个字段
     *
     * @param prov    :
     * @param city    :
     * @param area    :
     * @param town    :
     * @param address :
     * @param sep     :可以是逗号也可以是空
     * @return java.lang.String
     */
    public String getFullAddress(String prov, String city, String area, String address, String town, String sep) {


        String fullAddress = Strings.EMPTY;
        if (prov != null && prov.trim().length() > 0) {
            fullAddress += prov + sep;
        }
        if (city != null && city.trim().length() > 0) {
            fullAddress += city + sep;
        }
        if (area != null && area.trim().length() > 0) {
            fullAddress += area + sep;
        }
        if (town != null && town.trim().length() > 0) {
            fullAddress += town + sep;
        }
        if (Strings.isNullOrEmpty(fullAddress)) {
            return address;
        }


        if (address == null || address.trim().length() == 0) {
            return StringUtils.stripEnd(fullAddress, sep);
        }
        return fullAddress + address;
    }
}
