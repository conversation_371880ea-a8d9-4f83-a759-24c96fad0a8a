package com.jackrain.nea.oc.oms.nums;


import lombok.Getter;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: jg.zhan
 * @Date: 2022/6/24 13:54
 * @Description: 零售发货单订单补发枚举类型
 */
public enum OcOrderBillResetShipTypeEnum {

    LOST_ORDER_RESET_SHIP(1, "丢单补发"),
    ORDER_WRONG_RESET_SHIP(2, "错发补发"),
    ORDER_MISS_RESET_SHIP(3, "漏发补发"),
    GIFT_RESET_SHIP(4, "赠品补发"),
    JITX_ORDER_RESET_SHIP(5, "JITX发货异常补发"),
    /**
     * 认养一头牛增加其他补发：王珍飞 0912
     */
    OTHER_ORDER_RESET_SHIP(6, "其他补发"),
    UNKNOW(-1, "未知");

    OcOrderBillResetShipTypeEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    @Getter
    private Integer type;

    @Getter
    private String name;

    public static OcOrderBillResetShipTypeEnum getRestShipEnumByType(Integer type) {
        for (OcOrderBillResetShipTypeEnum value : OcOrderBillResetShipTypeEnum.values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return UNKNOW;
    }
}


