package com.jackrain.nea.oc.oms.services.advance;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.shade.org.apache.commons.lang3.StringUtils;
import com.burgeon.r3.sg.channel.model.request.sale.SgBChannelAdvanceSaleBillReleaseRequest;
import com.burgeon.r3.sg.channel.model.request.sale.SgBChannelAdvanceSaleItemReleaseRequest;
import com.burgeon.r3.sg.channel.model.request.sale.SgBChannelAdvanceSaleReleaseRequest;
import com.burgeon.r3.sg.channel.model.result.sale.SgBChannelAdvanceSaleReleaseResult;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.model.enums.LogTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderHoldConst;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.taobao.TaoBaoOrderStatus;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.OcBOrderOffService;
import com.jackrain.nea.oc.oms.services.OcBOrderTheAuditService;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.util.DateConversionUtil;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.request.StCDepositPreSaleSinkRequest;
import com.jackrain.nea.st.model.request.StCOrderLabelRequest;
import com.jackrain.nea.st.model.table.StCDepositPreSaleSinkItemDO;
import com.jackrain.nea.st.model.table.StCDepositPreSaleSinkLogisticsDO;
import com.jackrain.nea.st.model.table.StCOrderLabelDO;
import com.jackrain.nea.st.model.table.StCOrderLabelItemDO;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: r3-oc-oms
 * @description: 预售解析业务
 * @author: liuwj
 * @create: 2021-06-09 16:42
 **/
@Slf4j
@Component
public class OmsOrderAdvanceParseService {

    @Autowired
    protected OmsOrderService orderService;

    @Autowired
    private OcBOrderTheAuditService ocBOrderTheAuditService;

    @Autowired
    private OcBOrderOffService ocBOrderOffService;

    @Autowired
    private SgRpcService sgRpcService;

    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private StRpcService stRpcService;


    /**
     * <AUTHOR>
     * @Date 13:32 2021/6/10
     * @Description 解析明细预计发货时效 淘宝有两种返回格式
     * 2021年03月28号24点前
     * 付款后n天内
     */
    public Date parseColumn(String estimateConTime, Date payTime) {
        //设置日期格式
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            if (estimateConTime.contains(AdvanceConstant.way_1)) {//满足第一种
                //处理后得到 2021-03-28 24:00:00
                String dateStr = estimateConTime.substring(0, estimateConTime.indexOf("点"))
                        .replace("年", "-")
                        .replace("月", "-")
                        .replace("号", " ").replace("日", " ") + ":00:59";
                Date newEstimateConTime = newEstimateConTime = df.parse(dateStr);
                //减一秒
                return DateConversionUtil.plusMinutes(newEstimateConTime, -1);
            } else if (estimateConTime.contains(AdvanceConstant.way_2)) {         //满足第二种
                //处理后的到 n;
                Integer dayNum = Integer.valueOf(estimateConTime.substring(estimateConTime.indexOf("后") + 1, estimateConTime.indexOf("天")));
                return DateConversionUtil.plusDays(payTime, dayNum);
            } else {   //都不满足
                log.error(LogUtil.format("云枢纽发货时间格式有误,estimateConTime: {}"), estimateConTime);
                return null;
            }
        } catch (ParseException e) {
            log.error(LogUtil.format("解析发货时间异常,estimateConTime:{},payTime:{}"), estimateConTime, payTime);
        }
        return null;
    }

    /**
     * <AUTHOR>
     * @Date 17:08 2021/6/10saveOrderInfo = {OcBOrderRelation@16749} "OcBOrderRelation(orderInfo=OcBOrder(id=755, billNo=OM21061500000006, sourceCode=1721202912298857842, cpCShopId=7, cpCShopEcode=00004, cpCShopTitle=张颖的测试店铺, cpCStoreId=null, cpCStoreEcode=null, cpCStoreEname=null, cpCPhyWarehouseId=null, cpCPhyWarehouseEcode=null, cpCPhyWarehouseEname=null, cpCCustomerId=null, cpCCustomerEcode=null, cpCCustomerEname=null, userId=null, userNick=t_1510275141791_0916, orderType=9, orderStatus=50, occupyStatus=0, suffixInfo=null, uniqueKey=17212029122988578421721202912299857842460635176544017212029123008578424606351765439, orderFlag=0, productAmt=2.0000, productDiscountAmt=0.0000, orderDiscountAmt=0.0000, adjustAmt=0.0000, shipAmt=0.0000, serviceAmt=0.0000, orderAmt=2.0000, receivedAmt=0, consignAmt=0, consignShipAmt=0, amtReceive=null, codAmt=0, operateAmt=0, jdReceiveAmt=0, jdSettleAmt=0, logisticsCost=0, isInvoice=0, invoiceHeader=null, invoiceContent=null, isGeninvoiceNotice=0, weight=0, isCalcweight=0, cpCLogisticsId=null, cpCLogisticsEcode=null, cpCLo"… View
     * @Description 解析订单是否是预售
     */
    @Transactional(rollbackFor = Exception.class)
    public void advanceParse(OcBOrderRelation saveOrderInfo, User operateUser, String orderStatus) {
        if (saveOrderInfo.getOrderInfo() == null || CollectionUtils.isEmpty(saveOrderInfo.getOrderItemList())) {
            return;
        }
        OcBOrder order = saveOrderInfo.getOrderInfo();
        List<OcBOrderItem> ocBOrderItemList = saveOrderInfo.getOrderItemList();
        String stepTradeStatus = order.getStatusPayStep();
        //订金未付尾款未付（不存在）
        if (TaoBaoOrderStatus.FRONT_NOPAID_FINAL_NOPAID.equalsIgnoreCase(stepTradeStatus)) {
            log.info(LogUtil.format("订单id->{},订金未付尾款未付", order.getId()), order.getId());
            return;
            //尾款未付 交易状态 = 付款以前，卖家或买家主动关闭交易 执行反审核，执行取消,先查询有没有 全渠道订单有就执行，没有就生成作废的单据
            //单据状态=已审核，配货中--->反审核+取消订单；单据状态=传WMS中--->保持待转换，不处理；单据状态=待审核/缺货---->取消订单
        } else if (TaoBaoOrderStatus.FRONT_PAID_FINAL_NOPAID.equalsIgnoreCase(stepTradeStatus) && TaoBaoOrderStatus.TRADE_CLOSED_BY_TAOBAO.equalsIgnoreCase(orderStatus)) {
            log.info(LogUtil.format("订单id->{},订金未付尾款未付,交易关闭，取消订单生成虚拟定金订单", order.getId()), order.getId());
            return;
            //尾款已付 执行发货服务
        } else if (TaoBaoOrderStatus.FRONT_PAID_FINAL_PAID.equalsIgnoreCase(stepTradeStatus)) {
            //尾款已付的也打标
            order.setDouble11PresaleStatus(1);
            order.setOrderType(OrderTypeEnum.TBA_PRE_SALE.getVal());
            order.setAdvanceType(AdvanceConstant.PLATFORM_DEPOSIT_PRE_SALE);
            for (OcBOrderItem ocBOrderItem : ocBOrderItemList) {
                ocBOrderItem.setAdvanceType(AdvanceConstant.PLATFORM_DEPOSIT_PRE_SALE);
            }
            return;
            //进行预售解析，可能包含非预售订单
        } else {
            //进行解析预售
//            parseItem(order, ocBOrderItemList, operateUser, orderStatus);
        }
    }

    /**
     * <AUTHOR>
     * @Date 11:16 2021/6/11
     * @Description 解析明细状态 一种是直接打预售标  另一种需要在明细维度判断
     */
    private void parseItem(OcBOrder order, List<OcBOrderItem> ocBOrderItemList, User operateUser, String orderStatus) {
        String logMsg = "OrderId=" + order.getId() + "开始预售解析。";
        omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(), OrderLogTypeEnum.ADVANCE_PARSE.getKey(), logMsg, "", null, operateUser);
        log.info(LogUtil.format("订单id->{}开始预售解析", order.getId()), order.getId());
        String stepTradeStatus = order.getStatusPayStep();
        //构建调用sg的参数
        SgBChannelAdvanceSaleBillReleaseRequest sgBChannelAdvanceSaleBillReleaseRequest = buildSgBChannelAdvanceSaleReleaseRequest(order, ocBOrderItemList, operateUser, false);
        //调用sg服务
        List<SgBChannelAdvanceSaleReleaseResult> saleReleaseResultList = sgRpcService.querySgBChannelAdvanceSale(sgBChannelAdvanceSaleBillReleaseRequest);
        Map<Date, List<OcBOrderItem>> itemMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(ocBOrderItemList)) {
            itemMap = ocBOrderItemList.stream().filter(i -> i.getEstimateConTime() != null).collect(Collectors.groupingBy(OcBOrderItem::getEstimateConTime));
        }
        Map<Long, SgBChannelAdvanceSaleReleaseResult> saleReleaseResultMap = new HashMap<>();
        try {
            if (CollectionUtils.isNotEmpty(saleReleaseResultList)) {
                saleReleaseResultMap = saleReleaseResultList.stream().filter(i -> i.getPsCSkuId() != null).collect(Collectors.toMap(SgBChannelAdvanceSaleReleaseResult::getPsCSkuId, i -> i, (va1, va2) -> va2));
                //把查出的所有预售活动赋值
                if (saleReleaseResultMap.size() > 0) {
                    for (OcBOrderItem ocBOrderItem : ocBOrderItemList) {
                        SgBChannelAdvanceSaleReleaseResult saleReleaseResult = saleReleaseResultMap.get(ocBOrderItem.getPsCSkuId());
                        if (saleReleaseResult != null) {
                            ocBOrderItem.setAdvanceSaleId(saleReleaseResult.getSgBChannelAdvanceSaleId());
                            ocBOrderItem.setAdvanceSaleBillNo(saleReleaseResult.getBillNo());
                            if (ocBOrderItem.getEstimateConTime() == null) {
                                ocBOrderItem.setEstimateConTime(saleReleaseResult.getOutDate());
                            }
                        }
                    }
                }
            }
            if (TaoBaoOrderStatus.FRONT_PAID_FINAL_NOPAID.equalsIgnoreCase(stepTradeStatus) && TaoBaoOrderStatus.WAIT_BUYER_PAY.equalsIgnoreCase(orderStatus)) {
                //定金预售直接打预售标
                for (OcBOrderItem ocBOrderItem : ocBOrderItemList) {
                    setOrderItemValue(ocBOrderItem, AdvanceConstant.PLATFORM_DEPOSIT_PRE_SALE);
                }
                order.setDouble11PresaleStatus(1);
                order.setOrderType(OrderTypeEnum.TBA_PRE_SALE.getVal());
                order.setAdvanceType(AdvanceConstant.PLATFORM_DEPOSIT_PRE_SALE);
            } else {
                //每个明细都有预计发货时间 全款预售 直接打标
                if (itemMap.size()>0 && itemMap.size()==ocBOrderItemList.size() ){
                    Date estimateConTimeMax = null;
                    for (OcBOrderItem ocBOrderItem : ocBOrderItemList) {
                        setOrderItemValue(ocBOrderItem,AdvanceConstant.PLATFORM_FULL_PRE_SALE);
                        estimateConTimeMax =estimateConTimeMax(estimateConTimeMax,ocBOrderItem.getEstimateConTime());
                    }
                    order.setDouble11PresaleStatus(1);
                    order.setOrderType(OrderTypeEnum.TBA_PRE_SALE.getVal());
                    order.setAdvanceType(AdvanceConstant.PLATFORM_FULL_PRE_SALE);
                    //预计发货时间取最大
                    order.setEstimateConTime(estimateConTimeMax);
                }else if (itemMap.size()>0 &&itemMap.size()<ocBOrderItemList.size()){ // 全款预售 部分明细预计发货时间
                    Date estimateConTimeMax = CollectionUtils.isNotEmpty(ocBOrderItemList)?ocBOrderItemList.get(0).getEstimateConTime():null;
                    setColumnData(order,ocBOrderItemList,AdvanceConstant.PLATFORM_FULL_PRE_SALE+",");
                    String statusStr ="";
                    for (OcBOrderItem ocBOrderItem : ocBOrderItemList) {
                        //判断是不是平台预售
                        if (AdvanceConstant.IS_EXIST_CON_TIME_0.equals(ocBOrderItem.getIsExistConTime())) {
                            setOrderItemValue(ocBOrderItem, AdvanceConstant.PLATFORM_FULL_PRE_SALE);
                            if (statusStr != null && !statusStr.contains(AdvanceConstant.PLATFORM_FULL_PRE_SALE)) {
                                statusStr += AdvanceConstant.PLATFORM_FULL_PRE_SALE + ",";
                            }
                        } else {
                            //满足
                            if (ocBOrderItem.getAdvanceSaleId() != null) {
                                setOrderItemValue(ocBOrderItem, AdvanceConstant.STORE_PRE_SALE);
                                if (statusStr != null && !statusStr.contains(AdvanceConstant.STORE_PRE_SALE)) {
                                    statusStr += AdvanceConstant.STORE_PRE_SALE + ",";
                                }
                            } else {
                                setOrderItemValue(ocBOrderItem, AdvanceConstant.ORDINARY_GOODS);
                                if (statusStr != null && !statusStr.contains(AdvanceConstant.ORDINARY_GOODS)) {
                                    statusStr += AdvanceConstant.ORDINARY_GOODS + ",";
                                }
                            }
                        }
                        estimateConTimeMax =estimateConTimeMax(estimateConTimeMax,ocBOrderItem.getEstimateConTime());
                    }
                    setOrderAdvanceFlag(order,statusStr);
                    //预计发货时间取最大
                    order.setEstimateConTime(estimateConTimeMax);
                }else { //明细没有一个元素有预计发货时间 店铺预售
                    //todo 去查店铺预售解析 如果全部都满足就是店铺预售 如果一个都没有 就普通商品
                    //有满足店铺预售
                    if (CollectionUtils.isNotEmpty(saleReleaseResultList)) {
                        setColumnData(order, ocBOrderItemList, "");
                        //都不满足
                    } else if (CollectionUtils.isEmpty(saleReleaseResultList)) {
                        return;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            String erroMsg = "OrderId=" + order.getId() + "预售解析异常,异常信息为" + e.getMessage();
            log.error(LogUtil.format(erroMsg));
            OcBOrder lOrder = new OcBOrder();
            lOrder.setId(order.getId());
            lOrder.setSysremark(erroMsg);
            omsOrderService.updateOrderInfo(lOrder);
            log.error(LogUtil.format("OrderId={}回滚预售活动数量"), order.getId());

            //构建调用sg的参数
            SgBChannelAdvanceSaleBillReleaseRequest sgBChannelAdvanceSaleBillReleaseRequestCallBack = buildSgBChannelAdvanceSaleReleaseRequest(order, ocBOrderItemList, operateUser, true);
            //调用sg服务
            List<SgBChannelAdvanceSaleReleaseResult> saleReleaseResultListCallBack = sgRpcService.querySgBChannelAdvanceSale(sgBChannelAdvanceSaleBillReleaseRequestCallBack);
            log.info(LogUtil.format("OrderId={}回滚预售活动数量返回结果是：{}", order.getId()), order.getId(), saleReleaseResultListCallBack);
        }

    }
    /**
     * description: 最大的预计付款时间
     * @Author:  liuwenjin
     * @Date 2021/12/27 5:56 下午
     */
    private Date estimateConTimeMax (Date estimateConTimeMax,Date estimateConTime){
        if (estimateConTime !=null){
            if (estimateConTimeMax ==null){
                estimateConTimeMax =estimateConTime;
            }else {
                if (estimateConTime.before(estimateConTimeMax)) {
                    estimateConTimeMax = estimateConTime;
                }
            }
        }
        log.info("estimateConTimeMax:{},estimateConTime{}",estimateConTimeMax,estimateConTime);
        return estimateConTimeMax;
    }

    /**
     * <AUTHOR>
     * @Date 14:06 2021/6/15
     * @Description 给订单，订单明细福不同的预售值
     */
    private void setColumnData(OcBOrder order, List<OcBOrderItem> ocBOrderItemList, String statusStr) {
        for (OcBOrderItem ocBOrderItem : ocBOrderItemList) {
            //满足
            if (ocBOrderItem.getAdvanceSaleId() != null) {
                setOrderItemValue(ocBOrderItem, AdvanceConstant.STORE_PRE_SALE);
                if (statusStr != null && !statusStr.contains(AdvanceConstant.STORE_PRE_SALE)) {
                    statusStr += AdvanceConstant.STORE_PRE_SALE + ",";
                }
            } else {
                setOrderItemValue(ocBOrderItem, AdvanceConstant.ORDINARY_GOODS);
                if (statusStr != null && !statusStr.contains(AdvanceConstant.ORDINARY_GOODS)) {
                    statusStr += AdvanceConstant.ORDINARY_GOODS + ",";
                }
            }
        }
        setOrderAdvanceFlag(order, statusStr);
    }

    /**
     * <AUTHOR>
     * @Date 19:18 2021/6/11
     * @Description 建造sg的入参  flag 是否回滚
     */
    private SgBChannelAdvanceSaleBillReleaseRequest buildSgBChannelAdvanceSaleReleaseRequest(OcBOrder order, List<OcBOrderItem> ocBOrderItemList, User operateUser, boolean flag) {
        SgBChannelAdvanceSaleBillReleaseRequest sgBChannelAdvanceSaleBillReleaseRequest = new SgBChannelAdvanceSaleBillReleaseRequest();
        SgBChannelAdvanceSaleReleaseRequest sgBChannelAdvanceSaleReleaseRequest = new SgBChannelAdvanceSaleReleaseRequest();
        List<SgBChannelAdvanceSaleItemReleaseRequest> sgBChannelAdvanceSaleItemReleaseRequestList = new ArrayList<>();
        sgBChannelAdvanceSaleReleaseRequest.setCpCShopId(order.getCpCShopId()); //店铺id
        sgBChannelAdvanceSaleReleaseRequest.setType(AdvanceConstant.TYPE_1); //接口类型
        sgBChannelAdvanceSaleBillReleaseRequest.setSaleReleaseRequest(sgBChannelAdvanceSaleReleaseRequest);
        sgBChannelAdvanceSaleBillReleaseRequest.setLoginUser(operateUser);
        for (OcBOrderItem ocBOrderItem : ocBOrderItemList) {
            SgBChannelAdvanceSaleItemReleaseRequest sgBChannelAdvanceSaleItemReleaseRequest = new SgBChannelAdvanceSaleItemReleaseRequest();
            sgBChannelAdvanceSaleItemReleaseRequest.setPsCSkuEcode(ocBOrderItem.getPsCSkuEcode());
            sgBChannelAdvanceSaleItemReleaseRequest.setPsCSkuId(ocBOrderItem.getPsCSkuId());
            sgBChannelAdvanceSaleItemReleaseRequest.setQtyOut(BigDecimal.ZERO);
            if (flag) {
                sgBChannelAdvanceSaleItemReleaseRequest.setQtySold(ocBOrderItem.getQty().negate());
            } else {
                sgBChannelAdvanceSaleItemReleaseRequest.setQtySold(ocBOrderItem.getQty());

            }
            sgBChannelAdvanceSaleItemReleaseRequestList.add(sgBChannelAdvanceSaleItemReleaseRequest);
        }
        sgBChannelAdvanceSaleBillReleaseRequest.setSaleItemReleaseRequests(sgBChannelAdvanceSaleItemReleaseRequestList);
        return sgBChannelAdvanceSaleBillReleaseRequest;
    }

    /**
     * <AUTHOR>
     * @Date 2021/6/11
     * @Description 给明细赋值 预计发货时间
     */
    private void setOrderItemValue(OcBOrderItem ocBOrderItem, String statusStr) {
        ocBOrderItem.setAdvanceType(statusStr);

    }

    /**
     * <AUTHOR>
     * @Date 15:18 2021/6/11
     * @Description 给主表赋值 打标
     */
    private void setOrderAdvanceFlag(OcBOrder order, String statusStr) {
        order.setDouble11PresaleStatus(1);
        order.setOrderType(OrderTypeEnum.TBA_PRE_SALE.getVal());
        order.setAdvanceType(statusStr.substring(0, statusStr.length() - 1));
    }

    /**
     * <AUTHOR>
     * @Date 19:23 2021/6/10
     * @Description 调用反审核
     */
    private boolean toExamineOrder(OcBOrder ocBOrder, User user) {
        try {
            Long id = ocBOrder.getId();
            ValueHolderV14 holderV14 = new ValueHolderV14();
            boolean isSuccess = ocBOrderTheAuditService.updateOrderInfo(user, holderV14, id, LogTypeEnum.BEFORE_SHIPMENT_REFUND_REVERSE_AUDIT.getType());
            if (isSuccess) {
                //反审核成功  将订单状态改为 待审核
                ocBOrder.setOrderStatus(OmsOrderStatus.UNCONFIRMED.toInteger());
            }
            return isSuccess;
        } catch (Exception e) {
            log.error(LogUtil.format("调用反审核失败.异常: {}"), Throwables.getStackTraceAsString(e));
            return false;
        }
    }

    /**
     * description: 定金预售预下沉解析
     *
     * @Author: liuwenjin
     * @Date 2021/9/24 2:49 下午
     */
    public void OmsOrderAdvanceSinkParse(OcBOrderRelation ocBOrderRelation, User operateUser) {
        OcBOrder ocBOrder = ocBOrderRelation.getOrderInfo();
        try {
            OcBOrder order = new OcBOrder();
            //第一次进来清空物流信息
            order.setCpCLogisticsId(0L);
            order.setCpCLogisticsEname("");
            order.setCpCLogisticsEcode("");
            if (checkIsDepositPreSale(ocBOrder)) {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("order={}开始定金预售预下沉解析{}", order.getId()), order.getId(), JSONObject.toJSONString(ocBOrderRelation));
                }
                StCDepositPreSaleSinkRequest stCDepositPreSaleSinkRequest = filterStCDepositPreSaleSinkRequest(ocBOrder);
                if (checkStrategy(stCDepositPreSaleSinkRequest, ocBOrderRelation)) {
                    order.setId(ocBOrder.getId());
                    order.setSuggestPresinkStatus(TaoBaoOrderStatus.SUGGEST_PRESINK_STATUS_Y);
                    order.setActualPresinkStatus(TaoBaoOrderStatus.ACTUAL_PRESINK_STATUS_NOT_NOTIFIED);
                    order.setModifierid(Long.valueOf(operateUser.getId()));
                    order.setModifierename(operateUser.getEname());
                    order.setModifieddate(new Date(System.currentTimeMillis()));
                    //满足修改物流公司
                    checkLogistics(stCDepositPreSaleSinkRequest, ocBOrderRelation.getOrderInfo(), order);
                    String logMsg = "order=" + order.getId() + "预下沉策略生效！";
                    omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(), OrderLogTypeEnum.ADVANCE_SINK_STRATEGY.getKey(), logMsg, "", null, operateUser);
                } else {
                    order.setId(ocBOrder.getId());
                    order.setSuggestPresinkStatus(TaoBaoOrderStatus.SUGGEST_PRESINK_STATUS_N);
                    order.setActualPresinkStatus(null);
                    order.setModifierid(Long.valueOf(operateUser.getId()));
                    order.setModifierename(operateUser.getEname());
                    order.setModifieddate(new Date(System.currentTimeMillis()));
                    omsOrderService.updateOrderInfo(order);
                }
            } else {
                if (log.isInfoEnabled()) {
                    log.info(LogUtil.format("order={}不满足定金预售 double11PresaleStatus ={}, advanceType={} ,statusPayStep={}", order.getId()),
                            ocBOrder.getId(), ocBOrder.getDouble11PresaleStatus(), ocBOrder.getAdvanceType(), ocBOrder.getStatusPayStep());
                }
            }
            omsOrderService.updateOrderInfo(order);
        } catch (Exception e) {
            String logMsg = "order=" + ocBOrder.getId() + "预下沉策略解析异常！";
            log.error(LogUtil.format("orderId订单{}，预下沉策略解析异常 {}", ocBOrder.getId()), ocBOrder.getId(), Throwables.getStackTraceAsString(e));
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.ADVANCE_SINK_STRATEGY.getKey(), logMsg, "", null, operateUser);
        }

    }

    /**
     * 订单打标策略解析
     * @param ocBOrderRelation
     * @param operateUser
     */
    public void OmsOrderAdvanceOrderLabel(OcBOrderRelation ocBOrderRelation, User operateUser){
        OcBOrder ocBOrder =ocBOrderRelation.getOrderInfo();
        String logMsg = "OrderId="+ocBOrder.getId()+"开始订单打标策略解析。";
        omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.LABLEPARSE.getKey(), logMsg, "", null, operateUser);
        log.info("订单id->{}开始预售解析",ocBOrder.getId());
        try{
            List<StCOrderLabelRequest> stCOrderLabelRequests = filterStCOrderLabelRequest(ocBOrder);
            if (log.isDebugEnabled()){
                log.debug(" 订单打标策略解析结果 {}", JSONObject.toJSONString(stCOrderLabelRequests));
            }
            checkOrderLabel(stCOrderLabelRequests,ocBOrderRelation,operateUser);
        }catch (Exception e){
            log.error(" orderId订单{}，订单打标策略解析异常 {}",ocBOrder.getId(), Throwables.getStackTraceAsString(e)); }
    }

    /**
     * description:判断订单是否满足物流物流
     * @Author:  liuwenjin
     * @Date 2021/10/18 4:55 下午
     */
    private void checkLogistics(StCDepositPreSaleSinkRequest stCDepositPreSaleSinkRequest, OcBOrder ocBOrder, OcBOrder order) {
        if (stCDepositPreSaleSinkRequest == null) {
            return;
        }
        List<StCDepositPreSaleSinkLogisticsDO> stCDepositPreSaleSinkItemDOList = stCDepositPreSaleSinkRequest.getStCDepositPreSaleSinkLogisticsDOList();
        if (CollectionUtils.isEmpty(stCDepositPreSaleSinkRequest.getStCDepositPreSaleSinkLogisticsDOList())) {
            return;
        } else {
            for (StCDepositPreSaleSinkLogisticsDO stCDepositPreSaleSinkLogisticsDO : stCDepositPreSaleSinkItemDOList) {
                if (stCDepositPreSaleSinkLogisticsDO.getCpCPhyWarehouseId().equals(ocBOrder.getCpCPhyWarehouseId())) {
                    order.setCpCLogisticsId(stCDepositPreSaleSinkLogisticsDO.getCpCLogisticsId());
                    order.setCpCLogisticsEcode(stCDepositPreSaleSinkLogisticsDO.getCpCLogisticsEcode());
                    order.setCpCLogisticsEname(stCDepositPreSaleSinkLogisticsDO.getCpCLogisticsEname());
                    return;
                }
            }
        }
        return;
    }

    /**
     * description:
     *
     * @Author: liuwenjin 判断订单是否满足下沉策略
     * @Date 2021/9/24 3:51 下午
     */
    private boolean checkStrategy(StCDepositPreSaleSinkRequest stCDepositPreSaleSinkRequest, OcBOrderRelation ocBOrderRelation) {
        if (stCDepositPreSaleSinkRequest == null) {
            return false;
        }
        List<StCDepositPreSaleSinkItemDO> stCDepositPreSaleSinkItemDOList = stCDepositPreSaleSinkRequest.getStCDepositPreSaleSinkItemDOList();
        //没有明细默认是满足
        if (CollectionUtils.isEmpty(stCDepositPreSaleSinkItemDOList)) {
            return true;
        } else {
            List<OcBOrderItem> orderItemList = ocBOrderRelation.getOrderItemList();
            for (StCDepositPreSaleSinkItemDO stCDepositPreSaleSinkItemDO : stCDepositPreSaleSinkItemDOList) {
                for (OcBOrderItem ocBOrderItem : orderItemList) {
                    if (AdvanceConstant.MSKU.equals(stCDepositPreSaleSinkItemDO.getRulesRecognition()) && stCDepositPreSaleSinkItemDO.getContent().equals(ocBOrderItem.getPsCSkuEcode())) {
                        return false;
                    }
                    if (AdvanceConstant.PTSKU.equals(stCDepositPreSaleSinkItemDO.getRulesRecognition()) && stCDepositPreSaleSinkItemDO.getContent().equals(ocBOrderItem.getSkuNumiid())) {
                        return false;
                    }
                    if (AdvanceConstant.TPID.equals(stCDepositPreSaleSinkItemDO.getRulesRecognition()) && stCDepositPreSaleSinkItemDO.getContent().equals(ocBOrderItem.getNumIid())) {
                        return false;
                    }
                    if (AdvanceConstant.MPRO.equals(stCDepositPreSaleSinkItemDO.getRulesRecognition()) && stCDepositPreSaleSinkItemDO.getContent().equals(ocBOrderItem.getPsCProEcode())) {
                        return false;
                    }
                }
            }
        }
        return true;
    }

    /**
     * description:
     *
     * @Author: YCH 判断订单是否满足订单打标策略
     * @Date 2021/9/24 3:51 下午
     */
    private void checkOrderLabel(List<StCOrderLabelRequest> stCOrderLabelRequests, OcBOrderRelation ocBOrderRelation, User operateUser) {
        if (CollectionUtils.isEmpty(stCOrderLabelRequests)) {
            return;
        }
        OcBOrder orderInfo = ocBOrderRelation.getOrderInfo();

        for (int j = 0; j < stCOrderLabelRequests.size(); j++) {
            StCOrderLabelRequest request = stCOrderLabelRequests.get(j);
            StringBuffer labelId = new StringBuffer();
            StringBuffer labelEname = new StringBuffer();
            Boolean flag = true;
            List<StCOrderLabelItemDO> stCOrderLabelItemDOList = request.getStCOrderLabelItemDOList();
            StCOrderLabelDO stCOrderLabelDO = request.getStCOrderLabelDO();
            //没有明细默认是不满足
            if (CollectionUtils.isEmpty(stCOrderLabelItemDOList)){
               continue;
            }else {
                List<OcBOrderItem> orderItemList = ocBOrderRelation.getOrderItemList();
                Map<String, String> labelMap = new HashMap<>();
                BigDecimal qty = BigDecimal.ZERO;
                for (StCOrderLabelItemDO stCOrderLabelItemDO : stCOrderLabelItemDOList){
                    labelMap.put(stCOrderLabelItemDO.getRulesRecognition(), stCOrderLabelItemDO.getContent());
                }
                for (OcBOrderItem ocBOrderItem : orderItemList) {
                    qty = qty.add(ocBOrderItem.getQty());
                }
                for (StCOrderLabelItemDO stCOrderLabelItemDO : stCOrderLabelItemDOList) {
                    //扫描明细是否符合打标策略
                    if (AdvanceConstant.MSKU.equals(stCOrderLabelItemDO.getRulesRecognition())) {
                        for (int i = 0; i < orderItemList.size(); i++) {
                            OcBOrderItem item = orderItemList.get(i);
                            if (stCOrderLabelItemDO.getContent().equals(item.getPsCSkuEcode())) {
                                extract(labelId, labelEname, stCOrderLabelDO);
                                break;
                            } else if (i == orderItemList.size() - 1) {
                                flag = false;
                            }
                        }

                    }
                    if (AdvanceConstant.PTSKU.equals(stCOrderLabelItemDO.getRulesRecognition())) {
                        for (int i = 0; i < orderItemList.size(); i++) {
                            OcBOrderItem item = orderItemList.get(i);
                            if (stCOrderLabelItemDO.getContent().equals(item.getSkuNumiid())) {
                                extract(labelId, labelEname, stCOrderLabelDO);
                                break;
                            } else if (i == orderItemList.size() - 1) {
                                flag = false;
                                break;
                            }
                        }
                    }
                    if (AdvanceConstant.TPID.equals(stCOrderLabelItemDO.getRulesRecognition())) {

                        for (int i = 0; i < orderItemList.size(); i++) {
                            OcBOrderItem item = orderItemList.get(i);
                            if (stCOrderLabelItemDO.getContent().equals(item.getNumIid())) {
                                extract(labelId, labelEname, stCOrderLabelDO);
                                break;
                            } else if (i == orderItemList.size() - 1) {
                                flag = false;
                                break;
                            }
                        }
                    }
                    if (AdvanceConstant.MPRO.equals(stCOrderLabelItemDO.getRulesRecognition())) {
                        for (int i = 0; i < orderItemList.size(); i++) {
                            OcBOrderItem item = orderItemList.get(i);
                            if (stCOrderLabelItemDO.getContent().equals(item.getPsCProEcode())) {
                                extract(labelId, labelEname, stCOrderLabelDO);
                                break;
                            } else if (i == orderItemList.size() - 1) {
                                flag = false;
                                break;
                            }
                        }
                    }
                    //主表信息是否符合打标策略
                    String phone = orderInfo.getReceiverMobile();
                    if (StringUtils.isBlank(orderInfo.getReceiverMobile())) {
                        phone = orderInfo.getReceiverPhone();
                    }
                    if (AdvanceConstant.PNUM.equals(stCOrderLabelItemDO.getRulesRecognition()) ) {
                        if (stCOrderLabelItemDO.getContent().equals(phone)){
                            extract(labelId, labelEname, stCOrderLabelDO);
                        }else {
                            flag = false;
                            break;
                        }
                    }
                    if (AdvanceConstant.MNAM.equals(stCOrderLabelItemDO.getRulesRecognition())) {
                        if (stCOrderLabelItemDO.getContent().equals(orderInfo.getReceiverName())){
                            extract(labelId, labelEname, stCOrderLabelDO);
                        }else {
                            flag = false;
                            break;
                        }
                    }
                    if (AdvanceConstant.ARESS.equals(stCOrderLabelItemDO.getRulesRecognition())) {
                        if (stCOrderLabelItemDO.getContent().equals(orderInfo.getReceiverAddress())){
                            extract(labelId, labelEname, stCOrderLabelDO);
                        }else {
                            flag = false;
                            break;
                        }
                    }
                    if (AdvanceConstant.NICK.equals(stCOrderLabelItemDO.getRulesRecognition())) {
                        if (stCOrderLabelItemDO.getContent().equals(orderInfo.getUserNick())){
                            extract(labelId, labelEname, stCOrderLabelDO);
                        }else {
                            flag = false;
                            break;
                        }
                    }
                }
                if (!flag){
                    continue;
                }
                if (labelMap.containsKey(AdvanceConstant.AMT_GE)) {
                    if (labelMap.containsKey(AdvanceConstant.AMT_LE)) {
                        if (new BigDecimal(labelMap.get(AdvanceConstant.AMT_GE)).compareTo(orderInfo.getOrderAmt()) <= 0 && new BigDecimal(labelMap.get(AdvanceConstant.AMT_LE)).compareTo(orderInfo.getOrderAmt()) >= 0) {
                            extract(labelId, labelEname, stCOrderLabelDO);
                        }else {
                            continue;
                        }
                    } else if (new BigDecimal(labelMap.get(AdvanceConstant.AMT_GE)).compareTo(orderInfo.getOrderAmt()) <= 0) {
                        extract(labelId, labelEname, stCOrderLabelDO);
                    }else {
                        continue;
                    }
                }
                if (labelMap.containsKey(AdvanceConstant.AMT_LE)) {
                    if (labelMap.containsKey(AdvanceConstant.AMT_GE)) {
                        if (new BigDecimal(labelMap.get(AdvanceConstant.AMT_GE)).compareTo(orderInfo.getOrderAmt()) <= 0 && new BigDecimal(labelMap.get(AdvanceConstant.AMT_LE)).compareTo(orderInfo.getOrderAmt()) >= 0) {
                            extract(labelId, labelEname, stCOrderLabelDO);
                        }else {
                            continue;
                        }
                    } else if (new BigDecimal(labelMap.get(AdvanceConstant.AMT_LE)).compareTo(orderInfo.getOrderAmt()) >= 0) {
                        extract(labelId, labelEname, stCOrderLabelDO);
                    }else {
                        continue;
                    }
                }

                if (labelMap.containsKey(AdvanceConstant.SKUQTY_GE)) {
                    if (labelMap.containsKey(AdvanceConstant.SKUQTY_LE)){
                        if (new BigDecimal(labelMap.get(AdvanceConstant.SKUQTY_GE)).compareTo(qty) <= 0 && new BigDecimal(labelMap.get(AdvanceConstant.SKUQTY_LE)).compareTo(qty) >= 0){
                            extract(labelId, labelEname, stCOrderLabelDO);
                        }else {
                            continue;
                        }
                    }else if(new BigDecimal(labelMap.get(AdvanceConstant.SKUQTY_GE)).compareTo(qty) <= 0 ){
                        extract(labelId, labelEname, stCOrderLabelDO);
                    }else {
                        continue;
                    }
                }
                if (labelMap.containsKey(AdvanceConstant.SKUQTY_LE)){
                    if (labelMap.containsKey(AdvanceConstant.SKUQTY_GE)){
                        if (new BigDecimal(labelMap.get(AdvanceConstant.SKUQTY_GE)).compareTo(qty) <= 0 && new BigDecimal(labelMap.get(AdvanceConstant.SKUQTY_LE)).compareTo(qty) >= 0){
                            extract(labelId, labelEname, stCOrderLabelDO);
                        }else {
                            continue;
                        }
                    }else if(new BigDecimal(labelMap.get(AdvanceConstant.SKUQTY_LE)).compareTo(qty) >= 0 ){
                        extract(labelId, labelEname, stCOrderLabelDO);
                    }else {
                        continue;
                    }
                }
                Map<String, List<OcBOrderItem>> collect = orderItemList.stream().collect(Collectors.groupingBy(OcBOrderItem::getPsCProEcode));
                if (labelMap.containsKey(AdvanceConstant.SKU_GE)){
                    if (labelMap.containsKey(AdvanceConstant.SKU_LE)){
                        if (Integer.parseInt(labelMap.get(AdvanceConstant.SKU_GE)) <= collect.size()&& Integer.parseInt(labelMap.get(AdvanceConstant.SKU_LE)) >= collect.size()){
                            extract(labelId, labelEname, stCOrderLabelDO);
                        }else {
                            continue;
                        }
                    }else if(Integer.parseInt(labelMap.get(AdvanceConstant.SKU_GE)) <= collect.size()){
                        extract(labelId, labelEname, stCOrderLabelDO);
                    }else {
                        continue;
                    }
                }
                if (labelMap.containsKey(AdvanceConstant.SKU_LE)){
                    if (labelMap.containsKey(AdvanceConstant.SKU_GE)){
                        if (Integer.parseInt(labelMap.get(AdvanceConstant.SKU_GE)) <= collect.size()&& Integer.parseInt(labelMap.get(AdvanceConstant.SKU_LE)) >= collect.size()){
                            extract(labelId, labelEname, stCOrderLabelDO);
                        }else {
                            continue;
                        }
                    }else if(Integer.parseInt(labelMap.get(AdvanceConstant.SKU_LE)) >= collect.size()){
                        extract(labelId, labelEname, stCOrderLabelDO);
                    }else {
                        continue;
                    }
                }
                if (StringUtils.isNotEmpty(String.valueOf(labelId)) && flag){
                    if (StringUtils.isNotEmpty(orderInfo.getStCCustomLabelId())){
                        String customLabelId = orderInfo.getStCCustomLabelId();
                        String cCustomLabelEname = orderInfo.getStCCustomLabelEname();

                        String[] split = StringUtils.split(labelId.toString(), ",");
                        for (String s : split){
                            if (!customLabelId.contains(s)){
                                customLabelId = customLabelId  + s + ",";
                            }
                        }
                        cCustomLabelEname = cCustomLabelEname + ","+labelEname;
                        orderInfo.setStCCustomLabelId(String.valueOf(customLabelId));
                        orderInfo.setStCCustomLabelEname(cCustomLabelEname);
                    }else {
                        orderInfo.setStCCustomLabelId(String.valueOf(labelId));
                        orderInfo.setStCCustomLabelEname(String.valueOf(labelEname));
                    }

                }
            }
        }

        log.info(" 执行完订单策略返参:{}",JSON.toJSONString(ocBOrderRelation));
    }

    private void extract(StringBuffer labelId, StringBuffer labelEname, StCOrderLabelDO stCOrderLabelDO) {
//        if (StringUtils.isNotEmpty(String.valueOf(labelId)) && !labelId.toString().contains(stCOrderLabelDO.getStCCustomLabelIds())) {
//            //labelId.append(stCOrderLabelDO.getStCCustomLabelId());
//            labelId.append(",");
//            labelEname.append(",");
//            labelEname.append(stCOrderLabelDO.getStCCustomLabelEname());
//        } else if (StringUtils.isEmpty(String.valueOf(labelId))){
//            labelId.append(",");
//            //labelId.append(stCOrderLabelDO.getStCCustomLabelId());
//            labelId.append(",");
//            labelEname.append(stCOrderLabelDO.getStCCustomLabelEname());
//        }
    }

    /**
     * description:
     *
     * @param ocBOrder
     * @Author: 查询满足符合策略的
     * @Date 2021/9/24 3:23 下午
     */
    private StCDepositPreSaleSinkRequest filterStCDepositPreSaleSinkRequest(OcBOrder ocBOrder) {
        List<StCDepositPreSaleSinkRequest> stCDepositPreSaleSinkRequestList = stRpcService.queryDepositPreSaleSinkList(ocBOrder.getCpCPhyWarehouseId());
        if (CollectionUtils.isNotEmpty(stCDepositPreSaleSinkRequestList)) {
            Date createTime = ocBOrder.getOrderDate();
            Date payTime = ocBOrder.getPayTime();
            // 2.根据hold单策略 生成hold单明细，订单主表增加释放时间，修改is_interecept字段标识
            // 筛选出有效的策略（时间类型 下单时间 判断下单是否在方案生效时间内， 时间类型：支付时间 判断支付时间是否在方案生效时间内）
            List<StCDepositPreSaleSinkRequest> filterList = stCDepositPreSaleSinkRequestList.stream()
                    .filter(o -> (OcBOrderHoldConst.ORDER_DATE.equals(o.getStCDepositPreSaleSinkDO().getDayType()) && createTime.before(o.getStCDepositPreSaleSinkDO().getEndTime()) && createTime.after(o.getStCDepositPreSaleSinkDO().getBeginTime()))
                            || (OcBOrderHoldConst.PAY_TIME.equals(o.getStCDepositPreSaleSinkDO().getDayType()) && payTime.before(o.getStCDepositPreSaleSinkDO().getEndTime()) && payTime.after(o.getStCDepositPreSaleSinkDO().getBeginTime())))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filterList)) {
                log.info(LogUtil.format("order={}满足有效的策略为{}"), ocBOrder.getId(), JSON.toJSONString(filterList));
                return filterList.get(0);
            }
        }
        return null;
    }

    /**
     * 筛选满足订单打标策略
     */
    private List<StCOrderLabelRequest> filterStCOrderLabelRequest(OcBOrder ocBOrder) {
        List<StCOrderLabelRequest> stCOrderLabelRequests = stRpcService.queryCOrderLabelList(ocBOrder.getCpCShopId());
        if (CollectionUtils.isNotEmpty(stCOrderLabelRequests)) {
            Date createTime = ocBOrder.getOrderDate();
            Date payTime = ocBOrder.getPayTime();
            // 筛选出有效的策略（时间类型 下单时间 判断下单是否在方案生效时间内， 时间类型：支付时间 判断支付时间是否在方案生效时间内）
            List<StCOrderLabelRequest> filterList = stCOrderLabelRequests.stream()
                    .filter(o -> (OcBOrderHoldConst.ORDER_DATE.equals(Integer.valueOf(o.getStCOrderLabelDO().getTimeType())) && createTime.before(o.getStCOrderLabelDO().getEndTime()) && createTime.after(o.getStCOrderLabelDO().getBeginTime()))
                            || (OcBOrderHoldConst.PAY_TIME.equals(Integer.valueOf(o.getStCOrderLabelDO().getTimeType())) && payTime.before(o.getStCOrderLabelDO().getEndTime()) && payTime.after(o.getStCOrderLabelDO().getBeginTime())))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filterList)) {
                log.info(LogUtil.format("订单order={}满足有效的订单打标策略为{}"), ocBOrder.getId(), JSON.toJSONString(filterList));
                return filterList;
            }
        }
        return null;
    }

    /**
     * description:判断订单是否满足定金预售预下沉解析
     *
     * @Author: liuwenjin
     * @Date 2021/9/24 2:56 下午
     */
    public boolean checkIsDepositPreSale(OcBOrder ocBOrder) {
        return Optional.ofNullable(ocBOrder.getDouble11PresaleStatus()).orElse(0).intValue() == 1
                && AdvanceConstant.PLATFORM_DEPOSIT_PRE_SALE.equals(ocBOrder.getAdvanceType())
                && TaoBaoOrderStatus.FRONT_PAID_FINAL_NOPAID.equalsIgnoreCase(ocBOrder.getStatusPayStep());
    }
}
