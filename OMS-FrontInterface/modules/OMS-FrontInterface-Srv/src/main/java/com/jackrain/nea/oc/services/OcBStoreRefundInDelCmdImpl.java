package com.jackrain.nea.oc.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBStoreRefundInDelCmd;
import com.jackrain.nea.oc.oms.services.OcBStoreRefundInService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * className: OcBStoreRefundInSaveCmdImpl
 * description:门店退货入库删除
 *
 * <AUTHOR>
 * create: 2021-07-01
 * @since JDK 1.8
 */
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBStoreRefundInDelCmdImpl extends CommandAdapter implements OcBStoreRefundInDelCmd {

    @Autowired
    private OcBStoreRefundInService storeRefundInService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return storeRefundInService.delete(session);
    }

}
