package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.RefundPaymentCmd;
import com.jackrain.nea.oc.oms.model.table.OcBReturnAfSend;
import com.jackrain.nea.oc.oms.services.RefundFormAfterDeliveryService;
import com.jackrain.nea.oc.oms.util.ExportUtil;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;

/**
 * 发货后退款单 打款
 *
 * @author: 江家雷
 * @since: 2020/08/14
 * create at :2020/08/14 10:26
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class RefundPaymentCmdImpl extends CommandAdapter implements RefundPaymentCmd {
    @Autowired
    private RefundFormAfterDeliveryService refundFormAfterDeliveryService;
    @Autowired
    private ExportUtil exportUtil;

    @Value("${r3.oss.endpoint}")
    private String endpoint;

    @Value("${r3.oss.accessKey}")
    private String accessKeyId;

    @Value("${r3.oss.secretKey}")
    private String accessKeySecret;

    @Value("${r3.oss.bucketName}")
    private String bucketName;

    @Value("${r3.oss.timeout}")
    private String timeout;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        ValueHolder vh = new ValueHolder();
        User user = querySession.getUser();
        HashMap map = new HashMap();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        JSONArray ids = param.getJSONArray("ids");
        if (ids.isEmpty()) {
            vh.put("code", ResultCode.FAIL);
            vh.put("message", "请勾选一条数据来操作");
            return vh;
        }
        JSONArray array = new JSONArray();
        for (int i = 0; i < ids.size(); i++) {
            array.add(ids.getLong(i));
        }
        return refundFormAfterDeliveryService.payment(array, user);
    }

    @Override
    public ValueHolderV14 batchUpdateOcBReturnAfSend(List<OcBReturnAfSend> list, User user) {
        return refundFormAfterDeliveryService.batchUpdateOcBReturnAfSend(list, user);
    }

    @Override
    public ValueHolderV14 downloadTemp() {
        ValueHolderV14 holderV14 = new ValueHolderV14(ResultCode.SUCCESS, "导入打款结果模板下载成功！");
        exportUtil.setEndpoint(this.endpoint);
        exportUtil.setAccessKeyId(this.accessKeyId);
        exportUtil.setAccessKeySecret(this.accessKeySecret);
        exportUtil.setBucketName(this.bucketName);
        if (StringUtils.isEmpty(timeout)) {
            //如果获取不到apllo配置参数，设置默认过期时间为30分钟
            timeout = "1800000";
        }
        exportUtil.setTimeout(timeout);
        /**
         *  拼接Excel主表sheet表头字段
         * */
        String orderNames[] = {"单据编号", "打款状态", "失败原因"};
        User user = SystemUserResource.getRootUser();
        List orderN = Lists.newArrayList(orderNames);
        //生成Excel
        XSSFWorkbook hssfWorkbook = new XSSFWorkbook();
        String sdd = "";
        exportUtil.executeOrderSheet(hssfWorkbook, "打款结果", "", orderN, Lists.newArrayList(), Lists.newArrayList(), false);
        sdd = exportUtil.saveFileAndPutOss(hssfWorkbook, "打款结果导入模板", user, "OSS-Bucket/EXPORT/OC_B_RETURN_AF_SEND/");
        holderV14.setData(sdd);
        return holderV14;
    }
}
