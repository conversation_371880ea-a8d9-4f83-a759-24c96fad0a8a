package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * @author: 夏继超
 * @since: 2019/8/28
 * create at : 2019/8/28 15:44
 */
@TableName(value = "oc_b_return_order_exchange")
@Data
public class OcBReturnOrderExchangeExt extends OcBReturnOrderExchange {
    @TableField(exist = false)
    @JSONField(name = "SEX_ENAME")
    private String sexEname;

    @TableField(exist = false)
    private JSONObject selected;
}
