package com.jackrain.nea.oc.oms.model.result;


import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderDefect;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderExchangeExt;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefundExt;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @author: 郑立轩
 * @since: 2019/3/13
 * create at : 2019/3/13 11:03
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ChangingOrReFundingDetailResult implements Serializable {
    private QueryReturnOrderResult returnOrders;
    private List<OcBReturnOrderExchangeExt> exchangeDtoList;
    private List<OcBReturnOrderRefundExt> refundDtoList;
    private List<OcBReturnOrderDefect> orderDefects;
    private Integer totalEX;
    private Integer totalRE;
    private Integer pageEX;
    private Integer pageRE;
}
