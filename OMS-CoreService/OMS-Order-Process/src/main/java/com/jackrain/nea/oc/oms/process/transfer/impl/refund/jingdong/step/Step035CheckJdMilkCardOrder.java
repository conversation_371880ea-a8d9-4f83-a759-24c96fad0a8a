package com.jackrain.nea.oc.oms.process.transfer.impl.refund.jingdong.step;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpJingdongRefundRelation;
import com.jackrain.nea.oc.oms.model.relation.OcBReturnOrderRelation;
import com.jackrain.nea.oc.oms.model.resources.SysNotesConstant;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.process.transfer.impl.refund.jingdong.util.JdRefundUtil;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Descroption 校验是否是奶卡订单
 * <AUTHOR>
 * @Date 2019/4/28 18:53
 */
@Step(order = 35, description = "校验是否是奶卡订单")
@Slf4j
@Component
public class Step035CheckJdMilkCardOrder extends BaseJingdongRefundProcessStep
        implements IOmsOrderProcessStep<IpJingdongRefundRelation> {
    @Override
    public ProcessStepResult<IpJingdongRefundRelation> startProcess(IpJingdongRefundRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        IpBJingdongRefund jingdongRefund = orderInfo.getJingdongRefund();
        OcBOrder ocBOrder = orderInfo.getOcBOrder();
        Long afsserviceid = jingdongRefund.getAfsserviceid();
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("京东退单参数.{}","Step035CheckJdMilkCardOrder",orderInfo.getOrderNo()), JSONObject.toJSONString(orderInfo));
        }
        try {
            if (JdRefundUtil.RefundStatus.CANCEL.getCode().equals(jingdongRefund.getAfsservicestatusname())
                    || JdRefundUtil.RefundStatus.EXAMINE_CLOSE.getCode().equals(jingdongRefund.getAfsservicestatusname())) {
                ipJingdongRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(), "状态为取消,转换完成", jingdongRefund);
                return new ProcessStepResult<>(StepStatus.FINISHED, "单据" + orderInfo.getOrderNo() + "转换完成");
            }
            List<OcBOrderItem> orderItems = orderInfo.getOcBOrderItems();
            boolean isMilkCardOrder = orderInfo.getMilkCardOrder();
            if(!isMilkCardOrder){
                return new ProcessStepResult<>(StepStatus.SUCCESS);
            }
            //如果是奶卡订单，生成已发货退款单，如果已存在则更新
            ipJingdongRefundService.foundRefundSlipAfterNoUpdate(orderInfo,jingdongRefund, operateUser);
            orderInfo.setOcBOrderItems(null);
            //是否存在赠品，如果不存在赠品则转换结束
            if(CollectionUtils.isNotEmpty(orderInfo.getGiftItemList())){
                OcBOrder ocBGiftOrder = ocBOrderMapper.selectById(orderInfo.getGiftItemList().get(0).getOcBOrderId());
                orderInfo.setOcBOrder(ocBGiftOrder);
                List<Long> existReturnOrder = omsReturnOrderService.isExistReturnOrder(afsserviceid + "");
                if(CollectionUtils.isEmpty(existReturnOrder)){
                    //生成赠品的退换货单
                    List<OcBReturnOrderRelation> returnOrderRelations = ipJingdongRefundService.buildJingdongReturnOrders(orderInfo);
                    if (returnOrderRelations == null) {
                        ipJingdongRefundService.updateReturnOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(), SysNotesConstant.SYS_REMARK110, jingdongRefund);
                        return new ProcessStepResult<>(StepStatus.FINISHED,
                                "单据" + orderInfo.getOrderId() + "转换完成");
                    }
                    //执行退单流程
                    omsReturnOrderService.saveOmsReturnOrderInfo(returnOrderRelations, operateUser);
                }
            }
            ipJingdongRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(), SysNotesConstant.SYS_REMARK100, jingdongRefund);
            return new ProcessStepResult<>(StepStatus.FINISHED, "单据" + orderInfo.getOrderNo() + "转换完成");
        } catch (Exception e) {
            log.error(LogUtil.format("京东退单转换异常:{}", "京东退单转换异常"), Throwables.getStackTraceAsString(e));
            //修改中间表状态及系统备注
            ipJingdongRefundService.updateRefundIsTransError(jingdongRefund,e.getMessage());
            return new ProcessStepResult<>(StepStatus.FAILED, "退单转换异常：" + e.getMessage());
        }
    }
}
