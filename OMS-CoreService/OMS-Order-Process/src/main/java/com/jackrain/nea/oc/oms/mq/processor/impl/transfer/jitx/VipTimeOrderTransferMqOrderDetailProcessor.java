package com.jackrain.nea.oc.oms.mq.processor.impl.transfer.jitx;

import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.mapper.IpBTimeOrderVipMapper;
import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.OperateType;
import com.jackrain.nea.oc.oms.model.enums.OrderType;
import com.jackrain.nea.oc.oms.model.relation.IpVipTimeOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTimeOrderVipOccupyItem;
import com.jackrain.nea.oc.oms.mq.processor.IMqOrderDetailProcessor;
import com.jackrain.nea.oc.oms.process.jitx.timeorder.normal.VipTimeOrderProcessImpl;
import com.jackrain.nea.oc.oms.services.IpVipTimeOrderService;
import com.jackrain.nea.oc.oms.step.ProcessStepResultList;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 时效订单转换MQ监听类
 * @Date 2019-8-19
 **/
@Slf4j
public class VipTimeOrderTransferMqOrderDetailProcessor implements IMqOrderDetailProcessor {

    @Autowired
    private IpVipTimeOrderService ipVipTimeOrderService;

    @Autowired
    private VipTimeOrderProcessImpl vipTimeOrderProcess;

    @Autowired
    private IpBTimeOrderVipMapper timeOrderVipMapper;

    @Override
    public ProcessStepResultList start(OperateOrderMqInfo orderMqInfo) {
        String orderNo = orderMqInfo.getOrderNo();

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Start MqOrder.Transfer.occupiedOrderSn", orderNo));
        }

        IpVipTimeOrderRelation ipVipTimeOrderRelation = this.ipVipTimeOrderService.selectTimeOrder(orderNo);
        if (ipVipTimeOrderRelation == null || ipVipTimeOrderRelation.getIpBTimeOrderVip() == null) {
            String errorMessage = Resources.getMessage("Received OrderMqInfo Not Exist!occupiedOrderSn=" + orderNo);
            log.error(LogUtil.format(errorMessage));
            return new ProcessStepResultList();
        }
        return vipTimeOrderProcess.start(ipVipTimeOrderRelation, false, SystemUserResource.getRootUser());
//        Integer status = ipVipTimeOrderRelation.getIpBTimeOrderVip().getStatus();
//        boolean isCanTransferOrderFlag = TimeOrderVipStatusEnum.CREATED.getValue().equals(status)
//                || TimeOrderVipStatusEnum.OUT_STOCK.getValue().equals(status);
//        if (isCanTransferOrderFlag) {
//            resultList = vipTimeOrderProcess.start(ipVipTimeOrderRelation, false, SystemUserResource.getRootUser());
//            if (log.isDebugEnabled()) {
//                log.debug("Finished MqOrder Transfer. occupiedOrderSn=" + orderNo + ";Result=" + resultList);
//            }
//            return resultList;
//        } else {
//            IpBTimeOrderVip ipBTimeOrderVip = ipVipTimeOrderRelation.getIpBTimeOrderVip();
//            ipVipTimeOrderService.updateTimeOrderTransferStatus(TransferOrderStatus.TRANSFERRED.toInteger()
//                    , "时效订单不是已创建或缺货，不进行转单，标记为已转换！", ipBTimeOrderVip);
//            if (log.isDebugEnabled()) {
//                log.debug("Finished MqOrder Transfer.时效订单不是已创建或缺货，不进行转单 occupiedOrderSn="
//                        + orderNo + ";Result=" + resultList);
//            }
//        }
//        return new ProcessStepResultList();
    }

    private ProcessStepResultList getProcessStepResultList(String orderNo
            , IpVipTimeOrderRelation ipVipTimeOrderRelation
            , ProcessStepResultList resultList, User rootUser
            , List<IpBTimeOrderVipOccupyItem> filterOrderVipOccupyItemList) {
        if (CollectionUtils.isNotEmpty(filterOrderVipOccupyItemList)) {
            ipVipTimeOrderRelation.setIpBTimeOrderVipOccupyItemList(filterOrderVipOccupyItemList);
            resultList = vipTimeOrderProcess.start(ipVipTimeOrderRelation, false, rootUser);
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Finished.MqOrder.Transfer.occupiedOrderSn,Result= {}", orderNo), resultList);
        }
        return resultList;
    }

    @Override
    public boolean checkMqIsCanExecute(OperateOrderMqInfo orderMqInfo) {
        return orderMqInfo.getOperateType() == OperateType.TRANSFER_ORDER
                && orderMqInfo.getChannelType() == ChannelType.VIPJITX
                && orderMqInfo.getOrderType() == OrderType.TIMEORDER;
    }
}
