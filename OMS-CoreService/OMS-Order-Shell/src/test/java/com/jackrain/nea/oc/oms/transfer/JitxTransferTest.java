package com.jackrain.nea.oc.oms.transfer;

import com.jackrain.nea.oc.oms.model.enums.SyncStatus;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpBCancelTimeOrderVipRelation;
import com.jackrain.nea.oc.oms.model.relation.IpJitxDeliveryRelation;
import com.jackrain.nea.oc.oms.model.relation.IpJitxOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.IpVipTimeOrderRelation;
import com.jackrain.nea.oc.oms.process.jitx.feedback.JitxFeedBackDeliveryProcessImpl;
import com.jackrain.nea.oc.oms.process.jitx.timeorder.cancel.VipTimeOrderCancelProcessImpl;
import com.jackrain.nea.oc.oms.process.jitx.timeorder.normal.VipTimeOrderProcessImpl;
import com.jackrain.nea.oc.oms.process.transfer.impl.normal.jitx.JitxTransferOrderProcessImpl;
import com.jackrain.nea.oc.oms.services.*;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.util.ApplicationContextHandle;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Profile;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: 黄超
 * @since: 2019-06-27
 * create at : 2019-06-27 20:00
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest()
@Profile(value = "dev")
public class JitxTransferTest {

    @Autowired
    private JitxTransferOrderProcessImpl orderProcess;
    @Autowired
    private IpJitxOrderService jitxOrderService;
    @Autowired
    private JitxFeedBackDeliveryProcessImpl deliveryProcess;
    @Autowired
    private IpJitxDeliveryService jitxDeliveryService;
    @Autowired
    private IpVipTimeOrderCancelService timeOrderCancelService;
    @Autowired
    private VipTimeOrderCancelProcessImpl timeOrderCancelProcess;
    @Autowired
    private IpVipTimeOrderService ipVipTimeOrderService;
    @Autowired
    private VipTimeOrderProcessImpl vipTimeOrderProcess;
    @Autowired
    private RefundInMakeUpService refundInService;

    @Test
    public void testTransferjitx() {
        List<String> orderNoList = new ArrayList<>();
        orderNoList.add("19071549071599");
        for (String orderNo : orderNoList) {
            IpJitxOrderRelation orderRelation = jitxOrderService.selectJitxOrder(orderNo);
            if (orderRelation != null) {
                orderProcess.start(orderRelation, false, SystemUserResource.getRootUser());
            }
        }
//        RunTaskResult execute = autoJitxTransferTask.execute(null);
//        System.out.println(execute);
    }

    @Test
    public void testFeedBackjitxDelivery() {
        String orderNo = "1544524551691";
        IpJitxDeliveryRelation orderRelation = jitxDeliveryService.selectJitxDelivery(orderNo);
        if (orderRelation != null) {
            deliveryProcess.start(orderRelation, false, SystemUserResource.getRootUser());
        }
    }

    @Test
    public void testcancelTimeOrder() {
        String orderNo = "19090513198568691840406474564508314664516";

        IpBCancelTimeOrderVipRelation orderRelation = timeOrderCancelService.selectCancelTimeOrder(orderNo);
        if (orderRelation != null) {
            timeOrderCancelProcess.start(orderRelation, false, SystemUserResource.getRootUser());
        }

    }

    @Test
    public void pushES() {
        //vipTimeOrderService.updateTimeOrderES("19090513198568691840406474564508314664516");
//        String test = "[\"warehouse1\",\"warehouse2\",\"SPD0000999\",\"warehouse4\"]";
////        JSONArray obj= JSONArray.parseArray(test);
//        List<String> availableWares = JSONArray.parseArray(test, String.class);
//        // List<String> arr = JSONArray.parseArray(JSON.toJSONString(obj), String.class);
//        // String[] availableWares = jitxDeliveryService.deliveryRelation.getJitxDelivery().getAvailableWarehouses().split(",");
//        //判断匹配仓是否在可用仓列表
//        ValueHolderV14 v14 = jitxDeliveryService.isAvailableWareHouseExists(availableWares, "SPD0000999");
        timeOrderCancelService.updateTimeOrderTransStatus("19090513198568691840406474564508314664516", TransferOrderStatus.NOT_TRANSFER, "");
        jitxDeliveryService.updateJitxSyncStatus("1544524551691", SyncStatus.UNSYNC, "");
    }

    @Test
    public void testTimeOrder() {
        String orderNo = "19090926555088691792081941104322714664516";
        IpVipTimeOrderRelation ipVipTimeOrderRelation = ipVipTimeOrderService.selectTimeOrder(orderNo);

        if (ipVipTimeOrderRelation != null) {
            vipTimeOrderProcess.start(ipVipTimeOrderRelation, false, SystemUserResource.getRootUser());
        }
    }

    @Test
    public void testTask() {
        refundInService.dealTimeOutRefundInsException(1000, 10);
    }

    @Test
    public void testTask2() {
        refundInService.dealWaitMatchRefundInException(200);
        ReturnWrongAdjustmentServer sertver = ApplicationContextHandle.getBean(ReturnWrongAdjustmentServer.class);
//        sertver.testzxlcheshi();
        sertver.execLogic(200);
    }

}
