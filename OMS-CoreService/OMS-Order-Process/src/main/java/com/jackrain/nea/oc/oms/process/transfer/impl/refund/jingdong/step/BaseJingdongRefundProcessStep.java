package com.jackrain.nea.oc.oms.process.transfer.impl.refund.jingdong.step;

import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.services.IpJingdongRefundService;
import com.jackrain.nea.oc.oms.services.OcSaveChangingOrRefundingService;
import com.jackrain.nea.oc.oms.services.OmsRefundOrderService;
import com.jackrain.nea.oc.oms.services.OmsReturnOrderService;
import com.jackrain.nea.rpc.CpRpcService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Descroption 京东退款单转单处理阶段抽象基类
 * <AUTHOR>
 * @Date 2019/4/26 13:42
 */
public abstract class BaseJingdongRefundProcessStep {
    @Autowired
    protected IpJingdongRefundService ipJingdongRefundService;

    @Autowired
    protected OmsReturnOrderService omsReturnOrderService;

    @Autowired
    protected CpRpcService cpRpcService;

    @Autowired
    protected OmsRefundOrderService omsRefundOrderService;

    @Autowired
    protected OcSaveChangingOrRefundingService ocSaveChangingOrRefundingService;

    @Autowired
    protected OcBOrderMapper ocBOrderMapper;


}
