package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.OcBSapSalesDataGatherItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

@Mapper
public interface OcBSapSalesDataGatherItemMapper extends ExtentionMapper<OcBSapSalesDataGatherItem> {

    @Select("select * from OC_B_SAP_SALES_DATA_GATHER_ITEM\n" +
            "where OC_B_SAP_SALES_DATA_GATHER_ID=#{sapSalesDataGatherId}")
    List<OcBSapSalesDataGatherItem> querySaleDataGatherItem(@Param("sapSalesDataGatherId") Long sapSalesDataGatherId);
}