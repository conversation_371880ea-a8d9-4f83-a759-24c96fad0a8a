package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.IpBOrderLock;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.jdbc.SQL;

@Mapper
public interface IpBOrderLockMapper extends ExtentionMapper<IpBOrderLock> {

    /**
     * 更新订单转换状态
     *
     * @param ids          订单编号字符串
     * @param lockDays     锁单天数
     * @param isAutoUnlock 是否自动解锁
     * @return 更新结果
     */
    @UpdateProvider(type = IpBOrderLockMapper.lockSqlBuilder.class, method = "buildUpdateLockOrderByBatchSQL")
    int updateLockOrderByBatchSQL(@Param("ids") String ids, @Param("lockDays") int lockDays,
                                  @Param("isAutoUnlock") int isAutoUnlock);

    /**
     * 订单SQL创建器
     */
    class lockSqlBuilder {
        /**
         * 创建更新锁单解锁时间SQL
         *
         * @param ids          订单编号字符串
         * @param lockDays     锁单天数
         * @param isAutoUnlock 是否自动解锁
         * @return 更新SQL语句
         */
        public String buildUpdateLockOrderByBatchSQL(@Param("ids") String ids, @Param("lockDays") int lockDays,
                                                     @Param("isAutoUnlock") int isAutoUnlock) {
            return new SQL() {
                {
                    UPDATE("ip_b_order_lock");
                    SET("is_auto_unlock=#{isAutoUnlock}");
                    SET("except_unlock_time = DATE_ADD(creationdate, INTERVAL #{lockDays} DAY) ");
                    WHERE("ID in (" + ids + " )");

                }
            }.toString();
        }
    }
}