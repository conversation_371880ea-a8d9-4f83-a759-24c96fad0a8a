package com.jackrain.nea.oc.oms.es;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/12 1:09 上午
 */
public class ES4IpTaoBaoFxRefund {

    private ES4IpTaoBaoFxRefund() {
    }

    /**
     * 业务：退单补偿服务 ES获取退单中间表数据分库键
     * 根据isTrans查询purchaseOrderId
     *
     * @param transStatus 转换状态
     * @param pageIndex 页面索引
     * @param pageSize 每页条数
     *
     * purchaseOrderId  主采购单编号
     * @return List purchase_order_id
     */
    public static List<String> findPurchaseOrderIdByIsTrans(Integer transStatus, int pageIndex, int pageSize){
        List<String> list = new ArrayList<>();
        int startIndex = pageIndex * pageSize;
        if (startIndex < 0) {
            startIndex = 0;
        }
        JSONObject orderKes = new JSONObject();
        orderKes.put("name", "ID");
        orderKes.put("asc", false);
        JSONArray orderKeys = new JSONArray();
        orderKeys.add(orderKes);

        JSONObject whereKeys = new JSONObject();
        String[] returnFields = {"PURCHASE_ORDER_ID"};
        whereKeys.put("ISTRANS", transStatus);

        JSONObject search = ElasticSearchUtil.search(TaobaoReturnOrderExt.TABLENAME_IPBTAOBAOFXREFUND,
                TaobaoReturnOrderExt.TABLENAME_IPBTAOBAOFXREFUND, whereKeys, null,
                orderKeys, pageSize, startIndex, returnFields);

        if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
            JSONArray arrayObj = search.getJSONArray("data");
            for (Object obj : arrayObj) {
                JSONObject jsonObject = (JSONObject) obj;
                list.add(jsonObject.getString("PURCHASE_ORDER_ID"));
            }
        }
        return list;
    }
}
