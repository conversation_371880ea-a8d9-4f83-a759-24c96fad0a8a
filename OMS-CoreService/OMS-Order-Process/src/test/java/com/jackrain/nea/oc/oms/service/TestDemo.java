package com.jackrain.nea.oc.oms.service;

import com.alibaba.fastjson.JSONObject;

import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.ps.model.SkuType;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.apache.coyote.http11.upgrade.UpgradeProcessorBase;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/3/13 20:16
 */
@Slf4j
@Component
public class TestDemo {
    public static void main(String[] args) {
//        threeSumClosest(a,800);
        String sku ="[\n" +
                "    {\n" +
                "        \"ADJUST_AMT\":8,\n" +
                "        \"AD_CLIENT_ID\":37,\n" +
                "        \"AD_ORG_ID\":27,\n" +
                "        \"AMT_DISCOUNT\":0,\n" +
                "        \"CREATIONDATE\":1664108405000,\n" +
                "        \"EQUAL_EXCHANGE_MARK\":\"\",\n" +
                "        \"EQUAL_EXCHANGE_RATIO\":\"\",\n" +
                "        \"GROSS_WEIGHT\":0,\n" +
                "        \"ID\":100035830,\n" +
                "        \"ISACTIVE\":\"Y\",\n" +
                "        \"IS_ENABLE_EXPIRY\":0,\n" +
                "        \"IS_EXCHANGE_ITEM\":0,\n" +
                "        \"IS_GIFT\":0,\n" +
                "        \"IS_PRESALESKU\":0,\n" +
                "        \"MODIFIEDDATE\":1664108405000,\n" +
                "        \"MODIFIERENAME\":\"root\",\n" +
                "        \"MODIFIERID\":893,\n" +
                "        \"MODIFIERNAME\":\"root\",\n" +
                "        \"M_DIM4_ID\":127,\n" +
                "        \"M_DIM6_ID\":167,\n" +
                "        \"NUM_IID\":\"0\",\n" +
                "        \"OC_B_ORDER_ID\":200110370,\n" +
                "        \"OOID\":\"18051267438\",\n" +
                "        \"ORDER_SPLIT_AMT\":0,\n" +
                "        \"OWNERENAME\":\"root\",\n" +
                "        \"OWNERID\":893,\n" +
                "        \"OWNERNAME\":\"root\",\n" +
                "        \"PIC_PATH\":\"https://sf1-ttcdn-tos.pstatp.com/obj/temai/5802275796125ecf126c48c5810aca7cwww600-600\",\n" +
                "        \"PRESELL_TYPE\":0,\n" +
                "        \"PRICE\":19.9,\n" +
                "        \"PRICE_ACTUAL\":22.5667,\n" +
                "        \"PRICE_LIST\":0,\n" +
                "        \"PRO_TYPE\":0,\n" +
                "        \"PS_C_BRAND_ID\":4,\n" +
                "        \"PS_C_CLR_ECODE\":\"BSBSBS\",\n" +
                "        \"PS_C_CLR_ENAME\":\"bsbsbs\",\n" +
                "        \"PS_C_CLR_ID\":16,\n" +
                "        \"PS_C_PRO_ECODE\":\"CQ001\",\n" +
                "        \"PS_C_PRO_ENAME\":\"哇塞商品001\",\n" +
                "        \"PS_C_PRO_ID\":1355,\n" +
                "        \"PS_C_SIZE_ECODE\":\"LLL\",\n" +
                "        \"PS_C_SIZE_ENAME\":\"lll\",\n" +
                "        \"PS_C_SIZE_ID\":14,\n" +
                "        \"PS_C_SKU_ECODE\":\"CQSKU001\",\n" +
                "        \"PS_C_SKU_ID\":1119,\n" +
                "        \"PS_C_SKU_PT_ECODE\":\"0\",\n" +
                "        \"QTY\":2,\n" +
                "        \"QTY_REFUND\":0,\n" +
                "        \"REAL_AMT\":67.7,\n" +
                "        \"REFUND_STATUS\":0,\n" +
                "        \"SKU_NUMIID\":\"0\",\n" +
                "        \"SKU_SPEC\":\"lll,bsbsbs\",\n" +
                "        \"STANDARD_WEIGHT\":300,\n" +
                "        \"TID\":\"1284988202209252018\",\n" +
                "        \"TITLE\":\"通用商品1267438\"\n" +
                "    }\n" +
                "]";
//                "[{\"ADJUST_AMT\":0.0000,\"AD_CLIENT_ID\":37,\"AD_ORG_ID\":27,\"AMT_DISCOUNT\":0.0000," +
//                        "\"AMT_REFUND\":0.0000,\"BUYER_USED_INTEGRAL\":0,\"CREATIONDATE\":1663245905000," +
//                        "\"DISTRIBUTION_PRICE\":0.0000,\"GIFTBAG_SKU\":\"WASAI001\",\"GIFT_RELATION\":\"WASAI001\"," +
//                        "\"GROSS_WEIGHT\":0.0000,\"GROUP_GOODS_MARK\":\"CG1576653\",\"ID\":1576653," +
//                        "\"ISACTIVE\":\"Y\",\"IS_ALLOCATESTOCK\":0,\"IS_BUYER_RATE\":0,\"IS_ENABLE_EXPIRY\":0," +
//                        "\"IS_GIFT\":0,\"IS_LACKSTOCK\":0,\"IS_PRESALESKU\":0,\"IS_SENDOUT\":0," +
//                        "\"MODIFIEDDATE\":1663246022000,\"MODIFIERENAME\":\"root\",\"MODIFIERID\":893," +
//                        "\"MODIFIERNAME\":\"root\",\"NUM_IID\":\"0\",\"OC_B_ORDER_ID\":3220055," +
//                        "\"OOID\":\"44531249496\",\"ORDER_SPLIT_AMT\":0.0000,\"OUTERRCOUNT\":0," +
//                        "\"OWNERENAME\":\"root\",\"OWNERID\":893,\"OWNERNAME\":\"root\"," +
//                        "\"PIC_PATH\":\"https://sf1-ttcdn-tos.pstatp" +
//                        ".com/obj/temai/5802275796125ecf126c48c5810aca7cwww600-600\",\"PRESELL_TYPE\":0,\"PRICE\":19" +
//                        ".9000,\"PRICE_ACTUAL\":19.9000,\"PRICE_LIST\":18.0000,\"PRICE_TAG\":18.0000,\"PRO_TYPE\":4," +
//                        "\"PS_C_BRAND_ID\":4,\"PS_C_CLR_ID\":0,\"PS_C_PRO_ECODE\":\"WASAI001\"," +
//                        "\"PS_C_PRO_ENAME\":\"通用商品1249496\",\"PS_C_PRO_ID\":1358,\"PS_C_SIZE_ECODE\":\"WASAI001\"," +
//                        "\"PS_C_SIZE_ID\":0,\"PS_C_SKU_ECODE\":\"WASAI001\",\"PS_C_SKU_ID\":1124," +
//                        "\"PS_C_SKU_PT_ECODE\":\"0\",\"QTY\":2.0000,\"QTY_REFUND\":0.0000,\"QTY_RETURN_APPLY\":0" +
//                        ".0000,\"REAL_AMT\":39.8000,\"REFUND_STATUS\":0,\"SKU_NUMIID\":\"0\",\"SKU_SPEC\":\"\"," +
//                        "\"TID\":\"1270246202209152044\",\"TITLE\":\"[组合]通用商品1249496[WASAI001]\",\"VERSION\":0}," +
//                        "{\"ADJUST_AMT\":0.0000,\"AD_CLIENT_ID\":37,\"AD_ORG_ID\":27,\"AMT_DISCOUNT\":0.0000," +
//                        "\"AMT_REFUND\":0.0000,\"BUYER_USED_INTEGRAL\":0,\"CAN_SPLIT\":\"Y\"," +
//                        "\"CREATIONDATE\":1663245932000,\"DISTRIBUTION_PRICE\":0.0000,\"GIFTBAG_SKU\":\"WASAI001\"," +
//                        "\"GIFT_RELATION\":\"WASAI001\",\"GROUP_GOODS_MARK\":\"CG1576653\",\"GROUP_RADIO\":0.3333," +
//                        "\"ID\":1576713,\"ISACTIVE\":\"Y\",\"IS_ALLOCATESTOCK\":0,\"IS_BUYER_RATE\":0," +
//                        "\"IS_ENABLE_EXPIRY\":0,\"IS_GIFT\":0,\"IS_LACKSTOCK\":0,\"IS_PRESALESKU\":0," +
//                        "\"MODIFIEDDATE\":1663246022000,\"MODIFIERENAME\":\"root\",\"MODIFIERID\":893," +
//                        "\"MODIFIERNAME\":\"root\",\"M_DIM4_ID\":127,\"NUM_IID\":\"0\",\"OC_B_ORDER_ID\":3220055," +
//                        "\"OOID\":\"44531249496\",\"ORDER_SPLIT_AMT\":0.0000,\"OWNERENAME\":\"root\",\"OWNERID\":893," +
//                        "\"OWNERNAME\":\"root\",\"PIC_PATH\":\"https://sf1-ttcdn-tos.pstatp" +
//                        ".com/obj/temai/5802275796125ecf126c48c5810aca7cwww600-600\",\"PRICE\":6.6327," +
//                        "\"PRICE_ACTUAL\":3.3163,\"PRO_TYPE\":2,\"PS_C_CLR_ECODE\":\"HSHSHS\"," +
//                        "\"PS_C_CLR_ENAME\":\"hshshs\",\"PS_C_CLR_ID\":19,\"PS_C_PRO_ECODE\":\"CQ001\"," +
//                        "\"PS_C_PRO_ENAME\":\"哇塞商品001\",\"PS_C_PRO_ID\":1355,\"PS_C_SIZE_ECODE\":\"XSXSXS\"," +
//                        "\"PS_C_SIZE_ENAME\":\"xsxsxs\",\"PS_C_SIZE_ID\":15,\"PS_C_SKU_ECODE\":\"CQSKU002\"," +
//                        "\"PS_C_SKU_ID\":1120,\"PS_C_SKU_PT_ECODE\":\"WASAI001\",\"QTY\":4.0000,\"QTY_GROUP\":2.0000," +
//                        "\"QTY_REFUND\":0.0000,\"REAL_AMT\":13.2653,\"REFUND_STATUS\":0,\"SKU_NUMIID\":\"0\"," +
//                        "\"SKU_SPEC\":\"xsxsxs,hshshs\",\"STANDARD_WEIGHT\":200.0000,\"TID\":\"1270246202209152044\"," +
//                        "\"TITLE\":\"[组合]通用商品1249496[WASAI001]\",\"VERSION\":0},{\"ADJUST_AMT\":0.0000," +
//                        "\"AD_CLIENT_ID\":37,\"AD_ORG_ID\":27,\"AMT_DISCOUNT\":0.0000,\"AMT_REFUND\":0.0000," +
//                        "\"BUYER_USED_INTEGRAL\":0,\"CAN_SPLIT\":\"Y\",\"CREATIONDATE\":1663245932000," +
//                        "\"DISTRIBUTION_PRICE\":0.0000,\"GIFTBAG_SKU\":\"WASAI001\",\"GIFT_RELATION\":\"WASAI001\"," +
//                        "\"GROUP_GOODS_MARK\":\"CG1576653\",\"GROUP_RADIO\":0.5003,\"ID\":1576714,\"ISACTIVE\":\"Y\"," +
//                        "\"IS_ALLOCATESTOCK\":0,\"IS_BUYER_RATE\":0,\"IS_ENABLE_EXPIRY\":0,\"IS_GIFT\":0," +
//                        "\"IS_LACKSTOCK\":0,\"IS_PRESALESKU\":0,\"MODIFIEDDATE\":1663246022000," +
//                        "\"MODIFIERENAME\":\"root\",\"MODIFIERID\":893,\"MODIFIERNAME\":\"root\",\"M_DIM4_ID\":127," +
//                        "\"NUM_IID\":\"0\",\"OC_B_ORDER_ID\":3220055,\"OOID\":\"44531249496\",\"ORDER_SPLIT_AMT\":0" +
//                        ".0000,\"OWNERENAME\":\"root\",\"OWNERID\":893,\"OWNERNAME\":\"root\"," +
//                        "\"PIC_PATH\":\"https://sf1-ttcdn-tos.pstatp" +
//                        ".com/obj/temai/5802275796125ecf126c48c5810aca7cwww600-600\",\"PRICE\":9.9559," +
//                        "\"PRICE_ACTUAL\":4.9780,\"PRO_TYPE\":2,\"PS_C_CLR_ECODE\":\"LSLSLS\"," +
//                        "\"PS_C_CLR_ENAME\":\"lslsls\",\"PS_C_CLR_ID\":17,\"PS_C_PRO_ECODE\":\"CQ001\"," +
//                        "\"PS_C_PRO_ENAME\":\"哇塞商品001\",\"PS_C_PRO_ID\":1355,\"PS_C_SIZE_ECODE\":\"MMM\"," +
//                        "\"PS_C_SIZE_ENAME\":\"mmm\",\"PS_C_SIZE_ID\":13,\"PS_C_SKU_ECODE\":\"CQSKU003\"," +
//                        "\"PS_C_SKU_ID\":1121,\"PS_C_SKU_PT_ECODE\":\"WASAI001\",\"QTY\":4.0000,\"QTY_GROUP\":2.0000," +
//                        "\"QTY_REFUND\":0.0000,\"REAL_AMT\":19.9120,\"REFUND_STATUS\":0,\"SKU_NUMIID\":\"0\"," +
//                        "\"SKU_SPEC\":\"mmm,lslsls\",\"STANDARD_WEIGHT\":50.0000,\"TID\":\"1270246202209152044\"," +
//                        "\"TITLE\":\"[组合]通用商品1249496[WASAI001]\",\"VERSION\":0},{\"ADJUST_AMT\":0.0000," +
//                        "\"AD_CLIENT_ID\":37,\"AD_ORG_ID\":27,\"AMT_DISCOUNT\":0.0000,\"AMT_REFUND\":0.0000," +
//                        "\"BUYER_USED_INTEGRAL\":0,\"CAN_SPLIT\":\"Y\",\"CREATIONDATE\":1663245932000," +
//                        "\"DISTRIBUTION_PRICE\":0.0000,\"GIFTBAG_SKU\":\"WASAI001\",\"GIFT_RELATION\":\"WASAI001\"," +
//                        "\"GROUP_GOODS_MARK\":\"CG1576653\",\"GROUP_RADIO\":0.1664,\"ID\":1576715,\"ISACTIVE\":\"Y\"," +
//                        "\"IS_ALLOCATESTOCK\":0,\"IS_BUYER_RATE\":0,\"IS_ENABLE_EXPIRY\":0,\"IS_GIFT\":0," +
//                        "\"IS_LACKSTOCK\":0,\"IS_PRESALESKU\":0,\"MODIFIEDDATE\":1663246022000," +
//                        "\"MODIFIERENAME\":\"root\",\"MODIFIERID\":893,\"MODIFIERNAME\":\"root\",\"M_DIM4_ID\":127," +
//                        "\"NUM_IID\":\"\",\"OC_B_ORDER_ID\":3220055,\"OOID\":\"44531249496\",\"ORDER_SPLIT_AMT\":0" +
//                        ".0000,\"OWNERENAME\":\"root\",\"OWNERID\":893,\"OWNERNAME\":\"root\"," +
//                        "\"PIC_PATH\":\"https://sf1-ttcdn-tos.pstatp" +
//                        ".com/obj/temai/5802275796125ecf126c48c5810aca7cwww600-600\",\"PRICE\":3.3114," +
//                        "\"PRICE_ACTUAL\":3.3114,\"PRO_TYPE\":2,\"PS_C_CLR_ECODE\":\"BSBSBS\"," +
//                        "\"PS_C_CLR_ENAME\":\"bsbsbs\",\"PS_C_CLR_ID\":16,\"PS_C_PRO_ECODE\":\"CQ001\"," +
//                        "\"PS_C_PRO_ENAME\":\"哇塞商品001\",\"PS_C_PRO_ID\":1355,\"PS_C_SIZE_ECODE\":\"LLL\"," +
//                        "\"PS_C_SIZE_ENAME\":\"lll\",\"PS_C_SIZE_ID\":14,\"PS_C_SKU_ECODE\":\"CQSKU001\"," +
//                        "\"PS_C_SKU_ID\":1119,\"PS_C_SKU_PT_ECODE\":\"WASAI001\",\"QTY\":2.0000,\"QTY_GROUP\":2.0000," +
//                        "\"QTY_REFUND\":0.0000,\"REAL_AMT\":6.6227,\"REFUND_STATUS\":0,\"SKU_NUMIID\":\"\"," +
//                        "\"SKU_SPEC\":\"lll,bsbsbs\",\"STANDARD_WEIGHT\":300.0000,\"TID\":\"1270246202209152044\"," +
//                        "\"TITLE\":\"[组合]通用商品1249496[WASAI001]\",\"VERSION\":0},{\"ACTIVE_ID\":\"回归组合商品bug\"," +
//                        "\"ADJUST_AMT\":0.0000,\"AD_CLIENT_ID\":37,\"AD_ORG_ID\":27,\"AMT_DISCOUNT\":0.0000," +
//                        "\"AMT_REFUND\":0.0000,\"BUYER_USED_INTEGRAL\":0,\"CREATIONDATE\":1663246022000," +
//                        "\"DISTRIBUTION_PRICE\":0.0000,\"GIFT_TYPE\":\"1\"," +
//                        "\"GROSS_WEIGHT\":0.0000,\"ID\":1576996,\"ISACTIVE\":\"Y\",\"IS_ALLOCATESTOCK\":0," +
//                        "\"IS_BUYER_RATE\":0,\"IS_ENABLE_EXPIRY\":0,\"IS_GIFT\":1,\"IS_GIFT_SPLIT\":1," +
//                        "\"IS_LACKSTOCK\":0,\"IS_PRESALESKU\":0,\"IS_SENDOUT\":0,\"MODIFIEDDATE\":1663246022000," +
//                        "\"MODIFIERENAME\":\"root\",\"MODIFIERID\":893,\"MODIFIERNAME\":\"root\",\"M_DIM4_ID\":127," +
//                        "\"NUM_IID\":\"0\",\"OC_B_ORDER_ID\":3220055,\"OOID\":\"44531249496\",\"ORDER_SPLIT_AMT\":0" +
//                        ".0000,\"OUTERRCOUNT\":0,\"OWNERENAME\":\"root\",\"OWNERID\":893,\"OWNERNAME\":\"root\"," +
//                        "\"PRESELL_TYPE\":0,\"PRICE\":0.0000,\"PRICE_LIST\":0.0000,\"PRO_TYPE\":0," +
//                        "\"PS_C_CLR_ECODE\":\"LSLSLS\",\"PS_C_CLR_ENAME\":\"lslsls\",\"PS_C_CLR_ID\":17," +
//                        "\"PS_C_PRO_ECODE\":\"CQ001\",\"PS_C_PRO_ENAME\":\"哇塞商品001\",\"PS_C_PRO_ID\":1355," +
//                        "\"PS_C_SIZE_ECODE\":\"MMM\",\"PS_C_SIZE_ENAME\":\"mmm\",\"PS_C_SIZE_ID\":13," +
//                        "\"PS_C_SKU_ECODE\":\"CQSKU003\",\"PS_C_SKU_ID\":1121,\"PS_C_SKU_PT_ECODE\":\"CQSKU003\"," +
//                        "\"QTY\":2.0000,\"QTY_REFUND\":0.0000,\"QTY_RETURN_APPLY\":0.0000,\"REAL_AMT\":0.0000," +
//                        "\"REFUND_STATUS\":0,\"RESERVE_VARCHAR01\":\"CX2209150000018\",\"SKU_SPEC\":\"mmm,lslsls\"," +
//                        "\"STANDARD_WEIGHT\":50.0000,\"TID\":\"1270246202209152044\",\"VERSION\":0}]";
        List<OcBOrderItem> orderItemList = JSONObject.parseArray(sku, OcBOrderItem.class);
        //        orderItemList.forEach(o->o.setGiftRelation(null));
        ////        orderItemList.get(0).setGiftRelation("hahah");
        //        List<String> giftRelations =
        //                orderItemList.stream().map(OcBOrderItem::getGiftRelation).collect(Collectors.toList());
        //        boolean contains = giftRelations.contains(null);
        //        boolean contains1 = giftRelations.contains("1");

        orderItemList=orderItemList.stream().filter(i->i.getProType()!=4).collect(Collectors.toList());
        Map<Long, Boolean> categoryStrategyMap = new HashMap<>();
        Map<Long, BigDecimal> skuStrategyMap = new HashMap<>();
        Map<Long, BigDecimal> weightStrategyMap = new HashMap<>();

//        categoryStrategyMap.put(127L, true);
//        categoryStrategyMap.put(35L, true);
//        categoryStrategyMap.put(26L, true);
//
//        skuStrategyMap.put(1111L, new BigDecimal(1));
//        skuStrategyMap.put(1121L, new BigDecimal(2));
//        skuStrategyMap.put(974L, new BigDecimal(1));
//        skuStrategyMap.put(1329L, new BigDecimal(10));

        weightStrategyMap.put(127L,BigDecimal.valueOf(500L));

        Collection<List<OcBOrderItem>> splitOrderItemsPool = null;
        Collection<List<OcBOrderItem>> splitOrderItemsByCategory = new ArrayList<>();
        Collection<List<OcBOrderItem>> splitOrderItemsByWeight = new ArrayList<>();


        splitOrderItemsPool = splitStrategyBySku(orderItemList, skuStrategyMap);

        if (CollectionUtils.isNotEmpty(splitOrderItemsPool)) {
            for (List<OcBOrderItem> splitOrderItem : splitOrderItemsPool) {
                Collection<List<OcBOrderItem>> splitStrategyByCategory = splitStrategyByCategory(splitOrderItem,
                        categoryStrategyMap);
                splitOrderItemsByCategory.addAll(splitStrategyByCategory);
            }
            if (CollectionUtils.isNotEmpty(splitOrderItemsByCategory)) {
                splitOrderItemsPool = splitOrderItemsByCategory;
            }
        } else {
            splitOrderItemsPool = splitStrategyByCategory(orderItemList, categoryStrategyMap);
        }
        if (CollectionUtils.isNotEmpty(splitOrderItemsPool)) {
            for (List<OcBOrderItem> splitOrderItem : splitOrderItemsPool) {
                Collection<List<OcBOrderItem>> splitStrategyByWeight = splitWeightService(splitOrderItem,
                        weightStrategyMap);
                splitOrderItemsByWeight.addAll(splitStrategyByWeight);
            }
            if (CollectionUtils.isNotEmpty(splitOrderItemsByWeight)) {
                splitOrderItemsPool = splitOrderItemsByWeight;
            }
        } else {
            splitOrderItemsPool = splitWeightService(orderItemList, weightStrategyMap);
        }





    }



    private static Collection<List<OcBOrderItem>> splitStrategyByCategory(List<OcBOrderItem> ocOrderItemList,
                                                                          Map<Long,
                                                                                  Boolean> categoryStrategyMap) {
        log.info("Start.SplitBeforeSourcingStService.splitStrategyByCategory");
        //收集拆单和未拆单的的
        Table<List<String>, Boolean, List<OcBOrderItem>> employeeTable = HashBasedTable.create();
        //不满足拆单的
        List<OcBOrderItem> restList = new ArrayList<>();
        //其他可拆单的
        List<OcBOrderItem> canSplitList = new ArrayList<>();

        //不允许拆单的赠品
        List<OcBOrderItem> unSplitGiftList = new ArrayList<>();
        //所有赠品挂靠关系
        List<String> allSplitGiftRelation = new ArrayList<>();

        List<OcBOrderItem> isCombineProductList = new ArrayList<>();

        Map<String, OcBOrderItem> goodsMarkMap = new HashMap<>(16);

        //数据分组  不可拆单赠品 、可拆单商品、不可拆单(不包含赠品)
        groupBeforeSplitCategory(ocOrderItemList, canSplitList, isCombineProductList, unSplitGiftList, goodsMarkMap);

        if (CollectionUtils.isNotEmpty(isCombineProductList)) {
            Map<String, List<OcBOrderItem>> isCombineMap =
                    isCombineProductList.stream().collect(Collectors.groupingBy(x -> Optional.ofNullable(x.getGroupGoodsMark()).orElse(
                            String.valueOf(Math.random()))));
            Set<String> groupGoodsMark = isCombineMap.keySet();
            for (String mark : groupGoodsMark) {
                List<OcBOrderItem> ocOrderItems = isCombineMap.get(mark);
                OcBOrderItem virtualItem = goodsMarkMap.get(mark);
                //组合商品是否允许拆
                boolean b = ocOrderItems.stream().anyMatch(o -> "N".equals(o.getCanSplit()));
                if (b) {
                    //不允许判断品类是否一致
                    boolean noDim = ocOrderItems.stream().anyMatch(o -> o.getMDim4Id() == null);
                    if (noDim) {
                        restList.addAll(ocOrderItems);
                        restList.add(virtualItem);
                        continue;
                    }
                    Map<Long, List<OcBOrderItem>> dimMap =
                            ocOrderItems.stream().collect(Collectors.groupingBy(OcBOrderItem::getMDim4Id));
                    Set<Long> dimIds = dimMap.keySet();
                    if (dimIds.size() > 1) {
                        //品类不一致 不按品类拆
                        restList.addAll(ocOrderItems);
                        restList.add(virtualItem);
                        continue;
                    }
                    for (Long dim : dimIds) {
                        boolean isSplit = categoryStrategyMap.containsKey(dim);
                        if (isSplit) {
                            canSplitList.addAll(dimMap.get(dim));
                            //赋值后面按品类拆 再去除
                            virtualItem.setMDim4Id(dim);
                            canSplitList.add(virtualItem);
                        } else {
                            restList.addAll(dimMap.get(dim));
                            restList.add(virtualItem);
                        }
                    }

                } else {
                    canSplitList.addAll(ocOrderItems);
                }
            }
        }
        //品类分组 匹配品类的
        Map<Long, List<OcBOrderItem>> mDimMap =
                canSplitList.stream().filter(o -> o.getMDim4Id() != null).collect(Collectors.groupingBy(OcBOrderItem::getMDim4Id));

        List<OcBOrderItem> noMDimList =
                canSplitList.stream().filter(o -> o.getMDim4Id() == null).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(noMDimList)) {
            restList.addAll(noMDimList);
        }
        Set<Map.Entry<Long, List<OcBOrderItem>>> entries = mDimMap.entrySet();
        for (Map.Entry<Long, List<OcBOrderItem>> entry : entries) {
            Long mDimId = entry.getKey();
            Boolean strategy = categoryStrategyMap.get(mDimId);
            List<OcBOrderItem> value = entry.getValue();
            if (strategy != null) {
                //匹配拆单策略的
                List<String> giftRelations =
                        value.stream().map(OcBOrderItem::getGiftRelation).collect(Collectors.toList());
                //含有主品
                boolean haveMain = value.stream().anyMatch(o -> o.getIsGift() != 1);
                if (haveMain) {
                    allSplitGiftRelation.addAll(giftRelations);
                }
                boolean contains = employeeTable.contains(giftRelations, haveMain);
                //存在多key相同会覆盖
                if (contains) {
                    double random = Math.random();
                    giftRelations.add(String.valueOf(random));
                }
                value.forEach(v -> {
                    //虚拟条码清空品类分组标识
                    if (v.getProType() == 4) {
                        v.setMDim4Id(null);
                    }
                });
                employeeTable.put(giftRelations, haveMain, value);
                continue;
            }
            //可拆单情况且未匹配策略的集合(包含赠品)放到rest
            restList.addAll(value);
        }

        //不可拆单赠品挂靠分组
        Map<String, List<OcBOrderItem>> giftRelationMap =
                unSplitGiftList.stream().collect(Collectors.groupingBy((x -> Optional.ofNullable(x.getGiftRelation()).orElse("默认"))));

        Map<List<String>, List<OcBOrderItem>> splitMainMap = employeeTable.column(true);
        if (MapUtils.isNotEmpty(splitMainMap)){
            Set<List<String>> giftRelationSet = splitMainMap.keySet();
            Set<Map.Entry<String, List<OcBOrderItem>>> giftEntries = giftRelationMap.entrySet();
            //没有绑定主品的赠品
            for (Map.Entry<String, List<OcBOrderItem>> giftEntry : giftEntries) {
                String giftRelation = giftEntry.getKey();
                List<OcBOrderItem> gifts = giftEntry.getValue();
                for (List<String> stringList : giftRelationSet) {
                    if (allSplitGiftRelation.contains(giftRelation)) {
                        //赠品需随主品组成包裹
                        if (stringList.contains(giftRelation)) {
                            List<OcBOrderItem> orderItems = splitMainMap.get(stringList);
                            orderItems.addAll(gifts);
                            splitMainMap.put(stringList, orderItems);
                            break;
                        }
                    } else {
                        List<OcBOrderItem> orderItems = splitMainMap.get(stringList);
                        orderItems.addAll(gifts);
                        splitMainMap.put(stringList, orderItems);
                        break;
                    }
                }
            }
        }else{
            restList.addAll(unSplitGiftList);
        }

        List<List<OcBOrderItem>> lists = new ArrayList<>();
        Collection<List<OcBOrderItem>> values = employeeTable.values();
        if (CollectionUtils.isNotEmpty(values)) {
            lists.addAll(values);
        }
        if (CollectionUtils.isNotEmpty(restList)) {
            lists.add(restList);
        }
        log.info("Finish.SplitBeforeSourcingStService.splitStrategyByCategory,value.size={}", lists.size());
        return lists;
    }

    private static void groupBeforeSplitCategory(List<OcBOrderItem> ocOrderItemList, List<OcBOrderItem> canSplitList,
                                          List<OcBOrderItem> isCombineProductList,
                                          List<OcBOrderItem> unSplitGiftList,
                                          Map<String, OcBOrderItem> goodMarkMap) {
        for (OcBOrderItem orderItem : ocOrderItemList) {
            //为虚拟商品
            if (orderItem.getProType() == 4) {
                goodMarkMap.put(orderItem.getGroupGoodsMark(), orderItem);
                continue;
                //为组合商品
            } else if (orderItem.getProType() == 2) {
                isCombineProductList.add(orderItem);
                continue;
                //为赠品且不允许拆单
            } else if (orderItem.getIsGift() == 1 && (orderItem.getIsGiftSplit() == null || orderItem.getIsGiftSplit() != 2)) {
                unSplitGiftList.add(orderItem);
                continue;
            }
            //允许拆单的集合
            canSplitList.add(orderItem);

        }

    }

    private void groupBeforeSplitSku(List<OcBOrderItem> ocOrderItemList, List<OcBOrderItem> canSplitList,
                                     List<OcBOrderItem> isCombineProductList, List<OcBOrderItem> splitGiftList) {
        //        && !"Y".equals(orderItem.getCanSplit())
        for (OcBOrderItem orderItem : ocOrderItemList) {
            //为组合商品
            if (orderItem.getProType() == 2) {
                isCombineProductList.add(orderItem);
                continue;
                //为赠品
            } else if (orderItem.getIsGift() == 1) {
                splitGiftList.add(orderItem);
                continue;
            }
            //允许拆单的集合
            canSplitList.add(orderItem);

        }

    }

    private void groupBeforeSplit(List<OcBOrderItem> ocOrderItemList, Map<Long, Boolean> isGiftMap, Map<Long,
            Boolean> isCombineProductMap, Map<Long, Boolean> skuIsSplitMap, List<OcBOrderItem> isGiftList,
                                  List<OcBOrderItem> isCombineProductList, List<OcBOrderItem> otherList) {
        for (OcBOrderItem orderItem : ocOrderItemList) {
            if ("Y".equals(orderItem.getCanSplit())) {

            }

            Boolean isGift = isGiftMap.get(orderItem.getPsCSkuId());
            Boolean isCombineProduct = isCombineProductMap.get(orderItem.getPsCSkuId());
            if (isGift && (orderItem.getIsGiftSplit() == null || orderItem.getIsGiftSplit() != 2)) {
                isGiftList.add(orderItem);
                continue;
            }
            if (isCombineProduct) {
                //组合商品且不允许拆单
                Boolean skuIsSplit = skuIsSplitMap.get(orderItem.getPsCSkuId());
                if (!skuIsSplit) {
                    isCombineProductList.add(orderItem);
                    continue;
                }
            }
            otherList.add(orderItem);
        }
    }

    //按重量拆暂时无此场景
    //    private void splitStrategyByWeight(List<OcBOrderItem> ocOrderItemList, Map<Long,
    //            StCSplitBeforeSourceStrategyWeightItem> weightStrategyMap) {
    //
    //    }

    private static Collection<List<OcBOrderItem>> splitStrategyBySku(List<OcBOrderItem> ocOrderItemList,
                                                              Map<Long, BigDecimal> skuStrategyMap) {
        log.info("Start.SplitBeforeSourcingStService.splitStrategyBySku");

        //不满足拆单的
        List<OcBOrderItem> restList = new ArrayList<>();
        //赠品不可拆
        List<OcBOrderItem> giftList = new ArrayList<>();

        List<List<OcBOrderItem>> collect = new ArrayList<>();

        Map<String, OcBOrderItem> goodsMarkMap =
                ocOrderItemList.stream().filter(i -> i.getProType() == 4).collect(Collectors.toMap(OcBOrderItem::getGroupGoodsMark, Function.identity()));

        for (OcBOrderItem orderItem : ocOrderItemList) {
            if (orderItem.getProType() == 4) {
                continue;
            }
            if (orderItem.getProType() == 2) {
                if ("Y".equals(orderItem.getCanSplit()) && skuStrategyMap.containsKey(orderItem.getPsCSkuId())) {
                    splitQty(skuStrategyMap, restList, collect, orderItem);
                } else {
                    restList.add(orderItem);
                }
            } else if (orderItem.getIsGift() == 1) {
                if (orderItem.getIsGiftSplit() != null && orderItem.getIsGiftSplit() == 2) {
                    if (skuStrategyMap.containsKey(orderItem.getPsCSkuId())){
                        splitQty(skuStrategyMap, restList, collect, orderItem);
                    }else{
                        restList.add(orderItem);
                    }
                } else {
                    giftList.add(orderItem);
                }
            } else {
                if (skuStrategyMap.containsKey(orderItem.getPsCSkuId())) {
                    splitQty(skuStrategyMap, restList, collect, orderItem);
                } else {
                    restList.add(orderItem);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(giftList)) {
            for (OcBOrderItem gift : giftList) {
                boolean relationFlag = true;
                for (List<OcBOrderItem> items : collect) {
                    List<String> giftRelations =
                            items.stream().filter(o -> o.getGiftRelation() != null).map(OcBOrderItem::getGiftRelation).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(giftRelations)) {
                        if (giftRelations.contains(gift.getGiftRelation())) {
                            items.add(gift);
                            relationFlag = false;
                            break;
                        }
                    }
                }
                if (relationFlag) {
                    for (List<OcBOrderItem> items : collect) {
                        boolean haveMain = items.stream().anyMatch(o -> o.getIsGift() != 1);
                        if (haveMain) {
                            items.add(gift);
                            relationFlag = false;
                            break;
                        }
                    }
                }
                if (relationFlag) {
                    restList.add(gift);
                }

            }
        }

        if (CollectionUtils.isNotEmpty(restList)) {
            collect.add(restList);
        }
        log.info("Finish.SplitBeforeSourcingStService.splitStrategyBySku,value.size={}", collect.size());

        return collect;
    }

    private static void splitQty(Map<Long, BigDecimal> skuStrategyMap, List<OcBOrderItem> restList,
                          List<List<OcBOrderItem>> collect, OcBOrderItem orderItem) {
        BigDecimal splitQty = skuStrategyMap.get(orderItem.getPsCSkuId());
        BigDecimal qty = orderItem.getQty();
        BigDecimal divide = qty.divide(splitQty, 4, BigDecimal.ROUND_HALF_UP);
        if (divide.compareTo(BigDecimal.ONE) > 0) {
            BigDecimal integerPart = divide.setScale(0, RoundingMode.DOWN);
            Integer splitCount = Integer.valueOf(String.valueOf(integerPart));
            for (Integer i = 0; i < splitCount; i++) {
                List<OcBOrderItem> orderItems = new ArrayList<>();
                OcBOrderItem splitItem = new OcBOrderItem();
                BeanUtils.copyProperties(orderItem, splitItem);
                splitItem.setQty(splitQty);
                orderItems.add(splitItem);
                collect.add(orderItems);
            }
            BigDecimal subtractQty = qty.subtract(integerPart.multiply(splitQty));
            if (subtractQty.compareTo(BigDecimal.ZERO) > 0) {
                OcBOrderItem splitItem = new OcBOrderItem();
                BeanUtils.copyProperties(orderItem, splitItem);
                splitItem.setQty(subtractQty);
                restList.add(splitItem);
            }
        } else {
            restList.add(orderItem);
        }
    }

    /**
     * 计算金额
     *
     * @param itemMap    原明细
     * @param orderItems 寻源前拆单逻辑后的明细
     */
    private void computeAmt(Map<Long, OcBOrderItem> itemMap, List<OcBOrderItem> orderItems) {
        for (OcBOrderItem orderItem : orderItems) {
            OcBOrderItem oldItem = itemMap.get(orderItem.getId());
            if (oldItem == null) {
                continue;
            }
            // 成交金额
            BigDecimal qty = oldItem.getQty();
            BigDecimal realAmtSingle = oldItem.getRealAmt().divide(qty, 10, BigDecimal.ROUND_HALF_UP);
            //调整金额
            BigDecimal adjustAmtSingle = oldItem.getAdjustAmt().divide(qty, 10, BigDecimal.ROUND_HALF_UP);
            //优惠金额
            BigDecimal amtDiscountSingle = oldItem.getAmtDiscount().divide(qty, 10, BigDecimal.ROUND_HALF_UP);
            //平摊金额
            BigDecimal orderSplitAmtSingle = oldItem.getOrderSplitAmt().divide(qty, 10, BigDecimal.ROUND_HALF_UP);
            orderItem.setRealAmt(realAmtSingle.multiply(orderItem.getQty()).setScale(4, BigDecimal.ROUND_HALF_UP));
            orderItem.setAdjustAmt(adjustAmtSingle.multiply(orderItem.getQty()).setScale(4, BigDecimal.ROUND_HALF_UP));
            orderItem.setAmtDiscount(amtDiscountSingle.multiply(orderItem.getQty()).setScale(4,
                    BigDecimal.ROUND_HALF_UP));
            orderItem.setOrderSplitAmt(orderSplitAmtSingle.multiply(orderItem.getQty()).setScale(4,
                    BigDecimal.ROUND_HALF_UP));
            orderItem.setPriceActual(orderItem.getRealAmt().divide(orderItem.getQty(), 4, BigDecimal.ROUND_HALF_UP));
        }
    }
//    private static Collection<List<OcBOrderItem>> splitStrategyByWeight(List<OcBOrderItem> ocOrderItemList,
//                                                                 Map<Long, BigDecimal> weightStrategyMap) {
//        log.info("Start.SplitBeforeSourcingStService.splitStrategyByWeight");
//        BigDecimal totWeight =BigDecimal.ZERO;
//        for (OcBOrderItem item : ocOrderItemList) {
//            BigDecimal weight = Optional.ofNullable(item.getStandardWeight()).orElse(BigDecimal.ZERO);
//            BigDecimal qty = item.getQty();
//            totWeight = totWeight.add(weight.multiply(qty));
//        }
//        //不满足拆单的
//        List<OcBOrderItem> restList = new ArrayList<>();
//        //赠品
//        List<OcBOrderItem> giftList = new ArrayList<>();
//        //收集拆单和未拆单的的
//        Table<String, BigDecimal, List<OcBOrderItem>> employeeTable = HashBasedTable.create();
//        Map<String, OcBOrderItem> goodsMarkMap =
//                ocOrderItemList.stream().filter(i -> i.getProType() == 4).collect(Collectors.toMap(OcBOrderItem::getGroupGoodsMark, Function.identity()));
//        //商品重量有null值 排序
//        ocOrderItemList.sort((o1, o2) -> {
//            if (o1.getStandardWeight() == null || o2.getStandardWeight() == null) {
//                return 0;
//            }
//            return o1.getStandardWeight().compareTo(o2.getStandardWeight());
//        });
//        for (OcBOrderItem orderItem : ocOrderItemList) {
//            if (orderItem.getProType() == 4) {
//                continue;
//            }
//            if (orderItem.getProType() == 2) {
//                if ("Y".equals(orderItem.getCanSplit()) && weightStrategyMap.containsKey(orderItem.getMDim4Id())) {
//                    splitWeight(weightStrategyMap, restList, employeeTable, orderItem);
//                } else {
//                    restList.add(orderItem);
//                }
//            } else if (orderItem.getIsGift() == 1) {
//                if (orderItem.getIsGiftSplit() != null && orderItem.getIsGiftSplit() == 2 && weightStrategyMap.containsKey(orderItem.getPsCSkuId())) {
//                    splitWeight(weightStrategyMap, restList, employeeTable, orderItem);
//                } else {
//                    giftList.add(orderItem);
//                }
//            } else {
//                if (weightStrategyMap.containsKey(orderItem.getMDim4Id())) {
//                    splitWeight(weightStrategyMap, restList, employeeTable, orderItem);
//                } else {
//                    restList.add(orderItem);
//                }
//            }
//        }
//        List<List<OcBOrderItem>> collect = new ArrayList<>(employeeTable.values());
//        if (CollectionUtils.isNotEmpty(giftList)) {
//            for (OcBOrderItem gift : giftList) {
//                boolean notBlankGift = true;
//
//                if (StringUtils.isNotBlank(gift.getGiftRelation())) {
//                    for (List<OcBOrderItem> items : collect) {
//                        List<String> giftRelations =
//                                items.stream().filter(o -> o.getGiftRelation() != null).map(OcBOrderItem::getGiftRelation).collect(Collectors.toList());
//                        if (CollectionUtils.isNotEmpty(giftRelations)) {
//                            if (giftRelations.contains(gift.getGiftRelation())) {
//                                items.add(gift);
//                                notBlankGift = false;
//                                break;
//                            }
//                        }
//                    }
//                    if (notBlankGift) {
//                        if (restList.size() > 0) {
//                            restList.add(gift);
//                        } else {
//                            collect.get(0).add(gift);
//                        }
//                    }
//                } else {
//                    boolean restHaveMain = restList.stream().anyMatch(o -> o.getIsGift() != 1);
//                    boolean anchored = true;
//
//                    if (restHaveMain) {
//                        restList.add(gift);
//                        anchored = false;
//                    }
//                    if (anchored) {
//                        for (List<OcBOrderItem> items : collect) {
//                            boolean haveMain = items.stream().anyMatch(o -> o.getIsGift() != 1);
//                            if (haveMain) {
//                                items.add(gift);
//                                anchored = false;
//                                break;
//                            }
//                        }
//                    }
//                    if (anchored) {
//                        collect.get(0).add(gift);
//                    }
//                }
//            }
//        }
//        if (CollectionUtils.isNotEmpty(restList)) {
//            collect.add(restList);
//        }
//        log.info("Finish.SplitBeforeSourcingStService.splitStrategyByWeight,value.size={}", collect.size());
//        BigDecimal reduce = BigDecimal.ZERO;
//        for (List<OcBOrderItem> orderItems : collect) {
//            for (OcBOrderItem orderItem : orderItems) {
//                BigDecimal weight = Optional.ofNullable(orderItem.getStandardWeight()).orElse(BigDecimal.ZERO);
//                BigDecimal qty = orderItem.getQty();
//                reduce = reduce.add(weight.multiply(qty));
//            }
//
//            if (MapUtils.isNotEmpty(goodsMarkMap)) {
//                List<String> goodsMarks = orderItems.stream().filter(o -> o.getProType() == 2)
//                        .map(OcBOrderItem::getGroupGoodsMark).distinct().collect(Collectors.toList());
//                for (String goodsMark : goodsMarks) {
//                    OcBOrderItem item = goodsMarkMap.get(goodsMark);
//                    if (item != null) {
//                        OcBOrderItem ocBOrderItem = new OcBOrderItem();
//                        BeanUtils.copyProperties(item, ocBOrderItem);
//                        orderItems.add(ocBOrderItem);
//                    }
//                }
//            }
//        }
//        if (reduce.compareTo(totWeight) != 0) {
//
//            System.out.println("按重量拆单异常!拆分集合重量之和" + reduce + ",拆单前重量" + totWeight);
//        }
//        return collect;
//    }

    public static List<List<OcBOrderItem>> splitWeightService(List<OcBOrderItem> orderItems,
                                                        Map<Long, BigDecimal> weightStrategyMap) {
        List<List<OcBOrderItem>> items = new ArrayList<>();
        if (weightStrategyMap == null || weightStrategyMap.isEmpty()) {
            items.add(orderItems);
            return items;
        }
        if (orderItems.size() == 1 && orderItems.get(0).getQty().compareTo(BigDecimal.ONE) == 0) {
            items.add(orderItems);
            return items;
        }

        //没有品类的商品不需要拆的
        List<OcBOrderItem> noSplitItems = orderItems.stream().filter(p -> p.getMDim4Id() == null).collect(Collectors.toList());

        List<OcBOrderItem> spiltItems = orderItems.stream().filter(p -> p.getMDim4Id() != null).collect(Collectors.toList());
        //将商品分组出来  组合商品  赠品 挂靠赠品
        List<OcBOrderItem> groupItems = spiltItems.stream().filter(p -> p.getProType() == 2 || p.getProType() == 1).collect(Collectors.toList());
        //不含赠品
        List<OcBOrderItem> normalItems = spiltItems.stream().filter(p -> (p.getIsGift() == null || p.getIsGift() == 0) && p.getProType() == 0).collect(Collectors.toList());
        //赠品
        List<OcBOrderItem> giftItems = spiltItems.stream().filter(p -> (p.getIsGift() != null && p.getIsGift() == 1) && p.getProType() == 0).collect(Collectors.toList());
        handleItem(noSplitItems, groupItems, normalItems, giftItems);
        Map<Long, List<OcBOrderItem>> itemGroup = normalItems.stream().collect(Collectors.groupingBy(OcBOrderItem::getMDim4Id));
        // List<List<SpiltData>> lists = splitItem(itemGroup, weightStrategyMap);
        List<List<SpiltData>> listList = new ArrayList<>();
        for (Long mdim4 : weightStrategyMap.keySet()) {
            BigDecimal weight = weightStrategyMap.get(mdim4);
            List<OcBOrderItem> itemList = itemGroup.get(mdim4);
            if (CollectionUtils.isEmpty(itemList)) {
                continue;
            }
            List<List<SpiltData>> lists = handleSplit(weight, itemList);
            listList.addAll(lists);
            itemGroup.remove(mdim4);
        }
        if (!itemGroup.isEmpty()) {
            Collection<List<OcBOrderItem>> values = itemGroup.values();
            for (List<OcBOrderItem> value : values) {
                noSplitItems.addAll(value);
            }
        }

        List<OcBOrderItem> notSplit = new ArrayList<>();

        List<OcBOrderItem> giftItemsAll = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(noSplitItems)) {
            // items.add(noSplitItems);
            //判断不可拆的是否全部是赠品
            giftItemsAll = noSplitItems.stream().filter(p -> (p.getIsGift() != null && p.getIsGift() == 1) && p.getProType() == 0).collect(Collectors.toList());
            List<OcBOrderItem> noSplit = noSplitItems.stream().filter(p -> ((p.getIsGift() == null || p.getIsGift() == 0))
                    || (p.getIsGift() != null && p.getIsGift() == 1 && p.getProType() == SkuType.COMBINE_PRODUCT)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(noSplit)) {
                notSplit.addAll(noSplit);
            }
        }
        if (CollectionUtils.isNotEmpty(listList)) {
            Map<Long, OcBOrderItem> orderItemMap = orderItems.stream().collect(Collectors.toMap(OcBOrderItem::getId, Function.identity(), (key1, key2) -> key2));
            for (List<SpiltData> list : listList) {
                List<OcBOrderItem> itemList = new ArrayList<>();
                Map<Long, List<SpiltData>> splitMap = list.stream().collect(Collectors.groupingBy(SpiltData::getItemId));
                for (Long itemId : splitMap.keySet()) {
                    List<SpiltData> spiltData = splitMap.get(itemId);
                    BigDecimal qty = spiltData.stream().map(SpiltData::getQty).
                            reduce(BigDecimal.ZERO, BigDecimal::add);
                    OcBOrderItem item = orderItemMap.get(itemId);
                    OcBOrderItem orderItem = new OcBOrderItem();
                    BeanUtils.copyProperties(item, orderItem);
                    orderItem.setQty(qty);
                    itemList.add(orderItem);
                }
                List<OcBOrderItem> collect = itemList.stream().filter(p -> StringUtils.isNotEmpty(p.getGiftRelation())).collect(Collectors.toList());
                Map<String, List<OcBOrderItem>> giftRelationMap = new HashedMap();
                if (CollectionUtils.isNotEmpty(collect)) {
                    giftRelationMap = collect.stream().collect(Collectors.groupingBy(OcBOrderItem::getGiftRelation));
                }
                if (CollectionUtils.isNotEmpty(giftItemsAll)){
                    for (int i = 0; i < giftItemsAll.size(); i++) {
                        OcBOrderItem item = giftItemsAll.get(i);
                        String giftRelation = item.getGiftRelation();
                        Integer isGiftSplit = item.getIsGiftSplit();
                        if (isGiftSplit == null || isGiftSplit == 1) {
                            if (CollectionUtils.isEmpty(notSplit)) {
                                if (StringUtils.isEmpty(giftRelation)){
                                    itemList.add(item);
                                    giftItemsAll.remove(item);
                                    i--;
                                } else {
                                    if (giftRelationMap.containsKey(giftRelation)){
                                        itemList.add(item);
                                        giftItemsAll.remove(item);
                                        i--;
                                    }
                                }
                            }
                        }
                    }
                }
                items.add(itemList);
            }
        }
        if (CollectionUtils.isNotEmpty(giftItemsAll)){
            notSplit.addAll(giftItemsAll);
        }
        if (CollectionUtils.isNotEmpty(notSplit)) {
            items.add(notSplit);
        }
        return items;
    }

    private static BigDecimal getItemsWeight(List<OcBOrderItem> ocOrderItems) {
        if (CollectionUtils.isEmpty(ocOrderItems)) {
            return BigDecimal.ZERO;
        }
        BigDecimal itemsWeight = BigDecimal.ZERO;
        for (OcBOrderItem orderItem : ocOrderItems) {
            BigDecimal standardWeight = Optional.ofNullable(orderItem.getStandardWeight()).orElse(BigDecimal.ZERO);
            BigDecimal qty = orderItem.getQty();
            itemsWeight = itemsWeight.add(qty.multiply(standardWeight));
        }
        return itemsWeight;
    }

    public static int threeSumClosest(int[] nums, int target,boolean newPack){
        int sum = nums[0];
        int closeNum = target-sum;
        for (int i = 1; i < nums.length; i++) {
            int fristNum = nums[i];
            int closeTemp = target -fristNum ;
            if (closeTemp < closeNum) {
                closeNum = closeTemp;
                sum = fristNum;
            }
        }
        return sum;
    }
    @Data
    private static class SpiltData {
        private Long itemId;
        private BigDecimal qty;
        private BigDecimal weight;
        private BigDecimal totalWeight;
    }
    private static List<List<SpiltData>> handleSplit(BigDecimal weight, List<OcBOrderItem> itemList) {
        List<SpiltData> spiltDataList = new ArrayList<>();
        for (OcBOrderItem item : itemList) {
            SpiltData data = new SpiltData();
            BigDecimal standardWeight = item.getStandardWeight() == null ? BigDecimal.ZERO : item.getStandardWeight();
            data.setItemId(item.getId());
            data.setQty(item.getQty());
            data.setWeight(standardWeight);
            data.setTotalWeight(standardWeight.multiply(item.getQty()));
            spiltDataList.add(data);
        }
        //排序
        spiltDataList.sort((o1, o2) -> o2.getTotalWeight().compareTo(o1.getTotalWeight()));
        List<List<SpiltData>> lists = new ArrayList<>();
        List<SpiltData> noSpiltDatas = new ArrayList<>();
        List<SpiltData> preSpiltDatas = new ArrayList<>();
        //预处理
        for (SpiltData data : spiltDataList) {
            //            BigDecimal totalWeight = data.getTotalWeight();
            //            BigDecimal qty = data.getQty();
            //            BigDecimal divide = totalWeight.divide(qty, 4, BigDecimal.ROUND_HALF_UP);
            //            if (divide.compareTo(weight) >= 0) {
            //                noSpiltDatas.add(data);
            //                continue;
            //            }
            //            List<SpiltData> dataList = new ArrayList<>();
            //            if (totalWeight.compareTo(weight) == 0) {
            //                dataList.add(data);
            //                lists.add(dataList);
            //                continue;
            //            }
            //            if (qty.compareTo(BigDecimal.ONE) == 0) {
            //                preSpiltDatas.add(data);
            //                continue;
            //            }
            //            BigDecimal downQty = weight.divide(data.getWeight(), 0, BigDecimal.ROUND_DOWN);
            //            BigDecimal splitQty = qty.divide(downQty, 0, BigDecimal.ROUND_DOWN);
            //            for (int i = 0; i < splitQty.intValue(); i++) {
            //                SpiltData data1 = new SpiltData();
            //                data1.setItemId(data.getItemId());
            //                data1.setQty(downQty);
            //                data1.setWeight(data.getWeight());
            //                data1.setTotalWeight(downQty.multiply(data.getWeight()));
            //                preSpiltDatas.add(data1);
            //            }
            //            BigDecimal multiply = splitQty.multiply(downQty);
            //            BigDecimal overQty = qty.subtract(multiply);
            //            if (overQty.compareTo(BigDecimal.ZERO) != 0) {
            //                SpiltData data1 = new SpiltData();
            //                data1.setItemId(data.getItemId());
            //                data1.setQty(overQty);
            //                data1.setWeight(data.getWeight());
            //                data1.setTotalWeight(overQty.multiply(data.getWeight()));
            //                preSpiltDatas.add(data1);
            //            }
            //        }
            //        if (CollectionUtils.isNotEmpty(noSpiltDatas)) {
            //            lists.add(noSpiltDatas);
            //        }
            BigDecimal qty = data.getQty();
            for (int i = 0; i < qty.intValue(); i++) {
                SpiltData data1 = new SpiltData();
                data1.setItemId(data.getItemId());
                data1.setQty(BigDecimal.ONE);
                data1.setWeight(data.getWeight());
                data1.setTotalWeight(BigDecimal.ONE.multiply(data.getWeight()));
                preSpiltDatas.add(data1);
            }

        }
        handlePreSplit(preSpiltDatas, weight, lists);
        return lists;


    }
    private  static void handlePreSplit(List<SpiltData> preSpiltDatas, BigDecimal weight, List<List<SpiltData>> lists) {
        if (CollectionUtils.isEmpty(preSpiltDatas)) {
            return;
        }
        if (preSpiltDatas.size() == 1) {
            lists.add(preSpiltDatas);
            return;
        }
        preSpiltDatas.sort((o1, o2) -> o2.getTotalWeight().compareTo(o1.getTotalWeight()));
        for (int i = 0; i < preSpiltDatas.size(); i++) {
            SpiltData data = preSpiltDatas.get(i);
            BigDecimal totalWeight = data.getTotalWeight();
            List<SpiltData> dataList = new ArrayList<>();
            for (int j = 0; j < preSpiltDatas.size(); j++) {
                SpiltData data1 = preSpiltDatas.get(j);
                if (j == i) {
                    if (preSpiltDatas.size() == 1) {
                        lists.add(preSpiltDatas);
                        return;
                    }
                    preSpiltDatas.remove(data1);
                    i--;
                    j--;
                    continue;
                }
                BigDecimal totalWeightNew = data1.getTotalWeight();
                if (CollectionUtils.isNotEmpty(dataList)) {
                    totalWeight = dataList.stream().map(SpiltData::getTotalWeight).
                            reduce(BigDecimal.ZERO, BigDecimal::add);
                    totalWeight = totalWeight.add(data.getTotalWeight());
                }
                BigDecimal add = totalWeight.add(totalWeightNew);
                if (add.compareTo(weight) > 0) {
                    continue;
                }
                //小于或者等于
                dataList.add(data1);
                preSpiltDatas.remove(data1);
                j--;
            }

            dataList.add(data);
            //preSpiltDatas.remove(data);
            lists.add(dataList);

        }
        if (CollectionUtils.isNotEmpty(preSpiltDatas)) {
            lists.add(preSpiltDatas);
        }
    }


    private static void handleItem(List<OcBOrderItem> noSplitItems, List<OcBOrderItem> groupItems,
                            List<OcBOrderItem> normalItems, List<OcBOrderItem> giftItems) {
        if (CollectionUtils.isNotEmpty(groupItems)) {
            Map<String, List<OcBOrderItem>> groupMap = groupItems.stream().collect(Collectors.groupingBy(OcBOrderItem::getGroupGoodsMark));
            //判断是否可以拆单
            for (String key : groupMap.keySet()) {
                List<OcBOrderItem> itemList = groupMap.get(key);
                String canSplit = itemList.get(0).getCanSplit();
                if ("Y".equals(canSplit)) {
                    normalItems.addAll(itemList);
                } else {
                    noSplitItems.addAll(itemList);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(giftItems)) {
            for (OcBOrderItem giftItem : giftItems) {
                Integer isGiftSplit = giftItem.getIsGiftSplit();
                if (isGiftSplit == null || isGiftSplit == 1) {
                    noSplitItems.add(giftItem);
                } else {
                    normalItems.add(giftItem);
                }
            }
        }
    }

}