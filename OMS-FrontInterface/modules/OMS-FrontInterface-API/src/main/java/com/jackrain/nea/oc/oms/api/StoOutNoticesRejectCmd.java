package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

/**
 * className: StoOutNoticesRejectCmd
 * description: 全渠道发货单拒单更新零售发货单状态
 *
 * <AUTHOR>
 * create: 2021-07-08
 * @since JDK 1.8
 */
public interface StoOutNoticesRejectCmd {

    /**
     * 重置零售发货单到占单前的状态
     * @param id 单据id
     * @param user 用户
     * @return 操作结果
     */
    ValueHolderV14 resetOrderStatus(Long id, User user,Boolean isRelyOnWing);
}
