package com.jackrain.nea.ac.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.ac.model.AcFPayableAdjustmentDO;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.ac.AcFPayableAdjustmentMapper;
import com.jackrain.nea.oc.oms.model.constant.AcConstant;
import com.jackrain.nea.oc.oms.model.enums.ac.OperatorLogTypeEnum;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.AcBeanUtils;
import com.jackrain.nea.util.AcPayableAdjustmentPushESUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import com.jackrain.nea.web.query.QuerySessionImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * Bll层-作废逻辑
 *
 * <AUTHOR> 陈俊明
 * @since : 2019-03-26
 * create at : 2019-03-26 15:30
 */
@Component
@Slf4j
public class PayableAdjustmentVoidService extends CommandAdapter {
    @Resource
    private AcFPayableAdjustmentMapper acFPayableAdjustmentMapper;

    @Resource
    private PayableAdjustmentSaveService payableAdjustmentSaveService;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        ValueHolder valueHolder = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);

        if (param != null) {
            JSONArray itemArray = AcBeanUtils.makeVoidJsonArray(param);
            JSONArray errorArray = new JSONArray();
            if (itemArray.size() > 0) {
                for (int i = 0; i < itemArray.size(); i++) {
                    Long id = itemArray.getLong(i);
                    try {
                        voidFunction(id, querySession, null);
                    } catch (Exception e) {
                        JSONObject errJo = new JSONObject();
                        errJo.put("objid", id);
                        errJo.put("message", e.getMessage());
                        errorArray.add(errJo);
                    }
                }
            } else {
                throw new NDSException("未找到需要作废的记录！");
            }
            valueHolder = AcBeanUtils.getProcessValueHolder(itemArray, errorArray, "作废");
        } else {
            throw new NDSException("参数为空！");
        }
        return valueHolder;
    }

    public ValueHolder adjustmentVoidById(String logisticsNo, User user) throws NDSException {
        ValueHolder valueHolder = new ValueHolder();
        QueryWrapper<AcFPayableAdjustmentDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("logistics_no", logisticsNo);
        queryWrapper.eq("isactive", AcConstant.IS_ACTIVE_Y);
        List<AcFPayableAdjustmentDO> adjustmentList = acFPayableAdjustmentMapper.selectList(queryWrapper);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("PayableAdjustmentVoidService.adjustmentVoidById,获取丢件单:{}",
                    logisticsNo,"adjustmentVoidById"), adjustmentList);
        }
        if (CollectionUtils.isEmpty(adjustmentList)) {
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put("message", "无该丢件单,物流单号：" + logisticsNo);
            return valueHolder;
        }
        if (null != user) {
            QuerySessionImpl querySession = new QuerySessionImpl(user);
            try {
                adjustmentList.forEach(adjustmentDO -> {
                    voidFunction(adjustmentDO.getId(), querySession, AcConstant.RETURN_VOID_TYPE);
                });
                valueHolder.put("code", ResultCode.SUCCESS);
                valueHolder.put("message", "入库丢件单取消执行成功！");
            } catch (Exception e) {
                ;
                valueHolder.put("code", ResultCode.FAIL);
                valueHolder.put("message", "丢件单logisticsNo：" + logisticsNo + ",异常：" + e.getMessage());
            }
        } else {
            throw new NDSException("参数为空！");
        }
        return valueHolder;
    }

    private void checkStatus(AcFPayableAdjustmentDO acFPayableAdjustmentDO) {
        int iStatus = acFPayableAdjustmentDO.getBillStatus();
        if (iStatus != AcConstant.CON_BILL_STATUS_01) {
            throw new NDSException("单据处于未审核状态才能进行作废！");
        }
    }

    @Transactional(rollbackFor = {Exception.class})
    public void voidFunction(Long objId, QuerySession querySession, String type) {
        AcFPayableAdjustmentDO acFPayableAdjustmentDO = acFPayableAdjustmentMapper.selectById(objId);

        Date date = new Date();

        if (acFPayableAdjustmentDO != null) {
            checkStatus(acFPayableAdjustmentDO);

            //1:未审核 2:已客审 3:已财审 4:已作废
            acFPayableAdjustmentDO.setBillStatus(AcConstant.CON_BILL_STATUS_05);
            acFPayableAdjustmentDO.setIsactive("N");
            acFPayableAdjustmentDO.setDelid(Long.valueOf(querySession.getUser().getId()));
            acFPayableAdjustmentDO.setDelTime(date);
            acFPayableAdjustmentDO.setDelname(querySession.getUser().getName());
            acFPayableAdjustmentDO.setDelename(querySession.getUser().getEname());
            int iResult = acFPayableAdjustmentMapper.updateById(acFPayableAdjustmentDO);
            if (iResult < 0) {
                throw new NDSException("单据编码:" + acFPayableAdjustmentDO.getBillNo() + ",作废失败！");
            }

            //@20200818 新增 入库完成，作废丢件单
            int billLogTypeVal = OperatorLogTypeEnum.OPERATOR_VOID.getVal();
            String billLogTypeText = OperatorLogTypeEnum.OPERATOR_VOID.getText();

            if (AcConstant.RETURN_VOID_TYPE.equals(type)) {
                billLogTypeVal = OperatorLogTypeEnum.OPERATOR_RETURN_VOID.getVal();
                billLogTypeText = OperatorLogTypeEnum.OPERATOR_RETURN_VOID.getText();
            }
            //应付款调整单-操作类型=作废
            ValueHolderV14 v14 = payableAdjustmentSaveService.insertLogFun(querySession.getUser(), objId,
                    billLogTypeVal, billLogTypeText, date);
            //推送ES
            AcPayableAdjustmentPushESUtil.pushOrder(objId);
            if (v14.getCode() == ResultCode.FAIL) {
                throw new NDSException("应付款调整单=>新增操作日志-作废失败!");
            }
        } else {
            throw new NDSException("当前记录不存在！");
        }
    }
}
