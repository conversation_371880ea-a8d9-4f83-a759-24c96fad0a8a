package com.jackrain.nea.oc.oms.service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.HashBasedTable;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018/9/21
 */
@Getter
@Slf4j
public class MainTableRecord {


    public static void main(String[] args) {
        String s="[{\"ADJUST_AMT\":0.0000,\"AD_CLIENT_ID\":37,\"AD_ORG_ID\":27,\"AMT_DISCOUNT\":0.0000,\"AMT_REFUND\":0.0000,\"BUYER_USED_INTEGRAL\":0,\"CREATIONDATE\":1663254386000,\"DISTRIBUTION_PRICE\":0.0000,\"GIFTBAG_SKU\":\"Z1097\",\"GROSS_WEIGHT\":0.0000,\"GROUP_GOODS_MARK\":\"CG50050019\",\"ID\":50050019,\"ISACTIVE\":\"Y\",\"IS_ALLOCATESTOCK\":0,\"IS_BUYER_RATE\":0,\"IS_ENABLE_EXPIRY\":0,\"IS_GIFT\":0,\"IS_LACKSTOCK\":0,\"IS_PRESALESKU\":0,\"IS_SENDOUT\":0,\"MODIFIEDDATE\":1663254572000,\"MODIFIERENAME\":\"root\",\"MODIFIERID\":893,\"MODIFIERNAME\":\"root\",\"NUM_IID\":\"3567373618168133300\",\"OC_B_ORDER_ID\":50063362,\"OOID\":\"4978446087742142181\",\"ORDER_SPLIT_AMT\":0.0000,\"OUTERRCOUNT\":0,\"OWNERENAME\":\"root\",\"OWNERID\":893,\"OWNERNAME\":\"root\",\"PIC_PATH\":\"https://p9-aio.ecombdimg.com/obj/ecom-shop-material/HETnVNDU_m_64a0273e007ad58e1b5712185a49656a_sx_496515_www800-800\",\"PRESELL_TYPE\":0,\"PRICE\":199.0000,\"PRICE_ACTUAL\":199.0000,\"PRICE_LIST\":0.0000,\"PRICE_TAG\":0.0000,\"PRO_TYPE\":4,\"PS_C_CLR_ID\":0,\"PS_C_PRO_ECODE\":\"Z1097\",\"PS_C_PRO_ENAME\":\"1岁以上认养一头牛棒棒哒A2儿童奶∣4箱40盒送饭盒营养早餐【zb】\",\"PS_C_PRO_ID\":100222,\"PS_C_SIZE_ECODE\":\"Z1097\",\"PS_C_SIZE_ID\":0,\"PS_C_SKU_ECODE\":\"Z1097\",\"PS_C_SKU_ID\":100222,\"PS_C_SKU_PT_ECODE\":\"20953567373618168133300-1741881647715340\",\"QTY\":1.0000,\"QTY_REFUND\":0.0000,\"QTY_RETURN_APPLY\":0.0000,\"REAL_AMT\":199.0000,\"REFUND_STATUS\":0,\"SKU_NUMIID\":\"20953567373618168133300-1741881647715340\",\"SKU_SPEC\":\"\",\"TID\":\"4978446087742142181\",\"TITLE\":\"[组合]1岁以上认养一头牛棒棒哒A2儿童奶∣4箱40盒送饭盒营养早餐【zb】[z1097]\",\"VERSION\":0},{\"ADJUST_AMT\":0.0000,\"AD_CLIENT_ID\":37,\"AD_ORG_ID\":27,\"AMT_DISCOUNT\":0.0000,\"AMT_REFUND\":0.0000,\"BARCODE\":\"6970037312121\",\"BUYER_USED_INTEGRAL\":0,\"CAN_SPLIT\":\"Y\",\"CREATIONDATE\":1663254572000,\"DISTRIBUTION_PRICE\":0.0000,\"EXPIRY_DATE_RANGE\":\"20220701-20221231\",\"EXPIRY_DATE_TYPE\":1,\"GIFTBAG_SKU\":\"Z1097\",\"GROUP_GOODS_MARK\":\"CG50050019\",\"GROUP_RADIO\":1.0000,\"ID\":50050086,\"ISACTIVE\":\"Y\",\"IS_ALLOCATESTOCK\":0,\"IS_BUYER_RATE\":0,\"IS_ENABLE_EXPIRY\":1,\"IS_GIFT\":0,\"IS_LACKSTOCK\":0,\"IS_PRESALESKU\":0,\"MODIFIEDDATE\":1663254572000,\"MODIFIERENAME\":\"root\",\"MODIFIERID\":893,\"MODIFIERNAME\":\"root\",\"M_DIM4_ID\":9,\"M_DIM6_ID\":124,\"NUM_IID\":\"3567373618168133300\",\"OC_B_ORDER_ID\":50063362,\"OOID\":\"4978446087742142181\",\"ORDER_SPLIT_AMT\":0.0000,\"OWNERENAME\":\"root\",\"OWNERID\":893,\"OWNERNAME\":\"root\",\"PIC_PATH\":\"https://p9-aio.ecombdimg.com/obj/ecom-shop-material/HETnVNDU_m_64a0273e007ad58e1b5712185a49656a_sx_496515_www800-800\",\"PRICE\":199.0000,\"PRICE_ACTUAL\":99.5000,\"PRICE_LIST\":0.0000,\"PRICE_TAG\":0.0000,\"PRO_TYPE\":2,\"PS_C_CLR_ECODE\":\"RYYTN\",\"PS_C_CLR_ENAME\":\"-\",\"PS_C_CLR_ID\":1,\"PS_C_PRO_ECODE\":\"110101104303\",\"PS_C_PRO_ENAME\":\"认养一头牛A2β-酪蛋白儿童纯牛奶200ml*10原味二提装\",\"PS_C_PRO_ID\":1114,\"PS_C_SIZE_ECODE\":\"RYYTN\",\"PS_C_SIZE_ENAME\":\"-\",\"PS_C_SIZE_ID\":4,\"PS_C_SKU_ECODE\":\"110101104303\",\"PS_C_SKU_ID\":1111,\"PS_C_SKU_PT_ECODE\":\"Z1097\",\"QTY\":2.0000,\"QTY_GROUP\":1.0000,\"QTY_REFUND\":0.0000,\"REAL_AMT\":199.0000,\"REFUND_STATUS\":0,\"SKU_NUMIID\":\"20953567373618168133300-1741881647715340\",\"SKU_SPEC\":\"-,-\",\"STANDARD_WEIGHT\":5.3000,\"TID\":\"4978446087742142181\",\"TITLE\":\"[组合]1岁以上认养一头牛棒棒哒A2儿童奶∣4箱40盒送饭盒营养早餐【zb】[z1097]\",\"VERSION\":0},{\"ADJUST_AMT\":0.0000,\"AD_CLIENT_ID\":37,\"AD_ORG_ID\":27,\"AMT_DISCOUNT\":0.0000,\"AMT_REFUND\":0.0000,\"BARCODE\":\"69111001000151\",\"BUYER_USED_INTEGRAL\":0,\"CAN_SPLIT\":\"Y\",\"CREATIONDATE\":1663254572000,\"DISTRIBUTION_PRICE\":0.0000,\"GIFTBAG_SKU\":\"Z1097\",\"GIFT_TYPE\":\"1\",\"GROUP_GOODS_MARK\":\"CG50050019\",\"GROUP_RADIO\":0.0000,\"ID\":50050087,\"ISACTIVE\":\"Y\",\"IS_ALLOCATESTOCK\":0,\"IS_BUYER_RATE\":0,\"IS_ENABLE_EXPIRY\":1,\"IS_GIFT\":1,\"IS_LACKSTOCK\":0,\"IS_PRESALESKU\":0,\"MODIFIEDDATE\":1663254572000,\"MODIFIERENAME\":\"root\",\"MODIFIERID\":893,\"MODIFIERNAME\":\"root\",\"M_DIM4_ID\":26,\"M_DIM6_ID\":99,\"NUM_IID\":\"3567373618168133300\",\"OC_B_ORDER_ID\":50063362,\"OOID\":\"4978446087742142181\",\"ORDER_SPLIT_AMT\":0.0000,\"OWNERENAME\":\"root\",\"OWNERID\":893,\"OWNERNAME\":\"root\",\"PIC_PATH\":\"https://p9-aio.ecombdimg.com/obj/ecom-shop-material/HETnVNDU_m_64a0273e007ad58e1b5712185a49656a_sx_496515_www800-800\",\"PRICE\":0.0000,\"PRICE_ACTUAL\":0.0000,\"PRO_TYPE\":2,\"PS_C_CLR_ECODE\":\"RYYTN\",\"PS_C_CLR_ENAME\":\"-\",\"PS_C_CLR_ID\":1,\"PS_C_PRO_ECODE\":\"111001000151\",\"PS_C_PRO_ENAME\":\"便携式饭盒\",\"PS_C_PRO_ID\":1328,\"PS_C_SIZE_ECODE\":\"RYYTN\",\"PS_C_SIZE_ENAME\":\"-\",\"PS_C_SIZE_ID\":4,\"PS_C_SKU_ECODE\":\"111001000151\",\"PS_C_SKU_ID\":1324,\"PS_C_SKU_PT_ECODE\":\"Z1097\",\"QTY\":1.0000,\"QTY_GROUP\":1.0000,\"QTY_REFUND\":0.0000,\"REAL_AMT\":0.0000,\"REFUND_STATUS\":0,\"SKU_NUMIID\":\"20953567373618168133300-1741881647715340\",\"SKU_SPEC\":\"-,-\",\"STANDARD_WEIGHT\":0.6180,\"TID\":\"4978446087742142181\",\"TITLE\":\"[组合]1岁以上认养一头牛棒棒哒A2儿童奶∣4箱40盒送饭盒营养早餐【zb】[z1097]\",\"VERSION\":0}]";
        HashMap<Long, Boolean> categoryStrategyMap = new HashMap<>();
        categoryStrategyMap.put(9L,true);
        categoryStrategyMap.put(26L,true);
        HashMap<Long, BigDecimal> skuStrategyMap = new HashMap<>();
        skuStrategyMap.put(1111L,new BigDecimal(1));
        skuStrategyMap.put(1324L,new BigDecimal(1));
         List<OcBOrderItem> orderItemList = JSONObject.parseArray(s, OcBOrderItem.class);
        HashMap<Long, BigDecimal> weightStrategyMap = new HashMap<>();
        weightStrategyMap.put(184L,new BigDecimal(5));

        orderItemList=orderItemList.stream().filter(a->a.getProType()!=4).collect(Collectors.toList());

        Collection<List<OcBOrderItem>> splitOrderItemsPool = null;

        Collection<List<OcBOrderItem>> splitOrderItemsBySku = new ArrayList<>();

        Collection<List<OcBOrderItem>> splitOrderItemsByWeight = new ArrayList<>();
        if (orderItemList.size() > 1) {
            Map<Long, List<OcBOrderItem>> map =
                    orderItemList.stream().filter(o -> o.getMDim4Id() != null).collect(Collectors.groupingBy(OcBOrderItem::getMDim4Id));
                splitOrderItemsPool = splitStrategyByCategory(orderItemList, categoryStrategyMap);

        }
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(splitOrderItemsPool)) {
                for (List<OcBOrderItem> splitOrderItem : splitOrderItemsPool) {
//                    Collection<List<OcBOrderItem>> splitStrategyByWeights = splitStrategyByWeight(splitOrderItem,
//                            weightStrategyMap);
                    List<List<OcBOrderItem>> lists = splitWeightService(splitOrderItem, weightStrategyMap);
                    if (org.apache.commons.collections.CollectionUtils.isNotEmpty(lists)) {
                        splitOrderItemsByWeight.addAll(lists);
                    }
                }
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(splitOrderItemsByWeight)) {
                    splitOrderItemsPool = splitOrderItemsByWeight;
                }
            } else {
                splitOrderItemsPool = splitWeightService(orderItemList, weightStrategyMap);
            }


            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(splitOrderItemsPool)) {
                for (List<OcBOrderItem> splitOrderItem : splitOrderItemsPool) {
                    Collection<List<OcBOrderItem>> splitStrategyBySkus = splitStrategyBySku(splitOrderItem,
                            skuStrategyMap);
                    splitOrderItemsBySku.addAll(splitStrategyBySkus);
                }
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(splitOrderItemsBySku)) {
                    splitOrderItemsPool = splitOrderItemsBySku;
                }
            } else {
                splitOrderItemsPool = splitStrategyBySku(orderItemList, skuStrategyMap);
            }
        }

    private static Collection<List<OcBOrderItem>> splitStrategyByCategory(List<OcBOrderItem> ocOrderItemList,
                                                                   Map<Long,
                                                                           Boolean> categoryStrategyMap) {
        log.info("Start.SplitBeforeSourcingStService.splitStrategyByCategory");
        //收集拆单和未拆单的的
        com.google.common.collect.Table<List<String>, Boolean, List<OcBOrderItem>> employeeTable = HashBasedTable.create();
        //不满足拆单的
        List<OcBOrderItem> restList = new ArrayList<>();
        //其他可拆单的
        List<OcBOrderItem> canSplitList = new ArrayList<>();

        //不允许拆单的赠品
        List<OcBOrderItem> unSplitGiftList = new ArrayList<>();
        //所有赠品挂靠关系
        List<String> allSplitGiftRelation = new ArrayList<>();

        List<OcBOrderItem> isCombineProductList = new ArrayList<>();

        Map<String, OcBOrderItem> goodsMarkMap = new HashMap<>(16);

        //数据分组  不可拆单赠品 、可拆单商品、不可拆单(不包含赠品)
        groupBeforeSplitCategory(ocOrderItemList, canSplitList, isCombineProductList, unSplitGiftList, goodsMarkMap);

        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(isCombineProductList)) {
            Map<String, List<OcBOrderItem>> isCombineMap =
                    isCombineProductList.stream().collect(Collectors.groupingBy(x -> Optional.ofNullable(x.getGroupGoodsMark()).orElse(
                            String.valueOf(Math.random()))));
            Set<String> groupGoodsMark = isCombineMap.keySet();
            for (String mark : groupGoodsMark) {
                List<OcBOrderItem> ocOrderItems = isCombineMap.get(mark);
                OcBOrderItem virtualItem = goodsMarkMap.get(mark);
                //组合商品是否允许拆
                boolean b = ocOrderItems.stream().anyMatch(o -> "N".equals(o.getCanSplit()));
                if (b) {
                    //不允许判断品类是否一致
                    boolean noDim = ocOrderItems.stream().anyMatch(o -> o.getMDim4Id() == null);
                    if (noDim) {
                        restList.addAll(ocOrderItems);
                        restList.add(virtualItem);
                        continue;
                    }
                    Map<Long, List<OcBOrderItem>> dimMap =
                            ocOrderItems.stream().collect(Collectors.groupingBy(OcBOrderItem::getMDim4Id));
                    Set<Long> dimIds = dimMap.keySet();
                    if (dimIds.size() > 1) {
                        //品类不一致 不按品类拆
                        restList.addAll(ocOrderItems);
                        restList.add(virtualItem);
                        continue;
                    }
                    for (Long dim : dimIds) {
                        boolean isSplit = categoryStrategyMap.containsKey(dim);
                        if (isSplit) {
                            canSplitList.addAll(dimMap.get(dim));
                            //赋值后面按品类拆 再去除
                            virtualItem.setMDim4Id(dim);
                            canSplitList.add(virtualItem);
                        } else {
                            restList.addAll(dimMap.get(dim));
                            restList.add(virtualItem);
                        }
                    }

                } else {
                    canSplitList.addAll(ocOrderItems);
                }
            }
        }
        //品类分组 匹配品类的
        Map<Long, List<OcBOrderItem>> mDimMap =
                canSplitList.stream().filter(o -> o.getMDim4Id() != null).collect(Collectors.groupingBy(OcBOrderItem::getMDim4Id));

        List<OcBOrderItem> noMDimList =
                canSplitList.stream().filter(o -> o.getMDim4Id() == null).collect(Collectors.toList());

        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(noMDimList)) {
            restList.addAll(noMDimList);
        }
        Set<Map.Entry<Long, List<OcBOrderItem>>> entries = mDimMap.entrySet();
        for (Map.Entry<Long, List<OcBOrderItem>> entry : entries) {
            Long mDimId = entry.getKey();
            Boolean strategy = categoryStrategyMap.get(mDimId);
            List<OcBOrderItem> value = entry.getValue();
            if (strategy != null) {
                //匹配拆单策略的
                List<String> giftRelations =
                        value.stream().map(OcBOrderItem::getGiftRelation).collect(Collectors.toList());
                //含有主品
                boolean haveMain = value.stream().anyMatch(o -> o.getIsGift() != 1);
                if (haveMain) {
                    allSplitGiftRelation.addAll(giftRelations);
                }
                boolean contains = employeeTable.contains(giftRelations, haveMain);
                //存在多key相同会覆盖
                if (contains) {
                    double random = Math.random();
                    giftRelations.add(String.valueOf(random));
                }
                value.forEach(v -> {
                    //虚拟条码清空品类分组标识
                    if (v.getProType() == 4) {
                        v.setMDim4Id(null);
                    }
                });
                employeeTable.put(giftRelations, haveMain, value);
                continue;
            }
            //可拆单情况且未匹配策略的集合(包含赠品)放到rest
            restList.addAll(value);
        }

        //不可拆单赠品挂靠分组
        Map<String, List<OcBOrderItem>> giftRelationMap =
                unSplitGiftList.stream().collect(Collectors.groupingBy((x -> Optional.ofNullable(x.getGiftRelation()).orElse("默认"))));

        Map<List<String>, List<OcBOrderItem>> splitMainMap = employeeTable.column(true);
        if (MapUtils.isNotEmpty(splitMainMap)){
            Set<List<String>> giftRelationSet = splitMainMap.keySet();
            Set<Map.Entry<String, List<OcBOrderItem>>> giftEntries = giftRelationMap.entrySet();
            //没有绑定主品的赠品
            for (Map.Entry<String, List<OcBOrderItem>> giftEntry : giftEntries) {
                String giftRelation = giftEntry.getKey();
                List<OcBOrderItem> gifts = giftEntry.getValue();
                for (List<String> stringList : giftRelationSet) {
                    if (allSplitGiftRelation.contains(giftRelation)) {
                        //赠品需随主品组成包裹
                        if (stringList.contains(giftRelation)) {
                            List<OcBOrderItem> orderItems = splitMainMap.get(stringList);
                            orderItems.addAll(gifts);
                            splitMainMap.put(stringList, orderItems);
                            break;
                        }
                    } else {
                        List<OcBOrderItem> orderItems = splitMainMap.get(stringList);
                        orderItems.addAll(gifts);
                        splitMainMap.put(stringList, orderItems);
                        break;
                    }
                }
            }
        }else{
            restList.addAll(unSplitGiftList);
        }

        List<List<OcBOrderItem>> lists = new ArrayList<>();
        Collection<List<OcBOrderItem>> values = employeeTable.values();
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(values)) {
            lists.addAll(values);
        }
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(restList)) {
            lists.add(restList);
        }
        log.info("Finish.SplitBeforeSourcingStService.splitStrategyByCategory,value.size={}", lists.size());
        return lists;
    }

    private  static void groupBeforeSplitCategory(List<OcBOrderItem> ocOrderItemList, List<OcBOrderItem> canSplitList,
                                          List<OcBOrderItem> isCombineProductList,
                                          List<OcBOrderItem> unSplitGiftList,
                                          Map<String, OcBOrderItem> goodMarkMap) {
        for (OcBOrderItem orderItem : ocOrderItemList) {
            //为虚拟商品
            if (orderItem.getProType() == 4) {
                goodMarkMap.put(orderItem.getGroupGoodsMark(), orderItem);
                continue;
                //为组合商品
            } else if (orderItem.getProType() == 2) {
                isCombineProductList.add(orderItem);
                continue;
                //为赠品且不允许拆单
            } else if (orderItem.getIsGift() == 1 && (orderItem.getIsGiftSplit() == null || orderItem.getIsGiftSplit() != 2)) {
                unSplitGiftList.add(orderItem);
                continue;
            }
            //允许拆单的集合
            canSplitList.add(orderItem);

        }

    }

    private static Collection<List<OcBOrderItem>> splitStrategyBySku(List<OcBOrderItem> ocOrderItemList,
                                                              Map<Long, BigDecimal> skuStrategyMap) {
        log.info("Start.SplitBeforeSourcingStService.splitStrategyBySku");

        //不满足拆单的
        List<OcBOrderItem> restList = new ArrayList<>();
        //赠品不可拆
        List<OcBOrderItem> giftList = new ArrayList<>();

        List<List<OcBOrderItem>> collect = new ArrayList<>();

        for (OcBOrderItem orderItem : ocOrderItemList) {
            if (orderItem.getProType() == 4) {
                continue;
            }
            if (orderItem.getProType() == 2) {
                if ("Y".equals(orderItem.getCanSplit()) && skuStrategyMap.containsKey(orderItem.getPsCSkuId())) {
                    splitQty(skuStrategyMap, restList, collect, orderItem);
                } else {
                    restList.add(orderItem);
                }
            } else if (orderItem.getIsGift() == 1) {
                if (orderItem.getIsGiftSplit() != null && orderItem.getIsGiftSplit() == 2) {
                    if (skuStrategyMap.containsKey(orderItem.getPsCSkuId())){
                        splitQty(skuStrategyMap, restList, collect, orderItem);
                    }else{
                        restList.add(orderItem);
                    }
                } else {
                    giftList.add(orderItem);
                }
            } else {
                if (skuStrategyMap.containsKey(orderItem.getPsCSkuId())) {
                    splitQty(skuStrategyMap, restList, collect, orderItem);
                } else {
                    restList.add(orderItem);
                }
            }
        }

        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(giftList)) {
            for (OcBOrderItem gift : giftList) {
                boolean relationFlag = true;
                for (List<OcBOrderItem> items : collect) {
                    List<String> giftRelations =
                            items.stream().filter(o -> o.getGiftRelation() != null).map(OcBOrderItem::getGiftRelation).collect(Collectors.toList());
                    if (org.apache.commons.collections.CollectionUtils.isNotEmpty(giftRelations)) {
                        if (giftRelations.contains(gift.getGiftRelation())) {
                            items.add(gift);
                            relationFlag = false;
                            break;
                        }
                    }
                }
                if (relationFlag) {
                    for (List<OcBOrderItem> items : collect) {
                        boolean haveMain = items.stream().anyMatch(o -> o.getIsGift() != 1);
                        if (haveMain) {
                            items.add(gift);
                            relationFlag = false;
                            break;
                        }
                    }
                }
                if (relationFlag) {
                    restList.add(gift);
                }

            }
        }

        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(restList)) {
            collect.add(restList);
        }
        log.info("Finish.SplitBeforeSourcingStService.splitStrategyBySku,value.size={}", collect.size());

        return collect;
    }

    private static void splitQty(Map<Long, BigDecimal> skuStrategyMap, List<OcBOrderItem> restList,
                          List<List<OcBOrderItem>> collect, OcBOrderItem orderItem) {
        BigDecimal splitQty = skuStrategyMap.get(orderItem.getPsCSkuId());
        BigDecimal qty = orderItem.getQty();
        BigDecimal divide = qty.divide(splitQty, 4, BigDecimal.ROUND_HALF_UP);
        if (divide.compareTo(BigDecimal.ONE) > 0) {
            BigDecimal integerPart = divide.setScale(0, RoundingMode.DOWN);
            Integer splitCount = Integer.valueOf(String.valueOf(integerPart));
            for (Integer i = 0; i < splitCount; i++) {
                List<OcBOrderItem> orderItems = new ArrayList<>();
                OcBOrderItem splitItem = new OcBOrderItem();
                BeanUtils.copyProperties(orderItem, splitItem);
                splitItem.setQty(splitQty);
                orderItems.add(splitItem);
                collect.add(orderItems);
            }
            BigDecimal subtractQty = qty.subtract(integerPart.multiply(splitQty));
            if (subtractQty.compareTo(BigDecimal.ZERO) > 0) {
                OcBOrderItem splitItem = new OcBOrderItem();
                BeanUtils.copyProperties(orderItem, splitItem);
                splitItem.setQty(subtractQty);
                restList.add(splitItem);
            }
        } else {
            restList.add(orderItem);
        }
    }
    public static List<List<OcBOrderItem>> splitWeightService(List<OcBOrderItem> orderItems, Map<Long, BigDecimal> weightStrategyMap) {
        List<List<OcBOrderItem>> items = new ArrayList<>();
        if (weightStrategyMap == null || weightStrategyMap.isEmpty()) {
            items.add(orderItems);
            return items;
        }
        if (orderItems.size() == 1 && orderItems.get(0).getQty().compareTo(BigDecimal.ONE) == 0) {
            items.add(orderItems);
            return items;
        }

        //没有品类的商品不需要拆的
        List<OcBOrderItem> noSplitItems = orderItems.stream().filter(p -> p.getMDim4Id() == null).collect(Collectors.toList());

        List<OcBOrderItem> spiltItems = orderItems.stream().filter(p -> p.getMDim4Id() != null).collect(Collectors.toList());
        //将商品分组出来  组合商品  赠品 挂靠赠品
        List<OcBOrderItem> groupItems = spiltItems.stream().filter(p -> p.getProType() == 2 || p.getProType() == 1).collect(Collectors.toList());
        //不含赠品
        List<OcBOrderItem> normalItems = spiltItems.stream().filter(p -> (p.getIsGift() == null || p.getIsGift() == 0) && p.getProType() == 0).collect(Collectors.toList());
        //赠品
        List<OcBOrderItem> giftItems = spiltItems.stream().filter(p -> (p.getIsGift() != null && p.getIsGift() == 1) && p.getProType() != 2).collect(Collectors.toList());
        handleItem(noSplitItems, groupItems, normalItems, giftItems);
        Map<Long, List<OcBOrderItem>> itemGroup = normalItems.stream().collect(Collectors.groupingBy(OcBOrderItem::getMDim4Id));
        // List<List<SpiltData>> lists = splitItem(itemGroup, weightStrategyMap);
        List<List<SpiltData>> listList = new ArrayList<>();
        for (Long mdim4 : weightStrategyMap.keySet()) {
            BigDecimal weight = weightStrategyMap.get(mdim4);
            List<OcBOrderItem> itemList = itemGroup.get(mdim4);
            if (org.apache.commons.collections.CollectionUtils.isEmpty(itemList)){
                continue;
            }
            List<List<SpiltData>> lists =handleSplit(weight, itemList);
            listList.addAll(lists);
            itemGroup.remove(mdim4);
        }
        if (!itemGroup.isEmpty()) {
            Collection<List<OcBOrderItem>> values = itemGroup.values();
            for (List<OcBOrderItem> value : values) {
                noSplitItems.addAll(value);
            }
        }
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(noSplitItems)) {
            items.add(noSplitItems);
        }
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(listList)) {
            Map<Long, OcBOrderItem> orderItemMap = orderItems.stream().collect(Collectors.toMap(OcBOrderItem::getId, Function.identity(), (key1, key2) -> key2));
            for (List<SpiltData> list : listList) {
                List<OcBOrderItem> itemList = new ArrayList<>();
                Map<Long, List<SpiltData>> splitMap = list.stream().collect(Collectors.groupingBy(SpiltData::getItemId));
                for (Long itemId : splitMap.keySet()) {
                    List<SpiltData> spiltData = splitMap.get(itemId);
                    BigDecimal qty = spiltData.stream().map(SpiltData::getQty).
                            reduce(BigDecimal.ZERO, BigDecimal::add);
                    OcBOrderItem item = orderItemMap.get(itemId);
                    OcBOrderItem orderItem = new OcBOrderItem();
                    BeanUtils.copyProperties(item, orderItem);
                    orderItem.setQty(qty);
                    itemList.add(orderItem);

                }
                items.add(itemList);
            }
        }
        return items;
    }
    @Data
    private static class SpiltData {
        private Long itemId;
        private BigDecimal qty;
        private BigDecimal weight;
        private BigDecimal totalWeight;
    }
    private static void handleItem(List<OcBOrderItem> noSplitItems, List<OcBOrderItem> groupItems,
                            List<OcBOrderItem> normalItems, List<OcBOrderItem> giftItems) {
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(groupItems)) {
            Map<String, List<OcBOrderItem>> groupMap = groupItems.stream().collect(Collectors.groupingBy(OcBOrderItem::getGroupGoodsMark));
            //判断是否可以拆单
            for (String key : groupMap.keySet()) {
                List<OcBOrderItem> itemList = groupMap.get(key);
                String canSplit = itemList.get(0).getCanSplit();
                if ("Y".equals(canSplit)) {
                    normalItems.addAll(itemList);
                } else {
                    noSplitItems.addAll(itemList);
                }
            }
        }
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(giftItems)) {
            for (OcBOrderItem giftItem : giftItems) {
                Integer isGiftSplit = giftItem.getIsGiftSplit();
                if (isGiftSplit == null || isGiftSplit == 1) {
                    noSplitItems.add(giftItem);
                } else {
                    normalItems.add(giftItem);
                }
            }
        }
    }

    /**
     * 处理预拆单
     *
     * @param preSpiltDatas
     */
    private static void handlePreSplit(List<SpiltData> preSpiltDatas, BigDecimal weight, List<List<SpiltData>> lists) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(preSpiltDatas)) {
            return;
        }
        if (preSpiltDatas.size() == 1) {
            lists.add(preSpiltDatas);
            return;
        }
        preSpiltDatas.sort((o1, o2) -> o2.getTotalWeight().compareTo(o1.getTotalWeight()));
        for (int i = 0; i < preSpiltDatas.size(); i++) {
            SpiltData data = preSpiltDatas.get(i);
            BigDecimal totalWeight = data.getTotalWeight();
            List<SpiltData> dataList = new ArrayList<>();
            for (int j = 0; j < preSpiltDatas.size(); j++) {
                SpiltData data1 = preSpiltDatas.get(j);
                if (j == i) {
                    if (preSpiltDatas.size() == 1) {
                        lists.add(preSpiltDatas);
                        return;
                    }
                    preSpiltDatas.remove(data1);
                    i--;
                    j--;
                    continue;
                }
                BigDecimal totalWeightNew = data1.getTotalWeight();
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(dataList)) {
                    totalWeight = dataList.stream().map(SpiltData::getTotalWeight).
                            reduce(BigDecimal.ZERO, BigDecimal::add);
                    totalWeight = totalWeight.add(data.getTotalWeight());
                }
                BigDecimal add = totalWeight.add(totalWeightNew);
                if (add.compareTo(weight) > 0) {
                    continue;
                }
                //小于或者等于
                dataList.add(data1);
                preSpiltDatas.remove(data1);
                j--;
            }

            dataList.add(data);
            //preSpiltDatas.remove(data);
            lists.add(dataList);

        }
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(preSpiltDatas)) {
            lists.add(preSpiltDatas);
        }
    }
    private static List<List<SpiltData>> handleSplit(BigDecimal weight, List<OcBOrderItem> itemList) {
        List<SpiltData> spiltDataList = new ArrayList<>();
        for (OcBOrderItem item : itemList) {
            SpiltData data = new SpiltData();
            BigDecimal standardWeight = item.getStandardWeight() == null ? BigDecimal.ZERO : item.getStandardWeight();
            data.setItemId(item.getId());
            data.setQty(item.getQty());
            data.setWeight(standardWeight);
            data.setTotalWeight(standardWeight.multiply(item.getQty()));
            spiltDataList.add(data);
        }
        //排序
        spiltDataList.sort((o1, o2) -> o2.getTotalWeight().compareTo(o1.getTotalWeight()));
        List<List<SpiltData>> lists = new ArrayList<>();
        List<SpiltData> noSpiltDatas = new ArrayList<>();
        List<SpiltData> preSpiltDatas = new ArrayList<>();
        //预处理
        for (SpiltData data : spiltDataList) {
//            BigDecimal totalWeight = data.getTotalWeight();
//            BigDecimal qty = data.getQty();
//            BigDecimal divide = totalWeight.divide(qty, 4, BigDecimal.ROUND_HALF_UP);
//            if (divide.compareTo(weight) >= 0) {
//                noSpiltDatas.add(data);
//                continue;
//            }
//            List<SpiltData> dataList = new ArrayList<>();
//            if (totalWeight.compareTo(weight) == 0) {
//                dataList.add(data);
//                lists.add(dataList);
//                continue;
//            }
//            if (qty.compareTo(BigDecimal.ONE) == 0) {
//                preSpiltDatas.add(data);
//                continue;
//            }
//            BigDecimal downQty = weight.divide(data.getWeight(), 0, BigDecimal.ROUND_DOWN);
//            BigDecimal splitQty = qty.divide(downQty, 0, BigDecimal.ROUND_DOWN);
//            for (int i = 0; i < splitQty.intValue(); i++) {
//                SpiltData data1 = new SpiltData();
//                data1.setItemId(data.getItemId());
//                data1.setQty(downQty);
//                data1.setWeight(data.getWeight());
//                data1.setTotalWeight(downQty.multiply(data.getWeight()));
//                preSpiltDatas.add(data1);
//            }
//            BigDecimal multiply = splitQty.multiply(downQty);
//            BigDecimal overQty = qty.subtract(multiply);
//            if (overQty.compareTo(BigDecimal.ZERO) != 0) {
//                SpiltData data1 = new SpiltData();
//                data1.setItemId(data.getItemId());
//                data1.setQty(overQty);
//                data1.setWeight(data.getWeight());
//                data1.setTotalWeight(overQty.multiply(data.getWeight()));
//                preSpiltDatas.add(data1);
//            }
//        }
//        if (CollectionUtils.isNotEmpty(noSpiltDatas)) {
//            lists.add(noSpiltDatas);
//        }
            BigDecimal qty = data.getQty();
            for (int i = 0; i < qty.intValue(); i++) {
                SpiltData data1 = new SpiltData();
                data1.setItemId(data.getItemId());
                data1.setQty(BigDecimal.ONE);
                data1.setWeight(data.getWeight());
                data1.setTotalWeight(BigDecimal.ONE.multiply(data.getWeight()));
                preSpiltDatas.add(data1);
            }

        }
        handlePreSplit(preSpiltDatas, weight, lists);
        return lists;


    }
}
