package com.jackrain.nea.oc.oms.process.audit.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.OmsAuditFailedReason;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.services.audit.OmsOrderAutoAuditService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 判断订单金额-限制订单金额
 *
 * @author: heliu
 * @since: 2019-01-20
 * create at : 2019-01-20 01:54
 */
@Step(order = 55, description = "判断订单折扣是否在【订单自动审核策略】限制的范围")
@Slf4j
public class Step055CheckOrderDiscount extends BaseAuditOrderProcessStep implements IOmsOrderProcessStep<OcBOrderRelation> {

    @Autowired
    private OmsOrderAutoAuditService omsOrderAutoAuditService;

    @Override
    public ProcessStepResult<OcBOrderRelation> startProcess(OcBOrderRelation orderInfo, ProcessStepResult preStepResult,
                                                            boolean isAutoMakeup, User operateUser) {

        if (log.isDebugEnabled()) {
            log.debug("OrderId={},自动流程----Step055CheckOrderDiscount [检查订单折扣]", orderInfo.getOrderId());
            log.debug("Step055CheckOrderDiscount.startProcess,OcBOrderRelation的值为：{}", orderInfo);
        }

        if (!omsOrderAutoAuditService.checkOrderDiscountScope(orderInfo)){
            String message = "OrderId=" + orderInfo.getOrderId() + ",订单订单折扣不符合自动审核条件,审核失败!";
            log.error("OrderId={},订单订单折扣不符合自动审核条件,审核失败!", orderInfo.getOrderId());

            omsOrderAutoAuditService.updateOrderInfo(orderInfo.getOmsMethod(),orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_35);
                return new ProcessStepResult<>(StepStatus.FINISHED, message);
        }

        return new ProcessStepResult<>(StepStatus.SUCCESS);
        //20210117版本 黄志优修改 注释代码改成校验金额范围
        //boolean result = false;
        ////StCAutocheck ocStCAutocheck = omsOrderAutoAuditService.isExitsStrategy(orderInfo);
        ///*String effectiveCondition = ocStCAutocheck.getEffectiveCondition();
        //boolean isContainsEffectiveFlag = omsOrderAutoAuditService.isContainsEffectiveValue(effectiveCondition,OmsEffectiveConditionEnum.ORDER_AMOUNT.parseValue());
        //if(isContainsEffectiveFlag){
        //    result = omsOrderAutoAuditService.checkOrderAmount(orderInfo);
        //}else{
        //    result = true;
        //}*/
        ////去除勾选框判断-现森马页面无改功能
        //result = omsOrderAutoAuditService.checkOrderAmount(orderInfo);
        //if (result) {
        //    //根据订单店铺查询店铺策略
        //    StCShopStrategyDO stCShopStrategyDO = omsStCShopStrategyService.selectOcStCShopStrategy(orderInfo.getOrderInfo().getCpCShopId());
        //    if (stCShopStrategyDO != null) {
        //        //获取最低折扣的值
        //        //BigDecimal priceActualMin = stCShopStrategyDO.getPriceActualMin();
        //        BigDecimal priceActualMin = BigDecimal.ZERO;
        //        //获取商品明细列表
        //        List<OcBOrderItem> orderItemList = orderInfo.getOrderItemList();
        //        for (OcBOrderItem ocBOrderItem : orderItemList) {
        //            //零售价
        //            BigDecimal tagPrice = ocBOrderItem.getPriceTag() == null ? BigDecimal.ZERO : ocBOrderItem.getPriceTag();
        //            //成交价
        //            BigDecimal price = ocBOrderItem.getPrice() == null ? BigDecimal.ZERO : ocBOrderItem.getPrice();
        //            // 【成交价 < 零售价 * 最低折扣值】，订单不允许审核！
        //            if (price.compareTo(tagPrice.multiply(priceActualMin)) < 0) {
        //                String message = "订单OrderId=" + orderInfo.getOrderId() + "skuCode为：" + ocBOrderItem.getPsCSkuEcode() + "的商品，成交价 < 零售价 * 最低折扣值,审核失败!";
        //                if (log.isDebugEnabled()) {
        //                    log.error("订单OrderId={},skuCode为={},的商品，成交价 < 零售价 * 最低折扣值,审核失败!", orderInfo.getOrderId(), ocBOrderItem.getPsCSkuEcode());
        //                }
        //
        //                omsOrderAutoAuditService.updateOrderInfo(orderInfo, message);
        //                return new ProcessStepResult<>(StepStatus.FINISHED, message);
        //            }
        //        }
        //    }
        //    return new ProcessStepResult<>(StepStatus.SUCCESS, null, Step060CheckOrderReceiverAddress.class);
        //} else {
        //    String message = "订单OrderId=" + orderInfo.getOrderId() + "订单金额大于限制订单金额,审核失败!";
        //
        //    log.error("订单OrderId={},订单金额大于限制订单金额,审核失败!", orderInfo.getOrderId());
        //
        //    omsOrderAutoAuditService.updateOrderInfo(orderInfo, message);
        //    return new ProcessStepResult<>(StepStatus.FINISHED, message);
        //}
    }
}
