package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.BatchOperationGoodsCmd;
import com.jackrain.nea.oc.oms.model.relation.UserPermission;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * 批量操作商品相关
 *
 * @author: 夏继超
 * @since: 2020/2/14
 * create at : 2020/2/14 11:51
 */
@Api(value = "BatchOperationGoodsCtrl", tags = "批量商品操作")
@Slf4j
@RestController
public class BatchOperationGoodsCtrl {

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @Autowired
    private BatchOperationGoodsCmd batchOperationGoodsCmd;
    /**
     * 批量删除商品服务
     *
     * @param request 请求
     * @param obj     传入的参数
     * @return
     */
    @ApiOperation(value = "批量删除商品")
    @RequestMapping(value = "/api/cs/oc/oms/v1/batchDeleteGoods", method = RequestMethod.POST)
    public JSONObject batchDeleteGoods(HttpServletRequest request,
                                       @RequestBody JSONObject obj) {
        ValueHolderV14 vh = new ValueHolderV14();
        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        try {

            UserPermission usrPem = UserPermissionCtrlHelper.currentUserPermission(user, "OC_B_ORDER");
            if (usrPem == null) {
                vh = new ValueHolderV14<>();
                vh.setMessage("未获取到用户权限");
                vh.setCode(ResultCode.FAIL);
                return vh.toJSONObject();
            }

            vh = batchOperationGoodsCmd.batchDeleteGoods(obj, user, usrPem);
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
            //return valueHolder.toJSONObject();
        } catch (NDSException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
        }
    }


    /**
     * 批量跟换商品服务
     *
     * @param request 请求
     * @param obj     传入参数
     * @return
     */
    @ApiOperation(value = "批量跟换商品")
    @Deprecated
    @RequestMapping(value = "/api/cs/oc/oms/v1/bathChangeGoods/Deprecated", method = RequestMethod.POST)
    public JSONObject bathChangeGoods(HttpServletRequest request, @RequestBody JSONObject obj) {
        ValueHolderV14 vh = new ValueHolderV14();
        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        try {
            UserPermission usrPem = UserPermissionCtrlHelper.currentUserPermission(user, "OC_B_ORDER");
            if (usrPem == null) {
                vh = new ValueHolderV14<>();
                vh.setMessage("未获取到用户权限");
                vh.setCode(ResultCode.FAIL);
                return vh.toJSONObject();
            }

            vh = batchOperationGoodsCmd.batchChangeGoods(obj, user, usrPem);
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
            //return valueHolder.toJSONObject();
        } catch (NDSException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
        }
    }

    /**
     * 批量跟换商品服务
     *
     * @param request 请求
     * @param obj     传入参数
     * @return
     */
    @ApiOperation(value = "批量跟换商品(异步)")
    @RequestMapping(value = "/api/cs/oc/oms/v1/bathChangeGoods", method = RequestMethod.POST)
    public JSONObject bathChangeGoodsASync(HttpServletRequest request,
                                           @RequestBody JSONObject obj) {
        log.info(LogUtil.format("批量跟换商品服务,入参:{}",
                "BatchOperationGoodsCtrl.bathChangeGoodsASync"), obj.toJSONString());
        
        ValueHolderV14 vh = new ValueHolderV14();
        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        try {
            UserPermission usrPem = UserPermissionCtrlHelper.currentUserPermission(user, "OC_B_ORDER");
            if (usrPem == null) {
                vh = new ValueHolderV14<>();
                vh.setMessage("未获取到用户权限");
                vh.setCode(ResultCode.FAIL);
                return vh.toJSONObject();
            }

            vh = batchOperationGoodsCmd.batchChangeGoodsAsync(obj, user, usrPem);
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
            //return valueHolder.toJSONObject();
        } catch (NDSException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
        }
    }


    /**
     * 批量添加赠品服务
     *
     * @param request 请求
     * @param obj     传入参数
     * @return
     */
    @ApiOperation(value = "批量添加赠品")
    @RequestMapping(value = "/api/cs/oc/oms/v1/batchAddGoods", method = RequestMethod.POST)
    public JSONObject batchAddGoods(HttpServletRequest request,
                                    @RequestBody JSONObject obj) {
        ValueHolderV14 vh = new ValueHolderV14();
        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        try {

            UserPermission usrPem = UserPermissionCtrlHelper.currentUserPermission(user, "OC_B_ORDER");
            if (usrPem == null) {
                vh = new ValueHolderV14<>();
                vh.setMessage("未获取到用户权限");
                vh.setCode(ResultCode.FAIL);
                return vh.toJSONObject();
            }

            vh = batchOperationGoodsCmd.batchAddGoods(obj, user, usrPem);
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
            //return valueHolder.toJSONObject();
        } catch (NDSException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
        }
    }


    /**
     * 批量添加订单标记服务
     *
     * @param request 请求
     * @param obj     传入参数
     * @return
     */
    @ApiOperation(value = "批量添加订单标记")
    @RequestMapping(value = "/api/cs/oc/oms/v1/batchAddLabel", method = RequestMethod.POST)
    public JSONObject batchAddLabel(HttpServletRequest request,
                                    @RequestBody JSONObject obj) {
        ValueHolderV14 vh = new ValueHolderV14();
        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        try {

            vh = batchOperationGoodsCmd.batchAddLabel(obj, user);
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
            //return valueHolder.toJSONObject();
        } catch (NDSException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
        }
    }

    /**
     * 手工打标：会员加急发货
     *
     * @param request HTTP 请求对象
     * @param obj     传入的参数，JSON对象：{"ids":[1,2,3]}
     * @return
     */
    @ApiOperation(value = "手工打标：会员加急发货")
    @RequestMapping(value = "/api/cs/oc/oms/v1/orderDeliveryUrgent", method = RequestMethod.POST)
    public JSONObject orderDeliveryUrgent(HttpServletRequest request,
                                          @RequestBody JSONObject obj) {
        ValueHolderV14 vh = new ValueHolderV14();
        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        try {

            vh = batchOperationGoodsCmd.orderDeliveryUrgent(obj, user);
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
        } catch (NDSException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
        }
    }
    @ApiOperation(value = "手工打标：会员取消加急发货")
    @RequestMapping(value = "/api/cs/oc/oms/v1/orderUnDeliveryUrgent", method = RequestMethod.POST)
    public JSONObject orderUnDeliveryUrgent(HttpServletRequest request,
                                          @RequestBody JSONObject obj) {
        //记录日志信息
        if (log.isDebugEnabled()) {
            log.debug("Start BatchOperationGoodsCtrl.orderUnDeliveryUrgent.ReceiveParams={}", obj);
        }
        ValueHolderV14 vh = new ValueHolderV14();
        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        try {

            vh = batchOperationGoodsCmd.orderUnDeliveryUrgent(obj, user);
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
        } catch (NDSException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
        }
    }
}
