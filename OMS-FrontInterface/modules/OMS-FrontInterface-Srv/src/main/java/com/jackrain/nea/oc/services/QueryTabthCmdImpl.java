package com.jackrain.nea.oc.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.QueryTabthCmd;
import com.jackrain.nea.oc.oms.services.QueryTabthService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class QueryTabthCmdImpl extends CommandAdapter implements QueryTabthCmd {

    @Autowired
    private QueryTabthService queryTabthService;

    @Override
    public ValueHolder queryTabth(String obj, User userWeb) throws NDSException {
        return queryTabthService.queryTabth(obj, userWeb);
    }

}
