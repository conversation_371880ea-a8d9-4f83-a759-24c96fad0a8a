package com.jackrain.nea.oc.oms.constant;

import com.jackrain.nea.oc.oms.mq.processor.impl.OperateAuditProcessorImpl;
import com.jackrain.nea.oc.oms.mq.processor.impl.OperateMqOrderProcessorImpl;
import com.jackrain.nea.oc.oms.mq.processor.impl.OperateToBeConfirmedProcessorImpl;
import com.jackrain.nea.oc.oms.mq.processor.impl.mq.*;
import com.jackrain.nea.oc.oms.mq.processor.impl.sgMq.*;

/**
 * @program: ryytn-oc-oms-v3.0
 * @description: mq topic tag相关配置
 * @author: haiyang
 * @create: 2023-11-29 14:08
 **/
public class MqConstants {

    /**
     * 扫描入库
     * @see ScanIncomingReceiverMqService
     */
    public static final String TOPIC_R3_OC_OMS_CALL_REFUNDIN= "R3_OC_OMS_CALL_REFUNDIN";
    public static final String TAG_R3_OC_OMS_CALL_REFUNDIN = "OperateRefundIn";


    /**
     * 自动合单
     * @see AutoMergeOrderListenMq
     */
    public static final String TOPIC_R3_OC_OMS_CALL_AUTOMERGE = "R3_OC_OMS_CALL_AUTOMERGE";
    public static final String TAG_R3_OC_OMS_CALL_AUTOMERGE = "OperateMerge";


    /**
     * @see AutoTaskMq
     */
    public static final String TOPIC_R3_OC_OMS_CALL_AUTOTASK = "R3_OC_OMS_CALL_AUTOTASK";
    public static final String TAG_R3_OC_OMS_CALL_AUTOTASK = "tag_platform_delivery_send";


    /**
     * 批量修改仓库
     * @see ModifyWarehouseListenMq
     */
    public static String TOPIC_R3_OC_OMS_MODIFY_WH_LG = "R3_OC_OMS_MODIFY_WH_LG";
    public static String TAG_R3_OC_OMS_MODIFY_WH_LG = "warehouse";

    /**
     * 批量修改物流
     * @see ModifyOrderLogisticsListenMq
     */
    public static String TOPIC_R3_OC_OMS_MODIFY_LOGISTICS = "R3_OC_OMS_MODIFY_LOGISTICS";
    public static String TAG_R3_OC_OMS_MODIFY_LOGISTICS = "logistics";


    /**
     * 物流拦截
     * @see ZtoLogisticsInterceptCallBackListener
     * fixme 可能存在ipcs 和 r3中台都发
     */
    public static String TOPIC_ORDER_LOGISTICS_INTERCEPT = "ORDER_LOGISTICS_INTERCEPT";
    // groupId: GID_BJ_PRO_R3_IPCS
    public static String TAG_FROM_IPCS_ORDER_LOGISTICS_INTERCEPT = "ztoLogisticsIntercept";
    // groupId: GID_BJ_PRO_R3_OC_OMS
    public static String TAG_FROM_OMS_ORDER_LOGISTICS_INTERCEPT = "logisticsInterceptCallBack";


    /************** SG TO OMS START *****************/
    public static String TOPIC_R3_SG_CALLBACK_TO_OMS = "R3_SG_CALLBACK_TO_OMS";

    /**
     * @see UpdateInDistributionMqProcessorImpl
     */
    public static String TAG_Update_In_Distribution = "sg_to_oms_wms_out_creat_receipt";
    /**
     * @see OutOrderMqProcessorImpl
     */
    public static String TAG_Out_Order = "sg_to_oms_out_result_verify_postback";
    /**
     * @see ReturnOrderUpdateWarehouseProcessorImpl
     */
    public static String TAG_Return_Order_Update_Warehouse = "sg_to_oms_in_result_verify_postback";

    /**
     * @see TransferToOmsMqProcessorImpl
     */
    public static String TAG_Transfer_To_Oms = "sg_to_oms_transfer_result_verify_postback";
    /**
     * @see ReturnOrderWmsCallBackMqProcessorImpl
     */
    public static String TAG_Return_Order_Wms_CallBack = "sg_to_oms_wms_in_creat_receipt";

    /************** SG TO OMS END *****************/

    public static String TOPIC_R3_SG_TOBECONFIRM_CALL_BACK = "R3_SG_TOBECONFIRM_CALL_BACK";
    /**
     * 寻仓单占单回执mq监听mq
     * @see TobeConfirmCallBackDeliveryMqProcessorImpl
     */
    public static String TAG_R3_SG_TOBECONFIRM_CALL_BACK_DELIVERY = "OperateTobeConfirmCallBack_Vip_Delivery";
    /**
     * 占单回执mq监听mq
     * @see TobeConfirmCallBackMqProcessorImpl
     */
    public static String TAG_R3_SG_TOBECONFIRM_CALL_BACK = "OperateTobeConfirmCallBack";


    /**
     * @see OperateAuditProcessorImpl
     */
    public static String TOPIC_R3_OC_OMS_CALL_AUTOAUDIT = "R3_OC_OMS_CALL_AUTOAUDIT";

    public static String TAG_R3_OC_OMS_CALL_AUTOAUDIT = "OperateAudit";

    /**
     * @see OperateMqOrderProcessorImpl
     */
    public static String TOPIC_R3_OC_OMS_CALL_TRANSFER = "R3_OC_OMS_CALL_TRANSFER";
    public static String TAG_R3_OC_OMS_CALL_TRANSFER = "OperateMqOrder";


    /**
     * @see OperateToBeConfirmedProcessorImpl
     */
    public static String TOPIC_R3_OC_OMS_CALL_TOBECONFIRMED = "R3_OC_OMS_CALL_TOBECONFIRMED";
    public static String TAG_R3_OC_OMS_CALL_TOBECONFIRMED = "OperateTobeConfirmed";

    /**
     * 订单促销MQ批量处理
     * @see PromBatchExecMqProcessorImpl
     */
    public static String TOPIC_R3_PM_TO_OMS = "R3_PM_TO_OMS";
    public static String TAG_R3_PM_TO_OMS = "pm_to_oms_hold";

    /**
     * 寻源占单待分配（发）
     */
    public static String TOPIC_R3_SG_TOBECONFIRM = "R3_SG_TOBECONFIRM";
    public static String TAG_R3_SG_TOBECONFIRM = "OperateTobeConfirm";


    public static String TOPIC_R3_SG_CALLBACK = "R3_SG_CALLBACK";
    public static String TAG_R3_SG_CALLBACK = "oms_to_sg_out_notices_to_wms";


    public static String TOPIC_R3_OC_OMS_CALL_COMPENSATE_AUTOSPLIT = "R3_OC_OMS_CALL_COMPENSATE_AUTOSPLIT";
    public static String TAG_R3_OC_OMS_CALL_COMPENSATE_AUTOSPLIT = "CompensateOrderSplit";


    /*************** r3-hub start****************/

    /**
     * wms入库单回执
     * @see RefundOrderToWmsReceiptMqListener
     * BJ_DEV_R3_CLOUDHUB_CALL_QIMEN_RET
     * ryytntest_oc_oms
     */
    public static String TOPIC_R3_CLOUDHUB_CALL_QIMEN_RET = "R3_CLOUDHUB_CALL_QIMEN_RET";
    public static String TAG_R3_CLOUDHUB_CALL_QIMEN_RET = "adoptacow_oc_oms";

    /**
     * 入库单确认WMS回传
     * @see RefundOrderToWmsReceiptBackMq
     * BJ_TEST_R3_QIMEN_CALLBACK_CLOUDHUB
     * ryytntest
     */
    public static String TOPIC_R3_QIMEN_CALLBACK_CLOUDHUB = "R3_QIMEN_CALLBACK_CLOUDHUB";
    public static String TAG_R3_QIMEN_CALLBACK_CLOUDHUB = "adoptacow";

    /**
     * 淘宝云枢纽平台发货，结果回执消息
     * @see TbPlatformDeliveryOrderListener
     * TAOBO_LOGISICE_SEND_RET
     * ryytntest
     */
    public static String TOPIC_TAOBO_LOGISICE_SEND_RET = "TAOBO_LOGISICE_SEND_RET";
    public static String TAG_TAOBO_LOGISICE_SEND_RET = "adoptacow";

    /**
     * 京东云枢纽平台发货，结果回执消息
     * @see JdPlatformDeliveryOrderListener
     * JINGDONG_SENDONLINEORDER_RET
     * ryytntest
     */
    public static String TOPIC_JINGDONG_SENDONLINEORDER_RET = "JINGDONG_SENDONLINEORDER_RET";
    public static String TAG_JINGDONG_SENDONLINEORDER_RET = "adoptacow";

    /**
     * 拼多多,苏宁平台发货，结果回执消息
     * @see PddAndSNplatformMq
     * STANDPLAT_LOGISTIC_RET
     * ryytntest
     */
    public static String TOPIC_STANDPLAT_LOGISTIC_RET = "STANDPLAT_LOGISTIC_RET";
    public static String TAG_STANDPLAT_LOGISTIC_RET = "adoptacow";

    /**
     * 奇门退单新增消息
     * @see QimenMessageRouteListenerMq
     * BJ_TEST_R3_QIMEN_POS_CALLBACK_CLOUDHUB
     * ryytntest
     */
    public static String TOPIC_R3_QIMEN_POS_CALLBACK_CLOUDHUB = "R3_QIMEN_POS_CALLBACK_CLOUDHUB";
    public static String TAG_R3_QIMEN_POS_CALLBACK_CLOUDHUB = "adoptacow";

    /**
     * 京东厂直发货回执消息
     * @see JdDirectPlatformMq
     * JINGDONG_DROPSHIP_DELIVERY_RET
     * ryytntest
     */
    public static String TOPIC_JINGDONG_DROPSHIP_DELIVERY_RET = "JINGDONG_DROPSHIP_DELIVERY_RET";
    public static String TAG_JINGDONG_DROPSHIP_DELIVERY_RET = "adoptacow";

    /*************** r3-hub end****************/
}
