package com.jackrain.nea.oc.oms.thread;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 多线程处理池
 *
 * @author: 易邵峰
 * @since: 2019-03-12
 * create at : 2019-03-12 19:50
 */
public class OperateThreadPool {

    /**
     * 基本参数
     */
    private static final int CORE_POOL_SIZE = 10;

    private static final int MAX_POOL_SIZE = 20;

    private static final long KEEP_ALIVE_THREAD_TIME = 60L;

    private static final ArrayBlockingQueue BLOCKING_QUEUE = new ArrayBlockingQueue(10);

    private static final String THREAD_POOL_NAME = "OC_OMS_THREAD_POOL_%d";

    private OperateThreadPool() {

    }

}
