package com.jackrain.nea.oc.oms.model.result;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 查询订单列表结果
 *
 * @author: xiwen.z
 * create at: 2019/3/13 0013
 */
@Data
public class QueryDeficiencyItemResult {


    @J<PERSON>NField(name = "ID")
    private Long id;

    @JSONField(name = "OC_B_DEFICIENCY_ID")
    private Long ocBDeficiencyId;

    @JSONField(name = "BILL_TYPE")
    private Integer billType;

    @JSONField(name = "BILL_NO")
    private String billNo;

    @J<PERSON><PERSON>ield(name = "SOURCE_CODE")
    private String sourceCode;

    @JSONField(name = "QTY")
    private BigDecimal qty;

    @JSONField(name = "TOT_QTY")
    private BigDecimal totQty;

    @JSONField(name = "BILL_STATUS")
    private Integer billStatus;

    @J<PERSON>NField(name = "WMS_CANCEL_STATUS")
    private Integer wmsCancelStatus;

    @JSONField(name = "FLAG")
    private Integer flag;

    @J<PERSON>NField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    public QueryDeficiencyItemResult() {

    }
}
