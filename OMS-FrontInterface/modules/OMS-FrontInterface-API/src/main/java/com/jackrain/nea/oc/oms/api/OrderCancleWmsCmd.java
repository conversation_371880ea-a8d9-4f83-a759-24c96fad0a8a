package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

import java.util.List;

/**
 * @program: R3-OC-OMS-V1.4
 * @author: Lijp
 * @create: 2019-05-13 10:15
 */
public interface OrderCancleWmsCmd {
    /**
     * 订单管理--wms撤回按钮
     *
     * @param ids
     * @param loginUser
     * @return
     */
    ValueHolderV14 cancleWms(List<Long> ids, User loginUser);
}
