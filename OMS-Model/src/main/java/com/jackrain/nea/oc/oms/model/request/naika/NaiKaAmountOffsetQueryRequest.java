package com.jackrain.nea.oc.oms.model.request.naika;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName NaiKaAmountOffsetQueryRequest
 * @Description 奶卡金额冲抵查询
 * <AUTHOR>
 * @Date 2022/9/14 11:20
 * @Version 1.0
 */
@Data
public class NaiKaAmountOffsetQueryRequest implements Serializable {

    /**
     * 零售发货单单据编号
     */
    @JSONField(name = "BILL_NO")
    private String billNo;

    /**
     * 奶卡卡号
     */
    @JSONField(name = "CARD_CODE")
    private String cardCode;

    /**
     * 店铺id
     */
    @J<PERSON><PERSON>ield(name = "CP_C_SHOP_ID")
    private List<Long> cpCShopId;

    /**
     * 时间
     */
    @JSONField(name = "CREATIONDATE")
    private String creationDate;

    /**
     * 汇总单号ID
     */
    @JSONField(name = "OC_B_SAP_SALES_DATA_GATHER_ID")
    private String ocBSapSalesDataGatherId;

    /**
     * 业务类型编码
     */
    @JSONField(name = "MIDDLEGROUND_BILL_TYPE_CODE")
    private String middlegroundBillTypeCode;

    /**
     * 汇总类型
     */
    @JSONField(name = "SUM_TYPE")
    private String sumType;

}
