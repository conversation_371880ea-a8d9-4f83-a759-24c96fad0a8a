package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@TableName(value = "oc_b_refund_batch")
@Data
@Document(index = "oc_b_refund_batch",type = "oc_b_refund_batch")
@ApiModel(value = "oc_b_refund_batch", description = "退货批次表")
public class OcBRefundBatch extends BaseModel {

    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    @ApiModelProperty(value = "ID")
    private Long id;

    @ApiModelProperty(value = "商品标记")
    @JSONField(name = "PRODUCT_MARK")
    @Field(type = FieldType.Keyword)
    private String productMark;

    @ApiModelProperty(value = "商品数量")
    @JSONField(name = "QTY")
    @Field(type = FieldType.Double)
    private BigDecimal qty;

    @ApiModelProperty(value = "调拨店仓id")
    @JSONField(name = "TRANSFER_STORE_ID")
    @Field(type = FieldType.Long)
    private Long transferStoreId;

    @ApiModelProperty(value = "调拨店仓编码")
    @JSONField(name = "TRANSFER_STORE_ECODE")
    @Field(type = FieldType.Keyword)
    private String transferStoreEcode;

    @ApiModelProperty(value = "调拨店仓名称")
    @JSONField(name = "TRANSFER_STORE_ENAME")
    @Field(type = FieldType.Keyword)
    private String transferStoreEname;

    @ApiModelProperty(value = "入库店仓id")
    @JSONField(name = "IN_STORE_ID")
    @Field(type = FieldType.Long)
    private Long inStoreId;

    @ApiModelProperty(value = "入库店仓编码")
    @JSONField(name = "IN_STORE_ECODE")
    @Field(type = FieldType.Keyword)
    private String inStoreEcode;

    @ApiModelProperty(value = "入库店仓名称")
    @JSONField(name = "IN_STORE_ENAME")
    @Field(type = FieldType.Keyword)
    private String inStoreEname;

    @ApiModelProperty(value = "备注")
    @JSONField(name = "REMARK")
    @Field(type = FieldType.Keyword)
    private String remark;

    @ApiModelProperty(value = "批次状态")
    @JSONField(name = "BATCH_STATUS")
    @Field(type = FieldType.Integer)
    private Integer batchStatus;

    @ApiModelProperty(value = "批次号")
    @JSONField(name = "BATCH_NO")
    @Field(type = FieldType.Keyword)
    private String batchNo;

    @ApiModelProperty(value = "修改人姓名")
    @JSONField(name = "VERSION")
    @Field(type = FieldType.Long)
    private Long version;

    @ApiModelProperty(value = "创建人用户名")
    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @ApiModelProperty(value = "修改人姓名")
    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;

    @ApiModelProperty(value = "批次类型")
    @JSONField(name = "BATCH_TYPE")
    @Field(type = FieldType.Long)
    private Long batchType;

    @ApiModelProperty(value = "数字类型备用字段2")
    @JSONField(name = "RESERVE_BIGINT02")
    @Field(type = FieldType.Long)
    private Long reserveBigint02;

    @ApiModelProperty(value = "数字类型备用字段3")
    @JSONField(name = "RESERVE_BIGINT03")
    @Field(type = FieldType.Long)
    private Long reserveBigint03;

    @ApiModelProperty(value = "数字类型备用字段4")
    @JSONField(name = "RESERVE_BIGINT04")
    @Field(type = FieldType.Long)
    private Long reserveBigint04;

    @ApiModelProperty(value = "数字类型备用字段5")
    @JSONField(name = "RESERVE_BIGINT05")
    @Field(type = FieldType.Long)
    private Long reserveBigint05;

    @ApiModelProperty(value = "数字类型备用字段6")
    @JSONField(name = "RESERVE_BIGINT06")
    @Field(type = FieldType.Long)
    private Long reserveBigint06;

    @ApiModelProperty(value = "数字类型备用字段7")
    @JSONField(name = "RESERVE_BIGINT07")
    @Field(type = FieldType.Long)
    private Long reserveBigint07;

    @ApiModelProperty(value = "数字类型备用字段8")
    @JSONField(name = "RESERVE_BIGINT08")
    @Field(type = FieldType.Long)
    private Long reserveBigint08;

    @ApiModelProperty(value = "数字类型备用字段9")
    @JSONField(name = "RESERVE_BIGINT09")
    @Field(type = FieldType.Long)
    private Long reserveBigint09;

    @ApiModelProperty(value = "数字类型备用字段10")
    @JSONField(name = "RESERVE_BIGINT10")
    @Field(type = FieldType.Long)
    private Long reserveBigint10;

    @ApiModelProperty(value = "价格备用字段1")
    @JSONField(name = "RESERVE_DECIMAL01")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal01;

    @ApiModelProperty(value = "价格备用字段2")
    @JSONField(name = "RESERVE_DECIMAL02")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal02;

    @ApiModelProperty(value = "价格备用字段3")
    @JSONField(name = "RESERVE_DECIMAL03")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal03;

    @ApiModelProperty(value = "价格备用字段4")
    @JSONField(name = "RESERVE_DECIMAL04")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal04;

    @ApiModelProperty(value = "价格备用字段5")
    @JSONField(name = "RESERVE_DECIMAL05")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal05;

    @ApiModelProperty(value = "价格备用字段6")
    @JSONField(name = "RESERVE_DECIMAL06")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal06;

    @ApiModelProperty(value = "价格备用字段7")
    @JSONField(name = "RESERVE_DECIMAL07")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal07;

    @ApiModelProperty(value = "价格备用字段8")
    @JSONField(name = "RESERVE_DECIMAL08")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal08;

    @ApiModelProperty(value = "价格备用字段9")
    @JSONField(name = "RESERVE_DECIMAL09")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal09;

    @ApiModelProperty(value = "价格备用字段10")
    @JSONField(name = "RESERVE_DECIMAL10")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal10;

    @ApiModelProperty(value = "文本备用字段1")
    @JSONField(name = "RESERVE_VARCHAR01")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar01;

    @ApiModelProperty(value = "文本备用字段2")
    @JSONField(name = "RESERVE_VARCHAR02")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar02;

    @ApiModelProperty(value = "文本备用字段3")
    @JSONField(name = "RESERVE_VARCHAR03")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar03;

    @ApiModelProperty(value = "文本备用字段4")
    @JSONField(name = "RESERVE_VARCHAR04")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar04;

    @ApiModelProperty(value = "文本备用字段5")
    @JSONField(name = "RESERVE_VARCHAR05")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar05;

    @ApiModelProperty(value = "文本备用字段6")
    @JSONField(name = "RESERVE_VARCHAR06")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar06;

    @ApiModelProperty(value = "文本备用字段7")
    @JSONField(name = "RESERVE_VARCHAR07")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar07;

    @ApiModelProperty(value = "文本备用字段8")
    @JSONField(name = "RESERVE_VARCHAR08")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar08;

    @ApiModelProperty(value = "文本备用字段9")
    @JSONField(name = "RESERVE_VARCHAR09")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar09;

    @ApiModelProperty(value = "文本备用字段10")
    @JSONField(name = "RESERVE_VARCHAR10")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar10;

    @ApiModelProperty(value = "物流单号")
    @JSONField(name = "LOGISTIC_NUMBER")
    @Field(type = FieldType.Keyword)
    private String logisticNumber;

    @ApiModelProperty(value = "收货人")
    @JSONField(name = "RECEIVER_NAME")
    @Field(type = FieldType.Keyword)
    private String receiverName;

    @ApiModelProperty(value = "收货人手机")
    @JSONField(name = "RECEIVER_MOBILE")
    @Field(type = FieldType.Keyword)
    private String receiverMobile;

    @ApiModelProperty(value = "收货人电话")
    @JSONField(name = "RECEIVER_PHONE")
    @Field(type = FieldType.Keyword)
    private String receiverPhone;

    @ApiModelProperty(value = "收货人省id")
    @JSONField(name = "RECEIVER_PROVINCE_ID")
    @Field(type = FieldType.Long)
    private Long receiverProvinceId;

    @ApiModelProperty(value = "收货人省编码")
    @JSONField(name = "RECEIVER_PROVINCE_ECODE")
    @Field(type = FieldType.Keyword)
    private String receiverProvinceEcode;

    @ApiModelProperty(value = "收货人省名称")
    @JSONField(name = "RECEIVER_PROVINCE_ENAME")
    @Field(type = FieldType.Keyword)
    private String receiverProvinceEname;

    @ApiModelProperty(value = "收货人市id")
    @JSONField(name = "RECEIVER_CITY_ID")
    @Field(type = FieldType.Long)
    private Long receiverCityId;

    @ApiModelProperty(value = "收货人市编码")
    @JSONField(name = "RECEIVER_CITY_ECODE")
    @Field(type = FieldType.Keyword)
    private String receiverCityEcode;

    @ApiModelProperty(value = "收货人市名称")
    @JSONField(name = "RECEIVER_CITY_ENAME")
    @Field(type = FieldType.Keyword)
    private String receiverCityEname;

    @ApiModelProperty(value = "收货人区id")
    @JSONField(name = "RECEIVER_DISTRICT_ID")
    @Field(type = FieldType.Long)
    private Long receiverDistrictId;

    @ApiModelProperty(value = "收货人区编码")
    @JSONField(name = "RECEIVER_DISTRICT_ECODE")
    @Field(type = FieldType.Keyword)
    private String receiverDistrictEcode;

    @ApiModelProperty(value = "收货人区名称")
    @JSONField(name = "RECEIVER_DISTRICT_ENAME")
    @Field(type = FieldType.Keyword)
    private String receiverDistrictEname;

    @ApiModelProperty(value = "收货人详细地址")
    @JSONField(name = "RECEIVER_ADDRESS")
    @Field(type = FieldType.Keyword)
    private String receiverAddress;

    @ApiModelProperty(value = "收货人邮编")
    @JSONField(name = "RECEIVER_ZIP")
    @Field(type = FieldType.Keyword)
    private String receiverZip;

    @ApiModelProperty(value = "物流公司id")
    @JSONField(name = "CP_C_LOGISTICS_ID")
    @Field(type = FieldType.Long)
    private Long cpCLogisticsId;

    @ApiModelProperty(value = "物流公司编码")
    @JSONField(name = "CP_C_LOGISTICS_ECODE")
    @Field(type = FieldType.Keyword)
    private String cpCLogisticsEcode;

    @ApiModelProperty(value = "物流公司名称")
    @JSONField(name = "CP_C_LOGISTICS_ENAME")
    @Field(type = FieldType.Keyword)
    private String cpCLogisticsEname;

    @ApiModelProperty(value = "销退入库次品仓id")
    @JSONField(name = "RETURN_PHY_WAREHOUSE_ID")
    @Field(type = FieldType.Long)
    private Long returnPhyWarehouseId;

    @ApiModelProperty(value = "销退入库次品仓编码")
    @JSONField(name = "RETURN_PHY_WAREHOUSE_ECODE")
    @Field(type = FieldType.Keyword)
    private String returnPhyWarehouseEcode;

    @ApiModelProperty(value = "销退入库次品仓名称")
    @JSONField(name = "RETURN_PHY_WAREHOUSE_ENAME")
    @Field(type = FieldType.Keyword)
    private String returnPhyWarehouseEname;

    @ApiModelProperty(value = "次品数量")
    @JSONField(name = "QTY_SUBSTANDARD")
    @Field(type = FieldType.Double)
    private BigDecimal qtySubstandard;
}