package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.api.OcBOrderItemUpdateRemarkCmd;
import com.jackrain.nea.oc.oms.services.OcBOrderItemUpdateRemarkService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> lin yu
 * @date : 2022/7/20 下午4:37
 * @describe :
 */

@Slf4j
@Component
public class OcBOrderItemUpdateRemarkImpl implements OcBOrderItemUpdateRemarkCmd {

    @Autowired
    private OcBOrderItemUpdateRemarkService service;

    @Override
    public ValueHolderV14 updateItemRemark(JSONObject object, User user) {
        return service.updateItemRemark(object, user);
    }
}
