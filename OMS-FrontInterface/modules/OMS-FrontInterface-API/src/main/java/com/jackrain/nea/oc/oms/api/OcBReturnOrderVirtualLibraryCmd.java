package com.jackrain.nea.oc.oms.api;

import com.alibaba.fastjson.JSONArray;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

/**
 * @author: ming.fz
 * @since: 2019/3/26
 */

/**
 * 退单虚拟入库
 */
public interface OcBReturnOrderVirtualLibraryCmd {
    ValueHolderV14 execute(Long id, User user) throws NDSException;

    ValueHolderV14 batchExecute(JSONArray jsonArray, User user) throws NDSException;
}
