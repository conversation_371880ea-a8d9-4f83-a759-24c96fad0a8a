package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.oc.oms.model.request.TransferOrderRequest;
import com.jackrain.nea.oc.oms.model.result.TransferOrderResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

/**
 * @author: chenxiulou
 * @description: 取消时效订单接口
 * @since: 2019-09-04
 * create at : 2019-09-04 16:29
 */
public interface VipTimeOrderCancelCmd {
    ValueHolderV14<TransferOrderResult> startTransferTimeOrderCancel(TransferOrderRequest transferOrderRequest);

    ValueHolderV14 releaseTimeOrderStock(String billNo, User user);
}
