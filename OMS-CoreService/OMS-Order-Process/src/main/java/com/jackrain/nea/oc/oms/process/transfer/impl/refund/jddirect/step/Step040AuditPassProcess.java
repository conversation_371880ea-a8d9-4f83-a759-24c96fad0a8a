package com.jackrain.nea.oc.oms.process.transfer.impl.refund.jddirect.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.StepExeState;
import com.jackrain.nea.oc.oms.model.relation.OmsJDDirectCancelRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.StepExecInfo;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.process.transfer.impl.refund.jddirect.JDDirectTransferSupply;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.web.face.User;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2022/3/30
 */
@Component
@Step(order = 40, description = "audit success")
public class Step040AuditPassProcess extends AbstractJDDirectCancelProcessStep {


    @Override
    public ProcessStepResult<OmsJDDirectCancelRelation> startProcess(OmsJDDirectCancelRelation omsRelation,
                                                                     ProcessStepResult preStepResult,
                                                                     boolean isAutoMakeup, User operateUser) {

        StepExecInfo stepExecInfo = omsRelation.getStepExecInfo();
        List<OmsOrderRelation> ocRelations = omsRelation.getOcOrderRelations();
        for (OmsOrderRelation ocRelation : ocRelations) {
            OcBOrder order = ocRelation.getOcBOrder();
            Integer status = order.getOrderStatus();
            OmsOrderStatus orderStatus = OmsOrderStatus
                    .convert2Enum(status == null ? OmsOrderStatus.TO_BE_ASSIGNED.toInteger() : status);
            boolean isHoldSuccess = omsJDDirectCancelService.holdOcOrderProcess(ocRelation, stepExecInfo);
            if (!isHoldSuccess) {
                continue;
            }
            switch (orderStatus) {
                case UNCONFIRMED:
                case BE_OUT_OF_STOCK:
                    omsJDDirectCancelService.markOrderRefundCompletedProcess(ocRelation, stepExecInfo, operateUser);
                    break;
                case IN_DISTRIBUTION:
                case CHECKED:
                    boolean val = omsJDDirectCancelService.deAuditOcOrderProcess(ocRelation, stepExecInfo, operateUser);
                    if (val) {
                        // 整单取消
                        omsJDDirectCancelService.markOrderRefundCompletedProcess(ocRelation, stepExecInfo, operateUser);
                    }
                    break;
                case WAREHOUSE_DELIVERY:
                case PLATFORM_DELIVERY:
                    omsJDDirectCancelService.warehouseOrPlatformDeliveryProcess(ocRelation, stepExecInfo, operateUser);
                    break;
                case CANCELLED:
                case SYS_VOID:
                    omsJDDirectCancelService.cancelOrVoidProcess(ocRelation, stepExecInfo);
                    break;
                case TO_BE_ASSIGNED:
                case PENDING_WMS:
                    omsJDDirectCancelService.tobeConfirmOrderProcess(ocRelation, stepExecInfo);
                    break;
            }
        }
        return JDDirectTransferSupply.getTransStep(StepExeState.UPDATE);
    }


}
