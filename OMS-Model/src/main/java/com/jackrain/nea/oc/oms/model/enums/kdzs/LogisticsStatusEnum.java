package com.jackrain.nea.oc.oms.model.enums.kdzs;
/**
 * 当前最新物流状态枚举
 * <AUTHOR>
 * @date 2022/4/2 11:02
 *
 */
public enum LogisticsStatusEnum {

    WAIT_ACCEPT	(	"WAIT_ACCEPT"	,	"待揽收"	)	,
    ACCEPT	(	"ACCEPT"	,	"已揽收"	)	,
    TRANSPORT	(	"TRANSPORT"	,	"运输中"	)	,
    DELIVERING	(	"DELIVERING"	,	"派件中"	)	,
    AGENT_SIGN	(	"AGENT_SIGN"	,	"已代签收"	)	,
    SIGN	(	"SIGN"	,	"已签收"	)
    ;

    private String key;

    private String val;

    LogisticsStatusEnum(String key, String val) {
        this.key = key;
        this.val = val;
    }

    public String getKey() {
        return key;
    }

    public String getVal() {
        return val;
    }
}
