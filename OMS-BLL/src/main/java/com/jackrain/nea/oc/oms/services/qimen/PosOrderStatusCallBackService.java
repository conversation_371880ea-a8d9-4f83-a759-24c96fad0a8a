package com.jackrain.nea.oc.oms.services.qimen;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.cpext.model.table.CpCSalesroom;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ip.model.qimen.QimenPosOrderStatusQueryExtRequest;
import com.jackrain.nea.ip.model.qimen.QimenPosOrderStatusQueryResult;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.request.QimenPosOrderStatusRequest;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.util.OmsModelUtil;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * O2O&同城购订单门店接单回传功能
 *
 * @Auther: 黄志优
 * @Date: 2020/8/25 19:24
 */
@Slf4j
@Component
public class PosOrderStatusCallBackService {


    //OcBOrderMapper
    //OmsOrderService ---> selectOmsOrderInfo(String sourceCode)
    /*
     *
     message范例：
    {
    	"request": {
    		"cancleDate": "2017-11-11 10:00:00",
    		"delivery": "0-未取货",
    		"shippingCode": "00000",
    		"zf_message": "21341",
    		"orderBillCode": "A123",
    		"canceler": "hhh",
    		"confirm": "0",
    		"confirmor": "确认人",
    		"confirmDate": "2017-11-11 10:00:00",
    		"shippingSn": "9999",
    		"cancle": "作废",
    		"extendProps": "{ \t\t\"storeCode\": \"134\", \t\t\"storeName\": \"上海康城店\", \t\t\"cp_c_supplier_ename\":
    		* \"上海伯俊\", \t\t\"cp_c_supplier_ecode\": \"20012\", \t\t\"pos_orderno\": \"20200725\", \t\t\"zt_orderno\":
    		*  \"431325\", \t\t\"orgCode\": \"00001\", \t\t\"orgName\": \"上海伯俊\" \t}",
    		"finish": "完成",
    		"finishDate": "2017-11-11 10:00:00",
    		"zf_type": "324",
    		"deliveryDate": "2017-11-1110:00:00",
    		"status": "1"
    	},
    	"method": "3e9nl9rhrg.burgeon.taobao.pos.salesorder.update",
    	"customerid": "SEMIR_BJ_TEST"
    }
     */

    private static final int DEFAULT_SEARCH_ES_PAGE_SIZE = 100;
    @Autowired
    OmsOrderLogService omsOrderLogService;
    @Autowired
    CpRpcService cpRpcService;
    @Autowired
    private OcBOrderMapper orderMapper;
    @Autowired
    private IpRpcService ipRpcService;

    @Transactional(rollbackFor = Exception.class)
    public void receiveOrderStatusCallBack(String message) {

        JSONObject object = JSONObject.parseObject(message);
        JSONObject request = object.getJSONObject("request");
        validateRequest(request);

        //request参数
        String orderBillCode = request.getString("orderBillCode"); // 出库通知单号

        //扩展字段
        JSONObject extendProps = request.getJSONObject("extendProps"); // 扩展字段
        validateExtendProps(extendProps);

        //查询订单
        OcBOrder order = selectOmsOrderInfo(orderBillCode);

        /*
         * 判断订单状态是否配货中，不是则报错，返回错误信息“订单状态不为配货中，无法更新接单状态”
         */
        if (Objects.isNull(order)) {
            throw new NDSException("未找到出库通知单号【" + orderBillCode + "】的订单信息");
        }

        Integer orderStatus = order.getOrderStatus();
        if (Objects.isNull(orderStatus)) {
            throw new NDSException("出库通知单号【" + orderBillCode + "】的订单状态为空");
        }
        List<Integer> statusList = new ArrayList<>();
        statusList.add(OmsOrderStatus.IN_DISTRIBUTION.toInteger());
        statusList.add(OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger());
        statusList.add(OmsOrderStatus.PLATFORM_DELIVERY.toInteger());
        if (!statusList.contains(orderStatus)) {
            throw new NDSException("订单状态不为配货中/仓库发货/平台发货，无法更新接单状态");
        }

        /*
         * 校验报文中的门店编码在系统中是否有对应的门店。没有，报错，返回错误信息“不存在的门店”
         */
        //if (cpRpcExtService.selectCpCSalesroomQueryByCode(storeCode) == null) {
        //    log.error("不存在的门店");
        //    throw new NDSException("不存在的门店");
        //}

        //更新门店接单状态
        updateOrderStoreDeliveryInfo(order, request, extendProps);
    }


    private void validateRequest(JSONObject request) {
        if (request == null) {
            log.error("PosOrderStatusCallBackService.validateRequest，request字段为空");
            throw new NDSException("request字段为空");
        }

        //request参数
        String orderBillCode = request.getString("orderBillCode"); // 出库通知单号
        String status = request.getString("status"); // 状态类型
        String confirm = request.getString("confirm"); // 确认
        String confirmor = request.getString("confirmor"); // 接单人
        String confirmDate = request.getString("confirmDate"); // 接单日期

        if (StringUtils.isBlank(orderBillCode)) {
            log.error("PosOrderStatusCallBackService.validateRequest，出库单号参数为空");
            throw new NDSException("出库单号参数为空");
        }
        if (StringUtils.isBlank(status)) {
            log.error("PosOrderStatusCallBackService.validateRequest，状态类型参数为空");
            throw new NDSException("状态类型参数为空");
        }
        if (StringUtils.isBlank(confirm)) {
            log.error("PosOrderStatusCallBackService.validateRequest，确认参数为空");
            throw new NDSException("确认参数为空");
        }
        if (StringUtils.isBlank(confirmor)) {
            log.error("PosOrderStatusCallBackService.validateRequest，接单人参数为空");
            throw new NDSException("接单人参数为空");
        }
        if (StringUtils.isBlank(confirmDate)) {
            log.error("PosOrderStatusCallBackService.validateRequest，接单日期参数为空");
            throw new NDSException("接单日期参数为空");
        }
    }


    private void validateExtendProps(JSONObject extendProps) {

        if (extendProps == null) {
            log.error("PosOrderStatusCallBackService.validateExtendProps，扩展字段为空");
            throw new NDSException("扩展字段为空");
        }

        //扩展字段
        String storeCode = extendProps.getString("storeCode"); // 门店编码
        String storeName = extendProps.getString("storeName"); // 门店名称
        String supplierCode = extendProps.getString("cp_c_supplier_ecode"); // 结算供应商编码
        String supplierName = extendProps.getString("cp_c_supplier_ename"); // 结算供应商名称
        String ztOrderNo = extendProps.getString("zt_orderno"); // 线上中台系统订单号
        String orgCode = extendProps.getString("orgCode"); // 结算组织编码
        String orgName = extendProps.getString("orgName"); // 结算组织名称

        if (StringUtils.isBlank(storeCode)) {
            log.error("PosOrderStatusCallBackService.validateExtendProps，门店编码参数为空");
            throw new NDSException("门店编码参数为空");
        }
        if (StringUtils.isBlank(storeName)) {
            log.error("PosOrderStatusCallBackService.validateExtendProps，门店名称参数为空");
            throw new NDSException("门店名称参数为空");
        }
        if (StringUtils.isBlank(supplierCode)) {
            log.error("PosOrderStatusCallBackService.validateExtendProps，结算供应商编码参数为空");
            throw new NDSException("结算供应商编码参数为空");
        }
        if (StringUtils.isBlank(supplierName)) {
            log.error("PosOrderStatusCallBackService.validateExtendProps，结算供应商名称参数为空");
            throw new NDSException("结算供应商名称参数为空");
        }

        if (StringUtils.isBlank(ztOrderNo)) {
            log.error("PosOrderStatusCallBackService.validateExtendProps，线上中台系统订单号参数为空");
            throw new NDSException("线上中台系统订单号参数为空");
        }
        if (StringUtils.isBlank(orgCode)) {
            log.error("PosOrderStatusCallBackService.validateExtendProps，结算组织编码参数为空");
            throw new NDSException("结算组织编码参数为空");
        }
        if (StringUtils.isBlank(orgName)) {
            log.error("PosOrderStatusCallBackService.validateExtendProps，结算组织名称参数为空");
            throw new NDSException("结算组织名称参数为空");
        }
    }

    /**
     * 更新门店信息
     *
     * @param order       订单信息
     * @param request     请求参数
     * @param extendProps 扩展字段
     */
    public void updateOrderStoreDeliveryInfo(OcBOrder order, JSONObject request, JSONObject extendProps) {

        Long orderId = order.getId();

        //request参数
        String orderBillCode = request.getString("orderBillCode"); // 出库通知单号
        String confirmStr = request.getString("confirm"); // 确认
        String confirmor = request.getString("confirmor"); // 接单人
        String confirmDate = request.getString("confirmDate"); // 接单日期

        //扩展字段
        String storeCode = extendProps.getString("storeCode"); // 门店编码
        String storeName = extendProps.getString("storeName"); // 门店名称
        String supplierCode = extendProps.getString("cp_c_supplier_ecode"); // 结算供应商编码
        String supplierName = extendProps.getString("cp_c_supplier_ename"); // 结算供应商名称
        String ztOrderNo = extendProps.getString("zt_orderno"); // 线下中台系统订单号
        String orgCode = extendProps.getString("orgCode"); // 结算组织编码
        String orgName = extendProps.getString("orgName"); // 结算组织名称

        Long storeId = getStoreId(storeCode);

        if (orderId == null || 0L == orderId) {
            log.error("PosOrderStatusCallBackService.updateOrderStoreDeliveryInfo，出库通知单号：{},订单ID为空", orderBillCode);
            throw new NDSException("订单ID为空");
        }

        if (storeId == null || 0L == storeId) {
            log.error("PosOrderStatusCallBackService.updateOrderStoreDeliveryInfo，门店编码：{},门店ID为空", storeCode);
            throw new NDSException("门店ID为空");
        }

        int confirm = 0;

        try {
            confirm = Integer.parseInt(confirmStr);
        } catch (NumberFormatException e) {
            log.error("PosOrderStatusCallBackService.updateOrderStoreDeliveryInfo，确认值(confirm)格式有误");
            throw new NDSException("确认值(confirm)格式有误");
        }

        /*OcBOrder ocBOrder = new OcBOrder();
        ocBOrder.setId(orderId);
        ocBOrder.setStoreDeliveryStatus(confirm);
        ocBOrder.setDeliveryStoreId(storeId);
        ocBOrder.setDeliveryStoreCode(storeCode);
        ocBOrder.setDeliveryStoreName(storeName);
        ocBOrder.setSettleSupplierCode(supplierCode);
        ocBOrder.setSettleSupplierName(supplierName);
        ocBOrder.setSettleOrganizationCode(orgCode);
        ocBOrder.setSettleOrganizationName(orgName);
        ocBOrder.setOfflineOrderCode(ztOrderNo);

        OmsModelUtil.setDefault4Upd(ocBOrder, R3SystemUserResource.getSystemRootUser());
        orderMapper.updateById(ocBOrder);

        String logMessage = "";

        if ("0".equals(confirmStr)) {
            logMessage = "订单已下发" + storeName + "门店";
        } else if ("1".equals(confirmStr)) {
            logMessage = storeName + "门店已接单，接单人" + storeName + "，接单时间" + confirmDate;
        } else {
            throw new NDSException("订单已完成");
        }

        //记录接单日志信息
        omsOrderLogService.addUserOrderLog(orderId, order.getBillNo(),
                OrderLogTypeEnum.STORE_MODIFY_ORDER_RECEIVING_STATUS.getKey(),
                logMessage, "", "", SystemUserResource.getRootUser());*/
    }

    private Long getStoreId(String storeCode) {

        CpCSalesroom cpCSalesroom = cpRpcService.selectCpCSalesroomQueryByCode(storeCode);

        if (cpCSalesroom == null || cpCSalesroom.getId() == null) {
            throw new NDSException("门店编号：" + storeCode + "，未找到门店ID");
        } else {
            Long id = cpCSalesroom.getId();
            return id;
        }
    }

    /**
     * 依据orderBillCode查询订单信息
     *
     * @param orderBillCode 出库单号
     * @return OcBOrder
     */
    public OcBOrder selectOmsOrderInfo(String orderBillCode) {
        Long id = this.selectOmsOrderId(orderBillCode);

        if (id > 0L) {
            OcBOrder order = orderMapper.selectById(id);
            if (order != null) {
                OcBOrder temp = order;

                return temp;
            }
        }

        return null;
    }

    /**
     * 依据orderBillCode查询订单在ES中的ID
     *
     * @param orderBillCode 出库单号
     * @return Long
     */
    public Long selectOmsOrderId(String orderBillCode) {
        Long id = null;
        //2020-08-26  先查ES
        try {
            id = ES4Order.findIdBySgPhyOutResultBillNo(orderBillCode);
        } catch (Exception e) {
            log.error(LogUtil.format("Error PosOrderStatusCallBackService.selectOmsOrderId出库通知单:{},查询订单ID异常{}",orderBillCode), orderBillCode,Throwables.getStackTraceAsString(e));
        }
        if (Objects.isNull(id)) {
            id = 0L;
        }
        return id;
    }

    /**
     * 查询订单状态
     *
     * @param request
     * @param operateUser
     * @return
     */
    public ValueHolderV14<QimenPosOrderStatusQueryResult> queryOrderStatus(QimenPosOrderStatusRequest request,
                                                                           User operateUser) {

        ValueHolderV14<QimenPosOrderStatusQueryResult> vh = new ValueHolderV14<>();

        if (operateUser == null) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage("操作人为空，请检查参数后重试！"));
            return vh;
        }
        if (request == null /*|| StringUtils.isBlank(request.getCustomerId())*/
                || StringUtils.isBlank(request.getOrderId())) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage("请求参数为空，请检查参数后重试！"));
            return vh;
        }

        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
        String customerId = config.getProperty("qimen.customerid", "SEMIR_BJ_POS_TEST");
        request.setCustomerId(customerId);

        String orderBillCode = request.getOrderId();

        OcBOrder order = null;
        CpCPhyWarehouse cpCPhyWarehouse = null;
        try {
            order = this.selectOmsOrderInfo(orderBillCode);

            if (Objects.isNull(order)) {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage(Resources.getMessage("未找到出库通知单号【" + orderBillCode + "】的订单信息"));
                return vh;
            }

            Long cpCPhyWarehouseId = order.getCpCPhyWarehouseId();
            cpCPhyWarehouse = cpRpcService.queryByWarehouseId(cpCPhyWarehouseId);
            if (Objects.isNull(cpCPhyWarehouse)) {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage(Resources.getMessage("未找到仓库ID【" + cpCPhyWarehouseId + "】的仓库信息"));
                return vh;
            }
        } catch (Exception e) {
            log.error(LogUtil.format("queryOrderStatus，请求失败，请稍后重试:{}"), Throwables.getStackTraceAsString(e));
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage("请求失败，请稍后重试！"));
            return vh;
        }

        /**
         * 判断O2O仓类型,POS管控仓
         */
        if (StringUtils.equals(cpCPhyWarehouse.getWhType(), OcBOrderConst.CP_C_PHY_WAREHOUSE_TYPE)
                && StringUtils.equals(cpCPhyWarehouse.getIsPos(), "1")) {
            QimenPosOrderStatusQueryExtRequest queryExtRequest = new QimenPosOrderStatusQueryExtRequest();
            queryExtRequest.setCustomerid(request.getCustomerId());
            queryExtRequest.setOrderId(request.getOrderId());
            try {
                vh = ipRpcService.orderStatusQuery(queryExtRequest, operateUser);
            } catch (Exception e) {
                log.error(LogUtil.format("queryOrderStatus，调用IP RPC服务失败返回异常:{}"), Throwables.getStackTraceAsString(e));
                vh.setCode(ResultCode.FAIL);
                vh.setMessage("第三方POS订单状态查询失败");
            }
            return vh;
        } else {
            //    不是pos管控仓
            vh = new ValueHolderV14<>();
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("非O2O订单，不能查询第三方POS订单状态");
            return vh;
        }
    }
}
