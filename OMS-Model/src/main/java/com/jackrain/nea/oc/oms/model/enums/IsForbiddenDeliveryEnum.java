package com.jackrain.nea.oc.oms.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum IsForbiddenDeliveryEnum {

    PASS(0, "可发货"),
    FORBIDDEN(1, "不可发货");
    Integer code;
    String name;

    IsForbiddenDeliveryEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }


    public static String getName(Integer code) {
        if (code == null) {
            return "";
        }
        for (IsForbiddenDeliveryEnum e : IsForbiddenDeliveryEnum.values()) {
            if (e.getCode().equals(code)) {
                return e.getName();
            }
        }
        return "";
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    /**
     * 转为map
     *
     * @return map
     */
    public static Map convertAllToHashVal() {
        Map<Integer, String> m = new HashMap<>();
        for (IsForbiddenDeliveryEnum e : IsForbiddenDeliveryEnum.values()) {
            m.put(e.getCode(), e.getName());
        }
        return m;
    }
}
