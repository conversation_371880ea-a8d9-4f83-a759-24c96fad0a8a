package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.annotation.OmsOperationLog;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.StCShortStockNoSplitStrategyDelCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/3/5
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", group = "oc-core", version = "1.0")
public class StCShortStockNoSplitStrategyDelCmdImpl  extends CommandAdapter implements StCShortStockNoSplitStrategyDelCmd {

    @Autowired
    private StCShortStockNoSplitStrategyService shortStockNoSplitStrategyService;

    @Override
    @OmsOperationLog(mainTableName = "ST_C_SHORT_STOCK_NO_SPLIT_STRATEGY", operationType = "VOID")
    public ValueHolder execute(QuerySession session) throws NDSException {
        List<Long> objIds = shortStockNoSplitStrategyService.getObjIds(session);
        return shortStockNoSplitStrategyService.editShortStockNoSplitStrategyStatus(session.getUser(), objIds, "N");
    }
}
