package com.jackrain.nea.oc.oms.model.enums;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: 黄世新
 * @Date: 2020/3/13 9:42 下午
 * @Version 1.0
 */
public enum InterceptStatus {

    // 未发起拦截 0、发起拦截成功 1、发起拦截失败 2、配送拦截成功 3、配送拦截失败 4'
    NO_LAUNCH_INTERCEPT(0, "未发起拦截"),

    LAUNCH_INTERCEPT_SUCCESS(1, "发起拦截成功"),

    LAUNCH_INTERCEPT_FAIL(2, "发起拦截失败"),

    DELIVERY_INTERCEPT_SUCCESS(3, "配送拦截成功"),

    DELIVERY_INTERCEPT_FAIL(4, "配送拦截失败"),

    /**
     * 业务用字段
     */
    NEED_INTERCEPT(5, "需要发起拦截");

    @Getter
    private Integer code;
    @Getter
    private String message;

    InterceptStatus(Integer code, String message) {
        this.code = code;
        this.message = message;

    }

    public static Map<Integer, String> getMap() {
        Map<Integer, String> map = new HashMap<>();
        for (InterceptStatus value : InterceptStatus.values()) {
            map.put(value.getCode(), value.getMessage());
        }
        map.put(null, "");
        return map;
    }

}
