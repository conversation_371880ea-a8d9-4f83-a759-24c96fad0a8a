package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.oc.oms.model.request.StCHoldOrderRequest;
import com.jackrain.nea.oc.oms.model.table.StCHoldOrderDO;
import com.jackrain.nea.oc.services.st.StCHoldOrderSaveCmdImpl;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySessionImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.Date;


/**
 * <AUTHOR>
 * @Date 2020/07/02 17:07:00
 */
@RestController
@Slf4j
@Api(value = "StcHoldOrderController", description = "调整策略时间")
public class StcHoldOrderController {

    @Autowired
    private StCHoldOrderSaveCmdImpl stCHoldOrderSaveCmd;
    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @ApiOperation(value = "调整策略时间")
    @RequestMapping(path = "/api/cs/oc/oms/holdOrderUpdateStrategyEndTime", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder holdOrderUpdateStrategyEndTime(HttpServletRequest request, @RequestParam("END_TIME") String endTime) {
        ValueHolder valueHolder ;
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        JSONObject param = new JSONObject();

        StCHoldOrderRequest req = new StCHoldOrderRequest();
        req.setIsStrategyTime("Y");
        StCHoldOrderDO stCHoldOrderDO = new StCHoldOrderDO();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date time;
        try {
            time = format.parse(endTime);
        }catch (Exception e){
            return ValueHolderUtils.getFailValueHolder("时间格式不正确！");
        }
        stCHoldOrderDO.setEndTime(time);
        req.setStCHoldOrder(stCHoldOrderDO);
        param.put("fixcolumn", JSONObject.toJSONString(req));
        param.put("objids",request.getParameter("objids"));
        log.info(LogUtil.format("StcHoldOrderController.holdOrderUpdateStrategyEndTime param{}"),
                JSONObject.toJSONString(param));
        event.put("param", param);
        querySession.setEvent(event);
        valueHolder = stCHoldOrderSaveCmd.execute(querySession);
        return valueHolder;
    }
}
