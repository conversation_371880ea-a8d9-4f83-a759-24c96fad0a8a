package com.jackrain.nea.oc.oms.model.enums;

/**
 * 代销资金处理状态（0 已处理  1 未处理）
 *
 * @author: ming.fz
 * @since: 2019-08-27
 */
public enum OrderWorehouseIsRetry {

    /**
     * 已处理
     */
    YES("已处理", 0),

    /**
     * 未处理
     */
    NO("未处理", 1);

    String key;
    long val;

    OrderWorehouseIsRetry(String k, long v) {
        this.key = k;
        this.val = v;
    }

    public String getKey() {
        return key;
    }

    public long getVal() {
        return val;
    }


}
