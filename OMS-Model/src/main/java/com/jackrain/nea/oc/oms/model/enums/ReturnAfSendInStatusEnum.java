package com.jackrain.nea.oc.oms.model.enums;


/**
 * 发货后退款单退货状态枚举
 *
 * @author: 夏继超
 * create at: 2019/9/16
 */
public enum ReturnAfSendInStatusEnum {


    /**
     * 是否可用
     */
    PENDINGSTORAGE("待入库", 0),
    PARTIALWAREHOUSING("部分入库", 1),
    ALLINSTORAGE("全部入库", 2);


    String key;
    int val;

    ReturnAfSendInStatusEnum(String k, int v) {
        this.key = k;
        this.val = v;
    }

    public String getKey() {
        return key;
    }

    public int getVal() {
        return val;
    }

    public Integer integer(){
      return this.getVal();
    }


}


