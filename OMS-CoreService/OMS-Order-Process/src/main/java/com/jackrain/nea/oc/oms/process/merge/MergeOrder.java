package com.jackrain.nea.oc.oms.process.merge;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.burgeon.mq.core.DefaultProducerSend;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.constant.MqConstants;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.AutoMergeOrderMqInfo;
import com.jackrain.nea.oc.oms.model.MergeOrderInfo;
import com.jackrain.nea.oc.oms.model.StCMergeOrderInfo;
import com.jackrain.nea.oc.oms.model.enums.MergeTypeEnum;
import com.jackrain.nea.oc.oms.model.relation.MergeOderGroups;
import com.jackrain.nea.oc.oms.model.result.MergeEsHavingcountResult;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.process.merge.handle.MergeStrategyHandlerFactory;
import com.jackrain.nea.oc.oms.services.OrderMergeService;
import com.jackrain.nea.oc.oms.services.task.OmsAuditTaskService;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.table.StCMergeOrderDO;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.SplitListUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * <AUTHOR> ruan.gz
 * @Description : 合单类
 * @Date : 2020/6/18
 **/
@Slf4j
@Service
public class MergeOrder {
    @Autowired
    private BllRedisLockOrderUtil redisUtil;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OrderMergeService orderMergeService;

    @Autowired
    private PropertiesConf pconf;
//    @Autowired
//    R3MqSendHelper r3MqSendHelper;

    @Autowired
    private DefaultProducerSend defaultProducerSend;

    @Autowired
    StRpcService stRpcService;
    @Autowired
    private MergeStrategyHandlerFactory mergeStrategyHandlerFactory;

    @Autowired
    private OmsAuditTaskService auditTaskService;

    /**
     * 开始自动合单
     *
     * @param autoMergeOrderMqInfo
     */
    public void doAutoStrategy(AutoMergeOrderMqInfo autoMergeOrderMqInfo) {
        if (null == autoMergeOrderMqInfo
                || CollectionUtils.isEmpty(autoMergeOrderMqInfo.getOrderInfoList())) {
            //没有数据返回
            return;
        }

        List<MergeOrderInfo> orderInfoList = autoMergeOrderMqInfo.getOrderInfoList();
        for (MergeOrderInfo info : orderInfoList) {
            if (CollectionUtils.isEmpty(info.getOrderIds()) || info.getOrderIds().size() == 1) {
                continue;
            }
            //执行合单策略
            List<Long> orderIds = info.getOrderIds();
            //锁单
            Map<String, List> lockMap = lockOrder(orderIds);

            //锁集合用来解锁
            List<RedisReentrantLock> lockListKey = lockMap.get("lockList");

            try {
                //ids代表锁定成功的id集合 为空代表全部锁定失败
                List<Long> ids = lockMap.get("ids");
                //已经没有订单或只有1个退出
                if (CollectionUtils.isEmpty(ids) || ids.size() == 1) {
                    continue;
                }
                info.setOrderIds(ids);
                //查询订单信息
                List<OcBOrder> dbOcBOrderList = ocBOrderMapper.selectOrderListByIds(ids);

                //执行策略引擎
                mergeStrategyHandlerFactory.doHandle(ids, info, dbOcBOrderList);

                //合单
                List<Long> mergeIds = info.getOrderIds();
                if (CollectionUtils.isEmpty(mergeIds) || mergeIds.size() == 1) {
                    continue;
                }

                //反审核并合单订单
                orderMergeService.mergeBatch(mergeIds, MergeTypeEnum.AUTO.code(), SystemUserResource.getRootUser());

            } catch (Exception e) {
                log.error(LogUtil.format("合并订单异常:{}", "合并订单异常"), Throwables.getStackTraceAsString(e));
                //合并异常的订单开启自动审核
                auditTaskService.updateAuditTaskByOrderId(orderIds);
            } finally {
                for (RedisReentrantLock lockItem : lockListKey) {
                    lockItem.unlock();
                }
            }
        }
    }

    /**  20210117版本，黄志优修改 代码移到 mergeBatch()
     * 合单
     *
     * @param dbOcBOrderList
     * @param ids
     */
    //private void mergeOrder(List<OcBOrder> dbOcBOrderList, List<Long> ids) {
    //    if (CollectionUtils.isEmpty(dbOcBOrderList) || dbOcBOrderList.size() == 1) {
    //        return;
    //    }
    //    //取出所有的订单,并且订单数>10
    //    if (!orderMergeAutoService.checkOrderNum(dbOcBOrderList, ids)) {
    //        return;
    //    }
    //
    //    //反审核操作
    //    Iterator<OcBOrder> iterator = dbOcBOrderList.iterator();
    //    while (iterator.hasNext()) {
    //        OcBOrder order = iterator.next();
    //        if (order.getOrderStatus().intValue() == OmsOrderStatus.CHECKED.toInteger()) {
    //            boolean b = orderMergeService.toExamineOrder(order, SystemUserResource.getRootUser());
    //            if (!b) {
    //                if (log.isDebugEnabled()) {
    //                    log.debug("OrderId={},合单反审核失败", order.getId());
    //                }
    //                iterator.remove();
    //                ids.remove(order.getId());
    //            }
    //        }
    //    }
    //    if (log.isDebugEnabled()) {
    //        log.debug("自动合单结束ids{}", ids);
    //    }
    //    if (CollectionUtils.isEmpty(ids) || ids.size() == 1) {
    //        return;
    //    }
    //
    //    //开始组装操作数据库的数据
    //    OcBOrderRelation ocBOrderRelation = orderMergeService.getCopyOrderAndItem(dbOcBOrderList,
    //            ids, SystemUserResource.getRootUser());
    //    boolean result = ownMergeOrderService.orderListMerge(dbOcBOrderList,
    //            ocBOrderRelation, SystemUserResource.getRootUser());
    //    if (result) {
    //        orderMergeService.pushEsAddLogAndDistributeLogistics(ocBOrderRelation, SystemUserResource.getRootUser());
    //    }
    //}

    /**
     * 锁订单操作
     *
     * @param orderIds
     * @return
     */
    private Map<String, List> lockOrder(List<Long> orderIds) {
        List<RedisReentrantLock> lockList = new ArrayList<>();
        List<Long> ids = Lists.newArrayList();
        List<Long> failIds = Lists.newArrayList();

        Map<String, List> map = Maps.newHashMap();
        for (Long orderId : orderIds) {
            try {
                String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(orderId);
                RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
                if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                    lockList.add(redisLock);
                    ids.add(orderId);
                } else {
                    log.error(LogUtil.format("合单订单锁定失败", "合单订单锁定失败", orderId));
                    failIds.add(orderId);
                }
            } catch (Exception e) {
                log.error("合单订单锁定异常,Id#{}", orderId);
                failIds.add(orderId);
            }
        }
        map.put("lockList", lockList);
        map.put("ids", ids);
        map.put("failLockIds", failIds);
        //合并锁单失败的订单开启自动审核
        auditTaskService.updateAuditTaskByOrderId(failIds);
        return map;
    }

    /**
     * <AUTHOR> ruan.gz
     * @Description : 查询合单mq信息
     * @Date : 2020/6/19
     **/
    private void pushMergeOrderMq(List<MergeEsHavingcountResult> mergeEsHavingcountResultList, List<StCMergeOrderDO> stCMergeOrderDOList, User rootUser) {
        List<MergeOrderInfo> mergeOrderInfoList = Lists.newArrayList();
        Map<Long, StCMergeOrderDO> map = stCMergeOrderDOList.stream().collect(Collectors.toMap(StCMergeOrderDO::getCpCShopId, stCMergeOrderDO -> stCMergeOrderDO));
        for (MergeEsHavingcountResult mergeEsHavingcountResult : mergeEsHavingcountResultList) {
            if (null == mergeEsHavingcountResult.getMergeOderGroups()) {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("组信息为空",
                            "组信息为空"));
                }
                continue;
            }
            MergeOderGroups mergeOderGroups = mergeEsHavingcountResult.getMergeOderGroups();
            //没有店铺信息直接返回
            Long cpCshopId = mergeOderGroups.getCpCShopId();
            if (!map.containsKey(cpCshopId)) {
                continue;
            }
            StCMergeOrderDO stCMergeOrderDO = map.get(mergeOderGroups.getCpCShopId());
            //未开启的不发mq
            if (Objects.isNull(stCMergeOrderDO.getIsAutomerge()) || stCMergeOrderDO.getIsAutomerge().intValue() == 0) {
                continue;
            }

            //获取一组ids
            JSONArray jsonArray = ES4Order.getIds(mergeEsHavingcountResult);

            List<Long> idsByGroup = getIdsByGroup(jsonArray);
            //1个单直接返回
            if (CollectionUtils.isEmpty(idsByGroup) || idsByGroup.size() == 1) {
                continue;
            }
            MergeOrderInfo info = new MergeOrderInfo();
            info.setMergeOderGroups(mergeOderGroups);
            info.setOrderIds(idsByGroup);
            StCMergeOrderInfo stCMergeOrderInfo = new StCMergeOrderInfo();
            BeanUtils.copyProperties(stCMergeOrderDO, stCMergeOrderInfo);
            info.setStCMergeOrderDO(stCMergeOrderInfo);
            mergeOrderInfoList.add(info);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("合单定时任务预发送id:{}",
                        "合单定时任务预发送"), idsByGroup);
            }
        }
        if (CollectionUtils.isNotEmpty(mergeOrderInfoList)) {
            List<List<MergeOrderInfo>> collections = SplitListUtil.splitList(mergeOrderInfoList, 20);
            for (List<MergeOrderInfo> collection : collections) {
                AutoMergeOrderMqInfo autoMergeOrderMqInfo = new AutoMergeOrderMqInfo();
                autoMergeOrderMqInfo.setOrderInfoList(collection);
                //推送mq
                pushMergeOrderMqMsg(autoMergeOrderMqInfo);

            }
        }
    }


    /**
     * 查询自动合单参数
     */
    public void orderMergeAutoHandle() {
        List<StCMergeOrderDO> stCMergeOrderDOList = stRpcService.queryAllMergeShop();
        if (CollectionUtils.isEmpty(stCMergeOrderDOList)) {
            return;
        }

        List<MergeEsHavingcountResult> result = Lists.newArrayList();
        String[] groups = orderMergeService.getGroups();//分组条件
        JSONObject filterKeyJo = new JSONObject();
        JSONObject whereKeys = orderMergeService.getGroupWhereKeys();//查询条件
        JSONObject jsonObject = ElasticSearchUtil.havingCount(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME,
                OcElasticSearchIndexResources.OC_B_ORDER_TYPE_NAME, whereKeys, filterKeyJo, "1~", groups);
        //去除平台=50 唯品会jitX的订单
        Object data = jsonObject.get("data");
        if (null != data) {
            result = JSONArray.parseArray(data.toString(), MergeEsHavingcountResult.class);
            result = result.stream().filter(x -> null != x.getMergeOderGroups()).collect(Collectors.toList());
        }
        this.pushMergeOrderMq(result, stCMergeOrderDOList, SystemUserResource.getRootUser());
    }

    /**
     * <AUTHOR> ruan.gz
     * @Description : 推送mq
     * @Date : 2020/6/19
     **/
    private void pushMergeOrderMqMsg(AutoMergeOrderMqInfo autoMergeOrderMqInfo) {
//        String tag = pconf.getProperty("r3.oc.oms.merge.mq.tag");
        String tag = MqConstants.TAG_R3_OC_OMS_CALL_AUTOMERGE;
//        String topic = pconf.getProperty("r3.oc.oms.merge.mq.topic");
        String topic = MqConstants.TOPIC_R3_OC_OMS_CALL_AUTOMERGE;
        if (StringUtils.isEmpty(tag) || StringUtils.isEmpty(topic)) {
            return;
        }
        try {
//            r3MqSendHelper.sendMessage(JSON.toJSONString(autoMergeOrderMqInfo), topic, tag);
            //  近15天无使用
            defaultProducerSend.sendTopic(topic, tag, JSON.toJSONString(autoMergeOrderMqInfo), null);

        } catch (Exception e) {
            log.error(LogUtil.format("订单自动合单发送Mq异常,异常信息:{}", "订单自动合单发送Mq异常"), Throwables.getStackTraceAsString(e));
        }
    }

    private List<Long> getIdsByGroup(JSONArray jsonArray) {
        if (null == jsonArray || jsonArray.size() == 0) {
            return null;
        }
        List<Long> ids = Lists.newArrayList();
        for (int i = 0; i < jsonArray.size(); i++) {
//            Map<String, String> idMap = (Map<String, String>) jsonArray.get(i);
//            ids.add(Long.valueOf(idMap.get("ID")));
            // edit by lan 2020/7/17 解决 java.lang.ClassCastException: java.lang.Integer cannot be cast to java.lang.String
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            ids.add(Long.valueOf(jsonObject.getString("ID")));
        }
        return ids;
    }

    /**
     * 能否合并订单 map 值1代表可以合并 0代表不能合并
     *
     * @param idList
     * @return
     */
    public Map<Long, Integer> isMergeByIdList(List<Long> idList) {
        Map<Long, Integer> map = Maps.newHashMap();
        try {
            if (CollectionUtils.isEmpty(idList)) {
                return map;
            }
            List<StCMergeOrderDO> stCMergeOrderDOList = stRpcService.queryAllMergeShop();
            if (CollectionUtils.isEmpty(stCMergeOrderDOList)) {
                //20200714易邵峰修改：增加为空判断，为空则认为都不可以合并。
                for (Long orderId : idList) {
                    map.put(orderId, 0);
                }

                return map;
            }
            Map<Long, StCMergeOrderDO> mergeMap = stCMergeOrderDOList.stream()
                    .collect(Collectors.toMap(StCMergeOrderDO::getCpCShopId, stCMergeOrderDO -> stCMergeOrderDO));
            List<OcBOrder> ocBOrderList = ocBOrderMapper.selectByIdsList(idList);
            if (CollectionUtils.isEmpty(ocBOrderList)) {
                return map;
            }

//            List<MergeOrderInfo> mergeOrderInfoList = Lists.newArrayList();
//            List<String> keyList = Lists.newArrayList();
            for (OcBOrder order : ocBOrderList) {

                MergeOderGroups mergeOderGroups = new MergeOderGroups();
                //查询能够合单的数据
                List<Long> idsByGroup = getIsMergeOrderListByGroup(order, mergeOderGroups);
                if (CollectionUtils.isEmpty(idsByGroup)) {
                    map.put(order.getId(), 0);
                    continue;
                }
                // 只有待审核的订单才能合并
                List<OcBOrder> dbOcBOrderList = ocBOrderMapper.selectAuditOrderInfoList(idsByGroup);

                MergeOrderInfo info = new MergeOrderInfo();
                info.setOrderIds(idsByGroup);
                info.setOrderList(dbOcBOrderList);
                info.setOcBOrder(order);

                if (!mergeMap.containsKey(order.getCpCShopId())) {
                    map.put(order.getId(), 0);
                    continue;
                }

                info.setMergeOderGroups(mergeOderGroups);
                StCMergeOrderDO stCMergeOrderDO = mergeMap.get(order.getCpCShopId());
                StCMergeOrderInfo stCMergeOrderInfo = new StCMergeOrderInfo();
                BeanUtils.copyProperties(stCMergeOrderDO, stCMergeOrderInfo);
                info.setStCMergeOrderDO(stCMergeOrderInfo);
                //执行策略引擎
                mergeStrategyHandlerFactory.doHandle(idsByGroup, info, dbOcBOrderList);
                //合单
                List<Long> mergeIds = info.getOrderIds();
                if (CollectionUtils.isEmpty(mergeIds) || mergeIds.size() == 1) {
                    //只剩下一个单 不能合并
                    map.put(order.getId(), 0);
                } else {
                    //可以合并  审核过来的单都是未审核 不需要反审判断
                    map.put(order.getId(), 1);
//                    try {
//                        String key = mergeIds.stream().map(String::valueOf).collect(Collectors.joining(","));
//                        if (!keyList.contains(key)) {
//                            //对查出来不重复的进行添加
//                            keyList.add(key);
//                            mergeOrderInfoList.add(info);
//                            if (log.isDebugEnabled()) {
//                                log.debug("能否合并订单可以合的订单预发送id{}", key);
//                            }
//                        }
//                    } catch (Exception e) {
//                        log.error("审核发送mq查询错误", e);
//                    }
                }

            }

//            try {
//                if (CollectionUtils.isNotEmpty(mergeOrderInfoList)) {
//                    List<List<MergeOrderInfo>> collections = SplitListUtil.splitList(mergeOrderInfoList, 20);
//                    for (List<MergeOrderInfo> collection : collections) {
//                        AutoMergeOrderMqInfo autoMergeOrderMqInfo = new AutoMergeOrderMqInfo();
//                        autoMergeOrderMqInfo.setOrderInfoList(collection);
//                        //推送mq
//                        pushMergeOrderMqMsg(autoMergeOrderMqInfo);
//
//                    }
//                }
//            } catch (Exception e) {
//                log.error("审核发送mq错误{}", e);
//            }
        } catch (Exception e) {
            throw new NDSException("能否合并订单返回异常!");
        }
        return map;
    }

    /**
     * 是否满足前提校验规则
     *
     * @param order
     * @return
     */
    private boolean isSatisfiedMergeRule(OcBOrder order) {
        return Objects.nonNull(order)
                && Objects.nonNull(order.getCpCPhyWarehouseId())
                && Objects.nonNull(order.getCpCRegionAreaId())
                && Objects.nonNull(order.getCpCRegionCityId())
                && Objects.nonNull(order.getCpCRegionProvinceId())
                && Objects.nonNull(order.getCpCShopId())
                && Objects.nonNull(order.getPayType())
                && Objects.nonNull(order.getPlatform())
                && Objects.nonNull(order.getUserNick())
                && Objects.nonNull(order.getReceiverAddress())
                && Objects.nonNull(order.getOrderType())
                && Objects.nonNull(order.getReceiverName());
    }

    /**
     * 查询能够合单的数据
     *
     * @param order
     * @return
     */
    private List<Long> getIsMergeOrderListByGroup(OcBOrder order, MergeOderGroups mergeOderGroups) {
        // @20200730 增加非空的校验
        boolean isSatisfied = isSatisfiedMergeRule(order);

        if (!isSatisfied) {
            return Lists.newArrayList();
        }

        List<Long> ids = ES4Order.findMergeOrderIdByGroup(order, mergeOderGroups);
        return ids;
    }
}
