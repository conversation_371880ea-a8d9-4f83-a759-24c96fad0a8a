package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBOrderImportBeefOrderCmd;
import com.jackrain.nea.oc.oms.model.enums.GiftTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.vo.OcBOrderImpBeefOrderVO;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.async.AsyncTaskBody;
import com.jackrain.nea.web.common.AsyncTaskManager;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.FastDateFormat;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.InputStream;
import java.text.NumberFormat;
import java.text.ParseException;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

/**
 * @author: 李龙飞
 * @create: 2019-05-10 17:10
 **/
@RestController
@RequestMapping("/api/cs/oc/oms/v1/beef/")
@Api(value = "OcBOrderImportBeefOrderController", tags = "订单管理导入")
@Slf4j
public class OcBOrderImportBeefOrderController {

    public static final String cellStr = "cell_";
    public static final String rowStr = "row_";
    public static final int NUMERIC = 0;
    public static final int FORMULA = 2;
    private static final NumberFormat nf = NumberFormat.getInstance();
    @Autowired
    private OcBOrderImportBeefOrderCmd ocBOrderImportBeefOrderCmd;
    @Autowired
    private AsyncTaskManager asyncTaskManager;
    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;
    @Autowired
    private ThreadPoolTaskExecutor commonTaskExecutor;

    @ApiOperation(value = "订单管理导入")
    @RequestMapping(path = "importOcBOrder", method = RequestMethod.POST)
    public ValueHolderV14<Long> importByPro(HttpServletRequest request,
                                            @RequestParam(value = "file") MultipartFile file,
                                            @RequestParam(value = "order_status") Integer orderStatus) {

        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
//        User user = SystemUserResource.getRootUser();
        //插入我的任务里
        AsyncTaskBody asyncTaskBody = new AsyncTaskBody();
        asyncTaskBody.setTaskId(UUID.randomUUID().toString());
        asyncTaskBody.setMenu("肉业订单导入");
        asyncTaskBody.setTaskType("导入");
        asyncTaskManager.beforeExecute(user, asyncTaskBody);

        return asyncImport(asyncTaskBody, user, file, orderStatus);
    }

    public ValueHolderV14<Long> asyncImport(AsyncTaskBody asyncTaskBody, User user, MultipartFile file, Integer orderStatus) {
        log.info(LogUtil.format("OcBOrderImportBeefOrderController.asyncImport filename:{}",
                "OcBOrderImportBeefOrderController.asyncImport"), file.getOriginalFilename());
        ValueHolderV14<Long> holderV14 = new ValueHolderV14<>();
        Map<Object, Object> retMap = new LinkedHashMap<>();
        commonTaskExecutor.submit(() -> {
            try {
                if (user == null) {
                    throw new NDSException("用户信息为空");
                }
                // 1. 处理Excel文件
                try (InputStream inputStream = file.getInputStream()) {
                    // 使用流式处理Workbook，避免内存缓存
                    try (Workbook hssfWorkbook = WorkbookFactory.create(inputStream)) {
                        // 业务逻辑校验
                        if (hssfWorkbook.getNumberOfSheets() != 1) {
                            throw new NDSException("肉业订单导入模板不正确");
                        }

                        List<OcBOrderImpBeefOrderVO> ocBOrderList = getOcBOrderList(hssfWorkbook, orderStatus);
                        if (CollectionUtils.isEmpty(ocBOrderList)) {
                            throw new NDSException("导入数据不能为空!");
                        }
                        log.info(LogUtil.format("OcBOrderImportBeefOrderController.asyncImport ocBOrderListSize:{}",
                                "OcBOrderImportBeefOrderController.asyncImport"), ocBOrderList.size());
                        if (ocBOrderList.size() > 100000) {
                            throw new NDSException("导入条数请勿超过100000条！");
                        }

                        ValueHolderV14<String> valueHolder;
                        if (!checkParam(ocBOrderList)) {
                            String exportImpErrorResult = ocBOrderImportBeefOrderCmd.exportImpErrorResult(ocBOrderList, user, file.getOriginalFilename());
                            valueHolder = new ValueHolderV14<>();
                            valueHolder.setData(exportImpErrorResult);
                            valueHolder.setCode(ResultCode.FAIL);
                            valueHolder.setMessage("订单导入失败，详情见文件内容");
                        } else {
                            valueHolder = ocBOrderImportBeefOrderCmd.importOrderList(ocBOrderList, user, file.getOriginalFilename());
                        }

                        retMap.put("code", ResultCode.SUCCESS);
                        retMap.put("data", valueHolder.getData());
                        retMap.put("message", valueHolder.getMessage());
                        if (Objects.nonNull(valueHolder.getData())) {
                            asyncTaskBody.setExportUrl(String.valueOf(valueHolder.getData()));
                        }

                        asyncTaskBody.setTaskType("导出");
                        asyncTaskManager.afterExecute(user, asyncTaskBody, JSON.parseObject(JSON.toJSONString(retMap)));
                    }
                }
            } catch (Exception ex) {
                log.info(LogUtil.format("订单文件导入失败,fileName:{}"), file.getOriginalFilename(), ex);
                retMap.put("code", ResultCode.FAIL);
                retMap.put("message", "导入异常：" + ex.getMessage());
                asyncTaskManager.afterExecute(user, asyncTaskBody, JSON.parseObject(JSON.toJSONString(retMap)));
            }
        });
        holderV14.setCode(ResultCode.SUCCESS);
        holderV14.setData(asyncTaskBody.getId());
        holderV14.setMessage(Resources.getMessage("肉业订单导入任务开始！详情请在我的任务查看"));
        return holderV14;
    }

    /**
     * 检查数据合法性
     *
     * @param ocBOrderList
     * @return
     */
    private boolean checkParam(List<OcBOrderImpBeefOrderVO> ocBOrderList) {
        FastDateFormat dateFormat = FastDateFormat.getInstance("yyyy-MM-dd HH:mm:ss");
        boolean checkFlag = true;
        Map<String, Integer> checkMap = new HashMap<>();
        for (OcBOrderImpBeefOrderVO f : ocBOrderList) {
            StringBuilder checkMessage = new StringBuilder();
            if (StringUtils.isBlank(f.getShopCode())) {
                checkMessage.append("[店铺编码不允许为空]");
            }
            if (StringUtils.isBlank(f.getSourceCode())) {
                checkMessage.append("[平台单号不允许为空]");
            }
            if (OmsOrderStatus.PLATFORM_DELIVERY.toInteger().equals(f.getOrderStatus())
                    && StringUtils.isBlank(f.getBusinessTypeCode())) {
                checkMessage.append("[业务类型不允许为空]");
            }
            if (StringUtils.isBlank(f.getOrderTypeName())) {
                checkMessage.append("[订单类型不允许为空]");
            } else {
                Integer val = OrderTypeEnum.getValByText(f.getOrderTypeName());
                if (OrderTypeEnum.TYPE_NONE.getVal().equals(val)) {
                    checkMessage.append("[订单类型错误]");
                }
            }
            if (OmsOrderStatus.TO_BE_ASSIGNED.toInteger().equals(f.getOrderStatus())
                    && StringUtils.isBlank(f.getCpCRegionProvinceEname())) {
                checkMessage.append("[收货人省份不允许为空]");
            }
            if (OmsOrderStatus.TO_BE_ASSIGNED.toInteger().equals(f.getOrderStatus())
                    && StringUtils.isBlank(f.getCpCRegionCityEname())) {
                checkMessage.append("[收货人市不允许为空]");
            }
            if (OmsOrderStatus.TO_BE_ASSIGNED.toInteger().equals(f.getOrderStatus())
                    && StringUtils.isBlank(f.getCpCRegionAreaEname())) {
                checkMessage.append("[收货人区不允许为空]");
            }
            if (f.getReceiverAddress() != null && f.getReceiverAddress().length() > 400) {
                checkMessage.append("详细地址的长度不能大于400");
            }
            if (OmsOrderStatus.PLATFORM_DELIVERY.toInteger().equals(f.getOrderStatus())
                    && StringUtils.isBlank(f.getCpCPhyWarehouseEname())) {
                checkMessage.append("[仓库不允许为空]");
            }
            if (OmsOrderStatus.PLATFORM_DELIVERY.toInteger().equals(f.getOrderStatus())
                    && StringUtils.isBlank(f.getExpresscode())) {
                checkMessage.append("[快递单号不允许为空]");
            }
            if (OmsOrderStatus.PLATFORM_DELIVERY.toInteger().equals(f.getOrderStatus())
                    && StringUtils.isBlank(f.getCpCLogisticsEcode())) {
                checkMessage.append("[快递公司不允许为空]");
            }
            if (StringUtils.isBlank(f.getPsCSkuEcode())) {
                checkMessage.append("[商品SKU编码不允许为空]");
            }
            if (f.getQty() == null) {
                checkMessage.append("[数量不允许为空]");
            }
            if (StringUtils.isNotBlank(f.getGiftType())) {
                if ("系统赠品".equals(f.getGiftType())) {
                    f.setGiftType(GiftTypeEnum.SYSTEM.getVal());
                } else if ("平台赠品".equals(f.getGiftType())) {
                    f.setGiftType(GiftTypeEnum.PLATFORM.getVal());
                } else {
                    checkMessage.append("[赠品类型错误]");
                }
            }
            if (f.getPriceActual() == null) {
                checkMessage.append("[成交单价不允许为空]");
            }
            if (f.getRealAmt() == null) {
                checkMessage.append("[成交金额不允许为空]");
            }
            if (StringUtils.isBlank(f.getOrderDateStr())) {
                checkMessage.append("[下单时间不允许为空]");
            } else {
                try {
                    Date orderDate = dateFormat.parse(f.getOrderDateStr());
                    f.setOrderDate(orderDate);
                } catch (ParseException e) {
                    checkMessage.append("[下单时间格式错误]");
                }
            }
            if (StringUtils.isBlank(f.getPayTimeStr())) {
                checkMessage.append("[支付时间不允许为空]");
            } else {
                try {
                    Date payTime = dateFormat.parse(f.getPayTimeStr());
                    f.setPayTime(payTime);
                } catch (ParseException e) {
                    checkMessage.append("[支付时间格式错误]");
                }
            }
            if (StringUtils.isNotBlank(f.getScanTimeStr())) {
                try {
                    Date scanTime = dateFormat.parse(f.getScanTimeStr());
                    f.setScanTime(scanTime);
                } catch (ParseException e) {
                    checkMessage.append("[出库时间格式错误]");
                }
            }
            // 发货信息一致性校验
            if (checkMap.containsKey(f.getBillNo())) {
                int currentHash = computeHash(f);
                int storedHash = checkMap.get(f.getBillNo());
                if (currentHash != storedHash) {
                    checkMessage.append("[同一JY单号发货信息不一致！]");
                }
            } else {
                int hash = computeHash(f);
                checkMap.put(f.getBillNo(), hash);
            }

            if (StringUtils.isNotBlank(checkMessage.toString())) {
                f.setDesc(checkMessage.toString());
                checkFlag = false;
                checkMessage.setLength(0);
            }
        }
        checkMap.clear();
        if (log.isDebugEnabled()) {
            log.debug(" end OcBOrderImportBeefOrderController  check import flag:{}", checkFlag);
        }
        return checkFlag;
    }

    // 计算发货信息的哈希值
    private int computeHash(OcBOrderImpBeefOrderVO f) {
        return Objects.hash(
                f.getShopCode(),
                f.getSourceCode(),
                f.getBusinessTypeCode(),
                f.getOrderTypeName(),
                f.getReceiverName(),
                f.getReceiverMobile(),
                f.getCpCRegionProvinceEname(),
                f.getCpCRegionCityEname(),
                f.getCpCRegionAreaEname(),
                f.getReceiverAddress(),
                f.getOaid(),
                f.getCpCPhyWarehouseEname(),
                f.getExpresscode(),
                f.getCpCLogisticsEcode(),
                f.getOrderDate(),
                f.getPayTime(),
                f.getScanTime(),
                f.getBuyerMessage(),
                f.getSellerMemo()
        );
    }


    /**
     * 获取主it 表sheet数据，转换成主表对象
     */
    public List<OcBOrderImpBeefOrderVO> getOcBOrderList(Workbook hssfWorkbook, Integer orderStatus) {
        List<OcBOrderImpBeefOrderVO> OcBOrderImpVos = Lists.newArrayList();
        List<Map<String, String>> execlList = Lists.newArrayList();

        try {
            execlList = readExcel(0, hssfWorkbook);
        } catch (Exception e) {

        }
        int index = 0;
        for (Map<String, String> columnMap : execlList) {
            OcBOrderImpBeefOrderVO ocBOrderImpVo = new OcBOrderImpBeefOrderVO();
            ocBOrderImpVo.setOrderStatus(orderStatus);
            if (index == 0) {
                // 校验excel字段
                if (columnMap.size() != 28
                        || !"JY单号".equals(columnMap.get(rowStr + index + cellStr + 0))
                        || !"店铺编码".equals(columnMap.get(rowStr + index + cellStr + 1))
                        || !"平台单号".equals(columnMap.get(rowStr + index + cellStr + 2))
                        || !"子订单号".equals(columnMap.get(rowStr + index + cellStr + 3))
                        || !"业务类型".equals(columnMap.get(rowStr + index + cellStr + 4))
                        || !"订单类型".equals(columnMap.get(rowStr + index + cellStr + 5))
                        || !"收货人".equals(columnMap.get(rowStr + index + cellStr + 6))
                        || !"收货人手机".equals(columnMap.get(rowStr + index + cellStr + 7))
                        || !"收货人省份".equals(columnMap.get(rowStr + index + cellStr + 8))
                        || !"收货人市".equals(columnMap.get(rowStr + index + cellStr + 9))
                        || !"收货人区".equals(columnMap.get(rowStr + index + cellStr + 10))
                        || !"收货人地址".equals(columnMap.get(rowStr + index + cellStr + 11))
                        || !"OAID".equals(columnMap.get(rowStr + index + cellStr + 12))
                        || !"仓库".equals(columnMap.get(rowStr + index + cellStr + 13))
                        || !"快递单号".equals(columnMap.get(rowStr + index + cellStr + 14))
                        || !"快递公司".equals(columnMap.get(rowStr + index + cellStr + 15))
                        || !"买家备注".equals(columnMap.get(rowStr + index + cellStr + 16))
                        || !"卖家备注".equals(columnMap.get(rowStr + index + cellStr + 17))
                        || !"商品SKU编码".equals(columnMap.get(rowStr + index + cellStr + 18))
                        || !"数量".equals(columnMap.get(rowStr + index + cellStr + 19))
                        || !"赠品".equals(columnMap.get(rowStr + index + cellStr + 20))
                        || !"成交单价".equals(columnMap.get(rowStr + index + cellStr + 21))
                        || !"成交金额".equals(columnMap.get(rowStr + index + cellStr + 22))
                        || !"商品优惠金额".equals(columnMap.get(rowStr + index + cellStr + 23))
                        || !"平摊金额".equals(columnMap.get(rowStr + index + cellStr + 24))
                        || !"下单时间".equals(columnMap.get(rowStr + index + cellStr + 25))
                        || !"支付时间".equals(columnMap.get(rowStr + index + cellStr + 26))
                        || !"出库时间".equals(columnMap.get(rowStr + index + cellStr + 27))
                ) {
                    return Lists.newArrayList();
                }
            } else {
                if (StringUtils.isNotEmpty(columnMap.get(rowStr + index + cellStr + 0))) {
                    // 组装数据
                    OcBOrderImpVos.add(OcBOrderImpBeefOrderVO.importCreate(index, ocBOrderImpVo, columnMap));
                }
            }
            index++;
        }
        return OcBOrderImpVos;
    }

    /**
     * 导入方法，
     *
     * @param hssfWorkbook
     * @return
     * @throws Exception
     */
    public List<Map<String, String>> readExcel(Integer sheetIndex, Workbook hssfWorkbook) throws Exception {
        List list = Lists.newArrayList();
        Sheet hssfSheet = hssfWorkbook.getSheetAt(sheetIndex);
        if (Objects.isNull(hssfSheet)) {
            return Lists.newArrayList();
        }

        for (int rowNum = 0; rowNum <= hssfSheet.getLastRowNum(); rowNum++) {
            Map<String, String> map = Maps.newTreeMap();
            Row hssfRow = hssfSheet.getRow(rowNum);
            if (hssfRow != null) {
                Iterator<Cell> cellItr = hssfRow.cellIterator();
                while (cellItr.hasNext()) {
                    Cell cell = cellItr.next();
                    map.put(rowStr + cell.getRowIndex() + cellStr + cell.getColumnIndex(), getCellValue(cell));
                }
            }
            list.add(map);
        }
        return list;
    }

    private static String getCellValue(Cell cell) {
        int cellType = cell.getCellType();
        String cellValue;
        switch (cellType) {
            case NUMERIC:
                cellValue = nf.format(cell.getNumericCellValue());
                if (cellValue.indexOf(",") >= 0) {
                    cellValue = cellValue.replace(",", "");
                }
                break;
            case FORMULA:
                try {
                    cellValue = cell.getStringCellValue();
                } catch (IllegalStateException e) {
                    cellValue = String.valueOf(cell.getNumericCellValue());
                }
                break;

            default:
                cellValue = cell.getStringCellValue();
        }

        return cellValue.trim();
    }

}
