package com.jackrain.nea.oc.oms.task.makeup;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.xxl.job.starter.helper.R3XxlJobParamHelper;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;


/**
 * LTS任务调用库存中心接口（生成入库通知单/结果单，逻辑收货单）
 *
 * @author: tian<PERSON><PERSON><PERSON>
 * @since: 2019/8/26
 * create at : 2019/8/26 11:30
 */

@Slf4j
@Component
public class OmsCreatePhyNoticsMakeUpTask extends BaseR3Task implements IR3Task {

    @Autowired
    private SgRpcService sgRpcService;

    @Override
    @XxlJob("OmsCreatePhyNoticsMakeUpTask")
    public RunTaskResult execute(JSONObject params) {
        params = R3XxlJobParamHelper.xxlParam2R3Json();
        RunTaskResult result = new RunTaskResult();
        result.setSuccess(true);
        result.setMessage("success");
        try {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("LTS参数内容={}"),params);
            }
            if (params != null) {
                try {
//                    SgPhyInNoticesBillSaveRequest sgPhyInNoticesBillSaveRequest = JSON.parseObject(params.toJSONString(), SgPhyInNoticesBillSaveRequest.class);
//                    List<SgPhyInNoticesBillSaveRequest> sgPhyInNoticesBillSaveRequestList = new ArrayList();
//                    sgPhyInNoticesBillSaveRequestList.add(sgPhyInNoticesBillSaveRequest);
//                    if (log.isDebugEnabled()) {
//                        log.debug(this.getClass().getName() + " task调用sg生成生成入库通知单,生成逻辑收货单,生成入库结果单接口入参={}", sgPhyInNoticesBillSaveRequestList);
//                    }
//                    ValueHolderV14<SgR3BaseResult> resultSg = sgRpcService.saveSgBPhyInNoticesByReturn(sgPhyInNoticesBillSaveRequestList);
//                    if (log.isDebugEnabled()) {
//                        log.debug(this.getClass().getName() + " task调用sg生成生成入库通知单,生成逻辑收货单,生成入库结果单接口出参={}", resultSg);
//                    }
                } catch (Exception e) {
                    log.error(" task调用sg生成生成入库通知单,生成逻辑收货单,生成入库结果单接口rpc异常={}", e);
                    result.setSuccess(false);
                    result.setMessage(e.getMessage());
                    return result;
                }
            }

        } catch (Exception ex) {
            log.error(LogUtil.format("OmsCreatePhyNoticsMakeUpTask.Execute.Exp: {}"), Throwables.getStackTraceAsString(ex));
            result.setSuccess(false);
            result.setMessage(ex.getMessage());
        }
        return result;
    }


}
