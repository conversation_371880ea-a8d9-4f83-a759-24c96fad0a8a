package com.jackrain.nea.oc.oms.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * POS查询中台订单状态入参
 *
 * @Auther: 黄志优
 * @Date: 2020/8/30 13:02
 * @Description:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QimenPosOrderStatusRequest implements Serializable {
    /**
     * 出库通知单单据编号
     */
    @JSONField(name = "order_id")
    private String orderId;

    /**
     * 路由参数
     */
    @JSONField(name = "customerid")
    private String customerId;
}