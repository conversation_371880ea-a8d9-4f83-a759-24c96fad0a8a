package com.jackrain.nea.oc.oms.mapper;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.mapper.task.OrderDrdsSql;
import com.jackrain.nea.oc.oms.model.relation.EncryptCodeGSI;
import com.jackrain.nea.oc.oms.model.relation.GetOrderForCancelModel;
import com.jackrain.nea.oc.oms.model.relation.MergeOrderModel;
import com.jackrain.nea.oc.oms.model.relation.MergeParam;
import com.jackrain.nea.oc.oms.model.relation.NaiKaOrderQueryModel;
import com.jackrain.nea.oc.oms.model.relation.TaskParam;
import com.jackrain.nea.oc.oms.model.result.OcBOrderPlatResult;
import com.jackrain.nea.oc.oms.model.result.OcBOrderResult;
import com.jackrain.nea.oc.oms.model.result.QueryOrderResult;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.web.face.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.Update;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.jdbc.SQL;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;


@Mapper
@Component
public interface OcBOrderMapper extends ExtentionMapper<OcBOrder> {

    String FIELDS = "`ID`, `BILL_NO`, `SOURCE_CODE`, `CP_C_SHOP_ID`, `CP_C_SHOP_ECODE`, `CP_C_SHOP_TITLE`,IS_FORBIDDEN_DELIVERY,IS_VIP_UPDATE_WAREHOUSE,REVERSE_AUDIT_TYPE, "
            + " `CP_C_PHY_WAREHOUSE_ID`, "
            + "`CP_C_PHY_WAREHOUSE_ECODE`, `CP_C_PHY_WAREHOUSE_ENAME`, `CP_C_CUSTOMER_ID`, `CP_C_CUSTOMER_ECODE`, "
            + "`CP_C_CUSTOMER_ENAME`, `USER_ID`, `USER_NICK`, `ORDER_TYPE`, `ORDER_STATUS`, `OCCUPY_STATUS`, "
            + "`SUFFIX_INFO`, `ORDER_FLAG`, `PRODUCT_AMT`, `PRODUCT_DISCOUNT_AMT`, `ORDER_DISCOUNT_AMT`, "
            + "`ADJUST_AMT`, `SHIP_AMT`, `SERVICE_AMT`, `ORDER_AMT`, `RECEIVED_AMT`, `CONSIGN_AMT`, `CONSIGN_SHIP_AMT`,"
            + " `AMT_RECEIVE`, `COD_AMT`, " +
            // " `OPERATE_AMT`," +
            " `JD_RECEIVE_AMT`, `JD_SETTLE_AMT`, `LOGISTICS_COST`, "
            + "`IS_INVOICE`, `INVOICE_HEADER`, `INVOICE_CONTENT`, `IS_GENINVOICE_NOTICE`, `WEIGHT`, `IS_CALCWEIGHT`, "
            + "`CP_C_LOGISTICS_ID`, `CP_C_LOGISTICS_ECODE`, `CP_C_LOGISTICS_ENAME`, `EXPRESSCODE`, `ORDER_DATE`, "
            + "`END_TIME`, " +
            // "`SEND_TIME`, " +
            " `PAY_TIME`, `AUDIT_TIME`, `BUYER_EMAIL`, `RECEIVER_NAME`, `RECEIVER_MOBILE`, "
            + "`RECEIVER_PHONE`, `CP_C_REGION_PROVINCE_ID`, `CP_C_REGION_PROVINCE_ECODE`, `CP_C_REGION_PROVINCE_ENAME`,"
            + " `CP_C_REGION_CITY_ID`, `CP_C_REGION_CITY_ECODE`, `CP_C_REGION_CITY_ENAME`, `CP_C_REGION_AREA_ID`, "
            + "`CP_C_REGION_AREA_ECODE`, `CP_C_REGION_AREA_ENAME`, `RECEIVER_ADDRESS`, `RECEIVER_ZIP`, "
            + "`RECEIVER_EMAIL`, `IS_MERGE`, `IS_SPLIT`, `IS_INTERECEPT`, `IS_INRETURNING`, "
            + "`SALESMAN_ID`, `SALESMAN_NAME`, `ALL_SKU`, `PAY_TYPE`, `BUYER_MESSAGE`, `ORDER_SOURCE`, `ORIG_ORDER_ID`,"
            + " `ORIG_RETURN_ORDER_ID`, `PRESALE_DEPOSIT_TIME`,  "
            + "`IS_HASGIFT`, `QTY_ALL`, `SYSREMARK`, `INSIDE_REMARK`, `SELLER_MEMO`, `MERGE_SOURCE_CODE`, `PLATFORM`, "
            + "`MERGE_ORDER_ID`, `SPLIT_ORDER_ID`, " +
            // " `IS_TODRP`,`IS_GIVE_LOGISTIC`,`IS_HASPRESALESKU`, " +
            "`SCAN_TIME`, `OUT_STATUS`, `TID`, "
            + "`ORDER_TAG`, `WMS_CANCEL_STATUS`, `RETURN_STATUS`, `TB_STORECODE`, `REFUND_CONFIRM_STATUS`, "
            + "`AUTO_AUDIT_STATUS`,  `IS_JCORDER`,  `DOUBLE11_PRESALE_STATUS`, "
            + "`DISTRIBUTION_TIME`, `IS_INVENTED`, `IS_COMBINATION`,  `IS_OUT_URGENCY`, "
            + " `IS_HAS_TICKET`, `VERSION`, `AD_ORG_ID`, `AD_CLIENT_ID`, `OWNERID`, `OWNERENAME`, "
            + "`OWNERNAME`, `CREATIONDATE`, `MODIFIERID`, `MODIFIERENAME`, `MODIFIERNAME`, `MODIFIEDDATE`, `ISACTIVE`, "
            // + "`IS_WRITEOFF`, `SYS_PRESALE_STATUS`, "
            + " `BUYER_ALIPAY_NO`, `CP_C_SHOP_SELLER_NICK`, `INVOICE_STATUS`, "
            + "`OC_B_INVOICE_NOTICE_ID`, `IS_EXCHANGE_NO_IN`,`PRICE_LABEL`,`STATUS_PAY_STEP`,"
            + "`LOCK_STATUS`, `CP_C_LABEL_ENAME`,`CP_C_LABEL_CONTENT`,`CP_C_LABEL_ID`,`SKU_KIND_QTY`," +
            "TO_SAP_STATUS,"
            + "`LIVE_FLAG`,`ANCHOR_ID`,`ANCHOR_NAME`, `RECEIVER_MOBILE` AS `VIP_PHONE`,"
            + "`APPOINT_LOGISTICS_ID`,`APPOINT_LOGISTICS_ECODE`,`APPOINT_LOGISTICS_ENAME`";
//            "`IS_WOS_URGE`, `IS_WOS_CUT`";

    @SelectProvider(type = SqlProvider.class, method = "selectByNodeSql")
    List<OcBOrder> selectWaitSplitOrderList(@Param("tableName") String tableName,
                                            @Param("payTypes") String payTypes, @Param("platforms") String platforms,
                                            @Param("limit") int limit);

    /**
     * 查询指定快递且缺货的订单
     *
     * @param nodeName
     * @param tableName
     * @return
     */
    @SelectProvider(type = SqlProvider.class, method = "selectOrderListByAppointLogistics")
    List<Long> selectOrderListByAppointLogistics(@Param("tableName") String tableName);

    @Select("SELECT * FROM oc_b_order WHERE Source_Code=#{sourceCode}  and isactive='Y'")
    List<OcBOrder> selectBySourceCode(String sourceCode);

    @Select("SELECT * FROM oc_b_order WHERE order_status = 2 and (is_split = 2 or is_split = 3)  and id=#{orderID} and isactive='Y'")
    OcBOrder selectShortageAndSplitingOrderByID(Long orderID);

    /**
     * @param ids
     * @return
     */
    @Select("<script> "
            + "SELECT * FROM oc_b_order WHERE id "
            + "in <foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<OcBOrder> selectOrderListByIds(@Param("ids") List<Long> ids);

    @Select("<script> "
            + "SELECT * FROM oc_b_order WHERE id in"
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach> "
            + " order by id asc"
            + "</script>")
    List<OcBOrder> selectByIdsList(@Param("ids") List<Long> ids);


    @Select("<script> "
            + "SELECT id FROM oc_b_order WHERE order_status in (1,2) and id in"
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<Long> selectByIdsListByOrderStatus(@Param("ids") List<Long> ids);

    @Select("<script> "
            + "SELECT id FROM oc_b_order WHERE platform = #{platform} and id in"
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<Long> selectByIdsAndPlatform(@Param("ids") List<Long> ids, @Param("platform") Integer platform);

    @Select("<script> "
            + "SELECT id FROM oc_b_order WHERE gw_source_group = #{platform} and id in"
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<Long> selectByIdsAndGwSourceGroup(@Param("ids") List<Long> ids, @Param("platform") Integer platform);


    @Select("<script> "
            + "SELECT * FROM oc_b_order WHERE order_status in (1,2) and id in"
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<OcBOrder> selectByIdsListByOrders(@Param("ids") List<Long> ids);


    @Select("<script> "
            + "SELECT * FROM oc_b_order WHERE is_interecept = 0 and order_status = 3 and id "
            + "in <foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<OcBOrder> selectByIdsListAndInterecept(@Param("ids") List<Long> ids);

    @Select("SELECT * FROM oc_b_order WHERE id=#{orderID} and isactive='Y'")
    OcBOrder selectByID(Long orderID);

    @Select("SELECT tid FROM oc_b_order WHERE id=#{orderID} and isactive='Y'")
    String selectTidByID(Long orderID);

    @Select("SELECT * FROM oc_b_order WHERE id=#{orderID} and is_interecept = 0 and order_status not in ('7','8') and isactive='Y'")
    OcBOrder selectOrderCancelAndCancelInfo(Long orderID);

    @Select("select count(1) from oc_b_order where source_code=#{sourceCode} and order_status not in ('7','8')")
    Integer selectCountBySourceCode(@Param("sourceCode") String sourceCode);

    @Select("select count(1) from oc_b_order where source_code=#{sourceCode} and suffix_info=#{suffixInfo} and order_status not in ('7','8')")
    Integer selectCountBySourceCodeAndSuffixInfo(@Param("sourceCode") String sourceCode, @Param("suffixInfo") String suffixInfo);

    //*********************孙勇生 start

    @Select("<script> "
            + "SELECT * FROM oc_b_order WHERE is_interecept = 0 and order_status = 2 and isactive='Y' and id "
            + "in <foreach item='item' index='index' collection='orderIdList' open='(' separator=',' close=')'> #{item} </foreach> "
            + " order by CREATIONDATE asc</script>")
    List<Long> queryByShortageOrder(@Param("orderIdList") List<Long> orderIdList);

    @Select("<script> "
            + "SELECT ID as ORDER_ID,* FROM oc_b_order WHERE"
            + "<foreach item='item' index='index' collection='ids' separator='or'>"
            + "id= #{item} "
            + "</foreach>"
            + "order by USER_NICK,CP_C_PHY_WAREHOUSE_ID,PLATFORM,ORDER_TYPE,CP_C_SHOP_ID,PAY_TYPE,RECEIVER_NAME,RECEIVER_MOBILE,"
            + "RECEIVER_PHONE,CP_C_REGION_PROVINCE_ID,CP_C_REGION_CITY_ID,CP_C_REGION_AREA_ID,RECEIVER_ADDRESS"
            + "</script>")
    List<MergeOrderModel> selectMergeOrderByIds(@Param("ids") List<Integer> ids);

    /**
     * 全渠道订单
     *
     * @date 2019/3/6
     * @author: ming.fz AND wangqiang
     */

    @Select("select qty_all from oc_b_order where id=#{id}")
    BigDecimal selectQuyAllCount(Long id);

    /**
     * 待审核数据
     *
     * @param orderId 订单Id
     * @return OcBOrder
     */
    @Select("SELECT * FROM oc_b_order WHERE id=#{orderId} and is_interecept = 0 AND order_status=1 AND isactive='Y'")
    OcBOrder selectAuditOrderInfo(@Param("orderId") Long orderId);

    /**
     * 待审核数据
     *
     * @param orderId 订单Id
     * @return OcBOrder
     */
    @Select("SELECT * FROM oc_b_order WHERE id=#{orderId} AND order_status=1 AND isactive='Y'")
    OcBOrder selectUnConfirmedOrderInfo(@Param("orderId") Long orderId);

    /**
     * 缺货数据
     *
     * @param orderId 订单Id
     * @return OcBOrder
     */
    @Select("SELECT * FROM oc_b_order WHERE id=#{orderId} AND order_status=2  AND isactive='Y'")
    OcBOrder selectOrderUnStock(@Param("orderId") Long orderId);

    /**
     * 根据订单ID列表查询未HOLD单的所有订单
     *
     * @param ids 订单Id
     * @return OcBOrder
     */
    @Select("<script> "
            + "SELECT * FROM oc_b_order WHERE isactive='Y' and is_interecept = 0 and id in"
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    List<OcBOrder> selectNotHoldOrderList(@Param("ids") List<Long> ids);

    /**
     * 待分配数据
     *
     * @param orderId 订单Id
     * @return OcBOrder
     */
    @Select("SELECT * FROM oc_b_order WHERE id=#{orderId} AND order_status=50 AND isactive='Y'")
    OcBOrder selectWaitDistributeInfo(@Param("orderId") Long orderId);

    /**
     * 待分配,缺货数据
     *
     * @param orderId 订单Id
     * @return OcBOrder
     */
    @Select("SELECT * FROM oc_b_order WHERE id=#{orderId}  and order_status in ('50','2') and isactive='Y'")
    OcBOrder selectWaitDistributeInfoAndOutStock(@Param("orderId") Long orderId);

    /**
     * 查询订单: 淘宝预售
     *
     * @param orderId id
     * @return OcBOrder
     */
    @Select("SELECT * FROM oc_b_order WHERE id=#{orderId} AND order_status IN (50,2,1) AND isactive='Y'")
    OcBOrder selectTaoBaoPreSaleInfo(@Param("orderId") Long orderId);

    /**
     * 待确认和缺货数据
     *
     * @param orderId
     * @return
     */
    @Select("SELECT * FROM oc_b_order WHERE id=#{orderID} and is_interecept = 0 and order_status in ('1','2') and isactive='Y'")
    OcBOrder selectOrderUnConfirmAndStockInfo(Long orderId);

    /**
     * 配货中
     *
     * @param orderId
     * @return
     */
    @Select("SELECT * FROM oc_b_order WHERE id=#{orderID} and is_interecept = 0  and isactive='Y'")
    OcBOrder selectInDistributionOrder(Long orderId);

    /**
     * 更新订单信息
     *
     * @param jsonObject
     * @return
     * <AUTHOR>
     */
    @UpdateProvider(type = OcBOrderMapper.UpdateRecord.class, method = "updateSql")
    int updateRecord(JSONObject jsonObject);

    @Update("<script> "
            + "UPDATE OC_B_ORDER SET generic_mark = #{genericMark},modifieddate = now() where id in "
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    void updateGenericMark(@Param("genericMark") String genericMark, @Param("ids") List<Long> orderIds);

    /**
     * @param id
     * @return
     * <AUTHOR>
     * <p>
     * 获取订单状态、WMS撤回状态（WMS_CANCEL_STATUS）
     */
    @Select("SELECT ORDER_STATUS,CP_C_SHOP_ID,RECEIVED_AMT,WMS_CANCEL_STATUS FROM OC_B_ORDER WHERE id=#{id}")
    OcBOrder selectIdByStatusAndShopIdAndReceivedAmt(Long id);

    /**
     * @param id 订单id
     * @return
     * <AUTHOR>
     * <p>
     * 获取订单状态、是否被拦截、发货仓库id、物流公司、平台、支付方式、物流公司、用户昵称、预售状态(DOUBLE11_PRESALE_STATUS)、
     * 订单来源、到付代收金额、服务费、订单类型、 原订单号、平台单号、商品总额、店铺cp_c_shop_id、收货人receiver_name、
     * 收货人手机receiver_mobile、收货人电话号码receiver_phone、收货人、所在省（cp_c_region_province_id）、市（cp_c_region_city_id）
     * 区（cp_c_region_area_id）、物流编码（expresscode）
     */
    @Select("SELECT ID,ORDER_STATUS,IS_INTERECEPT,CP_C_PHY_WAREHOUSE_ID,CP_C_LOGISTICS_ID,EXPRESSCODE,SUFFIX_INFO, " +
            "PLATFORM,PAY_TYPE,CP_C_LOGISTICS_ENAME,USER_NICK,DOUBLE11_PRESALE_STATUS,ORDER_SOURCE,COD_AMT,SERVICE_AMT,ORDER_TYPE,ORIG_ORDER_ID, " +
            "SOURCE_CODE,PRODUCT_AMT,CP_C_SHOP_ID,RECEIVER_NAME,RECEIVER_MOBILE,RECEIVER_PHONE,CP_C_REGION_PROVINCE_ID," +
            "CP_C_REGION_CITY_ID,CP_C_REGION_AREA_ID FROM OC_B_ORDER WHERE id=#{id}")
    OcBOrder selectIdRecord(Long id);

    /**
     * @param id
     * @return
     * <AUTHOR>
     * <p>
     * 查看当前订单是否有多条
     */
    @Select("SELECT COUNT(1) FROM OC_B_ORDER WHERE id=#{id} and ISACTIVE='Y'")
    int selectIdByCount(Long id);

    /**
     * @param
     * @return
     * <AUTHOR>
     * <p>
     * 查看是否有相同的订单存在
     */
    @SelectProvider(type = OcBOrderMapper.TheSameOrder.class, method = "isTheSameOrder")
    Long[] isTheSameOrder(Map<String, Object> map);

    @Update("UPDATE oc_b_order SET bill_no=#{sequence} WHERE ID=#{ID}")
    void updateSequence(@Param("sequence") String sequence, @Param("ID") Long ID);

    /**
     * 订单列表查询
     *
     * @param sqlParam sql
     * @param isActive y/n
     * @return list
     */
    @SelectProvider(type = UpdateRecord.class, method = "queryOrderList")
    List<QueryOrderResult> orderListQuery(@Param("sqlParam") String sqlParam, @Param("isActive") String isActive);

    /*
     * 重单
     *
     * <AUTHOR>
     * @param sourceCode
     * @param suffixInfo
     * @return
     *
     */
    @Select("SELECT ID FROM OC_B_ORDER WHERE SOURCE_CODE=#{sourceCode} AND SUFFIX_INFO=#{suffixInfo}")
    Long[] selectIds(@Param("sourceCode") String sourceCode, @Param("suffixInfo") String suffixInfo);

    /**
     * sap 查询
     *
     * @param sqlString
     * @return
     */
    @SelectProvider(type = SqlProvider.class, method = "selectListForSap")
    List<OcBOrder> selectListForSap(@Param("sqlString") String sqlString);

    // ******************* zhang.xiwen start

    /**
     * 同步第三方系统 查询
     *
     * @param taskParam TaskParam
     * @return
     */
    @SelectProvider(type = SqlProvider.class, method = "selectList4SyncThirdSys")
    List<OcBOrder> selectList4SyncThirdSys(TaskParam taskParam);

    /**
     * 根据退货单标号查询全渠道订单
     *
     * @param id 退货单编号
     * @return 返回一个全渠道订单对象
     */
    @Select("SELECT * FROM OC_B_ORDER WHERE ORIG_RETURN_ORDER_ID=#{id} AND ORDER_STATUS != 8 AND ORDER_STATUS != 7 ")
    OcBOrder selectByReturnId(Long id);

    /**
     * 全渠道订单查询
     *
     * @param sqlParam sqlString
     * @param isActive y/n
     * @return List<QueryOrderResult>
     */
    @SelectProvider(type = SqlProvider.class, method = "orderListQueryByIds")
    List<QueryOrderResult> orderListQueryByIds(@Param("sqlParam") String sqlParam, @Param("isActive") String isActive);

    @SelectProvider(type = SqlProvider.class, method = "selectExportOrderResultById")
    List<QueryOrderResult> selectExportOrderResultById(@Param("sqlString") String sqlString,
                                                       @Param("isActive") String isActive);
    //********************夏继超 start

    /**
     * 更新全渠道订单,退货状态
     *
     * @param origOrderId  OC_B_ORDER id
     * @param returnStatus int
     * @return int
     */
    @Update("UPDATE OC_B_ORDER SET RETURN_STATUS = #{returnStatus} WHERE `ID`=#{origOrderId}")
    int updateOcBorderReturnStatus(@Param("origOrderId") Long origOrderId, @Param("returnStatus") Integer returnStatus);
    //********************夏继超 end

    /**
     * 查询.全渠道订单扩展
     *
     * @param id       id.order
     * @param isActive y/n
     * @return QueryOrderResult
     */
    @Select("SELECT * FROM OC_B_ORDER WHERE `ID`=#{id} AND ISACTIVE=#{isActive}")
    QueryOrderResult selectQueryOrderResultById(@Param("id") Long id, @Param("isActive") String isActive);

    /**
     * 全渠道订单查询
     * 列表查询专用, 禁止修改
     *
     * @param sqlParam sqlString
     * @return List<QueryOrderResult>
     */
    @SelectProvider(type = SqlProvider.class, method = "searchOrderList")
    List<QueryOrderResult> searchOrderList(@Param("sqlParam") String sqlParam, @Param("orderField") String orderField);

    /**
     * @param ids
     * @return
     */
    @Select("<script> "
            + "select * FROM OC_B_ORDER where is_history ='N' and order_status != 8 and id in "
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + " order by id "
            + "</script>")
    List<OcBOrderResult> listOrder(@Param("ids") List<Long> ids);

    /**
     * @param ids
     * @return
     */
    @Select("<script> "
            + "select id FROM OC_B_ORDER where order_status = 5 and id in "
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + " order by id "
            + "</script>")
    List<Long> selectWarehouseDelivery(@Param("ids") List<Long> ids);

    /**
     * @param ids
     * @return
     */
    @Select("<script> "
            + "select id FROM OC_B_ORDER where order_status != 5 and id in "
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + " order by id "
            + "</script>")
    List<Long> selectNoWarehouseDelivery(@Param("ids") List<Long> ids);

    /**
     * @param ids
     * @return
     */
    @Select("<script> "
            + "select id, order_status FROM OC_B_ORDER where id in "
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + " order by id "
            + "</script>")
    List<OcBOrder> selectIdAndStatus(@Param("ids") List<Long> ids);

    /**
     * @param ids
     * @return
     */
    @Select("<script> "
            + "select CP_C_SHOP_TITLE,CP_C_SHOP_ID,PLATFORM FROM OC_B_ORDER where id in "
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + " order by id "
            + "</script>")
    List<OcBOrderPlatResult> listPlantOrder(@Param("ids") List<Long> ids);

    /**
     * 开票记录.更新
     *
     * @param isInvoice 票签
     * @param id        id
     * @return int
     */
    @Update("UPDATE OC_B_ORDER SET IS_INVOICE=#{isInvoice}  WHERE `ID`=#{id}")
    int updateOcBorderCaseByInvoice(@Param("isInvoice") Integer isInvoice, @Param("id") Long id);

    /**
     * 根据id 查询平台单号
     *
     * @param id
     * @param isActive
     * @return
     */
    @Select("SELECT SOURCE_CODE FROM OC_B_ORDER WHERE `ID`=#{id} AND ISACTIVE=#{isActive}")
    String selectSourceCodeById(@Param("id") Long id, @Param("isActive") String isActive);

    /**
     * 淘宝.预售,作废订单
     *
     * @param id 订单id
     * @return 订单
     */
    @Select("SELECT * FROM OC_B_ORDER WHERE `ID`=#{id} AND STATUS_PAY_STEP='FRONT_PAID_FINAL_NOPAID' AND "
            + "ORDER_STATUS IN(1,2) AND ISACTIVE='Y'")
    OcBOrder selectUnPadPreSaleOrder(@Param("id") Long id);

    @Select("SELECT * FROM OC_B_ORDER WHERE ID = #{id} AND ( order_status = #{orderStatus} OR "
            + "order_status = #{orderStatusTwo})")
    OcBOrder selectByIdAndOrderStatus(@Param("id") Long id, @Param("orderStatus") int orderStatus,
                                      @Param("orderStatusTwo") int orderStatusTwo);

    /**
     * 查询实体仓下，订单总数量
     *
     * @param cpCPhyWarehouseId
     * @return BigDecimal
     */

    @Select("SELECT COUNT(1) FROM OC_B_ORDER A  WHERE A.CP_C_PHY_WAREHOUSE_ID = #{cpCPhyWarehouseId} AND A.ISACTIVE='Y'")
    int countSendsum(@Param("cpCPhyWarehouseId") Long cpCPhyWarehouseId);

    /**
     * 获取有效订单
     *
     * @param orderID
     * @param orderStatus
     * @return
     */
    @Select("SELECT * FROM oc_b_order WHERE id=#{orderID} and isactive='Y' and order_status NOT IN (#{orderStatus}) AND EXISTS(SELECT id FROM oc_b_order_item I WHERE I.oc_b_order_id = oc_b_order.id " +
            "AND I.tid=#{tid} AND I.ooid =#{wareid} AND I.qty - IFNULL( I.qty_refund, 0 ) > 0)")
    OcBOrder selectValidOrderByID(@Param("orderID") Long orderID, @Param("orderStatus") String orderStatus, @Param("tid") String tid, @Param("wareid") String wareid);


    /**
     * 确保复制订单唯一性
     *
     * @param sourceCode 平台单号信息
     * @param orderId    订单信息
     * @return
     */
    @Select("SELECT count(*) FROM oc_b_order WHERE source_code=#{sourceCode} AND id=#{orderId} and order_status != 7 and order_status != 8")
    int selectCopyOrderCount(@Param("sourceCode") String sourceCode, @Param("orderId") Long orderId);


    /**
     * 更新全渠道订单,物流单号，物流公司id，物流公司编码，物流公司名称
     *
     * @param expressCode    物流单号
     * @param orderId        订单号  logisticsId,logisticsEcode,logisticsEname
     * @param logisticsId    物流公司id
     * @param logisticsEcode 物流公司编码
     * @param logisticsEname 物流公司名称
     * @return int
     */
    @Update("UPDATE OC_B_ORDER SET EXPRESSCODE = #{expressCode},CP_C_LOGISTICS_ID = #{logisticsId}," +
            "CP_C_LOGISTICS_ECODE = #{logisticsEcode},CP_C_LOGISTICS_ENAME = #{logisticsEname} where `ID`=#{orderId}")
    int updateOcBorderExpresscode(@Param("expressCode") String expressCode, @Param("orderId") Long orderId,
                                  @Param("logisticsId") Long logisticsId, @Param("logisticsEcode") String logisticsEcode,
                                  @Param("logisticsEname") String logisticsEname);

    /**
     * 更新全渠道订单,物流单号，物流公司id，物流公司编码，物流公司名称
     *
     * @param operratCostPrice 物流单号 operate_amt
     * @param orderId          订单号  logisticsId,logisticsEcode,logisticsEname
     * @return int
     */
    /*@Update("UPDATE OC_B_ORDER SET OPERATE_AMT = #{operratCostPrice} where `ID`=#{orderId}")
    int updateOcBorderOperratCostPrice(@Param("operratCostPrice") BigDecimal operratCostPrice, @Param("orderId") Long orderId);
*/

    /**
     * 更新全渠道订单,物流单号，物流公司id，物流公司编码，物流公司名称
     *
     * @param freightCostPrice 物流单号 logistics_cost
     * @param orderId          订单号  logisticsId,logisticsEcode,logisticsEname
     * @return int
     */
    @Update("UPDATE OC_B_ORDER SET LOGISTICS_COST = #{freightCostPrice} where `ID`=#{orderId}")
    int updateOcBorderFreightCostPrice(@Param("freightCostPrice") BigDecimal freightCostPrice, @Param("orderId") Long orderId);

    /**
     * 更新平台发货mq回传 错误信息 到订单表
     *
     * @param errorMsg 错误信息
     * @param orderId  订单号
     * @return int
     */
    @Update("UPDATE OC_B_ORDER SET SYSREMARK = #{errorMsg} where `ID`=#{orderId}")
    int updateOcBorderInsideRemark(@Param("errorMsg") String errorMsg, @Param("orderId") Long orderId);

    /**
     * ljp add
     *
     * @param ids
     * @return
     */
    @Update("<script> "
            + "UPDATE OC_B_ORDER SET order_status = 8 where id in "
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    int updateList(@Param("ids") List<Long> ids);

    /**
     * 奶卡管理页面数据
     *
     * @param ids
     * @return
     */
    @Select("<script> "
            + "SELECT ID ,BILL_NO, SOURCE_CODE, ORDER_STATUS, COPY_REASON, business_type_id,business_type_name, TO_NAIKA_STATUS, PLATFORM, " +
            " CP_C_SHOP_ID, CP_C_SHOP_ECODE, CP_C_SHOP_TITLE, QTY_ALL, ORDER_AMT, IS_RESET_SHIP FROM oc_b_order WHERE id in"
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + " order by ID desc</script>")
    List<NaiKaOrderQueryModel> selectNaiKaQueryByIds(@Param("ids") List<Long> ids);

    /**
     * ljp add
     * 和合单共用一个model
     *
     * @param ids
     * @return
     */
    @Select("<script> "
            + "SELECT ID as ORDER_ID,* FROM oc_b_order WHERE id in"
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + " order by ID desc</script>")
    List<MergeOrderModel> selectListByIds(@Param("ids") List<Integer> ids);

    /**
     * ljp add
     *
     * @param ids
     * @return
     */
    @Update("<script> "
            + "UPDATE OC_B_ORDER SET invoice_status = #{invoiceStatus} where id in "
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    int updateOrderInvoicStatusList(@Param("ids") List<Long> ids, @Param("invoiceStatus") String invoiceStatus);

    /**
     * chenxiulou add
     *
     * @param ids
     * @return
     */
    @Select("<script> "
            + "SELECT * FROM oc_b_order WHERE id "
            + "in <foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<OcBOrder> selectByIdsStrList(@Param("ids") List<String> ids);

    /**
     * 更新为审核中
     *
     * @param orderIds
     * @param auditStatus
     */
    @Update("<script> "
            + "UPDATE OC_B_ORDER SET auto_audit_status = #{auditStatus},modifieddate = now() where is_interecept = 0 and id in "
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    void updateAuditOrderList(@Param("ids") List<Long> orderIds, @Param("auditStatus") Integer auditStatus);

    /**
     * 更新为审核中
     *
     * @param orderIds
     * @param auditStatus
     */
    @Update("<script> "
            + "UPDATE OC_B_ORDER SET OCCUPY_STATUS = #{occupyStatus},modifieddate = now() where OCCUPY_STATUS = #{whereOccupyStatus} and id in "
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    void updateOccpyOrderList(@Param("ids") List<Long> orderIds, @Param("occupyStatus") Integer auditStatus, @Param("whereOccupyStatus") Integer whereOccupyStatus);

    /**
     * 更新为已审核
     *
     * @param orderIds
     */
    @Update("<script> "
            + "UPDATE OC_B_ORDER SET auto_audit_status =6,order_status=3,modifieddate = now() where is_interecept = 0 and id in "
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    void updateAuditEndOrderList(@Param("ids") List<Long> orderIds);

    @Update("<script> "
            + "UPDATE OC_B_ORDER SET order_status = 21 WHERE is_interecept = 0 and order_status = 3 and is_overfive = 0 and id in "
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    void updateWmsOrderList(@Param("ids") List<Long> ids);

    @Update("<script> "
            + "UPDATE OC_B_ORDER SET order_status = 21 WHERE is_interecept = 0 and order_status = 3 and id in "
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    void updateWmsOrderTo21List(@Param("ids") List<Long> ids);

    @Update("UPDATE OC_B_ORDER SET order_status = 21 WHERE is_interecept = 0 and order_status = 3 and is_overfive = 0 and id = #{id} ")
    int updateByIdOrderStatus(@Param("id") Long id);

    @Select("<script> "
            + "SELECT * FROM oc_b_order WHERE order_status = 21 and id "
            + "in <foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<OcBOrder> selectByIdsListAndOrderStatus(@Param("ids") List<Long> ids);

    @Select("<script> "
            + "SELECT * FROM oc_b_order WHERE id "
            + "in <foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<OcBOrder> selectOrderListByIdsList(@Param("ids") List<Long> ids);

    @Select("<script> "
            + "SELECT * FROM oc_b_order WHERE   id "
            + "in <foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<OcBOrder> selectOrderListByIdsListOccpy7(@Param("ids") List<Long> ids);

    /**
     * 更新拆单状态【is_split 0=未拆分；1=已拆分；2=拆分中】
     * orderIds   * @param
     */
    @Update("<script> "
            + "UPDATE OC_B_ORDER SET is_split = #{status} where id in "
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    void updateSplitStatusList(@Param("status") int status, @Param("ids") List<Long> ids);

    @Update("UPDATE OC_B_ORDER SET is_split = #{status}  WHERE  id = #{id} ")
    int updateSplitStatusById(@Param("status") int status, @Param("id") Long id);

    @Update("UPDATE OC_B_ORDER SET POS_BILL_ID = #{status}  WHERE  id = #{id} ")
    int updateNotSplitStatusById(@Param("status") int status, @Param("id") Long id);

    @Select("<script> "
            + "SELECT * FROM oc_b_order WHERE is_interecept = 0 and order_status = 2  and isactive='Y' and id "
            + "in <foreach item='item' index='index' collection='orderIdList' open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<OcBOrder> queryShortageOrderInfo(@Param("orderIdList") List<Long> orderIdList);

    @Select("<script> "
            + "SELECT * FROM oc_b_order WHERE order_status !=7 and order_status != 8 and id in"
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    List<OcBOrder> selectListByIdsToVoid(@Param("ids") List<Long> ids);

    /**
     * 2019/9/24 胡林洋 【平台发货服务，发货失败次数】
     * 根据主订单id，更新订单的发货失败次数
     *
     * @param orderId 订单id 分库键
     * @return int
     */
    @Update("UPDATE oc_b_order  SET makeup_fail_num  = makeup_fail_num + 1 WHERE id=#{orderId}")
    int updateMasterFailTimesById(@Param("orderId") long orderId);

    /**
     * 批量更新订单状态
     * orderIds @param
     */
    @Update("<script> "
            + "UPDATE OC_B_ORDER SET order_status = #{status} where id in "
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    void updateOrderStatusList(@Param("status") int status, @Param("ids") List<Long> ids);

    /**
     * 批量查询待审核的数据
     *
     * @param ids
     * @return
     */
    @Select("<script> "
            + "SELECT * FROM oc_b_order WHERE is_interecept = 0 AND order_status=1 AND isactive='Y' and id in"
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    List<OcBOrder> selectAuditOrderInfoList(@Param("ids") List<Long> ids);

    @Select("<script> "
            + "SELECT * FROM oc_b_order WHERE order_status=1 AND isactive='Y' and id in"
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    List<OcBOrder> selectAuditOrderInfoListNotHold(@Param("ids") List<Long> ids);

    /**
     * 批量更新订单为“配货中”状态，
     * orderIds @param
     */
    @Update("<script> "
            + "UPDATE OC_B_ORDER SET order_status = 4 ,distribution_time = now() ,sysremark = '' ,is_exception = '0',exception_type = '',exception_type = '' where id in "
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    void updateInDistributionStatusList(@Param("ids") List<Long> ids);

    /**
     * 2020/08/29 黄志优
     * 保存出库单号
     *
     * @param orderId       订单ID
     * @param noticesBillNo 出库通知单
     */
    @Update("UPDATE OC_B_ORDER SET sg_b_out_bill_no = #{noticesBillNo},sg_b_out_bill_id=#{noticesBillId} where id = #{orderId}")
    void saveNoticesBillNo(@Param("orderId") Long orderId, @Param("noticesBillNo") String noticesBillNo, @Param("noticesBillId") Long noticeId);

    @Update("UPDATE OC_B_ORDER SET audit_time = null where id = #{orderId}")
    int cleanAuditTime(@Param("orderId") Long orderId);

    @Update("UPDATE oc_b_order " +
            "SET cp_c_phy_warehouse_ecode = NULL, " +
            "cp_c_phy_warehouse_id = NULL, " +
            "cp_c_phy_warehouse_ename = NULL, " +
            "cp_c_logistics_ecode = null, " +
            "cp_c_logistics_ename = null, " +
            "cp_c_logistics_id = null, " +
            "auto_audit_status = 0, " +
            "occupy_status = 0, " +
            "ORDER_STATUS = 50 " +
            "WHERE id = #{id}")
    int updateOrderEmptyWarehouse(@Param("id") Long id);


    @Update("UPDATE oc_b_order " +
            "SET cp_c_phy_warehouse_ecode = NULL, " +
            "cp_c_phy_warehouse_id = NULL, " +
            "cp_c_phy_warehouse_ename = NULL " +
            "WHERE id = #{id}")
    int updateOrderEmptyWarehouseById(@Param("id") Long id);

    @Select("<script> "
            + "SELECT * FROM oc_b_order WHERE order_status = 4 and id in"
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<OcBOrder> selectByIdsListDistribution(@Param("ids") List<Long> ids);

    /**
     * 根据传入时间，更新订单实缺标记为0
     *
     * @param fromDate 传入时间
     * @return
     */
    @Update("UPDATE oc_b_order  SET pos_bill_id  = 0 WHERE creationdate >= #{fromDate} and pos_bill_id  = 1")
    int updateRealShortageTag(@Param("fromDate") Date fromDate);

    @Select("SELECT * FROM oc_b_order WHERE id=#{orderID} and order_status not in ('7','8') and isactive='Y'")
    OcBOrder selectOrderCancelAndCancel(Long orderID);

    @Select("<script> "
            + "SELECT * FROM oc_b_order WHERE (order_status = 1 or order_status = 2)  and id "
            + "in <foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<OcBOrder> selectByIdsByStatus(@Param("ids") List<Long> ids);

    @Select("<script> "
            + "SELECT COST_CENTER_ID FROM oc_b_order WHERE id=#{id} "
            + "</script>")
    Long getCostCenterById(@Param("id") Long id);

    /**
     * 订单查询
     * 入库匹配
     *
     * @return
     */
    @SelectProvider(type = SqlProvider.class, method = "queryOrderListByIds4Match")
    List<OcBOrder> queryOrderListByIds4Match(@Param("sqlParam") String sqlParam);

    /**
     * 更新拆单次数
     * orderIds   * @param
     */
    @Update("<script> "
            + "UPDATE OC_B_ORDER SET qty_split = IFNULL(qty_split,0) + 1 where id in "
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    void updateSplitReserveBigint10List(@Param("ids") List<Long> ids);

    /**
     * 批量更新传对账状态
     *
     * @param toSettleStatus
     * @param ids
     * @return
     */
    /*@Update("<script> "
            + "update oc_b_order set to_settle_status = #{toSettleStatus}, modifieddate = now() where id "
            + "in <foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    int updateToSettleStatusByIds(@Param("toSettleStatus") Long toSettleStatus, @Param("ids") List<Long> ids);*/

    /**
     * 根据ids和传对账状态查询要传支付宝对账的订单(淘宝),
     *
     * @param node
     * @param limit
     * @return
     */
    @SelectProvider(type = OcBOrderMapper.SqlProvider.class, method = "selectOrderListByIdsAndToSettleStatus")
    List<OcBOrder> selectOrderListByIdsAndToSettleStatus(@Param("node") String node, @Param("tableName") String tableName
            , @Param("limit") Integer limit);

    @Update("<script> "
            + "UPDATE OC_B_ORDER SET split_status = #{splitStatus} where id in "
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    void updateSplitStatusByIds(@Param("splitStatus") int splitStatus, @Param("ids") List<Long> ids);

    /**
     * ☆更新平台发货
     *
     * @param id
     * @return
     */
    @Update("UPDATE oc_b_order  SET order_status=6,is_force=1 WHERE id=#{id}")
    int updateOrderAfterPlatDeliverySuccess(@Param("id") Long id);

    @Update("UPDATE OC_B_ORDER SET HOLD_RELEASE_TIME = #{holdReleaseTime} where id = #{orderId}")
    void updateHoldReleaseTime(@Param("orderId") Long orderId, @Param("holdReleaseTime") Date holdReleaseTime);

    /**
     * 审核补偿任务用 直接入rds查询
     *
     * @param nodeName
     * @param taskTableName
     */
    @SelectProvider(type = OrderDrdsSql.class, method = "selectByNodeSqlOrder")
    List<OcBOrder> selectOrderAudit(@Param(value = "nodeName") String nodeName,
                                    @Param(value = "taskTableName") String taskTableName, @Param("startTime") String startTime, @Param("endTime") String endTime);

    @SelectProvider(type = SqlProvider.class, method = "ocBOrderSelectByStatusSql")
    List<Long> ocBOrderSelectByStatusSql(@Param(value = "taskTableName") String taskTableName, @Param("orderStatus") Integer orderStatus);


    @UpdateProvider(type = SqlProvider.class, method = "updateByOnId")
    int updateByOnId(@Param("billNo") String billNo, @Param("status") String status, @Param("onroadDate") Date onroadDate, @Param("onroadTransferDate") Date onroadTransferDate, @Param("arrivedDate") Date arrivedDate);


    @Select("SELECT `ID` FROM oc_b_order WHERE SG_B_OUT_BILL_NO = #{billNo} AND ISACTIVE='Y'")
    OcBOrder selectBySgBOutBillNo(@Param("billNo") String billNo);

    @Select("<script>" +
            "select count(*) from oc_b_order where order_status not in (7,8) and tid in " +
            "<foreach item='item' index='index' collection='tids' open='(' separator=',' close=')'> #{item} </foreach>" +
            "</script>")
    long countByTidAndStatus(@Param("tids") List<String> tids);

    @Select("select id from oc_b_order where tid = #{tid}")
    Long queryByTid(@Param("tid") String tid);

    @Select("select cp_c_phy_warehouse_id from oc_b_order where bill_no = #{billNo} limit 1")
    Long queryStoreIdByBillNo(@Param("billNo") String billNo);

    @Select("<script>" +
            "select `ID`,ORDER_STATUS,RECEIVER_MOBILE,RECEIVER_NAME,CP_C_REGION_PROVINCE_ENAME,CP_C_REGION_CITY_ENAME,CP_C_REGION_AREA_ENAME,RECEIVER_ADDRESS,PLATFORM_STATUS,TID,BILL_NO,ORDER_AMT,RECEIVED_AMT,AMT_RECEIVE,IS_COPY_ORDER,IS_RESET_SHIP from oc_b_order where isactive = 'Y' and id in " +
            "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>" +
            "</script>")
    List<OcBOrder> queryListByTidsWithOutCopyAndResetShip(@Param("ids") List<Long> ids);

    @Select("<script>" +
            "select `ID`,TID,INVOICE_STATUS from oc_b_order where tid in " +
            "<foreach item='item' index='index' collection='tids' open='(' separator=',' close=')'> #{item} </foreach>" +
            "</script>")
    List<OcBOrder> queryInvoicedTid(@Param("tids") List<String> tidAll);

    @Select("select id from oc_b_order where tid = #{tid} and is_split = 1 and order_status = 6 and is_merge !=1 ")
    List<OcBOrder> selectDeliveryByTid(@Param("tid") String tid);

    @Select("<script>" +
            "select id, source_code, product_amt, creationdate,cp_c_region_province_ename,cp_c_region_city_ename, cp_c_region_area_ename,cp_c_shop_title " +
            "from oc_b_order where source_code in " +
            "<foreach item='item' collection='sourceCodes' open='(' separator=',' close=')'> #{item} </foreach>" +
            "</script>")
    List<OcBOrder> getOrdersForOaBySourceCodes(@Param("sourceCodes") List<String> sourceCodes);

    @Select("<script>" +
            "SELECT tid, oc_b_order_id, ps_c_sku_ecode, qty FROM oc_b_order_item WHERE is_gift = 1 and" +
            "<foreach collection='list' item='item' separator=' OR '>" +
            "(tid = #{item.tid} AND oc_b_order_id = #{item.ocBOrderId})" +
            "</foreach>" +
            "</script>")
    List<OcBOrderItem> getGiftOrderDetailByTidAndOrderId(@Param("list") List<Map<String, Object>> maps);

    @Select("<script>" +
            "select id, source_code, product_amt, creationdate,cp_c_region_province_ename,cp_c_region_city_ename, cp_c_region_area_ename,cp_c_shop_title " +
            "from oc_b_order where " +
            "<foreach item='item' collection='sourceCodes' separator=' or '> source_code like concat(#{item}, '%') </foreach>" +
            "</script>")
    List<OcBOrder> getOrdersForOaRegexSourceCodes(@Param("sourceCodes") List<String> sourceCodes);


    class UpdateRecord {
        public String updateSql(JSONObject map) {
            return new SQL() {
                {
                    UPDATE("OC_B_ORDER");
                    for (String key : map.keySet()) {
                        if (!"ID".equalsIgnoreCase(key)) {
                            SET(key + "= #{" + key + "}");
                        }
                    }
                    WHERE("ID = #{ID}");
                }
            }.toString();
        }

        /**
         * 条件查询
         *
         * @param sqlParam sql
         * @param isActive y/n
         * @return sql
         */
        public String queryOrderList(@Param("sqlParam") String sqlParam, @Param("isActive") String isActive) {
            String sql = "";
            if (sqlParam.indexOf("WHERE") == -1) {
                sql = sqlParam + "WHERE ISACTIVE='" + isActive + "'";
            } else {
                sql = sqlParam + " AND ISACTIVE='" + isActive + "'";
            }
            return sql;
        }
    }

    /**
     * <AUTHOR>
     */
    class TheSameOrder {
        public String isTheSameOrder(Map<String, Object> map) {
            StringBuilder sql = new StringBuilder("SELECT ID FROM OC_B_ORDER WHERE ");
            StringBuilder where = new StringBuilder();
            int i = 1;
            for (String key : map.keySet()) {
                if (i != 1) {
                    where.append(" AND ");
                }
                where = where.append(key + "= #{" + key + "}");
                i++;
            }
            return sql.append(where).toString();
        }
    }

    /**
     * 自动匹配
     *
     * @param id 订单编号
     * @return 订单
     */
    @Select("SELECT * FROM OC_B_ORDER WHERE `ID`=#{id} AND ORDER_STATUS IN (5,6,12) AND ISACTIVE='Y'")
    OcBOrder queryOrder4Match(@Param("id") Long id);

    /**
     * @param sourceCodes 平台单号信息集合
     * @return 订单编号集
     */
    @Select("<script> SELECT `ID` FROM OC_B_ORDER WHERE SOURCE_CODE IN "
            + "<foreach item='item' index='index' collection='sourceCode' "
            + "open='(' separator=',' close=')'> #{item} </foreach>  </script>")
    List<Long> listIdFromGsiBySourceCodes(@Param("sourceCode") List<String> sourceCodes);


    /**
     * 根据订单id更新wms撤回次数
     *
     * @param id
     * @return
     */
    @Update("UPDATE oc_b_order SET wms_cancel_number=IFNULL(wms_cancel_number, 0) + 1 WHERE id=#{id}")
    int updateWmsCancelNumberById(@Param("id") Long id);


    /**
     * 更新wms撤回次数
     *
     * @param id              订单id
     * @param wmsCancelStatus wms撤回状态
     * @param wmsCancelNum    wms撤回次数
     * @return int
     */
    @Update("UPDATE oc_b_order SET wms_cancel_status= #{wmsCancelStatus}, wms_cancel_number=#{wmsCancelNum} WHERE id=#{id}")
    int updateWmsCancelNumber(@Param("id") Long id, @Param("wmsCancelStatus") int wmsCancelStatus, @Param("wmsCancelNum") int wmsCancelNum);

    /**
     * 根据订单id更新wms撤回次数和wms撤回状态
     *
     * @param id
     * @return
     */
    @Update("UPDATE oc_b_order SET wms_cancel_number=6,wms_cancel_status=3 WHERE id=#{id}")
    int updateWmsCancelNumberAndWmsCancelStatusById(@Param("id") Long id);


    /**
     * 批量更新订单
     * orderIds @param
     */
    @Update("<script> "
            + "UPDATE OC_B_ORDER SET sysremark ='',is_overfive=0 where id in "
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    int updateOrderIsOverfiveSysremarkList(@Param("ids") List<Long> ids);

    @Update("update oc_b_order set IS_INTERECEPT = #{isInterecept}, HOLD_RELEASE_TIME = #{holdReleaseTime} where id = #{id}")
    void updateOcBOrderReleaseTime(@Param("isInterecept") int isInterecept, @Param("holdReleaseTime") Date holdReleaseTime, @Param("id") Long id);

    @Update("update oc_b_order set IS_INTERECEPT = #{isInterecept}, HOLD_RELEASE_TIME = #{holdReleaseTime},HOLD_REASON = #{holdReason}  where id = #{id}")
    void updateOcBOrderReleaseTimeEmpty(@Param("isInterecept") int isInterecept, @Param("holdReleaseTime") Date holdReleaseTime, @Param("id") Long id, @Param("holdReason") String holdReason);

    /**
     * 合单查询
     *
     * @param ocBOrder
     * @return
     */
    @SelectProvider(type = SqlProvider.class, method = "listOrder4MergeByIds")
    List<OcBOrder> listOrder4MergeByIds(@Param("ocBOrder") OcBOrder ocBOrder);

    // 20210117-release gsi start

    /**
     * @param sourceCode 平台单号信息
     * @return 订单编号集
     */
    @Select("SELECT `ID` FROM OC_B_ORDER WHERE SOURCE_CODE = #{sourceCode}")
    List<Long> listIdFromGsiBySourceCode(@Param("sourceCode") String sourceCode);

    /**
     * @param sourceCodes 平台单号信息集合
     * @return 订单编号集
     */
    @Select("<script> SELECT source_code FROM OC_B_ORDER WHERE SOURCE_CODE IN "
            + "<foreach item='item' index='index' collection='sourceCode' "
            + "open='(' separator=',' close=')'> #{item} </foreach>  </script>")
    List<String> listSourceCodeFromGsiBySourceCodes(@Param("sourceCode") List<String> sourceCodes);

    /**
     * @param sourceCode 平台单号
     * @return 订单集合
     */
    @Select("SELECT * FROM OC_B_ORDER WHERE SOURCE_CODE = #{sourceCode}")
    List<OcBOrder> getOrdersUnionGsiBySourceCode(@Param("sourceCode") String sourceCode);

    /**
     * 获取非已作废和已经取消的单据信息
     *
     * @param sourceCode 平台单号
     * @return 订单集合
     */
    @Select("SELECT * FROM OC_B_ORDER WHERE SOURCE_CODE = #{sourceCode} AND ORDER_STATUS NOT IN (7,8)")
    List<OcBOrder> getValidOrdersUnionGsiBySourceCode(@Param("sourceCode") String sourceCode);

    /**
     * 审核.校验重单
     *
     * @param sourceCode 平台单号
     * @param id         订单编号
     * @return 订单编号集合
     */
    @Select("SELECT `ID` FROM OC_B_ORDER WHERE SOURCE_CODE = #{sourceCode} AND"
            + " `ID`!=#{id} AND ORDER_STATUS NOT IN (7,8)")
    List<Long> listIdsBySourceCode4Audit(@Param("sourceCode") String sourceCode, @Param("id") Long id);


    // 20210117-release gsi end


    @Update("UPDATE OC_B_ORDER SET ORDER_STATUS = 3,AUTO_AUDIT_STATUS = 2,SYSREMARK='',AUDIT_TIME = now(),modifieddate = now()," +
            "MODIFIERENAME = #{name},AUDIT_FAILED_TYPE = null,AUDIT_FAILED_REASON = null where id = #{orderId}")
    int updateAuditSuccess(@Param("orderId") Long orderId, @Param("name") String name);

    @Update("<script>" +
            "UPDATE OC_B_ORDER SET ORDER_STATUS = 3,AUTO_AUDIT_STATUS = 2,SYSREMARK='',AUDIT_TIME = now()," +
            "modifieddate = now(),MODIFIERENAME = #{user.name}," +
            "AUDIT_TYPE = 'manual',AUDIT_SUCCESS_DATE = now(),AUDIT_ID=#{user.id},AUDIT_NAME=#{user.ename}," +
            "<if test = 'actualPresinkStatus != null'> ACTUAL_PRESINK_STATUS = #{actualPresinkStatus}, </if>" +
            "AUDIT_FAILED_TYPE = null,AUDIT_FAILED_REASON = null " +
            "where id = #{orderId} </script>")
    int updateAuditSuccessNew(@Param("orderId") Long orderId, @Param("user") User user, @Param("actualPresinkStatus") String actualPresinkStatus);

    /**
     * 审核.校验重单
     *
     * @param sourceCode 平台单号
     * @param id         订单编号
     * @param suffixInfo 订单补充信息
     * @return 订单编号集合
     */
    @Select("SELECT `ID` FROM OC_B_ORDER WHERE SOURCE_CODE = #{sourceCode} AND"
            + " `ID`!=#{id} AND SUFFIX_INFO=#{suffixInfo} AND ORDER_STATUS NOT IN (7,8)")
    List<Long> listIdsBySourceCodeSuffixInfo4Audit(@Param("sourceCode") String sourceCode, @Param("id") Long id,
                                                   @Param("suffixInfo") String suffixInfo);

    /**
     * description:获取平台发货
     *
     * @Author: liuwenjin
     * @Date 2022/9/23 22:54
     */
    @Select("SELECT * FROM OC_B_ORDER WHERE SOURCE_CODE = #{sourceCode} AND ORDER_STATUS = 6 AND ORDER_TYPE != 2 AND IS_COPY_ORDER != 1 AND IS_RESET_SHIP !=1")
    List<OcBOrder> queryGsiBySourceCodeByTid(@Param("sourceCode") String sourceCode);

    /**
     * 查询.订单
     * 内置.多过滤条件
     *
     * @return id, encrypt codes
     */
    @SelectProvider(type = SqlProvider.class, method = "listIdsByEncryptCodes")
    List<EncryptCodeGSI> listIdsByEncryptCodes(MergeParam taskParam);

    /**
     * @param taskParam
     * @return
     */
    @SelectProvider(type = SqlProvider.class, method = "listOrdersByEncryptCodes")
    List<OcBOrder> listOrdersByEncryptCodes(MergeParam taskParam);


    @Update("update `oc_b_order` set jitx_requires_delivery_warehouse_id=null,jitx_requires_delivery_warehouse_code=null,jitx_requires_delivery_warehouse_name=null where id=#{id}")
    int clearRequiresDeliveryWarehouse(Long id);

    /**
     * 批量更新订单状态为待分配寻源状态为开始占单
     * orderIds @param
     */
    @Update("<script> "
            + "UPDATE OC_B_ORDER SET order_status = 50,OCCUPY_STATUS = 2,modifieddate = now() where id in "
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + " AND order_status = 2 AND occupy_status != 3"
            + "</script>")
    void updateOrderStatusAndOccupyStatusList(@Param("ids") List<Long> ids);


    @Select("<script>" +
            "SELECT * FROM oc_b_order  WHERE bill_no in " +
            "<foreach item='billNo' collection='billNoList' separator=',' open='(' close=')' > #{billNo} </foreach>" +
            "</script>")
    List<OcBOrder> queryOrderByBillNo(@Param("billNoList") List<String> billNoList);

    @Select("<script>" +
            "SELECT * FROM oc_b_order  WHERE order_status in (1,3,4,5,6) and bill_no in " +
            "<foreach item='billNo' collection='billNoList' separator=',' open='(' close=')' > #{billNo} </foreach>" +
            "</script>")
    List<OcBOrder> queryOccupyOrderByBillNo(@Param("billNoList") List<String> billNoList);

    @Select("<script>" +
            "SELECT * FROM oc_b_order  WHERE order_status in (3,4,5,6) and bill_no in " +
            "<foreach item='billNo' collection='billNoList' separator=',' open='(' close=')' > #{billNo} </foreach>" +
            "</script>")
    List<OcBOrder> queryAuditOrderByBillNo(@Param("billNoList") List<String> billNoList);


    @Update("update `oc_b_order` set RECEIPT_DATE= #{sapArrivedDate} where id=#{id}")
    int setSapArrivedDate(@Param("id") Long id, @Param("sapArrivedDate") Date sapArrivedDate);

    @Select("<script>" +
            "SELECT id, bill_no, source_code, business_type, IS_RESET_SHIP, PLATFORM, to_naika_status, cp_c_shop_ecode FROM oc_b_order  WHERE id = #{id}" +
            "</script>")
    OcBOrder get4NaiKaOrder(Long id);


    @Select("<script>" +
            "SELECT id, business_type_id, business_type_code, business_type_name FROM oc_b_order  WHERE id = #{id}" +
            "</script>")
    OcBOrder get4AfReturn(Long id);

    /**
     * 查询待寻源的单子
     *
     * @param ids
     * @return
     */
    @Select("<script> "
            + "SELECT * FROM oc_b_order WHERE order_status = 2 and id in"
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<OcBOrder> selectByIdsListOccupy(@Param("ids") List<Long> ids);


    /**
     * 清空物流信息
     *
     * @param id id
     */
    @Update("UPDATE oc_b_order SET CP_C_LOGISTICS_ID=null,CP_C_LOGISTICS_ECODE=null,CP_C_LOGISTICS_Ename=null WHERE ID=#{id}")
    void removeExpressInfo(@Param("id") Long id);

    @Select("<script> "
            + "SELECT * FROM oc_b_order WHERE order_status = 50 and id in "
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach> "
            + " order by id asc"
            + "</script>")
    List<OcBOrder> selectByIdsListBy50(@Param("ids") List<Long> ids);


    @Select("<script> "
            + "SELECT * FROM oc_b_order WHERE order_status = 50 and occupy_status = 170 and id in "
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach> "
            + " order by id asc"
            + "</script>")
    List<OcBOrder> selectByIdsListByStatus50AndOccupy170(@Param("ids") List<Long> ids);


    /**
     * 根据子订单号查询订单
     *
     * @param ids 订单Id
     * @return OcBOrder
     */
    @Select("<script> "
            + "select b.* from OC_B_ORDER b,OC_B_ORDER_ITEM a where a.oc_b_order_id=b.id and a.ooid in"
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    List<OcBOrder> selectOcBOrderInfo(@Param("ids") List<String> ids);

    @Select("select * from OC_B_ORDER where bill_no=#{billNo}")
    OcBOrder queryOcBOrderInfo(@Param("billNo") String billNo);

    @Select("SELECT * FROM OC_B_ORDER  WHERE TID = #{tid}")
    List<OcBOrder> selectOcBOrderByTid(@Param("tid") String tid);


    @Update("<script> "
            + "UPDATE oc_b_order SET IS_INVOICE=1 WHERE ID in"
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    int updateOrderInvoice(@Param("ids") List<Long> ids);


    @Update("UPDATE oc_b_order " +
            "SET cp_c_phy_warehouse_ecode = NULL, cp_c_phy_warehouse_id = NULL,cp_c_phy_warehouse_ename = NULL,order_status = 2 " +
            "WHERE id = #{orderId}")
    int updateWarehouse(@Param("orderId") Long orderId);

    @Update("UPDATE oc_b_order " +
            "SET order_status = #{orderStatus} " +
            "WHERE id = #{orderId}")
    int updateOrderStatus(@Param("orderId") Long orderId, @Param("orderStatus") Integer orderStatus);

    @Select("SELECT oc_b_order_id FROM OC_B_ORDER_ITEM WHERE TID = #{tid}")
    List<Long> selectOcBOrderIdByTid(@Param("tid") String tid);

    class SqlProvider {

        String cols = "ID, BILL_NO, SOURCE_CODE, CP_C_SHOP_ID, CP_C_SHOP_TITLE, CP_C_PHY_WAREHOUSE_ID,FORCE_SEND_FAIL_REASON," +
                "CP_C_PHY_WAREHOUSE_ENAME, USER_NICK, ORDER_TYPE,MERGED_CODE, JITX_REQUIRES_MERGE,IS_FORBIDDEN_DELIVERY,IS_VIP_UPDATE_WAREHOUSE,REVERSE_AUDIT_TYPE," +
                "JITX_REQUIRES_DELIVERY_WAREHOUSE_NAME,MERGED_SN,JITX_MERGED_DELIVERY_SN,BASIC_PRICE_USED,EXPAND_PRICE_USED," +
                "ORDER_STATUS, SUFFIX_INFO, PRODUCT_AMT, ORDER_AMT, RECEIVED_AMT, CONSIGN_AMT, IS_INVOICE, PAY_TYPE," +
                "IS_GENINVOICE_NOTICE, IS_CALCWEIGHT, CP_C_LOGISTICS_ENAME, EXPRESSCODE, ORDER_DATE, PAY_TIME, " +
                "AUDIT_TIME, BUYER_EMAIL, RECEIVER_NAME, RECEIVER_MOBILE, CP_C_REGION_PROVINCE_ENAME, QTY_ALL," +
                "CP_C_REGION_CITY_ENAME, CP_C_REGION_AREA_ENAME, CP_C_REGION_TOWN_ENAME, RECEIVER_ADDRESS, IS_MERGE, IS_SPLIT, " +
                "WMS_CANCEL_STATUS,BUSINESS_TYPE_ID,BUSINESS_TYPE_NAME,BUSINESS_TYPE_CODE, " +
                "IS_INTERECEPT, IS_INRETURNING, BUYER_MESSAGE, ORDER_SOURCE, ORIG_ORDER_ID, ORIG_RETURN_ORDER_ID, " +
                "PRESALE_DEPOSIT_TIME, IS_HASGIFT, LOCK_STATUS,SKU_KIND_QTY," +
                "SYSREMARK,SELLER_MEMO, PLATFORM, SCAN_TIME,  OUT_STATUS, TID, ORDER_TAG," +
                " IS_JCORDER,  DOUBLE11_PRESALE_STATUS, IS_INVENTED, IS_COMBINATION, " +
                "IS_OUT_URGENCY, IS_HAS_TICKET, CREATIONDATE, " +
                // "IS_WRITEOFF, IS_GIVE_LOGISTIC,IS_HASPRESALESKU,SYS_PRESALE_STATUS,   " +
                "CP_C_SHOP_SELLER_NICK, INVOICE_STATUS, OC_B_INVOICE_NOTICE_ID, IS_EXCHANGE_NO_IN, " +
                "PRICE_LABEL, STATUS_PAY_STEP, CP_C_LABEL_ENAME, CP_C_LABEL_CONTENT, CP_C_LABEL_ID," +
                "PLATFORM_STATUS,PAY_STATUS,REFUND_STATUS,COPY_REASON,HOLD_RELEASE_TIME," +
                "IS_O2O_ORDER,IS_COPY_ORDER,IS_RESET_SHIP,IS_PROM_ORDER,IS_DELIVERY_URGENT,LIVE_FLAG," +
                "MERGE_SOURCE_CODE," +
                "ANCHOR_ID,ANCHOR_NAME,IS_MODIFIED_ORDER,IS_EQUAL_EXCHANGE,IS_OUT_STOCK,RECEIPT_DATE, RETURN_STATUS, OWNERID, OWNERENAME, " +
                "OWNERNAME, DISTRIBUTION_TIME," +
                "PRODUCT_DISCOUNT_AMT,ORDER_DISCOUNT_AMT,ADJUST_AMT,JD_RECEIVE_AMT,JD_SETTLE_AMT,AMT_RECEIVE," +
                "SHIP_AMT,SERVICE_AMT," +
                "IS_SAME_CITY_PURCHASE," +
                //SETTLE_SUPPLIER_CODE,SETTLE_SUPPLIER_NAME,SETTLE_ORGANIZATION_CODE,SETTLE_ORGANIZATION_NAME,
                "TO_SAP_STATUS," +
                "GW_SOURCE_GROUP," +
                "IS_CYCLE,IS_MEMBER,IS_DELIVERY_TO_DOOR,IS_UNAVAILABLE_SHOP,IS_NO_RANGE,IS_REMARK_GIFT," +
                "ORDER_SOURCE_PLATFORM_ECODE," +
                "APPOINT_LOGISTICS_ID," +
                "APPOINT_LOGISTICS_ECODE," +
                "APPOINT_LOGISTICS_ENAME," +
                "IS_EXTRA," +  // 加直播轻供字段查询
                "AUDIT_FAILED_TYPE,AUDIT_FAILED_REASON,SG_B_OUT_BILL_NO,SG_B_OUT_BILL_ID,SALESMAN_NAME," + // 20210117版本 黄志优增加 审核失败字段查询
                "LIVE_EVENTS,SPLIT_REASON_ID,CUSTOM_REASON,ADVANCE_TYPE,IS_DETENTION,IS_DETENTION,ALL_SKU,PLATFORM_DELIVERY_TIME,WAREHOUSE_DELIVERY_TIME,SUGGEST_PRESINK_STATUS,ACTUAL_PRESINK_STATUS,HOLD_REASON,DETENTION_REASON,ST_C_CUSTOM_LABEL_ENAME,ESTIMATE_CON_TIME," +//直播主体 配合主体 直播场次 -自定义拆单原因
                "HOLD_RELEASE_REASON,HOLD_RELEASE_NAME,HOLD_RELEASE_DATE,LOGISTICS_STATUS,audit_id,audit_name,is_occupy_stock_fail,is_exception,exception_type," +
                "exception_explain,seller_id,pay_type,HOLD_REASON_ID,DETENTION_REASON_ID,SALES_DEPARTMENT_NAME,SALES_GROUP_CODE,SALES_GROUP_NAME,SALE_PRODUCT_ATTR,IS_OVERDUE, G" +
                "ENERIC_MARK, CURRENT_CYCLE_NUMBER, CARPOOL_NO";

        /**
         * 查询需要拆单的订单
         */
        public String selectByNodeSql(Map<String, Object> para) {
            StringBuilder sql = new StringBuilder();
            String platforms = (String) para.get("platforms");
            String payTypes = (String) para.get("payTypes");
            int limit = para.get("limit") != null ? (int) para.get("limit") : 1000;
            String taskTableName = (String) para.get("tableName");
            sql.append("SELECT ")
                    .append(FIELDS)
                    .append(" FROM ")
                    .append(taskTableName)
                    .append(" where order_status = 2 ")
                    .append(" and platform not in ( ")
                    .append(platforms)
                    .append(" ) and pay_type in ( ")
                    .append(payTypes)
                    .append(" ) and split_status = 0")
                    .append(" limit ")
                    .append(limit);
            return sql.toString();
        }

        public String ocBOrderSelectByStatusSql(Map<String, Object> para) {
            StringBuilder sql = new StringBuilder();
            Integer orderStatus = (Integer) para.get("orderStatus");
            String taskTableName = (String) para.get("taskTableName");
            sql.append("SELECT ")
                    .append("ID")
                    .append(" FROM ")
                    .append(taskTableName)
                    .append(" where order_status =  ")
                    .append(orderStatus);
            return sql.toString();
        }

        /**
         * 查询缺货的订单
         */
        public String selectOutStockOccupyOrderByNodeSql(Map<String, Object> para) {
            StringBuilder sql = new StringBuilder();
            String nodeName = (String) para.get("nodeName");
            String orderStatus = (String) para.get("orderStatus");
            String occupyStatus = (String) para.get("occupyStatus");
            int limit = para.get("limit") != null ? (int) para.get("limit") : 1000;
            String taskTableName = (String) para.get("tableName");
            sql.append("/*!TDDL:NODE=" + nodeName + "*/")
                    .append("SELECT ")
                    .append("ID")
                    .append(" FROM ")
                    .append(taskTableName)
                    .append(" where order_status =  ")
                    .append(orderStatus)
                    .append(" and occupy_status != ")
                    .append(occupyStatus)
                    .append(" order by modifieddate ASC ")
                    .append(" limit ")
                    .append(limit);
            return sql.toString();
        }

        /**
         * ids查询
         *
         * @param sqlParam
         * @param isActive
         * @return
         */
        public String orderListQueryByIds(@Param("sqlParam") String sqlParam, @Param("isActive") String isActive) {
            StringBuilder sb = new StringBuilder();
            sb.append("SELECT ");
            sb.append(FIELDS);
            sb.append(" FROM OC_B_ORDER WHERE ");
            sb.append("ID IN (");
            sb.append(sqlParam);
            sb.append(" )");
            sb.append(" AND ISACTIVE='" + isActive + "' ORDER BY `ORDER_DATE` DESC");
            return sb.toString();
        }

        /**
         * 列表查询专用, 禁止修改
         *
         * @param sqlParam
         * @return
         */
        public String searchOrderList(@Param("sqlParam") String sqlParam, @Param("orderField") String orderField) {

            StringBuilder sb = new StringBuilder();
            sb.append("SELECT ")
                    .append(cols)
                    .append(" FROM OC_B_ORDER WHERE ")
                    .append("ID IN (")
                    .append(sqlParam)
                    .append(" )")
                    .append(" AND ISACTIVE='Y' ORDER BY ")
                    .append(orderField);
            return sb.toString();
        }

        /**
         * export
         *
         * @param sqlString string
         * @param isActive  y
         * @return string
         */
        public String selectExportOrderResultById(@Param("sqlString") String sqlString,
                                                  @Param("isActive") String isActive) {
            StringBuilder sb = new StringBuilder();
            sb.append("SELECT ");
            sb.append(FIELDS);
            sb.append(" FROM OC_B_ORDER WHERE ");
            sb.append("ID IN (");
            sb.append(sqlString);
            sb.append(" )");
            sb.append(" AND ISACTIVE='" + isActive + "' ORDER BY `ID` DESC");
            return sb.toString();
        }

        /**
         * sap
         *
         * @param sqlString string
         * @return string
         */
        public String selectListForSap(@Param("sqlString") String sqlString) {

            StringBuilder sb = new StringBuilder();
            sb.append("SELECT *  FROM OC_B_ORDER WHERE ")
                    .append("ID IN (").append(sqlString).append(" )")
                    .append(" AND IFNULL(TO_SAP_STATUS,0) =0 AND ISACTIVE='Y' AND (IS_HISTORY ='N' OR IS_HISTORY is " +
                            "null)");
            return sb.toString();
        }

        /**
         * 同步第三方系统
         *
         * @param taskParam TaskParam
         * @return string
         */
        public String selectList4SyncThirdSys(TaskParam taskParam) {

            StringBuilder sb = new StringBuilder("SELECT * FROM OC_B_ORDER WHERE ID IN (");
            sb.append(taskParam.getKeyStrings()).append(" ) AND IFNULL(")
                    .append(taskParam.getOrigStatusCol())
                    .append(",0)=0 AND ISACTIVE='Y' AND IS_HISTORY='N'");
            return sb.toString();
        }


        /**
         * 查询订单
         * 入库匹配专用.待变化, 勿使用
         *
         * @param sqlParam
         * @return
         */
        public String queryOrderListByIds4Match(@Param("sqlParam") String sqlParam) {
            StringBuilder sb = new StringBuilder("SELECT  * FROM OC_B_ORDER WHERE `ID` IN (");
            sb.append(sqlParam)
                    .append(" )  AND ISACTIVE ='Y' ORDER BY MODIFIEDDATE DESC");
            return sb.toString();
        }

        /**
         * 合单数据查询
         *
         * @param ocBOrder 查询条件
         * @return sql
         */
        public String listOrder4MergeByIds(@Param("ocBOrder") OcBOrder ocBOrder) {
            StringBuilder sb = new StringBuilder("SELECT * FROM OC_B_ORDER WHERE `ID` IN (");
            sb.append(ocBOrder.getReserveVarchar01());
            sb.append(") AND ORDER_STATUS=").append(ocBOrder.getOrderStatus())
                    .append(" AND IS_INTERECEPT=").append(ocBOrder.getIsInterecept())
                    .append(" AND IS_INRETURNING=").append(ocBOrder.getIsInreturning())
                    .append(" AND PAY_TYPE !=").append(ocBOrder.getPayType())
//                    .append(" AND IFNULL(IS_FORBIDDEN_DELIVERY,0) !=").append(ocBOrder.getIsForbiddenDelivery())
                    .append(" AND PLATFORM !='66'")
                    .append(" AND IS_SAME_CITY_PURCHASE !=").append(ocBOrder.getIsSameCityPurchase())
                    .append(" AND ISACTIVE='Y'");
            return sb.toString();
        }

        /**
         * 消费
         * 合单查询. 表. 索引
         *
         * @return id, 加密串
         */
        public String listIdsByEncryptCodes(MergeParam taskParam) {
            StringBuilder sb = new StringBuilder();
            sb.append("SELECT `ID`, ORDER_ENCRYPTION_CODE FROM OC_B_ORDER ")
                    .append("WHERE ORDER_STATUS=1 AND IS_INTERECEPT=0 AND IS_INRETURNING=0 AND PAY_TYPE !=2 ")
                    .append("AND PLATFORM !='66' AND IS_SAME_CITY_PURCHASE !=1 ")
                    .append("AND CP_C_SHOP_ID IN(").append(taskParam.getShopKeys()).append(" )")
                    .append("AND ORDER_ENCRYPTION_CODE IN(")
                    .append(taskParam.getEncryptCodes())
                    .append(")");
            return sb.toString();
        }

        /**
         * 查询订单.预合并数据
         *
         * @param taskParam 自动店铺
         * @return
         */
        public String listOrdersByEncryptCodes(MergeParam taskParam) {
            StringBuilder sb = new StringBuilder();
            sb.append("SELECT * FROM OC_B_ORDER ")
                    .append("WHERE ORDER_ENCRYPTION_CODE IN(")
                    .append(taskParam.getEncryptCodes())
                    .append(") ")
                    .append("AND CP_C_SHOP_ID IN(").append(taskParam.getShopKeys()).append(" ) ")
                    .append("AND ORDER_STATUS=1 AND IS_INTERECEPT=0 AND IS_INRETURNING=0 AND PAY_TYPE !=2 ")
                    .append("AND PLATFORM !='66' AND IS_SAME_CITY_PURCHASE !=1 ");
            return sb.toString();
        }

        /**
         * 查询对账数据
         *
         * @param node
         * @param limit
         * @return
         */
        public String selectOrderListByIdsAndToSettleStatus(@Param("node") String node, @Param("tableName") String tableName
                , @Param("limit") Integer limit) {
            StringBuilder sb = new StringBuilder();
            sb.append("/*!TDDL:NODE=")
                    .append(node)
                    .append("*/ ")
                    .append("SELECT * FROM ")
                    .append(tableName)
                    .append(" where order_status in (5,6)  and to_settle_status in(0,1,3) and platform = 2 limit ")
                    .append(limit);
            return sb.toString();
        }

        public String updateByOnId(@Param("billNo") String billNo, @Param("status") String status, @Param("onroadDate") Date onroadDate, @Param("onroadTransferDate") Date onroadTransferDate, @Param("arrivedDate") Date arrivedDate) {
            return new SQL() {
                {
                    UPDATE("oc_b_order");
                    if (status != null) {
                        SET("LOGISTICS_STATUS=#{status}");
                    }
                    if (onroadDate != null) {
                        //SET("ONROAD_DATE=#{onroadDate}");
                    }
                    if (onroadTransferDate != null) {
                        //SET("ONROAD_TRANSFER_DATE=#{onroadTransferDate}");
                    }
                    if (arrivedDate != null) {
                        //SET("ARRIVED_DATE=#{arrivedDate}");
                    }
                    WHERE("SG_B_OUT_BILL_NO=#{billNo}");
                }
            }.toString();
        }

        /**
         * 查询指定快递且缺货的订单
         *
         * @param nodeName
         * @param tableName
         * @return
         */
        public String selectOrderListByAppointLogistics(@Param("tableName") String tableName) {
            StringBuilder sb = new StringBuilder();
            sb.append("SELECT ")
                    .append("ID")
                    .append(" FROM ")
                    .append(tableName)
                    .append(" where order_status = 2 and appoint_logistics_id is not null ")
                    .append(" and is_out_stock = 1 and (is_detention is null or is_detention = 0) ");
            return sb.toString();
        }

    }


    @Select("<script> "
            + "SELECT * FROM oc_b_order WHERE id "
            + "in <foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + " order by pay_time"
            + "</script>")
    List<OcBOrder> selectOrderListByIdsOrderByPayTime(@Param("ids") List<Long> ids);

    @Update("<script> "
            + "UPDATE OC_B_ORDER SET platform_status = #{platformStatus}, modifieddate = now(), MODIFIERENAME = #{userName} where id in "
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    int updateTradeFinishList(@Param("ids") List<Long> ids, @Param("platformStatus") String platformStatus, @Param("userName") String userName);

    @Update("UPDATE oc_b_order " +
            "SET appoint_logistics_ecode = NULL, appoint_logistics_id = NULL,appoint_logistics_ename = NULL " +
            "WHERE id = #{orderId}")
    int updateAppointLogistics(@Param("orderId") Long orderId);

    @Update("UPDATE oc_b_order " +
            "SET ORDER_TYPE = #{orderType}, modifieddate = now() " +
            "WHERE id = #{orderId}")
    int updateOrderType(@Param("orderId") Long orderId, @Param("orderType") Integer orderType);

    @Select("<script> "
            + "SELECT ID, BILL_NO, ORDER_SOURCE, IS_COPY_ORDER, IS_RESET_SHIP, PLATFORM FROM oc_b_order WHERE id=#{orderId}"
            + "</script>")
    GetOrderForCancelModel getOrderForCancelById(@Param("orderId") Long orderId);

    @Select("SELECT id FROM oc_b_order WHERE order_status = #{status}")
    List<Long> selectOrderIdsByStatus(@Param("status") Integer status);

    @Select("<script> "
            + "SELECT id FROM oc_b_order WHERE isactive='Y' and order_status not in (2,22) and id in"
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    List<Long> select4CancelAppointLogistics(@Param("ids") List<Long> ids);

    @Select("<script> "
            + "SELECT * FROM oc_b_order WHERE isactive='Y' and tid = #{tid} and business_type_code in ('RYCK06','RYCK07') and order_status not in (7,8)"
            + "</script>")
    List<OcBOrder> selectCycleOrderByTid(@Param("tid") String tid);

    @Select("SELECT * FROM oc_b_order WHERE isactive='Y' and tid = #{tid} and current_cycle_number = 1  and order_status = 6")
    List<OcBOrder> selectFirstCycleDeliveryOrder(@Param("tid") String tid);

    @Select("SELECT * FROM oc_b_order WHERE isactive='Y' and tid = #{tid} and current_cycle_number = #{cycleNumber} and order_status = 6")
    List<OcBOrder> selectDeliveryOrderByTidAndCycleNumber(@Param("tid") String tid, @Param("cycleNumber") Integer cycleNumber);

    @Select("SELECT * FROM oc_b_order WHERE isactive='Y' and tid = #{tid} and current_cycle_number = #{cycleNumber} and order_status in (5,6)")
    List<OcBOrder> selectDeliveryByTidAndCycleNumber(@Param("tid") String tid, @Param("cycleNumber") Integer cycleNumber);


    @Select("SELECT * FROM oc_b_order WHERE isactive='Y' and tid = #{tid} and current_cycle_number = 1 and business_type_code in ('RYCK08') and order_status = 5")
    List<OcBOrder> selectDeliveryFailOrderByTid(@Param("tid") String tid);


    @Select("SELECT * FROM oc_b_order WHERE isactive='Y' and tid = #{tid} and order_status in (5,6)")
    List<OcBOrder> selectDeliveryOrderByTid(@Param("tid") String tid);

    /**
     * 根据订单id清空拼车单号
     *
     * @param ids
     */
    @Update("<script> "
            + "UPDATE OC_B_ORDER SET carpool_no = null WHERE id in "
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    void updateOrderCarpoolNoToNull(@Param("ids") List<Long> ids);

    /**
     * 根据时间范围 拉取带寻源(不卡单)的订单，限制最多只拉一万单
     *
     * @param startTime
     * @param endTime
     * @return
     */
    @Select("SELECT id FROM oc_b_order WHERE isactive='Y' and creationdate >= #{startTime} and creationdate <=#{endTime} and order_status = 2 and IS_Detention != 1 order by id asc limit 10000")
    List<Long> selectOcBOrderIdByCreationDate(@Param("startTime") String startTime, @Param("endTime") String endTime);
}