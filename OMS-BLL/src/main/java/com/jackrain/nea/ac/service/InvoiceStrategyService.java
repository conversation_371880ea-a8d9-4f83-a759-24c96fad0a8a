package com.jackrain.nea.ac.service;

import com.jackrain.nea.oc.oms.mapper.StCInvoiceStrategyMapper;
import com.jackrain.nea.oc.oms.model.table.StCInvoiceStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * @ClassName InvoiceStrategyService
 * @Description
 * @Date 2022/9/20 上午10:29
 * @Created by wuhang
 */
@Slf4j
@Service
public class InvoiceStrategyService {

    @Autowired
    private StCInvoiceStrategyMapper invoiceStrategyMapper;

    /**
     * 根据shopid获取店铺开票策略,如果存在则返回
     * @param shopId
     * @return
     */
    public StCInvoiceStrategy getByShopId(Long shopId){
        if(Objects.isNull(shopId)){
            return null;
        }
        StCInvoiceStrategy strategy = invoiceStrategyMapper.queryByShopId(shopId);
        if(Objects.nonNull(strategy)){
            return strategy;
        } else {
            StCInvoiceStrategy defaultStrategy = invoiceStrategyMapper.queryByShopNull();
            return defaultStrategy;
        }
    }
}
