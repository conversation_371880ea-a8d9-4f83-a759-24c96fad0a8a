package com.jackrain.nea.oc.oms.model.result;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 订单查询-分页
 *
 * @author: xiwen.z
 * create at: 2019/3/13 0013
 */
@Data
public class QueryDeficiencyItemListResult implements Serializable {
    /**
     * 其他相关业务数据
     */
    private List<QueryDeficiencyItemResult> queryItemResultList;
    /**
     * 当前页
     */
    private Integer pageNum;
    /**
     * 每页条数
     */
    private Integer pageSize;
    /**
     * 总页数
     */
    private Integer totalNum;
    /**
     * 总记录数
     */
    private Long totalSize;

}
