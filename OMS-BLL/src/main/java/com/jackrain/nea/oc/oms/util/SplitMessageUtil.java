package com.jackrain.nea.oc.oms.util;

import com.jackrain.nea.model.util.AdParamUtil;
import com.jackrain.nea.util.Tools;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * @author: heliu
 * @since: 2019/8/1
 * create at : 2019/8/1 13:50
 */
@Slf4j
public class SplitMessageUtil {
    public static final int SIZE_200 = 200;
    public static final int SIZE_500 = 500;
    public static final int SIZE_100 = 100;
    public static final int SIZE_999 = 999;

    /**
     * 设置订单流程错误信息截取长度动态参数
     *
     * @param remark
     * @return 截取后的信息
     */
    public static String splitMesssage(String remark) {

        //从系统参数取出默认参数
        int reamrkSize = Tools.getInt(AdParamUtil.getParam("oms.oc.order.remarksize.num"), 500);
        //异常信息超过500 截取500
        if (StringUtils.isNotEmpty(remark)) {
            if (remark.length() > reamrkSize) {
                remark = remark.substring(0, reamrkSize);
            }
        }
        return remark;
    }

    public static String splitErrMsgBySize(String remark, int size) {

        //异常信息超过500 截取500
        if (StringUtils.isNotEmpty(remark)) {
            if (remark.length() > size) {
                remark = remark.substring(0, size);
            }
            return remark;
        } else {
            return "异常信息为空";
        }
    }

    public static String splitMsgBySize(String remark, int size) {
        if (StringUtils.isNotEmpty(remark)) {
            if (remark.length() > size) {
                remark = remark.substring(0, size);
            }
            return remark;
        } else {
            return "";
        }
    }
}