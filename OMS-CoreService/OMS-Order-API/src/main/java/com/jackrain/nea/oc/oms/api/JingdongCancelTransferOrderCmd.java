package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.oc.oms.model.request.TransferOrderRequest;
import com.jackrain.nea.oc.oms.model.result.ReturnOrderResult;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongSaRefund;
import com.jackrain.nea.sys.domain.ValueHolderV14;

/**
 * @description: 京东取消订单
 * @author: 郑小龙
 * @date: 2020-06-04 11:58
 **/
public interface JingdongCancelTransferOrderCmd {
    ValueHolderV14<ReturnOrderResult> startCancelTransfer(TransferOrderRequest transferOrderRequest);

    ValueHolderV14 jdOrderCancelToSa(IpBJingdongSaRefund saRefund);
}
