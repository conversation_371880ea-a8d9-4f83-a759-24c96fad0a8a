package com.jackrain.nea.oc.controller.invoice;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.ac.service.OrderInvoiceChangeTaskService;
import com.jackrain.nea.oc.oms.mapper.ac.AcFInvoiceApplyMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName InvoiceApplyController
 * @Description
 * @Date 2022/9/13 下午7:21
 * @Created by wuhang
 */
@RestController
@RequestMapping("/api/cs/oc/oms")
public class InvoiceApplyController {

    @Autowired
    private OrderInvoiceChangeTaskService service;

    @Autowired
    private AcFInvoiceApplyMapper acFInvoiceApplyMapper;

    @RequestMapping("/apply/change")
    public String change(@RequestBody JSONObject param){
        Long id = param.getLong("id");
//        service.changeApplyInvoice(null,null,null,null,null,acFInvoiceApplyMapper.selectById(id));
        return "success!";
    }
}
