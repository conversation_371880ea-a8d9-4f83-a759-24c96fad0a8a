package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBOrderAppointSplitCmd;
import com.jackrain.nea.oc.oms.model.relation.UserPermission;
import com.jackrain.nea.oc.oms.services.OmsOrderAppointSplitService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: hulinyang
 * @since: 2020/3/12
 * create at : 2020/3/12 11:20
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBOrderAppointSplitCmdImpl implements OcBOrderAppointSplitCmd {

    @Autowired
    private OmsOrderAppointSplitService omsOrderAppointSplitService;

    @Override
    public ValueHolderV14 saveAppointSplitOrderInfo(JSONObject param, User user, UserPermission usrPem) throws NDSException {
        return omsOrderAppointSplitService.batchAppointSplitOrder(param, user, usrPem);
    }
}