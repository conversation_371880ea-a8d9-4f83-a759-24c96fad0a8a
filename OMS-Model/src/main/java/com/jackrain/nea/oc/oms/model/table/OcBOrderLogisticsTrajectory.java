package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.util.Date;

@TableName(value = "oc_b_order_logistics_trajectory")
@Data
public class OcBOrderLogisticsTrajectory extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "TYPE")
    @Field(type = FieldType.Keyword)
    private String type;

    @JSONField(name = "SENDER")
    @Field(type = FieldType.Keyword)
    private String sender;

    @JSONField(name = "CODE")
    @Field(type = FieldType.Keyword)
    private String code;

    @JSONField(name = "DATE")
    @Field(type = FieldType.Keyword)
    private String date;

    @JSONField(name = "BUSICODE")
    @Field(type = FieldType.Keyword)
    private String busicode;

    @JSONField(name = "INVOKE_UNIKEY")
    @Field(type = FieldType.Keyword)
    private String invokeUnikey;

    @JSONField(name = "ECNO")
    @Field(type = FieldType.Keyword)
    private String ecno;

    @JSONField(name = "ORIGINAL_LEGNO")
    @Field(type = FieldType.Keyword)
    private String originalLegno;

    @JSONField(name = "ORIGINAL_ORDER_CODE")
    @Field(type = FieldType.Keyword)
    private String originalOrderCode;

    @JSONField(name = "ORIGINAL_STATUS")
    @Field(type = FieldType.Keyword)
    private String originalStatus;

    @JSONField(name = "LEGNO")
    @Field(type = FieldType.Keyword)
    private String legno;

    @JSONField(name = "ORDER_CODE")
    @Field(type = FieldType.Keyword)
    private String orderCode;

    @JSONField(name = "TRACK_NO")
    @Field(type = FieldType.Keyword)
    private String trackNo;

    @JSONField(name = "ORDER_TYPE")
    @Field(type = FieldType.Keyword)
    private String orderType;

    @JSONField(name = "STATUS")
    @Field(type = FieldType.Keyword)
    private String status;

    /**
     * 物流状态，和框架status字段区分，显示到界面
     */
    @JSONField(name = "LOGISTIC_STATUS")
    @Field(type = FieldType.Keyword)
    private String logisticStatus;

    @JSONField(name = "KPI_TIME")
    @Field(type = FieldType.Keyword)
    private String kpiTime;

    @JSONField(name = "RECORD_TIME")
    @Field(type = FieldType.Keyword)
    private String recordTime;

    @JSONField(name = "CONS_TIME")
    @Field(type = FieldType.Keyword)
    private String consTime;

    @JSONField(name = "PLAN_ARRIVE_TIME")
    @Field(type = FieldType.Keyword)
    private String planArriveTime;

    @JSONField(name = "OPER_PERSON")
    @Field(type = FieldType.Keyword)
    private String operPerson;

    @JSONField(name = "LOCATION")
    @Field(type = FieldType.Keyword)
    private String location;

    @JSONField(name = "REMARK")
    @Field(type = FieldType.Keyword)
    private String remark;

    @JSONField(name = "NAME")
    @Field(type = FieldType.Keyword)
    private String name;

    @JSONField(name = "DESCRIPTION")
    @Field(type = FieldType.Keyword)
    private String description;

    @JSONField(name = "IMG_URL")
    @Field(type = FieldType.Keyword)
    private String imgUrl;

    @JSONField(name = "DRIVER_NAME")
    @Field(type = FieldType.Keyword)
    private String driverName;

    @JSONField(name = "DRIVER_MOBILE")
    @Field(type = FieldType.Keyword)
    private String driverMobile;

    @JSONField(name = "VEHICLE_NO")
    @Field(type = FieldType.Keyword)
    private String vehicleNo;

    @JSONField(name = "MERGE_ORIGINAL_LEGNO")
    @Field(type = FieldType.Keyword)
    private String mergeOriginalLegno;

    @JSONField(name = "MERGE_ORIGINAL_ORDER_CODE")
    @Field(type = FieldType.Keyword)
    private String mergeOriginalOrderCode;

    @JSONField(name = "QUANTITY")
    @Field(type = FieldType.Keyword)
    private String quantity;

    @JSONField(name = "WEIGHT")
    @Field(type = FieldType.Keyword)
    private String weight;

    @JSONField(name = "VOLUME")
    @Field(type = FieldType.Keyword)
    private String volume;

    @JSONField(name = "CARTON")
    @Field(type = FieldType.Keyword)
    private String carton;

    @JSONField(name = "VERSION")
    @Field(type = FieldType.Long)
    private Long version;

    @JSONField(name = "AD_ORG_ID")
    @Field(type = FieldType.Long)
    private Long adOrgId;

    @JSONField(name = "ISACTIVE")
    @Field(type = FieldType.Keyword)
    private String isactive;

    @JSONField(name = "AD_CLIENT_ID")
    @Field(type = FieldType.Long)
    private Long adClientId;

    @JSONField(name = "OWNERID")
    @Field(type = FieldType.Long)
    private Long ownerid;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "OWNERNAME")
    @Field(type = FieldType.Keyword)
    private String ownername;

    @JSONField(name = "CREATIONDATE")
    @Field(type = FieldType.Date)
    private Date creationdate;

    @JSONField(name = "MODIFIERID")
    @Field(type = FieldType.Long)
    private Long modifierid;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;

    @JSONField(name = "MODIFIERNAME")
    @Field(type = FieldType.Keyword)
    private String modifiername;

    @JSONField(name = "MODIFIEDDATE")
    @Field(type = FieldType.Date)
    private Date modifieddate;

    @JSONField(name = "ORIG_ORDER_ID")
    @Field(type = FieldType.Long)
    private Long origOrderId;

    /**
     * 原始订单类型：1-零售发货单；2-退换货单
     */
    @JSONField(name = "ORIG_ORDER_TYPE")
    @Field(type = FieldType.Long)
    private Integer origOrderType;
}