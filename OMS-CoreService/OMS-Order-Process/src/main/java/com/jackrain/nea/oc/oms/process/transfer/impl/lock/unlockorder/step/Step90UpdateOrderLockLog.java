package com.jackrain.nea.oc.oms.process.transfer.impl.lock.unlockorder.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.relation.IpOrderLockRelation;
import com.jackrain.nea.oc.oms.nums.OrderLockLogTypeEnum;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 更新锁单操作日志表
 *
 * @author: 黄超
 * @since: 2019-10-10
 * create at : 2019-10-10 19:00
 */
@Step(order = 90, description = "更新锁单操作日志表")
@Component
@Slf4j
public class Step90UpdateOrderLockLog extends BaseUnlockProcessStep
        implements IOmsOrderProcessStep<IpOrderLockRelation> {

    @Override
    public ProcessStepResult<IpOrderLockRelation> startProcess(IpOrderLockRelation orderInfo, ProcessStepResult preStepResult,
                                                               boolean isAutoMakeup, User operateUser) {
        this.ipOrderLockService.insetIpOrderLockLog(OrderLockLogTypeEnum.UNLOCK.getKey(),
                orderInfo.getRemarks() != null ? orderInfo.getRemarks() : "解锁成功",
                orderInfo.getOrderId(), null, operateUser);
        return new ProcessStepResult<>(StepStatus.SUCCESS);
    }
}
