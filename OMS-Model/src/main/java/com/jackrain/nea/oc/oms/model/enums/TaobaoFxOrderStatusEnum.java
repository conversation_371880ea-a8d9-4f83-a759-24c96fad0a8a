package com.jackrain.nea.oc.oms.model.enums;


/**
 * 退款状态选项值
 *
 * @author: 周琳胜
 * create at: 2019/3/20 13:20
 */
public enum TaobaoFxOrderStatusEnum {
    /**
     * 全渠道明细退款状态和淘宝分销退款状态有不同，
     * 具体参考（PRD-R3-接口平台-淘宝分销接口-退单发货前转换服务-曾志军 -20190709）
     * 订单明细 买家已退货，等待买家收货 3=淘宝分销3，10（卖家拒绝确认收货），12(同意退款，待打款)
     * 订单明细 卖家拒绝退款 4=淘宝分销 6
     * 订单明细 退款关闭 5 =淘宝分销4
     * 订单明细 退款成功 6=淘宝分销5
     */
    /**
     * 未退款
     */
    NOT_REFUND,

    /**
     * 申请退款，等待卖家同意
     */
    APPLY_REFUND_TO_SELLER,

    /**
     * 同意退款，等待买家收货
     */
    AGREE_WAIT_BUY_RECEIVER,

    /**
     * 买家已退货，等待卖家收货
     */
    WAIT_SELLER_RECEIVER,

    /**
     * 卖家拒绝退款
     */
    SELLER_REFUSE,
    /**
     * 退款关闭
     */
    REFUND_CLOSE,
    /**
     * 退款成功
     */
    REFUND_SUCCESS,
    /**
     * 10 卖家拒绝确认收货
     */
    REFUND_TEN,
    /**
     * 退款中
     */
    TRADE_REFUNDING,
    /**
     * 退款完成
     */
    TRADE_REFUNDED,
    /**
     * 12 同意退款，待打款
     */
    REFUND_TWELVE;

    public int toInteger() {
        if (this == NOT_REFUND) {
            return 0;
        } else if (this == APPLY_REFUND_TO_SELLER) {
            return 1;
        } else if (this == AGREE_WAIT_BUY_RECEIVER) {
            return 2;
        } else if (this == WAIT_SELLER_RECEIVER) {
            return 3;
        } else if (this == REFUND_TEN) {
            return 10;
        } else if (this == REFUND_TWELVE) {
            return 12;
        } else if (this == SELLER_REFUSE) {
            return 6;
        } else if (this == REFUND_CLOSE) {
            return 4;
        } else {
            return 5;
        }
    }


}


