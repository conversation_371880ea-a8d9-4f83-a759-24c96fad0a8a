package com.jackrain.nea.oc.oms.model.result;

import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class OcBReturnOrderRefundBatchResult implements Serializable {
    private List<OcBReturnOrderRefund> listRefund;
    private String allSku;
    private BigDecimal totamt;
    private BigDecimal addQty;

}
