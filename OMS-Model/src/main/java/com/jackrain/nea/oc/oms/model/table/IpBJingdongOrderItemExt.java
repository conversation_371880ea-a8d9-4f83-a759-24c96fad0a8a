package com.jackrain.nea.oc.oms.model.table;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.ps.model.ProductSku;
import lombok.Data;

/**
 * <AUTHOR> 孙俊磊
 * @since : 2019-04-28
 * create at : 2019-04-28 10:36 AM
 */
@TableName(value = "ip_b_jingdong_order_item")
@Data
public class IpBJingdongOrderItemExt extends IpBJingdongOrderItem {

    @TableField(exist = false)
    private ProductSku prodSku;

}
