package com.jackrain.nea.oc.oms.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: 黄世新
 * @Date: 2019/11/4 1:49 下午
 * @Version 1.0
 */
@Data
public class OmsOcBOrder extends BaseModel {


    @JSONField(name = "CP_C_SHOP_ID")
    private Long cpCShopId;

    @JSONField(name = "CP_C_SHOP_ECODE")
    private String cpCShopEcode;

    @JSONField(name = "ORDER_STATUS")
    private Integer orderStatus;

    private String billNo;

    @J<PERSON><PERSON>ield(name = "RECEIVER_NAME")
    private String receiverName;

    @JSONField(name = "RECEIVER_MOBILE")
    private String receiverMobile;

    @JSONField(name = "RECEIVER_PHONE")
    private String receiverPhone;

    @J<PERSON>NField(name = "CP_C_REGION_PROVINCE_ENAME")
    private String cpCRegionProvinceEname;

    @J<PERSON><PERSON>ield(name = "CP_C_REGION_CITY_ENAME")
    private String cpCRegionCityEname;

    @JSONField(name = "CP_C_REGION_AREA_ENAME")
    private String cpCRegionAreaEname;

    @JSONField(name = "RECEIVER_ADDRESS")
    private String receiverAddress;

    @JSONField(name = "RECEIVER_ZIP")
    private String receiverZip;

    @JSONField(name = "RECEIVER_EMAIL")
    private String receiverEmail;

    @JSONField(name = "PLATFORM")
    private Integer platform;

    @JSONField(name = "TID")
    private String tid;

    @JSONField(name = "VERSION")
    private Long version;

    @JSONField(name = "BUYER_MESSAGE")
    private String buyerMessage; //买家备注

    @JSONField(name = "SELLER_MEMO")
    private String sellerMemo;  //卖家备注

    @JSONField(name = "INSIDE_REMARK")
    private String insideRemark;

    @JSONField(name = "RECEIVED_AMT")
    private BigDecimal receivedAmt; //已收金额

    @JSONField(name = "ORDER_DISCOUNT_AMT")
    private BigDecimal orderDiscountAmt; //订单优惠金额

    @JSONField(name = "PAY_TIME")
    private Date pay_time; //付款时间

    @JSONField(name = "SYSREMARK")
    private String sysremark; //系统备注

    @JSONField(name = "POS_ID")
    private Long posId; //dui

    @JSONField(name = "CP_C_STORE_ID")
    private Long cpCStoreId;  //门店id

    @JSONField(name = "SEND_TIME")
    private Date sendTime;

    @JSONField(name = "DELIVERY_STORE_ID")
    private Long deliveryStoreId;

    //是否drp调用；1-是，0-否
    private Integer isDrp;

    private Date vpCVipEcode;

    private Date vpCVipMobil;

    //是否一件代发 1 是 0 否
    private Integer isOneDistribution;

    private String cpCShopTitle;

    private String cpCShopSellerNick;

    private String cpCStoreEcode;

}
