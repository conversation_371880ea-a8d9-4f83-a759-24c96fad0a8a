package com.jackrain.nea.oc.oms.process.transfer.impl.refund.vip.step;

import com.alibaba.fastjson.JSON;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.IsActiveEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpVipReturnOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBVipReturnOrder;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @create 2020-12-28 14:48
 * @desc 校验退供单的可用状态和转换状态
 **/
@Step(order = 1, description = "校验退供单的可用状态和转换状态")
@Slf4j
@Component
public class Step001CheckReturnOrderStatus extends BaseVipReturnOrderProcessStep
        implements IOmsOrderProcessStep<IpVipReturnOrderRelation> {
    @Override
    public ProcessStepResult<IpVipReturnOrderRelation> startProcess(IpVipReturnOrderRelation orderInfo,
                                                                    ProcessStepResult preStepResult,
                                                                    boolean isAutoMakeup, User operateUser) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Step001CheckReturnOrderStatus orderInfo:{}","Step001CheckReturnOrderStatus",orderInfo.getOrderNo()), JSON.toJSONString(orderInfo));
        }
        IpVipReturnOrderRelation orderInfo2 = ipVipReturnOrderService.selectVipReturnOrderRelation(orderInfo.getOrderNo());
        if (orderInfo2 == null || orderInfo2.getVipReturnOrder() == null) {
            return new ProcessStepResult<>(StepStatus.FAILED, "orderInfo2为空或者Order.vipReturnOrder为空；退出转换");
        }
        orderInfo.setVipReturnOrder(orderInfo2.getVipReturnOrder());
        orderInfo.setVipReturnOrderItems(orderInfo2.getVipReturnOrderItems());
        IpBVipReturnOrder vipReturnOrder = orderInfo.getVipReturnOrder();
        if (!IsActiveEnum.Y.getKey().equals(vipReturnOrder.getIsactive())) {
            return new ProcessStepResult(StepStatus.FAILED, "退供单已作废，不允许转换！");
        }
        if (vipReturnOrder.getTransStatus() == null
                || (vipReturnOrder.getTransStatus() != TransferOrderStatus.NOT_TRANSFER.toInteger()
                && vipReturnOrder.getTransStatus() != TransferOrderStatus.TRANSFERFAIL.toInteger())) {
            return new ProcessStepResult(StepStatus.FAILED,
                    "退供单转换状态不是未转换或者转换失败，不允许转换！");
        }
        return new ProcessStepResult(StepStatus.SUCCESS,
                "校验退供单的可用状态和转换状态成功，进入下一阶段！");
    }
}
