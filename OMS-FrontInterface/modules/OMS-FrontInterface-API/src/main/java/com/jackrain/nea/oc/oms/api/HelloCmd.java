package com.jackrain.nea.oc.oms.api;

import com.alibaba.fastjson.JSONObject;

/**
 * @Author: 黄世新
 * @Date: 2019/5/16 1:20 PM
 * @Version 1.0
 */
public interface HelloCmd {

    JSONObject queryEs();

    JSONObject queryEsByCondition(String var1, String var2, JSONObject var3, JSONObject var4);

    String sendMq(String var1, String var2, String var3, String var4, String var5);

    String hello(String var1);
}
