package com.jackrain.nea.oc.oms.model.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: gxx
 * @since: 2020-05-28
 * create at : 2020-05-28 14:46
 */


public enum StepTradeStatusEnum {
    /**
     * 全部付款
     */
    ALL_PAY(0, "全部付款"),

    /**
     * 部分付款
     */
    PART_PAY(1, "部分付款"),

    /**
     * 未付款
     */
    NO_PAY(2, "未付款");

    Integer key;
    String val;

    StepTradeStatusEnum(Integer k, String v) {
        this.key = k;
        this.val = v;
    }

    public Integer getKey() {
        return key;
    }

    public String getVal() {
        return val;
    }

    public static String getValueByKey(Integer key) {

        String s = "";
        if (key == null) {
            return s;
        }
        for (StepTradeStatusEnum e : StepTradeStatusEnum.values()) {
            if (e.getKey().equals(key)) {
                s = e.getVal();
                break;
            }
        }
        return s;
    }

    public static Map<Integer, String> getMap() {
        Map<Integer, String> m = new HashMap<>();
        for (StepTradeStatusEnum o : StepTradeStatusEnum.values()) {
            m.put(o.getKey(), o.getVal());
        }
        m.put(null, "");
        return m;
    }

}
