package com.jackrain.nea.ac.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.ac.model.AcFCompensationReason;
import com.jackrain.nea.ac.model.AcFCompensationType;
import com.jackrain.nea.ac.model.AcFPayableAdjustmentDO;
import com.jackrain.nea.ac.model.AcFPayableAdjustmentItemDO;
import com.jackrain.nea.ac.model.AcFPayableAdjustmentLogDO;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.cpext.model.table.CpCStore;
import com.jackrain.nea.cpext.model.table.CpLogistics;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ext.sequence.util.SequenceGenUtil;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.ac.AcFCompensationReasonMapper;
import com.jackrain.nea.oc.oms.mapper.ac.AcFCompensationTypeMapper;
import com.jackrain.nea.oc.oms.mapper.ac.AcFPayableAdjustmentItemMapper;
import com.jackrain.nea.oc.oms.mapper.ac.AcFPayableAdjustmentLogMapper;
import com.jackrain.nea.oc.oms.mapper.ac.AcFPayableAdjustmentMapper;
import com.jackrain.nea.oc.oms.model.constant.AcConstant;
import com.jackrain.nea.oc.oms.model.enums.ac.OperatorLogTypeEnum;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.psext.model.table.PsCPro;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.AcBeanUtils;
import com.jackrain.nea.util.AcPayableAdjustmentPushESUtil;
import com.jackrain.nea.util.AmountCalcUtils;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.util.JsonUtils;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Bll层-新增保存业务逻辑
 *
 * <AUTHOR> 陈俊明
 * @since : 2019-03-22
 * create at : 2019-03-22 14:13
 */
@Component
@Slf4j
public class PayableAdjustmentSaveService extends CommandAdapter {
    @Resource
    private AcFPayableAdjustmentMapper acFPayableAdjustmentMapper;

    @Resource
    private AcFPayableAdjustmentItemMapper acFPayableAdjustmentItemMapper;

    @Resource
    private AcFPayableAdjustmentLogMapper payableAdjustmentLogMapper;

    @Autowired
    AcFCompensationReasonMapper acFCompensationReasonMapper;

    @Autowired
    AcFCompensationTypeMapper acFCompensationTypeMapper;


    @Resource
    private PsRpcService psRpcService;

    @Resource
    private CpRpcService cpRpcService;

    /**
     * 新增和修改
     *
     * @param querySession 参数封装
     * @return 返回状态
     * @throws NDSException 异常信息
     */
    @Transactional(rollbackFor = {Exception.class})
    public ValueHolderV14 executeSave(QuerySession querySession) throws NDSException {
        ValueHolderV14 valueHolder = new ValueHolderV14();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("executeSave.entry", "executeSave"), param);
        }

        if (param != null) {
            Long id = param.getLong("objid");
            JSONObject fixColumn = param.getJSONObject("fixcolumn");
            String returnMsg = "保存成功";
            int resultCode = ResultCode.SUCCESS;

            if (fixColumn != null) {
                JSONObject mainMap = fixColumn.getJSONObject(AcConstant.TAB_AC_F_PAYABLE_ADJUSTMENT);
                JSONArray itemMap = fixColumn.getJSONArray(AcConstant.TAB_AC_F_PAYABLE_ADJUSTMENT_ITEM);

                if (itemMap == null || itemMap.size() <= 0) {
                    throw new NDSException("明细不能为空，保存失败");
                }

                try {
                    valueHolder = saveMain(mainMap, itemMap, valueHolder, querySession, id);
                } catch (Exception e) {
                    log.error(LogUtil.format("save丢件单异常: {}", "丢件单异常"), Throwables.getStackTraceAsString(e));
                    resultCode = ResultCode.FAIL;
                    returnMsg = e.getMessage();
                }
                valueHolder.setCode(resultCode);
                valueHolder.setMessage(returnMsg);
            }
        } else {
            throw new NDSException("参数为空！");
        }

        return valueHolder;
    }


    /**
     * 除法
     *
     * @param dec1         数字1
     * @param dec2         数字2
     * @param scale        位数
     * @param roundingMode 四舍五入规则
     * @return dec1 / dec2
     */
    public static BigDecimal divideBigDecimal(final BigDecimal dec1, final BigDecimal dec2,
                                              final int scale, final int roundingMode) {
        if (dec2 == null || BigDecimal.ZERO.compareTo(dec2) == 0) {
            return BigDecimal.ZERO;
        }
        return (dec1 != null ? dec1 : BigDecimal.ZERO).divide(dec2, scale, roundingMode);
    }

    private boolean checkStatus(AcFPayableAdjustmentDO acFPayableAdjustmentDO, Long objId, ValueHolderV14 valueHolder) {
        //判断数据是否存在
        if (objId > 0) {
            acFPayableAdjustmentDO = acFPayableAdjustmentMapper.selectById(objId);

            if (acFPayableAdjustmentDO != null) {
                int iStatus = acFPayableAdjustmentDO.getBillStatus();
                if (iStatus != 1) {
                    throw new NDSException("当前记录状态非未审核，不允许编辑！");
                }
            } else {
                throw new NDSException("当前记录已不存在！");
            }
        }

        return true;
    }

    private CpCPhyWarehouse cpPhyWarehouseGetRpc(Long id) {
        CpCPhyWarehouse cpCPhyWarehouse = null;
        try {
            //调用服务
            cpCPhyWarehouse = cpRpcService.selectPhyWarehouseById(id);
            if (cpCPhyWarehouse == null) {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("PayableAdjustmentSaveService.selectPhyWarehouseById is null ", "cpPhyWarehouseGetRpc"));
                }
            }
        } catch (Exception e) {
            log.error(LogUtil.format("PayableAdjustmentSaveService.selectPhyWarehouseById 服务调用异常:{}", "cpPhyWarehouseGetRpc"), Throwables.getStackTraceAsString(e));
        }

        return cpCPhyWarehouse;
    }

    private CpLogistics cpLogisticsGetRpc(Long id) {
        CpLogistics cpLogistics = null;
        try {
            //调用服务
            cpLogistics = cpRpcService.selectLogisticsById(id);
            if (cpLogistics == null) {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("PayableAdjustmentSaveService.cpLogisticsGetRpc is null ", "cpLogisticsGetRpc"));
                }
            }
        } catch (Exception e) {
            log.error(LogUtil.format("PayableAdjustmentSaveService.cpLogisticsGetRpc 服务调用异常:{}", "cpLogisticsGetRpc"), Throwables.getStackTraceAsString(e));
        }

        return cpLogistics;
    }

    private CpCPhyWarehouse getPhyWarehouseName(AcFPayableAdjustmentDO acFPayableAdjustmentDO) {
        CpCPhyWarehouse cpCPhyWarehouse = null;
        if (acFPayableAdjustmentDO != null) {
            if (acFPayableAdjustmentDO.getCpCPhyWarehouseId() != null) {
                cpCPhyWarehouse = cpPhyWarehouseGetRpc(acFPayableAdjustmentDO.getCpCPhyWarehouseId());
            }
        }
        return cpCPhyWarehouse;
    }

    private CpLogistics getLogisticsName(AcFPayableAdjustmentDO acFPayableAdjustmentDO) {
        CpLogistics cpLogistics = null;
        if (acFPayableAdjustmentDO != null) {
            if (acFPayableAdjustmentDO.getCpCLogisticsId() != null) {
                cpLogistics = cpLogisticsGetRpc(acFPayableAdjustmentDO.getCpCLogisticsId());
            }
        }
        return cpLogistics;
    }

    /**
     * 新增操作
     *
     * @param mainMap      主表数据
     * @param itemMap      子表数据
     * @param valueHolder  响应数据
     * @param querySession 封装数据
     * @param objId        id
     * @return 返回状态
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 saveMain(JSONObject mainMap,
                                   JSONArray itemMap,
                                   ValueHolderV14 valueHolder,
                                   QuerySession querySession, Long objId) throws NDSException {
        AcFPayableAdjustmentDO acFPayableAdjustmentDO = JsonUtils.jsonParseClass(mainMap,
                AcFPayableAdjustmentDO.class);
        User user = querySession.getUser();
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("PayableAdjustmentSaveService.saveMain.entry:{} ", "saveMain"), mainMap);
        }
        if (acFPayableAdjustmentDO != null) {
            Date date = new Date();

            //状态数据检查
            if (!checkStatus(acFPayableAdjustmentDO, objId, valueHolder)) {
                return valueHolder;
            }

            Long cpShopId = acFPayableAdjustmentDO.getCpCShopId();
            CpShop cpShop = cpRpcService.selectCpCShopById(cpShopId);
            if (cpShop != null) {
                //店铺名称
                String cpCShopTitle = cpShop.getCpCShopTitle();
                acFPayableAdjustmentDO.setCpCShopTitle(cpCShopTitle);

                //渠道类型
                Long channelType = Long.parseLong(cpShop.getChannelType());
                acFPayableAdjustmentDO.setReserveBigint01(channelType);
            }

            //根据实体仓ID，调用接口，关联出实体仓名称
            CpCPhyWarehouse cpCPhyWarehouse = this.getPhyWarehouseName(acFPayableAdjustmentDO);
            if (cpCPhyWarehouse != null) {
                acFPayableAdjustmentDO.setCpCPhyWarehouseEname(cpCPhyWarehouse.getEname());
                acFPayableAdjustmentDO.setCpCPhyWarehouseEcode(cpCPhyWarehouse.getEcode());
            }

            //根据物流公司档案ID,调用接口，关联出物流公司档案名称
            CpLogistics cpLogistics = this.getLogisticsName(acFPayableAdjustmentDO);
            if (cpLogistics != null) {
                acFPayableAdjustmentDO.setCpCLogisticsEname(cpLogistics.getEname());
                acFPayableAdjustmentDO.setCpCLogisticsEcode(cpLogistics.getEcode());
            }

            if (objId < 0) {
                acFPayableAdjustmentDO.setId(ModelUtil.getSequence(AcConstant.TAB_AC_F_PAYABLE_ADJUSTMENT));
                //新增单据状态默认为: 待审核(未审核)
                acFPayableAdjustmentDO.setBillStatus(AcConstant.CON_BILL_STATUS_01);
                //单据编号生成更新
                JSONObject sequence = new JSONObject();
                String billNo = SequenceGenUtil.generateSquence("SEQ_AC_F_PAYABLE_ADJUSTMENT",
                        sequence, querySession.getUser().getLocale(), false);
                acFPayableAdjustmentDO.setBillNo(billNo);

                //新增 字段 赔付类型
                Integer compensationTypeId = mainMap.getInteger("AC_F_COMPENSATION_TYPE_ID");
                acFPayableAdjustmentDO.setAcFCompensationTypeId(compensationTypeId);
                AcFCompensationType compensationType = this.acFCompensationTypeMapper.selectOne(new QueryWrapper<AcFCompensationType>().lambda().eq(AcFCompensationType::getId, compensationTypeId));
                if (compensationType != null) {
                    acFPayableAdjustmentDO.setCompensationTypeEname(compensationType.getEname());
                }
//                else {
//                    throw new NDSException("该赔付类型不存在,请添加赔付类型!!!");
//                }
                //赔付原因
                Integer reasonId = mainMap.getInteger("AC_F_COMPENSATION_REASON_ID");
                acFPayableAdjustmentDO.setAcFCompensationReasonId(reasonId);
                AcFCompensationReason reason = this.acFCompensationReasonMapper.selectById(reasonId);
                if (reason != null) {
                    acFPayableAdjustmentDO.setCompensationReason(reason.getAcFCompensationTypeEname());
                }
//                else {
//                    throw new NDSException("赔付原因不存在,请添加赔付原因!!!");
//                }
                //快递网点
                acFPayableAdjustmentDO.setExpressOutlets(mainMap.getString("EXPRESS_OUTLETS"));
                //快递单号
                acFPayableAdjustmentDO.setLogisticsNo(mainMap.getString("LOGISTICS_NO"));
                //原始出库日期
                if (Objects.isNull(mainMap.getString("SOURCE_OUTSOURCE_DATE"))) {
                    //为空则设置当前时间
                    acFPayableAdjustmentDO.setSourceOutsourceDate(new Date());
                } else {
                    acFPayableAdjustmentDO.setSourceOutsourceDate(mainMap.getDate("SOURCE_OUTSOURCE_DATE"));
                }
                //责任人
                acFPayableAdjustmentDO.setResponsiblePerson(mainMap.getString("RESPONSIBLE_PERSON"));
                //原因备注
                acFPayableAdjustmentDO.setReasonRemark(mainMap.getString("REASON_REMARK"));
                //责任方
                acFPayableAdjustmentDO.setResponsibleParty(mainMap.getInteger("RESPONSIBLE_PARTY"));

                //设置原单金额
                acFPayableAdjustmentDO.setOriginOrderAmt(mainMap.getBigDecimal("ORIGIN_ORDER_AMT"));

                //总应付金额
                BigDecimal totalPayablePrice = getTotalPayablePrice(itemMap, objId);

                acFPayableAdjustmentDO.setPayablePrice(totalPayablePrice);
                //来源单据类型默认 零售发货单
                acFPayableAdjustmentDO.setReserveVarchar01(AcConstant.PAYABLE_SOURCE_TYPE_1);
                BaseModelUtil.makeBaseCreateField(acFPayableAdjustmentDO, user);
                acFPayableAdjustmentDO.setOwnerename(user.getEname());
                acFPayableAdjustmentDO.setModifierename(user.getEname());
                if (acFPayableAdjustmentMapper.insert(acFPayableAdjustmentDO) < 0) {
                    throw new NDSException("保存失败！");
                }
                //新增操作日志-新增
                ValueHolderV14 v14 = insertLogFun(user, acFPayableAdjustmentDO.getId(), OperatorLogTypeEnum.OPERATOR_ADD.getVal(),
                        OperatorLogTypeEnum.OPERATOR_ADD.getText(), date);

                if (v14.getCode() == ResultCode.FAIL) {
                    throw new NDSException("保存失败！新增操作日志-新增失败！");
                }
            } else {
                //更新赔付类型
                Integer compensationTypeId = mainMap.getInteger("AC_F_COMPENSATION_TYPE_ID");
                //判断赔付类型是否有值
                if (compensationTypeId != null) {
                    acFPayableAdjustmentDO.setAcFCompensationTypeId(compensationTypeId);
                    AcFCompensationType compensationType = this.acFCompensationTypeMapper.selectOne(new QueryWrapper<AcFCompensationType>().lambda().eq(AcFCompensationType::getId, compensationTypeId));
                    if (compensationType != null) {
                        acFPayableAdjustmentDO.setCompensationTypeEname(compensationType.getEname());
                    } else {
                        throw new NDSException("该赔付类型不存在,请添加赔付类型!!!");
                    }
                }
                //赔付原因
                Integer reasonId = mainMap.getInteger("AC_F_COMPENSATION_REASON_ID");
                if (reasonId != null) {
                    acFPayableAdjustmentDO.setAcFCompensationReasonId(reasonId);
                    AcFCompensationReason reason = this.acFCompensationReasonMapper.selectById(reasonId);
                    if (reason != null) {
                        acFPayableAdjustmentDO.setCompensationReason(reason.getAcFCompensationTypeEname());
                    } else {
                        throw new NDSException("赔付原因不存在,请添加赔付原因!!!");
                    }
                }
                //快递网点
                if (Objects.nonNull(mainMap.getString("EXPRESS_OUTLETS"))) {
                    acFPayableAdjustmentDO.setExpressOutlets(mainMap.getString("EXPRESS_OUTLETS"));
                }
                //快递单号
                if (Objects.nonNull(mainMap.getString("LOGISTICS_NO"))) {
                    acFPayableAdjustmentDO.setLogisticsNo(mainMap.getString("LOGISTICS_NO"));
                }
                //责任人
                if (Objects.nonNull(mainMap.getString("RESPONSIBLE_PERSON"))) {
                    acFPayableAdjustmentDO.setResponsiblePerson(mainMap.getString("RESPONSIBLE_PERSON"));
                }
                //原因备注
                if (Objects.nonNull(mainMap.getString("REASON_REMARK"))) {
                    acFPayableAdjustmentDO.setReasonRemark(mainMap.getString("REASON_REMARK"));
                }
                //责任方
                if (Objects.nonNull(mainMap.getInteger("RESPONSIBLE_PARTY"))) {
                    acFPayableAdjustmentDO.setResponsibleParty(mainMap.getInteger("RESPONSIBLE_PARTY"));
                }
                //原始出库日期
                if (Objects.nonNull(mainMap.getString("SOURCE_OUTSOURCE_DATE"))) {
                    acFPayableAdjustmentDO.setSourceOutsourceDate(mainMap.getDate("SOURCE_OUTSOURCE_DATE"));
                } else {
                    throw new NDSException("原始出库日期不能为空!!!");
                }

                //总应付金额
                BigDecimal totalPayablePrice = getTotalPayablePrice(itemMap, objId);
                acFPayableAdjustmentDO.setPayablePrice(totalPayablePrice);

                acFPayableAdjustmentDO.setId(objId);
                BaseModelUtil.makeBaseModifyField(acFPayableAdjustmentDO, user);

                int updateFlag = acFPayableAdjustmentMapper.updateById(acFPayableAdjustmentDO);

                if (updateFlag < 0) {
                    throw new NDSException("保存失败！");
                }

                if (updateFlag > 0) {
                    //新增操作日志-编辑修改
                    ValueHolderV14 v14 = insertLogFun(user, objId, OperatorLogTypeEnum.OPERATOR_UPD.getVal(),
                            OperatorLogTypeEnum.OPERATOR_UPD.getText(), date);
                    if (v14.getCode() == ResultCode.FAIL) {
                        throw new NDSException("保存失败！新增操作日志-编辑修改失败！");
                    }
                }
            }
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("PayableAdjustmentSaveService.saveMain.entry:{} ", "saveMain"), mainMap);
        }

        /**
         * 子表新增
         */
        if (!itemMap.isEmpty()) {
            log.debug(LogUtil.format("PayableAdjustmentSaveService.saveMain.itemMap.size:{} ", "saveMain"), itemMap.size());
            if (objId > 0) {
                acFPayableAdjustmentItemMapper.deletePayableItem(objId);
            }

            for (int i = 0; i < itemMap.size(); i++) {
                JSONObject jsonObject = itemMap.getJSONObject(i);
                //商品ID
                Long psCProId = jsonObject.getLong("PS_C_PRO_ID") == null ? 0L : jsonObject.getLong("PS_C_PRO_ID");
                //原厂编码
                String factoryNo = "";
                //调用PRC 接口获取pro列表
                List<PsCPro> proList = psRpcService.queryProByIdList(Lists.newArrayList(psCProId));

                /*if (CollectionUtils.isNotEmpty(proList)) {
                    factoryNo = proList.get(0).getFactorycode();
                } else {
                    log.debug(LogUtil.format("saveMain.entry.itemMap.proList.size.isEmpty", "saveMain"));
                }*/

                //条码编码
                String psCSkuEode = jsonObject.getString("PS_C_SKU_ECODE");

                //商品条码
                Long psCSkuId = jsonObject.getLong("PS_C_SKU_ID");
                String skuId = psCSkuId == null ? "" : psCSkuId + "";
                ProductSku productSku = psRpcService.selectProductById(skuId);
                //数量
                BigDecimal qty = jsonObject.getBigDecimal("QTY");
                //标准价
                BigDecimal standardPrice = jsonObject.getBigDecimal("STANDARD_PRICE");
                //实际成交价
                BigDecimal truePrice = jsonObject.getBigDecimal("TRUE_PRICE");
                //应付金额与实际成交价不符则取实际成交价
                BigDecimal payablePrice = jsonObject.getBigDecimal("PAYABLE_PRICE");
                if (payablePrice.compareTo(truePrice) != 0 || payablePrice == null) {
                    payablePrice = jsonObject.getBigDecimal("TRUE_PRICE");
                }
                //发货逻辑仓
                Long logicalStoreId = jsonObject.getLong("LOGICAL_STORE_ID") == null ? 0L :
                        jsonObject.getLong("LOGICAL_STORE_ID");
                String logicalStoreCode = jsonObject.getString("LOGICAL_STORE_CODE");
                String logicalStoreName = jsonObject.getString("LOGICAL_STORE_NAME");

                //如果发货逻辑仓的ecode和ename存在一个为空则调用接口 进行冗余字段赋值
                if (!logicalStoreId.equals(0L) && (StringUtils.isEmpty(logicalStoreCode) || StringUtils.isEmpty(logicalStoreName))) {
                    List<CpCStore> cpCStoreList = cpRpcService.selectStoreById(logicalStoreId.intValue());
                    if (cpCStoreList != null && cpCStoreList.size() > 0) {
                        logicalStoreCode = cpCStoreList.get(0).getEcode();
                        logicalStoreName = cpCStoreList.get(0).getEname();
                    }
                }
                AcFPayableAdjustmentItemDO acFPayableAdjustmentItemDO = new AcFPayableAdjustmentItemDO();
                acFPayableAdjustmentItemDO.setId(ModelUtil.getSequence(AcConstant.TAB_AC_F_PAYABLE_ADJUSTMENT_ITEM));
                acFPayableAdjustmentItemDO.setAcFPayableAdjustmentId(acFPayableAdjustmentDO.getId());
                acFPayableAdjustmentItemDO.setPsCProId(productSku.getProdId());
                acFPayableAdjustmentItemDO.setPsCProEcode(productSku.getProdCode());
                acFPayableAdjustmentItemDO.setPsCProEname(productSku.getSkuName());
                acFPayableAdjustmentItemDO.setPsCSkuId(productSku.getId());
                acFPayableAdjustmentItemDO.setPsCClrId(productSku.getColorId());
                acFPayableAdjustmentItemDO.setPsCClrEcode(productSku.getColorCode());
                acFPayableAdjustmentItemDO.setPsCClrEname(productSku.getColorName());
                acFPayableAdjustmentItemDO.setPsCSizeId(productSku.getSizeId());
                acFPayableAdjustmentItemDO.setPsCSizeEcode(productSku.getSizeCode());
                acFPayableAdjustmentItemDO.setPsCSizeEname(productSku.getSizeName());
                acFPayableAdjustmentItemDO.setQty(qty);
                acFPayableAdjustmentItemDO.setStandardPrice(standardPrice);
                acFPayableAdjustmentItemDO.setTruePrice(truePrice);
                acFPayableAdjustmentItemDO.setPayablePrice(payablePrice);
                acFPayableAdjustmentItemDO.setPsCSkuEcode(productSku.getSkuEcode());
                acFPayableAdjustmentItemDO.setFactoryNo(factoryNo);
                acFPayableAdjustmentItemDO.setPsCBrandId(productSku.getPsCBrandId());
                acFPayableAdjustmentItemDO.setGbcode(productSku.getBarcode69());
                acFPayableAdjustmentItemDO.setLogicalStoreId(logicalStoreId);
                acFPayableAdjustmentItemDO.setLogicalStoreCode(logicalStoreCode);
                acFPayableAdjustmentItemDO.setLogicalStoreName(logicalStoreName);
                //订单明细id
                Long orderItemId = jsonObject.getLong("ORDER_ITEM_ID");
                acFPayableAdjustmentItemDO.setOrderItemId(orderItemId);
                //订单数量
                BigDecimal orderQty = jsonObject.getBigDecimal("ORDER_QTY") == null ? qty :
                        jsonObject.getBigDecimal("ORDER_QTY");
                acFPayableAdjustmentItemDO.setOrderQty(orderQty);
                //成交单价
                BigDecimal dealAmt = jsonObject.getBigDecimal("DEAL_AMT");
                acFPayableAdjustmentItemDO.setDealAmt(dealAmt);
                //应付单价=应付金额/数量
                BigDecimal payAmt = AmountCalcUtils.divideBigDecimal(payablePrice, orderQty, 4);
                acFPayableAdjustmentItemDO.setPayAmt(payAmt);
                BaseModelUtil.makeBaseCreateField(acFPayableAdjustmentItemDO, user);
                if (acFPayableAdjustmentItemMapper.insert(acFPayableAdjustmentItemDO) < 0) {
                    throw new NDSException("保存明细失败！");
                }
            }
        }
        //推送ES
        AcPayableAdjustmentPushESUtil.pushOrderAndItem(acFPayableAdjustmentDO.getId());

        valueHolder.setCode(ResultCode.SUCCESS);
        valueHolder.setMessage("保存成功！");
        Map<String, Object> data = new HashMap();
        data.put("objid", acFPayableAdjustmentDO.getId());
        data.put("tablename", AcConstant.TAB_AC_F_PAYABLE_ADJUSTMENT);
        valueHolder.setData(data);
        return valueHolder;
    }

    /**
     * 获取总应付金额
     *
     * @param itemMap 明细
     * @param objId   ID
     * @return
     */
    private BigDecimal getTotalPayablePrice(JSONArray itemMap, Long objId) {

        if (objId > 0) {
            acFPayableAdjustmentItemMapper.deletePayableItem(objId);
        }
        BigDecimal total = BigDecimal.ZERO;
        if (!itemMap.isEmpty()) {
            log.debug(LogUtil.format("saveMain.entry.itemMap.size:{}", "saveMain"), itemMap.size());
            for (int i = 0; i < itemMap.size(); i++) {
                JSONObject jsonObject = itemMap.getJSONObject(i);
                //应付金额
                BigDecimal payablePrice = jsonObject.getBigDecimal("PAYABLE_PRICE");
                payablePrice = payablePrice == null ? BigDecimal.ZERO : payablePrice;
//                BigDecimal truePrice = jsonObject.getBigDecimal("TRUE_PRICE");
//                truePrice = truePrice == null ? BigDecimal.ZERO : truePrice;
//                //若应付金额与实际成交价不一致则取实际成交价
//                if (payablePrice == null || payablePrice.compareTo(truePrice) != 0) {
//                    payablePrice = truePrice;
//                }
                total = total.add(payablePrice);
            }
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("getTotalPayablePrice:丢件单应付总金额:{}", "getTotalPayablePrice", "丢件单应付总金额"), total);
        }
        return total;
    }

    /**
     * 记录日志
     *
     * @param user
     * @param id
     * @param logType
     * @param logContent
     * @param date
     * @return
     */
    public ValueHolderV14 insertLogFun(User user, Long id, int logType, String logContent, Date date) {
        ValueHolderV14 v14 = new ValueHolderV14();

        //应付款调整单日志
        AcFPayableAdjustmentLogDO payableAdjustmentLogDO = new AcFPayableAdjustmentLogDO();

        //组织中心、所属公司、可用状态
        AcBeanUtils.makeCreateAd(payableAdjustmentLogDO, user);

        //id
        payableAdjustmentLogDO.setId(ModelUtil.getSequence(AcConstant.TAB_AC_F_PAYABLE_ADJUSTMENT_LOG));

        //操作日志关联单号
        payableAdjustmentLogDO.setAcFPayableAdjustmentId(id);

        //操作类型id
        payableAdjustmentLogDO.setLogType(logType);

        //操作类型名称
        payableAdjustmentLogDO.setLogContent(logContent);

        //创建人id
        payableAdjustmentLogDO.setOwnerid(Long.valueOf(user.getId()));
        //创建时间
        payableAdjustmentLogDO.setCreationdate(date);
        //创建人用户名
        payableAdjustmentLogDO.setOwnername(user.getName());
        //创建人姓名
        payableAdjustmentLogDO.setOwnerename(user.getEname());
        //修改人id
        payableAdjustmentLogDO.setModifierid(Long.valueOf(user.getId()));
        //修改时间
        payableAdjustmentLogDO.setModifieddate(date);
        //修改人用户名
        payableAdjustmentLogDO.setModifiername(user.getName());
        //修改人姓名
        payableAdjustmentLogDO.setModifierename(user.getEname());

        if (payableAdjustmentLogMapper.insert(payableAdjustmentLogDO) < 0) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("应付操作日志保存失败！");
            return v14;
        }
        v14.setCode(ResultCode.SUCCESS);
        return v14;
    }


}
