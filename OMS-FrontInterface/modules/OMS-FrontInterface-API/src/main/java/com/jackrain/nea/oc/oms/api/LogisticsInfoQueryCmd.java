package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.oc.oms.model.request.LogisticsInfoQueryRequest;
import com.jackrain.nea.oc.oms.model.result.LogisticsInfoQueryResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;

/**
 * <AUTHOR> wang<PERSON><PERSON>
 * @since : 2022/6/17
 * description : 物流轨迹信息查询-快递100
 */
public interface LogisticsInfoQueryCmd {
    /**
     * 物流轨迹信息查询-快递100
     *
     * @param request 单据ID
     * @return LogisticsInfoQueryResult
     */
    ValueHolderV14<LogisticsInfoQueryResult> queryLogisticsInfo(LogisticsInfoQueryRequest request);
}
