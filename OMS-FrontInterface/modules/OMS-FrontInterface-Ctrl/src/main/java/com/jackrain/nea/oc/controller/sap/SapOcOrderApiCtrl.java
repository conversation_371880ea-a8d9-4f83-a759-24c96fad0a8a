package com.jackrain.nea.oc.controller.sap;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.sap.OcBSapSalesDataRecordAddTaskSaveCmd;
import com.jackrain.nea.oc.oms.api.sap.SapOcOrderApiCmd;
import com.jackrain.nea.oc.oms.sap.OcBSapSalesDataRecordSumService;
import com.jackrain.nea.oc.oms.sap.OcBSapSalesDataRecordTaskService;
import com.jackrain.nea.oc.oms.sap.SapSalesDataGatherService;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.concurrent.ExecutionException;

@Api(value = "SapOcOrderApiCtrl", tags = "SAP销售业务下发接口")
@Slf4j
@RestController
public class SapOcOrderApiCtrl {

    @Autowired
    SapOcOrderApiCmd sapOcOrderApiCmd;


    @Resource
    private SapSalesDataGatherService salesDataGatherService;

    @Resource
    private OcBSapSalesDataRecordSumService ocBSapSalesDataRecordSumService;

    @Resource
    OcBSapSalesDataRecordTaskService ocBSapSalesDataRecordTaskService;
    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;
    @Autowired
    private OcBSapSalesDataRecordAddTaskSaveCmd sapSalesDataRecordAddTaskSaveCmd;


    @ApiOperation(value = "SAP签收下发接口")
    @PostMapping(path = "/api/cs/oc/oms/v1/signFor/ocOrder")
    public ValueHolderV14 signForOcOrder(@RequestBody JSONObject param) {
        log.info("SapOcOrderApiCtrl signForOcOrder param={}", param);
        ValueHolderV14 result = null;
        try {
            result = sapOcOrderApiCmd.ocOrderSignFor(param);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("服务器异常:{}", e);
            result = new ValueHolderV14(null, ResultCode.FAIL, "服务器繁忙，请稍后重试。");
        }
        return result;
    }


    @ApiOperation(value = "sapHandle")
    @PostMapping(path = "/api/cs/oc/oms/v1/sap/handle")
    public ValueHolderV14 sapHandle(@RequestBody JSONObject param) {
        log.info("SapOcOrderApiCtrl sapHandle param={}", param);
        ValueHolderV14 result = null;
        try {
            result = sapOcOrderApiCmd.sapOcOrder(param);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("服务器异常:", e);
            result = new ValueHolderV14(null, ResultCode.FAIL, "服务器繁忙，请稍后重试。");
        }
        return result;
    }

    @ApiOperation(value = "取消退回411T订单接口")
    @PostMapping(path = "/api/oc/oms/v1.0/saleOrderBack")
    public void saleOrderBack() throws ExecutionException, InterruptedException {
        ocBSapSalesDataRecordTaskService.executeThread();
    }

    @ApiOperation(value = "手工添加sap销售记录生成任务")
    @PostMapping(path = "/api/cs/oc/oms/v1/sap/addSaleDataTask")
    public JSONObject addSaleDataTask(HttpServletRequest request,
                                          @RequestBody JSONObject obj) {
        ValueHolderV14 vh;
        // 获取用户登录信息
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        if (obj == null) {
            throw new NDSException(Resources.getMessage("请求参数不能为空!", user.getLocale()));
        }
        try {
            vh = sapSalesDataRecordAddTaskSaveCmd.save(obj, user);
            return vh.toJSONObject();
        } catch (NDSException e) {
            vh = new ValueHolderV14();
            vh.setCode(com.jackrain.nea.constants.ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return vh.toJSONObject();

        }
    }
}
