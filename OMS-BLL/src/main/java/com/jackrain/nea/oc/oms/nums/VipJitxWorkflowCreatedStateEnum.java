package com.jackrain.nea.oc.oms.nums;


import lombok.Getter;

/**
 * <AUTHOR>
 * @create 2020-12-16 14:05
 * @desc JITX订单改仓工单创建状态
 **/
public enum VipJitxWorkflowCreatedStateEnum {

    UNCREATE(0, "未创建"),
    CREATE_SUCCESS(1, "创建成功"),
    CREATE_FAILED(2, "创建失败"),
    NO_NEED_CREATE(3, "无需创建");

    VipJitxWorkflowCreatedStateEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    @Getter
    private Integer code;

    @Getter
    private String name;

    public static String getNameByCode(Integer code) {
        for (VipJitxWorkflowCreatedStateEnum select : VipJitxWorkflowCreatedStateEnum.values()) {
            if (select.code.equals(code)) {
                return select.name;
            }
        }
        return "";
    }
}


