package com.jackrain.nea.oc.oms.util;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.st.model.StCAutoCheck;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.util.*;

/**
 * @Auther: 黄志优
 * @Date: 2020/12/2 23:20
 * @Description: Redis工具类
 */
@Slf4j
@Component
public class RedisHashCommonUtils {
    @Autowired
    private RedisOpsUtil<String, Map<String, String>> redisOpsUtil;

    /**
     * 添加or更新hash的值
     *
     * @param key
     * @param field
     * @param value
     */
    public void hset(String key, String field, String value) {
        redisOpsUtil.strRedisTemplate.opsForHash().put(key, field, value);
    }

    /**
     * 对象放到redis中
     *
     * @param key  key
     * @param hf   对象
     * @param <HF> 泛型
     */
    public <HF> void hset(String key, HF hf) {

        if (StringUtils.isBlank(key) || hf == null) {
            return;
        }

        Class<?> clazz = hf.getClass();
        Field[] declaredFields = clazz.getDeclaredFields();
        for (Field field : declaredFields) {
            JSONField annotation = field.getAnnotation(JSONField.class);
            if (annotation == null) {
                continue;
            }
            //打开私有访问
            field.setAccessible(true);
            String fieldName = field.getName();
            String value = null;
            try {
                Object valueObj = field.get(hf);
                if (valueObj == null) {
                    continue;
                }
                value = valueObj.toString();
            } catch (IllegalAccessException e) {
                continue;
            }

            String name = annotation.name();
            redisOpsUtil.strRedisTemplate.opsForHash().put(key, name, value);
        }
    }


    public <K, V> void hset(String suffixKey, Map<K, V> map) {
        if (MapUtils.isEmpty(map)) {
            return;
        }
        for (K key : map.keySet()) {
            hset(suffixKey + key.toString(), map.get(key));
        }
    }

    public void hdel(String key, String field) {
        redisOpsUtil.strRedisTemplate.opsForHash().delete(key, field);
    }

    public Map<String, String> hgetall(String key) {
        return redisOpsUtil.strRedisTemplate.execute((RedisCallback<Map<String, String>>) con -> {
            Map<byte[], byte[]> result = con.hGetAll(key.getBytes());
            if (CollectionUtils.isEmpty(result)) {
                return new HashMap<>(0);
            }

            Map<String, String> ans = new HashMap<>(result.size());
            for (Map.Entry<byte[], byte[]> entry : result.entrySet()) {
                ans.put(new String(entry.getKey()), new String(entry.getValue()));
            }
            return ans;
        });
    }

    public <T> T hgetall(String key, Class<T> clazz) {

        if (StringUtils.isBlank(key) || clazz == null) {
            return null;
        }

        Map<String, String> hgetall = this.hgetall(key);
        if (MapUtils.isEmpty(hgetall)) {
            return null;
        }

        T t = JSONObject.parseObject(JSONObject.toJSONString(hgetall), clazz);

        return t;
    }

    //public List<T> hgetallList(Collection<String> keys, Class<T> clazz) {
    //
    //    if (CollectionUtils.isEmpty(keys) || clazz == null) {
    //        return null;
    //    }
    //
    //    List<T> result = new ArrayList<>(keys.size());
    //
    //
    //    for (String key : keys) {
    //        Map<String, String> hgetall = this.hgetall(key);
    //        if (MapUtils.isEmpty(hgetall)) {
    //            continue;
    //        }
    //
    //        result.add(JSONObject.parseObject(JSONObject.toJSONString(hgetall), clazz));
    //
    //    }
    //
    //    return result;
    //}

    public Map<String, String> hmget(String key, List<String> fields) {
        List<String> result = redisOpsUtil.strRedisTemplate.<String, String>opsForHash().multiGet(key, fields);
        Map<String, String> ans = new HashMap<>(fields.size());
        int index = 0;
        for (String field : fields) {
            if (result.get(index) == null) {
                continue;
            }
            ans.put(field, result.get(index));
        }
        return ans;
    }

    /**
     * value为列表的场景
     *
     * @param key
     * @param field
     * @return
     */
    public <T> List<T> hGetList(String key, String field, Class<T> obj) {
        Object value = redisOpsUtil.strRedisTemplate.opsForHash().get(key, field);
        if (value != null) {
            return JSONObject.parseArray(value.toString(), obj);
        } else {
            return new ArrayList<>();
        }
    }

    public <T> void hSetList(String key, String field, List<T> values) {
        String v = JSONObject.toJSONString(values);
        redisOpsUtil.strRedisTemplate.opsForHash().put(key, field, v);
    }
}
