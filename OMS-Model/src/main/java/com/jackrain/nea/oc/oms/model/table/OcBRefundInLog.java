package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.util.Date;

@TableName(value = "oc_b_refund_in_log")
@Data
@Document(index = "oc_b_refund_in_log", type = "oc_b_refund_in_log")
public class OcBRefundInLog extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "USERNAME")
    @Field(type = FieldType.Keyword)
    private String username;

    @J<PERSON>NField(name = "LOGTYPE")
    @Field(type = FieldType.Keyword)
    private String logtype;

    @JSONField(name = "LOGMESSAGE")
    @Field(type = FieldType.Keyword)
    private String logmessage;

    @JSONField(name = "LOGPARAM")
    @Field(type = FieldType.Keyword)
    private String logparam;

    @JSONField(name = "ERRORMESSAGE")
    @Field(type = FieldType.Keyword)
    private String errormessage;

    @JSONField(name = "IPADDRESS")
    @Field(type = FieldType.Keyword)
    private String ipaddress;

    @JSONField(name = "OMSONLINEORDERID")
    @Field(type = FieldType.Long)
    private Long omsonlineorderid;

    @JSONField(name = "AD_ORG_ID")
    @Field(type = FieldType.Long)
    private Long adOrgId;

    @JSONField(name = "ISACTIVE")
    @Field(type = FieldType.Keyword)
    private String isactive;

    @JSONField(name = "AD_CLIENT_ID")
    @Field(type = FieldType.Long)
    private Long adClientId;

    @JSONField(name = "OWNERID")
    @Field(type = FieldType.Long)
    private Long ownerid;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "OWNERNAME")
    @Field(type = FieldType.Keyword)
    private String ownername;

    @JSONField(name = "CREATIONDATE")
    @Field(type = FieldType.Long)
    private Date creationdate;

    @JSONField(name = "MODIFIERID")
    @Field(type = FieldType.Long)
    private Long modifierid;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;

    @JSONField(name = "MODIFIERNAME")
    @Field(type = FieldType.Keyword)
    private String modifiername;

    @JSONField(name = "MODIFIEDDATE")
    @Field(type = FieldType.Long)
    private Date modifieddate;
}