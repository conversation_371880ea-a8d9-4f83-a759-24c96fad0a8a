package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@TableName(value = "ip_b_jingdong_order")
@Data
@Document(index = "ip_b_jingdong_order", type = "ip_b_jingdong_order")
public class IpBJdOrder extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "ORDER_ID")
    @Field(type = FieldType.Long)
    private Long orderId;

    @JSONField(name = "VENDER_ID")
    @Field(type = FieldType.Long)
    private Long venderId;

    @JSONField(name = "PAY_TYPE")
    @Field(type = FieldType.Keyword)
    private String payType;

    @JSONField(name = "ORDER_TOTAL_PRICE")
    @Field(type = FieldType.Double)
    private BigDecimal orderTotalPrice;

    @JSONField(name = "ORDER_PAYMENT")
    @Field(type = FieldType.Double)
    private BigDecimal orderPayment;

    @JSONField(name = "ORDER_SELLER_PRICE")
    @Field(type = FieldType.Double)
    private BigDecimal orderSellerPrice;

    @JSONField(name = "FREIGHT_PRICE")
    @Field(type = FieldType.Double)
    private BigDecimal freightPrice;

    @JSONField(name = "SELLER_DISCOUNT")
    @Field(type = FieldType.Double)
    private BigDecimal sellerDiscount;

    @JSONField(name = "ORDER_STATE")
    @Field(type = FieldType.Keyword)
    private String orderState;

    @JSONField(name = "ORDER_STATE_REMARK")
    @Field(type = FieldType.Keyword)
    private String orderStateRemark;

    @JSONField(name = "DELIVERY_TYPE")
    @Field(type = FieldType.Keyword)
    private String deliveryType;

    @JSONField(name = "INVOICE_INFO")
    @Field(type = FieldType.Keyword)
    private String invoiceInfo;

    @JSONField(name = "ORDER_REMARK")
    @Field(type = FieldType.Keyword)
    private String orderRemark;

    @JSONField(name = "ORDER_START_TIME")
    @Field(type = FieldType.Long)
    private Date orderStartTime;

    @JSONField(name = "ORDER_END_TIME")
    @Field(type = FieldType.Long)
    private Date orderEndTime;

    @JSONField(name = "ISTRANS")
    @Field(type = FieldType.Integer)
    private Integer istrans;

    @JSONField(name = "TRANSDATE")
    @Field(type = FieldType.Long)
    private Date transdate;

    @JSONField(name = "INSERTDATE")
    @Field(type = FieldType.Long)
    private Date insertdate;

    @JSONField(name = "CP_C_SHOP_ID")
    @Field(type = FieldType.Long)
    private Long cpCShopId;

    @JSONField(name = "SYSREMARK")
    @Field(type = FieldType.Keyword)
    private String sysremark;

    @JSONField(name = "BF_DELI_GOOD_GLAG")
    @Field(type = FieldType.Keyword)
    private String bfDeliGoodGlag;

    @JSONField(name = "CKY2_NAME")
    @Field(type = FieldType.Keyword)
    private String cky2Name;

    @JSONField(name = "SORTING_CODE")
    @Field(type = FieldType.Keyword)
    private String sortingCode;

    @JSONField(name = "PARTNER")
    @Field(type = FieldType.Keyword)
    private String partner;

    @JSONField(name = "SHOULD_PAY")
    @Field(type = FieldType.Keyword)
    private String shouldPay;

    @JSONField(name = "PAYMENT_TYPESTR")
    @Field(type = FieldType.Keyword)
    private String paymentTypestr;

    @JSONField(name = "VENDERREMARK")
    @Field(type = FieldType.Keyword)
    private String venderremark;

    @JSONField(name = "PIN")
    @Field(type = FieldType.Keyword)
    private String pin;

    @JSONField(name = "PAYMENT_CONFIRM_TIME")
    @Field(type = FieldType.Long)
    private Date paymentConfirmTime;

    @JSONField(name = "STORE_ORDER")
    @Field(type = FieldType.Keyword)
    private String storeOrder;

    @JSONField(name = "NICK")
    @Field(type = FieldType.Keyword)
    private String nick;

    @JSONField(name = "MODIFIED")
    @Field(type = FieldType.Long)
    private Date modified;

    @JSONField(name = "STOREID")
    @Field(type = FieldType.Long)
    private Long storeid;

    @JSONField(name = "PLATFORM_FLAG")
    @Field(type = FieldType.Long)
    private Long platformFlag;

    @JSONField(name = "BALANCEUSED")
    @Field(type = FieldType.Keyword)
    private String balanceused;

    @JSONField(name = "ORDERSIGN")
    @Field(type = FieldType.Keyword)
    private String ordersign;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;

    @JSONField(name = "TRANS_COUNT")
    @Field(type = FieldType.Long)
    private Long transCount;
}