package com.jackrain.nea.oc.oms.process.transfer.impl.refund.jingdong.step;

import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpJingdongRefundRelation;
import com.jackrain.nea.oc.oms.model.relation.OcBReturnOrderRelation;
import com.jackrain.nea.oc.oms.model.resources.SysNotesConstant;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.process.transfer.impl.refund.jingdong.util.JdRefundUtil;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Descroption 接口平台退单表插入订单中心退单表
 * <AUTHOR>
 * @Date 2019/4/28 18:53
 */
@Step(order = 40, description = "接口平台退单表插入订单中心退单表")
@Slf4j
@Component
public class Step040IpRefundToOcReturn extends BaseJingdongRefundProcessStep
        implements IOmsOrderProcessStep<IpJingdongRefundRelation> {
    @Override
    public ProcessStepResult<IpJingdongRefundRelation> startProcess(IpJingdongRefundRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        IpBJingdongRefund jingdongRefund = orderInfo.getJingdongRefund();
        OcBReturnOrder orderInfoOcReturnOrder = orderInfo.getOcReturnOrder();
        OcBOrder ocBOrder = orderInfo.getOcBOrder();
        Long afsserviceid = jingdongRefund.getAfsserviceid();

        try {
            if (JdRefundUtil.RefundStatus.CANCEL.getCode().equals(jingdongRefund.getAfsservicestatusname())
                    || JdRefundUtil.RefundStatus.EXAMINE_CLOSE.getCode().equals(jingdongRefund.getAfsservicestatusname())) {
                ipJingdongRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(), "状态为取消,转换完成", jingdongRefund);
                return new ProcessStepResult<>(StepStatus.FINISHED, "单据" + orderInfo.getOrderNo() + "转换完成");
            }
            List<Long> existReturnOrder = omsReturnOrderService.isExistReturnOrder(afsserviceid + "");
            Long returnId = null;
            if (CollectionUtils.isEmpty(existReturnOrder)) {
                //生成退单关系对象
                List<OcBReturnOrderRelation> returnOrderRelations = ipJingdongRefundService.buildJingdongReturnOrders(orderInfo);
                if (returnOrderRelations == null) {
                    return new ProcessStepResult<>(StepStatus.FINISHED,
                            "单据" + orderInfo.getOrderId() + "转换完成");
                }
                //执行退单流程
                returnId = omsReturnOrderService.saveOmsReturnOrderInfo(returnOrderRelations, operateUser);
            } else {
                returnId = existReturnOrder.get(0);
            }
            if (returnId == -1) {
                return new ProcessStepResult<>(StepStatus.FAILED, "保存oms退单失败!");
            } else {
                //todo  生成已发货退款单
                ipJingdongRefundService.foundRefundSlipAfterNoUpdate(returnId, orderInfo, jingdongRefund, operateUser);
                if (orderInfoOcReturnOrder == null) {
                    ipJingdongRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(), "生成退换货单成功!", jingdongRefund);
                    return new ProcessStepResult<>(StepStatus.FINISHED, "单据" + orderInfo.getOrderNo() + "转换完成");
                }
                //存在时更新物流信息及插入日志
                OcBReturnOrder ocReturnOrder = omsReturnOrderService.selectReturnOrderById(returnId);
                //1.更新退货单物流公司和物流单号
                boolean updateReturnFlag = ipJingdongRefundService.updateReturnOrderLogisticsInfo(ocReturnOrder);
                //2.插入退换货单的更新日志
                if (updateReturnFlag) {
                    if (operateUser == null) {
                        operateUser = SystemUserResource.getRootUser();
                    }
                    ipJingdongRefundService.insetOcReturnOrderLog(SysNotesConstant.UPDATE_EXCHANGEORDER_LOG_TYPE, SysNotesConstant.UPDATE_EXCHANGEORDER_LOG_MESSAGE_FOR_LOGISTICS, ocReturnOrder.getId(), operateUser);
                }
                //3.修改退单的状态为已转换，添加京东退单表的系统备注“退货单中已经存在，修改退单状态为已转换
                ipJingdongRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(), SysNotesConstant.SYS_REMARK6, jingdongRefund);
            }
            return new ProcessStepResult<>(StepStatus.FINISHED, "单据" + orderInfo.getOrderNo() + "转换完成");
        } catch (Exception e) {
            log.error(LogUtil.format("京东退单转换异常:{}", "京东退单转换异常"), Throwables.getStackTraceAsString(e));
            //修改中间表状态及系统备注
            ipJingdongRefundService.updateRefundIsTransError(jingdongRefund,e.getMessage());
            return new ProcessStepResult<>(StepStatus.FAILED, "退单转换异常：" + e.getMessage());
        }
    }
}
