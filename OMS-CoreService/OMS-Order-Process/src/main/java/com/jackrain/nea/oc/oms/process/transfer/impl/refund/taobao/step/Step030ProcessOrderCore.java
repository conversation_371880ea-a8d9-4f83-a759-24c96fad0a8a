package com.jackrain.nea.oc.oms.process.transfer.impl.refund.taobao.step;

import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransNodeTipEnum;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsTaobaoRefundRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.process.transfer.impl.refund.taobao.util.OrderStatusUtil;
import com.jackrain.nea.oc.oms.refund.util.TransRefundNodeTipUtil;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2020/2/27 5:07 下午
 * @Version 1.0
 */
@Step(order = 30, description = "处理订单")
@Slf4j
@Component
public class Step030ProcessOrderCore extends BaseTaobaoRefundProcessStep
        implements IOmsOrderProcessStep<OmsTaobaoRefundRelation> {
    @Override
    public ProcessStepResult<OmsTaobaoRefundRelation> startProcess(OmsTaobaoRefundRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        try {
            List<OmsOrderRelation> omsOrderRelation = orderInfo.getOmsOrderRelation();
            IpBTaobaoRefund ipBTaobaoRefund = orderInfo.getIpBTaobaoRefund();
            for (OmsOrderRelation orderRelation : omsOrderRelation) {
                OcBOrder ocBOrder = orderRelation.getOcBOrder();
                Integer orderStatus = ocBOrder.getOrderStatus();
                //判断订单是否为发货前
                boolean statusFront = OrderStatusUtil.checkOrderStatusFront(orderStatus);
                if (statusFront) {
                    //发货前
                    orderRelation.setOrderMark(TaobaoReturnOrderExt.ReturnBillsStatus.GOODS_FRONT.getCode());
                } else {
                    List<OcBOrderItem> ocBOrderItems = orderRelation.getOcBOrderItems();
                    // 发货后使用
                    List<OcBOrderItem> itemList = ocBOrderItems.stream().filter(p -> p.getProType() != SkuType.NO_SPLIT_COMBINE).collect(Collectors.toList());
                    // 0,仅退款   1:  退货退款
                    Integer hasGoodReturn = ipBTaobaoRefund.getHasGoodReturn();
                    // 发货后 如果生成了退款单 查看退款单的货物状态 退款类型是否发生变化 如果发生变化 则将退货单及退款单取消
                    boolean isCancel = omsRefundOrderService.judgeRefundFormInfoIsAgreement(ipBTaobaoRefund, operateUser);
                    if (isCancel) {
                        //重新查询订单明细信息
                        List<Long> longList = itemList.stream().map(OcBOrderItem::getId).collect(Collectors.toList());
                        itemList = ocBOrderItemMapper.selectOrderItemListsByIds(ocBOrder.getId(), longList);
                    }
                    orderRelation.setOcBOrderItems(itemList);
                    //拦截
                    // @20200731 如果不存在发货信息，则走发货后   物流拦截  线下拦截
                    if (TaobaoReturnOrderExt.HasGoodReturnStatus.NO_RETURN.getCode().equals(hasGoodReturn) && orderInfo.isExistsDelivers()) {
                        orderRelation.setOrderMark(TaobaoReturnOrderExt.ReturnBillsStatus.GOODS_INTERCEPT.getCode());
                    } else {
                        //发货后
                        orderRelation.setOrderMark(TaobaoReturnOrderExt.ReturnBillsStatus.GOODS_AFTER.getCode());
                    }
                }
            }
            return new ProcessStepResult<>(StepStatus.SUCCESS, "订单处理成功,进入下一阶段");
        } catch (Exception e) {
            log.error(LogUtil.format("订单处理异常:{}", "订单处理异常"), Throwables.getStackTraceAsString(e));
            return new ProcessStepResult<>(StepStatus.FAILED, "订单处理失败");
        }
    }
}