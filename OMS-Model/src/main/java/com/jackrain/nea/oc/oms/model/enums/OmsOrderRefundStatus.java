package com.jackrain.nea.oc.oms.model.enums;

/**
 * @author: heliu
 * @since: 2019-03-26
 * create at : 2019-03-26 21:24
 */
public enum OmsOrderRefundStatus {

    /**
     * 未退款
     */
    UNREFUND,

    /**
     * 买家已经申请退款，等待卖家同意
     */
    WAITSELLERAGREE,

    /**
     * 卖家已经同意退款，等待买家退货
     */
    WAITBUYERRETURNGOODS,
    /**
     * 买家已经退货，等待卖家确认收货
     */
    WAITSELLERCONFIRMGOODS,
    /**
     * 卖家拒绝退款
     */
    SELLERREFUSEBUYER,
    /**
     * 退款关闭
     */
    CLOSED,
    /**
     * 退款成功
     */
    SUCCESS;

    /**
     * 退款状态
     *
     * @return
     */
    public int toInteger() {
        if (this == UNREFUND) {
            return 0;
        } else if (this == WAITSELLERAGREE) {
            return 1;
        } else if (this == WAITBUYERRETURNGOODS) {
            return 2;
        } else if (this == WAITSELLERCONFIRMGOODS) {
            return 3;
        } else if (this == SELLERREFUSEBUYER) {
            return 4;
        } else if (this == CLOSED) {
            return 5;
        } else if (this == SUCCESS) {
            return 6;
        } else {
            return 100;
        }
    }

    /**
     * 退款状态
     *
     * @return
     */
    public static String toStatusString(int status) {
        if (0 == status) {
            return "未退款";
        } else if (1 == status) {
            return "买家已经申请退款，等待卖家同意";
        } else if (2 == status) {
            return "卖家已经同意退款，等待买家退货";
        } else if (3 == status) {
            return "买家已经退货，等待卖家确认收货";
        } else if (4 == status) {
            return "卖家拒绝退款";
        } else if (5 == status) {
            return "退款关闭";
        } else if (6 == status) {
            return "退款成功";
        } else {
            return "";
        }
    }
}
