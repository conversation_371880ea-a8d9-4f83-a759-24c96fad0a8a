package com.jackrain.nea.oc.oms.model.table;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.ps.model.ProductSku;
import lombok.Data;

/**
 * ming.fz 通用转单明细+sku
 */
@TableName(value = "ip_b_standplat_order_item")
@Data
public class IpBStandplatOrderItemEx extends IpBStandplatOrderItem {

    @TableField(exist = false)
    private ProductSku prodSku;


}