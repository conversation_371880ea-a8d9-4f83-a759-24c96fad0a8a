package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.oc.request.StandplatOrderCreateModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;

/**
 * <AUTHOR> zhuxing
 * @Date : 2021-12-16 16:58
 * @Description : 通用订单服务
 **/
public interface IpStandplatOrderCmd {

    /**
     * 保存通用订单
     * @param standplatOrderCreateModel
     * @return
     */
    ValueHolderV14 saveIpStandplatOrder(StandplatOrderCreateModel standplatOrderCreateModel);
}
