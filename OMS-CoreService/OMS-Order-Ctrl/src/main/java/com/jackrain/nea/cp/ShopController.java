package com.jackrain.nea.cp;

import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.rpc.CpRpcService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: 易邵峰
 * @since: 2019-07-31
 * create at : 2019-07-31 19:57
 */
@RestController
public class ShopController {

    @Autowired
    private CpRpcService cpRpcService;

    @RequestMapping("/selectShop")
    public CpShop selectShop(@RequestParam("id") long id) {
        return this.cpRpcService.selectShopById(id);
    }
}
