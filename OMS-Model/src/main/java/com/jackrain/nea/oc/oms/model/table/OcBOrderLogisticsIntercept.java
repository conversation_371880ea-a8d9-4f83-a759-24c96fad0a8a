package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@TableName(value = "oc_b_order_logistics_intercept")
@Data
public class OcBOrderLogisticsIntercept extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "BILL_NO")
    private String billNo;

    @JSONField(name = "INTERCEPT_TYPE")
    private Integer interceptType;

    @JSONField(name = "LOGISTICS_SERVICE_TYPE")
    private Integer logisticsServiceType;

    @JSONField(name = "INTERCEPT_REASON")
    private String interceptReason;

    @JSONField(name = "PLATFORM_CODE")
    private String platformCode;

    @JSONField(name = "SOURCE_PLATFORM_CODE")
    private String sourcePlatformCode;

    @JSONField(name = "ORDER_ID")
    private Long orderId;

    @JSONField(name = "ORDER_BILL_NO")
    private String orderBillNo;

    @JSONField(name = "ORDER_AMT")
    private BigDecimal orderAmt;

    @JSONField(name = "CP_C_SHOP_ID")
    private Long cpCShopId;

    @JSONField(name = "CP_C_SHOP_ECODE")
    private String cpCShopEcode;

    @JSONField(name = "CP_C_SHOP_TITLE")
    private String cpCShopTitle;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID")
    private Long cpCPhyWarehouseId;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ECODE")
    private String cpCPhyWarehouseEcode;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ENAME")
    private String cpCPhyWarehouseEname;

    @JSONField(name = "CP_C_LOGISTICS_ID")
    private Long cpCLogisticsId;

    @JSONField(name = "CP_C_LOGISTICS_ECODE")
    private String cpCLogisticsEcode;

    @JSONField(name = "CP_C_LOGISTICS_ENAME")
    private String cpCLogisticsEname;

    @JSONField(name = "EXPRESSCODE")
    private String expresscode;

    @JSONField(name = "SCAN_TIME")
    private Date scanTime;

    @JSONField(name = "INTERCEPT_BILL_NO")
    private String interceptBillNo;

    @JSONField(name = "INTERCEPT_STATUS")
    private Integer interceptStatus;

    @JSONField(name = "INTERCEPT_FAILURE_NUM")
    private Integer interceptFailureNum;

    @JSONField(name = "INTERCEPT_FAILURE_REASON")
    private String interceptFailureReason;

    @JSONField(name = "CANCEL_FAILURE_REASON")
    private String cancelFailureReason;

    @JSONField(name = "INTERCEPT_FAILURE_COUNT")
    private Integer interceptFailureCount;

    @JSONField(name = "REMARK")
    private String remark;

    @JSONField(name = "VERSION")
    private Long version;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    /**
     * 0 未同步
     * 1 已同步
     */
    @JSONField(name = "SYNC_SELLER_MEMO")
    private Integer syncSellerMemo;

    /**
     * 平台退款单号
     */
    @JSONField(name = "T_RETURN_ID")
    private String tReturnId;

    /**
     * 是否派件前拦截
     */
    @JSONField(name = "BEFORE_DELIVERY")
    private Integer beforeDelivery;

    /**
     * 是否有调用AG
     */
    @JSONField(name = "IS_AG")
    private Integer isAg;

    /**
     * 调用AG时间
     */
    @JSONField(name = "AG_TIME")
    private Date agTime;

    /**
     * 是否可以调用AG退款
     */
    @JSONField(name = "CAN_AG")
    private Integer canAg;
}