package com.jackrain.nea.oc.services.st;

import com.jackrain.nea.oc.oms.api.st.StCStickerSaveCmd;
import com.jackrain.nea.st.service.StCStickerService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;

/**
 * @Author: 黄世新
 * @Date: 2023/2/22 17:20
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class StCStickerSaveCmdImpl extends CommandAdapter implements StCStickerSaveCmd {

    @Override
    public ValueHolder execute(QuerySession session) {
        return ApplicationContextHandle.getBean(StCStickerService.class).saveSticker(session);
    }


}
