package com.jackrain.nea.oc.oms.mapper;

import com.alibaba.fastjson.JSONObject;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.jdbc.SQL;
import org.springframework.stereotype.Component;

/**
 * @Author: wang<PERSON><PERSON>
 * @Date: 2019-03-04 18:02
 * @Version 1.0
 */
@Mapper
@Component
public interface BillCopyMapper {
    /**
     * 根据订单Id 获取订单信息
     * 单据复制、丢单复
     *
     * @param id 订单Id
     * @return
     */
    //获取基本信息
    @Select("SELECT id AS ID,bill_no AS BILL_NO,source_code AS SOURCE_CODE,cp_c_shop_title AS CP_C_SHOP_TITLE,cp_c_shop_id AS CP_C_SHOP_ID," +
            "order_source AS ORDER_SOURCE , platform AS PLATFORM ,"
            +
            "ship_amt AS SHIP_AMT,user_nick AS USER_NICK,"
            +
            "tid AS TID,pay_type AS PAY_TYPE,"
            +
            "consign_ship_amt AS CONSIGN_SHIP_AMT ,"
            +
            "service_amt AS SERVICE_AMT,"
            +
            "cp_c_phy_warehouse_id AS CP_C_PHY_WAREHOUSE_ID,"
            +
            "cp_c_phy_warehouse_ecode AS CP_C_PHY_WAREHOUSE_ECODE,"
            +
            "cp_c_phy_warehouse_ename AS CP_C_PHY_WAREHOUSE_ENAME,"
            +
            "is_invoice AS IS_INVOICE,"
            +
            "sysremark AS SYSREMARK, "
            +
            "BUYER_MESSAGE AS BUYER_MESSAGE, "
            +
            "COPY_NUM AS COPY_NUM, "
            +
            "COPY_REASON AS COPY_REASON, "
            +
            "PAY_TIME AS PAY_TIME, "
            +
            "RECEIVER_ZIP AS RECEIVER_ZIP, "
            +
            "SELLER_MEMO AS SELLER_MEMO, "
            +
            "SUFFIX_INFO AS SUFFIX_INFO, "
            +
            "cod_amt AS COD_AMT,"
            +
            "merge_source_code AS MERGE_SOURCE_CODE,"
            +
            "inside_remark AS INSIDE_REMARK,"
            +
            "is_merge AS IS_MERGE,"
            +
            "business_type AS BUSINESS_TYPE,"
            +
            "business_type_id AS BUSINESS_TYPE_ID,"
            +
            "business_type_code AS BUSINESS_TYPE_CODE,"
            +
            "business_type_name AS BUSINESS_TYPE_NAME,"
            +
            "receiver_name AS RECEIVER_NAME,"
            +
            "receiver_mobile AS RECEIVER_MOBILE,"
            +
            "receiver_phone AS RECEIVER_PHONE,"
            +
            "cp_c_region_province_id AS CP_C_REGION_PROVINCE_ID,"
            +
            "cp_c_region_province_ecode AS CP_C_REGION_PROVINCE_ECODE,"
            +
            "cp_c_region_province_ename AS CP_C_REGION_PROVINCE_ENAME,"
            +
            "cp_c_region_city_id AS CP_C_REGION_CITY_ID,"
            +
            "cp_c_region_city_ecode AS CP_C_REGION_CITY_ECODE,"
            +
            "cp_c_region_city_ename AS CP_C_REGION_CITY_ENAME,"
            +
            "cp_c_region_area_id AS CP_C_REGION_AREA_ID,"
            +
            "cp_c_region_area_ecode AS CP_C_REGION_AREA_ECODE,"
            +
            "cp_c_region_area_ename AS CP_C_REGION_AREA_ENAME,"
            +
            "receiver_address AS RECEIVER_ADDRESS,"
            +
            "is_plain_addr AS IS_PLAIN_ADDR,"
            +
            "business_type_code AS BUSINESS_TYPE_CODE,"
            +
            "oaid as OAID,"
            +
            "sale_product_attr as SALE_PRODUCT_ATTR"
            +
            " FROM OC_B_ORDER WHERE id=#{id}")
    JSONObject queryOrderInfoById(@Param("id") Integer id);

    /**
     * 获取收货人基本信息
     *
     * @param id 订单ID
     * @return
     */
    @Select("SELECT receiver_name AS RECEIVER_NAME,cp_c_logistics_id AS CP_C_LOGISTICS_ID,"
            +
            "receiver_mobile AS RECEIVER_MOBILE,"
            +
            "receiver_phone AS RECEIVER_PHONE,"
            +
            "receiver_zip AS RECEIVER_ZIP, cp_c_shop_id AS CP_C_SHOP_ID,"
            +
            "cp_c_region_province_ename AS CP_C_REGION_PROVINCE_ENAME,"
            +
            "cp_c_region_city_ename AS CP_C_REGION_CITY_ENAME,cp_c_region_province_id AS CP_C_REGION_PROVINCE_ID,"
            +
            "cp_c_region_area_ename AS CP_C_REGION_AREA_ENAME,cp_c_region_city_id AS CP_C_REGION_CITY_ID,"
            +
            "logistics_cost AS LOGISTICS_COST,cp_c_region_area_id AS CP_C_REGION_AREA_ID,"
            +
            "cp_c_region_province_ecode AS CP_C_REGION_PROVINCE_ECODE, cp_c_region_town_ename AS CP_C_REGION_TOWN_ENAME,"
            +
            "cp_c_region_city_ecode AS CP_C_REGION_CITY_ECODE,"
            +
            "cp_c_region_area_ecode AS CP_C_REGION_AREA_ECODE,"
            +
            "is_plain_addr AS IS_PLAIN_ADDR,"
            +
            "receiver_address AS RECEIVER_ADDRESS"
            +
            "  FROM OC_B_ORDER WHERE id=#{id}")
    JSONObject queryReceiverInfoById(@Param("id") Integer id);

    /**
     * 获取备注信息
     *
     * @param id 订单ID
     * @return
     */
    @Select("SELECT buyer_message AS BUYER_MESSAGE,seller_memo AS SELLER_MEMO FROM OC_B_ORDER WHERE id=#{id}")
    JSONObject queryMessageById(@Param("id") Integer id);

    /**
     * 检查 数据是否存在
     *
     * @param id 订单ID
     * @return
     */
    @Select("SELECT COUNT(*) FROM oc_b_order WHERE id =#{id}")
    Integer getCountNum(@Param("id") Integer id);

    //添加订单信息
    @InsertProvider(type = OrderInfo.class, method = "addOrder")
    Integer addOrder(JSONObject jsonObject);

    class OrderInfo {
        public String addOrder(JSONObject jsonObject) {
            return new SQL() {
                {
                    INSERT_INTO("OC_B_ORDER");
                    for (String key : jsonObject.keySet()) {
                        VALUES(key, "#{" + key + "}");
                    }

                }
            }.toString();
        }
    }
}
