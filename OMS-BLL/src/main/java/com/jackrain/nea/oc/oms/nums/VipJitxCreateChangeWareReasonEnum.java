package com.jackrain.nea.oc.oms.nums;


import lombok.Getter;

/**
 * <AUTHOR>
 * @create 2020-12-16 14:05
 * @desc JITX订单创建改仓原因
 **/
public enum VipJitxCreateChangeWareReasonEnum {

    OUT_STOCK("1001", "原仓缺货改仓"),
    SYSTEM_MISJUDGMENT("1002", "系统错判改仓"),
    ADD_WAREHOUSE("1003", "新增仓库改仓");

    VipJitxCreateChangeWareReasonEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    @Getter
    private String code;

    @Getter
    private String name;

    public static String getNameByCode(String code) {
        for (VipJitxCreateChangeWareReasonEnum select : VipJitxCreateChangeWareReasonEnum.values()) {
            if (select.code.equals(code)) {
                return select.name;
            }
        }
        return "";
    }
}


