package com.jackrain.nea.oc.oms.process.transfer.impl.normal.taobao.step;

import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 卖家备注信息是否一致，不一致更新卖家备注信息  只更新备注，不修改状态
 * @Date 2019-11-21
 **/
@Step(order = 75, description = "卖家备注信息是否一致，不一致更新卖家备注信息")
@Component
@Slf4j
public class Step075UpdateSellerRemarkExt extends BaseTaobaoOrderProcessStep implements IOmsOrderProcessStep<IpTaobaoOrderRelation> {

    @Autowired
    private OmsOrderService orderService;

    @Override
    public ProcessStepResult<IpTaobaoOrderRelation> startProcess(IpTaobaoOrderRelation orderInfo,
                                                                 ProcessStepResult preStepResult,
                                                                 boolean isAutoMakeup, User operateUser) {
        ProcessStepResult<IpTaobaoOrderRelation> stepResult = new ProcessStepResult<>();

        if (preStepResult.getNextStepOperateObj() != null) {
            try {
                List<OcBOrder> afterTransferOrderList = (List<OcBOrder>) preStepResult.getNextStepOperateObj();
                for (OcBOrder afterTransferOrder : afterTransferOrderList) {
                    if (afterTransferOrder == null) {
                        continue;
                    }
                    String beforeMemo = "";
                    if (orderInfo != null && orderInfo.getTaobaoOrder() != null) {
                        beforeMemo = orderInfo.getTaobaoOrder().getSellerMemo();
                    }
                    String transferMemo = afterTransferOrder.getSellerMemo();
                    if (!StringUtils.equalsIgnoreCase(beforeMemo, transferMemo)) {
                        OcBOrder ocBOrder = new OcBOrder();
                        ocBOrder.setId(afterTransferOrder.getId());
                        if(StringUtils.isNotEmpty(beforeMemo) &&beforeMemo.length()>1000){
                            //卖家备注
                            ocBOrder.setSellerMemo(beforeMemo.substring(beforeMemo.length()-1000,beforeMemo.length()-1));
                        }else {
                            //卖家备注
                            ocBOrder.setSellerMemo(beforeMemo);
                        }
                        this.orderService.updateOrderInfo(ocBOrder);
                    }
                }
            } catch (Exception ex) {
                log.error(LogUtil.format("Step075UpdateSellerRemarkExt.StartProcess.Error={}", "Step075UpdateSellerRemarkExt"), Throwables.getStackTraceAsString(ex));

            }
            stepResult.setMessage("更新卖家备注信息成功");
        } else {
            stepResult.setMessage("未进行更新卖家备注信息");
        }
        //预售订单
        if (StringUtils.isNotBlank(orderInfo.getTaobaoOrder().getStepTradeStatus())) {
            stepResult.setMessage("预售订单更新卖家备注完成,进入下一阶段");
            stepResult.setStatus(StepStatus.SUCCESS);
            stepResult.setNextStepClass(Step008UpdateOrderTransferStatus.class);
        } else {
            stepResult.setStatus(StepStatus.FINISHED);
        }
        return stepResult;
    }
}
