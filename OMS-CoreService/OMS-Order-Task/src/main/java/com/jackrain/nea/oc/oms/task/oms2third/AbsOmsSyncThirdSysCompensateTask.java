//package com.jackrain.nea.oc.oms.task.oms2third;
//
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//import com.jackrain.nea.config.PropertiesConf;
//import com.google.common.base.Throwables;
//import com.jackrain.nea.config.PropertiesConf;
//import com.jackrain.nea.oc.oms.es.ES4ReturnOrder;
//import com.jackrain.nea.oc.oms.model.relation.TaskParam;
//import com.jackrain.nea.oc.oms.sap.Oms2SapMapper;
//import com.jackrain.nea.oc.oms.task.BaseR3Task;
//import com.jackrain.nea.task.IR3Task;
//import com.jackrain.nea.task.RunTaskResult;
//import com.jackrain.nea.util.ApplicationContextHandle;
//import com.jackrain.nea.utility.ExceptionUtil;
//import com.jackrain.nea.util.ApplicationContextHandle;
//import com.jackrain.nea.utility.LogUtil;
//import com.xxl.job.core.handler.annotation.XxlJob;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//
//import java.util.List;
//
///**
// * oms 传第三方 补偿任务
// * 退单-> 互道
// *
// * @Desc :
// * <AUTHOR> xiWen
// * @Date : 2020/4/26
// */
//@Slf4j
//public abstract class AbsOmsSyncThirdSysCompensateTask extends BaseR3Task implements IR3Task {
//
//    @Autowired
//    private Oms2SapMapper oms2SapMapper;
//
//    protected String compensationNameSpaceKey = "lts";
//
//    private final String compensationStatusKey = "omsSyncThirdSysCompensateTask.common.query.status";
//
//    private final String compensationEachSizeKey = "omsSyncThirdSysCompensateTask.common.query.eachSize";
//
//    /**
//     * update value
//     *
//     * @return
//     */
//    protected abstract String getTaskTableName();
//
//    protected abstract String getTaskStatusCol();
//
//    protected abstract String getTaskTypeCol();
//
//    /**
//     * query, update value
//     *
//     * @return
//     */
//    protected abstract String getOrigTableName();
//
//    protected abstract String getOrigStatusCol();
//
//    protected abstract String getOrigSendTimesCol();
//
//    protected abstract String getOrigEsIndex();
//
//    protected abstract String getOrigEsType();
//
//
//    @Override
//    @XxlJob("AbsOmsSyncThirdSysCompensateTask")
//    public RunTaskResult execute(JSONObject params) {
//        log.debug(LogUtil.format("execute Start..."));
//
//        TaskParam param = initTaskParam();
//        log.debug(LogUtil.format("execute.TaskParam-{}"), param.toString());
//        RunTaskResult result = new RunTaskResult();
//        long l = System.currentTimeMillis();
//
//        boolean updateResult = updateOmsOrderStatus(param);
//        log.debug(LogUtil.format("execute.Return_Order.Result: {}"), updateResult);
//        long l0 = System.currentTimeMillis();
//        result.setSuccess(updateResult);
//        result.setMessage("execute.Finished; UseTime: " + (l0 - l) + " ms");
//        return result;
//    }
//
//
//    /**
//     * 更新oms单据, task 表
//     *
//     * @param param
//     * @return
//     */
//    private boolean updateOmsOrderStatus(TaskParam param) {
//        //     int size, String indexName, String typeName, String tbName, String fieldName, String taskOrderName
//
//        JSONObject whereKey = new JSONObject();
//        whereKey.put(param.getOrigStatusCol(), param.getOrigCompensateFailedVal());
//        JSONObject filterKey = new JSONObject();
//        filterKey.put(param.getOrigSendTimesCol(), "0~5");
//
//        String ids = null;
//        try {
//            ids = searchKeyFromEs(param.getOrigEsIndex(), param.getOrigEsType(), param.getLimit(), whereKey, filterKey);
//            if (StringUtils.isBlank(ids)) {
//                return true;
//            }
//
//            param.setKeyStrings(ids);
//            List<Long> listIds = oms2SapMapper.selectCompensateOrigOrder(param);
//            ids = join2String(listIds);
//            param.setKeyStrings(ids);
//
//            if (StringUtils.isNotBlank(ids)) {
//
//                int updateResult = oms2SapMapper.updateOrigOrderByCompensate(param);
//                int taskResult = oms2SapMapper.updateTaskOrderByCompensate(param);
//            }
//        } catch (Exception ex) {
//            log.error(LogUtil.format("AbsOmsSyncThirdSysCompensateTask.execute Update Param-{}"), Throwables.getStackTraceAsString(ex));
//
//            return false;
//        }
//        return true;
//    }
//
//    protected TaskParam initTaskParam() {
//
//        return new TaskParam()
//                .setTaskStatusCol(getTaskStatusCol())
//                .setTaskStatusVal(getTaskStatusVal())
//                .setTaskTypeCol(getTaskTypeCol())
//                .setTaskTypeVal(getTaskTypeVal())
//                .setTaskTableName(getTaskTableName())
//
//                .setLimit(getEachSize())
//                .setOrigEsIndex(getOrigEsIndex())
//                .setOrigEsType(getOrigEsType())
//                .setOrigStatusCol(getOrigStatusCol())
//                .setOrigSendTimesCol(getOrigSendTimesCol())
//                .setOrigCompensateFailedVal(getCompensateFailedVal())
//                .setOrigTableName(getOrigTableName());
//    }
//
//
//    /**
//     * default method
//     */
//    protected int getCompensateFailedVal() {
//        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
//        return config.getProperty(compensationStatusKey, 3);
//    }
//
//    protected int getTaskStatusVal() {
//
//        return 0;
//    }
//
//    /**
//     * is send?
//     * send type
//     *
//     * @return
//     */
//    protected int getTaskTypeVal() {
//        return 1;
//    }
//
//    protected int getEachSize() {
//        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
//        return config.getProperty(compensationEachSizeKey, 200);
//    }
//
//
//    protected String join2String(List<Long> listIds) {
//        StringBuilder sb = new StringBuilder();
//        int i = 0, l = listIds.size();
//        for (; i < l; i++) {
//            if (i > 0) {
//                sb.append(",");
//            }
//            sb.append(listIds.get(i));
//        }
//        return sb.toString();
//    }
//
//
//    /**
//     * es 查询
//     *
//     * @param indexName
//     * @param typeName
//     * @param eachSize
//     * @param whereKeyJo
//     * @param filterKey
//     * @return 分库键字符串
//     */
//    private String searchKeyFromEs(String indexName, String typeName, int eachSize, JSONObject whereKeyJo, JSONObject filterKey) {
//
//        final String shardKey = "ID";
//        JSONObject esJsn = ES4ReturnOrder.findIdByDynamic(indexName, typeName, eachSize, whereKeyJo, filterKey);
//
//
//        if (esJsn == null) {
//            return null;
//        }
//
//        StringBuilder sb = new StringBuilder();
//        Long totalCount = esJsn.getLong("total");
//        if (totalCount == null || totalCount.intValue() < 1) {
//            return null;
//        }
//
//        JSONArray ary = esJsn.getJSONArray("data");
//        if (ary == null) {
//            return null;
//        }
//        int i = 0, l = ary.size();
//        for (; i < l; i++) {
//            JSONObject o = ary.getJSONObject(i);
//            if (o == null) {
//                continue;
//            }
//            String id = o.getString(shardKey);
//            if (id == null) {
//                continue;
//            }
//            sb.append(id);
//            i++;
//            break;
//        }
//        for (; i < l; i++) {
//            JSONObject o = ary.getJSONObject(i);
//            if (o == null) {
//                continue;
//            }
//            String id = o.getString(shardKey);
//            if (id == null) {
//                continue;
//            }
//            sb.append(",").append(id);
//        }
//        return sb.toString();
//    }
//
//    /**
//     * level debug recorder
//     *
//     * @param msg log message
//     */
//    protected void logDebug(String msg, Object... params) {
//
//        if (log.isDebugEnabled()) {
//            if (params.length == 0) {
//                log.debug(new StringBuilder(this.getClass().getSimpleName()).append(".").append(msg).toString());
//            } else {
//                log.debug(new StringBuilder(this.getClass().getSimpleName()).append(".").append(msg).toString(), params);
//            }
//        }
//    }
//
//    /**
//     * level error recorder
//     *
//     * @param msg    message or format string
//     * @param params print values
//     */
//    protected void logError(String msg, Object... params) {
//
//        if (params.length == 0) {
//            log.error(new StringBuilder(this.getClass().getSimpleName()).append(".").append(msg).toString());
//        } else {
//            log.error(new StringBuilder(this.getClass().getSimpleName()).append(".").append(msg).toString(), params);
//        }
//    }
//
//}
