package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;

@TableName(value = "ip_b_jitx_order_item")
@Data
@Document(index = "ip_b_jitx_order_item", type = "ip_b_jitx_order_item")
public class IpBJitxOrderItem extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "IP_B_JITX_ORDER_ID")
    @Field(type = FieldType.Long)
    private Long ipBJitxOrderId;

    @JSONField(name = "BARCODE")
    @Field(type = FieldType.Keyword)
    private String barcode;

    @JSONField(name = "PRODUCT_NAME")
    @Field(type = FieldType.Keyword)
    private String productName;

    @JSONField(name = "BRAND_NAME")
    @Field(type = FieldType.Keyword)
    private String brandName;

    @JSONField(name = "CSIZE")
    @Field(type = FieldType.Keyword)
    private String csize;

    @JSONField(name = "QUANTITY")
    @Field(type = FieldType.Long)
    private Long quantity;

    @JSONField(name = "PO_NO")
    @Field(type = FieldType.Keyword)
    private String poNo;

    @JSONField(name = "SN")
    @Field(type = FieldType.Keyword)
    private String sn;

    @JSONField(name = "PRICE")
    @Field(type = FieldType.Double)
    private BigDecimal price;

    @JSONField(name = "PROMOTION_PRICE")
    @Field(type = FieldType.Double)
    private BigDecimal promotionPrice;

    @JSONField(name = "COOPERATION_NO")
    @Field(type = FieldType.Long)
    private Long cooperationNo;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;
}