package com.jackrain.nea.oc.oms.services.returnin;

import com.jackrain.nea.oc.oms.es.ES4ReturnOrder;
import com.jackrain.nea.oc.oms.model.enums.IsMatchEnum;
import com.jackrain.nea.oc.oms.model.enums.ProReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnStockInTimes;
import com.jackrain.nea.oc.oms.model.relation.OcReturnInRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsStockInMatchParam;
import com.jackrain.nea.oc.oms.model.relation.RefundInRelation;
import com.jackrain.nea.oc.oms.model.table.OcBRefundIn;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInProductItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
import com.jackrain.nea.oc.oms.refund.util.NumUtil;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.util.ThreadLocalUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2023/3/6
 */
@Slf4j
@Component
public class OcReturnInB2BStockIn implements ApplicationContextAware {

    @Autowired
    protected OcReturnInSupport inSupport;

    @Autowired
    protected OcReturnInCommService inCommService;

    private final Map<ReturnStockInTimes, IReturnMatchStockIn> inServices = new HashMap<>();

    /**
     * 匹配退单入库处理
     *
     * @param inRelation 退货入库结果单,明细
     */
    public void matchReturnProcessor(RefundInRelation inRelation) {
        logStep("match return start...");
        try {
            // query return key
            Long returnId = esLookupReturnKey(inRelation);
            // match
            OcReturnInRelation stockInRelation = matchReturnProcess(returnId, inRelation);
            // refund write return store info
            OcBReturnOrder returnOrder = stockInRelation.getItem();
            ReturnStockInTimes route = routeStockInWay(returnOrder);
            // prepare update params
            OmsStockInMatchParam prepareParams = inServices.get(route).handleBilProcessing(inRelation, stockInRelation);
            // stock in
            boolean isUpdate = inServices.get(route).persistDataAndStockIn(prepareParams);
            // arrange refundIn items
            arrangeRefundInMatchResult(isUpdate, inRelation, stockInRelation);
        } catch (Exception e) {
            logStep(OcReturnInSupport.expMsgFun.apply(e));
        }
        logStep("match return end...");
    }

    /**
     * log
     *
     * @param express 日志内容表达式
     * @param obs     日志动态参数
     */
    protected void logStep(String express, Object... obs) {
        ThreadLocalUtil.logStepMsg.get().add(String.format(express, obs));
    }

    /**
     * 查询退换货单分库键
     *
     * @param inRelation 退货入库结果单关系数据
     * @return 退换货单分库键
     */
    private Long esLookupReturnKey(RefundInRelation inRelation) {
        OcBRefundIn refundIn = inRelation.getRefundIn();
        String sgBNoticeInBillNo = refundIn.getSgBNoticeInBillNo();
        AssertUtil.assertException(StringUtils.isBlank(sgBNoticeInBillNo), "inNoticeNo is blank");
        Long returnId = ES4ReturnOrder.searchReturnOrderByNoticeInNo(sgBNoticeInBillNo);
        AssertUtil.assertException(Objects.isNull(returnId), "es query null");
        return returnId;
    }

    /**
     * 匹配流程
     *
     * @param returnId   退换货单ID
     * @param inRelation 退货入库结果单数据
     * @return 匹配结果
     */
    private OcReturnInRelation matchReturnProcess(Long returnId, RefundInRelation inRelation) {
        // lock
        String lockRedisKey = BllRedisKeyResources.buildLockReturnOrderKey(returnId);
        boolean lockSate = inSupport.lockBil(lockRedisKey);
        logStep(lockRedisKey);
        AssertUtil.isTrue(lockSate, "return.lock.fail");
        // query,check return
        OcBReturnOrder returnOrder = inSupport.getReturnOrder(returnId);
        AssertUtil.assertException(Objects.isNull(returnOrder), "DBQuery.return.empty");
        String returnInlType = returnOrder.getReserveVarchar05();
        logStep("B2BTag-05=%s", returnInlType);
        AssertUtil.assertException(!(StringUtils.equalsIgnoreCase(OcReturnInCommService.B2B_PLAT_TAG, returnInlType) || (StringUtils.equalsIgnoreCase(OcReturnInCommService.B2B_PLAT_TAG2, returnInlType))),
                "Just.match.B2B.bil");
        AssertUtil.assertException(isInvalidReturn(returnOrder), "return.invalid.status");
        // query return items
        List<OcBReturnOrderRefund> subItems = inSupport.getReturnItems(returnOrder.getId());
        AssertUtil.assertException(CollectionUtils.isEmpty(subItems), "query.returnItems.empty");
        // match
        OcReturnInRelation stockInRelation = matching(subItems, inRelation);
        AssertUtil.notNull(stockInRelation, "match.return.result.null");
        // set relation info
        stockInRelation.setItem(returnOrder);
        stockInRelation.setSubItems(subItems);
        stockInRelation.setCurrentMatchFinished(true);
        OcReturnInSupport.matchedReturn.set(stockInRelation);
        return stockInRelation;
    }

    /**
     * 退单状态.校验
     *
     * @param order Return Order
     * @return is legal
     */
    private boolean isInvalidReturn(OcBReturnOrder order) {
        boolean inValidReturnStatus = ReturnStatusEnum.CANCLE.getVal().equals(order.getReturnStatus());
        boolean wholeProReturnStatus = ProReturnStatusEnum.WHOLE.getVal().equals(order.getProReturnStatus());
        return inValidReturnStatus || wholeProReturnStatus;
    }

    private ReturnStockInTimes routeStockInWay(OcBReturnOrder order) {
        Integer proReturnStatus = order.getProReturnStatus();
        return ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal().equals(order.getReturnStatus())
                || ProReturnStatusEnum.WAIT.getVal().equals(proReturnStatus)
                ? ReturnStockInTimes.INIT : ReturnStockInTimes.MULTI;
    }

    /**
     * 获取匹配入库单明细条码
     *
     * @param item 入库单明细
     * @return 实际匹配条码
     */
    private String getRefundInSkuCode(OcBRefundInProductItem item) {
        return StringUtils.isBlank(item.getRealSkuEcode()) ? item.getPsCSkuEcode() : item.getRealSkuEcode();
    }

    /**
     * 获取可匹配数量
     *
     * @param item 退货单明细
     * @return 可匹配数量
     */
    private BigDecimal getCanMatchQty(OcBReturnOrderRefund item) {
        return item.getQtyRefund().subtract(NumUtil.toBigDecimal(item.getQtyMatch()));
    }

    /**
     * 匹配
     *
     * @param returnItems 退换货单明细
     * @param inRelation  退货入库结果单
     * @return 匹配结果
     */
    private OcReturnInRelation matching(List<OcBReturnOrderRefund> returnItems, RefundInRelation inRelation) {
        Map<String, List<OcBReturnOrderRefund>> unReturnItemMap = returnItems
                .stream().sorted(Comparator.comparing(this::getCanMatchQty).reversed())
                .collect(Collectors.groupingBy(OcBReturnOrderRefund::getPsCSkuEcode));
        List<OcBRefundInProductItem> unInItems = inRelation.getUnMatchItems()
                .stream().sorted(Comparator.comparing(this::getRefundInSkuCode).thenComparing(Comparator
                        .comparing(OcBRefundInProductItem::getQty).reversed())).collect(Collectors.toList());
        List<OcBReturnOrderRefund> unReturnItems;
        Set<Long> subReturnKeys = new HashSet<>();
        OcReturnInRelation stockInRelation = new OcReturnInRelation();
        List<OcBRefundInProductItem> matchInItems = new ArrayList<>();
        List<OcBReturnOrderRefund> matchReturnItems = new ArrayList<>();
        for (OcBRefundInProductItem inItm : unInItems) {
            if (inItm.getOcBReturnOrderId() != null || IsMatchEnum.MATCHED.getVal().equals(inItm.getIsMatch())) {
                continue;
            }
            BigDecimal inItmQty = inItm.getQty();
            String inSkuCode = getRefundInSkuCode(inItm);
            unReturnItems = unReturnItemMap.get(inSkuCode);
            AssertUtil.notEmpty(unReturnItems, inSkuCode + ": match return item not found");
            // get return item
            int idx = 0;
            int returnSize = unReturnItems.size();
            do {
                OcBReturnOrderRefund returnItm = unReturnItems.get(idx);
                BigDecimal canMatchQty = getCanMatchQty(returnItm);
                // check return item remain qty
                if (!NumUtil.gtZero(canMatchQty)) {
                    idx++;
                    continue;
                }
                // filter
                if (subReturnKeys.add(returnItm.getId())) {
                    matchReturnItems.add(returnItm);
                }
                // record mapping
                if (!NumUtil.prevGtNext(inItmQty, canMatchQty)) {
                    returnItm.setQtyMatch(NumUtil.init(returnItm.getQtyMatch()) + NumUtil.toInt(inItmQty));
                    returnItm.setQtyIn(NumUtil.init(returnItm.getQtyIn()).add(inItmQty));
                    stockInRelation.mappingQty(returnItm, inItmQty);
                    inCommService.branchInQty(inItmQty, returnItm, inItm, stockInRelation);
                    inItmQty = BigDecimal.ZERO;
                    break;
                } else {
                    returnItm.setQtyMatch(NumUtil.init(returnItm.getQtyMatch()) + NumUtil.toInt(canMatchQty));
                    returnItm.setQtyIn(NumUtil.init(returnItm.getQtyIn()).add(canMatchQty));
                    stockInRelation.mappingQty(returnItm, canMatchQty);
                    inCommService.branchInQty(canMatchQty, returnItm, inItm, stockInRelation);
                    inItmQty = inItmQty.subtract(canMatchQty);
                    idx++;
                }
            } while (idx < returnSize);
            AssertUtil.assertException(NumUtil.gtZero(inItmQty), inSkuCode + ": inItem remain qty");
            matchInItems.add(inItm);
        }
        stockInRelation.setSubInMatchItems(matchInItems);
        stockInRelation.setSubMatchItems(matchReturnItems);
        return stockInRelation;
    }

    /**
     * 退货入库结果明细,匹配入库结果整理
     *
     * @param result     入库结果
     * @param inRelation 退货入库结果关系数据
     * @param stockIn    匹配入库结果关系数据
     */
    private void arrangeRefundInMatchResult(boolean result, RefundInRelation inRelation, OcReturnInRelation stockIn) {
        if (result) {
            List<OcBRefundInProductItem> subInMatchItems = stockIn.getSubInMatchItems();
            inRelation.popMatchedItem(subInMatchItems);
            logStep("pop");
        }
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        Map<String, IReturnMatchStockIn> beans = applicationContext.getBeansOfType(IReturnMatchStockIn.class);
        beans.forEach((key, value) -> inServices.put(value.stockInTimes(), value));
    }

}
