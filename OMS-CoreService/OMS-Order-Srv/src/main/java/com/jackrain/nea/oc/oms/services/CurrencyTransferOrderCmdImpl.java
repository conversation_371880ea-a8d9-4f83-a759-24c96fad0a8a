package com.jackrain.nea.oc.oms.services;

import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.api.CurrencyTransferOrderCmd;
import com.jackrain.nea.oc.oms.model.Standplat.IpBStandplatRefundType;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.relation.OmsStandPlatRefundRelation;
import com.jackrain.nea.oc.oms.model.request.TransferOrderRequest;
import com.jackrain.nea.oc.oms.model.result.TransferOrderResult;
import com.jackrain.nea.oc.oms.process.transfer.impl.exchange.standplat.StandplatTransferExchangeProcessImpl;
import com.jackrain.nea.oc.oms.process.transfer.impl.refund.currency.StandplatRefundRefundProcessImpl;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.ProcessStepResultList;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> 夏繼超
 * @since : 2019-05-14
 * create at : 2019-05-14 9:21 AM
 */
@Service(protocol = "dubbo", validation = "true", group = "oc-core", version = "1.0")
@Slf4j
@Component
public class CurrencyTransferOrderCmdImpl implements CurrencyTransferOrderCmd {

    @Autowired
    private OmsStandplatRefundService omsStandplatRefundService;

    @Autowired
    private StandplatRefundRefundProcessImpl standplatRefundRefundProcess;

    @Autowired
    private StandplatTransferExchangeProcessImpl standplatTransferExchangeProcess;

    @Autowired
    private BusinessSystemParamService businessSystemParamService;

    @Override
    public ValueHolderV14<TransferOrderResult> startTransferOrder(TransferOrderRequest transferOrderRequest) {

        ValueHolderV14<TransferOrderResult> resultValueHolderV14 = new ValueHolderV14<>();
        try {
            boolean hasError = false;
            int failedNumber = 0;
            int successNumber = 0;
            List<ProcessStepResult> errorStepResultList = new ArrayList<>();
            for (String orderNo : transferOrderRequest.getOrderNoList()) {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("TransferOrderCmdImpl,ChannelType：{}", orderNo, "auditOrder"), transferOrderRequest.getChannelType());
                }
                if (transferOrderRequest.getChannelType() == ChannelType.STANDPLAT) {
                    OmsStandPlatRefundRelation standPlatRefundRelation = omsStandplatRefundService.selectStandplatRefundRelation(orderNo);

                    if (standPlatRefundRelation != null) {
                        ProcessStepResultList processStepResultList;
                        if (IpBStandplatRefundType.EXCHANGE_GOODS == standPlatRefundRelation.getIpBStandplatRefund().getRefundType() && businessSystemParamService.standlatExchange()) {
                            log.info("{}.standplatTransferExchangeProcess start", this.getClass().getSimpleName());
                            processStepResultList = standplatTransferExchangeProcess.start(standPlatRefundRelation,
                                    false, SystemUserResource.getRootUser());
                        } else {
                            processStepResultList = standplatRefundRefundProcess.start(standPlatRefundRelation,
                                    false, SystemUserResource.getRootUser());
                        }
                        if (!processStepResultList.isProcessFishSuccess()) {
                            hasError = true;
                            errorStepResultList.add(processStepResultList.getLastFaileOrFinishProcessStepResult());
                            failedNumber++;
                        } else {
                            successNumber++;
                        }
                    } else {
                        hasError = false;
                        failedNumber++;
                    }
                }
            }

            if (hasError) {
                resultValueHolderV14.setCode(ResultCode.FAIL);
                StringBuilder sbMessage = new StringBuilder();
                sbMessage.append(String.format("转换成功%s条；转换失败%s条；失败原因：\r\n", successNumber, failedNumber));
                for (ProcessStepResult stepResult : errorStepResultList) {
                    if (stepResult != null) {
                        sbMessage.append(stepResult.getMessage());
                        sbMessage.append("\r\n");
                    }
                }
                resultValueHolderV14.setMessage(sbMessage.toString());
            } else {
                resultValueHolderV14.setCode(ResultCode.SUCCESS);
                resultValueHolderV14.setMessage("转换全部成功。");
            }
        } catch (Exception ex) {
            resultValueHolderV14.setCode(ResultCode.FAIL);
            resultValueHolderV14.setMessage("转换异常：" + ex.getMessage());
            log.error(LogUtil.format("TransferOrderCmdImpl,异常信息:{}", "TransferOrderCmdImpl"), Throwables.getStackTraceAsString(ex));
        }

        return resultValueHolderV14;
    }

}