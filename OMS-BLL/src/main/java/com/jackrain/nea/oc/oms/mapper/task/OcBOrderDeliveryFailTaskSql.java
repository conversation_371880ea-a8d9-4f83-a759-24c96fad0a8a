package com.jackrain.nea.oc.oms.mapper.task;

import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * @ClassName OcBOrderDeliveryFailTaskSql
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/4/6 15:25
 * @Version 1.0
 */
@Slf4j
public class OcBOrderDeliveryFailTaskSql {

    public String selectDeliveryFail4Retry(Map<String, Object> para) {
        StringBuffer sql = new StringBuffer();
        StringBuffer limitStr = new StringBuffer(" LIMIT ");
        int limit = para.get("limit") != null ? (int) para.get("limit") : 50;
        limitStr.append(limit);

        String taskTableName = (String) para.get("taskTableName");
        int retryTimes = 18;

        sql.append("select * from ")
                .append(taskTableName)
                .append("  where next_time < now()")
                .append(" and retry_number < ")
                .append(retryTimes)
                .append(" and status = 0 ")
                .append(" order by modifieddate asc")
                .append(limitStr);
        return sql.toString();
    }
}
