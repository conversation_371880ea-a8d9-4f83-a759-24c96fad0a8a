package com.jackrain.nea.oc.oms.services.impl;

import com.aliyun.openservices.shade.org.apache.commons.lang3.StringUtils;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.OcBRemarkGiftTaskMapper;
import com.jackrain.nea.oc.oms.model.table.OcBRemarkGiftTask;
import com.jackrain.nea.oc.oms.services.OcBRemarkGiftTaskService;
import com.jackrain.nea.oc.oms.services.StCRemarkGiftStrategyService;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 卖家添加备注任务服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class OcBRemarkGiftTaskServiceImpl implements OcBRemarkGiftTaskService {

    /**
     * 任务状态：待执行
     */
    private static final int TASK_STATUS_PENDING = 0;

    /**
     * 任务状态：执行成功
     */
    private static final int TASK_STATUS_SUCCESS = 1;

    /**
     * 任务状态：超时失效
     */
    private static final int TASK_STATUS_TIMEOUT = 2;

    /**
     * 任务状态：执行中
     */
    private static final int TASK_STATUS_PROCESSING = 3;

    @Resource
    private OcBRemarkGiftTaskMapper remarkGiftTaskMapper;

    @Resource
    private StCRemarkGiftStrategyService stCRemarkGiftStrategyService;

    @Override
    public void processRemarkGiftTask() {
        try {
            // 1. 处理超时任务（24小时前创建的待执行任务）
            processTimeoutTasks();

            // 2. 处理待执行任务
            processPendingTasks();
        } catch (Exception e) {
            log.warn(LogUtil.format("处理卖家添加备注任务异常", "OcBRemarkGiftTaskServiceImpl.processRemarkGiftTask"), e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 处理超时任务
     */
    private void processTimeoutTasks() {
        // 获取24小时前的时间
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.HOUR, -24);
        Date beforeTime = calendar.getTime();

        // 查询超时任务
        List<OcBRemarkGiftTask> timeoutTasks = remarkGiftTaskMapper.selectByStatusAndCreationdate(TASK_STATUS_PENDING, beforeTime);
        if (CollectionUtils.isEmpty(timeoutTasks)) {
            return;
        }

        // 更新超时任务状态
        List<Long> timeoutIds = timeoutTasks.stream()
                .map(OcBRemarkGiftTask::getId)
                .collect(Collectors.toList());

        remarkGiftTaskMapper.updateTaskStatusBatch(timeoutIds, TASK_STATUS_TIMEOUT);
        log.info(LogUtil.format("处理超时任务完成，任务数:{}", "OcBRemarkGiftTaskServiceImpl.processTimeoutTasks"), timeoutIds.size());
    }

    /**
     * 处理待执行任务
     */
    private void processPendingTasks() {
        // 查询待执行任务
        List<OcBRemarkGiftTask> remarkGiftTasks = remarkGiftTaskMapper.selectIdsByStatus(TASK_STATUS_PENDING);
        if (CollectionUtils.isEmpty(remarkGiftTasks)) {
            return;
        }

        // 更新任务状态为执行中
        List<Long> remarkGiftTaskIds = remarkGiftTasks.stream()
                .map(OcBRemarkGiftTask::getId)
                .collect(Collectors.toList());
        remarkGiftTaskMapper.updateTaskStatusBatch(remarkGiftTaskIds, TASK_STATUS_PROCESSING);

        // 处理每个任务
        for (OcBRemarkGiftTask remarkGiftTask : remarkGiftTasks) {
            try {
                // 调用赠品策略服务
                boolean b = stCRemarkGiftStrategyService.addGiftSku(remarkGiftTask.getCpCShopId(), remarkGiftTask.getTid());
                if (!b) {
                    // 更新任务状态为待执行
                    remarkGiftTaskMapper.updateTaskStatus(remarkGiftTask.getId(), TASK_STATUS_PENDING);
                } else {
                    // 更新任务状态为成功
                    remarkGiftTaskMapper.updateTaskStatus(remarkGiftTask.getId(), TASK_STATUS_SUCCESS);
                }
                log.info(LogUtil.format("处理备注任务结束 taskId:{},b:{}", "OcBRemarkGiftTaskServiceImpl.processPendingTasks"), remarkGiftTask.getId(), b);
            } catch (Exception e) {
                // 更新任务状态为待执行
                remarkGiftTaskMapper.updateTaskStatus(remarkGiftTask.getId(), TASK_STATUS_PENDING);
                log.warn(LogUtil.format("处理备注任务异常 taskId:{}, error:{}", "OcBRemarkGiftTaskServiceImpl.processPendingTasks"),
                        remarkGiftTask.getId(), e.getMessage());
            }
        }
    }

    @Override
    public boolean createRemarkGiftTask(Long shopId, String tid) {
        if (shopId == null || StringUtils.isEmpty(tid)) {
            log.warn(LogUtil.format("创建备注赠品任务参数无效 shopId:{}, tid:{}", "OcBRemarkGiftTaskServiceImpl.createRemarkGiftTask"),
                    shopId, tid);
            return false;
        }

        try {
            List<OcBRemarkGiftTask> remarkGiftTasks = remarkGiftTaskMapper.selectByTid(tid);
            if (CollectionUtils.isNotEmpty(remarkGiftTasks)) {
                log.warn(LogUtil.format("创建备注赠品任务已存在 shopId:{}, tid:{}", "OcBRemarkGiftTaskServiceImpl.createRemarkGiftTask"),
                        shopId, tid);
                return false;
            }

            // 创建新任务
            OcBRemarkGiftTask task = new OcBRemarkGiftTask();
            task.setId(ModelUtil.getSequence("oc_b_remark_gift_task"));
            task.setCpCShopId(shopId);
            task.setTid(tid);
            task.setTaskStatus(TASK_STATUS_PENDING);
            task.setCreationdate(new Date());
            task.setModifieddate(new Date());
            task.setIsactive("Y");

            // 插入任务
            int result = remarkGiftTaskMapper.insert(task);
            if (result > 0) {
                log.info(LogUtil.format("创建备注赠品任务成功 shopId:{}, tid:{}", "OcBRemarkGiftTaskServiceImpl.createRemarkGiftTask"),
                        shopId, tid);
                return true;
            } else {
                log.warn(LogUtil.format("创建备注赠品任务失败 shopId:{}, tid:{}", "OcBRemarkGiftTaskServiceImpl.createRemarkGiftTask"),
                        shopId, tid);
                return false;
            }
        } catch (Exception e) {
            log.error(LogUtil.format("创建备注赠品任务异常 shopId:{}, tid:{}, error:{}", "OcBRemarkGiftTaskServiceImpl.createRemarkGiftTask"),
                    shopId, tid, e.getMessage());
            return false;
        }
    }

}