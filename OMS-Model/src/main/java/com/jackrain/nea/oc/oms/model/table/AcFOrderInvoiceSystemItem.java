package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;

@TableName(value = "ac_f_order_invoice_system_item")
@Data
public class AcFOrderInvoiceSystemItem extends BaseModel {
    private static final long serialVersionUID = 5013749210832394775L;
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "AC_F_ORDER_INVOICE_ID")
    private Long acFOrderInvoiceId;

    @JSONField(name = "TID")
    private String tid;

    @JSONField(name = "ORDER_BILL_NO")
    private String orderBillNo;

    @JSONField(name = "UNIT")
    private String unit;

    @JSONField(name = "PS_C_SKU_ID")
    private Long psCSkuId;

    @JSONField(name = "PS_C_SKU_ECODE")
    private String psCSkuEcode;

    @JSONField(name = "PS_C_PRO_ID")
    private Long psCProId;

    @JSONField(name = "PS_C_PRO_ECODE")
    private String psCProEcode;

    @JSONField(name = "PS_C_PRO_ENAME")
    private String psCProEname;

    @JSONField(name = "IS_RETURN_GOODS")
    private String isReturnGoods;

    @JSONField(name = "PRICE_ACTUAL")
    private BigDecimal priceActual;

    @JSONField(name = "PRICE_AMT")
    private BigDecimal priceAmt;

    @JSONField(name = "QTY")
    private BigDecimal qty;

    @JSONField(name = "QTY_RETURN")
    private BigDecimal qtyReturn;

    @JSONField(name = "PRICE_RETURN_AMT")
    private BigDecimal priceReturnAmt;

    @JSONField(name = "SHIP_PRICE")
    private BigDecimal shipPrice;

    @JSONField(name = "IS_GIFT")
    private Integer isGift;

    @JSONField(name = "OC_B_ORDER_ID")
    private Long ocBOrderId;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;
}