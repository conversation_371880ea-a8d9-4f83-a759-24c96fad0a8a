package com.jackrain.nea.oc.oms.api.st;

import com.jackrain.nea.oc.oms.vo.StCEquityBarterStrategyImpVo;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

import java.util.List;

/**
 * @Author: lijin
 * @Date: 2024/06/04
 * @Version 1.0
 */
public interface StCEquityBarterStrategyImportCmd {
    /**
     * 下载模版
     *
     * @return
     */
    ValueHolderV14<String> queryTemplateDownloadUrl();

    /**
     * 对等换货策略头明细覆盖
     *
     * @param dataImpVos
     * @param user
     * @return
     */
    ValueHolderV14<String> importCoverDataList(List<StCEquityBarterStrategyImpVo> dataImpVos, User user);

    /**
     * 对等换货策略头明细追加
     *
     * @param dataImpVos
     * @param user
     * @return
     */
    ValueHolderV14<String> importAddDataList(List<StCEquityBarterStrategyImpVo> dataImpVos, User user);
}
