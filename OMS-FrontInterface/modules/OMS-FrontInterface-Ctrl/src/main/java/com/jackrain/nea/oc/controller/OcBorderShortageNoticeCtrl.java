package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBorderShortageNoticeCmd;
import com.jackrain.nea.oc.oms.model.relation.UserPermission;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/15 下午8:30
 * @description 猫超缺货回传
 **/

@RestController
@Slf4j
@Api(value = "OcBorderShortageNoticeCtrl", tags = "缺货回告")
public class OcBorderShortageNoticeCtrl {

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @Autowired
    private OcBorderShortageNoticeCmd ocBorderShortageNoticeCmd;

    @ApiOperation(value = "缺货回告")
    @RequestMapping(value = "/api/cs/oc/oms/v1/shortageNotice", method = RequestMethod.POST)
    public JSONObject shortageNotice(HttpServletRequest request,
                                     @RequestBody JSONObject obj) {

        ValueHolderV14 vh;
        try {
            User user = r3PrimWebAuthService.getLoginPrimWebUser(request);

            if (obj == null) {
                throw new NDSException(Resources.getMessage("请求参数不能为空!", user.getLocale()));
            }

            JSONArray jsonArray = obj.getJSONArray("ids");
            List<Long> orderIdList = new ArrayList<>();
            if (jsonArray != null && jsonArray.size() > 0) {
                for (int i = 0; i < jsonArray.size(); i++) {
                    Long orderId = jsonArray.getLong(i);
                    orderIdList.add(orderId);
                }
            } else {
                throw new NDSException(Resources.getMessage("请至少选择一条单据!", user.getLocale()));
            }

            UserPermission usrPem = UserPermissionCtrlHelper.currentUserPermission(user, "OC_B_ORDER");
            if (usrPem == null) {
                vh = new ValueHolderV14<>();
                vh.setMessage("未获取到用户权限");
                vh.setCode(ResultCode.FAIL);
                return vh.toJSONObject();
            }

            vh = ocBorderShortageNoticeCmd.shortageNotice(orderIdList, user);
            vh.setCode(vh.getCode());
            vh.setMessage(Resources.getMessage(vh.getMessage()));
            return vh.toJSONObject();
        } catch (Exception e) {
            vh = new ValueHolderV14();
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return vh.toJSONObject();
        }
    }
}