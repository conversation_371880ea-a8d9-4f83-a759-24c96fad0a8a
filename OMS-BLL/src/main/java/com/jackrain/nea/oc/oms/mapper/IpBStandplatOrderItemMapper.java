package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatOrderItem;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatOrderItemEx;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Mapper
public interface IpBStandplatOrderItemMapper extends ExtentionMapper<IpBStandplatOrderItem> {

    @Select(" SELECT * FROM IP_B_STANDPLAT_ORDER_ITEM t where t.TID = #{tid}")
    List<IpBStandplatOrderItem> selectIpBStandplatOrderItemByTid(@Param("tid") String tid);

    @Select(" SELECT * FROM IP_B_STANDPLAT_ORDER_ITEM t where t.OID = #{oid}")
    List<IpBStandplatOrderItem> selectIpBStandplatOrderItemByOid(@Param("oid") String oid);

    @Select("SELECT * FROM IP_B_STANDPLAT_ORDER_ITEM WHERE IP_B_STANDPLAT_ORDER_ID=#{orderId}")
    List<IpBStandplatOrderItemEx> selectOrderItemList(@Param("orderId") long orderId);


    @Select("SELECT * FROM IP_B_STANDPLAT_ORDER_ITEM WHERE IP_B_STANDPLAT_ORDER_ID=#{orderId} and sku_id = #{skuId} and num_iid = #{numIid}")
    List<IpBStandplatOrderItemEx> selectOrderItemListByTid(@Param("orderId") long orderId, @Param("skuId") String skuId, @Param("numIid") String numIid);

}