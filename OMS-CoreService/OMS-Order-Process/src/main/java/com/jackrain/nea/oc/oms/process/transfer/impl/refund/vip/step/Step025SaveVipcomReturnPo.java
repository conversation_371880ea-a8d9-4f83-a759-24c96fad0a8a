package com.jackrain.nea.oc.oms.process.transfer.impl.refund.vip.step;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.store.model.request.transfer.SgBStoTransferBillSaveRequest;
import com.burgeon.r3.sg.store.model.request.transfer.SgBStoTransferItemSaveRequest;
import com.burgeon.r3.sg.store.model.request.transfer.SgBStoTransferSaveRequest;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.cpext.model.table.CpCStore;
import com.jackrain.nea.cpext.model.table.CpCVipcomWahouse;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.constant.OcCommonConstant;
import com.jackrain.nea.oc.oms.model.constant.VipConstant;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpVipReturnOrderRelation;
import com.jackrain.nea.oc.oms.model.resources.SysNotesConstant;
import com.jackrain.nea.oc.oms.model.table.IpBVipReturnOrder;
import com.jackrain.nea.oc.oms.model.table.IpBVipReturnOrderItemEx;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.psext.model.table.PsCSku;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.request.OcBVipcomReturnPoRequest;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.rpc.VipComRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.vip.model.OcBVipcomReturnPoDO;
import com.jackrain.nea.vip.model.OcBVipcomReturnPoItemDO;
import com.jackrain.nea.vip.model.OcBVipcomReturnPoSysmatchItemDO;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description: 保存唯品会退供PO单
 * @create 2021-06-21 17:32
 */
@Step(order = 20, description = "保存唯品会退供PO单")
@Slf4j
@Component
public class Step025SaveVipcomReturnPo extends BaseVipReturnOrderProcessStep
        implements IOmsOrderProcessStep<IpVipReturnOrderRelation> {

    @Autowired
    private VipComRpcService vipComRpcService;

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private SgRpcService sgRpcService;

    @Override
    public ProcessStepResult<IpVipReturnOrderRelation> startProcess(IpVipReturnOrderRelation orderInfo,
                                                                    ProcessStepResult preStepResult, boolean isAutoMakeup,
                                                                    User operateUser) {
        String orderNo = orderInfo.getOrderNo();
        Map<String,Long> skuCodeMap = orderInfo.getSkuCodeMap();
        try {
            IpBVipReturnOrder vipReturnOrder = orderInfo.getVipReturnOrder();
            List<IpBVipReturnOrderItemEx> vipReturnOrderItems = orderInfo.getVipReturnOrderItems();
            // 将明细数据按po号进行分组
            Map<String, List<IpBVipReturnOrderItemEx>> poListMap = vipReturnOrderItems.stream().collect(Collectors.groupingBy(m -> m.getPoNo()));
            StringBuilder sbErrorInfo = new StringBuilder();
            boolean hasErrorInfo = false;
            if (MapUtils.isNotEmpty(poListMap)) {
                for (String poNo : poListMap.keySet()) {
                    // 根据退供单号和po号查询是否已经存在生成的PO单
//                    Boolean exist = vipComRpcService.selectVipcomReturnPo(orderNo, poNo);
//                    // 存在则跳过
//                    if (exist) {
//                        continue;
//                    }
                    // 获取同一po号的明细数据
                    List<IpBVipReturnOrderItemEx> ipBVipReturnOrderItemExes = poListMap.get(poNo);
                    // 构建生成唯品会退供PO单数据
                    OcBVipcomReturnPoRequest vipcomReturnPoRequest = bulidSaveRequest(poNo, vipReturnOrder, ipBVipReturnOrderItemExes,skuCodeMap);
                    vipcomReturnPoRequest.setObjId(-1L);
                    vipcomReturnPoRequest.setLoginUser(operateUser);
                    ValueHolder holder = vipComRpcService.saveVipcomReturnPo(vipcomReturnPoRequest);
                    if (log.isDebugEnabled()) {
                        log.debug("Step025SaveVipcomReturnPo.saveVipcomReturnPo.result:{}", JSONObject.toJSONString(holder));
                    }
                    if(!holder.isOK()){
                        hasErrorInfo = true;
                        String msg = Resources.getMessage("转换失败;");
                        sbErrorInfo.append("PO号：");
                        sbErrorInfo.append(poNo);
                        sbErrorInfo.append(msg);
                        sbErrorInfo.append("\r\n");
                    }
                    /*if (holder.isOK()) {
                        // 返回的po单数据
                        HashMap data = (HashMap) holder.getData().get(OcCommonConstant.DATA);
                        Long objid = (Long) data.get(OcCommonConstant.OBJ_ID);
                        // 构建生成调拨单数据
                        SgBStoTransferBillSaveRequest sgTransferBillSaveRequest = buildTransferBillSaveRequest(data, vipcomReturnPoRequest, operateUser);
                        // 生成并提交调拨单
                        ValueHolderV14 valueHolderV14 = sgRpcService.saveAndSubmit(sgTransferBillSaveRequest);
                        if (valueHolderV14.isOK()) {
                            // 调拨单新增成功更新PO单主表对应的调拨单数据
                            SgR3BaseResult result = (SgR3BaseResult) valueHolderV14.getData();
                            Long transferId = result.getDataJo().getLong(OcCommonConstant.OBJ_ID);
                            vipComRpcService.updateStatusAndtransfer(objid,
                                    VipConstant.OC_B_VIPCOM_RETURN_PO_STATUS2, transferId, result.getBillNo(), operateUser);
                        } else {
                            // 失败更新失败次数和原因
                            vipComRpcService.updatefailNumAndReason(objid, valueHolderV14.getMessage(), operateUser);
                        }
                    } else {
                        hasErrorInfo = true;
                        String msg = Resources.getMessage("转换失败;");
                        sbErrorInfo.append("PO号：");
                        sbErrorInfo.append(poNo);
                        sbErrorInfo.append(msg);
                        sbErrorInfo.append("\r\n");
                    }*/
                }
            }
            if (hasErrorInfo) {
                String errorMessage = "生成唯品会退供PO单失败，退出转单操作";
                boolean updateStatusRes = ipVipReturnOrderService.updateRemark(sbErrorInfo.toString(), orderNo,
                        TransferOrderStatus.TRANSFERFAIL.toInteger());
                if (!updateStatusRes) {
                    errorMessage += ";更新状态失败=False";
                }
                return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
            } else {
                ipVipReturnOrderService.updateRemark("转单完成！", orderNo,
                        TransferOrderStatus.TRANSFERRED.toInteger());
                return new ProcessStepResult(StepStatus.FINISHED, "唯品会退供单转PO单成功！");
            }
        } catch (Exception e) {
            log.error("唯品会退供单转换异常!，错误信息：{}", e);
            String errorMessage = "唯品会退供单转换异常!";
            // 修改中间表状态及系统备注
            ipVipReturnOrderService.updateRemark(SysNotesConstant.SYS_REMARK0 + "，" + errorMessage, orderNo,
                    TransferOrderStatus.TRANSFERFAIL.toInteger());
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
    }

    /**
     * 构建生成调拨单数据
     * @param data
     * @param vipcomReturnPoRequest
     * @return
     */
    private SgBStoTransferBillSaveRequest buildTransferBillSaveRequest(HashMap data, OcBVipcomReturnPoRequest vipcomReturnPoRequest, User user) {
        SgBStoTransferBillSaveRequest transferBillSaveRequest = new SgBStoTransferBillSaveRequest();
        OcBVipcomReturnPoDO vipcomReturnPo = vipcomReturnPoRequest.getOcBVipcomReturnPoDO();
        // 构建逻辑调拨单主表信息
        SgBStoTransferSaveRequest transferSaveRequest = buildTransferSaveRequest(data, vipcomReturnPo);
        // 查询唯品会退供PO单条码明细
        List<OcBVipcomReturnPoItemDO> vipcomReturnPoItems = vipComRpcService.selectVipcomReturnPoItems(transferSaveRequest.getSourceBillId());
        if (log.isDebugEnabled()) {
            log.debug("Step025SaveVipcomReturnPo.selectVipcomReturnPoItems.data:{}", JSONObject.toJSONString(vipcomReturnPoItems));
        }
        List<SgBStoTransferItemSaveRequest> transferItemSaveRequests = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(vipcomReturnPoItems)) {
            // 构建逻辑调拨单条码明细数据
            buildTransferItemSaveRequests(vipcomReturnPoItems, transferItemSaveRequests);
        }
        transferBillSaveRequest.setTransferSaveRequest(transferSaveRequest);
        transferBillSaveRequest.setItems(transferItemSaveRequests);
        transferBillSaveRequest.setObjId(-1L);
        transferBillSaveRequest.setLoginUser(user);
        if (log.isDebugEnabled()) {
            log.debug("Step025SaveVipcomReturnPo.buildTransferBillSaveRequest.param:{}", JSONObject.toJSONString(transferBillSaveRequest));
        }
        return transferBillSaveRequest;
    }

    /**
     * 构建逻辑调拨单明细数据
     * @param vipcomReturnPoItems
     * @param transferItemSaveRequests
     */
    private void buildTransferItemSaveRequests(List<OcBVipcomReturnPoItemDO> vipcomReturnPoItems, List<SgBStoTransferItemSaveRequest> transferItemSaveRequests) {
        for (OcBVipcomReturnPoItemDO vipcomReturnPoItem : vipcomReturnPoItems) {
            SgBStoTransferItemSaveRequest transferItemSaveRequest = new SgBStoTransferItemSaveRequest();
            BeanUtils.copyProperties(vipcomReturnPoItem, transferItemSaveRequest);
            // 来源明细id
            transferItemSaveRequest.setSourceBillItemId(vipcomReturnPoItem.getId());
            // 吊牌金额
            transferItemSaveRequest.setAmt(vipcomReturnPoItem.getAmtList());
            transferItemSaveRequests.add(transferItemSaveRequest);
        }
    }

    /**
     * 构建逻辑调拨单数据
     * @param data
     * @param vipcomReturnPo
     * @return
     */
    private SgBStoTransferSaveRequest buildTransferSaveRequest(HashMap data, OcBVipcomReturnPoDO vipcomReturnPo) {
        SgBStoTransferSaveRequest transferSaveRequest = new SgBStoTransferSaveRequest();
        Long id = (Long) data.get(OcCommonConstant.OBJ_ID);
        String billno = (String) data.get(OcCommonConstant.BILL_NO);
        Date date = (Date) data.get(OcCommonConstant.DATE);
        transferSaveRequest.setId(-1L);
        transferSaveRequest.setSourceBillId(id);
        transferSaveRequest.setSourceBillNo(billno);
        transferSaveRequest.setSourceBillDate(date);
        transferSaveRequest.setSourceBillType(7);
        transferSaveRequest.setBillDate(new Date());
        transferSaveRequest.setReceiverStoreId(vipcomReturnPo.getCpCDestId());
        transferSaveRequest.setReceiverStoreEcode(vipcomReturnPo.getCpCDestEcode());
        transferSaveRequest.setReceiverStoreEname(vipcomReturnPo.getCpCDestEname());
        transferSaveRequest.setIsAutoOut("Y");
        transferSaveRequest.setIsAutoIn("N");
        return transferSaveRequest;
    }

    /**
     * 构建生成唯品会退供PO单数据方法
     *
     * @param poNo
     * @param vipReturnOrder
     * @param vipReturnOrderItems
     * @return
     */
    private OcBVipcomReturnPoRequest bulidSaveRequest(String poNo,
                                                      IpBVipReturnOrder vipReturnOrder,
                                                      List<IpBVipReturnOrderItemEx> vipReturnOrderItems,
                                                      Map<String,Long> skuCodeMap) {
        // 创建一个保存的request
        OcBVipcomReturnPoRequest vipcomReturnPoRequest = new OcBVipcomReturnPoRequest();
        // 构建主表信息
        OcBVipcomReturnPoDO vipcomReturnPo = bulidVipcomReturnPo(poNo, vipReturnOrder, vipReturnOrderItems);

        // 条码明细集合
        List<OcBVipcomReturnPoItemDO> vipcomReturnPoItems = new ArrayList<>();
        // 系统匹配明细集合
        List<OcBVipcomReturnPoSysmatchItemDO> vipcomReturnPoSysmatchItems = new ArrayList<>();
        for (IpBVipReturnOrderItemEx vipReturnOrderItemEx : vipReturnOrderItems) {
            // 条码
            String barcode = vipReturnOrderItemEx.getBarcode();
            // 系统条码ID
            Long skuId = skuCodeMap.get(barcode);
            bulidVipcomReturnPoItem(skuId, vipReturnOrderItemEx, vipcomReturnPoItems);
        }
        vipcomReturnPoRequest.setOcBVipcomReturnPoDO(vipcomReturnPo);
        vipcomReturnPoRequest.setOcBVipcomReturnPoItemDOList(vipcomReturnPoItems);
        vipcomReturnPoRequest.setOcBVipcomReturnPoSysmatchItemDOList(vipcomReturnPoSysmatchItems);
        if (log.isDebugEnabled()) {
            log.debug("Step025SaveVipcomReturnPo.bulidSaveRequest.param:{}", JSONObject.toJSONString(vipcomReturnPoRequest));
        }
        return vipcomReturnPoRequest;
    }

    /**
     * 构建唯品会退供po单主表数据
     *
     * @param poNo
     * @param vipReturnOrder
     * @param vipReturnOrderItems
     * @return
     */
    private OcBVipcomReturnPoDO bulidVipcomReturnPo(String poNo, IpBVipReturnOrder vipReturnOrder, List<IpBVipReturnOrderItemEx> vipReturnOrderItems) {
        OcBVipcomReturnPoDO vipcomReturnPo = new OcBVipcomReturnPoDO();
        // 退供单号
        vipcomReturnPo.setReturnSn(vipReturnOrder.getReturnSn());
        // PO号
        vipcomReturnPo.setPoNo(poNo);
        vipcomReturnPo.setReturnType(vipReturnOrder.getReturnType());
        vipcomReturnPo.setWarehouseEcode(vipReturnOrder.getWarehouse());
        // 查询出当前店铺
        CpShop cpShop = cpRpcService.selectShopById(vipReturnOrder.getCpCShopId());
        List<CpCStore> vipReturns = cpRpcService.selectStoreById(cpShop.getVipReturns().intValue());
        if (CollectionUtils.isNotEmpty(vipReturns)) {
            CpCStore cpCStore = vipReturns.get(0);
            // 收货逻辑仓
            vipcomReturnPo.setCpCDestId(cpCStore.getId());
            vipcomReturnPo.setCpCDestEcode(cpCStore.getEcode());
            vipcomReturnPo.setCpCDestEname(cpCStore.getEname());
        }
        //根据销售区域查询jit仓库信息
        /*CpCVipcomWahouse cpCVipcomWahouse = cpRpcService.selectByCode(vipReturnOrder.getWarehouse());
        if(cpCVipcomWahouse != null){
            vipcomReturnPo.setWarehouseId(cpCVipcomWahouse.getId());
            vipcomReturnPo.setWarehouseEcode(cpCVipcomWahouse.getWarehouseCode());
            vipcomReturnPo.setWarehouseEname(cpCVipcomWahouse.getWarehouseName());
        }else{
            log.error("Step025SaveVipcomReturnPo.bulidVipcomReturnPo.Warehouse:{} query CpCVipcomWahouse is null ",vipReturnOrder.getWarehouse());
        }*/

        // 退供收货人
        vipcomReturnPo.setConsignee(vipReturnOrder.getConsignee());
        // 省、市、区、地址等信息
        vipcomReturnPo.setState(vipReturnOrder.getState());
        vipcomReturnPo.setCity(vipReturnOrder.getCity());
        vipcomReturnPo.setRegion(vipReturnOrder.getRegion());
        vipcomReturnPo.setTown(vipReturnOrder.getTown());
        vipcomReturnPo.setAddress(vipReturnOrder.getAddress());
        vipcomReturnPo.setTelephone(vipReturnOrder.getTelephone());
        vipcomReturnPo.setMobile(vipReturnOrder.getMobile());
        // 买家昵称
        vipcomReturnPo.setSellerNick(vipReturnOrder.getSellerNick());
        // 店铺
        vipcomReturnPo.setCpCShopId(cpShop.getId());
        vipcomReturnPo.setCpCShopEcode(cpShop.getEcode());
        vipcomReturnPo.setCpCShopEname(cpShop.getCpCShopTitle());
        vipcomReturnPo.setPsCBrandId(vipReturnOrder.getPsCBrandId());
        // 汇总明细退供商品数量
        BigDecimal totalQty = vipReturnOrderItems.stream().map(IpBVipReturnOrderItemEx::getQty).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        vipcomReturnPo.setTotalQty(totalQty);
        if (log.isDebugEnabled()) {
            log.debug("Step025SaveVipcomReturnPo.bulidVipcomReturnPo.param:{}", JSONObject.toJSONString(vipcomReturnPo));
        }
        vipcomReturnPo.setPayType(vipReturnOrder.getPayType());
        vipcomReturnPo.setSelfReference(vipReturnOrder.getSelfReference());
        vipcomReturnPo.setOutTime(vipReturnOrder.getOutTime());
        vipcomReturnPo.setPsCBrandId(vipReturnOrder.getPsCBrandId());
        return vipcomReturnPo;
    }

    /**
     * 构建生成唯品会退供PO单条码明细
     *
     * @param skuId
     * @param vipReturnOrderItemEx
     * @param vipcomReturnPoItems
     * @return
     */
    private void bulidVipcomReturnPoItem(Long skuId, IpBVipReturnOrderItemEx vipReturnOrderItemEx,
                                         List<OcBVipcomReturnPoItemDO> vipcomReturnPoItems) {
        OcBVipcomReturnPoItemDO vipcomReturnPoItem = new OcBVipcomReturnPoItemDO();
        vipcomReturnPoItem.setPsCSkuId(skuId);
        ProductSku productSku = psRpcService.selectProductById(skuId.toString());
        if (productSku != null) {
            vipcomReturnPoItem.setPsCSkuEcode(productSku.getEcode());
            vipcomReturnPoItem.setGbcode(productSku.getBarcode69());
            vipcomReturnPoItem.setForcode(productSku.getForCode());
            // 商品信息、颜色、尺寸、吊牌价
            vipcomReturnPoItem.setPsCProId(productSku.getProdId());
            vipcomReturnPoItem.setPsCProEcode(productSku.getProdCode());
            vipcomReturnPoItem.setPsCProEname(productSku.getName());
            vipcomReturnPoItem.setPsCSpec1Id(productSku.getColorId());
            vipcomReturnPoItem.setPsCSpec1Ecode(productSku.getColorCode());
            vipcomReturnPoItem.setPsCSpec1Ename(productSku.getColorName());
            vipcomReturnPoItem.setPsCSpec2Id(productSku.getSizeId());
            vipcomReturnPoItem.setPsCSpec2Ecode(productSku.getSizeCode());
            vipcomReturnPoItem.setPsCSpec2Ename(productSku.getSizeName());
            vipcomReturnPoItem.setPriceList(productSku.getPricelist() == null ? productSku.getPricelist() : BigDecimal.ZERO);
        }
        // 退供数量
        vipcomReturnPoItem.setQty(vipReturnOrderItemEx.getQty());
        vipcomReturnPoItem.setQtyIn(BigDecimal.ZERO);
        vipcomReturnPoItem.setQtyOut(BigDecimal.ZERO);
        vipcomReturnPoItem.setQtyDiff(BigDecimal.ZERO);
        vipcomReturnPoItem.setAmtList(BigDecimal.ZERO);
        vipcomReturnPoItem.setPoNo(vipReturnOrderItemEx.getPoNo());
        vipcomReturnPoItem.setBoxNo(vipReturnOrderItemEx.getBoxNo());
        vipcomReturnPoItems.add(vipcomReturnPoItem);
    }

    /**
     * 构建系统匹配条码明细
     *
     * @param vipSkuCode
     * @param systemSkuCode
     * @param qty
     * @param vipcomReturnPoSysmatchItems
     */
    private void buildVipcomReturnPoSysmatchItem(String vipSkuCode, String systemSkuCode, BigDecimal qty,
                                                 List<OcBVipcomReturnPoSysmatchItemDO> vipcomReturnPoSysmatchItems) {
        OcBVipcomReturnPoSysmatchItemDO vipcomReturnPoSysmatchItem = new OcBVipcomReturnPoSysmatchItemDO();
        vipcomReturnPoSysmatchItem.setVipSkuEcode(vipSkuCode);
        vipcomReturnPoSysmatchItem.setPsCSkuEcode(systemSkuCode);
        vipcomReturnPoSysmatchItem.setQty(qty);
        vipcomReturnPoSysmatchItems.add(vipcomReturnPoSysmatchItem);
    }

}
