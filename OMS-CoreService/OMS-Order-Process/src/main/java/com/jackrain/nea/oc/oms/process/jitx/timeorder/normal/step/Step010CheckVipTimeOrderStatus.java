package com.jackrain.nea.oc.oms.process.jitx.timeorder.normal.step;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TimeOrderOccupyItemStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.TimeOrderVipStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpVipTimeOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTimeOrderVip;
import com.jackrain.nea.oc.oms.model.table.IpBTimeOrderVipOccupyItem;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 判断时效订单单据状态
 * @Date 2019-9-2
 **/
@Step(order = 10, description = "判断时效订单单据状态")
@Slf4j
@Component
public class Step010CheckVipTimeOrderStatus extends BaseVipTimeOrderProcessStep
        implements IOmsOrderProcessStep<IpVipTimeOrderRelation> {

    @Override
    public ProcessStepResult<IpVipTimeOrderRelation> startProcess(IpVipTimeOrderRelation timeOrderInfo
            , ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        //判断订单是否为空
        if (timeOrderInfo == null || timeOrderInfo.getIpBTimeOrderVip() == null) {
            return new ProcessStepResult<>(StepStatus.FAILED, "时效订单为空，退出时效订单转单！");
        }
        IpVipTimeOrderRelation orderInfo = this.ipVipTimeOrderService.selectTimeOrder(timeOrderInfo.getOccupiedOrderSn());
        if (orderInfo == null || orderInfo.getIpBTimeOrderVip() == null) {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("重新查询时效订单或明细为空，退出时效订单转单！timeOrderInfo:{}",
                        "合单剔除原因", timeOrderInfo.getOrderSn()), JSON.toJSONString(timeOrderInfo));
            }
            return new ProcessStepResult<>(StepStatus.FAILED, "时效订单为空，退出时效订单转单！");
        }
        timeOrderInfo.setIpBTimeOrderVip(orderInfo.getIpBTimeOrderVip());
        timeOrderInfo.setIpBTimeOrderVipOccupyItemList(orderInfo.getIpBTimeOrderVipOccupyItemList());
        timeOrderInfo.setIpBTimeOrderVipItemExList(orderInfo.getIpBTimeOrderVipItemExList());
        timeOrderInfo.setCphyWarehouseId(orderInfo.getCphyWarehouseId());
        timeOrderInfo.setCancelTimeOrderVip(orderInfo.getCancelTimeOrderVip());
        IpBTimeOrderVip timeOrderVip = timeOrderInfo.getIpBTimeOrderVip();
        // 时效订单库存占用明细
        List<IpBTimeOrderVipOccupyItem> orderVipOccupyItemList = timeOrderInfo.getIpBTimeOrderVipOccupyItemList();
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("orderVipOccupyItemList:{}",
                    "orderVipOccupyItemList", timeOrderInfo.getOrderSn()), JSON.toJSONString(orderVipOccupyItemList));
        }
        //转换状态
        int isTrans = timeOrderVip.getIstrans();
        boolean notTransferAndCreateFlag = TimeOrderVipStatusEnum.CREATED.getValue().equals(timeOrderVip.getStatus());
        boolean transferAndOutStockFlag = TimeOrderVipStatusEnum.OUT_STOCK.getValue().equals(timeOrderVip.getStatus());
        /*if (isTrans == TransferOrderStatus.TRANSFERRED.toInteger() && !transferAndOutStockFlag) {
            //修改中间表系统备注
            ipVipTimeOrderService.updateTimeOrderTransferStatus(timeOrderVip.getIstrans()
                    , "时效订单已转换，退出转换！", timeOrderVip);
            return new ProcessStepResult<>(StepStatus.FAILED
                    , "时效订单已转换，退出转换！");
        }*/
        //单据状态为：待占单，转换状态为：未转换、转换失败、转换异常的才能进行转单
        List<Integer> transStatus = new ArrayList<>();
        transStatus.add(TransferOrderStatus.NOT_TRANSFER.toInteger());
        transStatus.add(TransferOrderStatus.TRANSFERFAIL.toInteger());
        transStatus.add(TransferOrderStatus.TRANSFEREXCEPTION.toInteger());
        if(!transStatus.contains(isTrans) || !TimeOrderVipStatusEnum.CREATED.getValue().equals(timeOrderVip.getStatus())){
            return new ProcessStepResult<>(StepStatus.FAILED, "时效订单:"+timeOrderInfo.getBillNo()+"转换状态或者单据状态不满足，不能进行转换！");
        }
        //判断转换状态
        try {
            /*if (notTransferAndCreateFlag || transferAndOutStockFlag) {
            }*/
            List<IpBTimeOrderVipOccupyItem> filterOrderVipOccupyItemList = orderVipOccupyItemList.stream()
                    .filter(obj -> TimeOrderOccupyItemStatusEnum.NOT_CONFIRM.getValue() == obj.getStatus()
                            || TimeOrderOccupyItemStatusEnum.OUT_OF_STOCK.getValue() == obj.getStatus())
                    .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(filterOrderVipOccupyItemList)) {
                timeOrderInfo.setIpBTimeOrderVipOccupyItemList(filterOrderVipOccupyItemList);
                return new ProcessStepResult<>(StepStatus.SUCCESS, "单据" + timeOrderInfo.getBillNo()
                        + "判断时效订单库存占用明细数据成功，进入下一阶段！");
            } else {
                if (log.isDebugEnabled()) {
                    log.debug("时效订单没有库存占用明细，退出转换，订单ID：{}", timeOrderVip.getId());
                }
                //修改中间表系统备注
                ipVipTimeOrderService.updateTimeOrderTransferStatus(TransferOrderStatus.TRANSFERFAIL.toInteger()
                        , "时效订单没有库存占用明细，转换失败！", timeOrderVip);
                return new ProcessStepResult<>(StepStatus.FAILED
                        , "时效订单没有库存占用明细，退出转换！");
            }
            /*if (log.isDebugEnabled()) {
                log.debug("时效订单状态不是待占单或者缺货，退出转换，订单ID：{}",
                        timeOrderVip.getId());
            }
            //修改中间表系统备注
            ipVipTimeOrderService.updateTimeOrderTransferStatus(TransferOrderStatus.TRANSFERRED.toInteger()
                    , "时效订单状态不是待占单或者缺货，标记为已转换！", timeOrderVip);
            return new ProcessStepResult<>(StepStatus.FAILED
                    , "时效订单状态不是待占单或者缺货，标记为已转换！");*/
        } catch (Exception e) {
            //1：业务数据异常 2：系统程序异常
            timeOrderVip.setExceptionType("2");
            log.error(LogUtil.format("订单转换异常:{}", "订单转换异常", timeOrderInfo.getOrderSn()), Throwables.getStackTraceAsString(e));
            //修改中间表状态及系统备注
            ipVipTimeOrderService.updateIsTransException(timeOrderVip, e.getMessage());
            String errorMessage = "订单转换异常！" + e.getMessage();
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
    }
}
