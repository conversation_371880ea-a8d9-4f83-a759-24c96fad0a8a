package com.jackrain.nea.oc.oms.task.jitxorder;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.services.task.AutoJitxYyUpdateBillsTaskService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @create 2021/12/22 14:44
 * <p>
 * 捞取经销商任务表 类型4 状态为失败或未处理 更新单据信息
 */
@Slf4j
@Component
public class AutoJitxYyUpdateBillsTask extends BaseR3Task implements IR3Task {


    @Autowired
    private AutoJitxYyUpdateBillsTaskService autoJitxYyUpdateBillsTaskService;

    @Override
    @XxlJob("AutoJitxYyUpdateBillsTask")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult result = new RunTaskResult();
        try {
            autoJitxYyUpdateBillsTaskService.execute();
            result.setSuccess(true);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error("AutoJitxYyUpdateBillsTask execute Error", ex);
            result.setSuccess(false);
            result.setMessage(ex.getMessage());
        }
        return result;
    }
}
