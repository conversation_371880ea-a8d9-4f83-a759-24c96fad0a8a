package com.jackrain.nea.oc.oms.es;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.web.face.User;

import static com.jackrain.nea.resource.OcElasticSearchIndexResources.OC_B_RETURN_ORDER_LOG_INDEX_NAME;
import static com.jackrain.nea.resource.OcElasticSearchIndexResources.OC_B_RETURN_ORDER_LOG_TYPE_NAME;

/**
 * <AUTHOR>
 * @date 2020/11/11 4:46 下午
 * @description
 * @since version -1.0
 */
public class ES4ReturnOrderLog {

    /**
     * 查ES 根据message日志内容查询ids
     *
     */
    public static JSONObject findJSONObjectByMessage(String message) {
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("LOG_MESSAGE", message);
        JSONObject search = ElasticSearchUtil.searchExact(OC_B_RETURN_ORDER_LOG_INDEX_NAME, OC_B_RETURN_ORDER_LOG_TYPE_NAME,
                whereKeys, null, null, 1000, 0, new String[]{"ID"});
        return search;
    }
}
