package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.api.OrderReturnRecallFromWmsServiceCmd;
import com.jackrain.nea.oc.oms.services.OrderReturnRecallFromWmsService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: 郑立轩
 * @since: 2019/5/15
 * create at : 2019/5/15 16:36
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OrderReturnRecallFromWmsCmdImpl extends CommandAdapter implements OrderReturnRecallFromWmsServiceCmd {
    @Autowired
    private OrderReturnRecallFromWmsService service;

    @Override
    public ValueHolderV14 OrderReturnRecallFromWms(JSONObject object, User user) {
        return service.orderReturnRecallFromWms(object, user);
    }

}
