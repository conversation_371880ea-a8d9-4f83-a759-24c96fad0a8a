package com.jackrain.nea.oc.oms.process.transfer.impl.normal.standplat.step;

import cn.hutool.core.util.ObjectUtil;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpStandplatOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.OcBOrderUpdateAddressService;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.services.StCRemarkGiftStrategyService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 卖家备注信息是否一致，不一致更新卖家备注信息
 *
 * @author: ming.fz
 * @since: 2019-07-11
 * create at : 2019-07-11
 */
@Step(order = 70, description = "卖家备注信息是否一致，不一致更新卖家备注信息")
@Component
@Slf4j
public class Step070StandplatUpdateSellerRemark extends BaseStandplatOrderProcessStep implements IOmsOrderProcessStep<IpStandplatOrderRelation> {

    @Autowired
    private OmsOrderService orderService;
    @Autowired
    private OcBOrderUpdateAddressService ocBOrderUpdateAddressService;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private StCRemarkGiftStrategyService stCRemarkGiftStrategyService;

    private static final String ERROR_MSG = "状态异常";

    @SuppressWarnings("unchecked")
    @Override
    public ProcessStepResult<IpStandplatOrderRelation> startProcess(IpStandplatOrderRelation orderInfo,
                                                                    ProcessStepResult preStepResult,
                                                                    boolean isAutoMakeup, User operateUser) {
        ProcessStepResult<IpStandplatOrderRelation> stepResult = new ProcessStepResult<>();

        if (preStepResult.getNextStepOperateObj() != null) {
            boolean isUpdateRemark = false;
            try {
                List<OcBOrder> afterTransferOrderList = (List<OcBOrder>) preStepResult.getNextStepOperateObj();
                for (OcBOrder afterTransferOrder : afterTransferOrderList) {
                    if (afterTransferOrder == null) {
                        continue;
                    }

                    String beforeMemo = "";
                    String beforeStatus = "";
                    if (orderInfo != null && orderInfo.getStandplatOrder() != null) {
                        beforeMemo = orderInfo.getStandplatOrder().getSellerMemo();
                        beforeStatus = orderInfo.getStandplatOrder().getStatus();
                    }
                    String transferMemo = afterTransferOrder.getSellerMemo();
                    String transferStatus = afterTransferOrder.getPlatformStatus();
                    if (!StringUtils.equalsIgnoreCase(beforeMemo, transferMemo)
                            || !StringUtils.equalsIgnoreCase(beforeStatus, transferStatus)) {
                        OcBOrder ocBOrder = new OcBOrder();
                        ocBOrder.setId(afterTransferOrder.getId());
                        ocBOrder.setSellerMemo(beforeMemo);
                        ocBOrder.setPlatformStatus(beforeStatus);
                        this.orderService.updateOrderInfo(ocBOrder);
                        if (!StringUtils.equalsIgnoreCase(beforeMemo, transferMemo)) {
                            // 增加操作日志
                            omsOrderLogService.addUserOrderLog(afterTransferOrder.getId(), afterTransferOrder.getBillNo(), OrderLogTypeEnum.SELLERMEMO_UPDATE.getKey(),
                                    "修改卖家备注为:" + beforeMemo,
                                    "", null, SystemUserResource.getRootUser());
                        }
                        isUpdateRemark = true;
                    }
                }

                //卖家备注送赠品
                if (isUpdateRemark) {
                    stCRemarkGiftStrategyService.addGiftSku(orderInfo.getStandplatOrder().getCpCShopId(), orderInfo.getOrderNo());
                }

                //更新买家收货信息
                boolean isReturn = handleAddress(orderInfo, afterTransferOrderList, operateUser);
                if (isReturn) {
                    stepResult.setMessage("更新地址失败");
                    stepResult.setStatus(StepStatus.FINISHED);
                    return stepResult;
                }
            } catch (Exception ex) {
                log.error(LogUtil.format("Step070UpdateSellerRemark StartProcess Error:{}", "Step070UpdateSellerRemark"), Throwables.getStackTraceAsString(ex));

            }

            stepResult.setMessage("更新卖家备注信息成功，进入下一阶段");
        } else {
            stepResult.setMessage("未进行更新卖家备注信息，进入下一阶段");
        }
        stepResult.setNextStepClass(Step080StandplatUpdateOrderTransferStatus.class);
        stepResult.setStatus(StepStatus.SUCCESS);
        return stepResult;
    }

    private boolean handleAddress(IpStandplatOrderRelation orderInfo, List<OcBOrder> afterTransferOrderList, User operateUser) {
        StringBuffer remark = new StringBuffer();
        Boolean hasDefaultStatus = Boolean.FALSE;
        for (OcBOrder afterTransferOrder : afterTransferOrderList) {
            if (afterTransferOrder == null) {
                continue;
            }
            Integer isManualAddr = afterTransferOrder.getIsManualAddr();
            if (isManualAddr != null && isManualAddr == 1) {
                continue;
            }
            if (ObjectUtil.isNotNull(afterTransferOrder.getOrderStatus()) && ObjectUtil.equal(afterTransferOrder.getOrderStatus(), OmsOrderStatus.ORDER_DEFAULT.toInteger())) {
                hasDefaultStatus = Boolean.TRUE;
            }
            //零售发货单状态不为取消或作废状态
            if (checkOrderForModifyAddr(afterTransferOrder)) {
                //更新买家收货信息
                ValueHolderV14 valueHolderV14 = ocBOrderUpdateAddressService.updateOmsOrderReceiveInfo(orderInfo, afterTransferOrder, operateUser);
                if (!valueHolderV14.isOK()) {
                    remark.append(valueHolderV14.getMessage());
                    remark.append(",");
                }
            }
        }
        if (remark.length() != 0) {
            //去掉备注最后一个逗号
            String sub = remark.substring(0, remark.length() - 1);
            String remarkStr = StringUtils.substring(sub, 0, 300);
            if (remarkStr.contains(ERROR_MSG) && hasDefaultStatus) {
                this.ipStandplatOrderService.updateStandPlatOrderTransStatus(orderInfo.getOrderNo(), TransferOrderStatus.NOT_TRANSFER, remarkStr, null);
            } else {
                this.ipStandplatOrderService.updateStandPlatOrderTransStatus(orderInfo.getOrderNo(), TransferOrderStatus.TRANSFERRED, remarkStr, null);
            }
            return true;
        }
        return false;
    }

    /**
     * 修改地址支持 正常+预售的订单
     */
    private boolean checkOrderForModifyAddr(OcBOrder order) {
        if(OmsOrderStatus.SYS_VOID.toInteger().equals(order.getOrderStatus()) ||
                OmsOrderStatus.CANCELLED.toInteger().equals(order.getOrderStatus())){
            return  false;
        }
        if("手工新增".equals(order.getOrderSource())){
            return  false;
        }
        return (order.getOrderType() == 1
                || Objects.equals(order.getOrderType(), OrderTypeEnum.TBA_PRE_SALE.getVal()));
    }
}
