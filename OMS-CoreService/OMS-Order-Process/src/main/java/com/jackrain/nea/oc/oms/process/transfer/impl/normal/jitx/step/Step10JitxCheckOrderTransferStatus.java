package com.jackrain.nea.oc.oms.process.transfer.impl.normal.jitx.step;

import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpJitxOrderRelation;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 判断订单单据转换状态
 *
 * @author: 黄超
 * @since: 2019-06-25
 * create at : 2019-06-25 19:00
 */
@Step(order = 10, description = "判断订单单据转换状态")
@Slf4j
@Component
public class Step10JitxCheckOrderTransferStatus extends BaseJitxOrderProcessStep
        implements IOmsOrderProcessStep<IpJitxOrderRelation> {

    @Override
    public ProcessStepResult<IpJitxOrderRelation> startProcess(IpJitxOrderRelation orderInfo,
                                                               ProcessStepResult preStepResult,
                                                               boolean isAutoMakeup, User operateUser) {
        if (orderInfo == null || orderInfo.getJitxOrder() == null) {
            return new ProcessStepResult<>(StepStatus.FAILED, "OrderInfo为空或者Order.JitxOrder为空；退出转换");
        }
        IpJitxOrderRelation orderInfo2 = ipJitxOrderService.selectJitxOrder(orderInfo.getOrderNo());
        if (orderInfo2 == null || orderInfo2.getJitxOrder() == null) {
            return new ProcessStepResult<>(StepStatus.FAILED, "orderInfo2为空或者Order.JitxOrder为空；退出转换");
        }
        orderInfo.setJitxOrder(orderInfo2.getJitxOrder());
        orderInfo.setJitxOrderItemList(orderInfo2.getJitxOrderItemList());
        orderInfo.setRemarks(orderInfo2.getRemarks());
        orderInfo.setCpCPhyWarehouse(orderInfo2.getCpCPhyWarehouse());
        orderInfo.setLogisticsInfo(orderInfo2.getLogisticsInfo());
        int currentStatus = orderInfo.getJitxOrder().getIstrans();
        if (TransferOrderStatus.TRANSFERRED.toInteger() == currentStatus) {
            String operateMessage = Resources.getMessage("单据" + orderInfo.getOrderId() + "状态=已转换，转换完成");
            return new ProcessStepResult<>(StepStatus.FINISHED, operateMessage);
        } else if (TransferOrderStatus.TRANSFERRING.toInteger() == currentStatus) {
            String operateMessage = Resources.getMessage("单据" + orderInfo.getOrderId() + "正在转换中");
            return new ProcessStepResult<>(StepStatus.FINISHED, operateMessage);
        } else {
            String operateMessage = Resources.getMessage("单据" + orderInfo.getOrderId() + "检查状态成功，进入下一阶段");
            return new ProcessStepResult<>(StepStatus.SUCCESS, operateMessage);
        }
    }
}
