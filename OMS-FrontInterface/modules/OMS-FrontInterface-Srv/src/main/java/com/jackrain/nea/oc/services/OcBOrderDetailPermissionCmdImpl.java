package com.jackrain.nea.oc.services;

import com.jackrain.nea.oc.oms.api.OcBOrderDetailPermissionCmd;
import com.jackrain.nea.oc.oms.model.relation.UserPermission;
import com.jackrain.nea.oc.oms.services.OcBOrderDetailPermissionService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: xiWen.z
 * create at: 2019/8/28 0028
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBOrderDetailPermissionCmdImpl implements OcBOrderDetailPermissionCmd {

    @Autowired
    private OcBOrderDetailPermissionService ocBOrderDetailPermissionService;

    @Override
    public ValueHolderV14 getOrderDetailPermission(String tableName, User usr, UserPermission pem) {
        return ocBOrderDetailPermissionService.getSingleObjectPermission(tableName, usr, pem);
    }
}
