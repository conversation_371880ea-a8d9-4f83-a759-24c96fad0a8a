package com.jackrain.nea.oc.oms.api.ac;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

/**
 * 供OC调用的RPC接口
 *
 * @author: 陈俊明
 * @since: 2019-04-30
 * @create at : 2019-04-30 8:43
 */
public interface OmsPayableAdjustDropCopyCmd {
    /**
     * 全渠道订单丢单复制 生成应付调整单逻辑
     *
     * @param jsonObject
     * @return
     */
    ValueHolderV14 insertPayableAdjustDropCopy(JSONObject jsonObject, User user);
}
