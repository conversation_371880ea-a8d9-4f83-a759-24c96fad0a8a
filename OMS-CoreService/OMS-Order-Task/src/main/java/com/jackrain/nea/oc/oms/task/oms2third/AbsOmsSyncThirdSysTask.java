//package com.jackrain.nea.oc.oms.task.oms2third;
//
//import com.alibaba.fastjson.JSONObject;
//import com.google.common.base.Throwables;
//import com.jackrain.nea.config.PropertiesConf;
//import com.jackrain.nea.exception.NDSException;
//import com.jackrain.nea.oc.oms.model.relation.TaskParam;
//import com.jackrain.nea.oc.oms.sap.Oms2SapDBMetaCache;
//import com.jackrain.nea.oc.oms.sap.Oms2SapMapper;
//import com.jackrain.nea.oc.oms.sap.Oms2SapStatusEnum;
//import com.jackrain.nea.oc.oms.task.BaseR3Task;
//import com.jackrain.nea.r3.mq.exception.SendMqException;
//import com.jackrain.nea.r3.mq.util.R3MqSendHelper;
//import com.jackrain.nea.task.IR3Task;
//import com.jackrain.nea.task.RunTaskResult;
//import com.jackrain.nea.util.ApplicationContextHandle;
//import com.jackrain.nea.utility.ExceptionUtil;
//import com.jackrain.nea.utility.LogUtil;
//import com.xxl.job.core.handler.annotation.XxlJob;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
//import org.springframework.transaction.annotation.Transactional;
//
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Map;
//import java.util.concurrent.Callable;
//import java.util.concurrent.Future;
//import java.util.function.Function;
//import java.util.function.Predicate;
//
///**
// * @Desc : oms 同步第三方 (--公共类里禁止再写定制化的业务代码)
// * <AUTHOR> xiWen
// * @Date : 2020/8/14
// */
//@Slf4j
//public abstract class AbsOmsSyncThirdSysTask<T> extends BaseR3Task implements IR3Task {
//
//    @Autowired
//    protected R3MqSendHelper r3MqSendHelper;
//
//    @Autowired
//    protected Oms2SapMapper oms2SapMapper;
//
//    @Autowired
//    protected OmsSyncThirdSysConfig omsSyncThirdSysConfig;
//
//    @Autowired
//    private ThreadPoolTaskExecutor nextTaoReturnTaskrThreadPoolExecutor;
//
//    protected String nameSpaceKey = "lts";
//
//    private final String statusKey = "omsSyncThirdSysTask.common.query.status";
//
//    private final String eachSizeKey = "omsSyncThirdSysTask.common.query.eachSize";
//
//    /**
//     * thread pool config. default
//     */
//    private final int corePoolSize = 16;
//    private final int maxPoolSize = 32;
//    private final long keepAliveThreadTime = 60;
//
//    /**
//     * tog.mq
//     *
//     * @return tag
//     */
//    protected abstract String getTag();
//
//    /**
//     * topic.mq
//     *
//     * @return topic
//     */
//    protected abstract String getTopic();
//
//    /**
//     * task order table
//     *
//     * @return auto task order name
//     */
//    protected abstract String getTaskTableName();
//
//
//    /**
//     * task order column
//     *
//     * @return current operate column
//     */
//    protected abstract String getTaskStatus();
//
//    /**
//     * task  order type column
//     *
//     * @return order belongs to which system
//     */
//    protected abstract String getTaskType();
//
//    /**
//     * task order type value
//     *
//     * @return
//     */
//    protected abstract int getTaskTypeVal();
//
//    /**
//     * origin order prev modify column
//     *
//     * @return origin order column
//     */
//    protected abstract String getOriginCol();
//
//    /**
//     * target  order table name
//     *
//     * @return origin table name
//     */
//    protected abstract String getOriginTableName();
//
//    /**
//     * current thread pool name
//     *
//     * @return thread pool name
//     */
//    protected abstract String getThreadPoolName();
//
//    /**
//     * current target order operator
//     *
//     * @return order data
//     */
//    protected abstract List<T> getSyncData(TaskParam taskParam);
//
//    protected abstract String convert2String(List<T> list);
//
//    /**
//     * assert value
//     */
//    protected Predicate<List> unExpect = objects -> {
//        return objects == null || objects.size() == 0;
//    };
//
//    @Override
//    @XxlJob("AbsOmsSyncThirdSysTask")
//    public RunTaskResult execute(JSONObject params) {
//
//        RunTaskResult result = new RunTaskResult();
//        long start = System.currentTimeMillis();
//
//        try {
//            TaskParam taskParam = initTaskParam();
//            List<Future<Boolean>> results = new ArrayList<>();
//            for (String node : tplMap.keySet()) {
//                results.add(nextTaoReturnTaskrThreadPoolExecutor.submit(new OmsToThirdCallable(taskParam)));
//            }
//
//            for (Future<Boolean> futureResult : results) {
//                try {
//                    log.debug(LogUtil.format("Result {}", "同步第三方"), futureResult.get().toString());
//                } catch (Exception e) {
//                    log.error(LogUtil.format("同步第三方异常：{}", "同步第三方"), Throwables.getStackTraceAsString(e));
//                }
//            }
//
//            long end = System.currentTimeMillis();
//            result.setSuccess(true);
//            result.setMessage("Task Execute Finished, UseTime: " + (end - start));
//        } catch (Exception e) {
//            log.error(LogUtil.format("同步第三方异常：{}", "同步第三方"), Throwables.getStackTraceAsString(e));
//            result.setSuccess(false);
//            result.setMessage(e.getMessage());
//        }
//        return result;
//    }
//
//    /**
//     * apollo config task each size
//     *
//     * @return task size
//     */
//    protected int getTaskEachSize() {
//        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
//        Integer taskSize = config.getProperty(eachSizeKey, 1000);
//        return taskSize;
//    }
//
//    /**
//     * apollo config
//     * deal current order status
//     *
//     * @return order status
//     */
//    protected int getTaskStatusVal() {
//        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
//        Integer orderStatus = config.getProperty(statusKey, 0);
//        return orderStatus;
//    }
//
//    /**
//     * deal order size each time
//     *
//     * @return default 200
//     */
//    protected int getBatchEachSize() {
//        return 200;
//    }
//
//    /**
//     * init param
//     *
//     * @return taskParam
//     */
//    protected TaskParam initTaskParam() {
//        return new TaskParam()
//                .setTaskTypeCol(this.getTaskType()).setTaskTypeVal(this.getTaskTypeVal())
//                .setTaskStatusCol(this.getTaskStatus()).setTaskStatusVal(this.getTaskStatusVal())
//                .setTaskTableName(this.getTaskTableName())
//                .setLimit(this.getTaskEachSize())
//                .setOrigTableName(getOriginTableName())
//                .setOrigStatusCol(getOriginCol());
//    }
//
//    /**
//     * get thread pool
//     * override for pool settings
//     *
//     * @return thread pool
//     */
//    protected ThreadPoolTaskExecutor getThreadPool(String poolName) {
//       /* return OmsThreadPoolFactory.getOmsSyncThirdPool(poolName, corePoolSize, maxPoolSize, 16,
//                keepAliveThreadTime);*/
//        // 当前只有销售退实现类, 如果实现类增加, 使用  OMSThreadPoolFactory.getTaskPoolByName(poolName);
//        return nextTaoReturnTaskrThreadPoolExecutor;
//    }
//
//
//    /**
//     * send mq to sap
//     * update order status
//     *
//     * @param thdName current thread name
//     * @return bool
//     */
//    private boolean batchUpdateAndSendThirdSys(String thdName, TaskParam taskParam) {
//
//        long l = System.currentTimeMillis();
//        taskParam.setValidKeys(new ArrayList<>());
//        int step = 1;
//
//        try {
//
//            List<T> items = getSyncData(taskParam);
//            if (unExpect.test(taskParam.getValidKeys())) {
//                taskParam.setValidKeys(taskParam.getKeys());
//            }
//            if (unExpect.test(items)) {
//                step = 2;
//                taskParam.setTaskStatusVal(Oms2SapStatusEnum.PROCESSED.val());
//                int taskFail = oms2SapMapper.updateTaskOrder(taskParam);
//                // todo 原单暂记3,  后期为4
//                int orderFail = oms2SapMapper.updateDynamicOrigOrder(getOriginTableName(), getOriginCol(),
//                        Oms2SapStatusEnum.FAILED.val(), taskParam.getValidKeys());
//                return false;
//            }
//
//            step = 3;
//            AbsOmsSyncThirdSysTask bean = ApplicationContextHandle.getBean(this.getClass());
//            bean.batchSendSap(thdName, items, taskParam);
//
//            step = 4;
//
//            if (taskParam.getInvalidKeys().size() > 0) {
//                taskParam.setValidKeys(taskParam.getInvalidKeys());
//                taskParam.setTaskStatusVal(Oms2SapStatusEnum.PROCESSED.val());
//                oms2SapMapper.updateTaskOrder(taskParam);
//            }
//            return true;
//        } catch (Exception e) {
//            switch (step) {
//                case 1:
//                    logError("{} Step02-1 BatchDeal Search And Convert Origin Order Exception,ids-{}", thdName, taskParam.getKeys());
//                    break;
//                case 2:
//                    logError("{} Step02-2 BatchDeal Empty OriginOrder, Update Task Exception,ids-{}", thdName, taskParam.getKeys());
//                    break;
//                case 3:
//                    logError("{} Step02-3 BatchDeal Update Task Order,Send MQ Exception,ids-{}", thdName, taskParam.getValidKeys());
//                    break;
//                case 4:
//                    logError("{} Step02-4 Deal Void Task Order Exception,ids-{}", thdName, taskParam.getKeys());
//                    break;
//                default:
//                    logError("{} Step02-x BatchDeal Default Exception,ids-{}", thdName, taskParam.getKeys());
//                    break;
//            }
//            logError(ExceptionUtil.getMessage(e));
//        }
//        return false;
//    }
//
//
//    /**
//     * update task table  and  send mq to the third system
//     *
//     * @param thdName
//     * @param items
//     * @param taskParam
//     */
//    @Transactional(rollbackFor = NDSException.class)
//    public void batchSendSap(String thdName, List<T> items, TaskParam taskParam) {
//
//        try {
//
//            taskParam.setTaskStatusVal(Oms2SapStatusEnum.SUCCESS.val());
//            int task = oms2SapMapper.updateTaskOrder(taskParam);
//            int order = oms2SapMapper.updateDynamicOrigOrder(getOriginTableName(), getOriginCol(),
//                    Oms2SapStatusEnum.SUCCESS.val(), taskParam.getValidKeys());
//
//            String msgId = r3MqSendHelper.sendMessage(convert2String(items), getTopic(), getTag());
//
//        } catch (SendMqException e) {
//            throw new NDSException("Send MQ To Sap Exception");
//        }
//    }
//
//    /**
//     * inner thread class
//     */
//    class OmsToThirdCallable implements Callable<Boolean> {
//
//        private final TaskParam taskParam;
//
//        public OmsToThirdCallable(TaskParam taskParam) {
//            this.taskParam = taskParam;
//        }
//
//        @Override
//        public Boolean call() throws Exception {
//
//            long t1 = System.currentTimeMillis();
//            String threadName = Thread.currentThread().getName();
//
//            logDebug("{} Step0 Task Start TaskParam-{}", threadName, taskParam.toString());
//            List<Long> odrIds = oms2SapMapper.selectTaskOrder(taskParam);
//            if (CollectionUtils.isEmpty(odrIds)) {
//                return true;
//            }
//            int count = odrIds.size(), length, time = 0, startIndex;
//            List<Long> subList;
//            startIndex = 0;
//            length = count;
//            while (count > 0) {
//
//                if (count > getBatchEachSize()) {
//                    subList = odrIds.subList(startIndex, startIndex + getBatchEachSize());
//                    startIndex += getBatchEachSize();
//                } else {
//                    subList = odrIds.subList(startIndex, length);
//                }
//
//                boolean eachTime = batchUpdateAndSendThirdSys(threadName, taskParam.setKeys(subList));
//
//                count -= getBatchEachSize();
//            }
//
//            long t2 = System.currentTimeMillis();
//            return true;
//        }
//    }
//
//
//    /**
//     * get order id function
//     */
//    protected Function<Object, Long> orderIdFun;
//
//    /**
//     * statistics active order id
//     *
//     * @param objects   order
//     * @param activeIds id
//     */
//    protected void statisticsCsm(List<? extends Object> objects, List<Long> activeIds) {
//        for (Object o : objects) {
//            if (o == null) {
//                continue;
//            }
//            activeIds.add(orderIdFun.apply(o));
//        }
//    }
//
//    /**
//     * @param list list
//     * @return string
//     */
//    protected String joinList2String(List<Long> list) {
//
//        StringBuilder sb = new StringBuilder();
//        for (int i = 0, l = list.size(); i < l; i++) {
//            sb.append(",").append(list.get(i));
//        }
//        return sb.substring(1);
//    }
//
//
//    /**
//     * level debug recorder
//     *
//     * @param msg log message
//     */
//    protected void logDebug(String msg, Object... params) {
//
//        if (log.isDebugEnabled()) {
//            if (params.length == 0) {
//                log.debug(new StringBuilder(this.getClass().getSimpleName()).append(".").append(msg).toString());
//            } else {
//                log.debug(new StringBuilder(this.getClass().getSimpleName()).append(".").append(msg).toString(), params);
//            }
//        }
//    }
//
//    /**
//     * level error recorder
//     *
//     * @param msg    message or format string
//     * @param params print values
//     */
//    protected void logError(String msg, Object... params) {
//
//        if (params.length == 0) {
//            log.error(new StringBuilder(this.getClass().getSimpleName()).append(".").append(msg).toString());
//        } else {
//            log.error(new StringBuilder(this.getClass().getSimpleName()).append(".").append(msg).toString(), params);
//        }
//    }
//
//
//}
