package com.jackrain.nea.oc.oms.mq.processor.impl.sgMq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.burgeon.mq.error.MqException;
import com.google.common.base.Throwables;
import com.jackrain.nea.ip.model.result.OrderCreateBaseResult;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * create at : 2022-08-18 18:08
 * @ description:  SG入库通知单传WMS结果回传 退换货单
 */

// fixme tag:sg_to_oms_wms_in_creat_receipt

@Component
@Slf4j
//@RocketMqMessageListener(name = "ReturnOrderWmsCallBackMqProcessorImpl", type = MqTypeEnum.DEFAULT)
public class ReturnOrderWmsCallBackMqProcessorImpl {

    @Autowired
    private OcBReturnOrderMapper ocReturnOrderMapper;

    public void consume(String messageBody, String messageTopic, String messageKey, String messageTag, Object object) {
        log.info(LogUtil.format("入库通知单传WMS结果返回监听messageTopic={} messageKey = {} messageBody = {} messageTag = {}",
                        "ReturnOrderWmsCallBackMqProcessorImpl", messageTopic, messageKey), messageTopic, messageKey,
                messageBody, messageTag);
        try {
            JSONObject messageJson = JSONObject.parseObject(messageBody);
            List<OrderCreateBaseResult> orderCreateBaseResults =
                    JSON.parseArray(messageJson.getString("body"), OrderCreateBaseResult.class);
            User user = SystemUserResource.getRootUser();
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("入库通知单传WMS结果返回监听,orderCreateBaseResults:{}",
                        "ReturnOrderWmsCallBackMqProcessorImpl", JSON.toJSONString(orderCreateBaseResults)));
            }
            ocReturnOrderMapper.batchUpdateWmsInfoByList(orderCreateBaseResults);
        } catch (Exception e) {
            log.error(LogUtil.format("ReturnOrderWmsCallBackMqProcessorImpl.consume.ExpMsg: {}"), Throwables.getStackTraceAsString(e));
            throw new MqException(e);
        }
    }

}
