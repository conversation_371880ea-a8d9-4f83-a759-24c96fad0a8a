package com.jackrain.nea.oc.oms.mapper;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoExchange;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.jdbc.SQL;
import org.springframework.stereotype.Component;

import java.util.List;

@Mapper
@Component
public interface IpBTaobaoExchangeMapper extends ExtentionMapper<IpBTaobaoExchange> {
    @Select("SELECT * FROM ip_b_taobao_exchange WHERE dispute_id=#{orderNo} and istrans=0")
    IpBTaobaoExchange selectTaobaoExchangeByDisputeId(@Param("orderNo") String orderNo);


    @Select("SELECT * FROM ip_b_taobao_exchange WHERE dispute_id=#{orderNo}")
    IpBTaobaoExchange selectTaobaoExchangeDisputeId(@Param("orderNo") String orderNo);

    @Select("<script> "
            + "SELECT * FROM ip_b_taobao_exchange WHERE dispute_id in"
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<IpBTaobaoExchange> selectTaobaoExchangeByDisputeIdList(@Param("ids") List<Long> disputeIdList);

    /**
     * 更新状态及次数
     */
    class TaobaoExchangeSqlBuilder {
        public String buildUpdateExchangeTransSQL(@Param("id") long id, @Param("isTrans") int isTrans,
                                                  @Param("isUpdateTransNum") boolean isUpdateTransNum) {
            return new SQL() {
                {
                    UPDATE("ip_b_taobao_exchange");
                    SET("istrans=#{isTrans}");
                    if (isUpdateTransNum) {
                        SET("trans_count = IFNULL(trans_count, 0) + 1");
                    }
                    WHERE("ID=#{id}");
                }
            }.toString();
        }

        public String update(JSONObject map) {
            return new SQL() {
                {
                    UPDATE("ip_b_taobao_exchange");
                    SET("trans_count = IFNULL(trans_count, 0) + 1");
                    for (String key : map.keySet()) {
                        if (!"dispute_id".equals(key)) {
                            SET(key + "= #{" + key + "}");
                        }
                    }
                    WHERE("dispute_id = #{dispute_id}");
                }
            }.toString();
        }


        public String selectDynamicExchangeId(@Param("node") String node, @Param("name") String name,
                                              @Param("size") int size, @Param("isTrans") int isTrans,
                                              @Param("lessThanTransCnt") int lessThanTransCnt,
                                              @Param("timestamp") int timestamp,
                                              @Param("minutes") int minutes) {

            StringBuilder sb = new StringBuilder();
            sb.append("/*!TDDL:NODE=");
            sb.append(node);
            sb.append("*/ ");
            sb.append("SELECT dispute_id FROM ");
            sb.append(name);
            sb.append(" WHERE istrans = ");
            sb.append(isTrans);
            if (lessThanTransCnt > 0) {
                sb.append(" AND IFNULL(trans_count,0) < ");
                sb.append(lessThanTransCnt);
            }
            sb.append(" AND IFNULL(reserve_bigint01,0) != ");
            sb.append(timestamp);
            if (minutes > 0) {
                sb.append(" AND creationdate > (DATE_SUB(NOW(),INTERVAL ");
                sb.append(minutes);
                sb.append(" MINUTE))");
            }
            sb.append(" ORDER BY modifieddate DESC");
            sb.append(" LIMIT ");
            sb.append(size);
            return sb.toString();
        }

        public String updateDynamicExchangeTimeStamp(@Param("node") String node,
                                                     @Param("name") String name,
                                                     @Param("disputeIds") String disputeIds,
                                                     @Param("timestamp") int timestamp) {
            StringBuilder sb = new StringBuilder();
            sb.append("/*!TDDL:NODE=");
            sb.append(node);
            sb.append("*/ ");
            sb.append("UPDATE ");
            sb.append(name);
            sb.append(" SET reserve_bigint01 = ");
            sb.append(timestamp);
            sb.append(", modifieddate=NOW()");
            sb.append(" WHERE dispute_id in (");
            sb.append(disputeIds);
            sb.append(")");
            return sb.toString();

        }
    }

    /**
     * 通过dispute_id(分库键) 更新数据
     *
     * @param jsonObject 数据
     * @return
     */
    @UpdateProvider(type = TaobaoExchangeSqlBuilder.class, method = "update")
    int updateTaobaoExchange(JSONObject jsonObject);

    @Update("update ip_b_taobao_exchange set occupancy_status = #{occupancyStatus} WHERE dispute_id = #{disputeId} ")
    int updateTaobaoExchangeByDisputeId(@Param("occupancyStatus") Integer occupancyStatus, @Param("disputeId") Long disputeId);



    @SelectProvider(type = TaobaoExchangeSqlBuilder.class, method = "selectDynamicExchangeId")
    List<String> selectDynamicExchange(@Param("node") String node, @Param("name") String name,
                                       @Param("size") int size, @Param("isTrans") int isTrans,
                                       @Param("lessThanTransCnt") int lessThanTransCnt,
                                       @Param("timestamp") int timestamp, @Param("minutes") int minutes);


    @UpdateProvider(type = TaobaoExchangeSqlBuilder.class, method = "updateDynamicExchangeTimeStamp")
    int updateDynamicExchangeTimeStamp(@Param("node") String node,
                                     @Param("name") String name,
                                     @Param("disputeIds") String disputeIds,
                                     @Param("timestamp") int timestamp);
}