package com.jackrain.nea.thread;

import org.apache.commons.lang3.time.FastDateFormat;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * @author: 易邵峰
 * @since: 2019-04-22
 * create at : 2019-04-22 20:51
 */
@RestController
@RequestMapping("/p")
public class AsyncThreadController {


    /**
     * 开始异步调用
     *
     * @return
     */
    @RequestMapping("/startAsync")
    public String startAsync() {
        TestAsyncService.getInstance().start();
        return FastDateFormat.getInstance("yyyy-MM-dd HH:mm:ss:SSS").format(new Date()) + " --- OK";
    }


}
