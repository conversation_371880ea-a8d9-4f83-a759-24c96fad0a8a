package com.jackrain.nea.oc.oms.services.refund;

import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.currency.SapRefundModel;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.refund.util.NumUtil;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolderV14Utils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * sap refund
 *
 * <AUTHOR>
 */
@Component
public class SapRefundService {

    @Autowired
    private OcBOrderMapper ocBOrderMapper;


    @Autowired
    private OcBOrderItemMapper itemMapper;

    public ValueHolderV14 validateCanRefund(SapRefundModel sapRefundModel) {

        if (sapRefundModel == null || StringUtils.isBlank(sapRefundModel.getBillNo()) || sapRefundModel.getQty() == null || StringUtils.isBlank(sapRefundModel.getSku())) {
            return ValueHolderV14Utils.getFailValueHolder("参数异常,参数为空!");
        }
        Long id = ES4Order.getIdByBillNo(sapRefundModel.getBillNo());

        if (id == null) {
            return ValueHolderV14Utils.getFailValueHolder("根据订单编号查询零售发货单异常!");
        }

        OcBOrder order = ocBOrderMapper.selectById(id);

        if (order == null || OmsOrderStatus.isInvalid(order.getOrderStatus())) {
            return ValueHolderV14Utils.getFailValueHolder("零售发货单状态异常!");
        }
        List<OcBOrderItem> items = itemMapper.selectUnSuccessRefundBySku(id,sapRefundModel.getSku());

        if (CollectionUtils.isEmpty(items)) {
            return ValueHolderV14Utils.getFailValueHolder("零售发货单可用明细为空!");
        }

        BigDecimal canQty =
                items.stream().filter(item -> sapRefundModel.getSku().equals(item.getPsCSkuEcode())).map(item -> NumUtil.init(item.getQty()).subtract(NumUtil.init(item.getQtyHasReturn()))).reduce(BigDecimal.ZERO, BigDecimal::add);

        return ValueHolderV14Utils.custom(canQty.compareTo(sapRefundModel.getQty()) >= 0 ? ResultCode.SUCCESS :
                ResultCode.FAIL, "validate over", "");
    }
}
