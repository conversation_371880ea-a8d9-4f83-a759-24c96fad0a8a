package com.jackrain.nea.oc.oms.model.enums.naika;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @ClassName OmsOrderNaiKaStatusEnum
 * @Description 奶卡状态枚举
 * <AUTHOR>
 * @Date 2022/6/25 09:24
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum OmsOrderNaiKaStatusEnum {

    /**
     * 无需解冻
     */
    UN_NECESSARY(0, "无需解冻"),

    /**
     * 待解冻
     */
    FREEZE(1, "待解冻"),

    /**
     * 解冻成功
     */
    FREEZE_SUCCESS(2, "解冻成功"),

    /**
     * 解冻失败
     */
    FREEZE_FAILED(3, "解冻失败"),

    /**
     * 作废成功(零售发货单不会维护此状态)
     */
    VOID_SUCCESS(4, "作废成功"),

    /**
     * 作废失败((零售发货单不会维护此状态))
     */
    VOID_FAILED(5, "作废失败"),

    /**
     * 待作废
     */
    TO_VOID(6, "待作废"),
    ;

    private Integer status;

    private String desc;

    public static OmsOrderNaiKaStatusEnum getOrderStatusByStatus(Integer status) {
        for (OmsOrderNaiKaStatusEnum value : OmsOrderNaiKaStatusEnum.values()) {
            if (value.getStatus().equals(status)) {
                return value;
            }
        }
        return null;
    }

}
