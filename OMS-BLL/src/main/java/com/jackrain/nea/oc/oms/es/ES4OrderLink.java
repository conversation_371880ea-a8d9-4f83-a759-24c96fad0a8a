package com.jackrain.nea.oc.oms.es;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/12 1:22 下午
 */
public class ES4OrderLink {
    private ES4OrderLink() {
    }

    /**
     * 根据 syncStatus 查询全链路日志的 orderId
     *
     * @param syncStatus 全链路日志状态
     * @param pageIndex  页码
     * @param pageSize   每页显示条数
     * @return List ocBOrderId
     */
    public static List<Long> findOrderIdBySyncStatusOrderByCreation(String syncStatus, int pageIndex, int pageSize) {

        int startIndex = pageIndex * pageSize;
        if (startIndex < 0) {
            startIndex = 0;
        }

        JSONObject whereKey = new JSONObject();
        whereKey.put("SYNC_STATUS", syncStatus);
        //根据全联路日志创建时间设置排序键
        JSONArray orderKeys = new JSONArray();
        JSONObject orderKey = new JSONObject();
        orderKey.put("asc", false);
        orderKey.put("name", "CREATIONDATE");
        orderKeys.add(orderKey);

        List<Long> orderIds = new ArrayList<>();

        JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_ORDER_LINK,
                OcElasticSearchIndexResources.OC_B_ORDER_LINK_TYPE_NAME,
                whereKey, null, orderKeys, pageSize, startIndex, new String[]{"ID", "OC_B_ORDER_ID"});

        if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
            JSONArray arrayObj = search.getJSONArray("data");
            for (Object obj : arrayObj) {
                JSONObject jsonObject = (JSONObject) obj;
                Long orderId = jsonObject.getLong("OC_B_ORDER_ID");
                if (orderId != null) {
                    orderIds.add(orderId);
                }
            }
            return orderIds;
        }
        return orderIds;
    }
}
