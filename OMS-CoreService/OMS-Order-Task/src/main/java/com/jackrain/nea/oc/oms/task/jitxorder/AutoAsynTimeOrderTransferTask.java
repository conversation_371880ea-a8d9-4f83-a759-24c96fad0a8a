//package com.jackrain.nea.oc.oms.task.jitxorder;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//import com.alibaba.fastjson.parser.Feature;
//import com.burgeon.mq.core.DefaultProducerSend;
//import com.burgeon.mq.model.MqSendResult;
//import com.google.common.base.Throwables;
//import com.jackrain.nea.config.PropertiesConf;
//import com.jackrain.nea.oc.oms.config.TransferOrderMqConfig;
//import com.jackrain.nea.oc.oms.constant.MqConstants;
//import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
//import com.jackrain.nea.oc.oms.model.enums.ChannelType;
//import com.jackrain.nea.oc.oms.model.enums.OperateType;
//import com.jackrain.nea.oc.oms.model.enums.OrderType;
//import com.jackrain.nea.oc.oms.model.enums.TimeOrderVipStatusEnum;
//import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
//import com.jackrain.nea.oc.oms.model.table.IpBTimeOrderVip;
//import com.jackrain.nea.oc.oms.services.IpVipTimeOrderService;
//import com.jackrain.nea.oc.oms.task.BaseR3Task;
//import com.jackrain.nea.resource.SystemUserResource;
//import com.jackrain.nea.task.IR3Task;
//import com.jackrain.nea.task.RunTaskResult;
//import com.jackrain.nea.util.ApplicationContextHandle;
//import com.jackrain.nea.utility.LogUtil;
//import com.jackrain.nea.web.face.User;
//import com.xxl.job.core.handler.annotation.XxlJob;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.apache.commons.lang.time.DateUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
//import org.springframework.stereotype.Component;
//
//import java.util.ArrayList;
//import java.util.Date;
//import java.util.List;
//import java.util.Map;
//import java.util.Set;
//import java.util.concurrent.Callable;
//import java.util.concurrent.ExecutionException;
//import java.util.concurrent.Future;
//import java.util.function.Predicate;
//import java.util.stream.Collectors;
//
///**
// * 唯品会时效订单转单补偿任务 - 发送mq形式
// * 1、查询ES中ISSTATUS=(未转换)0，status =(新建)1的单据
// *
// * @author: chenxiulou
// * @since: 2019-09-03
// * create at : 2019-03-15 23:10
// */
//@Component
//@Slf4j
//public class AutoAsynTimeOrderTransferTask extends BaseR3Task implements IR3Task {
//
//    @Autowired
//    private IpVipTimeOrderService ipVipTimeOrderService;
//
////    @Autowired
////    private R3MqSendHelper sendHelper;
//
//    @Autowired
//    private DefaultProducerSend defaultProducerSend;
//
//    @Autowired
//    private TransferOrderMqConfig orderMqConfig;
//    @Autowired
//    private ThreadPoolTaskExecutor vipTimeOrderTaskThreadPoolExecutor;
//
//    /**
//     * apollo 配置类别
//     */
//    public static final String APPLICATION_GROUP_NAME = "lts";
//    /**
//     * apollo 配置时效订单获取最大条数的配置key
//     */
//    public static final String LTS_AUTO_ASYN_TIME_ORDER_TRANSFER_TASK_DEFAULT_MAXSIZE
//            = "lts.AutoAsynTimeOrderTransferTask.default.maxSize";
//    /**
//     * apollo 配置每次发送mq时效订单条数配置key
//     */
//    public static final String LTS_AUTO_ASYN_TIME_ORDER_TRANSFER_TASK_DEFAULT_PAGE_SIZE
//            = "lts.AutoAsynTimeOrderTransferTask.default.pageSize";
//    /**
//     * apollo 配置时效订单获取最大次数
//     */
//    public static final String LTS_AUTO_ASYN_TIME_ORDER_TRANSFER_TASK_MAX_COMPENSATION_TIME
//            = "lts.AutoAsynTimeOrderTransferTask.max.compensationTime";
//    /**
//     * apollo 非线性增长 初次时间间隔
//     */
//    public static final String LTS_AUTO_ASYN_TIME_ORDER_TRANSFER_TASK_FIRST_TIME_INTERVAL
//            = "lts.AutoAsynTimeOrderTransferTask.first.timeInterval";
//    /**
//     * 默认最大补偿次数
//     */
//    public static final int DEFAULT_MAX_COMPENSATION_TIME = 5;
//
//    /**
//     * 默认时间倍数增长-初始时间间隔 5分钟 10 20 40 80
//     */
//    public static final int DEFAULT_MAX_COMPENSATION_DATE_TIME = 5;
//
//    @Override
//    @XxlJob("AutoAsynTimeOrderTransferTask")
//    public RunTaskResult execute(JSONObject params) {
//        RunTaskResult result = new RunTaskResult();
//
//        long start = System.currentTimeMillis();
//        final String taskTableName = "ip_b_time_order_vip";
//        Map<String, String> topMap = null;
//        Set<String> nodes = topMap.keySet();
//        if (CollectionUtils.isEmpty(nodes)) {
//            log.error(LogUtil.format("AutoAsynTimeOrderTransferTask.nodes not get！"));
//            result.setSuccess(false);
//            result.setMessage("请检查环境，node获取不到！！");
//            return result;
//        }
//        try {
//            List<Future<Boolean>> results = new ArrayList<>();
//
//            for (String node : nodes) {
//                results.add(vipTimeOrderTaskThreadPoolExecutor.submit(new CallableVipTimeOrderTaskWithResult(node, topMap.get(node))));
//            }
//
//            //线程执行结果获取
//            for (Future<Boolean> futureResult : results) {
//                try {
//                    Boolean aBoolean = futureResult.get();
//                } catch (InterruptedException e) {
//                    log.error(LogUtil.format("AutoAsynTimeOrderTransferTask多线程获取InterruptedException.异常: {}"),
//                            Throwables.getStackTraceAsString(e));
//                } catch (ExecutionException e) {
//                    log.error(LogUtil.format("AutoAsynTimeOrderTransferTask多线程获取ExecutionException.异常: {}"),
//                            Throwables.getStackTraceAsString(e));
//                }
//            }
//
//            if (log.isDebugEnabled()) {
//                log.debug(LogUtil.format("AutoAsynTimeOrderTransferTask 补偿定时任务完成 useTime: {}"),
//                        (System.currentTimeMillis() - start));
//            }
//
//            result.setSuccess(true);
//        } catch (Exception e) {
//            log.error(LogUtil.format("AutoAsynTimeOrderTransferTask.Execute Error: {}"), Throwables.getStackTraceAsString(e));
//            result.setSuccess(false);
//            result.setMessage(e.getMessage());
//        }
//        return result;
//    }
//
//    public RunTaskResult getRunTaskResult() {
//        RunTaskResult result = new RunTaskResult();
//        String errorMessage;
//        Date currentDate = new Date();
//        long currentTimeMills = currentDate.getTime();
//        try {
//            PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
//            Integer defaultMaxSize = config.getProperty(LTS_AUTO_ASYN_TIME_ORDER_TRANSFER_TASK_DEFAULT_MAXSIZE
//                    , DEFAULT_PAGE_SIZE);
//            Integer defaultPageSize = config.getProperty(LTS_AUTO_ASYN_TIME_ORDER_TRANSFER_TASK_DEFAULT_PAGE_SIZE
//                    , DEFAULT_PAGE_SIZE);
//            // 最大补偿次数
//            Integer maxCompensationTime = config.getProperty(
//                    LTS_AUTO_ASYN_TIME_ORDER_TRANSFER_TASK_MAX_COMPENSATION_TIME, DEFAULT_MAX_COMPENSATION_TIME);
//            // 非线性增长初次时间间隔
//            Integer firstTimeInterval = config.getProperty(
//                    LTS_AUTO_ASYN_TIME_ORDER_TRANSFER_TASK_FIRST_TIME_INTERVAL, DEFAULT_MAX_COMPENSATION_DATE_TIME);
//
//            List<String> orderNoList = new ArrayList<>();
//            List<String> orderNoListOne = ipVipTimeOrderService.selectTimeOrderEs(0
//                    , defaultMaxSize
//                    , currentTimeMills
//                    , TransferOrderStatus.NOT_TRANSFER.toInteger()
//                    , maxCompensationTime
//                    , TimeOrderVipStatusEnum.CREATED.getValue());
//            List<String> orderNoListTwo = ipVipTimeOrderService.selectTimeOrderEs(0
//                    , defaultMaxSize
//                    , currentTimeMills
//                    , TransferOrderStatus.TRANSFERFAIL.toInteger()
//                    , maxCompensationTime
//                    , TimeOrderVipStatusEnum.CREATED.getValue());
//            orderNoList.addAll(orderNoListOne);
//            orderNoList.addAll(orderNoListTwo);
//            List<String> alearyTransferOrderNoList = ipVipTimeOrderService.selectTimeOrderEs(0
//                    , defaultMaxSize
//                    , currentTimeMills
//                    , TransferOrderStatus.TRANSFERRED.toInteger()
//                    , maxCompensationTime
//                    , TimeOrderVipStatusEnum.OUT_STOCK.getValue());
//            List<String> orderNos = new ArrayList<>();
//            if (CollectionUtils.isNotEmpty(orderNoList)) {
//                orderNos.addAll(orderNoList);
//            }
//            if (log.isDebugEnabled()) {
//                log.debug(LogUtil.format("AutoAsynTimeOrderTransferTask.orderNoList: {}"), JSON.toJSONString(orderNoList));
//            }
//            if (CollectionUtils.isNotEmpty(alearyTransferOrderNoList)) {
//                orderNos.addAll(alearyTransferOrderNoList);
//            }
//            if (log.isDebugEnabled()) {
//                log.debug(LogUtil.format("AutoAsynTimeOrderTransferTask.alearyTransferOrderNoList: {}"),
//                        JSON.toJSONString(alearyTransferOrderNoList));
//            }
//            if (CollectionUtils.isEmpty(orderNos)) {
//                errorMessage = this.getClass().getSimpleName() + ".Execute 获取数据为空";
//                log.error(LogUtil.format(errorMessage));
//                result.setSuccess(true);
//                result.setMessage(errorMessage);
//                return result;
//            }
//            orderNos = orderNos.stream().distinct().collect(Collectors.toList());
//            long totalPage;
//            long totalSize = orderNos.size();
//            if (totalSize % defaultPageSize == 0) {
//                totalPage = totalSize / defaultPageSize;
//            } else {
//                totalPage = totalSize / defaultPageSize + 1;
//            }
//            for (int i = 1; i <= totalPage; i++) {
//                List<String> tempOrderNos = orderNos.stream()
//                        .skip((i - 1) * defaultPageSize)
//                        .limit(defaultPageSize).collect(Collectors.toList());
//                List<IpBTimeOrderVip> ipBTimeOrderVips = this.ipVipTimeOrderService
//                        .listBatchTimeOrderByOccupiedOrderSns(tempOrderNos);
//
//                // 获取拣货单为空 && 次数小于最大次数
//                ipBTimeOrderVips.forEach(obj -> {
//                    Integer tempCompensationTime = obj.getCompensationTime() == null ? 0 : obj.getCompensationTime();
//                    obj.setCompensationTime(tempCompensationTime);
//                });
//                int sumUpdateRecord = 0;
//                ipBTimeOrderVips = ipBTimeOrderVips.stream()
//                        .filter(getIpBTimeOrderVipPredicate(maxCompensationTime, currentDate))
//                        .collect(Collectors.toList());
//                for (IpBTimeOrderVip vip : ipBTimeOrderVips) {
//                    User user = SystemUserResource.getRootUser();
//                    int tempCompensationTime = vip.getCompensationTime() == null ? 0 : vip.getCompensationTime();
//                    int tempAddMinutes = calculate(tempCompensationTime) * firstTimeInterval;
//                    Date addDate = DateUtils.addMinutes(currentDate, tempAddMinutes);
//                    int tempUpdateRecord = this.ipVipTimeOrderService.updateVipOrder(vip.getOccupiedOrderSn()
//                            , user, currentDate, "时效订单转单-mq", addDate, vip.getStatus(), vip.getIstrans());
//                    sumUpdateRecord = sumUpdateRecord + tempUpdateRecord;
//                }
//                if (sumUpdateRecord > 0) {
//                    this.sendMQ(ipBTimeOrderVips);
//                }
//            }
//            result.setSuccess(true);
//        } catch (Exception ex) {
//            ex.printStackTrace();
//            log.error(LogUtil.format("Execute Error.异常: {}"), Throwables.getStackTraceAsString(ex));
//            result.setSuccess(false);
//            result.setMessage(ex.getMessage());
//        }
//        return result;
//    }
//
//    private Predicate<IpBTimeOrderVip> getIpBTimeOrderVipPredicate(Integer maxCompensationTime, Date date) {
//        return obj -> "1".equals(obj.getPickNo())
//                && obj.getCompensationTime().compareTo(maxCompensationTime) < 0
//                && obj.getNextCompensationDate() != null && obj.getNextCompensationDate().compareTo(date) <= 0;
//    }
//
//    /**
//     * 2 的 n次方
//     *
//     * @param n
//     * @return
//     */
//    private static int calculate(int n) {
//        if (n <= 0) {
//            return 1;
//        }
//        return 2 * calculate(n - 1);
//    }
//
//    /**
//     * 发送MQ消息
//     *
//     * @param ipBTimeOrderVips 保存的OrderInfo订单信息
//     */
//    private void sendMQ(List<IpBTimeOrderVip> ipBTimeOrderVips) {
//        if (CollectionUtils.isNotEmpty(ipBTimeOrderVips)) {
////            String productMqTopic = orderMqConfig.getSendTransferMqTopic();
//            String productMqTopic = MqConstants.TOPIC_R3_OC_OMS_CALL_TRANSFER;
////            String productMqTag = orderMqConfig.getSendTransferMqTag();
//            String productMqTag = MqConstants.TAG_R3_OC_OMS_CALL_TRANSFER;
//            if (log.isDebugEnabled()) {
//                log.debug(LogUtil.format("AutoAsynTimeOrderTransferTask.sendMQ,mqMessageTopic:{},mqMessageTag:{}"),
//                        productMqTopic, productMqTag);
//            }
//            JSONArray sendArr = new JSONArray();
//            for (IpBTimeOrderVip item : ipBTimeOrderVips) {
//                OperateOrderMqInfo orderMqInfo = new OperateOrderMqInfo();
//                orderMqInfo.setChannelType(ChannelType.VIPJITX);
//                orderMqInfo.setOperateType(OperateType.TRANSFER_ORDER);
//                orderMqInfo.setOrderType(OrderType.TIMEORDER);
//                orderMqInfo.setOrderId(item.getId());
//                orderMqInfo.setOrderNo(item.getOccupiedOrderSn());
//                JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(orderMqInfo), Feature.OrderedField);
//                sendArr.add(jsonObject);
//            }
//            String messageId;
//
//            try {
////                messageId = sendHelper.sendMessage(sendArr
////                        , productMqTopic
////                        , productMqTag);
//                MqSendResult result = defaultProducerSend.sendTopic(productMqTopic, productMqTag, sendArr.toJSONString(), null);
//                messageId = result.getMessageId();
//                if (log.isDebugEnabled()) {
//                    log.info(LogUtil.format("AutoAsynTimeOrderTransferTask.sendMQ,messageId:{},messageData:{}"),
//                            messageId, sendArr);
//                }
//
//            } catch (Exception e) {
//                log.error(LogUtil.format("sendMQ ,messageData:{} Error{}"), sendArr, Throwables.getStackTraceAsString(e));
//            }
//        }
//    }
//
//
//    /**
//     * 开启线程类
//     */
//    class CallableVipTimeOrderTaskWithResult implements Callable<Boolean> {
//        private final String nodeName;
//
//        private final String taskTableName;
//
//        public CallableVipTimeOrderTaskWithResult(String nodeName, String taskTableName) {
//            this.nodeName = nodeName;
//            this.taskTableName = taskTableName;
//        }
//
//        @Override
//        public Boolean call() throws Exception {
//            Date currentDate = new Date();
//            PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
//            Integer defaultMaxSize = config.getProperty(LTS_AUTO_ASYN_TIME_ORDER_TRANSFER_TASK_DEFAULT_MAXSIZE
//                    , DEFAULT_PAGE_SIZE);
//            Integer defaultPageSize = config.getProperty(LTS_AUTO_ASYN_TIME_ORDER_TRANSFER_TASK_DEFAULT_PAGE_SIZE
//                    , DEFAULT_PAGE_SIZE);
//            // 最大补偿次数
//            Integer maxCompensationTime = config.getProperty(
//                    LTS_AUTO_ASYN_TIME_ORDER_TRANSFER_TASK_MAX_COMPENSATION_TIME, DEFAULT_MAX_COMPENSATION_TIME);
//            // 非线性增长初次时间间隔
//            Integer firstTimeInterval = config.getProperty(
//                    LTS_AUTO_ASYN_TIME_ORDER_TRANSFER_TASK_FIRST_TIME_INTERVAL, DEFAULT_MAX_COMPENSATION_DATE_TIME);
//            List<IpBTimeOrderVip> ipBTimeOrderVips = null;
//            long start = System.currentTimeMillis();
//            if (log.isDebugEnabled()) {
//                log.debug(LogUtil.format("AutoAsynTimeOrderTransferTask.Param.NodeName={}"), nodeName);
//            }
//            if (StringUtils.isNotEmpty(nodeName)) {
//                if (log.isDebugEnabled()) {
//                    log.debug("AutoAsynTimeOrderTransferTask.Start.SelectTask.NodeName={}", nodeName);
//                }
//                ipBTimeOrderVips = null;//ipVipTimeOrderService.selectNotHandleTimeOrder(nodeName, taskTableName, defaultMaxSize, maxCompensationTime, currentDate);
//                ipBTimeOrderVips = ipVipTimeOrderService.selectNotHandleTimeOrder(nodeName, taskTableName, defaultMaxSize, maxCompensationTime, currentDate);
//            }
//            long time1 = System.currentTimeMillis();
//            if (CollectionUtils.isEmpty(ipBTimeOrderVips)) {
//                return false;
//            }
//            long totalPage;
//            long totalSize = ipBTimeOrderVips.size();
//            if (totalSize % defaultPageSize == 0) {
//                totalPage = totalSize / defaultPageSize;
//            } else {
//                totalPage = totalSize / defaultPageSize + 1;
//            }
//            for (int i = 1; i <= totalPage; i++) {
//                ipBTimeOrderVips = ipBTimeOrderVips.stream()
//                        .skip((i - 1) * defaultPageSize)
//                        .limit(defaultPageSize).collect(Collectors.toList());
//                // 获取拣货单为空 && 次数小于最大次数
////                ipBTimeOrderVips.forEach(obj -> {
////                    Integer tempCompensationTime = obj.getCompensationTime() == null ? 0 : obj.getCompensationTime();
////                    obj.setCompensationTime(tempCompensationTime);
////                });
//                int sumUpdateRecord = 0;
////                ipBTimeOrderVips = ipBTimeOrderVips.stream()
////                        .filter(getIpBTimeOrderVipPredicate(maxCompensationTime, currentDate))
////                        .collect(Collectors.toList());
//                for (IpBTimeOrderVip vip : ipBTimeOrderVips) {
//                    User user = SystemUserResource.getRootUser();
//                    int tempCompensationTime = vip.getCompensationTime() == null ? 0 : vip.getCompensationTime();
//                    int tempAddMinutes = calculate(tempCompensationTime) * firstTimeInterval;
//                    Date addDate = DateUtils.addMinutes(currentDate, tempAddMinutes);
//                    int tempUpdateRecord = ipVipTimeOrderService.updateVipOrder(vip.getOccupiedOrderSn()
//                            , user, currentDate, "时效订单转单-mq", addDate, vip.getStatus(), vip.getIstrans());
//                    sumUpdateRecord = sumUpdateRecord + tempUpdateRecord;
//                }
//                if (sumUpdateRecord > 0) {
//                    sendMQ(ipBTimeOrderVips);
//                }
//            }
//            if (log.isDebugEnabled()) {
//                log.debug("AutoAsynTimeOrderTransferTask 时效订单补偿任务单个线程完成 useTime :" + (System.currentTimeMillis() - start));
//            }
//            return true;
//        }
//    }
//}
