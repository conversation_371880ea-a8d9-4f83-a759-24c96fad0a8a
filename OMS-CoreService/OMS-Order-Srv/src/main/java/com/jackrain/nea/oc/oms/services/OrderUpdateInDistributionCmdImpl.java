package com.jackrain.nea.oc.oms.services;

import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.api.OmsOrderUpdateInDistributionCmd;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.request.OrderUpdateInDistributionRequest;
import com.jackrain.nea.oc.oms.model.result.OrderUpdateInDistributionResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: 胡林洋
 * @since: 2019-04-29
 * create at : 2019-04-29 13:29
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", group = "oc-core", version = "1.0")
public class OrderUpdateInDistributionCmdImpl implements OmsOrderUpdateInDistributionCmd {

    @Autowired
    private OmsOrderService omsOrderService;
    @Autowired
    private OmsOrderUpdateInDistributionService omsOrderUpdateInDistributionService;

    @Override
    public ValueHolderV14<OrderUpdateInDistributionResult> startUpdateInDistribution(OrderUpdateInDistributionRequest updateInDistributionRequest) {
        ValueHolderV14<OrderUpdateInDistributionResult> resultValueHolderV14 = new ValueHolderV14<>();
        try {
            User user = null;
            Long orderId = updateInDistributionRequest.getOrderId();
            int type = 2;
            OcBOrderRelation orderRelation = omsOrderService.selectOmsOrderInfo(orderId);
            boolean flag = false;
            if (orderRelation != null) {
                flag = omsOrderUpdateInDistributionService.updateInDistribution(orderRelation, user);
            }
            if (flag) {
                resultValueHolderV14.setCode(0);
                resultValueHolderV14.setMessage("更新“配货中”成功");
            } else {
                resultValueHolderV14.setCode(0);
                resultValueHolderV14.setMessage("更新“配货中”失败");
            }
        } catch (Exception ex) {
            resultValueHolderV14.setCode(-1);
            resultValueHolderV14.setMessage("更新“配货中”流程异常：" + ex.getMessage());
            log.error(LogUtil.format("更新“配货中”流程异常,异常信息:{}", "更新“配货中”流程异常"), Throwables.getStackTraceAsString(ex));
        }
        return resultValueHolderV14;
    }
}