package com.jackrain.nea.oc.services;

import com.jackrain.nea.oc.oms.api.BatchSaveBillCmd;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderExtend;
import com.jackrain.nea.oc.oms.services.BatchSaveBillService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 批量新增订单（導入）
 *
 * @author: 郑立轩
 * @since: 2019/6/5
 * create at : 2019/6/5 14:53
 */
@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class BatchSaveBillImpl implements BatchSaveBillCmd {
    @Autowired
    private BatchSaveBillService batchSaveBillService;

    @Override
    public ValueHolderV14 batchSaveBillCmd(List<OcBOrderExtend> ocBOrderExtends) {
        return null;
    }
}
