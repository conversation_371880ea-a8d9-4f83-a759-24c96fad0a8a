package com.jackrain.nea.oc.oms.model.result;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR> lin yu
 * @date : 2022/8/3 下午5:58
 * @describe :
 */
@Data
public class OcBOrderToBStorageInfoResult implements Serializable {

    @J<PERSON><PERSON><PERSON>(name = "PS_C_PRO_ID")
    private Long psCProId;

    @J<PERSON><PERSON>ield(name = "PS_C_PRO_ECODE")
    private String psCProEcode;

    @J<PERSON><PERSON>ield(name = "PS_C_PRO_ENAME")
    private String psCProEname;

    @JSONField(name = "QTY")
    private BigDecimal qty;

    @J<PERSON><PERSON>ield(name = "QTY_STORAGE")
    private BigDecimal qtyStorage;

    @J<PERSON><PERSON>ield(name = "QTY_AVAILABLE")
    private BigDecimal qtyAvailable;

    @<PERSON><PERSON><PERSON>ield(name = "QTY_UNSHARED_PREOUT")
    private BigDecimal qtyUnsharedPreout;

    @J<PERSON><PERSON><PERSON>(name = "QTY_DIFF")
    private BigDecimal qtyDiff;

    @J<PERSON><PERSON>ield(name = "EXPIRY_DATE_RANGE")
    private String expiryDateRange;
}
