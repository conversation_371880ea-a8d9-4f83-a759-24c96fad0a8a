package com.jackrain.nea.oc.oms.mapper.task;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.OcBMsgSendRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Component;

import java.util.List;


@Mapper
@Component
public interface OcBMsgTaskMapper extends ExtentionMapper<OcBMsgSendRecord> {

    @SelectProvider(type = MsgDrdsSql.class, method = "selectByNodeSql")
    List<OcBMsgSendRecord> selectTaskIdList(@Param(value = "nodeName") String nodeName, @Param(value = "limit") int limit,
                                            @Param(value = "taskTableName") String taskTableName);

    @Update("<script> "
            + "UPDATE OC_B_MSG_SEND_RECORD SET STATUS = 1,modifieddate = now() where id in "
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    void updateTaskStatus(@Param("ids") List<Long> orderIds);

    @Update("UPDATE OC_B_MSG_SEND_RECORD SET STATUS = 1,modifieddate = now() where id = #{id}")
    void updateTaskStatusById(@Param("id") Long id);

    @Update("<script> "
            + "UPDATE OC_B_MSG_SEND_RECORD SET modifieddate = now() where id in "
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    void updateTaskModitimes(@Param("ids") List<Long> orderIds);
}


