package com.jackrain.nea.oc.oms.process.transfer.impl.refund.jdcancel.step;

import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.relation.IpJingdongCancelRelation;
import com.jackrain.nea.oc.oms.model.resources.SysNotesConstant;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongSaRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.oc.oms.util.OmsRefundTransferUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @description: 检查京东取消订单平台单号在全渠道是否存在
 * @author: 郑小龙
 * @date: 2020-06-01 11:37
 **/
@Step(order = 20, description = "检查京东取消订单平台单号在全渠道是否存在")
@Slf4j
@Component
public class Step020CheckOriginalStatus extends BaseJingdongCancelProcessStep
        implements IOmsOrderProcessStep<IpJingdongCancelRelation> {

    @Override
    public ProcessStepResult<IpJingdongCancelRelation> startProcess(IpJingdongCancelRelation orderInfo,
                                                                    ProcessStepResult preStepResult,
                                                                    boolean isAutoMakeup, User operateUser) {

        IpBJingdongSaRefund saRefund = orderInfo.getJingdongSaRefund();
        try {
            if (orderInfo != null) {
                List<OcBOrder> ocBOrder = orderInfo.getOcBOrder();

                // 订单状态判断-传wms不允许继续
                if (OmsRefundTransferUtil.isForbidOrderTransfer(ocBOrder)) {
                    saRefundService.updateSaRefundIsTransError(saRefund, SysNotesConstant.SYS_REMARK90);
                    return new ProcessStepResult<>(StepStatus.FINISHED, SysNotesConstant.SYS_REMARK90);
                }

                boolean flag = saRefundService.noOriginalOrder(saRefund, ocBOrder);
                if (flag) {
                    return new ProcessStepResult<>(StepStatus.FINISHED, "单据" + orderInfo.getOrderId() + "转换完成");
                }
            }
            //存在原单
            return new ProcessStepResult<>(StepStatus.SUCCESS);
        } catch (Exception e) {
            log.error(LogUtil.format("京东取消订单转换异常, 异常信息:{}", "京东取消订单转换异常"), Throwables.getStackTraceAsString(e));
            //修改中间表状态及系统备注
            saRefundService.updateSaRefundIsTransError(saRefund, e.getMessage());
            String errorMessage = "退单转换异常!" + e.getMessage();
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
    }

}
