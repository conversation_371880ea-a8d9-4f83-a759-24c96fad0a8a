package com.jackrain.nea.oc.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OrderHeavySingleServiceCmd;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.services.OrderHeavySingleService;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 重单服务
 *
 * @date 2019/3/15
 * @author: ming.fz
 */
@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OrderHeavySingleServiceCmdImpl implements OrderHeavySingleServiceCmd {

    @Autowired
    OrderHeavySingleService orderHeavySingleService;


    @Override
    public void orderHeavySingleService(OcBOrder ocBorder, User user) throws NDSException {
        orderHeavySingleService.heavySingleSveice(ocBorder, user);
    }


}