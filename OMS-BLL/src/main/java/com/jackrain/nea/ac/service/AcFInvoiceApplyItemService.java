package com.jackrain.nea.ac.service;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.jackrain.nea.core.db.Tools;
import com.jackrain.nea.oc.oms.mapper.ac.AcFInvoiceApplyItemMapper;
import com.jackrain.nea.oc.oms.model.constant.InvoiceConst;
import com.jackrain.nea.oc.oms.model.table.AcFInvoiceApplyItem;
import com.jackrain.nea.oc.oms.model.table.AcFOrderInvoiceSystemItem;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName AcFInvoiceApplyItemService
 * @Description
 * @Date 2022/9/19 下午6:38
 * @Created by wuhang
 */
@Slf4j
@Component
public class AcFInvoiceApplyItemService {

    @Autowired
    private AcFInvoiceApplyItemMapper invoiceApplyItemMapper;

    /**
     * 根据开票订单明细生成申请明细
     * 因为开票申请明细表有tid+shopId的组合唯一索引,所以目前只是将原来的申请明细关联的主表id改为新的申请明细id
     * @param systemItemList
     * @param id
     */
    public void insertBySystemItemList(List<AcFOrderInvoiceSystemItem> systemItemList, Long id, User user, Long shopId) {
        Date now = new Date();
//        List<String> tids = systemItemList.stream().map(AcFOrderInvoiceSystemItem::getTid).distinct().collect(Collectors.toList());
//        if(CollectionUtils.isNotEmpty(tids)){
//            for(String tid : tids){
//                String nS = tid + "_" + System.currentTimeMillis()/1000;
//                UpdateWrapper<AcFInvoiceApplyItem> update = new UpdateWrapper<>();
//                update.lambda().eq(AcFInvoiceApplyItem::getTid,tid)
//                        .set(AcFInvoiceApplyItem::getTid,nS);
//                invoiceApplyItemMapper.update(null,update);
//            }
//        }
        for(AcFOrderInvoiceSystemItem systemItem : systemItemList){
            UpdateWrapper<AcFInvoiceApplyItem> update = new UpdateWrapper<>();
            update.lambda().eq(AcFInvoiceApplyItem::getTid,systemItem.getTid())
                    .set(AcFInvoiceApplyItem::getAcFInvoiceApplyId,id)
                    .set(AcFInvoiceApplyItem::getAmt,systemItem.getPriceAmt())
                    .set(AcFInvoiceApplyItem::getCpCShopId,shopId)
                    .set(AcFInvoiceApplyItem::getModifieddate,now)
                    .set(AcFInvoiceApplyItem::getModifierid,Long.valueOf(user.getId()))
                    .set(AcFInvoiceApplyItem::getModifierename,user.getEname())
                    .set(AcFInvoiceApplyItem::getModifiername,user.getName());
            invoiceApplyItemMapper.update(null, update);
        }
    }
}
