package com.jackrain.nea.oc.oms.process.transfer.impl.normal.standplat.step;

import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.AbnormalTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.relation.IpStandplatOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatOrder;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatOrderItemEx;
import com.jackrain.nea.oc.oms.services.BusinessSystemParamService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.ps.services.PsGetCommodityInformationService;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;


/**
 * 检查订单商品是否在系统中
 *
 * @author: ming.fz
 * @since: 2019-07-08
 * create at : 2019-07-08
 */
@Slf4j
@Step(order = 50, description = "检查订单商品是否在系统中")
@Component
public class Step050StandplatCheckProductExist extends BaseStandplatOrderProcessStep
        implements IOmsOrderProcessStep<IpStandplatOrderRelation> {

    @Autowired
    private PsGetCommodityInformationService psGetCommodityInformationService;
    @Resource
    private BusinessSystemParamService businessSystemParamService;

    @Override
    public ProcessStepResult<IpStandplatOrderRelation> startProcess(IpStandplatOrderRelation orderInfo,
                                                                    ProcessStepResult preStepResult,
                                                                    boolean isAutoMakeup, User operateUser) {
        //系统备注
        String msg = null;
        String orderNo = orderInfo.getOrderNo();
        //标识当前商品是否存在
        boolean hasErrorInfo = false;
        boolean skuNotExist = false;
        //标识当前订单是否为虚拟商品订单
        boolean isVirtualOrder = true;

        IpBStandplatOrder standplatOrder = orderInfo.getStandplatOrder();
        List<IpBStandplatOrderItemEx> standplatOrderItemList = orderInfo.getStandPlatOrderItemList();
        if (CollectionUtils.isEmpty(standplatOrderItemList)) {
            hasErrorInfo = true;
            msg = "当前订单没有明细";
        }
        Long shopId = standplatOrder.getCpCShopId();
        boolean flag = false;
        String presaleTransfer = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get("business_system:pro_sku_or_platform_sku");
        if (StringUtils.isNotEmpty(presaleTransfer)) {
            flag = "是".equals(presaleTransfer.trim());
        }
        //遍历中间表明细，查询当前商品是否存在
        for (IpBStandplatOrderItemEx standplatOrderItem : standplatOrderItemList) {
            String skuId = standplatOrderItem.getOuterSkuId();
            if (StringUtils.isEmpty(skuId)) {
                skuId = standplatOrderItem.getOuterIid();
            }
            ProductSku skuInfo = null;
            if (StringUtils.isEmpty(skuId)) {
                skuId = standplatOrderItem.getSkuId();
                if (StringUtils.isEmpty(skuId)) {
                    hasErrorInfo = true;
                    msg = "商品维护异常，商品条码" + skuId + "在本地商品中不存在";
                    break;
                } else if (!PlatFormEnum.SAP.getCode().equals(standplatOrder.getCpCPlatformId().intValue())) {
                    //查询第三方映射关系表
                    List<ProductSku> productSkus = psRpcService.selectProductSkuByThirdCode(skuId, shopId);
                    if (CollectionUtils.isNotEmpty(productSkus) && productSkus.size() == 1) {
                        skuInfo = productSkus.get(0);
                    }
                }
            } else {
                //20240603 佳哥让SAP不走平台映射关系
                if (flag && !PlatFormEnum.SAP.getCode().equals(standplatOrder.getCpCPlatformId().intValue()) && !PlatFormEnum.DMS.getCode().equals(standplatOrder.getCpCPlatformId().intValue())) {
                    //查询第三方映射关系表
                    List<ProductSku> productSkus = psRpcService.selectProductSkuByThirdCode(skuId, shopId);
                    if (CollectionUtils.isEmpty(productSkus)) {
                        skuInfo = psRpcService.selectProductSku(skuId);
                    } else {
                        if (productSkus.size() == 1) {
                            skuInfo = productSkus.get(0);
                        }
                    }
                } else {
                    skuInfo = psRpcService.selectProductSku(skuId);
                    if (skuInfo == null && !PlatFormEnum.SAP.getCode().equals(standplatOrder.getCpCPlatformId().intValue())) {
                        List<ProductSku> productSkus = psRpcService.selectProductSkuByThirdCode(skuId, shopId);
                        if (CollectionUtils.isNotEmpty(productSkus) && productSkus.size() == 1) {
                            skuInfo = productSkus.get(0);
                        }
                    }
                }

            }
            if (skuInfo == null) {
                hasErrorInfo = true;
                skuNotExist = true;
                msg = "商品维护异常，商品条码" + skuId + "在本地商品中不存在或者平台编码映射关系异常,请检查";
                Resources.getMessage(msg);
                break;
            } else if (YesNoEnum.N.getKey().equals(skuInfo.getIsactive())) {
                hasErrorInfo = true;
                msg = "商品维护异常，商品条码" + skuId + "在本地商品中已作废,请检查";
                break;
            } else {
                standplatOrderItem.setProdSku(skuInfo);
                if (!"Y".equals(skuInfo.getIsVirtual())) {
                    isVirtualOrder = false;
                }
            }
        }

        if (hasErrorInfo) {
            boolean updateStatusRes = ipStandplatOrderService.updateStandPlatOrderTransStatus(orderNo,
                    TransferOrderStatus.TRANSFEREXCEPTION, msg, AbnormalTypeEnum.SKU_ABNORMAL.getKey());
            if (!updateStatusRes) {
                msg += ";更新状态失败=False";
            }
            msg = Resources.getMessage("单据" + orderInfo.getOrderId() + msg);
            if (log.isDebugEnabled()) {
                log.info(msg);
            }
            // 判断是否包含 "商品维护异常" 字眼。 如果是的话 再看下转换失败的次数 如果次数大于等于3次 则直接标记完成
            if (skuNotExist) {
                // 判断平台
                List<Long> platformList = businessSystemParamService.getForceTransferPlatform();
                if (standplatOrder.getTransCount() != null && standplatOrder.getTransCount() >= 3 && platformList.contains(Long.valueOf(standplatOrder.getCpCPlatformEcode()))) {
                    ipStandplatOrderService.updateStandPlatOrderTransStatus(orderInfo.getOrderNo(),
                            TransferOrderStatus.TRANSFERRED, "特殊平台，商品不存在，并且转换次数超过3次 直接标记完成", null);
                    return new ProcessStepResult<>(StepStatus.FINISHED, msg);
                }
            }
            return new ProcessStepResult<>(StepStatus.FAILED, msg);
        } else {
            Long platmform = standplatOrder.getCpCPlatformId();
            //如果是有赞平台，并且是虚拟订单，省市区为空时，赋默认值
            if(PlatFormEnum.YOUZAN.getCode().equals(platmform.intValue()) && isVirtualOrder){
                //省
                orderInfo.getStandplatOrder().setReceiverProvince("浙江省");
                //买家所在市
                orderInfo.getStandplatOrder().setReceiverCity("杭州市");
                //买家所在区。
                orderInfo.getStandplatOrder().setReceiverDistrict("余杭区");
            }


            return new ProcessStepResult<>(StepStatus.SUCCESS, null, "检查商品是否存在成功，进入下一阶段",
                    Step060StandPlatSaveOmsOrder.class);
        }
    }
}
