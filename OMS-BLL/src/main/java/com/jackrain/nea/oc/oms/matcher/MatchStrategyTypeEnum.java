package com.jackrain.nea.oc.oms.matcher;

import lombok.Getter;

import java.util.Objects;

/**
 * Description： 匹配策略类型，如直播解析匹配
 * Author: RESET
 * Date: Created in 2020/6/15 20:24
 * Modified By:
 */
public enum MatchStrategyTypeEnum {

    // 匹配策略类型
    LIVE(1, "live", "直播策略解析");

    @Getter
    private Integer value;
    @Getter
    private String code;
    @Getter
    private String description;

    MatchStrategyTypeEnum(Integer value, String code, String description) {
        this.value = value;
        this.code = code;
        this.description = description;
    }

    @Override
    public String toString() {
        return String.valueOf(value);
    }

    public static MatchStrategyTypeEnum fromValue(Integer v) {
        for (MatchStrategyTypeEnum c : MatchStrategyTypeEnum.values()) {
            if (Objects.equals(v, c.value)) {
                return c;
            }
        }
        throw new IllegalArgumentException(String.valueOf(v));
    }

}
