package com.jackrain.nea.oc.oms.model.request;

import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.web.face.User;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @author: hulinyang
 * @since: 2019/4/28
 * create at : 2019/4/28 17:33
 */
@Data
public class OrderOutRequest implements Serializable {
    private ChannelType channelType;

    private User operateUser;

    private List<Long> orderIdList;

    private Long orderId;

}