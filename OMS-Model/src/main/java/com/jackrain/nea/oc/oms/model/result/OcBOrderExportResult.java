package com.jackrain.nea.oc.oms.model.result;

import com.jackrain.nea.oc.oms.model.relation.OcBOrderExtend;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderItemExtend;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @author: 李龙飞
 * @create: 2019-05-14 17:21
 **/
@Data
public class OcBOrderExportResult implements Serializable {

    List<OcBOrderExtend> ocBOrderList;
    List<OcBOrderItemExtend> ocBOrderItemList;

}
