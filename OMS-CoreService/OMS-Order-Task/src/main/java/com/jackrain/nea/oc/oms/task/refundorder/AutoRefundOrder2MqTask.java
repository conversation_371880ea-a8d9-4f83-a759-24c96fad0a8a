package com.jackrain.nea.oc.oms.task.refundorder;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.mq.core.DefaultProducerSend;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.oc.oms.config.TransferOrderMqConfig;
import com.jackrain.nea.oc.oms.constant.MqConstants;
import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.OperateType;
import com.jackrain.nea.oc.oms.model.enums.OrderType;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.services.IpTaobaoRefundService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ListSplitUtil;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;

/**
 * 淘宝退单补偿发送MQ任务
 *
 * <AUTHOR>
 * @since 2020-07-24
 * Created at 2020-07-24 17:53
 */
@Component
@Slf4j
public class AutoRefundOrder2MqTask extends BaseR3Task implements IR3Task {

    @Autowired
    private DefaultProducerSend defaultProducerSend;

    @Autowired
    private IpTaobaoRefundService ipTaobaoRefundService;

    @Autowired
    private TransferOrderMqConfig transferOrderMqConfig;
    @Autowired
    private ThreadPoolTaskExecutor tbRefundOrderToMqTaskThreadPoolExecutor;


    @Override
    @XxlJob("AutoRefundOrder2MqTask")
    public RunTaskResult execute(JSONObject params) {
        String tableName = "ip_b_taobao_refund";
        String taskName = "AutoRefundOrder2MqTask";
        RunTaskResult result = new RunTaskResult();
        long start = System.currentTimeMillis();
        int timeStamp = (int) (start / 1000);
        try {
            PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
            Integer size = config.getProperty("lts.AutoRefundOrder2MqTask.range", DEFAULT_PAGE_SIZE) * 24;
            Integer transCount = config.getProperty("lts.AutoRefundOrder2MqTask.order.transCount", 60);
            Integer minutes = config.getProperty("lts.AutoRefundOrder2MqTask.order.minutes", 7500);
            List<String> refundIdList = ipTaobaoRefundService.selectDynamicRefundId(tableName, size
                    , TransferOrderStatus.NOT_TRANSFER.toInteger(), transCount, timeStamp, minutes);
            if (CollectionUtils.isEmpty(refundIdList)) {
                result.setSuccess(true);
                result.setMessage("无数据");
                return result;
            }
            List<List<String>> lists = ListSplitUtil.averageAssign(refundIdList, 24);
            List<Future<Integer>> results = new ArrayList<>();
            for (List<String> data : lists) {
                results.add(tbRefundOrderToMqTaskThreadPoolExecutor.submit(new UnTransferRefundOrderToMqCallable(data)));
            }
            int executeCount = 0;
            for (Future<Integer> futureResult : results) {
                try {
                    executeCount += futureResult.get();
                } catch (Exception e) {
                    log.error(LogUtil.format("AutoRefundOrder2MqTask:异常！{}", "AutoRefundOrder2MqTask"), Throwables.getStackTraceAsString(e));
                }
            }
            long end = System.currentTimeMillis();
            result.setSuccess(true);
            result.setMessage(taskName + " 执行完毕, 数量：" + executeCount + ", 用时: " + (end - start) + " ms");
        } catch (Exception ex) {
            log.error(LogUtil.format("AutoRefundOrder2MqTask:异常{}",  "AutoRefundInTimeOutTask"), Throwables.getStackTraceAsString(ex));
            result.setSuccess(false);
            result.setMessage(ex.getMessage());
        } finally {

        }
        return result;
    }

    /**
     * debug level log
     *
     * @param msg message
     * @param obj objects
     */
    protected void debugLog(String msg, Object... obj) {
        if (log.isDebugEnabled()) {
            log.debug(msg, obj);
        }
    }

    /**
     * 内部类，淘宝退单补偿发送到MQ
     */
    class UnTransferRefundOrderToMqCallable implements Callable<Integer> {
        private final List<String> data;

        public UnTransferRefundOrderToMqCallable(List<String> data) {
            this.data = data;
        }

        @Override
        public Integer call() {
            String threadName = Thread.currentThread().getName();
            int executeCnt = 0;
            if (data == null || data.size() == 0) {
                return executeCnt;
            }

            List<OperateOrderMqInfo> mqInfoList = new ArrayList<>();
            for (String orderNo : data) {
                OperateOrderMqInfo orderMqInfo = new OperateOrderMqInfo();
                orderMqInfo.setOperateType(OperateType.TRANSFER_ORDER);
                orderMqInfo.setChannelType(ChannelType.TAOBAO);
                orderMqInfo.setOrderType(OrderType.REFUND);
                orderMqInfo.setOrderNo(orderNo);
                mqInfoList.add(orderMqInfo);
            }
            String jsonValue = JSONObject.toJSONString(mqInfoList);
            int step = 1;
            try {
                PropertiesConf propertiesConf = ApplicationContextHandle.getBean(PropertiesConf.class);
                String refundTopic = propertiesConf.getProperty("r3.oc.oms.refundTransfer.mq.topic");
                String refundTag = propertiesConf.getProperty("r3.oc.oms.refundTransfer.mq.tag");
                if (StringUtils.isEmpty(refundTopic)) {
                    refundTopic = MqConstants.TOPIC_R3_OC_OMS_CALL_TRANSFER;
                    refundTag = MqConstants.TAG_R3_OC_OMS_CALL_TRANSFER;
                }
                defaultProducerSend.sendTopic(refundTopic, refundTag, jsonValue, null);
            } catch (Exception e) {
                switch (step) {
                    case 1:
                        log.error("{} 淘宝退单补偿发送MQ异常：{}", threadName, e);
                        break;
                    case 2:
                        log.error("{} 淘宝退单更新时间戳异常：{}", threadName, e);
                        break;
                    default:
                        log.error("{} 淘宝退单补偿未知异常：{}", threadName, e);
                        break;
                }
                return executeCnt;
            }
            return executeCnt;
        }
    }
}
