//package com.jackrain.nea.oc.oms.task.ordertowing;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import com.google.common.base.Throwables;
//import com.jackrain.nea.config.PropertiesConf;
//import com.google.common.util.concurrent.ThreadFactoryBuilder;
//import com.jackrain.nea.config.PropertiesConf;
//import com.jackrain.nea.ip.model.wing.WingDeliveryItemModel;
//import com.jackrain.nea.ip.model.wing.WingDeliveryModel;
//import com.jackrain.nea.ip.model.wing.WingReturnResult;
//import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
//import com.jackrain.nea.oc.oms.mapper.task.OcBToWingDeliveryTaskMapper;
//import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
//import com.jackrain.nea.oc.oms.services.task.OmsToWingDeliveryTaskService;
//import com.jackrain.nea.oc.oms.task.BaseR3Task;
//import com.jackrain.nea.rpc.IpRpcService;
//import com.jackrain.nea.sys.domain.ValueHolderV14;
//import com.jackrain.nea.task.IR3Task;
//import com.jackrain.nea.task.RunTaskResult;
//import com.jackrain.nea.util.ApplicationContextHandle;
//import com.jackrain.nea.util.ApplicationContextHandle;
//import com.jackrain.nea.util.OMSThreadPoolFactory;
//import com.jackrain.nea.util.RuntimeCompute;
//import com.jackrain.nea.util.SplitListUtil;
//import com.jackrain.nea.utility.LogUtil;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.math.BigDecimal;
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Optional;
//import java.util.concurrent.Callable;
//import java.util.concurrent.ExecutionException;
//import java.util.concurrent.ExecutorService;
//import java.util.concurrent.Future;
//
//@Slf4j
//@Component
//public class AutoToWingDeliveryTask extends BaseR3Task implements IR3Task {
//
//    @Autowired
//    OcBToWingDeliveryTaskMapper ocBToWingDeliveryTaskMapper;
//
//    @Autowired
//    OmsToWingDeliveryTaskService omsToWingDeliveryTaskService;
//
//    @Autowired
//    private IpRpcService ipRpcService;
//
//    @Autowired
//    private OcBOrderItemMapper orderItemMapper;
//
//    @Override
//    public RunTaskResult execute(JSONObject params) {
//
//        ExecutorService executor = OMSThreadPoolFactory.getWingDeliveryTaskThreadPool();
//        RunTaskResult result = new RunTaskResult();
//        RuntimeCompute runtimeCompute = new RuntimeCompute();
//        runtimeCompute.startRuntime();
//        result.setSuccess(true);
//        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
//        //默认查询总条数
//        Integer totalCount = config.getProperty("lts.autotowing.delivery.totalcount", DEFAULT_PAGE_SIZE);
//        //拉取数据未成功的数据，默认200条修改时间正序  0未转换 1 是转换中  3 失败  4成功
//        List<String> outBillNoList = ocBToWingDeliveryTaskMapper.queryList(totalCount);
//        if (log.isDebugEnabled()) {
//            log.debug(LogUtil.format("AutoToWingDeliveryTask.oms调用wing发货拉取数据为{}", "oms调用wing发货拉取数据"), outBillNoList);
//        }
//        if (CollectionUtils.isEmpty(outBillNoList)) {
//            log.debug(LogUtil.format("AutoToWingDeliveryTask.oms调用wing发货拉取数据为空", "oms调用wing发货拉取数据"));
//            double usedTime = runtimeCompute.endRuntime();
//            result.setSuccess(true);
//            result.setMessage("wing发货任务执行成功!UsedTime=" + usedTime);
//            return result;
//        }
//        //新建一个集合作为修改中间表用
//        List<String> newTidList = new ArrayList<>(outBillNoList);
//        try {
//            //调用ip wing发货接口
//            if (CollectionUtils.isNotEmpty(outBillNoList)) {
//                List<WingDeliveryModel> wingDeliveryModelList = new ArrayList<>();
//                for (String outBillNo : outBillNoList) {
//                    WingDeliveryModel wingDeliveryModel = new WingDeliveryModel();
//                    wingDeliveryModel.setId(outBillNo);
//                    List<OcBOrderItem> orderItemList = orderItemMapper.selectOrderItemListByoutBillNo(outBillNo);
//                    if (CollectionUtils.isNotEmpty(orderItemList)) {
//                        List<WingDeliveryItemModel> wingDeliveryItemModelList = new ArrayList<>();
//                        for (OcBOrderItem ocBOrderItem : orderItemList) {
//                            WingDeliveryItemModel wingDeliveryItemModel = new WingDeliveryItemModel();
//                            wingDeliveryItemModel.setZtId(outBillNo);
//                            wingDeliveryItemModel.setTid(ocBOrderItem.getTid());
//                            wingDeliveryItemModel.setSku(ocBOrderItem.getPsCSkuEcode());
//                            BigDecimal realAmt = ocBOrderItem.getRealAmt();
//                            BigDecimal goodsQty = ocBOrderItem.getQty();
//                            BigDecimal priceActual = Optional.ofNullable(realAmt).orElse(BigDecimal.ZERO).divide(goodsQty, 4, BigDecimal.ROUND_HALF_UP);
//                            wingDeliveryItemModel.setPrice(priceActual.toString());
//                            wingDeliveryItemModelList.add(wingDeliveryItemModel);
//                        }
//                        wingDeliveryModel.setOrderGoods(wingDeliveryItemModelList);
//                        wingDeliveryModelList.add(wingDeliveryModel);
//                    }
//                }
//                if (CollectionUtils.isEmpty(wingDeliveryModelList)) {
//                    log.debug(LogUtil.format("AutoToWingDeliveryTask.wingDeliveryModelList数据为空！", "oms调用wing发货拉取数据"));
//                    double usedTime = runtimeCompute.endRuntime();
//                    result.setSuccess(true);
//                    result.setMessage("wing发货任务执行成功!UsedTime=" + usedTime);
//                    return result;
//                }
//                log.debug("AutoToWingDeliveryTask.wingDeliveryModelList数据为{}",JSON.toJSONString(wingDeliveryModelList));
//                int splitNum = config.getProperty("lts.autotowing.delivery.split.num", 5);
//                int tmpNum = wingDeliveryModelList.size() / splitNum;//每个小list分的个数
//                if (wingDeliveryModelList.size() % splitNum != 0) {
//                    tmpNum = tmpNum + 1;
//                }
//                //查询明细构造
//                List<List<WingDeliveryModel>> splitList = SplitListUtil.partition(wingDeliveryModelList, tmpNum);
//                List<Future<List<ValueHolderV14<WingReturnResult>>>> results =
//                        new ArrayList<Future<List<ValueHolderV14<WingReturnResult>>>>();
//                //线程提交
//                for (int i = 0; i < splitList.size(); i++) {
//                    results.add(executor.submit(new AutoToWingDeliveryTask.CallableTaskWithResult(splitList.get(i))));
//                }
//                //线程执行结果获取
//                for (Future<List<ValueHolderV14<WingReturnResult>>> futureResult : results) {
//                    try {
//                        List<ValueHolderV14<WingReturnResult>> valueHolderV14List = futureResult.get();
//                        for (ValueHolderV14<WingReturnResult> v14 : valueHolderV14List) {
//                            if (v14 != null) {
//                                if (log.isDebugEnabled()) {
//                                    log.debug(LogUtil.format("AutoToWingDeliveryTask.调用wing发货接口返回值为{}", "oms调用wing发货拉取数据"), JSON.toJSONString(v14));
//                                }
//                                WingReturnResult data = v14.getData();
//                                List<String> failTids = new ArrayList<>();
//                                if (data.getStatus() == 1) {
//                                    List<WingReturnResult.FailMsg> faileds = data.getFaileds();
//                                    if (CollectionUtils.isNotEmpty(faileds)) {
//                                        for (WingReturnResult.FailMsg failed : faileds) {
//                                            //有错误信息就插入没有就是成功
//                                            if (outBillNoList.contains(failed.getId())) {
//                                                failTids.add(failed.getId());
//                                                String outBillNo = failed.getId();
//                                                String failMsg = failed.getErrMsg();
//                                                Integer status = 1;
//                                                ocBToWingDeliveryTaskMapper.updateByOutBillNo(outBillNo, failMsg, status);
//                                            }
//                                        }
//                                    }
//                                } else {
//                                    List<WingReturnResult.FailMsg> faileds = data.getFaileds();
//                                    if (CollectionUtils.isNotEmpty(faileds)) {
//                                        for (WingReturnResult.FailMsg failed : faileds) {
//                                            //有错误信息就插入没有就是成功
//                                            if (outBillNoList.contains(failed.getId())) {
//                                                String outBillNo = failed.getId();
//                                                String failMsg = failed.getErrMsg();
//                                                Integer status = 3;
//                                                ocBToWingDeliveryTaskMapper.updateByOutBillNo(outBillNo, failMsg, status);
//                                            }
//                                        }
//                                    }
//                                }
//                                //存在错误信息
//                                if (CollectionUtils.isNotEmpty(failTids)) {
//                                    newTidList.removeAll(failTids);
//                                }
//                                if (log.isDebugEnabled()) {
//                                    log.debug(LogUtil.format("AutoToWingDeliveryTask.调用wing发货任务执行成功，修改中间表状态！", "oms调用wing发货拉取数据"));
//                                }
//                                omsToWingDeliveryTaskService.updateToBeConfirmedTaskStatus(newTidList);
//                            }
//                        }
//                    } catch (InterruptedException e) {
//                        Thread.currentThread().interrupt();
//                        log.error("{} Thread InterruptedException：{}", OMSThreadPoolFactory.THREAD_POOL_WING_DELIVERY_TASK, e);
//                    } catch (ExecutionException e) {
//                        log.error("{} Thread ExecutionException：{}", OMSThreadPoolFactory.THREAD_POOL_WING_DELIVERY_TASK, e);
//                    }
//
//                }
//            } else {
//                log.debug(LogUtil.format("AutoToWingDeliveryTask.通过平台单号查询es满足配货的查询数据为空", "oms调用wing发货拉取数据"));
//                double usedTime = runtimeCompute.endRuntime();
//                result.setSuccess(true);
//                result.setMessage("wing发货任务执行成功!UsedTime=" + usedTime);
//            }
//        } catch (Exception e) {
//            log.error(LogUtil.format("AutoToWingDeliveryTask.调用wing发货任务失败 Error:{}", "调用wing发货任务失败"), Throwables.getStackTraceAsString(e));
//        } finally {
//            log.debug(LogUtil.format("AutoToWingDeliveryTask.不管定时任务执行成功失败都修改时间,tids:{}", "oms调用wing发货拉取数据"), outBillNoList);
//            omsToWingDeliveryTaskService.updateToBeConfirmedTaskDate(outBillNoList);
//        }
//        return result;
//    }
//
//    class CallableTaskWithResult implements Callable<List<ValueHolderV14<WingReturnResult>>> {
//        private List<WingDeliveryModel> wingDeliveryModelList;
//
//        public CallableTaskWithResult(List<WingDeliveryModel> wingDeliveryModelList) {
//            this.wingDeliveryModelList = wingDeliveryModelList;
//        }
//
//        @Override
//        public List<ValueHolderV14<WingReturnResult>> call() throws Exception {
//            List<ValueHolderV14<WingReturnResult>> valueHolderV14List = new ArrayList<>();
//            //更新中间表 出库通知单
//            ValueHolderV14<WingReturnResult> v14 = ipRpcService.wingDeliveryOrder(wingDeliveryModelList);
//            valueHolderV14List.add(v14);
//            return valueHolderV14List;
//        }
//    }
//}
