package com.jackrain.nea.oc.controller.convert;


import com.google.common.base.Converter;
import com.jackrain.nea.oc.oms.model.enums.OmsPayType;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderExtend;
import com.jackrain.nea.oc.oms.vo.OcBOrderImpVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Optional;

@Component
public class OrderConverter extends Converter<OcBOrderImpVO, OcBOrderExtend> {


    @Override
    protected OcBOrderExtend doForward(OcBOrderImpVO ocBOrderImpVo) {
        OcBOrderExtend ocBOrderExtend = new OcBOrderExtend();
        BeanUtils.copyProperties(ocBOrderImpVo, ocBOrderExtend);
        ocBOrderExtend.setTid(Optional.ofNullable(ocBOrderImpVo.getSourceCode()).orElse(""));
        ocBOrderExtend.setBK(Optional.ofNullable(ocBOrderImpVo.getSourceCode()).orElse(""));
        ocBOrderExtend.setSourceCode(Optional.ofNullable(ocBOrderImpVo.getSourceCode()).orElse(""));
        ocBOrderExtend.setMergeSourceCode(Optional.ofNullable(ocBOrderImpVo.getSourceCode()).orElse(""));
        if ("货到付款".equalsIgnoreCase(ocBOrderImpVo.getPayTypeName())) {
            ocBOrderExtend.setPayType(OmsPayType.CASH_ON_DELIVERY.toInteger());
        } else if("在线支付".equalsIgnoreCase(ocBOrderImpVo.getPayTypeName())){
            ocBOrderExtend.setPayType(OmsPayType.ON_LINE_PAY.toInteger());
        } else {
            ocBOrderExtend.setPayType(0);
        }
        ocBOrderExtend.setShipAmt(Optional.ofNullable(ocBOrderImpVo.getShipAmt()).orElse(BigDecimal.ZERO));
        return ocBOrderExtend;
    }

    @Override
    protected OcBOrderImpVO doBackward(OcBOrderExtend ocBOrderExtend) {
        OcBOrderImpVO ocBOrderImpVo = new OcBOrderImpVO();
        BeanUtils.copyProperties(ocBOrderImpVo, ocBOrderExtend);
        return ocBOrderImpVo;
    }

}
