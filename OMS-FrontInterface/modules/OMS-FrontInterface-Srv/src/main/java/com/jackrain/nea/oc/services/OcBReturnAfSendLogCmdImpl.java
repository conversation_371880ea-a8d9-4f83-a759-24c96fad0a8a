package com.jackrain.nea.oc.services;

import com.jackrain.nea.oc.oms.api.OcBReturnAfSendLogCmd;
import com.jackrain.nea.oc.oms.model.table.OcBReturnAfSendLog;
import com.jackrain.nea.oc.oms.services.RefundFormAfterDeliveryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description: 额外退款日志查询
 * @author: 江家雷
 * @since: 2020/8/14
 * create at : 2020/8/14 17:30
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBReturnAfSendLogCmdImpl implements OcBReturnAfSendLogCmd {

    @Autowired
    private RefundFormAfterDeliveryService refundFormAfterDeliveryService;

    @Override
    public List<OcBReturnAfSendLog> getOcBReturnAfSendLog(Long ocBReturnAfSendId) {
        return refundFormAfterDeliveryService.getOcBReturnAfSendLog(ocBReturnAfSendId);
    }
}