package com.jackrain.nea.oc.services.sap;

import com.jackrain.nea.cpext.model.ProvinceCityAreaInfo;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.sap.SalesDataGatherBackSapR3Cmd;
import com.jackrain.nea.oc.oms.model.table.StCBusinessType;
import com.jackrain.nea.oc.oms.sap.SalesDataGatherBackSapService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Desc :
 * <AUTHOR> WANGJUN
 * @Date : 2022/8/24
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class SalesDataGatherBackSapCmdImpl extends CommandAdapter implements SalesDataGatherBackSapR3Cmd {

    @Autowired
    SalesDataGatherBackSapService salesDataGatherBackSapService;
    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        SalesDataGatherBackSapService bean = ApplicationContextHandle.getBean(SalesDataGatherBackSapService.class);
        return bean.backSalesDataGather(session);
    }

    @Override
    public ValueHolderV14 queryOcBOrderInfo(String billNo) throws Exception {
        return salesDataGatherBackSapService.queryOcBOrderInfo(billNo);
    }

    @Override
    public StCBusinessType queryStCBusinessType(Long id){
        return salesDataGatherBackSapService.queryStCBusinessType(id);
    }

    @Override
    public ProvinceCityAreaInfo selectByProvinceAndCityAndArea(String provinceName, String cityName, String areaName) {
        return salesDataGatherBackSapService.selectByProvinceAndCityAndArea(provinceName,cityName,areaName);
    }
}
