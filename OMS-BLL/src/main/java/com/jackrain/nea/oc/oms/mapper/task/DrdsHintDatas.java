package com.jackrain.nea.oc.oms.mapper.task;

import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR> 孙勇生
 * create at:  2020/3/11  12:49
 * @description: 分库信息
 */
@Slf4j
@Service
public class DrdsHintDatas {
    /**
     * key=NAME
     */
    private List<HashMap> nodeMap;
    @Autowired
    private DBManagerDrds dbManagerDrds;

    public List<HashMap> getNodeMap() {
        if (nodeMap != null) {
            return nodeMap;
        }

        OcBToBeConfirmedTaskMapper mapper = dbManagerDrds.getSqlSession().getMapper(OcBToBeConfirmedTaskMapper.class);
        try {
            nodeMap = mapper.selectNodeList();
        } catch (Exception e) {
            log.debug(LogUtil.format("DrdsHintDatas is not drds"));
        }
        return nodeMap;
    }


}
