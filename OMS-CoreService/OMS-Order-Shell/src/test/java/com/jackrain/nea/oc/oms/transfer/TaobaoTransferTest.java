package com.jackrain.nea.oc.oms.transfer;

import com.jackrain.nea.oc.oms.model.relation.IpTaobaoFxOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoOrderRelation;
import com.jackrain.nea.oc.oms.process.transfer.impl.normal.taobao.TaobaoTransferOrderProcessImpl;
import com.jackrain.nea.oc.oms.process.transfer.impl.normal.taobaofx.TaobaoFxTransferOrderProcessImpl;
import com.jackrain.nea.oc.oms.services.IpTaobaoFxService;
import com.jackrain.nea.oc.oms.services.IpTaobaoOrderService;
import com.jackrain.nea.resource.SystemUserResource;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Profile;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * @author: 易邵峰
 * @since: 2019-03-30
 * create at : 2019-03-30 20:16
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest()
@Profile(value = "dev")
public class TaobaoTransferTest {

    @Autowired
    private TaobaoTransferOrderProcessImpl orderProcess;

    @Autowired
    private IpTaobaoOrderService taobaoOrderService;

    @Autowired
    private TaobaoFxTransferOrderProcessImpl fxTransferOrderProcess;

    @Autowired
    private IpTaobaoFxService taobaoFxOrderService;

    @Test
    public void testTransfer001() {
        String orderNo = "1553509929850415012";
        IpTaobaoOrderRelation orderRelation = taobaoOrderService.selectTaobaoOrderByTid(orderNo);
        if (orderRelation != null) {
            orderProcess.start(orderRelation, false, SystemUserResource.getRootUser());
        }
    }

    @Test
    public void taobaoFxtestTransfer() {
        String orderNo = "35182391754959";
        IpTaobaoFxOrderRelation orderRelation = taobaoFxOrderService.selectTaobaoFxOrder(orderNo);
        if (orderRelation != null) {
            fxTransferOrderProcess.start(orderRelation, false, SystemUserResource.getRootUser());
        }
    }
}
