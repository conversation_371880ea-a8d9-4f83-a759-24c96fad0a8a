package com.jackrain.nea.oc.oms.util;

import com.alibaba.fastjson.JSON;
import com.jackrain.nea.oc.oms.config.OmsSystemConfig;
import com.jackrain.nea.oc.oms.model.enums.OmsPayType;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.result.StCWarehouseQueryResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2020/11/30 20:18
 * @desc
 */
@Component
@Slf4j
public class SplitOrderUtils {

    @Autowired
    private StRpcService stRpcService;

    @Autowired
    private OmsSystemConfig omsSystemConfig;
    /**
     * 仓库拆单redis key (默认前缀在common里面已处理)
     */
    private static final String IS_OPEN_WAREHOUSE_SPLIT_ORDER = "is_open_warehouse_split_order";

    /**
     * 是否开启仓库拆单
     *
     * @return boolean  是 , 否
     */
    public static boolean getIsOpenWareHouseSplitOrder() {
        return "是".equals(RedisOpsUtil.getStrRedisTemplate().opsForValue().get("business_system:" + IS_OPEN_WAREHOUSE_SPLIT_ORDER));
    }

    /**
     * 是否支持仓库拆单判断
     * 1.猫超直发，货到付款订单 不支持拆单
     * 2.未配置仓库拆单策略，不拆单
     * 3.仓库拆单总开关未开，不拆单
     * @param ocBOrder 订单信息
     * @return
     */
    public boolean isOpenWareHouseSplitOrder(OcBOrder ocBOrder) {
        StCWarehouseQueryResult stCWarehouseQueryResult =
                stRpcService.getStCWarehouseQueryResultByWareHouseId(ocBOrder.getCpCPhyWarehouseId());
        // 是否 配置了仓库拆单策略
        boolean isHasWarehouseSt = stCWarehouseQueryResult != null;
        // 是否 非猫超直发
        boolean isNotAliBabaAscp = !PlatFormEnum.ALIBABAASCP.getCode().equals(ocBOrder.getPlatform());
        // 是否 非货到付款订单
        boolean isNotCashOnDelivery = Optional.ofNullable(ocBOrder.getPayType()).orElse(1) != OmsPayType.CASH_ON_DELIVERY.toInteger();
        // 是否 开启仓库拆单
        boolean isOpenWareHouseSplitOrder = getIsOpenWareHouseSplitOrder();
        return isHasWarehouseSt && isNotAliBabaAscp && isNotCashOnDelivery && isOpenWareHouseSplitOrder;
    }
}
