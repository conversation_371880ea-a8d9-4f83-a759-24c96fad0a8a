package com.jackrain.nea.oc.oms.services.advance;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jackrain.nea.oc.oms.SplitReason;
import com.jackrain.nea.oc.oms.model.enums.OmsSpiltRuleEnum;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.SpiltOrderParam;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.util.OmsOrderSplitReasonUtil;
import com.jackrain.nea.oc.oms.util.OrderAmountUtil;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.st.service.OmsStCShopStrategyService;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: r3-oc-oms
 * @description: 预售拆单
 * @author: liuwj
 * @create: 2021-06-23 19:36
 **/
@Slf4j
@Component
public class OmsOrderAdvanceSplit {
    @Autowired
    private OmsOrderAdvanceDetentionService omsOrderAdvanceDetentionService;

    @Autowired
    private OrderAmountUtil orderAmountUtil;

    @Autowired
    private OmsStCShopStrategyService omsStCShopStrategyService;

    @Autowired
    OmsOrderSplitReasonUtil omsOrderSplitReasonUtil;

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    OmsOrderSpiltUtill omsOrderSpiltUtill;

    @Autowired
    private OmsOrderLogService omsOrderLogService;

    public void orderAdvanceSplit(OcBOrderRelation orderInfo, User operateUser) {
        List<OcBOrderRelation> ocBOrderRelationList = new ArrayList<>();
        List<OcBOrderRelation> retRelationList = new ArrayList<>();
        OcBOrder order = orderInfo.getOrderInfo();
        List<OcBOrderItem> ocBOrderItemList = orderInfo.getOrderItemList();
        //有预计发货日的分组
        Map<String, List<OcBOrderItem>> itemMap = new HashMap<>();
        //先过滤掉预计发货日为空的 然后分组
        List<OcBOrderItem> itemTimeNullList = new ArrayList<>();
        int n = 0;
        try {
            if (CollectionUtils.isNotEmpty(ocBOrderItemList)) {
                //不包含增品
                List<OcBOrderItem> orderItemList = omsOrderSpiltUtill.getOcBOrderItem(ocBOrderItemList);
                //获取所以挂靠赠品Map
                Map<String, List<OcBOrderItem>> isHangGiftMap = omsOrderSpiltUtill.getGiftListMap(ocBOrderItemList);
                //获取所有组合商品map（真实条码）
                Map<String, List<OcBOrderItem>> isGroupItemMap = omsOrderSpiltUtill.getIsGroupItemMap(ocBOrderItemList);
                //组合商品的明细id,如果组合商品有赠品，加入集合，在后面查找非挂靠赠品时排除赠品，解决组合商品赠品多发问题
                List<Long> noIds = Lists.newArrayList();
                Date estimateConTimeMax = null;
                if (CollectionUtils.isNotEmpty(orderItemList)) {
                    itemTimeNullList = orderItemList.stream().filter(o -> o.getEstimateConTime() == null && omsOrderSpiltUtill.checkFilteItem(o)).collect(Collectors.toList());
                    boolean flag = orderItemList.size() != itemTimeNullList.size();
                    if (itemTimeNullList.size() > 0) {
                        n++;
                        OcBOrderRelation ocBOrderRelation = new OcBOrderRelation();
                        String advanceTypeAll = "";
                        List<OcBOrderItem> newOcBOrderItemList = new ArrayList<>();
                        newOcBOrderItemList.addAll(itemTimeNullList);
                        for (OcBOrderItem orderItem : newOcBOrderItemList) {
                            if (orderItem.getAdvanceType() != null) {
                                if (advanceTypeAll != null && !advanceTypeAll.contains(orderItem.getAdvanceType())) {
                                    advanceTypeAll += orderItem.getAdvanceType() + ",";
                                }
                            }
                            //加入赠品
                            if (isHangGiftMap != null) {
                                List<OcBOrderItem> giftList = isHangGiftMap.get(omsOrderSpiltUtill.getPsCSkuEcodeKey(orderItem));
                                if (CollectionUtils.isNotEmpty(giftList)) {
                                    itemTimeNullList.addAll(giftList);
                                }
                            }
                            //加入组合商品
                            if (isGroupItemMap != null) {
                                List<OcBOrderItem> groupList = isGroupItemMap.get(orderItem.getPsCSkuEcode());
                                if (CollectionUtils.isNotEmpty(groupList)) {
                                    itemTimeNullList.addAll(groupList);

                                    //组合商品处理。如果里面有非挂靠关系的赠品，记录noIds里，后续排除
                                    giftExistDeal(noIds, groupList);
                                }
                            }
                            //预计发货时间取最大
                            if (orderItem.getEstimateConTime() != null) {
                                if (estimateConTimeMax == null) {
                                    estimateConTimeMax = orderItem.getEstimateConTime();
                                } else {
                                    if (orderItem.getEstimateConTime().before(estimateConTimeMax)) {
                                        estimateConTimeMax = orderItem.getEstimateConTime();
                                    }
                                }
                            }
                        }
                        // 构建订单头信息
                        OcBOrder ocBOrder = new OcBOrder();
                        BeanUtils.copyProperties(order, ocBOrder);
                        //拆单标识
                        ocBOrder.setSplitReason(SplitReason.SPLIT_BY_ADVANCE);
                        //预售 类型
                        if (advanceTypeAll.length() > 0) {
                            ocBOrder.setAdvanceType(advanceTypeAll.substring(0, advanceTypeAll.length() - 1));
                            //订单=普通商品，不显示预售标
                            if (AdvanceConstant.ORDINARY_GOODS.equals(advanceTypeAll.substring(0, advanceTypeAll.length() - 1))) {
                                ocBOrder.setDouble11PresaleStatus(0);
                                ocBOrder.setOrderType(OrderTypeEnum.NORMAL.getVal());
                            }
                        } else {
                            ocBOrder.setAdvanceType("");
                        }
                        ocBOrder.setEstimateConTime(estimateConTimeMax);
                        ocBOrderRelation.setOrderInfo(ocBOrder);
                        ocBOrderRelation.setOrderItemList(itemTimeNullList);
                        if (flag) {
                            ocBOrderRelationList.add(ocBOrderRelation);
                        }
                    }
                }
                if (CollectionUtils.isNotEmpty(orderItemList)) {
                    itemMap = orderItemList.stream().filter(i -> i.getEstimateConTime() != null).collect(Collectors.groupingBy(o -> o.getEstimateConTime().toString()));
                    if (itemMap.size() > 0) {
                        n += itemMap.size();
                    }
                }
                //判断店铺策略有没有勾选预售拆单
                boolean flag = true;
                StCShopStrategyDO stCShopStrategyDO = omsStCShopStrategyService.selectOcStCShopStrategy(order.getCpCShopId());
                if (stCShopStrategyDO != null) {
                    flag = stCShopStrategyDO.getIsAdvanceSplit() != null && "Y".equals(stCShopStrategyDO.getIsAdvanceSplit()) ? true : false;
                }
                log.info(LogUtil.format("店铺策略预售拆单按钮是否勾选,orderId={},flag={},itemMap.size()={},itemTimeNullList.size(){}"), order.getId(), flag, itemMap.size(), itemTimeNullList.size());
                //有预计发货日的，和无预计发货日的总和大于一就不拆
                if (!flag || (flag && n <= 1)) {
                    log.info(LogUtil.format("店铺策略未勾选预售拆单，或者不满足拆单,原单输出不拆,orderId={}", order.getId()), order.getId());
                    List<OcBOrderRelation> relation = new ArrayList<>();
                    relation.add(orderInfo);
                    return;
                } else {
                    log.info(LogUtil.format("满足拆单,orderId={}", order.getId()), JSON.toJSONString(order));
                    if (itemMap.size() > 0) {
                        for (String str : itemMap.keySet()) {
                            OcBOrderRelation ocBOrderRelation = new OcBOrderRelation();
                            List<OcBOrderItem> orderItems = itemMap.get(str);
                            //拆单赋值对应的预售类型，明细类型的综合
                            String advanceTypeAll = "";
                            estimateConTimeMax = null;
                            for (OcBOrderItem orderItem : orderItems) {
                                if (orderItem.getAdvanceType() != null) {
                                    if (advanceTypeAll != null && !advanceTypeAll.contains(orderItem.getAdvanceType())) {
                                        advanceTypeAll += orderItem.getAdvanceType() + ",";
                                    }
                                }
                                //预计发货时间取最大
                                if (orderItem.getEstimateConTime() != null) {
                                    if (estimateConTimeMax == null) {
                                        estimateConTimeMax = orderItem.getEstimateConTime();
                                    } else {
                                        if (orderItem.getEstimateConTime().before(estimateConTimeMax)) {
                                            estimateConTimeMax = orderItem.getEstimateConTime();
                                        }
                                    }
                                }
                            }
                            // 构建订单头信息
                            OcBOrder ocBOrder = new OcBOrder();
                            BeanUtils.copyProperties(order, ocBOrder);
                            //拆单标识
                            ocBOrder.setSplitReason(SplitReason.SPLIT_BY_ADVANCE);
                            //预售 类型
                            if (advanceTypeAll.length() > 0) {
                                ocBOrder.setAdvanceType(advanceTypeAll.substring(0, advanceTypeAll.length() - 1));
                                //订单=普通商品，不显示预售标
                                if (AdvanceConstant.ORDINARY_GOODS.equals(advanceTypeAll.substring(0, advanceTypeAll.length() - 1))) {
                                    ocBOrder.setDouble11PresaleStatus(0);
                                    ocBOrder.setOrderType(OrderTypeEnum.NORMAL.getVal());
                                }
                            } else {
                                ocBOrder.setAdvanceType("");
                            }
                            ocBOrder.setEstimateConTime(estimateConTimeMax);
                            List<OcBOrderItem> newOcBOrderItemList = new ArrayList<>();
                            newOcBOrderItemList.addAll(orderItems);
                            for (OcBOrderItem orderItem : newOcBOrderItemList) {
                                //加入赠品
                                if (isHangGiftMap != null) {
                                    List<OcBOrderItem> giftList = isHangGiftMap.get(omsOrderSpiltUtill.getPsCSkuEcodeKey(orderItem));
                                    if (CollectionUtils.isNotEmpty(giftList)) {
                                        orderItems.addAll(giftList);
                                    }
                                }
                                //加入组合商品
                                if (isGroupItemMap != null) {
                                    List<OcBOrderItem> groupList = isGroupItemMap.get(orderItem.getPsCSkuEcode());
                                    if (CollectionUtils.isNotEmpty(groupList)) {
                                        orderItems.addAll(groupList);

                                        //组合商品处理。如果里面有非挂靠关系的赠品，记录noIds里，后续排除
                                        giftExistDeal(noIds, groupList);
                                    }
                                }
                            }
                            ocBOrderRelation.setOrderInfo(ocBOrder);
                            ocBOrderRelation.setOrderItemList(orderItems);
                            ocBOrderRelationList.add(ocBOrderRelation);
                        }
                    }
                }
                //所有非挂靠的赠品挂在卡单上；排除已有明细
                List<OcBOrderItem> ocBOrderItems = ocBOrderItemList.stream().filter(p -> !noIds.contains(p.getId())).collect(Collectors.toList());
                List<OcBOrderItem> noGiftRelationList = omsOrderSpiltUtill.getNoGiftRelationList(ocBOrderItems);
                if (CollectionUtils.isNotEmpty(noGiftRelationList)) {
                    ocBOrderRelationList.get(ocBOrderRelationList.size() - 1).getOrderItemList().addAll(noGiftRelationList);
                }
                //自定义拆单赋值
                omsOrderSplitReasonUtil.setCustomReason(ocBOrderRelationList);
                int suffixInfo = 0;
                Map<Set<Long>, SpiltOrderParam> paramMap = new HashMap<>();
                for (OcBOrderRelation relation : ocBOrderRelationList) {
                    List<OcBOrderItem> orderItemList1 = relation.getOrderItemList();
                    Set<Long> itemIds = orderItemList1.stream().map(OcBOrderItem::getId).collect(Collectors.toSet());
                    paramMap.put(itemIds, new SpiltOrderParam());

                }
                Map<Integer, Map<Set<Long>, SpiltOrderParam>> spiltRule = orderInfo.getSpiltRule();
                spiltRule.put(OmsSpiltRuleEnum.PRE_SALE.getCode(), paramMap);
                orderInfo.setSpiltRule(spiltRule);

            }
        } catch (Exception e) {
            e.printStackTrace();
            String erroMsg = "OrderId=" + order.getId() + "预售拆单异常,异常信息为" + e.getMessage();
            log.error(LogUtil.format(erroMsg));
            OcBOrder lOrder = new OcBOrder();
            lOrder.setId(order.getId());
            lOrder.setSysremark(erroMsg);
            omsOrderService.updateOrderInfo(lOrder);
        }
    }

    private void giftExistDeal(List<Long> noIds, List<OcBOrderItem> groupList) {
        List<OcBOrderItem> noGiftRelationList = omsOrderSpiltUtill.getNoGiftRelationList(groupList);
        if (CollectionUtils.isNotEmpty(noGiftRelationList)) {
            noIds.addAll(noGiftRelationList.stream().map(OcBOrderItem::getId).collect(Collectors.toList()));
        }
    }
}
