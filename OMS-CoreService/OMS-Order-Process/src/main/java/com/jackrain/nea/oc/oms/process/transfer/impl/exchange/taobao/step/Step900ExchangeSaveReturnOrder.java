package com.jackrain.nea.oc.oms.process.transfer.impl.exchange.taobao.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoExchangeRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoExchange;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 保存退换货订单信息
 * 1）判断退换货订单是否存在
 * 2）不存在新增退换货信息
 * 3）存在更新收货人信息、及
 *
 * @author: 孙勇生
 * @since: 2019-03-07
 * create at : 2019-01-21 14:44
 */
@Step(order = 90, description = "保存退换货订单信息")
@Slf4j
@Component
public class Step900ExchangeSaveReturnOrder extends BaseTaobaoExchangeProcessStep
        implements IOmsOrderProcessStep<IpTaobaoExchangeRelation> {


    @Override
    public ProcessStepResult<IpTaobaoExchangeRelation> startProcess(IpTaobaoExchangeRelation orderInfo,
                                                                    ProcessStepResult preStepResult,
                                                                    boolean isAutoMakeup, User operateUser) {
        if (log.isDebugEnabled()) {
            log.debug("Start.Step90ExchangeSaveReturnOrder.OrderNo={}", orderInfo.getOrderNo());
        }
        //调用保存的逻辑
        IpBTaobaoExchange taobaoExchange = orderInfo.getTaobaoExchange();
        List<OcBOrderItem> originalOrderItemList = orderInfo.getOriginalOrderItemList();

        try {
            Long aLong = omsReturnOrderService.saveOmsExchangeOrderInfo(orderInfo, operateUser);
            if (aLong != null) {
                //获取
                String exchangeSku = orderInfo.getExchangeSku();
                if (CollectionUtils.isNotEmpty(originalOrderItemList)) {
                    //因为换货只会对应一条明细
//                    String skuEcode = "";
                    StringBuilder sbSkuEcode = new StringBuilder();
                    for (OcBOrderItem orderItemInfo : originalOrderItemList) {
                        sbSkuEcode.append(orderItemInfo.getPsCSkuEcode());
                        sbSkuEcode.append(";");
//                        skuEcode = skuEcode + orderItemInfo.getPsCSkuEcode() + ";";
                    }
                    String skuEcode = sbSkuEcode.toString().substring(0, sbSkuEcode.toString().length() - 1);
                    //同时更新换货中间表的的换货skuid以及原始的skuid
                    ipTaobaoExchangeService.updateExchangeIsSuccess(aLong, operateUser, exchangeSku, skuEcode, orderInfo);
                }
            } else {
                //更新完成
                ipTaobaoExchangeService.updateExchangeIsTransTransferring(taobaoExchange, TransferOrderStatus.TRANSFERRED.toInteger());
            }
            return new ProcessStepResult<>(StepStatus.FINISHED);
        } catch (Exception e) {
            ipTaobaoExchangeService.updateExchangeIsTransError(taobaoExchange, e.getMessage());
            log.error(this.getClass().getName() + " 退换货转换异常", e);
            String errorMessage = "退换货转换异常!" + e.getMessage();
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
    }
}
