package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoOrderItem;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoOrderItemEx;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;


@Mapper
@Component
public interface IpBTaobaoOrderItemMapper extends ExtentionMapper<IpBTaobaoOrderItem> {

    @Select("SELECT * FROM ip_b_taobao_order_item WHERE ip_b_taobao_order_id=#{orderId}")
    List<IpBTaobaoOrderItemEx> selectOrderItemList(@Param("orderId") long orderId);

    /**
     * 胡林洋 2019/4/4 新增【发货平台服务用】
     * 根据子订单编号查询，淘宝订单明细中间表数据
     *
     * @param tid 分库键，主表订单id
     * @param oid 子表订单编号
     * @return List<IpBTaobaoOrderItem>
     */
    @Select("SELECT b.* FROM ip_b_taobao_order a ,ip_b_taobao_order_item b WHERE a.id = b.ip_b_taobao_order_id and a.tid = #{tid} and b.oid = #{oid}")
    List<IpBTaobaoOrderItem> selectTaobaoOrderItemList(@Param("oid") String oid, @Param("tid") long tid);

    @Select(" SELECT * FROM ip_b_taobao_order  a ,ip_b_taobao_order_item b  where a.id = b.ip_b_taobao_order_id and b.ip_b_taobao_order_id = #{orderId} AND a.TID = #{tid}")
    List<IpBTaobaoOrderItem> selectTaobaoOrderItemByTid(@Param("orderId") long orderId, @Param("tid") long tid);

    @Select("SELECT * FROM ip_b_taobao_order_item WHERE ip_b_taobao_order_id = #{orderId} AND oid = #{oid}")
    IpBTaobaoOrderItem selectTaobaoOrderItemByOid(@Param("orderId") long orderId, @Param("oid") String oid);

}