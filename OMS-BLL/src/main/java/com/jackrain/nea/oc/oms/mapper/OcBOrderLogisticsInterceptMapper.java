package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrderLogisticsIntercept;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
public interface OcBOrderLogisticsInterceptMapper extends ExtentionMapper<OcBOrderLogisticsIntercept> {

    @Select("SELECT * FROM oc_b_order_logistics_intercept WHERE order_id = #{orderId}")
    List<OcBOrderLogisticsIntercept> selectByOrderId(@Param("orderId") Long orderId);

    @Select("SELECT * FROM oc_b_order_logistics_intercept WHERE platform_code = #{tid}")
    List<OcBOrderLogisticsIntercept> selectByTid(@Param("tid") String tid);

    /**
     * 获取所有没同步卖家备注到平台的拦截单
     *
     * @return 1
     */
    @Select("SELECT * FROM oc_b_order_logistics_intercept WHERE sync_seller_memo = 0 and intercept_status not in (0,2) limit 200")
    List<OcBOrderLogisticsIntercept> select4SyncSellerMemo();

    /**
     * 修改备注回传平台状态
     *
     * @param ids
     * @return
     */
    @Update("<script> "
            + "UPDATE oc_b_order_logistics_intercept SET sync_seller_memo = 1, modifieddate = now() where id in "
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    int batchUpdateSyncSellerMemo(@Param("ids") List<Long> ids);

}