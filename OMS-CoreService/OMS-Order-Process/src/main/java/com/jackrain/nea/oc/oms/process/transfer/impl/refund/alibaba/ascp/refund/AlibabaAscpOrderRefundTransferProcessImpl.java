package com.jackrain.nea.oc.oms.process.transfer.impl.refund.alibaba.ascp.refund;

import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.MakeupOrderType;
import com.jackrain.nea.oc.oms.model.relation.IpBAlibabaAscpOrderRefundRelation;
import com.jackrain.nea.oc.oms.process.AbstractOrderProcess;
import com.jackrain.nea.oc.oms.util.LockOrderType;
import org.springframework.stereotype.Component;

/**
 * @Descroption 猫超直发退货转换
 * <AUTHOR>
 * @Date 2020/09/04 18:27
 */
@Component
public class AlibabaAscpOrderRefundTransferProcessImpl extends AbstractOrderProcess<IpBAlibabaAscpOrderRefundRelation> {
    @Override
    protected String getChildPackageName() {
        return null;
    }

    @Override
    protected long getProcessOrderId(IpBAlibabaAscpOrderRefundRelation orderInfo) {
        return orderInfo.getOrderId();
    }

    @Override
    protected String getProcessOrderNo(IpBAlibabaAscpOrderRefundRelation orderInfo) {
        return null;
    }

    @Override
    protected LockOrderType getCurrentLockOrderType() {
        return LockOrderType.TRANSFER_ALIBABA_ASCP_REFUND;
    }

    @Override
    protected ChannelType getCurrentChannelType() {
        return ChannelType.ALIBABAASCP;
    }

    @Override
    protected MakeupOrderType getCurrentMakeupOrderType() {
        return null;
    }
}
