package com.jackrain.nea.oc.services.invoice;

import com.jackrain.nea.ac.service.InvoiceApplyService;
import com.jackrain.nea.oc.oms.api.invoice.InvoiceApplySaveCmd;
import com.jackrain.nea.oc.oms.model.table.AcFInvoiceApply;
import com.jackrain.nea.oc.oms.services.invoice.InvoiceApplySaveService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class InvoiceApplySaveCmdImpl implements InvoiceApplySaveCmd {

    @Autowired
    private InvoiceApplySaveService service;

    @Override
    public ValueHolderV14 save(List<AcFInvoiceApply> list) {
        return service.save(list);
    }
}
