package com.jackrain.nea.oc.oms.services.audit.wait;

/**
 * @Auther: 黄志优
 * @Date: 2020/12/11 16:30
 * @Description: 审核计算时间原因
 */
public enum OmsAuditTimeCalculateReason {

    HOLD(0, "Hold单"),
    RESERVE_AUDIT(1, "反审核"),
    OCCUPY(2, "占单"),
    MERGE(3, "合单"),
    CANCEL_MERGE(4, "取消合并"),
    SPLIT(5, "拆单"),
    UPDATE_WAREHOUSE(6, "修改仓库");


    private int key;
    private String value;


    OmsAuditTimeCalculateReason(int key, String value) {
        this.key = key;
        this.value = value;
    }

    public int getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }
}
