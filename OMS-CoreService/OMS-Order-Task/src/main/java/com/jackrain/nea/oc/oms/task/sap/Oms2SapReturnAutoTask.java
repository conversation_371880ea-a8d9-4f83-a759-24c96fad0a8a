//package com.jackrain.nea.oc.oms.task.sap;
//
//import com.jackrain.nea.cpext.model.table.CpShop;
//import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
//import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderRefundMapper;
//import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
//import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
//import com.jackrain.nea.oc.oms.sap.SapTaskTableEnum;
//import com.jackrain.nea.rpc.CpRpcService;
//import com.jackrain.nea.util.SapModelTransferUtil;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.util.ArrayList;
//import java.util.Date;
//import java.util.List;
//
///**
// * @Desc :
// * <AUTHOR> xiWen
// * @Date : 2020/3/23
// */
//@Slf4j
//@Component
//@Deprecated
//public class Oms2SapReturnAutoTask extends AbstractOms2SapTask {
//
//    @Autowired
//    private OcBReturnOrderMapper ocBReturnOrderMapper;
//
//    @Autowired
//    private OcBReturnOrderRefundMapper ocBReturnOrderRefundMapper;
//
//    @Autowired
//    private CpRpcService cpRpcService;
//
//
//    private String threadPoolName = "R3_OMS_SAP_RETURN_TASK_THREAD_POOL_%d";
//
//
//    @Override
//    protected String getTag() {
//        return oms2SapMqConfig.getOms2SapReturnTag();
//    }
//
//    @Override
//    protected String getTopic() {
//        return oms2SapMqConfig.getOms2SapReturnTopic();
//    }
//
//    @Override
//    protected String getTaskTableName() {
//        return SapTaskTableEnum.RETURN.txt();
//    }
//
//    @Override
//    protected String getOriginTableName() {
//        return "OC_B_RETURN_ORDER";
//    }
//
//    @Override
//    protected String getOriginCol() {
//        //return "RESERVE_BIGINT02"; @20200222 改字段不准确
//        return "TO_SAP_STATUS";
//    }
//
//    @Override
//    protected String getThreadPoolName() {
//        return threadPoolName;
//    }
//
//    @Override
//    protected List getSapItems(List<Long> list, List<Long> activeIds) {
//        return null;
//    }
//
//    //@Override
////    protected List<B2cOrderRefundRequest> getSapItems(List<Long> list, List<Long> activeIds) {
////
////        String ids = joinList2String(list);
////        if (log.isDebugEnabled()) {
////            log.debug("Oms2SapReturnAutoTask.SapItemsid集合:{}", ids);
////        }
////        List<OcBReturnOrder> returnList = ocBReturnOrderMapper.selectListForSapByIds(ids);
////        if (log.isDebugEnabled()) {
////            log.debug("Oms2SapReturnAutoTask.ocBorders集合:{}", returnList);
////        }
////        if (unExpect.test(returnList)) {
////            return null;
////        }
////        List<OcBReturnOrder> returnOrderList = new ArrayList<>();
////        for (OcBReturnOrder bReturnOrder : returnList) {
////            bReturnOrder.setCreationdate(new Date());
////            if (null == bReturnOrder.getInTime()) {
////                bReturnOrder.setInTime(new Date());
////            }
////            //@20200811 调用渠道店铺接口 防止发送SAP shopcode为空
////            if (null != bReturnOrder.getCpCShopId() && StringUtils.isEmpty(bReturnOrder.getCpCShopEcode())) {
////                CpShop cpShop = cpRpcService.selectShopById(bReturnOrder.getCpCShopId());
////                if (log.isDebugEnabled()) {
////                    log.debug("Oms2SapReturnAutoTask.getSapItems.cpShop集合为空,bReturnID:{},平台档案ID:{}", bReturnOrder.getId(), bReturnOrder.getCpCShopId());
////                }
////                if (cpShop != null) {
////                    //获取平台店铺档案 ecode
////                    bReturnOrder.setCpCShopEcode(cpShop.getEcode());
////                }
////            }
////            returnOrderList.add(bReturnOrder);
////        }
////        List<OcBReturnOrderRefund> rfnList = ocBReturnOrderRefundMapper.queryReturnOrderRefundByoIds(ids, "Y");
////        if (unExpect.test(rfnList)) {
////            return null;
////        }
////        orderIdFun = o -> ((OcBReturnOrder) o).getId();
////        statisticsCsm(returnList, activeIds);
////        return SapModelTransferUtil.saleReturnTransfer(returnOrderList, rfnList);
////    }
//}
