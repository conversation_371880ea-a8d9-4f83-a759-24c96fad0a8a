package com.jackrain.nea.oc.oms.services;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpCLogistics;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.gsi.GSI4Order;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderNaiKaMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderPaymentMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderSourceRelationMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnAfSendItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnAfSendMapper;
import com.jackrain.nea.oc.oms.mapper.task.OcBToBeConfirmedTaskMapper;
import com.jackrain.nea.oc.oms.matcher.live.LiveMatchManager;
import com.jackrain.nea.oc.oms.model.enums.AGStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.CardAutoVoidEnum;
import com.jackrain.nea.oc.oms.model.enums.GiftTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OcBOrderSourceRelationTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OcBorderListEnums;
import com.jackrain.nea.oc.oms.model.enums.OcOrderCheckBoxEnum;
import com.jackrain.nea.oc.oms.model.enums.OcOrderDoublellPresaleStatus;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderBusinessTypeCodeEnum;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnAfSendReturnBillTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.ToACStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.enums.ac.PayTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.naika.OmsOrderNaiKaStatusEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderParam;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.resources.OrderOccupyStatus;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderNaiKa;
import com.jackrain.nea.oc.oms.model.table.OcBOrderPayment;
import com.jackrain.nea.oc.oms.model.table.OcBOrderSourceRelation;
import com.jackrain.nea.oc.oms.model.table.OcBReturnAfSend;
import com.jackrain.nea.oc.oms.model.table.OcBReturnAfSendItem;
import com.jackrain.nea.oc.oms.model.table.StCBusinessType;
import com.jackrain.nea.oc.oms.model.table.task.OcBToBeConfirmedTask;
import com.jackrain.nea.oc.oms.nums.OcBOrderNodeEnum;
import com.jackrain.nea.oc.oms.nums.OcOrderBillResetShipTypeEnum;
import com.jackrain.nea.oc.oms.nums.OcOrderRefundStatusEnum;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.nums.RefundOrderSourceTypeEnum;
import com.jackrain.nea.oc.oms.nums.RefundSourceEnum;
import com.jackrain.nea.oc.oms.services.naika.OmsNaiKaOrderVoidService;
import com.jackrain.nea.oc.oms.services.task.OmsToBeConfirmedTaskService;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.oc.oms.util.OrderAmountUtil;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.psext.result.ProExtResult;
import com.jackrain.nea.rpc.AcRpcService;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.sg.service.SgOccupiedInventoryService;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.st.service.OmsStCShopStrategyService;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.util.OperateUserUtils;
import com.jackrain.nea.util.OrderAddressConvertUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 *
 */
@Slf4j
@Component
public class SaveBillService {
    //订单明细表
    private static final String ORDER_ITEM_TABLE_NAME = "OC_B_ORDER_ITEM";
    //订单表
    private static final String ORDER_TABLE_NAME = "OC_B_ORDER";

    private static final String ORDER_AF_RETURN_TABLE_NAME = "oc_b_return_af_send";

    private static final String ORDER_AF_RETURN_ITEM_TABLE_NAME = "oc_b_return_af_send_item";

    //支付信息表
    private static final String OC_B_ORDER_PAYMENT = "OC_B_ORDER_PAYMENT";

    private static final String MESSAGE = "message";

    //组合商品标识前缀
    private static final String GROUP_GOODS_MARK = "CG";

    private static final String REG = "[\ud83c\udc00-\ud83c\udfff]|[\ud83d\udc00-\ud83d\udfff]|[\u2600-\u27ff]";

    @Autowired
    OcBOrderMapper ocBOrderMapper;
    @Autowired
    OcBOrderItemMapper ocBorderItemMapper;
    @Autowired
    private OcBOrderPaymentMapper paymentMapper;
    @Autowired
    OmsOrderLogService omsOrderLogService;
    @Autowired
    OmsOrderDistributeWarehouseService omsWarehousRuleService;
    @Autowired
    OmsOrderDistributeLogisticsService omsOrderDistributeLogisticsService;
    @Autowired
    OmsOrderService omsOrderService;
    @Autowired
    CpRpcService cpRpcService;
    @Autowired
    private LiveMatchManager liveMatchManager;

    @Autowired
    private PsRpcService psRpcService;
    @Autowired
    private BuildSequenceUtil sequenceUtil;
    @Autowired
    private OmsConstituteSplitService omsConstituteSplitService;
    @Autowired
    private SgOccupiedInventoryService sgOccupiedInventoryService;
    @Autowired
    private AcRpcService acRpcService;
    @Autowired
    private IpRpcService ipRpcService;
    @Autowired
    private OrderAmountUtil orderAmountUtil;

    @Autowired
    private OmsStCShopStrategyService omsStCShopStrategyService;

    @Autowired
    private OcBToBeConfirmedTaskMapper toBeConfirmedTaskMapper;

    @Autowired
    private OmsToBeConfirmedTaskService toBeConfirmedTaskService;

    @Autowired
    private OcBOrderNodeRecordService ocBOrderNodeRecordService;
    @Autowired
    private OcBReturnAfSendMapper ocBReturnAfSendMapper;
    @Autowired
    private OcBReturnAfSendItemMapper ocBReturnAfSendItemMapper;
    @Autowired
    private OcBOrderHoldService ocBOrderHoldService;
    @Autowired
    private OcBOrderNaiKaMapper ocBOrderNaiKaMapper;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private OmsOccupyTaskService omsOccupyTaskService;
    @Autowired
    private OmsNaiKaOrderVoidService omsNaiKaOrderVoidService;
    @Autowired
    private OmsExpiryDateStService omsExpiryDateStService;
    @Autowired
    private OmsRefundOrderService omsRefundOrderService;
    @Autowired
    private BuildSequenceUtil buildSequenceUtil;
    @Autowired
    private OcBOrderSourceRelationMapper sourceRelationMapper;


    private final String[] businessTypeArray = new String[]{"RYCK04", "RYCK05", "RYCK12", "RYCK13"};

    /**
     * saveBill
     * 保存订单
     *
     * @param obj  参数
     * @param user 用户
     * @return 结果
     */
    public ValueHolder saveBill(JSONObject obj, User user, Boolean resetShip) {
        ValueHolder vh = new ValueHolder();
        try {
            if (log.isDebugEnabled()) {
                log.debug(" SaveBillService saveBill  obj:{}", JSONObject.toJSONString(obj));
            }
            //订单
            JSONObject ocBorderJson = obj.getJSONObject("ocBorderDto");
            //订单明细
            JSONArray ocBorderItemJsonArray = obj.getJSONArray("ocBorderItemDto");
            if (ocBorderJson == null) {
                throw new NDSException("订单主表信息为空！");
            }
            if (ocBorderItemJsonArray == null || ocBorderItemJsonArray.isEmpty()) {
                throw new NDSException("订单明细表为空！");
            }
            Long orderId = obj.getLong("orderId");
            // 构建订单头表数据 1.丢单补发 2.错发补发 3.漏发补发 4.赠品出库补发 5.JITX发货异常补发  不传就是新增
            Integer type = obj.getInteger("type");
            String typeName = obj.getString("typeName");
            OcBOrder ocBOrder = JSON.parseObject(ocBorderJson.toJSONString(), OcBOrder.class);
            // 校验订单基本信息
            chcekOrderParam(ocBOrder, orderId, type);
            //查询店铺策略 是否允许手工建单
            StCShopStrategyDO shopStrategyDO = omsStCShopStrategyService.selectOcStCShopStrategy(ocBOrder.getCpCShopId());
            if (shopStrategyDO != null) {
                if (!YesNoEnum.Y.getKey().equals(shopStrategyDO.getIsManuallyCreate())) {
                    throw new NDSException("当前店铺未开启手工建单，不允许新增");
                }
            }
            List<OcBOrderItem> orderItems = JSON.parseArray(ocBorderItemJsonArray.toJSONString(), OcBOrderItem.class);
            if (CollectionUtils.isEmpty(orderItems)) {
                throw new NDSException("订单明细表为空！");
            }
            // 构建订单头表数据 1.丢单补发 2.错发补发 3.漏发补发 4.赠品出库补发 5.JITX发货异常补发  不传就是新增
            //查询原单
            OcBOrder oldOrder = ocBOrderMapper.selectById(orderId);
            if (type != null && orderId != null) {
                if (ocBOrder.getIsResetShip() != null && ocBOrder.getIsResetShip() == 1) {
                    if (type == 1) {
                        ocBOrder.setIsLoseCopyOrder(1);
                        ocBOrder.setCopyReason(OcOrderBillResetShipTypeEnum.LOST_ORDER_RESET_SHIP.getName());
                    } else if (type == 2) {
                        ocBOrder.setCopyReason(OcOrderBillResetShipTypeEnum.ORDER_WRONG_RESET_SHIP.getName());
                    } else if (type == 3) {
                        ocBOrder.setCopyReason(OcOrderBillResetShipTypeEnum.ORDER_MISS_RESET_SHIP.getName());
                    } else if (type == 4) {
                        ocBOrder.setCopyReason(OcOrderBillResetShipTypeEnum.GIFT_RESET_SHIP.getName());
                    } else if (type == 5) {
                        ocBOrder.setCopyReason(OcOrderBillResetShipTypeEnum.JITX_ORDER_RESET_SHIP.getName());
                    } else if (type == 6) {
                        ocBOrder.setCopyReason(OcOrderBillResetShipTypeEnum.OTHER_ORDER_RESET_SHIP.getName());
                    }
                    resetShip = Boolean.TRUE;
                } else if (ocBOrder.getIsCopyOrder() == 1) {
                    if (type == 1) {
                        ocBOrder.setCopyReason("原单无效复制");
                    } else {
                        ocBOrder.setCopyReason("正常复制");
                    }
                }
                if (StringUtils.isNotBlank(typeName)) {
                    ocBOrder.setCopyReason(typeName);
                }

                //收货人信息处理：如果做了修改，带*的话提示格式有误，复制后的收货人信息解密后保存
                // 如果是补发单 然后又是抖音的话 则不进行复制
                // 202403071020: 只要是抖音都不用解密 20240821只要是京东、京东供销、京东厂直都不用解密
                if (!(ObjectUtil.equal(PlatFormEnum.DOU_YIN.getCode(), oldOrder.getPlatform())
                        || ObjectUtil.equal(PlatFormEnum.JINGDONG.getCode(), oldOrder.getPlatform())
                        || ObjectUtil.equal(PlatFormEnum.JINGDONG_CZ.getCode(), oldOrder.getPlatform())
                        || ObjectUtil.equal(PlatFormEnum.JINGDONG_DX.getCode(), oldOrder.getPlatform())
                )) {
                    copyBuyerInfo(ocBOrder, oldOrder);
                }
                // 如果原单是小红书订单，则不清空oaid
                if (PlatFormEnum.HONGSHU_OPEN.getCode().equals(oldOrder.getPlatform())) {
                    ocBOrder.setOaid(oldOrder.getOaid());
                }

                ocBOrder.setGwSourceCode(oldOrder.getGwSourceCode());
                String receiverAddress = ocBOrder.getReceiverAddress();
                String receiverAddress1 = oldOrder.getReceiverAddress();
                //继承原单标
                markTag(ocBOrder, oldOrder);
                //清除不继承的标
                clearTag(ocBOrder);

                //重新计算赠、组、播标
                calculationTag(ocBOrder, orderItems);
                if (PlatFormEnum.VIP_JITX.getCode().equals(oldOrder.getPlatform())) {
                    ocBOrder.setCpCPhyWarehouseId(oldOrder.getCpCPhyWarehouseId());
                    ocBOrder.setCpCPhyWarehouseEcode(oldOrder.getCpCPhyWarehouseEcode());
                    ocBOrder.setCpCPhyWarehouseEname(oldOrder.getCpCPhyWarehouseEname());
                    ocBOrder.setMergedCode(oldOrder.getMergedCode());
                    ocBOrder.setIsStoreDelivery(oldOrder.getIsStoreDelivery());
                    ocBOrder.setJitxMergedDeliverySn(oldOrder.getJitxMergedDeliverySn());
                    ocBOrder.setMergedSn(oldOrder.getMergedSn());
                    ocBOrder.setJitxRequiresMerge(oldOrder.getJitxRequiresMerge());
                    ocBOrder.setJitxRequiresDeliveryWarehouseName(oldOrder.getJitxRequiresDeliveryWarehouseName());
                    ocBOrder.setJitxRequiresDeliveryWarehouseId(oldOrder.getJitxRequiresDeliveryWarehouseId());
                    ocBOrder.setJitxRequiresDeliveryWarehouseCode(oldOrder.getJitxRequiresDeliveryWarehouseCode());
                }
            }
            this.buildOcBOrder(ocBOrder, orderId, user, oldOrder);
            // 构建订单明细表数据
            this.buildOcBOrderItems(orderItems, orderId, ocBOrder, user);
            OcBOrderRelation ocBOrderRelation = new OcBOrderRelation();
            ocBOrderRelation.setOrderInfo(ocBOrder);
            ocBOrderRelation.setOrderItemList(orderItems);
            // 重新计算ocBOrder对应的金额
            orderAmountUtil.recountOrderAmount(ocBOrderRelation);
            //判断是否为赠品订单新增
            if (obj.getInteger("isGift") != null && obj.getInteger("isGift") == 1) {
                OcBOrder orderInfo = ocBOrderRelation.getOrderInfo();
                // 商品金额
                orderInfo.setProductAmt(BigDecimal.ZERO);
                // 服务费
                orderInfo.setServiceAmt(BigDecimal.ZERO);
                // 物流费用
                orderInfo.setShipAmt(BigDecimal.ZERO);
                // 商品优惠金额
                orderInfo.setProductDiscountAmt(BigDecimal.ZERO);
                // 订单优惠金额
                orderInfo.setOrderDiscountAmt(BigDecimal.ZERO);
                // 调整金额
                orderInfo.setAdjustAmt(BigDecimal.ZERO);
                // 订单总金额
                orderInfo.setOrderAmt(BigDecimal.ZERO);
                // 已收金额（对应详情页已支付金额）已支付金额 = 订单总金额 (拆合单时的逻辑)
                orderInfo.setReceivedAmt(BigDecimal.ZERO);
                ocBOrder.setIsHasgift(YesNoEnum.Y.getVal());

                for (OcBOrderItem item : ocBOrderRelation.getOrderItemList()) {
                    // 成交金额
                    item.setRealAmt(BigDecimal.ZERO);
                    item.setAdjustAmt(BigDecimal.ZERO);
                    item.setOrderSplitAmt(BigDecimal.ZERO);
                    item.setPriceActual(BigDecimal.ZERO);
                    item.setPrice(BigDecimal.ZERO);
                    item.setIsGift(1);
                }
            }
            log.info(" 零售发货单新增封装参数2 {}", JSON.toJSONString(ocBOrderRelation));
            // 保存订单信息
            ApplicationContextHandle.getApplicationContext()
                    .getBean(SaveBillService.class).saveOrderRelation(ocBOrderRelation, type, orderId, obj.getString("isGift"), user);
            Boolean jitxReDelivery = obj.getBoolean("jitx_redelivery");
            if (jitxReDelivery != null && jitxReDelivery) {
                OcBOrder orderInfo = ocBOrderRelation.getOrderInfo();
                omsOrderLogService.addUserOrderLog(orderInfo.getId(), orderInfo.getBillNo(), OrderLogTypeEnum.JITX_REDELIVERY_SPLIT.getKey(), "操作门店发货异常补发功能生成该笔订单!", null, null, user);
            }
            ocBOrderNodeRecordService.insertByNode(OcBOrderNodeEnum.ORDER_CREAT, new Date(), ocBOrderRelation.getOrderInfo().getId(), user);
            vh.put("code", ResultCode.SUCCESS);
            vh.put("data", ocBOrder.getId());
            vh.put(MESSAGE, Resources.getMessage("成功", user.getLocale()));
            return vh;
        } catch (Exception e) {
            log.info(LogUtil.format("SaveBillService.saveBill 保存订单失败,异常信息为:{}", "保存订单失败"), Throwables.getStackTraceAsString(e));
            vh.put("code", ResultCode.FAIL);
            vh.put(MESSAGE, Resources.getMessage(e.getMessage(), user.getLocale()));
            return vh;
        }
    }


    /**
     * 复制收货人信息
     *
     * @param dest 复制后订单
     * @param orig 原始订单
     */
    public void copyBuyerInfo(OcBOrder dest, OcBOrder orig) {
        //做了修改，带*的话提示格式有误，复制后的收货人信息解密后保存

        //做了修改统一清除oaid
        //dest.setOaid("");

        boolean encrypted = getNotNullString(orig.getReceiverName()).contains("*") ||
                getNotNullString(orig.getReceiverMobile()).contains("*") ||
                getNotNullString(orig.getReceiverPhone()).contains("*") ||
                getNotNullString(orig.getReceiverAddress()).contains("*");

        //原本单据未加密直接返回(淘宝),其他平台待定判断方式
        if (!encrypted) {
            dest.setOaid("");
            return;
        }

        String name = getNotNullString(dest.getReceiverName());
        String mobile = getNotNullString(dest.getReceiverMobile());
        String phone = getNotNullString(dest.getReceiverPhone());
        String address = getNotNullString(dest.getReceiverAddress());

        OcBOrder decryptOrder = new OcBOrder();
        BeanUtils.copyProperties(orig, decryptOrder);

        ipRpcService.decrypt(decryptOrder);

        boolean nameModified = !getNotNullString(decryptOrder.getReceiverName()).equals(name);
        boolean mobileModified = !getNotNullString(decryptOrder.getReceiverMobile()).equals(mobile);
        boolean phoneModified = !getNotNullString(decryptOrder.getReceiverPhone()).equals(phone);
        boolean addrModified = !getNotNullString(decryptOrder.getReceiverAddress()).equals(address);

        if (nameModified || mobileModified || phoneModified || addrModified) {
            /*不判断修改内容中是否有【*】*/
//            AssertUtil.assertException(nameModified && name.contains("*"), "修改后的收货人姓名不能含有特殊符号");
//            AssertUtil.assertException(mobileModified && mobile.contains("*"), "修改后的收货人手机不能含有特殊符号");
//            AssertUtil.assertException(phoneModified && phone.contains("*"), "修改后的收货人电话不能含有特殊符号");
//            AssertUtil.assertException(addrModified && address.contains("*"), "修改后的收货人详细地址不能含有特殊符号");
            //统一解密
            if (!nameModified) {
                dest.setReceiverName(decryptOrder.getReceiverName());
            }
            if (!phoneModified) {
                dest.setReceiverPhone(decryptOrder.getReceiverPhone());
            }
            if (!mobileModified) {
                dest.setReceiverMobile(decryptOrder.getReceiverMobile());
            }
            if (!addrModified) {
                dest.setReceiverAddress(decryptOrder.getReceiverAddress());
            }
            dest.setOaid("");
        }
    }

    /**
     * 获取不为空的字符串
     *
     * @param value 字符
     * @return 最终结果
     */
    private String getNotNullString(String value) {
        return Optional.ofNullable(value).orElse("");
    }

    /**
     * 打标. 继承
     *
     * @param dest 合并后的订单
     * @param orig 原单
     */
    private void markTag(OcBOrder dest, OcBOrder orig) {
        // 合、hold、拆、换、手、、到、急、禁
        dest.setIsMerge(orig.getIsMerge());

        // hold 无hold原因
        dest.setIsInterecept(orig.getIsInterecept());
        // 拆
        //dest.setIsSplit(orig.getIsSplit());
        // 换 明细 IS_EXCHANGE_ITEM
        dest.setOrderType(orig.getOrderType());
        // 手
        dest.setOrderSource(orig.getOrderSource());
        // 预
//        dest.setPresaleType(orig.getPresaleType());
        // 到
        dest.setPayType(orig.getPayType());
        // 急
        dest.setIsDeliveryUrgent(orig.getIsDeliveryUrgent());
        // 禁
//        dest.setIsJitxFjorbidDelivery(orig.getIsJitxFjorbidDelivery());
        //促
        dest.setIsPromOrder(orig.getIsPromOrder());
        dest.setBusinessType(orig.getBusinessType());
    }

    /**
     * 清除标签
     */
    private void clearTag(OcBOrder order) {
        // 改
        order.setIsModifiedOrder(order.getIsModifiedOrder());
        // 额
        order.setIsExtra(order.getIsExtra());
        // 手
        order.setOrderSource("");
    }

    public void calculationTag(OcBOrder ocBOrder, List<OcBOrderItem> orderItemList) {
        // 赠
        Optional<OcBOrderItem> optionalZOcBOrderItem = orderItemList.stream()
                .filter(obj -> GiftTypeEnum.PLATFORM.getVal().equals(obj.getGiftType())
                        || GiftTypeEnum.SYSTEM.getVal().equals(obj.getGiftType()))
                .findAny();
        if (optionalZOcBOrderItem.isPresent()) {
            ocBOrder.setIsHasgift(YesNoEnum.Y.getVal());
        } else {
            ocBOrder.setIsHasgift(YesNoEnum.N.getVal());
        }
        // 组
        Optional<OcBOrderItem> optionalOcBOrderItem = orderItemList.stream()
                .filter(obj -> obj.getProType() != null && obj.getProType() == SkuType.NO_SPLIT_COMBINE)
                .findAny();
        if (optionalOcBOrderItem.isPresent()) {
            ocBOrder.setIsCombination(YesNoEnum.Y.getVal());
        } else {
            ocBOrder.setIsCombination(YesNoEnum.N.getVal());
        }
        // 播
        liveMatchManager.cleanUp(ocBOrder, orderItemList);
    }


    /**
     * 保存OC_B_ORDER,OC_B_ORDER_ITEM,占用库存，生成丢件单
     *
     * @param ocBOrderRelation
     * @param type
     * @param user
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveOrderRelation(OcBOrderRelation ocBOrderRelation, Integer type, Long origOrderId, String isGift, User user) {
        // 保存订单信息
        if (ocBOrderRelation == null || ocBOrderRelation.getOrderInfo() == null
                || CollectionUtils.isEmpty(ocBOrderRelation.getOrderItemList())) {
            throw new NDSException("订单信息不存在");
        }
        OcBOrder ocBOrder = ocBOrderRelation.getOrderInfo();
        OcBOrder oriOrder = ocBOrderMapper.selectByID(origOrderId);

        // 记录订单日志
        boolean isSourceRelation = insertOmsOrderLog(type, origOrderId, user, ocBOrder, isGift);

        //记录补发的来源单关系信息
        if (isSourceRelation) {
            OcBOrderSourceRelation sourceRelation = new OcBOrderSourceRelation();
            sourceRelation.setId(sequenceUtil.buildOrderSourceRelationSequenceId());
            sourceRelation.setOrderId(ocBOrder.getId());
            sourceRelation.setSourceOrderId(origOrderId);
            sourceRelation.setType(OcBOrderSourceRelationTypeEnum.REISSUE.getKey());
            sourceRelation.setCreationdate(new Date());
            sourceRelation.setModifieddate(new Date());
            sourceRelation.setOwnerid(user != null ? user.getId() : 0L);
            sourceRelation.setOwnername(user != null ? user.getEname() : "");
            sourceRelation.setIsactive("Y");
            sourceRelationMapper.insert(sourceRelation);
        }

        // 分仓
        //this.distributeWarehouse(user, ocBOrderRelation);
        if (ocBOrder.getIsCopyOrder() != null && ocBOrder.getIsCopyOrder() == 1
                || ocBOrder.getIsResetShip() != null && ocBOrder.getIsResetShip() == 1) {
            OcBOrder updateOrder = new OcBOrder();
            updateOrder.setId(origOrderId);
            updateOrder.setCopyNum(Optional.ofNullable(oriOrder.getCopyNum()).orElse(0) + 1);
            makeModiferField(updateOrder, user);
            updateOrder.setModifierename(user.getEname());

            ocBOrderMapper.updateById(updateOrder);

            //如果支付信息不为空,保存支付信息
            List<OcBOrderPayment> paymentList = ocBOrderRelation.getOrderPaymentList();
            if (CollectionUtils.isNotEmpty(paymentList)) {
                paymentMapper.batchInsert(paymentList);
            }

        }
        if (YesNoEnum.Y.getVal().equals(ocBOrder.getIsResetShip())) {
            ocBOrder.setOrderStatus(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger());
        } else {
            ocBOrder.setOrderStatus(OmsOrderStatus.ORDER_DEFAULT.toInteger());
        }
        OrderBusinessTypeCodeEnum businessTypeCodeEnum = OrderBusinessTypeCodeEnum.getOrderBusinessTypeEnumByCode(ocBOrder.getBusinessTypeCode());
        if (ObjectUtil.equals(businessTypeCodeEnum.getCode(), OrderBusinessTypeCodeEnum.MILK_CARD_RESET.getCode())
                || ObjectUtil.equals(businessTypeCodeEnum.getCode(), OrderBusinessTypeCodeEnum.FREE_MILK_CARD_RESET.getCode())) {
            ocBOrder.setIsInterecept(1);
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                    OrderLogTypeEnum.ORDER_HOLD.getKey(), "奶卡补单新生成的订单hold单", null, null, user);
        }
        ocBOrderMapper.insert(ocBOrder);

        // 复制订单时 如果挂靠赠品的主商品被删除，挂靠赠品的挂靠关系gift_relation清空
        Map<String, List<OcBOrderItem>> itemMap =
                ocBOrderRelation.getOrderItemList().stream()
                        .filter(v -> StringUtils.isNotBlank(v.getGiftRelation())
                                && !OcBorderListEnums.YesOrNoEnum.IS_YES.getVal().equals(v.getIsGift()))
                        .collect(Collectors.groupingBy(OcBOrderItem::getGiftRelation));
        // 保存订单明细
        for (OcBOrderItem item : ocBOrderRelation.getOrderItemList()) {
            String giftRelation = item.getGiftRelation();
            if (StringUtils.isNotBlank(giftRelation)
                    && (itemMap == null || CollectionUtils.isEmpty(itemMap.get(giftRelation)))) {
                item.setGiftRelation(null);
            }
            item.setExpiryDateRange("");
            item.setExpiryDateType(0);
            ocBorderItemMapper.insert(item);
        }
        //复制奶卡信息 如果原单业务类型是：奶卡提货 RYCK04，免费奶卡提货 RYCK05，奶卡周期购提货 RYCK12，免费奶卡周期够提货 RYCK13，需要复制奶卡信息
        if (Objects.nonNull(oriOrder)) {
            String businessTypeCode = oriOrder.getBusinessTypeCode();
            boolean isCopyNaika = false;
            for (int i = 0; i < businessTypeArray.length; i++) {
                if (businessTypeArray[i].equals(businessTypeCode)) {
                    isCopyNaika = true;
                    break;
                }
            }
            if (isCopyNaika) {
                List<OcBOrderItem> orderItemList = ocBOrderRelation.getOrderItemList();
                Map<Long, Long> itemIdMap = orderItemList.stream().collect(Collectors.toMap(OcBOrderItem::getOriginalId, OcBOrderItem::getId, (v1, v2) -> v2));
                List<OcBOrderNaiKa> ocBOrderNaiKas = ocBOrderNaiKaMapper.selectList(new QueryWrapper<OcBOrderNaiKa>()
                        .lambda()
                        .eq(OcBOrderNaiKa::getOcBOrderId, origOrderId)
                        .eq(OcBOrderNaiKa::getIsactive, YesNoEnum.Y.getKey()));
                if (CollectionUtils.isNotEmpty(ocBOrderNaiKas)) {
                    List<OcBOrderNaiKa> insertList = new ArrayList<>();
                    for (OcBOrderNaiKa ocBOrderNaiKa : ocBOrderNaiKas) {
                        Long newItemId = itemIdMap.get(ocBOrderNaiKa.getOcBOrderItemId());
                        BaseModelUtil.makeBaseCreateField(ocBOrderNaiKa, user);
                        ocBOrderNaiKa.setOcBOrderId(ocBOrder.getId());
                        ocBOrderNaiKa.setId(buildSequenceUtil.buildOrderNaiKaSequenceId());
                        //赋值新单的明细ID
                        ocBOrderNaiKa.setOcBOrderItemId(null);
                        insertList.add(ocBOrderNaiKa);
                    }
                    if (CollectionUtils.isNotEmpty(insertList)) {
                        ocBOrderNaiKaMapper.batchInsert(insertList);
                    }
                }
            }
        }


        // 占用库存
        try {
            if (YesNoEnum.Y.getVal().equals(ocBOrder.getIsResetShip())) {
                omsOccupyTaskService.addOcBOccupyTask(ocBOrder, null);
                //指定效期
                OcBOrderParam param = new OcBOrderParam();
                param.setOcBOrder(ocBOrder);
                param.setOrderItemList(ocBOrderRelation.getOrderItemList());
                omsExpiryDateStService.expiryDateStService(param, user, "自动");
            } else {
                this.createToBeConfirmedTask(ocBOrder.getId());
            }
        } catch (Exception e) {
            log.error(LogUtil.format("复制or新增订单占用库存失败,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            throw new NDSException("占用库存失败" + e.getMessage());
        }
        // 生成丢件单 参数中ocBOrder信息取原单信息，ocBOrderItem取新单明细
        if (ocBOrder.getIsLoseCopyOrder() != null && ocBOrder.getIsLoseCopyOrder() == 1) {
            List<OcBOrderItem> items = ocBOrderRelation.getOrderItemList()
                    .stream().filter(item -> item.getProType() == null || item.getProType() != SkuType.NO_SPLIT_COMBINE)
                    .collect(Collectors.toList());
            JSONObject param = new JSONObject();
            OcBOrder oriOcBOrder = omsOrderService.selectOrderInfo(origOrderId);
            param.put("ocBorderDto", oriOcBOrder);
            param.put("ocBorderItemDto", items);
            param.put("sourceType", 1);
        }
        List<String> proEcodes = ocBOrderRelation.getOrderItemList().stream().map(OcBOrderItem::getPsCProEcode).collect(Collectors.toList());
        List<ProExtResult> proExtResults = psRpcService.queryProExtByEcodes(proEcodes);
        List<String> materialCodes =
                proExtResults.stream().filter(p -> p.getMaterial() != null).map(ProExtResult::getMaterial).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(materialCodes)) {
            throw new NDSException("校验补发订单明细失败,补发订单明细物料组信息不能为空!");
        }
        if (Objects.nonNull(oriOrder) && !YesNoEnum.Y.getVal().equals(ocBOrder.getIsCopyOrder())) {
            //若原订单的业务类型是：线上奶卡销售和线上免费奶卡，则需要校验补发订单中的商品必须是奶卡类的商品（即：商品的物料组必须是10800）
            if (oriOrder.getBusinessTypeCode().equals(OrderBusinessTypeCodeEnum.ON_LINE_MILK_CARD_SALE.getCode()) ||
                    oriOrder.getBusinessTypeCode().equals(OrderBusinessTypeCodeEnum.ON_LINE_FREE_MILK_CARD.getCode())) {
                if (materialCodes.size() > 1 || !"10800".equals(materialCodes.get(0))) {
                    throw new NDSException("补发订单中的商品必须是奶卡类的商品!");
                }
            } else {
                if (materialCodes.contains("10800") || materialCodes.contains("10801") || materialCodes.contains("10802")) {
                    throw new NDSException("补发订单中的商品不能是奶卡类或周期购类的商品!");
                }
            }
        }

        // 如果新生成的订单业务类型为奶卡补发
        if (ObjectUtil.equals(businessTypeCodeEnum.getCode(), OrderBusinessTypeCodeEnum.MILK_CARD_RESET.getCode())
                || ObjectUtil.equals(businessTypeCodeEnum.getCode(), OrderBusinessTypeCodeEnum.FREE_MILK_CARD_RESET.getCode())) {
            applicationContext.getBean(SaveBillService.class).extracted(ocBOrderRelation, user, oriOrder);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void extracted(OcBOrderRelation ocBOrderRelation, User user, OcBOrder oriOrder) {

        OcBOrder ocBOrder = ocBOrderRelation.getOrderInfo();
        OcBReturnAfSend ocBReturnAfSend = new OcBReturnAfSend();
        Long id = ModelUtil.getSequence(ORDER_AF_RETURN_TABLE_NAME);
        // 根据原单查询退款订单业务类型
        StCBusinessType stCBusinessType = omsRefundOrderService.queryRefundOrderType(oriOrder);
        ocBReturnAfSend.setId(id);
        ocBReturnAfSend.setCpCShopEcode(oriOrder.getCpCShopEcode());
        ocBReturnAfSend.setCpCShopId(oriOrder.getCpCShopId());
        ocBReturnAfSend.setCpCShopTitle(oriOrder.getCpCShopTitle());
        ocBReturnAfSend.setBillNo(sequenceUtil.aFbuildBillNo());
        ocBReturnAfSend.setTid(oriOrder.getTid());

        ocBReturnAfSend.setReturnStatus(ReturnAfSendReturnBillTypeEnum.REFUNDCOMPLETED.getVal());
        // 待审核待传对账,赋默认值
        ocBReturnAfSend.setToSettleStatus(ToACStatusEnum.INIT.val());
        ocBReturnAfSend.setTReturnId("");
        //单据类型 0 退货退款 1仅退款',
        ocBReturnAfSend.setBillType(1);
        ocBReturnAfSend.setVipNick(oriOrder.getUserNick());
        // @******** 增加手机号码
        ocBReturnAfSend.setVipPhone(oriOrder.getReceiverPhone());
        // @******** 增加阿里支付账号
        ocBReturnAfSend.setPayAccount(oriOrder.getBuyerAlipayNo());
        ocBReturnAfSend.setReason("补发订单作废奶卡");
        //单据来源设置默认值为2 自动
        ocBReturnAfSend.setRefundOrderSourceType(RefundOrderSourceTypeEnum.MANUAL.getValue());
        //支付方式
        ocBReturnAfSend.setPayMode(oriOrder.getPayType() + "");
        //支付宝账号
        ocBReturnAfSend.setPayAccount(oriOrder.getBuyerAlipayNo());
        //申请退款金额
        ocBReturnAfSend.setAmtReturnApply(new BigDecimal(0));
        ocBReturnAfSend.setSourceBillNo(oriOrder.getId() + "");
        ocBReturnAfSend.setCpCPlatformId(Long.valueOf(oriOrder.getPlatform()));
        ocBReturnAfSend.setPayMode(PayTypeEnum.getTextByVal(oriOrder.getPayType()));
        ocBReturnAfSend.setAmtReturnActual(new BigDecimal(0));
        ocBReturnAfSend.setPtGoodStatus("");
        //申请退款时间
        ocBReturnAfSend.setReturnApplyTime(new Date());
        ocBReturnAfSend.setAgStatus(AGStatusEnum.INIT.getVal() + "");
        ocBReturnAfSend.setReturnExplain("补发订单作废奶卡");
        ocBReturnAfSend.setHasGoodReturn(ReturnStatusEnum.COMPLETION.getVal());
        ocBReturnAfSend.setReturnStatus(ReturnAfSendReturnBillTypeEnum.REFUNDCOMPLETED.getVal());
        ocBReturnAfSend.setReturnPaymentTime(new Date());
        ocBReturnAfSend.setNewOcBOrderId(ocBOrder.getId());
        ocBReturnAfSend.setBusinessTypeId(stCBusinessType.getId());
        ocBReturnAfSend.setBusinessTypeCode(stCBusinessType.getEcode());
        ocBReturnAfSend.setBusinessTypeName(stCBusinessType.getEname());
        OperateUserUtils.saveOperator(ocBReturnAfSend, user);
        ocBReturnAfSend.setCardAutoVoid(CardAutoVoidEnum.HAVE_NOT_VOID.getCode());
        ocBReturnAfSend.setCardAutoVoidMark(1);
        ocBReturnAfSend.setVoidMark(1);
        ocBReturnAfSend.setTReturnStatus("SUCCESS");
        // 设置为补发生成
        ocBReturnAfSend.setRefundSource(RefundSourceEnum.REISSUE.getCode());
        ocBReturnAfSendMapper.insert(ocBReturnAfSend);
        // item表中写入数据
        for (OcBOrderItem orderItem : ocBOrderRelation.getOrderItemList()) {
            OcBReturnAfSendItem ocBReturnAfSendItem = new OcBReturnAfSendItem();
            //关联类型
            ocBReturnAfSendItem.setRelationBillType(0L);
            ocBReturnAfSendItem.setId(ModelUtil.getSequence(ORDER_AF_RETURN_ITEM_TABLE_NAME));
            ocBReturnAfSendItem.setRelationBillId(orderItem.getOcBOrderId());
            ocBReturnAfSendItem.setRelationBillNo(oriOrder.getBillNo());
            ocBReturnAfSendItem.setOcBReturnAfSendId(id);
            //'单据类型  客退 0，拦截 1，拒收 2 ',
            ocBReturnAfSendItem.setBillType(1);
            //赠品
            ocBReturnAfSendItem.setGift(orderItem.getGiftType());
            ocBReturnAfSendItem.setPsCSkuId(orderItem.getPsCSkuId());
            ocBReturnAfSendItem.setPsCSkuEcode(orderItem.getPsCSkuEcode());
            ocBReturnAfSendItem.setPsCProEcode(orderItem.getPsCProEcode());
            ocBReturnAfSendItem.setPsCProEname(orderItem.getPsCProEname());
            ocBReturnAfSendItem.setPsCProId(orderItem.getPsCProId());
            ocBReturnAfSendItem.setFreight(BigDecimal.ZERO);

            //申请退货数量
            ocBReturnAfSendItem.setQtyReturnApply(orderItem.getQtyRefund());
            ocBReturnAfSendItem.setAmtReturn(orderItem.getAmtRefund());
            ocBReturnAfSendItem.setPurchaseQty(orderItem.getQtyRefund());

            ocBReturnAfSendItem.setPurchaseQty(orderItem.getQty());
            ocBReturnAfSendItem.setPtProName(orderItem.getPtProName());
            ocBReturnAfSendItem.setPsCSkuEname(orderItem.getPsCSkuEname());
            ocBReturnAfSendItem.setPsCSkuPtEcode(orderItem.getPsCSkuPtEcode());
            ocBReturnAfSendItem.setAmtActual(orderItem.getRealAmt());

            ocBReturnAfSendItem.setOcBOrderItemId(orderItem.getId());
            ocBReturnAfSendItem.setOcBOrderId(ocBOrder.getId());
            ocBReturnAfSendItem.setBusinessTypeId(ocBOrder.getBusinessTypeId());
            ocBReturnAfSendItem.setBusinessTypeCode(ocBOrder.getBusinessTypeCode());
            ocBReturnAfSendItem.setBusinessTypeName(ocBOrder.getBusinessTypeName());

            OperateUserUtils.saveOperator(ocBReturnAfSendItem, user);
            ocBReturnAfSendItemMapper.insert(ocBReturnAfSendItem);
        }
        // 如果原单只有一张奶卡的话 会自动调用作废接口
        List<OcBOrderNaiKa> ocBOrderNaiKaList = ocBOrderNaiKaMapper.selectNaiKaByOcBOrderId(oriOrder.getId());
        if (CollectionUtils.isNotEmpty(ocBOrderNaiKaList) && (ocBOrderNaiKaList.size() == 1)) {
            OcBOrderNaiKa ocBOrderNaiKa = ocBOrderNaiKaList.get(0);
            if (ObjectUtil.notEqual(OmsOrderNaiKaStatusEnum.VOID_SUCCESS.getStatus(), ocBOrderNaiKa.getNaikaStatus())) {
                ValueHolder valueHolder = omsNaiKaOrderVoidService.naiKaOrderVoid(Collections.singletonList(ocBOrderNaiKaList.get(0).getId()), id, user);
                if (valueHolder.isOK()) {
                    // 解hold单
                    OcBOrder updateOcBOrder = new OcBOrder();
                    updateOcBOrder.setId(ocBOrder.getId());
                    updateOcBOrder.setIsInterecept(0);
                    updateOcBOrder.setModifieddate(new Date());
                    ocBOrderMapper.updateById(updateOcBOrder);
                    omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                            OrderLogTypeEnum.ORDER_HOLD_CANCEL.getKey(), "奶卡补单作废后自动取消hold", null, null, user);
                    OcBReturnAfSend updateOcBReturnAfSend = new OcBReturnAfSend();
                    updateOcBReturnAfSend.setCardAutoVoid(CardAutoVoidEnum.VOID_SUCCESS.getCode());
                    updateOcBReturnAfSend.setId(ocBReturnAfSend.getId());
                    updateOcBReturnAfSend.setModifieddate(new Date());
                    ocBReturnAfSendMapper.updateById(updateOcBReturnAfSend);
                } else {
                    OcBReturnAfSend updateOcBReturnAfSend = new OcBReturnAfSend();
                    updateOcBReturnAfSend.setCardAutoVoid(CardAutoVoidEnum.VOID_ERROR.getCode());
                    updateOcBReturnAfSend.setId(ocBReturnAfSend.getId());
                    updateOcBReturnAfSend.setModifieddate(new Date());
                    ocBReturnAfSendMapper.updateById(updateOcBReturnAfSend);
                }
            }
        }
    }

    /**
     * <AUTHOR>
     * @Date 19:27 2021/7/12
     * @Description 创建占单任务表
     */
    private void createToBeConfirmedTask(Long oredrId) {

        int n = toBeConfirmedTaskMapper.selectCount(new QueryWrapper<OcBToBeConfirmedTask>().lambda().eq(OcBToBeConfirmedTask::getOrderId, oredrId));
        if (n > 0) {
            this.updateToBeConfirmedTask(oredrId);
        } else {
            OcBToBeConfirmedTask toBeConfirmedTask = new OcBToBeConfirmedTask();
            toBeConfirmedTask.setId(sequenceUtil.buildToBeConfirmedTaskId());
            toBeConfirmedTask.setOrderId(oredrId);
            toBeConfirmedTask.setCreationdate(new Date());
            toBeConfirmedTask.setStatus(0);
            toBeConfirmedTaskService.insertToBeConfirmedTask(toBeConfirmedTask);
        }
    }

    /**
     * <AUTHOR>
     * @Date 19:27 2021/7/12
     * @Description 占单结束后继续走流程
     */
    private void updateToBeConfirmedTask(Long orderId) {
        List<Long> ids = new ArrayList<>();
        ids.add(orderId);
        toBeConfirmedTaskService.updateToBeConfirmedTask(ids);
    }

    // 保存日志信息 不能影响订单保存的业务
    private boolean insertOmsOrderLog(Integer type, Long origOrderId, User user, OcBOrder ocBOrder, String isGift) {
        try {
            if (log.isDebugEnabled()) {
                log.debug(" SaveBillService insertOmsOrderLog , type={},origOrderId={},orderId={}", type, origOrderId, ocBOrder.getId());
            }
            if (origOrderId == null) {
                if (isGift == null || !"1".equals(isGift)) {
                    omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                            OrderLogTypeEnum.ORDER_ADD.getKey(), "手工新增订单成功", null, null, user);
                } else {
                    omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                            OrderLogTypeEnum.ORDER_ADD.getKey(), "手工新增赠品订单成功", null, null, user);
                }
                //1.丢单补发 2.错发补发 3.漏发补发 4.赠品出库补发 5.JITX发货异常补发
            } else if (type != null && type == 1) {
                String message = "丢单补发订单成功";
                String logType = OrderLogTypeEnum.ORDER_RESHIP.getKey();
                if (ocBOrder.getIsCopyOrder() != null && 1 == ocBOrder.getIsCopyOrder()) {
                    message = "原单无效复制成功";
                    logType = OrderLogTypeEnum.LOSEORDER_COPY.getKey();
                }
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                        logType, message + ",源订单编号：" + origOrderId, null, null, user);
                if (OrderLogTypeEnum.ORDER_RESHIP.getKey().equals(logType)){
                    return true;
                }
            } else if (type != null && type == 2) {
                String message = "错发补发订单成功";
                String logType = OrderLogTypeEnum.ORDER_RESHIP.getKey();
                if (ocBOrder.getIsCopyOrder() != null && 1 == ocBOrder.getIsCopyOrder()) {
                    message = "正常复制成功";
                    logType = OrderLogTypeEnum.LOSEORDER_COPY.getKey();
                }
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                        //OrderLogTypeEnum.ORDER_COPY.getKey(), "原单无效复制订单成功,源订单编号：" + origOrderId, null, null, user);
                        logType, message + ",源订单编号：" + origOrderId, null, null, user);
                if (OrderLogTypeEnum.ORDER_RESHIP.getKey().equals(logType)){
                    return true;
                }
            } else if (type != null && type == 3) {
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                        OrderLogTypeEnum.ORDER_RESHIP.getKey(), "漏发补发订单成功,源订单编号：" + origOrderId, null, null, user);
                return true;
            } else if (type != null && type == 4) {
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                        OrderLogTypeEnum.ORDER_RESHIP.getKey(), "赠品出库补发订单成功,源订单编号：" + origOrderId, null, null, user);
                return true;
            } else if (type != null && type == 5) {
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                        OrderLogTypeEnum.ORDER_RESHIP.getKey(), "JITX发货异常补发订单成功,源订单编号：" + origOrderId, null, null, user);
                return true;
            } else if (type != null && type == 6) {
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                        OrderLogTypeEnum.ORDER_RESHIP.getKey(), "其他补发订单成功,源订单编号：" + origOrderId, null, null, user);
                return true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return false;
    }

    private void recountAmount(OcBOrderRelation ocBOrderRelation) {
        OcBOrder ocBOrder = ocBOrderRelation.getOrderInfo();
        ocBOrder.setIsInterecept(0);
        List<OcBOrderItem> orderItems = ocBOrderRelation.getOrderItemList();
        BigDecimal productAmt = BigDecimal.ZERO;
        BigDecimal orderAmt;
        BigDecimal productDiscountAmt = BigDecimal.ZERO;
        BigDecimal orderDiscountAmt = BigDecimal.ZERO;
        BigDecimal qtyAll = BigDecimal.ZERO;
        BigDecimal adjustAmt = BigDecimal.ZERO;
        BigDecimal weight = BigDecimal.ZERO;
        for (OcBOrderItem item : orderItems) {
            if (item.getProType() != null && item.getProType() == 4L) {
                ocBOrder.setIsCombination(1);
                continue;
            }
            productAmt = productAmt.add(Optional.ofNullable(item.getPrice()).orElse(BigDecimal.ZERO)
                    .multiply(Optional.ofNullable(item.getQty()).orElse(BigDecimal.ZERO)));
            productDiscountAmt =
                    productDiscountAmt.add(Optional.ofNullable(item.getAmtDiscount()).orElse(BigDecimal.ZERO));
            orderDiscountAmt =
                    orderDiscountAmt.add(Optional.ofNullable(item.getOrderSplitAmt()).orElse(BigDecimal.ZERO));
            adjustAmt = adjustAmt.add(Optional.ofNullable(item.getAdjustAmt()).orElse(BigDecimal.ZERO));
            qtyAll = qtyAll.add(Optional.ofNullable(item.getQty()).orElse(BigDecimal.ZERO));
            weight = weight.add(Optional.ofNullable(item.getStandardWeight()).orElse(BigDecimal.ZERO));
        }
        ocBOrder.setProductAmt(productAmt);
        ocBOrder.setProductDiscountAmt(productDiscountAmt);
        ocBOrder.setOrderDiscountAmt(orderDiscountAmt);
        ocBOrder.setAdjustAmt(adjustAmt);
        ocBOrder.setQtyAll(qtyAll);
        orderAmt = productAmt.subtract(productDiscountAmt)
                .subtract(orderDiscountAmt)
                .add(adjustAmt).add(ocBOrder.getShipAmt());
        ocBOrder.setOrderAmt(orderAmt);
        ocBOrder.setReceivedAmt(orderAmt);
        ocBOrder.setAmtReceive(orderAmt);
        ocBOrder.setServiceAmt(Optional.ofNullable(ocBOrder.getServiceAmt()).orElse(BigDecimal.ZERO));
        ocBOrder.setConsignAmt(Optional.ofNullable(ocBOrder.getConsignAmt()).orElse(BigDecimal.ZERO));
        ocBOrder.setConsignShipAmt(Optional.ofNullable(ocBOrder.getConsignShipAmt()).orElse(BigDecimal.ZERO));
        ocBOrder.setCodAmt(Optional.ofNullable(ocBOrder.getCodAmt()).orElse(BigDecimal.ZERO));
        // ocBOrder.setOperateAmt(Optional.ofNullable(ocBOrder.getOperateAmt()).orElse(BigDecimal.ZERO));
        ocBOrder.setJdReceiveAmt(Optional.ofNullable(ocBOrder.getJdReceiveAmt()).orElse(BigDecimal.ZERO));
        ocBOrder.setJdSettleAmt(Optional.ofNullable(ocBOrder.getJdSettleAmt()).orElse(BigDecimal.ZERO));
        ocBOrder.setLogisticsCost(Optional.ofNullable(ocBOrder.getLogisticsCost()).orElse(BigDecimal.ZERO));
        ocBOrder.setWeight(weight);
    }

    private void distributeWarehouse(User user, OcBOrderRelation ocBOrderRelation) {
        OcBOrder ocBOrder = ocBOrderRelation.getOrderInfo();
        if (ocBOrder.getCpCPhyWarehouseId() == null) {
            CpCPhyWarehouse cpCPhyWarehouse = omsWarehousRuleService.doCallDistributeWarehouseInfo(ocBOrderRelation, user);
            if (cpCPhyWarehouse == null) {
                throw new NDSException("当前订单分仓失败");
            }
            ocBOrder.setCpCPhyWarehouseId(cpCPhyWarehouse.getId());
            ocBOrder.setCpCPhyWarehouseEcode(cpCPhyWarehouse.getEcode());
            ocBOrder.setCpCPhyWarehouseEname(cpCPhyWarehouse.getEname());
            if (StringUtils.equals(cpCPhyWarehouse.getWhType(), OcBOrderConst.CP_C_PHY_WAREHOUSE_TYPE)) {
                ocBOrder.setIsO2oOrder(OcBOrderConst.IS_STATUS_IY);
            } else {
                ocBOrder.setIsO2oOrder(OcBOrderConst.IS_STATUS_IN);
            }
            try {
                // 插入日志
                omsOrderLogService.addUserOrderLog(ocBOrderRelation.getOrderId(),
                        ocBOrderRelation.getOrderInfo().getBillNo(), OrderLogTypeEnum.WAREHOUSE_SERVICE.getKey(),
                        "分配的仓库:" + cpCPhyWarehouse.getEname(), null, null, user);
            } catch (Exception e) {
                log.error(LogUtil.format("分仓保存日志异常,异常信息为:{}", ocBOrderRelation.getOrderId()), Throwables.getStackTraceAsString(e));
            }
        }
    }

    private void distributeLogistics(User user, OcBOrderRelation ocBOrderRelation) {
        // 未指定物流公司, 进行重新分物流服务
        if (Optional.ofNullable(ocBOrderRelation.getOrderInfo().getCpCLogisticsId()).orElse(0L) != 0L) {
            return;
        }
        CpCLogistics cpCLogistics = omsOrderDistributeLogisticsService.distributeLogistics(ocBOrderRelation);
        // 未找到合适物流公司
        if (cpCLogistics == null) {
            return;
        }
        OcBOrder ocBOrder = new OcBOrder();
        ocBOrder.setId(ocBOrderRelation.getOrderId());
        ocBOrder.setCpCLogisticsId(cpCLogistics.getId());
        ocBOrder.setCpCLogisticsEname(cpCLogistics.getEname());
        ocBOrder.setCpCLogisticsEcode(cpCLogistics.getEcode());
        ocBOrderMapper.updateById(ocBOrder);
    }

    /**
     * 组合商品时，虚拟条码不传需要从老单据中取
     *
     * @param orderItems
     * @param orderId
     * @param ocBOrder
     * @param user
     */
    private void buildOcBOrderItems(List<OcBOrderItem> orderItems, Long orderId, OcBOrder ocBOrder, User
            user) {
        // 获取传入订单明细中的福袋or组合商品的sku编码，查询虚拟订单
        List<String> giftSkus = orderItems.stream()
                .map(OcBOrderItem::getGiftbagSku)
                .filter(StringUtils::isNotEmpty)
                .distinct().collect(Collectors.toList());
        List<OcBOrderItem> giftBagItems = new ArrayList<>();
//        if (CollectionUtils.isNotEmpty(giftSkus)) {
//            List<OcBOrderItem> oldItems = ocBorderItemMapper.selectOrderItems(orderId);
//            if (CollectionUtils.isNotEmpty(oldItems)) {
//                for (OcBOrderItem item : oldItems) {
//                    if (Optional.ofNullable(item.getProType()).orElse(0L) == 4
//                            && giftSkus.contains(item.getPsCSkuEcode())) {
//                        item.setId(ModelUtil.getSequence(ORDER_ITEM_TABLE_NAME));
//                        item.setOcBOrderId(ocBOrder.getId());
//                        giftBagItems.add(item);
//                    }
//                }
//            }
//        }

        for (OcBOrderItem item : orderItems) {
            if (StringUtils.isEmpty(item.getPsCSkuEcode())) {
                throw new NDSException("订单明细商品SKU条码为空");
            }
            if (item.getQty() == null || item.getQty().compareTo(BigDecimal.ZERO) == 0) {
                throw new NDSException("订单明细的数量不能为0或null");
            }
            makeCreateField(item, user);
            //记录原始明细ID，用于奶卡复制映射新的明细ID
            item.setOriginalId(item.getId());
            item.setModifierename(user.getEname());
            item.setOwnerename(user.getEname());
            item.setId(ModelUtil.getSequence(ORDER_ITEM_TABLE_NAME));
            item.setOcBOrderId(ocBOrder.getId());
            item.setIsGift(Optional.ofNullable(item.getIsGift()).orElse(0));
            item.setQtyReturnApply(BigDecimal.ZERO);
            item.setPsCSkuEcode(item.getPsCSkuEcode().toUpperCase());
            ProductSku productSku = psRpcService.selectProductSku(item.getPsCSkuEcode());
            if (productSku == null) {
                throw new NDSException("Sku《" + item.getPsCSkuEcode() + "》在商品中心不存在！");
            }
            // 供应类型 0 普通 1.代销轻供 2.寄售轻供
            item.setPsCProSupplyType(productSku.getPsCProSupplyType());
            item.setIsManualAdd("1");
            item.setQtyRefund(BigDecimal.ZERO);
            if (StringUtils.isEmpty(item.getTid())) {
                item.setTid(ocBOrder.getTid());
            }
            String isEnableExpiry = productSku.getIsEnableExpiry();
            if ("Y".equals(isEnableExpiry)) {
                item.setIsEnableExpiry(1);
            } else {
                item.setIsEnableExpiry(0);
            }
            //item.setEqualExchangeRatio("");
            //销售短出，新单子默认0
            item.setRealOutNum(BigDecimal.ZERO);

            item.setEqualExchangeMark("");
            item.setIsEqualExchange(0);
            item.setEqualExchangeRatio("");
            item.setNumIid(Optional.ofNullable(item.getNumIid()).orElse("0"));
            item.setPsCSkuId(productSku.getId());
            item.setPsCSkuEname(productSku.getSkuName());
            item.setSkuSpec(productSku.getSkuSpec());
            item.setPsCProId(productSku.getProdId());
            item.setPsCProEcode(productSku.getProdCode());
            item.setPsCProEname(productSku.getName());
            item.setSex(productSku.getSex());
            item.setPsCClrId(productSku.getColorId());
            item.setPsCClrEcode(productSku.getColorCode());
            item.setPsCClrEname(productSku.getColorName());
            item.setPsCSizeId(productSku.getSizeId());
            item.setPsCSizeEcode(productSku.getSizeCode());
            item.setPsCSizeEname(productSku.getSizeName());
            item.setMDim4Id(productSku.getMDim4Id());
            item.setMDim6Id(productSku.getMDim6Id());
            item.setPsCProMaterieltype(productSku.getMaterialType());
            item.setStandardWeight(Optional.ofNullable(productSku.getWeight()).orElse(BigDecimal.ZERO));
            item.setRefundStatus(OcOrderRefundStatusEnum.NOTREFUND.getVal());
            item.setPriceTag(productSku.getPricelist()); // 吊牌价
            item.setPriceList(productSku.getPricelist()); // 吊牌价
            // 金额相关字段的值依靠前端传入，若前端未传默认给0
            BigDecimal realAmt = Optional.ofNullable(item.getRealAmt()).orElse(BigDecimal.ZERO);
            item.setRealAmt(realAmt); //成交金额
            BigDecimal priceActual = realAmt.divide(item.getQty(), 4, BigDecimal.ROUND_HALF_UP);
            item.setPriceActual(priceActual); //成交单价
            if (orderId == null) {
                item.setPrice(Optional.ofNullable(item.getPrice()).orElse(priceActual));//平台售价 若前端未传，则取商品单价作为平台售价
                item.setOrderSplitAmt(Optional.ofNullable(item.getOrderSplitAmt()).orElse(BigDecimal.ZERO)); //平摊金额
                item.setAmtDiscount(Optional.ofNullable(item.getAmtDiscount()).orElse(BigDecimal.ZERO)); //商品优惠金额
                //调整金额
                BigDecimal adjustAmt = item.getRealAmt() // 成交金额
                        .subtract(item.getPrice().multiply(item.getQty())) //平台售价*数量 = 商品金额
                        .add(Optional.ofNullable(item.getAmtDiscount()).orElse(BigDecimal.ZERO))
                        .add(Optional.ofNullable(item.getOrderSplitAmt()).orElse(BigDecimal.ZERO));
                item.setAdjustAmt(adjustAmt); //调整金额
            } else {
                // 按原单复制、补发的需要处理金额，针对对等换货的复制之后去掉对等换货的标记(平台售价根据成交金额重算)
                BigDecimal productAmt = item.getRealAmt()
                        .add(Optional.ofNullable(item.getAmtDiscount()).orElse(BigDecimal.ZERO))
                        .add(Optional.ofNullable(item.getOrderSplitAmt()).orElse(BigDecimal.ZERO))
                        .subtract(Optional.ofNullable(item.getAdjustAmt()).orElse(BigDecimal.ZERO));
                item.setPrice(productAmt.divide(item.getQty(), 4, BigDecimal.ROUND_HALF_UP));
            }
            // 需要拆解的订单明细
            if (productSku.getSkuType() == 0) {
                item.setProType(0L);
            }
            List<OcBOrderItem> combineOrGiftBagList = new ArrayList<>();
            if (item.getProType() == null
                    && (productSku.getSkuType() == SkuType.COMBINE_PRODUCT
                    || productSku.getSkuType() == SkuType.GIFT_PRODUCT)) {
                item.setProType((long) SkuType.NO_SPLIT_COMBINE);
                item.setGiftbagSku(productSku.getSkuEcode());
                item.setGroupGoodsMark(GROUP_GOODS_MARK + item.getId());
                combineOrGiftBagList.add(item);
            }
            if (CollectionUtils.isNotEmpty(combineOrGiftBagList)) {
                try {
                    List<OcBOrderItem> giftOrCombineList = omsConstituteSplitService
                            .encapsulationParameter(combineOrGiftBagList, ocBOrder, user, 0);
                    if (CollectionUtils.isEmpty(giftOrCombineList)) {
                        throw new NDSException("福袋或组合商品抽取失败！");
                    }
                    giftBagItems.addAll(giftOrCombineList);
                    omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                            OrderLogTypeEnum.COMBINATION_SPLIT.getKey(), "组合商品解析成功", null,
                            null, user);
                } catch (Exception e) {
                    log.error(LogUtil.format("福袋或组合商品抽取失败,异常信息为:{}"), Throwables.getStackTraceAsString(e));
                    throw new NDSException("福袋或组合商品抽取失败！");
                }
            }
        }
        orderItems.addAll(giftBagItems);
    }

    private void buildOcBOrder(OcBOrder ocBOrder, Long orderId, User user, OcBOrder oldOrder) {
        ocBOrder.setId(ModelUtil.getSequence(ORDER_TABLE_NAME));
        ocBOrder.setOrderSource("手工新增");
        ocBOrder.setOrigOrderId(orderId);
        //待分配
        ocBOrder.setOrderStatus(50);
        if (YesNoEnum.Y.getVal().equals(ocBOrder.getIsResetShip())) {
            ocBOrder.setOrderType(OrderTypeEnum.REISSUE.getVal());
            ocBOrder.setOrderStatus(OcOrderCheckBoxEnum.CHECKBOX_OUT_OF_STOCK.getVal());
        } else {
            ocBOrder.setOrderType(OrderTypeEnum.NORMAL.getVal());
        }
        ocBOrder.setTid(ocBOrder.getSourceCode());
        ocBOrder.setMergeSourceCode(ocBOrder.getSourceCode());
        ocBOrder.setOccupyStatus(OrderOccupyStatus.STATUS_0);
        ocBOrder.setOrderDate(Optional.ofNullable(ocBOrder.getOrderDate()).orElse(new Date()));
        ocBOrder.setDouble11PresaleStatus(Optional.ofNullable(ocBOrder.getDouble11PresaleStatus())
                .orElse(OcOrderDoublellPresaleStatus.FEI_PRESALL.toInteger()));
        if (ocBOrder.getServiceAmt() == null) {
            ocBOrder.setServiceAmt(BigDecimal.ZERO);
        }
        if (StringUtils.isEmpty(ocBOrder.getOrderFlag())) {
            ocBOrder.setOrderFlag("0");
        }
        ocBOrder.setOutStatus(1);
        ocBOrder.setWmsCancelStatus(0);
        ocBOrder.setRefundConfirmStatus(0);
        ocBOrder.setAutoAuditStatus(0);
        // ocBOrder.setSysPresaleStatus(0);
        ocBOrder.setIsModifiedOrder(0);
        ocBOrder.setIsSameCityPurchase(0);
        //复制订单 需要清空审核时间 、配货时间
        ocBOrder.setDistributionTime(null); //配货时间
        ocBOrder.setAuditTime(null); // 审核时间
        ocBOrder.setSplitStatus(0); // 拆单状态
        ocBOrder.setQtySplit(0L);
        ocBOrder.setBillNo(sequenceUtil.buildBillNo());
        //复制省市区code
        ocBOrder.setCpCRegionProvinceEcode(ocBOrder.getCpCRegionProvinceId() == null ? null : String.valueOf(ocBOrder.getCpCRegionProvinceId()));
        ocBOrder.setCpCRegionCityEcode(ocBOrder.getCpCRegionCityId() == null ? null : String.valueOf(ocBOrder.getCpCRegionCityId()));
        ocBOrder.setCpCRegionAreaEcode(ocBOrder.getCpCRegionAreaId() == null ? null : String.valueOf(ocBOrder.getCpCRegionAreaId()));

        // 设置店铺信息
        CpShop cpShop = cpRpcService.selectShopById(ocBOrder.getCpCShopId());
        if (cpShop == null) {
            throw new NDSException("店铺信息不存在");
        }
        if (YesNoEnum.Y.getVal().equals(ocBOrder.getIsCopyOrder())) {
            ocBOrder.setBusinessTypeCode(null);
            ocBOrder.setBusinessTypeId(null);
            ocBOrder.setBusinessTypeName(null);
            ocBOrder.setBusinessType(null);
        }
        if (oldOrder != null) {
            ocBOrder.setGwSourceGroup(oldOrder.getGwSourceGroup());
            ocBOrder.setOrderSourcePlatformEcode(oldOrder.getOrderSourcePlatformEcode());
            ocBOrder.setCurrentCycleNumber(oldOrder.getCurrentCycleNumber());
        }
        ocBOrder.setCpCShopTitle(cpShop.getCpCShopTitle());
        ocBOrder.setCpCShopSellerNick(cpShop.getSellerNick());
        ocBOrder.setCpCShopEcode(cpShop.getEcode());
        ocBOrder.setPlatform(Optional.ofNullable(cpShop.getCpCPlatformId()).orElse(-1L).intValue());

        makeCreateField(ocBOrder, user);
        ocBOrder.setOwnerename(user.getEname());
        ocBOrder.setModifierename(user.getEname());

        OrderAddressConvertUtil.convert(ocBOrder);
    }

    private void makeCreateField(BaseModel model, User user) {
        Date date = new Date();
        model.setAdClientId((long) user.getClientId());//所属公司
        model.setAdOrgId((long) user.getOrgId());//所属组织
        model.setOwnerid(Long.valueOf(user.getId()));//创建人id
        model.setCreationdate(date);//创建时间
        model.setOwnername(user.getName());//创建人用户名
        model.setModifierid(Long.valueOf(user.getId()));//修改人id
        model.setModifiername(user.getName());//修改人用户名
        model.setModifieddate(date);//修改时间
        model.setIsactive("Y");//是否启用
    }

    private void makeModiferField(BaseModel model, User user) {
        Date date = new Date();
        model.setModifierid(Long.valueOf(user.getId()));//修改人id
        model.setModifiername(user.getName());//修改人用户名
        model.setModifieddate(date);//修改时间
        model.setIsactive("Y");//是否启用
    }

    private static boolean checkMobile(String mobile) {
        filterSpecialStr(mobile);
        filterEmoji(mobile);
        boolean flag = false;
        for (char c : mobile.toCharArray()) {
            if (c >= 0x4E00 && c <= 0x9FA5) {
                flag = true;
                break;
            }
        }
        return flag;
    }

    /**
     * 过滤特殊字符
     *
     * @param str
     * @return
     */
    private static String filterSpecialStr(String str) {
        String regEx = "[`~!@#$%^&*()+=|{}':;',\\[\\].<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。，、？]";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(str);
        return m.replaceAll("").trim();
    }

    private static String filterEmoji(String source) {
        if (source != null) {
            Pattern emoji = Pattern.compile(REG, Pattern.UNICODE_CASE | Pattern.CASE_INSENSITIVE);
            Matcher emojiMatcher = emoji.matcher(source);
            if (emojiMatcher.find()) {
                source = emojiMatcher.replaceAll("");
                return source;
            }
            return source;
        }
        return source;
    }

    /**
     * 检查参数合法性
     *
     * @param borderDto 订单实体
     * @return
     * @throws NDSException 异常
     */
    public void chcekOrderParam(OcBOrder borderDto, Long orderId, Integer type) {
        // 平台单号+订单补充信息不唯一，不允许保存 排除已取消 和 已作废
        String sourceCode = borderDto.getSourceCode();
        String suffixInfo = null;
        // 复制订单 需要订单补充信息
        if (orderId != null) {
            OcBOrder oriOrder = ocBOrderMapper.selectByID(orderId);
            if (oriOrder == null) {
                throw new NDSException("订单" + orderId + "，原单不存在！");
            }

            if (StringUtils.isNotEmpty(oriOrder.getAnchorId())) {
                borderDto.setAnchorId(oriOrder.getAnchorId());
                borderDto.setAnchorName(oriOrder.getAnchorName());
            }

            if (!OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger().equals(oriOrder.getOrderStatus())
                    && !OmsOrderStatus.PLATFORM_DELIVERY.toInteger().equals(oriOrder.getOrderStatus())) {
                if (YesNoEnum.Y.getVal().equals(borderDto.getIsCopyOrder())) {
                    if (type == 2) {
                        throw new NDSException("只能对【仓库发货，平台发货】订单状态的原单进行复制操作");
                    }
                } else {
                    throw new NDSException("只能对【仓库发货，平台发货】订单状态的原单进行补发操作");
                }
            }

            //补发单增加店铺一致性校验
            if (!YesNoEnum.Y.getVal().equals(borderDto.getIsCopyOrder())) {
                Long oriOrderCpCShopId = oriOrder.getCpCShopId();
                Long cpCShopId = borderDto.getCpCShopId();
                if (!Objects.equals(oriOrderCpCShopId, cpCShopId)) {
                    throw new NDSException("只能对相同店铺订单进行补发,请重试");
                }
            }

            //唯品会JITX订单允许复制订单继续进行复制
            if (!PlatFormEnum.VIP_JITX.getCode().equals(oriOrder.getPlatform())) {
                if ((borderDto.getIsResetShip() == null || borderDto.getIsResetShip() != 1)
                        && Optional.ofNullable(oriOrder.getIsCopyOrder()).orElse(0) == 1) {
                    throw new NDSException("原单是复制订单，不能对复制订单进行复制！");
                }
            }
            suffixInfo = sourceCode + "diy" + orderId + (Optional.ofNullable(oriOrder.getCopyNum()).orElse(0) + 1);
            borderDto.setSuffixInfo(suffixInfo);
            // @20210109 FixBug :31440 合并的订单复制后，合标丢失
            borderDto.setIsMerge(oriOrder.getIsMerge());
        }
        List<Long> ids = Lists.newArrayList();
        // 一头牛订单业务逻辑调整  20220921
        List<OcBOrder> orderList = GSI4Order.getValidOrderListBySourceCode(sourceCode);
        // 50070150
        if (CollectionUtils.isNotEmpty(orderList)) {
            String finalSuffixInfo = suffixInfo;
            ids = orderList.stream().filter(x -> StringUtils.equalsIgnoreCase(finalSuffixInfo, x.getSuffixInfo()))
                    .map(OcBOrder::getId).collect(Collectors.toList());
        }

        if (CollectionUtils.isNotEmpty(ids)) {
            throw new NDSException(Resources.getMessage("平台单号+订单补充信息不唯一，不允许保存"));
        }

        if (StringUtils.isNotEmpty(borderDto.getReceiverMobile())) {
            if (checkMobile(borderDto.getReceiverMobile())) {
                throw new NDSException(Resources.getMessage("收货人手机号格式有误，请重新输入！"));
            }
        }


        //下单店铺Id
        Long cpCShopId = borderDto.getCpCShopId();
        if (cpCShopId == null) {
            throw new NDSException(Resources.getMessage("下单店铺ID不能为空"));
        }
        //下单店铺名称
        String cpCShopTitle = borderDto.getCpCShopTitle();
        if (cpCShopTitle == null) {
            throw new NDSException(Resources.getMessage("下单店铺名称不能为空"));
        }
        //配送费用
        BigDecimal shipAmt = borderDto.getShipAmt();
        if (shipAmt == null) {
            throw new NDSException(Resources.getMessage("配送费用不能为空"));
        }
        //付款方式
        Integer payType = borderDto.getPayType();
        if (payType == null) {
            throw new NDSException(Resources.getMessage("付款方式不能为空"));
        }
        //收货人
        String receiverName = borderDto.getReceiverName();
        if (StringUtils.isEmpty(receiverName)) {
            throw new NDSException(Resources.getMessage("收货人不能为空"));
        }
        //收货人手机
        String receiverMobile = borderDto.getReceiverMobile();
        if (StringUtils.isEmpty(receiverMobile)) {
            throw new NDSException(Resources.getMessage("收货人手机号码不能为空"));
        }
        //收货人省份
        String cpCRegionProvinceEname = borderDto.getCpCRegionProvinceEname();
        if (StringUtils.isEmpty(cpCRegionProvinceEname)) {
            throw new NDSException(Resources.getMessage("收货人省份不能为空"));
        }
        //收货人市
        String cpCRegionCityEname = borderDto.getCpCRegionCityEname();
        if (StringUtils.isEmpty(cpCRegionCityEname)) {
            throw new NDSException(Resources.getMessage("收货人市不能为空"));
        }
        //收货人地址
        String receiverAddress = borderDto.getReceiverAddress();
        if (StringUtils.isEmpty(receiverAddress)) {
            throw new NDSException(Resources.getMessage("收货人详细地址不能为空"));
        }
    }

    /**
     * 保存主表信息
     *
     * @param user             用户
     * @param borderDto        订单实体
     * @param orderItemResults 订单明细
     * @return 返回
     */
    public OcBOrder saveOrderInfo(User user, OcBOrder borderDto, List<OcBOrderItem> orderItemResults) {
        return borderDto;
    }

    /**
     * 更新主表信息
     *
     * @param user             用户
     * @param borderDto        订单实体
     * @param orderItemResults 订单明细
     * @return 返回
     */
    public OcBOrder updateOrderInfo(User user, OcBOrder borderDto, List<OcBOrderItem> orderItemResults) {

        return borderDto;
    }

    /**
     * @param object
     * @param user
     * @return
     */
    public ValueHolder updateIsLackstock(JSONObject object, User user) {
        ValueHolder vh = new ValueHolder();
        Long orderId = object.getLong("orderId");
        Long item = object.getLong("itemId");
        Integer isLackstock = object.getInteger("isLackstock");
        QueryWrapper<OcBOrderItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", item);
        queryWrapper.eq("oc_b_order_id", orderId);
        OcBOrderItem orderItem1 = ocBorderItemMapper.selectOne(queryWrapper);
        orderItem1.setModifierename(user.getEname());
        orderItem1.setModifiername(user.getName());
        orderItem1.setModifieddate(new Date());
        orderItem1.setIsLackstock(isLackstock);
        OcBOrderItem orderItem = new OcBOrderItem();
        orderItem.setIsLackstock(isLackstock);

        ocBorderItemMapper.update(orderItem, queryWrapper);
        vh.put("code", ResultCode.SUCCESS);
        vh.put(MESSAGE, "更新成功");
        return vh;
    }

    /**
     * 复制支付信息
     *
     * @param orderId    原订单id
     * @param newOrderId 新订单id
     * @return 支付信息集合
     */
    @Deprecated
    private List<OcBOrderPayment> buildPaymentList(Long orderId, Long newOrderId, User user) {
        List<OcBOrderPayment> orderPaymentList = paymentMapper.selectList(new LambdaQueryWrapper<OcBOrderPayment>()
                .eq(OcBOrderPayment::getOcBOrderId, orderId));
        if (CollectionUtils.isNotEmpty(orderPaymentList)) {
            orderPaymentList.forEach(payment -> {
                payment.setId(ModelUtil.getSequence(OC_B_ORDER_PAYMENT));
                payment.setOcBOrderId(newOrderId);
                payment.setPayTime(new Date());
                BaseModelUtil.makeBaseCreateField(payment, user);
                payment.setOwnerename(user.getEname());
                payment.setModifierename(user.getEname());
            });
        }
        return orderPaymentList;
    }

}
