package com.jackrain.nea.oc.oms.services.delivery.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.ac.api.AcLogisticsFeeQueryCmd;
import com.jackrain.nea.ac.model.result.AcLogisticsFeeInfoBySapResult;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.ip.model.sap.SapOrderBackRequest;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.services.delivery.OrderDeliveryCmd;
import com.jackrain.nea.ps.api.table.PsCProdimItem;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.DateUtil;
import com.jackrain.nea.util.OmsBusinessTypeUtil;
import com.jackrain.nea.util.ValueHolderV14Utils;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 私域平台发货服务
 * @date 2021/12/29 11:23
 */
@Slf4j
@Component
public class OrderDeliveryOfSapImpl implements OrderDeliveryCmd {
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderItemMapper itemMapper;
    @Autowired
    private OmsOrderLogService orderLogService;
    @Autowired
    private PsRpcService psRpcService;
    @DubboReference(group = "ac", version = "1.0")
    private AcLogisticsFeeQueryCmd acLogisticsFeeQueryCmd;


    @Autowired
    private IpRpcService ipRpcService;
    //第三方日志单据类型(零售发货单)
    private static final Long THIRD_LOG_BILL_TYPE_101 = 101L;


    @Override
    public boolean deliveryDeal(OcBOrderRelation ocBOrderRelation, List<String> tips) {

        Long orderId = ocBOrderRelation.getOrderId();
        String billNo = ocBOrderRelation.getOrderInfo().getBillNo();

        //判断是否为手工单，如果为手工单直接标记平台发货
        if ("手工新增".equals(ocBOrderRelation.getOrderInfo().getOrderSource())) {
            //更新发货状态，插入日志
            String logMsg = "OrderId=" + orderId + "为手工新增单，直接标记平台发货";
            orderLogService.addUserOrderLog(orderId, billNo, OrderLogTypeEnum.PLATFORM_SEND.getKey(), logMsg, "",
                    null, null);
            OcBOrder update = new OcBOrder();
            update.setOrderStatus(OmsOrderStatus.PLATFORM_DELIVERY.toInteger());
            update.setId(orderId);
            ocBOrderMapper.updateById(update);
            return true;
        }

        JSONArray build = this.build(ocBOrderRelation);

        List<SapOrderBackRequest> requestList = new ArrayList<>();
        List<String> orderNoList = new ArrayList<>();
        ValueHolderV14 v14;
        try {
            for (int i = 0; i < build.size(); i++) {
                JSONObject jsonObject = build.getJSONObject(i);
                log.info("deliveryDeal jsonObject:{}",jsonObject);
                SapOrderBackRequest request= JSONObject.parseObject(jsonObject.toString(),SapOrderBackRequest.class);
                requestList.add(request);
                orderNoList.add(String.valueOf(jsonObject.get("VBELN")));
            }
            v14 = ipRpcService.returnOrderDelivery(requestList);
            if (v14 == null) {
                v14 = ValueHolderV14Utils.getFailValueHolder("fail");
            }
        } catch (Exception e) {
            log.error(LogUtil.format("deliveryDeal.ExpMsg: {}"), Throwables.getStackTraceAsString(e));
            v14 = ValueHolderV14Utils.getFailValueHolder(e.getMessage());
        }
        AtomicBoolean result = new AtomicBoolean(false);
        AtomicBoolean allSuccess = new AtomicBoolean(true);


        if (v14.getCode() == ResultCode.FAIL) {
            //更新发货状态，插入日志
            for(String orderNo:orderNoList){
                String logMsg = "OrderId=" + orderId + ",平台单号=" + orderNo + "发货通知平台失败,";
                orderLogService.addUserOrderLog(orderId, billNo, OrderLogTypeEnum.PLATFORM_SEND.getKey(), logMsg, "",
                        null, null);
            }
            allSuccess.set(false);
        } else {
            //更新发货状态，插入日志
            for(String orderNo:orderNoList){
                itemMapper.updateItemsWhenDeliverySuccess(orderId, orderNo);
                String logMsg = "OrderId=" + orderId + ",平台单号=" + orderNo + "发货通知平台成功";
                orderLogService.addUserOrderLog(orderId, billNo, OrderLogTypeEnum.PLATFORM_SEND.getKey(), logMsg, "",
                        null, null);
            }
            result.set(true);
        }

        if (allSuccess.get()) {
            OcBOrder update = new OcBOrder();
            update.setOrderStatus(OmsOrderStatus.PLATFORM_DELIVERY.toInteger());
            update.setId(orderId);
            ocBOrderMapper.updateById(update);
        }
        return result.get();
    }


    private JSONArray build(OcBOrderRelation ocBOrderRelation) {

        OcBOrder orderInfo = ocBOrderRelation.getOrderInfo();
        List<OcBOrderItem> orderItemList = ocBOrderRelation.getOrderItemList();
        // 过滤出明细未发货或者发货失败的
        Map<String, List<OcBOrderItem>> itemMap =
                orderItemList.stream().filter(item -> !NumberUtils.INTEGER_ONE.equals(item.getIsSendout())).collect(Collectors.groupingBy(OcBOrderItem::getTid));

        AcLogisticsFeeInfoBySapResult logisticsFeeInfoBySapResult = null;
        if (OmsBusinessTypeUtil.isToBOrder(orderInfo)) {
            itemMap = orderItemList.stream().filter(item -> (!NumberUtils.INTEGER_ONE.equals(item.getIsSendout()) && item.getRealOutNum().compareTo(BigDecimal.ZERO) > 0)).collect(Collectors.groupingBy(OcBOrderItem::getTid));
            try {
                //查询物流费用
                ValueHolderV14<AcLogisticsFeeInfoBySapResult> v14 = acLogisticsFeeQueryCmd.calculateLogisticsFee(orderInfo.getId());
                log.info(LogUtil.format("OrderDeliveryOfSapImpl.build.queryFee orderId:{},v14:{}",
                        "OrderDeliveryOfSapImpl.build.queryFee"), orderInfo.getId(), JSONObject.toJSONString(v14));
                if (v14.isOK() && v14.getData() != null) {
                    logisticsFeeInfoBySapResult = v14.getData();
                }
            } catch (Exception e) {
                log.error(LogUtil.format("OrderDeliveryOfSapImpl.build.queryFee error:{}",
                        "OrderDeliveryOfSapImpl.build.queryFee"), Throwables.getStackTraceAsString(e));
            }
        }

        JSONArray deliverRequests = new JSONArray();

        for (Map.Entry<String, List<OcBOrderItem>> entry : itemMap.entrySet()) {

            String tid = entry.getKey();
            List<OcBOrderItem> items = entry.getValue();
            JSONObject deliverRequest = this.build(orderInfo, tid, logisticsFeeInfoBySapResult);
            deliverRequest.put("item", this.buildItem(orderInfo, items));
            deliverRequests.add(deliverRequest);
        }

        return deliverRequests;
    }


    private JSONObject build(OcBOrder orderInfo, String tid, AcLogisticsFeeInfoBySapResult logisticsFeeInfoBySapResult) {
        JSONObject model = new JSONObject();
        model.put("VBELN", tid);
        model.put("KEYID", orderInfo.getSgBOutBillNo());
        model.put("LIFEX", orderInfo.getBillNo());
        model.put("BLDAT", DateUtil.datenoFormatter.format(orderInfo.getScanTime()));
        model.put("ITM_KZABE", orderInfo.getSgBOutBillNo());
        // 主表物流公司
        model.put("BOLNR", orderInfo.getCpCLogisticsEname());
        // 主表物流单号
        model.put("TRAID", orderInfo.getExpresscode());
        model.put("BILL_TYPE", THIRD_LOG_BILL_TYPE_101);
        if (logisticsFeeInfoBySapResult != null) {
            model.put("ZLIFNR", logisticsFeeInfoBySapResult.getLogisticsSupplierCode());
            model.put("ZLIFNRT", logisticsFeeInfoBySapResult.getLogisticsSupplierName());
            model.put("XABLN", logisticsFeeInfoBySapResult.getBaseFee().stripTrailingZeros().toPlainString());
        }
        return model;
    }

    private JSONArray buildItem(OcBOrder ocBOrder, List<OcBOrderItem> ocBOrderItems) {

        JSONArray items = new JSONArray();

        ocBOrderItems.forEach(item -> {
            try {
                items.add(this.buildItem(ocBOrder, item));
            } catch (Exception e) {
                e.printStackTrace();
            }
        });

        return items;

    }

    private JSONObject buildItem(OcBOrder ocBOrder, OcBOrderItem orderItem) throws Exception {

        JSONObject item = new JSONObject();
        if (orderItem.getOoid().contains("-")){
             String[] split=orderItem.getOoid().split("-");
            item.put("POSNR", split[1]);
        }else {
            item.put("POSNR", orderItem.getOoid());
        }

        // 取值退换货单明细表条码
        item.put("MATNR", orderItem.getPsCSkuEcode());
        // 取值单明细表对应行条码的发货数量
        item.put("LFIMG", orderItem.getRealOutNum());
        // 取值退换货单主表入库实体仓库的仓库编码
        item.put("LGORT", ocBOrder.getCpCPhyWarehouseEcode());
        // 取值零售发货单明细表对应行条码的商品sku档案中的单位
        PsCProdimItem psCProdimItem = psRpcService.selectPsCProDimItemInfo(orderItem.getPsCSkuEcode());
        item.put("VRKME", psCProdimItem.getEcode());
        return item;
    }
}
