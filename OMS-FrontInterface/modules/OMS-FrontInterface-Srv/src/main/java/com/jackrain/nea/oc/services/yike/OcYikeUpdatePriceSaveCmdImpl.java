package com.jackrain.nea.oc.services.yike;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.yike.OcYikeUpdatePriceSaveCmd;
import com.jackrain.nea.oc.oms.api.yike.OcYikeUpdatePriceSubmitCmd;
import com.jackrain.nea.sg.service.OcYikeUpdatePriceSaveService;
import com.jackrain.nea.sg.service.OcYikeUpdatePriceSubmitService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * className: OcYikeUpdatePriceSubmitCmdImpl
 * description:驿客批量修改价格接口保存接口
 *
 * <AUTHOR>
 * create: 2021-12-7
 * @since JDK 1.8
 */
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcYikeUpdatePriceSaveCmdImpl extends CommandAdapter implements OcYikeUpdatePriceSaveCmd {

    @Autowired
    private OcYikeUpdatePriceSaveService service;


    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return service.execute(session);
    }

}
