package com.jackrain.nea.oc.oms.task.plaformdelivery;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.burgeon.mq.core.DefaultProducerSend;
import com.burgeon.r3.xxl.job.starter.helper.R3XxlJobParamHelper;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.oc.oms.constant.MqConstants;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.RuntimeCompute;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 订单平台发货服务，补偿服务
 *
 * @author: hulinyang
 * @since: 2019/09/24
 * create at : 2019/09/24 15:30
 */
@Slf4j
@Component
public class AutoMakeUpPlaformDeliveryTask extends BaseR3Task implements IR3Task {


    @Autowired
    private DefaultProducerSend defaultProducerSend;
    @Autowired
    private PropertiesConf propertiesConf;

    @Override
    @XxlJob("AutoMakeUpPlaformDeliveryTask")
    public RunTaskResult execute(JSONObject params) {
        params = R3XxlJobParamHelper.xxlParam2R3Json();
        RunTaskResult result = new RunTaskResult();

        try {
            RuntimeCompute runtimeCompute = new RuntimeCompute();
            runtimeCompute.startRuntime();
            PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
            Integer failNum = Optional.ofNullable(params.getInteger("failNum")).orElse(5);
            Integer pullNum = config.getProperty("oms.oc.order.autoPlaformDeliveryOrder.pull.num", 200);
            List<Long> orderIdList = ES4Order.findIdsByOrderStatusAndSourceAndForce(failNum, 0, pullNum,
                    params.getLong("platform"));
            if (log.isDebugEnabled()) {
                log.debug("平台发货补偿任务，单次最大拉取记录数:{},订单为:{}", pullNum, orderIdList);
            }
            sendMq(orderIdList);
            result.setSuccess(true);
            double usedTime = runtimeCompute.endRuntime();
            log.debug(LogUtil.format("平台发货补偿定时任务执行成功!UsedTime:{}",  "平台发货补偿定时任务"), usedTime);
        } catch (Exception ex) {
            log.error(LogUtil.format("AutoMakeUpPlatformDeliveryTask.Execute.Error！{}", "平台发货补偿定时任务"), Throwables.getStackTraceAsString(ex));
            result.setSuccess(false);
            result.setMessage(ex.getMessage());
        }

        return result;
    }

    /**
     * 发送批量 -- 当前单条，为后续可能改成批量预留逻辑
     *
     * @param orderIds
     */
    private void sendMq(List<Long> orderIds) {
        if (CollectionUtils.isNotEmpty(orderIds)) {
            try {
                // 直接发送ID，不封装对象
                // topic -- 定时任务共享的topic
//                String topic = propertiesConf.getProperty("oms.oc.order.autotask.mq.topic");
                String topic = MqConstants.TOPIC_R3_OC_OMS_CALL_AUTOTASK;
                // tag -- 平台发货tag
//                String tag = propertiesConf.getProperty("oms.oc.order.autotask.platformDelivery.mq.tag");
                String tag = MqConstants.TAG_R3_OC_OMS_CALL_AUTOTASK;

                if (Objects.nonNull(topic) && Objects.nonNull(tag)) {
//                    sendHelper.sendMessage(JSON.toJSONString(orderIds), topic, tag);
                    defaultProducerSend.sendTopic(topic, tag, JSON.toJSONString(orderIds), null);
                } else {
                    log.error("platform.delivery.task.topicOrTagIsNull:{}/{}/{}", topic, tag, orderIds);
                }
            } catch (Exception e) {
                log.error(LogUtil.format("platform.delivery.task.sendMqError",  "平台发货补偿定时任务sendMqError"), Throwables.getStackTraceAsString(e));
            }
        }
    }

}