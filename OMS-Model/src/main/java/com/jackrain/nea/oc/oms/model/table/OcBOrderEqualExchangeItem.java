package com.jackrain.nea.oc.oms.model.table;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * oc_b_order_equal_exchange_item
 * <AUTHOR>
@TableName
@Data
public class OcBOrderEqualExchangeItem implements Serializable {
    /**
     * 明细编号
     */
    private Long id;

    /**
     * 组合名称
     */
    private String groupName;

    /**
     * 商品数字编号
     */
    private String numIid;

    /**
     * 是否是赠品
     */
    private Integer isGift;

    /**
     * 平摊金额
     */
    private BigDecimal orderSplitAmt;

    /**
     * 库位
     */
    private Long storeSite;

    /**
     * 国标码
     */
    private String barcode;

    /**
     * 商品id
     */
    private Long psCProId;

    /**
     * 商品货号
     */
    private String psCProEcode;

    /**
     * 商品名称
     */
    private String psCProEname;

    /**
     * 颜色id
     */
    private Long psCClrId;

    /**
     * 颜色编码
     */
    private String psCClrEcode;

    /**
     * 颜色名称
     */
    private String psCClrEname;

    /**
     * 尺寸id
     */
    private Long psCSizeId;

    /**
     * 尺寸编码
     */
    private String psCSizeEcode;

    /**
     * 尺寸名称
     */
    private String psCSizeEname;

    /**
     * 商品属性
     */
    private String psCProMaterieltype;

    /**
     * 商品供应类型
     */
    private Long psCProSupplyType;

    /**
     * 规格
     */
    private String skuSpec;

    /**
     * 标题
     */
    private String title;

    /**
     * 商品路径
     */
    private String picPath;

    /**
     * 子订单编号(明细编号)
     */
    private String ooid;

    /**
     * 条码id
     */
    private Long psCSkuId;

    /**
     * 条码编码
     */
    private String psCSkuEcode;

    /**
     * 标准重量
     */
    private BigDecimal standardWeight;

    /**
     * 标准价
     */
    private BigDecimal priceList;

    /**
     * 成交价格
     */
    private BigDecimal price;

    /**
     * 优惠金额
     */
    private BigDecimal amtDiscount;

    /**
     * 调整金额
     */
    private BigDecimal adjustAmt;

    /**
     * 单行实际成交金额
     */
    private BigDecimal realAmt;

    /**
     * 订单编号
     */
    private Long ocBOrderId;

    /**
     * 数量
     */
    private BigDecimal qty;

    /**
     * 取消状态(0,1:否,6:是)
     */
    private Integer refundStatus;

    /**
     * 平台单号
     */
    private String tid;

    /**
     * 预售状态
     */
    private Integer isPresalesku;

    /**
     * 活动编号
     */
    private String activeId;

    /**
     * 虚拟条码
     */
    private String giftbagSku;

    /**
     * 所属组织
     */
    private Long adOrgId;

    /**
     * 所属公司
     */
    private Long adClientId;

    /**
     * 创建人ID
     */
    private Long ownerid;

    /**
     * 创建人姓名
     */
    private String ownerename;

    /**
     * 创建人用户名
     */
    private String ownername;

    /**
     * 创建时间
     */
    private Date creationdate;

    /**
     * 修改人ID
     */
    private Long modifierid;

    /**
     * 修改人姓名
     */
    private String modifierename;

    /**
     * 修改人用户名
     */
    private String modifiername;

    /**
     * 修改时间
     */
    private Date modifieddate;

    /**
     * 是否可用
     */
    private String isactive;

    /**
     * 商品类型(0:正常,1:福袋,2:组合,3:预售;4:未拆分的组合商品;5:轻供商品)
     */
    private Long proType;

    /**
     * 单件实际成交价
     */
    private BigDecimal priceActual;

    /**
     * 性别
     */
    private Long sex;

    /**
     * sku数字编码
     */
    private String skuNumiid;

    /**
     * 赠品类型 0 否 1 是系统赠品 2 平台赠品
     */
    private String giftType;

    /**
     * 组合商品购买数量
     */
    private BigDecimal qtyGroup;

    /**
     * 是否手工新增商品
     */
    private String isManualAdd;

    /**
     * 赠品挂靠关系
     */
    private String giftRelation;

    /**
     * 平台sku编码
     */
    private String psCSkuPtEcode;

    /**
     * 平台商品名称
     */
    private String ptProName;

    /**
     * 条码名称
     */
    private String psCSkuEname;

    /**
     * 商品毛重
     */
    private BigDecimal grossWeight;

    /**
     * 主播ID
     */
    private String anchorId;

    /**
     * 主播昵称
     */
    private String anchorName;

    /**
     * 直播平台
     */
    private String livePlatform;

    /**
     * 直播标识：1-直播单，2-非直播单
     */
    private Integer liveFlag;

    /**
     * 预售类型 0:非预售 1:平台定金预售  2:店铺全款预售 3:自定义全款预售
     */
    private Integer presellType;

    /**
     * 商品品牌ID
     */
    private Long psCBrandId;

    /**
     * 京东平台优惠券应收金额
     */
    private BigDecimal amtJingdongCoupon;

    /**
     * 直播主体
     */
    private Long acFManageId;

    /**
     * 直播主体(经营主体编码）
     */
    private String acFManageEcode;

    /**
     * 直播主体(经营主体名称)
     */
    private String acFManageEname;

    /**
     * 配合主体
     */
    private Long cooperateId;

    /**
     * 配合主体(经营主体编码）
     */
    private String cooperateEcode;

    /**
     * 配合主体(经营主体名称)
     */
    private String cooperateEname;

    /**
     * 直播场次
     */
    private Long liveEvents;

    /**
     * 预计发货日期
     */
    private Date expectedDeliveryDate;

    /**
     * 预计发货日期
     */
    private Date estimateConTime;

    /**
     * 预售类型
     */
    private String advanceType;

    /**
     * 渠道预售活动id
     */
    private Long advanceSaleId;

    /**
     * 渠道预售活动NO
     */
    private String advanceSaleBillNo;

    /**
     * 购物金核销子订单权益金分摊金额
     */
    private BigDecimal expandCardExpandPriceUsedSuborder;

    /**
     * 购物金核销子订单本金分摊金额
     */
    private BigDecimal expandCardBasicPriceUsedSuborder;

    /**
     * 是否存在预计发货日
     */
    private String isExistConTime;

    /**
     * 官网券码
     */
    private String gwCouponCode;

    /**
     * 组合商品平台条码id
     */
    private String ptGroupSkuId;

    /**
     * 组合商品平台商品id
     */
    private String ptGroupNumId;

    private String canSplit;

    /**
     * 是否开启批次管理(1是)
     */
    private Integer isEnableExpiry;

    /**
     * 1:生产日期范围,2:生产天数范围3:生产日期和数量
     */
    private Integer expiryDateType;

    /**
     * 效期范围
     */
    private String expiryDateRange;

    /**
     * 对等换货标识
     */
    private String equalExchangeMark;

    /**
     * 已退数量
     */
    private BigDecimal qtyRefund;



    private Long mDim4Id;


    private Long mDim6Id;

    /**
     * 赠品拆单类型 1不拆单  2 可拆弹  3赠品后发
     */
    private Integer isGiftSplit;

    /**
     * 组合商品标识
     */
    private String groupGoodsMark;

    /**
     * 小米有品商家外部编码
     */
    private String reserveVarchar04;

    /**
     * 订单效期标签
     */
    private String orderLabel;


    private static final long serialVersionUID = 1L;

    /**
     * 缺货不还原
     */
    private String outStockNoRestore;
}