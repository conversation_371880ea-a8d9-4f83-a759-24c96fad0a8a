package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.OMSFrontInterfaceMainApplication;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.ip.model.vips.VipCreateShipResetWorkflowResult;
import com.jackrain.nea.oc.oms.model.table.IpBJitxResetShipWorkflow;
import com.jackrain.nea.util.ValueHolderV14Utils;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * Description： redis缓存测试
 * Author: RESET
 * Date: Created in 2020/8/31 8:37
 * Modified By:
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = OMSFrontInterfaceMainApplication.class)
@Slf4j
public class OmsResetShipServiceTest {

    @Autowired
    IpBJitxResetShipWorkflowService service;

    @Test
    public void test() {
        String obj = "[{\"AD_CLIENT_ID\":37,\"AD_ORG_ID\":27,\"CP_C_SHOP_ECODE\":\"D0009999\",\"CP_C_SHOP_ID\":42,\"CP_C_SHOP_TITLE\":\"唯品会JITX测试店铺\",\"CREATED_STATUS\":0,\"CREATIONDATE\":1635157063000,\"FAIL_NUMBER\":0,\"ID\":16,\"ISACTIVE\":\"Y\",\"MODIFIEDDATE\":1635157063000,\"MODIFIERID\":893,\"MODIFIERNAME\":\"root\",\"ORDER_ID\":9685,\"ORDER_NO\":\"OM21102500000308\",\"ORDER_SN\":\"SNT760051107098\",\"ORDER_SN_LIST\":\"SNT760051107098,SNTE09877651107\",\"OWNERID\":893,\"OWNERNAME\":\"root\",\"REASON_CODE\":\"1001\",\"REASON_REMARK\":\"原仓缺货\",\"REQUEST_ID\":\"16\",\"SELLER_NICK\":\"斯凯奇唯品会测试店\",\"UPDATE_TIME\":1635157063000,\"VENDOR_ID\":\"23932\"}]";
        String result = "{\"code\":0,\"data\":{\"result\":{\"message\":\"成功\",\"status\":\"200\"},\"workflows\":[{\"request_id\":\"16\",\"result\":{\"message\":\"订单SNT760051107098状态为已发货才可以重置,未发货/已揽收/取消都无法重置\",\"status\":\"原仓缺货\"},\"workflow_sn\":\"SNT760051107098\"}]},\"message\":\"成功\",\"oK\":true}";
        JSONObject resultJson = JSON.parseObject(result);
        JSONObject data = resultJson.getJSONObject("data");
        VipCreateShipResetWorkflowResult workflowResult = JSON.parseObject(data.toJSONString(), VipCreateShipResetWorkflowResult.class);
        ;
        service.transferResult(JSON.parseArray(obj, IpBJitxResetShipWorkflow.class), ValueHolderV14Utils.custom(ResultCode.SUCCESS, "", workflowResult));
    }

}
