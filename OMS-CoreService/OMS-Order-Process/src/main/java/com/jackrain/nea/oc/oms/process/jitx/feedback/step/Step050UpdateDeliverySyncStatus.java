package com.jackrain.nea.oc.oms.process.jitx.feedback.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.SyncStatus;
import com.jackrain.nea.oc.oms.model.enums.TimeOrderVipStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpJitxDeliveryRelation;
import com.jackrain.nea.oc.oms.services.IpVipTimeOrderService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 释放当前行，更新反馈状态为1;
 *
 * @author: chenxiulou
 * @since: 2019-01-21
 * create at : 2019-01-21 22:33
 */
//@Step(order = 50, description = "更新寻仓反馈状态为寻仓中")
@Component
@Slf4j
public class Step050UpdateDeliverySyncStatus extends BaseJitxDeliveryProcessStep
        implements IOmsOrderProcessStep<IpJitxDeliveryRelation> {

    @Autowired
    protected IpVipTimeOrderService ipVipTimeOrderService;

    @Override
    public ProcessStepResult<IpJitxDeliveryRelation> startProcess(IpJitxDeliveryRelation orderInfo,
                                                                  ProcessStepResult preStepResult,
                                                                  boolean isAutoMakeup, User operateUser) {
        String orderNo = orderInfo.getOrderNo();
        //更新寻仓单反馈状态为：寻仓中
        this.ipJitxDeliveryService.updateJitxSyncStatus(orderNo,
                SyncStatus.SYNCIN, orderInfo.getRemarks() != null ? orderInfo.getRemarks() : "寻仓中");

        //更新时效订单状态为：寻仓中
        ipVipTimeOrderService.updateIpBTimeOrderVipByOrderSn(orderNo, TimeOrderVipStatusEnum.IN_SEEKING_STORE.getValue(), "寻仓中！");

        return new ProcessStepResult<>(StepStatus.SUCCESS);
    }
}
