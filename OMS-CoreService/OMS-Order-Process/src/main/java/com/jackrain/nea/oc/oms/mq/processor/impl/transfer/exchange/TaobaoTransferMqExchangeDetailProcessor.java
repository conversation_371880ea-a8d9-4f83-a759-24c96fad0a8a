package com.jackrain.nea.oc.oms.mq.processor.impl.transfer.exchange;

import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.OperateType;
import com.jackrain.nea.oc.oms.model.enums.OrderType;
import com.jackrain.nea.oc.oms.model.relation.OmsTaobaoExchangeRelation;
import com.jackrain.nea.oc.oms.mq.processor.IMqOrderDetailProcessor;
import com.jackrain.nea.oc.oms.process.transfer.impl.exchange.taobaonew.TaobaoTransferExchangeProcessImpl;
import com.jackrain.nea.oc.oms.services.OmsTaobaoExchangeService;
import com.jackrain.nea.oc.oms.step.ProcessStepResultList;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR> 孙勇生
 * create at:  19/3/6  20:33
 * @description: 淘宝退换货单退款单消息处理器
 */
@Slf4j
public class TaobaoTransferMqExchangeDetailProcessor implements IMqOrderDetailProcessor {
    @Autowired
    private OmsTaobaoExchangeService omsTaobaoExchangeService;

    @Autowired
    private TaobaoTransferExchangeProcessImpl taobaoTransferExchangeProcess;

    @Override
    public ProcessStepResultList start(OperateOrderMqInfo orderMqInfo) {
        String orderNo = orderMqInfo.getOrderNo();

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Start MqOrder Transfer", orderNo));
        }

        OmsTaobaoExchangeRelation relation = omsTaobaoExchangeService.selectTaobaoExchangeInfo(orderNo);
        if (relation == null) {
            String errorMessage = Resources.getMessage("TaoBaoExchange.Received.OrderMqInfo.Not.Exist!OrderNo=" + orderNo);
            log.error(LogUtil.format(errorMessage));
            return new ProcessStepResultList();
        } else {
            ProcessStepResultList resultList = taobaoTransferExchangeProcess.start(relation,
                    false, SystemUserResource.getRootUser());
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("Finished.MqOrder.Transfer,Result= {}", orderNo), resultList);
            }
            return resultList;
        }
    }

    @Override
    public boolean checkMqIsCanExecute(OperateOrderMqInfo orderMqInfo) {
        return orderMqInfo.getOperateType() == OperateType.TRANSFER_ORDER
                && orderMqInfo.getChannelType() == ChannelType.TAOBAO
                && orderMqInfo.getOrderType() == OrderType.EXCHANGE;
    }
}
