package com.jackrain.nea.rpc;

import com.alibaba.fastjson.JSON;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.hub.api.minipt.HubOrderCmd;
import com.jackrain.nea.hub.model.HXInvoicingModel.ResultType;
import com.jackrain.nea.hub.model.minipt.OrderDeliveryReq;
import com.jackrain.nea.hub.model.minipt.OrderRefundReq;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolderV14Utils;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR> zhuxing
 * @Date : 2022-09-06 13:51
 * @Description : TODO
 **/
@Slf4j
@Component
public class HubRpcService {

    @Reference(group = "hub", version = "1.0")
    private HubOrderCmd hubOrderCmd;

    /**
     * 退单审核同步美团闪购
     * @param orderReq
     * @return
     */
    public ValueHolderV14 meituanFlashSalesAg(OrderRefundReq orderReq){
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("meituanFlashSalesAg.Request:{}",
                    "HubRpcService"), JSON.toJSONString(orderReq));
        }
        ResultType resultType = hubOrderCmd.refundorder(orderReq);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("meituanFlashSalesAg.Response:{}",
                    "HubRpcService"), JSON.toJSONString(resultType));
        }
        String code = resultType.getCode();
        if(code.equals(ResultCode.SUCCESS + "")){
            return ValueHolderV14Utils.getSuccessValueHolder(resultType.getMsg());
        }else {
            return ValueHolderV14Utils.getFailValueHolder(resultType.getMsg());
        }
    }

    /**
     * 小平台订单发货服务
     * @param deliveryReq 发货明细
     * @return 请求结果
     */
    public ValueHolderV14 deliveryOrder(OrderDeliveryReq deliveryReq){
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("deliveryOrder.Request:{}",
                    "HubRpcService.deliveryOrder"), JSON.toJSONString(deliveryReq));
        }
        ResultType resultType = hubOrderCmd.deliveryOrder(deliveryReq);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("deliveryOrder.Response:{}",
                    "HubRpcService.deliveryOrder"), JSON.toJSONString(resultType));
        }
        String code = resultType.getCode();
        if(code.equals(ResultCode.SUCCESS + "")){
            return ValueHolderV14Utils.getSuccessValueHolder(resultType.getMsg());
        }else {
            return ValueHolderV14Utils.getFailValueHolder(resultType.getMsg());
        }
    }

    /**
     * 多包裹发货
     *
     * @param deliveryReq
     * @return
     */
    public ValueHolderV14 manyDelivery(OrderDeliveryReq deliveryReq) {
        ResultType resultType = hubOrderCmd.manyDeliveryOrder(deliveryReq);
        log.info(LogUtil.format("manyDelivery.deliveryReq:{},resultType:{}", "manyDelivery"), JSON.toJSONString(deliveryReq), JSON.toJSONString(resultType));
        if (resultType.getCode().equals(ResultCode.SUCCESS + "")) {
            return ValueHolderV14Utils.getSuccessValueHolder(resultType.getMsg());
        } else {
            return ValueHolderV14Utils.getFailValueHolder(resultType.getMsg());
        }
    }
}
