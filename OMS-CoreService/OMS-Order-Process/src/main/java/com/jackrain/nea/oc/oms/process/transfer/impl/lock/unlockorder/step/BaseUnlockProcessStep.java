package com.jackrain.nea.oc.oms.process.transfer.impl.lock.unlockorder.step;

import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.services.IpOrderLockService;
import com.jackrain.nea.oc.oms.services.OrderInterceptionService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Descroption 锁单处理阶段抽象基类
 * <AUTHOR>
 * @Date 2019/10/9 13:42
 */
public abstract class BaseUnlockProcessStep {
    @Autowired
    protected OcBOrderMapper ocBOrderMapper;
    @Autowired
    protected IpOrderLockService ipOrderLockService;
    @Autowired
    protected OrderInterceptionService orderInterceptionService;
}
