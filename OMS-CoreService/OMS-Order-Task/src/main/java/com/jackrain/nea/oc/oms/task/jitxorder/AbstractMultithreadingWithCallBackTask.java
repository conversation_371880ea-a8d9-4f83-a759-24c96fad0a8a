//package com.jackrain.nea.oc.oms.task.jitxorder;
//
//import com.alibaba.fastjson.JSONObject;
//import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
//import com.jackrain.nea.oc.oms.sap.Oms2SapDBMetaCache;
//import com.jackrain.nea.oc.oms.task.BaseR3Task;
//import com.jackrain.nea.resource.SystemUserResource;
//import com.jackrain.nea.sys.domain.ValueHolderV14;
//import com.jackrain.nea.task.IR3Task;
//import com.jackrain.nea.task.RunTaskResult;
//import com.jackrain.nea.web.face.User;
//import com.xxl.job.core.handler.annotation.XxlJob;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
//
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Map;
//import java.util.Set;
//import java.util.concurrent.Callable;
//import java.util.concurrent.Future;
//
///**
// * <AUTHOR>
// * @create 2020-12-16
// * @desc
// **/
//@Slf4j
//public abstract class AbstractMultithreadingWithCallBackTask extends BaseR3Task implements IR3Task {
//
//    @Autowired
//    private ThreadPoolTaskExecutor commonThreadPoolExecutor;
//
//    protected abstract String threadPoolName();
//
//    protected abstract String taskTableName();
//
//
//    protected abstract <T> Callable<T> executeRunnable(String nodeName, String tableName, User operateUser);
//
//    @Override
//    public RunTaskResult execute(JSONObject params) {
//        RunTaskResult result = new RunTaskResult();
//        try {
//            AssertUtil.assertException(commonThreadPoolExecutor == null, "线程池创建结果为null");
//            Set<String> nodes = topMap.keySet();
//            if (CollectionUtils.isEmpty(nodes)) {
//                if (log.isDebugEnabled()) {
//                    log.debug("tableName.nodes not get！" + taskTableName());
//                }
//                result.setSuccess(false);
//                result.setMessage("请检查环境，node获取不到！！");
//                return result;
//            }
//            User operateUser = SystemUserResource.getRootUser();
//            List<Future<ValueHolderV14>> results = new ArrayList<>();
//            for (String nodeName : nodes) {
//                results.add(commonThreadPoolExecutor.submit(executeRunnable(nodeName, topMap.get(nodeName), operateUser)));
//            }
//            result.setSuccess(true);
//            result.setMessage("任务执行完成");
//        } catch (Exception ex) {
//            result.setSuccess(false);
//            result.setMessage(ex.getMessage());
//        }
//        return result;
//    }
//}
