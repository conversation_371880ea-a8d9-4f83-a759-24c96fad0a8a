package com.jackrain.nea.oc.services;

import com.jackrain.nea.oc.oms.api.ModifyReturnOrderIsWriteoffCmd;
import com.jackrain.nea.oc.oms.services.ModifyReturnOrderIsWriteoffService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: 周琳胜
 * @since: 2019/4/22
 * create at : 2019/4/22 8:56
 */
@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class ModifyReturnOrderIsWriteoffCmdImpl implements ModifyReturnOrderIsWriteoffCmd {
    @Autowired
    ModifyReturnOrderIsWriteoffService modifyReturnOrderIsWriteoffService;

    @Override
    public ValueHolderV14 execute(List<Long> ids) {
        return modifyReturnOrderIsWriteoffService.modifyIsWriteoffService(ids);
    }
}
