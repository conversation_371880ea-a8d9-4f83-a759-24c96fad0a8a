package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.api.OcbOrderMergeMenuCmd;
import com.jackrain.nea.oc.oms.model.relation.UserPermission;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySessionImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @author: 孙继东
 * @since: 2020-02-13
 * create at : 2020-02-13 9:25
 */
@Api(value = "MergeOrderCtrl", tags = "订单合并按钮")
@Slf4j
@RestController
public class MergeOrderCtrl {

    @Autowired
    private OcbOrderMergeMenuCmd ocbOrderMergeMenuCmd;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    /**
     * 合并订单ctrl
     *
     * @param request 获取用户信息
     * @param param   请求参数
     * @return 合并结果
     */
    @ApiOperation(value = "订单合并")
    @PostMapping(value = "/api/cs/oc/oms/v1/mergeOrder")
    public JSONObject mergeOrder(HttpServletRequest request, @RequestParam(value = "param") String param) {
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder vh = ocbOrderMergeMenuCmd.execute(querySession);
        JSONObject result = JSONObject.parseObject(JSONObject.toJSONString(
                vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
        return result;
    }

    /**
     * 合并订单ctrl
     * 2B的待审核+2C的待审核+2C的待寻源卡单
     *
     * @param request 获取用户信息
     * @param param   请求参数
     * @return 合并结果
     */
    @ApiOperation(value = "创建订单页面订单合并所有")
    @PostMapping(value = "/api/cs/oc/oms/v1/mergeOrderOne")
    public JSONObject mergeOrderOne(HttpServletRequest request, @RequestParam(value = "param") String param) {
        //param: {"ids":[20,26]}
//        User user = SystemUserResource.getRootUser();
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder vh = ocbOrderMergeMenuCmd.mergeOrderOne(querySession);
        JSONObject result = JSON.parseObject(JSON.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
        return result;
    }

    /**
     * 取消合并ctrl
     *
     * @param request 获取用户信息
     * @param param   请求参数
     * @return 取消合并结果
     */
    @ApiOperation(value = "取消合并")
    @PostMapping(value = "/api/cs/oc/oms/v1/cancelMergeOrder")
    public ValueHolderV14 cancelMergeOrder(HttpServletRequest request, @RequestParam(value = "param") String param) {
        ValueHolderV14 vh14 = new ValueHolderV14();
        try {
            User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
            UserPermission usrPem = UserPermissionCtrlHelper.currentUserPermission(user, "OC_B_ORDER");
            if (usrPem == null) {
                vh14.setMessage("未获取到用户权限");
                vh14.setCode(ResultCode.FAIL);
                return vh14;
            }
            JSONObject jsonParam = JSON.parseObject(param);
            String idsStr = jsonParam.getString("ids");
            if(StringUtils.isEmpty(idsStr)){
                vh14.setCode(ResultCode.FAIL);
                vh14.setMessage("请勾选需要取消合并的订单");
                return vh14;
            }
            List<Long> ids = JSONArray.parseArray(idsStr, Long.class);
            vh14 = ocbOrderMergeMenuCmd.cancelMergeOrder(user, ids);
        } catch (Exception e) {
            vh14.setCode(ResultCode.FAIL);
            vh14.setMessage(e.getMessage());
        }
        return vh14;
    }
}

