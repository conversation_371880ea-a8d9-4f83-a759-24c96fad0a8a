package com.jackrain.nea.oc.services.ac;

import com.jackrain.nea.ac.service.PayableAdjustmentImportService;
import com.jackrain.nea.oc.oms.api.ac.OmsPayableAdjustmentImportCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

/**
 * @author:洪艺安
 * @since: 2019/7/11
 * @create at : 2019/7/11 9:12
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OmsPayableAdjustmentImportCmdImpl extends CommandAdapter implements OmsPayableAdjustmentImportCmd {
    @Autowired
    private PayableAdjustmentImportService payableAdjustmentImportService;

    @Override
    public ValueHolderV14 downloadTemp() {
        return payableAdjustmentImportService.downloadTemp();
    }

    @Override
    public ValueHolderV14 importPayableAdjustment(MultipartFile file, User user) {
        return payableAdjustmentImportService.importPayableAdjustment(file, user);
    }
}
