package com.jackrain.nea.oc.oms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jackrain.nea.st.model.StCShortStockNoSplitStrategyDetailEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface StCShortStockNoSplitStrategyDetailMapper extends BaseMapper<StCShortStockNoSplitStrategyDetailEntity> {

    /**
     * 根据策略id 查询到所有策略明细的数据
     *
     * @param strategyId 策略ID
     * @return
     */
    @Select("select * from st_c_short_stock_no_split_strategy_detail where  ISACTIVE='Y' and STRATEGY_ID = #{strategyId} ORDER BY ageing DESC ")
    List<StCShortStockNoSplitStrategyDetailEntity> selectByStrategyId(Long strategyId);
}

