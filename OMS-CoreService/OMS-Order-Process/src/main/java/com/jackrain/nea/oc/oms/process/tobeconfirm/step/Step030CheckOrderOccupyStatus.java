package com.jackrain.nea.oc.oms.process.tobeconfirm.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.resources.OrderOccupyStatus;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;

/**
 * 判断占单状态
 *
 * @author: 易邵峰
 * @since: 2019-01-18
 * create at : 2019-01-18 14:15
 */
@Step(order = 30, description = "判断占单状态")
@Slf4j
public class Step030CheckOrderOccupyStatus extends BaseTobeConfirmedProcessStep implements IOmsOrderProcessStep<OcBOrderRelation> {

    /**
     * 获取下一阶段操作Class
     *
     * @param orderInfo 订单信息
     * @return 下一阶段操作Class
     */
    private Class<? extends IOmsOrderProcessStep<OcBOrderRelation>> getNextStepOperateClass(
            OcBOrderRelation orderInfo) {
        if (orderInfo.getOrderInfo().getOccupyStatus() == null
                || orderInfo.getOrderInfo().getOccupyStatus() == OrderOccupyStatus.STATUS_0 ) {
            return Step040MarkLabelService.class;
        } else if (orderInfo.getOrderInfo().getOccupyStatus() == OrderOccupyStatus.STATUS_5) {
            return Step040MarkLabelService.class;
        } else if (orderInfo.getOrderInfo().getOccupyStatus() == OrderOccupyStatus.STATUS_10) {
            return Step050CheckComposeOrder.class;
        } else if (orderInfo.getOrderInfo().getOccupyStatus() == OrderOccupyStatus.STATUS_20) {
            return Step070CheckAndUpdateBlanceMoney.class;
        } else if (orderInfo.getOrderInfo().getOccupyStatus() == OrderOccupyStatus.STATUS_30) {
            return Step070CheckAndUpdateBlanceMoney.class;
        }else if (orderInfo.getOrderInfo().getOccupyStatus() == OrderOccupyStatus.STATUS_40) {
            return Step080CallPromotionPreExecService.class;
        } else if (orderInfo.getOrderInfo().getOccupyStatus() == OrderOccupyStatus.STATUS_45) {
            return Step090CyclePromotionExecService.class;
        } else if (orderInfo.getOrderInfo().getOccupyStatus() == OrderOccupyStatus.STATUS_50) {
            return Step100ExecuteExpiryDateService.class;
        } else if (orderInfo.getOrderInfo().getOccupyStatus() == OrderOccupyStatus.STATUS_60) {
            return Step100ExecuteExpiryDateService.class;
        } else if (orderInfo.getOrderInfo().getOccupyStatus() == OrderOccupyStatus.STATUS_70) {
            return Step100ExecuteExpiryDateService.class;
        } else if (orderInfo.getOrderInfo().getOccupyStatus() == OrderOccupyStatus.STATUS_75) {
            return Step075ImperfectStrategyService.class;
        } else if (orderInfo.getOrderInfo().getOccupyStatus() == OrderOccupyStatus.STATUS_80) {
            return Step100ExecuteExpiryDateService.class;
        } else if (orderInfo.getOrderInfo().getOccupyStatus() == OrderOccupyStatus.STATUS_90) {
            return Step100ExecuteExpiryDateService.class;
        }else if (orderInfo.getOrderInfo().getOccupyStatus() == OrderOccupyStatus.STATUS_110) {
            return Step150SourcingBeforeDisassemble.class;
        }else if (orderInfo.getOrderInfo().getOccupyStatus() == OrderOccupyStatus.STATUS_160) {
            return Step160GenerateCyclePurchaseSubOrder.class;
        } else if (orderInfo.getOrderInfo().getOccupyStatus() == OrderOccupyStatus.STATUS_170) {
            return Step150SourcingBeforeDisassemble.class;
        } else {
            return null;
        }
    }

    @Override
    public ProcessStepResult<OcBOrderRelation> startProcess(OcBOrderRelation orderInfo, ProcessStepResult preStepResult,
                                                            boolean isAutoMakeup, User operateUser) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("TobeConfirmed.Step020CheckOrderOccupyStatus,OccupyStatus={},判断占单状态",
                    "Step020CheckOrderOccupyStatus", orderInfo.getOrderId()), orderInfo.getOrderInfo().getOccupyStatus());
        }
        String message = "OrderID=" + orderInfo.getOrderId()
                + ";OccupyStatus=" + (orderInfo.getOrderInfo().getOccupyStatus() == null ? "" : orderInfo.getOrderInfo().getOccupyStatus());

        Class<? extends IOmsOrderProcessStep<OcBOrderRelation>> nextStepOperateClass = this.getNextStepOperateClass(
                orderInfo);
        if (nextStepOperateClass != null) {
            message += "下一阶段=" + nextStepOperateClass;
            return new ProcessStepResult<>(StepStatus.SUCCESS, null, message, nextStepOperateClass);
        } else {
            message += "结束流程";
            return new ProcessStepResult<>(StepStatus.FINISHED, message);
        }
    }
}
