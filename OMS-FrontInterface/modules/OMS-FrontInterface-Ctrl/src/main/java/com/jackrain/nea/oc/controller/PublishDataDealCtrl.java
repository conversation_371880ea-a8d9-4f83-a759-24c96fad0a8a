package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.model.request.CycleConfirmRequest;
import com.jackrain.nea.oc.oms.services.CycleBuyInfoService;
import com.jackrain.nea.oc.oms.services.OmsOrderOutService;
import com.jackrain.nea.oc.oms.services.PublishDataDealService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 发布、处理数据
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
public class PublishDataDealCtrl {

    @Resource
    private PublishDataDealService publishDataDealService;
    @Resource
    private OmsOrderOutService omsOrderOutService;
    @Resource
    private CycleBuyInfoService cycleBuyInfoService;

    /**
     * refresh product level by billNos
     *
     * @param request
     * @param obj
     * @return
     */
    @RequestMapping(value = "/api/cs/oc/oms/v1/dealdata/productLevel/ids", method = RequestMethod.POST)
    public String refreshIds(HttpServletRequest request, @RequestBody JSONObject obj) {
        String data = obj.getString("data");
        Integer batchNum = obj.getInteger("batchNum");

        List<String> ids = Arrays.asList(data.split(","));
        if (CollectionUtils.isEmpty(ids)) {
            return "param ids is empty";
        }

        List<Long> orderIds = ids.stream().map(Long::valueOf).collect(Collectors.toList());
        publishDataDealService.refreshProduceLevelByBillNos(orderIds, batchNum);

        return "done";
    }

    @RequestMapping(value = "/api/cs/oc/oms/v1/dealdata/productLevel/ids/equal", method = RequestMethod.POST)
    public String refreshEqualIds(HttpServletRequest request, @RequestBody JSONObject obj) {
        String data = obj.getString("data");
        Integer batchNum = obj.getInteger("batchNum");

        List<String> ids = Arrays.asList(data.split(","));
        if (CollectionUtils.isEmpty(ids)) {
            return "param ids is empty";
        }
        List<Long> orderIds = ids.stream().map(Long::valueOf).collect(Collectors.toList());
        publishDataDealService.refreshProduceLevelByEqualBillNos(orderIds, batchNum);

        return "done";
    }


    @RequestMapping(value = "/api/cs/oc/oms/v1/dealdata/productLevel/ids/ext", method = RequestMethod.POST)
    public String refreshExts(HttpServletRequest request, @RequestBody JSONObject obj) {
        String data = obj.getString("data");
        Integer batchNum = obj.getInteger("batchNum");

        List<String> ids = Arrays.asList(data.split(","));
        if (CollectionUtils.isEmpty(ids)) {
            return "param ids is empty";
        }
        List<Long> orderIds = ids.stream().map(Long::valueOf).collect(Collectors.toList());
        publishDataDealService.refreshProduceLevelByExtBillNos(orderIds, batchNum);
        return "done";
    }

    /**
     * refresh product level by order status
     *
     * @param request
     * @param obj
     * @return
     */
    @RequestMapping(value = "/api/cs/oc/oms/v1/dealdata/productLevel/status", method = RequestMethod.POST)
    public String refreshStatus(HttpServletRequest request, @RequestBody JSONObject obj) {
        Integer status = obj.getInteger("status");
        Integer batchNum = obj.getInteger("batchNum");

        publishDataDealService.refreshProduceLevelByStatus(status, batchNum);
        return "done";
    }

    /**
     * 部分出库异常补（真实部分出库订单补偿）
     *
     * @param request
     * @param obj
     * @return
     */
    @RequestMapping(value = "/api/cs/oc/oms/v1/dealdata/out/part", method = RequestMethod.POST)
    public String outPartError(HttpServletRequest request, @RequestBody JSONObject obj) {
        Long orderId = obj.getLong("orderId");
        return omsOrderOutService.outPartErrorFix(orderId);
    }

    /**
     * 中台周期购进销存报表-额外信息表数据初始化
     *
     * @param request
     * @param obj
     * @return
     */
    @RequestMapping(value = "/api/cs/oc/oms/v1/dealdata/cycle/extinfo/init", method = RequestMethod.POST)
    public String cycleExtInfoInit(HttpServletRequest request, @RequestBody JSONObject obj) {
        Integer batchNum = obj.getInteger("batchNum");
        publishDataDealService.cycleExtInfoInit(batchNum);
        return "done";
    }

    /**
     * 中台周期购进销存报表-额外信息表数据初始化-指定单号
     *
     * @param request
     * @param obj
     * @return
     */
    @RequestMapping(value = "/api/cs/oc/oms/v1/dealdata/cycle/extinfo/init/code", method = RequestMethod.POST)
    public String cycleExtInfoInitSingle(HttpServletRequest request, @RequestBody JSONObject obj) {
        String codes = obj.getString("codes");
        String[] split = codes.split(",");
        List<String> list = Lists.newArrayList(split);
        publishDataDealService.cycleExtInfoInitSingle(list);
        return "done";
    }

    /**
     * 指定订单对账/取消对账后触发重新计算提货金额等
     *
     * @param request
     * @param obj
     * @return
     */
    @RequestMapping(value = "/api/cs/oc/oms/v1/dealdata/cycle/confirm/cal/reload", method = RequestMethod.POST)
    public String cycleConfirmCalReload(HttpServletRequest request, @RequestBody JSONObject obj) {
        String tid = obj.getString("tid");
        String billNo = obj.getString("billNo");
        String amt = obj.getString("amt");
        boolean isConfirm = obj.getBoolean("isConfirm");

        List<CycleConfirmRequest> cycleConfirmList = Lists.newArrayList();
        CycleConfirmRequest cycleConfirmRequest = new CycleConfirmRequest();
        cycleConfirmRequest.setTid(tid);
        cycleConfirmRequest.setBillNo(billNo);
        cycleConfirmRequest.setAmt(new BigDecimal(amt));
        cycleConfirmRequest.setIsConfirm(isConfirm);
        cycleConfirmList.add(cycleConfirmRequest);

        cycleBuyInfoService.changeByConfirm(cycleConfirmList, false);

        return "done";
    }

    /**
     * 指定订单报表计算
     *
     * @param request
     * @param obj
     * @return
     */
    @RequestMapping(value = "/api/cs/oc/oms/v1/dealdata/cycle/report/cal/tid", method = RequestMethod.POST)
    public String cycleReportCalData(HttpServletRequest request, @RequestBody JSONObject obj) {
        String codes = obj.getString("codes");
        Date date = obj.getDate("date");
        String[] split = codes.split(",");
        List<String> list = Lists.newArrayList(split);
        cycleBuyInfoService.cycleReportCalData(list,date);
        return "done";
    }
}
