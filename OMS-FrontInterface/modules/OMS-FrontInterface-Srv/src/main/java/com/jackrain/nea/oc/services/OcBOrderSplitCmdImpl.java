package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBOrderSplitCmd;
import com.jackrain.nea.oc.oms.services.OmsOrderManualSplitService;
import com.jackrain.nea.oc.oms.spiltorder.OmsOrderManualSplitNewService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: heliu
 * @since: 2019/5/7
 * create at : 2019/5/7 19:20
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBOrderSplitCmdImpl implements OcBOrderSplitCmd {

    @Autowired
    private OmsOrderManualSplitService omsOrderManualSplitService;
    @Autowired
    private OmsOrderManualSplitNewService omsOrderManualSplitNewService;


    @Override
    public ValueHolderV14 querySkuListAndStorageInfo(JSONObject obj, User user) throws NDSException {
        return omsOrderManualSplitNewService.selectOmsOrderItem(obj, user);
    }

    @Override
    public ValueHolderV14 saveSplitOrderInfo(String param, User loginUser) throws NDSException {
        return omsOrderManualSplitService.saveSplitOrderInfo(param, loginUser);
    }

    @Override
    public ValueHolderV14 confirmSplitOrder(JSONObject obj, User user) throws NDSException {
        return omsOrderManualSplitNewService.confirmSplitOrder(obj, user, true, Lists.newArrayList());
    }

    @Override
    public ValueHolderV14 confirmSplitOrderByRow(List<Long> orderIds, User user, String splitReason) throws NDSException {
        return omsOrderManualSplitNewService.orderSplitByRow(orderIds, user, splitReason);
    }

    @Override
    public ValueHolderV14 confirmSplitOrderByOne(List<Long> orderIds, User user, String splitReason) throws NDSException {
        return omsOrderManualSplitNewService.splitOne(orderIds, user, splitReason);
    }

    @Override
    public ValueHolderV14 getNumByItemIdAndQty(JSONObject obj, User user) throws NDSException {
        return omsOrderManualSplitNewService.getNumByItemIdAndQty(obj, user);
    }

    @Override
    public ValueHolderV14 splitOrderByBoxStrategy(JSONObject obj, User user) throws NDSException {
        return omsOrderManualSplitNewService.splitOrderByBoxStrategy(obj, user);
    }
}