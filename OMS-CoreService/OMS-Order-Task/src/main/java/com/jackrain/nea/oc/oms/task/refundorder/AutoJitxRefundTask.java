package com.jackrain.nea.oc.oms.task.refundorder;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.es.ES4IpJitXOrder;
import com.jackrain.nea.oc.oms.model.jitx.JitxOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpJitxOrderRelation;
import com.jackrain.nea.oc.oms.process.transfer.impl.refund.jitx.JitxTransferRefundProcessImpl;
import com.jackrain.nea.oc.oms.services.IpJitxOrderService;
import com.jackrain.nea.oc.oms.services.IpJitxRefundService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description JITX退单补偿定时任务
 * @Date 2019-6-28
 **/
@Component
@Slf4j
public class AutoJitxRefundTask extends BaseR3Task implements IR3Task {

    @Autowired
    private IpJitxRefundService ipJitxRefundService;

    @Autowired
    private JitxTransferRefundProcessImpl jitxTransferRefundProcess;

    @Autowired
    private IpJitxOrderService ipJitxOrderService;

    @Override
    @XxlJob("AutoJitxRefundTask")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult result = new RunTaskResult();
        try {

            List<String> orderNoList = ES4IpJitXOrder
                    .findOrderSnByTransStatus(0, DEFAULT_PAGE_SIZE);

            if (log.isDebugEnabled()) {
                log.debug("###AutoJitxRefundTask###OrderNoList=" + JSONObject.toJSONString(orderNoList));
            }

            List<IpJitxOrderRelation> orderRelationList = new ArrayList<>();
            for (String orderNo : orderNoList) {
                IpJitxOrderRelation jitxOrderRelation = this.ipJitxOrderService.selectJitxOrder(orderNo);
                if (jitxOrderRelation == null) {
                    String errorMessage = Resources.getMessage("###AutoJitxRefundTask.Order.NotExist!###OrderNo="
                            + orderNo);
                    log.error(errorMessage);
                } else {
                    orderRelationList.add(jitxOrderRelation);
                }
            }

            threadOrderProcessor.startMultiThreadExecute(this.jitxTransferRefundProcess, orderRelationList);

            result.setSuccess(true);
        } catch (Exception ex) {
            log.error(LogUtil.format("AutoJitxRefundTask.Execute Error:,异常：{}", "AutoJitxRefundTask"), Throwables.getStackTraceAsString(ex));
            result.setSuccess(false);
            result.setMessage(ex.getMessage());
        }

        return result;

    }
}
