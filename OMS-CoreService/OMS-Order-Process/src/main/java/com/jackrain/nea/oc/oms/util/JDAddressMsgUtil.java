package com.jackrain.nea.oc.oms.util;

import com.alibaba.fastjson.JSONObject;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClients;

import java.io.IOException;

/**
 * <AUTHOR>
 * @Date 2024/3/6
 */
public class JDAddressMsgUtil {
    public static final String url = "https://oapi.dingtalk.com/robot/send?access_token=22e3d93f653061aba5ded4a73f2292d668f2fcad8551b192f3646ee462be3732";

    public static final String KEY_WORD = "订单地址修改失败";

    public static void sendDingTalk(String msg) {
        HttpClient httpClient = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(url);
        httpPost.addHeader("Content-Type", "application/json; charset=utf-8");
        JSONObject bodys = new JSONObject();
        bodys.put("msgtype", "text");
        JSONObject text = new JSONObject();
        text.put("content", msg);
        bodys.put("text", text);
        JSONObject at = new JSONObject();
        at.put("isAtAll", false);
        bodys.put("at", at);
        StringEntity se = new StringEntity(bodys.toJSONString(), "utf-8");
        httpPost.setEntity(se);
        try {
            httpClient.execute(httpPost);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

}
