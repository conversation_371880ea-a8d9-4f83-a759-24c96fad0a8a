package com.jackrain.nea.oc.oms.model.table.task;

import com.baomidou.mybatisplus.annotation.IdType;
import com.jackrain.nea.sys.domain.BaseModel;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.annotations.Document;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * <p>
 * 仓库拆单任务表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Document(index = "oc_b_warehouse_split_task", type = "oc_b_warehouse_split_task")
@ApiModel(value="oc_b_warehouse_split_task", description="仓库拆单任务表")
public class OcBWarehouseSplitTask extends BaseModel {


    @Field(type = FieldType.Long)
    @JSONField(name= "ID")
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @ApiModelProperty(value = "订单ID")
    @Field(type = FieldType.Long)
    @JSONField(name= "ORDER_ID")
    private Long orderId;

    @ApiModelProperty(value = "处理状态:0未处理，1已处理")
    @Field(type = FieldType.Integer)
    @JSONField(name= "STATUS")
    private Integer status;

    @ApiModelProperty(value = "版本号")
    @Field(type = FieldType.Long)
    @JSONField(name= "VERSION")
    private Long version;

    @ApiModelProperty(value = "创建人姓名")
    @Field(type = FieldType.Keyword)
    @JSONField(name= "OWNERENAME")
    private String ownerename;

    @ApiModelProperty(value = "修改人姓名")
    @Field(type = FieldType.Keyword)
    @JSONField(name= "MODIFIERENAME")
    private String modifierename;


}
