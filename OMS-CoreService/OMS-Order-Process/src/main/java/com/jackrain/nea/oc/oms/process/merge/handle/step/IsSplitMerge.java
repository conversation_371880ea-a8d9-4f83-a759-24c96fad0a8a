package com.jackrain.nea.oc.oms.process.merge.handle.step;

import com.jackrain.nea.oc.oms.model.MergeOrderInfo;
import com.jackrain.nea.oc.oms.process.merge.handle.MergeEnum;
import com.jackrain.nea.oc.oms.process.merge.handle.MergeStrategyHandler;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> ruan.gz
 * @Description : 是否可以拆单策略检查
 * @Date : 2020/6/20
 **/
@Service("IsSplitMerge")
class IsSplitMerge implements MergeStrategyHandler {


    @Override
    public Boolean doSingleHandle(MergeOrderInfo info) {
        return false;
    }

    @Override
    public Integer getSort(String name) {
        return MergeEnum.getValueFromValueTag(name);
    }

    @Override
    public Boolean doWholeHandle(MergeOrderInfo info) {
        //TODO 调用拆单策略
        return false;
    }

}




