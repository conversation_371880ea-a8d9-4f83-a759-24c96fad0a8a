package com.jackrain.nea.oc.oms.task.logisticsIntercept;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.services.ZtoLogisticsInterceptService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2023/3/7 13:22
 * @Description 物流拦截结果主动查询定时任务
 */
@Slf4j
@Component
public class LogisticsInterceptResultQueryTask implements IR3Task {

    @Resource
    private ZtoLogisticsInterceptService ztoLogisticsInterceptService;

    @Override
    @XxlJob("LogisticsInterceptResultQueryTask")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult result = new RunTaskResult();
        try {
            ztoLogisticsInterceptService.updateYunDaInterceptResult();
            result.setSuccess(true);
        } catch (Exception e) {
            log.error(LogUtil.format("YunDaLogisticsInterceptResultQueryTask.Execute.Error: {}"), Throwables.getStackTraceAsString(e));
            result.setSuccess(false);
            result.setMessage(e.getMessage());
        }
        return result;
    }
}
