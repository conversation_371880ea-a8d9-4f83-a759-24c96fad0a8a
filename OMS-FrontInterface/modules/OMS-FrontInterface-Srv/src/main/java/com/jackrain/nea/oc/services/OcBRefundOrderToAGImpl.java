package com.jackrain.nea.oc.services;

import com.jackrain.nea.oc.oms.api.OcBRefundOrderToAGCmd;
import com.jackrain.nea.oc.oms.services.OcBRefundOrderToAGService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> 孙俊磊
 * @since :  2019-04-02
 * create at:  2019-04-02 10:05
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBRefundOrderToAGImpl implements OcBRefundOrderToAGCmd {

    @Autowired
    private OcBRefundOrderToAGService ocBRefundOrderToAGService;

    @Override
    public ValueHolderV14 refundOrderToAg(String param, User loginUser) {
        return ocBRefundOrderToAGService.refundOrderToAg(param, loginUser);
    }
}
