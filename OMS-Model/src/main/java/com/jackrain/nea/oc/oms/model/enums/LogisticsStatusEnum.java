package com.jackrain.nea.oc.oms.model.enums;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: 黄世新
 * @Date: 2020/3/9 2:13 下午
 * @Version 1.0
 * 物流状态枚举类
 */
public enum LogisticsStatusEnum {

    ACCEPT("ACCEPT", "仓库接单"),
    PARTFULFILLED("PARTFULFILLED", "部分收货完成"),
    FULFILLED("FULFILLED", "收货完成"),
    PRINT("PRINT", "打印"),
    PICK("PICK", "捡货"),
    CHECK("CHECK", "复核"),
    PACKAGE("PACKAGE", "打包"),
    WEIGH("WEIGH", "称重"),
    READY("READY", "待提货"),
    DELIVERED("DELIVERED", "已发货"),
    REFUSE("REFUSE", "买家拒签"),
    EXCEPTION("EXCEPTION", "异常"),
    CLOSED("CLOSED", "关闭"),
    CANCELED("CANCELED", "取 消"),
    REJECT("REJECT", "仓库拒单"),
    SIGN("SIGN", "签收"),
    TMSCANCELED("TMSCANCELED", "快递拦截"),
    OTHER("OTHER", "其他"),
    PARTDELIVERED("PARTDELIVERED", "部分发货完成"),
    TMSCANCELFAILED("TMSCANCELFAILED", "快递拦截失败"),
    OPEN("OPEN", "打开"),
    AVAILABLE("AVAILABLE", "打开"),
    CANCEL("CANCEL", "取消"),
    DISPATCH("DISPATCH", "已调度"),
    WAIT_SHIP("WAIT_SHIP", "待发运"),
    ONROAD("ONROAD", "在途"),
    ARRIVED("ARRIVED", "签收"),
    SIGNED("SIGNED", "已回单");


    @Getter
    private String code;

    @Getter
    private String message;

    LogisticsStatusEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public static Map<String, String> getStatusMap() {
        Map<String, String> map = new HashMap<>();
        for (LogisticsStatusEnum value : LogisticsStatusEnum.values()) {
            map.put(value.getCode(), value.getMessage());
        }
        map.put(null, "");
        return map;
    }


}
