package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.jdbc.datasource.TargetDataSource;
import com.jackrain.nea.oc.oms.mapper.IpBThirdPartyInterfaceLogMapper;
import com.jackrain.nea.oc.oms.model.table.IpBThirdPartyInterfaceLog;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName ThirdPartyInterfaceLogQueryService
 * @Description 第三方接口日志查询服务
 * <AUTHOR>
 * @Date 2025/4/22 20:20
 * @Version 1.0
 */
@Slf4j
@Service
public class ThirdPartyInterfaceLogQueryService {

    @Autowired
    private IpBThirdPartyInterfaceLogMapper ipBThirdPartyInterfaceLogMapper;

    /**
     * 框架格式返回
     *
     * @param dataList 原始数据列表
     * @return 格式化后的数据数组
     */
    private JSONArray getFrameDataFormat(List<JSONObject> dataList) {
        JSONArray array = new JSONArray();
        if (dataList != null && dataList.size() > 0) {
            for (JSONObject emp : dataList) {
                JSONObject json = new JSONObject();

                // 遍历所有字段
                for (String key : emp.keySet()) {
                    JSONObject val = new JSONObject();
                    Object value = emp.get(key);

                    // 处理时间字段
                    if ("REQUEST_TIME".equalsIgnoreCase(key) || "RESPONSE_TIME".equalsIgnoreCase(key)) {
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        if (value instanceof Date) {
                            val.put("val", sdf.format((Date) value));
                        } else if (value instanceof Long) {
                            // 处理时间戳
                            val.put("val", sdf.format(new Date((Long) value)));
                        } else if (value instanceof String && !((String) value).isEmpty()) {
                            try {
                                // 尝试将字符串解析为时间戳
                                long timestamp = Long.parseLong((String) value);
                                val.put("val", sdf.format(new Date(timestamp)));
                            } catch (NumberFormatException e) {
                                // 如果不是数字格式，直接返回原值
                                val.put("val", value);
                            }
                        } else {
                            val.put("val", value);
                        }
                    } else if (value instanceof Date) {
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        val.put("val", sdf.format((Date) value));
                    }
                    // 处理单据类型字段
                    else if ("RESERVE_BIGINT01".equalsIgnoreCase(key) && value != null) {
                        val.put("val", getBillTypeDesc(value.toString()));
                    }
                    // 处理接口类型字段
                    else if ("TYPE".equalsIgnoreCase(key) && value != null) {
                        val.put("val", getInterfaceTypeDesc(value.toString()));
                    }
                    // 处理调用结果字段
                    else if ("CODE".equalsIgnoreCase(key) && value != null) {
                        if ("0".equals(value.toString())) {
                            val.put("val", "成功");
                        } else {
                            val.put("val", "失败");
                        }
                    }
                    // 处理可用字段
                    else if ("ISACTIVE".equalsIgnoreCase(key) && value != null) {
                        if ("Y".equals(value.toString())) {
                            val.put("val", "是");
                        } else {
                            val.put("val", "否");
                        }
                    }
                    // 其他字段直接转换
                    else {
                        val.put("val", value);
                    }

                    json.put(key.toUpperCase(), val);
                }
                array.add(json);
            }
        }
        return array;
    }

    /**
     * 根据单据类型代码获取单据类型描述
     *
     * @param billTypeCode 单据类型代码
     * @return 单据类型描述
     */
    private String getBillTypeDesc(String billTypeCode) {
        switch (billTypeCode) {
            case "101":
                return "零售发货单";
            case "201":
                return "退换货单";
            case "301":
                return "杂费单";
            case "401":
                return "出库通知单";
            case "601":
                return "入库通知单";
            case "701":
                return "主数据";
            case "801":
                return "商品";
            case "901":
                return "仓内加工";
            case "501":
                return "唯品会退供PO单";
            case "150":
                return "其他入库单";
            case "160":
                return "其他出库单";
            case "6":
                return "库存调整单";
            case "15":
                return "逻辑调拨单";
            case "16":
                return "逻辑入库单";
            case "17":
                return "逻辑出库单";
            case "18":
                return "通用退单";
            case "19":
                return "通用订单";
            case "20":
                return "销售数据汇总";
            case "21":
                return "对账结算单";
            case "22":
                return "月结汇总单";
            case "241":
                return "冻结出库单";
            default:
                return billTypeCode;
        }
    }

    /**
     * 根据接口类型代码获取接口类型描述
     *
     * @param typeCode 接口类型代码
     * @return 接口类型描述
     */
    private String getInterfaceTypeDesc(String typeCode) {
        switch (typeCode) {
            case "1":
                return "WEBSERVICE";
            case "2":
                return "RESTFUL";
            case "3":
                return "RPC";
            case "4":
                return "MQ";
            default:
                return typeCode;
        }
    }

    /**
     * 查询第三方接口日志
     *
     * @param session 查询会话
     * @return 查询结果
     */
    public ValueHolder queryThirdPartyInterfaceLog(QuerySession session) {
        ValueHolder vh = new ValueHolder();
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                        event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);
        log.info("查询第三方接口日志参数: {}", param.toJSONString());
        // 获取分页参数
        Integer startIndex = param.getInteger("startindex");
        Integer range = param.getInteger("range");
        try {

            if (startIndex == null) {
                startIndex = 0;
            }
            if (range == null) {
                range = 100;
            }

            // 构建查询条件
            LambdaQueryWrapper<IpBThirdPartyInterfaceLog> queryWrapper = new LambdaQueryWrapper<>();

            // 处理固定查询条件
            if (param.containsKey("fixedcolumns")) {
                JSONObject fixedColumns = param.getJSONObject("fixedcolumns");

                // 检查是否有业务单号
                boolean hasBillNo = false;
                String billNo = null;
                if (fixedColumns.containsKey("BILL_NO")) {
                    billNo = fixedColumns.getString("BILL_NO");
                    if (billNo != null && !billNo.isEmpty()) {
                        hasBillNo = true;
                        queryWrapper.eq(IpBThirdPartyInterfaceLog::getBillNo, billNo);
                    }
                }

                // 处理第三方平台名称查询
                if (fixedColumns.containsKey("THIRD_PARTY_NAME")) {
                    String thirdPartyName = fixedColumns.getString("THIRD_PARTY_NAME");
                    if (thirdPartyName != null && !thirdPartyName.isEmpty()) {
                        queryWrapper.eq(IpBThirdPartyInterfaceLog::getThirdPartyName, thirdPartyName);
                    }
                }

                // 处理单据类型查询
                if (fixedColumns.containsKey("RESERVE_BIGINT01")) {
                    Object billTypeObj = fixedColumns.get("RESERVE_BIGINT01");
                    if (billTypeObj instanceof JSONArray) {
                        JSONArray billTypeArray = fixedColumns.getJSONArray("RESERVE_BIGINT01");
                        if (billTypeArray != null && !billTypeArray.isEmpty()) {
                            // 如果有多个值，使用OR条件连接
                            List<String> eqBillTypes = new ArrayList<>();
                            List<String> neBillTypes = new ArrayList<>();

                            for (int i = 0; i < billTypeArray.size(); i++) {
                                String billTypeStr = billTypeArray.getString(i);
                                if (billTypeStr.startsWith("=")) {
                                    eqBillTypes.add(billTypeStr.substring(1));
                                } else if (billTypeStr.startsWith("!=")) {
                                    neBillTypes.add(billTypeStr.substring(2));
                                }
                            }

                            // 处理等于条件
                            if (!eqBillTypes.isEmpty()) {
                                queryWrapper.and(wrapper -> {
                                    for (int i = 0; i < eqBillTypes.size(); i++) {
                                        if (i == 0) {
                                            wrapper.eq(IpBThirdPartyInterfaceLog::getReserveBigint01, eqBillTypes.get(i));
                                        } else {
                                            wrapper.or().eq(IpBThirdPartyInterfaceLog::getReserveBigint01, eqBillTypes.get(i));
                                        }
                                    }
                                    return wrapper;
                                });
                            }

                            // 处理不等于条件
                            for (String neBillType : neBillTypes) {
                                queryWrapper.ne(IpBThirdPartyInterfaceLog::getReserveBigint01, neBillType);
                            }
                        }
                    } else if (billTypeObj instanceof String) {
                        String billTypeStr = fixedColumns.getString("RESERVE_BIGINT01");
                        if (billTypeStr.startsWith("=")) {
                            queryWrapper.eq(IpBThirdPartyInterfaceLog::getReserveBigint01, billTypeStr.substring(1));
                        } else if (billTypeStr.startsWith("!=")) {
                            queryWrapper.ne(IpBThirdPartyInterfaceLog::getReserveBigint01, billTypeStr.substring(2));
                        }
                    }
                }

                // 处理接口名查询
                if (fixedColumns.containsKey("METHOD")) {
                    String method = fixedColumns.getString("METHOD");
                    if (method != null && !method.isEmpty()) {
                        queryWrapper.eq(IpBThirdPartyInterfaceLog::getMethod, method);
                    }
                }

                // 处理CODE查询
                if (fixedColumns.containsKey("CODE")) {
                    Object codeObj = fixedColumns.get("CODE");
                    if (codeObj instanceof JSONArray) {
                        JSONArray codeArray = fixedColumns.getJSONArray("CODE");
                        if (codeArray != null && !codeArray.isEmpty()) {
                            // 如果有多个值，使用OR条件连接
                            List<Integer> eqCodes = new ArrayList<>();
                            List<Integer> neCodes = new ArrayList<>();

                            for (int i = 0; i < codeArray.size(); i++) {
                                String codeStr = codeArray.getString(i);
                                if (codeStr.startsWith("=")) {
                                    eqCodes.add(Integer.parseInt(codeStr.substring(1)));
                                } else if (codeStr.startsWith("!=")) {
                                    neCodes.add(Integer.parseInt(codeStr.substring(2)));
                                }
                            }

                            // 处理等于条件
                            if (!eqCodes.isEmpty()) {
                                queryWrapper.and(wrapper -> {
                                    for (int i = 0; i < eqCodes.size(); i++) {
                                        if (i == 0) {
                                            wrapper.eq(IpBThirdPartyInterfaceLog::getCode, eqCodes.get(i));
                                        } else {
                                            wrapper.or().eq(IpBThirdPartyInterfaceLog::getCode, eqCodes.get(i));
                                        }
                                    }
                                    return wrapper;
                                });
                            }

                            // 处理不等于条件
                            for (Integer neCode : neCodes) {
                                queryWrapper.ne(IpBThirdPartyInterfaceLog::getCode, neCode);
                            }
                        }
                    } else if (codeObj instanceof String) {
                        String codeStr = fixedColumns.getString("CODE");
                        if (codeStr.startsWith("=")) {
                            queryWrapper.eq(IpBThirdPartyInterfaceLog::getCode, Integer.parseInt(codeStr.substring(1)));
                        } else if (codeStr.startsWith("!=")) {
                            queryWrapper.ne(IpBThirdPartyInterfaceLog::getCode, Integer.parseInt(codeStr.substring(2)));
                        }
                    }
                }

                // 处理接口类型查询
                if (fixedColumns.containsKey("TYPE")) {
                    Object typeObj = fixedColumns.get("TYPE");
                    if (typeObj instanceof JSONArray) {
                        JSONArray typeArray = fixedColumns.getJSONArray("TYPE");
                        if (typeArray != null && !typeArray.isEmpty()) {
                            // 如果有多个值，使用OR条件连接
                            List<String> eqTypes = new ArrayList<>();
                            List<String> neTypes = new ArrayList<>();

                            for (int i = 0; i < typeArray.size(); i++) {
                                String typeStr = typeArray.getString(i);
                                if (typeStr.startsWith("=")) {
                                    eqTypes.add(typeStr.substring(1));
                                } else if (typeStr.startsWith("!=")) {
                                    neTypes.add(typeStr.substring(2));
                                }
                            }

                            // 处理等于条件
                            if (!eqTypes.isEmpty()) {
                                queryWrapper.and(wrapper -> {
                                    for (int i = 0; i < eqTypes.size(); i++) {
                                        if (i == 0) {
                                            wrapper.eq(IpBThirdPartyInterfaceLog::getType, eqTypes.get(i));
                                        } else {
                                            wrapper.or().eq(IpBThirdPartyInterfaceLog::getType, eqTypes.get(i));
                                        }
                                    }
                                    return wrapper;
                                });
                            }

                            // 处理不等于条件
                            for (String neType : neTypes) {
                                queryWrapper.ne(IpBThirdPartyInterfaceLog::getType, neType);
                            }
                        }
                    } else if (typeObj instanceof String) {
                        String typeStr = fixedColumns.getString("TYPE");
                        if (typeStr.startsWith("=")) {
                            queryWrapper.eq(IpBThirdPartyInterfaceLog::getType, typeStr.substring(1));
                        } else if (typeStr.startsWith("!=")) {
                            queryWrapper.ne(IpBThirdPartyInterfaceLog::getType, typeStr.substring(2));
                        }
                    }
                }

                // 处理请求时间范围查询
                if (fixedColumns.containsKey("REQUEST_TIME")) {
                    String requestTimeRange = fixedColumns.getString("REQUEST_TIME");
                    if (requestTimeRange != null && !requestTimeRange.isEmpty() && requestTimeRange.contains("~")) {
                        String[] timeRange = requestTimeRange.split("~");
                        if (timeRange.length == 2) {
                            SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
                            try {
                                Date startTime = sdf.parse(timeRange[0].trim());
                                Date endTime = sdf.parse(timeRange[1].trim());

                                // 如果没有业务单号，需要检查时间范围不能超过一天
                                if (!hasBillNo) {
                                    // 计算时间差值（毫秒）
                                    long timeDiff = endTime.getTime() - startTime.getTime();
                                    // 24小时 = 24 * 60 * 60 * 1000 = 86400000毫秒
                                    if (timeDiff > 86400000 * 3) {
                                        JSONObject resultData = new JSONObject();
                                        resultData.put("start", startIndex);
                                        resultData.put("rowCount", range);
                                        resultData.put("row", new JSONArray());
                                        resultData.put("totalRowCount", 0);
                                        vh.put("code", ResultCode.FAIL);
                                        vh.put("message", "查询时间范围不能超过三天，除非指定业务单号");
                                        vh.put("data", resultData);
                                        return vh;
                                    }
                                }

                                queryWrapper.ge(IpBThirdPartyInterfaceLog::getRequestTime, startTime);
                                queryWrapper.le(IpBThirdPartyInterfaceLog::getRequestTime, endTime);
                            } catch (Exception e) {
                                log.error("解析时间范围失败: {}", requestTimeRange, e);
                            }
                        }
                    }
                } else if (!hasBillNo) {
                    // 如果没有业务单号且没有请求时间，返回错误
                    JSONObject resultData = new JSONObject();
                    resultData.put("start", startIndex);
                    resultData.put("rowCount", range);
                    resultData.put("row", new JSONArray());
                    resultData.put("totalRowCount", 0);
                    vh.put("code", ResultCode.FAIL);
                    vh.put("message", "请求时间必填，除非指定业务单号");
                    vh.put("data", resultData);
                    return vh;
                }
            }

            // queryWrapper需要设置根据id 倒序
            queryWrapper.orderByDesc(IpBThirdPartyInterfaceLog::getId);

            // 执行查询
            // 注释掉原来的ADB查询逻辑
            /*
            // 先从 ADB 查询总数和 ID 列表
            List<Long> ids = queryAdbForIds(queryWrapper, startIndex, range);
            // ids 可能为空!
            if (CollectionUtils.isEmpty(ids)) {
                JSONObject resultData = new JSONObject();
                resultData.put("start", startIndex);
                resultData.put("rowCount", range);
                resultData.put("row", new JSONArray());
                resultData.put("totalRowCount", 0);
                // 打印出来参数
                vh.put("code", ResultCode.SUCCESS);
                vh.put("message", "success");
                vh.put("data",resultData);
                return vh;
            }

            // 查询总数
            int totalCount = queryAdbForCount(queryWrapper);

            List<IpBThirdPartyInterfaceLog> logList = ipBThirdPartyInterfaceLogMapper.selectByIds(ids);
            */

            // 直接从数据库查询数据和总数
            // 查询总数
            int totalCount = ipBThirdPartyInterfaceLogMapper.selectCount(queryWrapper);

            // 如果没有数据，直接返回空结果
            if (totalCount == 0) {
                JSONObject resultData = new JSONObject();
                resultData.put("start", startIndex);
                resultData.put("rowCount", range);
                resultData.put("row", new JSONArray());
                resultData.put("totalRowCount", 0);
                // 打印出来参数
                vh.put("code", ResultCode.SUCCESS);
                vh.put("message", "success");
                vh.put("data",resultData);
                return vh;
            }

            // 创建分页对象
            Page<IpBThirdPartyInterfaceLog> page = new Page<>(startIndex / range + 1, range);

            // 执行分页查询
            IPage<IpBThirdPartyInterfaceLog> resultPage = ipBThirdPartyInterfaceLogMapper.selectPage(page, queryWrapper);

            // 获取查询结果
            List<IpBThirdPartyInterfaceLog> logList = resultPage.getRecords();
            JSONArray jsonArray = (JSONArray) JSONArray.toJSON(logList);
            List<JSONObject> jsonObjectList = JSONObject.parseArray(
                    JSONObject.toJSONString(jsonArray, SerializerFeature.WriteMapNullValue), JSONObject.class);
            JSONObject resultData = new JSONObject();
            resultData.put("start", startIndex);
            resultData.put("rowCount", range);
            resultData.put("row", getFrameDataFormat(jsonObjectList));
            resultData.put("totalRowCount", totalCount);
            // 打印出来参数
            vh.put("code", ResultCode.SUCCESS);
            vh.put("message", "success");
            vh.put("data", resultData);
            return vh;

        } catch (Exception e) {
            JSONObject resultData = new JSONObject();
            resultData.put("start", startIndex);
            resultData.put("rowCount", range);
            resultData.put("row", new JSONArray());
            resultData.put("totalRowCount", 0);

            vh.put("datas", resultData);
            vh.put("code", -1);
        }

        return vh;
    }

    /**
     * 从 ADB 查询总数
     *
     * @param queryWrapper 查询条件
     * @return 总数
     */
    @TargetDataSource(name = "adb")
    private int queryAdbForCount(LambdaQueryWrapper<IpBThirdPartyInterfaceLog> queryWrapper) {
        return ipBThirdPartyInterfaceLogMapper.selectCount(queryWrapper);
    }

    /**
     * 从 ADB 查询ID列表
     *
     * @param queryWrapper 查询条件
     * @param startIndex   起始索引
     * @param range        范围
     * @return ID列表
     */
    @TargetDataSource(name = "adb")
    private List<Long> queryAdbForIds(LambdaQueryWrapper<IpBThirdPartyInterfaceLog> queryWrapper, int startIndex, int range) {
        // 只查询ID字段
        queryWrapper.select(IpBThirdPartyInterfaceLog::getId);

        // 创建分页对象
        Page<IpBThirdPartyInterfaceLog> page = new Page<>(startIndex / range + 1, range);

        // 执行查询
        IPage<IpBThirdPartyInterfaceLog> resultPage = ipBThirdPartyInterfaceLogMapper.selectPage(page, queryWrapper);

        // 提取ID列表
        if (resultPage != null && CollectionUtils.isNotEmpty(resultPage.getRecords())) {
            return resultPage.getRecords().stream()
                    .map(IpBThirdPartyInterfaceLog::getId)
                    .collect(Collectors.toList());
        }

        return new ArrayList<>();
    }

    /**
     * 构建表头信息
     *
     * @return 表头信息JSON数组
     */
    private JSONArray buildTableHeader() {
        JSONArray tabthArray = new JSONArray();

        // ID列
        JSONObject idCol = new JSONObject();
        idCol.put("issubtotal", false);
        idCol.put("agfilter", "LIST");
        idCol.put("orderno", 1);
        idCol.put("isak", true);
        idCol.put("display", "text");
        idCol.put("length", 20);
        idCol.put("scale", 0);
        idCol.put("type", "NUMBER");
        idCol.put("datelimit", "all");
        idCol.put("isagfilter", false);
        idCol.put("isfilter", false);
        idCol.put("colname", "ID");
        idCol.put("isuppercase", false);
        idCol.put("isnotnull", false);
        idCol.put("ismodify", false);
        idCol.put("name", "ID");
        idCol.put("inputname", "ID");
        tabthArray.add(idCol);

        // 第三方平台名称列
        JSONObject thirdPartyNameCol = new JSONObject();
        thirdPartyNameCol.put("issubtotal", false);
        thirdPartyNameCol.put("agfilter", "LIST");
        thirdPartyNameCol.put("orderno", 10);
        thirdPartyNameCol.put("isak", false);
        thirdPartyNameCol.put("display", "text");
        thirdPartyNameCol.put("length", 20);
        thirdPartyNameCol.put("type", "STRING");
        thirdPartyNameCol.put("datelimit", "all");
        thirdPartyNameCol.put("isagfilter", false);
        thirdPartyNameCol.put("isfilter", true);
        thirdPartyNameCol.put("colname", "THIRD_PARTY_NAME");
        thirdPartyNameCol.put("isuppercase", false);
        thirdPartyNameCol.put("isnotnull", false);
        thirdPartyNameCol.put("ismodify", false);
        thirdPartyNameCol.put("name", "第三方平台名称");
        thirdPartyNameCol.put("inputname", "THIRD_PARTY_NAME");
        tabthArray.add(thirdPartyNameCol);

        // 业务单号列
        JSONObject billNoCol = new JSONObject();
        billNoCol.put("issubtotal", false);
        billNoCol.put("agfilter", "LIST");
        billNoCol.put("orderno", 20);
        billNoCol.put("isak", false);
        billNoCol.put("display", "text");
        billNoCol.put("length", 200);
        billNoCol.put("type", "STRING");
        billNoCol.put("datelimit", "all");
        billNoCol.put("isagfilter", false);
        billNoCol.put("isfilter", true);
        billNoCol.put("colname", "BILL_NO");
        billNoCol.put("isuppercase", false);
        billNoCol.put("isnotnull", false);
        billNoCol.put("ismodify", false);
        billNoCol.put("name", "业务单号");
        billNoCol.put("inputname", "BILL_NO");
        tabthArray.add(billNoCol);

        // 单据类型列
        JSONObject billTypeCol = new JSONObject();
        billTypeCol.put("issubtotal", false);
        billTypeCol.put("agfilter", "LIST");
        billTypeCol.put("orderno", 25);
        billTypeCol.put("isak", false);
        billTypeCol.put("display", "select");
        billTypeCol.put("length", 20);
        billTypeCol.put("type", "STRING");
        billTypeCol.put("datelimit", "all");
        billTypeCol.put("isagfilter", false);
        billTypeCol.put("isfilter", true);
        billTypeCol.put("colname", "RESERVE_BIGINT01");
        billTypeCol.put("isuppercase", false);
        billTypeCol.put("isnotnull", false);
        billTypeCol.put("ismodify", false);
        billTypeCol.put("name", "单据类型");

        // 单据类型下拉选项
        JSONArray billTypeCombobox = new JSONArray();
        addComboboxItem(billTypeCombobox, "零售发货单", "101");
        addComboboxItem(billTypeCombobox, "退换货单", "201");
        addComboboxItem(billTypeCombobox, "杂费单", "301");
        addComboboxItem(billTypeCombobox, "出库通知单", "401");
        addComboboxItem(billTypeCombobox, "入库通知单", "601");
        addComboboxItem(billTypeCombobox, "主数据", "701");
        addComboboxItem(billTypeCombobox, "商品", "801");
        addComboboxItem(billTypeCombobox, "仓内加工", "901");
        addComboboxItem(billTypeCombobox, "唯品会退供PO单", "501");
        addComboboxItem(billTypeCombobox, "其他入库单", "150");
        addComboboxItem(billTypeCombobox, "其他出库单", "160");
        addComboboxItem(billTypeCombobox, "库存调整单", "6");
        addComboboxItem(billTypeCombobox, "逻辑调拨单", "15");
        addComboboxItem(billTypeCombobox, "逻辑入库单", "16");
        addComboboxItem(billTypeCombobox, "逻辑出库单", "17");
        addComboboxItem(billTypeCombobox, "通用退单", "18");
        addComboboxItem(billTypeCombobox, "通用订单", "19");
        addComboboxItem(billTypeCombobox, "销售数据汇总", "20");
        addComboboxItem(billTypeCombobox, "对账结算单", "21");
        addComboboxItem(billTypeCombobox, "月结汇总单", "22");
        addComboboxItem(billTypeCombobox, "冻结出库单", "241");

        billTypeCol.put("combobox", billTypeCombobox);
        billTypeCol.put("inputname", "RESERVE_BIGINT01");
        tabthArray.add(billTypeCol);

        // 接口名列
        JSONObject methodCol = new JSONObject();
        methodCol.put("issubtotal", false);
        methodCol.put("agfilter", "LIST");
        methodCol.put("orderno", 30);
        methodCol.put("isak", false);
        methodCol.put("display", "text");
        methodCol.put("length", 200);
        methodCol.put("type", "STRING");
        methodCol.put("datelimit", "all");
        methodCol.put("isagfilter", false);
        methodCol.put("isfilter", true);
        methodCol.put("colname", "METHOD");
        methodCol.put("isuppercase", false);
        methodCol.put("isnotnull", false);
        methodCol.put("ismodify", false);
        methodCol.put("name", "接口名");
        methodCol.put("inputname", "METHOD");
        tabthArray.add(methodCol);

        // 提供方列
        JSONObject providerCol = new JSONObject();
        providerCol.put("issubtotal", false);
        providerCol.put("agfilter", "LIST");
        providerCol.put("orderno", 40);
        providerCol.put("isak", false);
        providerCol.put("display", "text");
        providerCol.put("length", 20);
        providerCol.put("type", "STRING");
        providerCol.put("datelimit", "all");
        providerCol.put("isagfilter", false);
        providerCol.put("isfilter", false);
        providerCol.put("colname", "PROVIDER");
        providerCol.put("isuppercase", false);
        providerCol.put("isnotnull", false);
        providerCol.put("ismodify", false);
        providerCol.put("name", "提供方");
        providerCol.put("inputname", "PROVIDER");
        tabthArray.add(providerCol);

        // 调用方列
        JSONObject consumerCol = new JSONObject();
        consumerCol.put("issubtotal", false);
        consumerCol.put("agfilter", "LIST");
        consumerCol.put("orderno", 50);
        consumerCol.put("isak", false);
        consumerCol.put("display", "text");
        consumerCol.put("length", 20);
        consumerCol.put("type", "STRING");
        consumerCol.put("datelimit", "all");
        consumerCol.put("isagfilter", false);
        consumerCol.put("isfilter", false);
        consumerCol.put("colname", "CONSUMER");
        consumerCol.put("isuppercase", false);
        consumerCol.put("isnotnull", false);
        consumerCol.put("ismodify", false);
        consumerCol.put("name", "调用方");
        consumerCol.put("inputname", "CONSUMER");
        tabthArray.add(consumerCol);

        // 接口类型列
        JSONObject typeCol = new JSONObject();
        typeCol.put("issubtotal", false);
        typeCol.put("agfilter", "LIST");
        typeCol.put("orderno", 60);
        typeCol.put("isak", false);
        typeCol.put("display", "select");
        typeCol.put("length", 1);
        typeCol.put("type", "STRING");
        typeCol.put("datelimit", "all");
        typeCol.put("isagfilter", false);
        typeCol.put("isfilter", true);
        typeCol.put("colname", "TYPE");
        typeCol.put("isuppercase", false);
        typeCol.put("isnotnull", false);
        typeCol.put("ismodify", false);
        typeCol.put("name", "接口类型");

        // 接口类型下拉选项
        JSONArray typeCombobox = new JSONArray();
        addComboboxItem(typeCombobox, "WEBSERVICE", "1");
        addComboboxItem(typeCombobox, "RESTFUL", "2");
        addComboboxItem(typeCombobox, "RPC", "3");
        addComboboxItem(typeCombobox, "MQ", "4");

        typeCol.put("combobox", typeCombobox);
        typeCol.put("inputname", "TYPE");
        tabthArray.add(typeCol);

        // 调用结果列
        JSONObject codeCol = new JSONObject();
        codeCol.put("issubtotal", false);
        codeCol.put("agfilter", "LIST");
        codeCol.put("orderno", 70);
        codeCol.put("isak", false);
        codeCol.put("display", "select");
        codeCol.put("length", 1);
        codeCol.put("type", "STRING");
        codeCol.put("datelimit", "all");
        codeCol.put("isagfilter", false);
        codeCol.put("isfilter", true);
        codeCol.put("colname", "CODE");
        codeCol.put("isuppercase", false);
        codeCol.put("isnotnull", false);
        codeCol.put("ismodify", false);
        codeCol.put("name", "调用结果");

        // 调用结果下拉选项
        JSONArray codeCombobox = new JSONArray();
        addComboboxItem(codeCombobox, "成功", "0");
        addComboboxItem(codeCombobox, "失败", "-1");

        codeCol.put("combobox", codeCombobox);
        codeCol.put("inputname", "CODE");
        tabthArray.add(codeCol);

        // 请求时间列
        JSONObject requestTimeCol = new JSONObject();
        requestTimeCol.put("issubtotal", false);
        requestTimeCol.put("agfilter", "LIST");
        requestTimeCol.put("orderno", 80);
        requestTimeCol.put("isak", false);
        requestTimeCol.put("display", "OBJ_DATE");
        requestTimeCol.put("length", 19);
        requestTimeCol.put("type", "STRING");
        requestTimeCol.put("datelimit", "all");
        requestTimeCol.put("isagfilter", false);
        requestTimeCol.put("isfilter", true);
        requestTimeCol.put("colname", "REQUEST_TIME");
        requestTimeCol.put("isuppercase", false);
        requestTimeCol.put("isnotnull", false);
        requestTimeCol.put("ismodify", false);
        requestTimeCol.put("name", "请求时间");
        requestTimeCol.put("inputname", "REQUEST_TIME");
        tabthArray.add(requestTimeCol);

        // 响应时间列
        JSONObject responseTimeCol = new JSONObject();
        responseTimeCol.put("issubtotal", false);
        responseTimeCol.put("agfilter", "LIST");
        responseTimeCol.put("orderno", 90);
        responseTimeCol.put("isak", false);
        responseTimeCol.put("display", "OBJ_DATE");
        responseTimeCol.put("length", 19);
        responseTimeCol.put("type", "STRING");
        responseTimeCol.put("datelimit", "all");
        responseTimeCol.put("isagfilter", false);
        responseTimeCol.put("isfilter", false);
        responseTimeCol.put("colname", "RESPONSE_TIME");
        responseTimeCol.put("isuppercase", false);
        responseTimeCol.put("isnotnull", false);
        responseTimeCol.put("ismodify", false);
        responseTimeCol.put("name", "响应时间");
        responseTimeCol.put("inputname", "RESPONSE_TIME");
        tabthArray.add(responseTimeCol);

        // 可用列
        JSONObject isActiveCol = new JSONObject();
        isActiveCol.put("issubtotal", false);
        isActiveCol.put("agfilter", "LIST");
        isActiveCol.put("orderno", 10000);
        isActiveCol.put("isak", false);
        isActiveCol.put("display", "check");
        isActiveCol.put("length", 1);
        isActiveCol.put("type", "STRING");
        isActiveCol.put("datelimit", "all");
        isActiveCol.put("isagfilter", false);
        isActiveCol.put("isfilter", false);
        isActiveCol.put("colname", "ISACTIVE");
        isActiveCol.put("isuppercase", false);
        isActiveCol.put("isnotnull", false);
        isActiveCol.put("ismodify", true);
        isActiveCol.put("name", "可用");

        // 可用下拉选项
        JSONArray isActiveCombobox = new JSONArray();
        JSONObject yesOption = new JSONObject();
        yesOption.put("limitdesc", "是");
        yesOption.put("limitcss", null);
        yesOption.put("limitdis", true);
        yesOption.put("limitval", "Y");
        isActiveCombobox.add(yesOption);

        JSONObject noOption = new JSONObject();
        noOption.put("limitdesc", "否");
        noOption.put("limitcss", "inactive-row");
        noOption.put("limitdis", false);
        noOption.put("limitval", "N");
        isActiveCombobox.add(noOption);

        isActiveCol.put("combobox", isActiveCombobox);
        isActiveCol.put("inputname", "ISACTIVE");
        tabthArray.add(isActiveCol);

        return tabthArray;
    }

    /**
     * 添加下拉选项
     *
     * @param combobox 下拉选项数组
     * @param desc     选项描述
     * @param val      选项值
     */
    private void addComboboxItem(JSONArray combobox, String desc, String val) {
        JSONObject option = new JSONObject();
        option.put("limitdesc", desc);
        option.put("limitcss", null);
        option.put("limitval", val);
        combobox.add(option);
    }

    /**
     * 构建行数据
     *
     * @param log 第三方接口日志对象
     * @return 行数据JSON对象
     */
    private JSONObject buildRowData(IpBThirdPartyInterfaceLog log) {
        JSONObject rowObj = new JSONObject();

        // ID
        JSONObject idObj = new JSONObject();
        idObj.put("val", log.getId() != null ? log.getId().toString() : "");
        rowObj.put("ID", idObj);

        // 第三方平台名称
        JSONObject thirdPartyNameObj = new JSONObject();
        thirdPartyNameObj.put("val", "富勒WMS"); // 这里可以根据实际情况从log对象中获取或者映射
        rowObj.put("THIRD_PARTY_NAME", thirdPartyNameObj);

        // 业务单号
        JSONObject billNoObj = new JSONObject();
        billNoObj.put("val", log.getBillNo() != null ? log.getBillNo() : "");
        rowObj.put("BILL_NO", billNoObj);

        // 单据类型
        JSONObject billTypeObj = new JSONObject();
        billTypeObj.put("val", ""); // 根据实际情况设置
        rowObj.put("RESERVE_BIGINT01", billTypeObj);

        // 接口名
        JSONObject methodObj = new JSONObject();
        methodObj.put("val", "taobao.qimen.order.cancel"); // 根据实际情况从log对象中获取
        rowObj.put("METHOD", methodObj);

        // 提供方
        JSONObject providerObj = new JSONObject();
        providerObj.put("val", "奇门"); // 根据实际情况从log对象中获取
        rowObj.put("PROVIDER", providerObj);

        // 调用方
        JSONObject consumerObj = new JSONObject();
        consumerObj.put("val", "中台"); // 根据实际情况从log对象中获取
        rowObj.put("CONSUMER", consumerObj);

        // 接口类型
        JSONObject typeObj = new JSONObject();
        typeObj.put("val", "RESTFUL"); // 根据实际情况从log对象中获取
        rowObj.put("TYPE", typeObj);

        // 调用结果
        JSONObject codeObj = new JSONObject();
        if (log.getCode() != null) {
            if (log.getCode() == 0) {
                codeObj.put("val", "成功");
            } else {
                codeObj.put("val", "失败");
            }
        } else {
            codeObj.put("val", "");
        }
        rowObj.put("CODE", codeObj);

        // 请求时间
        JSONObject requestTimeObj = new JSONObject();
        if (log.getRequestTime() != null) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            requestTimeObj.put("val", sdf.format(log.getRequestTime()));
        } else {
            requestTimeObj.put("val", "");
        }
        rowObj.put("REQUEST_TIME", requestTimeObj);

        // 响应时间
        JSONObject responseTimeObj = new JSONObject();
        if (log.getResponseTime() != null) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            responseTimeObj.put("val", sdf.format(log.getResponseTime()));
        } else {
            responseTimeObj.put("val", "");
        }
        rowObj.put("RESPONSE_TIME", responseTimeObj);

        // 可用
        JSONObject isActiveObj = new JSONObject();
        isActiveObj.put("val", "是"); // 根据实际情况从log对象中获取
        rowObj.put("ISACTIVE", isActiveObj);

        return rowObj;
    }
}
