package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.oc.oms.model.request.TransferOrderRequest;
import com.jackrain.nea.oc.oms.model.result.TransferOrderResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;

/**
 * @author: chenxiulou
 * @description: 寻仓订单
 * @since: 2019-09-04
 * create at : 2019-09-04 17:41
 */
public interface JitxDeliveryFeedBackCmd {
    ValueHolderV14<TransferOrderResult> startDeliveryFeedBack(TransferOrderRequest transferOrderRequest);
}
