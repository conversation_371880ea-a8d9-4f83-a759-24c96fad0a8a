package com.jackrain.nea.oc.oms.config;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
@Data
public class OmsSystemConfig {

    /**
     * 是否开启查询会员紧急程度
     */
    @Value("${r3.oc.oms.transfer.select.vip.urgency.enabled:true}")
    private boolean transferSelectVipUrgencyEnabled;

    /**
     * 是否开启转换淘宝订单其他项内容(支付信息、优惠信息、淘宝订单表数据)
     */
    @Value("${r3.oc.oms.transfer.taobao.other.items.enabled:true}")
    private boolean transferTaobaoOrderOtherItemEnabled;

    /**
     * 是否开启转换过程中验证价格策略
     */
    @Value("${r3.oc.oms.transfer.validate.price.enabled:true}")
    private boolean transferValidatePriceEnabled;

    /**
     * 是否开启转换过程中直播策略标签
     */
    @Value("${r3.oc.oms.transfer.live.strategy.enabled:true}")
    private boolean transferLiveStrategyEnabled;

    /**
     * 转单过程中是否对双十一预售订单进行转单服务；
     * 乔丹不需要进行转单，主要是不想占库存
     */
    @Value("${r3.oc.oms.transfer.pre.sale.double11.enabled:true}")
    private boolean transferPreSale4Double11;

    /**
     * 是否开启跳过自动审单业务（审单业务阶段）
     */
    @Value("${r3.oc.oms.audit.skip.audit.enabled:false}")
    private boolean auditOrderSkipAuditEnabled;

    /**
     * 是否开启跳过调用促销业务（占单业务阶段）
     */
    @Value("${r3.oc.oms.tobeconfirmed.skip.call.promotion.enabled:false}")
    private boolean tobeConfirmedSkipCallPromotionEnabled;

    /**
     * 占单业务阶段中是否缓存库存同步策略信息（占单业务阶段）
     */
    @Value("${r3.oc.oms.tobeconfirmed.cache.sync.stock.strategy.enabled:true}")
    private boolean tobeConfirmedCacheSyncStockStrategyEnabled;

    /**
     * 审单业务阶段中是否开启价格审核策略（审单业务阶段）
     */
    @Value("${r3.oc.oms.audit.check.price.strategy.enabled:true}")
    private boolean auditCheckOrderPriceStrategyEnabled;

    /**
     * 审单业务阶段中审核完成后立即自动传输WMS（审单业务阶段）
     */
    @Value("${r3.oc.oms.audit.send.order.to.wms.enabled:false}")
    private boolean auditAutoSendOrderToWmsEnabled;

    /**
     * 退单业务中，自动调用AG退款接口（退单业务阶段）
     */
    @Value("${r3.oc.oms.refund.auto.call.ag.enabled:false}")
    private boolean refundAutoCallAgEnabled;

    /**
     * 分销代销审核业务中，对分销商资金进行占用是否开启（审核业务）
     */
    @Value("${r3.oc.oms.audit.occupy.online.fund.enabled:false}")
    private boolean auditOccupyOnlineFundEnabled;

    /**
     * 占单过程中是否进行按照仓库拆单逻辑（占单业务）
     */
    @Value("${r3.oc.oms.tobeconfirmed.split.order.storage.enabled:true}")
    private boolean tobeConfirmedSplitOrderFromStorageEnabled;

    /**
     * 占单过程中是否跳过订单分销业务SKU（占单业务）
     */
    @Value("${r3.oc.oms.tobeconfirmed.skip.order.sku.filter.enabled:false}")
    private boolean tobeConfirmedSkipOrderSkuFilterStrategyEnabled;

    /**
     * 占单过程中是否跳过检查订单是否为京东仓库订单（占单业务）
     */
    @Value("${r3.oc.oms.tobeconfirmed.skip.operate.jd.warehouse.enabled:false}")
    private boolean tobeConfirmedSkipOperateJdWarehouseEnabled;

    /**
     * 占单过程中是否跳过检查订单是否为京东仓库订单（占单业务）
     */
    @Value("${r3.oc.oms.tobeconfirmed.skip.split.virtual.sku.enabled:false}")
    private boolean tobeConfirmedSkipSplitVirtualSkuEnabled;

    /**
     * 占单过程中是否跳过检查订单是否有退款SKU（占单业务）
     */
    @Value("${r3.oc.oms.tobeconfirmed.skip.check.refund.sku.enabled:false}")
    private boolean tobeConfirmedSkipCheckHasRefundSkuEnabled;

    /**
     * 占单过程中是否跳过重新计算订单金额（占单业务）
     */
    @Value("${r3.oc.oms.tobeconfirmed.skip.balance.order.money.enabled:false}")
    private boolean tobeConfirmedSkipBalanceOrderMoneyEnabled;

    /**
     * 占单过程中是否跳过唯品会时效订单处理（占单业务）
     */
    @Value("${r3.oc.oms.tobeconfirmed.skip.vip.time.order.enabled:false}")
    private boolean tobeConfirmedSkipVipTimeOrderEnabled;

    /**
     * 合单方式.默认mq
     */
    @Value("${r3.oc.oms.merge.auto.routetype.mq:true}")
    private boolean autoMergeOrderRouteTypeByMq;

    /**
     * 合单.每组合并单据数量上限
     */
    @Value("${r3.oc.oms.merge.eachgroup.limit.qty:50}")
    private int autoMergeOrderEachGroupQty;

    /**
     * 合单.每组合并单据数量上限 JITX限制 20
     */
    @Value("${r3.oc.oms.jitx.merge.eachgroup.limit.qty:20}")
    private int jitxAutoMergeOrderEachGroupQty;

    /**
     * 合单.mq发送每条合并组数量  30/条
     */
    @Value("${r3.oc.oms.merge.eachsendmq.group.qty:30}")
    private int autoMergeOrderEachSendMqGroupQty;

    /**
     * 合单.task 分库. 分页查询,每页数量
     */
    @Value("${r3.oc.oms.merge.gsitask.eachSize:500}")
    private int autoMergeOrderTaskSearchEachSplitQty;


    /**
     * 退单新增是否带入赠品
     */
    @Value("${r3.oc.oms.return.order.add.bring.gift:true}")
    private boolean returnOrderAddBringGift;


    /**
     * jitx订单审核前提前发货开关
     */
    @Value("${r3.oc.oms.jitx.order.merged.ship.enable:true}")
    private boolean jitxMergedOrderShipWhenAudit;

    /**
     * 提前平台发货测试使用
     */
    @Value("${r3.oc.oms.jitx.order.merged.ship.audit.result:false}")
    private boolean jitxMergedOrderShipFailAuditResult;

    /**
     * 重置无物流单号延迟推送退单时间
     */
    @Value("${return.order.push.delay.time.reset:true}")
    private boolean resetDelayTime;

    /**
     * 重置无物流单号延迟推送退单时间
     */
    @Value("${return.order.to.wms.task.push.delay.time.reset:false}")
    private boolean resetDelayTimeWhenTaskExcute;

    /**
     * 是否生成退单
     */
    @NacosValue(value = "${isCreatedReturnOrder:true}", autoRefreshed = true)
    private boolean isCreatedReturnOrder;

    @NacosValue(value = "${r3.oc.oms.wms_stock_in_back.qty.limit:2000}", autoRefreshed = true)
    private int wmsStockInBackQty;

    /**
     * 是否开启通用转单优惠特殊处理：1027
     */
    @NacosValue("${r3.oc.oms.transfer.standplat.discount.special.isopen:false}")
    private Boolean transferDiscountSpecialIsOpen;
    /**
     * 去除商品优惠的平台ids：多个,分割 1027
     */
    @NacosValue("${r3.oc.oms.transfer.standplat.discount.special.product.platformIds:}")
    private String transferDiscountSpecialProductPlatformIds;
    /**
     * 去除订单优惠的平台ids：多个,分割 1027
     */
    @NacosValue("${r3.oc.oms.transfer.standplat.discount.special.order.platformIds:}")
    private String transferDiscountSpecialOrderPlatformIds;

    @NacosValue("${r3.oc.oms.transfer.standplat.jingdong.dx.logistics.code:'JDZY'}")
    private String jingDongDXLogisticsCode;

    /**
     * 手机号黑名单卡单打标
     */
    @NacosValue(value = "${r3.oc.oms.highRiskOrder.stCCustomLabelId:5}", autoRefreshed = true)
    private String stCCustomLabelId;
    @NacosValue(value = "${r3.oc.oms.highRiskOrder.stCCustomLabelEname:手机黑名单}", autoRefreshed = true)
    private String stCCustomLabelEname;
    @NacosValue(value = "${r3.oc.oms.highRiskOrder.orderNumLimit:50}", autoRefreshed = true)
    private int orderNumLimit;
    @NacosValue(value = "${r3.oc.oms.highRiskOrder.orderAmountLimit:5000}", autoRefreshed = true)
    private String orderAmountLimit;

    // adb库名
    @NacosValue(value = "${r3.oc.oms.order.export.adb.order_db_name}")
    public String rptOrderDbName;
    @NacosValue(value = "${r3.oc.oms.order.export.adb.basics_db_name}")
    public String rptBasicsDbName;


    /**
     * ES使用【ORDER_SOURCE_PLATFORM_ECODE】查询时，由于测试与生产的ES字段类型不一致，
     * 在生产环境，需要使用keyword查询，在测试环境代码时，则不需要
     */
    @NacosValue(value = "${r3.oc.oms.order.query.elastic.useKeyword:true}", autoRefreshed = true)
    private Boolean orderQueryElasticUseKeyword;
}
