package com.jackrain.nea.oc.oms.refund.util;

import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @Desc : 日志记录
 * <AUTHOR> xiWen
 * @Date : 2020/11/3
 */
public class LogStepRecord {

    private int step;

    private String currentKey;

    private StringBuilder sb;

    private List<StringBuilder> noteList;

    /**
     * @return LogStepRecord
     */
    public static LogStepRecord build() {
        return new LogStepRecord().step();
    }

    /**
     * @param stepKey key
     * @return LogStepRecord
     */
    public static LogStepRecord build(String stepKey) {
        return new LogStepRecord().step(stepKey);
    }

    /**
     * @param msg msg
     */
    public LogStepRecord record(Object msg) {
        sb.append(msg);
        return this;
    }

    /**
     * @param stepKey new step
     * @return LogStepRecord
     */
    public LogStepRecord step(String... stepKey) {
        sb = new StringBuilder();
        if (stepKey.length < 1 || StringUtils.isBlank(stepKey[0])) {
            currentKey = getKey();
        } else {
            step++;
            currentKey = stepKey[0];
        }
        if (noteList == null) {
            noteList = new ArrayList<>();
        }
        noteList.add(sb.append(currentKey).append(": "));
        return this;
    }

    /**
     * @return log content
     */
    public String content() {
        if (noteList == null) {
            return "";
        }
        return JSON.toJSONString(noteList);
    }

    private String getKey() {
        return "step0" + (++step);
    }

    private LogStepRecord() {
    }

}
