package com.jackrain.nea.oc.oms.task.jitxorder;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.es.ES4IpCancelTimeOrderVip;
import com.jackrain.nea.oc.oms.model.relation.IpBCancelTimeOrderVipRelation;
import com.jackrain.nea.oc.oms.process.jitx.timeorder.cancel.VipTimeOrderCancelProcessImpl;
import com.jackrain.nea.oc.oms.services.IpVipTimeOrderCancelService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 唯品会取消时效订单 - 补偿任务
 * 1、查询ES中SYNSTATUS=0的单据
 *
 * @author: chenxiulou
 * @since: 2019-09-03
 * create at : 2019-03-15 23:10
 */
@Component
@Slf4j
public class AutoTimeOrderCancelTask extends BaseR3Task implements IR3Task {

    @Autowired
    private IpVipTimeOrderCancelService cancelService;


    @Autowired
    private VipTimeOrderCancelProcessImpl timeOrderCancelProcess;

    @Override
    @XxlJob("AutoTimeOrderCancelTask")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult result = new RunTaskResult();
        try {

            List<String> orderNoList = ES4IpCancelTimeOrderVip
                    .findOrderSnByTransStatusAndSysRemark(0, DEFAULT_PAGE_SIZE);

            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("AutoTimeOrderCancelTask.OrderNoList={}"), JSONObject.toJSONString(orderNoList));
            }

            List<IpBCancelTimeOrderVipRelation> cancelRelationList = new ArrayList<>();
            for (String orderNo : orderNoList) {
                IpBCancelTimeOrderVipRelation cancelRelation = this.cancelService.selectCancelTimeOrder(orderNo);
                if (cancelRelation == null) {
                    String errorMessage = Resources.getMessage("###AutoTimeOrderCancelTask.Order.NotExist!###OrderNo="
                            + orderNo);
                    log.error(LogUtil.format(errorMessage));
                } else {
                    cancelRelationList.add(cancelRelation);
                }
            }

            threadOrderProcessor.startMultiThreadExecute(this.timeOrderCancelProcess, cancelRelationList);

            result.setSuccess(true);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(LogUtil.format("AutoTimeOrderCancelTask.Execute.Error: {}"), Throwables.getStackTraceAsString(ex));
            result.setSuccess(false);
            result.setMessage(ex.getMessage());
        }

        return result;

    }


}
