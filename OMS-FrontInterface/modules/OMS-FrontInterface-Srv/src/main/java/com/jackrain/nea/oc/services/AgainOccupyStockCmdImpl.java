package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.api.AgainOccupyStockCmd;
import com.jackrain.nea.oc.oms.services.AgainOccupyStockService;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class AgainOccupyStockCmdImpl implements AgainOccupyStockCmd {

    @Autowired
    AgainOccupyStockService againOccupyStockService;

    @Override
    public ValueHolder againOccupyStock(JSONObject obj, User user) {
        return againOccupyStockService.againOccupyStock(obj,user);
    }
}
