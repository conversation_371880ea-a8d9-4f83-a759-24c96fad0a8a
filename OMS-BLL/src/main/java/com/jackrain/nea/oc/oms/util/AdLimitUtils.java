package com.jackrain.nea.oc.oms.util;

import com.google.common.collect.Maps;
import com.jackrain.nea.core.config.TableManagerCache;
import com.jackrain.nea.core.schema.*;
import com.jackrain.nea.exception.NDSException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date  2022-02-22 17:38
 */
@Slf4j
public class AdLimitUtils {

    /**
     * 根据value从字段选项组的list中获取lable
     *
     * @param limitValue  当前字段对应字段选项组的value
     * @param adLimitName 字段选项组的名称
     * @param tm          tablemanager对象
     * @return
     */
    public static String getAdLimitValue(String limitValue, String adLimitName, TableManager tm) {
        LimitvalueGroup limitvalueGroup = new LimitvalueGroup();
        List<Limitvalue> list = new ArrayList<>();
        if (tm != null) {
            limitvalueGroup = tm.getAdLimitValueGroup(adLimitName);
            if (limitvalueGroup != null) {
                list = limitvalueGroup.getAdLimitvalues();
            }
        }
        if (CollectionUtils.isNotEmpty(list)) {
            List<Limitvalue> listLimit = list.stream().filter(x -> x.getValue().equals(limitValue)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(listLimit)) {
                return listLimit.get(0) == null ? "" : listLimit.get(0).getDescription();
            } else {
                return "";
            }
        } else {
            return "";
        }
    }

    /**
     * 通过字段选项组的值获取名称
     *
     * @param tableName  表名
     * @param columnName 字段名
     * @param limitValue 字段值
     * @return
     */
    public static String getAdLimitValue(String tableName, String columnName, String limitValue) {
        try {
            if (StringUtils.isBlank(limitValue)) {
                return "";
            }
            TableManager tableManager = TableManagerCache.getTableManager();
            Table table = tableManager.getTable(tableName);
            Column column = table.getColumn(columnName);
            if (column == null) {
                throw new NDSException("表字段查询异常!");
            }
            if (column.getLimitValueGroupName() != null) {
                //字段选项组
                return getAdLimitValue(limitValue, column.getLimitValueGroupName().toUpperCase(), tableManager);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("获取字段选项组" + tableName + "-" + columnName + "异常：" + e.getMessage(), e);
        }
        return "";
    }

    /**
     * 获取值Map
     *
     * @param tableName  表明
     * @param columnName 字段名
     * @return 字段描述
     */
    public static Map<String, String> getValueMap(String tableName, String columnName) {
        try {
            TableManager tableManager = TableManagerCache.getTableManager();
            Table table = tableManager.getTable(tableName);
            Column column = table.getColumn(columnName);
            if (column == null) {
                throw new NDSException("表字段查询异常!");
            }
            if (log.isDebugEnabled()) {
                log.debug("--开始查询字段选项组：" + column.getLimitValueGroupName());
            }
            if (column.getLimitValueGroupName() != null) {
                List<Limitvalue> list = new ArrayList<>();
                if (tableManager != null) {
                    LimitvalueGroup limitvalueGroup = tableManager.getAdLimitValueGroup(column.getLimitValueGroupName().toUpperCase());
                    if (limitvalueGroup != null) {
                        list = limitvalueGroup.getAdLimitvalues();
                    }
                }
                if (CollectionUtils.isNotEmpty(list)) {
                    Map<String, String> map = new HashMap<>();
                    list.forEach(l -> {
                        map.put(l.getValue(), l.getDescription());
                    });
                    return map;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Maps.newHashMap();
    }

    /**
     * 获取描述Map
     *
     * @param tableName  表明
     * @param columnName 字段名
     * @return 字段描述
     */
    public static Map<String, String> getDescriptionMap(String tableName, String columnName) {
        try {
            TableManager tableManager = TableManagerCache.getTableManager();
            Table table = tableManager.getTable(tableName);
            Column column = table.getColumn(columnName);
            if (column == null) {
                throw new NDSException("表字段查询异常!");
            }
            if (log.isDebugEnabled()) {
                log.debug("--开始查询字段选项组：" + column.getLimitValueGroupName());
            }
            if (column.getLimitValueGroupName() != null) {
                List<Limitvalue> list = new ArrayList<>();
                if (tableManager != null) {
                    LimitvalueGroup limitvalueGroup = tableManager.getAdLimitValueGroup(column.getLimitValueGroupName().toUpperCase());
                    if (limitvalueGroup != null) {
                        list = limitvalueGroup.getAdLimitvalues();
                    }
                }
                if (CollectionUtils.isNotEmpty(list)) {
                    Map<String, String> map = new HashMap<>();
                    list.forEach(l -> {
                        map.put(l.getDescription(), l.getValue());
                    });
                    return map;
                }
            }
        } catch (Exception e) {
            log.error("查询字段选项组失败：" + e.getMessage(), e);
            e.printStackTrace();
        }
        return Maps.newHashMap();
    }

    /**
     * 根据object和object的字段名称获取对应字段的值
     *
     * @param obj
     * @param filedName 字段名称
     * @return
     */
    public static Object getFiledValueByName(Object obj, String filedName) {
        try {
            Class<?> aClass = obj.getClass();
            Field declaredField = aClass.getDeclaredField(under2camel(filedName));
            declaredField.setAccessible(true);
            PropertyDescriptor pd = new PropertyDescriptor(declaredField.getName(), aClass);
            Method readMethod = pd.getReadMethod();
            return readMethod.invoke(obj);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 功能：驼峰命名转下划线命名
     * 小写和大写紧挨一起的地方,加上分隔符,然后全部转小写
     */
    public static String camel2under(String c) {
        String separator = "_";
        c = c.replaceAll("([a-z])([A-Z])", "$1" + separator + "$2").toLowerCase();
        return c;
    }

    /**
     * 功能：下划线命名转驼峰命名
     * 将下划线替换为空格,将字符串根据空格分割成数组,再将每个单词首字母大写
     *
     * @param s
     * @return
     */
    private static String under2camel(String s) {
        String separator = "_";
        String under = "";
        s = s.toLowerCase().replace(separator, " ");
        String sarr[] = s.split(" ");
        for (int i = 0; i < sarr.length; i++) {
            String w = "";
            if (i == 0) {
                w = sarr[i];
            } else {
                w = sarr[i].substring(0, 1).toUpperCase() + sarr[i].substring(1);
            }
            under += w;
        }
        return under;
    }
}
