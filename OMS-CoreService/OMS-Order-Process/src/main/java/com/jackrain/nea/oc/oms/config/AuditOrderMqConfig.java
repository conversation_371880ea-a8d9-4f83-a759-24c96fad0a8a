package com.jackrain.nea.oc.oms.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * 定时任务审单tag
 *
 * @author: heliu
 * @since: 2019/5/29
 * create at : 2019/5/29 9:59
 */
@Configuration
@Data
public class AuditOrderMqConfig {

    @Value("${r3.oc.oms.audit.mq.topic:}")
    private String sendMqTopic;

    @Value("${r3.oc.oms.audit.mq.tag:}")
    private String sendAuditTag;
}