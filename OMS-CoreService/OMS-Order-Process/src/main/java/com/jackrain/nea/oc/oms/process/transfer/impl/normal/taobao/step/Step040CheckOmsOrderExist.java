package com.jackrain.nea.oc.oms.process.transfer.impl.normal.taobao.step;


import com.alibaba.nacos.api.config.ConfigService;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoOrderRelation;
import com.jackrain.nea.oc.oms.model.resources.SysNotesConstant;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.taobao.TaoBaoOrderStatus;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.OcBOrderOffService;
import com.jackrain.nea.oc.oms.services.advance.AdvanceConstant;
import com.jackrain.nea.oc.oms.services.audit.wait.OmsAuditTimeCalculateReason;
import com.jackrain.nea.oc.oms.services.task.OmsAuditTaskService;
import com.jackrain.nea.oc.oms.services.task.OmsToWingDeliveryTaskService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.UserImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * 检查订单是否已经存在
 *
 * @author: 易邵峰
 * @since: 2019-01-20
 * create at : 2019-01-20 02:45
 */
@Step(order = 40, description = "检查中间表订单是否已经存在")
@Slf4j
@Component
public class Step040CheckOmsOrderExist extends BaseTaobaoOrderProcessStep
        implements IOmsOrderProcessStep<IpTaobaoOrderRelation> {
    @Autowired
    private OcBOrderOffService ocBOrderOffService;

    @Autowired
    OmsToWingDeliveryTaskService omsToWingDeliveryTaskService;

    @Autowired
    private OmsAuditTaskService omsAuditTaskService;

    @Override
    public ProcessStepResult<IpTaobaoOrderRelation> startProcess(IpTaobaoOrderRelation orderInfo,
                                                                 ProcessStepResult preStepResult,
                                                                 boolean isAutoMakeup, User operateUser) {
        ProcessStepResult<IpTaobaoOrderRelation> stepResult = new ProcessStepResult<>();


        String orderStatus = orderInfo.getTaobaoOrder().getStatus();
        String stepTradeStatus = orderInfo.getTaobaoOrder().getStepTradeStatus();
        boolean isTaobaoNormalStatus = TaoBaoOrderStatus.WAIT_SELLER_SEND_GOODS.equalsIgnoreCase(orderStatus)
                && (TaoBaoOrderStatus.FRONT_PAID_FINAL_PAID.equalsIgnoreCase(stepTradeStatus)
                || StringUtils.isEmpty(stepTradeStatus));
        boolean isHistoryOrder = TaoBaoOrderStatus.TRADE_FINISHED.equalsIgnoreCase(orderStatus)
                || TaoBaoOrderStatus.WAIT_BUYER_CONFIRM_GOODS.equalsIgnoreCase(orderStatus);
        // 增加是否关闭订单判断。如果是关闭订单状态，则直接标记为转换
        boolean isTradeClosed = TaoBaoOrderStatus.TRADE_CLOSED.equalsIgnoreCase(orderStatus)
                || TaoBaoOrderStatus.TRADE_CLOSED_BY_TAOBAO.equalsIgnoreCase(orderStatus);

        String orderNo = orderInfo.getOrderNo();
        long orderId = orderInfo.getOrderId();
        List<OcBOrder> findOrderInfoList = orderService.selectOmsOrderInfo(orderInfo.getTaobaoOrder().getTid());

        //天猫周期购订单
        if (CollectionUtils.isNotEmpty(orderInfo.getTaobaoOrderCycleBuyList()) && CollectionUtils.isNotEmpty(findOrderInfoList)){
            String operateMessage = "天猫周期购订单更新信息。OrderId=" + orderId + ";OrderNo=" + orderNo;
            stepResult.setMessage(operateMessage);
            stepResult.setStatus(StepStatus.SUCCESS);
            stepResult.setNextStepOperateObj(findOrderInfoList);
            stepResult.setNextStepClass(Step073UpdateCycleInfo.class);
            return stepResult;
        }

        Boolean isTransform = false;
        //控制历史订单是否需要转换
        if (isHistoryOrder && !isTransform) {
            String operateMessage = "订单状态=" + orderStatus + ";订单已经被关闭。进入下一阶段设置已转换状态。OrderId="
                    + orderId + ";OrderNo=" + orderNo;
            stepResult.setMessage(operateMessage);
            stepResult.setStatus(StepStatus.SUCCESS);
            stepResult.setNextStepOperateObj(findOrderInfoList);
            stepResult.setNextStepClass(Step070UpdateSellerRemark.class);
            return stepResult;
        }

        if (isTradeClosed) {
            String operateMessage = "订单状态=" + orderStatus + ";订单已经被关闭。进入下一阶段更新卖家备注。OrderId=" + orderId
                    + ";OrderNo=" + orderNo;
            stepResult.setMessage(operateMessage);
            stepResult.setStatus(StepStatus.SUCCESS);
            //2019-11-21 交易关闭 更新卖家备注
            stepResult.setNextStepOperateObj(findOrderInfoList);
            stepResult.setNextStepClass(Step070UpdateSellerRemark.class);
        } else if (isTaobaoNormalStatus || isHistoryOrder) {
            stepResult.setStatus(StepStatus.SUCCESS);
            if (findOrderInfoList != null && findOrderInfoList.size() > 0) {
                stepResult.setMessage("订单状态=" + orderStatus + ";单据编号=" + orderNo + "已存在. OrderId=" + orderId
                        + ";OrderNo=" + orderNo + " 进入更新卖家备注阶段。");
                stepResult.setNextStepOperateObj(findOrderInfoList);
                stepResult.setNextStepClass(Step070UpdateSellerRemark.class);
            } else {
                stepResult.setMessage("订单状态=" + orderStatus + ";单据编号=" + orderNo + "，进入下一阶段");
            }
        } else {
            if (findOrderInfoList != null && findOrderInfoList.size() > 0) {
                stepResult.setMessage("订单状态=" + orderStatus + ";单据编号=" + orderNo + "已存在. OrderId=" + orderId
                        + ";OrderNo=" + orderNo + " 进入更新卖家备注阶段。");
                stepResult.setStatus(StepStatus.SUCCESS);
                stepResult.setNextStepOperateObj(findOrderInfoList);
                stepResult.setNextStepClass(Step070UpdateSellerRemark.class);
            } else {
                String errorMessage = "订单状态=" + orderStatus + ";订单单据不存在. OrderId=" + orderId
                        + ";OrderNo=" + orderNo;
                stepResult.setMessage(errorMessage);
                stepResult.setStatus(StepStatus.FAILED);
            }
        }
        return stepResult;
    }
}
