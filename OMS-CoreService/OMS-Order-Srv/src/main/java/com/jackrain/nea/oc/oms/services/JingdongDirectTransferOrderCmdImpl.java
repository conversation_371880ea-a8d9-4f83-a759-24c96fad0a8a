package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.api.JingdongDirectTransferOrderCmd;
import com.jackrain.nea.oc.oms.mapper.IpBJingdongDirectMapper;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.relation.IpJingdongDirectOrderRelation;
import com.jackrain.nea.oc.oms.model.request.TransferOrderRequest;
import com.jackrain.nea.oc.oms.model.result.TransferOrderResult;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongDirect;
import com.jackrain.nea.oc.oms.process.transfer.impl.normal.jddirect.JingdongDirectTransferOrderProcessImpl;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.ProcessStepResultList;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;


@Service(protocol = "dubbo", validation = "true", group = "oc-core", version = "1.0")
@Slf4j
@Component
public class JingdongDirectTransferOrderCmdImpl implements JingdongDirectTransferOrderCmd {

    @Autowired
    private JingdongDirectTransferOrderProcessImpl jingdongDirectTransferOrderProcess;

    @Autowired
    private IpBJingdongDirectMapper ipBJingdongDirectMapper;

    @Override
    public ValueHolderV14<TransferOrderResult> startTransferOrder(TransferOrderRequest transferOrderRequest) {
        ValueHolderV14<TransferOrderResult> resultValueHolderV14 = new ValueHolderV14<>();
        try {
            boolean hasError = false;
            int failedNumber = 0;
            int successNumber = 0;
            List<ProcessStepResult> errorStepResultList = new ArrayList<>();
            for (String orderNo : transferOrderRequest.getOrderNoList()) {
                if (log.isDebugEnabled()) {
                    log.debug("JingdongDirectTransferOrderProcessImpl.startTransferOrder OrderNo=" + orderNo + ";ChannelType="
                            + transferOrderRequest.getChannelType());
                }
                if (transferOrderRequest.getChannelType() == ChannelType.JINGDONG_DIRECT) {
                    IpBJingdongDirect direct = ipBJingdongDirectMapper.selectIpBJingdongDirectByOrderNo(orderNo);
                    if (direct != null) {
                        IpJingdongDirectOrderRelation relation = new IpJingdongDirectOrderRelation();
                        relation.setIpBJingdongDirect(direct);
                        ProcessStepResultList processStepResultList = jingdongDirectTransferOrderProcess.start(relation,
                                false, transferOrderRequest.getOperateUser());
                        if (!processStepResultList.isProcessFishSuccess()) {
                            hasError = true;
                            errorStepResultList.add(processStepResultList.getLastFaileOrFinishProcessStepResult());
                            failedNumber++;
                        } else {
                            successNumber++;
                        }
                    } else {
                        hasError = false;
                        failedNumber++;
                    }
                }
            }

            if (hasError) {
                resultValueHolderV14.setCode(ResultCode.FAIL);
                StringBuilder sbMessage = new StringBuilder();
                sbMessage.append(String.format("转换成功%s条；转换失败%s条；失败原因：\r\n", successNumber, failedNumber));
                for (ProcessStepResult stepResult : errorStepResultList) {
                    if (stepResult != null) {
                        sbMessage.append(stepResult.getMessage());
                        sbMessage.append("\r\n");
                    }
                }
                resultValueHolderV14.setMessage(sbMessage.toString());
            } else {
                resultValueHolderV14.setCode(ResultCode.SUCCESS);
                resultValueHolderV14.setMessage("转换全部成功。");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            resultValueHolderV14.setCode(ResultCode.FAIL);
            resultValueHolderV14.setMessage("转换异常：" + ex.getMessage());
            log.error("JingdongDirectTransferOrderProcessImpl.startTransferOrder", ex);
        }

        return resultValueHolderV14;
    }

}