package com.jackrain.nea;

import cn.hutool.extra.spring.EnableSpringUtil;
import com.burgeon.mq.annotation.EnableMq;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.slf4j.MDC;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * Main注册程序
 *
 * @author: 易邵峰
 * @since: 2019-01-09
 * create at : 2019-01-09 21:30
 */
@EnableDiscoveryClient
@SpringBootApplication
@EnableAsync
@EnableDubbo(scanBasePackages = {"com.burgeon.r3", "com.jackrain.nea"})
@EnableSpringUtil
@EnableMq
public class OMSFrontInterfaceMainApplication extends SpringBootServletInitializer {
    static {
        System.setProperty("dubbo.application.logger", "slf4j");
        System.setProperty("dubbo.config-center.namespace","public");
        System.setProperty("dubbo.config-center.group","r3-oms");
        System.setProperty("dubbo.config-center.config-file","dubbo");
    }

    public static void main(String[] args) {
        // 若将devtools.enabled设置为true，会导致无法加载Dubbo
        System.setProperty("spring.devtools.restart.enabled", "false");
        ApplicationContext context = SpringApplication.run(applicationClass, args);
        System.out.println("Start SprintBoot Success ContextId=" + context.getId());
        String appServer = System.getProperty("app.id");
        System.out.println("Start Up app " + appServer);
        MDC.put("SERVER_NAME", appServer);
    }

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
        return application.sources(applicationClass);
    }

    private static final Class<OMSFrontInterfaceMainApplication> applicationClass = OMSFrontInterfaceMainApplication.class;

}
