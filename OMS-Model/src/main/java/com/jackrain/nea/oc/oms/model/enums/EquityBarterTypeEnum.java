package com.jackrain.nea.oc.oms.model.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * @program: r3-oc-oms
 * @description: 商品效期策略类型
 * @author: caomalai
 * @create: 2022-08-11 11:34
 **/
public enum EquityBarterTypeEnum {
    COMMUNAL("1", "公用"),
    APPOINT_SHOP("2", "指定店铺");
    @Getter
    private String key;
    @Getter
    private String desc;

    EquityBarterTypeEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public static EquityBarterTypeEnum getByKey(String key) {
        for (EquityBarterTypeEnum current : values()) {
            if (Objects.equals(current.getKey(), key)) {
                return current;
            }
        }
        return null;
    }

    public static EquityBarterTypeEnum getByDesc(String desc) {
        for (EquityBarterTypeEnum current : values()) {
            if (Objects.equals(current.getDesc(), desc)) {
                return current;
            }
        }
        return null;
    }
}
