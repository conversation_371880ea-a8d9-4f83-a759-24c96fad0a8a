package com.jackrain.nea.oc.oms.api;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.model.result.DeleteGiftResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

/**
 * @author: 夏继超
 * @since: 2019/3/11
 * create at : 2019/3/11 14:41
 */
public interface DeleteGift {
    /**
     * 删除赠品
     * @param orderId 订单的id
     * @param ids  明细id的集合
     * @param loginUser 当前登录用户
     * @return 返回的信息
     *//*
    ValueHolderV14<DeleteGiftResult> deleteGift(Long orderId, List<Long> ids, User loginUser);*/

    /**
     * 删除赠品
     *
     * @param param     传入的参数
     * @param loginUser 登陆的用户
     * @return 返回值
     */
    ValueHolderV14<DeleteGiftResult> deleteGift(JSONObject obj, User loginUser) throws NDSException;
}
