package com.jackrain.nea.oc.oms.model.enums.ac;

import lombok.Getter;

/**
 * @author:洪艺安
 * @since: 2019/6/21
 * @create at : 2019/6/21 10:16
 */
@Getter
public enum BillTypeEnum {
    /**
     * 退款
     */
    REFUND(1,"退款"),
    /**
     * 其他
     */
    OTHER(2,"其他");

    String text;
    int val;

    BillTypeEnum(int val,String text) {
        this.text = text;
        this.val = val;
    }

    public String getText() {
        return text;
    }

    public int getVal() {
        return val;
    }
}

