package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.ChargebackCheckCmd;
import com.jackrain.nea.oc.oms.services.ReturnOrderAuditService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: 周琳胜
 * @since: 2019/3/22
 * create at : 2019/3/22 10:36
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class ChargebackCheckCmdImlp implements ChargebackCheckCmd {

    @Autowired
    private ReturnOrderAuditService returnOrderAuditService;

    @Override
    public ValueHolderV14 execute(JSONObject obj, User user) throws NDSException {
        return returnOrderAuditService.returnOrderBatchAudit(obj, user);
    }

    @Override
    public ValueHolderV14 syncPlatformRefundStatus(List<Long> idList, User user) throws NDSException {
        return returnOrderAuditService.batchSyncPlatform(idList,false,user);
    }

    @Override
    public ValueHolderV14 updatePlatformRefundStatus(List<String> returnIdSuccessList, List<String> returnIdFailList, User user) throws NDSException {
        return returnOrderAuditService.updatePlatformRefundStatus(returnIdSuccessList,returnIdFailList,user);
    }
}
