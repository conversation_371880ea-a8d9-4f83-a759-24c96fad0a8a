//package com.jackrain.nea.oc.oms.process;
//
//import com.jackrain.nea.oc.oms.model.relation.IpJingdongRefundRelation;
//import com.jackrain.nea.oc.oms.process.transfer.impl.refund.jingdong.JingdongTransferRefundProcessImpl;
//import com.jackrain.nea.oc.oms.services.IpJingdongRefundService;
//import com.jackrain.nea.oc.oms.step.ProcessStepResultList;
//import com.jackrain.nea.resource.SystemUserResource;
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.context.annotation.Profile;
//import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//
///**
// * @Descroption 京东退单测试类
// * <AUTHOR>
// * @Date 2019/4/26 10:59
// */
//@RunWith(SpringJUnit4ClassRunner.class)
//@SpringBootTest()
//@Profile(value = "dev")
//public class TransferJdRefundProcessTest {
//    @Autowired
//    private JingdongTransferRefundProcessImpl jingdongTransferRefundProcessImpl;
//
//    @Autowired
//    private IpJingdongRefundService jingdongRefundService;
//    @Before
//    public void initial() {
//
//    }
//    @Test
//    public void testJingdongRefundProcess() {
//        IpJingdongRefundRelation orderInfo = jingdongRefundService.getJingdongRefundRelation("22");
//
//        ProcessStepResultList resultList = jingdongTransferRefundProcessImpl.start(orderInfo, false, SystemUserResource.getRootUser());
//        System.out.println(resultList.size());
//    }
//}
