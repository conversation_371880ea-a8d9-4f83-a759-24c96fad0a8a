package com.jackrain.nea.oc.oms.services.invoice;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.hub.api.HubInvoicingCmd;
import com.jackrain.nea.hub.model.HXInvoicingModel.JsonRootBean;
import com.jackrain.nea.hub.model.HXInvoicingModel.ResultType;
import com.jackrain.nea.oc.oms.constant.SapSalesDateConstant;
import com.jackrain.nea.oc.oms.mapper.ac.AcFOrderInvoiceItemMapper;
import com.jackrain.nea.oc.oms.mapper.ac.AcFOrderInvoiceMapper;
import com.jackrain.nea.oc.oms.mapper.ac.AcFOrderInvoiceSystemItemMapper;
import com.jackrain.nea.oc.oms.model.table.AcFOrderInvoice;
import com.jackrain.nea.oc.oms.model.table.AcFOrderInvoiceItem;
import com.jackrain.nea.oc.oms.model.table.AcFOrderInvoiceSystemItem;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Description: 封装数据调用开票接口
 *
 * @Author: guo.kw
 * @Since: 2022/9/7
 * create at: 2022/9/7 10:57
 */
@Slf4j
@Component
public class AcFOrderInvoiceRealTimeService {

    @Reference(group = "hub", version = "1.0")
    private HubInvoicingCmd hubInvoicingCmd;

    @Autowired
    private AcFOrderInvoiceMapper acFOrderInvoiceMapper;

    @Autowired
    private AcFOrderInvoiceItemMapper acFOrderInvoiceItemMapper;

    @Autowired
    private AcFOrderInvoiceSystemItemMapper acFOrderInvoiceSystemItemMapper;

    public ValueHolderV14<List<ResultType>> executeInvoice(List<Long> ids) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Start AcFOrderInvoiceRealTimeService executeInvoice",
                    "开票任务执行开始"));
        }
        ValueHolderV14<List<ResultType>> v14 = new ValueHolderV14<>();
        //分页查询开票信息
        if (CollectionUtils.isEmpty(ids)) {
            log.info(LogUtil.format("AcFOrderInvoiceRealTimeService executeInvoice is null"));
            v14.setCode(ResultCode.SUCCESS);
            v14.setMessage("没有对应的符合单据");
            return v14;
        }
        /*JsonRootBean jsonRootBean = new JsonRootBean();
        jsonRootBean.setBillRemark("测试数据");
        ResultType resultType = hubInvoicingCmd.jsonInvoicing(jsonRootBean);
        System.out.println(resultType);*/
        try {
            //查询订单发票表
            List<List<Long>> partition = Lists.partition(ids, SapSalesDateConstant.QUERY_SIZE_FIVE);
            List<AcFOrderInvoice> acFOrderInvoices = new ArrayList<>();
            //查询开票明细
            List<AcFOrderInvoiceItem> acFOrderInvoiceItems = new ArrayList<>();
            //系统单据明细
            List<AcFOrderInvoiceSystemItem> acFOrderInvoiceSystemItems = new ArrayList<>();
            for (List<Long> objIds : partition) {
                acFOrderInvoices.addAll(acFOrderInvoiceMapper.selectBatchIds(objIds));
                acFOrderInvoiceItems.addAll(acFOrderInvoiceItemMapper.selectList(new LambdaQueryWrapper<AcFOrderInvoiceItem>()
                        .in(AcFOrderInvoiceItem::getAcFOrderInvoiceId, objIds)
                        .eq(AcFOrderInvoiceItem::getIsactive, SapSalesDateConstant.ISACTIVE_YES)));
                acFOrderInvoiceSystemItems.addAll(acFOrderInvoiceSystemItemMapper.selectList(new LambdaQueryWrapper<AcFOrderInvoiceSystemItem>()
                        .in(AcFOrderInvoiceSystemItem::getAcFOrderInvoiceId, objIds)
                        .eq(AcFOrderInvoiceSystemItem::getIsactive, SapSalesDateConstant.ISACTIVE_YES)));
            }
            if (CollectionUtils.isEmpty(acFOrderInvoices)) {
                log.info(LogUtil.format("AcFOrderInvoiceRealTime query acFOrderInvoices is null"));
                v14.setCode(ResultCode.SUCCESS);
                v14.setMessage("订单发票中未查询到相对应单句");
                return v14;
            }
            List<JsonRootBean> jsonRootBeans = buildObject(acFOrderInvoices, acFOrderInvoiceItems, acFOrderInvoiceSystemItems);

            //返回值信息
            List<ResultType> resultTypes = new ArrayList<>();
            for (JsonRootBean jsonRootBean: jsonRootBeans) {
                // todo rpc调用开票接口
                ResultType resultType = hubInvoicingCmd.jsonInvoicing(jsonRootBean);
                resultTypes.add(resultType);
            }
            v14.setData(resultTypes);
        } catch (Exception e) {
            log.error(LogUtil.format("AcFOrderInvoiceRealTime error executeInvoice message:{}",
                    "AcFOrderInvoiceRealTime error executeInvoice"), Throwables.getStackTraceAsString(e));
            throw new NDSException("开票异常请检查");
        }
        v14.setMessage("开票任务执行完成！！！");
        v14.setCode(ResultCode.SUCCESS);
        return v14;
    }

    /**
     * 封装数据
     * @param acFOrderInvoices 主表
     * @param acFOrderInvoiceItems 开票明细
     * @param acFOrderInvoiceSystemItems 系统单据明细
     * @return
     */
    private List<JsonRootBean> buildObject(List<AcFOrderInvoice> acFOrderInvoices,
                                           List<AcFOrderInvoiceItem> acFOrderInvoiceItems,
                                           List<AcFOrderInvoiceSystemItem> acFOrderInvoiceSystemItems) {
        Map<Long, List<AcFOrderInvoiceItem>> acFOrderInvoiceItemMap = acFOrderInvoiceItems.stream()
                .collect(Collectors.groupingBy(AcFOrderInvoiceItem::getAcFOrderInvoiceId));
        Map<Long, List<AcFOrderInvoiceSystemItem>> acFOrderInvoiceSystemItemMap = acFOrderInvoiceSystemItems.stream()
                .collect(Collectors.groupingBy(AcFOrderInvoiceSystemItem::getAcFOrderInvoiceId));

        List<JsonRootBean> jsonRootBeans = new ArrayList<>();
        for (AcFOrderInvoice orderInvoice : acFOrderInvoices) {
            //todo 封装数据
        }
        return jsonRootBeans;
    }
}
