package com.jackrain.nea.oc.oms.model.enums;


/**
 * 发货后退款单单据状态
 *
 * @author: 夏继超
 * create at: 2019/9/16
 */
public enum ReturnAfSendReturnBillTypeEnum {


    /**
     * 是否可用
     */
    NOREFUND("未退款", 0),
    REFUNDING("退款中", 1),
    REFUNDCOMPLETED("退款完成", 2),
    CANCEL("取消", 3);

    String key;
    int val;

    ReturnAfSendReturnBillTypeEnum(String k, int v) {
        this.key = k;
        this.val = v;
    }

    public static String getKeyByVal(int val) {
        ReturnAfSendReturnBillTypeEnum[] enums = values();
        for (ReturnAfSendReturnBillTypeEnum billTypeEnum : enums) {
            if (val == billTypeEnum.getVal()) {
                return billTypeEnum.getKey();
            }
        }
        return null;
    }

    public String getKey() {
        return key;
    }

    public int getVal() {
        return val;
    }


    public Integer toInteger(){
        return val;
    }

}


