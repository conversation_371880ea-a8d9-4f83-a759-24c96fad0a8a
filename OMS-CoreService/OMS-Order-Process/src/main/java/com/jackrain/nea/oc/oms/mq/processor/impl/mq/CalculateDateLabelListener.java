package com.jackrain.nea.oc.oms.mq.processor.impl.mq;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.mq.annotation.RocketMqMessageListener;
import com.burgeon.mq.core.BaseMessageListener;
import com.burgeon.mq.enums.MqTypeEnum;
import com.google.common.base.Throwables;
import com.jackrain.nea.st.service.StCExpiryDateLabelService;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.UserImpl;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Date 2023/3/6 21:22
 * @Description
 */
@Slf4j
@RocketMqMessageListener(name = "CalculateDateLabelListener", type = MqTypeEnum.DEFAULT)
public class CalculateDateLabelListener implements BaseMessageListener {


    @Override
    public void consume(String messageBody, String messageTopic, String messageKey, String messageTag, Object object) {
        try {
            log.info(LogUtil.format("CalculateDateLabelListener.consume messageBody:{},messageKey:{},messageTopic:{},messageTag:{}",
                    "CalculateDateLabelListener.consume"), messageBody, messageKey, messageTopic, messageTag);
            User user = JSONObject.parseObject(messageBody, UserImpl.class);
            StCExpiryDateLabelService bean = ApplicationContextHandle.getBean(StCExpiryDateLabelService.class);
            bean.calculateEffectiveLabel(user, false);
        } catch (Exception e) {
            log.error(LogUtil.format("CalculateDateLabelListener.consume.error：{}",
                    "CalculateDateLabelListener.consume"), Throwables.getStackTraceAsString(e));
            throw e;
        }
    }
}
