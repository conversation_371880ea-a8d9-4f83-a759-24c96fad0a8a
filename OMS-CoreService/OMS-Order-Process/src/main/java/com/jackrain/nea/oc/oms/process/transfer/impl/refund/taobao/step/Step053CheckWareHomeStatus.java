package com.jackrain.nea.oc.oms.process.transfer.impl.refund.taobao.step;

import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsTaobaoRefundRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoRefund;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;

import java.util.List;

/**
 * @Author: gxx
 * @Date: 2020/6/5 11:40
 * @Version 1.0
 */
// @20200706 删除需求："4. 零售发货单状态是仓库发货，则更新“转换状态”为已转换，创建或更新未发货退款单。"
//@Step(order = 53, description = "判断订单是否为仓库发货")
//@Slf4j
//@Component
public class Step053CheckWareHomeStatus extends BaseTaobaoRefundProcessStep
        implements IOmsOrderProcessStep<OmsTaobaoRefundRelation> {
    @Override
    public ProcessStepResult<OmsTaobaoRefundRelation> startProcess(OmsTaobaoRefundRelation orderInfo, ProcessStepResult preStepResult,
                                                                   boolean isAutoMakeup, User operateUser) {
        List<OmsOrderRelation> omsOrderRelations = orderInfo.getOmsOrderRelation();
        IpBTaobaoRefund ipBTaobaoRefund = orderInfo.getIpBTaobaoRefund();
        int count = 0;
        for (OmsOrderRelation omsOrderRelation : omsOrderRelations) {
            Integer orderMark = omsOrderRelation.getOrderMark();
            if (TaobaoReturnOrderExt.ReturnBillsStatus.GOODS_FRONT.getCode().equals(orderMark)
                    || TaobaoReturnOrderExt.ReturnBillsStatus.WAREHOUSE_DELIVERY.getCode().equals(orderMark)) {
                count++;
            }
        }
        if (count == omsOrderRelations.size()) {
            String remark = "仓库发货,生成发货前退款单(仅退款)成功";
            omsRefundOrderService.foundRefundFrontRefundOnly(omsOrderRelations.get(0).getOcBOrder(), ipBTaobaoRefund, operateUser);
            ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                    remark, ipBTaobaoRefund);
            return new ProcessStepResult<>(StepStatus.FINISHED, remark);
        }
        return new ProcessStepResult<>(StepStatus.SUCCESS, "仓库发货处理完成,进入下一阶段");
    }
}
