package com.jackrain.nea.oc.services;

import com.jackrain.nea.oc.oms.api.OmsSkuQueryCmd;
import com.jackrain.nea.oc.oms.services.OmsItemProServices;
import com.jackrain.nea.ps.model.OmsProDelCmdRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: 黄世新
 * @Date: 2022/6/30 下午4:05
 * @Version 1.0
 */
@Component
public class OmsSkuQueryCmdImpl implements OmsSkuQueryCmd {
    @Autowired
    private OmsItemProServices omsItemProServices;
    @Override
    public ValueHolderV14 querySkuInfo(OmsProDelCmdRequest model, int operation) {
        return omsItemProServices.selectSkuInfo(model, operation);
    }

    @Override
    public ValueHolderV14 querySkuInfoSpec1(OmsProDelCmdRequest model, int operation) {
        return omsItemProServices.selectSkuInfoSpec1(model,operation);
    }
}
