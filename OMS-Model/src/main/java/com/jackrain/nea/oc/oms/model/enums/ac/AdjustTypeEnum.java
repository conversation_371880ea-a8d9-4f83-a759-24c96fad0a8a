package com.jackrain.nea.oc.oms.model.enums.ac;

import lombok.Getter;

/**
 * @author:洪艺安
 * @since: 2019/6/21
 * @create at : 2019/6/21 10:16
 */
@Getter
public enum AdjustTypeEnum {
    /**
     * 线上
     */
    ONLINE(1,"线上"),
    /**
     * 线下
     */
    UNDERLINE(2,"线下");

    int val;
    String text;

    AdjustTypeEnum(int val,String text) {
        this.text = text;
        this.val = val;
    }

    public String getText() {
        return text;
    }

    public int getVal() {
        return val;
    }
}

