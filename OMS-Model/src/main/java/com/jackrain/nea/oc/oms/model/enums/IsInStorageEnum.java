package com.jackrain.nea.oc.oms.model.enums;

import com.jackrain.nea.oc.oms.model.result.QueryOrderCheckBoxResult;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> 孙俊磊
 * @since :  2019-04-03
 * create at:  2019-04-03 13:11
 */
public enum IsInStorageEnum {

    /**
     * 是否入仓成功 0否1是
     */
    NOT_INSTORAGE("未入仓成功", 0),
    IS_INSTORAGE("入仓成功", 1);

    String key;
    Integer val;

    IsInStorageEnum(String k, Integer v) {
        this.key = k;
        this.val = v;
    }

    public String getKey() {
        return key;
    }

    public Integer getVal() {
        return val;
    }

    /**
     * 转化成QueryOrderCheckBoxResult
     *
     * @return list<QueryOrderCheckBoxResult>
     */
    public static List<QueryOrderCheckBoxResult> toQueryOrderCheckBoxResult() {
        List<QueryOrderCheckBoxResult> list = new ArrayList<>();
        for (IsMatchEnum e : IsMatchEnum.values()) {
            QueryOrderCheckBoxResult o = new QueryOrderCheckBoxResult();
            o.setLabel(e.getKey());
            o.setValue(String.valueOf(e.getVal()));
            list.add(o);
        }
        return list;
    }
}
