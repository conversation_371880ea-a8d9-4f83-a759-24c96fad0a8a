package com.jackrain.nea.oc.oms.process.transfer.impl.refund.jingdong.step;

import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransNodeTipEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpJingdongRefundRelation;
import com.jackrain.nea.oc.oms.model.resources.SysNotesConstant;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongOrder;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongRefund;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.utility.ExceptionUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Descroption 判断京东订单中间表是否存在
 * <AUTHOR>
 * @Date 2019/4/26 14:44
 */
@Step(order = 20, description = "判断京东订单中间表是否存在")
@Slf4j
@Component
public class Step020HasJdOrder extends BaseJingdongRefundProcessStep
        implements IOmsOrderProcessStep<IpJingdongRefundRelation> {
    @Override
    public ProcessStepResult<IpJingdongRefundRelation> startProcess(IpJingdongRefundRelation orderInfo,
                                                                    ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        IpBJingdongRefund jingdongRefund = orderInfo.getJingdongRefund();
        try {
            IpBJingdongOrder ipJingdongOrder = orderInfo.getIpJingdongOrder();
            if (ipJingdongOrder == null) {
                ipJingdongRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(), SysNotesConstant.SYS_REMARK21, jingdongRefund);
                return new ProcessStepResult<>(StepStatus.FINISHED,
                        "单据" + orderInfo.getOrderNo() + "转换完成");
            } else {
                boolean existSkuFlag = ipJingdongRefundService.transformOriginalOrder(jingdongRefund, ipJingdongOrder, orderInfo);
                if (!existSkuFlag) {
                    return new ProcessStepResult<>(StepStatus.FINISHED, "单据" + orderInfo.getOrderNo() + "转换完成");
                }
            }
            return new ProcessStepResult<>(StepStatus.SUCCESS);
        } catch (Exception e) {
            log.error(LogUtil.format("京东退单转换异常:{}", "京东退单转换异常"), Throwables.getStackTraceAsString(e));
            //修改中间表状态及系统备注
            ipJingdongRefundService.updateRefundIsTransError(jingdongRefund,e.getMessage());
            return new ProcessStepResult<>(StepStatus.FAILED, "退单转换异常：" + e.getMessage());
        }
    }
}
