package com.jackrain.nea.oc.oms.step;

import com.jackrain.nea.log.LogEvent;
import lombok.Data;

import java.util.List;

/**
 * 步骤处理结果
 *
 * @param <T> 泛型数据类型
 * @author: 易邵峰
 * @since: 2019-01-18
 * create at : 2019-01-18 14:15
 */
@Data
public class ProcessStepResult<T> {

    /**
     * 每个步骤的结果状态
     */
    private StepStatus status;

    /**
     * 操作内容对象
     */
    private Object nextStepOperateObj;

    /**
     * 操作结果消息
     */
    private String message;

    /**
     * 下个步骤的Class内容
     */
    private Class<? extends IOmsOrderProcessStep<T>> nextStepClass;

    /**
     * 当前步骤
     */
    private IOmsOrderProcessStep currentStep;

    private List<LogEvent> logEventList;

    public ProcessStepResult() {

    }

    public ProcessStepResult(StepStatus stepStatus) {
        this.status = stepStatus;
    }

    public ProcessStepResult(StepStatus stepStatus, String message) {
        this.status = stepStatus;
        this.message = message;
    }

    public ProcessStepResult(StepStatus stepStatus, Object data) {
        this.status = stepStatus;
        this.nextStepOperateObj = data;
    }

    public ProcessStepResult(StepStatus stepStatus, Object data,
                             Class<? extends IOmsOrderProcessStep<T>> nextStepClass) {
        this.status = stepStatus;
        this.nextStepOperateObj = data;
        this.nextStepClass = nextStepClass;
    }

    public ProcessStepResult(StepStatus stepStatus, Object data, String message,
                             Class<? extends IOmsOrderProcessStep<T>> nextStepClass) {
        this.status = stepStatus;
        this.nextStepOperateObj = data;
        this.message = message;
        this.nextStepClass = nextStepClass;
    }
}
