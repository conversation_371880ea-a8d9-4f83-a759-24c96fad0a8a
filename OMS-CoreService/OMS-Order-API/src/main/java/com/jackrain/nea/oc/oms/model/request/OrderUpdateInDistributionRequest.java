package com.jackrain.nea.oc.oms.model.request;

import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.web.face.User;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @author: hulinyang
 * @since: 2019/4/29
 * create at : 2019/4/29 10:33
 */
@Data
public class OrderUpdateInDistributionRequest implements Serializable {
    private ChannelType channelType;

    private User operateUser;

    private List<Long> orderIdList;

    private Long orderId;

}