package com.jackrain.nea.oc.oms.util;


import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * @Auther: chenhao
 * @Date: 2021-12-15 11:11
 * @Description:
 */

@Slf4j
@Component
public class OmsPlatformUtil {

    //系统参数分隔符
    private final static String R3_SPLIT = ",";

    @Value("${r3_oms_special_type_order:119}")
    private String param;


    /**
     * 判断平台是否是特殊平台类型
     *
     * @param platformId 平台
     * @return 是：true 否：false
     */
    public Boolean isSpecialPlatform(Long platformId) {
        //是否是特殊平台 false: 否  true:是
        boolean isSpecialPlatform = Boolean.FALSE;

        //获取系统参数 特殊转单平台
        log.info("System parameters r3_oms_special_type_order={}", param);

        if (StringUtils.isNotEmpty(param)) {
            List<String> specialPlatformList = Arrays.asList(param.split(R3_SPLIT));
            isSpecialPlatform = specialPlatformList.indexOf(platformId.toString()) >= 0 ? Boolean.TRUE : Boolean.FALSE;
        }

        return isSpecialPlatform;
    }
}
