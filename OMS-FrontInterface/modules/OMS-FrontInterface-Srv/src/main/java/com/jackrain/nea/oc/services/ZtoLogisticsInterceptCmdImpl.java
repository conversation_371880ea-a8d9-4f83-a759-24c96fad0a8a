package com.jackrain.nea.oc.services;

import com.jackrain.nea.oc.oms.api.ZtoLogisticsInterceptCmd;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.services.ZtoLogisticsInterceptService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/3/2 17:25
 * @Description
 */
@Component
public class ZtoLogisticsInterceptCmdImpl implements ZtoLogisticsInterceptCmd {

    @Resource
    private ZtoLogisticsInterceptService ztoLogisticsInterceptService;

    @Override
    public ValueHolderV14<List<OcBOrder>> interceptCheck(List<Long> orderIds) {
        return ztoLogisticsInterceptService.interceptCheck(orderIds);
    }

    @Override
    public ValueHolderV14<Void> interceptCreate(List<Long> orderIds, String interceptReason, User user, String refundNo) {
        return ztoLogisticsInterceptService.interceptCreate(orderIds, interceptReason, user, refundNo);
    }

    @Override
    public ValueHolderV14<Void> interceptCancel(List<Long> ids, User user) {
        return ztoLogisticsInterceptService.interceptCancel(ids, user);
    }
}
