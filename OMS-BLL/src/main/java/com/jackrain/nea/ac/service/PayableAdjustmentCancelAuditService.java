package com.jackrain.nea.ac.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.oc.oms.model.constant.AcConstant;
import com.jackrain.nea.ac.model.AcFPayableAdjustmentDO;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.ac.AcFPayableAdjustmentItemMapper;
import com.jackrain.nea.oc.oms.mapper.ac.AcFPayableAdjustmentMapper;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.AcBeanUtils;
import com.jackrain.nea.util.AcPayableAdjustmentPushESUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

@Component
@Slf4j
@Transactional
public class PayableAdjustmentCancelAuditService extends CommandAdapter {
    @Autowired
    private AcFPayableAdjustmentMapper acFPayableAdjustmentMapper;

    @Autowired
    private AcFPayableAdjustmentItemMapper acFPayableAdjustmentItemMapper;

    /**
     * 反审核
     *
     * @param querySession 参数封装
     * @return 返回状态
     * @throws NDSException 异常信息
     */
    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        ValueHolder valueHolder = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);

        if (param != null) {
            JSONArray itemArray = AcBeanUtils.makeUnAuditJsonArray(param);

            valueHolder = cancelAuditFunction(itemArray, valueHolder, querySession);
        } else {
            throw new NDSException("参数为空！");
        }
        return valueHolder;
    }

    private JSONObject checkStatus(AcFPayableAdjustmentDO acFPayableAdjustmentDO, Long objId, JSONObject errJo) {
        int iStatus = acFPayableAdjustmentDO.getBillStatus();
        if (iStatus != AcConstant.CON_BILL_STATUS_02) {
            errJo.put("objid", objId);
            errJo.put("message", "单据处于已客审状态才能进行反审！");
            return errJo;
        }

        return null;
    }

    private ValueHolder cancelAuditFunction(JSONArray itemArray, ValueHolder valueHolder, QuerySession querySession) {
        //反审核记录前，先判断是否存在
        if (itemArray.size() > 0) {
            JSONArray errorArray = new JSONArray();
            for (int i = 0; i < itemArray.size(); i++) {
                JSONObject errJo = new JSONObject();
                Long objId = itemArray.getLong(i);
                AcFPayableAdjustmentDO acFPayableAdjustmentDO = acFPayableAdjustmentMapper.selectById(objId);

                if (acFPayableAdjustmentDO != null) {
                    JSONObject errorJson = checkStatus(acFPayableAdjustmentDO, objId, errJo);
                    if (errorJson != null) {
                        errorArray.add(errorJson);
                    }
                    JSONObject jsonObject = new JSONObject();

                    //修改人id
                    jsonObject.put("MODIFIERID", Long.valueOf(querySession.getUser().getId()));
                    //修改人用户名
                    jsonObject.put("MODIFIERNAME", querySession.getUser().getName());
                    //修改人名称
                    jsonObject.put("MODIFIERENAME", querySession.getUser().getEname());
                    //修改时间
                    jsonObject.put("MODIFIEDDATE", new Date());

                    //反审-客审置空信息
                    AcBeanUtils.makeCancelField(jsonObject);

                    //1:未审核 2:已客审 3:已财审 4:已作废
                    jsonObject.put("ID", objId);
                    jsonObject.put("BILL_STATUS", AcConstant.CON_BILL_STATUS_01);

                    int iResult = acFPayableAdjustmentMapper.updateAtrributes(jsonObject);
                    //推送ES
                    AcPayableAdjustmentPushESUtil.pushOrder(objId);
                    if (iResult < 0) {
                        errJo.put("objid", objId);
                        errJo.put("message", "单据编码:" + acFPayableAdjustmentDO.getBillNo() + ",反审核失败！");
                        errorArray.add(errJo);
                    }
                } else {
                    //记录不存在
                    errJo.put("objid", objId);
                    errJo.put("message", "当前记录不存在！");
                    errorArray.add(errJo);
                }
            }
            valueHolder = AcBeanUtils.getProcessValueHolder(itemArray, errorArray, "反审核");
            return valueHolder;
        }
        return valueHolder;
    }

}
