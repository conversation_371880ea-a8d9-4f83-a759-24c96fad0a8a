package com.jackrain.nea.oc.oms.api;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

/**
 * 退货入库单列表
 *
 * @author: 夏继超
 * @since: 2019/4/1
 * create at : 2019/4/1 11:00
 */
public interface ReturnStorageListCmd {
    /**
     * 退货入库单列表查询
     *
     * @param object 传输的查询条件
     * @param user   当前用户
     * @return 返回的值
     */
    ValueHolderV14 returnStorageList(String object, User user);

    /**
     * 退货入库单 的新增编辑
     *
     * @param obj  传入的参数
     * @param user 当前用户
     * @return 返回的数据
     */
    ValueHolderV14 returnStorageSave(JSONObject obj, User user) throws NDSException;

    /**
     * 退货入库单手工匹配前校验
     *
     * @param obj  传入的参数
     * @param user 当前的用户
     * @return 返回的数据
     */
    ValueHolderV14 manualMatchingCheck(JSONObject obj, User user);

    /**
     * 手工匹配页面
     *
     * @param obj
     * @param user
     * @return
     */
    ValueHolderV14 manualMatchingList(JSONObject obj, User user);

    /**
     * 手工匹配明细搜索按钮服务
     *
     * @param obj  传入的参数
     * @param user 当前用户
     * @return
     */
    ValueHolderV14 searchButtonsInDetail(String obj, User user);

    /**
     * 手工匹配弹出确认按钮服务
     *
     * @param param 传入的参数
     * @param user  当前用户
     * @return
     */
    ValueHolderV14 manualMatchingConfirmationButton(JSONObject param, User user) throws NDSException;

    /**
     * 手工匹配列表确认按钮服务
     *
     * @param param 传入的参数
     * @param user  当前用户
     * @return 返回的数据
     */
    ValueHolderV14 markSureButton(JSONObject param, User user) throws NDSException;

    /**
     * 新增的搜索页面的确定按钮
     *
     * @param param 传入的参数
     * @param user  当前用户
     * @return 返货的参数
     */
    ValueHolderV14 saveButton(JSONObject param, User user);

    /**
     * 强制匹配服务
     *
     * @param param
     * @param user  当前登录用户
     * @return
     */
    ValueHolderV14 forcedMatching(JSONObject param, User user) throws NDSException;

    /***
     *  强制匹配搜索列表确定按钮
     */
    ValueHolderV14 seachForced(JSONObject param, User user) throws NDSException;

    /**
     * @param param
     * @param user
     * @return
     */
    ValueHolderV14 forcedCompletion(JSONObject param, User user);

    /**
     * 确认按钮 - 服务
     * @param param
     * @param user
     * @return
     */
    ValueHolderV14 returnOrderConfirm(JSONObject param, User user);

    /**
     * 确认按钮 - 二次验证
     *
     * @param param
     * @return
     */
    ValueHolderV14 returnOrderSecondaryVerify(JSONObject param);

    ValueHolderV14 cutInLine(JSONObject param, User user);
}
