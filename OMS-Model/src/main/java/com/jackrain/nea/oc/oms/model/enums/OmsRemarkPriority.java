package com.jackrain.nea.oc.oms.model.enums;

/**
 * 备注优先级
 *
 * @author: 胡林洋
 * @since: 2019-03-14
 * create at : 2019-03-14 20:52
 */
public enum OmsRemarkPriority {
    /**
     * 卖家备注优先 ：1
     */
    SELLER_PRIORITY,

    /**
     * 买家备注优先 ：2
     */
    BUYER_PRIORITY;

    public String parseValue() {
        if (this == SELLER_PRIORITY) {
            return "1";
        } else if (this == BUYER_PRIORITY) {
            return "2";
        } else {
            return "0";
        }
    }

}
