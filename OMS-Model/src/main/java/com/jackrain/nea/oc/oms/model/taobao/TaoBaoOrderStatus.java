package com.jackrain.nea.oc.oms.model.taobao;

/**
 * 淘宝订单状态
 *
 * @author: 易邵峰
 * @since: 2019-01-21
 * create at : 2019-01-21 12:58
 */
public class TaoBaoOrderStatus {

    private TaoBaoOrderStatus() {

    }

    /**
     * 待发货
     */
    public static final String WAIT_SELLER_SEND_GOODS = "WAIT_SELLER_SEND_GOODS";

    /**
     * 待买家付款
     */
    public static final String WAIT_BUYER_PAY = "WAIT_BUYER_PAY";

    /**
     * 淘宝预售.尾款已付
     */
    public static final String FRONT_PAID_FINAL_PAID = "FRONT_PAID_FINAL_PAID";

    /**
     * 淘宝预售.尾款未付
     */
    public static final String FRONT_PAID_FINAL_NOPAID = "FRONT_PAID_FINAL_NOPAID";

    /**tatusPayStep();
     if (TaoBaoOrderStatus.FRONT_PAI
     * 淘宝预售.订金未付尾款未付
     */
    public static final String FRONT_NOPAID_FINAL_NOPAID = "FRONT_NOPAID_FINAL_NOPAID";


    public static final String OTHER_STATUS = "";

    /**
     * 完成
     */
    public static final String TRADE_FINISHED = "TRADE_FINISHED";

    /**
     * 交易关闭
     */
    public static final String TRADE_CLOSED = "TRADE_CLOSED";

    /**
     * 交易取消
     *
     * @20200724 通用平台的交易取消状态
     */
    public static final String TRADE_CANCELED = "TRADE_CANCELED";

    /**
     * 交易关闭
     */
    public static final String TRADE_CLOSED_BY_TAOBAO = "TRADE_CLOSED_BY_TAOBAO";

    /**
     * 等待买家确认收货
     */
    public static final String WAIT_BUYER_CONFIRM_GOODS = "WAIT_BUYER_CONFIRM_GOODS";

    /**
     * 退款完成
     */
    public static final String REFUND_FINISHED = "REFUND_FINISHED";

    /**
     * 部分发货
     */
    public static final String SELLER_CONSIGNED_PART = "SELLER_CONSIGNED_PART";

    /**  通用转单单据状态
     买家已付款  等待卖家发货 WAIT_SELLER_SEND_GOODS
     卖家已发货  等待买家收货  WAIT_BUYER_CONFIRM_GOODS
     订单完成  TRADE_FINISHED
     订单取消  TRADE_CANCELED
     退款完成 REFUND_FINISHED
     */

    /**
     * 建议预下沉状态
     */
    public static final String SUGGEST_PRESINK_STATUS_Y = "Y";
    public static final String SUGGEST_PRESINK_STATUS_N = "N";

    /**
     * 实际预下沉状态 1 未通知 2 通知中  3 已通知  4 已完成  5 取消中 6取消成功 7 取消失败'
     */
    public static final String ACTUAL_PRESINK_STATUS_NOT_NOTIFIED = "1";
    public static final String ACTUAL_PRESINK_STATUS_NOTIFIED = "2";
    public static final String ACTUAL_PRESINK_STATUS_NOTIFIED_SUCCESS = "3";
    public static final String ACTUAL_PRESINK_STATUS_COMPLETED = "4";
    public static final String ACTUAL_PRESINK_STATUS_CANCELING = "5";
    public static final String ACTUAL_PRESINK_STATUS_CANCELING_SUCCESS = "6";
    public static final String ACTUAL_PRESINK_STATUS_CANCELING_FALL = "7";
}
