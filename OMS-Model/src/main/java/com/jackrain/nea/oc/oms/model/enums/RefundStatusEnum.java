package com.jackrain.nea.oc.oms.model.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: gxx
 * @since: 2020-05-28
 * create at : 2020-05-28 14:46
 */


public enum RefundStatusEnum {
    /**
     * 全部退款
     */
    ALL_PAY(0, "全部退款"),

    /**
     * 部分退款
     */
    PART_PAY(1, "部分退款"),

    /**
     * 未退款
     */
    NO_PAY(2, "未退款");

    Integer key;
    String val;

    RefundStatusEnum(Integer k, String v) {
        this.key = k;
        this.val = v;
    }

    public Integer getKey() {
        return key;
    }

    public String getVal() {
        return val;
    }

    public static String getValueByKey(Integer key) {

        String s = "";
        if (key == null) {
            return s;
        }
        for (RefundStatusEnum e : RefundStatusEnum.values()) {
            if (e.getKey().equals(key)) {
                s = e.getVal();
                break;
            }
        }
        return s;
    }

    public static Map<Integer, String> getMap() {
        Map<Integer, String> m = new HashMap<>();
        for (RefundStatusEnum o : RefundStatusEnum.values()) {
            m.put(o.getKey(), o.getVal());
        }
        m.put(null, "");
        return m;
    }

}
