package com.jackrain.nea.oc.oms.process.transfer.impl.normal.jitx.step;

import com.google.common.collect.Lists;
import com.alibaba.fastjson.JSON;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.jitx.JitxOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpJitxOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBJitxOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 检查订单是否已经存在
 *
 * @author: 黄超
 * @since: 2019-06-26
 * create at : 2019-06-26 9:00
 */
@Step(order = 20, description = "检查中间表订单是否已经存在")
@Slf4j
@Component
public class Step20JitxCheckOmsOrderExist extends BaseJitxOrderProcessStep
        implements IOmsOrderProcessStep<IpJitxOrderRelation> {


    @Override
    public ProcessStepResult<IpJitxOrderRelation> startProcess(IpJitxOrderRelation orderInfo,
                                                               ProcessStepResult preStepResult,
                                                               boolean isAutoMakeup, User operateUser) {
        ProcessStepResult<IpJitxOrderRelation> stepResult = new ProcessStepResult<>();
        String orderSn = orderInfo.getJitxOrder().getOrderSn();
        if (log.isDebugEnabled()) {
            log.debug("自动流程----Step20JitxCheckOmsOrderExist [检查中间表订单是否已经存在]{}", orderSn);
        }
        String orderStatus = orderInfo.getJitxOrder().getOrderStatus();
        if (JitxOrderStatus.ORDER_ALREADY_SEND.equalsIgnoreCase(orderStatus)
                || JitxOrderStatus.ORDER_ALREADY_COLLECTED.equalsIgnoreCase(orderStatus)) {
            //JITX订单已发货、已揽收状态的标记为已转换
            this.ipJitxOrderService.updateJitxOrderTransStatus(orderInfo.getOrderNo(),
                    TransferOrderStatus.TRANSFERRED, "已发货、已揽收状态的标记为已转换，退出转换！");
            return new ProcessStepResult<>(StepStatus.FINISHED, "已发货、已揽收状态的标记为已转换，退出转换！");
        }
        boolean isJitxNormalStatus = JitxOrderStatus.ORDER_ALREADY_AUDITED.equalsIgnoreCase(orderStatus);

        String orderNo = orderInfo.getOrderNo();
        long orderId = orderInfo.getOrderId();
        List<OcBOrder> findOrderInfoList = orderService.selectOmsOrderInfoList(orderSn);

        if (isJitxNormalStatus) {
            //更新数据，“转换状态”=1
            IpBJitxOrder jitxOrder = orderInfo.getJitxOrder();
            jitxOrder.setIstrans(TransferOrderStatus.TRANSFERRING.toInteger());
            ipJitxOrderService.updateIpJitxOrderInfo(jitxOrder);

            stepResult.setStatus(StepStatus.SUCCESS);
            if (CollectionUtils.isNotEmpty(findOrderInfoList)) {
                if (log.isDebugEnabled()) {
                    log.debug("[Step20JitxCheckOmsOrderExist.selectOmsOrderInfoList]{}", orderSn);
                }
                stepResult.setMessage("订单状态=" + orderStatus + ";单据编号=" + orderNo + "已存在. OrderId=" + orderId
                        + ";OrderNo=" + orderNo + " 进入判断订单地址阶段。");
                Optional<OcBOrder> maxIdOrder = findOrderInfoList.stream()
                        .filter(x -> !(OmsOrderStatus.CANCELLED.toInteger().equals(x.getOrderStatus()) || OmsOrderStatus.SYS_VOID.toInteger().equals(x.getOrderStatus())))
                        .max(Comparator.comparing(OcBOrder::getId));
                stepResult.setNextStepOperateObj(Lists.newArrayList(maxIdOrder.get()));
                stepResult.setNextStepClass(Step65JitxOrderUpdate.class);
            } else {
                stepResult.setMessage("订单状态=" + orderStatus + ";单据编号=" + orderNo + "，进入下一阶段");
            }
        } else {
            String errorMessage = "订单状态=" + orderStatus + ";不进入转单. OrderId=" + orderId
                    + ";OrderNo=" + orderNo;
            stepResult.setMessage(errorMessage);
            stepResult.setStatus(StepStatus.FAILED);
        }

        return stepResult;
    }
}
