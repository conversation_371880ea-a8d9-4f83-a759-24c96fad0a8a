package com.jackrain.nea.oc.oms.process.transfer.impl.normal.taobaofx.step;

import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.MakeupOrderType;
import com.jackrain.nea.oc.oms.services.IpTaobaoFxService;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.util.LockOrderType;

import com.jackrain.nea.rpc.PsRpcService;
import org.springframework.beans.factory.annotation.Autowired;


/**
 * 基础淘宝Order处理阶段
 *
 * @author: 周琳胜
 * @since: 2019-07-10
 * create at : 2019-07-010 14:13
 */
public abstract class BaseTaobaoFxOrderProcessStep {


    @Autowired
    protected IpTaobaoFxService ipTaobaoFxService;

    @Autowired
    protected OmsOrderService orderService;

    @Autowired
    protected PsRpcService psRpcService;

    @Autowired
    protected OmsOrderService omsOrderService;

    protected ChannelType getCurrentChannelType() {
        return ChannelType.TAOBAOFX;
    }

    protected MakeupOrderType getCurrentMakeupOrderType() {
        return MakeupOrderType.TRANSFER_ORDER;
    }

    protected LockOrderType getCurrentLockOrderType() {
        return LockOrderType.TRANSFER_FX_TAOBAO_ORDER;
    }


}
