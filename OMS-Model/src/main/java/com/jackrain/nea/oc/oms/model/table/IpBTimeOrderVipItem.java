package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 唯品会时效订单商品明细表
 */
@TableName(value = "ip_b_time_order_vip_item")
@Data
@Document(index = "ip_b_time_order_vip_item", type = "ip_b_time_order_vip_item")
public class IpBTimeOrderVipItem extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    /**
     * 专场编号
     */
    @JSONField(name = "SALES_NO")
    @Field(type = FieldType.Keyword)
    private String salesNo;

    /**
     * 平台仓库编码
     */
    @JSONField(name = "WAREHOUSE")
    @Field(type = FieldType.Keyword)
    private String warehouse;

    /**
     * 发货仓库id
     */
    @JSONField(name = "WAREHOUSE_ID")
    @Field(type = FieldType.Long)
    private Long warehouseId;

    /**
     * 发货仓库code
     */
    @JSONField(name = "WAREHOUSE_CODE")
    @Field(type = FieldType.Keyword)
    private String warehouseCode;

    /**
     * 发货仓库名称
     */
    @JSONField(name = "WAREHOUSE_NAME")
    @Field(type = FieldType.Keyword)
    private String warehouseName;

    /**
     * 平台仓库编码
     */
    @JSONField(name = "COOPERATION_NO")
    @Field(type = FieldType.Keyword)
    private String cooperationNo;

    /**
     * 销售来源
     */
    @JSONField(name = "SALES_SOURCE_INDICATOR")
    @Field(type = FieldType.Keyword)
    private String salesSourceIndicator;

    /**
     * 品牌id
     */
    @JSONField(name = "BRAND_ID")
    @Field(type = FieldType.Long)
    private Long brandId;

    /**
     * 数量
     */
    @JSONField(name = "AMOUNT")
    @Field(type = FieldType.Double)
    private BigDecimal amount;

    /**
     * 商品条码
     */
    @JSONField(name = "BARCODE")
    @Field(type = FieldType.Keyword)
    private String barcode;

    /**
     * 时效订单头表关联id
     */
    @JSONField(name = "IP_B_TIME_ORDER_VIP_ID")
    @Field(type = FieldType.Long)
    private Long ipBTimeOrderVipId;

    /**
     * 下单时间
     */
    @JSONField(name = "CREATE_TIME")
    @Field(type = FieldType.Long)
    private Date createTime;

    /**
     * 合作模式
     */
    @JSONField(name = "COOPERATION_MODE")
    @Field(type = FieldType.Keyword)
    private String cooperationMode;

    /**
     * 是否占单（未启用）
     */
    @JSONField(name = "IS_HOLD_BILL")
    @Field(type = FieldType.Integer)
    private Integer isHoldBill;

    /**
     * 缺货数量
     */
    @JSONField(name = "OUT_STOCK_QUANTITY")
    @Field(type = FieldType.Double)
    private BigDecimal outStockQuantity;

    /**
     * 创建者
     */
    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    /**
     * 最后更新者
     */
    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;
}