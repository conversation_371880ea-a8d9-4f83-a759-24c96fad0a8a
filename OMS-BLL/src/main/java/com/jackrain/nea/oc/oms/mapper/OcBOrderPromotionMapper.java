package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.relation.MergeOrderPromotionQbModel;
import com.jackrain.nea.oc.oms.model.table.OcBOrderPayment;
import com.jackrain.nea.oc.oms.model.table.OcBOrderPromotion;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;

@Mapper
@Component
public interface OcBOrderPromotionMapper extends ExtentionMapper<OcBOrderPromotion> {

    @Select("<script> "
            + " SELECT ID AS ORDER_ID,* FROM OC_B_ORDER_PROMOTION WHERE "
            + "<foreach item='item' index='index' collection='ids' separator='or'>"
            + " ID= #{item} "
            + "</foreach>"
            + "ORDER BY PROMOTION_DESC,GIFT_ITEM_CODE, GIFT_ITEM_NAME, PROMOTION_NAME,AMT_DISCOUNT,GIFT_ITEM_QTY,OC_B_ORDER_ID "
            + "</script>")
    List<MergeOrderPromotionQbModel> selectMergeOrderPromotionQbByIds(@Param("ids") List<Integer> ids);

    /**
     * 整单优惠明细
     * @param orderId 订单ID
     * @return
     */
    @Select("SELECT * FROM OC_B_ORDER_PROMOTION WHERE oc_b_order_id=#{orderId} and isactive='Y' ")
    List<OcBOrderPromotion> selectOcBOrderPromotionList(long orderId);
}