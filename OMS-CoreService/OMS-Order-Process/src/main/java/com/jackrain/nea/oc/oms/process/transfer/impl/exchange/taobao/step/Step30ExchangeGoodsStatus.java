package com.jackrain.nea.oc.oms.process.transfer.impl.exchange.taobao.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoExchangeRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoExchange;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoExchangeOrderExt;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


/**
 * 状态为 //换货关闭(4),请退款(14)
 *
 * <AUTHOR>
 */
@Step(order = 30, description = "判断淘宝换货状态")
@Slf4j
@Component
public class Step30ExchangeGoodsStatus extends BaseTaobaoExchangeProcessStep
        implements IOmsOrderProcessStep<IpTaobaoExchangeRelation> {
    @Override
    public ProcessStepResult<IpTaobaoExchangeRelation> startProcess(IpTaobaoExchangeRelation orderInfo,
                                                                    ProcessStepResult preStepResult,
                                                                    boolean isAutoMakeup, User operateUser) {
        IpBTaobaoExchange taobaoExchange = orderInfo.getTaobaoExchange();
        try {
            String status = taobaoExchange.getStatus();
            OcBOrder originalValidOrderInfo = orderInfo.getOriginalValidOrderInfo();
            // 状态=换货关闭；请退款状态
            if (TaobaoExchangeOrderExt.ExchangeOrderCentreStatus.EXCHANGE_CLOSE.getName().equals(status)
                    || TaobaoExchangeOrderExt.ExchangeOrderCentreStatus.REFUND_PLEASE.getName().equals(status)) {
                //判断原单是否存在
                if (originalValidOrderInfo == null) {
                    return new ProcessStepResult<>(StepStatus.SUCCESS, null, Step050OriginalOrderNoExist.class);
                } else {
                    //判断是否为福袋商品
                    Boolean isFortuneBag = orderInfo.getIsFortuneBag();
                    if (isFortuneBag) {
                        //更新为已转换
                        ipTaobaoExchangeService.updateExchangeIsTransTransferred(taobaoExchange);
                        String message = "当前条码为福袋商品,不允许换货,标记已转换";
                        return new ProcessStepResult<>(StepStatus.FINISHED, message);
                    }
                    ipTaobaoExchangeService.exchangeStatusCloseAndRefund(orderInfo, operateUser);
                    return new ProcessStepResult<>(StepStatus.FINISHED);
                }
            }
            return new ProcessStepResult<>(StepStatus.SUCCESS);
        } catch (Exception e) {
            ipTaobaoExchangeService.updateExchangeIsTransError(taobaoExchange, e.getMessage());
            log.error(this.getClass().getName() + " 退换货转换异常", e);
            String errorMessage = "退换货转换异常!" + e.getMessage();
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
    }
}
