package com.jackrain.nea.oc.services.naika;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.api.naika.OmsNaiKaOrderVoidCmd;
import com.jackrain.nea.oc.oms.model.constant.OcCommonConstant;
import com.jackrain.nea.oc.oms.services.naika.OmsNaiKaOrderVoidService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.CommandAdapterUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName OmsNaiKaOrderApiCmdImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/7/11 10:40
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OmsNaiKaOrderVoidCmdImpl extends CommandAdapter implements OmsNaiKaOrderVoidCmd {

    @Autowired
    private OmsNaiKaOrderVoidService omsNaiKaOrderVoidService;

    @Override
    public ValueHolder execute(QuerySession querySession) {
        ValueHolder valueHolder = CommandAdapterUtil.checkDeleteSession(querySession, OcCommonConstant.AC_F_CUSTOMER_PACKAGE_FEE);
        if (!valueHolder.isOK()) {
            return valueHolder;
        }
        User user = querySession.getUser();
        ValueHolder vh = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(event.getParameterValue("param").toString());
        JSONObject subParam = JSON.parseObject(param.get("subParam").toString());

        // 判断表名
        String tableName = (String) subParam.get("table");
        if (ObjectUtil.notEqual(tableName, "OC_B_NAIKA_RETURN_AF_SEND_ITEM")) {
            vh.put("code", ResultCode.FAIL);
            vh.put("message", "明细表信息有误");
            return vh;
        }
        JSONArray jsonArray = JSON.parseArray(param.get("idArr").toString());
        Long id = param.getLong("ID");
        List<Long> naiKaOrderIds = new ArrayList<>();
        for (Object object : jsonArray) {
            naiKaOrderIds.add((Long) object);
        }
        omsNaiKaOrderVoidService.naiKaOrderVoid(naiKaOrderIds, id, user);
        vh.put("code", ResultCode.SUCCESS);
        vh.put("message", "success");
        return vh;
    }
}
