package com.jackrain.nea.oc.oms.model.enums;

/**
 * 快递否定值
 *
 * @author: 胡林洋
 * @since: 2019-03-16
 * create at : 2019-03-16 15:06
 */
public enum OmsSysParam {
    /**
     * 快递否定值
     */
    EXPRESS_NEGATIVE_VALUE,
    /**
     * 买卖家备注优先级
     */
    REMARK_PRIORITY_NAME,
    /**
     * 物流公司关键字
     */
    LOGITSICS_KEY_WORD,

    /**
     * 是否排除物流公司
     */
    REMOVE_LOGITSICS_FLAG,

    /**
     * 京东平台店铺不发的物流公司
     */
    JD_NO_SEND_LOGITSICS,

    /**
     * 京东平台货到付款不发的仓库
     */
    JD_NO_SEND_WAREHOUSE,

    /**
     * 是否排除物流公司
     */
    TB_NO_SEND_LOGITSICS;


    public String parseValue() {
        if (this == EXPRESS_NEGATIVE_VALUE) {
            return "expressNegativeValue";
        } else if (this == REMARK_PRIORITY_NAME) {
            return "remarkPriorityName";
        } else if (this == LOGITSICS_KEY_WORD) {
            return "logitsicsKeyWord";
        } else if (this == REMOVE_LOGITSICS_FLAG) {
            return "removeLogitsicsFlag";
        } else if (this == JD_NO_SEND_LOGITSICS) {
            return "jdNoSendLogistics";
        } else if (this == TB_NO_SEND_LOGITSICS) {
            return "tbNoSendLogistics";
        } else if (this == JD_NO_SEND_WAREHOUSE) {
            return "jdNoSendWareHouse";
        } else {
            return "";
        }
    }

}
