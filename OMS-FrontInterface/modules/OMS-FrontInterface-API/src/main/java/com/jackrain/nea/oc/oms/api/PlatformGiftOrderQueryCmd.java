package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.oc.oms.model.request.PlatformGiftOrderRequest;
import com.jackrain.nea.oc.oms.model.result.PlatformGiftOrderResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;

import java.util.List;

/**
 * @program: ryytn-oc-oms-v3.0
 * @description: 通过平台单号获取订单赠品信息
 * @author: haiyang
 * @create: 2024-01-15 14:41
 **/
public interface PlatformGiftOrderQueryCmd {

    ValueHolderV14<List<PlatformGiftOrderResult>> queryByPlatformOrderNos(PlatformGiftOrderRequest request);
}
