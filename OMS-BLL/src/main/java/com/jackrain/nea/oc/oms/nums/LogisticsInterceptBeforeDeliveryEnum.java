package com.jackrain.nea.oc.oms.nums;

import lombok.Getter;

/**
 * @ClassName LogisticsInterceptBeforeDeliveryEnum
 * @Description 是否派件前拦截
 * <AUTHOR>
 * @Date 2024/4/23 10:16
 * @Version 1.0
 */
public enum LogisticsInterceptBeforeDeliveryEnum {

    BEFORE_DELIVERY(1, "是"),
    AFTER_DELIVERY(2, "否"),
    NO_TRACE(3, "无轨迹"),
    QUERY_ERROR(4, "查询异常");

    @Getter
    private final Integer key;
    @Getter
    private final String desc;

    LogisticsInterceptBeforeDeliveryEnum(Integer key, String desc) {
        this.key = key;
        this.desc = desc;
    }
}
