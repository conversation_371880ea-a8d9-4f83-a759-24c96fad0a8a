package com.jackrain.nea.oc.oms.api;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.request.o2o.SplitOrderRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

/**
 * sap api
 *
 * @Desc :
 * <AUTHOR> gale
 * @Date : 2020/8/13
 */
public interface OrderPodHandleCmd {

    /**
     * pod签收
     *
     * @param params params
     * @return v14
     */
    ValueHolderV14 podHandle(JSONObject params);


    /**
     * pod签收
     *
     * @param params params
     * @return v14
     */
    ValueHolderV14 cancelOrder(JSONObject params);

    /**
     * pod签收
     *
     * @param params params
     * @return v14
     */
    ValueHolderV14 cancelReturn(JSONObject params);

    /**
     * 411T订单取消
     *
     * @param params params
     * @return v14
     */
    ValueHolderV14 cancel411TReturn(JSONObject params);
}
