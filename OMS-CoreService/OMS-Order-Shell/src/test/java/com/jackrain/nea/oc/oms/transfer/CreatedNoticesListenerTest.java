package com.jackrain.nea.oc.oms.transfer;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.OmsOrderProcessApplication;
import com.jackrain.nea.oc.oms.task.refundorder.AutoRefundInMakeUpTask;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * Description： 消息监听单元测试
 * Author: RESET
 * Date: Created in 2020/8/25 21:42
 * Modified By:
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = OmsOrderProcessApplication.class)
@Slf4j
public class CreatedNoticesListenerTest {

    @Autowired
    AutoRefundInMakeUpTask receiptBackMq;

    @Test
    public void testConsume() {
        receiptBackMq.execute(new JSONObject());
    }

    private Object buildBody() {
        JSONObject order = new JSONObject();
        order.put("method", "returnorder.confirm");

        JSONObject request = new JSONObject();
        JSONObject returnOrder = new JSONObject();
        returnOrder.put("returnOrderCode", "AXOR202008280936");

        request.put("returnOrder", returnOrder);
        order.put("request", request);

        return order;
    }

}
