package com.jackrain.nea.oc.oms.service;

import com.jackrain.nea.oc.oms.mapper.task.DrdsHintDatas;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Profile;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR> 孙勇生
 * create at:  2020/3/10  13:51
 * @description: 测试 show语言
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest()
@Profile(value = "dev")
public class ShowDatasTest {


    @Autowired
    private DrdsHintDatas drdsHintDatas;

    @Test
    public void showdata1Test() {

        List<HashMap> list = drdsHintDatas.getNodeMap();
        if (null != list) {
            for (HashMap map : list) {
                System.out.println("NAME :" + map.get("NAME"));
            }
        }
    }

}
