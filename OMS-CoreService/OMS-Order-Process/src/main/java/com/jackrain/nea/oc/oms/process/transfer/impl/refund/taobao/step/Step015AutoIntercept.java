package com.jackrain.nea.oc.oms.process.transfer.impl.refund.taobao.step;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsTaobaoRefundRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoOrder;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.taobao.TaoBaoOrderStatus;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.services.ZtoLogisticsInterceptService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName Step025AutoIntercept
 * @Description 订单自动拦截
 * <AUTHOR>
 * @Date 2023/11/11 17:47
 * @Version 1.0
 */
@Step(order = 15, description = "订单自动拦截")
@Slf4j
@Component
public class Step015AutoIntercept extends BaseTaobaoRefundProcessStep
        implements IOmsOrderProcessStep<OmsTaobaoRefundRelation> {

    @Autowired
    private ZtoLogisticsInterceptService logisticsInterceptService;
    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Override
    public ProcessStepResult<OmsTaobaoRefundRelation> startProcess(OmsTaobaoRefundRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Step015AutoIntercept.startProcess.orderInfo:{};",
                    "Step015AutoIntercept"), JSON.toJSONString(orderInfo));
        }
        //获取订单信息
        List<OmsOrderRelation> omsOrderRelation = orderInfo.getOmsOrderRelation();
        List<OmsOrderRelation> isGiftOrderRelation = orderInfo.getIsGiftOrderRelation();
        IpBTaobaoOrder taobaoOrder = orderInfo.getIpBTaobaoOrder();
        IpBTaobaoRefund taobaoRefund = orderInfo.getIpBTaobaoRefund();

        if (StringUtils.isEmpty(taobaoRefund.getStatus())) {
            return new ProcessStepResult<>(StepStatus.SUCCESS, "售后状态不符合自动拦截");

        }
        if (!(ObjectUtil.equal(taobaoRefund.getStatus(), TaobaoReturnOrderExt.RefundStatus.WAIT_SELLER_AGREE.getCode()) ||
                ObjectUtil.equal(taobaoRefund.getStatus(), TaobaoReturnOrderExt.RefundStatus.WAIT_BUYER_RETURN_GOODS.getCode()) ||
                ObjectUtil.equal(taobaoRefund.getStatus(), TaobaoReturnOrderExt.RefundStatus.SUCCESS.getCode()))) {
            return new ProcessStepResult<>(StepStatus.SUCCESS, "售后状态不符合自动拦截");
        }

        // 只拦截申请退款与退款完成状态的

        // 判断淘宝订单是否存在 如果不存在 往下走
        if (ObjectUtil.isNull(taobaoOrder)) {
            // 后面会校验订单的合法性
            return new ProcessStepResult<>(StepStatus.SUCCESS, "淘宝中间表无数据，进入下一阶段");
        }
        //20240109佳哥让通用订单中间表的的交易状态为订单完成状态时直接跳过
        if (TaoBaoOrderStatus.TRADE_FINISHED.equals(taobaoOrder.getStatus())) {
            return new ProcessStepResult<>(StepStatus.SUCCESS, "交易状态为交易成功，进入下一阶段");
        }
        // 判断订单金额
        if (ObjectUtil.notEqual(taobaoOrder.getPayment(), taobaoRefund.getRefundFee())) {
            return new ProcessStepResult<>(StepStatus.SUCCESS, "不是全额退，进入下一阶段");
        }

        // 判断是不是仅退款

        Integer hasGoodReturn = taobaoRefund.getHasGoodReturn();
        if (ObjectUtil.isNull(hasGoodReturn) || ObjectUtil.notEqual(hasGoodReturn, TaobaoReturnOrderExt.HasGoodReturnStatus.NO_RETURN.getCode())) {
            return new ProcessStepResult<>(StepStatus.SUCCESS, "不是仅退款，进入下一阶段");
        }

        List<OmsOrderRelation> allOrderRelation = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(omsOrderRelation)) {
            allOrderRelation.addAll(omsOrderRelation);
        }
        if (CollectionUtils.isNotEmpty(isGiftOrderRelation)) {
            allOrderRelation.addAll(isGiftOrderRelation);
        }

        if (CollectionUtils.isEmpty(allOrderRelation)) {
            // 此时订单还没转下去 肯定还没发货 无法进行拦截 往下走
            return new ProcessStepResult<>(StepStatus.SUCCESS, "无有效零售发货单，进入下一阶段");
        }
        // 可能存在omsOrderRelation为空 不过isGiftOrderRelation 不为空的情况。
        // 反正两个集合的订单都要看 只要订单整单退了  并且申请的是仅退款 就可以看中台是不是已发货 发货的都执行拦截
        for (OmsOrderRelation orderRelation : allOrderRelation) {
            OcBOrder ocBOrder = orderRelation.getOcBOrder();
            if (ObjectUtil.isNull(ocBOrder)) {
                continue;
            }

            // 判断订单状态 如果是仓库发货或者平台发货 则生成拦截单
            Integer orderStatus = ocBOrder.getOrderStatus();
            if (ObjectUtil.equal(orderStatus, OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger()) || ObjectUtil.equal(orderStatus, OmsOrderStatus.PLATFORM_DELIVERY.toInteger())) {
                ValueHolderV14<Void> valueHolder = logisticsInterceptService.autoIntercept(ocBOrder.getId(), taobaoRefund.getRefundId());
                if (!valueHolder.isOK()) {
                    log.info("Step015AutoIntercept startProcess error {}，tid:{}", valueHolder.getMessage(), taobaoOrder.getTid());
                    // 没拦截的话 也要打印操作日志
//                    omsOrderLogService.addUserOrderLog(ocBOrder.getId(),
//                            ocBOrder.getBillNo(), OrderLogTypeEnum.INTERCEPT_RETURN.getKey(),
//                            valueHolder.getMessage(), "", "", SystemUserResource.getRootUser());
                }
            }
        }
        return new ProcessStepResult<>(StepStatus.SUCCESS, "自动拦截结束，进入下一阶段");
    }
}
