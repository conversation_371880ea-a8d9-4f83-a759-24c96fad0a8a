package com.jackrain.nea.oc.oms.mapper;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2019/2/28 13:18
 * describe ...
 */
@Mapper
@Repository
public interface OmsItemProMapper extends ExtentionMapper {

    /**
     * 根据款号查询sku信息
     *
     * @param objId
     * @param tableName
     * @param proECode
     * @param key
     * @return
     */
    @SelectProvider(type = selectProvider.class, method = "selectSkuInfo")
    List<JSONObject> selectSkuInfo(@Param("objId") Long objId, @Param("tableName") String tableName, @Param
            ("proECode") String proECode, @Param("key") String key, @Param("operation") int operation);

    /**
     * 根据款号查询sku信息
     *
     * @param objId
     * @param tableName
     * @param proECode
     * @param key
     * @return
     */
    @SelectProvider(type = selectProvider.class, method = "selectSkuInfoSpec1")
    List<JSONObject> selectSkuInfoSpec1(@Param("objId") Long objId, @Param("tableName") String tableName, @Param
            ("proECode") String proECode, @Param("key") String key, @Param("operation") int operation);


    /**
     * 查询明细ids集合
     */
    @SelectProvider(type = selectProvider.class, method = "selectItemIds")
    Long[] getItemIds(@Param("objId") Long objId, @Param("tableName") String tableName, @Param("proECodes") List
            proECodes, @Param("key") String key);


    class selectProvider {
        public String selectItemIds(@Param("objId") Long objId, @Param("tableName") String tableName, @Param
                ("proECodes") List proECodes, @Param("key") String key) {
            StringBuilder sql = new StringBuilder("SELECT ID FROM " + tableName + " WHERE " + key + " = " + objId + "" +
                    " ");
            sql.append("AND PS_C_PRO_ECODE in (");
            for (int i = 0; i < proECodes.size(); i++) {
                sql.append("'");
                sql.append(proECodes.get(i));
                sql.append("'");
                if (i != (proECodes.size() - 1)) {
                    sql.append(",");
                }
            }
            sql.append(")");
            return sql.toString();
        }

        public String selectSkuInfoSpec1(@Param("objId") Long objId, @Param("tableName") String tableName, @Param
                ("proECode") String proECode, @Param("key") String key, @Param("operation") int operation) {
            StringBuilder sql = new StringBuilder("SELECT   " +
                    "  ID,   " + key + ",   " +
                    "  PS_C_SKU_ID,   " +
                    "  PS_C_SKU_ECODE,   " +
                    "  ps_c_spec1_id AS PS_C_CLR_ID,   " +
                    "  ps_c_spec1_ecode PS_C_CLR_ECODE,   " +
                    "  ps_c_spec2_id AS PS_C_SIZE_ID,   " +
                    "  ps_c_spec2_ecode PS_C_SIZE_ECODE,   ");
            if (operation == 1) {
                sql.append(" QTY_SCAN,");
            }
            sql.append(" QTY");
            sql.append(" FROM " + tableName + " WHERE " + key + " = " + objId + " AND ps_c_pro_ecode = '" + proECode
                    + "'");
            return sql.toString();
        }

        public String selectSkuInfo(@Param("objId") Long objId, @Param("tableName") String tableName, @Param
                ("proECode") String proECode, @Param("key") String key, @Param("operation") int operation) {
            StringBuilder sql = new StringBuilder("SELECT ID, " + key + ", PS_C_SKU_ID, PS_C_SKU_ECODE, PS_C_CLR_ID, " +
                    "PS_C_CLR_ECODE, PS_C_SIZE_ID, PS_C_SIZE_ECODE,");
            if ("sc_b_inventory_item".equalsIgnoreCase(tableName)) {
                sql.append(" storehouse as STOREHOUSE,");
            }
            if ("oc_b_return_order_refund".equalsIgnoreCase(tableName)) {
                sql.append(" QTY_REFUND as QTY");
            } else if ("oc_b_return_order_exchange".equalsIgnoreCase(tableName)) {
                sql.append(" QTY_EXCHANGE as QTY");
            } else if ("dl_b_retail_item".equalsIgnoreCase(tableName)) {
                sql.append(" QTY_BILL as QTY");
            }else {
                if (operation == 1) {
                    sql.append(" QTY_SCAN,");
                }
                sql.append("QTY ");

            }
            sql.append(" FROM " + tableName + " WHERE " + key + " = " + objId + " AND ps_c_pro_ecode = '" + proECode
                    + "'");
            return sql.toString();
        }
    }

    /**
     * 单条码新增查询服务
     *
     * @param table
     * @param depotsKey
     * @param depotsValue
     * @param skuEcode
     * @return
     */
    @Select("SELECT ID, PS_C_SKU_ID, PS_C_SKU_ECODE, PS_C_PRO_ID, PS_C_PRO_ECODE, QTY FROM ${table} WHERE " +
            "${depotsKey} = #{depotsValue} AND ps_c_sku_ecode = #{skuEcode}")
    JSONObject selectBillSkuNum(@Param("table") String table, @Param("depotsKey") String depotsKey, @Param
            ("depotsValue") Long depotsValue, @Param("skuEcode") String skuEcode);

    /**
     * 扫描数量查询
     *
     * @param table
     * @param depotsKey
     * @param depotsValue
     * @param skuEcode
     * @return
     */
    @Select("SELECT ID, PS_C_SKU_ID, PS_C_SKU_ECODE, PS_C_PRO_ID, PS_C_PRO_ECODE, QTY_SCAN FROM ${table} WHERE " +
            "${depotsKey} = #{depotsValue} AND ps_c_sku_ecode = #{skuEcode}")
    JSONObject selectBillScanSkuNum(@Param("table") String table, @Param("depotsKey") String depotsKey, @Param
            ("depotsValue") Long depotsValue, @Param("skuEcode") String skuEcode);

}
