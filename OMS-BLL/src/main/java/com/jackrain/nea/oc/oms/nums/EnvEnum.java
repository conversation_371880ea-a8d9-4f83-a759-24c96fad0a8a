package com.jackrain.nea.oc.oms.nums;

public enum EnvEnum {
    /**
     * 当前环境
     */
    PROD,
    TEST,
    DEV,
    UNKNOWN,
    ;

    public static EnvEnum getEnv() {
        String property = System.getProperty("nacos.config.server-addr");
        if ("192.168.226.216:8848".equals(property) || "121.199.38.129:8848".equals(property)) {
            return PROD;
        }

        if ("10.48.13.151:28848".equals(property) || "118.31.111.118:28848".equals(property)) {
            return TEST;
        }

        if ("10.108.138.214:18848".equals(property) || "118.31.111.118:18848".equals(property)) {
            return DEV;
        }

        return UNKNOWN;
    }
}
