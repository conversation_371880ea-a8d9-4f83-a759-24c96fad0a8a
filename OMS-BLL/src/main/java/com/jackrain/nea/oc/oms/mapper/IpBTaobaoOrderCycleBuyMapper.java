package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoOrderCycleBuy;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Mapper
public interface IpBTaobaoOrderCycleBuyMapper extends ExtentionMapper<IpBTaobaoOrderCycleBuy> {

    @Select("SELECT * FROM ip_b_taobao_order_cycle_buy WHERE ip_b_taobao_order_id=#{orderId}")
    List<IpBTaobaoOrderCycleBuy> selectOrderCycleBuyList(@Param("orderId") long orderId);

    @Select("SELECT * FROM ip_b_taobao_order_cycle_buy WHERE order_id = #{orderId} and curr_phase = #{currPhase}")
    IpBTaobaoOrderCycleBuy selectOrderCycleBuyCurrPhase(@Param("orderId") String orderId, @Param("currPhase") int currPhase);


    /**
     * 根据订单号查询周期购数据
     *
     * @param orderNos
     * @return
     */
    @Select("<script>"
            + "SELECT * FROM ip_b_taobao_order_cycle_buy WHERE order_id in"
            + "<foreach item='orderNo' index='index' collection='orderNos' open='(' separator=',' "
            + "close=')'> #{orderNo} </foreach>"
            + "</script>")
    List<IpBTaobaoOrderCycleBuy> selectCycleBuyByOrderNos(@Param("orderNos") List<String> orderNos);

}