package com.jackrain.nea.oc.oms.task.transfer;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.es.ES4IpJingDongOrder;
import com.jackrain.nea.oc.oms.model.relation.IpJingdongOrderRelation;
import com.jackrain.nea.oc.oms.process.transfer.impl.normal.jingdong.JingdongTransferOrderProcessImpl;
import com.jackrain.nea.oc.oms.services.IpJingdongOrderService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> 孙俊磊
 * @since : 2019-04-24
 * create at : 2019-04-24 3:44 PM
 * 京东转单服务
 */
@Component
@Slf4j
public class AutoJingdongTransferTask extends BaseR3Task implements IR3Task {

    @Autowired
    private IpJingdongOrderService ipJingdongOrderService;

    @Autowired
    private JingdongTransferOrderProcessImpl JingdongTransferOrderProcess;

    @Override
    @XxlJob("AutoJingdongTransferTask")
    public RunTaskResult execute(JSONObject params) {
        long start = System.currentTimeMillis();
        RunTaskResult result = new RunTaskResult();
        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
        Integer range = config.getProperty("lts.autoJinDongTransferTask.queryEs.range", DEFAULT_PAGE_SIZE);
        if (log.isDebugEnabled()) {
            log.debug("AutoJinDongTransferTask.queryEs.range: {}", range);
        }
        try {
            List<String> orderNoList = ES4IpJingDongOrder.selectUnTransferredOrderFromEs(0, range);

            List<IpJingdongOrderRelation> orderRelationList = new ArrayList<>();
            for (String orderNo : orderNoList) {
                IpJingdongOrderRelation JingdongOrderRelation = this.ipJingdongOrderService.selectJingdongOrder(orderNo);
                if (JingdongOrderRelation == null) {
                    String errorMessage = Resources.getMessage("AutoMakeupTransfer Order NotExist!OrderNo="
                            + orderNo);
                    log.error(errorMessage);
                } else {
                    orderRelationList.add(JingdongOrderRelation);
                }
            }

            threadOrderProcessor.startMultiThreadExecute(this.JingdongTransferOrderProcess, orderRelationList);

            result.setSuccess(true);
            result.setMessage("耗时：" + (System.currentTimeMillis() - start) + "ms");
        } catch (Exception ex) {
            log.error(LogUtil.format("AutoJingdongTransferTask,异常信息:{}", "AutoJingdongTransferTask"), Throwables.getStackTraceAsString(ex));
            result.setSuccess(false);
            result.setMessage(ex.getMessage());
        }

        return result;

    }
}