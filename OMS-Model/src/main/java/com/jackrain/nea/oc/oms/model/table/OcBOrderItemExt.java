package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * @ClassName OcBOrderItemExt
 * @Description 订单明细拓展实体类
 * <AUTHOR>
 * @Date 2024/7/23 17:12
 * @Version 1.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Document(index = "oc_b_order_item_ext", type = "oc_b_order_item_ext")
@ApiModel(value = "oc_b_order_item_ext", description = "订单明细拓展")
@TableName(value = "oc_b_order_item_ext")
public class OcBOrderItemExt extends BaseModel {
    private static final long serialVersionUID = 7117115610805272861L;

    @ApiModelProperty(value = "ID")
    @Field(type = FieldType.Long)
    @JSONField(name = "ID")
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @ApiModelProperty(value = "订单编号")
    @Field(type = FieldType.Long)
    @JSONField(name = "OC_B_ORDER_ID")
    private Long ocBOrderId;

    @ApiModelProperty(value = "订单明细ID")
    @Field(type = FieldType.Long)
    @JSONField(name = "ORDER_ITEM_ID")
    private Long orderItemId;

    @ApiModelProperty(value = "平台单号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "TID")
    private String tid;

    @ApiModelProperty(value = "一级分类")
    @Field(type = FieldType.Long)
    @JSONField(name = "M_DIM4_ID")
    private Long mDim4Id;

    @ApiModelProperty(value = "四级分类")
    @Field(type = FieldType.Long)
    @JSONField(name = "M_DIM6_ID")
    private Long mDim6Id;

    @ApiModelProperty(value = "零级分类")
    @Field(type = FieldType.Long)
    @JSONField(name = "M_DIM12_ID")
    private Long mDim12Id;

    @ApiModelProperty(value = "零级分类")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "M_DIM12_CODE")
    private String mDim12Code;

    @ApiModelProperty(value = "零级分类")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "M_DIM12_NAME")
    private String mDim12Name;

    @ApiModelProperty(value = "销售组织编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SALE_COMPANY")
    private String saleCompany;

    @ApiModelProperty(value = "销售中心编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SALES_CENTER_CODE")
    private String salesCenterCode;

    @ApiModelProperty(value = "销售中心名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SALES_CENTER_NAME")
    private String salesCenterName;

    @ApiModelProperty(value = "销售部门编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SALES_DEPARTMENT_CODE")
    private String salesDepartmentCode;

    @ApiModelProperty(value = "销售部门名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SALES_DEPARTMENT_NAME")
    private String salesDepartmentName;

    @ApiModelProperty(value = "销售组编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SALES_GROUP_CODE")
    private String salesGroupCode;

    @ApiModelProperty(value = "销售组名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SALES_GROUP_NAME")
    private String salesGroupName;

    @ApiModelProperty(value = "一级分货组织编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "DIST_CODE_LEVEL_ONE")
    private String distCodeLevelOne;

    @ApiModelProperty(value = "一级分货组织名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "DIST_NAME_LEVEL_ONE")
    private String distNameLevelOne;

    @ApiModelProperty(value = "二级分货组织编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "DIST_CODE_LEVEL_TWO")
    private String distCodeLevelTwo;

    @ApiModelProperty(value = "二级分货组织名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "DIST_NAME_LEVEL_TWO")
    private String distNameLevelTwo;

    @ApiModelProperty(value = "三级分货组织编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "DIST_CODE_LEVEL_THREE")
    private String distCodeLevelThree;

    @ApiModelProperty(value = "三级分货组织名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "DIST_NAME_LEVEL_THREE")
    private String distNameLevelThree;

    @ApiModelProperty(value = "数字类型备用字段1")
    @Field(type = FieldType.Long)
    @JSONField(name = "RESERVE_BIGINT01")
    private Long reserveBigint01;

    @ApiModelProperty(value = "数字类型备用字段2")
    @Field(type = FieldType.Long)
    @JSONField(name = "RESERVE_BIGINT02")
    private Long reserveBigint02;

    @ApiModelProperty(value = "价格备用字段1")
    @Field(type = FieldType.Double)
    @JSONField(name = "RESERVE_DECIMAL01")
    private BigDecimal reserveDecimal01;

    @ApiModelProperty(value = "价格备用字段2")
    @Field(type = FieldType.Double)
    @JSONField(name = "RESERVE_DECIMAL02")
    private BigDecimal reserveDecimal02;

    @ApiModelProperty(value = "文本备用字段1")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RESERVE_VARCHAR01")
    private String reserveVarchar01;

    @ApiModelProperty(value = "文本备用字段2")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RESERVE_VARCHAR02")
    private String reserveVarchar02;
}
