package com.jackrain.nea.ac.service;

import com.jackrain.nea.ac.model.AcFCompensationReason;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.ac.AcFCompensationReasonMapper;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @Author: anna
 * @CreateDate: 2020/7/10$ 21:20$
 * @Description:根据赔付类型查询赔付原因
 */
@Component
@Slf4j
@Transactional
public class AcFCompensationTypeQueryService {
    @Autowired
    AcFCompensationReasonMapper acFCompensationReasonMapper;

    public ValueHolderV14<List<AcFCompensationReason>> queryAcFCompensationReasonById(Integer id) throws NDSException {
        log.info(LogUtil.format("查询赔付类型ID", id));
        ValueHolderV14<List<AcFCompensationReason>> valueHolderV14 = new ValueHolderV14<>();
        if (id == null) {
            valueHolderV14.setCode(ResultCode.SUCCESS);
            valueHolderV14.setMessage("获取参数类型ID为空!!!");
        }
        List<AcFCompensationReason> listReason = acFCompensationReasonMapper.queryByCompesationTypeId(id);
        if (listReason == null) {
            valueHolderV14.setCode(ResultCode.SUCCESS);
            valueHolderV14.setMessage("该赔付类型下不存在赔付原因!!!");
        } else {
            valueHolderV14.setCode(ResultCode.SUCCESS);
            valueHolderV14.setData(listReason);
            valueHolderV14.setMessage("查询成功!!!");
        }
        return valueHolderV14;
    }
}
