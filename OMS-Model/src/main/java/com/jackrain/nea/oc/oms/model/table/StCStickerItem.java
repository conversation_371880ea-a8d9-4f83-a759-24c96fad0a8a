package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * st_c_sticker_item
 *
 * <AUTHOR>
@TableName
@Data
public class StCStickerItem extends BaseModel {
    @JSONField(name = "ID")
    private Long id;

    /**
     * 策略id
     */
    @JSONField(name = "ST_C_STICKER_ID")
    private Long stCStickerId;
    /**
     * 指定维度(0-无主播ID+SKU，1-指定主播ID+SKU，2-无主播ID+四级类目，3-指定主播ID+四级类目)
     */
    @JSONField(name = "APPOINT_DIMENSION")
    private Integer appointDimension;
    /**
     * 识别内容
     */
    @JSONField(name = "IDENTIFY_CONTENT")
    private String identifyContent;
    /**
     * 匹配内容
     */
    @JSONField(name = "MATCH_CONTENT")
    private String matchContent;
    /**
     * 商品id
     */
    @JSONField(name = "PS_C_PRO_ID")
    private Long psCProId;

    /**
     * 商品编码
     */
    @JSONField(name = "PS_C_PRO_ECODE")
    private String psCProEcode;

    /**
     * 商品名称
     */
    @JSONField(name = "PS_C_PRO_ENAME")
    private String psCProEname;

    /**
     * 数量
     */
    @JSONField(name = "QTY")
    private BigDecimal qty;

    /**
     * 条码编码
     */
    @JSONField(name = "PS_C_SKU_ECODE")
    private String psCSkuEcode;

    @JSONField(name = "PS_C_SKU_ID")
    private Long psCSkuId;

    /**
     * 规格
     */
    @JSONField(name = "SKU_SPEC")
    private String skuSpec;

    /**
     * 备注
     */
    @JSONField(name = "REMARKS")
    private String remarks;

    private static final long serialVersionUID = 1L;
}