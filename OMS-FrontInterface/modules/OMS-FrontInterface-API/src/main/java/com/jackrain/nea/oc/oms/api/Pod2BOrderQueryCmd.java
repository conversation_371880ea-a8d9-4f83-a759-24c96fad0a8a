package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.oc.oms.model.result.Pod2BOrderQueryRequest;
import com.jackrain.nea.oc.oms.model.result.Pod2BOrderQueryResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;

/**
 * @ClassName Pod2BOrderQueryCmd
 * @Description pod 2b订单查询
 * <AUTHOR>
 * @Date 2024/8/29 16:43
 * @Version 1.0
 */
public interface Pod2BOrderQueryCmd {

    /**
     * 分页查询数据
     *
     * @param request
     * @return
     */
    ValueHolderV14<Pod2BOrderQueryResult> queryByPage(Pod2BOrderQueryRequest request);

    /**
     * 导出excel
     *
     * @param request
     * @return
     */
    ValueHolderV14<String> exportByPage(Pod2BOrderQueryRequest request);
}
