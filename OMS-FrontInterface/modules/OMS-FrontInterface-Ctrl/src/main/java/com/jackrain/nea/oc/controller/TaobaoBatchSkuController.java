package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.TaobaoBatchSkuCmd;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * @ClassName : TaobaoBatchSkuController  
 * @Description : 淘宝中间表批量处理SKU特殊字符
 * <AUTHOR>  YCH
 * @Date: 2021-12-08 14:36  
 */
@RestController
@Slf4j
@Api(value = "TaobaoBatchSkuController", description = "淘宝中间表批量处理SKU特殊字符")
public class TaobaoBatchSkuController {

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @Autowired
    private TaobaoBatchSkuCmd taobaoBatchSkuCmd;

    @ApiOperation(value = "淘宝中间表批量处理SKU特殊字符")
    @RequestMapping(path = "/api/cs/oc/oms/v1/taobaoBatchSku", method = {RequestMethod.POST, RequestMethod.GET})
    public JSONObject taobaoBatchSku(HttpServletRequest request, @RequestBody JSONObject obj) {

        //记录日志信息
        if (log.isDebugEnabled()) {
            log.debug(" Start TaobaoBatchSkuController. ReceiveParams={}",JSON.toJSONString(obj));
        }

        //获取用户登录信息
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        if (obj == null) {
            throw new NDSException(Resources.getMessage("请求参数不能为空!", user.getLocale()));
        }
        ValueHolderV14 vh = new ValueHolderV14();
        try {
            vh = taobaoBatchSkuCmd.execute(obj, user);

            //记录日志信息。Finish 标记结束
            if (log.isDebugEnabled()) {
                log.debug(" Finish TaobaoBatchSkuController.taobaoBatchSkuCmd.ReceiveParams:{}", vh.toJSONObject());
            }
            return vh.toJSONObject();
        } catch (NDSException e) {
            vh = new ValueHolderV14();
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            log.error(" TaobaoBatchSkuController处理异常" + e.toString());
            return vh.toJSONObject();

        }
    }
}
