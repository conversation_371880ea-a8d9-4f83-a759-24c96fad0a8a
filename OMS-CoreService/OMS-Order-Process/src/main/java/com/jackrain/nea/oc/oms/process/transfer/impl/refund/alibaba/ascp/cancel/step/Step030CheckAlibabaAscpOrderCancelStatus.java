package com.jackrain.nea.oc.oms.process.transfer.impl.refund.alibaba.ascp.cancel.step;

import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.relation.IpBAlibabaAscpOrderCancelRelation;
import com.jackrain.nea.oc.oms.model.table.IpBAlibabaAscpOrderCancel;
import com.jackrain.nea.oc.oms.process.transfer.impl.refund.alibaba.ascp.util.AlibabaAscpOrderCommonUtil;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @description: 判断猫超直发取消订单审核状态
 * @author: xtt
 * @date: 2020-09-04 16:25
 **/
@Step(order = 30, description = "判断猫超直发取消订单审核状态")
@Slf4j
@Component
public class Step030CheckAlibabaAscpOrderCancelStatus extends BaseAlibabaAscpOrderCancelProcessStep
        implements IOmsOrderProcessStep<IpBAlibabaAscpOrderCancelRelation> {

    @Override
    public ProcessStepResult<IpBAlibabaAscpOrderCancelRelation> startProcess(IpBAlibabaAscpOrderCancelRelation orderInfo,
                                                                             ProcessStepResult preStepResult,
                                                                             boolean isAutoMakeup, User operateUser) {
        IpBAlibabaAscpOrderCancel ipBAlibabaAscpOrderCancel = orderInfo.getIpBAlibabaAscpOrderCancel();
        Long id = ipBAlibabaAscpOrderCancel.getId();
        String bizOrderCode = ipBAlibabaAscpOrderCancel.getBizOrderCode();
        AlibabaAscpOrderCommonUtil.printDebugLog(this.getClass().getName() + " 判断猫超直发取消订单审核状态，猫超直发取消订单退款单id：{},bizOrderCode:{}", id, bizOrderCode);
        long starTime = System.currentTimeMillis();
        try {
            //猫超直发取消接口转单处理逻辑
            orderCancelService.tAlibabaAscpOrderCancel(orderInfo, operateUser);
            long endTime = System.currentTimeMillis();
            long Time = endTime - starTime;
            AlibabaAscpOrderCommonUtil.printDebugLog(this.getClass().getName() + " 猫超直发取消接口转单处理逻辑耗时:{}ms", Time);
            return new ProcessStepResult<>(StepStatus.FINISHED, String.format("单据id:%s,bizOrderCode:%s转换完成", id, bizOrderCode));
        } catch (Exception e) {
            log.error(LogUtil.format("猫超直发取消订单转换异常:{}", "猫超直发取消订单转换异常"), Throwables.getStackTraceAsString(e));
            //修改中间表状态及系统备注
            orderCancelService.updateSaRefundIsTransError(ipBAlibabaAscpOrderCancel, e.getMessage());
            String errorMessage = "退单转换异常!" + e.getMessage();
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
    }
}
