package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.IpBAlibabaAscpOrderRefundItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface IpBAlibabaAscpOrderRefundItemMapper extends ExtentionMapper<IpBAlibabaAscpOrderRefundItem> {

    @Select("SELECT * FROM ip_b_alibaba_ascp_order_refund_item WHERE ip_b_alibaba_ascp_order_refund_id=#{orderId}")
    List<IpBAlibabaAscpOrderRefundItem> selectAlibabaOrderRefundItemList(@Param("orderId") Long orderId);

}