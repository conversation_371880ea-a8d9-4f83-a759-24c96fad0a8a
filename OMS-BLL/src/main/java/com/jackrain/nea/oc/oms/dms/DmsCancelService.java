package com.jackrain.nea.oc.oms.dms;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.jackrain.nea.oc.oms.es.ES4ReturnOrder;
import com.jackrain.nea.oc.oms.gsi.GSI4OrderItem;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderSaleProductAttrEnum;
import com.jackrain.nea.oc.oms.model.enums.ProReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.request.DmsCancelRequest;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.sap.SapCommonService;
import com.jackrain.nea.oc.oms.services.OcBOrderHoldService;
import com.jackrain.nea.oc.oms.services.OcCancelChangingOrRefundService;
import com.jackrain.nea.oc.oms.services.refund.OmsReturnUtil;
import com.jackrain.nea.oc.oms.spiltorder.OmsOrderManualSplitNewService;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.OmsBusinessTypeUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.util.ValueHolderV14Utils;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Sets;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * dms订单取消服务
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class DmsCancelService {

    @Resource
    private OcBOrderMapper orderMapper;
    @Resource
    private OcBOrderItemMapper orderItemMapper;
    @Resource
    private OcBReturnOrderMapper returnOrderMapper;
    @Resource
    private BllRedisLockOrderUtil redisUtil;
    @Resource
    private OcCancelChangingOrRefundService refundService;
    @Resource
    private OmsReturnUtil omsReturnUtil;
    @Resource
    private OmsOrderManualSplitNewService omsOrderManualSplitNewService;
    @Resource
    private OcBOrderHoldService holdService;

    private static User DMS_USER;

    private static List<String> TERMINATION_TYPE;

    private static List<Integer> NOT_CANCEL_ORDER_STATUS;

    private static List<Integer> NEED_THE_AUDIT_ORDER_STATUS;

    //终止状态(1-全部，2-部门)
    private final static String TERMINATION_TYPE_01 = "1";

    private final static String TERMINATION_TYPE_02 = "2";

    /**
     * 订单取消
     *
     * @param dmsCancelRequest
     * @return
     */
    public ValueHolderV14 cancelOrder(DmsCancelRequest dmsCancelRequest) {
        log.info(LogUtil.format("cancelOrder dmsCancelRequest:{}", "dmsOrderCancel"), JSON.toJSONString(dmsCancelRequest));

        ValueHolderV14 validateHolder = validate(dmsCancelRequest);
        if (validateHolder != null) {
            return validateHolder;
        }

        String tid = dmsCancelRequest.getTid();

        List<Long> orderIds = GSI4OrderItem.selectOcBOrderItemByTid(tid);
        if (CollectionUtils.isEmpty(orderIds)) {
            log.warn(LogUtil.format("cancelOrder orderIds is null tid:{}", "dmsOrderCancel"), tid);
            return ValueHolderV14Utils.getFailValueHolder("取消失败，单据不存在!");
        }
        
        //orderIds去重
        orderIds = orderIds.stream().distinct().collect(Collectors.toList());

        List<RedisReentrantLock> redisReentrantLocks = lockOrderList(orderIds);

        try {
            List<OcBOrder> orders = orderMapper.selectBatchIds(orderIds);
            if (CollectionUtils.isEmpty(orders)) {
                log.warn(LogUtil.format("cancelOrder orderIds is null orderIds:{}", "dmsOrderCancel"), orderIds);
                return ValueHolderV14Utils.getFailValueHolder("取消失败，单据不存在!");
            }

            //残次订单取消
            if (OmsBusinessTypeUtil.isToBOrder(orders.get(0)) && OrderSaleProductAttrEnum.isToBCC(orders.get(0).getSaleProductAttr())) {
                //全部取消+含有合单，返回取消失败
                Boolean allCancel = dmsCancelRequest.getAllCancel();
                Set<Integer> merges = orders.stream().map(OcBOrder::getIsMerge).collect(Collectors.toSet());
                if (allCancel != null && allCancel && merges.contains(OcBOrderConst.IS_STATUS_IY)) {
                    return ValueHolderV14Utils.getFailValueHolder("取消失败(整+合)");
                }

                //过滤合单
                List<OcBOrder> ocBOrders = orders.stream().filter(p -> !OcBOrderConst.IS_STATUS_IY.equals(p.getIsMerge())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(ocBOrders)) {
                    log.warn(LogUtil.format("cancelOrder orderIds no merge order is null orderIds:{}", "dmsOrderCancel"), orderIds);
                    return ValueHolderV14Utils.getFailValueHolder("取消失败，单据不存在(合)!");
                }

                return this.cancelC(dmsCancelRequest, tid, ocBOrders);
            }

            //非残次取消
            ValueHolderV14 holderV14 = cancelNoC(dmsCancelRequest, tid, orders);
            if (holderV14 != null) {
                return holderV14;
            }
        } catch (Exception e) {
            log.error(LogUtil.format("cancelOrder or cancelCOrder error dmsCancelRequest:{}", "dmsOrderCancel"), JSON.toJSONString(dmsCancelRequest), e);
            return ValueHolderV14Utils.getFailValueHolder(e.getMessage());
        } finally {
            unLockOrderList(redisReentrantLocks);
        }

        return ValueHolderV14Utils.getSuccessValueHolder("success");
    }

    /**
     * dms-订单取消（非残次）
     *
     * @param dmsCancelRequest
     * @param tid
     * @param orders
     * @return
     */
    private ValueHolderV14 cancelNoC(DmsCancelRequest dmsCancelRequest, String tid, List<OcBOrder> orders) {
        List<OcBOrder> canCancelList = orders.stream().filter(order -> !getNotCancelOrderStatus().contains(order.getOrderStatus())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(canCancelList)) {
            return ValueHolderV14Utils.getFailValueHolder("取消失败，订单状态不满足取消!");
        }

        Set<Long> orderIds = canCancelList.stream().map(OcBOrder::getId).collect(Collectors.toSet());

        List<OcBOrderItem> orderItems = new ArrayList<>();
        int count = 0;
        List<DmsCancelRequest.Item> items = dmsCancelRequest.getItems();
        for (DmsCancelRequest.Item item : items) {
            List<OcBOrderItem> ocBOrderItems = orderItemMapper.selectUnSuccessRefundByIds(orderIds, tid + "-" + item.getLine());
            if (CollectionUtils.isEmpty(ocBOrderItems)) {
                continue;
            }
            BigDecimal qtySum = ocBOrderItems.stream().map(OcBOrderItem::getQty).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (qtySum.compareTo(item.getQty()) == 0) {
                orderItems.addAll(ocBOrderItems);
                count++;
                continue;
            }
            for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
                BigDecimal qty = ocBOrderItem.getQty();
                BigDecimal qty1 = item.getQty();
                if (qty.compareTo(qty1) == 0) {
                    orderItems.add(ocBOrderItem);
                    count++;
                    break;
                }
            }
        }
        if (count != items.size()) {
            return ValueHolderV14Utils.getFailValueHolder("取消失败");
        }

        //查找需要取消的明细对应的订单
        List<OcBOrder> autidOrders = getOcBOrders(canCancelList, orderItems);
        List<OcBOrder> needAuditResOrders = autidOrders.stream().filter(order -> getNeedTheAuditOrderStatus().contains(order.getOrderStatus())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(needAuditResOrders)) {
            User sapUser = SystemUserResource.getSapUser();
            for (OcBOrder ocBOrder : needAuditResOrders) {
                boolean b = omsReturnUtil.toExamineOrder(ocBOrder, sapUser);
                if (!b) {
                    return ValueHolderV14Utils.getFailValueHolder("订单反审核失败!");
                }
            }

            List<Long> collect = needAuditResOrders.stream().map(OcBOrder::getId).collect(Collectors.toList());

            for (OcBOrder ocBOrder : canCancelList) {
                Long id = ocBOrder.getId();
                Integer orderStatus = ocBOrder.getOrderStatus();
                if (collect.contains(id) && OmsOrderStatus.CHECKED.toInteger().equals(orderStatus)) {
                    ocBOrder.setOrderStatus(OmsOrderStatus.UNCONFIRMED.toInteger());
                }
            }
        }

        Map<Long, OcBOrder> orderMap = canCancelList.stream().collect(Collectors.toMap(OcBOrder::getId, Function.identity(), (key1, key2) -> key2));

        Map<Long, List<OcBOrderItem>> itemMap = orderItems.stream().collect(Collectors.groupingBy(OcBOrderItem::getOcBOrderId));
        for (Map.Entry<Long, List<OcBOrderItem>> entry : itemMap.entrySet()) {
            SapCommonService.getInstance().markCancel(orderMap.get(entry.getKey()), entry.getValue());
        }
        return null;
    }

    private List<OcBOrder> getOcBOrders(List<OcBOrder> canCancelList, List<OcBOrderItem> orderItems) {
        List<OcBOrder> autidOrders = Lists.newArrayList();
        Map<Long, OcBOrder> orderMap = canCancelList.stream().collect(Collectors.toMap(OcBOrder::getId, Function.identity(), (key1, key2) -> key2));

        for (OcBOrderItem orderItem : orderItems) {
            Long ocBOrderId = orderItem.getOcBOrderId();
            OcBOrder order = orderMap.get(ocBOrderId);
            if (CollectionUtils.isEmpty(autidOrders)) {
                autidOrders.add(order);
            } else {
                List<Long> existIds = autidOrders.stream().map(OcBOrder::getId).collect(Collectors.toList());
                if (!existIds.contains(ocBOrderId)) {
                    autidOrders.add(order);
                }
            }
        }
        return autidOrders;
    }

    /**
     * 残次取消
     *
     * @param dmsCancelRequest
     * @param tid
     * @param orders
     * @return
     */
    private ValueHolderV14 cancelC(DmsCancelRequest dmsCancelRequest, String tid, List<OcBOrder> orders) {
        if (orders.size() > 1) {
            //拆单了
            ValueHolderV14 failValueHolder = splitCancel(dmsCancelRequest, tid, orders);
            if (failValueHolder != null) {
                return failValueHolder;
            }
        } else {
            //未拆单
            ValueHolderV14 FailValueHolder = noSplitCancel(dmsCancelRequest, tid, orders);
            if (FailValueHolder != null) {
                return FailValueHolder;
            }
        }

        return ValueHolderV14Utils.getSuccessValueHolder("success");
    }

    /**
     * 未拆单残次取消
     *
     * @param dmsCancelRequest
     * @param tid
     * @param orders
     * @return
     */
    private ValueHolderV14 noSplitCancel(DmsCancelRequest dmsCancelRequest, String tid, List<OcBOrder> orders) {
        OcBOrder order = orders.get(0);
        Integer orderStatus = order.getOrderStatus();
        //判断当前订单状态是否为“待寻源”&“待审核”
        if (!orderStatus.equals(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger()) && !orderStatus.equals(OmsOrderStatus.UNCONFIRMED.toInteger())) {
            return ValueHolderV14Utils.getFailValueHolder("取消失败，订单状态不满足取消!");
        }

        //判断是否整单取消
        Boolean allCancel = dmsCancelRequest.getAllCancel();
        if (allCancel != null && allCancel) {
            ValueHolderV14 FailValueHolder = splitAllCancel(Lists.newArrayList(order));
            return FailValueHolder;
        } else {
            //非整单取消
            List<DmsCancelRequest.Item> items = dmsCancelRequest.getItems();
            int count = 0;
            List<OcBOrderItem> orderItems = new ArrayList<>();
            for (DmsCancelRequest.Item item : items) {
                List<OcBOrderItem> ocBOrderItems = orderItemMapper.selectUnSuccessRefundByIds(Sets.newHashSet(Lists.newArrayList(order.getId())), tid + "-" + item.getLine());
                if (CollectionUtils.isEmpty(ocBOrderItems)) {
                    continue;
                }
                BigDecimal qtySum = ocBOrderItems.stream().map(OcBOrderItem::getQty).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                if (qtySum.compareTo(item.getQty()) == 0) {
                    orderItems.addAll(ocBOrderItems);
                    count++;
                    continue;
                }
                for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
                    BigDecimal qty = ocBOrderItem.getQty();
                    BigDecimal qty1 = item.getQty();
                    if (qty.compareTo(qty1) == 0) {
                        orderItems.add(ocBOrderItem);
                        count++;
                        break;
                    }
                }
            }
            if (count != items.size()) {
                log.warn(LogUtil.format("未拆单非整单数量不符合取消失败 tid:{},count:{},itemsSize:{}", "dmsOrderCancel"), tid, count, items.size());
                return ValueHolderV14Utils.getFailValueHolder("取消失败");
            }


            //订单部分取消
            ValueHolderV14 failValueHolder = orderPartCancel(items, order);
            return failValueHolder;
        }
    }

    /**
     * 拆单残次取消
     *
     * @param dmsCancelRequest
     * @param tid
     * @param orders
     * @return
     */
    private ValueHolderV14 splitCancel(DmsCancelRequest dmsCancelRequest, String tid, List<OcBOrder> orders) {
        Boolean allCancel = dmsCancelRequest.getAllCancel();
        if (allCancel != null && allCancel) {
            ValueHolderV14 FailValueHolder = splitAllCancel(orders);
            return FailValueHolder;
        } else {
            //非整单取消-寻找平台单号下所有状态为“待寻源”&“待审核”的订单
            List<OcBOrder> ocBOrders = orders.stream().filter(p -> OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(p.getOrderStatus())
                    || OmsOrderStatus.UNCONFIRMED.toInteger().equals(p.getOrderStatus())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(ocBOrders)) {
                return ValueHolderV14Utils.getFailValueHolder("取消失败(无符合订单)");
            }

            Set<Long> orderIds = ocBOrders.stream().map(OcBOrder::getId).collect(Collectors.toSet());

            List<DmsCancelRequest.Item> items = dmsCancelRequest.getItems();
            List<OcBOrderItem> orderItems = new ArrayList<>();
            int count = 0;
            for (DmsCancelRequest.Item item : items) {
                List<OcBOrderItem> ocBOrderItems = orderItemMapper.selectUnSuccessRefundByIds(orderIds, tid + "-" + item.getLine());
                if (CollectionUtils.isEmpty(ocBOrderItems)) {
                    continue;
                }
                BigDecimal qtySum = ocBOrderItems.stream().map(OcBOrderItem::getQty).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                if (qtySum.compareTo(item.getQty()) == 0) {
                    orderItems.addAll(ocBOrderItems);
                    count++;
                    continue;
                }
                for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
                    BigDecimal qty = ocBOrderItem.getQty();
                    BigDecimal qty1 = item.getQty();
                    if (qty.compareTo(qty1) == 0) {
                        orderItems.add(ocBOrderItem);
                        count++;
                        break;
                    }
                }
            }

            if (count != items.size()) {
                return ValueHolderV14Utils.getFailValueHolder("取消失败");
            }

            //查找需要取消的明细对应的订单
            List<OcBOrder> autidOrders = getOcBOrders(ocBOrders, orderItems);
            List<OcBOrder> needAuditResOrders = autidOrders.stream().filter(order -> getNeedTheAuditOrderStatus().contains(order.getOrderStatus())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(needAuditResOrders)) {
                User sapUser = SystemUserResource.getSapUser();
                for (OcBOrder ocBOrder : needAuditResOrders) {
                    boolean b = omsReturnUtil.toExamineOrder(ocBOrder, sapUser);
                    if (!b) {
                        //反审核成功
                        return ValueHolderV14Utils.getFailValueHolder("订单反审核失败!");
                    }
                }

                List<Long> collect = needAuditResOrders.stream().map(OcBOrder::getId).collect(Collectors.toList());

                for (OcBOrder ocBOrder : ocBOrders) {
                    Long id = ocBOrder.getId();
                    Integer orderStatus = ocBOrder.getOrderStatus();
                    if (collect.contains(id) && OmsOrderStatus.CHECKED.toInteger().equals(orderStatus)) {
                        ocBOrder.setOrderStatus(OmsOrderStatus.UNCONFIRMED.toInteger());
                    }
                }
            }

            Map<Long, OcBOrder> orderMap = ocBOrders.stream().collect(Collectors.toMap(OcBOrder::getId, Function.identity(), (key1, key2) -> key2));

            Map<Long, List<OcBOrderItem>> itemMap = orderItems.stream().collect(Collectors.groupingBy(OcBOrderItem::getOcBOrderId));
            for (Map.Entry<Long, List<OcBOrderItem>> entry : itemMap.entrySet()) {
                OcBOrder order = orderMap.get(entry.getKey());
                List<OcBOrderItem> cancelOrderItems = entry.getValue();
                List<DmsCancelRequest.Item> dmsCancelItems = new ArrayList<>();
                for (OcBOrderItem item : cancelOrderItems) {
                    DmsCancelRequest.Item dmsCancelItem = new DmsCancelRequest.Item();
                    String ooid = item.getOoid();
                    if (ooid.contains("-")) {
                        String[] split = ooid.split("-");
                        dmsCancelItem.setLine(Integer.valueOf(split[1]));
                    }
                    dmsCancelItem.setQty(item.getQty());
                    dmsCancelItem.setSku(item.getPsCSkuEcode());
                    dmsCancelItems.add(dmsCancelItem);
                }
                orderPartCancel(dmsCancelItems, order);
            }
            return null;
        }
    }

    /**
     * 单个残次订单部分取消
     *
     * @param items
     * @param order
     * @return
     */
    private ValueHolderV14 orderPartCancel(List<DmsCancelRequest.Item> items, OcBOrder order) {
        log.info(LogUtil.format("单个残次订单部分取消 orderId:{},items:{}", "dmsOrderCancel"), order.getId(), JSON.toJSONString(items));
        //如果不是卡单，则卡单
        if (!YesNoEnum.Y.getVal().equals(order.getIsDetention())) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("DETENTION_REASON", "普通商品卡单");
            jsonObject.put("IS_AUTO_RELEASE", "N");
            jsonObject.put("HOLD_DETENTION_ORDER_REASON", "10001");
            jsonObject.put("HOLD_ORDER_REASON_MSG", "残次取消拆单卡单");
            try {
                holdService.detainWithRedisLock(order.getId(), jsonObject, SystemUserResource.getRootUser(), new ValueHolder());
            } catch (Exception e) {
                log.warn(LogUtil.format("单个残次订单部分取消异常-卡单失败 orderId:{}", "dmsOrderCancel"), order.getId(), e);
                return ValueHolderV14Utils.getFailValueHolder("取消失败(卡单失败)");
            }
        }

        //重新查询订单最新信息
        order = orderMapper.selectByID(order.getId());

        //拆单
        JSONObject splitObj = new JSONObject();
        JSONArray jsonArray = new JSONArray();
        //要拆出去的
        JSONArray splitJsonArray = new JSONArray();
        //老的
        JSONArray noSplitJsonArray = new JSONArray();

        Map<Integer, List<DmsCancelRequest.Item>> lineMap = items.stream().collect(Collectors.groupingBy(DmsCancelRequest.Item::getLine));
        log.info(LogUtil.format("单个残次订单部分取消 orderId:{},lineMap:{}", "dmsOrderCancel"), order.getId(), JSON.toJSONString(lineMap));

        List<OcBOrderItem> orderItemsList = orderItemMapper.selectOrderItemsNotRefundFroAppointSku(order.getId());
        if (CollectionUtils.isEmpty(orderItemsList)) {
            return ValueHolderV14Utils.getFailValueHolder("取消失败(订单明细空)");
        }

        List<Long> splitItemIds = Lists.newArrayList();
        for (OcBOrderItem ocBOrderItem : orderItemsList) {
            String line = ocBOrderItem.getOoid().split("-")[1];
            List<DmsCancelRequest.Item> itemList = lineMap.get(Integer.valueOf(line));
            if (CollectionUtils.isEmpty(itemList)) {
                //老的
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("itemId", ocBOrderItem.getId());
                jsonObject.put("qty", ocBOrderItem.getQty());
                noSplitJsonArray.add(jsonObject);
            } else {
                //要拆出去
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("itemId", ocBOrderItem.getId());
                jsonObject.put("qty", 0);
                noSplitJsonArray.add(jsonObject);

                JSONObject jsonObjectNew = new JSONObject();
                jsonObjectNew.put("itemId", ocBOrderItem.getId());
                jsonObjectNew.put("qty", ocBOrderItem.getQty());
                splitJsonArray.add(jsonObjectNew);
                splitItemIds.add(ocBOrderItem.getId());
            }
        }

        jsonArray.add(noSplitJsonArray);
        jsonArray.add(splitJsonArray);

        splitObj.put("item", jsonArray);
        splitObj.put("orderId", order.getId());

        log.info(LogUtil.format("残次部分取消拆单 splitObj:{},splitItemIds:{}", "dmsOrderCancel"), splitObj, splitItemIds);

        //明细都取消，不需要拆单
        BigDecimal oldCount = BigDecimal.ZERO;
        for (Object o : noSplitJsonArray) {
            JSONObject jsonObject = (JSONObject) o;
            BigDecimal qtyB = new BigDecimal(String.valueOf(jsonObject.get("qty")));
            oldCount = oldCount.add(qtyB);
        }
        if (oldCount.compareTo(BigDecimal.ZERO) == 0) {
            SapCommonService.getInstance().markCancel(order, orderItemsList);
            return null;
        }

        ValueHolderV14 valueHolderV14 = omsOrderManualSplitNewService.confirmSplitOrder(splitObj, getDmsUser(), false, splitItemIds);
        if (!valueHolderV14.isOK()) {
            return ValueHolderV14Utils.getFailValueHolder("取消失败(拆单失败):" + valueHolderV14.getMessage());
        }

        //取消拆单后的订单
        String redisKey = BllRedisKeyResources.getCOrderCancelMark(order.getTid());
        CusRedisTemplate<String, String> objRedisTemplate = RedisMasterUtils.getObjRedisTemplate();
        String s = objRedisTemplate.opsForValue().get(redisKey);
        if (StringUtils.isBlank(s)) {
            return ValueHolderV14Utils.getFailValueHolder("取消失败(拆单后取消):" + valueHolderV14.getMessage());
        }

        log.info(LogUtil.format("残次部分取消拆单-取消的订单 s:{}", "dmsOrderCancel"), s);
        Long aLong = Long.valueOf(s);
        OcBOrder ocBOrder = orderMapper.selectByID(aLong);
        List<OcBOrderItem> ocBOrderItems = orderItemMapper.selectOrderItems(aLong);
        SapCommonService.getInstance().markCancel(ocBOrder, ocBOrderItems);
        return null;
    }

    /**
     * 残次拆单整单取消
     *
     * @param orders
     * @return
     */
    private ValueHolderV14 splitAllCancel(List<OcBOrder> orders) {
        //整单取消-判断当前平台单号下的订单状态是否全部为“待寻源”&“待审核”&“系统作废”的订单
        List<Integer> orderStatuses = orders.stream().map(OcBOrder::getOrderStatus).collect(Collectors.toList());
        for (Integer orderStatus : orderStatuses) {
            if (!OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(orderStatus)
                    && !OmsOrderStatus.UNCONFIRMED.toInteger().equals(orderStatus)
                    && !OmsOrderStatus.SYS_VOID.toInteger().equals(orderStatus)) {
                return ValueHolderV14Utils.getFailValueHolder("取消失败，订单状态不满足取消!");
            }
        }

        //待寻源&待审核订单取消
        List<OcBOrder> cancelOrders = orders.stream().filter(p -> OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(p.getOrderStatus())
                || OmsOrderStatus.UNCONFIRMED.toInteger().equals(p.getOrderStatus())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(cancelOrders)) {
            //将当前平台单号下的全部订单进行取消
            for (OcBOrder order : cancelOrders) {
                List<OcBOrderItem> orderItemsList = orderItemMapper.selectOrderItemsNotRefundFroAppointSku(order.getId());
                SapCommonService.getInstance().markCancel(order, orderItemsList);
            }
        }
        return null;
    }

    private static List<Integer> getNeedTheAuditOrderStatus() {
        if (CollectionUtils.isEmpty(NEED_THE_AUDIT_ORDER_STATUS)) {
            NEED_THE_AUDIT_ORDER_STATUS = Lists.newArrayList(OmsOrderStatus.CHECKED.toInteger(), OmsOrderStatus.IN_DISTRIBUTION.toInteger());
            return NEED_THE_AUDIT_ORDER_STATUS;
        }
        return NEED_THE_AUDIT_ORDER_STATUS;
    }

    private static List<Integer> getNotCancelOrderStatus() {
        if (CollectionUtils.isEmpty(NOT_CANCEL_ORDER_STATUS)) {
            NOT_CANCEL_ORDER_STATUS = Lists.newArrayList(
                    OmsOrderStatus.TO_BE_ASSIGNED.toInteger(),
                    OmsOrderStatus.PENDING_WMS.toInteger(),
                    OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger(),
                    OmsOrderStatus.PLATFORM_DELIVERY.toInteger(),
                    OmsOrderStatus.CANCELLED.toInteger(),
                    OmsOrderStatus.SYS_VOID.toInteger(),
                    OmsOrderStatus.IN_DISTRIBUTION.toInteger(),
                    OmsOrderStatus.OCCUPY_IN.toInteger());
            return NOT_CANCEL_ORDER_STATUS;
        }
        return NOT_CANCEL_ORDER_STATUS;
    }

    /**
     * add lock
     *
     * @param toList
     * @return
     */
    private List<RedisReentrantLock> lockOrderList(Collection<Long> toList) {
        List<RedisReentrantLock> lockList = new ArrayList<>();
        for (Long orderId : toList) {
            String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(orderId);
            RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
            try {
                if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                    lockList.add(redisLock);
                } else {
                    redisLock.unlock();
                }
            } catch (Exception e) {
                redisLock.unlock();
            }
        }
        return lockList;
    }


    /**
     * check params
     *
     * @param dmsCancelRequest
     * @return
     */
    private ValueHolderV14 validate(DmsCancelRequest dmsCancelRequest) {
        if (dmsCancelRequest == null) {
            return ValueHolderV14Utils.getFailValueHolder("取消失败,参数为空！");
        }
        if (StringUtils.isBlank(dmsCancelRequest.getTid())) {
            return ValueHolderV14Utils.getFailValueHolder("取消失败,平台单号为空!");
        }
        if (CollectionUtils.isEmpty(dmsCancelRequest.getItems())) {
            return ValueHolderV14Utils.getFailValueHolder("取消失败,明细为空!");
        }

        for (DmsCancelRequest.Item item : dmsCancelRequest.getItems()) {
            if (Objects.isNull(item.getLine())) {
                return ValueHolderV14Utils.getFailValueHolder("取消失败,行号为空!");
            }
            if (StringUtils.isBlank(item.getSku())) {
                return ValueHolderV14Utils.getFailValueHolder("取消失败,SKU为空!");
            }
            if (Objects.isNull(item.getQty())) {
                return ValueHolderV14Utils.getFailValueHolder("取消失败,数量为空!");
            }
        }
        return null;
    }

    /**
     * 解锁
     *
     * @param lockList
     */
    private void unLockOrderList(List<RedisReentrantLock> lockList) {
        if (CollectionUtils.isEmpty(lockList)) {
            return;
        }
        for (RedisReentrantLock redisReentrantLock : lockList) {
            redisReentrantLock.unlock();
        }
    }

    /**
     * 退单取消
     *
     * @param dmsCancelRequest
     * @return
     */
    public ValueHolderV14 cancelReturn(DmsCancelRequest dmsCancelRequest) {
        log.info(LogUtil.format("cancelReturn dmsCancelRequest:{}", "dmsReturnCancel"), JSON.toJSONString(dmsCancelRequest));

        ValueHolderV14 validate = validate(dmsCancelRequest);
        if (validate != null) {
            return validate;
        }

        //查es
        Set<Long> returnIds = ES4ReturnOrder.findIdByReturnId(dmsCancelRequest.getTid());
        if (CollectionUtils.isEmpty(returnIds)) {
            log.info(LogUtil.format("根据bill:{}，未查询到订单信息", dmsCancelRequest.getTid()), returnIds);
            return ValueHolderV14Utils.getFailValueHolder("取消失败，单据不存在!");
        }

        List<OcBReturnOrder> returnOrderList = returnOrderMapper.selectBatchIds(returnIds);
        log.info(LogUtil.format("cancelReturn dmsCancelRequest:{},returnOrderList:{}", "dmsReturnCancel"), JSON.toJSONString(dmsCancelRequest), JSON.toJSONString(returnOrderList));

        //单据状态集合
        List<Integer> returnStatusList = returnOrderList.stream().map(OcBReturnOrder::getReturnStatus).collect(Collectors.toList());

        //中台退换货单的单据状态全部为“取消”，不执行取消，直接返回DMS取消成功
        if (checkAllStatus(returnStatusList, ReturnStatusEnum.CANCLE.getVal())) {
            return ValueHolderV14Utils.getSuccessValueHolder("取消成功");
        }

        //中台退换货单的单据状态存在不同状态并存（等待售后确认&完成视为相同状态）不执行取消，直接返回DMS取消失败
        if (returnStatusList.contains(ReturnStatusEnum.WAIT_AFTERSALE_ENSURE.getVal()) && returnStatusList.contains(ReturnStatusEnum.COMPLETION.getVal())) {
            return ValueHolderV14Utils.getFailValueHolder("取消失败");
        }

        //中台退换货单的单据状态全部为“等待售后确认&完成”
        if ((checkAllStatus(returnStatusList, ReturnStatusEnum.WAIT_AFTERSALE_ENSURE.getVal())) || (checkAllStatus(returnStatusList, ReturnStatusEnum.COMPLETION.getVal()))) {
            List<Integer> proReturnStatuses = returnOrderList.stream().map(OcBReturnOrder::getProReturnStatus).collect(Collectors.toList());

            //退货状态全部为“全部入库”
            if (checkAllStatus(proReturnStatuses, ProReturnStatusEnum.WHOLE.getVal())) {
                return ValueHolderV14Utils.getFailValueHolder("取消失败");
            }

            //退货状态存在“部分入库”
            if (checkExistStatus(proReturnStatuses, ProReturnStatusEnum.PORTION.getVal())) {
                if (checkExistTerminationStatus(returnOrderList)) {
                    //存在退换货单有“强制完成标识”
                    return ValueHolderV14Utils.getSuccessValueHolder("取消成功");
                } else {
                    //不存在任何退换货单有“强制完成标识”
                    return ValueHolderV14Utils.getFailValueHolder("取消失败");
                }
            }
        }

        //中台退换货单的单据状态全部为“等待退货入库”
        if (checkAllStatus(returnStatusList, ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal())) {
            ValueHolderV14 v14 = new ValueHolderV14();
            try {
                refundService.oneOcCancle(getDmsUser(), v14, JSONObject.parseArray(JSONObject.toJSONString(returnIds)));
            } catch (Exception e) {
                log.warn(LogUtil.format("cancelReturn error dmsCancelRequest:{}", "dmsReturnCancel"), JSON.toJSONString(dmsCancelRequest), e);
                return ValueHolderV14Utils.getFailValueHolder("取消异常");
            }

            return v14;
        }

        return ValueHolderV14Utils.getFailValueHolder("取消失败");
    }

    private static List<String> getTerminationType() {
        if (CollectionUtils.isEmpty(TERMINATION_TYPE)) {
            TERMINATION_TYPE = Lists.newArrayList(TERMINATION_TYPE_01, TERMINATION_TYPE_02);
            return TERMINATION_TYPE;
        }
        return TERMINATION_TYPE;
    }

    private static User getDmsUser() {
        if (DMS_USER == null) {
            DMS_USER = SystemUserResource.getDmsUser();
        }
        return DMS_USER;
    }

    /**
     * 判断是否都为传入的状态
     *
     * @param statusList
     * @param status
     * @return true:是，false:否
     */
    private boolean checkAllStatus(List<Integer> statusList, Integer status) {
        if (CollectionUtils.isEmpty(statusList)) {
            return false;
        }

        for (Integer integer : statusList) {
            if (!integer.equals(status)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 判断是否存在传入的状态
     *
     * @param statusList
     * @param status
     * @return
     */
    private boolean checkExistStatus(List<Integer> statusList, Integer status) {
        if (CollectionUtils.isEmpty(statusList)) {
            return false;
        }

        for (Integer integer : statusList) {
            if (integer.equals(status)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 是否存在强制完成标识
     *
     * @param returnOrderList
     * @return true:存在，false:不存在
     */
    private boolean checkExistTerminationStatus(List<OcBReturnOrder> returnOrderList) {
        if (CollectionUtils.isEmpty(returnOrderList)) {
            return false;
        }

        for (OcBReturnOrder ocBReturnOrder : returnOrderList) {
            String terminationType = ocBReturnOrder.getTerminationType();
            if (StringUtils.isBlank(terminationType)) {
                continue;
            }
            if ("1".equals(terminationType) || "2".equals(terminationType)) {
                return true;
            }
        }

        return false;
    }
}
