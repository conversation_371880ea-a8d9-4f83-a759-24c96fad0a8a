package com.jackrain.nea.oc.oms.model.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 换货预留库存
 *
 * <AUTHOR>
 * @date 2020/11/18 10:58 下午
 */
public enum IsReservedEnum {

    NO(0, "否"),
    YES(1, "是");

    Integer value;
    String name;

    IsReservedEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public static String getName(Integer code) {
        if (code == null) {
            return "";
        }
        for (IsReservedEnum e : IsReservedEnum.values()) {
            if (e.getValue().equals(code)) {
                return e.getName();
            }
        }
        return "";
    }

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    /**
     * 转为map
     *
     * @return map
     */
    public static Map<Integer, String> convertAllToHashVal() {
        Map<Integer, String> m = new HashMap<>();
        for (IsReservedEnum e : IsReservedEnum.values()) {
            m.put(e.getValue(), e.getName());
        }
        m.put(null, "");
        return m;
    }
}
