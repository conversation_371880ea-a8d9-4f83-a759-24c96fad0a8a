package com.jackrain.nea.oc.oms.model.result;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: wang<PERSON><PERSON>
 * @Date: 2019-03-09 16:43
 * @Version 1.0
 */
@Data
public class SaveOrderItemResult implements Serializable {
    @JSONField(name = "ID")
    private Long id;
    /**
     * SKU
     */
    @JSONField(name = "PS_C_SKU_ECODE")
    private String pscSkueCode;
    /**
     * 数量
     */
    @JSONField(name = "QTY_BILL")
    private BigDecimal qtyBill;
    /**
     * 成交价
     */
    @JSONField(name = "PRICE")
    private BigDecimal price;
    /**
     * 分销金额
     */
    @JSONField(name = "DISTRIBUTION_PRICE")
    private BigDecimal distributionPrice;

    /**
     * 规格
     */
    @JSONField(name = "SKU_SPEC")
    private String sku_spec;
}
