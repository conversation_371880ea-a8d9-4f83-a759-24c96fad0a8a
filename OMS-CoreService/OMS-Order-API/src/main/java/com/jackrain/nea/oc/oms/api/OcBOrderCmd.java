package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.request.CancelOrderModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;

/**
 * <AUTHOR> zhuxing
 * @Date : 2021-12-20 15:07
 * @Description : 零售发货单相关服务
 **/
public interface OcBOrderCmd {

    /**
     * 取消零售发货单
     * @param cancelOrderModel
     * @return
     * @throws NDSException
     */
    ValueHolderV14 cancelOrder(CancelOrderModel cancelOrderModel) throws NDSException;
}
