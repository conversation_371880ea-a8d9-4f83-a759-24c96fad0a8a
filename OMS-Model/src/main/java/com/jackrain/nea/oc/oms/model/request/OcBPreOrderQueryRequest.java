package com.jackrain.nea.oc.oms.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName OcBPreOrderQueryRequest
 * @Description 订单预导入查询条件
 * <AUTHOR>
 * @Date 2022/11/25 15:05
 * @Version 1.0
 */
@Data
public class OcBPreOrderQueryRequest implements Serializable {

    /**
     * 平台单号
     */
    @JSONField(name = "TID")
    private String tid;

    /**
     * 收件人手机
     */
    @JSONField(name = "RECEIVER_MOBILE")
    private String receiverMobile;

    /**
     * 转换状态
     */
    @JSONField(name = "TRANSFER_STATUS")
    private List<String> transferStatus;

    /**
     * 收件人名称
     */
    @JSONField(name = "RECEIVER_NAME")
    private String receiverName;

    /**
     * 流水号
     */
    @JSONField(name = "SERIAL_NUMBER")
    private String serialNum;

    /**
     * 店铺
     */
    @JSONField(name = "CP_C_SHOP_ID")
    private List<Long> cpcShopId;

    /**
     * 时间
     */
    @JSONField(name = "CREATIONDATE")
    private String creationDate;

    /**
     * 创建人
     */
    @JSONField(name = "OWNERID")
    private String ownerId;


    /**
     * 业务员
     */
    @JSONField(name = "SALESMAN_NAME")
    private String salesmanName;

    /**
     * 错误原因
     */
    @JSONField(name = "SYSREMARK")
    private String sysRemark;

}
