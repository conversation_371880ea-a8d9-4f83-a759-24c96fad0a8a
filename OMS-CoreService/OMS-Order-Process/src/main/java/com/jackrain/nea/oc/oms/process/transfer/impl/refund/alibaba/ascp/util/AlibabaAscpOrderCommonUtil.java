package com.jackrain.nea.oc.oms.process.transfer.impl.refund.alibaba.ascp.util;

import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.util.ApplicationContextHandle;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

/**
 * @Author: 黄世新
 * @Date: 2020/2/27 4:35 下午
 * @Version 1.0
 * 退单转单的工具类
 */
@Slf4j
public class AlibabaAscpOrderCommonUtil {

    /**
     * 发货后的状态
     *
     * @param orderStatus
     * @return
     */
    public static boolean checkOrderStatusAfter(Integer orderStatus) {
        return OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger().equals(orderStatus)
                || OmsOrderStatus.PLATFORM_DELIVERY.toInteger().equals(orderStatus)
                || OmsOrderStatus.DEAL_DONE.toInteger().equals(orderStatus);
    }
//
//
//    public static boolean checkOrderWarehouseDelivery(Integer orderStatus) {
//        // @20200706 需求，仓库发货和平台发货一样的处理
//        // 减：4. 零售发货单状态是仓库发货，则更新“转换状态”为已转换，创建或更新未发货退款单。
//        // 加：6. 若订单状态为仓库发货或者平台发货，则进行下一步判断（判断订单类型）；
//        return false;//OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger() == orderStatus;
//    }

    /**
     * 判断订单是否取消或者作废
     *
     * @param orderStatus
     * @return
     */
    public static boolean checkOrderIsCancelAndVoid(Integer orderStatus) {
        return OmsOrderStatus.CANCELLED.toInteger().equals(orderStatus)
                || OmsOrderStatus.SYS_VOID.toInteger().equals(orderStatus);
    }

    public static void printDebugLog(String s, Object... s2) {
        if (log.isDebugEnabled()) {
            log.debug(s, s2);
        }
    }

    /**
     * 是否超过指定天数 1、true 超过 2、false 未超过
     *
     * @param creationDate
     * @return
     */
    public static boolean isPassAppointTime(Date creationDate, Integer expirationDays) {
        Date date = new Date();
        //判断时间是否超过 指定过期天数
        Long expirationTimes = expirationDays * 24 * 60 * 60 * 1000L + creationDate.getTime();
        if (expirationTimes < date.getTime()) {
            return true;
        }
        return false;
    }

    /**
     * 是否超过指定天数 1、true 超过 2、false 未超过
     *
     * @param creationDate
     * @return
     */
    public static boolean isOrderPassAppointTime(Date creationDate) {
        if (null == creationDate) {
            return false;
        }
        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
        // 配置过期天数
        int expirationDays = config.getProperty("oms.oc.alibaba.ascp.order.expire.day", 3);
        return isPassAppointTime(creationDate, expirationDays);
    }
}
