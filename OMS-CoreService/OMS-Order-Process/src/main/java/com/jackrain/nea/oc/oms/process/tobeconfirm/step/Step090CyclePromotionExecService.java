package com.jackrain.nea.oc.oms.process.tobeconfirm.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.StCCycleItemStrategy;
import com.jackrain.nea.oc.oms.services.StCCycleStrategyMatchService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.util.OmsOrderUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @ClassName Step090CyclePromotionExecService
 * @Description 天猫周期购促销
 * <AUTHOR>
 * @Date 2024/8/22 09:19
 * @Version 1.0
 */
@Step(order = 90, description = "天猫周期购促销")
@Slf4j
public class Step090CyclePromotionExecService extends BaseTobeConfirmedProcessStep implements IOmsOrderProcessStep<OcBOrderRelation> {

    @Autowired
    private StCCycleStrategyMatchService cCycleStrategyMatchService;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Override
    public ProcessStepResult<OcBOrderRelation> startProcess(OcBOrderRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        OcBOrder ocBOrder = orderInfo.getOrderInfo();
        List<OcBOrderItem> ocBOrderItemList = orderInfo.getOrderItemList();
        // 判断订单是不是天猫周期购
        Integer isCycle = ocBOrder.getIsCycle();
        if (isCycle == null || isCycle == 0) {
            // 结束
            return new ProcessStepResult<>(StepStatus.SUCCESS);
        }

        //toc残次订单跳过
        if (OmsOrderUtil.isToCCcOrder(orderInfo.getOrderInfo())){
            return new ProcessStepResult<>(StepStatus.SUCCESS);
        }

        log.info(LogUtil.format("TobeConfirmed.Step090CyclePromotionExecService[执行天猫周期购促销]",
                "执行天猫周期购促销", orderInfo.getOrderId()));
        // 匹配策略 需要输入订单、订单明细信息 去获取到需要赠送的东西
        Map<Long, List<StCCycleItemStrategy>> cycleItemStrategyMap = cCycleStrategyMatchService.match(ocBOrder, ocBOrderItemList, 1);

        if (cycleItemStrategyMap.isEmpty()) {
            return new ProcessStepResult<>(StepStatus.SUCCESS);
        }
        // 准备新增商品
        List<OcBOrderItem> giftOrderItemList = cCycleStrategyMatchService.buildGiftOrderItemList(cycleItemStrategyMap, ocBOrder);
        if (CollectionUtils.isEmpty(giftOrderItemList)) {
            return new ProcessStepResult<>(StepStatus.SUCCESS);
        }
        // 订单需要打上标签
        OcBOrder updateOrder = new OcBOrder();
        updateOrder.setId(ocBOrder.getId());
        updateOrder.setModifieddate(new Date());
        updateOrder.setIsHasgift(1);
        updateOrder.setIsPromOrder(1);
        ocBOrderMapper.updateById(updateOrder);
        // 插入赠品
        ocBOrderItemMapper.batchInsert(giftOrderItemList);
        // 根据订单id 查询出来所有的明细
        List<OcBOrderItem> orderItemList = ocBOrderItemMapper.selectOrderItemListOccupy(orderInfo.getOrderId());
        orderInfo.setOrderItemList(orderItemList);
        return new ProcessStepResult<>(StepStatus.SUCCESS, giftOrderItemList);
    }
}
