package com.jackrain.nea.oc.oms.model.enums;
/**
 * jd转单客户期望
 *
 * @author: jqq
 * create at: 2020/11/10 13:20
 */
public enum  JdCustomerExEnum {
    /**
     * 是否插入核销流水
     */
    Return("退货", 10L),

    Exchange("换货", 20L);

    String key;
    Long val;

    JdCustomerExEnum(String k, Long v) {
        this.key = k;
        this.val = v;
    }

    public String getKey() {
        return key;
    }

    public Long getVal() {
        return val;
    }
}
