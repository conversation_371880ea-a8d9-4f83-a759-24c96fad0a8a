package com.jackrain.nea.oc.oms.mapper.ac;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.AcFOrderInvoiceItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface AcFOrderInvoiceItemMapper extends ExtentionMapper<AcFOrderInvoiceItem> {
    @Select("select * from ac_f_order_invoice_item where ac_f_order_invoice_id = #{originId}")
    List<AcFOrderInvoiceItem> queryByOrderInvoiceId(@Param("originId") Long originId);
}