package com.jackrain.nea.oc.oms.process.transfer.impl.refund.currency.step;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.mapper.IpBStandplatRefundItemMapper;
import com.jackrain.nea.oc.oms.mapper.task.OcBOrderToAgTaskMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderToAgStatus;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.enums.TransNodeTipEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsStandPlatRefundRelation;
import com.jackrain.nea.oc.oms.model.resources.SysNotesConstant;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatRefund;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatRefundItem;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.task.OcBOrderToAgTask;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.refund.util.TransRefundNodeTipUtil;
import com.jackrain.nea.oc.oms.services.OcBRefundOrderStandPlatToRefundService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.DateUtil;
import com.jackrain.nea.util.OperateUserUtils;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * @Author: 黄世新
 * @Date: 2020/2/28 8:44 下午
 * @Version 1.0
 */
@Step(order = 80, description = "调用通用自动退款")
@Slf4j
@Component
public class Step080ToStanplatAutoRefund extends BaseStanPlatRefundProcessStep
        implements IOmsOrderProcessStep<OmsStandPlatRefundRelation> {

    @Autowired
    private IpBStandplatRefundItemMapper ipBStandplatRefundItemMapper;

    @Override
    public ProcessStepResult<OmsStandPlatRefundRelation> startProcess(OmsStandPlatRefundRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        List<OmsOrderRelation> omsOrderRelation = orderInfo.getOmsOrderRelation();
        //如果多单的 只有全部标记退款成功才能调用AG
        IpBStandplatRefund ipBStandplatRefund = orderInfo.getIpBStandplatRefund();
        if (log.isDebugEnabled()) {
            log.debug("Step080ToStanplatAutoRefund.startProcess 调通用自动退中间表数据:{},退款单号:{},time:{}",
                    JSONObject.toJSONString(ipBStandplatRefund), ipBStandplatRefund.getReturnNo(), DateUtil.getDateTime());
        }
        OcBOrder ocBOrder = omsOrderRelation.get(0).getOcBOrder();
        boolean isPinduoduo = PlatFormEnum.PINDUODUO.getCode().equals(ocBOrder.getPlatform());

        log.info("是否是拼多多:{}", isPinduoduo);

        //判断店铺是否对接AG
        boolean isJointAg = standPlatAutoRefundService.isToAgByShopStrategy(ocBOrder.getCpCShopId());
        if (isJointAg) {
            boolean isToAg = standPlatAutoRefundService.executeAutoRefund(orderInfo, operateUser, null, null, false);
            if (isToAg) {
                for (OmsOrderRelation orderRelation : omsOrderRelation) {
                    OcBOrder order = orderRelation.getOcBOrder();
                    omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(), OrderLogTypeEnum.AG_SEND_CANCLE.getKey(), "退款单号为:" + ipBStandplatRefund.getReturnNo() + ",订单AG取消发货成功", null, null, operateUser);
                }
                //调用ag成功后 更新成已转换时   需要根据状态去更新  不然并发会导致退款完成的状态不转换
                String remark = SysNotesConstant.SYS_REMARK17;
                TransRefundNodeTipUtil.standardTransTipCAS(ipBStandplatRefund, TransNodeTipEnum.DEFAULT);
                ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(), remark, ipBStandplatRefund);
                if (!isPinduoduo) {
                    return new ProcessStepResult<>(StepStatus.FINISHED, "订单AG退款成功,转换结束!");
                }
            } else {
                if (!isPinduoduo) {
                    //List<Long> ids = new ArrayList<>();
                    for (OmsOrderRelation orderRelation : omsOrderRelation) {
                        OcBOrder order = orderRelation.getOcBOrder();
                        omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(), OrderLogTypeEnum.AG_SEND_CANCLE.getKey(), "退款单号为:" + ipBStandplatRefund.getReturnNo() + ",订单AG取消发货失败", null, null, operateUser);
                        // ids.add(order.getId());
                    }
                    String remark = "订单AG退款失败,转换结束!";
                    // this.addToAgTask(operateUser, ipBTaobaoRefund, ocBOrder, ids);
                    TransRefundNodeTipUtil.standardTransTipCAS(ipBStandplatRefund, TransNodeTipEnum.AG_FAIL);
                    ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(), remark, ipBStandplatRefund);
                    return new ProcessStepResult<>(StepStatus.FAILED, remark);
                }

            }
        } else {
            if (!isPinduoduo) {
                ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                        "未对接AG,转换结束", ipBStandplatRefund);
            }

        }

        // 拼多多额外逻辑
        if (isPinduoduo) {

            List<OcBOrderItem> ocBOrderItems = orderInfo.getOmsOrderRelation().get(0).getOcBOrderItems();

            List<IpBStandplatRefundItem> ipBStandplatRefundItems = ipBStandplatRefundItemMapper.selectList(
                    new QueryWrapper<IpBStandplatRefundItem>().lambda()
                            .eq(IpBStandplatRefundItem::getIpBStandplatRefundId, ipBStandplatRefund.getId()));


            if (CollectionUtils.isEmpty(ipBStandplatRefundItems)) {
                ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                        "未对接AG,平台:拼多多（特殊逻辑）,退款失败！退单中间表明细查找失败,转换结束!", ipBStandplatRefund);
                return new ProcessStepResult<>(StepStatus.FINISHED, "未对接AG,平台:拼多多（特殊逻辑）,退款失败！,转换结束!");
            }

            //处理额外逻辑
            OcBRefundOrderStandPlatToRefundService bean = ApplicationContextHandle.getBean(OcBRefundOrderStandPlatToRefundService.class);
            Boolean auto = bean.executeAutoRefundByPinduoduo(ocBOrder.getCpCShopId(), ocBOrder, ocBOrderItems,
                    orderInfo.getOcBReturnOrder(), ipBStandplatRefund,
                    ipBStandplatRefundItems == null ? BigDecimal.ZERO : ipBStandplatRefundItems.get(0).getReturnQuantity(),
                    operateUser, this.checkOrderStatusFront(ocBOrder.getOrderStatus()));
            if (!auto) {
                ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                        "未对接AG,平台:拼多多（特殊逻辑）,退款失败！,转换结束!", ipBStandplatRefund);
                return new ProcessStepResult<>(StepStatus.FINISHED, "未对接AG,平台:拼多多（特殊逻辑）,退款失败！,转换结束!");
            }
            ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                    "未对接AG,平台:拼多多（特殊逻辑）,退款成功！,转换结束!", ipBStandplatRefund);
            return new ProcessStepResult<>(StepStatus.FINISHED, "未对接AG,平台:拼多多（特殊逻辑）,退款成功！,转换结束!");
        }

        return new ProcessStepResult<>(StepStatus.FINISHED, "未对接AG,转换结束!");
    }


    /**
     * 发货前的状态
     *
     * @param orderStatus
     * @return
     */
    public boolean checkOrderStatusFront(Integer orderStatus) {
        return OmsOrderStatus.TO_BE_ASSIGNED.toInteger().equals(orderStatus)
                || OmsOrderStatus.UNCONFIRMED.toInteger().equals(orderStatus)
                || OmsOrderStatus.CHECKED.toInteger().equals(orderStatus)
                || OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(orderStatus)
                || OmsOrderStatus.IN_DISTRIBUTION.toInteger().equals(orderStatus)
                || OmsOrderStatus.PENDING_WMS.toInteger().equals(orderStatus);
    }

    /**
     * 添加到补偿任务
     *
     * @param operateUser     用户
     * @param ipBTaobaoRefund 中间表
     * @param ocBOrder        订单
     * @param ids             订单id集合
     */
    private void addToAgTask(User operateUser, IpBTaobaoRefund ipBTaobaoRefund, OcBOrder ocBOrder, List<Long> ids) {
        try {
            /* 添加task重试AG任务 */
            OcBOrderToAgTaskMapper orderToAgTaskMapper =
                    ApplicationContextHandle.getBean(OcBOrderToAgTaskMapper.class);
            OcBOrderToAgTask orderToAgTask =
                    orderToAgTaskMapper.selectToAgTaskByRefundId(ipBTaobaoRefund.getRefundId());
            if (Objects.isNull(orderToAgTask)) {
                orderToAgTask = new OcBOrderToAgTask();
                orderToAgTask.setId(ModelUtil.getSequence("oc_b_order_to_ag_task"));
                orderToAgTask.setRefundId(ipBTaobaoRefund.getRefundId());
                orderToAgTask.setOcBOrderId(ocBOrder.getId());
                // or_b_order id集合
                orderToAgTask.setOcBOrderIds(JSONObject.toJSONString(ids));
                // 重试次数
                orderToAgTask.setRetriesTimes(0);
                orderToAgTask.setStatus(OrderToAgStatus.UNTREATED.getVal());
                OperateUserUtils.saveOperator(orderToAgTask, operateUser);

                orderToAgTaskMapper.insert(orderToAgTask);
            } else {
                UpdateWrapper<OcBOrderToAgTask> updateWrapper = new UpdateWrapper<>();
                updateWrapper.set("refund_id", orderToAgTask.getRefundId());
                orderToAgTask.setRefundId(null);
                orderToAgTask.setRemark(null);
                orderToAgTask.setStatus(OrderToAgStatus.UNTREATED.getVal());
                orderToAgTask.setRetriesTimes(0);
                orderToAgTaskMapper.update(orderToAgTask, updateWrapper);

            }
        } catch (Exception ex) {
            log.error("Step080ToAgRefund Insert Task Error", ex);
        }
    }
}
