package com.jackrain.nea.oc.oms.model.enums.ac;

import lombok.Getter;

/**
 * @ClassName InvoiceHeaderTypeEnum
 * @Description
 * @Date 2022/8/31 下午8:00
 * @Created by wuhang
 */
@Getter
public enum InvoiceHeaderTypeEnum {

    PERSONAL("0","个人"),
    COMPANY("1","企业");

    private String code;
    private String value;

    InvoiceHeaderTypeEnum(String code,String value){
        this.code = code;
        this.value = value;
    }
}
