package com.jackrain.nea.oc.oms.api;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.model.relation.OcBReturnAfSendExtend;
import com.jackrain.nea.oc.oms.model.relation.OcBReturnAfSendItemExtend;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

import java.util.List;

/**
 * 发货后退款单 相关前端对接服务
 *
 * @author: 夏继超
 * @since: 2020/3/13
 * create at : 2020/3/13 10:54
 */
public interface RefundFormAfterDeliveryCmd {

    /**
     * 发货后退款单明细新增
     *
     * @param param
     * @param user
     * @return
     */
    ValueHolderV14 saveAfterDeliverItem(JSONObject param, User user) throws NDSException;

    /**
     * 发货后退款单明细删除
     *
     * @param param
     * @param user
     * @return
     */
    ValueHolderV14 deleteAfterDeliverItem(JSONObject param, User user) throws NDSException;

    /**
     * 发货后退款单编辑保存服务
     *
     * @param param
     * @param user
     * @return
     */
    ValueHolderV14 saveAfterDeliver(JSONObject param, User user) throws NDSException;

    /**
     * 发货会退款复制服务
     *
     * @param param
     * @param user
     * @return
     */
    ValueHolderV14 copyAfterDeliver(JSONObject param, User user) throws NDSException;

    /**
     * 已发货后退款单模板下载
     *
     * @param usr
     * @return
     */
    ValueHolderV14 afterDeliverImportDownload(User usr);

    /**
     * 已发货后退款单导入
     *
     * @param afSendList           已发货后退款单主表
     * @param afSendItemExtendList 已发货后退款单明细表
     * @param user
     * @return
     */
    ValueHolderV14 importList(List<OcBReturnAfSendExtend> afSendList, List<OcBReturnAfSendItemExtend> afSendItemExtendList, User user);

    /**
     * 校验是否展示退款按钮
     *
     * @param param
     * @param user
     * @return
     */
    ValueHolderV14 checkDisplayBtn(JSONObject param, User user);

}
