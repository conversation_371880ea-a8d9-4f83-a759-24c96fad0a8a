package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.api.RefundFormAfterDeliveryCmd;
import com.jackrain.nea.oc.oms.model.relation.OcBReturnAfSendExtend;
import com.jackrain.nea.oc.oms.model.relation.OcBReturnAfSendItemExtend;
import com.jackrain.nea.oc.oms.services.RefundFormAfterDeliveryService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: 夏继超
 * @since: 2020/3/13
 * create at : 2020/3/13 11:02
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class RefundFormAfterDeliveryCmdImpl implements RefundFormAfterDeliveryCmd {
    @Autowired
    RefundFormAfterDeliveryService afterDeliveryService;

    @Override
    public ValueHolderV14 saveAfterDeliverItem(JSONObject param, User user) {
        return afterDeliveryService.saveAfterDeliverItem(param, user);
    }

    @Override
    public ValueHolderV14 deleteAfterDeliverItem(JSONObject param, User user) {
        return afterDeliveryService.deleteAfterDeliverItem(param, user);
    }

    @Override
    public ValueHolderV14 saveAfterDeliver(JSONObject param, User user) {
        return afterDeliveryService.saveAfterDeliver(param, user);
    }

    @Override
    public ValueHolderV14 copyAfterDeliver(JSONObject param, User user) {
        return afterDeliveryService.copyAfterDeliver(param, user);
    }

    @Override
    public ValueHolderV14 afterDeliverImportDownload(User usr) {
        return afterDeliveryService.afterDeliverImportDownload(usr);
    }

    @Override
    public ValueHolderV14 importList(List<OcBReturnAfSendExtend> afSendList, List<OcBReturnAfSendItemExtend> afSendItemExtendList, User user) {
        return afterDeliveryService.importList(afSendList, afSendItemExtendList, user);

    }

    @Override
    public ValueHolderV14 checkDisplayBtn(JSONObject param, User user) {
        return afterDeliveryService.checkDisplayBtn(param,user);
    }
}
