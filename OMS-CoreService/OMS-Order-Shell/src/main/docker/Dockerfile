FROM --platform=linux/amd64 registry-vpc.cn-hangzhou.aliyuncs.com/ryytn-r3-uat/ibmjdk:8u402-font

RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && echo 'Asia/Shanghai' >/etc/timezone

ENV LOCAL_PATH /acs/user/src
ENV APP_NAME r3-oc-oms-core-shell-3.0.0-SNAPSHOT

COPY OMS-CoreService/OMS-Order-Shell/target/${APP_NAME}.jar ${LOCAL_PATH}/${APP_NAME}.jar
COPY OMS-CoreService/OMS-Order-Shell/src/main/docker/entrypoint.sh ${LOCAL_PATH}/entrypoint.sh

WORKDIR /acs/user/src

EXPOSE 8080
ENTRYPOINT ["/bin/sh", "entrypoint.sh"]