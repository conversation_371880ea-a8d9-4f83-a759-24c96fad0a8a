package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.api.CheckAddGift;
import com.jackrain.nea.oc.oms.services.CheckAddGiftService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: 夏继超
 * @since: 2019/3/13
 * create at : 2019/3/13 15:15
 */
@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class CheckAddGiftImpl implements CheckAddGift {
    @Autowired
    CheckAddGiftService checkAddGiftService;

    @Override
    public ValueHolderV14 checkAddGift(JSONObject obj) {
        return checkAddGiftService.checkAddGift(obj);
    }
}
