package com.jackrain.nea.oc.oms.tag;

import lombok.Getter;

import java.util.Objects;

/**
 * Description： 标记类型
 * Author: RESET
 * Date: Created in 2020/6/15 20:24
 * Modified By:
 */
public enum TaggerTypeEnum {

    // 匹配策略类型
    LIVE(1, "live", "直播打标"),
    SUPPLY(2, "supply", "轻供打标");

    @Getter
    private Integer value;
    @Getter
    private String code;
    @Getter
    private String description;

    TaggerTypeEnum(Integer value, String code, String description) {
        this.value = value;
        this.code = code;
        this.description = description;
    }

    @Override
    public String toString() {
        return String.valueOf(value);
    }

    public static TaggerTypeEnum fromValue(Integer v) {
        for (TaggerTypeEnum c : TaggerTypeEnum.values()) {
            if (Objects.equals(v, c.value)) {
                return c;
            }
        }
        throw new IllegalArgumentException(String.valueOf(v));
    }

}
