package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.oc.oms.api.OmsManualEqualExchangeCmd;
import com.jackrain.nea.oc.oms.dto.EqualExchangeStInfo;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2022/7/12 上午10:53
 * @Version 1.0
 */
@Api(value = "OmsExpiryDateCtrl", tags = "手动指定对等换货")
@Slf4j
@RestController
public class OmsManualEqualExchangeCtrl {

    @Autowired
    private OmsManualEqualExchangeCmd omsManualEqualExchangeCmd;
    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @RequestMapping("api/cs/oc/oms/v1/selectEqualExchangeOrder")
    public ValueHolderV14 selectEqualExchangeOrder(HttpServletRequest request,
                                           @RequestBody JSONObject obj){
        ValueHolderV14 holder = new ValueHolderV14();
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        if (user == null) {
            holder.setCode(-1);
            holder.setMessage("用户不能为空!");
            return holder;
        }
        JSONArray ids = obj.getJSONArray("ids");
        if (ids == null) {
            holder.setCode(-1);
            holder.setMessage("参数有误");
            return holder;
        }
        List<Long> longs = JSON.parseArray(ids.toJSONString(), Long.class);
        return omsManualEqualExchangeCmd.selectEqualExchangeOrder(longs);

    }




    @RequestMapping("api/cs/oc/oms/v1/selectEqualExchange")
    public ValueHolderV14<List<EqualExchangeStInfo>> selectExpiryDate(HttpServletRequest request,
                                                                      String sku_code) {
        ValueHolderV14<List<EqualExchangeStInfo>> holder = new ValueHolderV14<>();
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        if (user == null) {
            holder.setCode(-1);
            holder.setMessage("用户不能为空!");
            return holder;
        }
        if (StringUtils.isEmpty(sku_code)) {
            holder.setCode(-1);
            holder.setMessage("参数不能为空!");
            return holder;
        }
        return omsManualEqualExchangeCmd.selectEqualExchange(sku_code);


    }


    @RequestMapping("api/cs/oc/oms/v1/confirmEqualExchange")
    public ValueHolderV14 executeOmsOrderItem(HttpServletRequest request,
                                              @RequestBody EqualExchangeStInfo  obj){
        ValueHolderV14 holder = new ValueHolderV14();
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        if (user == null) {
            holder.setCode(-1);
            holder.setMessage("用户不能为空!");
            return holder;
        }
        if (obj == null) {
            holder.setCode(-1);
            holder.setMessage("参数有误!");
            return holder;
        }
        return omsManualEqualExchangeCmd.confirmEqualExchange(obj, user);
    }

}
