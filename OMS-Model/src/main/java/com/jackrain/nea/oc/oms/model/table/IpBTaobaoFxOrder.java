package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@TableName(value = "ip_b_taobao_fx_order")
@Data
@Document(index = "ip_b_taobao_fx_order", type = "ip_b_taobao_fx_order")
@ApiModel
public class IpBTaobaoFxOrder extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "ID")
    private Long id;

    @JSONField(name = "SUPPLIER_MEMO")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "SUPPLIER_MEMO")
    private String supplierMemo;

    @JSONField(name = "FENXIAO_ID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "FENXIAO_ID")
    private Long fenxiaoId;

    @JSONField(name = "PAY_TYPE")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "PAY_TYPE")
    private String payType;

    @JSONField(name = "TRADE_TYPE")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "TRADE_TYPE")
    private String tradeType;

    @JSONField(name = "DISTRIBUTOR_FROM")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "DISTRIBUTOR_FROM")
    private String distributorFrom;

    @JSONField(name = "CID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "CID")
    private Long cid;

    @JSONField(name = "STATUS")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "STATUS")
    private String status;

    @JSONField(name = "BUYER_NICK")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "BUYER_NICK")
    private String buyerNick;

    @JSONField(name = "MEMO")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "MEMO")
    private String memo;

    @JSONField(name = "TC_ORDER_ID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "TC_ORDER_ID")
    private Long tcOrderId;

    @JSONField(name = "NAME")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "NAME")
    private String name;

    @JSONField(name = "PHONE")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "PHONE")
    private String phone;

    @JSONField(name = "MOBILE_PHONE")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "MOBILE_PHONE")
    private String mobilePhone;

    @JSONField(name = "ADDRESS")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "ADDRESS")
    private String address;

    @JSONField(name = "DISTRICT")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "DISTRICT")
    private String district;

    @JSONField(name = "CITY")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "CITY")
    private String city;

    @JSONField(name = "ZIP")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "ZIP")
    private String zip;

    @JSONField(name = "STATE")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "STATE")
    private String state;

    @JSONField(name = "SHIPPING")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "SHIPPING")
    private String shipping;

    @JSONField(name = "LOGISTICS_COMPANY_NAME")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "LOGISTICS_COMPANY_NAME")
    private String logisticsCompanyName;

    @JSONField(name = "LOGISTICS_ID")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "LOGISTICS_ID")
    private String logisticsId;

    @JSONField(name = "ISV_CUSTOM_KEY")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "ISV_CUSTOM_KEY")
    private String isvCustomKey;

    @JSONField(name = "ISV_CUSTOM_VALUE")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "ISV_CUSTOM_VALUE")
    private String isvCustomValue;

    @JSONField(name = "END_TIME")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "END_TIME")
    private Date endTime;

    @JSONField(name = "SUPPLIER_FLAG")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "SUPPLIER_FLAG")
    private Long supplierFlag;

    @JSONField(name = "BUYER_PAYMENT")
    @Field(type = FieldType.Double)
    @ApiModelProperty(name = "BUYER_PAYMENT")
    private BigDecimal buyerPayment;

    @JSONField(name = "SUPPLIER_FROM")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "SUPPLIER_FROM")
    private String supplierFrom;

    @JSONField(name = "SUPPLIER_USERNAME")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "SUPPLIER_USERNAME")
    private String supplierUsername;

    @JSONField(name = "DISTRIBUTOR_USERNAME")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "DISTRIBUTOR_USERNAME")
    private String distributorUsername;

    @JSONField(name = "CREATED")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "CREATED")
    private Date created;

    @JSONField(name = "MODIFIED")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "MODIFIED")
    private Date modified;

    @JSONField(name = "ALIPAY_NO")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "ALIPAY_NO")
    private String alipayNo;

    @JSONField(name = "TOTAL_FEE")
    @Field(type = FieldType.Double)
    @ApiModelProperty(name = "TOTAL_FEE")
    private BigDecimal totalFee;

    @JSONField(name = "POST_FEE")
    @Field(type = FieldType.Double)
    @ApiModelProperty(name = "POST_FEE")
    private BigDecimal postFee;

    @JSONField(name = "DISTRIBUTOR_PAYMENT")
    @Field(type = FieldType.Double)
    @ApiModelProperty(name = "DISTRIBUTOR_PAYMENT")
    private BigDecimal distributorPayment;

    @JSONField(name = "SNAPSHOT_URL")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "SNAPSHOT_URL")
    private String snapshotUrl;

    @JSONField(name = "PAY_TIME")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "PAY_TIME")
    private Date payTime;

    @JSONField(name = "CONSIGN_TIME")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "CONSIGN_TIME")
    private Date consignTime;

    @JSONField(name = "CP_C_SHOP_ID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "CP_C_SHOP_ID")
    private Long cpCShopId;

    @JSONField(name = "ISTRANS")
    @Field(type = FieldType.Integer)
    @ApiModelProperty(name = "ISTRANS")
    private Integer istrans;

    @JSONField(name = "TRANSDATE")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "TRANSDATE")
    private Date transdate;

    @JSONField(name = "INSERTDATE")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "INSERTDATE")
    private Date insertdate;

    @JSONField(name = "SYSREMARK")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "SYSREMARK")
    private String sysremark;

    @JSONField(name = "PLATFORM_FLAG")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "PLATFORM_FLAG")
    private Long platformFlag;

    @JSONField(name = "AD_ORG_ID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "AD_ORG_ID")
    private Long adOrgId;

    @JSONField(name = "ISACTIVE")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "ISACTIVE")
    private String isactive;

    @JSONField(name = "AD_CLIENT_ID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "AD_CLIENT_ID")
    private Long adClientId;

    @JSONField(name = "OWNERID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "OWNERID")
    private Long ownerid;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "OWNERNAME")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "OWNERNAME")
    private String ownername;

    @JSONField(name = "CREATIONDATE")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "CREATIONDATE")
    private Date creationdate;

    @JSONField(name = "MODIFIERID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "MODIFIERID")
    private Long modifierid;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "MODIFIERENAME")
    private String modifierename;

    @JSONField(name = "MODIFIERNAME")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "MODIFIERNAME")
    private String modifiername;

    @JSONField(name = "MODIFIEDDATE")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "MODIFIEDDATE")
    private Date modifieddate;

    @JSONField(name = "TRANS_COUNT")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "TRANS_COUNT")
    private Long transCount;

    @JSONField(name = "OAID")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "OAID")
    private String oaid;

    @JSONField(name = "RESERVE_BIGINT01")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_BIGINT01")
    private Long reserveBigint01;

    @JSONField(name = "RESERVE_BIGINT02")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_BIGINT02")
    private Long reserveBigint02;

    @JSONField(name = "RESERVE_BIGINT03")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_BIGINT03")
    private Long reserveBigint03;

    @JSONField(name = "RESERVE_BIGINT04")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_BIGINT04")
    private Long reserveBigint04;

    @JSONField(name = "RESERVE_BIGINT05")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_BIGINT05")
    private Long reserveBigint05;

    @JSONField(name = "RESERVE_BIGINT06")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_BIGINT06")
    private Long reserveBigint06;

    @JSONField(name = "RESERVE_BIGINT07")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_BIGINT07")
    private Long reserveBigint07;

    @JSONField(name = "RESERVE_BIGINT08")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_BIGINT08")
    private Long reserveBigint08;

    @JSONField(name = "RESERVE_BIGINT09")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_BIGINT09")
    private Long reserveBigint09;

    @JSONField(name = "RESERVE_BIGINT10")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_BIGINT10")
    private Long reserveBigint10;

    @JSONField(name = "RESERVE_DECIMAL01")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_DECIMAL01")
    private Long reserveDecimal01;

    @JSONField(name = "RESERVE_DECIMAL02")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_DECIMAL02")
    private Long reserveDecimal02;

    @JSONField(name = "RESERVE_DECIMAL03")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_DECIMAL03")
    private Long reserveDecimal03;

    @JSONField(name = "RESERVE_DECIMAL04")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_DECIMAL04")
    private Long reserveDecimal04;

    @JSONField(name = "RESERVE_DECIMAL05")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_DECIMAL05")
    private Long reserveDecimal05;

    @JSONField(name = "RESERVE_DECIMAL06")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_DECIMAL06")
    private Long reserveDecimal06;

    @JSONField(name = "RESERVE_DECIMAL07")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_DECIMAL07")
    private Long reserveDecimal07;

    @JSONField(name = "RESERVE_DECIMAL08")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_DECIMAL08")
    private Long reserveDecimal08;

    @JSONField(name = "RESERVE_DECIMAL09")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_DECIMAL09")
    private Long reserveDecimal09;

    @JSONField(name = "RESERVE_DECIMAL10")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_DECIMAL10")
    private Long reserveDecimal10;

    @JSONField(name = "RESERVE_VARCHAR01")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "RESERVE_VARCHAR01")
    private String reserveVarchar01;

    @JSONField(name = "RESERVE_VARCHAR02")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "RESERVE_VARCHAR02")
    private String reserveVarchar02;

    @JSONField(name = "RESERVE_VARCHAR03")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "RESERVE_VARCHAR03")
    private String reserveVarchar03;

    @JSONField(name = "RESERVE_VARCHAR04")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "RESERVE_VARCHAR04")
    private String reserveVarchar04;

    @JSONField(name = "RESERVE_VARCHAR05")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "RESERVE_VARCHAR05")
    private String reserveVarchar05;

    @JSONField(name = "RESERVE_VARCHAR06")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "RESERVE_VARCHAR06")
    private String reserveVarchar06;

    @JSONField(name = "RESERVE_VARCHAR07")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "RESERVE_VARCHAR07")
    private String reserveVarchar07;

    @JSONField(name = "RESERVE_VARCHAR08")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "RESERVE_VARCHAR08")
    private String reserveVarchar08;

    @JSONField(name = "RESERVE_VARCHAR09")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "RESERVE_VARCHAR09")
    private String reserveVarchar09;

    @JSONField(name = "RESERVE_VARCHAR10")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "RESERVE_VARCHAR10")
    private String reserveVarchar10;
}