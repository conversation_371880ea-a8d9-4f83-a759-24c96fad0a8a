package com.jackrain.nea.oc.oms.services.task;


import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.inf.api.wms.in.SgBRefundInTaskCmd;
import com.burgeon.r3.sg.inf.model.request.wms.in.SgBRefundInTaskRequest;
import com.burgeon.r3.sg.inf.model.result.oms.SgBRefundInTaskResult;
import com.burgeon.r3.sg.store.common.SgStoreConstantsIF;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.Enum.ThirdWmsTypeEnum;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.task.OcBRefundPackageTaskMapper;
import com.jackrain.nea.oc.oms.model.request.OmsRefundInSaveRequest;
import com.jackrain.nea.oc.oms.model.table.task.OcBRefundPackageTask;
import com.jackrain.nea.oc.oms.services.returnin.OcRefundInService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Auther: chenhao
 * @Date: 2022-07-20 10:49
 * @Description: 退货包裹状态通知接口定时任务具体业务
 */

@Slf4j
@Component
public class OcBRefundPackageTaskService {

    @Autowired
    private OcBRefundPackageTaskMapper ocBRefundPackageTaskMapper;
    @Reference(group = "sg", version = "1.0")
    SgBRefundInTaskCmd inTaskCmd;
    @Autowired
    private OcRefundInService inService;

    public ValueHolderV14 execute() {
        ValueHolderV14 v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "执行成功！");
        log.info(LogUtil.format("OcBRefundPackageTaskService.execute start",
                "OcBRefundPackageTaskService.execute"));
        //查数据
        List<String> wmsType = Lists.newArrayList(ThirdWmsTypeEnum.QMWMS.getCode(), ThirdWmsTypeEnum.FLWMS.getCode());
        List<OcBRefundPackageTask> inTaskList = ocBRefundPackageTaskMapper.selectList(new LambdaQueryWrapper<OcBRefundPackageTask>()
                .lt(OcBRefundPackageTask::getFailedCount, SgConstantsIF.FAIL_COUNT)
                .ne(OcBRefundPackageTask::getTransformStatus, SgStoreConstantsIF.WMS_TO_RESULT_STATUS_SUCCESS)
                .in(OcBRefundPackageTask::getWmsWarehouseType, wmsType));
        if (CollectionUtils.isEmpty(inTaskList)) {
            v14.setMessage("无处理数据！");
            return v14;
        }
        try {
            Map<String, List<OcBRefundPackageTask>> listMap =
                    inTaskList.stream().collect(Collectors.groupingBy(OcBRefundPackageTask::getWmsWarehouseType));
            if (MapUtils.isNotEmpty(listMap) && CollectionUtils.isNotEmpty(listMap.get(ThirdWmsTypeEnum.QMWMS.getCode()))) {
                executeForJw(listMap.get(ThirdWmsTypeEnum.QMWMS.getCode()));
            }
            if (MapUtils.isNotEmpty(listMap) && CollectionUtils.isNotEmpty(listMap.get(ThirdWmsTypeEnum.FLWMS.getCode()))) {
                executeForFl(listMap.get(ThirdWmsTypeEnum.FLWMS.getCode()));
            }
        } catch (Exception e) {
            log.error(LogUtil.format("OcBRefundPackageTaskService.execute error:{}",
                    "OcBRefundPackageTaskService.execute"), Throwables.getStackTraceAsString(e));
            v14.setCode(ResultCode.FAIL);
            v14.setMessage(e.getMessage());
        }
        return v14;
    }

    /**
     * 处理巨沃逻辑
     *
     * @param inTaskList
     */
    private void executeForJw(List<OcBRefundPackageTask> inTaskList) {
        List<SgBRefundInTaskRequest> requestLit = new ArrayList<>();
        HashMap<Long, OcBRefundPackageTask> inTaskHashMap = new HashMap<>(16);
        //调用sg check
        for (OcBRefundPackageTask inTask : inTaskList) {
            requestLit.add(buildCheckRequest(inTask));
            inTaskHashMap.put(inTask.getId(), inTask);
        }

        log.info(LogUtil.format("OcBRefundPackageTaskService.check requestLit.size:{} ",
                "OcBRefundPackageTaskService.check "), requestLit.size());
        ValueHolderV14<SgBRefundInTaskResult> execute = inTaskCmd.refunPpackageCheck(requestLit);
        log.info(LogUtil.format("OcBRefundPackageTaskService.check.ValueHolderV14:{} ",
                "OcBRefundPackageTaskService.check.ValueHolderV14 "), JSONObject.toJSONString(execute));
        SgBRefundInTaskResult data = execute.getData();
        if (data != null) {
            //check 有问题d数据更新
            List<SgBRefundInTaskRequest> failList = data.getFailList();
            if (CollectionUtils.isNotEmpty(failList)) {
                for (SgBRefundInTaskRequest inTaskRequest : failList) {
                    //id 封装参数d时候就传过去了
                    updateFail(inTaskHashMap.get(inTaskRequest.getId()), "退货包裹数据校验异常：" + inTaskRequest.getFailedReason());
                }
            }
            //成功的走下面逻辑
            List<SgBRefundInTaskRequest> successList = data.getSuccessList();
            if (CollectionUtils.isNotEmpty(successList)) {
                for (SgBRefundInTaskRequest inTaskRequest : successList) {
                    OcBRefundPackageTask inTask = inTaskHashMap.get(inTaskRequest.getId());
                    OmsRefundInSaveRequest omsRefundInSaveRequest = buildRequest(inTask.getMessage());
                    log.info(LogUtil.format("OcBRefundPackageTaskService.omsRefundInSaveRequest :{} ",
                            "OcBRefundPackageTaskService.omsRefundInSaveRequest "), JSONObject.toJSONString(omsRefundInSaveRequest));
                    // 新增更新退货入库结果单
                    ValueHolderV14<String> save = inService.save(omsRefundInSaveRequest);
                    log.info(LogUtil.format("OcBRefundPackageTaskService.save :{} ",
                            "OcBRefundPackageTaskService.save "), JSONObject.toJSONString(save));
                    if (!save.isOK()) {
                        updateFail(inTask, "退货包裹数据新增更新退货入库结果单异常：" + save.getMessage());
                        continue;
                    }
                    //都成功记录成功
                    updateSuccess(inTask);
                }
            }
        } else {
            throw new NDSException("ckeck 有误，无返回值");
        }
    }

    /**
     * 处理富勒逻辑
     *
     * @param inTaskList
     */
    private void executeForFl(List<OcBRefundPackageTask> inTaskList) {
        List<SgBRefundInTaskRequest> requestLit = new ArrayList<>();
        HashMap<Long, OcBRefundPackageTask> inTaskHashMap = new HashMap<>(16);
        //调用sg check
        for (OcBRefundPackageTask inTask : inTaskList) {
            requestLit.add(buildCheckRequest(inTask));
            inTaskHashMap.put(inTask.getId(), inTask);
        }

        log.info(LogUtil.format("OcBRefundPackageTaskService.check requestLit.size:{} ",
                "OcBRefundPackageTaskService.check "), requestLit.size());
        ValueHolderV14<SgBRefundInTaskResult> execute = inTaskCmd.refunPpackageCheckForFl(requestLit);
        log.info(LogUtil.format("OcBRefundPackageTaskService.check.ValueHolderV14:{} ",
                "OcBRefundPackageTaskService.check.ValueHolderV14 "), JSONObject.toJSONString(execute));
        SgBRefundInTaskResult data = execute.getData();
        if (data != null) {
            //check 有问题d数据更新
            List<SgBRefundInTaskRequest> failList = data.getFailList();
            if (CollectionUtils.isNotEmpty(failList)) {
                for (SgBRefundInTaskRequest inTaskRequest : failList) {
                    //id 封装参数d时候就传过去了
                    updateFail(inTaskHashMap.get(inTaskRequest.getId()), "退货包裹数据校验异常：" + inTaskRequest.getFailedReason());
                }
            }
            //成功的走下面逻辑
            List<SgBRefundInTaskRequest> successList = data.getSuccessList();
            if (CollectionUtils.isNotEmpty(successList)) {
                for (SgBRefundInTaskRequest inTaskRequest : successList) {
                    OcBRefundPackageTask inTask = inTaskHashMap.get(inTaskRequest.getId());
                    OmsRefundInSaveRequest omsRefundInSaveRequest = buildRequestForFl(inTask.getMessage());
                    log.info(LogUtil.format("OcBRefundPackageTaskService.omsRefundInSaveRequest :{} ",
                            "OcBRefundPackageTaskService.omsRefundInSaveRequest "), JSONObject.toJSONString(omsRefundInSaveRequest));
                    // 新增更新退货入库结果单
                    ValueHolderV14<String> save = inService.save(omsRefundInSaveRequest);
                    log.info(LogUtil.format("OcBRefundPackageTaskService.save :{} ",
                            "OcBRefundPackageTaskService.save "), JSONObject.toJSONString(save));
                    if (!save.isOK()) {
                        updateFail(inTask, "退货包裹数据新增更新退货入库结果单异常：" + save.getMessage());
                        continue;
                    }
                    //都成功记录成功
                    updateSuccess(inTask);
                }
            }
        } else {
            throw new NDSException("ckeck 有误，无返回值");
        }
    }

    /**
     * 新增退货入库结果单参数
     *
     * @param msg 报文
     * @return OmsRefundInSaveRequest
     */
    private OmsRefundInSaveRequest buildRequest(String msg) {
        JSONObject request = JSONObject.parseObject(msg);

        JSONObject orderMsg = request.getJSONObject("order");
        JSONArray packages = request.getJSONArray("packages");
        OmsRefundInSaveRequest.ReturnOrder order = new OmsRefundInSaveRequest.ReturnOrder();
        //入库仓库
        order.setWarehouseCode(orderMsg.getString("warehouseCode"));
        List<OmsRefundInSaveRequest.OrderLine> orderLineList = new ArrayList<>();
        packages.forEach(pack -> {
            JSONObject packObj = (JSONObject) pack;

            //物流公司
            order.setLogisticsCode(packObj.getString("logisticsCode"));
            OmsRefundInSaveRequest.SenderInfo info = new OmsRefundInSaveRequest.SenderInfo();
            //物流单号
            order.setExpressCode(packObj.getString("expressCode"));
            //姓名
            info.setName(packObj.getString("signUserName"));
            order.setSenderInfo(info);
            //备注
            order.setRemark(packObj.getString("remarks"));
            //WMS单据编号
            order.setReturnOrderId(orderMsg.getString("orderCode"));

            JSONArray items = packObj.getJSONArray("items");
            items.forEach(item -> {
                JSONObject orderLine = (JSONObject) item;
                OmsRefundInSaveRequest.OrderLine line = new OmsRefundInSaveRequest.OrderLine();
                //条码
                line.setItemCode(orderLine.getString("itemCode"));
                //数量
                line.setActualQty(orderLine.getBigDecimal("quantity"));
                //类型
                line.setAbnormalFlag(0);
                orderLineList.add(line);
            });
        });

        OmsRefundInSaveRequest omsRefundInSaveRequest = new OmsRefundInSaveRequest();
        omsRefundInSaveRequest.setReturnInType(0);
        omsRefundInSaveRequest.setOrderLines(orderLineList);
        omsRefundInSaveRequest.setReturnOrder(order);

        return omsRefundInSaveRequest;
    }

    /**
     * 新增退货入库结果单参数(富勒)
     *
     * @param msg 报文
     * @return OmsRefundInSaveRequest
     */
    private OmsRefundInSaveRequest buildRequestForFl(String msg) {
        JSONObject request = JSONObject.parseObject(msg);

        JSONObject orderMsg = request.getJSONObject("order");
        JSONArray packages = request.getJSONArray("packages");
        OmsRefundInSaveRequest.ReturnOrder order = new OmsRefundInSaveRequest.ReturnOrder();
        //入库仓库
        order.setWarehouseCode(orderMsg.getString("warehouseCode"));
        List<OmsRefundInSaveRequest.OrderLine> orderLineList = new ArrayList<>();
        packages.forEach(pack -> {
            JSONObject packObj = (JSONObject) pack;

            //物流公司
            order.setLogisticsCode(packObj.getString("logisticsCode"));
            OmsRefundInSaveRequest.SenderInfo info = new OmsRefundInSaveRequest.SenderInfo();
            //物流单号
            order.setExpressCode(packObj.getString("expressCode"));
            //姓名
            info.setName(packObj.getString("signUserName"));
            order.setSenderInfo(info);
            //备注
            order.setRemark(packObj.getString("remarks"));
            //WMS单据编号
            order.setReturnOrderId(orderMsg.getString("orderCode"));
            //富勒库存地点
            order.setStorageLocation(packObj.getString("remarks"));

            JSONArray items = packObj.getJSONArray("items");
            items.forEach(item -> {
                JSONObject orderLine = (JSONObject) item;
                OmsRefundInSaveRequest.OrderLine line = new OmsRefundInSaveRequest.OrderLine();
                //条码
                line.setItemCode(orderLine.getString("itemCode"));
                //数量
                line.setActualQty(orderLine.getBigDecimal("quantity"));
                //类型
                line.setAbnormalFlag(0);
                //商品属性
                line.setInventoryType(orderLine.getString("status"));
                //商品效期
                line.setBatchCode(orderLine.getString("amount"));
                orderLineList.add(line);
            });
        });

        OmsRefundInSaveRequest omsRefundInSaveRequest = new OmsRefundInSaveRequest();
        omsRefundInSaveRequest.setReturnInType(0);
        omsRefundInSaveRequest.setOrderLines(orderLineList);
        omsRefundInSaveRequest.setReturnOrder(order);

        return omsRefundInSaveRequest;
    }

    /**
     * 封装请求参数
     *
     * @param inTask inTask
     * @return SgBRefundInTaskRequest
     */
    private SgBRefundInTaskRequest buildCheckRequest(OcBRefundPackageTask inTask) {
        SgBRefundInTaskRequest refundInTaskRequest = new SgBRefundInTaskRequest();
        refundInTaskRequest.setId(inTask.getId());
        refundInTaskRequest.setMessage(inTask.getMessage());
        refundInTaskRequest.setWarehouseCode(inTask.getWarehouseCode());
        return refundInTaskRequest;
    }

    /**
     * 更新错误信息
     *
     * @param inTask       中间表数据
     * @param failedReason 失败原因
     */
    private void updateFail(OcBRefundPackageTask inTask, String failedReason) {
        OcBRefundPackageTask update = new OcBRefundPackageTask();
        update.setId(inTask.getId());
        int failedConut = Optional.ofNullable(inTask.getFailedCount()).orElse(0) + 1;
        update.setFailedCount(failedConut);
        update.setFailedReason(failedReason.length() > 500 ? failedReason.substring(500) : failedReason);
        update.setTransformStatus(SgStoreConstantsIF.WMS_TO_RESULT_STATUS_FAILED);
        ocBRefundPackageTaskMapper.updateById(update);
    }

    /**
     * 更新成功信息
     *
     * @param inTask 中间表数据
     */
    private void updateSuccess(OcBRefundPackageTask inTask) {
        OcBRefundPackageTask update = new OcBRefundPackageTask();
        update.setId(inTask.getId());
        update.setTransformStatus(SgStoreConstantsIF.WMS_TO_RESULT_STATUS_SUCCESS);
        update.setTransformationData(new Date());
        ocBRefundPackageTaskMapper.update(update, new LambdaUpdateWrapper<OcBRefundPackageTask>()
                .set(OcBRefundPackageTask::getFailedReason, null)
                .eq(OcBRefundPackageTask::getId, inTask.getId()));
    }

}
