package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.IpBTimeOrderVipOccupyItem;
import org.apache.commons.lang.StringUtils;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.jdbc.SQL;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Component
@Mapper
public interface IpBTimeOrderVipOccupyItemMapper extends ExtentionMapper<IpBTimeOrderVipOccupyItem> {

    /**
     * 更新库存占用明细 未确认及缺货作废
     */
    @UpdateProvider(type = SqlBuilder.class, method = "updateIsActiveNByStatusAndMainId")
    int updateIsActiveNByStatusAndMainId(@Param("ocBVipComDistributionId") Long ocBVipComDistributionId
            , @Param("modifierId") Long modifierId
            , @Param("modifierEName") String modifierEName
            , @Param("modifierName") String modifierName
            , @Param("modifiedDate") Date modifiedDate);

    /**
     * 更新库存占用明细
     */
    @UpdateProvider(type = SqlBuilder.class, method = "updateByCondition")
    int updateByCondition(@Param("id") Long id
            , @Param("status") Integer status
            , @Param("isActive") String isActive
            , @Param("amount") BigDecimal amount
            , @Param("outStockQuantity") BigDecimal outStockQuantity
            , @Param("ocBVipComDistributionId") Long ocBVipComDistributionId
            , @Param("distributionNo") String distributionNo
            , @Param("modifierId") Long modifierId
            , @Param("modifierEName") String modifierEName
            , @Param("modifierName") String modifierName
            , @Param("modifiedDate") Date modifiedDate);

    /**
     * 订单SQL创建器
     */
    class SqlBuilder {
        /**
         * 更新库存占用明细
         */
        public String updateByCondition(@Param("id") Long id
                , @Param("status") Integer status
                , @Param("isActive") String isActive
                , @Param("amount") BigDecimal amount
                , @Param("outStockQuantity") BigDecimal outStockQuantity
                , @Param("ocBVipComDistributionId") Long ocBVipComDistributionId
                , @Param("distributionNo") String distributionNo
                , @Param("modifierId") Long modifierId
                , @Param("modifierEName") String modifierEName
                , @Param("modifierName") String modifierName
                , @Param("modifiedDate") Date modifiedDate
        ) {
            return new SQL() {
                {
                    UPDATE("ip_b_time_order_vip_occupy_item");
                    if (status != null) {
                        SET("status=#{status}");
                    }
                    if (StringUtils.isNotBlank(isActive)) {
                        SET("isactive=#{isActive}");
                    }
                    if (amount != null) {
                        SET("amount=#{amount}");
                    }
                    if (outStockQuantity != null) {
                        SET("out_stock_quantity=#{outStockQuantity}");
                    }
                    if (ocBVipComDistributionId != null) {
                        SET("oc_b_vipcom_distribution_id=#{ocBVipComDistributionId}");
                    }
                    if (StringUtils.isNotBlank(distributionNo)) {
                        SET("distribution_no=#{distributionNo}");
                    }
                    if (modifierId != null) {
                        SET("modifierid = #{modifierId} ");
                    }
                    if (StringUtils.isNotBlank(modifierEName)) {
                        SET("modifierename = #{modifierEName} ");
                    }
                    if (StringUtils.isNotBlank(modifierName)) {
                        SET("modifiername = #{modifierName} ");
                    }
                    if (modifiedDate != null) {
                        SET("modifieddate = #{modifiedDate} ");
                    }
                    WHERE("id=#{id}");
                }
            }.toString();
        }

        /**
         * 更新库存占用明细
         */
        public String updateIsActiveNByStatusAndMainId(@Param("ocBVipComDistributionId") Long ocBVipComDistributionId
                , @Param("modifierId") Long modifierId
                , @Param("modifierEName") String modifierEName
                , @Param("modifierName") String modifierName
                , @Param("modifiedDate") Date modifiedDate
        ) {
            return new SQL() {
                {
                    UPDATE("ip_b_time_order_vip_occupy_item");
                    SET("status = 5");
                    SET("isactive = 'N'");
                    if (modifierId != null) {
                        SET("modifierid = #{modifierId} ");
                    }
                    if (StringUtils.isNotBlank(modifierEName)) {
                        SET("modifierename = #{modifierEName} ");
                    }
                    if (StringUtils.isNotBlank(modifierName)) {
                        SET("modifiername = #{modifierName} ");
                    }
                    if (modifiedDate != null) {
                        SET("modifieddate = #{modifiedDate} ");
                    }
                    WHERE("ip_b_time_order_vip_id = #{ocBVipComDistributionId} " +
                            "and status IN(1,2) and isactive ='Y'");
                }
            }.toString();
        }
    }

    /**
     * 获取库存占用明细（单个）
     *
     * @param orderId 时效订单ID
     * @return 返回结果
     */
    @Select("SELECT * FROM ip_b_time_order_vip_occupy_item WHERE ip_b_time_order_vip_id=#{orderId} and status <> 5")
    List<IpBTimeOrderVipOccupyItem> selectOrderOccupyItemList(@Param("orderId") long orderId);

    @Select("SELECT * FROM ip_b_time_order_vip_occupy_item WHERE ip_b_time_order_vip_id=#{orderId} and status <> 5 limit 1")
    IpBTimeOrderVipOccupyItem selectOneOrderOccupyItem(@Param("orderId") long orderId);


    /**
     * 获取库存占用明细（根据状态）
     *
     * @param orderId 时效订单ID
     * @param status  状态
     * @return 返回结果
     */
    @Select("SELECT * FROM ip_b_time_order_vip_occupy_item WHERE ip_b_time_order_vip_id=#{orderId} and status = #{status}")
    List<IpBTimeOrderVipOccupyItem> selectOrderOccupyItemListByStatus(@Param("orderId") long orderId, @Param("status") int status);

    /**
     * 获取库存占用明细（批量）
     *
     * @param orderId 时效订单ID集合
     * @return 返回结果
     */
    @Select("<script> " +
            "SELECT * FROM ip_b_time_order_vip_occupy_item " +
            "WHERE status != 4 and ip_b_time_order_vip_id in " +
            "<foreach item='item' index='index' collection='orderIds' " +
            "open='(' separator=',' close=')'> #{item} </foreach> " +
            "</script> ")
    List<IpBTimeOrderVipOccupyItem> selectOrderOccupyItemListByOrderIds(@Param("orderIds") List<Long> orderId);

    /**
     * 根据ID和分库建更新库存占用明细状态
     *
     * @param status   状态
     * @param isActive 可用
     * @param orderId  分库建
     * @param id       ID
     * @return 返回结果
     */
    @Update("UPDATE ip_b_time_order_vip_occupy_item SET status = #{status},isactive = #{isActive} where ip_b_time_order_vip_id=#{orderId} and id = #{id}")
    int updateOccItemStatus(@Param("status") int status, @Param("isActive") String isActive, @Param("orderId") Long orderId, @Param("id") Long id);


    /**
     * 根据ID和分库建更新库存占用明细为缺货和更新缺货数量
     *
     * @param outStockQuantity 缺货数量
     * @param orderId          分库建
     * @param id               ID
     * @return 返回结果
     */
    @Update("UPDATE ip_b_time_order_vip_occupy_item SET status = 2,out_stock_quantity = #{outStockQuantity} where ip_b_time_order_vip_id=#{orderId} and id = #{id}")
    int updateOccItemStatusAndOutStockQuantity(@Param("outStockQuantity") BigDecimal outStockQuantity, @Param("orderId") Long orderId, @Param("id") Long id);


    /**
     * 更新明细的数量和缺货数量
     *
     * @param amount           应发数量
     * @param outStockQuantity 缺货数量
     * @param orderId          订单ID
     * @param id               明细ID
     * @param status           状态
     * @return 返回结果
     */
    @Update("UPDATE ip_b_time_order_vip_occupy_item SET amount = #{amount},out_stock_quantity = #{outStockQuantity},status = #{status} where ip_b_time_order_vip_id=#{orderId} and id = #{id}")
    int updateOccItemAmountAndOutStockQuantityAndStatus(@Param("amount") BigDecimal amount, @Param("outStockQuantity") BigDecimal outStockQuantity, @Param("status") int status, @Param("orderId") Long orderId, @Param("id") Long id);


    /**
     * 查询总缺货数量
     *
     * @param orderId 时效订单ID
     * @return 总缺货数量
     */
    @Select("SELECT sum(out_stock_quantity) from ip_b_time_order_vip_occupy_item WHERE ip_b_time_order_vip_id=#{orderId} AND status <> 5")
    BigDecimal selectTotalOutStockQuantity(@Param("orderId") Long orderId);


    /**
     * 根据分库建更新库存占用明细为已匹配成功
     *
     * @param orderId 分库建
     * @return 返回结果
     */
    @Update("UPDATE ip_b_time_order_vip_occupy_item SET status = 4,modifieddate = now() where ip_b_time_order_vip_id=#{orderId} and status = 3")
    int updateMatchedByOrderId(@Param("orderId") Long orderId);
}