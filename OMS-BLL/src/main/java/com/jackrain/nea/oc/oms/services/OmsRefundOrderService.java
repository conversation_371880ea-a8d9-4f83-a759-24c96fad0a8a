package com.jackrain.nea.oc.oms.services;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jackrain.nea.ac.service.AcFInvoiceReturnRedOffsetService;
import com.jackrain.nea.cpext.model.LogisticsInfo;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ip.model.qimen.QimenOrderCallbackModel;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.dto.invoice.ReturnRedOffsetDTO;
import com.jackrain.nea.oc.oms.es.ES4ReturnOrder;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnAfSendItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnAfSendMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnBfSendMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderLogMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderRefundMapper;
import com.jackrain.nea.oc.oms.mapper.task.OcBSapSalesDataRecordAddTaskMapper;
import com.jackrain.nea.oc.oms.model.Standplat.IpBStandplatRefundType;
import com.jackrain.nea.oc.oms.model.enums.AGStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.CardAutoVoidEnum;
import com.jackrain.nea.oc.oms.model.enums.InterceptStatus;
import com.jackrain.nea.oc.oms.model.enums.OcBReturnAfSendListEnums;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderBusinessTypeCodeEnum;
import com.jackrain.nea.oc.oms.model.enums.OrderHoldReasonEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.enums.RefundStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnAfSendReturnBillTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnAfSendReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.ToACStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OcBReturnAfSendRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsStandPlatRefundRelation;
import com.jackrain.nea.oc.oms.model.relation.ReturnOrderRelation;
import com.jackrain.nea.oc.oms.model.resources.SysNotesConstant;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatRefund;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatRefundItem;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnAfSend;
import com.jackrain.nea.oc.oms.model.table.OcBReturnAfSendItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnBfSend;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderLog;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.oc.oms.model.table.StCBusinessType;
import com.jackrain.nea.oc.oms.model.table.task.OcBSapSalesDataRecordAddTask;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.nums.RefundOrderSourceTypeEnum;
import com.jackrain.nea.oc.oms.sap.OcBSapSalesDataRecordAddTaskService;
import com.jackrain.nea.oc.oms.sap.Oms2SapMapper;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.oc.oms.util.ReturnOrderTransferUtil;
import com.jackrain.nea.oc.oms.util.StandplatRefundOrderTransferUtil;
import com.jackrain.nea.oc.oms.util.TaobaoRefundOrderTransferUtil;
import com.jackrain.nea.ps.model.OmsProAttributeInfo;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.OperateUserUtils;
import com.jackrain.nea.util.Tools;
import com.jackrain.nea.util.TransactionUtils;
import com.jackrain.nea.util.ValueHolderV14Utils;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2020/3/2 10:42 上午
 * @Version 1.0
 */
@Slf4j
@Component
public class OmsRefundOrderService {


    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;
    @Autowired
    private OcBReturnOrderRefundMapper ocBReturnOrderRefundMapper;
    @Autowired
    private OcCancelChangingOrRefundService ocCancelChangingOrRefundService;
    @Autowired
    private OcBReturnOrderLogMapper ocBReturnOrderLogMapper;
    @Autowired
    private IpTaobaoRefundService ipTaobaoRefundService;
    @Autowired
    private TaobaoRefundOrderTransferUtil taobaoRefundOrderTransferUtil;
    @Autowired
    private OcBReturnAfSendMapper ocBReturnAfSendMapper;
    @Autowired
    private OcBReturnAfSendItemMapper ocBReturnAfSendItemMapper;
    @Autowired
    private OmsRefundOrderService omsRefundOrderService;
    @Autowired
    private OcBReturnBfSendMapper ocBReturnBfSendMapper;
    @Autowired
    private IpRpcService ipRpcService;
    @Autowired
    private OmsOrderItemService omsOrderItemService;
    @Autowired
    private OmsReturnOrderService omsReturnOrderService;
    @Autowired
    private OcSaveChangingOrRefundingService ocSaveChangingOrRefundingService;
    @Autowired
    private Oms2SapMapper oms2SapMapper;
    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private OcBOrderHoldService holdService;
    @Autowired
    private OcBOrderHoldItemService holdItemService;
    @Autowired
    private BuildSequenceUtil sequenceUtil;
    @Autowired
    private RefundFormAfterDeliveryService refundFormAfterDeliveryService;
    @Autowired
    private ReturnOrderTransferUtil returnOrderTransferUtil;
    @Autowired
    private ReturnOrderAuditService returnOrderAuditService;
    @Autowired
    private StRpcService stRpcService;
    @Autowired
    private PsRpcService psRpcService;
    @Autowired
    private OmsBusinessTypeStService omsBusinessTypeStService;
    @Autowired
    private StandplatRefundOrderTransferUtil standplatRefundOrderTransferUtil;
    @Autowired
    private AcFInvoiceReturnRedOffsetService acFInvoiceReturnRedOffsetService;
    @Autowired
    private OcBSapSalesDataRecordAddTaskService sapSalesDataRecordAddTaskService;
    @Autowired
    private OcBSapSalesDataRecordAddTaskMapper ocBSapSalesDataRecordAddTaskMapper;

    /**
     * 拦截生成发货后退款单(退货退款)
     */

    public void foundRefundSlipAfter(List<Long> returnId, OcBOrder ocBOrder, IpBTaobaoRefund ipBTaobaoRefund,
                                     User user) {
        //获取所有的退单明细数据
        try {
            List<OcBReturnOrder> ocBReturnOrders = ocBReturnOrderMapper.selectReturnOrderListByOrderIds(returnId);
            ocBReturnOrders = ocBReturnOrders.stream().filter(p -> TaobaoReturnOrderExt.BillType.REFUND.getCode().equals(p.getBillType())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(ocBReturnOrders)) {
                returnId = ocBReturnOrders.stream().map(OcBReturnOrder::getId).collect(Collectors.toList());
                List<OcBOrderItem> orderItems = ocBOrderItemMapper.selectOrderItemListAndReturn(ocBOrder.getId());
                List<OcBReturnOrderRefund> orderRefunds =
                        ocBReturnOrderRefundMapper.selectOcBReturnOrderRefundByOrderIds(returnId);
                this.updateOcBReturnOrder(returnId, ipBTaobaoRefund, ocBReturnOrders, orderRefunds);
                //获取发货单主表数据
                OcBReturnAfSend ocBReturnAfSend = selectOcBReturnAfSend(ipBTaobaoRefund.getRefundId());
                if (ocBReturnAfSend != null) {
                    //判断退款原因是否相等
                    if (!ocBReturnAfSend.getReason().equals(ipBTaobaoRefund.getReason())) {
                        ocBReturnAfSendMapper.updateOcBReturnAfReason(ipBTaobaoRefund.getReason(), ipBTaobaoRefund.getRefundId());
                    }
                    return;
                }
                OcBReturnAfSendRelation ocBReturnAfSendRelation =
                        taobaoRefundOrderTransferUtil.taobaoRefundAfSendToReturn(orderRefunds, ocBOrder,
                                ipBTaobaoRefund, user, orderItems, ocBReturnOrders);
                omsRefundOrderService.insertOcBReturnAfSend(ocBReturnAfSendRelation, user);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("生成退款单异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            throw new NDSException(e);
        }
    }

    /**
     * 生成已发货退款单(不更新退货单)
     *
     * @param returnId
     * @param ocBOrder
     * @param ipBTaobaoRefund
     * @param user
     */
    public void foundRefundSlipAfterNoUpdate(List<Long> returnId, OcBOrder ocBOrder, IpBTaobaoRefund ipBTaobaoRefund,
                                             User user) {
        //获取所有的退单明细数据
        try {
            if (!isRefundSlipAfExist(ipBTaobaoRefund.getRefundId())) {
                return;
            }
            List<OcBReturnOrder> ocBReturnOrders = ocBReturnOrderMapper.selectReturnOrderListByOrderIds(returnId);
            ocBReturnOrders = ocBReturnOrders.stream().filter(p -> TaobaoReturnOrderExt.BillType.REFUND.getCode().equals(p.getBillType())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(ocBReturnOrders)) {
                returnId = ocBReturnOrders.stream().map(OcBReturnOrder::getId).collect(Collectors.toList());
                List<OcBOrderItem> orderItems = ocBOrderItemMapper.selectOrderItemListAndReturn(ocBOrder.getId());
                List<OcBReturnOrderRefund> orderRefunds =
                        ocBReturnOrderRefundMapper.selectOcBReturnOrderRefundByOrderIds(returnId);
                //获取发货单主表数据
                OcBReturnAfSendRelation ocBReturnAfSendRelation =
                        taobaoRefundOrderTransferUtil.taobaoRefundAfSendToReturn(orderRefunds, ocBOrder,
                                ipBTaobaoRefund, user, orderItems, ocBReturnOrders);
                omsRefundOrderService.insertOcBReturnAfSend(ocBReturnAfSendRelation, user);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("生成退款单异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            throw new NDSException(e);
        }
    }

    /**
     * 当申请数量大于已退数量时判断已发货退款单是否存在
     */
    public void foundRefundSlipAfterNoUpdate(OcBOrder ocBOrder, IpBTaobaoRefund ipBTaobaoRefund, User user) {
        //通过ooid查询退货单  是否存在
        List<Long> refundId = isExistReturnOrderRefundByOid(ipBTaobaoRefund);
        if (CollectionUtils.isNotEmpty(refundId)) {
            this.foundRefundSlipAfterNoUpdate(refundId, ocBOrder, ipBTaobaoRefund, user);
        }
    }


    /**
     * 更新退款单
     *
     * @param returnId
     * @param ipBTaobaoRefund
     * @param ocBReturnOrders
     * @param orderRefunds
     */

    public void updateOcBReturnOrder(List<Long> returnId, IpBTaobaoRefund ipBTaobaoRefund, List<OcBReturnOrder> ocBReturnOrders,
                                     List<OcBReturnOrderRefund> orderRefunds) {
        //中间表退款申请时间
        Date created = ipBTaobaoRefund.getCreated();
        //存放不符合更新条件的退单id
        List<Long> returnOrderId = new ArrayList<>();
        for (OcBReturnOrder ocBReturnOrder : ocBReturnOrders) {
            //申请退款时间
            Date returnCreateTime = ocBReturnOrder.getReturnCreateTime();
            boolean flag = returnCreateTime != null && returnCreateTime.getTime() > created.getTime();
            if ((StringUtils.isNotEmpty(ocBReturnOrder.getReturnId())
                    && ocBReturnOrder.getReturnId().equals(ipBTaobaoRefund.getRefundId())) || flag) {
                returnOrderId.add(ocBReturnOrder.getId());
                continue;
            }
            OcBReturnOrder returnOrder = new OcBReturnOrder();
            returnOrder.setId(ocBReturnOrder.getId());
            //平台退款单号
            returnOrder.setReturnId(ipBTaobaoRefund.getRefundId());
            //卖家昵称
            returnOrder.setBuyerNick(ipBTaobaoRefund.getBuyerNick());
            //申请退款时间
            returnOrder.setReturnCreateTime(ipBTaobaoRefund.getCreated());
            //最后修改时间
            returnOrder.setLastUpdateTime(ipBTaobaoRefund.getModified());
            //货物退回时间
            returnOrder.setReturnTime(ipBTaobaoRefund.getGoodReturnTime());
            //退款说明
            returnOrder.setReturnDesc(ipBTaobaoRefund.getReason());
            //returnOrder.setReturnReason(ipBTaobaoRefund.getReason());
            //商品应退金额(
            returnOrder.setReturnAmtList(ipBTaobaoRefund.getRefundFee());
            //售后/售中
            returnOrder.setReturnPhase(ipBTaobaoRefund.getRefundPhase());
            //退款金额(计算 商品应退金额+退还运费+退还其他费用-换货金额) = 商品应退金额
            returnOrder.setReturnAmtActual(ipBTaobaoRefund.getRefundFee());
            //卖家呢城
            returnOrder.setSellerNick(ipBTaobaoRefund.getSellerNick());
            //物流公司名称
            String companyName = ipBTaobaoRefund.getCompanyName();
            //退回物流单号
            returnOrder.setLogisticsCode(ipBTaobaoRefund.getSid());
            returnOrder.setCpCLogisticsEname(companyName);
            //加入“空运单号延迟推单有效时间”字段
            returnOrder.setPushDelayTime(ocSaveChangingOrRefundingService.PushDelayTime(returnOrder));
            ocBReturnOrderMapper.updateById(returnOrder);
        }
        for (OcBReturnOrderRefund returnOrderRefund : orderRefunds) {
            if (returnOrderId.contains(returnOrderRefund.getOcBReturnOrderId())) {
                continue;
            }
            if (returnOrderRefund.getOid() != null && returnOrderRefund.getOid().equals(ipBTaobaoRefund.getOid() + "")) {
                OcBReturnOrderRefund orderRefund = new OcBReturnOrderRefund();
                orderRefund.setId(returnOrderRefund.getId());
                orderRefund.setRefundStatus(ipBTaobaoRefund.getStatus());
                orderRefund.setRefundBillNo(ipBTaobaoRefund.getRefundId());
                orderRefund.setAmtPtRefund(ipBTaobaoRefund.getRefundFee());
                returnOrderRefund.setRefundStatus(ipBTaobaoRefund.getStatus());
                QueryWrapper<OcBReturnOrderRefund> wrapper = new QueryWrapper<>();
                wrapper.eq("id", returnOrderRefund.getId());
                wrapper.eq("oc_b_return_order_id", returnOrderRefund.getOcBReturnOrderId());
                //更新之前分库建必须设置为空
                ocBReturnOrderRefundMapper.update(orderRefund, wrapper);
            }
        }
    }

    /**
     * 判断退款金额是否与已发货退款单的金额是否相等
     *
     * @param ocBReturnAfSend
     * @param ipBTaobaoRefund
     */
    private void judgeAmtIsEqual(OcBReturnAfSend ocBReturnAfSend, IpBTaobaoRefund ipBTaobaoRefund, User user) {

        // 申请的退款金额
        BigDecimal amtReturnApply = ocBReturnAfSend.getAmtReturnApply();
        //退款原因
        String refundDes = ocBReturnAfSend.getReason();
        //平台退款原因
        String ptRefundDes = ipBTaobaoRefund.getReason();
        // 退还金额
        BigDecimal refundFee = ipBTaobaoRefund.getRefundFee();
        String status = ipBTaobaoRefund.getStatus();
        //更新退款原因
        OcBReturnAfSend returnAfSend = null;
        if (ptRefundDes != null) {
            if (!ptRefundDes.equals(refundDes)) {
                returnAfSend = new OcBReturnAfSend();
                returnAfSend.setReason(ptRefundDes);
            }
        }
        if (amtReturnApply != null && refundFee != null && amtReturnApply.compareTo(refundFee) != 0
                && !TaobaoReturnOrderExt.RefundStatus.SUCCESS.getCode().equals(status)) {
            //金额不相等    则将申请的金额重新更新
            if (returnAfSend == null) {
                returnAfSend = new OcBReturnAfSend();
            }
            returnAfSend.setAmtReturnApply(refundFee);
            returnAfSend.setAmtReturnActual(refundFee);

            List<OcBReturnAfSendItem> ocBReturnAfSendItems =
                    ocBReturnAfSendItemMapper.selectByOcBReturnAfSendIdListBySendId(ocBReturnAfSend.getId());
            if (CollectionUtils.isNotEmpty(ocBReturnAfSendItems)) {
                List<OcBReturnAfSendItem> afSendItems =
                        ocBReturnAfSendItems.stream().filter(p -> p.getAmtReturn() != null && p.getAmtHasReturn() != null
                                && p.getAmtReturn().compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
                BigDecimal realAmt = afSendItems.stream().map(OcBReturnAfSendItem::getAmtActual).
                        reduce(BigDecimal.ZERO, BigDecimal::add);
                for (OcBReturnAfSendItem ocBReturnAfSendItem : afSendItems) {
                    OcBReturnAfSendItem afSendItem = new OcBReturnAfSendItem();
                    BigDecimal amt =
                            ocBReturnAfSendItem.getAmtActual().divide(realAmt, 4, BigDecimal.ROUND_HALF_UP).multiply(refundFee);
                    afSendItem.setAmtHasReturn(amt);
                    afSendItem.setAmtReturn(amt);
                    QueryWrapper<OcBReturnAfSendItem> wra = new QueryWrapper<>();
                    wra.eq("id", ocBReturnAfSendItem.getId());
                    wra.eq("oc_b_return_af_send_id", ocBReturnAfSendItem.getOcBReturnAfSendId());
                    ocBReturnAfSendItemMapper.update(afSendItem, wra);
                }
            }
            refundFormAfterDeliveryService.insertReturnAfSendLog("已发货退款单更新金额",
                    "更新之前金额为" + amtReturnApply + "更新之后为" + refundFee,
                    null, user, ocBReturnAfSend.getId());
        }
        if (returnAfSend != null) {
            QueryWrapper<OcBReturnAfSend> wrapper = new QueryWrapper<>();
            wrapper.eq("id", ocBReturnAfSend.getId());
            wrapper.eq("t_return_id", ocBReturnAfSend.getTReturnId());
            ocBReturnAfSendMapper.update(returnAfSend, wrapper);
        }
    }

    /**
     * 生成发货后退款单(仅退款)
     *
     * @param ocBOrderItems
     * @param ocBOrder
     * @param ipBTaobaoRefund
     */
    public void foundRefundSlipAfterRefundOnly(List<OcBOrderItem> ocBOrderItems, OcBOrder ocBOrder,
                                               IpBTaobaoRefund ipBTaobaoRefund, User user) {
        //获取所有的退单明细数据
        try {
            OcBReturnAfSend ocBReturnAfSend =
                    ocBReturnAfSendMapper.selectOcBReturnAfSendByRefundId(ipBTaobaoRefund.getRefundId());
            if (ocBReturnAfSend != null) {
                //存在 则判断退款金额是否与当前生成的已发货退款的金额是否相等
                this.judgeAmtIsEqual(ocBReturnAfSend, ipBTaobaoRefund, user);
                if (!ocBReturnAfSend.getReason().equals(ipBTaobaoRefund.getReason())) {
                    ocBReturnAfSendMapper.updateOcBReturnAfReason(ipBTaobaoRefund.getReason(), ipBTaobaoRefund.getRefundId());
                }
                return;
            }

            OcBReturnAfSendRelation ocBReturnAfSendRelation = taobaoRefundOrderTransferUtil
                    .taobaoRefundAfSendToRefundOnly(ocBOrderItems, ocBOrder, ipBTaobaoRefund, user);

            /*//查询退款业务类型
            StCBusinessType stCBusinessType = queryRefundOrderType(ocBOrder);
            ocBReturnAfSendRelation.getOcBReturnAfSend().setBusinessTypeId(stCBusinessType.getId());
            ocBReturnAfSendRelation.getOcBReturnAfSend().setBusinessTypeCode(stCBusinessType.getEcode());
            ocBReturnAfSendRelation.getOcBReturnAfSend().setBusinessTypeName(stCBusinessType.getEname());*/

            //获取发货单主表数据
            omsRefundOrderService.insertOcBReturnAfSend(ocBReturnAfSendRelation, user);
            //
            if (ReturnAfSendReturnBillTypeEnum.REFUNDCOMPLETED.getVal() == ocBReturnAfSendRelation.getOcBReturnAfSend().getReturnStatus().intValue()) {
                ReturnRedOffsetDTO dto = bulidReturnRedOffsetDTO(ocBReturnAfSendRelation.getOcBReturnAfSend(), ocBReturnAfSendRelation.getOcBReturnAfSendItems());
                acFInvoiceReturnRedOffsetService.createRedOffserRecord(dto);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("生成退款单异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            throw new NDSException(e);
        }
    }


    /**
     * 通用退单 生成发货后退款单(仅退款)
     *
     * @param ocBOrderItems
     * @param ocBOrder
     * @param refundRelation
     * @param isCreateReturnOrder 是否创建退换货单
     */
    public void foundRefundSlipAfterRefundOnly(List<OcBOrderItem> ocBOrderItems, OcBOrder ocBOrder,
                                               OmsStandPlatRefundRelation refundRelation, User user, boolean isCreateReturnOrder) {
        IpBStandplatRefund ipBStandplatRefund = refundRelation.getIpBStandplatRefund();
        //获取所有的退单明细数据
        try {
            if (!isRefundSlipAfExist(ipBStandplatRefund.getReturnNo())) {
                OcBReturnAfSend ocBReturnAfSend = ocBReturnAfSendMapper.selectOcBReturnAfSendByRefundId(ipBStandplatRefund.getReturnNo());
                // 退款金额
                BigDecimal refundFee = refundRelation.getIpBStandplatRefund().getRefundAmount();

                BigDecimal totalItemReturnFee = new BigDecimal(0);
                if (CollectionUtils.isEmpty(refundRelation.getIpBStandplatRefundItem())) {
                    totalItemReturnFee = refundFee;
                } else {
                    for (IpBStandplatRefundItem ipBStandplatRefundItem : refundRelation.getIpBStandplatRefundItem()) {
                        if (Objects.nonNull(ipBStandplatRefundItem.getRefundFee())) {
                            totalItemReturnFee = totalItemReturnFee.add(ipBStandplatRefundItem.getRefundFee());
                        }
                    }
                }

                OcBReturnAfSend update = new OcBReturnAfSend();
                // 申请退款金额
                update.setAmtReturnApply(totalItemReturnFee);
                update.setAmtReturnActual(refundFee);
                update.setRemark("金额发生变化");
                update.setModifieddate(new Date());
                update.setId(ocBReturnAfSend.getId());
                ocBReturnAfSendMapper.updateById(update);
                return;
            }
            OcBReturnAfSendRelation ocBReturnAfSendRelation =
                    this.refundAfSendToRefundOnly(ocBOrderItems, ocBOrder, refundRelation, user, isCreateReturnOrder);
            //获取发货单主表数据
            omsRefundOrderService.insertOcBReturnAfSend(ocBReturnAfSendRelation, user);
            //如果是退款完成，则保存退单待红冲发票信息
            if (ReturnAfSendReturnBillTypeEnum.REFUNDCOMPLETED.getVal() == ocBReturnAfSendRelation.getOcBReturnAfSend().getReturnStatus().intValue()) {
                ReturnRedOffsetDTO dto = bulidReturnRedOffsetDTO(ocBReturnAfSendRelation.getOcBReturnAfSend(), ocBReturnAfSendRelation.getOcBReturnAfSendItems());
                acFInvoiceReturnRedOffsetService.createRedOffserRecord(dto);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("生成退款单异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
        }
    }

    /**
     * 处理发货后退款单的数据(仅退款)
     *
     * @param ocBOrderItems
     * @param ocBOrder
     * @param refundRelation
     * @return
     */
    public OcBReturnAfSendRelation refundAfSendToRefundOnly(List<OcBOrderItem> ocBOrderItems,
                                                            OcBOrder ocBOrder,
                                                            OmsStandPlatRefundRelation refundRelation,
                                                            User user, Boolean isCreateReturnOrder) {
        OcBReturnAfSendRelation ocBReturnAfSendRelation = new OcBReturnAfSendRelation();
        // 退款金额
        BigDecimal refundFee = BigDecimal.ZERO;
        Map<String, IpBStandplatRefundItem> ooidMap = Maps.newHashMap();
        for (IpBStandplatRefundItem current : refundRelation.getIpBStandplatRefundItem()) {
            ooidMap.put(current.getSubOrderId(), current);
            BigDecimal bigDecimal = Optional.ofNullable(current.getRefundFee()).orElse(BigDecimal.ZERO);
            refundFee = refundFee.add(bigDecimal);
        }
        // 假如子表金额之和等于0 ,就从头表上取
        if (BigDecimal.ZERO.compareTo(refundFee) == 0) {
            refundFee = refundRelation.getIpBStandplatRefund().getRefundAmount();
        }
        // 这里生成的单据类型根据是否生成退换货单决定，如果不生成则是仅退款。不一定会使用billType 优先使用通用退单的类型
        Integer billType = TaobaoReturnOrderExt.SendBillType.REFUND_ONLY.getCode();
        if (isCreateReturnOrder) {
            billType = TaobaoReturnOrderExt.SendBillType.RETURN_REFUND.getCode();
        }
        Integer refundType = refundRelation.getIpBStandplatRefund().getRefundType();
        if (refundType == IpBStandplatRefundType.ONLY_REFUND) {
            billType = TaobaoReturnOrderExt.SendBillType.REFUND_ONLY.getCode();
        } else if (refundType == IpBStandplatRefundType.RETURN_GOODS_RERUEN) {
            billType = TaobaoReturnOrderExt.SendBillType.RETURN_REFUND.getCode();
        }

        OcBReturnAfSend ocBReturnAfSend = this.buildOcBReturnAfSend(ocBOrder, refundRelation, refundFee,
                billType, user);
        List<OcBReturnAfSendItem> ocBReturnAfSendItems = buildOcBReturnAfSendItem(ocBOrderItems, ooidMap, user,
                ocBReturnAfSend, ocBOrder, refundFee);
        standplatRefundOrderTransferUtil.setReturnAfSendItemsByRatio(ocBOrderItems, ocBReturnAfSendItems, refundRelation.getIpBStandplatRefund());
        ocBReturnAfSendRelation.setOcBReturnAfSend(ocBReturnAfSend);
        ocBReturnAfSendRelation.setOcBReturnAfSendItems(ocBReturnAfSendItems);
        return ocBReturnAfSendRelation;
    }


    private List<OcBReturnAfSendItem> buildOcBReturnAfSendItem(List<OcBOrderItem> ocBOrderItems,
                                                               Map<String, IpBStandplatRefundItem> itemMap,
                                                               User user, OcBReturnAfSend ocBReturnAfSend, OcBOrder ocBOrder, BigDecimal refundFeeTotal) {
        List<OcBReturnAfSendItem> ocBReturnAfSendItems = new ArrayList<>();

        BigDecimal totalAmtActual = BigDecimal.ZERO;

        for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
            OcBReturnAfSendItem ocBReturnAfSendItem = new OcBReturnAfSendItem();
            //关联类型
            ocBReturnAfSendItem.setRelationBillType(1L);
            ocBReturnAfSendItem.setRelationBillId(ocBOrderItem.getOcBOrderId());
            ocBReturnAfSendItem.setRelationBillNo(ocBOrder.getBillNo());
            //赠品
            ocBReturnAfSendItem.setGift(ocBOrderItem.getIsGift() + "");
            ocBReturnAfSendItem.setPsCSkuId(ocBOrderItem.getPsCSkuId());
            ocBReturnAfSendItem.setPsCSkuEcode(ocBOrderItem.getPsCSkuEcode());
            ocBReturnAfSendItem.setPsCProEcode(ocBOrderItem.getPsCProEcode());
            ocBReturnAfSendItem.setPsCProEname(ocBOrderItem.getPsCProEname());
            ocBReturnAfSendItem.setPsCProId(ocBOrderItem.getPsCProId());
            ocBReturnAfSendItem.setPtProName(ocBOrderItem.getPtProName());

            ocBReturnAfSendItem.setPurchaseQty(ocBOrderItem.getQty());
            ocBReturnAfSendItem.setAmtActual(ocBOrderItem.getRealAmt());
            totalAmtActual = totalAmtActual.add(ocBOrderItem.getRealAmt() == null ? BigDecimal.ZERO : ocBOrderItem.getRealAmt());
            IpBStandplatRefundItem refundItem =
                    Optional.ofNullable(itemMap.get(ocBOrderItem.getOoid())).orElse(new IpBStandplatRefundItem());
            BigDecimal refundFee = refundItem.getRefundFee() == null ? ocBOrderItem.getRealAmt() :
                    refundItem.getRefundFee();
//
//            ocBReturnAfSendItem.setAmtHasReturn(refundFee);
//            ocBReturnAfSendItem.setAmtReturn(refundFee);
            ocBReturnAfSendItem.setPsCSkuEname(ocBOrderItem.getPsCSkuEname());
            ocBReturnAfSendItem.setFreight(BigDecimal.ZERO);
            ocBReturnAfSendItem.setRelationBillItemId(ocBOrderItem.getId());
            ocBReturnAfSendItem.setPsCSkuPtEcode(ocBOrderItem.getPsCSkuPtEcode());
            ocBReturnAfSendItem.setGift(ocBOrderItem.getGiftType());
            BigDecimal priceActual = ocBOrderItem.getPriceActual();
            if (priceActual == null) {
                priceActual = ocBOrderItem.getRealAmt().divide(ocBOrderItem.getQty(), 4,
                        BigDecimal.ROUND_HALF_UP);
            }
            if (Objects.isNull(refundItem.getReturnQuantity()) && !isNullOrZero(priceActual)) {
                // 申请退货数量 余数进1
                ocBReturnAfSendItem.setQtyReturnApply(refundFee.divide(priceActual, 0,
                        BigDecimal.ROUND_UP));
            } else {
                // 申请退货数量
                ocBReturnAfSendItem.setQtyReturnApply(refundItem.getReturnQuantity());
            }

            ocBReturnAfSendItem.setOcBOrderItemId(ocBOrderItem.getId());
            if (ObjectUtil.isNotNull(ocBOrderItem.getOcBOrderId())) {
                OcBOrder order = ocBOrderMapper.get4AfReturn(ocBOrderItem.getOcBOrderId());
                if (ObjectUtil.isNotNull(order)) {
                    ocBReturnAfSendItem.setOcBOrderId(order.getId());
                    ocBReturnAfSendItem.setBusinessTypeCode(order.getBusinessTypeCode());
                    ocBReturnAfSendItem.setBusinessTypeId(order.getBusinessTypeId());
                    ocBReturnAfSendItem.setBusinessTypeName(order.getBusinessTypeName());
                }
            }
            OperateUserUtils.saveOperator(ocBReturnAfSendItem, user);
            ocBReturnAfSendItems.add(ocBReturnAfSendItem);
        }

//        // 先过滤掉已发货退款单明细中 成交金额为0的 不去计算
        List<OcBReturnAfSendItem> zeroMoreOcBReturnAfSendItems = ocBReturnAfSendItems.stream().filter(x -> x.getAmtActual().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
        List<OcBReturnAfSendItem> zeroOcBReturnAfSendItems = ocBReturnAfSendItems.stream().filter(x -> x.getAmtActual().compareTo(BigDecimal.ZERO) <= 0).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(zeroOcBReturnAfSendItems)) {
            for (OcBReturnAfSendItem ocBReturnAfSendItem : zeroOcBReturnAfSendItems) {
                ocBReturnAfSendItem.setAmtHasReturn(BigDecimal.ZERO);
                ocBReturnAfSendItem.setAmtReturn(BigDecimal.ZERO);
            }
        }

        if (CollectionUtils.isNotEmpty(zeroMoreOcBReturnAfSendItems)) {
            // 判断是不是只有一个明细 如果只有一个明细则直接把已发货退款单主表的金额给到明细
            if (zeroMoreOcBReturnAfSendItems.size() == 1) {
                zeroMoreOcBReturnAfSendItems.get(0).setAmtHasReturn(refundFeeTotal);
                zeroMoreOcBReturnAfSendItems.get(0).setAmtReturn(refundFeeTotal);
            }
            BigDecimal subtractAmt = BigDecimal.ZERO;
            for (int i = 0; i < zeroMoreOcBReturnAfSendItems.size(); i++) {
                OcBReturnAfSendItem returnAfSendItem = zeroMoreOcBReturnAfSendItems.get(i);
                if (i == zeroMoreOcBReturnAfSendItems.size() - 1) {
                    BigDecimal refundFee = refundFeeTotal.subtract(subtractAmt);
                    if (refundFee.compareTo(BigDecimal.ZERO) < 0) {
                        returnAfSendItem.setAmtHasReturn(BigDecimal.ZERO);
                        returnAfSendItem.setAmtReturn(BigDecimal.ZERO);
                    } else {
                        returnAfSendItem.setAmtHasReturn(refundFee);
                        returnAfSendItem.setAmtReturn(refundFee);
                    }
                } else {
                    BigDecimal ratio = returnAfSendItem.getAmtActual().divide(totalAmtActual, 4, BigDecimal.ROUND_HALF_UP);
                    BigDecimal refundFee = refundFeeTotal.multiply(ratio);
                    returnAfSendItem.setAmtHasReturn(refundFee);
                    returnAfSendItem.setAmtReturn(refundFee);
                    subtractAmt = subtractAmt.add(refundFee);
                }
            }
        }
        ocBReturnAfSendItems = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(zeroMoreOcBReturnAfSendItems)) {
            ocBReturnAfSendItems.addAll(zeroMoreOcBReturnAfSendItems);
        }
        if (CollectionUtils.isNotEmpty(zeroOcBReturnAfSendItems)) {
            ocBReturnAfSendItems.addAll(zeroOcBReturnAfSendItems);
        }

        // 遍历
        log.info(LogUtil.format("buildOcBReturnAfSendItem.ocBReturnAfSendItems {}",
                "buildOcBReturnAfSendItem.ocBReturnAfSendItems"), JSON.toJSONString(ocBReturnAfSendItems));

        if (itemMap.size() == 1 && ocBReturnAfSendItems.size() == 1) {
            //通用退单中间表明细只有一条 并且 是普通商品
            for (OcBReturnAfSendItem sendItem : ocBReturnAfSendItems) {
                if ("1".equals(sendItem.getGift())) {
                    sendItem.setAmtReturn(BigDecimal.ZERO);
                    sendItem.setAmtHasReturn(BigDecimal.ZERO);
                    continue;
                }
                sendItem.setAmtHasReturn(ocBReturnAfSend.getAmtReturnActual());
                sendItem.setAmtReturn(ocBReturnAfSend.getAmtReturnActual());
            }
        }
        log.info(LogUtil.format("buildOcBReturnAfSendItem.ocBReturnAfSendItems.after {}",
                "buildOcBReturnAfSendItem.ocBReturnAfSendItems.after"), JSON.toJSONString(ocBReturnAfSendItems));
        return ocBReturnAfSendItems;
    }


    /**
     * 生成发货后退款单
     *
     * @return
     */
    private OcBReturnAfSend buildOcBReturnAfSend(OcBOrder ocBOrder,
                                                 OmsStandPlatRefundRelation refundRelation,
                                                 BigDecimal refundFee,
                                                 Integer billType, User user) {
        IpBStandplatRefund standplatRefund = refundRelation.getIpBStandplatRefund();
        OcBReturnAfSend ocBReturnAfSend = new OcBReturnAfSend();
        ocBReturnAfSend.setCpCShopId(ocBOrder.getCpCShopId());
        ocBReturnAfSend.setCpCShopEcode(ocBOrder.getCpCShopEcode());
        ocBReturnAfSend.setCpCShopTitle(ocBOrder.getCpCShopTitle());
        ocBReturnAfSend.setTid(ocBOrder.getTid());
        ocBReturnAfSend.setBillNo(sequenceUtil.aFbuildBillNo());
        //退款状态 0 待审核
        ocBReturnAfSend.setReturnStatus(ReturnAfSendReturnBillTypeEnum.NOREFUND.getVal());
        ocBReturnAfSend.setTReturnId(standplatRefund.getReturnNo());
        ocBReturnAfSend.setTReturnStatus(String.valueOf(standplatRefund.getReturnStatus()));
        //单据类型 0 退货退款 1仅退款',
        ocBReturnAfSend.setBillType(billType);
        ocBReturnAfSend.setVipNick(standplatRefund.getBuyerNick());
        // @******** 加手机
        ocBReturnAfSend.setVipPhone(ocBOrder.getReceiverPhone());
        ocBReturnAfSend.setReason(standplatRefund.getReturnReason());
        //单据来源设置默认值为2 自动
        ocBReturnAfSend.setRefundOrderSourceType(RefundOrderSourceTypeEnum.AUTO.getValue());
        //支付方式
        ocBReturnAfSend.setPayMode(ocBOrder.getPayType() + "");
        //支付宝账号
        ocBReturnAfSend.setPayAccount(ocBOrder.getBuyerAlipayNo());
        // 通用平台退款状态
        ocBReturnAfSend.setTReturnStatus(TaobaoReturnOrderExt.RefundStandPlatStatus.getValueByKey(standplatRefund.getReturnStatus()));
        ocBReturnAfSend.setSourceBillNo(ocBOrder.getId() + "");
        ocBReturnAfSend.setCpCPlatformId(Long.valueOf(ocBOrder.getPlatform()));
        ocBReturnAfSend.setPayMode(OcBReturnAfSendListEnums.PayTypeEnum.Alipay.getVal());
        // 申请退款金额
        ocBReturnAfSend.setAmtReturnApply(refundFee);
        //todo 实际退款金额
        ocBReturnAfSend.setAmtReturnActual(refundFee);
        //申请退款时间
        ocBReturnAfSend.setReturnApplyTime(new Date());
        ocBReturnAfSend.setAgStatus(AGStatusEnum.INIT.getVal() + "");
        ocBReturnAfSend.setReturnExplain(standplatRefund.getReturnReason());
        String status = TaobaoReturnOrderExt.RefundStandPlatStatus.getValueByKey(standplatRefund.getReturnStatus());
        if (TaobaoReturnOrderExt.RefundStatus.SUCCESS.getCode().equals(status)) {
            ocBReturnAfSend.setReturnStatus(ReturnAfSendReturnBillTypeEnum.REFUNDCOMPLETED.getVal());
            ocBReturnAfSend.setReturnPaymentTime(new Date());
            // 退款完成传对账,赋待传
            ocBReturnAfSend.setToSettleStatus(ToACStatusEnum.PENDING.val());
        }
        //设置业务类型
//        if (billType != null && billType.equals(TaobaoReturnOrderExt.SendBillType.RETURN_REFUND.getCode())){
//            StCBusinessType stCBusinessType = omsRefundOrderService.queryReturnOrderType(ocBOrder);
//            ocBReturnAfSend.setBusinessTypeId(stCBusinessType.getId());
//            ocBReturnAfSend.setBusinessTypeCode(stCBusinessType.getEcode());
//            ocBReturnAfSend.setBusinessTypeName(stCBusinessType.getEname());
//        }else {
//
//        }
        StCBusinessType stCBusinessType = omsRefundOrderService.queryRefundOrderType(ocBOrder);
        ocBReturnAfSend.setBusinessTypeId(stCBusinessType.getId());
        ocBReturnAfSend.setBusinessTypeCode(stCBusinessType.getEcode());
        ocBReturnAfSend.setBusinessTypeName(stCBusinessType.getEname());
        OperateUserUtils.saveOperator(ocBReturnAfSend, user);
        return ocBReturnAfSend;
    }


    /**
     * 更新仅退款单状态
     *
     * @param refundId 退款单单号
     * @param status   状态
     * @return 是否更新
     */
    public boolean updateRefundSlip(String refundId, String status) {
        boolean hasSendOrder = false;
        log.info("OmsRefundOrderService.updateRefundSlip  refundId={},status={}",
                refundId, status);
        OcBReturnAfSend ocBReturnAfSend = ocBReturnAfSendMapper.selectOcBReturnAfSendByRefundId(refundId);
        if (ocBReturnAfSend != null) {
            if (TaobaoReturnOrderExt.RefundStatus.SUCCESS.getCode().equals(status)) {
                if (ocBReturnAfSend.getReturnStatus() != null && ReturnAfSendReturnBillTypeEnum.REFUNDCOMPLETED.getVal() == ocBReturnAfSend.getReturnStatus().intValue()) {
                    ocBReturnAfSendMapper.updateOcBReturnAfSendRefundStatus(ReturnAfSendReturnBillTypeEnum.REFUNDCOMPLETED.getVal(), status, refundId);
                } else {
                    //第一次更新成退款完成时，更新打款时间，后续不在更新
                    ocBReturnAfSendMapper.updateOcBReturnAfSendRefundStatusAndTime(ReturnAfSendReturnBillTypeEnum.REFUNDCOMPLETED.getVal(), status, refundId);
                }
                try {
                    if ("RYTK01".equals(ocBReturnAfSend.getBusinessTypeCode())
                            || "RYTK02".equals(ocBReturnAfSend.getBusinessTypeCode())
                            || "RYTK04".equals(ocBReturnAfSend.getBusinessTypeCode())) {
                        // 销售数据汇总
                        OcBSapSalesDataRecordAddTask ocBSapSalesDataRecordAddTask = ocBSapSalesDataRecordAddTaskMapper.selectByOrderIdAndBillType(ocBReturnAfSend.getId(), 2);
                        // 已经存在记录且执行失败过
                        if (Objects.nonNull(ocBSapSalesDataRecordAddTask) && ocBSapSalesDataRecordAddTask.getStatus() == 0
                                && ocBSapSalesDataRecordAddTask.getRetryNumber() > 0) {
                            OcBSapSalesDataRecordAddTask updateRecord = new OcBSapSalesDataRecordAddTask();
                            updateRecord.setId(ocBSapSalesDataRecordAddTask.getId());
                            updateRecord.setStatus(0);
                            updateRecord.setRetryNumber(0);
                            updateRecord.setNextTime(new Date());
                            ocBSapSalesDataRecordAddTaskMapper.updateById(updateRecord);
                        }
                        // 不存在则新增
                        if (Objects.isNull(ocBSapSalesDataRecordAddTask)) {
                            sapSalesDataRecordAddTaskService.addTask(2, ocBReturnAfSend.getId(), SystemUserResource.getRootUser());
                        }
                    }
                } catch (Exception e) {
                    log.error(LogUtil.format("奶卡退单销售数据汇总异常"), e);
                }
                //仅退款 - 退款完成保存退单待红冲发票信息
                Integer billType = ocBReturnAfSend.getBillType();
                if (billType.equals(1)) {
                    List<OcBReturnAfSendItem> ocBReturnAfSendItems = ocBReturnAfSendItemMapper.selectByOcBReturnAfSendIdListBySendId(ocBReturnAfSend.getId());
                    ReturnRedOffsetDTO dto = bulidReturnRedOffsetDTO(ocBReturnAfSend, ocBReturnAfSendItems);
                    acFInvoiceReturnRedOffsetService.createRedOffserRecord(dto);
                }
                oms2SapMapper.insertSingleTaskOrder("oc_b_task_refund_sap", ocBReturnAfSend.getId(),
                        ModelUtil.getSequence("oc_b_task_refund_sap"));
            } else {
                ocBReturnAfSendMapper.updateOcBReturnAfSend(status, refundId);
            }
            hasSendOrder = true;
        }
        // 是否存在发货前退款单
        if (isRefundSlipBfExist(refundId)) {
            ocBReturnBfSendMapper.updateOcBReturnBfSend(status, refundId);
            hasSendOrder = true;
        }
        return hasSendOrder;
    }

    /**
     * 更新物流信息
     *
     * @param logisticCode 物流编码
     * @param returnId     退单编号
     * @param user         更新用户
     */
    public void updateRefundLogicNumber(String logisticCode, String companyName, String returnId, User user) {
        OcBReturnOrder ocBReturnOrder = this.selectReturnOrderByReturnId(returnId);
        if (ocBReturnOrder != null) {
            // 假如中间表有物流单号 && 假如退货单有物流信息并且不去更新
            if (StringUtils.isBlank(logisticCode) || (StringUtils.isNotBlank(ocBReturnOrder.getLogisticsCode())
                    && Objects.equals(ocBReturnOrder.getLogisticsCode(), logisticCode))) {
                return;
            }
            ocBReturnOrder.setLogisticsCode(logisticCode);
            ocBReturnOrder.setCpCLogisticsEname(companyName);
            this.setLogisticInfo(ocBReturnOrder, companyName);
            ocBReturnOrder.setModifieddate(new Date());
            ocBReturnOrder.setModifierid(Long.valueOf(user.getId()));
            ocBReturnOrder.setModifieddate(new Date());
            ocBReturnOrder.setModifiername(user.getName());
            //加入“空运单号延迟推单有效时间”字段
            ocBReturnOrder.setPushDelayTime(ocSaveChangingOrRefundingService.PushDelayTime(ocBReturnOrder));
            ocBReturnOrderMapper.update(ocBReturnOrder,
                    new LambdaQueryWrapper<OcBReturnOrder>().eq(OcBReturnOrder::getId, ocBReturnOrder.getId()));
        }
    }

    /**
     * 查询发货后退款单是否存在
     *
     * @param refundId
     * @return
     */
    public boolean isRefundSlipAfExist(String refundId) {
        List<OcBReturnAfSend> ocBReturnAfSends = ocBReturnAfSendMapper.selectListByRefundId(refundId);
        return CollectionUtils.isEmpty(ocBReturnAfSends);
    }

    public OcBReturnAfSend selectOcBReturnAfSend(String refundId) {
        OcBReturnAfSend ocBReturnAfSend = ocBReturnAfSendMapper.selectOcBReturnAfSendByRefundId(refundId);
        return ocBReturnAfSend;
    }

    /**
     * 查询发货前退款单是否存在
     *
     * @param refundId
     * @return
     */
    public boolean isRefundSlipBfExist(String refundId) {
        List<OcBReturnBfSend> ocBReturnBfSend = ocBReturnBfSendMapper.selectOcBReturnBfSendsByRefundId(refundId);
        return !ocBReturnBfSend.isEmpty();
    }


    /**
     * 生成发货前退款单(仅退款)
     */
    public void foundRefundFrontRefundOnly(OcBOrder ocBOrder, IpBTaobaoRefund ipBTaobaoRefund, User user) {
        OcBReturnBfSend ocBReturnBfSend = null;
        List<OcBReturnBfSend> ocBReturnBfSendList = ocBReturnBfSendMapper.selectOcBReturnBfSendsByRefundId(ipBTaobaoRefund.getRefundId());
        if (CollectionUtils.isNotEmpty(ocBReturnBfSendList)) {
            ocBReturnBfSend = ocBReturnBfSendList.get(0);
        } else {
            ocBReturnBfSend = new OcBReturnBfSend();
            ocBReturnBfSend.setId(ModelUtil.getSequence(TaobaoReturnOrderExt.TABLENAME_OCBRETURNBFSEND));
            ocBReturnBfSend.setCpCShopId(ocBOrder.getCpCShopId());
            ocBReturnBfSend.setCpCShopEcode(ocBOrder.getCpCShopEcode());
            ocBReturnBfSend.setCpCShopTitle(ocBOrder.getCpCShopTitle());
            ocBReturnBfSend.setTid(ipBTaobaoRefund.getTid() + "");
            ocBReturnBfSend.setSubBillNo(ipBTaobaoRefund.getOid() + "");
            ocBReturnBfSend.setTReturnId(ipBTaobaoRefund.getRefundId());
            ocBReturnBfSend.setBuyerNick(ipBTaobaoRefund.getBuyerNick());
            ocBReturnBfSend.setAmtReturn(ipBTaobaoRefund.getRefundFee());
            ocBReturnBfSend.setReason(ipBTaobaoRefund.getReason());
            ocBReturnBfSend.setReturnApplyTime(ipBTaobaoRefund.getGoodReturnTime());
            ocBReturnBfSend.setTReturnStatus(ipBTaobaoRefund.getStatus());
            ocBReturnBfSend.setReturnApplyTime(ipBTaobaoRefund.getCreated());
            //退款说明
            ocBReturnBfSend.setReturnExplain(ipBTaobaoRefund.getRefunddesc());
            OperateUserUtils.saveOperator(ocBReturnBfSend, user);
            ocBReturnBfSendMapper.insert(ocBReturnBfSend);
        }
        //如果退款完成，则保存发票表信息
        /*if(TaobaoReturnOrderExt.RefundStatus.SUCCESS.getCode().equals(ipBTaobaoRefund.getStatus())){
            ReturnRedOffsetDTO dto = bulidReturnRedOffsetDTO(ocBReturnBfSend,ipBTaobaoRefund);
            acFInvoiceReturnRedOffsetService.createRedOffserRecord(dto);
        }*/
    }


    private void buildCardAutoVoidStatus(OcBReturnAfSend ocBReturnAfSend) {
        if (ObjectUtil.isNotNull(ocBReturnAfSend.getBusinessTypeId())) {
            // 判断是否是需要设置成"未作废"的订单(主要是奶卡相关的订单)
            if (ObjectUtil.equal(ocBReturnAfSend.getBusinessTypeCode(), OrderBusinessTypeCodeEnum.MILK_RETURN_ONLY.getCode()) ||
                    ObjectUtil.equal(ocBReturnAfSend.getBusinessTypeCode(), OrderBusinessTypeCodeEnum.CYCLE_RETURN_ONLY.getCode())) {
                ocBReturnAfSend.setCardAutoVoid(CardAutoVoidEnum.HAVE_NOT_VOID.getCode());
                ocBReturnAfSend.setCardAutoVoidMark(1);
            }
        }
    }


    @Transactional(rollbackFor = Exception.class)
    public void insertOcBReturnAfSend(OcBReturnAfSendRelation sendRelation, User user) {
        try {
            OcBReturnAfSend ocBReturnAfSend = sendRelation.getOcBReturnAfSend();
            List<OcBReturnAfSendItem> ocBReturnAfSendItems = sendRelation.getOcBReturnAfSendItems();
            long id = ModelUtil.getSequence(TaobaoReturnOrderExt.TABLENAME_OCBRETURNAFSEND);
            ocBReturnAfSend.setId(id);
            buildCardAutoVoidStatus(ocBReturnAfSend);
            ocBReturnAfSendMapper.insert(ocBReturnAfSend);
            if ("RYTK01".equals(ocBReturnAfSend.getBusinessTypeCode())
                    || "RYTK02".equals(ocBReturnAfSend.getBusinessTypeCode())
                    || "RYTK04".equals(ocBReturnAfSend.getBusinessTypeCode())) {
                // 销售数据汇总 只汇总退款完成的订单数据
                if (Objects.equals(ReturnAfSendReturnBillTypeEnum.REFUNDCOMPLETED.getVal(), ocBReturnAfSend.getReturnStatus())) {
                    sapSalesDataRecordAddTaskService.addTask(2, ocBReturnAfSend.getId(), user);
                }
            }
            if (ocBReturnAfSend.getReturnStatus() == ReturnAfSendReturnBillTypeEnum.REFUNDCOMPLETED.getVal()) {
                oms2SapMapper.insertSingleTaskOrder("oc_b_task_refund_sap", ocBReturnAfSend.getId(),
                        ModelUtil.getSequence("oc_b_task_refund_sap"));
            }
            for (OcBReturnAfSendItem ocBReturnAfSendItem : ocBReturnAfSendItems) {
                long itemId = ModelUtil.getSequence(TaobaoReturnOrderExt.TABLENAME_OCBRETURNAFSENDITEM);
                ocBReturnAfSendItem.setId(itemId);
                ocBReturnAfSendItem.setOcBReturnAfSendId(id);
                ocBReturnAfSendItemMapper.insert(ocBReturnAfSendItem);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("生成退款单异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            throw new NDSException("生成退款单异常");
        }

    }


    /**
     * 判断金额是否一致
     *
     * @param omsOrderRelation 订单对象
     */
    public boolean checkMoneyAgreement(List<OmsOrderRelation> omsOrderRelation, IpBTaobaoRefund ipBTaobaoRefund) {
        BigDecimal refundFee = ipBTaobaoRefund.getRefundFee();
        BigDecimal realAmtSum = BigDecimal.ZERO;
        BigDecimal qtyCount = BigDecimal.ZERO;
        for (OmsOrderRelation orderRelation : omsOrderRelation) {
            List<OcBOrderItem> ocBOrderItems = orderRelation.getOcBOrderItems();
            BigDecimal reduce = ocBOrderItems.stream().map(OcBOrderItem::getRealAmt).
                    reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal qty = ocBOrderItems.stream().map(OcBOrderItem::getQty).
                    reduce(BigDecimal.ZERO, BigDecimal::add);
            qtyCount = qtyCount.add(qty);
            realAmtSum = realAmtSum.add(reduce);

        }
        if (refundFee.compareTo(realAmtSum) == 0 || realAmtSum.compareTo(refundFee) < 0) {
            return true;
        }
        BigDecimal amtCount = realAmtSum.divide(qtyCount, 4, BigDecimal.ROUND_HALF_DOWN);
        return refundFee.compareTo(amtCount) == 0;

    }


    /**
     * 发起拦截并对结果进行更新
     */
    public void sendIntercept(List<Long> refundIds) {
        List<OcBReturnOrder> ocBReturnOrders = ocBReturnOrderMapper.selectOcBReturnOrderListByOrderIds(refundIds,
                InterceptStatus.NEED_INTERCEPT.getCode());
        for (OcBReturnOrder ocBReturnOrder : ocBReturnOrders) {
            OcBReturnOrder returnOrder = new OcBReturnOrder();
            try {
                QimenOrderCallbackModel needParam = new QimenOrderCallbackModel();
                //物流单号
                needParam.setExpressCode(ocBReturnOrder.getLogisticsCode());
                returnOrder.setId(ocBReturnOrder.getId());
                Integer thirdWarehouseType = ocBReturnOrder.getThirdWarehouseType();
                if (thirdWarehouseType != null && thirdWarehouseType == 0) {
                    ValueHolderV14 holderV14 = ipRpcService.distributionInterception(needParam);
                    int code = Tools.getInt(holderV14.getCode(), -1);
                    if (code == 0) {
                        returnOrder.setIntercerptStatus(InterceptStatus.LAUNCH_INTERCEPT_SUCCESS.getCode());
                    } else {
                        returnOrder.setIntercerptStatus(InterceptStatus.LAUNCH_INTERCEPT_FAIL.getCode());
                    }
                } else {
                    returnOrder.setIntercerptStatus(InterceptStatus.NO_LAUNCH_INTERCEPT.getCode());

                }
            } catch (Exception e) {
                log.error(this.getClass().getName() + " 发起拦截失败,快递单号:" + ocBReturnOrder.getLogisticsCode(), e);
                returnOrder.setIntercerptStatus(InterceptStatus.LAUNCH_INTERCEPT_FAIL.getCode());
            }
            ocBReturnOrderMapper.updateById(returnOrder);
        }
    }

    /**
     * 发起拦截并对结果进行更新 todo 待确定
     */
    public void sendIntercept(List<Long> refundIds, User user) {
        List<OcBReturnOrder> ocBReturnOrders = ocBReturnOrderMapper.selectOcBReturnOrderListByOrderIds(refundIds,
                InterceptStatus.NEED_INTERCEPT.getCode());
        for (OcBReturnOrder ocBReturnOrder : ocBReturnOrders) {
            Long origOrderId = ocBReturnOrder.getOrigOrderId(); //订单id
            OcBReturnOrder returnOrder = new OcBReturnOrder();
            try {
                QimenOrderCallbackModel needParam = new QimenOrderCallbackModel();
                //物流单号
                needParam.setExpressCode(ocBReturnOrder.getLogisticsCode());
                needParam.setOperateUser(user);
                returnOrder.setId(ocBReturnOrder.getId());
                Long cpCPhyWarehouseInId = ocBReturnOrder.getCpCPhyWarehouseInId();
                CpCPhyWarehouse cpCPhyWarehouse = cpRpcService.queryByWarehouseId(cpCPhyWarehouseInId);
                /*if (cpCPhyWarehouse != null && cpCPhyWarehouse.getIsAutointercept() != null
                        && cpCPhyWarehouse.getIsAutointercept() == 1) {*/
                if (false) {
                    needParam.setCustomerId(cpCPhyWarehouse.getWmsAccount());
                    needParam.setWarehouseCode(cpCPhyWarehouse.getWmsWarehouseCode());
                    needParam.setOwnerCode(cpCPhyWarehouse.getOwnerCode());
                    OcBOrder ocBOrder = ocBOrderMapper.selectById(origOrderId);
                    String wmsBillNo = ocBOrder.getWmsBillNo();
                    String reserveVarchar04 = ocBOrder.getSgBOutBillNo(); //出库单号
                    //needParam.setDeliveryOrderCode(reserveVarchar04);
                    needParam.setOrderId(wmsBillNo);
                    ValueHolderV14 holderV14 = ipRpcService.distributionInterception(needParam);
                    int code = Tools.getInt(holderV14.getCode(), -1);
                    if (code == 0) {
                        returnOrder.setIntercerptStatus(InterceptStatus.LAUNCH_INTERCEPT_SUCCESS.getCode());
                    } else {
                        returnOrder.setIntercerptStatus(InterceptStatus.LAUNCH_INTERCEPT_FAIL.getCode());
                    }
                } else {
                    returnOrder.setIntercerptStatus(InterceptStatus.NO_LAUNCH_INTERCEPT.getCode());

                }
            } catch (Exception e) {
                log.error(LogUtil.format("发起拦截失败,快递单号:{},异常信息为:{}"), ocBReturnOrder.getLogisticsCode(), Throwables.getStackTraceAsString(e));
                returnOrder.setIntercerptStatus(InterceptStatus.LAUNCH_INTERCEPT_FAIL.getCode());
            }
            ocBReturnOrderMapper.updateById(returnOrder);
        }

    }

    /**
     * 更新退款明细的退款状态ocBReturnOrderRefundMapper
     */
    public boolean updateReturnOrderItem(String returnId, String oid, String status) {
        boolean hasReturnOrderItem = false;
        List<Long> existReturnOrderByOid = this.selectReturnOrderByOidOrReturnId(returnId, oid);
        if (CollectionUtils.isNotEmpty(existReturnOrderByOid)) {
            ocBReturnOrderRefundMapper.updateReturnOrderRefund(existReturnOrderByOid, status, returnId);
            hasReturnOrderItem = true;
        }
        return hasReturnOrderItem;
    }

    public boolean updateReturnOrderItem(String returnId, List<String> oid, String status) {
        boolean hasReturnOrderItem = false;
        List<Long> existReturnOrderByOid = this.selectReturnOrderByOidOrReturnId(returnId, oid);
        if (CollectionUtils.isNotEmpty(existReturnOrderByOid)) {
            ocBReturnOrderRefundMapper.updateReturnOrderRefund(existReturnOrderByOid, status, returnId);
            hasReturnOrderItem = true;
        }
        return hasReturnOrderItem;
    }


    /**
     * 根据订单明细更新主订单的退款状态
     */

    @Transactional(rollbackFor = Exception.class)
    public void updateOcOrderStatusInfo(List<OmsOrderRelation> OmsOrderRelation, String status) {
        // List<Long> ids = new ArrayList<>();
        if (CollectionUtils.isEmpty(OmsOrderRelation)) {
            return;
        }
        for (OmsOrderRelation omsOrderRelation : OmsOrderRelation) {
            //如果零售发货单状态是待分配，则不更新退款中标识
            Integer orderStatus = omsOrderRelation.getOcBOrder().getOrderStatus();
            if (OmsOrderStatus.ORDER_DEFAULT.toInteger().equals(orderStatus)) {
                continue;
            }
            List<OcBOrderItem> ocBOrderItems = omsOrderRelation.getOcBOrderItems();
            if (CollectionUtils.isNotEmpty(ocBOrderItems)) {
                for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
                    ocBOrderItemMapper.updateOrderItemPtReturnStatusByOrderId(
                            ocBOrderItem.getOcBOrderId(), status, ocBOrderItem.getId());
                }
            }
            //更新主表的退款状态
            OcBOrder ocBOrder = omsOrderRelation.getOcBOrder();
            if (ocBOrder == null) {
                continue;
            }
            TransactionUtils.afterCommitSyncExecute(() -> {
                List<OcBOrderItem> orderItems = ocBOrderItemMapper.selectOrderItemList(ocBOrder.getId());
                if (CollectionUtils.isEmpty(orderItems)) {
                    return;
                }
                List<String> beforePtReturnStatusList = ocBOrderItems.stream().map(OcBOrderItem::getPtReturnStatus).collect(Collectors.toList());
                List<String> afterCommitPtReturnStatsList = orderItems.stream().map(OcBOrderItem::getPtReturnStatus).collect(Collectors.toList());
                log.info("updateOcOrderStatusInfo.beforePtReturnStatusList: {},afterCommitPtReturnStatsList: {}",
                        beforePtReturnStatusList, afterCommitPtReturnStatsList);
                Integer integer = this.judgeReturnStatus(orderItems);
                OcBOrder order = new OcBOrder();
                order.setId(ocBOrder.getId());
                order.setRefundStatus(integer);
                List<OcBOrderItem> collect =
                        orderItems.stream().filter(ocBOrderItem -> isReturning(ocBOrderItem.getPtReturnStatus())).collect(Collectors.toList());
                // 假如有退款中的，且原本不是退款中的，就把状态改为退款中，打标
                if (collect.isEmpty() && !Objects.equals(ocBOrder.getIsInreturning(), 0)) {
                    order.setIsInreturning(0);
                } else if (!collect.isEmpty() && Objects.equals(ocBOrder.getIsInreturning(), 0)) {
                    //  holdOrder(ocBOrder, true);
                    order.setIsInreturning(1);
                }
                ocBOrderMapper.updateById(order); //  死锁报错标记-1
            });
            
        }
    }

    /**
     * hold order handle
     *
     * @param ocBOrder  订单原单
     * @param hasRefund 是有退款
     */
    private void holdOrder(OcBOrder ocBOrder, boolean hasRefund) {
        int isTrue = hasRefund ? 1 : 0;
        String status = hasRefund ? "Y" : "N";
        // todo 调用 拦截/取消
        OcBOrder hold = new OcBOrder();
        hold.setId(ocBOrder.getId());
        hold.setIsInterecept(isTrue);
        hold.setIsInreturning(isTrue);
        boolean isHolding = holdItemService.isHolding(ocBOrder.getId(), OrderHoldReasonEnum.REFUND_HOLD, status);
        if (!isHolding) {
            holdService.holdOrUnHoldOrder(hold, OrderHoldReasonEnum.REFUND_HOLD);
        }
        ocBOrderMapper.updateById(hold);
    }

    /**
     * 是否是退款中
     *
     * @param ptRefundStatus 平台退款状态
     * @return
     */
    boolean isReturning(String ptRefundStatus) {
        ArrayList<String> refundIng = Lists.newArrayList(
                TaobaoReturnOrderExt.RefundStatus.WAIT_SELLER_AGREE.getCode(),
                TaobaoReturnOrderExt.RefundStatus.WAIT_BUYER_RETURN_GOODS.getCode(),
                TaobaoReturnOrderExt.RefundStatus.WAIT_SELLER_CONFIRM_GOODS.getCode()
        );
        return refundIng.contains(ptRefundStatus);
    }


    /**
     * 是否是退款中
     *
     * @param ptRefundStatus 平台退款状态
     * @return
     */
    boolean isReturning(String ptRefundStatus, boolean isBefore) {
        ArrayList<String> refundIng = Lists.newArrayList(
                TaobaoReturnOrderExt.RefundStatus.WAIT_SELLER_AGREE.getCode(),
                TaobaoReturnOrderExt.RefundStatus.WAIT_SELLER_CONFIRM_GOODS.getCode()
        );
        if (!isBefore) {
            refundIng.add(TaobaoReturnOrderExt.RefundStatus.WAIT_BUYER_RETURN_GOODS.getCode());
        }
        return refundIng.contains(ptRefundStatus);
    }

    //判断明细的退款状态

    /**
     * 根据订单明细退款状态
     *
     * @param orderItems
     * @return
     */
    private Integer judgeReturnStatus(List<OcBOrderItem> orderItems) {
        int noRefund = 0;
        int partRefund = 0;
        for (OcBOrderItem orderItem : orderItems) {
            String ptReturnStatus = orderItem.getPtReturnStatus();
            if (StringUtils.isEmpty(ptReturnStatus)
                    || TaobaoReturnOrderExt.RefundStatus.CLOSED.getCode().equals(ptReturnStatus)) {
                noRefund++;
                continue;
            }
            boolean flag = TaobaoReturnOrderExt.RefundStatus.WAIT_SELLER_AGREE.getCode().equals(ptReturnStatus)
                    || TaobaoReturnOrderExt.RefundStatus.WAIT_BUYER_RETURN_GOODS.getCode().equals(ptReturnStatus)
                    || TaobaoReturnOrderExt.RefundStatus.WAIT_SELLER_CONFIRM_GOODS.getCode().equals(ptReturnStatus)
                    || TaobaoReturnOrderExt.RefundStatus.SELLER_REFUSE_BUYER.getCode().equals(ptReturnStatus)
                    || TaobaoReturnOrderExt.RefundStatus.SUCCESS.getCode().equals(ptReturnStatus);
            if (flag) {
                partRefund++;
            }
        }
        if (orderItems.size() == noRefund) {
            return RefundStatusEnum.NO_PAY.getKey();
        } else if (partRefund == orderItems.size()) {
            return RefundStatusEnum.ALL_PAY.getKey();
        } else {
            return RefundStatusEnum.PART_PAY.getKey();
        }
    }

    /**
     * 客退的退单是否存在的处理
     *
     * @param ipBTaobaoRefund
     * @return
     */
    public ReturnOrderRelation goodsAfterOrderIsExist(IpBTaobaoRefund ipBTaobaoRefund) {
        ReturnOrderRelation relation = new ReturnOrderRelation();
        List<Long> existReturnOrder = this.selectReturnOrderRefundByReturnId(ipBTaobaoRefund.getRefundId());
        if (CollectionUtils.isEmpty(existReturnOrder)) {
            List<Long> refundByoid = this.isExistReturnOrderRefundByOid(ipBTaobaoRefund);
            //将必要的退款数据更新到对应的退货单
            if (CollectionUtils.isNotEmpty(refundByoid)) {
                List<OcBReturnOrder> ocBReturnOrders =
                        ocBReturnOrderMapper.selectReturnOrderListByOrderIds(refundByoid);
                ocBReturnOrders = ocBReturnOrders.stream().filter(p -> TaobaoReturnOrderExt.BillType.REFUND.getCode().equals(p.getBillType())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(ocBReturnOrders)) {
                    refundByoid = ocBReturnOrders.stream().map(OcBReturnOrder::getId).collect(Collectors.toList());
                    List<OcBReturnOrderRefund> orderRefunds =
                            ocBReturnOrderRefundMapper.selectOcBReturnOrderRefundByOrderIds(refundByoid);
                    BigDecimal qty = orderRefunds.stream().map(OcBReturnOrderRefund::getQtyRefund).
                            reduce(BigDecimal.ZERO, BigDecimal::add);
                    relation.setQty(qty);
                    this.updateOcBReturnOrder(refundByoid, ipBTaobaoRefund, ocBReturnOrders, orderRefunds);
                    relation.setIds(refundByoid);
                    relation.setFlag(true);
                }
            }
        } else {
            relation.setIds(existReturnOrder);
            relation.setFlag(false);
        }
        return relation;
    }

    /**
     * 拦截关闭时查询退款单是否存在的处理
     *
     * @param ipBTaobaoRefund
     * @return
     */
    public List<Long> interceptOrderIsExist(IpBTaobaoRefund ipBTaobaoRefund) {
        List<Long> existReturnOrder = this.isExistReturnOrderRefundByReturnId(ipBTaobaoRefund);
        if (CollectionUtils.isEmpty(existReturnOrder)) {
            List<Long> refundByoid = this.isExistReturnOrderRefundByOid(ipBTaobaoRefund);
            //将必要的退款数据更新到对应的退货单
            if (CollectionUtils.isNotEmpty(refundByoid)) {
                List<OcBReturnOrder> ocBReturnOrders =
                        ocBReturnOrderMapper.selectReturnOrderListByOrderIds(refundByoid);
                List<OcBReturnOrderRefund> orderRefunds =
                        ocBReturnOrderRefundMapper.selectOcBReturnOrderRefundByOrderIds(refundByoid);
                this.updateOcBReturnOrder(refundByoid, ipBTaobaoRefund, ocBReturnOrders, orderRefunds);
                List<OcBReturnOrderRefund> orderRefundList =
                        orderRefunds.stream().filter(p -> p.getRefundStatus() != null
                                && (TaobaoReturnOrderExt.RefundStatus.CLOSED.getCode().equals(p.getRefundStatus())
                                || TaobaoReturnOrderExt.RefundStatus.SELLER_REFUSE_BUYER.getCode().equals(p.getRefundStatus()))).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(orderRefundList) && orderRefunds.size() == orderRefundList.size()) {
                    return refundByoid;
                } else {
                    return null;
                }
            }
        } else {
            List<OcBReturnOrderRefund> orderRefunds =
                    ocBReturnOrderRefundMapper.selectOcBReturnOrderRefundByOrderIds(existReturnOrder);
            List<OcBReturnOrderRefund> orderRefundList = orderRefunds.stream().filter(p -> p.getRefundStatus() != null
                    && (TaobaoReturnOrderExt.RefundStatus.CLOSED.getCode().equals(p.getRefundStatus())
                    || TaobaoReturnOrderExt.RefundStatus.SELLER_REFUSE_BUYER.getCode().equals(p.getRefundStatus()))).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(orderRefundList) && orderRefunds.size() == orderRefundList.size()) {
                return existReturnOrder;
            } else {
                return null;
            }
        }
        return existReturnOrder;
    }


    /**
     * 根据明细表的退款单号查询退单是否存在
     *
     * @param refundId
     * @return
     */
    public List<Long> selectReturnOrderRefundByReturnId(String refundId) {
        Set<Long> ids = new HashSet<>();
        String[] returnFileds = {"OC_B_RETURN_ORDER_ID"};
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("REFUND_BILL_NO", refundId);
        whereKeys.put("RETURN_STATUS", "!=" + TaobaoReturnOrderExt.ReturnOrderStatus.CANCEL.getCode());
        JSONObject search = ElasticSearchUtil.search(TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER,
                TaobaoReturnOrderExt.TABLENAME_OCRETURNORDERREFUND, whereKeys, null, null, 10, 0, returnFileds);
        JSONArray returnData = search.getJSONArray("data");
        if (null != returnData && !returnData.isEmpty()) {
            for (int i = 0; i < returnData.size(); i++) {
                JSONObject jsonObject = returnData.getJSONObject(i);
                Long orderId = jsonObject.getLong("OC_B_RETURN_ORDER_ID");
                ids.add(orderId);
            }
        } else {
            Long returnOrderId =
                    OmsReturnOrderService.selectOmsReturnOrderFromRedisByReturnId(refundId);
            if (returnOrderId > 0L) {
                ids.add(returnOrderId);
            }
        }
        List<Long> returnIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(ids)) {
            List<OcBReturnOrder> list = ocBReturnOrderMapper.selectOcBReturnOrderByOrder(new ArrayList<>(ids));
            if (CollectionUtils.isNotEmpty(list)) {
                returnIds = list.stream().map(OcBReturnOrder::getId).collect(Collectors.toList());
            }
        }
        return returnIds;
    }


    /**
     * 根据明细表的退款单号查询退单是否存在
     *
     * @param ipBTaobaoRefund
     * @return
     */
    public List<Long> isExistReturnOrderRefundByReturnId(IpBTaobaoRefund ipBTaobaoRefund) {
        // ES 查询
        Set<Long> ids = ES4ReturnOrder.findReturnOrderIdByRefundBillNo(ipBTaobaoRefund.getRefundId());
        if (CollectionUtils.isNotEmpty(ids)) {
            return new ArrayList<>(ids);
        } else {
            // @20200720 增加查redis逻辑
            Long returnOrderId =
                    OmsReturnOrderService.selectOmsReturnOrderFromRedisByReturnId(ipBTaobaoRefund.getRefundId());

            if (returnOrderId > 0) {
                List<Long> rids = new ArrayList<>();
                rids.add(returnOrderId);
                return rids;
            }

            return null;
        }
    }

    /**
     * 根据明细表的oid查询退货单是否存在
     *
     * @param ipBTaobaoRefund
     * @return
     */
    public List<Long> isExistReturnOrderRefundByOid(IpBTaobaoRefund ipBTaobaoRefund) {
        Set<Long> ids = ES4ReturnOrder.findReturnOrderIdByOid(ipBTaobaoRefund.getOid());
        if (CollectionUtils.isNotEmpty(ids)) {
            //排除已经取消的退换货订单
            List<OcBReturnOrder> list = ocBReturnOrderMapper.selectOcBReturnOrderByOrder(new ArrayList<>(ids));
            if (CollectionUtils.isNotEmpty(list)) {
                return list.stream().map(OcBReturnOrder::getId).collect(Collectors.toList());
            }
        }
        return null;
    }

    /**
     * 根据明细表的oid查询退货单是否存在
     *
     * @param ipBTaobaoRefund
     * @return
     */
    public List<OcBReturnOrder> queryReturnOrderRefundListByOid(IpBTaobaoRefund ipBTaobaoRefund) {
        Set<Long> ids = ES4ReturnOrder.findReturnOrderIdByOid(ipBTaobaoRefund.getOid());
        if (CollectionUtils.isNotEmpty(ids)) {
            //排除已经取消的退换货订单
            List<OcBReturnOrder> list = ocBReturnOrderMapper.selectOcBReturnOrderByOrder(new ArrayList<>(ids));
            if (CollectionUtils.isNotEmpty(list)) {
                return list;
            }
        }
        return null;
    }


    /**
     * 先根据退款单号查  如果未查询到则用(oid)子订单号查询
     *
     * @return 退货单ID列表
     */
    private List<Long> selectReturnOrderByOidOrReturnId(String refundId, String oid) {

        Set<Long> ids = ES4ReturnOrder.findReturnOrderIdByRefundBillNo(refundId);
        if (CollectionUtils.isNotEmpty(ids)) {
            return new ArrayList<>(ids);
        }
        ids = ES4ReturnOrder.findReturnOrderIdByOid(oid);
        if (CollectionUtils.isNotEmpty(ids)) {
            return new ArrayList<>(ids);
        }
        // @20200720 增加查reids逻辑
        Long returnOrderId = OmsReturnOrderService.selectOmsReturnOrderFromRedisByReturnId(refundId);
        // redis返回的默认是0
        if (returnOrderId > 0) {
            List<Long> rids = new ArrayList<>();
            rids.add(returnOrderId);
            return rids;
        }

        return null;
    }

    /**
     * 先根据退款单号查  如果未查询到则用(oid)子订单号查询
     *
     * @return 退货单ID列表
     */
    private List<Long> selectReturnOrderByOidOrReturnId(String refundId, List<String> oid) {

        Set<Long> ids = ES4ReturnOrder.findReturnOrderIdByRefundBillNo(refundId);
        if (CollectionUtils.isNotEmpty(ids)) {
            return new ArrayList<>(ids);
        }

        ids = ES4ReturnOrder.findReturnOrderIdByOid(StringUtils.join(oid, ","));
        if (CollectionUtils.isNotEmpty(ids)) {
            return new ArrayList<>(ids);
        }

        // @20200720 增加查reids逻辑
        Long returnOrderId = OmsReturnOrderService.selectOmsReturnOrderFromRedisByReturnId(refundId);
        // redis返回的默认是0
        if (Objects.nonNull(returnOrderId) && returnOrderId.longValue() > 0) {
            List<Long> rids = new ArrayList<>();
            rids.add(returnOrderId);
            return rids;
        }
        return null;
    }

    /**
     * 根据refundId去获取退换货订单
     *
     * @return 退换货订单
     */
    private OcBReturnOrder selectReturnOrderByReturnId(String refundId) {
        Set<Long> ids = ES4ReturnOrder.findIdByReturnId(refundId);
        if (CollectionUtils.isNotEmpty(ids)) {
            LambdaQueryWrapper<OcBReturnOrder> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(OcBReturnOrder::getId, ids);
            return ocBReturnOrderMapper.selectList(wrapper).stream().findFirst().orElse(null);
        }
        return null;
    }

    /**
     * 退款关闭服务
     *
     * @param returnOrderIds
     * @param omsOrderRelation
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 refundOrderClose(List<Long> returnOrderIds, List<OmsOrderRelation> omsOrderRelation,
                                           IpBTaobaoRefund ipBTaobaoRefund, User user) {
        List<OcBReturnOrder> ocBReturnOrders = ocBReturnOrderMapper.selectOcBReturnOrderByOrderIds(returnOrderIds);
        if (CollectionUtils.isNotEmpty(ocBReturnOrders)) {
            JSONObject jsonObject = new JSONObject();
            JSONArray returnOrderIdsWhichHaveNotBeenInterceptedSuccessfully = new JSONArray();
            List<OcBReturnOrderRefund> ocBReturnOrderRefunds = new ArrayList<>();
            CusRedisTemplate<String, Long> objRedisTemplate = RedisMasterUtils.getObjRedisTemplate();
            for (OcBReturnOrder ocBReturnOrder : ocBReturnOrders) {
                //
                List<OcBReturnOrderRefund> returnOrderRefundList = ocBReturnOrderRefundMapper.selectByOcOrderId(ocBReturnOrder.getId());
                ocBReturnOrderRefunds.addAll(returnOrderRefundList);
                Integer returnStatus = ocBReturnOrder.getReturnStatus();
                //包裹拦截状态
                //if status isn't intercept success ,then delete from redis
                if (TaobaoReturnOrderExt.ReturnOrderStatus.WAITIN.getCode().equals(returnStatus)) {
                    returnOrderIdsWhichHaveNotBeenInterceptedSuccessfully.add(ocBReturnOrder.getId());
                    String logisticsCode = ocBReturnOrder.getLogisticsCode();
                    String returnId = ocBReturnOrder.getReturnId();
                    Long tbDisputeId = ocBReturnOrder.getTbDisputeId();
                    if (StringUtils.isNotEmpty(logisticsCode)) {
                        String redisKey = BllRedisKeyResources.getOmsReturnOrderLogisticsKey(logisticsCode);
                        objRedisTemplate.delete(redisKey);
                    }
                    if (StringUtils.isNotEmpty(returnId)) {
                        String redisKey = BllRedisKeyResources.getOmsReturnOrderReturnIdKey(returnId);
                        objRedisTemplate.delete(redisKey);
                    }
                    if (tbDisputeId != null) {
                        String redisKey = BllRedisKeyResources.getReturnOrderDisputeIdKey(tbDisputeId + "");
                        RedisMasterUtils.getStrRedisTemplate().delete(redisKey);
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(returnOrderIdsWhichHaveNotBeenInterceptedSuccessfully)) {
                jsonObject.put("ids", returnOrderIdsWhichHaveNotBeenInterceptedSuccessfully);
                ValueHolderV14 holderV14 = ocCancelChangingOrRefundService.orRefundService(jsonObject, user, Boolean.FALSE);
                int code = Tools.getInt(holderV14.getCode(), -1);
                if (code == 0) {
                    if (ipBTaobaoRefund != null) {
                        ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                                "", ipBTaobaoRefund);
                    }
                    if (CollectionUtils.isNotEmpty(ocBReturnOrderRefunds)) {
                        String flagMark = "";
                        for (OcBReturnOrderRefund ocBReturnOrderRefund : ocBReturnOrderRefunds) {
                            OcBOrderItem item = new OcBOrderItem();
                            item.setOcBOrderId(ocBReturnOrderRefund.getOcBOrderId());
                            OcBOrderItem ocBOrderItem = ocBOrderItemMapper.queryOrderById(ocBReturnOrderRefund.getOcBOrderItemId(), ocBReturnOrderRefund.getOcBOrderId());
                            if (ocBOrderItem.getProType() == SkuType.COMBINE_PRODUCT) {
                                String groupGoodsMark = ocBOrderItem.getGroupGoodsMark();
                                if (StringUtils.isNotEmpty(groupGoodsMark) && groupGoodsMark.equals(flagMark)) {
                                    ocBOrderItemMapper.updateApplyQtyByOrderId(ocBReturnOrderRefund.getOcBOrderId(), groupGoodsMark);
                                    flagMark = groupGoodsMark;
                                }
                            }
                            item.setId(ocBReturnOrderRefund.getOcBOrderItemId());
                            item.setQtyReturnApply(BigDecimal.ZERO);
                            omsOrderItemService.updateOcBOrderItem(item, ocBReturnOrderRefund.getOcBOrderId());
                        }
                    }
                    if (ipBTaobaoRefund != null) {
                        //更新中间表转换转状态
                        this.updateOrder(omsOrderRelation);
                    }
                    //插入日志
                    this.insertLogs(returnOrderIdsWhichHaveNotBeenInterceptedSuccessfully, user);
                    return ValueHolderV14Utils.getSuccessValueHolder("取消退换货单成功");
                } else {
//                    throw new NDSException("退换货单取消失败!");
                    return ValueHolderV14Utils.getFailValueHolder("取消退换货单失败");
                }
            } else {
                if (ipBTaobaoRefund != null) {
                    String remark = SysNotesConstant.SYS_REMARK33;
                    ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                            remark, ipBTaobaoRefund);
                }
            }
        }
        return ValueHolderV14Utils.getSuccessValueHolder("执行完成");
    }


    private void insertLogs(JSONArray jsonArray, User user) {
        for (Object o : jsonArray) {

            Long returnId = Long.valueOf(o.toString());
            this.addReturnLog(returnId, SysNotesConstant.CANCEL_LOG_MESSAGE,
                    SysNotesConstant.CANCEL_LOG_TYPE, user);
        }
    }

    private void addReturnLog(Long returnId, String logMsg, String logType, User user) {
        OcBReturnOrderLog ocBReturnOrderLog = new OcBReturnOrderLog();
        ocBReturnOrderLog.setId(ModelUtil.getSequence("oc_b_return_order_log"));
        ocBReturnOrderLog.setLogMessage(logMsg);
        ocBReturnOrderLog.setLogType(logType);
        ocBReturnOrderLog.setOcBReturnOrderId(returnId);
        returnOrderTransferUtil.saveSysLog(ocBReturnOrderLog, user);
        ocBReturnOrderLogMapper.insert(ocBReturnOrderLog);
    }


    private void insertLogs(JSONArray jsonArray) {
        for (Object o : jsonArray) {
            Long returnId = Long.valueOf(o.toString());

            OcBReturnOrderLog ocBReturnOrderLog = new OcBReturnOrderLog();

            ocBReturnOrderLog.setLogMessage(SysNotesConstant.CANCEL_LOG_MESSAGE);
            ocBReturnOrderLog.setLogType(SysNotesConstant.CANCEL_LOG_TYPE);
            ocBReturnOrderLog.setOcBReturnOrderId(returnId);
            //returnOrderTransferUtil.saveSysLog(ocBReturnOrderLog, user);
            ocBReturnOrderLogMapper.insert(ocBReturnOrderLog);
        }
    }

    private void updateOrder(List<OmsOrderRelation> omsOrderRelation) {
        for (OmsOrderRelation orderRelation : omsOrderRelation) {
            OcBOrder ocBOrder1 = orderRelation.getOcBOrder();
            OcBOrder ocBOrder = new OcBOrder();
            ocBOrder.setId(ocBOrder1.getId());
            //无退货
            ocBOrder.setReturnStatus(0);
            omsOrderService.updateOrderInfo(ocBOrder);
        }
    }

    /**
     * 检验原单不存在的时间
     *
     * @return
     */
    private boolean checkReturnOrderData(IpBTaobaoRefund ipBTaobaoRefund) {
        Date date = new Date();
        //判断退单时间是否超过三天
        Date created = ipBTaobaoRefund.getCreated();
        Long threeDays = 3 * 24 * 60 * 60 * 1000L + created.getTime();
        return threeDays < date.getTime();
    }


    @Transactional(rollbackFor = Exception.class)
    public void saveExistReturnOrder(List<Long> ids, String sid, String companyName, String status, User operateUser) {
        try {
            List<OcBReturnOrder> ocBReturnOrders = ocBReturnOrderMapper.selectOcBReturnOrderByOrderIds(ids);
            for (OcBReturnOrder ocBReturnOrder : ocBReturnOrders) {

                String logisticsCode = ocBReturnOrder.getLogisticsCode();
                if (StringUtils.isNotEmpty(sid) && (StringUtils.isEmpty(logisticsCode) || !logisticsCode.equalsIgnoreCase(sid))) {
                    OcBReturnOrder returnOrder1 = new OcBReturnOrder();
                    returnOrder1.setId(ocBReturnOrder.getId());
                    returnOrder1.setLogisticsCode(sid);
                    returnOrder1.setCpCLogisticsEname(companyName);
                    ocBReturnOrder.setLogisticsCode(sid);
                    ocBReturnOrder.setCpCLogisticsEname(companyName);
                    this.setLogisticInfo(returnOrder1, companyName);
                    ocSaveChangingOrRefundingService.checkBillType(ocBReturnOrder);
                    returnOrder1.setBillType(ocBReturnOrder.getBillType());
                    //加入“空运单号延迟推单有效时间”字段
                    returnOrder1.setPushDelayTime(ocSaveChangingOrRefundingService.PushDelayTime(returnOrder1));
                    ocBReturnOrderMapper.updateById(returnOrder1);

                    //修改退单中间表状态更新系统备注
                    //增加系统日志
                    omsReturnOrderService.saveAddOrderReturnLog(ocBReturnOrder.getId(),
                            SysNotesConstant.UPDATE_LOG_MESSAGE,
                            SysNotesConstant.UPDATE_LOG_TYPE, operateUser);
                }

                List<OcBReturnOrderRefund> orderRefunds =
                        ocBReturnOrderRefundMapper.selectByOcOrderId(ocBReturnOrder.getId());
                if (CollectionUtils.isNotEmpty(orderRefunds)) {
                    for (OcBReturnOrderRefund orderRefund : orderRefunds) {
                        OcBReturnOrderRefund returnOrderRefund = new OcBReturnOrderRefund();
                        returnOrderRefund.setId(orderRefund.getId());
                        returnOrderRefund.setOcBReturnOrderId(orderRefund.getOcBReturnOrderId());
                        returnOrderRefund.setRefundStatus(status);
                        JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(returnOrderRefund));
                        ocBReturnOrderRefundMapper.updateRecord(jsonObject);
                    }
                }
            }
        } catch (Exception e) {
            log.error(LogUtil.format("更新退换货单异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            throw new NDSException(this.getClass().getName() + "更新退换货单异常");
        }
    }


    /**
     * 作废退款单
     *
     * @param refundId /**
     *                 作废退款单
     * @param refundId
     */
    public boolean closedRefundSlip(String refundId) {
        OcBReturnAfSend ocBReturnAfSend = ocBReturnAfSendMapper.selectOcBReturnAfSendByRefundId(refundId);
        if (ocBReturnAfSend != null) {
            //判断退款单的审核状态
            Integer returnStatus = ocBReturnAfSend.getReturnStatus();
            if (returnStatus == ReturnAfSendReturnBillTypeEnum.CANCEL.getVal()) {
                return true;
            }
            //退款中  则取消
            if (ReturnAfSendReturnBillTypeEnum.REFUNDING.getVal() == returnStatus
                    || ReturnAfSendReturnBillTypeEnum.NOREFUND.getVal() == returnStatus) {
                //状态为未审核,将状态改为已作废
                int i = ocBReturnAfSendMapper.updateOcBReturnAfSendByReturnStatus(ReturnAfSendReturnStatusEnum.INVALID.getVal(), refundId);
                return i > 0;
            }
        }
        return true;
    }

    public void setLogisticInfo(OcBReturnOrder returnOrder, String buyerLogisticName) {
        if (StringUtils.isNotEmpty(buyerLogisticName)) {
            LogisticsInfo logisticsInfo = cpRpcService.selectLogisticsInfoByName(buyerLogisticName);
            if (logisticsInfo != null) {
                returnOrder.setCpCLogisticsEcode(logisticsInfo.getCode());
                returnOrder.setCpCLogisticsId(logisticsInfo.getId());
            }
        }
    }

    public void setLogisticInfo(OcBReturnOrder ocBReturnOrder, IpBStandplatRefund refund) {
        if (refund == null) {
            return;
        }
        // 假如中间表有物流单号 && 假如退货单有物流信息并且不去更新
        if (StringUtils.isBlank(refund.getLogisticsNo()) || (StringUtils.isNotBlank(ocBReturnOrder.getLogisticsCode())
                && Objects.equals(ocBReturnOrder.getLogisticsCode(), refund.getLogisticsNo()))) {
            return;
        }
        // 设置物流信息
        ocBReturnOrder.setLogisticsCode(refund.getLogisticsNo());
        if (StringUtils.isNotBlank(refund.getCompanyName())) {
            LogisticsInfo logisticsInfo =
                    Optional.ofNullable(cpRpcService.selectLogisticsInfoByName(refund.getCompanyName())).orElse(new LogisticsInfo());
            ocBReturnOrder.setCpCLogisticsEname(logisticsInfo.getName());
            ocBReturnOrder.setCpCLogisticsEcode(logisticsInfo.getCode());
        }
    }


    /**
     * 是空的或者是0
     *
     * @param arg
     * @return
     */
    public boolean isNullOrZero(BigDecimal arg) {
        return arg == null || BigDecimal.ZERO.compareTo(arg) == 0;
    }

    /**
     * 根据平台单号校验 退货单  发货前/后退款单 是否存在
     *
     * @param tid
     */
    public boolean isExistReturnRelevantInfo(String tid) {
        if (StringUtils.isBlank(tid)) {
            return false;
        }

        List<OcBReturnBfSend> ocBReturnBfSends =
                ocBReturnBfSendMapper.selectList(new LambdaQueryWrapper<OcBReturnBfSend>().
                        eq(OcBReturnBfSend::getTid, tid));
        if (!ocBReturnBfSends.isEmpty()) {
            return true;
        }

        List<OcBReturnAfSend> ocBReturnAfSends =
                ocBReturnAfSendMapper.selectList(new LambdaQueryWrapper<OcBReturnAfSend>().
                        eq(OcBReturnAfSend::getTid, tid));
        if (!ocBReturnAfSends.isEmpty()) {
            return true;
        }
        List<OcBReturnOrder> ocBReturnOrders = ocBReturnOrderMapper.selectList(new LambdaQueryWrapper<OcBReturnOrder>()
                .eq(OcBReturnOrder::getTid, tid));
        return !ocBReturnOrders.isEmpty();
    }

    /**
     * hold order handle
     *
     * @param ocBOrder
     */
    public void holdOrderHandle(OcBOrder ocBOrder, boolean isBefore) {
        OcBOrder order = ocBOrderMapper.selectById(ocBOrder.getId());
        if (Objects.isNull(order)) {
            return;
        }
        List<OcBOrderItem> orderItems = ocBOrderItemMapper.selectOrderItemList(ocBOrder.getId());
        if (CollectionUtils.isEmpty(orderItems)) {
            return;
        }
        List<OcBOrderItem> collect =
                orderItems.stream().filter(ocBOrderItem -> isReturning(ocBOrderItem.getPtReturnStatus(), isBefore)).collect(Collectors.toList());
        // 订单,是否有退款中
        holdOrder(order, !collect.isEmpty());
    }


    /**
     * 先根据退款单号查  如果未查询到则用(oid)子订单号查询
     *
     * @return
     */
    public List<Long> isExistReturnOrderByOidAndReturnId(String refundId, String oid) {
        Set<Long> ids = new HashSet<>();
        String[] returnFileds = {"OC_B_RETURN_ORDER_ID"};
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("REFUND_BILL_NO", refundId);
        JSONObject search = ElasticSearchUtil.search(TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER,
                TaobaoReturnOrderExt.TABLENAME_OCRETURNORDERREFUND, whereKeys, null, null, 10, 0, returnFileds);
        JSONArray returnData = search.getJSONArray("data");
        if (null != returnData && !returnData.isEmpty()) {
            for (int i = 0; i < returnData.size(); i++) {
                JSONObject jsonObject = returnData.getJSONObject(i);
                Long orderId = jsonObject.getLong("OC_B_RETURN_ORDER_ID");
                ids.add(orderId);
            }
        } else {
            whereKeys = new JSONObject();
            whereKeys.put("OID", oid);
            search = ElasticSearchUtil.search(TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER,
                    TaobaoReturnOrderExt.TABLENAME_OCRETURNORDERREFUND, whereKeys, null, null, 10, 0, returnFileds);
            log.debug(this.getClass().getName() + " 查询退换货单是否存在11:{}", search.toString());
            returnData = search.getJSONArray("data");
            if (null != returnData && !returnData.isEmpty()) {
                for (int i = 0; i < returnData.size(); i++) {
                    JSONObject jsonObject = returnData.getJSONObject(i);
                    Long orderId = jsonObject.getLong("OC_B_RETURN_ORDER_ID");
                    ids.add(orderId);
                }

            }
        }
        if (CollectionUtils.isNotEmpty(ids)) {
            return new ArrayList<>(ids);
        }
        return null;
    }

    /**
     * 已发货退款单存在时  判断退款单的类型以及货物状态是否与平台一致  不一致则取消退换货单和已发货退款单
     * 等待后续继续生成
     */

    public boolean judgeRefundFormInfoIsAgreement(IpBTaobaoRefund ipBTaobaoRefund, User operateUser) {
        String refundId = ipBTaobaoRefund.getRefundId();
        OcBReturnAfSend ocBReturnAfSend = ocBReturnAfSendMapper.selectOcBReturnAfSendByRefundId(refundId);
        if (ocBReturnAfSend == null) {
            return false;
        }
        //中间的是否为 0 仅退款或者 1 退货退款
        Integer hasGoodReturn = ipBTaobaoRefund.getHasGoodReturn();
        //货物状态
        String goodStatus = ipBTaobaoRefund.getGoodStatus();
        //退款类型 0 退货退款 1仅退款',
        Integer billType = ocBReturnAfSend.getHasGoodReturn();
        //货物状态
        String ptGoodStatus = ocBReturnAfSend.getPtGoodStatus();
        //只要有一个不相等就取消
        boolean isCancel = false;
        if (!hasGoodReturn.equals(billType) || (!goodStatus.equals(ptGoodStatus) && !TaobaoReturnOrderExt.GoodStatus.BUYER_RETURNED_GOODS.getCode().equals(goodStatus))) {

            //通过oid查询退换货单是否存在 并判断退单的类型是否为拦截类型 如果是 则取消 不是则取消已发货退款单
            List<OcBReturnOrder> list = this.queryReturnOrderRefundListByOid(ipBTaobaoRefund);
            List<Long> refundOrderIds = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(list)) {
                for (OcBReturnOrder ocBReturnOrder : list) {
                    refundOrderIds.add(ocBReturnOrder.getId());
                }
            }
            if (CollectionUtils.isNotEmpty(refundOrderIds)) {
                omsRefundOrderService.refundOrderClose(refundOrderIds, null, null, operateUser);
                if (log.isDebugEnabled()) {
                    log.debug(this.getClass().getName() + " 取消退换货单id {}", JSONObject.toJSONString(refundOrderIds));
                }
                //将明细对象的已申请数量置零
                isCancel = true;
            }
            omsRefundOrderService.closedRefundSlip(ipBTaobaoRefund.getRefundId());
        }
        return isCancel;
    }

    public boolean checkReturnOrder(Long cpShopId) {
        Boolean flag = false;
        StCShopStrategyDO stCShopStrategyDO = stRpcService.selectOcStCShopStrategyByCpCshopId(cpShopId);
        if (stCShopStrategyDO == null || StringUtils.isEmpty(stCShopStrategyDO.getCancelReturnOrder()) || "N".equalsIgnoreCase(stCShopStrategyDO.getCancelReturnOrder())) {
            flag = true;
        }
        return flag;
    }

    /**
     * 调用退货单审核服务
     *
     * @param refundOrderIds
     */
    public void callRefundAudit(List<Long> refundOrderIds, User user) {
        List<OcBReturnOrder> ocBReturnOrders = ocBReturnOrderMapper.selectReturnOrderListByOrderIds(refundOrderIds);
        if (CollectionUtils.isNotEmpty(ocBReturnOrders)) {
            //存在多个退货单时 因为退单审核服务会根据退款单去关联其他退货单 所以审核时只调用一次就好
            ocBReturnOrders = ocBReturnOrders.stream().filter(p -> ReturnStatusEnum.WAIT_AFTERSALE_ENSURE.getVal().equals(p.getReturnStatus())
                    || ReturnStatusEnum.COMPLETION.getVal().equals(p.getReturnStatus())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(ocBReturnOrders)) {
                OcBReturnOrder returnOrder = ocBReturnOrders.get(0);
                returnOrderAuditService.invokeAfterSendAudit(user, true, returnOrder, "退换货单审核");
            }
        }
    }

    public boolean checkIsMilkCardOrderNew(List<OcBOrder> ocBOrderList) {
        boolean isMilkCardOrder = true;
        // 判断业务类型是不是都跟奶卡相关
        for (OcBOrder ocBOrder : ocBOrderList) {
            if (StringUtils.isNotEmpty(ocBOrder.getBusinessTypeCode())) {
                String businessTypeCode = ocBOrder.getBusinessTypeCode();
                OrderBusinessTypeCodeEnum businessTypeCodeEnum = OrderBusinessTypeCodeEnum.getOrderBusinessTypeEnumByCode(businessTypeCode);
                if (!StringUtils.equals(businessTypeCodeEnum.getNaiKaType(), "entity") && !StringUtils.equals(businessTypeCodeEnum.getNaiKaType(), "virtual")) {
                    return false;
                }
            }
        }
        return isMilkCardOrder;
    }

    /**
     * 校验订单是否只有奶卡类商品
     *
     * @param orderItems
     * @return
     */
    public boolean checkIsMilkCardOrder(List<OcBOrderItem> orderItems) {
        boolean isMilkCardOrder = true;
        if (CollectionUtils.isEmpty(orderItems)) {
            return false;
        }
        List<String> milkCardCodes = new ArrayList<>();
        milkCardCodes.add("10800");
        milkCardCodes.add("10801");
        milkCardCodes.add("10802");
        int giftSize = 0;
        for (OcBOrderItem item : orderItems) {
            if (item.getIsGift() != null && item.getIsGift().equals(1)) {
                //赠品
                giftSize++;
                continue;
            }
            //根据条码查询商品信息
            ProductSku productSku = psRpcService.selectProductSku(item.getPsCSkuEcode());
            if (productSku == null) {
                throw new NDSException("查询商品属性信息失败！sku=" + item.getPsCSkuEcode());
            }
            if (productSku.getProAttributeMap() == null) {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("查询商品属性信息为空sku:{};",
                            "Step070OrderGoodsAfterReturn"), item.getPsCSkuEcode());
                }
                return false;
            }
            Map<String, OmsProAttributeInfo> proAttributeMap = productSku.getProAttributeMap();
            OmsProAttributeInfo attributeInfo = proAttributeMap.get("M_DIM2_ID");
            if (attributeInfo == null) {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("查询商品物料信息为空sku:{};",
                            "Step070OrderGoodsAfterReturn"), item.getPsCSkuEcode());
                }
                return false;
            }
            String ecode = attributeInfo.getEcode();
            if (!milkCardCodes.contains(ecode)) {
                isMilkCardOrder = false;
                break;
            }
        }
        // 如果都是正品 返回 false(修复明细都是赠品的时候，没有生成退换货单的问题)
        if (giftSize == orderItems.size()) {
            return false;
        }
        return isMilkCardOrder;
    }

    /**
     * 查询原单对应的退款业务类型
     *
     * @param ocBOrder
     */
    public StCBusinessType queryRefundOrderType(OcBOrder ocBOrder) {
        StCBusinessType resultType = null;
        try {
            Long businessTypeId = ocBOrder.getBusinessTypeId();
            StCBusinessType stCBusinessType = omsBusinessTypeStService.selectStCBusinessTypeById(businessTypeId);
            Long refundTypeId = stCBusinessType.getRefundTypeId();
            resultType = omsBusinessTypeStService.selectStCBusinessTypeById(refundTypeId);
            if (resultType == null) {
                throw new NDSException("查询退款业务类型为空！");
            }
        } catch (Exception e) {
            log.error(LogUtil.format("查询退款业务类型失败={}",
                    "OmsRefundOrderService.queryRefundOrderType"), Throwables.getStackTraceAsString(e));
            throw new NDSException("查询退款业务类型失败！");
        }
        return resultType;
    }

    /**
     * 查询非奶卡类型退款单的业务类型
     *
     * @param ocBOrder
     * @param ocBReturnAfSendRelation
     * @return
     */
    public StCBusinessType queryNotMilkCardType(OcBOrder ocBOrder, OcBReturnAfSendRelation ocBReturnAfSendRelation) {
        StCBusinessType resultType = null;
        try {
            //已发货退款单主表信息
            OcBReturnAfSend ocBReturnAfSend = ocBReturnAfSendRelation.getOcBReturnAfSend();
            Long businessTypeId = ocBOrder.getBusinessTypeId();
            String businessTypeName = ocBOrder.getBusinessTypeName();
            StCBusinessType stCBusinessType = omsBusinessTypeStService.selectStCBusinessTypeById(businessTypeId);

            //退款类型
            Integer billType = ocBReturnAfSend.getBillType();
            Long typeId = null;
            if (TaobaoReturnOrderExt.SendBillType.RETURN_REFUND.getCode().equals(billType)) {
                //退货退款
                if ("电商销售".equals(businessTypeName)) {
                    typeId = stCBusinessType.getRefundTypeId();
                } else {
                    typeId = stCBusinessType.getReturnTypeId();
                }
            } else if (TaobaoReturnOrderExt.SendBillType.REFUND_ONLY.getCode().equals(billType)) {
                //仅退款
                typeId = stCBusinessType.getRefundTypeId();
            }
            resultType = omsBusinessTypeStService.selectStCBusinessTypeById(typeId);
            if (resultType == null) {
                throw new NDSException("查询非奶卡类型退款单的业务类型为空！");
            }
        } catch (Exception e) {
            log.error(LogUtil.format("查询非奶卡类型退款单的业务类型失败={}",
                    "OmsRefundOrderService.queryNotMilkCardType"), Throwables.getStackTraceAsString(e));
            throw new NDSException("查询非奶卡类型退款单的业务类型为空！");
        }
        return resultType;
    }


    /**
     * 查询原单对应的退换业务类型
     *
     * @param ocBOrder
     */
    public StCBusinessType queryReturnOrderType(OcBOrder ocBOrder) {
        StCBusinessType resultType = null;
        try {
            Long businessTypeId = ocBOrder.getBusinessTypeId();
            StCBusinessType stCBusinessType = omsBusinessTypeStService.selectStCBusinessTypeById(businessTypeId);
            Long refundTypeId = stCBusinessType.getReturnTypeId();
            resultType = omsBusinessTypeStService.selectStCBusinessTypeById(refundTypeId);
            if (resultType == null) {
                throw new NDSException("查询退换业务类型为空！");
            }
        } catch (Exception e) {
            log.error(LogUtil.format("查询退换业务类型为空={}",
                    "OmsRefundOrderService.queryReturnOrderType"), Throwables.getStackTraceAsString(e));
            throw new NDSException("查询退换业务类型为空！");
        }
        return resultType;
    }

    /**
     * 发货前退款单生成 - 退单待红冲发票信息
     *
     * @param ocBReturnBfSend
     */
    public ReturnRedOffsetDTO bulidReturnRedOffsetDTO(OcBReturnBfSend ocBReturnBfSend, IpBTaobaoRefund ipBTaobaoRefund) {
        ReturnRedOffsetDTO dto = new ReturnRedOffsetDTO();
        dto.setTid(ocBReturnBfSend.getTid());
        dto.setBillNo(ocBReturnBfSend.getTReturnId());
        dto.setBillType("4");
        dto.setCpCShopId(ocBReturnBfSend.getCpCShopId());
        dto.setCpCShopEcode(ocBReturnBfSend.getCpCShopEcode());
        dto.setCpCShopTitle(ocBReturnBfSend.getCpCShopTitle());
        dto.setCpCPlatformId(PlatFormEnum.TAOBAO.getCode().longValue());
        dto.setCpCPlatformEcode(String.valueOf(PlatFormEnum.TAOBAO.getCode()));
        dto.setCpCPlatformEname(PlatFormEnum.TAOBAO.getName());
        dto.setReturnType("1");
        dto.setChangeStatus("0");
        dto.setChangeStatus("0");
        dto.setReturnCount(String.valueOf(ipBTaobaoRefund.getNum()));
        dto.setRefundMoney(ipBTaobaoRefund.getRefundFee());
        dto.setReturnDate(ipBTaobaoRefund.getCreated());
        dto.setSystemRemark(ipBTaobaoRefund.getSysremark());
        return dto;
    }

    /**
     * 发货后仅退款生成 - 退单待红冲发票信息
     *
     * @param ocBReturnAfSend
     * @return
     */
    public ReturnRedOffsetDTO bulidReturnRedOffsetDTO(OcBReturnAfSend ocBReturnAfSend, List<OcBReturnAfSendItem> ocBReturnAfSendItems) {

        ReturnRedOffsetDTO dto = new ReturnRedOffsetDTO();
        dto.setTid(ocBReturnAfSend.getTid());
        dto.setBillNo(ocBReturnAfSend.getBillNo());
        dto.setBillType("3");
        dto.setCpCShopId(ocBReturnAfSend.getCpCShopId());
        dto.setCpCShopEcode(ocBReturnAfSend.getCpCShopEcode());
        dto.setCpCShopTitle(ocBReturnAfSend.getCpCShopTitle());
        dto.setCpCPlatformId(ocBReturnAfSend.getCpCPlatformId());
        //dto.setCpCPlatformEcode();
        //dto.setCpCPlatformEname();
        dto.setReturnType("1");
        dto.setChangeStatus("0");
        dto.setChangeStatus("0");
        dto.setReturnDate(ocBReturnAfSend.getReturnApplyTime());
        //dto.setSystemRemark(ipBTaobaoRefund.getSysremark());

        if (CollectionUtils.isNotEmpty(ocBReturnAfSendItems)) {
            BigDecimal qtyReturnApply = ocBReturnAfSendItems.stream().filter(x -> x.getQtyReturnApply() != null).map(OcBReturnAfSendItem::getQtyReturnApply).
                    reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal amtReturn = ocBReturnAfSendItems.stream().filter(x -> x.getAmtReturn() != null).map(OcBReturnAfSendItem::getAmtReturn).
                    reduce(BigDecimal.ZERO, BigDecimal::add);
            dto.setReturnCount(String.valueOf(qtyReturnApply));
            dto.setRefundMoney(amtReturn);

            List<ReturnRedOffsetDTO.ReturnOffsetItemDTO> returnOffsetItemDTOList = new ArrayList<>();
            for (OcBReturnAfSendItem sendItem : ocBReturnAfSendItems) {
                ReturnRedOffsetDTO.ReturnOffsetItemDTO itemDTO = new ReturnRedOffsetDTO.ReturnOffsetItemDTO();
                itemDTO.setProductCode(sendItem.getPsCProEcode());
                itemDTO.setProductName(sendItem.getPsCProEname());
                itemDTO.setSkuCode(sendItem.getPsCSkuEcode());
                itemDTO.setSkuName(sendItem.getPsCSkuEname());
                if (sendItem.getQtyReturnApply() != null) {
                    itemDTO.setReturnCount(sendItem.getQtyReturnApply().intValue());
                }
                itemDTO.setRefundAmount(sendItem.getAmtReturn());
                itemDTO.setGiftFlag(sendItem.getGift());
                returnOffsetItemDTOList.add(itemDTO);
            }
            dto.setItemList(returnOffsetItemDTOList);
        }
        return dto;
    }


    /**
     * 发货后退货退款生成 - 退单待红冲发票信息
     *
     * @param returnOrder
     * @param refunds
     * @return
     */
    public ReturnRedOffsetDTO bulidReturnRedOffsetDTO(OcBReturnOrder returnOrder, List<OcBReturnOrderRefund> refunds) {

        ReturnRedOffsetDTO dto = new ReturnRedOffsetDTO();
        dto.setTid(returnOrder.getTid());
        dto.setBillNo(returnOrder.getBillNo());
        dto.setBillType("1");
        dto.setCpCShopId(returnOrder.getCpCShopId());
        dto.setCpCShopEcode(returnOrder.getCpCShopEcode());
        dto.setCpCShopTitle(returnOrder.getCpCShopTitle());
        dto.setCpCPlatformId(returnOrder.getPlatform().longValue());
        //dto.setCpCPlatformEcode();
        //dto.setCpCPlatformEname();
        dto.setReturnType("0");
        dto.setChangeStatus("0");
        dto.setReturnDate(returnOrder.getReturnCreateTime());
        //dto.setSystemRemark();

        if (CollectionUtils.isNotEmpty(refunds)) {
            BigDecimal qty = BigDecimal.ZERO;
            BigDecimal amtRefund = BigDecimal.ZERO;
            String terminationType = returnOrder.getTerminationType();
            if ("1".equals(terminationType)) {
                //全部终止入库
                qty = refunds.stream().filter(x -> x.getQtyRefund() != null).map(OcBReturnOrderRefund::getQtyRefund).
                        reduce(BigDecimal.ZERO, BigDecimal::add);
            } else {
                qty = refunds.stream().filter(x -> x.getQtyIn() != null).map(OcBReturnOrderRefund::getQtyIn).
                        reduce(BigDecimal.ZERO, BigDecimal::add);
            }
            amtRefund = refunds.stream().filter(x -> x.getAmtPtRefund() != null).map(OcBReturnOrderRefund::getAmtPtRefund).
                    reduce(BigDecimal.ZERO, BigDecimal::add);

            dto.setReturnCount(String.valueOf(qty));
            dto.setRefundMoney(amtRefund);

            List<ReturnRedOffsetDTO.ReturnOffsetItemDTO> returnOffsetItemDTOList = new ArrayList<>();
            for (OcBReturnOrderRefund returnOrderRefund : refunds) {
                ReturnRedOffsetDTO.ReturnOffsetItemDTO itemDTO = new ReturnRedOffsetDTO.ReturnOffsetItemDTO();
                itemDTO.setProductCode(returnOrderRefund.getPsCProEcode());
                itemDTO.setProductName(returnOrderRefund.getPsCProEname());
                itemDTO.setSkuCode(returnOrderRefund.getPsCSkuEcode());
                itemDTO.setSkuName(returnOrderRefund.getPsCSkuEname());
                itemDTO.setReturnCount(returnOrderRefund.getQtyIn().intValue());
                itemDTO.setRefundAmount(returnOrderRefund.getAmtRefund());
                //itemDTO.setGiftFlag(sendItem.getGift());
                returnOffsetItemDTOList.add(itemDTO);
            }
            dto.setItemList(returnOffsetItemDTOList);
        }
        return dto;
    }

    /**
     * 是否全部赠品明细
     *
     * @param orderItems
     * @return
     */
    public boolean checkIsGiftOrder(List<OcBOrderItem> orderItems) {
        if (CollectionUtils.isEmpty(orderItems)) {
            return false;
        }
        Integer isGift = 1;
        List<OcBOrderItem> isGiftItemList = orderItems.stream().filter(p -> isGift.equals(p.getIsGift())).collect(Collectors.toList());
        return CollectionUtils.isNotEmpty(isGiftItemList) && isGiftItemList.size() == orderItems.size();
    }

    /**
     * 查询发货后退款单是否存在
     *
     * @param refundId
     * @return
     */
    public boolean isRefundAfExist(String refundId) {
        OcBReturnAfSend ocBReturnAfSend = ocBReturnAfSendMapper.selectOcBReturnAfSendByRefundId(refundId);
        return ocBReturnAfSend == null;
    }

}