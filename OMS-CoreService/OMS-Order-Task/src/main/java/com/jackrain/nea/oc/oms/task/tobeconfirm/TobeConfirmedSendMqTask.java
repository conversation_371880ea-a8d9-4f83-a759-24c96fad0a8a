package com.jackrain.nea.oc.oms.task.tobeconfirm;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.oc.oms.config.ToBeConfirmedOrderMqConfig;
import com.jackrain.nea.oc.oms.constant.Mq5Constants;
import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
import com.jackrain.nea.oc.oms.model.enums.BackflowStatus;
import com.jackrain.nea.oc.oms.model.enums.OperateType;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.services.OcBOrderLinkService;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.services.task.OmsToBeConfirmedTaskService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.resource.BllSystemParameterKeyResources;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BllCommonUtil;
import com.jackrain.nea.util.ListSplitUtil;
import com.jackrain.nea.util.SendMQAsyncUtils;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yomahub.tlog.core.rpc.TLogLabelBean;
import com.yomahub.tlog.core.rpc.TLogRPCHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * 新占单服务定时任务
 * 1）批量更新占单状态为占单中 where 占单状态=0
 * 2) 分页发MQ
 * 3) 全链路日志
 *
 * @author: 孙勇生
 * @since: 2019-03-23
 * create at : 2019-03-23 12:19
 */
@Slf4j
@Component
public class TobeConfirmedSendMqTask extends BaseR3Task implements IR3Task {
    @Autowired
    private OmsOrderService orderService;
    @Autowired
    private ToBeConfirmedOrderMqConfig toBeConfirmedOrderMqConfig;
    @Autowired
    private OcBOrderLinkService ocBOrderLinkService;
    @Autowired
    private SendMQAsyncUtils sendMQAsyncUtils;
    @Autowired
    private OmsToBeConfirmedTaskService toBeConfirmedTaskService;
    @Autowired
    private ThreadPoolTaskExecutor confirmedOrderThreadPoolExecutor;


    @Override
    @XxlJob("TobeConfirmedSendMqTask")
    public RunTaskResult execute(JSONObject params) {
        String threadPoolName = "R3_OMS_BECONFIRMED_TASK_THREAD_POOL_%d";


        RunTaskResult result = new RunTaskResult();
        if (BllCommonUtil.isOpen(null, BllSystemParameterKeyResources.OMS_TRANSFER_AUTO_MQ)) {
            result.setSuccess(false);
            result.setMessage("请检查配置，mq已传任务不再执行！");
            return result;
        }

        try {
            long start = System.currentTimeMillis();
            final String taskTableName = "oc_b_tobeconfirmed_task";
            PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
            Integer pageSize = config.getProperty("lts.TobeConfirmedSendMqTask.range", 5000);
            List<Long> orderIdList = toBeConfirmedTaskService.selectToBeConfirmedTask(pageSize, taskTableName);
            if (CollectionUtils.isEmpty(orderIdList)) {
                result.setSuccess(true);
                result.setMessage("success");
                return result;
            }
            List<List<Long>> lists = ListSplitUtil.averageAssign(orderIdList, 24);
            List<Future<Boolean>> results = new ArrayList<Future<Boolean>>();
            for (List<Long> data : lists) {
                results.add(confirmedOrderThreadPoolExecutor.submit(new CallableTobeConfirmedTaskWithResult(data)));
            }

            //线程执行结果获取
            for (Future<Boolean> futureResult : results) {
                try {
                    log.debug(LogUtil.format("TobeConfirmedSendMqTask------>线程结果:{}", "TobeConfirmedSendMqTask"), futureResult.get().toString());
                } catch (Exception e) {
                    log.error(LogUtil.format("TobeConfirmedSendMqTask多线程获取ExecutionException异常：{}", threadPoolName, "TobeConfirmedSendMqTask"), Throwables.getStackTraceAsString(e));
                }
            }

            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("TobeConfirmedSendMqTask 占单服务定时任务完成 useTime:{}",  "TobeConfirmedSendMqTask", (System.currentTimeMillis() - start)));
            }
            result.setSuccess(true);
        } catch (Exception ex) {
            log.error(LogUtil.format("TobeConfirmedOrderTask.Execute Error：{}", threadPoolName, "TobeConfirmedSendMqTask"), Throwables.getStackTraceAsString(ex));
            result.setSuccess(false);
            result.setMessage(ex.getMessage());
        }
        return result;
    }

    /**
     * 分割list
     *
     * @param dataList 数据集合
     */

    public void splitDataList(List<Long> dataList) {

        //订单自动审核单次最大处理数 默认200单
        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
        int pointsDataLimit = config.getProperty("lts.occpyOrder.push.num", 20);
        log.debug("TobeConfirmedSendMqTask订单占单单次最大发送数--> " + pointsDataLimit);
        //分批次处理
        List<List<Long>> listList = BllCommonUtil.getBasePageList(dataList, pointsDataLimit);
        for (List<Long> orderIdList : listList) {
            if (CollectionUtils.isNotEmpty(orderIdList)) {
                List<OcBOrder> ocBOrders = orderService.selectOrderListByIdsListOccpy7(orderIdList);
                List<Long> ids = ListUtils.emptyIfNull(ocBOrders).stream()
                        .map(OcBOrder::getId).collect(Collectors.toList());
                log.info(LogUtil.format("TobeConfirmedSendMqTask selectOrderListByIdsListOccpy7 orderIdList:{},ids:{}",
                        "TobeConfirmedSendMqTask"), JSONObject.toJSONString(orderIdList), JSONObject.toJSONString(ids));
                //再发Mq
                this.sendMQ(orderIdList, ocBOrders);
                //转单全链路日志
                long startTime = System.currentTimeMillis();
                // @20200804 线程池中不再嵌套创建线程池
                ocBOrderLinkService.addOrderFinkLogs(ocBOrders, BackflowStatus.QIMEN_ERP_TRANSFER.parseValue());
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("占单发MQ全链路日志耗时:{}", "TobeConfirmedSendMqTask"), System.currentTimeMillis() - startTime);
                }
            } else {
            }

        }
    }

    /**
     * 开启线程类
     */
    class CallableTobeConfirmedTaskWithResult implements Callable<Boolean> {
        private final List<Long> data;

        public CallableTobeConfirmedTaskWithResult(List<Long> data) {
            this.data = data;
        }

        @Override
        public Boolean call() throws Exception {
            new TLogRPCHandler().processProviderSide(new TLogLabelBean());
            log.info(LogUtil.format("TobeConfirmedSendMqTask selectToBeConfirmedTask orderIdList:{}",
                    "TobeConfirmedSendMqTask"), data.size());
            if (CollectionUtils.isEmpty(data)) {
                return true;
            }
            splitDataList(data);
            return true;
        }
    }

    /**
     * 发送MQ消息执行审单
     *
     * @param orderIds 订单ID集合
     */
    private void sendMQ(List<Long> orderIds, List<OcBOrder> ocBOrders) {

        String msgKey = "ORDER_TR_" + "_" + BllCommonUtil.getmicTime();
        if (log.isDebugEnabled()) {
            log.debug("msgKey" + msgKey);
        }
        List<OperateOrderMqInfo> mqInfoList = new ArrayList<>();
        for (OcBOrder ocBOrder : ocBOrders) {
            OperateOrderMqInfo orderMqInfo = new OperateOrderMqInfo();
            orderMqInfo.setOperateType(OperateType.TOBE_CONFIRMED);
            orderMqInfo.setOrderId(ocBOrder.getId());
            orderMqInfo.setOrderNo(ocBOrder.getBillNo());
            mqInfoList.add(orderMqInfo);
        }
        String jsonValue = JSONObject.toJSONString(mqInfoList);
        String topicName = Mq5Constants.TOPIC_R3_OC_OMS_CALL_TOBECONFIRMED;
        String tagName = Mq5Constants.TAG_R3_OC_OMS_CALL_TOBECONFIRMED;

        toBeConfirmedTaskService.updateToBeConfirmedTask(orderIds);
        if (log.isDebugEnabled()) {
            log.debug("ToBeConfirmedSendMqTask,topicName={},tagName={}", topicName, tagName);
        }
        try {
            sendMQAsyncUtils.sendDelayMessageToBeConfimed("default", "占单", jsonValue,
                    topicName, tagName, msgKey, 2L, null, orderIds);
        } catch (Exception e){
            //回滚
            toBeConfirmedTaskService.updateToBeConfirmedTask0(orderIds);
        }
    }

}
