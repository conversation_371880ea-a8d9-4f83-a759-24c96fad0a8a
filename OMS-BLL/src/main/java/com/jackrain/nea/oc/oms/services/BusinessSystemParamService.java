package com.jackrain.nea.oc.oms.services;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Splitter;
import com.google.common.base.Throwables;
import com.google.common.collect.Maps;
import com.jackrain.nea.oc.oms.config.OmsSystemConfig;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

/**
 * <AUTHOR>
 * @Description: 业务系统参数获取
 * @date 2021/10/25 14:15
 */
@Slf4j
@Service
public class BusinessSystemParamService {
    /**
     * JitX订单合单最大数量
     */
    private static final String JITX_MERGE_LIMIT_QUANTITY = "business_system:jitx_merge_limit_quantity";

    /**
     * 退货单定时任务传WMS数量
     */
    public static final String RETURN_ORDER_TRANSFER_WMS_NUM = "business_system:return_order_transfer_wms_num";

    /**
     * 退单wms回传中间表拉取数量
     */
    public static final String REFUND_IN_TASK_NUM = "business_system:refund_in_task_num";

    /**
     * 退单推送wms忽略无物流单号
     */
    private static final String RETURN_ORDER_PUSH_WMS_IGNORE_LOGISTICCODE = "business_system:return_order_push_wms_ignore_logisticCode";


    /**
     * jitx订单获取电子面单失败时重试间隔时间
     */
    private static final String JITX_ORDER_GET_LABEL_FAIL_RETRY_DELAY_TIME = "business_system:jitx_order_get_label_fail_retry_delay_time";
    /**
     * jitx订单失败补偿间隔时间
     */
    private static final String JITX_ORDER_VIP_GET_MODIFY_WAREHOUSE_INTERVAL_TIME = "business_system:vip_get_modify_warehouse_Interval_time";
    /**
     * 获取业务系统参数 通用退单是否走换货
     */
    private static final String IP_STANDPLAT_REFUND_ORDER_EXCHANGE = "business_system:ip_standplat_refund_order_exchange";
    /**
     * 获取业务系统参数 是否开启退换货单通知WMS入库控制
     */
    private static final String RETURN_ORDER_TO_WMS_CONTROL = "business_system:return_order_to_wms_control";

    /**
     * 获取业务系统参数 自动寻源停寻时间段(24h制，精确到分, 支持跨天：开始时间晚于等于结束时间)
     */
    private static final String AUTO_OCCUPY_STOP_TIME = "business_system:auto_occupy_stop_time";

    /**
     * 抖音发货重试错误信息
     */
    private static final String DOUYIN_PLATFORM_DELIVERY_FAIL_RETRY_MSG = "business_system:douyin_platform_delivery_fail_retry_msg";

    /**
     * 抖音发货错误信息不重试发货
     */
    private static final String DOUYIN_PLATFORM_DELIVERY_MARK_FAIL_MSG = "business_system:douyin_platform_delivery_mark_fail_msg";

    /**
     * 抖音发货提示直接标记平台发货
     */
    private static final String DOUYIN_PLATFORM_DELIVERY_MARK_SUCCESS_MSG = "business_system:douyin_platform_delivery_mark_success_msg";

    /**
     * 会员识别关键字
     */
    private static final String MEMBER_MATCH_CONTENT = "business_system:member_match_content";

    /**
     * 快递拦截失败重试（失败原因关键字）
     */
    private static final String LOGISTICS_INTERCEPT_FAIL_RETRY_REASON = "business_system:logistics_intercept_fail_retry_reason";

    /**
     * 平台强制转单完成
     */
    private static final String STAND_PLATFORM_ORDER_FORCE_TRANSFER = "business_system:stand_platform_order_force_transfer";

    /**
     * 低温SKU行大于等于2优先寻源时间
     */
    private static final String LOW_TEMPERATURE_OCCUPY_TIME = "business_system:priority_occupy_time";

    /**
     * 低温寻源开关
     */
    private static final String LOW_TEMPERATURE_SWITCH = "business_system:2c_low-temperature_occupy";

    private static final String FIND_SOURCE_SPECIFIED_BUSINESS_TYPE = "business_system:find_source_specified_business_type";
    private static final String FIND_SOURCE_SPECIFIED_M_DIM4_ID = "business_system:find_source_specified_m_dim4_id";
    private static final String FIND_SOURCE_SPECIFIED_BUSINESS_COLLECT_TYPE = "business_system:find_source_specified_business_collect_type";
    private static final String FIND_SOURCE_SPECIFIED_SHOP = "business_system:find_source_specified_shop";

    private static final String TO_B_ORDER_REMARK = "business_system:to_b_order_remark";

    private static final String YTO_CUSTOMER_CODE = "business_system:yto_customer_code";

    /**
     * 衍生品一级分类(用于下单时发送钉钉通知)
     */
    private static final String FIRST_LEVEL_CLASSIFICATION_OF_DERIVATIVES = "business_system:first_level_classification_of_derivatives";
    private static final String FIRST_LEVEL_CLASSIFICATION_OF_DERIVATIVES_MAX_QTY = "business_system:first_level_classification_of_derivatives_max_qty";
    private static final String FIRST_LEVEL_CLASSIFICATION_OF_DERIVATIVES_USER_CODE = "business_system:first_level_classification_of_derivatives_user_code";

    /**
     * 奶粉一级分类(用于下单时发送钉钉通知)
     */
    private static final String FIRST_LEVEL_CLASSIFICATION_OF_MILK_POWDER = "business_system:first_level_classification_of_milk_powder";
    private static final String FIRST_LEVEL_CLASSIFICATION_OF_MILK_POWDER_USER_CODE = "business_system:first_level_classification_of_milk_powder_user_code";

    /**
     * 液奶一级分类(用于下单时发送钉钉通知)
     */
    private static final String FIRST_LEVEL_CLASSIFICATION_OF_LIQUID_MILK = "business_system:first_level_classification_of_liquid_milk";
    private static final String DMS_FREE_ORDER_CARD = "business_system:dms_free_order_card";

    private static final String PRE_OCCUPY_RANGE = "business_system:pre_occupy_range";

    private static final String OMS_STANDPLAT_ORDER_GIFT_SPLIT = "business_system:oms_standplat_order_gift_split";

    /**
     * 贴纸四级类目参数key
     */
    private static final String STICKER_CATEGORY_KEY = "business_system:sticker_category_codes";

    @Autowired
    private OmsSystemConfig omsSystemConfig;

    public String get2CLowTemperatureOccupySwitch() {
        String param = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(LOW_TEMPERATURE_SWITCH);
        if (StringUtils.isEmpty(param)) {
            return "";
        } else {
            return param;
        }
    }

    public String getLowTemperatureOccupyTime() {
        String param = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(LOW_TEMPERATURE_OCCUPY_TIME);
        if (StringUtils.isEmpty(param)) {
            return "";
        } else {
            return param;
        }
    }


    public String getMemberMatchContent() {
        String param = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(MEMBER_MATCH_CONTENT);
        if (StringUtils.isEmpty(param)) {
            return "";
        } else {
            return param;
        }
    }


    public int getJitxMergedOrderLimit() {
        try {
            String limitNum = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(JITX_MERGE_LIMIT_QUANTITY);
            if (StringUtils.isNotEmpty(limitNum)) {
                return Integer.valueOf(limitNum);
            } else {
                return omsSystemConfig.getJitxAutoMergeOrderEachGroupQty();
            }
        } catch (Exception e) {
            log.error(" getJitxMergedOrderLimit from  redis error:{}", Throwables.getStackTraceAsString(e));
            return omsSystemConfig.getJitxAutoMergeOrderEachGroupQty();
        }
    }


    public boolean isCheckLogistiCcode() {
        try {
            String value = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(RETURN_ORDER_PUSH_WMS_IGNORE_LOGISTICCODE);
            return YesNoEnum.ONE.getKey().equals(value);
        } catch (Exception e) {
            log.error(LogUtil.format("getReturnOrderPushWmsIsCheckLogistiCcode.异常: {}"), Throwables.getStackTraceAsString(e));
            return true;
        }
    }

    /**
     * 退货单定时任务传WMS数量
     *
     * @return
     */
    public int getReturnOrderTransferWmsNum() {
        int num = 200;
        try {
            String numStr = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(RETURN_ORDER_TRANSFER_WMS_NUM);
            if (StringUtils.isNotBlank(numStr)) {
                num = Integer.parseInt(numStr);
            }
        } catch (Exception e) {
            log.error("获取退货单定时任务每次传输WMS数量异常", e);
        }
        return num;
    }

    /**
     * 退单wms回传中间表拉取数量
     *
     * @return
     */
    public int getRefundInTaskNum() {
        int num = 200;
        try {
            String numStr = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(REFUND_IN_TASK_NUM);
            if (StringUtils.isNotBlank(numStr)) {
                num = Integer.parseInt(numStr);
            }
        } catch (Exception e) {
            log.error("退单wms回传中间表拉取数量", e);
        }
        return num;
    }

    public boolean standlatExchange() {
        boolean flag;
        String param = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(IP_STANDPLAT_REFUND_ORDER_EXCHANGE);
        if (StringUtils.isEmpty(param)) {
            flag = false;
        } else {
            if (log.isDebugEnabled()) {
                log.debug(" Start standlatExchange param={}",
                        JSONObject.toJSONString(param));
            }
            flag = Boolean.valueOf(param);
        }
        return flag;
    }


    public Integer jitxGetLabelRetryDelayTime() {
        try {
            String value = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(JITX_ORDER_GET_LABEL_FAIL_RETRY_DELAY_TIME);
            if (StringUtils.isEmpty(value)) {
                return 0;
            }
            return Integer.valueOf(value);
        } catch (Exception e) {
            log.error(" jitxGetLabelRetryDelayTime from  redis error:{}", Throwables.getStackTraceAsString(e));
            return 30;
        }
    }

    public Integer getJitxOrderVipGetModifyWarehouseIntervalTime() {
        try {
            String value = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(JITX_ORDER_VIP_GET_MODIFY_WAREHOUSE_INTERVAL_TIME);
            if (StringUtils.isEmpty(value)) {
                return 5;
            }
            return Integer.valueOf(value);
        } catch (Exception e) {
            log.error(" getJitxOrderVipGetModifyWarehouseIntervalTime from  redis error:{}", Throwables.getStackTraceAsString(e));
            return 5;
        }
    }

    public boolean getReturnOrderToWmsControl() {
        String param = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(RETURN_ORDER_TO_WMS_CONTROL);
        if (StringUtils.isEmpty(param)) {
            return false;
        } else {
            return Boolean.valueOf(param);
        }
    }

    public boolean checkAutoOccupyStopTime() {
        try {
            String value = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(AUTO_OCCUPY_STOP_TIME);
            if (StringUtils.isBlank(value)) {
                return false;
            }
            String[] times = value.split("/");
            for (String time : times) {
                String[] t = time.split("-");
                if (t.length != 2) {
                    continue;
                }
                String today = DateUtil.today();
                //yyyy-MM-dd + HH:mm
                Date startTime = DateUtil.parse(today + " " + t[0], "yyyy-MM-dd HH:mm");
                Date endTime = DateUtil.parse(today + " " + t[1], "yyyy-MM-dd HH:mm");
                Date now = new Date();
                //开始时间晚于等于结束时间，视为跨天
                if (startTime.after(endTime) || startTime.equals(endTime)) {
                    if (now.after(startTime) || now.before(endTime)) {
                        return true;
                    }
                } else {
                    if (now.after(startTime) && now.before(endTime)) {
                        return true;
                    }
                }
            }
            return false;
        } catch (Exception e) {
            log.error("checkAutoOccupyStopTime from  redis error:{}", Throwables.getStackTraceAsString(e));
            return false;
        }
    }

    public boolean getDouyinDeliveryFailRetry(String msg) {
        String failMsg = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(DOUYIN_PLATFORM_DELIVERY_FAIL_RETRY_MSG);
        if (StringUtils.isBlank(failMsg)) {
            return false;
        }
        List<String> msgList = Arrays.stream(failMsg.split("\\|")).map(String::trim).filter(s -> StringUtils.isNotBlank(s)).collect(Collectors.toList());
        for (String s : msgList) {
            if (msg.contains(s)) {
                return true;
            }
        }
        return false;
    }

    public boolean getDouyinDeliveryMarkFail(String msg) {
        String failMsg = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(DOUYIN_PLATFORM_DELIVERY_MARK_FAIL_MSG);
        if (StringUtils.isBlank(failMsg)) {
            return false;
        }
        List<String> msgList = Arrays.stream(failMsg.split("\\|")).map(String::trim).filter(s -> StringUtils.isNotBlank(s)).collect(Collectors.toList());
        for (String s : msgList) {
            if (msg.contains(s)) {
                return true;
            }
        }
        return false;
    }

    public boolean getDouyinDeliveryMarkSuccess(String msg) {
        String failMsg = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(DOUYIN_PLATFORM_DELIVERY_MARK_SUCCESS_MSG);
        if (StringUtils.isBlank(failMsg)) {
            return false;
        }
        List<String> msgList = Arrays.stream(failMsg.split("\\|")).map(String::trim).filter(s -> StringUtils.isNotBlank(s)).collect(Collectors.toList());
        for (String s : msgList) {
            if (msg.contains(s)) {
                return true;
            }
        }
        return false;
    }

    public List<String> getLogisticsInterceptFailRetryReason() {
        String failMsg = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(LOGISTICS_INTERCEPT_FAIL_RETRY_REASON);
        if (StringUtils.isBlank(failMsg)) {
            return null;
        }
        return StreamSupport.stream(Splitter.on("【】").split(failMsg).spliterator(), false).collect(Collectors.toList());
    }

    public List<Long> getForceTransferPlatform() {
        List<Long> platformList = new ArrayList<>();
        try {
            String platform = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(STAND_PLATFORM_ORDER_FORCE_TRANSFER);
            if (StringUtils.isBlank(platform)) {
                return null;
            }
            String[] platformArr = platform.split(",");
            for (String platformCode : platformArr) {
                platformList.add(Long.valueOf(platformCode));
            }
        } catch (Exception e) {
            return null;
        }
        return platformList;
    }

    public Map<String, String> getSpecifiedBusinessType() {
        Map<String, String> distByBusinessType = new HashMap();
        try {
            String value =
                    (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(FIND_SOURCE_SPECIFIED_BUSINESS_TYPE);
            if (StringUtils.isEmpty(value)) {
                return distByBusinessType;
            }
            String[] businessTypes = value.split(";");
            for (String businessType : businessTypes) {
                if (StringUtils.isNotEmpty(businessType)) {
                    String[] businessTypeAndDist = businessType.split(":");
                    if (StringUtils.isNotEmpty(businessTypeAndDist[0]) && StringUtils.isNotEmpty(businessTypeAndDist[1])) {
                        distByBusinessType.put(businessTypeAndDist[0], businessTypeAndDist[1]);
                    }
                }
            }
        } catch (Exception e) {
            log.error("BusinessSystemParamService.getSpecifiedBusinessTyp error:{}",
                    Throwables.getStackTraceAsString(e));
            return null;
        }
        return distByBusinessType;
    }

    public Map<String, String> getSpecifiedMDim4() {
        Map<String, String> specifiedMDimMap = new HashMap();
        try {
            String value =
                    (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(FIND_SOURCE_SPECIFIED_M_DIM4_ID);
            if (StringUtils.isEmpty(value)) {
                return specifiedMDimMap;
            }
            String[] mdim4Codes = value.split(";");
            for (String mdim4Code : mdim4Codes) {
                // 根据冒号隔开
                if (StringUtils.isNotEmpty(mdim4Code)) {
                    String[] mdim4CodeAndDist = mdim4Code.split(":");
                    if (StringUtils.isNotEmpty(mdim4CodeAndDist[0]) && StringUtils.isNotEmpty(mdim4CodeAndDist[1])) {
                        specifiedMDimMap.put(mdim4CodeAndDist[0], mdim4CodeAndDist[1]);
                    }
                }
            }
        } catch (Exception e) {
            log.error("BusinessSystemParamService.getSpecifiedMDim4 error:{}",
                    Throwables.getStackTraceAsString(e));
            return null;
        }
        return specifiedMDimMap;
    }

    public Map<String, Map<String, String>> getSpecifiedBusinessCollectType() {
        Map<String, Map<String, String>> specialTypeMap = new HashMap();
        String value =
                (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(FIND_SOURCE_SPECIFIED_BUSINESS_COLLECT_TYPE);
        // value的格式为 RYCK19,Z3:FH374;RYCK03,Z3:FH374 。将值拆分。逗号前面的为key， 逗号后面的为value。然后逗号后面的又根据冒号来生成一个新的map
        if (StringUtils.isNotBlank(value)) {
            String[] businessTypes = value.split(";");
            for (String businessType : businessTypes) {
                if (StringUtils.isNotBlank(businessType)) {
                    String[] businessTypeAndDist = businessType.split(",");
                    if (StringUtils.isNotBlank(businessTypeAndDist[0]) && StringUtils.isNotBlank(businessTypeAndDist[1])) {
                        String[] collectTypeAndDist = businessTypeAndDist[1].split(":");
                        Map<String, String> valueMap = specialTypeMap.get(businessTypeAndDist[0]);
                        if (valueMap == null) {
                            valueMap = new HashMap<>();
                        }
                        valueMap.put(collectTypeAndDist[0], collectTypeAndDist[1]);
                        specialTypeMap.put(businessTypeAndDist[0], valueMap);
                    }
                }
            }
        }
        return specialTypeMap;
    }

    public Map<String, String> getSpecifiedShop() {
        String value = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(FIND_SOURCE_SPECIFIED_SHOP);
        // value的格式为 901:FH374;902:FH374 生成map。冒号前面的是key 冒号后面的是value。两个map之间根据分号隔开
        Map<String, String> shopMap = new HashMap<>();
        if (StringUtils.isBlank(value)) {
            return null;
        }
        String[] shopArr = value.split(";");
        for (String shop : shopArr) {
            if (StringUtils.isNotBlank(shop)) {
                String[] shopAndDist = shop.split(":");
                if (StringUtils.isNotBlank(shopAndDist[0]) && StringUtils.isNotBlank(shopAndDist[1])) {
                    shopMap.put(shopAndDist[0], shopAndDist[1]);
                }
            }
        }
        return shopMap;
    }


    public List<String> getToBOrderRemark() {
        List<String> remarkList = new ArrayList<>();
        try {
            String remarks = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(TO_B_ORDER_REMARK);
            if (StringUtils.isBlank(remarks)) {
                return null;
            }
            String[] remarkArr = remarks.split(";");
            for (String remark : remarkArr) {
                remarkList.add(remark);
            }
        } catch (Exception e) {
            return null;
        }
        return remarkList;
    }

    /**
     * 获取圆通客户编码
     * <p>
     * 客户编码,仓/仓/仓,快递编码(K11111111,8000/9000,YTO;K222222,8011,YTO-YZD)
     *
     * @return key:warehouseCode+logisticeCode;value:customerCode
     */
    public Map<String, String> getWarehouseCustomerCodeMap() {
        String value = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(YTO_CUSTOMER_CODE);
        if (StringUtils.isBlank(value)) {
            return Maps.newHashMap();
        }
        try {
            Map<String, String> map = Maps.newHashMap();
            for (String config : value.split(";")) {
                String[] parts = config.split(",");
                if (parts.length != 3) {
                    continue;
                }

                String customerCode = parts[0];
                String warehouseCodes = parts[1];
                String logisticsCode = parts[2];

                String[] warehouseCodeList = warehouseCodes.split("/");
                if (warehouseCodeList.length == 0) {
                    continue;
                }

                for (String warehouseCode : warehouseCodeList) {
                    map.put(warehouseCode + logisticsCode, customerCode);
                }
            }
            return map;
        } catch (Exception e) {
            log.warn("BusinessSystemParamService.getWarehouseCustomerCodeMap error value:{}", value, e);
            return Maps.newHashMap();
        }
    }

    /**
     * 获取衍生品一级分类、最大数量、钉钉发送用户（用于下单后钉钉通知）
     *
     * @return
     */
    public List<String> getFirstLevelClassificationOfDerivatives() {
        List<String> firstLevelList = new ArrayList<>();
        String redisValue = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(FIRST_LEVEL_CLASSIFICATION_OF_DERIVATIVES);
        try {
            if (StringUtils.isEmpty(redisValue)) {
                return new ArrayList<>();
            } else {
                String[] split = redisValue.split(",");
                firstLevelList = Arrays.asList(split);
            }
        } catch (Exception e) {
            log.error("BusinessSystemParamService.getFirstLevelClassificationOfDerivatives value:{},error:{}", redisValue, e);
        }
        return firstLevelList;
    }

    public BigDecimal getFirstLevelClassificationOfDerivativesMaxQty() {
        BigDecimal maxQty = null;
        String redisValue = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(FIRST_LEVEL_CLASSIFICATION_OF_DERIVATIVES_MAX_QTY);
        try {
            if (StringUtils.isEmpty(redisValue)) {
                return null;
            } else {
                maxQty = new BigDecimal(redisValue);
            }
        } catch (Exception e) {
            log.error("BusinessSystemParamService.getFirstLevelClassificationOfDerivativesMaxQty value:{},error:{}", redisValue, e);
        }
        return maxQty;
    }

    public List<String> getFirstLevelClassificationOfDerivativesUserCodes() {
        List<String> userCodeList = new ArrayList<>();
        String redisValue = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(FIRST_LEVEL_CLASSIFICATION_OF_DERIVATIVES_USER_CODE);
        try {
            if (StringUtils.isEmpty(redisValue)) {
                return new ArrayList<>();
            } else {
                String[] split = redisValue.split(",");
                userCodeList = Arrays.asList(split);
            }
        } catch (Exception e) {
            log.error("BusinessSystemParamService.getFirstLevelClassificationOfDerivativesUserCodes value:{},error:{}", redisValue, e);
        }
        return userCodeList;
    }

    /**
     * 获取奶粉一级分类、钉钉发送用户（用于下单后钉钉通知）
     *
     * @return
     */
    public List<String> getFirstLevelClassificationOfMilkPowder() {
        List<String> firstLevelList = new ArrayList<>();
        String redisValue = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(FIRST_LEVEL_CLASSIFICATION_OF_MILK_POWDER);
        try {
            if (StringUtils.isEmpty(redisValue)) {
                return new ArrayList<>();
            } else {
                String[] split = redisValue.split(",");
                firstLevelList = Arrays.asList(split);
            }
        } catch (Exception e) {
            log.error("BusinessSystemParamService.getFirstLevelClassificationOfMilkPowder value:{},error:{}", redisValue, e);
        }
        return firstLevelList;
    }

    public List<String> getFirstLevelClassificationOfMilkPowderUserCodes() {
        List<String> userCodeList = new ArrayList<>();
        String redisValue = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(FIRST_LEVEL_CLASSIFICATION_OF_MILK_POWDER_USER_CODE);
        try {
            if (StringUtils.isEmpty(redisValue)) {
                return new ArrayList<>();
            } else {
                String[] split = redisValue.split(",");
                userCodeList = Arrays.asList(split);
            }
        } catch (Exception e) {
            log.error("BusinessSystemParamService.getFirstLevelClassificationOfMilkPowderUserCodes value:{},error:{}", redisValue, e);
        }
        return userCodeList;
    }

    /**
     * 获取液奶一级分类（用于下单后钉钉通知）
     *
     * @return
     */
    public List<String> getFirstLevelClassificationOfLiquidMilk() {
        List<String> firstLevelList = new ArrayList<>();
        String redisValue = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(FIRST_LEVEL_CLASSIFICATION_OF_LIQUID_MILK);
        try {
            if (StringUtils.isEmpty(redisValue)) {
                return new ArrayList<>();
            } else {
                String[] split = redisValue.split(",");
                firstLevelList = Arrays.asList(split);
            }
        } catch (Exception e) {
            log.error("BusinessSystemParamService.getFirstLevelClassificationOfLiquidMilk value:{},error:{}", redisValue, e);
        }
        return firstLevelList;
    }

    public List<String> getDmsFreeOrder() {
        List<String> dmsFreeOrder = new ArrayList<>();
        String redisValue = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(DMS_FREE_ORDER_CARD);
        try {
            if (StringUtils.isEmpty(redisValue)) {
                return new ArrayList<>();
            } else {
                String[] split = redisValue.split(",");
                dmsFreeOrder = Arrays.asList(split);
            }
        } catch (Exception e) {
            log.error("BusinessSystemParamService.getDmsFreeOrder value:{},error:{}", redisValue, e);
        }
        return dmsFreeOrder;
    }


    public String getPreOccupyRange() {
        String value = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(PRE_OCCUPY_RANGE);
        if (StringUtils.isBlank(value)) {
            return null;
        }
        return value;
    }


    public List<String> getOmsStandplatOrderGiftSplit() {
        List<String> list = new ArrayList<>();
        String value = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(OMS_STANDPLAT_ORDER_GIFT_SPLIT);
        if (StringUtils.isBlank(value)) {
            return list;
        }
        String[] split = value.split(",");
        list = Arrays.asList(split);
        return list;
    }

    /**
     * 获取贴纸四级类目编码
     *
     * @return 贴纸四级类目编码列表
     */
    public List<String> getStickerCategoryCodes() {
        List<String> stickerCategoryList = new ArrayList<>();
        String stickerCategoryCodes = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(STICKER_CATEGORY_KEY);
        if (StringUtils.isNotEmpty(stickerCategoryCodes)) {
            stickerCategoryList = Arrays.asList(stickerCategoryCodes.split(","));
        }
        return stickerCategoryList;
    }
}
