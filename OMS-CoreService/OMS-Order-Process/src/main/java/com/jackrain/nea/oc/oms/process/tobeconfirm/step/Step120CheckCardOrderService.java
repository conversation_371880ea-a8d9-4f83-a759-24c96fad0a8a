package com.jackrain.nea.oc.oms.process.tobeconfirm.step;

import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.OmsSpiltRuleEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderParam;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.SpiltOrderParam;
import com.jackrain.nea.oc.oms.model.resources.OrderOccupyStatus;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.services.OmsAutoHoldNewService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.oc.oms.util.SplitMessageUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Set;

/**
 * @Author: 黄世新
 * @Date: 2022/8/12 上午10:11
 * @Version 1.0
 */
@Step(order = 120, description = "卡单策略")
@Slf4j
@Component
public class Step120CheckCardOrderService extends BaseTobeConfirmedProcessStep implements IOmsOrderProcessStep<OcBOrderRelation> {

    @Autowired
    private OmsAutoHoldNewService omsAutoHoldNewService;

    @Override
    public ProcessStepResult<OcBOrderRelation> startProcess(OcBOrderRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {


        OcBOrder ocBOrder = orderInfo.getOrderInfo();
        try {
            OcBOrderParam param = new OcBOrderParam();
            param.setOcBOrder(orderInfo.getOrderInfo());
            param.setOrderItemList(orderInfo.getOrderItemList());
            Map<Set<Long>, SpiltOrderParam> setSpiltOrderParamMap = omsAutoHoldNewService.autoCardOrderService(orderInfo, operateUser, true);
            if (setSpiltOrderParamMap != null && !setSpiltOrderParamMap.isEmpty()){
                Map<Integer, Map<Set<Long>, SpiltOrderParam>> spiltRule = orderInfo.getSpiltRule();
                spiltRule.put(OmsSpiltRuleEnum.CARD.getCode(), setSpiltOrderParamMap);
            }
            return new ProcessStepResult<>(StepStatus.SUCCESS);
        } catch (Exception e) {
            log.error(LogUtil.format("执行卡单策略失败:{}", ocBOrder.getId(), "执行卡单策略失败"), Throwables.getStackTraceAsString(e));
            OcBOrder errorOrderInfo = new OcBOrder();
            errorOrderInfo.setId(orderInfo.getOrderId());
            errorOrderInfo.setSysremark(SplitMessageUtil.splitMesssage("执行卡单策略失败"));
            errorOrderInfo.setOccupyStatus(OrderOccupyStatus.STATUS_50);
            omsOrderService.updateOrderInfo(errorOrderInfo);
            omsToBeConfirmedTaskService.updateToBeConfirmedTaskByOrderId(orderInfo.getOrderId());
            return new ProcessStepResult<>(StepStatus.FAILED, "执行卡单策略失败");
        }
    }
}
