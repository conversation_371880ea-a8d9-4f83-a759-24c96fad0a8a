package com.jackrain.nea.oc.oms.task.makeup;


import com.alibaba.fastjson.JSONObject;
import com.burgeon.mq.core.DefaultProducerSend;
import com.burgeon.mq.model.MqSendResult;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.oc.oms.config.TransferOrderMqConfig;
import com.jackrain.nea.oc.oms.constant.MqConstants;
import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.OperateType;
import com.jackrain.nea.oc.oms.model.enums.OrderType;
import com.jackrain.nea.oc.oms.services.task.CommonRefundMakeUpService;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Callable;

/**
 * <AUTHOR>
 * @date 2020/11/23 9:49
 * @desc
 */
@Slf4j
public class RefundOrderTransferToMqMakeCallable implements Callable<Integer> {

    private final String taskName;
    private final String nodeName;
    private final String tableName;
    private final String returnField;
    private final Integer eachSize;
    private final Integer isTrans;
    private final Integer count;
    private final CommonRefundMakeUpService refundService;
    private final TransferOrderMqConfig transferOrderMqConfig;
//    private final R3MqSendHelper r3MqSendHelper;

    private DefaultProducerSend defaultProducerSend;
    private final ChannelType channelType;
    private final Integer minutes;

    public RefundOrderTransferToMqMakeCallable(String taskName, String nodeName, String tableName, String returnField, Integer eachSize,
                                               Integer isTrans, Integer count, ChannelType channelType, Integer minutes) {
        this.taskName = taskName;
        this.nodeName = nodeName;
        this.tableName = tableName;
        this.returnField = returnField;
        this.eachSize = eachSize;
        this.isTrans = isTrans;
        this.count = count;
        this.channelType = channelType;
        this.minutes = minutes;
        this.transferOrderMqConfig = ApplicationContextHandle.getBean(TransferOrderMqConfig.class);
        this.refundService = ApplicationContextHandle.getBean(CommonRefundMakeUpService.class);
        this.defaultProducerSend = ApplicationContextHandle.getBean(DefaultProducerSend.class);
    }

    @Override
    public Integer call() {
        String threadName = Thread.currentThread().getName();
        boolean hasNext = true;
        int executeCnt = 0;
        while (hasNext) {
            List<String> refundIdList = refundService.selectDynamicRefundNo(nodeName, tableName, returnField, eachSize
                    , isTrans, count,minutes);
            if (CollectionUtils.isEmpty(refundIdList)) {
                log.warn(LogUtil.format("未查到通用退单补偿数据: {}"), threadName);
                return executeCnt;
            }

            // 如果size小于每次查询数则不存在需要查询的数据
            if (refundIdList.size() < eachSize) {
                hasNext = false;
            }

            List<OperateOrderMqInfo> mqInfoList = new ArrayList<>();
            for (String orderNo : refundIdList) {
                OperateOrderMqInfo orderMqInfo = new OperateOrderMqInfo();
                orderMqInfo.setOperateType(OperateType.TRANSFER_ORDER);
                orderMqInfo.setChannelType(channelType);
                orderMqInfo.setOrderType(OrderType.REFUND);
                orderMqInfo.setOrderNo(orderNo);
                mqInfoList.add(orderMqInfo);
            }
            String jsonValue = JSONObject.toJSONString(mqInfoList);
            try {
                PropertiesConf propertiesConf = ApplicationContextHandle.getBean(PropertiesConf.class);
//                String refundTopic = propertiesConf.getProperty("r3.oc.oms.refundTransfer.mq.topic", transferOrderMqConfig.getSendTransferMqTopic());
                String refundTopic = MqConstants.TOPIC_R3_OC_OMS_CALL_TRANSFER;
//                String refundTag = propertiesConf.getProperty("r3.oc.oms.refundTransfer.mq.tag", transferOrderMqConfig.getSendTransferMqTag());
                String refundTag = MqConstants.TAG_R3_OC_OMS_CALL_TRANSFER;
//                String message = r3MqSendHelper.sendMessage(jsonValue, refundTopic, refundTag);
                MqSendResult result = defaultProducerSend.sendTopic(refundTopic, refundTag, jsonValue, null);
                log.info(LogUtil.format("退单补偿发送MQ结果: {}"), result.getMessageId());
            } catch (Exception e) {
                log.error(LogUtil.format("京东退单补偿发送MQ异常: {}"), Throwables.getStackTraceAsString(e));
                return executeCnt;
            }
            log.debug(LogUtil.format("Execute.count: {}"), refundIdList.size());
            executeCnt += refundIdList.size();
        }
        return executeCnt;
    }
}
