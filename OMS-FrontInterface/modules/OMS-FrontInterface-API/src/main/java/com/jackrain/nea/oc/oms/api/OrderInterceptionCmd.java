package com.jackrain.nea.oc.oms.api;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import org.springframework.stereotype.Component;


/**
 * @author: 夏继超
 * @since: 2019/3/11
 * create at : 2019/3/11 10:59
 */
@Component
public interface OrderInterceptionCmd {
    /**
     * 订单挂起
     *
     * @param object    订单的id
     * @param loginUser 登录的用户
     * @return 返回ValueHolder
     */
    ValueHolder orderInterception(JSONObject object, User loginUser) throws NDSException;

    /**
     * 取消订单挂起
     *
     * @param object    传入的参数
     * @param loginUser 当前登录的用户
     * @return
     */
    ValueHolder cancelInterception(JSONObject object, User loginUser) throws NDSException;

    /**
     * 配送拦截 按钮
     *
     * @param obj  传入的参数
     * @param user 当前用户
     * @return
     */
    ValueHolderV14 distributionInterception(JSONObject obj, User user);
}
