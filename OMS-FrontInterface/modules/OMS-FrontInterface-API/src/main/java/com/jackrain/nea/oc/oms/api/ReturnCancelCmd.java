package com.jackrain.nea.oc.oms.api;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

/**
 * 退换货作废按钮
 *
 * @author: 夏继超
 * @since: 2019/3/25
 * create at : 2019/3/25 16:51
 */
public interface ReturnCancelCmd {
    /**
     * 退换货作废服务
     *
     * @param jsonObject 传入的参数
     * @param user       当前用户
     * @return
     */
    ValueHolderV14 returnCancel(JSONObject jsonObject, User user);

    /* *//**
     * 退换货作废按钮
     * @param jsonObject 传入的参数
     * @param user  当前的用户
     * @return
     *//*
    ValueHolderV14 invalidButton(JSONObject jsonObject, User user);*/
}
