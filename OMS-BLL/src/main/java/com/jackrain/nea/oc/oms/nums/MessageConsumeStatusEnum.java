package com.jackrain.nea.oc.oms.nums;

import lombok.Getter;

import java.util.Objects;

/**
 * Description： 消息消费状态枚举
 * Author: RESET
 * Date: Created in 2020/6/15 21:39
 * Modified By:
 */
public enum MessageConsumeStatusEnum {

    // 匹配策略类型
    INIT(0, "init", "初始未处理"),
    SUCCESS(1, "success", "处理成功"),
    ERROR(2, "error", "处理失败"),
    RETRY_SUCCESS(3, "retrySuccess", "重试成功"),
    RETRY_ERROR(4, "retryError", "重试失败"),
    DISCARD(5, "discard", "废弃"),
    ;

    @Getter
    private Integer value;
    @Getter
    private String code;
    @Getter
    private String description;

    MessageConsumeStatusEnum(Integer value, String code, String description) {
        this.value = value;
        this.code = code;
        this.description = description;
    }

    @Override
    public String toString() {
        return String.valueOf(value);
    }

    public static MessageConsumeStatusEnum fromValue(Integer v) {
        for (MessageConsumeStatusEnum c : MessageConsumeStatusEnum.values()) {
            if (Objects.equals(v, c.value)) {
                return c;
            }
        }
        throw new IllegalArgumentException(String.valueOf(v));
    }

}
