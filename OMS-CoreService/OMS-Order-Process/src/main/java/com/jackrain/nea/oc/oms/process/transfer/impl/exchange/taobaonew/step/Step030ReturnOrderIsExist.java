package com.jackrain.nea.oc.oms.process.transfer.impl.exchange.taobaonew.step;

import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.ExchangeOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderExchangeRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsTaobaoExchangeRelation;
import com.jackrain.nea.oc.oms.model.table.*;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoExchangeOrderExt;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2020/12/1 10:03 上午
 * @Version 1.0
 */
@Step(order = 30, description = "判断是否生成退换货单和换货订单")
@Slf4j
@Component
public class Step030ReturnOrderIsExist extends BaseTaobaoExchangeProcessStep
        implements IOmsOrderProcessStep<OmsTaobaoExchangeRelation> {

    @Override
    public ProcessStepResult<OmsTaobaoExchangeRelation> startProcess(OmsTaobaoExchangeRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        IpBTaobaoExchange ipBTaobaoExchange = orderInfo.getIpBTaobaoExchange();
        try {
            //换货数量
            Long qty = ipBTaobaoExchange.getQty();
            Long disputeId = ipBTaobaoExchange.getDisputeId();
            //换货的sku
            String sku = orderInfo.getProductSku().getSkuEcode();
            ExchangeOrderRelation relation = omsTaobaoExchangeService.selectReturnOrderList(ipBTaobaoExchange);
            if (relation != null) {
                //换货状态
                Integer flag = relation.getFlag();
                String status = ipBTaobaoExchange.getStatus();
                //判断是否需要
                List<OcBReturnOrder> ocBReturnOrders = relation.getOcBReturnOrders();
                ocBReturnOrders = ocBReturnOrders.stream().filter(p -> TaobaoReturnOrderExt.BillType.EXCHANGE.getCode().equals(p.getBillType())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(ocBReturnOrders)) {
                    if (TaobaoExchangeOrderExt.ExchangeOrderCentreStatus.EXCHANGE_CLOSE.getName().equals(status)) {
                        String message = "换货关闭,不存在退换货类型的退换货单,直接标记为已转换";
                        ipTaobaoExchangeService.updateExchangeRemarkAndIsTrans(TransferOrderStatus.TRANSFERRED.toInteger(), message,
                                ipBTaobaoExchange);
                        return new ProcessStepResult<>(StepStatus.FINISHED, message);
                    }
                    String message = "存在退货类型的退换货单,不自动转换,转换失败";
                    ipTaobaoExchangeService.updateExchangeRemarkAndIsTrans(TransferOrderStatus.TRANSFERFAIL.toInteger(), message,
                            ipBTaobaoExchange);
                    return new ProcessStepResult<>(StepStatus.FAILED, message);
                }
                //存在退换货类型的退货单 则比对换货明细的sku以及换货数量是否一致
                for (OcBReturnOrder ocBReturnOrder : ocBReturnOrders) {
                    List<OcBReturnOrderExchange> ocBReturnOrderExchanges = omsTaobaoExchangeService.selectReturnOrderExchangeList(ocBReturnOrder.getId());
                    if (flag == 1 && ocBReturnOrder.getTbDisputeId().equals(disputeId)) {
                        orderInfo.setOcBReturnOrder(ocBReturnOrder);
                        orderInfo.setOcBReturnOrderExchanges(ocBReturnOrderExchanges);
                        break;
                    }
                    if (CollectionUtils.isNotEmpty(ocBReturnOrderExchanges) && ocBReturnOrderExchanges.size() == 1) {
                        OcBReturnOrderExchange ocBReturnOrderExchange = ocBReturnOrderExchanges.get(0);
                        //换货数量
                        BigDecimal qtyExchange = ocBReturnOrderExchange.getQtyExchange();
                        //sku信息
                        String psCSkuEcode = ocBReturnOrderExchange.getPsCSkuEcode();
                        if (qtyExchange.compareTo(new BigDecimal(qty)) == 0 && sku.equals(psCSkuEcode)) {
                            orderInfo.setOcBReturnOrder(ocBReturnOrder);
                            orderInfo.setOcBReturnOrderExchanges(ocBReturnOrderExchanges);
                            break;
                        }
                    }
                }
                if (flag == 2 && orderInfo.getOcBReturnOrder() == null) {
                    //通过oid存在退换货单 但是数量或者sku不一致
                    if (TaobaoExchangeOrderExt.ExchangeOrderCentreStatus.EXCHANGE_CLOSE.getName().equals(status)) {
                        String message = "换货关闭,存在手动新增退换货类型的退换货单,标记为已转换";
                        ipTaobaoExchangeService.updateExchangeRemarkAndIsTrans(TransferOrderStatus.TRANSFERRED.toInteger(), message,
                                ipBTaobaoExchange);
                        return new ProcessStepResult<>(StepStatus.FINISHED, message);
                    }
                    String message = "存在退换货单,但数量或者sku与平台申请不一致,转换失败";
                    ipTaobaoExchangeService.updateExchangeRemarkAndIsTrans(TransferOrderStatus.TRANSFERFAIL.toInteger(), message,
                            ipBTaobaoExchange);
                    return new ProcessStepResult<>(StepStatus.FAILED, message);
                }

                //找到对应实际的换货订单
                List<OmsOrderExchangeRelation> exchangeOrderNew = new ArrayList<>();
                List<OmsOrderExchangeRelation> exchangeOrder = orderInfo.getExchangeOrder();
                if (CollectionUtils.isNotEmpty(exchangeOrder)) {
                    Long id = orderInfo.getOcBReturnOrder().getId();
                    for (OmsOrderExchangeRelation exchangeRelation : exchangeOrder) {
                        OmsOrderExchangeRelation orderExchangeRelation = new OmsOrderExchangeRelation();
                        OcBOrder ocBOrder = exchangeRelation.getOcBOrder();
                        List<OcBOrderItem> ocBOrderItems = exchangeRelation.getOcBOrderItems();
                        ocBOrderItems = ocBOrderItems.stream().filter(p -> p.getReturnOrderId() != null
                                && p.getReturnOrderId().equals(id)).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(ocBOrderItems)) {
                            orderExchangeRelation.setOcBOrder(ocBOrder);
                            orderExchangeRelation.setOcBOrderItems(ocBOrderItems);
                            exchangeOrderNew.add(orderExchangeRelation);
                        }
                    }
                } else {
                    List<Long> ids = ES4Order.getIdsByOrigReturnOrderId(orderInfo.getOcBReturnOrder().getId());
                    if (CollectionUtils.isNotEmpty(ids)){
                        List<OcBOrder> ocBOrders = ocBOrderMapper.selectByIdsList(ids);
                        if (CollectionUtils.isNotEmpty(ocBOrders)) {
                            ocBOrders= ocBOrders.stream().filter(p -> !(OmsOrderStatus.SYS_VOID.toInteger().equals(p.getOrderStatus())
                                    || OmsOrderStatus.CANCELLED.toInteger().equals(p.getOrderStatus()))).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(ocBOrders)) {
                                for (OcBOrder ocBOrder : ocBOrders) {
                                    List<OcBOrderItem> ocBOrderItems = ocBOrderItemMapper.selectOrderItemListOccupy(ocBOrder.getId());
                                    OmsOrderExchangeRelation orderExchangeRelation = new OmsOrderExchangeRelation();
                                    orderExchangeRelation.setOcBOrder(ocBOrder);
                                    orderExchangeRelation.setOcBOrderItems(ocBOrderItems);
                                    exchangeOrderNew.add(orderExchangeRelation);
                                }
                            }
                        }
                    }
                }
                //如果未匹配到对应的实际换货订单  则将改对象置空
                orderInfo.setExchangeOrder(exchangeOrderNew);
            }
            return new ProcessStepResult<>(StepStatus.SUCCESS);
        } catch (Exception e) {
            ipTaobaoExchangeService.updateExchangeIsTransError(ipBTaobaoExchange, e.getMessage());
            log.error(LogUtil.format("查找原单以及退换货单失败:{}", "查找原单以及退换货单失败"), Throwables.getStackTraceAsString(e));
            String errorMessage = "退换货转换异常!" + e.getMessage();
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
    }
}
