package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.StCExpiryDateItem;
import com.jackrain.nea.oc.oms.model.table.StCExpiryDateLabel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface StCExpiryDateItemMapper extends ExtentionMapper<StCExpiryDateItem> {

    @Select("SELECT COUNT(1) FROM st_c_expiry_date_item WHERE appoint_dimension =#{appointDimension} AND appoint_content = #{appointContent} AND ST_C_EXPIRY_DATE_ID = #{mainId}")
    int selectStCExpiryDateItem(@Param("appointDimension") Integer appointDimension,@Param("appointContent") String appointContent, @Param("mainId")Long mainId);


    @Select("SELECT COUNT(1) FROM st_c_expiry_date_item WHERE ST_C_EXPIRY_DATE_ID = #{mainId}")
    int selectStCExpiryDateItemByMainId(@Param("mainId") Long mainId);

    @Select("SELECT * FROM st_c_expiry_date_item WHERE ST_C_EXPIRY_DATE_ID = #{mainId}")
    List<StCExpiryDateItem> selectStCExpiryDateItemListByMainId(@Param("mainId") Long mainId);

    @Select("<script> "
            + "SELECT * FROM st_c_expiry_date_item WHERE ST_C_EXPIRY_DATE_ID "
            + "in <foreach item='item' index='index' collection='mainIds' open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<StCExpiryDateItem> selectStCExpiryDateItemByMainIds(@Param("mainIds") List<Long> mainIds);

    /**
     * 查询已审核或者未审核的公共的商品效期明细信息
     *
     * @return
     */
    @Select("select item.id             itemId, " +
            "       main.id             mainId, " +
            "       item.appoint_type   appointType, " +
            "       item.start_date_day startDateDay, " +
            "       item.end_date_day   endDateDay, " +
            "       item.order_label    orderLabel " +
            "from st_c_expiry_date_item item " +
            "         left join st_c_expiry_date main on item.st_c_expiry_date_id = main.id " +
            "where (main.submit_status = 2 or (main.expiry_type = 1 and submit_status = 1)) " +
            "  and main.expiry_type != 3 " +
            "  and main.isactive = 'Y' " +
            "  and item.isactive = 'Y'")
    List<StCExpiryDateLabel> selectExpiryDateLabel();

}