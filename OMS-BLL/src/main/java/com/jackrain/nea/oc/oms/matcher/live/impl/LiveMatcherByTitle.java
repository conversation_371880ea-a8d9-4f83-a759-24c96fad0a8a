package com.jackrain.nea.oc.oms.matcher.live.impl;

import com.jackrain.nea.oc.oms.matcher.live.ILiveMatcher;
import com.jackrain.nea.oc.oms.matcher.live.LiveStrategyTypeEnum;
import com.jackrain.nea.oc.oms.matcher.vo.ParamInputVO;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * Description： 按标题解析
 * Author: RESET
 * Date: Created in 2020/6/15 21:37
 * Modified By:
 */
@Component
public class LiveMatcherByTitle implements ILiveMatcher {

    /**
     * 解析匹配
     *
     * @param inputVO
     */
    @Override
    public boolean doMatch(ParamInputVO inputVO) {
        String ruleContext = inputVO.getRuleContext();
        String targetText = inputVO.getOriginalTitle();

        if (Objects.nonNull(ruleContext) && Objects.nonNull(targetText)) {
            return targetText.indexOf(ruleContext) >= 0;
        }

        return false;
    }

    /**
     * 策略类型
     *
     * @return
     */
    @Override
    public Integer getLiveStrategyType() {
        return LiveStrategyTypeEnum.TITLE.getValue();
    }

}
