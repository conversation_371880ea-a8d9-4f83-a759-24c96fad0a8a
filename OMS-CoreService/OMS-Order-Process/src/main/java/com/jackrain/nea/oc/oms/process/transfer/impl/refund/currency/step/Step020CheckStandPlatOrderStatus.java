package com.jackrain.nea.oc.oms.process.transfer.impl.refund.currency.step;

import com.alibaba.fastjson.JSON;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.TransNodeTipEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsStandPlatRefundRelation;
import com.jackrain.nea.oc.oms.model.resources.SysNotesConstant;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatOrder;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.refund.util.TransRefundNodeTipUtil;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2020/2/27 3:12 下午
 * @Version 1.0
 */
@Step(order = 20, description = "判断原单的状态")
@Slf4j
@Component
public class Step020CheckStandPlatOrderStatus extends BaseStanPlatRefundProcessStep
        implements IOmsOrderProcessStep<OmsStandPlatRefundRelation> {

    @Override
    public ProcessStepResult<OmsStandPlatRefundRelation> startProcess(OmsStandPlatRefundRelation orderInfo,
                                                                      ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        IpBStandplatRefund ipBStandplatRefund = orderInfo.getIpBStandplatRefund();
        //获取订单信息
        List<OmsOrderRelation> omsOrderRelation = orderInfo.getOmsOrderRelation();
        String message = "";
        if (log.isDebugEnabled()) {
            log.debug("{} check standard refund order to success with process 020 step. into order information params:{} ", this.getClass().getName(), JSON.toJSONString(orderInfo));
        }
        /**
         *  针对已经退款完成的订单，在转换中，如果零售发货单不存在且通用订单接口已存在并是已转换状态的，直接将退单接口中间表标记为已转换
         */
        Integer returnStatus = ipBStandplatRefund.getReturnStatus();
        String status = TaobaoReturnOrderExt.RefundStandPlatStatus.getValueByKey(returnStatus);
        if ( TaobaoReturnOrderExt.RefundStatus.SUCCESS.getCode().equals(status)){
            //判断零售发货单是否存在
            String orderNo = ipBStandplatRefund.getOrderNo();
            List<OcBOrder> findOrderInfoList = omsOrderService.selectOmsOrderInfo(orderNo);
            if (CollectionUtils.isEmpty(findOrderInfoList)) {
                //判断通用订单接口是否已存在切状态为已转换
                IpBStandplatOrder ipBStandplatOrder = ipStandplatOrderMapper.selectStandplatOrderByTid(orderNo);
                if (ipBStandplatOrder != null &&  TransferOrderStatus.TRANSFERRED.toInteger() == ipBStandplatOrder.getIstrans() ){
                    String remarks = "退款成功，没有零售发货单且通用订单已转换！";
                    ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                            remarks, ipBStandplatRefund);
                    return new ProcessStepResult<>(StepStatus.FAILED, remarks);
                }

            }
        }
        if (CollectionUtils.isEmpty(omsOrderRelation)) {
            // 原单信息不存在超过三天
            if (checkReturnOrderData(ipBStandplatRefund)) {
                message = SysNotesConstant.SYS_REMARK2;
                TransRefundNodeTipUtil.standardTransTipCAS(ipBStandplatRefund, TransNodeTipEnum.ORDER_NOT_FOUND);
                ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                        message, ipBStandplatRefund);
            } else {
                message = SysNotesConstant.SYS_REMARK1;
                TransRefundNodeTipUtil.standardTransTipCAS(ipBStandplatRefund, TransNodeTipEnum.ORDER_NOT_FOUND);
                ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(),
                        message, ipBStandplatRefund);
            }
            return new ProcessStepResult<>(StepStatus.FAILED, message + "转换失败");
        } else {
            //判断是否存在有效的原始订单
            if (checkOrderEffective(omsOrderRelation)) {
                message = SysNotesConstant.SYS_REMARK4;
                ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                        message, ipBStandplatRefund);
                return new ProcessStepResult<>(StepStatus.FAILED, message + "转换失败");
            }
        }
        return new ProcessStepResult<>(StepStatus.SUCCESS, "判断原单状态成功,进入下一阶段");
    }


    /**
     * 检验原单不存在的时间
     *
     * @return
     */
    private boolean checkReturnOrderData(IpBStandplatRefund ipBStandplatRefund) {
        Date date = new Date();
        //判断退单时间是否超过三天
        Date created = ipBStandplatRefund.getCreationdate();
        Long threeDays = 24 * 60 * 60 * 1000L + created.getTime();
        return threeDays < date.getTime();
    }

    /**
     * 判断订单是否有效
     *
     * @param omsOrderRelation
     * @return
     */
    /**
     * 判断订单是否有效
     *
     * @param omsOrderRelation
     * @return
     */
    private boolean checkOrderEffective(List<OmsOrderRelation> omsOrderRelation) {
        for (int i = 0; i < omsOrderRelation.size(); i++) {
            OmsOrderRelation omsOrderRelation1 = omsOrderRelation.get(i);
            OcBOrder ocBOrder = omsOrderRelation1.getOcBOrder();
            Integer orderStatus = ocBOrder.getOrderStatus();
            if (OmsOrderStatus.CANCELLED.toInteger().equals(orderStatus)
                    || OmsOrderStatus.SYS_VOID.toInteger().equals(orderStatus)) {
                String suffixInfo = ocBOrder.getSuffixInfo();
                if (!"REFUND-VOID".equals(suffixInfo)) {
                    omsOrderRelation.remove(omsOrderRelation1);
                    i--;
                }
            }
        }
        return CollectionUtils.isEmpty(omsOrderRelation);
    }
}
