package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.jackrain.nea.OMSFrontInterfaceMainApplication;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.model.enums.OcOrderTagEum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OmsWmsCancelStatus;
import com.jackrain.nea.oc.oms.services.OrderOutService;
import com.jackrain.nea.web.query.QueryUtils;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = OMSFrontInterfaceMainApplication.class)
@Slf4j
public class OrderOutImplTest {
    @Autowired
    private OrderOutService orderOutService;


    /**
     * 框架传入的列解析成es查询需要的where条件
     *
     * @param fixedcolumns
     * @return
     */
    public JSONObject getWhereKey(JSONObject fixedcolumns) {

        JSONObject whereKey = new JSONObject();
        //whereKey.put("IS_INTERECEPT", 0); 去除挂起校验 已经确认去除20190807
        if (fixedcolumns.containsKey("ORDER_STATUS")) {
            //订单状态
            JSONArray orderStatusList = fixedcolumns.getJSONArray("ORDER_STATUS");
            JSONArray jsonArray = new JSONArray();
            if (CollectionUtils.isNotEmpty(orderStatusList)) {
                for (Object orderStatus : orderStatusList) {
                    jsonArray.add(orderStatus.toString().replaceAll("=", ""));
                }
                whereKey.put("ORDER_STATUS", jsonArray);
            }
        } else {
            JSONArray jsonArray = new JSONArray();
            jsonArray.add(OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger());//仓库发货
            jsonArray.add(OmsOrderStatus.PLATFORM_DELIVERY.toInteger());//平台发货
            whereKey.put("ORDER_STATUS", jsonArray);
        }
        if (fixedcolumns.containsKey("WMS_CANCEL_STATUS")) {
            JSONArray wmsCancleStatus = fixedcolumns.getJSONArray("WMS_CANCEL_STATUS");
            JSONArray jsonArray = new JSONArray();
            if (CollectionUtils.isNotEmpty(wmsCancleStatus)) {
                for (Object wmsStatus : wmsCancleStatus) {
                    jsonArray.add(wmsStatus.toString().replaceAll("=", ""));
                }
                whereKey.put("WMS_CANCEL_STATUS", jsonArray);
            }
        } else {
            JSONArray jsonArray = new JSONArray();
            jsonArray.add(OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_YES.toInteger());
            jsonArray.add(OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_NO.toInteger());
            jsonArray.add(OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_FAIL.toInteger());
            whereKey.put("WMS_CANCEL_STATUS", jsonArray);
        }
        if (fixedcolumns.containsKey("ID")) {
            String ids = fixedcolumns.getString("ID");
            if (StringUtils.isNotEmpty(ids)) {
                String idsReplace = ids.replaceAll("\\[", "").replaceAll("]","");
                String[] splitids = idsReplace.split(",|，");
                JSONArray jsonArray = new JSONArray(Arrays.asList(splitids));
                whereKey.put("ID", jsonArray);
            }
        }
        if (fixedcolumns.containsKey("SOURCE_CODE")) {
            String sourceCode = fixedcolumns.getString("SOURCE_CODE");
            if (StringUtils.isNotEmpty(sourceCode)) {
                String sourceCodeReplace = sourceCode.replaceAll("\\s*", "");
                String[] splitSourceCode = sourceCodeReplace.split(",|，");
                JSONArray jsonArray = new JSONArray(Arrays.asList(splitSourceCode));
                whereKey.put("SOURCE_CODE", jsonArray);
            }
        }
        if (fixedcolumns.containsKey("BILL_NO")) {
            String sourceCode = fixedcolumns.getString("BILL_NO");
            if (StringUtils.isNotEmpty(sourceCode)) {
                String sourceCodeReplace = sourceCode.replaceAll("\\s*", "");
                String[] splitSourceCode = sourceCodeReplace.split(",|，");
                JSONArray jsonArray = new JSONArray(Arrays.asList(splitSourceCode));
                whereKey.put("BILL_NO", jsonArray);
            }
        }
        if (fixedcolumns.containsKey("USER_NICK")) {
            whereKey.put("USER_NICK", fixedcolumns.getString("USER_NICK"));
        }
        if (fixedcolumns.containsKey("CP_C_SHOP_ID")) {
            JSONArray shopIdList = fixedcolumns.getJSONArray("CP_C_SHOP_ID");
            whereKey.put("CP_C_SHOP_ID", shopIdList);
        }
        if (fixedcolumns.containsKey("CP_C_PHY_WAREHOUSE_ID")) {
            JSONArray warehouseIdList = fixedcolumns.getJSONArray("CP_C_PHY_WAREHOUSE_ID");
            whereKey.put("CP_C_PHY_WAREHOUSE_ID", warehouseIdList);
        }
        if (fixedcolumns.containsKey("ORDER_TYPE")) {
            JSONArray orderTypeList = fixedcolumns.getJSONArray("ORDER_TYPE");
            JSONArray jsonArray = new JSONArray();
            if (CollectionUtils.isNotEmpty(orderTypeList)) {
                for (Object orderType : orderTypeList) {
                    jsonArray.add(orderType.toString().replaceAll("=", ""));
                }
                whereKey.put("ORDER_TYPE", jsonArray);
            }
        }
        if (fixedcolumns.containsKey("IS_FORCE")) {
            //平台
            JSONArray flagList = fixedcolumns.getJSONArray("IS_FORCE");
            JSONArray jsonArray = new JSONArray();
            if (CollectionUtils.isNotEmpty(flagList)) {
                for (Object flagType : flagList) {
                    jsonArray.add(flagType.toString().replaceAll("=", ""));
                }
                whereKey.put("IS_FORCE", jsonArray);
            }
        }
        //是否手工新增 新增查询条件
        if (fixedcolumns.containsKey("IS_ADD")) {
            //Integer isAdd = fixedcolumns.getInteger("IS_ADD");
            JSONArray flagList = fixedcolumns.getJSONArray("IS_ADD");
            JSONArray jsonArray = new JSONArray();
            if (CollectionUtils.isNotEmpty(flagList)) {
                for (Object flagType : flagList) {
                    jsonArray.add(flagType.toString().replaceAll("=", ""));
                }
            }
            if (jsonArray.size() == 1) {
                Integer isAdd = Integer.parseInt((String) jsonArray.get(0));
                if (isAdd == 1) {
                    whereKey.put("ORDER_SOURCE", OcOrderTagEum.TAG_HAND.getVal());
                } else {
                    whereKey.put("ORDER_SOURCE", "!=" + OcOrderTagEum.TAG_HAND.getVal());
                }
            }


        }
        //增加交易平台查询条件
        if (fixedcolumns.containsKey("PLATFORM")) {
            JSONArray flagList = fixedcolumns.getJSONArray("PLATFORM");
            JSONArray jsonArray = new JSONArray();
            if (CollectionUtils.isNotEmpty(flagList)) {
                for (Object flagType : flagList) {
                    jsonArray.add(flagType.toString().replaceAll("=", ""));
                }
                whereKey.put("PLATFORM", jsonArray);
            }
        }
        /**
         * 失败原因
         */
        if (fixedcolumns.containsKey("FORCE_SEND_FAIL_REASON")) {
            whereKey.put("FORCE_SEND_FAIL_REASON", "*" + fixedcolumns.getString("FORCE_SEND_FAIL_REASON") + "*");
        }
        return whereKey;

    }

    @Test
    public void orderListQuery() {
        orderListQueryES();
    }

    public void orderListQueryES() {
        JSONObject resultData = new JSONObject();
        JSONObject param = JSON.parseObject("{\"startindex\":0,\"range\":500,\"column_include_uicontroller\":true,\"fixedcolumns\":{\"ID\":[6327284]},\"table\":\"OC_B_ORDER_WARE\",\"isfixedid\":true}");

        log.debug("订单出库查询入参" + param.toString());
        JSONObject fixedcolumns = param.getJSONObject("fixedcolumns");
        Integer range = param.getInteger("range") == null ? QueryUtils.getdefalutrange() : param.getInteger("range");
        Integer startIndex = param.getInteger("startindex") == null ? 0 : param.getInteger("startindex");
        JSONArray orderByKey = this.getOrderByKey();
        try {
            JSONObject whereKey = this.getWhereKey(fixedcolumns);
            JSONObject filterKey = this.getFilterKey(fixedcolumns);


            JSONObject esResult = ES4Order.queryOrderOutboundList(whereKey, filterKey, orderByKey, range, startIndex);

            JSONArray aryIds = esResult.getJSONArray("data");
            Integer totalCount = esResult.getInteger("total");
            List<Integer> ids = Lists.newArrayList();
            for (int i = 0; i < aryIds.size(); i++) {
                Map<String, Integer> map = (Map<String, Integer>) aryIds.get(i);
                ids.add(map.get("ID"));
            }

        } catch (Exception e) {
            log.error("订单出库查询异常:", e);
            resultData.put("start", startIndex);
            resultData.put("row", "");
            resultData.put("totalRowCount", 0);

        }

    }

    /**
     * Es查询 日期过滤
     *
     * @param fixedcolumns
     * @return
     */
    public JSONObject getFilterKey(JSONObject fixedcolumns) {
        JSONObject filterKey = new JSONObject();
        if (fixedcolumns.containsKey("ORDER_DATE")) {
            String orderDate = fixedcolumns.getString("ORDER_DATE");
            String[] orderSplitDate = orderDate.split("~");
            String orderDateResult = convertDate(orderSplitDate[0], orderSplitDate[1]);
            filterKey.put("ORDER_DATE", orderDateResult);
        }
        return filterKey;
    }

    /**
     * ES 查询orderby条件
     *
     * @return
     */
    public JSONArray getOrderByKey() {
        JSONArray orderKeys = new JSONArray();
        JSONObject orderByKey = new JSONObject();
        orderByKey.put("asc", false);
        orderByKey.put("name", "CREATIONDATE");
        orderKeys.add(orderByKey);
        return orderKeys;
    }

    /**
     * 日期转成ES需要的格式
     *
     * @return
     */
    public String convertDate(String begindate, String endDate) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
        if (StringUtils.isEmpty(begindate) || StringUtils.isEmpty(endDate)) {
            return "";
        }
        try {
            String result = sdf.parse(begindate).getTime() + "~" + sdf.parse(endDate).getTime();
            return result;

        } catch (ParseException e) {
            e.printStackTrace();
        }
        return "";

    }
}