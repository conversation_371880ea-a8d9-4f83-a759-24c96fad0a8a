package com.jackrain.nea.oc.oms.vo;

import com.jackrain.nea.oc.oms.model.table.OcBReturnAfSend;
import lombok.Data;
import org.apache.commons.lang3.time.FastDateFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

/**
 * @ClassName : OcBReturnAfSendImpVO  
 * @Description : 
 * <AUTHOR>  YCH
 * @Date: 2021-08-31 10:34  
 */

@Data
public class OcBReturnAfSendImpVO extends OcBReturnAfSend implements Serializable {

    public static final String cellStr = "cell_";
    public static final String rowStr = "row_";
    private BigDecimal qty;//数量
    private String psCSkuEcode;//sku编码
    private BigDecimal amtReturn; //明细退款金额
    private BigDecimal freight; //运费
    private String billTypeV; //退款类型
    private String payModeV; //支付方式

    //行号
    private int rowNum;
    //错误信息
    private String desc;

    /**
     * 导入生成模型
     *
     * @return
     */
    public static OcBReturnAfSendImpVO importCreate(int index, OcBReturnAfSendImpVO ocBOrderImpVo, Map<String, String> columnMap) {
        FastDateFormat dateFormat = FastDateFormat.getInstance("yyyy-MM-dd");
        try {
            //单据日期
            ocBOrderImpVo.setReturnApplyTime(dateFormat.parse(columnMap.get(rowStr + index + cellStr + 0)));
        } catch (Exception e) {

        }
        try {
            //原始订单编号
            ocBOrderImpVo.setSourceBillNo(columnMap.get(rowStr + index + cellStr + 1));
        } catch (Exception e) {

        }

        try {
            //退款原因
            ocBOrderImpVo.setReason(columnMap.get(rowStr + index + cellStr + 2));
        } catch (Exception e) {

        }
        try {
            //支付方式
            ocBOrderImpVo.setPayModeV(columnMap.get(rowStr + index + cellStr + 3));
        } catch (Exception e) {

        }
        try {
            //支付账号
            ocBOrderImpVo.setPayAccount(columnMap.get(rowStr + index + cellStr + 4));
        } catch (Exception e) {

        }
        try {
            //退款分类
            ocBOrderImpVo.setOcBReturnTypeEname(columnMap.get(rowStr + index + cellStr + 5));
        } catch (Exception e) {

        }
        try {
            //退款描述
            ocBOrderImpVo.setOcBReturnTypeItemEname(columnMap.get(rowStr + index + cellStr + 6));
        } catch (Exception e) {

        }
        try {
            //收款人姓名
            ocBOrderImpVo.setReceiverName(columnMap.get(rowStr + index + cellStr + 7));
        } catch (Exception e) {

        }
        try {
            //备注
            ocBOrderImpVo.setRemark(columnMap.get(rowStr + index + cellStr + 8));
        } catch (Exception e) {

        }
        try {
            //卖家备注
            ocBOrderImpVo.setSellerRemark(columnMap.get(rowStr + index + cellStr + 9));
        } catch (Exception e) {

        }
        try {
            //sku编码
            ocBOrderImpVo.setPsCSkuEcode(columnMap.get(rowStr + index + cellStr + 10));
        } catch (Exception e) {

        }
        try {
            //数量
            ocBOrderImpVo.setQty(new BigDecimal(columnMap.get(rowStr + index + cellStr + 11)));
        } catch (Exception e) {

        }
        try {
            //退款金额
            ocBOrderImpVo.setAmtReturn(new BigDecimal(columnMap.get(rowStr + index + cellStr + 12)));
        } catch (Exception e) {

        }
        try {
            //运费
            ocBOrderImpVo.setFreight(new BigDecimal(columnMap.get(rowStr + index + cellStr + 13)));
        } catch (Exception e) {

        }

        ocBOrderImpVo.setRowNum(index + 1);
        return ocBOrderImpVo;
    }
}
