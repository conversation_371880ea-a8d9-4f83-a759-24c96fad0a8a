package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@TableName(value = "oc_b_order_item")
public class OcBorderItemExtention extends OcBOrderItem {
    /**
     * by 孙俊磊
     * 新增规格列表字段
     * 用于订单中心-订单单对象-订单明细的规格选项
     */
    @TableField(exist = false)
    @JSONField(name = "STANDARDS_LIST")
    private List<JSONObject> standardsList;

    /**
     * 条码数量
     */
    @TableField(exist = false)
    @JSONField(name = "SKU_NUM")
    private int skuNum;

    /**
     * 库存
     */
    @TableField(exist = false)
    @JSONField(name = "STOCK")
    private BigDecimal stock;

    /**
     * 退货状态
     * 将退货状态赋值，通过refund_status(int)字段
     */
    @TableField(exist = false)
    @JSONField(name = "REFUND_STATUS_EXT")
    private String refund_status_ext;

    /**
     * 乔丹需要的明细头部带上主表的单据编号
     * 单据编号
     */
    @TableField(exist = false)
    @JSONField(name = "BILL_NO")
    private String billNo;

    /**
     * 乔丹需要的明细需要退款金额（同退换货明细字段"单件退货金额"）
     */
    @TableField(exist = false)
    @JSONField(name = "AMT_REFUND_SINGLE")
    private String amtRefundSingle;

    /**
     * 性别扩展
     */
    @TableField(exist = false)
    @JSONField(name = "SEX_NAME")
    private String sexName;

    /**
     * 尺寸选项组
     */
    @TableField(exist = false)
    @JSONField(name = "SIZE_LIST")
    private List<JSONObject> sizeList;

    /**
     * 颜色选项组
     */
    @TableField(exist = false)
    @JSONField(name = "CLR_LIST")
    private List<JSONObject> clrList;

    /**
     * 缺货数量
     */
    @TableField(exist = false)
    @JSONField(name = "QTY_LOST_NUM")
    private int qtyLostNum;

    /**
     * 已申请退货数量
     */
    @TableField(exist = false)
    @JSONField(name = "QTY_RETURN_APPLY_NUM")
    private int qtyReturnApplyNum;

    /**
     * 已退数量
     */
    @TableField(exist = false)
    @JSONField(name = "QTY_REFUND_NUM")
    private int qtyRefundNum;

    /**
     * @20200714 新增订单明细ID
     * 订单明细表ID
     */
    @TableField(exist = false)
    @JSONField(name = "OC_B_ORDER_ITEM_ID")
    private Long ocBOrderItemId;

    /**
     * @20201202 新增平台退款状态
     * 平台退款状态
     */
    @TableField(exist = false)
    @JSONField(name= "PT_RETURN_STATUS_EXT")
    private String ptReturnStatusExt;

    /**
     * 体积
     */
    @TableField(exist = false)
    @JSONField(name = "VOLUME")
    private String volume;

    /**
     * 总体积
     */
    @TableField(exist = false)
    @JSONField(name = "TOTALVOLUME")
    private String totalVolume;

    @TableField(exist = false)
    @JSONField(name = "GIFT_ATTR")
    private String giftAttr;

    @TableField(exist = false)
    @JSONField(name = "CATEGORY_NAME")
    private String categoryName;

    @TableField(exist = false)
    @JSONField(name = "SALES_CENTER_NAME")
    private String salesCenterName;

    @TableField(exist = false)
    @JSONField(name = "SALES_DEPARTMENT_NAME")
    private String salesDepartmentName;

    @TableField(exist = false)
    @JSONField(name = "SALES_GROUP_NAME")
    private String salesGroupName;

    @TableField(exist = false)
    @JSONField(name = "DIST_NAME_LEVEL_ONE")
    private String distNameLevelOne;

    @TableField(exist = false)
    @JSONField(name = "DIST_NAME_LEVEL_TWO")
    private String distNameLevelTwo;

    @TableField(exist = false)
    @JSONField(name = "DIST_NAME_LEVEL_THREE")
    private String distNameLevelThree;

    @TableField(exist = false)
    @JSONField(name = "CHEAPEST_EXPRESS")
    private String cheapestExpress;
}
