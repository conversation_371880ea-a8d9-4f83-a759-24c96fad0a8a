package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.UpdateOrderInfoCmd;
import com.jackrain.nea.oc.oms.model.relation.UserPermission;
import com.jackrain.nea.oc.oms.services.UpdateOrderInfoService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: 孙继东
 * @since: 2019-03-12
 * create at : 2019-03-12 9:26
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class UpdateOrderInfoCmdImpl implements UpdateOrderInfoCmd {
    @Autowired
    private UpdateOrderInfoService updateOrderInfoService;

    @Override
    public ValueHolderV14 checkOrder(List<Long> ids, int flag) {
        return updateOrderInfoService.checkOrder(ids, flag);
    }

    @Override
    public ValueHolderV14 updateLogistics(List<Long> ids, Long cLogisticsId, User loginUser) throws NDSException {
        return updateOrderInfoService.updateLogistics(ids, cLogisticsId, loginUser);
    }

    @Override
    public ValueHolderV14 updateWarehouse(List<Long> ids, Long shareStoresId, String shareStoresEcode, Long warehouseId, String warehouseEcode, String updateRemark, User loginUser) throws NDSException {
        return updateOrderInfoService.updateWarehouse(ids,shareStoresId,shareStoresEcode, warehouseId,warehouseEcode, updateRemark, loginUser);
    }

    @Override
    public JSONObject queryList(User user, JSONObject jsn) {
        return updateOrderInfoService.queryList(user, jsn);
    }

    @Override
    public ValueHolderV14 reallocateLogistics(String jsonStr, User user, UserPermission pem) {
        return updateOrderInfoService.reallocateLogistics(jsonStr, user, pem);
    }

    @Override
    public ValueHolderV14 reallocateWarehouse(String jsonStr, User user, UserPermission pem) {
        return updateOrderInfoService.reallocateWarehouse(jsonStr, user, pem);
    }

    @Override
    public ValueHolderV14 queryOmsShopStorage(JSONObject jsonObject, User loginUser) {
        return updateOrderInfoService.queryOmsShopStorage(jsonObject,loginUser);
    }

    @Override
    public ValueHolderV14 queryOmsWarehouse(JSONObject jsonObject, User loginUser) {
        return updateOrderInfoService.queryOmsWarehouse(jsonObject,loginUser);
    }

    @Override
    public ValueHolderV14 reDistributionLogistics(List<Long> ids, User loginUser) throws NDSException {
        return updateOrderInfoService.reDistributionLogistics(ids,loginUser);

    }
}
