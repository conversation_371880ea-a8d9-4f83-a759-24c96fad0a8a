package com.jackrain.nea.oc.oms.services.naika;

import cn.hutool.json.JSONUtil;
import com.burgeon.r3.sg.store.model.result.out.SgOutResultMilkCardItemMqResult;
import com.burgeon.r3.sg.store.model.result.out.SgOutResultSendMsgResult;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.constant.NaiKaTypeConstant;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderNaiKaMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderNaiKaUnfreezeMapper;
import com.jackrain.nea.oc.oms.model.enums.UnFreezeEnum;
import com.jackrain.nea.oc.oms.model.enums.naika.OmsOrderNaiKaStatusEnum;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderNaiKa;
import com.jackrain.nea.oc.oms.model.table.OcBOrderNaikaUnfreeze;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.resource.SystemTableNames;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName TestService
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/8/2 20:01
 * @Version 1.0
 */
@Component
@Slf4j
public class TestService {

    @Autowired
    private OcBOrderNaiKaMapper ocBOrderNaiKaMapper;
    @Autowired
    private OmsNaiKaOrderUnFreezeService unFreezeService;
    @Autowired
    private OmsNaiKaOrderVoidService omsNaiKaOrderVoidService;
    @Autowired
    private OcBOrderNaiKaUnfreezeMapper unfreezeMapper;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderItemMapper orderItemMapper;
    @Autowired
    private PsRpcService psRpcService;

    @Transactional(rollbackFor = Exception.class)
    public void extracted(SgOutResultSendMsgResult sgOutResultSendMsgResult, OcBOrder ocBOrder) {
        Long ocBOrderId = ocBOrder.getId();
        OcBOrderNaikaUnfreeze ocBOrderNaikaUnfreeze = new OcBOrderNaikaUnfreeze();
        BaseModelUtil.initialBaseModelSystemField(ocBOrderNaikaUnfreeze);
        ocBOrderNaikaUnfreeze.setOcBOrderId(ocBOrderId);
        ocBOrderNaikaUnfreeze.setId(ModelUtil.getSequence(SystemTableNames.OC_ORDER_NAIKA_UNFREEZE_TABLE_NAME));
        ocBOrderNaikaUnfreeze.setTid(ocBOrder.getTid());
        ocBOrderNaikaUnfreeze.setUnfreezeStatus(UnFreezeEnum.UN_FREEZE.getStatus());
        ocBOrderNaikaUnfreeze.setUnfreezeTimes(0);
        unfreezeMapper.insert(ocBOrderNaikaUnfreeze);

        // 更新零售发货单 推送奶卡的状态
        OcBOrder updateOcBOrder = new OcBOrder();
        updateOcBOrder.setId(ocBOrder.getId());
        updateOcBOrder.setModifieddate(new Date());
        updateOcBOrder.setToNaikaStatus(OmsOrderNaiKaStatusEnum.FREEZE.getStatus());
        ocBOrderMapper.updateById(updateOcBOrder);

        List<SgOutResultMilkCardItemMqResult> mqResultMilkCardItems = sgOutResultSendMsgResult.getMqResultMilkCardItems();
        if (CollectionUtils.isNotEmpty(mqResultMilkCardItems)) {
            // 1 先对mqResultMilkCardItems 中的商品id+sku编码进行group by
            // 2 根据分组后的 零售发货单id+商品id+sku编码获取明细表中的明细数据 如果只能拿到一个明细 直接可以赋值这个明细 如果拿到两个明细 则需要维护一个list list里面根据具体明细的数量
            // 会不会出现

            Map<String, List<SgOutResultMilkCardItemMqResult>> milkCardItemResultMap =
                    mqResultMilkCardItems.stream().collect(Collectors.groupingBy(SgOutResultMilkCardItemMqResult::getPsCSkuEcode));
            Set<String> skuSet = milkCardItemResultMap.keySet();
            Map<String, List<Long>> orderItemIdMap = new HashMap<>();
            for (String skuCode : skuSet) {
                // 根据零售发货单id +skucode 查询明细 可能会存在多条  需要考虑是否要根据什么条件来过滤数据
                List<OcBOrderItem> ocBOrderItemList = orderItemMapper.selectUnSuccessRefundBySku(ocBOrderId, skuCode);
                List<Long> orderItemIds = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(ocBOrderItemList)) {
                    for (OcBOrderItem orderItem : ocBOrderItemList) {
                        Integer qty = orderItem.getQty().intValue();
                        for (int i = 0; i < qty; i++) {
                            orderItemIds.add(orderItem.getId());
                        }
                        orderItemIdMap.put(skuCode, orderItemIds);
                    }
                }
            }

            for (String skuCode : milkCardItemResultMap.keySet()) {
                List<SgOutResultMilkCardItemMqResult> milkCardItemMqResultList = milkCardItemResultMap.get(skuCode);
                List<Long> orderItemIds = orderItemIdMap.get(skuCode);
                for (int i = 0; i < milkCardItemMqResultList.size(); i++) {
                    SgOutResultMilkCardItemMqResult milkCardItemMqResult = milkCardItemMqResultList.get(i);
                    ProductSku productSku = psRpcService.selectProductSku(milkCardItemMqResult.getPsCSkuEcode());
                    List<OcBOrderNaiKa> ocBOrderNaiKaList = ocBOrderNaiKaMapper.selectNaiKaList(ocBOrderId, milkCardItemMqResult.getMilkCard());
                    if (CollectionUtils.isEmpty(ocBOrderNaiKaList)) {
                        OcBOrderNaiKa ocBOrderNaiKa = new OcBOrderNaiKa();
                        ocBOrderNaiKa.setNaikaStatus(OmsOrderNaiKaStatusEnum.FREEZE.getStatus());
                        ocBOrderNaiKa.setOcBOrderId(ocBOrderId);
                        ocBOrderNaiKa.setBusinessTypeName(ocBOrder.getBusinessTypeName());
                        ocBOrderNaiKa.setBusinessTypeId(ocBOrder.getBusinessTypeId());
                        ocBOrderNaiKa.setBusinessTypeCode(ocBOrder.getBusinessTypeCode());
                        ocBOrderNaiKa.setBusinessType(NaiKaTypeConstant.ENTITY);
                        ocBOrderNaiKa.setCardCode(milkCardItemMqResult.getMilkCard());
                        ocBOrderNaiKa.setSkuSpec(productSku.getSkuSpec());
                        ocBOrderNaiKa.setPsCSkuEname(milkCardItemMqResult.getPsCProEname());
                        ocBOrderNaiKa.setPsCSkuEname(productSku.getSkuName());
                        ocBOrderNaiKa.setPsCSkuEcode(productSku.getSkuEcode());
                        ocBOrderNaiKa.setPsCSkuId(productSku.getId());
                        ocBOrderNaiKa.setPsCProEname(productSku.getName());
                        ocBOrderNaiKa.setPsCProEcode(productSku.getEcode());
                        ocBOrderNaiKa.setPsCProId(productSku.getProdId());
                        ocBOrderNaiKa.setTid(ocBOrder.getTid());
                        ocBOrderNaiKa.setId(ModelUtil.getSequence(SystemTableNames.OC_ORDER_NAIKA_TABLE_NAME));
                        ocBOrderNaiKa.setOcBOrderItemId(orderItemIds.get(i));
                        ocBOrderNaiKaMapper.insert(ocBOrderNaiKa);
                    }
                }
            }
        }
    }

    public ValueHolder test(Long id) {
        List<OcBOrderNaiKa> ocBOrderNaiKaList = ocBOrderNaiKaMapper.selectNaiKaListByOrderId(id);
        if (CollectionUtils.isEmpty(ocBOrderNaiKaList)) {
            throw new NDSException("奶卡信息为空");
        }
        log.info("NaiKaUnFreezeCmdImpl.execute.ocBOrderNaiKaList:{}", JSONUtil.toJsonStr(ocBOrderNaiKaList));
        return unFreezeService.naiKaOrderUnFreeze(id, null, ocBOrderNaiKaList);
    }

    public void orderVoid(Long naikaOrderId, Long returnId, User user) {
        omsNaiKaOrderVoidService.naiKaOrderVoid(Collections.singletonList(naikaOrderId), returnId, user);
    }
}
