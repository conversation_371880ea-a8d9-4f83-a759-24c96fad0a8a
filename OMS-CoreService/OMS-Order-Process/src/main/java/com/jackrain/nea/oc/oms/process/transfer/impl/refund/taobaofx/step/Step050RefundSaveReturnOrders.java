package com.jackrain.nea.oc.oms.process.transfer.impl.refund.taobaofx.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoFxRefundRelation;
import com.jackrain.nea.oc.oms.model.relation.OcBReturnOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoFxRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.oc.oms.util.ReturnOrderTransferUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 保存退换货订单信息
 * 1）判断退换货订单是否存在
 * 2）不存在新增退换货信息
 * 3）存在更新收货人信息、及
 *
 * @author: 周琳胜
 * @since: 2019-07-16
 * create at : 2019-07-16 13:51
 */
@Step(order = 50, description = "保存退换货订单信息")
@Slf4j
@Component
public class Step050RefundSaveReturnOrders extends BaseTaobaoFxRefundProcessStep
        implements IOmsOrderProcessStep<IpTaobaoFxRefundRelation> {

    @Autowired
    ReturnOrderTransferUtil returnOrderTransferUtil;

    @Override
    public ProcessStepResult<IpTaobaoFxRefundRelation> startProcess(
            IpTaobaoFxRefundRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        log.debug("TaobaoFxReturnTransferOrder.step05" + orderInfo);
        IpBTaobaoFxRefund taobaoFxRefund = orderInfo.getTaobaoFxRefund();
        try {
            OcBOrder ocBOrder = orderInfo.getOcBOrder();
            List<OcBReturnOrderRelation> returnOrderRelations =
                    taobaoFxReturnTransferUtil.taobaoFxRefundOrderToReturnOrder(orderInfo);
            Long result =
                    omsReturnOrderService.saveOmsReturnOrderInfo(returnOrderRelations, operateUser);
            //-1:失败 0：新增成功 其他：单据已存在次单据的id
            log.debug(this.getClass().getName() + " 生成退换单返回id:{}", result);
            if (result == -1) {
                return new ProcessStepResult<>(StepStatus.FAILED, "订单id:" + ocBOrder.getId() + ";订单状态为:" + ocBOrder.getOrderStatus() + ",转换失败!");
            } else if (result == 0) {
                ipTaobaoFxRefundService.updateOrderReturnStatus(ocBOrder, taobaoFxRefund);
                return new ProcessStepResult<>(StepStatus.SUCCESS);
            } else {
                //存在时更新物流信息及插入日志
                ipTaobaoFxRefundService.saveExistReturnOrder(result, orderInfo, operateUser);
            }
            return new ProcessStepResult<>(StepStatus.SUCCESS);
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 退单转换异常!" + e, e);
            //修改中间表状态及系统备注
            ipTaobaoFxRefundService.updateRefundIsTransError(taobaoFxRefund, e.getMessage());
            String errorMessage = "退单转换异常!" + e.getMessage();
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
    }
}