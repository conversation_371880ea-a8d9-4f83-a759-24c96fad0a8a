package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 中台周期购额外信息
 *
 * <AUTHOR>
 */
@TableName(value = "oc_b_order_cycle_buy_info")
@Data
@Document(index = "oc_b_order_cycle_buy_info", type = "oc_b_order_cycle_buy_info")
public class OcBOrderCycleBuyInfo extends BaseModel {

    @JSONField(name = "ID")
    @Field(type = FieldType.Long)
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 订单id
     */
    @JSONField(name = "OC_B_ORDER_ID")
    @Field(type = FieldType.Long)
    private Long ocBOrderId;

    /**
     * 订单OM单号
     */
    @JSONField(name = "BILL_NO")
    @Field(type = FieldType.Keyword)
    private String billNo;

    /**
     * 平台单号
     */
    @JSONField(name = "TID")
    @Field(type = FieldType.Keyword)
    private String tid;

    /**
     * 订单业务类型id
     */
    @JSONField(name = "BUSINESS_TYPE_ID")
    @Field(type = FieldType.Long)
    private Long businessTypeId;

    /**
     * 订单业务类型编码
     */
    @JSONField(name = "BUSINESS_TYPE_CODE")
    @Field(type = FieldType.Keyword)
    private String businessTypeCode;

    /**
     * 周期购数量(提数)
     */
    @JSONField(name = "BUY_NUM")
    @Field(type = FieldType.Integer)
    private Integer buyNum;

    /**
     * 周期购剩余提数
     */
    @JSONField(name = "REMAINING_NUM")
    @Field(type = FieldType.Integer)
    private Integer remainingNum;

    /**
     * 提货金额
     */
    @JSONField(name = "PICK_GOODS_AMT")
    @Field(type = FieldType.Double)
    private BigDecimal pickGoodsAmt;

    /**
     * 订单明细id
     */
    @JSONField(name = "ORDER_ITEM_ID")
    @Field(type = FieldType.Long)
    private Long orderItemId;

    /**
     * SKU id
     */
    @JSONField(name = "SKU_ID")
    @Field(type = FieldType.Long)
    private Long skuId;

    /**
     * SKU 编码
     */
    @JSONField(name = "SKU_CODE")
    @Field(type = FieldType.Keyword)
    private String skuCode;

    /**
     * 是否是赠品
     */
    @JSONField(name = "IS_GIFT")
    @Field(type = FieldType.Integer)
    private Integer isGift;

}
