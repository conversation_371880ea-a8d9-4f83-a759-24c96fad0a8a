package com.jackrain.nea.oc.oms.es;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.es.util.SpecialElasticSearchUtil;
import com.jackrain.nea.oc.oms.gsi.GSI4ReturnOrder;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.constant.OcOmsReturnOrderConstant;
import com.jackrain.nea.oc.oms.model.enums.*;
import com.jackrain.nea.oc.oms.model.relation.IpStandplatRefundRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderExchange;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.jackrain.nea.resource.OcElasticSearchIndexResources.OC_B_RETURN_ORDER_TYPE_NAME;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2020/10/28
 */
@Slf4j
public class ES4ReturnOrder {

    /**
     * @20200706 加个注释：待退货入库RETURN_STATUS = 20
     */
    private static final Integer RETURN_STATUS = 20;

    private static final Integer IS_TO_WMS = 1;

    /**
     * 判断退单是否存在
     *
     * @param refundId 平台退款单号
     * @return 退单id
     */
    public static List<Long> isExistReturnOrder(String refundId) {
        String[] returnFields = {"ID"};
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("RETURN_ID", refundId);
        whereKeys.put("RETURN_STATUS", "!=" + TaobaoReturnOrderExt.ReturnOrderStatus.CANCEL.getCode());
        JSONObject search = ElasticSearchUtil.search(TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER,
                TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER, whereKeys, null, null, 10,
                0, returnFields);
        JSONArray returnData = search.getJSONArray("data");
        List<Long> ids = new ArrayList<>();
        if (null != returnData && !returnData.isEmpty()) {
            JSONObject jsonObject = returnData.getJSONObject(0);
            Long id = jsonObject.getLong("ID");
            ids.add(id);
        }
        return ids;
    }


    /**
     * 判断退单是否存在
     *
     * @param disputeId 平台退款单号
     * @return 退单id
     */
    public static List<Long> isExistReturnOrderByDisputeId(String disputeId) {

        String[] returnFields = {"ID"};
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("TB_DISPUTE_ID", disputeId);
        whereKeys.put("RETURN_STATUS", "!=" + TaobaoReturnOrderExt.ReturnOrderStatus.CANCEL.getCode());
        JSONObject search = ElasticSearchUtil.search(TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER,
                TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER, whereKeys, null, null, 10,
                0, returnFields);
        JSONArray returnData = search.getJSONArray("data");
        Set<Long> returnIds = new HashSet<>();
        if (null != returnData && !returnData.isEmpty()) {
            for (int i = 0; i < returnData.size(); i++) {
                JSONObject jsonObject = returnData.getJSONObject(i);
                Long id = jsonObject.getLong("ID");
                returnIds.add(id);
            }
        } else {
            String redisKey = BllRedisKeyResources.getReturnOrderDisputeIdKey(disputeId);
            Object id = RedisMasterUtils.getStrRedisTemplate().opsForValue().get(redisKey);
            if (id != null) {
                Long returnId = NumberUtils.toLong(id.toString());
                returnIds.add(returnId);
            }
        }
        return new ArrayList<>(returnIds);
    }

    public static List<Long> isExistReturnOrderByTid(String tid) {

        String[] returnFields = {"ID"};
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("TID", tid);
        whereKeys.put("RETURN_STATUS", "!=" + TaobaoReturnOrderExt.ReturnOrderStatus.CANCEL.getCode());
        JSONObject search = ElasticSearchUtil.search(TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER,
                TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER, whereKeys, null, null, 10,
                0, returnFields);
        JSONArray returnData = search.getJSONArray("data");
        Set<Long> returnIds = new HashSet<>();
        if (null != returnData && !returnData.isEmpty()) {
            for (int i = 0; i < returnData.size(); i++) {
                JSONObject jsonObject = returnData.getJSONObject(i);
                Long id = jsonObject.getLong("ID");
                returnIds.add(id);
            }
        }
        return new ArrayList<>(returnIds);
    }

    /**
     * 根据明细表的oid查询退货单是否存在
     *
     * @return
     */
    public static List<Long> isExistReturnOrderRefundByoid(String oid) {
        Set<Long> ids = new HashSet<>();
        String[] returnFileds = {"OC_B_RETURN_ORDER_ID"};
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("OID", oid);
        JSONObject search = ElasticSearchUtil.search(TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER,
                TaobaoReturnOrderExt.TABLENAME_OCRETURNORDERREFUND, whereKeys, null, null, 10, 0, returnFileds);
        JSONArray returnData = search.getJSONArray("data");
        if (null != returnData && !returnData.isEmpty()) {
            for (int i = 0; i < returnData.size(); i++) {
                JSONObject jsonObject = returnData.getJSONObject(i);
                Long orderId = jsonObject.getLong("OC_B_RETURN_ORDER_ID");
                ids.add(orderId);
            }
        }
        return new ArrayList<>(ids);
    }

    /**
     * 根据物流单号查询id
     *
     * @param logisticsNum 物流单号
     * @return 退单编号
     */
    public static List<Long> queryIdsByLogisticsCodeForLogisticsTrace(String logisticsNum) {

        JSONObject whereKeys = new JSONObject();
        JSONArray orderJAry = new JSONArray();
        JSONObject orderJo = new JSONObject();
        orderJo.put("asc", false);
        orderJo.put("name", "MODIFIEDDATE");
        orderJAry.add(orderJo);
        whereKeys.put("LOGISTICS_CODE", logisticsNum);

        JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_RETURN_ORDER_INDEX_NAME,
                OcElasticSearchIndexResources.OC_B_RETURN_ORDER_INDEX_NAME, whereKeys, null, orderJAry,
                100, 0, new String[]{"ID"});
        JSONArray returnIdAry = search.getJSONArray("data");
        return ES4Order.statisticsResult(returnIdAry, "ID");
    }


    /**
     * 根据物流单号查询id
     *
     * @param logisticsNum 物流单号
     * @return 退单编号
     */
    public static List<Long> queryIdsByLogisticsCode(String logisticsNum) {

        JSONObject whereKeys = new JSONObject();
        JSONArray orderJAry = new JSONArray();
        JSONObject orderJo = new JSONObject();
        orderJo.put("asc", false);
        orderJo.put("name", "MODIFIEDDATE");
        orderJAry.add(orderJo);
        whereKeys.put("LOGISTICS_CODE", logisticsNum);
        //    whereKeys.put("IS_TODRP", 0);

        JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_RETURN_ORDER_INDEX_NAME,
                OcElasticSearchIndexResources.OC_B_RETURN_ORDER_INDEX_NAME, whereKeys, null, orderJAry,
                100, 0, new String[]{"ID"});
        JSONArray returnIdAry = search.getJSONArray("data");
        return ES4Order.statisticsResult(returnIdAry, "ID");
    }

    /**
     * 收货人手机号码
     *
     * @param mobile 手机号码
     * @return ids
     */
    public static List<Long> queryIdsByReceiveMobile(String mobile) {

        JSONObject whereKeys = new JSONObject();
        whereKeys.put("RECEIVE_MOBILE", mobile);
        JSONObject orderJo = new JSONObject();
        orderJo.put("asc", false);
        orderJo.put("name", "MODIFIEDDATE");
        whereKeys.put("IS_TODRP", 0);
        JSONArray orderJAry = new JSONArray();
        orderJAry.add(orderJo);
        JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_RETURN_ORDER_INDEX_NAME,
                OcElasticSearchIndexResources.OC_B_RETURN_ORDER_INDEX_NAME, whereKeys, null, orderJAry,
                100, 0, new String[]{"ID"});
        JSONArray returnIdAry = search.getJSONArray("data");
        return ES4Order.statisticsResult(returnIdAry, "ID");
    }

    /**
     * 退单传wms
     *
     * @param range 每次预查询数据量
     * @return 退单编号集
     */
    public static JSONArray QueryOrderReturnIds(Integer range) {
        //1.查询 所有状态为：等待退货入库状态 并且 是原退的退货单
        JSONObject whereKeys = new JSONObject();
        //退单状态
        whereKeys.put("RETURN_STATUS", RETURN_STATUS);
//        whereKeys.put("IS_NEED_TO_WMS", IS_TO_WMS);
        // @20200715 增加WMS撤回状态为初始：0
        whereKeys.put("WMS_CANCEL_STATUS", OcBorderListEnums.WmsCanceStatusEnum.UN_RECALL.getVal());
        JSONObject orderKey = new JSONObject();
        //修改时间升序
        orderKey.put("asc", true);
        orderKey.put("name", "MODIFIEDDATE");
        JSONArray order = new JSONArray();
        order.add(orderKey);
        JSONArray result = new JSONArray();
        //未传WMS状态
        result.add(WmsWithdrawalState.NO.toInteger());
        //传WMS状态失败
        result.add(WmsWithdrawalState.FAIL.toInteger());
        whereKeys.put("IS_TOWMS", result);

        JSONObject filterKeys = new JSONObject();
        //失败次数小于5次
        filterKeys.put("TO_DRP_COUNT", "~" + 5);
        //失败次数小于5次
        filterKeys.put("QTY_WMS_FAIL", "~" + 5);
        String[] filed = {"ID"};
        Integer startIndex = 1;
        JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_RETURN_ORDER_INDEX_NAME,
                OcElasticSearchIndexResources.OC_B_RETURN_ORDER_TYPE_NAME, whereKeys,
                filterKeys, order,
                range, (startIndex - 1) * range, filed);
        JSONArray data = search.getJSONArray("data");
        JSONArray array = new JSONArray();
        if (CollectionUtils.isEmpty(data)) {
            return array;
        }
        for (int i = 0; i < data.size(); i++) {
            String id = "'" + data.getJSONObject(i).getString("ID") + "'";
            array.add(id);
        }
        return array;
    }

    /**
     * 退单传wms
     *
     * @param range 每次预查询数据量
     * @return 退单编号集
     */
    public static List<Long> searchReturnOrderForWms(Integer range, boolean flag) {
        //1.查询 所有状态为：等待退货入库状态 并且 是原退的退货单
        JSONObject whereKeys = new JSONObject();
        //退单状态
        whereKeys.put("RETURN_STATUS", RETURN_STATUS);
//        whereKeys.put("IS_NEED_TO_WMS", IS_TO_WMS);
        // @20200715 增加WMS撤回状态为初始：0
        whereKeys.put("WMS_CANCEL_STATUS", OcBorderListEnums.WmsCanceStatusEnum.UN_RECALL.getVal());
        JSONObject orderKey = new JSONObject();
        //修改时间升序
        orderKey.put("asc", true);
        orderKey.put("name", "MODIFIEDDATE");
        JSONArray order = new JSONArray();
        order.add(orderKey);
        JSONArray result = new JSONArray();
        //未传WMS状态
        result.add(WmsWithdrawalState.NO.toInteger());
        //传WMS状态失败
        result.add(WmsWithdrawalState.FAIL.toInteger());
        //传wms中
        result.add(WmsWithdrawalState.PASS.toInteger());
        whereKeys.put("IS_TOWMS", result);

        JSONArray toDrpSatus = new JSONArray();
        toDrpSatus.add(ToDRPStatusEnum.NOT.getCode());
        toDrpSatus.add(ToDRPStatusEnum.FAIL.getCode());
        whereKeys.put("TO_DRP_STATUS", toDrpSatus);

        JSONObject filterKeys = new JSONObject();
        //失败次数小于5次
        filterKeys.put("TO_DRP_COUNT", "~" + 5);
        //失败次数小于5次
        filterKeys.put("QTY_WMS_FAIL", "~" + 5);

        if (flag) {
            //无物流单号延迟推送时间
            filterKeys.put("PUSH_DELAY_TIME", "~" + System.currentTimeMillis());
        }
        String[] filed = {"ID"};
        JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_RETURN_ORDER_INDEX_NAME,
                OcElasticSearchIndexResources.OC_B_RETURN_ORDER_TYPE_NAME, whereKeys,
                filterKeys, order,
                range, 0, filed);
        List<Long> orderList = new ArrayList<>();
        if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
            JSONArray arrayObj = search.getJSONArray("data");
            for (Object obj : arrayObj) {
                JSONObject jsonObject = (JSONObject) obj;
                orderList.add(jsonObject.getLong("ID"));
            }
        }
        return orderList;
    }

    public static List<Long> searchReturnOrderForLogisticsTrace(Integer range, JSONArray shopIdArray) {
        log.info("退单物流轨迹订阅查询ES数据开始 range:{}", range);
        //1.查询 所有状态为：等待退货入库状态 并且 是原退的退货单
        JSONObject whereKeys = new JSONObject();
        JSONArray returnSatus = new JSONArray();
        //退单状态
        returnSatus.add(ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal());
//        returnSatus.add(ReturnStatusEnum.WAIT_AFTERSALE_ENSURE.getVal());
        whereKeys.put("RETURN_STATUS", returnSatus);

        whereKeys.put("CP_C_SHOP_ID", shopIdArray);

        whereKeys.put("IS_SEND_TMS_LOGISTIC", YesNoEnum.ZERO.getKey());

        // @20200715 增加WMS撤回状态为初始：0
        whereKeys.put("WMS_CANCEL_STATUS", OcBorderListEnums.WmsCanceStatusEnum.UN_RECALL.getVal());
        JSONObject orderKey = new JSONObject();
        //修改时间升序
        orderKey.put("asc", true);
        orderKey.put("name", "MODIFIEDDATE");
        JSONArray order = new JSONArray();
        order.add(orderKey);

        whereKeys.put("IS_TOWMS", WmsWithdrawalState.YES.toInteger());

        JSONObject filterKeys = new JSONObject();

        //失败次数小于5次
        filterKeys.put("LOGISTICS_TRACE_FAIL_NUM", "~" + 5);

        String[] filed = {"ID"};
        JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_RETURN_ORDER_INDEX_NAME,
                OcElasticSearchIndexResources.OC_B_RETURN_ORDER_TYPE_NAME, whereKeys,
                filterKeys, order,
                range, 0, filed);
        List<Long> orderList = new ArrayList<>();
        if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
            JSONArray arrayObj = search.getJSONArray("data");
            for (Object obj : arrayObj) {
                JSONObject jsonObject = (JSONObject) obj;
                orderList.add(jsonObject.getLong("ID"));
            }
        }
        return orderList;
    }

    /**
     * 根据退款单号(refund_bill_no) 查询 退换单id(oc_b_return_order_id)
     *
     * @param refundId 退款单号
     * @return Set oc_b_return_order_id 退单id
     */
    public static Set<Long> findReturnOrderIdByRefundBillNo(Object refundId) {
        Set<Long> ids = new HashSet<>();
        String[] returnFields = {"OC_B_RETURN_ORDER_ID"};
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("REFUND_BILL_NO", refundId);
        whereKeys.put("RETURN_STATUS", "!=" + TaobaoReturnOrderExt.ReturnOrderStatus.CANCEL.getCode());
        JSONObject search = ElasticSearchUtil.search(TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER,
                TaobaoReturnOrderExt.TABLENAME_OCRETURNORDERREFUND, whereKeys, null, null, 200, 0, returnFields);
        JSONArray returnData = search.getJSONArray("data");
        if (null != returnData && !returnData.isEmpty()) {
            for (int i = 0; i < returnData.size(); i++) {
                JSONObject jsonObject = returnData.getJSONObject(i);
                Long orderId = jsonObject.getLong("OC_B_RETURN_ORDER_ID");
                ids.add(orderId);
            }
        }

        return ids;
    }

    /**
     * 根据退款单号(refund_bill_no) 查询 退换单id(oc_b_return_order_id)
     *
     * @param returnNo 退款单号
     * @return Set oc_b_return_order_id 退单id
     */
    public static List<Long> queryShardKeyByRefundBillNo(String returnNo) {

        String[] returnFields = {"ID"};
        JSONObject whereKey = new JSONObject();
        whereKey.put("RETURN_STATUS", "!=" + TaobaoReturnOrderExt.ReturnOrderStatus.CANCEL.getCode());
        JSONObject childKey = new JSONObject();
        whereKey.put("REFUND_BILL_NO", returnNo);
        JSONObject search = ElasticSearchUtil.search(TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER,
                TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER, TaobaoReturnOrderExt.TABLENAME_OCRETURNORDERREFUND,
                whereKey, null, null, childKey, 50, 0, returnFields);
        JSONArray returnData = search.getJSONArray("data");
        Set<Long> filterKeys = new HashSet<>();
        if (CollectionUtils.isNotEmpty(returnData)) {
            for (int i = 0, l = returnData.size(); i < l; i++) {
                JSONObject jsonObject = returnData.getJSONObject(i);
                Long key = jsonObject.getLong("ID");
                if (Objects.isNull(key)) {
                    continue;
                }
                filterKeys.add(key);
            }
        }
        List<Long> shardKeys = new ArrayList<>(filterKeys);
        return shardKeys;
    }

    /**
     * 根据子订单id(oid)查询 退换单id(oc_b_return_order_id)
     *
     * @param oidList 子订单id
     * @return Set oc_b_return_order_id 退单id
     */
    public static List<Long> queryShardKeyByOidList(List<Object> oidList) {
        if (CollectionUtils.isEmpty(oidList)) {
            List<Long> shardKeys = new ArrayList<>();
            return shardKeys;
        }
        String[] returnFields = {"OC_B_RETURN_ORDER_ID"};
        JSONObject whereKey = new JSONObject();
        whereKey.put("RETURN_STATUS", "!=" + ReturnStatusEnum.CANCLE.getVal());
        JSONObject childKey = new JSONObject();
        JSONArray childJsonArray = new JSONArray(oidList);
        childKey.put("OID", childJsonArray);
        JSONObject search = ElasticSearchUtil.search(TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER,
                TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER, TaobaoReturnOrderExt.TABLENAME_OCRETURNORDERREFUND,
                whereKey, null, null, childKey, 50, 0, returnFields);
        JSONArray returnData = search.getJSONArray("data");
        Set<Long> filterKeys = new HashSet<>();
        if (CollectionUtils.isNotEmpty(returnData)) {
            for (int i = 0; i < returnData.size(); i++) {
                JSONObject jsonObject = returnData.getJSONObject(i);
                Long orderId = jsonObject.getLong("OC_B_RETURN_ORDER_ID");
                if (Objects.isNull(orderId)) {
                    continue;
                }
                filterKeys.add(orderId);
            }
        }
        List<Long> shardKeys = new ArrayList<>(filterKeys);
        return shardKeys;
    }

    /**
     * 根据子订单id(oid)查询 退换单id(oc_b_return_order_id)
     *
     * @param oid 子订单id
     * @return Set oc_b_return_order_id 退单id
     */
    public static Set<Long> findReturnOrderIdByOid(Object oid) {
        Set<Long> ids = new HashSet<>();
        String[] returnFields = {"OC_B_RETURN_ORDER_ID"};
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("OID", oid);
        whereKeys.put("RETURN_STATUS", "!=" + ReturnStatusEnum.CANCLE.getVal());
        JSONObject search = ElasticSearchUtil.search(TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER,
                TaobaoReturnOrderExt.TABLENAME_OCRETURNORDERREFUND, whereKeys, null, null, 100, 0, returnFields);
        JSONArray returnData = search.getJSONArray("data");
        if (CollectionUtils.isNotEmpty(returnData)) {
            for (int i = 0; i < returnData.size(); i++) {
                JSONObject jsonObject = returnData.getJSONObject(i);
                Long orderId = jsonObject.getLong("OC_B_RETURN_ORDER_ID");
                ids.add(orderId);
            }
        }
        return ids;
    }

    /**
     * 根据平台退款单号(returnNo)去获取退款单编号(id)
     *
     * @param returnNo 退单号
     * @return Set oc_b_return_order 退款单编号id
     */
    public static Set<Long> findIdByReturnId(Object returnNo) {
        Set<Long> ids = new HashSet<>();
        String[] returnFields = {"ID"};
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("RETURN_ID", returnNo);
        JSONObject search = ElasticSearchUtil.search(TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER,
                TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER, whereKeys, null, null, 10, 0, returnFields);
        JSONArray returnData = search.getJSONArray("data");
        if (null != returnData && !returnData.isEmpty()) {
            for (int i = 0; i < returnData.size(); i++) {
                JSONObject jsonObject = returnData.getJSONObject(i);
                Long orderId = jsonObject.getLong("ID");
                ids.add(orderId);
            }
        }
        return ids;
    }

    /**
     * 根据原始订单ID(orig_order_id)判断赠品退单是否存在
     *
     * @param originalOrderNo 原始订单ID
     * @return boolean
     */
    public static boolean isExistGiftReturnOrder(long originalOrderNo) {
        String[] returnFileds = {"ID"};
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("ORIG_ORDER_ID", originalOrderNo);

        JSONObject search = ElasticSearchUtil.search(TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER,
                TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER, whereKeys, null, null, 10, 0, returnFileds);

        JSONArray returnData = search.getJSONArray("data");
        return null != returnData && !returnData.isEmpty();
    }

    /**
     * 根据单据编号(bill_no)查询退单id
     *
     * @param billNo 单据编号
     * @return Set 退单id
     */
    public static Set<Long> findIdByBillNo(Object billNo) {
        Set<Long> idSet = new HashSet<>();
        String returnFieldId = "ID";
        String[] returnFileds = {returnFieldId};
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("BILL_NO", billNo);
        JSONObject search = ElasticSearchUtil.search(TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER,
                TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER, whereKeys, null,
                null, 50, 0, returnFileds);

        JSONArray returnData = search.getJSONArray("data");

        if (CollectionUtils.isNotEmpty(returnData)) {
            for (int i = 0; i < returnData.size(); i++) {
                Long orderId = returnData.getJSONObject(i).getLong(returnFieldId);
                idSet.add(orderId);
            }
        }
        return idSet;
    }

    /**
     * 根据入库通知单单据编号查询退单id
     *
     * @param noticeInNo 单据编号
     * @return Set 退单id
     */
    public static Long searchReturnOrderByNoticeInNo(String noticeInNo) {
        String returnFieldId = "ID";
        String[] returnField = {returnFieldId};
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("STO_IN_NOTICES_NO", noticeInNo);
        whereKeys.put("RETURN_STATUS", "!=" + ReturnStatusEnum.CANCLE.getVal());
        JSONArray proReturnStatus = new JSONArray();
        proReturnStatus.add(ProReturnStatusEnum.WAIT.getVal());
        proReturnStatus.add(ProReturnStatusEnum.PORTION.getVal());
        whereKeys.put("PRO_RETURN_STATUS", proReturnStatus);
        JSONObject search = ElasticSearchUtil.search(TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER,
                TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER, whereKeys, null,
                null, 1, 0, returnField);
        JSONArray returnData = search.getJSONArray("data");
        if (CollectionUtils.isNotEmpty(returnData)) {
            return returnData.getJSONObject(0).getLong(returnFieldId);
        }
        return null;
    }

    /**
     * 根据物流单号查询退单id
     *
     * @param no 物流编号
     * @return Set 退单id
     */
    public static Set<Long> searchReturnOrderByLogisticsNo(String no) {
        String returnFieldId = "ID";
        String[] returnField = {returnFieldId};
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("LOGISTICS_CODE", no);
        whereKeys.put("RETURN_STATUS", "!=" + ReturnStatusEnum.CANCLE.getVal());
        JSONArray proReturnStatus = new JSONArray();
        proReturnStatus.add(ProReturnStatusEnum.WAIT.getVal());
        proReturnStatus.add(ProReturnStatusEnum.PORTION.getVal());
        whereKeys.put("PRO_RETURN_STATUS", proReturnStatus);
        JSONObject search = ElasticSearchUtil.search(TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER,
                TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER, whereKeys, null,
                null, 1, 0, returnField);
        JSONArray returnData = search.getJSONArray("data");
        Set<Long> set = new HashSet<>();
        if (CollectionUtils.isNotEmpty(returnData)) {
            for (int i = 0; i < returnData.size(); i++) {
                Long aLong = returnData.getJSONObject(0).getLong(returnFieldId);
                set.add(aLong);
            }
        }
        return set;
    }

    /**
     * 根据退单id(return_id)查询退单状态不是"取消"的退款单编号(id)
     *
     * @param returnId 退单id
     * @return JSONArray
     */
    public static JSONArray findIdByReturnIdAndStatus(Object returnId) {
        JSONObject whereKeys = new JSONObject();
        String[] filed = {"ID"};
        whereKeys.put("RETURN_ID", returnId);
        whereKeys.put("RETURN_STATUS", "!=" + ReturnStatusEnum.CANCLE.getVal());
        JSONObject search = ElasticSearchUtil.search(TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER,
                TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER, whereKeys, null, null, 50, 0, filed);
        return search.getJSONArray("data");
    }

    /**
     * 根据物流单号(logistics_code)查询退换单不是取消状态(return_status!=cancel)的退单id
     *
     * @param logisticsCode 物流单号
     * @return List 退单id
     */
    public static List<Long> findIdByLogisticsCode(Object logisticsCode) {
        List<Long> idList = new ArrayList<>();
        String[] returnFields = {"ID"};
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("LOGISTICS_CODE", logisticsCode);
        whereKeys.put("RETURN_STATUS", "!=" + ReturnStatusEnum.CANCLE.getVal());
        JSONObject search = ElasticSearchUtil.search(TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER,
                TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER, whereKeys, null, null, 10, 0, returnFields);

        JSONArray returnData = search.getJSONArray("data");
        if (null != returnData && !returnData.isEmpty()) {
            for (int i = 0; i < returnData.size(); i++) {
                JSONObject jsonObject = returnData.getJSONObject(i);
                Long id = jsonObject.getLong("ID");
                idList.add(id);
            }
        }
        return idList;
    }

    /**
     * 根据淘宝中间表平台单号(tb_dispute_id)查询退单id
     *
     * @param disputeId 中间表平台单号
     * @return Long 退单id
     */
    public static Long findIdByTbDisputeId(Object disputeId) {
        Long returnOrderId = null;
        String[] returnFields = new String[]{"ID"};
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("TB_DISPUTE_ID", disputeId);
        JSONObject search = ElasticSearchUtil.search(TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER,
                TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER, whereKeys, null,
                null, 50, 0, returnFields);

        JSONArray data = search.getJSONArray("data");
        if (CollectionUtils.isNotEmpty(data)) {
            JSONObject jsonObj = data.getJSONObject(0);
            returnOrderId = jsonObj.getLong("ID");
        }
        return returnOrderId == null ? 0 : returnOrderId;
    }

    /**
     * 根据退单id(refund_bill_no)查询退单状态不是"取消"的退款单(ocBReturnOrderId)
     *
     * @param refundNo 退单id
     * @return Set 退单id
     */
    public static Set<Long> findReturnOrderIdByRefundIdAndStatus(Object refundNo) {
        Set<Long> ids = new HashSet<>();
        String[] returnFields = {"OC_B_RETURN_ORDER_ID"};
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("REFUND_BILL_NO", refundNo);
        whereKeys.put("REFUND_STATUS", "!=" + ReturnStatusEnum.CANCLE.getVal());
        JSONObject search = ElasticSearchUtil.search(TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER,
                TaobaoReturnOrderExt.TABLENAME_OCRETURNORDERREFUND, whereKeys, null, null, 10, 0, returnFields);

        JSONArray returnData = search.getJSONArray("data");
        if (CollectionUtils.isNotEmpty(returnData)) {
            for (int i = 0; i < returnData.size(); i++) {
                JSONObject jsonObject = returnData.getJSONObject(i);
                Long orderId = jsonObject.getLong("OC_B_RETURN_ORDER_ID");
                ids.add(orderId);
            }
        }
        return ids;
    }


    /**
     * 根据修改时间范围查询退单id
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return Set 退单id
     */
    public static Set<Long> findIdByModifiedDate(Date beginTime, Date endTime) {
        Set<Long> ids = new HashSet<>();
        String[] returnFields = new String[]{"ID"};
        JSONObject whereKeys = new JSONObject();
        JSONObject filterKeys = new JSONObject();
        filterKeys.put("MODIFIEDDATE", beginTime.getTime() + "~" + endTime.getTime());

        JSONObject search = ElasticSearchUtil.search(TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER,
                TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER, whereKeys, filterKeys,
                null, 1000, 0, returnFields);

        JSONArray data = search.getJSONArray("data");
        if (CollectionUtils.isNotEmpty(data)) {
            for (int i = 0; i < data.size(); i++) {
                JSONObject jsonObject = data.getJSONObject(i);
                Long id = jsonObject.getLong("ID");
                ids.add(id);
            }
        }
        return ids;
    }


    public static List<Long> selectRefundOrderForCancel(int pageIndex, int pageSize) {
        List<Long> list = new ArrayList<>();
        int startIndex = pageIndex * pageSize;
        if (startIndex < 0) {
            startIndex = 0;
        }
        JSONObject orderKes = new JSONObject();
        orderKes.put("name", "MODIFIEDDATE");
        orderKes.put("asc", true);
        JSONArray orderKeys = new JSONArray();
        orderKeys.add(orderKes);

        String[] returnFields = {"ID"};
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("RETURN_STATUS", "=" + ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal());
        whereKeys.put("BUSINESS_TYPE_NAME", "!=" + "SAP销售退货");
        whereKeys.put("BUSINESS_TYPE_NAME", "!=" + "SAP拒收退货");
        JSONObject search = ElasticSearchUtil.search(TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER,
                TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER, whereKeys, null, null, pageSize, startIndex, returnFields);
        JSONArray data = search.getJSONArray("data");
        if (CollectionUtils.isNotEmpty(data)) {
            for (int i = 0; i < data.size(); i++) {
                JSONObject jsonObject = data.getJSONObject(i);
                Long id = jsonObject.getLong("ID");
                list.add(id);
            }
        }
        return list;
    }


    /**
     * 根据oid和tid查询退换货单id
     *
     * @param orderId
     * @param tid     平台交易id
     * @return 退换货单id
     */
    public static Long findIdByOrderIdAndTid(Long orderId, String tid) {
        List<Long> returnIdListByOrigSourceCode = GSI4ReturnOrder.getReturnIdListByOrigSourceCode(tid);
        if (CollectionUtils.isEmpty(returnIdListByOrigSourceCode)) {
            return null;
        }
        OcBReturnOrderMapper ocBReturnOrderMapper = GSI4ReturnOrder.returnMapper();
        List<OcBReturnOrder> list = ocBReturnOrderMapper.selectOcBReturnOrderByOrder(returnIdListByOrigSourceCode);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        list = list.stream().filter(p -> p.getOrigOrderId() != null && p.getOrigOrderId().equals(orderId)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0).getId();
    }

    /**
     * 根据退单id(return_id)查询退单状态不是"取消"的退款单(id)
     *
     * @param returnId 服务单号
     * @return Long 退款单id
     */
    public static Long findJDIdByReturnIdAndStatus(Object returnId) {
        Long id = null;
        String[] returnFields = new String[]{"ID"};
        JSONObject whereKeys = new JSONObject();
        //退款单号
        whereKeys.put("RETURN_ID", returnId);
        whereKeys.put("PLATFORM", PlatFormEnum.JINGDONG.getCode());
        whereKeys.put("RETURN_STATUS", "!=" + ReturnStatusEnum.CANCLE.getVal());

        JSONObject search = ElasticSearchUtil.search(TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER,
                TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER, whereKeys, null,
                null, 50, 0, returnFields);

        JSONArray data = search.getJSONArray("data");
        if (CollectionUtils.isNotEmpty(data)) {
            id = data.getJSONObject(0).getLong("ID");
        }
        return id;
    }

    /**
     * 根据退款单号查询退单id
     *
     * @param refundId 退款单号
     * @return Set 退单id
     */
    public static Set<Long> findReturnOrderIdByRefundId(Object refundId) {
        Set<Long> ids = new HashSet<>();
        String[] returnFields = {"OC_B_RETURN_ORDER_ID"};
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("REFUND_BILL_NO", refundId);
        JSONObject search = ElasticSearchUtil.search(TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER,
                TaobaoReturnOrderExt.TABLENAME_OCRETURNORDERREFUND, whereKeys, null, null, 10, 0, returnFields);
        JSONArray returnData = search.getJSONArray("data");
        if (CollectionUtils.isNotEmpty(returnData)) {
            for (int i = 0; i < returnData.size(); i++) {
                JSONObject jsonObject = returnData.getJSONObject(i);
                Long orderId = jsonObject.getLong("OC_B_RETURN_ORDER_ID");
                ids.add(orderId);
            }
        }
        return ids;
    }

    /**
     * 通过平台单号(tid)查询退单id
     *
     * @param tid 平台单号
     * @return Set 退单id
     */
    public static Set<Long> findIdByTid(Object tid) {
        Set<Long> ids = new HashSet<>();
        String[] returnFields = {"ID"};
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("TID", tid);
        whereKeys.put("RETURN_STATUS", "!=" + ReturnStatusEnum.CANCLE.getVal());
        JSONObject search = ElasticSearchUtil.search(TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER,
                TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER, whereKeys, null, null, 10, 0, returnFields);
        JSONArray returnData = search.getJSONArray("data");
        if (CollectionUtils.isNotEmpty(returnData)) {
            for (int i = 0; i < returnData.size(); i++) {
                JSONObject jsonObject = returnData.getJSONObject(i);
                Long orderId = jsonObject.getLong("ID");
                ids.add(orderId);
            }
        }

        return ids;
    }

    /**
     * 根据物流单号和退款单号、以及状态"非取消"查询退换单id
     *
     * @param logisticNumber 流单号
     * @param returnId       退款单号
     * @return Set 退单id
     */
    public static Set<Long> findIdByLogisticsCodeAndReturnIdAndStatus(Object logisticNumber, Object returnId) {
        Set<Long> ids = new HashSet<>(10);
        String[] returnFields = {"ID"};
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("LOGISTICS_CODE", logisticNumber);
        whereKeys.put("RETURN_ID", returnId);
        whereKeys.put("RETURN_STATUS", "!=" + ReturnStatusEnum.CANCLE.getVal());
        JSONObject search = ElasticSearchUtil.search(TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER,
                TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER, whereKeys, null, null, 10, 0, returnFields);

        JSONArray returnData = search.getJSONArray("data");
        if (CollectionUtils.isNotEmpty(returnData)) {
            for (int i = 0; i < returnData.size(); i++) {
                JSONObject jsonObject = returnData.getJSONObject(i);
                Long id = jsonObject.getLong("ID");
                ids.add(id);
            }
        }
        return ids;
    }

    /**
     * 更新退单主表信息后更新ES退单主表
     *
     * @param ocBReturnOrder
     * @throws IOException
     */
    public static boolean updateReturnOrderById(OcBReturnOrder ocBReturnOrder) throws IOException {
        Boolean flag = SpecialElasticSearchUtil.indexDocument(TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER,
                TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER,
                ocBReturnOrder, ocBReturnOrder.getId());
        return flag;
    }

    /**
     * 判断退换货订单ES索引是否存在
     *
     * @param index
     * @throws IOException
     */
    public static void isOcBReturnOrderIndexExit(String index) throws IOException {
        Boolean aBoolean = SpecialElasticSearchUtil.indexExists(index);
        if (!aBoolean) {
            List<Class> list = new ArrayList<>();
            list.add(OcBReturnOrderExchange.class);
            list.add(OcBReturnOrderRefund.class);
            SpecialElasticSearchUtil.indexCreate(list, OcBReturnOrder.class);
        }
    }

    /**
     * 根据退换货单ID 推换货明细表数据
     *
     * @param orderExchangeList
     * @throws IOException
     */
    public static void updateOrderExchangeListByReturnId(List<OcBReturnOrderExchange> orderExchangeList) throws IOException {
        SpecialElasticSearchUtil.indexDocuments(TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER,
                TaobaoReturnOrderExt.TABLENAME_OCRETURNORDEREXCHAGE, orderExchangeList,
                "OC_B_RETURN_ORDER_ID");
    }

    /**
     * 根据退换货单ID 推退单明细数据
     *
     * @param orderRefundList
     * @throws IOException
     */
    public static void updateOrderRefundListByReturnId(List<OcBReturnOrderRefund> orderRefundList) throws IOException {
        SpecialElasticSearchUtil.indexDocuments(TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER,
                TaobaoReturnOrderExt.TABLENAME_OCRETURNORDERREFUND, orderRefundList,
                "OC_B_RETURN_ORDER_ID");
    }

    /**
     * 根据退换货单ID 保存退货商品表数据（退单明细）
     *
     * @param returnOrderInfo
     * @throws IOException
     */
    public static void insertOrderReturnRefundByInfo(OcBReturnOrderRefund returnOrderInfo) throws IOException {
        SpecialElasticSearchUtil.indexDocument(TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER, TaobaoReturnOrderExt
                        .TABLENAME_OCRETURNORDERREFUND, returnOrderInfo, returnOrderInfo.getId(),
                returnOrderInfo.getOcBReturnOrderId());
    }

    /**
     * 根据退换货单ID 保存换货商品表数据
     *
     * @param orderItem
     * @throws IOException
     */
    public static void insertOrderReturnExchangeByitem(OcBReturnOrderExchange orderItem) throws IOException {
        SpecialElasticSearchUtil.indexDocument(TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER,
                TaobaoReturnOrderExt.TABLENAME_OCRETURNORDEREXCHAGE, orderItem,
                orderItem.getId(), orderItem.getOcBReturnOrderId());
    }

    /**
     * 退换单转退货单 删除换货单明细
     *
     * @param ocBReturnOrder
     * @param ocBReturnOrderExchange
     * @throws IOException
     */
    public static void delOcBOrderExchange(OcBReturnOrder ocBReturnOrder,
                                           OcBReturnOrderExchange ocBReturnOrderExchange) throws IOException {
        SpecialElasticSearchUtil.delDocument(TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER,
                TaobaoReturnOrderExt.TABLENAME_OCRETURNORDEREXCHAGE,
                ocBReturnOrderExchange.getId(), ocBReturnOrder.getId());
    }

    /**
     * 平台发货取消退单，查看退单是否存在
     */
    public static JSONArray QueryReturnOrdersByOrderId(OcBOrder ocBOrder) {
        JSONObject whereKeys = new JSONObject();
        //订单id
        whereKeys.put("ORIG_ORDER_ID", ocBOrder.getId());
        whereKeys.put("RETURN_STATUS", ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal());
        JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_RETURN_ORDER_INDEX_NAME,
                OcElasticSearchIndexResources.OC_B_RETURN_ORDER_TYPE_NAME, whereKeys, null, null,
                100, 0, new String[]{"ID"});
        if (null == search) {
            return null;
        }
        JSONArray aryIds = search.getJSONArray("data");
        return aryIds;
    }

    /**
     * 根据退单ID 删除ES退单数据
     *
     * @param id
     * @throws IOException
     */
    public static void deleteOrderReturnById(Long id) throws IOException {
        SpecialElasticSearchUtil.delDocument(OcElasticSearchIndexResources.OC_B_RETURN_ORDER_INDEX_NAME,
                OcElasticSearchIndexResources.OC_B_RETURN_ORDER_INDEX_NAME, id);
    }

    /**
     * 根据退单ID,退单商品明细ID，删除退单商品明细数据
     *
     * @param id
     * @param ocBReturnOrderRefund
     * @throws IOException
     */
    public static void deleteReturnOrderRefundById(Long id, OcBReturnOrderRefund ocBReturnOrderRefund) throws IOException {
        SpecialElasticSearchUtil.delDocument(OcElasticSearchIndexResources.OC_B_RETURN_ORDER_INDEX_NAME,
                "oc_b_return_order_refund",
                ocBReturnOrderRefund.getId(), id);
    }

    /**
     * 公共交互ES拉取订单Id集合
     *
     * @param whereKeyJson 组装条件json
     * @param pageIndex    初始页面index
     * @param pageSize     页面数据大小
     * @return List<Long>
     */
    public static List<Long> queryEsReturnOrderList(JSONObject whereKeyJson, int pageIndex, int pageSize) {
        List<Long> orderList = new ArrayList<>();
        String indexName = OcElasticSearchIndexResources.OC_B_RETURN_ORDER_INDEX_NAME;
        int startIndex = pageIndex * pageSize;
        if (startIndex < 0) {
            startIndex = 0;
        }
        //根据订单创建时间设置排序键
        JSONArray orderKeys = new JSONArray();
        JSONObject orderKey = new JSONObject();
        orderKey.put("asc", true);
        orderKey.put("name", "MODIFIEDDATE");
        orderKeys.add(orderKey);
        JSONObject search = ElasticSearchUtil.search(indexName, OC_B_RETURN_ORDER_TYPE_NAME,
                whereKeyJson, null, orderKeys, pageSize, startIndex, new String[]{"ID"});
        if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
            JSONArray arrayObj = search.getJSONArray("data");
            for (Object obj : arrayObj) {
                JSONObject jsonObject = (JSONObject) obj;
                orderList.add(jsonObject.getLong("ID"));
            }
            return orderList;
        }
        return new ArrayList<>();
    }

    /**
     * 检查退款编号是不是在退换货表中存在
     *
     * @param returnNo 退款编号
     */
    public static Long checkReturId(String returnNo) {
        //2.设置退换货订单对象
        String[] returnFileds = new String[]{"ID"};
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("RETURN_ID", returnNo);//退款单号
        JSONObject searchByOrder =
                ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_RETURN_ORDER_INDEX_NAME,
                        OcElasticSearchIndexResources.OC_B_RETURN_ORDER_TYPE_NAME
                        , whereKeys, null,
                        null, 50, 0, returnFileds);
        JSONArray array = JSON.parseArray(searchByOrder.getString("data"));
        if (array != null) {
            Long returnId = Long.valueOf(array.get(0).toString());
            return returnId;
        }
        return null;
    }

    /**
     * 检查在原单在退换货中是不是存在
     *
     * @param orderInfo
     * @return
     */
    public static Boolean hasReturnOrder(IpStandplatRefundRelation orderInfo) {
        OcBOrder ocBOrder = orderInfo.getOcBOrder();
        JSONObject whereKeys = new JSONObject();
        String[] returnFileds = {"ID"};
        whereKeys.put("ORIG_ORDER_ID", ocBOrder.getId());
        JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_RETURN_ORDER_INDEX_NAME,
                OcElasticSearchIndexResources.OC_B_RETURN_ORDER_TYPE_NAME, whereKeys, null,
                null, 50, 0, returnFileds);
        JSONArray data = search.getJSONArray("data");
        return data != null && data.size() > 0;
    }

    /**
     * 插入 主子表的es(推送退换货主表和明细表的es)
     *
     * @param returnOrder
     * @param refunds
     */
    public static void insertParentChildEs(OcBReturnOrder returnOrder, List<OcBReturnOrderRefund> refunds) throws Exception {
        //推送退换货主表索引
        SpecialElasticSearchUtil.indexDocument(OcElasticSearchIndexResources.OC_B_RETURN_ORDER_INDEX_NAME,
                OcElasticSearchIndexResources.OC_B_RETURN_ORDER_TYPE_NAME, returnOrder, returnOrder.getId());
        //推送退换货明细表
        SpecialElasticSearchUtil.indexDocuments(OcElasticSearchIndexResources.OC_B_RETURN_ORDER_INDEX_NAME,
                OcElasticSearchIndexResources.OC_B_RETURN_ORDER_REFUND_TYPE_NAME, refunds, "OC_B_RETURN_ORDER_ID");
    }

    /**
     * 根据原始平台单号查询退单数据
     *
     * @param osc
     * @return
     */
    public static JSONObject getReturnOrderByOrigSourcecode(JSONArray osc) {
        // es查询所有
        JSONObject wheKey = new JSONObject();
        wheKey.put("ORIG_SOURCE_CODE", osc);
        return ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_RETURN_ORDER_INDEX_NAME,
                OcElasticSearchIndexResources.OC_B_RETURN_ORDER_TYPE_NAME, wheKey, null, null,
                1000, 0, new String[]{"ID"});
    }

    /**
     * 公共交互ES拉取退单Id集合
     *
     * @param whereKeyJson 组装条件json
     * @param pageIndex    初始页面index
     * @param pageSize     页面数据大小
     * @return List<Long>
     */
    public static List<Long> queryEsOrderList(JSONObject whereKeyJson, int pageIndex, int pageSize) {
        List<Long> returnOrderList = new ArrayList<>();
        String indexName = OcElasticSearchIndexResources.OC_B_RETURN_ORDER_INDEX_NAME;

        int startIndex = pageIndex * pageSize;
        if (startIndex < 0) {
            startIndex = 0;
        }
        JSONArray orderJAry = new JSONArray();
        JSONObject orderJo = new JSONObject();
        orderJo.put("asc", false);
        orderJo.put("name", "MODIFIEDDATE");
        orderJAry.add(orderJo);
        JSONObject search = ElasticSearchUtil.search(indexName,
                OcElasticSearchIndexResources.OC_B_RETURN_ORDER_TYPE_NAME,
                whereKeyJson, null, orderJAry,
                pageSize, startIndex, new String[]{"ID"});
        if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
            JSONArray arrayObj = search.getJSONArray("data");
            for (Object obj : arrayObj) {
                JSONObject jsonObject = (JSONObject) obj;
                returnOrderList.add(jsonObject.getLong("ID"));
            }
            return returnOrderList;
        }
        return new ArrayList<>();
    }

    /**
     * 查询退换货单Es信息 公用提取
     *
     * @param whereKeys
     * @param filterKeys
     * @param orderKeys
     * @param start
     * @param ranger
     * @param fileds
     * @return
     */
    public static JSONObject getReturnOrderSearchByUserinfo(JSONObject whereKeys, JSONObject filterKeys,
                                                            JSONArray orderKeys, Integer start, Integer ranger,
                                                            String[] fileds) {
        return ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_RETURN_ORDER_INDEX_NAME,
                OcElasticSearchIndexResources.OC_B_RETURN_ORDER_TYPE_NAME,
                whereKeys, filterKeys, orderKeys, ranger, start, fileds);
    }

    /**
     * 业务：退单-> 互道
     * 抽象动态查询(暂时没抽取查询条件)
     *
     * @param indexName  索引名称
     * @param typeName   索引类型
     * @param eachSize   每页条数
     * @param whereKeyJo 查询条件
     * @param filterKey  过滤条件
     * @return JSONObject 查询结果
     */
    public static JSONObject findIdByDynamic(String indexName, String typeName, int eachSize, JSONObject whereKeyJo,
                                             JSONObject filterKey) {
        final String shardKey = "ID";
        JSONObject esJsn = ElasticSearchUtil.search(indexName, typeName, whereKeyJo, filterKey, null,
                eachSize, 0, new String[]{shardKey});
        return esJsn;
    }

    /**
     * 通过平台单号(tid)查询退单id 和 status
     *
     * @param orderId 平台单号
     * @return JSONObject 退单id、status
     */
    public static JSONObject findIdAndStatusByTid(Long orderId) {
        String[] returnFields = {"RETURN_STATUS"};
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("ID", orderId);
        //whereKeys.put("RETURN_STATUS", "!=" + ReturnStatusEnum.CANCLE.getVal());
        JSONObject search = ElasticSearchUtil.search(TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER,
                TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER, whereKeys, null, null, 10, 0, returnFields);
        JSONArray returnData = search.getJSONArray("data");
        if (CollectionUtils.isNotEmpty(returnData)) {
            return returnData.getJSONObject(0);
        } else {
            return new JSONObject();
        }
    }

    /**
     * 查询未确认的 退换货单ID
     *
     * @return
     */
    public static List<Long> findIdByConfirmStatus() {
        List<Long> idList = new ArrayList<>();
        String[] returnFields = {"ID"};
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("CONFIRM_STATUS", 0);
        whereKeys.put("RETURN_STATUS", 20);
        JSONArray orderJAry = new JSONArray();
        JSONObject orderJo = new JSONObject();
        orderJo.put("asc", true);
        orderJo.put("name", "MODIFIEDDATE");
        orderJAry.add(orderJo);
        JSONObject search = ElasticSearchUtil.search(TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER,
                TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER, whereKeys, null, orderJAry, 1000, 0, returnFields);
        JSONArray returnData = search.getJSONArray("data");
        if (null != returnData && !returnData.isEmpty()) {
            for (int i = 0; i < returnData.size(); i++) {
                JSONObject jsonObject = returnData.getJSONObject(i);
                Long id = jsonObject.getLong("ID");
                idList.add(id);
            }
        }
        return idList;
    }


    public static List<Long> queryEsSyncPlatIdList(Integer range) {
        List<Long> returnOrderList = new ArrayList<>();
        String indexName = OcElasticSearchIndexResources.OC_B_RETURN_ORDER_INDEX_NAME;
        JSONObject whereKey = new JSONObject();
        whereKey.put("RETURN_STATUS", ReturnStatusEnum.COMPLETION.getVal());
        whereKey.put("PLATFORM_REFUND_STATUS", OcOmsReturnOrderConstant.PLATFORM_REFUND_STATUS_FAIL);

        JSONObject filterKeys = new JSONObject();
        //失败次数小于5次
        filterKeys.put("AUDIT_FAIL", "~" + 5);
        JSONArray orderJAry = new JSONArray();
        JSONObject orderJo = new JSONObject();
        orderJo.put("asc", true);
        orderJo.put("name", "MODIFIEDDATE");
        orderJAry.add(orderJo);
        JSONObject search = ElasticSearchUtil.search(indexName,
                OcElasticSearchIndexResources.OC_B_RETURN_ORDER_TYPE_NAME,
                whereKey, filterKeys, orderJAry,
                0, range, new String[]{"ID"});
        if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
            JSONArray arrayObj = search.getJSONArray("data");
            for (Object obj : arrayObj) {
                JSONObject jsonObject = (JSONObject) obj;
                returnOrderList.add(jsonObject.getLong("ID"));
            }
            return returnOrderList;
        }
        return new ArrayList<>();
    }

    public static List<Long> queryReturnOrderIdByBillNo(List<String> billNoList) {
        String[] returnFields = {"ID"};
        JSONObject whereKeys = new JSONObject();
        JSONArray jsonArray = JSONArray.parseArray(JSON.toJSONString(billNoList));
        whereKeys.put("BILL_NO", jsonArray);
        whereKeys.put("RETURN_STATUS", "!=" + TaobaoReturnOrderExt.ReturnOrderStatus.CANCEL.getCode());
        JSONObject search = ElasticSearchUtil.search(TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER,
                TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER, whereKeys, null, null, 1000,
                0, returnFields);
        JSONArray returnData = search.getJSONArray("data");
        List<Long> ids = new ArrayList<>();
        if (null != returnData && !returnData.isEmpty()) {

            for (int i = 0; i < returnData.size(); i++) {
                JSONObject jsonObject = returnData.getJSONObject(i);
                Long id = jsonObject.getLong("ID");
                ids.add(id);
            }
        }
        return ids;
    }

}
