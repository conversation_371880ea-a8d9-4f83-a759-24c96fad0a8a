package com.jackrain.nea.oc.oms.process.jitx.feedback.step;

import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.mapper.IpBTimeOrderVipOccupyItemMapper;
import com.jackrain.nea.oc.oms.model.constant.VipConstant;
import com.jackrain.nea.oc.oms.model.enums.SyncStatus;
import com.jackrain.nea.oc.oms.model.relation.IpJitxDeliveryRelation;
import com.jackrain.nea.oc.oms.services.IpVipTimeOrderService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 判断待寻仓订单单据反馈状态
 *
 * @author: chenxiulou
 * @since: 2019-06-25
 * create at : 2019-06-25 19:00
 */
@Step(order = 10, description = "判断寻仓订单据同步状态")
@Slf4j
@Component
public class Step010CheckDeliveryFeedBackStatus extends BaseJitxDeliveryProcessStep
        implements IOmsOrderProcessStep<IpJitxDeliveryRelation> {

    @Autowired
    protected IpVipTimeOrderService ipVipTimeOrderService;

    @Autowired
    private IpBTimeOrderVipOccupyItemMapper ipBTimeOrderVipOccupyItemMapper;

    @Override
    public ProcessStepResult<IpJitxDeliveryRelation> startProcess(IpJitxDeliveryRelation deliveryInfo,
                                                                  ProcessStepResult preStepResult,
                                                                  boolean isAutoMakeup, User operateUser) {
        if (deliveryInfo == null || deliveryInfo.getJitxDelivery() == null || deliveryInfo.getJitxDeliveryItemList() == null) {
            return new ProcessStepResult<>(StepStatus.FAILED, "deliveryInfo为空或者deliveryInfo.JitxDelivery或者deliveryInfo.getJitxDeliveryItemList为空；退出寻仓反馈");
        }
        IpJitxDeliveryRelation orderInfo = ipJitxDeliveryService.selectJitxDelivery(deliveryInfo.getOrderNo());
        if (orderInfo == null || orderInfo.getJitxDelivery() == null || orderInfo.getJitxDeliveryItemList() == null) {
            return new ProcessStepResult<>(StepStatus.FAILED, "orderInfo为空或者deliveryInfo.JitxDelivery或者deliveryInfo.getJitxDeliveryItemList为空；退出寻仓反馈");
        }
        if (CollectionUtils.isEmpty(orderInfo.getIpVipTimeOrderRelationList())) {
            return new ProcessStepResult<>(StepStatus.FAILED, "deliveryInfo.getIpVipTimeOrderRelationList为空；退出寻仓反馈");
        }
        deliveryInfo.setJitxDelivery(orderInfo.getJitxDelivery());
        deliveryInfo.setRemarks(orderInfo.getRemarks());
        deliveryInfo.setJitxDeliveryItemList(orderInfo.getJitxDeliveryItemList());
        deliveryInfo.setIpVipTimeOrderRelationList(orderInfo.getIpVipTimeOrderRelationList());

        //判断是否需要走虚拟寻源
        ipJitxDeliveryService.mergeJitxDelivery(deliveryInfo);
        Boolean storeJitx = deliveryInfo.getStoreJitx();
        String rootOrderSn = deliveryInfo.getRootOrderSn();
        Integer isVirtualOccupy = deliveryInfo.getIsVirtualOccupy();

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("寻仓单OrderSn:{},rootOrderSn:{}虚拟寻仓后开始寻源占单,storeJitx:{},isVirtualOccupy:{}",
                    "Step010CheckDeliveryFeedBackStatus",deliveryInfo.getOrderNo(),rootOrderSn),deliveryInfo.getOrderNo(),rootOrderSn,storeJitx,isVirtualOccupy);
        }

        int currentSyncStatus = deliveryInfo.getJitxDelivery().getSynstatus().intValue();
        if (SyncStatus.SYNCSUCCESS.toInteger() == currentSyncStatus) {
            String operateMessage = Resources.getMessage("单据" + deliveryInfo.getOrderId() + "状态为确认为JITX，同步完成");
            return new ProcessStepResult<>(StepStatus.FINISHED, operateMessage);
        } else if (SyncStatus.SYNCFAILD.toInteger() == currentSyncStatus) {
            String operateMessage = Resources.getMessage("单据" + deliveryInfo.getOrderId() + "状态为确认为JIT，同步完成");
            return new ProcessStepResult<>(StepStatus.FINISHED, operateMessage);
        } else if (SyncStatus.SYNCIN.toInteger() == currentSyncStatus) {
            String operateMessage = Resources.getMessage("单据" + deliveryInfo.getOrderId() + "状态为寻仓中，等待下一次转换");
            return new ProcessStepResult<>(StepStatus.FINISHED, operateMessage);
        } else if (SyncStatus.IN_VIRTUAL_OCCUPY.toInteger() == currentSyncStatus) {
            String operateMessage = "";
            if(isVirtualOccupy != null && VipConstant.JITX_DELIVERY_IS_VIRTUAL_OCCUPY_02.equals(isVirtualOccupy)){
                operateMessage = Resources.getMessage("单据" + deliveryInfo.getOrderId() + "虚拟寻仓完成，开始进行寻仓反馈");
                return new ProcessStepResult<>(StepStatus.SUCCESS, operateMessage);
            }else {
                operateMessage = Resources.getMessage("单据" + deliveryInfo.getOrderId() + "虚拟寻仓中，等待下一次转换");
                return new ProcessStepResult<>(StepStatus.FINISHED, operateMessage);
            }
        } else if (SyncStatus.UNSYNC.toInteger() == currentSyncStatus || SyncStatus.EXCEPTION.toInteger() == currentSyncStatus) {
            String operateMessage = Resources.getMessage("单据" + deliveryInfo.getOrderId() + "状态为未处理、反馈异常，开始进行寻仓反馈");
            if(SyncStatus.UNSYNC.toInteger() == currentSyncStatus && storeJitx && !VipConstant.JITX_DELIVERY_IS_VIRTUAL_OCCUPY_02.equals(isVirtualOccupy)){
                //是门店JITX订单，并且未虚拟寻仓
                return new ProcessStepResult<>(StepStatus.SUCCESS, operateMessage,Step055LockJitxDelivery.class);
            }else{
                return new ProcessStepResult<>(StepStatus.SUCCESS, operateMessage);
            }
        } else {
            String operateMessage = Resources.getMessage("单据" + deliveryInfo.getOrderId() + "检查状态失败，转换终止");
            return new ProcessStepResult<>(StepStatus.FAILED, operateMessage);
        }


    }
}
