package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsPhyStorageOutItemRequest;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsPhyStorageOutRequest;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsShareOutRequest;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsShopShareAndPhyQueryRequest;
import com.burgeon.r3.sg.inf.model.result.oms.SgOmsShopPhyQueryResult;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jackrain.nea.ac.model.result.AcStExpressQueryResult;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpCLogistics;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.cpext.model.table.CpLogistics;
import com.jackrain.nea.data.basic.services.BasicCpQueryService;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ext.permission.DataPermissionModel;
import com.jackrain.nea.ext.permission.Permissions;
import com.jackrain.nea.ip.model.request.VipJitxCreateChangeWarehouseWorkflowRequest;
import com.jackrain.nea.ip.model.result.VipJitxCreateChangeWarehouseWorkflowResult;
import com.jackrain.nea.oc.oms.mapper.IpBJitxDeliveryRecordMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OcOrderCheckBoxEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OmsPayType;
import com.jackrain.nea.oc.oms.model.enums.OmsWmsCancelStatus;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.UserPermission;
import com.jackrain.nea.oc.oms.model.table.IpBJitxDeliveryRecord;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.taobao.TaoBaoOrderStatus;
import com.jackrain.nea.oc.oms.nums.OmsParamConstant;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
import com.jackrain.nea.oc.oms.services.advance.OmsOrderAdvanceParseService;
import com.jackrain.nea.oc.oms.services.task.OcBJitxDealerOrderTaskService;
import com.jackrain.nea.oc.oms.services.task.OmsAuditTaskService;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.oc.oms.util.SplitMessageUtil;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.sg.service.SgOccupiedInventoryService;
import com.jackrain.nea.st.api.ExpressQueryServiceCmd;
import com.jackrain.nea.st.model.result.StCShopStrategyLogisticsItemResult;
import com.jackrain.nea.st.model.result.StCWarehouseLogisticStrategyResult;
import com.jackrain.nea.st.model.table.StCShopStrategyLogisticsItem;
import com.jackrain.nea.st.model.table.StCVipcomJitxWarehouse;
import com.jackrain.nea.st.service.VipcomJitxWarehouseService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.MD5Util;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.util.ValueHolderV14Utils;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import com.jackrain.nea.web.query.QuerySessionImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 修改物流和修改仓库
 *
 * @author: 孙继东
 * @since: 2019-03-11
 * create at : 2019-03-11 10:48
 */
@Slf4j
@Component
public class UpdateOrderInfoService {
    @Autowired
    private OcBJitxDealerOrderTaskService ocBJitxDealerOrderTaskService;

    @Autowired
    private BasicCpQueryService basicCpQueryService;

    @Autowired
    private IpBJitxDeliveryRecordMapper deliveryRecordMapper;

    public final static String SUCCESS = "SUCCESS";
    // 修改仓库标识redis过期时间-7天
    public final static int CHANGE_WAREHOUSE_FLAG_REDIS_EXPIRE_TIME = 7;
    private static final String ERROR_CODE = "ERROR_INVALID_NEW_DELIVERY_WAREHOUSE";
    @Autowired
    private OcBOrderMapper orderMapper;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private OmsOrderSplitService omsOrderSplitService;
    @Autowired
    private OmsOrderCancellationService omsOrderCancellationService;
    @Autowired
    private OmsOrderDistributeLogisticsService omsOrderDistributeLogisticsService;
    @Autowired
    private SgRpcService sgRpcService;
    @Autowired
    private OmsOrderService omsOrderService;
    @Autowired
    private BllRedisLockOrderUtil redisUtil;
    @Autowired
    private OmsOrderAutoSearchWarehouseService omsOrderAutoSearchWarehouseService;
    @Autowired
    private OmsOrderDistributeWarehouseService orderDistributeWarehouseService;
    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private OmsOrderLogService orderLogService;
    @Autowired
    private OcBOrderListQueryService ocBOrderListQueryService;
    @Autowired
    private StRpcService stRpcService;
    @Autowired
    private VipcomJitxWarehouseService jitxWarehouseService;
    @Autowired
    private IpRpcService ipRpcService;

    @Autowired
    private OcBJitxModifyWarehouseLogService ocBJitxModifyWarehouseLogService;

    @Autowired
    private OmsAuditTaskService omsAuditTaskService;

    @Reference(version = "1.0", group = "st")
    ExpressQueryServiceCmd expressQueryServiceCmd;

    @Autowired
    OmsOrderAdvanceParseService omsOrderAdvanceParseService;

    @Autowired
    private SgOccupiedInventoryService sgOccupiedInventoryService;

    @Autowired
    private OcBOrderNodeRecordService ocBOrderNodeRecordService;

    @Autowired
    private OcbCancelOrderMergeService cancelOrderMergeService;

    @Autowired
    private OmsOccupyTaskService omsOccupyTaskService;

    @Autowired
    private ModifyWarehouseService modifyWarehouseService;

    @Autowired
    private ModifyOrderLogisticsService modifyOrderLogisticsService;


    @Autowired
    private PropertiesConf propertiesConf;

    /**
     * 校验订单，是否弹出修改物流弹框
     *
     * @param ids  订单编号
     * @param flag 标记  1为修改物流，2为修改发货仓库
     * @return 返回信息
     */
    public ValueHolderV14 checkOrder(List<Long> ids, int flag) {
        ValueHolderV14 holder = new ValueHolderV14();
        if (!CollectionUtils.isEmpty(ids)) {
            //订单状态  1,待审核 2,缺货 3,已审核 4,配货中
            //** begin 夏继超 2.若订单状态非“待审核”、“缺货”，则提示：“当前状态异常，不允许修改发货仓库！”
            List<Integer> list = Arrays.asList(OcOrderCheckBoxEnum.CHECKBOX_UNCONFIRMED.getVal(), OcOrderCheckBoxEnum.CHECKBOX_OUT_OF_STOCK.getVal());
            //** end 夏继超
            //批量修改物流判断仓库一致才能修改
            List<OcBOrder> orderList = orderMapper.selectByIdsList(ids);
            List<OcBOrder> jitxOrder = orderList.stream()
                    .filter(x -> PlatFormEnum.VIP_JITX.getCode().equals(x.getPlatform())).collect(Collectors.toList());
            if (flag == 1 && CollectionUtils.isNotEmpty(jitxOrder)) {
                return ValueHolderV14Utils.getFailValueHolder("JITX订单不允许修改物流");
            }
            Integer isJitx = 0;
            if (flag == 2 && CollectionUtils.isNotEmpty(jitxOrder)) {
                if (jitxOrder.size() == orderList.size()) {
                    isJitx = 1;
                } else {
                    return ValueHolderV14Utils.getFailValueHolder("唯品订单需单独修改");
                }
            }
            HashSet<Object> set = new HashSet<>();
            HashSet<Object> ajustSome = new HashSet<>();
            JSONObject jsonObject = new JSONObject();
            List<OcBOrder> orders = orderMapper.selectByIdsList(ids);
            List<OcBOrderItem> allOrderItemList = ocBOrderItemMapper.selectOrderItemsByOrderIds(ids);
            Map<Long, List<OcBOrderItem>> orderItemListMap = allOrderItemList.stream().collect(Collectors.groupingBy(OcBOrderItem::getOcBOrderId));
            for (OcBOrder ocBOrder : orders) {
                Long id = ocBOrder.getId();
                if (ocBOrder == null) {
                    holder.setCode(ResultCode.FAIL);
                    holder.setData(id);
                    holder.setMessage("当前订单已不存在！");
                    return holder;
                }
                if (ocBOrder.getPlatform().equals(PlatFormEnum.ALIBABAASCP.getCode())) {
                    holder.setCode(ResultCode.FAIL);
                    holder.setData(id);
                    holder.setMessage("猫超订单不支持修改仓库");
                    return holder;
                }
                Integer status = ocBOrder.getOrderStatus();
                //status判空，否则null会进行强转为基本数据，从而导致空指针
                if (status != null) {
                    if (!list.contains(status)) {
                        //若订单状态非“待审核”、“缺货”、“已审核”、“配货中”等，则提示：“当前状态异常，不允许修改物流公司！”
                        holder.setCode(ResultCode.FAIL);
                        holder.setData(id);
                        holder.setMessage("当前状态异常，不允许修改" + (flag == 1 ? "物流公司" : "发货仓库") + "！");
                        return holder;
                    } else {
                        if (flag == 1) {
                            //修改物流公司
                            set.add(9L);
                            ajustSome.add(ocBOrder.getCpCLogisticsId());
                        } else {
                            //查询明细
                            List<OcBOrderItem> items = orderItemListMap.get(id);
                            if (PlatFormEnum.VIP_JITX.getCode().equals(ocBOrder.getPlatform())) {
                                if (StringUtils.isNotEmpty(ocBOrder.getMergedCode()) && items.size() > 1) {
                                    return ValueHolderV14Utils.getFailValueHolder("当前为JITX门店合单,不允许修改仓库");
                                }
                            }
                            if (CollectionUtils.isNotEmpty(items)) {
                                List<Long> skuList = items.stream().filter(i -> i.getProType() != null && i.getProType() != SkuType.NO_SPLIT_COMBINE).map(OcBOrderItem::getPsCSkuId).collect(Collectors.toList());
                                jsonObject.put("skuIds", skuList);
                            }
                            jsonObject.put("shopId", ocBOrder.getCpCShopId());
                            ajustSome.add(jsonObject);
                        }
                    }
                }
            }
            if (!ajustSome.isEmpty() || flag == 1) {
                holder.setCode(ResultCode.SUCCESS);
                if (ajustSome.iterator().hasNext()) {
                    holder.setData(ajustSome.iterator().next());
                }
                holder.setData(ajustSome.iterator().next());
                if (!ajustSome.isEmpty() && flag == 2) {
                    JSONObject data = (JSONObject) ajustSome.iterator().next();
                    data.put("isJitx", isJitx);
                    holder.setData(data);
                }
                holder.setMessage("允许修改");
            }
        } else {
            //若未选择明细，点击【修改物流】按钮，提示：“请选择需要修改物流记录！”
            holder.setCode(ResultCode.FAIL);
            holder.setMessage("请选择需要修改物流记录！");
        }
        return holder;
    }

    /**
     * @param ocBOrder    零售发货单主表
     * @param logisticsId 物流ID
     * @return 是否通过
     */
    public boolean checkCpLogistics(OcBOrder ocBOrder, Long logisticsId) {
        /*查询所有有效物流公司*/
        ValueHolderV14<List<CpLogistics>> valueHolderV14 = cpRpcService.queryLogisticsIsY();
        AssertUtil.assertException(!valueHolderV14.isOK(), valueHolderV14.getMessage());
        List<CpLogistics> logisticsList = Optional.ofNullable(valueHolderV14.getData()).orElse(new ArrayList<>());

        CpLogistics logistics = logisticsList.stream()
                .filter(obj -> logisticsId.equals(obj.getId())).findFirst().orElse(null);
        if (Objects.isNull(logistics)) {
            log.error(LogUtil.format("所选的物流公司不存在，物流公司ID:{}", "UpdateOrderInfoService.checkCpLogistics"),
                    logisticsId);
            return false;
        }

        /*如果【发货实体仓】不为空*/
        if (ocBOrder.getCpCPhyWarehouseId() != null) {
            /*查询仓库物流策略*/
            List<StCWarehouseLogisticStrategyResult> warehouseLogisticStrategyResults = stRpcService.getWarehouseExpress(ocBOrder.getCpCPhyWarehouseId());
            /*只保留仓物流策略中含有的*/
            logisticsList = getLogistics(warehouseLogisticStrategyResults, logisticsList);
        }

        /*如果选择的是大货物流，只需要校验仓策略即可*/
        if ("3".equals(String.valueOf(logistics.getType()))) {
            return logisticsList.stream().map(CpLogistics::getId).collect(Collectors.toSet()).contains(logisticsId);
        }

        /*根据店铺策略过滤*/
        logisticsList = filterCpLogistics(logisticsList, ocBOrder.getCpCShopId());
        if (CollectionUtils.isEmpty(logisticsList)) {
            return false;
        }
        /*返回该物流公司是否在过滤后的物流策略中*/
        List<Long> logisticsIdList = logisticsList.stream().map(CpLogistics::getId).collect(Collectors.toList());
        return logisticsIdList.contains(logisticsId);
    }


    /**
     * 修改物流
     *
     * @param ids          订单编号
     * @param cLogisticsId 物流公司id
     * @param loginUser    用户信息
     * @return 返回信息
     * @throws NDSException 异常信息
     */
    public ValueHolderV14 updateLogistics(List<Long> ids, Long cLogisticsId, User loginUser) throws NDSException {
        //默认走mq
        boolean flag = propertiesConf.getPropertyBoolean("r3.oc.oms.logistics.isMq");
        ValueHolderV14 vh = modifyOrderLogisticsService.modifyOrderLogistics(ids, cLogisticsId, loginUser);
        if (vh != null && flag) {
            return vh;
        }
        ValueHolderV14 holder = new ValueHolderV14();
        JSONObject data = new JSONObject();
        JSONObject coulumn = new JSONObject();
        JSONObject coulumn2 = new JSONObject();
        List<Object> columnList = new ArrayList<>();
        coulumn.put("key", "billNo");
        coulumn.put("title", "订单编号");
        columnList.add(coulumn);
        coulumn2.put("key", "message");
        coulumn2.put("title", "错误信息");
        columnList.add(coulumn2);
        data.put("columns", columnList);
        List<Object> failList = new ArrayList<>();
        //调用接口查询物流信息
        CpLogistics cpLogistics = null;
        try {
            cpLogistics = cpRpcService.checkCpLogistic(cLogisticsId);
        } catch (Exception e) {
            log.error(LogUtil.format("调用cp物流出错 错误信息{}"), Throwables.getStackTraceAsString(e));
        }
        if (CollectionUtils.isNotEmpty(ids) && cLogisticsId != null) {
            //判断界面录入的“物流公司”是否在【物流公司档案】中存在，且状态为已启用
            if (cpLogistics != null) {
                Integer count = 0;
                for (Long id : ids) {
                    JSONObject failMessage = new JSONObject();
                    String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(id);
                    RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
                    try {
                        if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                            OcBOrder ocBorder = orderMapper.selectById(id);
                            if (ocBorder != null && !(cLogisticsId.equals(ocBorder.getCpCLogisticsId()))) {
                                Integer status = ocBorder.getOrderStatus();
                                List<Integer> list = Arrays.asList(OcOrderCheckBoxEnum.CHECKBOX_UNCONFIRMED.getVal(), OcOrderCheckBoxEnum.CHECKBOX_OUT_OF_STOCK.getVal(), OcOrderCheckBoxEnum.CHECKBOX_IN_DISTRIBUTION.getVal());
                                if (!list.contains(status)) {
                                    failMessage.put("id", id);
                                    failMessage.put("message", id + "当前状态异常，不允许修改物流公司，建议刷新页面查看最新数据！");
                                    failList.add(failMessage);
                                    continue;
                                }
                                String billNo = ocBorder.getBillNo();
                                Integer wmsCancelStatus = ocBorder.getWmsCancelStatus();

                                //校验所选物流
                                if (!checkCpLogistics(ocBorder, cLogisticsId)) {
                                    failMessage.put("id", id);
                                    failMessage.put("message", id + "所选物流公司未维护，不允许修改！");
                                    failList.add(failMessage);
                                    continue;
                                }

                                //校验快递费用是否有配置
                                CpLogistics logi;
                                try {
                                    logi = getCpLogistics(cLogisticsId, id);
                                } catch (Exception e) {
                                    failMessage.put("id", id);
                                    failMessage.put("message", e.getMessage());
                                    failList.add(failMessage);
                                    continue;
                                }
                                //1-快递
                                if (logi.getType() == 1) {
                                    try {
                                        List<AcStExpressQueryResult> results = omsOrderDistributeLogisticsService.queryStExpress(ocBorder.getId(),
                                                ocBorder.getCpCPhyWarehouseId(), ocBorder.getCpCRegionProvinceId(), Lists.newArrayList(cLogisticsId));
                                        if (OcBOrderConst.IS_STATUS_IN.equals(results.get(0).getOrderAddressCheck())) {
                                            failMessage.put("id", id);
                                            failMessage.put("message", id + ",未维护目的省份报价快递");
                                            failList.add(failMessage);
                                            continue;
                                        }
                                    } catch (Exception e) {
                                        failMessage.put("id", id);
                                        failMessage.put("message", id + ",查询目的省份报价快递失败");
                                        failList.add(failMessage);
                                        continue;
                                    }
                                }

                                //若存在，判断订单平台是否为京东且支付方式为“货到付款”，若是，提示“货到付款的京东配送不允许修改物流！”
                                if (ocBorder.getPlatform().equals(PlatFormEnum.JINGDONG.getCode()) && ocBorder.getPayType() == OmsPayType.CASH_ON_DELIVERY.toInteger()) {
                                    failMessage.put("id", id);
                                    failMessage.put("message", "货到付款的京东配送不允许修改物流！");
                                    failList.add(failMessage);
                                    continue;
                                    //待审核、缺货，则点击【确认】按钮，则更新主表数据和日志信息
                                } else if (status == OcOrderCheckBoxEnum.CHECKBOX_UNCONFIRMED.getVal() || status == OcOrderCheckBoxEnum.CHECKBOX_OUT_OF_STOCK.getVal()) {
                                    //判断物流是否满足仓库物流策略
//                                    boolean flag= expressQueryServiceCmd.updateLogisticsNum(ocBorder.getCpCPhyWarehouseId(),cLogisticsId);
//                                    if (!flag){
//                                        failMessage.put("id", id);
//                                        failMessage.put("message", "订单["+ocBorder.getBillNo()+"]不满足仓库物流策略");
//                                        failList.add(failMessage);
//                                        continue;
//                                    }
                                    //获取修改之前的物流名称
                                    String preName = ocBorder.getCpCLogisticsEname();
                                    //获取修改之后的物流名称
                                    String afterName = cpLogistics.getEname();
                                    ocBorder.setCpCLogisticsId(cLogisticsId);
                                    ocBorder.setCpCLogisticsEcode(cpLogistics.getEcode());
                                    ocBorder.setCpCLogisticsEname(cpLogistics.getEname());
                                    // 自动打标：物流改单
                                    ocBorder.setIsModifiedOrder(1);
                                    //寻源失败标识
                                    ocBorder.setIsOccupyStockFail(OcBOrderConst.IS_STATUS_IN);
                                    //去除订单异常标签
                                    ocBorder.setIsException(OcBOrderConst.IS_STATUS_IN);
                                    ocBorder.setExceptionType("");
                                    ocBorder.setExceptionExplain("");
                                    this.updateOrderInfo(id, ocBorder, loginUser);
                                    count++;
                                    //更新订单日志
                                    omsOrderLogService.addUserOrderLog(id, billNo, OrderLogTypeEnum.LOGISTICS_UPDATE.getKey(), "物流公司：修改前：" + preName + "，修改后：" + afterName + "", "", "", loginUser);
                                    //配货中
                                } else if (status == OcOrderCheckBoxEnum.CHECKBOX_IN_DISTRIBUTION.getVal()) {
                                    //WMS撤回状态为已撤回
                                    if (wmsCancelStatus == OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_YES.toInteger()) {
                                        try {
                                            count = ApplicationContextHandle.getBean(UpdateOrderInfoService.class).logisticDealWmsCanceled(loginUser, failList, cpLogistics, count, failMessage, ocBorder);
                                        } catch (Exception e) {
                                            e.printStackTrace();
                                        }
                                        //WMS撤回状态为未撤回或撤回失败
                                    } else {
                                        failMessage.put("id", id);
                                        failMessage.put("message", "" + id + "订单在WMS中未取消，不允许修改物流公司，建议先撤回WMS再进行修改物流公司！");
                                        failList.add(failMessage);
                                        continue;
                                    }
                                }
                            } else {
                                failMessage.put("billNo", id);
                                failMessage.put("message", "物流公司与当前一致，无需修改！");
                                failList.add(failMessage);
                                continue;
                            }

                        } else {
                            failMessage.put("billNo", id);
                            failMessage.put("message", "当前订单其他人在操作，请稍后再试!");
                            failList.add(failMessage);
                            continue;
                        }
                    } catch (Exception e) {
                        log.error(LogUtil.format("修改物流公司异 错误信息{}"), Throwables.getStackTraceAsString(e));
                    } finally {
                        redisLock.unlock();
                    }
                }
                if (count == ids.size()) {
                    holder.setMessage("修改成功");
                    data.put("prompt_data", failList);
                } else {
                    holder.setMessage("成功" + count + "条,失败" + (ids.size() - count) + "条");
                    data.put("prompt_data", failList);
                    holder.setData(data);
                }
            } else {
                holder.setCode(ResultCode.FAIL);
                holder.setMessage("当前物流公司无效，请重新录入！");
            }
        } else {
            holder.setCode(ResultCode.FAIL);
            holder.setMessage("请选择需要修改物流记录！");
        }
        return holder;
    }

    private CpLogistics getCpLogistics(Long cLogisticsId, Long id) {
        ValueHolderV14<List<CpLogistics>> holderV14 = cpRpcService.queryLogisticsByIdsIgnoreStatus(Lists.newArrayList(cLogisticsId));
        if (!holderV14.isOK()) {
            throw new NDSException(id + "查询物流公司信息失败，请稍后重试！");
        }
        List<CpLogistics> logistics = holderV14.getData();
        if (CollectionUtils.isEmpty(logistics)) {
            throw new NDSException(id + "未查询到物流公司信息!");
        }
        CpLogistics logi = logistics.get(0);
        if (Objects.isNull(logi)) {
            throw new NDSException(id + ",未查询到物流公司信息!");
        }
        return logi;
    }

    /**
     * wms已撤回处理
     *
     * @param loginUser   用户信息
     * @param failList    失败信息集
     * @param cpLogistics 物流信息
     * @param count       成功条数
     * @param failMessage 失败信息
     * @param ocBorder    订单
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer logisticDealWmsCanceled(User loginUser, List<Object> failList, CpLogistics cpLogistics, Integer count, JSONObject failMessage, OcBOrder ocBorder) {
        //调用复制订单服务，生成新订单,订单状态为缺货（2）且物流公司为当前录入值
        OcBOrderRelation ocBOrderRelation = omsOrderSplitService.getOcBOrderRelation(ocBorder.getId(), true);
        OcBOrder newOcBorder = ocBOrderRelation.getOrderInfo();
        if (newOcBorder != null) {
            newOcBorder.setOrderStatus(OcOrderCheckBoxEnum.CHECKBOX_OUT_OF_STOCK.getVal());
            newOcBorder.setCpCLogisticsId(cpLogistics.getId());
            newOcBorder.setCpCLogisticsEcode(cpLogistics.getEcode());
            newOcBorder.setCpCLogisticsEname(cpLogistics.getEname());
            updateOrderInfo(newOcBorder.getId(), newOcBorder, loginUser);
            //调用作废订单服务
            ValueHolderV14 invoildOutOrder = omsOrderCancellationService.doInvoildOutOrder(ocBorder, loginUser);
            if (invoildOutOrder.getCode() != 0) {
                failMessage.put("id", ocBorder.getId());
                failMessage.put("message", "订单作废失败，不能修改物流公司(调用作废订单服务导致," + invoildOutOrder.getMessage() + ")");
                failList.add(failMessage);
                throw new NDSException("订单作废失败");
            }
            //插入作废订单日志
            omsOrderLogService.addUserOrderLog(ocBorder.getId(), ocBorder.getBillNo(), OrderLogTypeEnum.SYSTEM_ABOLITION.getKey(), "订单作废成功", "", "", loginUser);
            //新单调用占用库存服务
            ValueHolderV14 valueHolderV14 = sgRpcService.querySearchStockAndModifyAddress(ocBOrderRelation, loginUser);
            if (valueHolderV14.isOK()) {
                //插入占库日志
                omsOrderLogService.addUserOrderLog(newOcBorder.getId(), newOcBorder.getBillNo(), OrderLogTypeEnum.STOCK_OCCUPY_SUCCESS.getKey(), "占用库存成功", "", "", loginUser);
                //插入复制订单日志
                omsOrderLogService.addUserOrderLog(newOcBorder.getId(), newOcBorder.getBillNo(), OrderLogTypeEnum.ORDER_ADD.getKey(), "复制订单成功，由配货中订单" + ocBorder.getId() + "修改物流作废生成新单" + newOcBorder.getId() + "", "", "", loginUser);
                count++;
            } else if (valueHolderV14.getCode() == 3) {
                //插入占库日志
                omsOrderLogService.addUserOrderLog(newOcBorder.getId(), newOcBorder.getBillNo(), OrderLogTypeEnum.STOCK_OCCUPY_FAIL.getKey(), "因缺少库存，占用库存失败", "", "", loginUser);
                //插入复制订单日志
                omsOrderLogService.addUserOrderLog(newOcBorder.getId(), newOcBorder.getBillNo(), OrderLogTypeEnum.ORDER_ADD.getKey(), "复制订单成功，由配货中订单" + ocBorder.getId() + "修改物流作废生成新单" + newOcBorder.getId() + "", "", "", loginUser);
                count++;
            } else {
                //调用是失败或者占库失败
                failMessage.put("id", newOcBorder.getId());
                failMessage.put("message", "" + newOcBorder.getId() + "订单无库存，不允许修改物流公司！(调用占用库存服务导致," + valueHolderV14.getMessage() + ")");
                failList.add(failMessage);
                throw new NDSException("新单占用库存失败");
            }
        }
        return count;
    }

    /**
     * 修改仓库
     *
     * @param ids          订单编号
     * @param warehouseId  实体仓id
     * @param updateRemark 改仓原因
     * @param loginUser    用户信息
     * @return 返回信息
     * @throws NDSException 异常
     */
    public ValueHolderV14 updateWarehouse(List<Long> ids, Long shareStoresId, String shareStoresEcode, Long warehouseId, String warehouseEcode, String updateRemark, User loginUser) throws NDSException {

        ValueHolderV14 vh = modifyWarehouseService.modifyWarehouse(ids, warehouseId, updateRemark, loginUser);
        if (vh != null) {
            return vh;
        }
        ValueHolderV14 holder = new ValueHolderV14();
        JSONObject data = new JSONObject();
        JSONObject coulumn = new JSONObject();
        JSONObject coulumn2 = new JSONObject();
        List<Object> columnList = new ArrayList<>();
        coulumn.put("key", "billNo");
        coulumn.put("title", "订单编号");
        columnList.add(coulumn);
        coulumn2.put("key", "message");
        coulumn2.put("title", "错误信息");
        columnList.add(coulumn2);
        data.put("columns", columnList);
        List<Object> failList = new ArrayList<>();
        //调用接口查询仓库信息
        CpCPhyWarehouse warehouse = null;
        try {
            warehouse = cpRpcService.queryByWarehouseId(warehouseId);
        } catch (Exception e) {
            log.error(LogUtil.format("调用cp仓库信息出错 错误信息{}"), Throwables.getStackTraceAsString(e));
        }
        if (CollectionUtils.isNotEmpty(ids) && warehouseId != null) {
            Boolean skipSgAjFlag = false;
            // 用来作为下次继续调用的标识 （看内面包含不包含1  用来调过库存不满足判断）
            if (ids.contains(-1L)) {
                ids.remove(-1L);
                skipSgAjFlag = true;
            }
            //录入仓库是否在仓库档案中存在且启用
            if (warehouseId != null && warehouse != null) {
                //判断界面录入的“实体仓库”是否为【店铺同步库存策略】中存在且启用
//                List<Long> storeIds = cpRpcService.queryStoreList(warehouseId);
//                boolean check = stRpcService.checkByStoreIds(storeIds);
//                if (check) {
                Integer count = 0;
//                    if (ids.size() > 1) {
//                        skipSgAjFlag = true;
//                    }
                for (Long id : ids) {
                    JSONObject failMessage = new JSONObject();
                    String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(id);
                    RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
                    try {
                        if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                            OcBOrder ocBorderDto = orderMapper.selectById(id);
                            if (ocBorderDto != null) {
                                Integer status = ocBorderDto.getOrderStatus();
                                //订单状态  1,待审核 2,待寻源
                                List<Integer> list = Arrays.asList(OcOrderCheckBoxEnum.CHECKBOX_UNCONFIRMED.getVal(), OcOrderCheckBoxEnum.CHECKBOX_OUT_OF_STOCK.getVal());
                                if (!list.contains(status)) {
                                    failMessage.put("billNo", id);
                                    failMessage.put("message", id + "当前状态异常，不允许修改发货仓库，建议刷新页面查看最新数据！");
                                    failList.add(failMessage);
                                    continue;
                                }
                                boolean jitxDealerYYWarehouse = ocBJitxDealerOrderTaskService.isJitxDealerYYWarehouse(warehouseId);
                                if (jitxDealerYYWarehouse) {
//                                              //判断YY寻仓结果表中已存在记录，则要排除该YY仓库
                                    List<IpBJitxDeliveryRecord> ipBJitxDeliveryRecords = deliveryRecordMapper.selectList(new LambdaQueryWrapper<IpBJitxDeliveryRecord>()
                                            .eq(IpBJitxDeliveryRecord::getTid, ocBorderDto.getTid()));
//                                                    .in(IpBJitxDeliveryRecord::getYyStoreCode, storeCodeList));
                                    if (CollectionUtils.isNotEmpty(ipBJitxDeliveryRecords)) {
                                        failMessage.put("billNo", id);
                                        failMessage.put("message", id + "YY寻仓结果表中已存在记录,不允许修改为当前仓库");
                                        failList.add(failMessage);
                                        continue;
                                    }
                                }
                                Long preWarehourseId = ocBorderDto.getCpCPhyWarehouseId();
                                String preWarehourseCode = ocBorderDto.getCpCPhyWarehouseEcode();
                                String preWarehourse = ocBorderDto.getCpCPhyWarehouseEname();
                                preWarehourse = StringUtils.isEmpty(preWarehourse) ? "空" : preWarehourse;
                                List<OcBOrderItem> items = ocBOrderItemMapper.selectUnSuccessRefund(id);
//                                        //进行指定实体仓占用
//                                        SgOmsPhyStorageOutRequest sgOmsPhyStorageOutItemRequest = buildSgOmsPhyStorageOutRequest(ocBorderDto,items,shareStoresId,shareStoresEcode,warehouseId,warehouseEcode);
//                                        ValueHolderV14 v14 =sgRpcService.phyStorageOut(sgOmsPhyStorageOutItemRequest);

                                //调用指定商品释放库存服务
                                Integer orderStatus = ocBorderDto.getOrderStatus();
                                if (OmsOrderStatus.UNCONFIRMED.toInteger().equals(orderStatus)) {
                                    SgOmsShareOutRequest request = cancelOrderMergeService.buildSgOmsShareOutRequest(ocBorderDto, items, loginUser);
                                    ValueHolderV14 v14 = sgRpcService.voidSgOmsShareOut(request, ocBorderDto, items);

                                    if (v14.isOK()) {
                                        omsOrderLogService.addUserOrderLog(ocBorderDto.getId(), ocBorderDto.getBillNo(), OrderLogTypeEnum.RELEASE_STOCK_SUCCESS.getKey(), "释放库存成功", "", "", loginUser);
                                        //更新零售发货单占用状态
                                        updateOrderStatus(ocBorderDto, loginUser);
                                    } else {
                                        throw new NDSException("释放库存失败！");
                                    }
                                }

                                ocBorderDto.setCpCPhyWarehouseId(warehouseId);
                                ocBorderDto.setOrderStatus(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger());
                                ocBorderDto.setCpCPhyWarehouseEcode(warehouseEcode);
                                ocBorderDto.setCpCPhyWarehouseEname(warehouse.getEname());
//                                            ocBorderDto.setIsLackstock(OcBOrderConst.IS_STATUS_IN);
                                // 需要传有地址信息的订单参数
                                MD5Util.encryptOrderInfo4Merge(ocBorderDto);
                                ocBorderDto.setOrderEncryptionCode(ocBorderDto.getOrderEncryptionCode());
                                //1、若【零售发货单】的【来源平台】=POS时，则对订单打标【o2o】；
                                //2、若【零售发货单】的【来源平台】非POS时，若订单占用的【发货仓库】的对应【仓库类型】=门店，则对订单打标【o2o】；
                                if (sgOccupiedInventoryService.isO2OOrder(ocBorderDto)) {
                                    ocBorderDto.setIsO2oOrder(OcBOrderConst.IS_STATUS_IY);
                                } else {
                                    ocBorderDto.setIsO2oOrder(OcBOrderConst.IS_STATUS_IN);
                                }
                                //埋点占用成功
//                                            ocBorderDto.setOccupySuccessDate(new Date());
//                                            //分物流
//                                            omsOrderDistributeLogisticsService.distributeLogisticsDistributeLogistics(ocBorderDto);
                                orderMapper.updateById(ocBorderDto);


                                //卡单不释放
                                if (!YesNoEnum.Y.getVal().equals(ocBorderDto.getIsDetention())) {
                                    //加入占单表
                                    omsOccupyTaskService.addOcBOccupyTask(ocBorderDto, null, 0);
                                }

                                try {
                                    //调用日志服务
                                    omsOrderLogService.addUserOrderLog(id, ocBorderDto.getBillNo(), OrderLogTypeEnum.WAREHOUSE_UPDATE.getKey(), "发货仓库公司：修改前：" + preWarehourse + "，修改后：" + warehouse.getEname() + ", 改仓原因：" + updateRemark, "", "", loginUser);
                                } catch (NDSException e) {
                                    log.error("调用服务出错" + e);
                                }
//                                 //todo 后续可能要仓库拆单
                                count++;

                                String billNo = ocBorderDto.getBillNo();
//
                                //待审核、缺货
                                if ((status == OcOrderCheckBoxEnum.CHECKBOX_UNCONFIRMED.getVal() ||
                                        status == OcOrderCheckBoxEnum.CHECKBOX_OUT_OF_STOCK.getVal())) {
                                    String jitWarehouseEcode = null;
                                    // 若是JITX平台订单调用【创建改仓申请单接口】
                                    if (PlatFormEnum.VIP_JITX.getCode().equals(ocBorderDto.getPlatform())) {
                                        StCVipcomJitxWarehouse jitxWarehouse = jitxWarehouseService.queryJitxCapacity(ocBorderDto.getCpCShopId(),
                                                warehouseId, null);
                                        log.debug("JITX修改仓库，根据实体仓ID和店铺ID查询仓库编码：" + jitxWarehouse + "订单id为：" + ocBorderDto.getId());
                                        if (jitxWarehouse == null) {
                                            failMessage.put("id", id);
                                            failMessage.put("message", "未查询到唯品会对照仓库数据");
                                            failList.add(failMessage);
                                            continue;
                                        }
                                        jitWarehouseEcode = jitxWarehouse.getVipcomWarehouseEcode();
                                        if (!YesNoEnum.Y.getVal().equals(ocBorderDto.getIsStoreDelivery())) {
                                            jitWarehouseEcode = jitxWarehouse.getVipcomUnshopWarehouseEcode();
                                        }
                                        if (StringUtils.isEmpty(jitWarehouseEcode)) {
                                            failMessage.put("id", id);
                                            failMessage.put("message", "实体仓对应的【唯品会仓库编码】不能为空。");
                                            failList.add(failMessage);
                                            continue;
                                        }
                                        // 设定redisJITX修改仓库标识，更新数据库JITX修改仓库标识
//                                        this.setJITXRedisChangeWarehouseFlag(ocBorderDto, loginUser);
                                        // 创建JITX订单改仓日志表
                                        ValueHolderV14 valueHolderV14 = ocBJitxModifyWarehouseLogService.createByOrder(
                                                ocBorderDto, false, preWarehourseId, preWarehourseCode,
                                                preWarehourse, jitWarehouseEcode, loginUser);
                                        String logMessage = "创建JITX订单改仓中间表失败";
                                        if (valueHolderV14.isOK()) {
                                            logMessage = "创建JITX订单改仓中间表成功";
                                        }
                                        omsOrderLogService.addUserOrderLog(id, billNo, OrderLogTypeEnum.JITX_ORDER_UPDATE_WAREHOUSE.getKey(),
                                                logMessage + valueHolderV14.getMessage(), "", valueHolderV14.getMessage(), loginUser);
                                    }
                                }
//
//
//
                            } else {
                                failMessage.put("billNo", id);
                                failMessage.put("message", "发货仓库与当前一致，无需修改！");
                                failList.add(failMessage);
                                continue;
                            }
//
                        } else {
                            failMessage.put("billNo", id);
                            failMessage.put("message", "当前订单其他人在操作，请稍后再试!");
                            failList.add(failMessage);
                            continue;
                        }
                    } catch (Exception e) {
                        failMessage.put("billNo", id);
                        failMessage.put("message", e.getMessage());
                        failList.add(failMessage);
                        log.error(LogUtil.format("修改发货仓库异常 错误信息{}", "修改发货仓库异常"), Throwables.getStackTraceAsString(e));
                    } finally {
                        redisLock.unlock();
                    }
                }
                if (count == ids.size()) {
                    holder.setMessage("修改成功");
                    data.put("prompt_data", failList);
                } else {
                    holder.setCode(ResultCode.FAIL);
                    holder.setMessage("成功" + count + "条,失败" + (ids.size() - count) + "条");
                    data.put("prompt_data", failList);
                    holder.setData(data);
                }
            } else {
                holder.setCode(ResultCode.FAIL);
                holder.setMessage("当前发货仓库无效，请重新录入！");
            }
        } else {
            holder.setCode(ResultCode.FAIL);
            holder.setMessage("请选择需要修改仓库记录！");
        }

        return holder;
    }

    /**
     * description:预下沉解析
     *
     * @Author: liuwenjin
     * @Date 2021/10/10 5:45 下午
     */
    private void orderAdvanceSinkParse(OcBOrder order) {
        //执行定金预售预下沉服务
        if (TaoBaoOrderStatus.FRONT_PAID_FINAL_NOPAID.equals(order.getStatusPayStep())) {
            //查询原单明细
            List<OcBOrderItem> ocBOrderItemList = ocBOrderItemMapper.selectOrderItemListOccupy(order.getId());
            OcBOrderRelation orderInfo = new OcBOrderRelation();
            orderInfo.setOrderInfo(order);
            orderInfo.setOrderItemList(ocBOrderItemList);
            omsOrderAdvanceParseService.OmsOrderAdvanceSinkParse(orderInfo, SystemUserResource.getRootUser());
        }
    }

    /**
     * WMS撤回状态为已撤回处理
     *
     * @param loginUser   用户信息
     * @param failList    失败信息集
     * @param warehouse   实体仓信息
     * @param count       成功数量
     * @param failMessage 失败信息
     * @param ocBorderDto 订单信息
     * @return 成功数量
     */
    @Transactional(rollbackFor = Exception.class)

    public Integer warehourseDealWmsCanceled(User loginUser, List<Object> failList, CpCPhyWarehouse
            warehouse, Integer count, JSONObject failMessage, OcBOrder ocBorderDto) {
        //调用复制订单服务，生成新订单,订单状态为缺货（2），发货仓库界面录入值，调用日志服务
        OcBOrderRelation newOrderRelation = omsOrderSplitService.getOcBOrderRelation(ocBorderDto.getId(), true);
        OcBOrder newOcBorder = newOrderRelation.getOrderInfo();
        if (newOcBorder != null) {
            newOcBorder.setCpCPhyWarehouseId(warehouse.getId());
            newOcBorder.setCpCPhyWarehouseEcode(warehouse.getEcode());
            newOcBorder.setCpCPhyWarehouseEname(warehouse.getEname());
            //如果实体仓是o2o仓库，对订单进行打标
            if (StringUtils.equals(warehouse.getWhType(), OcBOrderConst.CP_C_PHY_WAREHOUSE_TYPE)) {
                newOcBorder.setIsO2oOrder(OcBOrderConst.IS_STATUS_IY);
            } else {
                newOcBorder.setIsO2oOrder(OcBOrderConst.IS_STATUS_IN);
            }
            // ocBorderDto.setThirdWarehouseType(warehouse.getThirdWarehouseType());
            newOcBorder.setOrderStatus(OcOrderCheckBoxEnum.CHECKBOX_OUT_OF_STOCK.getVal());
            // 改仓成功 置空物流公司
            newOcBorder.setCpCLogisticsId(0L);
            newOcBorder.setCpCLogisticsEname("");
            newOcBorder.setCpCLogisticsEcode("");
            updateOrderInfo(newOcBorder.getId(), newOcBorder, loginUser);
            //更新后分配物流
            dealLogistic(warehouse.getId(), loginUser, newOcBorder);
            //新单调占用库存服务
            ValueHolderV14 valueHolderV14 = sgRpcService.querySearchStockAndModifyAddress(newOrderRelation, loginUser);
            if (valueHolderV14.isOK()) {
                //插入占库日志
                omsOrderLogService.addUserOrderLog(newOcBorder.getId(), newOcBorder.getBillNo(), OrderLogTypeEnum.STOCK_OCCUPY_SUCCESS.getKey(), "占用库存成功", "", "", loginUser);
            } else if (valueHolderV14.getCode() == 3) {
                //插入占库日志
                omsOrderLogService.addUserOrderLog(newOcBorder.getId(), newOcBorder.getBillNo(), OrderLogTypeEnum.STOCK_OCCUPY_FAIL.getKey(), "因缺少库存，占用库存失败", "", "", loginUser);
            } else {
                //调用失败或者占库失败
                failMessage.put("id", ocBorderDto.getId());
                failMessage.put("message", "修改发货仓库失败！(新单调用占用库存失败导致" + valueHolderV14.getMessage() + ")");
                failList.add(failMessage);
                throw new NDSException("占用库存服务失败");
            }
            //新单日志
            omsOrderLogService.addUserOrderLog(newOcBorder.getId(), newOcBorder.getBillNo(), OrderLogTypeEnum.ORDER_ADD.getKey(), "复制订单成功，由配货中订单" + ocBorderDto.getId() + "修改仓库作废生成新单" + newOcBorder.getId() + "", "", "", loginUser);
            //调用作废订单服务
            ValueHolderV14 doInvoildOutOrder = omsOrderCancellationService.doInvoildOutOrder(ocBorderDto, loginUser);
            if (doInvoildOutOrder.getCode() != 0) {
                //调用失败，返回“修改发货仓库失败”,回滚新增数据
                failMessage.put("id", ocBorderDto.getId());
                failMessage.put("message", "修改发货仓库失败！(调用作废订单服务失败导致," + doInvoildOutOrder.getMessage() + ")");
                failList.add(failMessage);
                throw new NDSException("调用作废订单服务失败");
            }
            //旧单作废日志
            omsOrderLogService.addUserOrderLog(ocBorderDto.getId(), ocBorderDto.getBillNo(), OrderLogTypeEnum.SYSTEM_ABOLITION.getKey(), "修改实体仓配货中作废订单" + ocBorderDto.getId() + "(" + ocBorderDto.getId() + "为原订单号)", "", "", loginUser);
            count++;
        }
        return count;
    }

    /**
     * <AUTHOR>
     * @Date 18:02 2021/7/22
     * @Description 封装对应的数据
     */
    public SgOmsPhyStorageOutRequest buildSgOmsPhyStorageOutRequest(OcBOrder ocBorderDto, List<OcBOrderItem> items, Long shareStoresId, String shareStoresEcode, Long warehouseId, String warehouseEcode) {
        SgOmsPhyStorageOutRequest sgOmsPhyStorageOutRequest = new SgOmsPhyStorageOutRequest();
        sgOmsPhyStorageOutRequest.setRetailBillNo(ocBorderDto.getBillNo());
        sgOmsPhyStorageOutRequest.setCpCShopId(ocBorderDto.getCpCShopId());
        sgOmsPhyStorageOutRequest.setCpCShopEcode(ocBorderDto.getCpCShopEcode());
        sgOmsPhyStorageOutRequest.setCpCPhyWarehouseId(warehouseId);
        sgOmsPhyStorageOutRequest.setCpCPhyWarehouseEcode(warehouseEcode);
        List<Long> sgCShareStoreIds = new ArrayList<>();
        sgCShareStoreIds.add(shareStoresId);
        sgOmsPhyStorageOutRequest.setSgCShareStoreIds(sgCShareStoreIds);
        sgOmsPhyStorageOutRequest.setSgCShareStoreEcode(shareStoresEcode);
        sgOmsPhyStorageOutRequest.setReceiverName(ocBorderDto.getReceiverName());
        //sgOmsPhyStorageOutRequest.setReceiverDate();
        sgOmsPhyStorageOutRequest.setReceiverPhone(ocBorderDto.getReceiverPhone());
        sgOmsPhyStorageOutRequest.setReceiverAddress(ocBorderDto.getReceiverAddress());
        sgOmsPhyStorageOutRequest.setReceiverMobile(ocBorderDto.getReceiverMobile());

        sgOmsPhyStorageOutRequest.setCpCRegionProvinceId(ocBorderDto.getCpCRegionProvinceId());
        sgOmsPhyStorageOutRequest.setCpCRegionProvinceEname(ocBorderDto.getCpCRegionProvinceEname());
        sgOmsPhyStorageOutRequest.setCpCRegionProvinceEcode(ocBorderDto.getCpCRegionProvinceEcode());

        sgOmsPhyStorageOutRequest.setCpCRegionCityId(ocBorderDto.getCpCRegionCityId());
        sgOmsPhyStorageOutRequest.setCpCRegionCityEcode(ocBorderDto.getCpCRegionCityEcode());
        sgOmsPhyStorageOutRequest.setCpCRegionCityEname(ocBorderDto.getCpCRegionCityEname());

        sgOmsPhyStorageOutRequest.setSourceBillId(ocBorderDto.getId());
        sgOmsPhyStorageOutRequest.setSourceBillNo(ocBorderDto.getBillNo());
        sgOmsPhyStorageOutRequest.setSourceBillDate(ocBorderDto.getOrderDate());

        sgOmsPhyStorageOutRequest.setTid(ocBorderDto.getSourceCode());


        List<SgOmsPhyStorageOutItemRequest> itemRequestList = new ArrayList<>();
        for (OcBOrderItem item : items) {
            SgOmsPhyStorageOutItemRequest sgOmsPhyStorageOutItemRequest = new SgOmsPhyStorageOutItemRequest();
            sgOmsPhyStorageOutItemRequest.setRetailItemId(item.getId());
            sgOmsPhyStorageOutItemRequest.setQty(item.getQty());
            sgOmsPhyStorageOutItemRequest.setPsCSkuId(item.getPsCSkuId());
            sgOmsPhyStorageOutItemRequest.setSkuId(item.getSkuNumiid());
            sgOmsPhyStorageOutItemRequest.setPsCSkuEcode(item.getPsCSkuEcode());
            itemRequestList.add(sgOmsPhyStorageOutItemRequest);
        }
        sgOmsPhyStorageOutRequest.setItemRequestList(itemRequestList);
        return sgOmsPhyStorageOutRequest;
    }


    /**
     * 更新完成后，分配物流
     *
     * @param warehouseId 发货仓
     * @param loginUser   用户信息
     * @param ocBOrder    订单
     */
    private boolean dealLogistic(Long warehouseId, User loginUser, OcBOrder ocBOrder) {
        Long id = ocBOrder.getId();
        //liqb 去掉仓库物流策略，直接调用分物流服务
//        Long cpCLogisticsId = ocBOrder.getCpCLogisticsId();
//        CpLogistics cpLogistics = cpLogisticsInfo.checkCpLogistic(cpCLogisticsId);
        return this.distributeLogistics(loginUser, id, ocBOrder);
        /*//物流公司存在
        if (cpCLogisticsId != null && cpLogistics != null) {
            Boolean checkLogisticRules = expressAllocationCheckCmd.checkLogisticRules(warehouseId, cpCLogisticsId);
            //当前“物流公司”是否在“发货仓库”对应的【物流分配比例设置】明细中不存在或者不启用
            if (!checkLogisticRules) {
                //调用分配物流公司服务更新覆盖原物流公司信息
                this.distributeLogistics(loginUser, id, ocBOrder);
            }
        } else {
            //物流公司不存在,则调用分配物流公司服务
            this.distributeLogistics(loginUser, id, ocBOrder);
        }*/
    }

    /**
     * 分配物流公司
     *
     * @param loginUser 操作用户的信息
     * @param id        订单id
     * @param ocBorder  订单实体
     */
    public boolean distributeLogistics(User loginUser, Long id, OcBOrder ocBorder) {
        try {
            OcBOrderRelation orderInfo = omsOrderService.selectOmsOrderInfo(id);
            CpCLogistics cpLogistics = omsOrderDistributeLogisticsService.distributeLogistics(orderInfo);
            if (cpLogistics != null) {
                ocBorder.setCpCLogisticsId(cpLogistics.getId());
                ocBorder.setCpCLogisticsEcode(cpLogistics.getEcode());
                ocBorder.setCpCLogisticsEname(cpLogistics.getEname());
                this.updateOrderInfo(id, ocBorder, loginUser);
                return true;
            } else {
                UpdateWrapper<OcBOrder> wrapper = new UpdateWrapper<>();
                wrapper.set("CP_C_LOGISTICS_ID", 0L).set("CP_C_LOGISTICS_ECODE", "").set("CP_C_LOGISTICS_ENAME", "");
                wrapper.eq("ID", ocBorder.getId());
                this.wrapperUpdateOrderInfo(loginUser, wrapper);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("分配物流失败,异常信息为:{}"), Throwables.getStackTraceAsString(e));
        }
        return false;
    }

    /**
     * 更新方法
     *
     * @param id        订单id
     * @param ocBOrder  订单实体数据
     * @param loginUser 用户信息
     */
    public void updateOrderInfo(Long id, OcBOrder ocBOrder, User loginUser) {
        try {
            ocBOrder.setModifierename(loginUser.getEname());
            ocBOrder.setModifiername(loginUser.getName());
            ocBOrder.setModifieddate(new Date());
            ocBOrder.setModifierid(loginUser.getId().longValue());
            //更新订单主表
            int i = orderMapper.updateById(ocBOrder);
            //将数据重新推送到ES
            //   SpecialElasticSearchUtil.indexDocument(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME, OcElasticSearchIndexResources.OC_B_ORDER_TYPE_NAME, ocBOrder, id);

        } catch (Exception e) {
            log.error(LogUtil.format("更新异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));

        }
    }

    public void wrapperUpdateOrderInfo(User loginUser, UpdateWrapper<OcBOrder> wrapper) {
        try {
            OcBOrder order = new OcBOrder();
            BaseModelUtil.setupUpdateParam(order, loginUser);
            order.setModifierename(loginUser.getEname());
            int update = orderMapper.update(order, wrapper);
        } catch (Exception e) {
            log.error(LogUtil.format("wrapper更新异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
        }
    }


    /**
     * 更新方法
     *
     * @param id        订单id
     * @param ocBOrder  订单实体数据
     * @param loginUser 用户信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateOrderInfoForUpdateWarehouse(Long id, OcBOrder ocBOrder, List<OcBOrderItem> items, User loginUser) {
        try {
            ocBOrder.setModifierename(loginUser.getEname());
            ocBOrder.setModifiername(loginUser.getName());
            ocBOrder.setModifieddate(new Date());
            ocBOrder.setModifierid(loginUser.getId().longValue());
            ocBOrder.setIsModifiedOrder(1);
            //更新订单主表
            orderMapper.updateById(ocBOrder);
            //将数据重新推送到ES
            //  SpecialElasticSearchUtil.indexDocument(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME, OcElasticSearchIndexResources.OC_B_ORDER_TYPE_NAME, ocBOrder, id);

            for (OcBOrderItem item : items) {
                OcBOrderItem updateOrderItem = new OcBOrderItem();
                updateOrderItem.setQtyLost(BigDecimal.ZERO);
                UpdateWrapper<OcBOrderItem> wrapper = new UpdateWrapper<>();
                wrapper.eq("oc_b_order_id", id).eq("id", item.getId());
                ocBOrderItemMapper.update(updateOrderItem, wrapper);
                item.setQtyLost(BigDecimal.ZERO);
                //SpecialElasticSearchUtil.indexDocument(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME, OcElasticSearchIndexResources.OC_B_ORDER_ITEM_TYPE_NAME, item, item.getId(), id);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("更新异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));

        }
    }

    /**
     * 根据店铺策略的发货物流公司过滤
     *
     * @param cpLogistics
     * @param shopId
     * @return
     */
    public List<CpLogistics> filterCpLogistics(List<CpLogistics> cpLogistics, Long shopId) {
        if (CollectionUtils.isEmpty(cpLogistics)) {
            return cpLogistics;
        }
        StCShopStrategyLogisticsItemResult shopLogistics = stRpcService.queryShopStrategyLogisticsList(shopId);
        if (shopLogistics == null) {
            throw new NDSException("未查询到物流公司信息！");
        }
        Integer logisticsType = shopLogistics.getLogisticsType();
        if (logisticsType.intValue() == 0) {
            //不做要求
            return cpLogistics;
        }
        List<StCShopStrategyLogisticsItem> itemList = shopLogistics.getLogisticsItemList();
        if (CollectionUtils.isEmpty(itemList)) {
            throw new NDSException("店铺策略没有维护物流公司！");
        }
        List<Long> logisticsIdList = itemList.stream().map(StCShopStrategyLogisticsItem::getCpCLogisticsId).collect(Collectors.toList());
        if (logisticsType.intValue() == 2) {
            //排除所选物流公司
            cpLogistics = cpLogistics.stream().filter(x -> !logisticsIdList.contains(x.getId())).collect(Collectors.toList());
        } else if (logisticsType.intValue() == 1) {
            //可发所选物流公司
            cpLogistics = cpLogistics.stream().filter(x -> logisticsIdList.contains(x.getId())).collect(Collectors.toList());
        }
        return cpLogistics;
    }

    public List<CpLogistics> getLogistics(List<StCWarehouseLogisticStrategyResult> warehouseLogisticStrategyResults, List<CpLogistics> logistics) {
        if (CollectionUtils.isNotEmpty(warehouseLogisticStrategyResults)) {
            List<Long> logisticsIdList = warehouseLogisticStrategyResults.stream().map(StCWarehouseLogisticStrategyResult::getCpCLogisticsId).collect(Collectors.toList());
            logistics = logistics.stream().filter(x -> logisticsIdList.contains(x.getId())).collect(Collectors.toList());
        }
        return logistics;
    }


    /**
     * 筛选列表
     * <p>
     * flag 标记
     * id   实体仓库id或者店铺id
     * num  页数
     * size 每页大小
     * 结果数据
     */
    public JSONObject queryList(User usr, JSONObject jsn) {
        if (jsn == null) {
            throw new NDSException("参数丢失");
        }

        int flag = jsn.getIntValue("flag");
        Long id = jsn.getLong("id");
        int num = jsn.getIntValue("num");
        int size = jsn.getIntValue("size");
        String ids = jsn.getString("ids");
        String name = jsn.getString("inputValue");
        //修改物流公司：skx项目无仓库物流设置，修改为可选择所有物流公司
        ValueHolderV14<List<CpLogistics>> valueHolderV14 = cpRpcService.queryLogisticsIsY();
        AssertUtil.assertException(!valueHolderV14.isOK(), valueHolderV14.getMessage());
        List<CpLogistics> logisticsList = Optional.ofNullable(valueHolderV14.getData()).orElse(new ArrayList<>());
        JSONObject response = new JSONObject();
        List<Object> list = new ArrayList<>();
        if (flag == 1) {
            //查询仓库物流策略，多个单据，取集合的交集，没有仓库，没有配置物流，查不到的默认就显示所有的。
            if (StringUtils.isNotEmpty(ids)) {
                String[] strings = ids.split(",");
                List<String> idList = Arrays.asList(strings);
                //单条明细
                if (idList.size() == 1) {
                    OcBOrder ocBOrder = orderMapper.selectById(idList.get(0));
                    //List<CpLogistics> rpcList = expressQueryServiceCmd.selectLogisticsByWarehousId(ocBOrder.getCpCPhyWarehouseId());
                    if (ocBOrder.getCpCPhyWarehouseId() != null) {
                        List<StCWarehouseLogisticStrategyResult> warehouseLogisticStrategyResults = stRpcService.getWarehouseExpress(ocBOrder.getCpCPhyWarehouseId());
                        logisticsList = getLogistics(warehouseLogisticStrategyResults, logisticsList);
                    }
                    //根据店铺策略设置的物流公司过滤
                    logisticsList = filterCpLogistics(logisticsList, ocBOrder.getCpCShopId());
                } else {
                    List<CpLogistics> logistics = new ArrayList<>();
                    List<OcBOrder> orderList = orderMapper.selectByIdsStrList(idList);

                    Set<Long> weaIds = new HashSet<>();
                    for (OcBOrder ocBOrder : orderList) {
                        List<CpLogistics> logisticsListTemp = new ArrayList<>();
                        logisticsListTemp = logisticsList;
                        Long shopId = ocBOrder.getCpCShopId();
                        if (ocBOrder.getCpCPhyWarehouseId() != null) {
                            weaIds.add(ocBOrder.getCpCPhyWarehouseId());
                            //List<CpLogistics> rpcList = expressQueryServiceCmd.selectLogisticsByWarehousId(ocBOrder.getCpCPhyWarehouseId());
                            List<StCWarehouseLogisticStrategyResult> warehouseLogisticStrategyResults = stRpcService.getWarehouseExpress(ocBOrder.getCpCPhyWarehouseId());
                            logisticsListTemp = getLogistics(warehouseLogisticStrategyResults, logisticsListTemp);
                            //根据店铺策略设置的物流公司过滤
                            List<CpLogistics> rpcList = filterCpLogistics(logisticsListTemp, shopId);
                            logistics.addAll(rpcList);
                        }
                    }
                    if (CollectionUtils.isNotEmpty(logistics)) {
                        logistics = logistics.stream().distinct().collect(Collectors.toList());
                        logisticsList = logistics;
                    }
                }
            }
            try {
                for (CpLogistics cpLogistic : logisticsList) {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("id", cpLogistic.getId());
                    jsonObject.put("ename", cpLogistic.getEname());
                    jsonObject.put("ecode", cpLogistic.getEcode());
                    jsonObject.put("shortName", cpLogistic.getShortName());
                    list.add(jsonObject);
                }
            } catch (IndexOutOfBoundsException e) {
                //当不够一页或者总页数不是也尺寸的整数倍时，集合会越界
                log.error(LogUtil.format("获取列表越界,异常信息为:{}"), Throwables.getStackTraceAsString(e));

            } catch (Exception e) {
                log.error(LogUtil.format("获取列表失败,异常信息为:{}"), Throwables.getStackTraceAsString(e));
                response.put("code", ResultCode.FAIL);
                response.put("message", "获取列表失败，请稍后重试");
                return response;
            }
            response.put("data", list);
            //不做分页，只显示一页
            response.put("count", 1);
            response.put("code", ResultCode.SUCCESS);
        } else {
            final String permissionFlag = "CP_C_WAREHOUSE_ID";
            List<Long> datas = Permissions.getWritableDatas(permissionFlag, usr.getId());
            boolean hasPm = CollectionUtils.isNotEmpty(datas);
            //修改发货仓库
            List<CpCPhyWarehouse> cpCPhyWarehouses = cpRpcService.queryWarehouseByShopIdAndName(id, name);
            if (CollectionUtils.isEmpty(cpCPhyWarehouses)) {
                response.put("message", "获取列表为空，请稍后重试");
                response.put("code", ResultCode.FAIL);
                return response;
            }
            int listSize = cpCPhyWarehouses.size();
            try {
                for (int i = (num - 1) * size; i < size * num; i++) {
                    CpCPhyWarehouse warehouse = cpCPhyWarehouses.get(i);
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("id", warehouse.getId());
                    jsonObject.put("ecode", warehouse.getEcode());
                    jsonObject.put("ename", warehouse.getEname());
                    if (usr.isAdmin()) {
                        list.add(jsonObject);
                        continue;
                    }
                    if (hasPm && datas.contains(warehouse.getId())) {
                        list.add(jsonObject);
                    }
                }
            } catch (IndexOutOfBoundsException e) {
                //当不够一页或者总页数不是也尺寸的整数倍时，集合会越界
                log.error(LogUtil.format("获取列表越界,异常信息为:{}"), Throwables.getStackTraceAsString(e));

            } catch (Exception e) {
                log.error(LogUtil.format("获取列表失败,异常信息为:{}"), Throwables.getStackTraceAsString(e));
                response.put("message", "获取列表失败，请稍后重试");
                response.put("code", ResultCode.FAIL);
                return response;
            }
            response.put("data", list);
            response.put("count", listSize);
            response.put("code", ResultCode.SUCCESS);
        }
        return response;
    }

    /**
     * 查询需要重新分配物流的订单信息
     *
     * @param jsonStr
     * @param user
     * @param pem
     * @return ValueHolderV14
     */
    public ValueHolderV14 reallocateLogistics(String jsonStr, User user, UserPermission pem) {
        ValueHolderV14 vh = new ValueHolderV14<>();
        vh.setCode(ResultCode.SUCCESS);
        vh.setMessage("查询成功");
        if (null == jsonStr) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("参数为空");
            return vh;
        }
        JSONObject whereKeys = new JSONObject();
        JSONObject filterKeys = new JSONObject();
        JSONObject childsKeys = new JSONObject();
        ocBOrderListQueryService.handlerSqlConditionQuery(jsonStr, whereKeys, filterKeys, childsKeys);
        ocBOrderListQueryService.filterSearchCondition(pem, whereKeys);
        //ES查询
        JSONArray ja = new JSONArray();
        ja.add(OmsOrderStatus.UNCONFIRMED.toInteger());
        ja.add(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger());
        whereKeys.put("ORDER_STATUS", ja);
        List<OcBOrder> orderList = selectAllOrderByEs(whereKeys, filterKeys, childsKeys);
        if (CollectionUtils.isEmpty(orderList)) {
            return vh;
        }
        //循环分配物流
        orderDistributeLogistics(user, orderList);
        return vh;
    }

    private void orderDistributeLogistics(User user, List<OcBOrder> orderList) {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for (OcBOrder ocBOrder : orderList) {
            if (!ocBOrder.getPlatform().equals(PlatFormEnum.VIP_JITX.getCode())) {
                String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(ocBOrder.getId());
                RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
                try {
                    if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                        //调用分物流服务
                        OcBOrderRelation orderInfo = new OcBOrderRelation();
                        orderInfo.setOrderInfo(ocBOrder);
                     /*   Long cpClogisticsId = omsOrderDistributeLogisticsService.orderDistributeLogistics(orderInfo, user);
                        //相同物流无需更改
                        if (cpClogisticsId == null || cpClogisticsId.equals(0L) || cpClogisticsId.equals(ocBOrder.getCpCLogisticsId())) {
                            continue;
                        }
                        //然后再去查询物流公司合法性校验
                        CpCLogistics cpCLogistics = cpRpcExtService.queryLogisticsById(cpClogisticsId);
*/
                        CpCLogistics cpCLogistics = omsOrderDistributeLogisticsService.distributeLogistics(orderInfo);
                        OcBOrder ocBOrderDto = new OcBOrder();
                        if (cpCLogistics == null) {
                            String errorMessage = "订单OrderId" + ocBOrder.getId() + "的订单调用分物流服务未匹配到有效物流公司" +
                                    ",操作时间" + df.format(new Date());
                            ocBOrderDto.setSysremark(SplitMessageUtil.splitMesssage(errorMessage));
                            //  重新分物流后清空物流公司的值 20220911 一头牛逻辑
                            ocBOrderDto.setCpCLogisticsId(-1L);
                            ocBOrderDto.setCpCLogisticsEname("");
                            ocBOrderDto.setCpCLogisticsEcode("");
                            //插入日志
                            orderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                                    OrderLogTypeEnum.SUBLOGISTICS_SERVICE.getKey(), errorMessage, null, null, user);
                        } else {
                            String message = "订单OrderId" + ocBOrder.getId() + "的订单调用分物流服务,返回物流公司Id["
                                    + cpCLogistics.getId() + "][" + cpCLogistics.getEname() + "],操作时间" + df.format(new Date());
                            if (cpCLogistics.getId().equals(ocBOrder.getCpCLogisticsId())) {
                                continue;
                            }
                            ocBOrderDto.setCpCLogisticsId(cpCLogistics.getId());
                            ocBOrderDto.setCpCLogisticsEname(cpCLogistics.getEname());
                            ocBOrderDto.setCpCLogisticsEcode(cpCLogistics.getEcode());
                            //插入日志
                            orderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                                    OrderLogTypeEnum.SUBLOGISTICS_SERVICE.getKey(), message, null, null, user);
                        }
                        ocBOrderDto.setModifierename(SystemUserResource.ROOT_USER_NAME);
                        ocBOrderDto.setModifieddate(new Date());
                        ocBOrderDto.setId(ocBOrder.getId());
                        //根据更新占单状态统一修改
                        omsOrderService.updateOrderInfo(ocBOrderDto);
                    }
                } catch (Exception e) {
                    log.error(LogUtil.format("修改物流异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
                } finally {
                    redisLock.unlock();
                }
            }
        }
    }

    /**
     * 查询需要重新分配仓库的订单信息
     *
     * @param jsonStr
     * @param user
     * @param pem
     * @return ValueHolderV14
     */
    public ValueHolderV14 reallocateWarehouse(String jsonStr, User user, UserPermission pem) {
        ValueHolderV14 vh = new ValueHolderV14<>();
        vh.setCode(ResultCode.SUCCESS);
        vh.setMessage("查询成功");
        if (null == jsonStr) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("参数为空");
            return vh;
        }
        JSONObject whereKeys = new JSONObject();
        JSONObject filterKeys = new JSONObject();
        JSONObject childsKeys = new JSONObject();
        ocBOrderListQueryService.handlerSqlConditionQuery(jsonStr, whereKeys, filterKeys, childsKeys);
        ocBOrderListQueryService.filterSearchCondition(pem, whereKeys);
        //ES查询
        JSONArray ja = new JSONArray();
        ja.add(OmsOrderStatus.UNCONFIRMED.toInteger());
        whereKeys.put("ORDER_STATUS", ja);
        List<OcBOrder> orderList = selectAllOrderByEs(whereKeys, filterKeys, childsKeys);
        if (CollectionUtils.isEmpty(orderList)) {
            return vh;
        }
        //循环分配仓库
        orderDistributeWarehouse(user, orderList);
        return vh;
    }

    private void orderDistributeWarehouse(User user, List<OcBOrder> orderList) {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for (OcBOrder ocBOrder : orderList) {
            if (!ocBOrder.getPlatform().equals(PlatFormEnum.VIP_JITX.getCode())) {
                String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(ocBOrder.getId());
                RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
                try {
                    if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                        //调用分物流服务
                        OcBOrderRelation ocBOrderRelation = new OcBOrderRelation();
                        ocBOrderRelation.setOrderInfo(ocBOrder);
                        Long warehouseId = orderDistributeWarehouseService.doCallDistributeWarehouse(ocBOrderRelation, user);
                        if (!ocBOrder.getCpCPhyWarehouseId().equals(warehouseId)) {
                            CpCPhyWarehouse warehouse = null;
                            try {
                                warehouse = cpRpcService.queryByWarehouseId(warehouseId);
                            } catch (Exception e) {
                                continue;
                            }
                            String billNo = ocBOrder.getBillNo();
                            String preWarehourse = ocBOrder.getCpCPhyWarehouseEname();
                            ocBOrder.setCpCPhyWarehouseId(warehouseId);
                            ocBOrder.setCpCPhyWarehouseEname(warehouse.getEname());
                            ocBOrder.setCpCPhyWarehouseEcode(warehouse.getEcode());
                            //如果实体仓是o2o仓库，对订单进行打标
                            if (StringUtils.equals(warehouse.getWhType(), OcBOrderConst.CP_C_PHY_WAREHOUSE_TYPE)) {
                                ocBOrder.setIsO2oOrder(OcBOrderConst.IS_STATUS_IY);
                            } else {
                                ocBOrder.setIsO2oOrder(OcBOrderConst.IS_STATUS_IN);
                            }
                            // ocBOrder.setThirdWarehouseType(warehouse.getThirdWarehouseType());
                            List<OcBOrderItem> items = ocBOrderItemMapper.selectUnSuccessRefund(ocBOrder.getId());
                            //调用寻仓库存服务 不直接调用，调用贺柳相同逻辑方法
                            List<Long> cpwareHouseIds = null;
                            try {
                                cpwareHouseIds = omsOrderAutoSearchWarehouseService.queryCpwareHouseIdList(ocBOrder, items);
                            } catch (Exception e) {
                            }
                            if (CollectionUtils.isNotEmpty(cpwareHouseIds) && cpwareHouseIds.contains(warehouseId)) {
                                //调用零售发货单修改实体仓服务
                                ocBOrderRelation.setOrderItemList(items);
                                ValueHolderV14 holderV14 = sgRpcService.queryChangeWareHouse(ocBOrderRelation, user);
                                if (holderV14.getCode() != 0) {
                                    //调用失败
                                    OcBOrder ocBOrderDto = new OcBOrder();
                                    String errorMessage = "订单OrderId" + ocBOrder.getId() + "的订单,重新分配仓库Id["
                                            + cpwareHouseIds + "]修改发货仓库失败！调用零售发货单修改实体仓服务失败！操作时间" + df.format(new Date());
                                    log.error(errorMessage);
                                    ocBOrderDto.setSysremark(SplitMessageUtil.splitMesssage(errorMessage));
                                    //插入日志
                                    orderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                                            OrderLogTypeEnum.SUBLOGISTICS_SERVICE.getKey(), errorMessage, null, null, user);
                                    continue;
                                }
                                //更新主表,订单状态1，仓库信息当前录入，用户信息
                                ocBOrder.setOrderStatus(OcOrderCheckBoxEnum.CHECKBOX_UNCONFIRMED.getVal());
                                this.updateOrderInfo(ocBOrder.getId(), ocBOrder, user);
                                try {
                                    //调用日志服务
                                    omsOrderLogService.addUserOrderLog(ocBOrder.getId(), billNo,
                                            OrderLogTypeEnum.WAREHOUSE_UPDATE.getKey(), "发货仓库公司：修改前：" +
                                                    preWarehourse + "，修改后：" + warehouse.getEname() + "", "", "", user);
                                    //分配物流
                                    dealLogistic(warehouseId, user, ocBOrder);
                                } catch (NDSException e) {
                                }
                            } else {
                                OcBOrder ocBOrderDto = new OcBOrder();
                                String errorMessage = "订单OrderId" + ocBOrder.getId() + "的订单,重新分配仓库Id["
                                        + cpwareHouseIds + "]仓库库存不满足,不允许修改发货仓库！操作时间" + df.format(new Date());
                                ocBOrderDto.setSysremark(SplitMessageUtil.splitMesssage(errorMessage));
                                //插入日志
                                orderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                                        OrderLogTypeEnum.SUBLOGISTICS_SERVICE.getKey(), errorMessage, null, null, user);
                                continue;
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error(LogUtil.format("修改仓库异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
                } finally {
                    redisLock.unlock();
                }
            }
        }
    }

    /**
     * @Description 根据条件查询开票通知所有数据
     */
    public List<OcBOrder> selectAllOrderByEs(JSONObject whereKeys, JSONObject filterKeys, JSONObject childsKeys) {
        Integer range = 1000;
        String[] returnFileds = {"ID"};
        JSONArray orderKeys = new JSONArray();
        JSONObject order = new JSONObject();
        order.put("asc", true);
        order.put("name", "CREATIONDATE");
        orderKeys.add(order);

        List<OcBOrder> orderList = Lists.newArrayList();
        esSearchOrder(whereKeys, filterKeys, childsKeys, orderKeys, range, 0, returnFileds, orderList);
        return orderList;
    }

    /**
     * @Description 开票通知 ES递归查询
     */
    private void esSearchOrder(JSONObject whereKeys, JSONObject filterKeys, JSONObject childsKeys, JSONArray orderKeys,
                               Integer range, Integer startIndex, String[] returnFileds, List<OcBOrder> orderAllList) {
        List<Long> list = Lists.newArrayList();
        String indexName = OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME;
        JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME,
                OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME,
                OcElasticSearchIndexResources.OC_B_ORDER_ITEM_TYPE_NAME, whereKeys, filterKeys, orderKeys,
                childsKeys, range, startIndex, returnFileds);

        if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
            JSONArray arrayObj = search.getJSONArray("data");
            for (Object obj : arrayObj) {
                JSONObject jsonObject = (JSONObject) obj;
                list.add(jsonObject.getLong(returnFileds[0]));
            }
        }
        if (!CollectionUtils.isEmpty(list)) {
            List<OcBOrder> orderList = orderMapper.selectList(new QueryWrapper<OcBOrder>().lambda().in(OcBOrder::getId, list));
            orderAllList.addAll(orderList);
        }
        //返回的总数
        Integer total = search.getInteger("total");
        startIndex = startIndex + range;
        if (total > startIndex) {
            esSearchOrder(whereKeys, filterKeys, childsKeys, orderKeys, range, startIndex, returnFileds, orderAllList);
        }
    }


    /**
     * 修改仓库
     *
     * @param ids         订单编号
     * @param warehouseId 实体仓id
     * @param loginUser   用户信息
     * @return 返回信息
     * @throws NDSException 异常
     */
    public ValueHolderV14 updateWarehouseForUpdateAddress(List<Long> ids, Long warehouseId, User loginUser) throws NDSException {
        ValueHolderV14 holder = new ValueHolderV14();
        JSONObject data = new JSONObject();
        JSONObject coulumn = new JSONObject();
        JSONObject coulumn2 = new JSONObject();
        List<Object> columnList = new ArrayList<>();
        coulumn.put("key", "billNo");
        coulumn.put("title", "订单编号");
        columnList.add(coulumn);
        coulumn2.put("key", "message");
        coulumn2.put("title", "错误信息");
        columnList.add(coulumn2);
        data.put("columns", columnList);
        List<Object> failList = new ArrayList<>();
        //调用接口查询仓库信息
        CpCPhyWarehouse warehouse = null;
        try {
            warehouse = cpRpcService.queryByWarehouseId(warehouseId);
        } catch (Exception e) {
            log.error("调用cp仓库信息出错" + e);
        }
        if (CollectionUtils.isNotEmpty(ids) && warehouseId != null) {
            Boolean skipSgAjFlag = false;
            // 用来作为下次继续调用的标识 （看内面包含不包含1  用来调过库存不满足判断）
            if (ids.contains(-1L)) {
                ids.remove(-1L);
                skipSgAjFlag = true;
            }
            //录入仓库是否在仓库档案中存在且启用
            if (warehouseId != null && warehouse != null) {
                //判断界面录入的“实体仓库”是否为【店铺同步库存策略】中存在且启用
                List<Long> storeIds = cpRpcService.queryStoreList(warehouseId);
                boolean check = stRpcService.checkByStoreIds(storeIds);
                if (check) {
                    Integer count = 0;
                    if (ids.size() > 1) {
                        skipSgAjFlag = true;
                    }
                    for (Long id : ids) {
                        JSONObject failMessage = new JSONObject();
                        try {
                            OcBOrder ocBorderDto = orderMapper.selectById(id);
                            // 2)若存在则先判断此订单是否存在物流公司
                            if (ocBorderDto.getCpCLogisticsId() != null) {
                                //判断物流公司在修改实体仓库的仓库物流规则是否存在
                                if (ocBorderDto != null && warehouseId.equals(ocBorderDto.getCpCPhyWarehouseId())) {
                                    omsOrderLogService.addUserOrderLog(id, ocBorderDto.getBillNo(), OrderLogTypeEnum.WAREHOUSE_UPDATE.getKey(), " 修改仓库前和修改仓库后仓库一样，不做修改", "", "", loginUser);
                                }
                                if (ocBorderDto != null && !(warehouseId.equals(ocBorderDto.getCpCPhyWarehouseId()))) {
                                    Integer status = ocBorderDto.getOrderStatus();
                                    //订单状态  1,待审核 2,缺货
                                    List<Integer> list = Arrays.asList(OcOrderCheckBoxEnum.CHECKBOX_UNCONFIRMED.getVal(), OcOrderCheckBoxEnum.CHECKBOX_OUT_OF_STOCK.getVal(), OcOrderCheckBoxEnum.CHECKBOX_IN_DISTRIBUTION.getVal());
                                    if (!list.contains(status)) {
                                        failMessage.put("billNo", id);
                                        failMessage.put("message", id + " 当前状态异常，不允许修改发货仓库，建议刷新页面查看最新数据！");
                                        failList.add(failMessage);
                                        continue;
                                    }
                                    String billNo = ocBorderDto.getBillNo();
                                    Integer wmsCancelStatus = ocBorderDto.getWmsCancelStatus();
                                    String preWarehourse = ocBorderDto.getCpCPhyWarehouseEname();
                                    ocBorderDto.setCpCPhyWarehouseId(warehouseId);
                                    ocBorderDto.setCpCPhyWarehouseEname(warehouse.getEname());
                                    ocBorderDto.setCpCPhyWarehouseEcode(warehouse.getEcode());
                                    //如果实体仓是o2o仓库，对订单进行打标
                                    if (StringUtils.equals(warehouse.getWhType(), OcBOrderConst.CP_C_PHY_WAREHOUSE_TYPE)) {
                                        ocBorderDto.setIsO2oOrder(OcBOrderConst.IS_STATUS_IY);
                                    } else {
                                        ocBorderDto.setIsO2oOrder(OcBOrderConst.IS_STATUS_IN);
                                    }
                                    // ocBorderDto.setThirdWarehouseType(warehouse.getThirdWarehouseType());
                                    List<OcBOrderItem> items = ocBOrderItemMapper.selectUnSuccessRefund(id);
                                    //调用寻仓库存服务 不直接调用，调用贺柳相同逻辑方法
                                    List<Long> cpwareHouseIds = null;
                                    try {
                                        cpwareHouseIds = omsOrderAutoSearchWarehouseService.queryCpwareHouseIdList(ocBorderDto, items);
                                    } catch (Exception e) {
                                        log.error(LogUtil.format("寻仓库存异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
                                        failMessage.put("id", id);
                                        failMessage.put("message", id + " 寻仓库存异常，" + e.getMessage());
                                        failList.add(failMessage);
                                        continue;
                                    }
                                    if (CollectionUtils.isNotEmpty(cpwareHouseIds) && cpwareHouseIds.contains(warehouseId) || skipSgAjFlag) {
                                        //待审核、缺货
                                        if (status == OcOrderCheckBoxEnum.CHECKBOX_UNCONFIRMED.getVal() || status == OcOrderCheckBoxEnum.CHECKBOX_OUT_OF_STOCK.getVal()) {
                                            //调用零售发货单修改实体仓服务
                                            OcBOrderRelation ocBOrderRelation = new OcBOrderRelation();
                                            ocBOrderRelation.setOrderInfo(ocBorderDto);
                                            ocBOrderRelation.setOrderItemList(items);
                                            ValueHolderV14 holderV14 = sgRpcService.queryChangeWareHouse(ocBOrderRelation, loginUser);
                                            if (holderV14.getCode() != 0) {
                                                if (!"1".equals(holderV14.getData())) {
                                                    //调用失败
                                                    failMessage.put("id", id);
                                                    failMessage.put("message", " 修改发货仓库失败！(调用零售发货单修改实体仓服务失败，" + holderV14.getMessage() + ")");
                                                    failList.add(failMessage);
                                                }
                                            } else {
                                                omsOrderLogService.addUserOrderLog(id, billNo, OrderLogTypeEnum.STOCK_OCCUPY_SUCCESS.getKey(), "占用库存", "", "", loginUser);
                                                //更新主表,订单状态1，仓库信息当前录入，用户信息
                                                ocBorderDto.setOrderStatus(OcOrderCheckBoxEnum.CHECKBOX_UNCONFIRMED.getVal());
                                                UpdateOrderInfoService bean = ApplicationContextHandle.getBean(UpdateOrderInfoService.class);
                                                bean.updateOrderInfoForUpdateWarehouse(id, ocBorderDto, items, loginUser);
                                                count++;
                                            }
                                            try {
                                                //调用日志服务
                                                omsOrderLogService.addUserOrderLog(id, billNo, OrderLogTypeEnum.WAREHOUSE_UPDATE.getKey(), "发货仓库公司：修改前：" + preWarehourse + "，修改后：" + warehouse.getEname() + "", "", "", loginUser);
                                                //分配物流

                                                OcBOrder order = orderMapper.selectById(id);
                                                ocBorderDto.setOrderStatus(order.getOrderStatus());
//                                                ocBorderDto.setIsLackstock(order.getIsLackstock());
                                                dealLogistic(warehouseId, loginUser, ocBorderDto);
                                            } catch (NDSException e) {
                                                log.error(LogUtil.format("调用服务出错,异常信息为:{}"), Throwables.getStackTraceAsString(e));
                                            }
                                            //配货中
                                        } else if (status == OcOrderCheckBoxEnum.CHECKBOX_IN_DISTRIBUTION.getVal()) {
                                            //WMS撤回状态为“已撤回
                                            if (wmsCancelStatus == OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_YES.toInteger()) {
                                                try {
                                                    count = ApplicationContextHandle.getBean(UpdateOrderInfoService.class).warehourseDealWmsCanceled(loginUser, failList, warehouse, count, failMessage, ocBorderDto);
                                                } catch (Exception e) {
                                                    e.printStackTrace();
                                                }
                                                //WMS撤回状态为“未撤回”或“撤回失败
                                            } else {
                                                failMessage.put("billNo", id);
                                                failMessage.put("message", id + " 订单在WMS中未取消，不允许修改发货仓库，建议先撤回WMS再进行修改发货仓库！");
                                                failList.add(failMessage);
                                                continue;
                                            }
                                        }
                                    } else {
                                        failMessage.put("billNo", id);
                                        failMessage.put("message", id + " 当前发货仓库库存不满足，不允许修改发货仓库！");
                                        failList.add(failMessage);
                                        holder.setMessage("修改仓库失败");
                                        data.put("prompt_data", failList);
                                        holder.setData(data);
                                        // 2 代表是当前发货仓库库存不满足
                                        holder.setCode(2);
                                        return holder;
                                    }
                                } else {
                                    // @20200801 未修改仓库，但是修改了地址的情况下，也要重新分物流
//                                    failMessage.put("billNo", id);
//                                    failMessage.put("message", "发货仓库与当前一致，无需修改！");
//                                    failList.add(failMessage);
//                                    continue;
                                    if (Objects.nonNull(ocBorderDto)) {
                                        try {
                                            dealLogistic(warehouseId, loginUser, ocBorderDto);
                                            count++;
                                            continue;
                                        } catch (Exception e) {
                                            log.error(LogUtil.format("改地址不改仓的分物流出错,异常信息为:{}"), Throwables.getStackTraceAsString(e));
                                            failMessage.put("id", id);
                                            failMessage.put("message", "调用分物流失败：" + e.getMessage());
                                            failList.add(failMessage);
                                            continue;
                                        }
                                    } else {
                                        String msg = "重新分物流错误，依据ID查询不到对应的订单：" + id;
                                        failMessage.put("id", id);
                                        failMessage.put("message", "调用分物流失败：" + msg);
                                        failList.add(failMessage);
                                        continue;
                                    }
                                }
                            } else {
                                failMessage.put("billNo", id);
                                failMessage.put("message", "该订单不存在物流公司，请先添加物流公司！");
                                failList.add(failMessage);
                                continue;
                            }
                        } catch (Exception e) {
                            log.error(LogUtil.format("修改发货仓库异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
                        }
                    }
                    if (count == ids.size()) {
                        holder.setMessage("修改成功");
                        data.put("prompt_data", failList);
                    } else {
                        holder.setMessage("成功" + count + "条,失败" + (ids.size() - count) + "条");
                        data.put("prompt_data", failList);
                        holder.setData(data);
                    }
                } else {
                    holder.setCode(ResultCode.FAIL);
                    holder.setMessage("当前发货仓库非店铺发货仓库，不允许修改发货仓库！");
                }
            } else {
                holder.setCode(ResultCode.FAIL);
                holder.setMessage("当前发货仓库无效，请重新录入！");
            }
        } else {
            holder.setCode(ResultCode.FAIL);
            holder.setMessage("请选择需要修改仓库记录！");
        }

        return holder;
    }

    /**
     * 修改仓库去除原单需要物流公司的判断    淘宝小卡片修改地址   界面手动修改地址失败
     */
    public ValueHolderV14 updateWarehouseForUpdateAddressNew(List<Long> ids, Long warehouseId, User loginUser) throws NDSException {
        ValueHolderV14 holder = new ValueHolderV14();
        JSONObject data = new JSONObject();
        JSONObject coulumn = new JSONObject();
        JSONObject coulumn2 = new JSONObject();
        List<Object> columnList = new ArrayList<>();
        coulumn.put("key", "billNo");
        coulumn.put("title", "订单编号");
        columnList.add(coulumn);
        coulumn2.put("key", "message");
        coulumn2.put("title", "错误信息");
        columnList.add(coulumn2);
        data.put("columns", columnList);
        List<Object> failList = new ArrayList<>();
        //调用接口查询仓库信息
        CpCPhyWarehouse warehouse = null;
        try {
            warehouse = cpRpcService.queryByWarehouseId(warehouseId);
        } catch (Exception e) {
            log.error(LogUtil.format("调用cp仓库信息出错,异常信息为:{}"), Throwables.getStackTraceAsString(e));
        }
        if (CollectionUtils.isNotEmpty(ids) && warehouseId != null) {
            Boolean skipSgAjFlag = false;
            // 用来作为下次继续调用的标识 （看内面包含不包含1  用来调过库存不满足判断）
            if (ids.contains(-1L)) {
                ids.remove(-1L);
                skipSgAjFlag = true;
            }
            //录入仓库是否在仓库档案中存在且启用
            if (warehouseId != null && warehouse != null) {
                //判断界面录入的“实体仓库”是否为【店铺同步库存策略】中存在且启用
                List<Long> storeIds = cpRpcService.queryStoreList(warehouseId);
                boolean check = stRpcService.checkByStoreIds(storeIds);
                if (check) {
                    Integer count = 0;
                    if (ids.size() > 1) {
                        skipSgAjFlag = true;
                    }
                    for (Long id : ids) {
                        JSONObject failMessage = new JSONObject();
                        OcBOrder ocBorderDto = null;
                        try {
                            ocBorderDto = orderMapper.selectById(id);
                            //判断物流公司在修改实体仓库的仓库物流规则是否存在
                            if (ocBorderDto != null && warehouseId.equals(ocBorderDto.getCpCPhyWarehouseId())) {
                                omsOrderLogService.addUserOrderLog(id, ocBorderDto.getBillNo(), OrderLogTypeEnum.WAREHOUSE_UPDATE.getKey(), ocBorderDto.getBillNo() + " 修改仓库前和修改仓库后仓库一样，不做修改", "", "", loginUser);
                            }
                            if (ocBorderDto != null && !(warehouseId.equals(ocBorderDto.getCpCPhyWarehouseId()))) {
                                Integer status = ocBorderDto.getOrderStatus();
                                //订单状态  1,待审核 2,缺货
                                List<Integer> list = Arrays.asList(OcOrderCheckBoxEnum.CHECKBOX_UNCONFIRMED.getVal(), OcOrderCheckBoxEnum.CHECKBOX_OUT_OF_STOCK.getVal(), OcOrderCheckBoxEnum.CHECKBOX_IN_DISTRIBUTION.getVal());
                                if (!list.contains(status)) {
                                    omsOrderLogService.addUserOrderLog(id, ocBorderDto.getBillNo(), OrderLogTypeEnum.WAREHOUSE_UPDATE.getKey(), ocBorderDto.getBillNo() + " 当前状态异常，不允许修改发货仓库，建议刷新页面查看最新数据！", "", "", loginUser);
                                    failMessage.put("billNo", id);
                                    failMessage.put("message", id + " 当前状态异常，不允许修改发货仓库，建议刷新页面查看最新数据！");
                                    failList.add(failMessage);
                                    continue;
                                }
                                String billNo = ocBorderDto.getBillNo();
                                Integer wmsCancelStatus = ocBorderDto.getWmsCancelStatus();
                                String preWarehourse = ocBorderDto.getCpCPhyWarehouseEname();
                                ocBorderDto.setCpCPhyWarehouseId(warehouseId);
                                ocBorderDto.setCpCPhyWarehouseEname(warehouse.getEname());
                                ocBorderDto.setCpCPhyWarehouseEcode(warehouse.getEcode());
                                //如果实体仓是o2o仓库，对订单进行打标
                                if (StringUtils.equals(warehouse.getWhType(), OcBOrderConst.CP_C_PHY_WAREHOUSE_TYPE)) {
                                    ocBorderDto.setIsO2oOrder(OcBOrderConst.IS_STATUS_IY);
                                } else {
                                    ocBorderDto.setIsO2oOrder(OcBOrderConst.IS_STATUS_IN);
                                }
                                // ocBorderDto.setThirdWarehouseType(warehouse.getThirdWarehouseType());
                                List<OcBOrderItem> items = ocBOrderItemMapper.selectUnSuccessRefund(id);
                                //调用寻仓库存服务 不直接调用，调用贺柳相同逻辑方法
                                List<Long> cpwareHouseIds = null;
                                try {
                                    cpwareHouseIds = omsOrderAutoSearchWarehouseService.queryCpwareHouseIdList(ocBorderDto, items);
                                } catch (Exception e) {
                                    omsOrderLogService.addUserOrderLog(id, ocBorderDto.getBillNo(), OrderLogTypeEnum.WAREHOUSE_UPDATE.getKey(), ocBorderDto.getBillNo() + " 寻仓库存异常", "", "", loginUser);
                                    log.error(LogUtil.format("寻仓库存异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
                                    failMessage.put("id", id);
                                    failMessage.put("message", id + " 寻仓库存异常，" + e.getMessage());
                                    failList.add(failMessage);
                                    continue;
                                }
                                if (CollectionUtils.isNotEmpty(cpwareHouseIds) && cpwareHouseIds.contains(warehouseId) || skipSgAjFlag) {
                                    //待审核、缺货
                                    if (status == OcOrderCheckBoxEnum.CHECKBOX_UNCONFIRMED.getVal() || status == OcOrderCheckBoxEnum.CHECKBOX_OUT_OF_STOCK.getVal()) {
                                        //调用零售发货单修改实体仓服务
                                        OcBOrderRelation ocBOrderRelation = new OcBOrderRelation();
                                        ocBOrderRelation.setOrderInfo(ocBorderDto);
                                        ocBOrderRelation.setOrderItemList(items);
                                        ValueHolderV14 holderV14 = sgRpcService.queryChangeWareHouse(ocBOrderRelation, loginUser);
                                        if (holderV14.getCode() != 0) {
                                            if (!"1".equals(holderV14.getData())) {
                                                //调用失败
                                                omsOrderLogService.addUserOrderLog(id, ocBorderDto.getBillNo(), OrderLogTypeEnum.WAREHOUSE_UPDATE.getKey(), ocBorderDto.getBillNo() + " 调用零售发货单修改实体仓服务失败", "", "", loginUser);
                                                failMessage.put("id", id);
                                                failMessage.put("message", " 修改发货仓库失败！(调用零售发货单修改实体仓服务失败，" + holderV14.getMessage() + ")");
                                                failList.add(failMessage);
                                                continue;
                                            }
                                        } else {
                                            omsOrderLogService.addUserOrderLog(id, billNo, OrderLogTypeEnum.STOCK_OCCUPY_SUCCESS.getKey(), "占用库存", "", "", loginUser);
                                            //更新主表,订单状态1，仓库信息当前录入，用户信息
                                            ocBorderDto.setOrderStatus(OcOrderCheckBoxEnum.CHECKBOX_UNCONFIRMED.getVal());
                                            UpdateOrderInfoService bean = ApplicationContextHandle.getBean(UpdateOrderInfoService.class);
                                            // 改仓成功 置空物流信息
                                            ocBorderDto.setCpCLogisticsId(0L);
                                            ocBorderDto.setCpCLogisticsEname("");
                                            ocBorderDto.setCpCLogisticsEcode("");
                                            bean.updateOrderInfoForUpdateWarehouse(id, ocBorderDto, items, loginUser);
                                            count++;
                                        }
                                        try {
                                            //调用日志服务
                                            omsOrderLogService.addUserOrderLog(id, billNo, OrderLogTypeEnum.WAREHOUSE_UPDATE.getKey(), "发货仓库公司：修改前：" + preWarehourse + "，修改后：" + warehouse.getEname() + "", "", "", loginUser);
                                            //分配物流

                                            OcBOrder order = orderMapper.selectById(id);
                                            ocBorderDto.setOrderStatus(order.getOrderStatus());
//                                            ocBorderDto.setIsLackstock(order.getIsLackstock());
                                            dealLogistic(warehouseId, loginUser, ocBorderDto);
                                        } catch (NDSException e) {
                                            log.error(LogUtil.format("调用服务出错,异常信息为:{}"), Throwables.getStackTraceAsString(e));
                                        }
                                        //配货中
                                    } else if (status == OcOrderCheckBoxEnum.CHECKBOX_IN_DISTRIBUTION.getVal()) {
                                        //WMS撤回状态为“已撤回
                                        if (wmsCancelStatus == OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_YES.toInteger()) {
                                            try {
                                                count = ApplicationContextHandle.getBean(UpdateOrderInfoService.class).warehourseDealWmsCanceled(loginUser, failList, warehouse, count, failMessage, ocBorderDto);
                                            } catch (Exception e) {
                                                e.printStackTrace();
                                            }
                                            //WMS撤回状态为“未撤回”或“撤回失败
                                        } else {
                                            omsOrderLogService.addUserOrderLog(id, ocBorderDto.getBillNo(), OrderLogTypeEnum.WAREHOUSE_UPDATE.getKey(), ocBorderDto.getBillNo() + " 订单在WMS中未取消，不允许修改发货仓库，建议先撤回WMS再进行修改发货仓库！", "", "", loginUser);
                                            failMessage.put("billNo", id);
                                            failMessage.put("message", id + " 订单在WMS中未取消，不允许修改发货仓库，建议先撤回WMS再进行修改发货仓库！");
                                            failList.add(failMessage);
                                            continue;
                                        }
                                    }
                                } else {
                                    omsOrderLogService.addUserOrderLog(id, ocBorderDto.getBillNo(), OrderLogTypeEnum.WAREHOUSE_UPDATE.getKey(), ocBorderDto.getBillNo() + " 当前发货仓库库存不满足，不允许修改发货仓库！", "", "", loginUser);
                                    failMessage.put("billNo", id);
                                    failMessage.put("message", id + " 当前发货仓库库存不满足，不允许修改发货仓库！");
                                    failList.add(failMessage);
                                    holder.setMessage("修改仓库失败");
                                    data.put("prompt_data", failList);
                                    holder.setData(data);
                                    // 2 代表是当前发货仓库库存不满足
                                    holder.setCode(2);
                                    return holder;
                                }
                            } else {
                                // @20200801 未修改仓库，但是修改了地址的情况下，也要重新分物流
//                                    failMessage.put("billNo", id);
//                                    failMessage.put("message", "发货仓库与当前一致，无需修改！");
//                                    failList.add(failMessage);
//                                    continue;
                                if (Objects.nonNull(ocBorderDto)) {
                                    try {
                                        // 改仓成功 置空物流公司
                                        UpdateWrapper<OcBOrder> wrapper = new UpdateWrapper<>();
                                        wrapper.set("CP_C_LOGISTICS_ID", 0L).set("CP_C_LOGISTICS_ECODE", "").set("CP_C_LOGISTICS_ENAME", "");
                                        wrapper.eq("ID", ocBorderDto.getId());
                                        this.wrapperUpdateOrderInfo(loginUser, wrapper);
                                        dealLogistic(warehouseId, loginUser, ocBorderDto);
                                        count++;
                                        continue;
                                    } catch (Exception e) {
                                        log.error(LogUtil.format("改地址不改仓的分物流出错,异常信息为:{}"), Throwables.getStackTraceAsString(e));
                                        omsOrderLogService.addUserOrderLog(id, ocBorderDto.getBillNo(), OrderLogTypeEnum.WAREHOUSE_UPDATE.getKey(), ocBorderDto.getBillNo() + " 改地址不改仓的分物流出错", "", "", loginUser);
                                        failMessage.put("id", id);
                                        failMessage.put("message", "调用分物流失败：" + e.getMessage());
                                        failList.add(failMessage);
                                        continue;
                                    }
                                } else {
                                    omsOrderLogService.addUserOrderLog(id, ocBorderDto.getBillNo(), OrderLogTypeEnum.WAREHOUSE_UPDATE.getKey(), ocBorderDto.getBillNo() + " 重新分物流错误，依据ID查询不到对应的订单", "", "", loginUser);
                                    String msg = "重新分物流错误，依据ID查询不到对应的订单：" + id;
                                    failMessage.put("id", id);
                                    failMessage.put("message", "调用分物流失败：" + msg);
                                    failList.add(failMessage);
                                    continue;
                                }
                            }

                        } catch (Exception e) {
                            log.error(LogUtil.format("修改发货仓库异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
                            omsOrderLogService.addUserOrderLog(id, ocBorderDto.getBillNo(), OrderLogTypeEnum.WAREHOUSE_UPDATE.getKey(), ocBorderDto.getBillNo() + " 重新分物流错误，依据ID查询不到对应的订单", "", "", loginUser);
                        }
                    }
                    if (count == ids.size()) {
                        holder.setMessage("修改成功");
                        data.put("prompt_data", failList);
                    } else {
                        holder.setMessage("成功" + count + "条,失败" + (ids.size() - count) + "条");
                        data.put("prompt_data", failList);
                        holder.setData(data);
                    }
                } else {
                    holder.setCode(ResultCode.FAIL);
                    holder.setMessage("当前发货仓库非店铺发货仓库，不允许修改发货仓库！");
                }
            } else {
                holder.setCode(ResultCode.FAIL);
                holder.setMessage("当前发货仓库无效，请重新录入！");
            }
        } else {
            holder.setCode(ResultCode.FAIL);
            holder.setMessage("请选择需要修改仓库记录！");
        }

        return holder;
    }

    /**
     * 调用【创建改仓申请单接口】
     * edit by lwf 2020/6/8
     *
     * @return
     */
    public ValueHolderV14 callCreateChangeWarehouseWorkflow(User loginUser, OcBOrder ocBorderDto, String jitWarehouseEcode) {

        ValueHolderV14 rtnModel = new ValueHolderV14();
        rtnModel.setCode(ResultCode.FAIL);
        rtnModel.setMessage("返回数据异常");

        long start = System.currentTimeMillis();
        VipJitxCreateChangeWarehouseWorkflowRequest vipRequest = new VipJitxCreateChangeWarehouseWorkflowRequest();
        vipRequest.setOperateUser(loginUser);
        vipRequest.setSellerNick(ocBorderDto.getCpCShopSellerNick());
        //vipRequest.setVendorId(vendorId);
        List<VipJitxCreateChangeWarehouseWorkflowRequest.CreateWorkflow> workflows = new ArrayList<>();
        VipJitxCreateChangeWarehouseWorkflowRequest.CreateWorkflow createWorkflow = new VipJitxCreateChangeWarehouseWorkflowRequest.CreateWorkflow();
        createWorkflow.setRequestId(UUID.randomUUID().toString());
        createWorkflow.setOrderSn(ocBorderDto.getSourceCode());
        // 唯品会仓库编码
        createWorkflow.setNewDeliveryWarehouse(jitWarehouseEcode);
        // 原仓缺货改仓
        createWorkflow.setReasonCode("1001");
        workflows.add(createWorkflow);
        vipRequest.setWorkflows(workflows);
        ValueHolderV14<List<VipJitxCreateChangeWarehouseWorkflowResult>> valueHolderV14 = ipRpcService.createChangeWarehouseWorkflow(vipRequest);
        if (valueHolderV14 != null) {
            if (valueHolderV14.isOK()) {
                List<VipJitxCreateChangeWarehouseWorkflowResult> resultList = valueHolderV14.getData();
                if (resultList != null && resultList.size() == 1) {
                    VipJitxCreateChangeWarehouseWorkflowResult result = resultList.get(0);
                    if (SUCCESS.equals(result.getResult())) {
                        log.debug("调用【创建改仓申请单接口】成功");
                        rtnModel.setCode(ResultCode.SUCCESS);
                        rtnModel.setMessage(null);
                        rtnModel.setData(result.getWorkflowSn());
                        this.updateOrderVipWorkflowSn(ocBorderDto, loginUser, result.getWorkflowSn());
                        return rtnModel;
                    } else {
                        log.debug("调用【创建改仓申请单接口】返回失败:" + result.getErrorMsg());
                        rtnModel.setMessage(result.getErrorMsg());
                        rtnModel.setData(result.getErrorCode());
                        return rtnModel;
                    }
                } else {
                    if (resultList == null) {
                        return rtnModel;
                    }
                    if (resultList.size() != 1) {
                        return rtnModel;
                    }
                }
            } else {
                rtnModel.setMessage(valueHolderV14.getMessage());
                return rtnModel;
            }
        }
        return rtnModel;
    }


    /**
     * 更新订单唯品会工号
     *
     * @param order      订单对象
     * @param loginUser  用户
     * @param workflowSn 工号
     */
    public void updateOrderVipWorkflowSn(OcBOrder order, User loginUser, String workflowSn) {
        try {
            OcBOrder ocBOrder = new OcBOrder();
            ocBOrder.setModifierename(loginUser.getEname());
            ocBOrder.setModifiername(loginUser.getName());
            ocBOrder.setModifieddate(new Date());
            ocBOrder.setModifierid(loginUser.getId().longValue());
            ocBOrder.setId(order.getId());
            ocBOrder.setVipWorkflowSn(workflowSn);
            //更新订单主表
            orderMapper.updateById(ocBOrder);
            //将数据重新推送到ES
            order.setVipWorkflowSn(workflowSn);
            //  SpecialElasticSearchUtil.indexDocument(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME, OcElasticSearchIndexResources.OC_B_ORDER_TYPE_NAME, order, order.getId());

        } catch (Exception e) {
        }
    }

    /**
     * 更新订单唯品会修改仓库状态
     *
     * @param order     订单对象
     * @param loginUser 登录用户
     */
    public void updateOrderVipUpdateWarehouseStatusOrExpressCode(OcBOrder order, User loginUser, int status, String expressCode) {
        try {
            OcBOrder ocBOrder = new OcBOrder();
            ocBOrder.setModifierename(loginUser.getEname());
            ocBOrder.setModifiername(loginUser.getName());
            ocBOrder.setModifieddate(new Date());
            ocBOrder.setModifierid(loginUser.getId().longValue());
            ocBOrder.setId(order.getId());
            ocBOrder.setIsVipUpdateWarehouse(status);
            if (StringUtils.isNotEmpty(expressCode)) {
                ocBOrder.setExpresscode(expressCode);
                //改仓成功 更新jitx要求发货仓为空
                ocBOrder.setJitxRequiresDeliveryWarehouseId(null);
                ocBOrder.setJitxRequiresDeliveryWarehouseName(null);
                ocBOrder.setJitxRequiresDeliveryWarehouseCode(null);
                order.setExpresscode(expressCode);
                order.setJitxRequiresDeliveryWarehouseId(null);
                order.setJitxRequiresDeliveryWarehouseName(null);
                order.setJitxRequiresDeliveryWarehouseCode(null);
                orderMapper.clearRequiresDeliveryWarehouse(ocBOrder.getId());
            }
            //更新订单主表
            orderMapper.updateById(ocBOrder);

            //将数据重新推送到ES
            order.setIsVipUpdateWarehouse(status);
            //  SpecialElasticSearchUtil.indexDocument(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME, OcElasticSearchIndexResources.OC_B_ORDER_TYPE_NAME, order, order.getId());

        } catch (Exception e) {
            log.error("更新异常,异常信息为：", e);
        }
    }

    /**
     * 设定JITX修改仓库标识redisKey，更新数据库JITX修改仓库标识1
     *
     * @param order       订单
     * @param operateUser 操作者
     */
    public void setJITXRedisChangeWarehouseFlag(OcBOrder order, User operateUser) {
        String redisKey = BllRedisKeyResources.getJitxChangeWarehouseFlagKey(order.getId());
        CusRedisTemplate<String, String> objRedisTemplate = RedisMasterUtils.getObjRedisTemplate();
        objRedisTemplate.opsForValue().set(redisKey, "1",
                CHANGE_WAREHOUSE_FLAG_REDIS_EXPIRE_TIME, TimeUnit.DAYS);
        log.info("修改仓库标识redisKey:{}", redisKey);
        OcBOrder ocBOrder = new OcBOrder();
        ocBOrder.setId(order.getId());
        ocBOrder.setIsVipUpdateWarehouse(YesNoEnum.Y.getVal());
        orderMapper.updateById(ocBOrder);
    }

    /**
     * 移除JITX修改仓库标识redisKey，更新数据库JITX修改仓库标识0
     *
     * @param orderId
     * @param operateUser
     */
    public void removeJITXRedisChangeWarehouseFlag(Long orderId, User operateUser) {
        // 删除改仓成功的redisKey
        String redisKey = BllRedisKeyResources.getJitxChangeWarehouseFlagKey(orderId);
        CusRedisTemplate<String, String> objRedisTemplate = RedisMasterUtils.getObjRedisTemplate();
        boolean flag = objRedisTemplate.delete(redisKey);
        log.info("删除改仓的redisKey：{}, 结果：{}", redisKey, flag);
        // 更新零售发货单是否唯品会改仓为否
        OcBOrder order = orderMapper.selectByID(orderId);
        if (order != null) {
            OcBOrder ocBOrder = new OcBOrder();
            ocBOrder.setId(orderId);
            ocBOrder.setIsVipUpdateWarehouse(YesNoEnum.N.getVal());
            //更新合单加密信息
            MD5Util.encryptOrderInfo4Merge(order);
            ocBOrder.setOrderEncryptionCode(order.getOrderEncryptionCode());
            orderMapper.updateById(ocBOrder);
        }
    }

    /**
     * 更新方法
     *
     * @param id        订单id
     * @param ocBOrder  订单实体数据
     * @param loginUser 用户信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateOrderInfoForUpdateWarehouse2(Long id, OcBOrder ocBOrder, User loginUser) {
        try {
            ocBOrder.setModifierename(loginUser.getEname());
            ocBOrder.setModifiername(loginUser.getName());
            ocBOrder.setModifieddate(new Date());
            ocBOrder.setModifierid(loginUser.getId().longValue());
            //更新订单主表
            orderMapper.updateById(ocBOrder);
            //将数据重新推送到ES
            //  SpecialElasticSearchUtil.indexDocument(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME, OcElasticSearchIndexResources.OC_B_ORDER_TYPE_NAME, ocBOrder, id);
        } catch (Exception e) {
            log.error("更新异常,异常信息为：", e);
        }
    }

    /**
     * <AUTHOR>
     * @Date 14:52 2021/7/21
     * @Description 查询聚合仓/实体仓
     */
    public ValueHolderV14 queryOmsShopStorage(JSONObject jsonObject, User loginUser) {
        ValueHolderV14 vh = new ValueHolderV14<>();
        JSONObject result = new JSONObject();
        vh.setCode(ResultCode.SUCCESS);
        vh.setMessage("查询成功");
        if (null == jsonObject) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("参数为空");
            return vh;
        }
        Long shopId = jsonObject.getLong("shopId");
        if (shopId == null) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("店铺不能为空！");
            return vh;
        }
        Integer pageSize = jsonObject.getInteger("pageSize");
        if (pageSize == null) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("pageSize不能为空！");
            return vh;
        }
        Integer pageNum = jsonObject.getInteger("pageNum");
        if (pageNum == null) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("pageNum不能为空！");
            return vh;
        }
//        List<Long> skuIdList = (List<Long>) jsonObject.get("skuIdList");
//        if (skuIdList ==null || skuIdList.size()==0){
//            vh.setCode(ResultCode.FAIL);
//            vh.setMessage("sku不能为空！");
//            return vh;
//        }
        SgOmsShopShareAndPhyQueryRequest sgOmsShopStorageQueryRequest = buildSgOmsShopStorageQueryRequest(jsonObject, loginUser);
        ValueHolderV14<PageInfo<SgOmsShopPhyQueryResult>> v14 = sgRpcService.queryOmsShopShareAndPhyWarehouse(sgOmsShopStorageQueryRequest, loginUser);

        if (v14.isOK()) {
            PageInfo pageInfo = v14.getData();
            Integer count = (int) pageInfo.getTotal();
            boolean includePhyStorage = jsonObject.getBoolean("includePhyStorage");
            if (CollectionUtils.isNotEmpty(pageInfo.getList())) {
                //查询实体仓
                if (includePhyStorage) {
                    result.put("PhyWarehouseStores", pageInfo.getList());
                } else {
                    result.put("ShareStores", pageInfo.getList());
                }
            } else {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage("未查询到有可用库存的仓库！");
            }
            result.put("count", count);
        } else {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(v14.getMessage());
        }
        vh.setData(result);
        return vh;
    }

    /**
     * <AUTHOR>
     * @Date 16:22 2021/7/21
     * @Description 封装数据
     */
    private SgOmsShopShareAndPhyQueryRequest buildSgOmsShopStorageQueryRequest(JSONObject jsonObject, User loginUser) {
        SgOmsShopShareAndPhyQueryRequest sgOmsShopStorageQueryRequest = new SgOmsShopShareAndPhyQueryRequest();
        Long shopId = jsonObject.getLong("shopId");
        List<Long> skuIdList = (List<Long>) jsonObject.get("skuIdList");
        Long sgCShareStoreId = jsonObject.getLong("sgCShareStoreId");
        boolean includePhyStorage = jsonObject.getBoolean("includePhyStorage");
        Integer pageSize = jsonObject.getInteger("pageSize");
        Integer pageNum = jsonObject.getInteger("pageNum");
        sgOmsShopStorageQueryRequest.setCpCShopId(shopId);
        sgOmsShopStorageQueryRequest.setSgCShareStoreId(sgCShareStoreId);
        sgOmsShopStorageQueryRequest.setIncludePhyStorage(includePhyStorage);
        sgOmsShopStorageQueryRequest.setPageSize(pageSize);
        sgOmsShopStorageQueryRequest.setPageNum(pageNum);
        sgOmsShopStorageQueryRequest.setFuzzyVal(jsonObject.getString("fuzzyVal"));
        return sgOmsShopStorageQueryRequest;
    }

    void updateOrderStatus(OcBOrder ocBOrder, User loginUser) {
        OcBOrder updateOrder = new OcBOrder();
        updateOrder.setId(ocBOrder.getId());
        updateOrder.setOrderStatus(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger());
        updateOrder.setOccupyStatus(OmsParamConstant.INT_ZERO);
        updateOrder.setSysremark("");
        updateOrder.setModifierename(loginUser.getName());
        updateOrder.setModifieddate(new Date());
        // 订单合单加密信息，需要传有地址信息的订单参数
        MD5Util.encryptOrderInfo4Merge(ocBOrder);
        updateOrder.setOrderEncryptionCode(ocBOrder.getOrderEncryptionCode());
        omsOrderService.updateOrderInfo(updateOrder);
    }

    public ValueHolderV14 reDistributionLogistics(List<Long> ids, User loginUser) {
        if (CollectionUtils.isEmpty(ids)) {
            return ValueHolderV14Utils.getFailValueHolder("参数异常,请选择合适的订单信息!");
        }
        Map<Long, String> result = Maps.newHashMap();

        boolean isSuccess = false;
        for (Long orderId : ids) {
            String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(orderId);
            RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
            try {
                if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                    OcBOrder order = orderMapper.selectById(orderId);
                    if (order == null || !getCanRedistributionLogistics().contains(order.getOrderStatus())) {
                        result.put(orderId, "订单状态异常!");
                    } else {
                        orderMapper.removeExpressInfo(orderId);
                        isSuccess = true;
                        result.put(orderId, "修改成功!");
                    }
                } else {
                    throw new NDSException(Resources.getMessage("当前订单其他人在操作，请稍后再试!", loginUser.getLocale()));
                }
            } catch (Exception ex) {
                result.put(orderId, ex.getMessage());
                log.error(LogUtil.format("reDistributionLogistics={},订单ID=", orderId), Throwables.getStackTraceAsString(ex));
            } finally {
                redisLock.unlock();
            }
        }
        return ValueHolderV14Utils.custom(isSuccess ? ResultCode.SUCCESS : ResultCode.FAIL, isSuccess ? "重新分物流成功!" : "重新分物流失败!", result);
    }

    public static List<Integer> canReDistributionLogistics;

    public static List<Integer> getCanRedistributionLogistics() {
        if (CollectionUtils.isEmpty(canReDistributionLogistics)) {
            canReDistributionLogistics = Lists.newArrayList(OmsOrderStatus.UNCONFIRMED.toInteger(), OmsOrderStatus.BE_OUT_OF_STOCK.toInteger());
        }
        return canReDistributionLogistics;
    }

    /**
     * <AUTHOR> @Date
     * @Description 查询用户实体仓
     */
    public ValueHolderV14 queryOmsWarehouse(JSONObject jsonObject, User loginUser) {
        ValueHolderV14 vh = new ValueHolderV14<>();
        JSONObject result = new JSONObject();
        vh.setCode(ResultCode.SUCCESS);
        vh.setMessage("查询成功");
        if (null == jsonObject) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("参数为空");
            return vh;
        }
        Integer pageSize = jsonObject.getInteger("pageSize");
        if (pageSize == null) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("pageSize不能为空！");
            return vh;
        }
        Integer pageNum = jsonObject.getInteger("pageNum");
        if (pageNum == null) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("pageNum不能为空！");
            return vh;
        }
        //为空时默认按照非唯品会返回
        Integer isJitx = jsonObject.getInteger("isJitx");
        if (isJitx == null) {
            isJitx = YesNoEnum.N.getVal();
        }
        int start = pageSize * (pageNum - 1);
        int end = pageNum * pageSize;
        List<DataPermissionModel> ret = new ArrayList<>();
        if (loginUser.isAdmin()) {
            QuerySession querySession = new QuerySessionImpl();
            querySession.setEvent(new DefaultWebEvent("", new HashMap()));
            querySession.getEvent().put("param", null);
            ValueHolder valueHolder = cpRpcService.queryWarehouseByLike(querySession);
            log.debug(LogUtil.format("管理员-获取当前用户下的实体仓权限:{}", loginUser.getEname()), JSON.toJSONString(valueHolder));
            if (valueHolder != null && valueHolder.isOK()) {
                List<CpCPhyWarehouse> warehouses = (List<CpCPhyWarehouse>) valueHolder.get("data");
                if (CollectionUtils.isNotEmpty(warehouses)) {
                    for (CpCPhyWarehouse warehouse : warehouses) {
                        DataPermissionModel map = new DataPermissionModel();
                        map.setId(warehouse.getId());
                        map.setEcode(warehouse.getEcode());
                        map.setEname(warehouse.getEname());
                        ret.add(map);
                    }
                }
            } else {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage("管理员-未查询到有可用库存的仓库");
            }
        } else {
            ret = Permissions.getReadableModels("CP_C_WAREHOUSE_ID", loginUser.getId(), null);
            log.debug(LogUtil.format("获取当前用户下的实体仓权限:{}", loginUser.getEname()), JSON.toJSONString(ret));
        }
        if (YesNoEnum.Y.getVal().equals(isJitx)) {
            // todo查询唯品会仓库信息并过滤ret的信息
            List<StCVipcomJitxWarehouse> jitxWarehouses = jitxWarehouseService.queryVipWarehouseListByShopId(null);
            if (CollectionUtils.isEmpty(jitxWarehouses)) {
                ret = new ArrayList<>();
            } else {
                List<Long> jitxWarehouseIds = jitxWarehouses.stream()
                        .map(StCVipcomJitxWarehouse::getCpCPhyWarehouseId).collect(Collectors.toList());
                List<DataPermissionModel> tempList =
                        ret.stream().filter(s -> jitxWarehouseIds.contains(s.getId())).collect(Collectors.toList());
                ret = tempList;
            }
        }
        String keyword = jsonObject.getString("fuzzyVal");
        if (CollectionUtils.isNotEmpty(ret) && StringUtils.isNotEmpty(keyword)) {
            List<DataPermissionModel> tempList = ret.stream()
                    .filter(o -> o.getEcode().contains(keyword) || o.getEname().contains(keyword)).collect(Collectors.toList());
            ret = tempList;
        }
        if (CollectionUtils.isNotEmpty(ret) && ret.size() > start) {
            end = end > ret.size() ? ret.size() : end;
            List<DataPermissionModel> list = ret.subList(start, end);
            PageInfo pageInfo = new PageInfo(list);
            Integer count = ret.size();
            if (CollectionUtils.isNotEmpty(pageInfo.getList())) {
                result.put("PhyWarehouseStores", pageInfo.getList());
            } else {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage("未查询到有可用库存的仓库！");
            }
            result.put("count", count);
        } else {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("未查询到有可用库存的仓库");
        }
        vh.setData(result);
        return vh;
    }
}
