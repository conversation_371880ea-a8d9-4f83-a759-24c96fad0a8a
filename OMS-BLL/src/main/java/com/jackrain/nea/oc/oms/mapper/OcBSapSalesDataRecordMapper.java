package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.OcBSapSalesDataRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
public interface OcBSapSalesDataRecordMapper extends ExtentionMapper<OcBSapSalesDataRecord> {
    @Select("select * from OC_B_SAP_SALES_DATA_RECORD where SUM_STATUS='0' and isactive='Y' and MERGE_CODE <>''\n" +
            "and OC_B_SAP_SALES_DATA_GATHER_ID=0 and creationdate <=now() limit #{pageSize}")
    List<OcBSapSalesDataRecord> querySalesDataRecord(@Param("pageSize") Integer pageSize);

    @Update("<script> "
            + "UPDATE OC_B_SAP_SALES_DATA_RECORD SET OC_B_SAP_SALES_DATA_GATHER_ID=0\n" +
            ",SUM_STATUS='0'\n" +
            ",MODIFIEDDATE=now()\n" +
            "where id in "
            + "<foreach item='ids' index='index' collection='ids' open='(' separator=',' close=')'>"
            + " #{ids} "
            + "</foreach>"
            + "</script>")
    Integer recordCount(@Param("ids") List<Long> ids);

    @Select("<script>" +
            "select * from oc_b_sap_sales_data_record where oc_b_sap_sales_data_gather_id in"
            + "<foreach item='ids' index='index' collection='ids' open='(' separator=',' close=')'>"
            + " #{ids} "
            + "</foreach>"
            + "</script>")
    List<OcBSapSalesDataRecord> selectRecordDataInfo(@Param("ids") List<Long> ids);


    @SelectProvider(type = OcBSapSalesDataRecordMapper.OcBSalesDataRecordMapperProvider.class, method = "selectOcBSalesDataRecordList")
    List<OcBSapSalesDataRecord> selectOcBSalesDataRecordList(@Param("taskTableName") String taskTableName,
                                                             @Param("limit") int limit,
                                                             @Param("inTime") String inTime);

    @Select("<script> "
            + "SELECT * FROM oc_b_sap_sales_data_record WHERE id in"
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<OcBSapSalesDataRecord> selectSaleDataRecord(@Param("ids") List<Long> idList);

    class OcBSalesDataRecordMapperProvider {

        public String selectOcBSalesDataRecordList(@Param("taskTableName") String taskTableName,
                                                   @Param("limit") int limit,
                                                   @Param("inTime") String inTime) {
            StringBuffer sql = new StringBuffer();
            sql.append("select * from ")
                    .append(taskTableName)
                    .append(" where sum_status= 0 ")
                    .append(" and isactive='Y' ")
                    .append(" and merge_code<>'' ")
                    .append(" and oc_b_sap_sales_data_gather_id=0 ")
                    .append(" and IN_TIME <")
                    .append("'")
                    .append(inTime)
                    .append("'")
                    .append(" limit ").append(limit);
            return sql.toString();

        }
    }

    @Select("<script> "
            + "SELECT * FROM oc_b_sap_sales_data_record WHERE bill_type = 2 and bill_no = #{billNo}"
            + "</script>")
    List<OcBSapSalesDataRecord> selectNaiKaRecordByAfBillNo(@Param("billNo") String billNo);


}