package com.jackrain.nea.oc.oms.process.transfer.impl.normal.standplat.step;

import cn.hutool.core.util.ObjectUtil;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.gsi.GSI4OrderItem;
import com.jackrain.nea.oc.oms.model.enums.OcOrderCheckBoxEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderRefundStatus;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.relation.IpStandplatOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatOrder;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatOrderItemEx;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.taobao.TaoBaoOrderStatus;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 检查订单是否已经存在
 *
 * @author: ming.fz
 * @since: 2019-07-08
 * create at : 2019-07-08
 */
@Step(order = 40, description = "检查中间表订单是否已经存在")
@Component
@Slf4j
public class Step040StandplatCheckOmsOrderExist extends BaseStandplatOrderProcessStep
        implements IOmsOrderProcessStep<IpStandplatOrderRelation> {

    @Autowired
    private StRpcService stRpcService;

    @Override
    public ProcessStepResult<IpStandplatOrderRelation> startProcess(IpStandplatOrderRelation orderInfo,
                                                                    ProcessStepResult preStepResult,
                                                                    boolean isAutoMakeup, User operateUser) {
        IpBStandplatOrder standplatOrder = orderInfo.getStandplatOrder();
        String tid = standplatOrder.getTid();
        try {
            List<Long> esIdList = GSI4OrderItem.selectOcBOrderItemByTid(standplatOrder.getTid());
            if (CollectionUtils.isEmpty(esIdList)) {
                return new ProcessStepResult<>(StepStatus.SUCCESS);
            }
            List<OcBOrder> ocBOrders = ocBOrderMapper.selectByIdsList(esIdList);
            if (CollectionUtils.isEmpty(ocBOrders)) {
                return new ProcessStepResult<>(StepStatus.SUCCESS);
            }
            ipStandplatOrderService.currencyDeliverGoodsBefore(orderInfo, ocBOrders, operateUser);
            return new ProcessStepResult<>(StepStatus.SUCCESS, ocBOrders, Step070StandplatUpdateSellerRemark.class);
        } catch (Exception e) {
            log.error(LogUtil.format("通用转单发货前退款处理失败:{}", "通用转单发货前退款处理失败"), Throwables.getStackTraceAsString(e));
            this.ipStandplatOrderService.updateStandPlatOrderTransStatus(tid, TransferOrderStatus.NOT_TRANSFER, "通用转单发货前退款处理失败"+ e.getMessage(), null);
            return new ProcessStepResult<>(StepStatus.FAILED, "通用转单发货前退款处理失败:"+e.getMessage());
        }
    }


//    @Override
//    public ProcessStepResult<IpStandplatOrderRelation> startProcess(IpStandplatOrderRelation orderInfo,
//                                                                    ProcessStepResult preStepResult,
//                                                                    boolean isAutoMakeup, User operateUser) {
//        ProcessStepResult<IpStandplatOrderRelation> stepResult = null;
//
//        Long orderId = orderInfo.getOrderId();
//
//        //通过tid获取全渠道所有订单
//        // @20200721 修改查询逻辑：查询订单应该按oid查，查不到才按tid查
//        // List<OcBOrder> findOrderInfoList = orderService.selectOmsOrderRecord(orderInfo.getStandplatOrder().getTid());
//        List<OmsOrderRelation> relations = orderService.selectOmsOrderRelation(orderInfo);
//        List<OcBOrder> findOrderInfoList = new ArrayList<>();
//
//        if (CollectionUtils.isNotEmpty(relations)) {
//            relations.forEach(r -> {
//                if (Objects.nonNull(r.getOcBOrder())) {
//                    findOrderInfoList.add(r.getOcBOrder());
//                }
//            });
//        }
//
//        if (log.isDebugEnabled()) {
//            log.debug("Step040StandplatCheckOmsOrderExist.findOrderInfoList:{}/{}", orderInfo.getOrderNo(), JSON.toJSONString(findOrderInfoList));
//        }
//
//        if (findOrderInfoList.size() > 0) {
//            StringBuilder returnMessage = new StringBuilder();
//            //排除所有已取消和已作废订单
//            List<OmsOrderRelation> relationsFilter = filterAndRemove(relations);
//            // List<OcBOrder> ocBOrders = getCheckOrder(findOrderInfoList);
//
//            if (log.isDebugEnabled()) {
//                log.debug("Step040StandplatCheckOmsOrderExist.relationsFilter:{}/{}", orderInfo.getOrderNo(), JSON.toJSONString(relationsFilter));
//            }
//
//            //是否都是已作废或都为取消单据
//            if (relationsFilter.size() > 0) {
//                /*
//                状态处理说明，明细退款状态：refund_status : OcOrderRefundStatusEnum
//                null/0：
//                1：拦截
//                2：取消
//                *3：目前无处理逻辑
//                4/5：取消拦截
//                6：取消
//                 */
//                // @20200721 如果排除取消作废，还剩余单据，则可能：已经转换，不需要重复处理；或者已经转换，需要退款（发货前退款，发货后不在这里处理）
//                // 是否是退单通过明细的refund_status来判断
//                boolean isRefund = isRefundByItem(orderInfo);
//
//                if (log.isDebugEnabled()) {
//                    log.debug("是否是退款单判断结果：{}/{}", isRefund, JSON.toJSONString(orderInfo));
//                }
//
//                if (isRefund) {
//                    for (int i = 0; i < relationsFilter.size(); i++) {
//                        OmsOrderRelation relation = relationsFilter.get(i);
//
//                        String result = omsStandplatReturnOrderService.returnOrderService(orderInfo, relation.getOcBOrder(),
//                                isAutoMakeup, operateUser, relation.getOcBOrderItems());
//
//                        returnMessage.append(i);
//                        returnMessage.append("通用发货前退单单据");
//                        returnMessage.append(orderId);
//                        returnMessage.append(result);
//                        returnMessage.append("\n");
//                    }
//
//                    String operateMessage = Resources.getMessage(returnMessage.deleteCharAt(returnMessage.length() - 1).toString());
//                    stepResult = new ProcessStepResult<>(StepStatus.FINISHED, operateMessage);
////
//                } else {
//                    OcBOrder ocBOrder = findOrderInfoList.get(0);
//                    //核对收件人地址信息
    //if (!checkReceiveInfo(ocBOrder, orderInfo)) {
//                        this.ipStandplatOrderService.updateStandPlatOrderTransStatus(orderInfo.getOrderNo(), TransferOrderStatus.TRANSFERFAIL, "中间表收货人信息变更,请更新订单收货人信息！", null);
//                        return new ProcessStepResult<>(StepStatus.FAILED);
//                    }
//
//                    // 重复转单的场景：不做任何处理，直接结束
//                    // @20200722 应该要更新掉转单状态，否则单据一直处于未转换状态
//                    String remark = "单据已存在，标记已转换";
//                    this.ipStandplatOrderService.updateStandPlatOrderTransStatus(orderInfo.getOrderNo(), TransferOrderStatus.TRANSFERRED, remark, null);
//                    // @20201228 假如已存在就去校验 备注是否一样;不一样则进行更新
//                    return new ProcessStepResult<>(StepStatus.SUCCESS, findOrderInfoList, Step070StandplatUpdateSellerRemark.class);
//                }
//            } else {
//                //都为已作废或都为取消单据 释放当前行，更新转换状态为2
//                String orderNo = orderInfo.getOrderNo();
//                this.ipStandplatOrderService.updateStandPlatOrderTransStatus(orderNo, TransferOrderStatus.TRANSFERRED, "订单已取消或作废", null);
//                return new ProcessStepResult<>(StepStatus.FINISHED);
//            }
//        } else {
//            String operateMessage = Resources.getMessage("单据" + orderId + "中间表订单在全渠道" +
//                    "订单表中不存在，进入下一阶段");
//            stepResult = new ProcessStepResult<>(StepStatus.SUCCESS, operateMessage);
//        }
//
//        return stepResult;
//
//    }

        /**
         * 核对收货人信息
         */
        private boolean checkReceiveInfo (OcBOrder ocBOrder, IpStandplatOrderRelation ipStandplatOrderRelation){
            IpBStandplatOrder ipBTaobaoOrder = ipStandplatOrderRelation.getStandplatOrder();
            String orderReceiveAddr = ocBOrder.getReceiverAddress();
            String tbReceiveAddr = ipBTaobaoOrder.getReceiverAddress();
            String orderReceiveName = ocBOrder.getReceiverName();
            String tbReceiverName = ipBTaobaoOrder.getReceiverName();
            String orderReceiveMobile = ocBOrder.getReceiverMobile();
            String tbReceiveMobile = ipBTaobaoOrder.getReceiverMobile();
            String orderReceiveCity = ocBOrder.getCpCRegionCityEname();
            String tbReceiveCity = ipBTaobaoOrder.getReceiverCity();
            String orderReceiveProvice = ocBOrder.getCpCRegionProvinceEname();
            String tbReceiveProvice = ipBTaobaoOrder.getReceiverProvince();
            String orderReceiveArea = ocBOrder.getCpCRegionAreaEname();
            String tbReceiveArea = ipBTaobaoOrder.getReceiverDistrict();
            boolean addrIsOk = StringUtils.equalsIgnoreCase(orderReceiveAddr, tbReceiveAddr) &&
                    StringUtils.equalsIgnoreCase(orderReceiveName, tbReceiverName) &&
                    StringUtils.equalsIgnoreCase(orderReceiveMobile, tbReceiveMobile) &&
                    StringUtils.equalsIgnoreCase(orderReceiveCity, tbReceiveCity) &&
                    StringUtils.equalsIgnoreCase(orderReceiveProvice, tbReceiveProvice) &&
                    StringUtils.equalsIgnoreCase(orderReceiveArea, tbReceiveArea);
            return addrIsOk;
        }


        /**
         * 判断是否是退单，依据明细单的refund
         * 只要明细存在refund_status != null && refund_status <> 0，则认为是退单的
         *
         * @param orderInfo
         * @return
         */
        private boolean isRefundByItem (IpStandplatOrderRelation orderInfo){
            // 判断头表
            if (Objects.nonNull(orderInfo) && Objects.nonNull(orderInfo.getStandplatOrder())) {
                if (TaoBaoOrderStatus.TRADE_CANCELED.equals(orderInfo.getStandplatOrder().getStatus())) {
                    // 如果订单已经是标识为退款
                    return true;
                }
            }

            // 判断明细表
            if (Objects.nonNull(orderInfo) && CollectionUtils.isNotEmpty(orderInfo.getStandPlatOrderItemList())) {
                for (IpBStandplatOrderItemEx item : orderInfo.getStandPlatOrderItemList()) {
                    String refundStatus = item.getRefundStatus();
                    // 存在，则返回为true
                    // @20200824 要判断多条明细，只要有一条明细是退款的，就认为是退款的
                    if (Objects.nonNull(refundStatus) && !String.valueOf(OmsOrderRefundStatus.UNREFUND.toInteger()).equals(refundStatus)) {
                        return true;
                    }
                }
            }

            return false;
        }

        /**
         * 过滤无效数据
         *
         * @param relations
         */
        private List<OmsOrderRelation> filterAndRemove (List < OmsOrderRelation > relations) {
            if (CollectionUtils.isNotEmpty(relations)) {
                return relations.stream().filter(r -> !getCheckOrder(r.getOcBOrder())).collect(Collectors.toList());
            }

            return relations;
        }

        /**
         * 排除所有已取消和已作废订单
         *
         * @param findOrderInfoList
         * @return
         */
        private List<OcBOrder> getCheckOrder (List < OcBOrder > findOrderInfoList) {
            if (CollectionUtils.isNotEmpty(findOrderInfoList)) {
                return findOrderInfoList.stream().filter(o -> !getCheckOrder(o)).collect(Collectors.toList());
            }

            return findOrderInfoList;
//        //是否是已作废或取消订单
//        List<OcBOrder> ocBOrders = new ArrayList<>();
//        for (OcBOrder order : findOrderInfoList) {
//            // Integer orderStatus = order.getOrderStatus();
//            if (!getCheckOrder(order)) {
//                ocBOrders.add(order);
//            }
//        }
//
//        return ocBOrders;
        }

        /**
         * 判断单据是否失效完结等
         *
         * @param order
         * @return
         */
        private boolean getCheckOrder (OcBOrder order){
            // 是否失效需要移除的数据
            boolean isValidate = true;

            if (Objects.nonNull(order)) {
                Integer orderStatus = order.getOrderStatus();

                if (!(OcOrderCheckBoxEnum.CHECKBOX_CANCELLED.getVal() == orderStatus
                        || OcOrderCheckBoxEnum.CHECKBOX_SYSTEM_INVALIDATION.getVal() == orderStatus)) {
                    isValidate = false;
                }
            }

            return isValidate;
        }
    }
