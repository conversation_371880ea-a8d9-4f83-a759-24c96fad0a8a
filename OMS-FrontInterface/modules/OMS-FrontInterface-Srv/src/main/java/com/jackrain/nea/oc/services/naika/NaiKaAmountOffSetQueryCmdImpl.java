package com.jackrain.nea.oc.services.naika;

import com.jackrain.nea.oc.oms.api.naika.NaiKaAmountOffSetQueryCmd;
import com.jackrain.nea.oc.oms.services.naika.OmsNaiKaAmountOffsetQueryService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName NaiKaAmountOffSetQueryCmdImpl
 * @Description 奶卡金额冲抵查询接口
 * <AUTHOR>
 * @Date 2022/9/14 11:12
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class NaiKaAmountOffSetQueryCmdImpl extends CommandAdapter implements NaiKaAmountOffSetQueryCmd {

    @Autowired
    private OmsNaiKaAmountOffsetQueryService omsNaiKaAmountOffsetQueryService;

    @Override
    public ValueHolder execute(QuerySession querySession) {
        return omsNaiKaAmountOffsetQueryService.naiKaAmountOffsetQuery(querySession);
    }
}
