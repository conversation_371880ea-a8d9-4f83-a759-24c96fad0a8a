package com.jackrain.nea.oc.oms.util;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.model.relation.UserPermission;
import com.jackrain.nea.oc.oms.model.result.QueryOrderListResult;
import com.jackrain.nea.oc.oms.services.OcBOrderListQueryService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * @Author: 黄世新
 * @Date: 2020/3/18 2:02 下午
 * @Version 1.0
 */
@Slf4j
@Component
public class SelectOrderUtil {

    @Autowired
    private OcBOrderListQueryService ocBOrderListQueryService;


    /**
     * 根据条件查询es顶大id
     *
     * @param param
     * @param loginUser
     * @param usrPem
     * @return
     */
    public List<Long> checkParamForIds(JSONObject param, User loginUser, UserPermission usrPem) {
        try {
            // 标识  只获取订单id
            param.put("SearchForId", 1);
            ValueHolderV14<QueryOrderListResult> queryOrderList = ocBOrderListQueryService.queryOrderList(param.toJSONString(), loginUser, usrPem);
            if (ResultCode.SUCCESS == queryOrderList.getCode()) {
                QueryOrderListResult data = queryOrderList.getData();
                if (data != null && !CollectionUtils.isEmpty(data.getIds())) {
                    return data.getIds();
                }
            }
        } catch (Exception e) {
            log.error(LogUtil.format("查询订单异常:{}"), Throwables.getStackTraceAsString(e));
        }
        return null;
    }


    /**
     * 通过ooid判断是否拆过订单
     *
     * @param oOid 子订单号
     * @return
     */
    public boolean selectOrder(String oOid) {
        if (StringUtils.isEmpty(oOid)) {
            return false;
        }
        Set<Long> ids = ES4Order.findIdByOidAndIsGift(oOid, 0);
        if (CollectionUtils.isEmpty(ids)){
            throw new NDSException("数据异常");
        }

        return ids.size() > 1;
    }
}
