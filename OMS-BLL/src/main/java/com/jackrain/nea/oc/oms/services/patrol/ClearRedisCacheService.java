package com.jackrain.nea.oc.oms.services.patrol;

import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.util.R3RedisSlaverUtil;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * @Author: 黄世新
 * @Date: 2019/11/6 1:43 下午
 * @Version 1.0
 */
@Slf4j
@Component
public class ClearRedisCacheService {


    /**
     * @param redisKey 需要删除的key
     * @param type     类型
     */
    public ValueHolderV14 clearRedisCache(String redisKey, String type, String env) {
        ValueHolderV14 valueHolder = new ValueHolderV14();
        try {
            // 获取写的redis template
            CusRedisTemplate<String, List<Long>> objRedisTemplate = null;
            if (StringUtils.isEmpty(env)) {
                objRedisTemplate = RedisOpsUtil.getObjRedisTemplate();
            } else {
                objRedisTemplate = R3RedisSlaverUtil.getStrRedisTemplate(env);

            }
            Set<String> keys = new HashSet<>();
            if (StringUtils.isNotEmpty(redisKey)) {
                if (StringUtils.isEmpty(type)) {
                    objRedisTemplate.delete(redisKey);
                    keys.add(redisKey);
                } else {
                    keys = objRedisTemplate.keys(redisKey + "*");
                    for (String key : keys) {
                        objRedisTemplate.delete(key);
                    }
                }
            }
            valueHolder.setCode(0);
            valueHolder.setData(keys);
            valueHolder.setMessage("删除成功");
        } catch (Exception e) {
            valueHolder.setCode(-1);
            valueHolder.setMessage("删除失败" + e.getMessage());
        }
        return valueHolder;
    }
}
