package com.jackrain.nea;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.ApplicationContext;

/**
 * @author: 易邵峰
 * @since: 2019-03-18
 * create at : 2019-03-18 16:48
 */
@SpringBootApplication
public class OrderTestApplication extends SpringBootServletInitializer {
    static {
        System.setProperty("dubbo.application.logger", "slf4j");
    }

    public static void main(String[] args) {
        // 若将devtools.enabled设置为true，会导致无法加载Dubbo
        System.setProperty("spring.devtools.restart.enabled", "false");
        ApplicationContext context = SpringApplication.run(applicationClass, args);

        System.out.println("Start SprintBoot Success ContextId=" + context.getId());
    }

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
        return application.sources(applicationClass);
    }

    private static final Class<OrderTestApplication> applicationClass = OrderTestApplication.class;

}
