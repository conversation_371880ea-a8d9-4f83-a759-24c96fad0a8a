package com.jackrain.nea.oc.oms.nums;


import lombok.Getter;

/**
 * 开票通知日志类别枚举
 *
 * @author: huang.zaizai
 * create at: 2019/7/23 19:20
 */
public enum OcInvoiceLogTypeEnum {

    ADD(1, "新增"),
    SAVE(2, "编辑保存"),
    VOID(3, "作废"),
    AUDIT(4, "审核"),
    UNAUDIT(5, "反审核"),
    REPRIEVE_INVOICE(6, "暂缓开票"),
    CANCEL_REPRIEVE(7, "撤销暂缓"),
    INVOICE_IMPORT(8, "开票信息导入"),
    CONFIRM_INVOICE(9, "确认开票");

    OcInvoiceLogTypeEnum(Integer key, String name) {
        this.key = key;
        this.name = name;
    }

    @Getter
    private Integer key;

    @Getter
    private String name;


    /**
     * 根据状态值,获取状态名
     *
     * @param key
     * @return String
     */
    public static String enumToStringBykey(Integer key) {
        String s = "";
        if (key == null) {
            return s;
        }
        for (OcInvoiceLogTypeEnum e : OcInvoiceLogTypeEnum.values()) {
            if (Integer.valueOf(e.getKey()).equals(key)) {
                s = e.getName();
                return s;
            }
        }
        return null;
    }
}


