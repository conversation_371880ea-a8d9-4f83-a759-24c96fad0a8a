package com.jackrain.nea.oc.oms.process.jitx.feedback.step;

import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.SyncStatus;
import com.jackrain.nea.oc.oms.model.relation.IpJitxDeliveryRelation;
import com.jackrain.nea.oc.oms.services.IpJitxDeliveryService;
import com.jackrain.nea.oc.oms.services.IpVipTimeOrderService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> zhuxing
 * @Date : 2022-03-01 20:30
 * @Description : 根单号加锁
 **/
@Step(order = 55, description = "根单号加锁，防止同一时间多个寻仓单进行虚拟寻仓")
@Slf4j
@Component
public class Step055LockJitxDelivery extends BaseJitxDeliveryProcessStep
        implements IOmsOrderProcessStep<IpJitxDeliveryRelation> {
    @Autowired
    protected IpVipTimeOrderService ipVipTimeOrderService;

    @Autowired
    protected IpJitxDeliveryService ipJitxDeliveryService;

    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    @Override
    public ProcessStepResult<IpJitxDeliveryRelation> startProcess(IpJitxDeliveryRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        ProcessStepResult<IpJitxDeliveryRelation> stepResult = new ProcessStepResult<>();
        String rootOrderSn = orderInfo.getRootOrderSn();
        String orderSn = orderInfo.getRootOrderSn();
        String lockRedisKey = BllRedisKeyResources.buildVipDeliveryRootOrderSnLockOrderKey(rootOrderSn);
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try{
            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                orderInfo.setRedisLock(redisLock);
                stepResult.setMessage("根单号加锁成功，进入下一阶段！");
                stepResult.setStatus(StepStatus.SUCCESS);
            }else{
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("寻仓单OrderSn:{}同批寻仓单正在寻源占单,请稍后再试","Step055LockJitxDelivery"),
                            orderSn);
                }
                ipJitxDeliveryService.updateJitxSyncStatus(orderSn, SyncStatus.UNSYNC, "同批寻仓单正在寻源占单,请稍后再试");
                stepResult.setMessage("同批寻仓单正在转换中，请稍后再试！");
                stepResult.setStatus(StepStatus.FAILED);
            }
        }catch (Exception e){
            log.error(LogUtil.format("寻仓单OrderSn:{}转换失败:{}","Step055LockJitxDelivery"),
                    orderSn, Throwables.getStackTraceAsString(e));
            ipJitxDeliveryService.updateJitxSyncStatus(orderSn, SyncStatus.UNSYNC, "转换失败"+e.getMessage());
            stepResult.setMessage("寻仓单转换失败！");
            stepResult.setStatus(StepStatus.FAILED);
        }finally {
            if(!StepStatus.SUCCESS.equals(stepResult.getStatus())){
                redisLock.unlock();
            }
        }
        return stepResult;
    }
}
