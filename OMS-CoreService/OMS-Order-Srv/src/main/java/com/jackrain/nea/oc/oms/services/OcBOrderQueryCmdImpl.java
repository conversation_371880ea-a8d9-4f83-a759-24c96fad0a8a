package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.oc.oms.api.OcBOrderQueryCmd;
import com.jackrain.nea.oc.request.OcBOrderRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @create 2020-07-09
 * @desc
 **/
@Component
@Service(protocol = "dubbo", validation = "true", group = "oc-core", version = "1.0")
public class OcBOrderQueryCmdImpl implements OcBOrderQueryCmd {

    @Autowired
    private QueryOrderService queryOrderService;

    /**
     * 订单列表查询
     *
     * @param ocBOrderRequest 请求参数
     * @return
     */
    @Override
    public ValueHolderV14 queryOrderList(OcBOrderRequest ocBOrderRequest) {
        return queryOrderService.queryOrderList(ocBOrderRequest);
    }

    /**
     * 获取对应零售订单的下单店铺、对应平台店铺档案的平台
     *
     * @param ocBOrderRequest 请求参数
     * @return
     */
    @Override
    public ValueHolderV14 queryPlatform(OcBOrderRequest ocBOrderRequest) {
        return queryOrderService.queryPlatform(ocBOrderRequest);
    }
}
