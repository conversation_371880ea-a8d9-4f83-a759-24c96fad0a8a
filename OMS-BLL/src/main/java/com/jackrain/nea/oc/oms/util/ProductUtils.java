package com.jackrain.nea.oc.oms.util;

import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.redis.util.RedisOpsUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;


/**
 * 商品工具类
 */
public class ProductUtils {

    public static BigDecimal getWeight(ProductSku prodSku) {

        if (prodSku.getWeight() != null && prodSku.getWeight().compareTo(BigDecimal.ZERO) != 0) {
            return prodSku.getWeight().divide(new BigDecimal("1000"), 4, RoundingMode.HALF_UP);
        }
        Object day = RedisOpsUtil.getStrRedisTemplate().opsForValue().get("business_system:ps:product:sku:default:weight");
        if (day == null) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(String.valueOf(day)).divide(new BigDecimal("1000"), 4, RoundingMode.HALF_DOWN);
    }

    public static BigDecimal getFxWeight(BigDecimal weight) {

        if (weight != null && weight.compareTo(BigDecimal.ZERO) != 0) {
            return weight;
        }
        Object day = RedisOpsUtil.getStrRedisTemplate().opsForValue().get("business_system:ps:product:sku:default:weight");
        if (day == null) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(String.valueOf(day));
    }
}
