#!/bin/bash

# 生成唯一的目录路径
unique_dir="${LOCAL_PATH}/app/log/jvm"
# 创建唯一的目录
mkdir -p "$unique_dir"

BASE_OPTION="-Ddubbo.consumer.check=false -Ddubbo.protocol.name=dubbo -Xargencoding:utf8 -Xconcurrentbackground4 -Xgcthreads4 -Xgc:concurrentScavenge -Xshareclasses:utilities -Xnoaot -Xjit:verbose,vlog=vlogfile -XX:+IgnoreUnrecognizedVMOptions -XX:+OriginalJDK8HeapSizeCompatibilityMode -XX:HeapDumpPath=${unique_dir}/heapdump.hprof"

if [ -z "${BIZ_OPTS}" ]; then
    BIZ_OPTS="-Xms2g -Xmx4g -Dnacos.config.server-addr=************:28848 -Dspring.cloud.nacos.discovery.server-addr=************:28848 -Ddubbo.registry.address=nacos://************:28848 -Ddubbo.metadata-report.address=nacos://************:28848 -Ddubbo.config-center.address=nacos://************:28848 -Ddubbo.protocol.port=-1"
    echo "Use default jvm options: ${JAVA_OPTS}"
else
    echo "Use custom jvm options: ${JAVA_OPTS}"
fi
JAVA_OPTS="${BIZ_OPTS} ${BASE_OPTION}"

exec ${JAVA_HOME}/bin/java ${JAVA_OPTS} -Djava.security.egd=file:/dev/./urandom -jar ${APP_NAME}.jar