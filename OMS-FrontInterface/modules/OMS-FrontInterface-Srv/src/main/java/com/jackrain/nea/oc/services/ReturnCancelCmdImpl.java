package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.api.ReturnCancelCmd;
import com.jackrain.nea.oc.oms.services.ReturnCancelService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: 夏继超
 * @since: 2019/3/25
 * create at : 2019/3/25 16:54
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class ReturnCancelCmdImpl implements ReturnCancelCmd {
    @Autowired
    ReturnCancelService returnCancelService;

    @Override
    public ValueHolderV14 returnCancel(JSONObject jsonObject, User user) {
        return returnCancelService.returnCancel(jsonObject, user);
    }

    /* *//**
     * 退货单作废按钮
     * @param obj 传入的参数
     * @param user  当前的用户
     * @return 返回的数据
     *//*
    @Override
    public ValueHolderV14 invalidButton(JSONObject obj, User user) {
        return returnCancelService.invalidButton(obj, user);
    }*/
}
