package com.jackrain.nea.oc.services.patrol;

import com.jackrain.nea.oc.oms.api.patrol.CheckDirtyDataCmd;
import com.jackrain.nea.oc.oms.services.patrol.CheckDirtyData;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Description:
 *
 * <AUTHOR> 孙继东
 * @since : 2019-06-06
 * create at : 2019-06-06 10:45
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class CheckDirtyDataImpl implements CheckDirtyDataCmd {
    @Autowired
    private CheckDirtyData checkDirtyData;

    @Override
    public ValueHolderV14 checkReturn1() {
        return checkDirtyData.checkReturn1();
    }

    @Override
    public ValueHolderV14 checkReturn2() {
        return checkDirtyData.checkReturn2();
    }

    @Override
    public ValueHolderV14 checkReturn3() {
        return checkDirtyData.checkReturn3();
    }
}
