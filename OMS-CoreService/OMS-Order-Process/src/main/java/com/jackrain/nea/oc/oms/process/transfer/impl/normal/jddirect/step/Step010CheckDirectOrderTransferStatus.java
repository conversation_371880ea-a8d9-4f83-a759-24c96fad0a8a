package com.jackrain.nea.oc.oms.process.transfer.impl.normal.jddirect.step;

import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpJingdongDirectOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongDirect;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Author: 黄世新
 * @Date: 2022/3/28 下午3:18
 * @Version 1.0
 */
@Slf4j
@Component
@Step(order = 10, description = "检验厂直订单中间表状态")
public class Step010CheckDirectOrderTransferStatus extends BaseJingdongDirectOrderProcessStep implements IOmsOrderProcessStep<IpJingdongDirectOrderRelation> {
    @Override
    public ProcessStepResult<IpJingdongDirectOrderRelation> startProcess(IpJingdongDirectOrderRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        String orderNo = orderInfo.getOrderNo();
        //查询数据
        IpJingdongDirectOrderRelation relation = ipJingdongDirectService.selectDirectOrderRelation(orderNo);
        if (relation == null || relation.getIpBJingdongDirect() == null) {
            return new ProcessStepResult<>(StepStatus.FAILED, "京东厂直订单为空吗,退出转换");
        }
        orderInfo.setIpBJingdongDirect(relation.getIpBJingdongDirect());
        orderInfo.setIpBJingdongDirectItems(relation.getIpBJingdongDirectItems());
        IpBJingdongDirect ipBJingdongDirect = relation.getIpBJingdongDirect();
        int currentStatus = ipBJingdongDirect.getIstrans();
        if (TransferOrderStatus.TRANSFERRED.toInteger() == currentStatus) {
            String operateMessage = Resources.getMessage("单据" + relation.getOrderId() + "状态=已转换，转换完成");
            return new ProcessStepResult<>(StepStatus.FINISHED, operateMessage);
        } else if (TransferOrderStatus.TRANSFERRING.toInteger() == currentStatus) {
            String operateMessage = Resources.getMessage("单据" + relation.getOrderId() + "正在转换中");
            return new ProcessStepResult<>(StepStatus.FINISHED, operateMessage);
        } else {
            String operateMessage = Resources.getMessage("单据" + relation.getOrderId() + "检查状态成功，进入下一阶段");
            return new ProcessStepResult<>(StepStatus.SUCCESS, operateMessage);
        }

    }
}
