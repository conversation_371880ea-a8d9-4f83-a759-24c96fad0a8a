package com.jackrain.nea.oc.oms.api;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.model.request.OrderHoldRequest;
import com.jackrain.nea.oc.oms.model.result.OrderDetentionFutureResult;
import com.jackrain.nea.oc.oms.vo.ExecuteErrorVO;
import com.jackrain.nea.oc.oms.vo.OcBOrderImpVO;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * @author: 江家雷
 * @since: 2020/07/04
 * create at : 2020/07/04
 */
@Component
public interface OrderHoldCmd {
    /**
     * 手工取消HOLD单
     *
     * @param object
     * @param operateUser
     * @return
     * @throws NDSException
     */
    ValueHolder manualUnHoldOrder(JSONObject object, User operateUser) throws NDSException;

    /**
     * 手工HOLD单
     *
     * @param request
     * @param operateUser
     * @return
     * @throws NDSException
     */
    ValueHolder manualHoldOrder(OrderHoldRequest request, User operateUser) throws NDSException;

    /**
     * <AUTHOR>
     * @Date 15:39 2021/7/28
     * @Description 卡单手动释放
     */
    ValueHolder orderDetentionRelease(JSONObject obj, User user);

    /**
     * <AUTHOR>
     * @Date 15:39 2021/7/28
     * @Description 手动卡单
     */
    ValueHolder orderDetention(JSONObject obj, User user);


    OrderDetentionFutureResult orderDetentionFuture(JSONObject obj, User operateUser);


    /**
     * 异常错误信息导出
     *
     * @param ocBOrderList 订单列表信息
     * @param user         用户信息
     * @return
     */
    String exportImpErrorResult(List<ExecuteErrorVO> ocBOrderList, User user, String origFileName);
}
