package com.jackrain.nea.oc.oms.services.delivery.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.services.OrderPlatformDeliveryService;
import com.jackrain.nea.oc.oms.services.delivery.OrderDeliveryCmd;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Description:猫超平台发货实现类
 *
 * <AUTHOR> sunies
 * @since : 2020-11-03
 * create at : 2020-11-03 20:02
 */
@Slf4j
@Component
public class OrderDeliveryOfAscpImpl implements OrderDeliveryCmd {
    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;

    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Autowired
    private OrderPlatformDeliveryService orderPlatformDeliveryService;

    @Override
    public boolean deliveryDeal(OcBOrderRelation ocBOrderRelation, List<String> tips) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("猫超平台发货服务,订单id为={}", "猫超平台发货服务", ocBOrderRelation.getOrderId()), ocBOrderRelation.getOrderId());
        }

        OcBOrder ocBOrder = ocBOrderRelation.getOrderInfo();
        ValueHolderV14 vh = orderPlatformDeliveryService.alibabaAscpShippingBack(ocBOrderRelation);
        String msg = vh.getMessage();
        omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.WAREHOUSE_DELIVERY_CALL_INTERFACE.getKey(), ocBOrder.getId() + "猫超仓库发货接口接收云枢纽返回信息为：" + msg, null, msg, SystemUserResource.getRootUser());
        if (vh.getCode() == ResultCode.SUCCESS) {
            ocBOrderMapper.updateOrderAfterPlatDeliverySuccess(ocBOrder.getId());
        } else {
            JSONObject retData = (JSONObject) vh.getData();
            if (retData != null) {
                JSONArray failArray = retData.getJSONArray("failed_list");
                if (failArray != null && !failArray.isEmpty()) {
                    JSONObject tmpJson = failArray.getJSONObject(0);
                    msg = tmpJson.getString("msg");
                }
            }
            OcBOrder orderFlag = new OcBOrder();
            orderFlag.setId(ocBOrder.getId());
            orderFlag.setBillNo(ocBOrder.getBillNo());
            orderFlag.setSysremark(msg);
            orderFlag.setIsForce(0L);
            orderFlag.setForceSendFailReason(vh.getMessage());
            orderFlag.setMakeupFailNum(ocBOrder.getMakeupFailNum() + 1);
            ocBOrderMapper.updateById(ocBOrder);
            ocBOrderItemMapper.updateFailTimesByOrderid(ocBOrder.getId());
        }
        return false;
    }


}
