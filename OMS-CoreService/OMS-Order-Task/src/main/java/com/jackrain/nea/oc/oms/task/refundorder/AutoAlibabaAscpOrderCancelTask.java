package com.jackrain.nea.oc.oms.task.refundorder;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.config.TransferOrderMqConfig;
import com.jackrain.nea.oc.oms.mapper.IpBAlibabaAscpOrderCancelMapper;
import com.jackrain.nea.oc.oms.model.relation.IpBAlibabaAscpOrderCancelRelation;
import com.jackrain.nea.oc.oms.process.transfer.impl.refund.alibaba.ascp.cancel.AlibabaAscpOrderCancelTransferProcessImpl;
import com.jackrain.nea.oc.oms.services.IpBAlibabaAscpOrderCancelService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @description: 猫超直发取消订单转单补偿任务
 * @author: 郑小龙
 * @date: 2020-06-04 12:13
 **/

@Slf4j
@Component
public class AutoAlibabaAscpOrderCancelTask extends BaseR3Task implements IR3Task {

    @Autowired
    private IpBAlibabaAscpOrderCancelService orderCancelService;
    @Autowired
    private IpBAlibabaAscpOrderCancelMapper ipBAlibabaAscpOrderCancelMapper;
//    @Autowired
//    private R3MqSendHelper sendHelper;
    @Autowired
    private TransferOrderMqConfig transferMqConfig;
    @Autowired
    private AlibabaAscpOrderCancelTransferProcessImpl cancelTransferProcess;

    @Override
    @XxlJob("AutoAlibabaAscpOrderCancelTask")
    public RunTaskResult execute(JSONObject params) {
        log.debug(LogUtil.format("AutoAlibabaAscpOrderCancelTask",  "execute方法开始执行"));
        RunTaskResult result = new RunTaskResult();
        long starTime = System.currentTimeMillis();
        try {
//            //一次捞取猫超直发取消订单的数量
            List<String> list = orderCancelService.selectAlibabaAscpCancelOrder(0, DEFAULT_PAGE_SIZE);
            List<IpBAlibabaAscpOrderCancelRelation> relations = new ArrayList<>();
            for (String refundId : list) {
                if (StringUtils.isBlank(refundId)) {
                    continue;
                }
                IpBAlibabaAscpOrderCancelRelation ascpOrderCancelRelation = orderCancelService.getIpBAlibabaAscpOrderCancelRelation(refundId);
                if (ascpOrderCancelRelation != null) {
                    relations.add(ascpOrderCancelRelation);
                }
            }
            //2.多线程处理订单数据
            threadOrderProcessor.startMultiThreadExecute(this.cancelTransferProcess, relations);
            result.setSuccess(true);
            long endTime = System.currentTimeMillis();
            long Time = endTime - starTime;
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("猫超直发取消订单定时任务耗时:{}ms",  "猫超直发取消订单"), Time);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("AutoAlibabaAscpOrderCancelTask.Execute Error:,异常：{}", "猫超直发取消订单"), Throwables.getStackTraceAsString(e));
            result.setSuccess(false);
            result.setMessage(e.getMessage());
        }
        return result;
    }
}
