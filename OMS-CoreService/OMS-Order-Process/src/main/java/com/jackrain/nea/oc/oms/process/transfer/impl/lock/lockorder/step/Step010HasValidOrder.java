package com.jackrain.nea.oc.oms.process.transfer.impl.lock.lockorder.step;

import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.IpOrderLockStatusEnum;
import com.jackrain.nea.oc.oms.model.relation.IpOrderLockRelation;
import com.jackrain.nea.oc.oms.model.resources.SysNotesConstant;
import com.jackrain.nea.oc.oms.model.table.IpBOrderLock;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.nums.OrderLockLogTypeEnum;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * @Descroption 判断满足条件的全渠道订单
 * <AUTHOR>
 * @Date 2019/10/9 14:14
 */
@Step(order = 10, description = "判断满足条件的锁单订单")
@Slf4j
@Component
public class Step010HasValidOrder extends BaseLockProcessStep
        implements IOmsOrderProcessStep<IpOrderLockRelation> {
    @Override
    public ProcessStepResult<IpOrderLockRelation> startProcess(IpOrderLockRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        log.info(this.getClass().getName() + " 锁单Id{}判断满足条件的锁单订单", orderInfo.getOrderId());
        IpBOrderLock orderLock = orderInfo.getOrderLock();
        try {
            List<OcBOrder> orderList = orderInfo.getOcBOrders();
            //全渠道订单非空进入下一步,为空判断创建时间超过三天记录锁单失败
            if (CollectionUtils.isEmpty(orderList)) {
                Date createDate = orderLock.getCreationdate();
                Date currentDate = new Date();
                long overTime = 3 * 24 * 60 * 60 * 1000L + createDate.getTime();
                String logMessage = SysNotesConstant.SYS_REMARK50;
                //判断创建时间是否超过三天
                if (overTime < currentDate.getTime()) {
                    logMessage = SysNotesConstant.SYS_REMARK49;
                    ipOrderLockService.updateLockOrder(IpOrderLockStatusEnum.LOCK_FAIL.getKey(),
                            logMessage, null, orderLock);
                } else if (orderLock.getExceptUnlockTime() != null) {
                    Date now = new Date();
                    if (now.getTime() > orderLock.getExceptUnlockTime().getTime()) {
                        logMessage = SysNotesConstant.SYS_REMARK51;
                        ipOrderLockService.updateLockOrder(IpOrderLockStatusEnum.LOCK_FAIL.getKey(),
                                logMessage, null, orderLock);
                    }
                }
                //插入日志
                ipOrderLockService.insetIpOrderLockLog(OrderLockLogTypeEnum.LOCK.getKey(), logMessage, orderLock.getId(), null, operateUser);
                return new ProcessStepResult<>(StepStatus.FINISHED,
                        "单据" + orderInfo.getOrderId() + "转换完成");
            }
            return new ProcessStepResult<>(StepStatus.SUCCESS);
        } catch (Exception e) {
            log.error(LogUtil.format("锁单异常:{}", "锁单异常"), Throwables.getStackTraceAsString(e));
            //修改中间表状态及系统备注
            ipOrderLockService.updateLockErrorLog(orderLock);
            return new ProcessStepResult<>(StepStatus.FAILED, "锁单异常：" + e.getMessage());
        }
    }
}
