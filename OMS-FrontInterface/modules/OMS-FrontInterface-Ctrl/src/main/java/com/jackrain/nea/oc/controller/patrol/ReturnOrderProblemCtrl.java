package com.jackrain.nea.oc.controller.patrol;

import com.jackrain.nea.oc.oms.api.patrol.ReturnOrderProblemCmd;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2019/6/6 10:50 AM
 * @Version 1.0
 */

@Api(value = "ReturnOrderProblemCtrl", tags = "退款完成金额不为0,ooid重复,退换货没有明细")
@Slf4j
@RestController
public class ReturnOrderProblemCtrl {

    @Autowired
    private ReturnOrderProblemCmd returnOrderProblemCmd;

    @ApiOperation(value = "退款完成金额不为0")
    @RequestMapping(value = "/api/cs/oc/oms/v1/selectOrder", method = RequestMethod.POST)
    public String selectReturnOrderIsZero(int page, int size) {
        List<Long> longs = returnOrderProblemCmd.selectOrderItem(page, size);
        if (CollectionUtils.isEmpty(longs)) {
            return null;
        }
        return longs.toString();

    }

    @ApiOperation(value = "明细有重复的ooid")
    @RequestMapping(value = "/api/cs/oc/oms/v1/repeatOoid", method = RequestMethod.POST)
    public String selectReturnOrderRepeatOoid(String begin, String end) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date beginTime = sdf.parse(begin);
        Date endTime = sdf.parse(end);
        List<String> list = returnOrderProblemCmd.selectOrderItemOoid(beginTime, endTime);
        if (CollectionUtils.isEmpty(list)) {
            return "暂无数据";
        }
        return list.toString();
    }


    @ApiOperation(value = "退换货单没有生成明细数据")
    @RequestMapping(value = "/api/cs/oc/oms/v1/notItem", method = RequestMethod.POST)
    public String selectReturnOrderNotItem(String begin, String end) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date beginTime = sdf.parse(begin);
        Date endTime = sdf.parse(end);
        List<String> list = returnOrderProblemCmd.selectReturnOrderNotItem(beginTime, endTime);
        if (CollectionUtils.isEmpty(list)) {
            return "暂无数据";
        }
        return list.toString();
    }
}
