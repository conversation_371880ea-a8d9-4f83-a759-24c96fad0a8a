package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.api.OcBReturnOrderRemarkCmd;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.services.OcBImportReturnRemarkService;
import com.jackrain.nea.oc.oms.services.OcBModifyReturnRemarkService;
import com.jackrain.nea.oc.oms.services.OcBReturnRemarkDownLoadTempService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @author: xiWen.z
 * create at: 2019/9/17 0017
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBReturnOrderRemarkCmdImpl implements OcBReturnOrderRemarkCmd {


    @Autowired
    private OcBReturnRemarkDownLoadTempService ocBReturnRemarkDownLoadTempService;

    @Autowired
    private OcBImportReturnRemarkService ocBImportReturnRemarkService;


    @Autowired
    private OcBModifyReturnRemarkService ocBModifyReturnRemarkService;

    @Override
    public ValueHolderV14 modifySellerRemark(JSONObject jsnObj, User usr) {
        return ocBModifyReturnRemarkService.modifySellerRemark(jsnObj, usr);
    }

    @Override
    public ValueHolderV14 downloadReturnRemarkTemp(User usr) {
        return ocBReturnRemarkDownLoadTempService.downLoadRemarkTemp(usr);
    }

    @Override
    public ValueHolderV14 importReturnRemark(Map<String, OcBReturnOrder> sourceMap, JSONArray osc, boolean cv, User usr) {
        return ocBImportReturnRemarkService.importReturnRemark(sourceMap, osc, cv, usr);
    }

}
