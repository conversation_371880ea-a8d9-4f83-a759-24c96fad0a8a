package com.jackrain.nea.oc.oms.aspect;

import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.nums.ReturnOrderNodeEnum;
import com.jackrain.nea.oc.oms.services.OcBReturnOrderNodeRecordService;
import com.jackrain.nea.resource.SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.Date;

/**
 * className: OcBReturnOrderInsertAspect
 * description: 退换货单新增拦截器，用于创建时间埋点
 *
 * <AUTHOR>
 * create: 2021-12-09
 * @since JDK 1.8
 */
@Aspect
@Component
@Slf4j
public class OcBReturnOrderInsertAspect {

    @Autowired
    private OcBReturnOrderNodeRecordService nodeRecordService;

    @Pointcut("execution(public * com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper.insert(..))")
    public void pointcut() {
    }


        /**
     * 切面处理
     * @param joinPoint
     * @return
     * @throws Throwable
     */
    @AfterReturning(pointcut = "pointcut()", returning = "result")
    public void doAfterReturning(JoinPoint joinPoint, Object result) {
        try {
            int num = (int) result;
            if(num < 1){
                return;
            }

            OcBReturnOrder param = (OcBReturnOrder) joinPoint.getArgs()[0];
            nodeRecordService.insertByNode(ReturnOrderNodeEnum.CREATE_TIME,param.getCreationdate(),
                    param.getId(), SystemUserResource.getRootUser());
            //如果物流单号不为空，插入物流更新时间
            if(!ObjectUtils.isEmpty(param) && !ObjectUtils.isEmpty(param.getId()) &&
                    !ObjectUtils.isEmpty(param.getLogisticsCode())){
                nodeRecordService.insertByNode(ReturnOrderNodeEnum.LOGISTIC_MODIFIED_TIME, new Date(),
                        param.getId(),SystemUserResource.getRootUser());
            }
        }catch (Exception e){
            log.error(" 新增退换货单成功后处理异常：{}", Throwables.getStackTraceAsString(e));
        }
    }

}
