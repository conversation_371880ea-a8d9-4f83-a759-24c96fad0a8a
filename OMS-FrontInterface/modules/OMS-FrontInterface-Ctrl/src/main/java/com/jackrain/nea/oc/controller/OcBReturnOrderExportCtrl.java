package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.api.OcBReturnOrderExportCmd;
import com.jackrain.nea.oc.oms.model.relation.UserPermission;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * @author: 李龙飞
 * @create: 2019-06-03 11:13
 **/
@RestController
@Slf4j
public class OcBReturnOrderExportCtrl {

    @Autowired
    private OcBReturnOrderExportCmd ocBReturnOrderExportCmd;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    /**
     * 列表导出
     */
    @ApiOperation(value = "列表导出")
    @RequestMapping(path = "/api/cs/oc/oms/v1/exportReturnOrder", method = RequestMethod.POST)
    public ValueHolderV14 getOrderList(HttpServletRequest request, @RequestBody JSONObject obj) {

        //获取用户登录信息
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);

        //调用服务
        ValueHolderV14 vh;
        UserPermission usrPem = UserPermissionCtrlHelper.currentUserPermission(user, "OC_B_RETURN_ORDER");

        if (usrPem == null) {
            vh = new ValueHolderV14<>();
            vh.setMessage("未获取到用户权限");
            vh.setCode(ResultCode.FAIL);
            return vh;
        }
        vh = ocBReturnOrderExportCmd.exportReturnOrder(obj, user, usrPem);
        return vh;

    }


    /**
     * 下载模板
     *
     * @return string str
     * @throws Exception ex
     */
    @ApiOperation(value = "下载模板")
    @RequestMapping(path = "/api/cs/oc/oms/v1/downloadReturnOrderTemp", method = RequestMethod.POST)
    public ValueHolderV14 downloadTemp() {

        //处理返回数据
        ValueHolderV14 holderV14 = ocBReturnOrderExportCmd.downloadTemp();
        return holderV14;
    }

}
