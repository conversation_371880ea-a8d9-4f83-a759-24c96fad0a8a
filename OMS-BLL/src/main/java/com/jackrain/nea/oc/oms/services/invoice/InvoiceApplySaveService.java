package com.jackrain.nea.oc.oms.services.invoice;


import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.ac.service.converter.ApplyInvoiceConvert;
import com.jackrain.nea.core.db.Tools;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.gsi.GSI4Order;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.ac.AcFInvoiceApplyItemMapper;
import com.jackrain.nea.oc.oms.mapper.ac.AcFInvoiceApplyMapper;
import com.jackrain.nea.oc.oms.model.constant.InvoiceConst;
import com.jackrain.nea.oc.oms.model.table.AcFInvoiceApply;
import com.jackrain.nea.oc.oms.model.table.AcFInvoiceApplyItem;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BaseModelUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class InvoiceApplySaveService {

    @Autowired
    private AcFInvoiceApplyMapper invoiceApplyMapper;

    @Autowired
    private ApplyInvoiceConvert applyInvoiceConvert;

    @Autowired
    private OcBOrderMapper orderMapper;

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private AcFInvoiceApplyItemMapper acInvoiceApplyItemMapper;

    /**
     * description:保存发票申请
     *
     * @Author: liuwenjin
     * @Date 2022/10/9 15:37
     */
    @Transactional
    public ValueHolderV14 save(List<AcFInvoiceApply> list) {
        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, "保存发票申请成功！");
        if (CollectionUtils.isEmpty(list)) {
            v14.setMessage("保存发票申请入参为空");
            v14.setCode(ResultCode.FAIL);
            return v14;
        }
        if (log.isDebugEnabled()) {
            log.debug("InvoiceApplySaveService.save param:{}", JSONObject.toJSONString(list));
        }
        CpShop cpShop = cpRpcService.selectShopById(list.get(0).getCpCShopId());
        if (cpShop == null) {
            return new ValueHolderV14<>(com.jackrain.nea.constants.ResultCode.FAIL, "当前开票店铺不存在或不可用!");
        }
        int n = 0;
        try {
            List<String> tidList = list.stream().map(AcFInvoiceApply::getTid).collect(Collectors.toList());
            //去重
            List<String> tids = invoiceApplyMapper.selectLisByTid(tidList);
            if (log.isDebugEnabled()) {
                log.debug("InvoiceApplySaveService.save tids:{}", JSONObject.toJSONString(tids));
            }
            List<String> newTids = new ArrayList<>();
            List<AcFInvoiceApplyItem> insertItems = new ArrayList<>();
            //赋值
            for (AcFInvoiceApply acFInvoiceApply : list) {
                String tid = acFInvoiceApply.getTid();
                if (CollectionUtils.isEmpty(tids) || !tids.contains(tid)) {
                    Long mainId = ModelUtil.getSequence(InvoiceConst.AC_F_INVOICE_APPLY);
                    acFInvoiceApply.setId(mainId);
                    acFInvoiceApply.setApplyBillNo(applyInvoiceConvert.getBillNo());
                    acFInvoiceApply.setCpCShopEcode(cpShop.getEcode());
                    acFInvoiceApply.setCpCShopTitle(cpShop.getCpCShopTitle());
                    acFInvoiceApply.setCpCPlatformEcode(cpShop.getCpCPlatformEcode());
                    acFInvoiceApply.setCpCPlatformEname(cpShop.getCpCPlatformEname());
                    String invoiceKind = acFInvoiceApply.getInvoiceKind();
                    acFInvoiceApply.setInvoiceKind(invoiceKind.equals("0") ? "1" : invoiceKind);
                    acFInvoiceApply.setTransStatus(InvoiceConst.TransStatus.NOT_TRANS);
                    acFInvoiceApply.setNextTime(new Date());
                    BaseModelUtil.initialBaseModelSystemField(acFInvoiceApply, SystemUserResource.getRootUser());
                    newTids.add(tid);
                    //明细数据
                    AcFInvoiceApplyItem applyItem = new AcFInvoiceApplyItem();
                    applyItem.setId(Tools.getSequence(InvoiceConst.AC_F_INVOICE_APPLY_ITEM));
                    applyItem.setAmt(acFInvoiceApply.getInvoiceAmt());
                    applyItem.setTid(tid);
                    applyItem.setAcFInvoiceApplyId(mainId);
                    applyItem.setCpCShopId(cpShop.getCpCShopId());
                    BaseModelUtil.initialBaseModelSystemField(applyItem,  SystemUserResource.getRootUser());
                    insertItems.add(applyItem);
                }
            }
            //批量插入数据
            list = list.stream().filter(o -> CollectionUtils.isEmpty(tids) || !tids.contains(o.getTid())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(list)){
                n = invoiceApplyMapper.batchInsert(list);
                //插入明细
                acInvoiceApplyItemMapper.batchInsert(insertItems);
                List<Long> idList = GSI4Order.getIdListBySourceCodes(tidList);
                //批量修改
                if (CollectionUtils.isNotEmpty(idList)) {
                    orderMapper.updateOrderInvoice(idList);
                }
            }
        } catch (Exception e) {
            log.error("保存发票申请异常{}", Throwables.getStackTraceAsString(e));
            v14.setMessage("保存发票申请异常！");
            v14.setCode(ResultCode.FAIL);
            new NDSException("保存发票申请异常!");
        }
        v14.setMessage("保存发票入参" + list.size() + "条! 成功了 " + n + "");
        v14.setCode(ResultCode.SUCCESS);
        if (log.isDebugEnabled()) {
            log.debug("InvoiceApplySaveService.save 返回结果:{}", JSONObject.toJSONString(v14));
        }
        return v14;
    }
}
