package com.jackrain.nea.oc.oms.model.request;

import com.jackrain.nea.web.face.User;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version :1.0
 * description ： 价格取值查询request
 * @date :2019/7/19 15:30
 */
@Data
public class TimeOrderVoidSgSendRequest implements Serializable {

    private static final long serialVersionUID = 6615617147597146577L;
    /**
     * 唯品会订单号
     */
    private String orderSn;

    /**
     * 拣货单号
     */
    private String pickNo;

    /**
     * 单据编号
     */
    private String occupiedOrderSn;

    /**
     * user
     */
    private User user;

    /**
     * 操作备注
     */
    private String remark;

    /**
     * 是否取消
     */
    private Boolean isCancel;
}
