package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.api.BillCopyCmd;
import com.jackrain.nea.oc.oms.services.BillCopyBatchService;
import com.jackrain.nea.oc.oms.services.BillCopyService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: wangqiang
 * @Date: 2019-03-06 15:02
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class BillCopyCmdImpl extends CommandAdapter implements BillCopyCmd {
    @Autowired
    BillCopyService billCopyService;
    @Autowired
    BillCopyBatchService billCopyBatchService;

    @Override
    public ValueHolder billCopy(JSONObject obj, User user) {
        return billCopyBatchService.billCopy(obj, user);
    }
}
