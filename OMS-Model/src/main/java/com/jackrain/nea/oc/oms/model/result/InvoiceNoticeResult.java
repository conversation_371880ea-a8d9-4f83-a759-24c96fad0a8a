package com.jackrain.nea.oc.oms.model.result;

import com.jackrain.nea.oc.oms.model.table.OcBInvoiceNoticePro;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: huang.z<PERSON><PERSON>
 * @Date: 2019-03-12
 * @Version 1.0
 */
@Data
public class InvoiceNoticeResult implements Serializable {
    private OcBInvoiceNoticeResult invoiceNotice;
    private List<OcBInvoiceNoticePro> invoiceNoticeProList;
    private List<OcBInvoiceNoticeItemResult> invoiceNoticeItemList;
    private List<OcBInvoiceNoticeLogResult> invoiceNoticeLogList;
}
