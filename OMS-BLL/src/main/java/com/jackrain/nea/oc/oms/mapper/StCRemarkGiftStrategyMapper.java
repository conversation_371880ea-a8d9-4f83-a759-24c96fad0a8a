package com.jackrain.nea.oc.oms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jackrain.nea.oc.oms.model.table.StCRemarkGiftStrategy;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 卖家备注赠品策略
 *
 * <AUTHOR>
 */
@Mapper
public interface StCRemarkGiftStrategyMapper extends BaseMapper<StCRemarkGiftStrategy> {

    @Select("SELECT * FROM st_c_remark_gift_strategy WHERE shop_id = #{shopId} and ISACTIVE = 'Y'")
    List<StCRemarkGiftStrategy> selectByShopId(@Param("shopId") Long shopId);

    @Update("UPDATE st_c_remark_gift_strategy SET ISACTIVE = 'Y',modifieddate = now() WHERE ID = #{id}")
    int activeStrategy(Long id);

    @Update("UPDATE st_c_remark_gift_strategy SET ISACTIVE = 'N',modifieddate = now() WHERE ID = #{id}")
    int voidStrategy(Long id);

    @Select("SELECT * FROM st_c_remark_gift_strategy WHERE shop_id = #{shopId} and ISACTIVE = 'Y' and match_keyword = #{matchKeyword}")
    List<StCRemarkGiftStrategy> selectByShopIdAndKeyword(@Param("shopId") Long shopId, @Param("matchKeyword") String matchKeyword);
}