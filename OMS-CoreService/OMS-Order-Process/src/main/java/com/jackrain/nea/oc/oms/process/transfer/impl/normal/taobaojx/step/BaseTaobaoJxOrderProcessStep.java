package com.jackrain.nea.oc.oms.process.transfer.impl.normal.taobaojx.step;

import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.MakeupOrderType;
import com.jackrain.nea.oc.oms.services.IpTaobaoJxOrderService;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.util.LockOrderType;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.rpc.CpRpcService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @Description 淘宝经销订单转单抽象类
 **/
public abstract class BaseTaobaoJxOrderProcessStep {
    /**
     * 淘宝经销转单服务
     **/
    @Autowired
    protected IpTaobaoJxOrderService ipTaobaoJxOrderService;
    /**
     * 全渠道订单服务
     **/
    @Autowired
    protected OmsOrderService omsOrderService;
    /**
     * 商品服务
     **/
    @Autowired
    protected PsRpcService psRpcService;
    @Autowired
    protected CpRpcService cpRpcService;

    protected ChannelType getCurrentChannelType() {
        return ChannelType.TAOBAO;
    }

    protected MakeupOrderType getCurrentMakeupOrderType() {
        return MakeupOrderType.TRANSFER_ORDER;
    }

    protected LockOrderType getCurrentLockOrderType() {
        return LockOrderType.TRANSFER_TAOBAO_JX_ORDER;
    }
}
