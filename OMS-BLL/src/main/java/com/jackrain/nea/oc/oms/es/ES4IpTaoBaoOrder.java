package com.jackrain.nea.oc.oms.es;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/11/11 5:11 下午
 * @description
 * @since version -1.0
 */
@Slf4j
public class ES4IpTaoBaoOrder {


    /**
     * 根据转换状态和系统备注从ES中查询未转换成功的单据信息
     *
     * @param pageIndex 页码
     * @param pageSize  每页大小
     * @return 单据编号列表
     */
    public static List<String> selectUnTransferredOrderFromEs(int pageIndex, int pageSize) {
        List<String> orderNoList = new ArrayList<>();
        try {
            JSONObject whereKeys = new JSONObject();
            whereKeys.put("ISTRANS", "0");
//            whereKeys.put("SYSREMARK", null);

            String[] returnFieldNames = new String[]{"TID"};
            JSONArray orderKeys = new JSONArray();
            JSONObject orderKey = new JSONObject();
            // 按照倒序进行查询
            orderKey.put("asc", false);
            orderKey.put("name", "CREATIONDATE");
            orderKeys.add(orderKey);
            int startIndex = pageIndex * pageSize;
            if (startIndex < 0) {
                startIndex = 0;
            }

            JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.IP_B_TAOBAO_ORDER_INDEX_NAME,
                    OcElasticSearchIndexResources.IP_B_TAOBAO_ORDER_TYPE_NAME,
                    whereKeys, null, orderKeys,
                    pageSize, startIndex, returnFieldNames);

            if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
                JSONArray arrayObj = search.getJSONArray("data");

                for (Object obj : arrayObj) {
                    JSONObject jsonObject = (JSONObject) obj;
                    String orderNo = jsonObject.getString("TID");
                    orderNoList.add(orderNo);
                }
            }

        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(LogUtil.format("selectUnTransferredOrderFromEs.异常: {}"), Throwables.getStackTraceAsString(ex));
        }
        return orderNoList;
    }


    /**
     * 查询转换失败的淘宝订单
     * 按照修改时间升序，取长时间未被转换过的单据
     *
     * @param pageIndex 页码
     * @param pageSize  每页大小
     * @return 单据编号列表
     */
    public static List<String> selectTransferExceptionOrderFromEsOfTb(int pageIndex, int pageSize) {
        List<String> tidList = Lists.newArrayList();
        try {
            JSONObject whereKeys = new JSONObject();
            whereKeys.put("ISTRANS", "5");
            JSONArray orderKeys = new JSONArray();
            JSONObject orderKey = new JSONObject();
            orderKey.put("asc", true);
            orderKey.put("name", "MODIFIEDDATE");
            orderKeys.add(orderKey);
            int startIndex = pageIndex * pageSize;
            if (startIndex < 0) {
                startIndex = 0;
            }

            JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.IP_B_TAOBAO_ORDER_INDEX_NAME,
                    OcElasticSearchIndexResources.IP_B_TAOBAO_ORDER_TYPE_NAME,
                    whereKeys, null, orderKeys,
                    pageSize, startIndex, new String[]{"TID"});

            JSONArray data = search.getJSONArray("data");
            if (CollectionUtils.isNotEmpty(data)) {
                tidList = data.stream().map(a -> ((JSONObject) a).getString("TID")).collect(Collectors.toList());
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("selectTransferExceptionOrderFromEs.异常: {}"), Throwables.getStackTraceAsString(ex));
        }
        return tidList;
    }


    /**
     * 查询转换失败的京东订单
     * 按照修改时间升序，取长时间未被转换过的单据
     *
     * @param pageIndex 页码
     * @param pageSize  每页大小
     * @return 单据编号列表
     */
    public static List<String> selectTransferExceptionOrderFromEsOfJd(int pageIndex, int pageSize) {
        try {
            JSONObject whereKeys = new JSONObject();
            whereKeys.put("ISTRANS", "5");
            JSONArray orderKeys = new JSONArray();
            JSONObject orderKey = new JSONObject();
            orderKey.put("asc", true);
            orderKey.put("name", "MODIFIEDDATE");
            orderKeys.add(orderKey);
            int startIndex = pageIndex * pageSize;
            if (startIndex < 0) {
                startIndex = 0;
            }

            JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.IP_B_JINGDONG_ORDER_INDEX_NAME,
                    OcElasticSearchIndexResources.IP_B_JINGDONG_ORDER_TYPE_NAME,
                    whereKeys, null, orderKeys,
                    pageSize, startIndex, new String[]{"ORDER_ID"});

            JSONArray data = search.getJSONArray("data");
            if (CollectionUtils.isNotEmpty(data)) {
                return data.stream().map(a -> ((JSONObject) a).getString("ORDER_ID")).collect(Collectors.toList());
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("selectTransferExceptionOrderFromEs.异常: {}"), Throwables.getStackTraceAsString(ex));
        }
        return Lists.newArrayList();
    }

    /**
     * 查询转换失败的通用平台订单
     * 按照修改时间升序，取长时间未被转换过的单据
     *
     * @param pageIndex 页码
     * @param pageSize  每页大小
     * @return 单据编号列表
     */
    public static List<String> selectTransferExceptionOrderFromEsOfStandard(int pageIndex, int pageSize) {
        try {
            JSONObject whereKeys = new JSONObject();
            whereKeys.put("ISTRANS", "5");
            JSONArray orderKeys = new JSONArray();
            JSONObject orderKey = new JSONObject();
            orderKey.put("asc", true);
            orderKey.put("name", "MODIFIEDDATE");
            orderKeys.add(orderKey);
            int startIndex = pageIndex * pageSize;
            if (startIndex < 0) {
                startIndex = 0;
            }

            JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.IP_B_STANDPLAT_ORDER_INDEX_NAME,
                    OcElasticSearchIndexResources.IP_B_STANDPLAT_ORDER_TYPE_NAME,
                    whereKeys, null, orderKeys, pageSize, startIndex, new String[]{"TID"});

            JSONArray data = search.getJSONArray("data");
            if (CollectionUtils.isNotEmpty(data)) {
                return data.stream().map(a -> ((JSONObject) a).getString("TID")).collect(Collectors.toList());
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("selectTransferExceptionOrderFromEs.异常: {}"), Throwables.getStackTraceAsString(ex));
        }
        return Lists.newArrayList();
    }
}
