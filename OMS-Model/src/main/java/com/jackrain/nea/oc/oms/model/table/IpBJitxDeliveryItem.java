package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

@TableName(value = "ip_b_jitx_delivery_item")
@Data
@Document(index = "ip_b_jitx_delivery", type = "ip_b_jitx_delivery_item")
public class IpBJitxDeliveryItem extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "IP_B_JITX_DELIVERY_ID")
    @Field(type = FieldType.Long)
    private Long ipBJitxDeliveryId;

    @JSONField(name = "BARCODE")
    @Field(type = FieldType.Keyword)
    private String barcode;

    @JSONField(name = "PRODUCT_NAME")
    @Field(type = FieldType.Keyword)
    private String productName;

    @JSONField(name = "BRAND_NAME")
    @Field(type = FieldType.Keyword)
    private String brandName;

    @JSONField(name = "SIZE")
    @Field(type = FieldType.Long)
    private Long size;

    @JSONField(name = "QUANTITY")
    @Field(type = FieldType.Long)
    private Long quantity;

    @JSONField(name = "PO_NO")
    @Field(type = FieldType.Keyword)
    private String poNo;

    @JSONField(name = "CSIZE")
    @Field(type = FieldType.Keyword)
    private String csize;

    @JSONField(name = "COOPERATION_NO")
    @Field(type = FieldType.Keyword)
    private String cooperationNo;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;
}