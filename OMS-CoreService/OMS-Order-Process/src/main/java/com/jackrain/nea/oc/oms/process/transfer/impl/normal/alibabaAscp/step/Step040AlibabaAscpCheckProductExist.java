package com.jackrain.nea.oc.oms.process.transfer.impl.normal.alibabaAscp.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpAlibabaAscpOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBAlibabaAscpOrderItemExt;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * @author: 秦雄飞
 * @date 2020/9/3 下午4:32
 */
@Step(order = 40, description = "商品条形码是否能找到对应商品")
@Component
@Slf4j
public class Step040AlibabaAscpCheckProductExist extends BaseAlibabaAscpOrderProcessStep
        implements IOmsOrderProcessStep<IpAlibabaAscpOrderRelation> {

    @Override
    public ProcessStepResult<IpAlibabaAscpOrderRelation> startProcess(IpAlibabaAscpOrderRelation orderInfo,
                                                                      ProcessStepResult preStepResult,
                                                                      boolean isAutoMakeup, User operateUser) {
        String orderNo = orderInfo.getOrderNo();
        boolean hasErrorInfo = false;
        StringBuilder sbErrorInfo = new StringBuilder();

        for (IpBAlibabaAscpOrderItemExt alibabaAscpOrderItemEx : orderInfo.getAlibabaAscpOrderItemExList()) {
            String skuId = alibabaAscpOrderItemEx.getBarcode();
            if (StringUtils.isEmpty(skuId)) {
                hasErrorInfo = true;
                sbErrorInfo.append("商品SkuId为空;");
                sbErrorInfo.append("\r\n");
            } else {
                ProductSku skuInfo = psRpcService.selectProductSku(skuId);
                if (skuInfo == null) {
                    hasErrorInfo = true;
                    sbErrorInfo.append("找不到商品条形码为" + alibabaAscpOrderItemEx.getBarcode()).append("的商品SKU;");
                    sbErrorInfo.append("\r\n");
                } else {
                    alibabaAscpOrderItemEx.setProdSku(skuInfo);
                }
            }
        }
        if (hasErrorInfo) {
            String errorMessage = "商品数据不存在，退出转单操作";
            ipAlibabaAscpOrderService.updateAlibabaAscpOrderTransStatus(orderNo,
                    TransferOrderStatus.TRANSFERFAIL, sbErrorInfo.toString());
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        } else {
            return new ProcessStepResult<>(StepStatus.SUCCESS, "检查商品是否存在成功，进入下一阶段");
        }
    }
}