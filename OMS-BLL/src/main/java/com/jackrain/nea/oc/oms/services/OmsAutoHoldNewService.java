package com.jackrain.nea.oc.oms.services;

import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Maps;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.config.OmsSystemConfig;
import com.jackrain.nea.oc.oms.mapper.OcBOrderHoldItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OcOrderTagEum;
import com.jackrain.nea.oc.oms.model.enums.OrderDetentionEnum;
import com.jackrain.nea.oc.oms.model.enums.OrderHoldEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderHoldConst;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderParam;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.SpiltOrderParam;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderHoldItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.StCHoldOrderDO;
import com.jackrain.nea.oc.oms.model.table.StCHoldOrderItemDO;
import com.jackrain.nea.oc.oms.model.table.StCHoldOrderReason;
import com.jackrain.nea.oc.oms.model.table.StCHoldProvinceItemDO;
import com.jackrain.nea.oc.oms.nums.OcBOrderNodeEnum;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.advance.AdvanceConstant;
import com.jackrain.nea.oc.oms.util.DateConversionUtil;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.psext.result.Dim8Result;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.st.model.StAutoHoldRelation;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.st.service.OmsStCShopStrategyService;
import com.jackrain.nea.st.service.StAutoHoldNewService;
import com.jackrain.nea.st.service.StCHoldOrderReasonQueryService;
import com.jackrain.nea.st.service.StCHoldOrderSaveService;
import com.jackrain.nea.util.OmsBusinessTypeUtil;
import com.jackrain.nea.util.OrderUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.BoundSetOperations;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2022/8/6 下午1:55
 * @Version 1.0
 * 新写的自动hold单 及卡单逻辑
 */
@Component
@Slf4j
public class OmsAutoHoldNewService {

    @Autowired
    private StAutoHoldNewService stAutoHoldNewService;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    private final String TAB_OC_B_ORDER_HOLD_ITEM = "oc_b_order_hold_item";
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderHoldItemMapper ocBOrderHoldItemMapper;
    @Autowired
    private OmsAutoHoldNewService omsAutoHoldNewService;
    @Autowired
    private OmsStCShopStrategyService omsStCShopStrategyService;
    @Autowired
    private OmsOccupyTaskService omsOccupyTaskService;
    @Autowired
    private OmsSystemConfig omsSystemConfig;
    @Autowired
    private OcBOrderNodeRecordService ocBOrderNodeRecordService;
    @Autowired
    private StCHoldOrderReasonQueryService holdOrderReasonQueryService;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private PsRpcService psRpcService;
    @Autowired
    private StCHoldOrderSaveService stCHoldOrderSaveService;

    /**
     * 自动卡单拆
     *
     * @param orderInfo
     * @param user
     * @param cardSt 确保只在寻源卡单策略时执行，其他场景不执行黑名单策略卡单计算
     */
    public Map<Set<Long>, SpiltOrderParam> autoCardOrderService(OcBOrderRelation orderInfo, User user, boolean cardSt) {
        OcBOrder ocBOrder = orderInfo.getOrderInfo();
        //高风险客户打标/黑名单
        if (highRiskOrder(orderInfo, user, ocBOrder)) {
            return null;
        }

        Integer isJcorder = ocBOrder.getIsJcorder();
        if (isJcorder != null && isJcorder == 1) {
            OcBOrder order = new OcBOrder();
            order.setId(ocBOrder.getId());
            order.setIsDetention(AdvanceConstant.DETENTION_STATUS_1);
            order.setDetentionReason(OrderDetentionEnum.JD_SELF_FLOW_CARD.getVal());
            order.setDetentionReasonId(OrderDetentionEnum.JD_SELF_FLOW_CARD.getKey());
            order.setSysremark(OrderDetentionEnum.JD_SELF_FLOW_CARD.getVal());
            ocBOrderMapper.updateById(order);
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                    OrderLogTypeEnum.ADVANCE_DETENTION.getKey(), "京东自流转订单整单卡单", "", "", user);
            return null;
        }
        if (OcBOrderConst.IS_STATUS_IY.equals(ocBOrder.getIsCycle())) {
            OcBOrder order = new OcBOrder();
            order.setId(ocBOrder.getId());
            order.setIsDetention(AdvanceConstant.DETENTION_STATUS_1);
            order.setDetentionReason(OrderDetentionEnum.TMALL_CYCLE_BUY_CARD.getVal());
            order.setDetentionReasonId(OrderDetentionEnum.TMALL_CYCLE_BUY_CARD.getKey());
            order.setDetentionReleaseDate(ocBOrder.getEstimateConTime());
            ocBOrderMapper.updateById(order);
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                    OrderLogTypeEnum.ADVANCE_DETENTION.getKey(), OrderDetentionEnum.TMALL_CYCLE_BUY_CARD.getVal(), "", "", user);
            return null;
        }
        boolean toBOrder = OmsBusinessTypeUtil.isToBOrder(ocBOrder);
        boolean toCOrder = OmsBusinessTypeUtil.isSapToCOrder(ocBOrder);
        if (toBOrder || toCOrder) {
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                    OrderLogTypeEnum.ADVANCE_DETENTION.getKey(), "该业务类型不执行卡单策略", "", "", user);
            return null;
        }
        List<OcBOrderItem> orderItemList = orderInfo.getOrderItemList();
        Map<Integer, List<StAutoHoldRelation>> holdRelationMap = stAutoHoldNewService.selectStAutoHold(ocBOrder);
        if (holdRelationMap == null || holdRelationMap.isEmpty()) {
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                    OrderLogTypeEnum.ADVANCE_DETENTION.getKey(), "未匹配到卡单策略", "", "", user);
            return null;
        }
        Map<Set<Long>, SpiltOrderParam> setSpiltOrderParamMap = handleCardOrder(ocBOrder, orderItemList, holdRelationMap, user, orderInfo, cardSt);
        return setSpiltOrderParamMap;
    }

    /**
     * 高风险客户打标/黑名单
     *
     * @param orderInfo
     * @param user
     * @param ocBOrder
     * @return
     */
    private boolean highRiskOrder(OcBOrderRelation orderInfo, User user, OcBOrder ocBOrder) {
        String receiverMobile = orderInfo.getOrderInfo().getReceiverMobile();
        if (OrderUtil.isMobileCheck(receiverMobile)) {
            CusRedisTemplate<String, String> objRedisTemplate = RedisOpsUtil.getObjRedisTemplate();
            String recordKey = BllRedisKeyResources.buildBlackCardRecordKey(receiverMobile);
            String value = objRedisTemplate.opsForValue().get(recordKey);
            if (StringUtils.isNotBlank(value)) {
                String[] split = value.split(",");
                Double totalOrderNums = Double.valueOf(split[0]);
                BigDecimal totalOrderAmount = new BigDecimal(split[1]);
                log.info(" highRiskOrder id:{}, totalOrderNums:{},limit:{},total:{},amountLimit:{},bool1:{},bool2:{}",ocBOrder.getId(), totalOrderNums, omsSystemConfig.getOrderNumLimit(),
                        totalOrderAmount, omsSystemConfig.getOrderAmountLimit(), (totalOrderNums > omsSystemConfig.getOrderNumLimit()),
                        (totalOrderAmount.compareTo(new BigDecimal(omsSystemConfig.getOrderAmountLimit())) > 0));
                if (!OcOrderTagEum.TAG_HAND.getVal().equals(ocBOrder.getOrderSource()) && totalOrderNums > omsSystemConfig.getOrderNumLimit() && totalOrderAmount.compareTo(new BigDecimal(omsSystemConfig.getOrderAmountLimit())) > 0) {
                    //符合卡单条件，卡单
                    OcBOrder order = new OcBOrder();
                    order.setId(ocBOrder.getId());
                    order.setIsDetention(AdvanceConstant.DETENTION_STATUS_1);
                    order.setDetentionReason(OrderDetentionEnum.BLACKLIST_CARD.getVal());
                    order.setDetentionReasonId(OrderDetentionEnum.BLACKLIST_CARD.getKey());
                    //黑名单用户标记
                    String oldStCCustomLabelId = "";
                    if (StringUtils.isNotEmpty(ocBOrder.getStCCustomLabelId())) {
                        oldStCCustomLabelId = ocBOrder.getStCCustomLabelId() + ",";
                    }
                    String oldStCCustomLabelName = "";
                    if (StringUtils.isNotEmpty(ocBOrder.getStCCustomLabelEname())) {
                        oldStCCustomLabelName = ocBOrder.getStCCustomLabelEname() + ",";
                    }
                    order.setStCCustomLabelId(oldStCCustomLabelId + omsSystemConfig.getStCCustomLabelId());
                    order.setStCCustomLabelEname(oldStCCustomLabelName + omsSystemConfig.getStCCustomLabelEname());
                    ocBOrderMapper.updateById(order);
                    ocBOrderNodeRecordService.insertByNode(OcBOrderNodeEnum.DETENTION_DATE, new Date(), ocBOrder.getId(), user);
                    omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                            OrderLogTypeEnum.ADVANCE_DETENTION.getKey(), "刷单用户黑名单卡单", "", "", user);
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 新的自动hold单
     *
     * @param param
     * @param user
     */
    public void autoHandleHoldOrder(OcBOrderParam param, User user) {
        OcBOrder ocBOrder = param.getOcBOrder();
        boolean toBOrder = OmsBusinessTypeUtil.isToBOrder(ocBOrder);
        boolean toCOrder = OmsBusinessTypeUtil.isUnHoldToCOrder(ocBOrder);
        if (toBOrder || toCOrder) {
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                    OrderLogTypeEnum.ORDER_HOLD.getKey(), "该业务类型不执行hold单策略", "", "", user);
            return;
        }
        List<OcBOrderItem> orderItemList = param.getOrderItemList();
        Map<Integer, List<StAutoHoldRelation>> holdRelationMap = stAutoHoldNewService.selectStAutoHold(ocBOrder);
        if (holdRelationMap == null || holdRelationMap.isEmpty()) {
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                    OrderLogTypeEnum.ORDER_HOLD.getKey(), "未匹配到Hold策略", "", "", user);
            return;
        }
        handleHoldOrder(ocBOrder, orderItemList, holdRelationMap, user);
    }

    public static void main(String[] args) {
        Map<Long, String> map = new HashMap<>();
        System.out.println(map.keySet());
    }

    /**
     * 处理卡单
     *
     * @param ocBOrder
     * @param orderItemList
     * @param holdRelationMap
     * @param user
     */
    private Map<Set<Long>, SpiltOrderParam> handleCardOrder(OcBOrder ocBOrder, List<OcBOrderItem> orderItemList,
                                                            Map<Integer, List<StAutoHoldRelation>> holdRelationMap,
                                                            User user, OcBOrderRelation orderInfo, boolean cardSt) {
        List<StAutoHoldRelation> relations = holdRelationMap.get(1);
        if (CollectionUtils.isEmpty(relations)) {
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                    OrderLogTypeEnum.ADVANCE_DETENTION.getKey(), "未匹配到卡单策略", "", "", user);
            return null;
        }
        Set<Long> itemIds = new HashSet<>();
        List<StCHoldOrderDO> orderDOList = new ArrayList<>();
        for (StAutoHoldRelation relation : relations) {
            StCHoldOrderDO stCHoldOrder = relation.getStCHoldOrder();
            HoldResult result = holdOrderFlag(ocBOrder, orderItemList, relation, cardSt);
            if (!result.isHold()) {
                continue;
            }
            itemIds.addAll(result.getItemIds());
            orderDOList.add(stCHoldOrder);
        }
        if (CollectionUtils.isEmpty(itemIds)) {
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                    OrderLogTypeEnum.ADVANCE_DETENTION.getKey(), "订单明细不满足策略", "", "", user);
            return null;
        }
        List<Date> dates = new ArrayList<>();
        List<String> holdName = new ArrayList<>();
        List<Integer> holdIds = new ArrayList<>();
        for (StCHoldOrderDO stCHoldOrderDO : orderDOList) {
            Date date = buildReleaseTime(ocBOrder, stCHoldOrderDO, orderItemList);
            if (date != null) {
                dates.add(date);
            }
            holdName.add(stCHoldOrderDO.getEname());
            holdIds.add(stCHoldOrderDO.getHoldDetentionOrderReason());
        }
        Date date = null;
        if (CollectionUtils.isNotEmpty(dates)) {
            date = Collections.max(dates);
        }
        String holdNameListStr = null;
        if (CollectionUtils.isNotEmpty(holdName)) {
            holdNameListStr = StringUtils.join(holdName, ",");
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                    OrderLogTypeEnum.ADVANCE_DETENTION.getKey(), "订单卡单策略命中，策略名称：" + holdNameListStr, "", "", user);
        }
        
        //判断是否需要拆单
        if (CollectionUtils.isNotEmpty(itemIds) && itemIds.size() != orderItemList.size() && !OcBOrderConst.IS_STATUS_IY.equals(ocBOrder.getIsCycle())) {
            StCShopStrategyDO stCShopStrategyDO = omsStCShopStrategyService.selectOcStCShopStrategy(ocBOrder.getCpCShopId());
            boolean flag = false;
            if (stCShopStrategyDO != null) {
                flag = stCShopStrategyDO.getIsDetentionSplit() != null && "Y".equals(stCShopStrategyDO.getIsDetentionSplit());
            }
            if (flag) {
                //todo 拆单
                Map<Long, OcBOrderItem> orderItemMap = orderItemList.stream().collect(Collectors.toMap(OcBOrderItem::getId, Function.identity(), (key1, key2) -> key2));
                orderItemMap.keySet().removeAll(itemIds);

                // orderItemMap 表示不卡单的集合
                // itemIds 表示卡单的集合
                List<OcBOrderItem> unCardList = ocBOrderItemMapper.selectOrderItemListsByIds(ocBOrder.getId(), new ArrayList<>(orderItemMap.keySet()));
                List<OcBOrderItem> cardList = ocBOrderItemMapper.selectOrderItemListsByIds(ocBOrder.getId(), new ArrayList<>(itemIds));
                List<Long> cardByGiftNotSplit = new ArrayList<>();
                List<Long> cardByGiftRelation = new ArrayList<>();
                List<OcBOrderItem> cardNoSplitGiftList = cardList.stream()
                        .filter(cardItem -> ObjectUtil.equal(1, cardItem.getIsGift()) && ObjectUtil.equal(1, cardItem.getIsGiftSplit()))
                        .collect(Collectors.toList());

                List<OcBOrderItem> cardNoGiftList = cardList.stream()
                        .filter(cardItem -> ObjectUtil.notEqual(1, cardItem.getIsGift()))
                        .collect(Collectors.toList());

                List<OcBOrderItem> unCardNoSplitGiftList = unCardList.stream()
                        .filter(cardItem -> ObjectUtil.equal(1, cardItem.getIsGift()) && ObjectUtil.equal(1, cardItem.getIsGiftSplit()))
                        .collect(Collectors.toList());

                // 不卡单列表中 非赠品列表。 如果为空 代表不卡单列表中都是赠品
                List<OcBOrderItem> unCardNoGiftList = unCardList.stream()
                        .filter(cardItem -> ObjectUtil.notEqual(1, cardItem.getIsGift()))
                        .collect(Collectors.toList());

                // 全是赠品 并且包含不拆单的赠品
                if (CollectionUtils.isEmpty(cardNoGiftList) && CollectionUtils.isNotEmpty(cardNoSplitGiftList) && CollectionUtils.isNotEmpty(unCardList)) {
                    for (OcBOrderItem ocBOrderItem : cardNoSplitGiftList) {
                        // 判断是否有挂靠关系
                        if (StringUtils.isNotEmpty(ocBOrderItem.getGiftRelation())) {
                            // 判断挂靠关系是否在不卡单列表中
                            for (OcBOrderItem unCard : unCardList) {
                                if (ObjectUtil.equal(ocBOrderItem.getGiftRelation(), unCard.getPsCSkuEcode())) {
                                    // 在不卡单列表中 则将不卡单列表中的明细 放入卡单列表中
                                    itemIds.add(unCard.getId());
                                    orderItemMap.keySet().remove(unCard.getId());
                                    cardByGiftRelation.add(unCard.getId());
                                }
                            }
                        } else {
                            OcBOrderItem unCard = unCardList.get(0);
                            itemIds.add(unCard.getId());
                            orderItemMap.keySet().remove(unCard.getId());
                            cardByGiftNotSplit.add(unCard.getId());
                        }
                    }
                } else if (CollectionUtils.isEmpty(unCardNoGiftList) && CollectionUtils.isNotEmpty(unCardNoSplitGiftList) && CollectionUtils.isNotEmpty(cardList)) {
                    for (OcBOrderItem ocBOrderItem : unCardNoSplitGiftList) {
                        // 将不卡单不拆单的赠品放入到  卡单列表中
                        itemIds.add(ocBOrderItem.getId());
                        orderItemMap.keySet().remove(ocBOrderItem.getId());
                        cardByGiftNotSplit.add(ocBOrderItem.getId());
                    }
                } else {
                    // 不卡单的 不拆单赠品不为空
                    if (CollectionUtils.isNotEmpty(unCardNoSplitGiftList)) {
                        List<String> cardItemSkuList = cardList.stream().map(OcBOrderItem::getGiftRelation).collect(Collectors.toList());
                        for (OcBOrderItem giftUnSplit : unCardNoSplitGiftList) {
                            if ((StringUtils.isNotEmpty(giftUnSplit.getGiftRelation())) && cardItemSkuList.contains(giftUnSplit.getGiftRelation())) {
                                orderItemMap.remove(giftUnSplit.getId());
                                itemIds.add(giftUnSplit.getId());
                                cardByGiftNotSplit.add(giftUnSplit.getId());
                            }
                        }
                    }
                }
                orderInfo.setCardByGiftNotSplit(cardByGiftNotSplit);
                orderInfo.setCardByGiftRelation(cardByGiftRelation);

                // 1  2  3为互斥的关系

                // 1 判断 卡单列表中是否全是赠品 并且有不拆单的赠品
                // 1.1 如果该赠品有挂靠关系 并且在不卡单列表中有的话 则从不卡单列表中将主品放到卡单列表中
                // 1.1 如果该赠品没有挂靠关系 则直接从不卡单列表中随机拿一个商品放到卡单列表中
                // 2 判断 不卡单列表中 是否只有赠品 并且有不拆单的赠品
                // 2.1 如果有不拆单的赠品并且卡单列表不为空 则直接将不拆单的赠品放到卡单列表中即可
                // 2.2 如果有不拆单的赠品并且卡单列表为空 则不处理
                // 3 判断 不卡单的明细中是否包含不拆单的赠品(有主品 也有赠品)
                // 3.1 如果有 并且有挂靠关系挂靠到了卡单的列表页


                // 判断剩余的orderItemMap中表示为不需要卡单的商品。 判断是否含有赠品 并且不能拆单的 并且挂靠关系挂靠到了卡单里面
                // 不拆单的赠品
                if (ObjectUtil.isNotNull(orderItemMap) && orderItemMap.keySet().size() > 0) {
                    Map<Set<Long>, SpiltOrderParam> spiltRule = new HashMap<>();
                    //释放时间
                    SpiltOrderParam param = new SpiltOrderParam();
                    param.setCardOrder(true);
                    param.setCardReleaseTime(date);
                    spiltRule.put(new HashSet<>(itemIds), param);
                    spiltRule.put(orderItemMap.keySet(), new SpiltOrderParam());
                    return spiltRule;
                }
            }
        }
        OcBOrder order = new OcBOrder();
        order.setId(ocBOrder.getId());
        order.setIsDetention(AdvanceConstant.DETENTION_STATUS_1);
        if (StringUtils.isNotEmpty(holdNameListStr) && holdNameListStr.length() > 50) {
            holdNameListStr = holdNameListStr.substring(0, 49);
        }
        order.setDetentionReason(holdNameListStr);
        if (CollectionUtils.isNotEmpty(holdIds)) {
            order.setDetentionReasonId(holdIds.get(0));
        }
        order.setSysremark("订单整单卡");
        order.setDetentionReleaseDate(date);
        ocBOrderMapper.updateById(order);
        return null;
    }


    /**
     * 处理hold单
     *
     * @param ocBOrder
     * @param orderItemList
     * @param holdRelationMap
     * @param user
     */
    private void handleHoldOrder(OcBOrder ocBOrder, List<OcBOrderItem> orderItemList,
                                 Map<Integer, List<StAutoHoldRelation>> holdRelationMap, User user) {
        List<StAutoHoldRelation> relations = holdRelationMap.get(2);
        if (CollectionUtils.isEmpty(relations)) {
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                    OrderLogTypeEnum.ORDER_HOLD.getKey(), "未匹配到Hold策略", "", "", user);

            return;
        }
        List<OcBOrderHoldItem> holdItems = new ArrayList<>();
        for (StAutoHoldRelation relation : relations) {
            StCHoldOrderDO stCHoldOrder = relation.getStCHoldOrder();
            HoldResult result = holdOrderFlag(ocBOrder, orderItemList, relation, false);
            if (result.isHold()) {
                OcBOrderHoldItem holdItem = new OcBOrderHoldItem();
                holdItem.setId(ModelUtil.getSequence(TAB_OC_B_ORDER_HOLD_ITEM));
                holdItem.setOcBOrderId(ocBOrder.getId());
                holdItem.setStCHoldOrderId(stCHoldOrder.getId());
                holdItem.setHoldOrderReason(stCHoldOrder.getHoldDetentionOrderReason());
                StCHoldOrderReason stCHoldOrderReason =
                        holdOrderReasonQueryService.selectHoldOrderById(Long.valueOf(stCHoldOrder.getHoldDetentionOrderReason()));
                if (Objects.nonNull(stCHoldOrderReason)) {
                    holdItem.setHoldOrderReasonMsg(stCHoldOrderReason.getReason());
                }
                holdItem.setHoldStatus(OcBOrderHoldConst.YES);
                //带出策略名称
                holdItem.setStCHoldOrderEname(stCHoldOrder.getEname());
                Date date = buildReleaseTime(ocBOrder, stCHoldOrder, orderItemList);
                holdItem.setReleaseTime(date);
                holdItems.add(holdItem);
            }
        }
        StringBuilder builder = new StringBuilder();
        Integer holdReasonId = null;
        List<OcBOrderHoldItem> ocBOrderHolds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(holdItems)) {
            Map<Integer, List<OcBOrderHoldItem>> holdMap = holdItems.stream().collect(Collectors.groupingBy(OcBOrderHoldItem::getHoldOrderReason));
            for (Integer integer : holdMap.keySet()) {
                List<OcBOrderHoldItem> holdItem1 = ocBOrderHoldItemMapper.selectOrderHoldItemByOrderIdAndHoldReasonList(ocBOrder.getId(), integer);
                if (CollectionUtils.isNotEmpty(holdItem1)) {
                    break;
                }
                List<OcBOrderHoldItem> ocBOrderHoldItems = holdMap.get(integer);
                List<OcBOrderHoldItem> collect = ocBOrderHoldItems.stream().filter(p -> p.getReleaseTime() == null).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(collect)) {
                    ocBOrderHolds.add(collect.get(0));
                    builder.append(collect.get(0).getStCHoldOrderEname());
                    holdReasonId = collect.get(0).getHoldOrderReason();
                    break;
                }
                ocBOrderHoldItems.sort((o1, o2) -> {
                    long time2 = o2.getReleaseTime().getTime();
                    long time1 = o1.getReleaseTime().getTime();
                    return time2 > time1 ? 1 : 0;
                });
                ocBOrderHolds.add(ocBOrderHoldItems.get(0));
                builder.append(ocBOrderHoldItems.get(0).getStCHoldOrderEname());
                holdReasonId = ocBOrderHoldItems.get(0).getHoldOrderReason();
            }
        }
        if (CollectionUtils.isNotEmpty(ocBOrderHolds)) {
            omsAutoHoldNewService.handleOrder(ocBOrder, builder.toString(), holdReasonId, ocBOrderHolds, user);
        } else {
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                    OrderLogTypeEnum.ORDER_HOLD.getKey(), "订单明细未匹配到策略或已hold单", "", "", user);
        }

    }

    @Transactional(rollbackFor = Exception.class)
    public void handleOrder(OcBOrder ocBOrder, String holdReason, Integer holdReasonId, List<OcBOrderHoldItem> holdItems, User user) {
        //订单需要hold单
        OcBOrder order = new OcBOrder();
        order.setId(ocBOrder.getId());
        order.setIsInterecept(OcBOrderHoldConst.HOLD_ORDER_YES);
        order.setHoldReason(holdReason);
        order.setHoldReasonId(holdReasonId);
        ocBOrderMapper.updateById(order);
        if (CollectionUtils.isNotEmpty(holdItems)) {
            ocBOrderHoldItemMapper.batchInsert(holdItems);
        }
        omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                OrderLogTypeEnum.ORDER_HOLD.getKey(), "订单自动hold单", "", "", user);
    }


    private HoldResult holdOrderFlag(OcBOrder ocBOrder, List<OcBOrderItem> orderItemList,
                                     StAutoHoldRelation relation, boolean cardSt) {
        HoldResult result = new HoldResult();
        StCHoldOrderDO stCHoldOrder = relation.getStCHoldOrder();
        String orderFlag = stCHoldOrder.getOrderFlag();
        List<Long> itemIdList = orderItemList.stream().map(OcBOrderItem::getId).collect(Collectors.toList());
        if (OcOrderTagEum.TAG_LIVE.getKey().equalsIgnoreCase(orderFlag)
                && Optional.ofNullable(ocBOrder.getLiveFlag()).orElse(0) == 1) {
            List<OcBOrderItem> newItemList = orderItemList.stream().filter(o -> o.getLiveEvents() != null).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(newItemList)) {
                result.setHold(false);
                return result;
            } else {
                String holdLiveEvents = stCHoldOrder.getHoldLiveEvents();
                if (StringUtils.isEmpty(holdLiveEvents)) {
                    result.setHold(true);
                    result.setItemIds(itemIdList);
                    return result;
                }
                for (OcBOrderItem ocBOrderItem : newItemList) {
                    if (!holdLiveEvents.contains(ocBOrderItem.getLiveEvents().toString())) {
                        result.setHold(false);
                        return result;
                    }
                }
            }
            result.setHold(true);
            return result;
        }
        if (OcOrderTagEum.TAG_O2O.getKey().equalsIgnoreCase(orderFlag)
                && Optional.ofNullable(ocBOrder.getIsO2oOrder()).orElse(0) == 1) {
            result.setHold(true);
            result.setItemIds(itemIdList);
            return result;
        }
        //预售hold
        if (OcOrderTagEum.TAG_ELE_PRE.getKey().equalsIgnoreCase(orderFlag)
                && Optional.ofNullable(ocBOrder.getDouble11PresaleStatus()).orElse(0) == 1) {
            result.setHold(true);
            result.setItemIds(itemIdList);
            return result;
        }
        if (OcBOrderHoldConst.NORMAL.equalsIgnoreCase(orderFlag)) {
            List<StCHoldOrderItemDO> stCHoldOrderItems = relation.getStCHoldOrderItems();
            List<Long> longs = checkGoodsHoldNormal(orderItemList, stCHoldOrderItems, 2, ocBOrder, cardSt);
            boolean notEmpty = CollectionUtils.isNotEmpty(longs);
            result.setHold(notEmpty);
            result.setItemIds(longs);
            return result;
        }
        //省市区HOld单
        if (OcBOrderHoldConst.PROVINCE.equalsIgnoreCase(orderFlag)) {
            List<StCHoldProvinceItemDO> stCHoldProvinceItems = relation.getStCHoldProvinceItems();
            boolean b = checkProvinceHold(stCHoldProvinceItems, ocBOrder);
            result.setHold(b);
            result.setItemIds(itemIdList);
            return result;
        }
        result.setHold(false);
        return result;
    }


    private boolean checkProvinceHold(List<StCHoldProvinceItemDO> stCHoldProvinceItems, OcBOrder orderInfo) {
        if (CollectionUtils.isNotEmpty(stCHoldProvinceItems)) {
            for (StCHoldProvinceItemDO stCHoldProvinceItemDO : stCHoldProvinceItems) {
                StringBuilder sb = new StringBuilder();
                if (stCHoldProvinceItemDO.getSellerProvinceId() != null) {
                    sb.append(stCHoldProvinceItemDO.getSellerProvinceId() + "");
                }
                if (stCHoldProvinceItemDO.getSellerCityId() != null) {
                    sb.append(stCHoldProvinceItemDO.getSellerCityId() + "");
                }
                if (stCHoldProvinceItemDO.getSellerAreaId() != null) {
                    sb.append(stCHoldProvinceItemDO.getSellerAreaId() + "");
                }
                StringBuilder sb1 = new StringBuilder();
                if (orderInfo.getCpCRegionProvinceId() != null && stCHoldProvinceItemDO.getSellerProvinceId() != null) {
                    sb1.append(orderInfo.getCpCRegionProvinceId() + "");
                }
                if (orderInfo.getCpCRegionCityId() != null && stCHoldProvinceItemDO.getSellerCityId() != null) {
                    sb1.append(orderInfo.getCpCRegionCityId() + "");
                }
                if (orderInfo.getCpCRegionAreaId() != null && stCHoldProvinceItemDO.getSellerAreaId() != null) {
                    sb1.append(orderInfo.getCpCRegionAreaId() + "");
                }
                if (sb1.toString().equals(sb.toString())) {
                    return true;
                }
            }
        }
        return false;
    }


    private List<Long> checkGoodsHoldNormal(List<OcBOrderItem> orderItemList, List<StCHoldOrderItemDO> stCHoldOrderItems,
                                            Integer flag, OcBOrder ocBOrder, boolean cardSt) {
        String receiverMobile = ocBOrder.getReceiverMobile();
        String buyerMemo = ocBOrder.getBuyerMessage();
        String sellerMemo = ocBOrder.getSellerMemo();
        String cpCRegionProvinceEname = ocBOrder.getCpCRegionProvinceEname();
        String cpCRegionCityEname = ocBOrder.getCpCRegionCityEname();

        if (CollectionUtils.isEmpty(stCHoldOrderItems)) {
            return null;
        }
        Map<String, List<StCHoldOrderItemDO>> rulesMap = stCHoldOrderItems.stream().collect(Collectors.groupingBy(StCHoldOrderItemDO::getRulesRecognition));
        Set<Long> itemIds = new HashSet<>();
        for (OcBOrderItem item : orderItemList) {
            if (matchOrderItem(item, rulesMap, flag)) {
                itemIds.add(item.getId());
            }
        }
        if (orderItemList.size() == itemIds.size()) {
            return new ArrayList<>(itemIds);
        }
        List<StCHoldOrderItemDO> stCHoldOrderItemDOS1 = rulesMap.get(OrderHoldEnum.ORDER_WEIGHT.getKey());
        if (CollectionUtils.isNotEmpty(stCHoldOrderItemDOS1)) {
            BigDecimal wightCount = BigDecimal.ZERO;
            Set<Long> itemIdList = new HashSet<>();
            for (OcBOrderItem orderItem : orderItemList) {
                itemIdList.add(orderItem.getId());
                BigDecimal standardWeight = orderItem.getStandardWeight();
                if (standardWeight != null) {
                    wightCount = wightCount.add(standardWeight.multiply(orderItem.getQty()));
                }

            }
            for (StCHoldOrderItemDO stCHoldOrderItemDO : stCHoldOrderItemDOS1) {
                BigDecimal minimumAmount = stCHoldOrderItemDO.getMinimumAmount();
                if (minimumAmount != null && wightCount.compareTo(minimumAmount) >= 0) {
                    itemIds.addAll(itemIdList);
                    break;
                }

            }
        }

        //手机号码
        List<StCHoldOrderItemDO> mobileHoldItems = rulesMap.get(OrderHoldEnum.MOBILE_NUMBER.getKey());
        if (CollectionUtils.isNotEmpty(mobileHoldItems)) {
            for (StCHoldOrderItemDO stCHoldOrderItemDO : mobileHoldItems) {
                if (receiverMobile != null && receiverMobile.equals(stCHoldOrderItemDO.getContent())) {
                    itemIds.addAll(orderItemList.stream().map(OcBOrderItem::getId).collect(Collectors.toList()));
                    break;
                }
            }
        }

        // 买家留言
        List<StCHoldOrderItemDO> buyerMemos = rulesMap.get(OrderHoldEnum.BUYER_MEMO.getKey());
        if (CollectionUtils.isNotEmpty(buyerMemos) && StringUtils.isNotEmpty(buyerMemo)) {
            for (StCHoldOrderItemDO stCHoldOrderItemDO : buyerMemos) {
                String buyerReamrk = stCHoldOrderItemDO.getContent();
                buyerReamrk = buyerReamrk.replace(",", "，");
                String[] buyerReamrkArr = buyerReamrk.split("，");

                for (String buyerKeyWord : buyerReamrkArr) {
                    if (buyerMemo.contains(buyerKeyWord.trim())) {
                        itemIds.addAll(orderItemList.stream().map(OcBOrderItem::getId).collect(Collectors.toList()));
                        break;
                    }
                }
            }
        }

        // 卖家留言
        List<StCHoldOrderItemDO> sellerMemos = rulesMap.get(OrderHoldEnum.SELLER_MEMO.getKey());
        if (CollectionUtils.isNotEmpty(sellerMemos) && StringUtils.isNotEmpty(sellerMemo)) {
            for (StCHoldOrderItemDO stCHoldOrderItemDO : sellerMemos) {
                String sellerRemark = stCHoldOrderItemDO.getContent();
                sellerRemark = sellerRemark.replace(",", "，");
                String[] sellerReamrkArr = sellerRemark.split("，");

                for (String sellerKeyWord : sellerReamrkArr) {
                    if (sellerMemo.contains(sellerKeyWord.trim())) {
                        itemIds.addAll(orderItemList.stream().map(OcBOrderItem::getId).collect(Collectors.toList()));
                        break;
                    }
                }
            }
        }

        // 按收货地省
        List<StCHoldOrderItemDO> receiveProvinces = rulesMap.get(OrderHoldEnum.RECEIVE_PROVINCE.getKey());
        if (CollectionUtils.isNotEmpty(receiveProvinces)) {
            for (StCHoldOrderItemDO stCHoldOrderItemDO : receiveProvinces) {
                if (StringUtils.isNotBlank(cpCRegionProvinceEname) && cpCRegionProvinceEname.equals(stCHoldOrderItemDO.getContent())) {
                    itemIds.addAll(orderItemList.stream().map(OcBOrderItem::getId).collect(Collectors.toList()));
                    break;
                }
            }
        }

        // 按收货地市
        List<StCHoldOrderItemDO> receiveCities = rulesMap.get(OrderHoldEnum.RECEIVE_CITY.getKey());
        if (CollectionUtils.isNotEmpty(receiveCities)) {
            for (StCHoldOrderItemDO stCHoldOrderItemDO : receiveCities) {
                if (StringUtils.isNotBlank(cpCRegionCityEname) && cpCRegionCityEname.equals(stCHoldOrderItemDO.getContent())) {
                    itemIds.addAll(orderItemList.stream().map(OcBOrderItem::getId).collect(Collectors.toList()));
                    break;
                }
            }
        }

        //手机号下单数&地址下单数黑名单
        if (StringUtils.isNotBlank(receiverMobile) && receiverMobile.length() <= 11) {
            //手机号下单数
            List<StCHoldOrderItemDO> mobileOrderHoldItems = rulesMap.get(OrderHoldEnum.MOBILE_ORDER_NUM.getKey());
            //地址下单数
            List<StCHoldOrderItemDO> addressOrderHoldItems = rulesMap.get(OrderHoldEnum.ADDRESS_ORDER_NUM.getKey());
            List<Dim8Result> dim8Results = Lists.newArrayList();
            Map<String, List<OcBOrderItem>> codeItemMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(mobileOrderHoldItems) || CollectionUtils.isNotEmpty(addressOrderHoldItems)) {
                //查询提数,正常商品&组合商品
                List<OcBOrderItem> availItems = orderItemList.stream().filter(p -> (SkuType.NORMAL_PRODUCT == p.getProType() || SkuType.COMBINE_PRODUCT == p.getProType())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(availItems)) {
                    codeItemMap = availItems.stream().collect(Collectors.groupingBy(OcBOrderItem::getPsCSkuEcode));

                    List<String> ecodes = availItems.stream().map(OcBOrderItem::getPsCSkuEcode).collect(Collectors.toList());
                    dim8Results = psRpcService.selectPsCProDim8ItemInfo(ecodes);
                }
            }

            //手机号下单数
            if (CollectionUtils.isNotEmpty(mobileOrderHoldItems) && StringUtils.isNotBlank(receiverMobile)) {
                //订单的数量（提数）
                Map<Long, Integer> map = getTiNum(dim8Results, codeItemMap);
                if (MapUtils.isNotEmpty(map)) {
                    CusRedisTemplate<String, String> strRedisTemplate = RedisOpsUtil.getStrRedisTemplate();
                    String mobileKey = BllRedisKeyResources.blackStMobileKey(ocBOrder.getCpCShopId(), DigestUtils.md5Hex(receiverMobile));

                    for (Map.Entry<Long, Integer> entry : map.entrySet()) {
                        //记录提数
                        BoundSetOperations<String, String> mobileOperations = strRedisTemplate.boundSetOps(mobileKey);
                        mobileOperations.add(ocBOrder.getId() + "," + entry.getKey() + "," + entry.getValue());
                        mobileOperations.expire(ChronoUnit.SECONDS.between(LocalDateTime.now(), LocalDateTime.now().plusDays(1).withHour(0).withMinute(0).withSecond(0).withNano(0)), TimeUnit.SECONDS);
                    }

                    //获取提数
                    Set<String> tiSets = strRedisTemplate.boundSetOps(mobileKey).members();
                    if (CollectionUtils.isNotEmpty(tiSets)) {
                        blackSt(orderItemList, ocBOrder, itemIds, mobileOrderHoldItems, tiSets);
                    }
                }
            }

            //地址下单数
            String receiverAddress = ocBOrder.getReceiverAddress();
            String mobile = ocBOrder.getReceiverMobile();
            if (CollectionUtils.isNotEmpty(addressOrderHoldItems) && StringUtils.isNotBlank(receiverAddress) && StringUtils.isNotBlank(mobile)) {
                //订单的数量（提数）
                Map<Long, Integer> map = getTiNum(dim8Results, codeItemMap);
                if (MapUtils.isNotEmpty(map)) {
                    CusRedisTemplate<String, String> strRedisTemplate = RedisOpsUtil.getStrRedisTemplate();
                    String addressKey = BllRedisKeyResources.blackStAddressKey(ocBOrder.getCpCShopId(), DigestUtils.md5Hex(receiverAddress));

                    for (Map.Entry<Long, Integer> entry : map.entrySet()) {
                        //记录提数
                        BoundSetOperations<String, String> addressOperations = strRedisTemplate.boundSetOps(addressKey);
                        addressOperations.add(ocBOrder.getId() + "," + entry.getKey() + "," + entry.getValue());
                        addressOperations.expire(ChronoUnit.SECONDS.between(LocalDateTime.now(), LocalDateTime.now().plusDays(1).withHour(0).withMinute(0).withSecond(0).withNano(0)), TimeUnit.SECONDS);
                    }

                    //获取提数
                    Set<String> tiSets = strRedisTemplate.boundSetOps(addressKey).members();
                    if (CollectionUtils.isNotEmpty(tiSets)) {
                        blackSt(orderItemList, ocBOrder, itemIds, addressOrderHoldItems, tiSets);
                    }
                }
            }
        }

        return new ArrayList<>(itemIds);
    }

    /**
     * 黑名单计算
     *
     * @param orderItemList
     * @param ocBOrder
     * @param itemIds
     * @param holdOrderItemDOS
     * @param tiSets
     */
    private void blackSt(List<OcBOrderItem> orderItemList, OcBOrder ocBOrder, Set<Long> itemIds, List<StCHoldOrderItemDO> holdOrderItemDOS, Set<String> tiSets) {
        //兼容历史配置数据，增加个判断
        if (holdOrderItemDOS.get(0).getContentId() == null) {
            return;
        }

        boolean isCard = false;

        for (OcBOrderItem ocBOrderItem : orderItemList) {
            Long mDim4Id = ocBOrderItem.getMDim4Id();
            if (mDim4Id == null) {
                //如果为空，设默认值0
                ocBOrderItem.setMDim4Id(0L);
            }
        }
        Map<Long, List<OcBOrderItem>> dim4Map = orderItemList.stream().collect(Collectors.groupingBy(OcBOrderItem::getMDim4Id));

        Map<Long, Integer> recordMap = Maps.newHashMap();
        for (String value : tiSets) {
            String[] split = value.split(",");
            Long dim4Id = Long.valueOf(split[1]);
            Integer integer = Integer.valueOf(split[2]);

            recordMap.merge(dim4Id, integer, Integer::sum);
        }

        Map<Long, List<StCHoldOrderItemDO>> configMap = holdOrderItemDOS.stream().collect(Collectors.groupingBy(StCHoldOrderItemDO::getContentId));

        for (Map.Entry<Long, List<OcBOrderItem>> entry : dim4Map.entrySet()) {
            Long orderDim4Id = entry.getKey();

            List<StCHoldOrderItemDO> stCHoldOrderItemDOS = configMap.get(orderDim4Id);
            if (CollectionUtils.isEmpty(stCHoldOrderItemDOS)) {
                continue;
            }
            StCHoldOrderItemDO itemDO = stCHoldOrderItemDOS.get(0);

            Integer recordValue = recordMap.get(orderDim4Id);
            if (recordValue != null && recordValue >= Integer.parseInt(itemDO.getContent())) {
                isCard = true;
                break;
            }
        }

        if (isCard) {
            //达到配置数量，卡单
            itemIds.addAll(orderItemList.stream().map(OcBOrderItem::getId).collect(Collectors.toList()));
            //手机号黑名单策略卡单配置
            try {
                stCHoldOrderSaveService.addBalckStCard(ocBOrder.getCpCShopId(), ocBOrder.getCpCShopTitle(), ocBOrder.getReceiverMobile());
            } catch (Exception e) {
                log.error(LogUtil.format("orderId:{}", "手机号黑名单策略卡单配置失败"), ocBOrder.getId(), e);
            }
        }
    }

    /**
     * 返回一级分类维度的提数总数
     *
     * @param dim8Results
     * @param codeItemMap
     * @return
     */
    private Map<Long, Integer> getTiNum(List<Dim8Result> dim8Results, Map<String, List<OcBOrderItem>> codeItemMap) {
        if (CollectionUtils.isEmpty(dim8Results)) {
            return Maps.newHashMap();
        }

        Map<Long, Integer> map = Maps.newHashMap();
        for (Dim8Result dim8Result : dim8Results) {
            //商品提数
            BigDecimal tiName = StringUtils.isBlank(dim8Result.getEname()) ? BigDecimal.ZERO : new BigDecimal(dim8Result.getEname());
            int tiNum = tiName.intValue();

            //商品数量
            List<OcBOrderItem> items = codeItemMap.get(dim8Result.getSkuCode());
            BigDecimal qty = items.stream().map(OcBOrderItem::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);

            map.merge(items.get(0).getMDim4Id(), BigDecimal.valueOf(tiNum).multiply(qty).intValue(), Integer::sum);
        }

        return map;
    }

    /**
     * @param item
     * @param rulesMap
     * @param flag     1 卡 2 hold
     */
    private boolean matchOrderItem(OcBOrderItem item, Map<String, List<StCHoldOrderItemDO>> rulesMap, Integer flag) {
        List<StCHoldOrderItemDO> stCHoldOrderItemDOS = rulesMap.get(OrderHoldEnum.GOODS_CODE.getKey());
        BigDecimal realAmt = item.getRealAmt();
        if (CollectionUtils.isNotEmpty(stCHoldOrderItemDOS)) {
            String psCProEcode = item.getPsCProEcode();
            if (handleItem(flag, stCHoldOrderItemDOS, psCProEcode, realAmt)) {
                return true;
            }
        }
        stCHoldOrderItemDOS = rulesMap.get(OrderHoldEnum.GOODS_SKU.getKey());
        if (CollectionUtils.isNotEmpty(stCHoldOrderItemDOS)) {
            String psCSkuEcode = item.getPsCSkuEcode();
            if (handleItem(flag, stCHoldOrderItemDOS, psCSkuEcode, realAmt)) {
                return true;
            }
        }
        stCHoldOrderItemDOS = rulesMap.get(OrderHoldEnum.PT_SPU_ID.getKey());
        if (CollectionUtils.isNotEmpty(stCHoldOrderItemDOS)) {
            String numIid = item.getNumIid();
            if (handleItem(flag, stCHoldOrderItemDOS, numIid, realAmt)) {
                return true;
            }
        }
        stCHoldOrderItemDOS = rulesMap.get(OrderHoldEnum.PT_SKU_ID.getKey());
        if (CollectionUtils.isNotEmpty(stCHoldOrderItemDOS)) {
            String skuNumiid = item.getSkuNumiid();
            if (handleItem(flag, stCHoldOrderItemDOS, skuNumiid, realAmt)) {
                return true;
            }
        }
        stCHoldOrderItemDOS = rulesMap.get(OrderHoldEnum.PT_PRO_TITLE.getKey());
        if (CollectionUtils.isNotEmpty(stCHoldOrderItemDOS)) {
            String ptProName = item.getTitle();
            //都不检验金额 写死
            if (handleItem(1, stCHoldOrderItemDOS, ptProName, realAmt)) {
                return true;
            }
        }
        //只有hold走这个逻辑
        if (flag == 2) {
            stCHoldOrderItemDOS = rulesMap.get(OrderHoldEnum.ALL.getKey());
            return CollectionUtils.isNotEmpty(stCHoldOrderItemDOS);
        }
        return false;
    }


    private boolean handleItem(Integer flag, List<StCHoldOrderItemDO> stCHoldOrderItemDOS,
                               String matchContent, BigDecimal realAmt) {
        if (StringUtils.isEmpty(matchContent)) {
            return false;
        }
        for (StCHoldOrderItemDO stCHoldOrderItemDO : stCHoldOrderItemDOS) {
            BigDecimal minimumAmount = stCHoldOrderItemDO.getMinimumAmount() == null ? BigDecimal.ZERO : stCHoldOrderItemDO.getMinimumAmount();
            String content = stCHoldOrderItemDO.getContent();
            boolean isSuccess = false;
            if (flag == 2) {
                isSuccess = matchContent.contains(content) && realAmt.compareTo(minimumAmount) >= 0;
            } else {
                isSuccess = matchContent.contains(content);
            }
            if (isSuccess) {
                return true;
            }
        }
        return false;
    }

    // 根据策略计算释放时间
    private Date buildReleaseTime(OcBOrder order, StCHoldOrderDO stCHoldOrder, List<OcBOrderItem> orderItemList) {
        if (!OcBOrderHoldConst.YES.equalsIgnoreCase(stCHoldOrder.getIsAutoRelease())) {
            return null;
        }
        // 指定时点释放
        if (OcBOrderHoldConst.RELEASE_TIME_TYPE_1.equals(stCHoldOrder.getReleaseTimeType())) {
            return stCHoldOrder.getReleaseTime();
        }
        // 固定时长释放 需要根据配置计算释放时间
        if (OcBOrderHoldConst.RELEASE_TIME_TYPE_2.equals(stCHoldOrder.getReleaseTimeType())) {
            Date time = OcBOrderHoldConst.ORDER_DATE.equals(stCHoldOrder.getDayType()) ? order.getOrderDate() :
                    order.getPayTime();
            if (OcBOrderHoldConst.TIME_UNIT_MINUTE.equals(stCHoldOrder.getTimeUnit())) {
                return DateConversionUtil.plusMinutes(time, stCHoldOrder.getFixedDuration());
            } else if (OcBOrderHoldConst.TIME_UNIT_HOUR.equals(stCHoldOrder.getTimeUnit())) {
                return DateConversionUtil.plusHours(time, stCHoldOrder.getFixedDuration());
            } else if (OcBOrderHoldConst.TIME_UNIT_DAY.equals(stCHoldOrder.getTimeUnit())) {
                return DateConversionUtil.plusDays(time, stCHoldOrder.getFixedDuration());
            }
        }
        // 临近预计发货日释放 需要根据配置计算释放时间
        if (OcBOrderHoldConst.RELEASE_TIME_TYPE_3.equals(stCHoldOrder.getReleaseTimeType())) {
            Date newDate = null;
            for (OcBOrderItem ocBOrderItem : orderItemList) {

                if (ocBOrderItem.getEstimateConTime() != null) {
                    Date expectedDeliveryDate = parsDate(ocBOrderItem.getEstimateConTime(), stCHoldOrder.getTimeUnit(), stCHoldOrder.getAdvanceTime());
                    newDate = newDate == null ? expectedDeliveryDate : newDate;
                    if (expectedDeliveryDate.after(newDate)) {
                        newDate = expectedDeliveryDate;
                    }
                }
            }
            return newDate;
        }

        return null;
    }

    private Date parsDate(Date time, Integer timeUnit, Integer number) {
        if (OcBOrderHoldConst.TIME_UNIT_MINUTE.equals(timeUnit)) {
            return DateConversionUtil.plusMinutes(time, -number);
        } else if (OcBOrderHoldConst.TIME_UNIT_HOUR.equals(timeUnit)) {
            return DateConversionUtil.plusHours(time, -number);
        } else if (OcBOrderHoldConst.TIME_UNIT_DAY.equals(timeUnit)) {
            return DateConversionUtil.plusDays(time, -number);
        }
        return time;
    }


    @Data
    private static class HoldResult {

        private boolean isHold;

        private List<Long> itemIds;
    }


}
