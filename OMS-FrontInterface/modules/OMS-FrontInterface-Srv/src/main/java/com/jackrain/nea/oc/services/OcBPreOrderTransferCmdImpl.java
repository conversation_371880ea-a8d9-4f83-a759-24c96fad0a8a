package com.jackrain.nea.oc.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBPreOrderTransferCmd;
import com.jackrain.nea.oc.oms.api.OcBRefundInTaskSaveCmd;
import com.jackrain.nea.oc.oms.services.OcBPreOrderUpdateService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName OcBPreOrderTransferCmdImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/10/16 15:07
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBPreOrderTransferCmdImpl extends CommandAdapter implements OcBPreOrderTransferCmd {

    @Autowired
    private OcBPreOrderUpdateService updateService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
       return updateService.transfer(session);
    }
}
