package com.jackrain.nea.ps;

import com.jackrain.nea.ps.api.result.ProSkuResult;
import com.jackrain.nea.ps.model.ProductSku;

import com.jackrain.nea.rpc.PsRpcService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: 易邵峰
 * @since: 2019-04-15
 * create at : 2019-04-15 21:58
 */
@RestController
@RequestMapping("/p")
public class ProductController {

    @Autowired
    private PsRpcService psRpcService;


    /**
     * 查找商品信息
     *
     * @param sku 商品SKU
     * @return 商品信息
     */
    @RequestMapping("/selectProd")
    public ProductSku selectProd(@RequestParam("sku") String sku) {
        return psRpcService.selectProductSku(sku);
    }

}
