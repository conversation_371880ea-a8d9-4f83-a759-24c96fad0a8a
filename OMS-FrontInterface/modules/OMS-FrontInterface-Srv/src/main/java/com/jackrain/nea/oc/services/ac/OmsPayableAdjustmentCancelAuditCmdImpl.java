package com.jackrain.nea.oc.services.ac;

import com.jackrain.nea.ac.service.PayableAdjustmentCancelAuditService;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.ac.OmsPayableAdjustmentCancelAuditCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 调用BLL层
 *
 * <AUTHOR> 陈俊明
 * @since : 2019-03-25
 * create at : 2019-03-25 11:33
 */
@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OmsPayableAdjustmentCancelAuditCmdImpl extends CommandAdapter implements OmsPayableAdjustmentCancelAuditCmd {
    @Autowired
    private PayableAdjustmentCancelAuditService payableAdjustmentCancelAuditService;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        return payableAdjustmentCancelAuditService.execute(querySession);
    }
}