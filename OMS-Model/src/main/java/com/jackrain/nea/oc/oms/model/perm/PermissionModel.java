package com.jackrain.nea.oc.oms.model.perm;

import java.util.ArrayList;
import java.util.List;

/**
 * Permission Collection
 *
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2020/4/20
 */
public class PermissionModel {


    private List<PermissionEnum> list;


    public static final PermissionModel build() {
        return new PermissionModel();
    }

    public PermissionModel set(PermissionEnum permissionEnum) {
        if (list == null) {
            list = new ArrayList<>();
        }
        list.add(permissionEnum);
        return this;
    }

    public List<PermissionEnum> permissions() {
        return list;
    }


}
