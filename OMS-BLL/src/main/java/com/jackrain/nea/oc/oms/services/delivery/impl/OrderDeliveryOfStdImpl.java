package com.jackrain.nea.oc.oms.services.delivery.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.data.basic.model.request.ProInfoQueryRequest;
import com.jackrain.nea.oc.oms.mapper.OcBOrderDeliveryMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderDelivery;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.OmsOrderItemService;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.services.delivery.OrderDeliveryCmd;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.StdRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 私域平台发货服务
 * @date 2021/12/29 11:23
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderDeliveryOfStdImpl implements OrderDeliveryCmd {

    private final OmsOrderService omsOrderService;

    private final OmsOrderItemService omsOrderItemServie;

    private final CpRpcService cpRpcService;

    private final OcBOrderDeliveryMapper ocBOrderDeliveryMapper;

    private final StdRpcService stdRpcService;

    private final OmsOrderLogService omsOrderLogService;

    @Override
    public boolean deliveryDeal(OcBOrderRelation ocBOrderRelation, List<String> tips) {
        if (log.isDebugEnabled()) {
            log.debug("私域平台发货服务,订单id为={}", ocBOrderRelation.getOrderId());
        }
        List<JSONObject> sendDataModels = this.addCommonIpParam(ocBOrderRelation);
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("data", sendDataModels);
            OcBOrder orderInfo = ocBOrderRelation.getOrderInfo();
            ValueHolderV14 delivery = stdRpcService.delivery(jsonObject, orderInfo.getPlatform());
            //发货成功，添加发货成功日志
            if (!delivery.isOK()) {
                throw new RuntimeException(StringUtils.isEmpty(delivery.getMessage()) ? "调用私域平台发货服务返回接口为空" : delivery.getMessage());
            }
            User user = SystemUserResource.getRootUser();
            //新增日志信息
            String logMsg = "订单" + orderInfo.getId() + "(平台单号=" + orderInfo.getTid() + ")平台发货成功";
            omsOrderLogService.addUserOrderLog(orderInfo.getId(), orderInfo.getBillNo(), OrderLogTypeEnum.PLATFORM_SEND.getKey(), logMsg, null, orderInfo.getForceSendFailReason(), user);
            OcBOrder ocBOrderFlag = new OcBOrder();
            ocBOrderFlag.setId(ocBOrderRelation.getOrderInfo().getId());
            ocBOrderFlag.setIsForce(1L);
            ocBOrderFlag.setOrderStatus(OmsOrderStatus.PLATFORM_DELIVERY.toInteger());
            omsOrderService.updateOrderInfo(ocBOrderFlag);
        } catch (Exception e) {
            log.error("调用私域平台发货rpc异常", e);
            OcBOrder ocBOrderFlag = new OcBOrder();
            ocBOrderFlag.setId(ocBOrderRelation.getOrderInfo().getId());
            ocBOrderFlag.setIsForce(0L);
            ocBOrderFlag.setForceSendFailReason(e.getMessage().length() > 100 ? e.getMessage().substring(0, 100) : e.getMessage());
            omsOrderService.updateOrderInfo(ocBOrderFlag);
        }
        return true;
    }


    /**
     * 组装通用发货平台参数
     *
     * @param ocBOrderRelation
     * @return
     */
    private List<JSONObject> addCommonIpParam(OcBOrderRelation ocBOrderRelation) {
        OcBOrder orderInfo = ocBOrderRelation.getOrderInfo();
        List<JSONObject> result = new ArrayList<>();
        List<OcBOrderItem> ocBOrderItems = omsOrderItemServie.selectUnSuccessRefund(orderInfo.getId());
        if (CollectionUtils.isNotEmpty(ocBOrderItems)) {
            Map<String, List<OcBOrderItem>> lists = ocBOrderItems.stream().collect(Collectors.groupingBy(OcBOrderItem::getTid));
            for (Map.Entry<String, List<OcBOrderItem>> map : lists.entrySet()) {
                List<OcBOrderItem> orderItems = map.getValue();
                if (CollectionUtils.isNotEmpty(orderItems)) {
                    JSONObject model = buildJSONObject(ocBOrderRelation, orderItems);
                    if (model != null) {
                        result.add(model);
                    }
                }
            }
        }
        return result;
    }

    /**
     * 组装单个通用发货平台参数
     *
     * @param ocBOrderRelation
     * @param orderItems
     * @return
     */
    private JSONObject buildJSONObject(OcBOrderRelation ocBOrderRelation, List<OcBOrderItem> orderItems) {
        OcBOrder ocBOrder = ocBOrderRelation.getOrderInfo();
        BigDecimal skuNum = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(orderItems)) {
            for (OcBOrderItem ocBOrderItem : orderItems) {
                if (ocBOrderItem.getQty() != null) {
                    skuNum = skuNum.add(ocBOrderItem.getQty());
                }
            }
        }
        JSONObject sendModel = new JSONObject();
        String companyCode = cpRpcService.getPlatformLogisticEcode(ocBOrder.getCpCLogisticsId(), Long.valueOf(ocBOrder.getPlatform()));
        Map<Long, String> companyCodeMap = new HashMap<>();
        companyCodeMap.put(ocBOrder.getCpCLogisticsId(), companyCode);
        log.debug("调用物流公司编码查询服务==============结束：结束后，物流编码为" + companyCode);
        //mq回执返回过来用来订单ID
        sendModel.put("order_id", ocBOrder.getId());
        sendModel.put("is_split", ocBOrder.getIsSplit() == null ? null : ocBOrder.getIsSplit().longValue());
        sendModel.put("tid", ocBOrder.getTid());
        sendModel.put("out_sid", ocBOrder.getExpresscode());
        sendModel.put("company_code", companyCode);
        sendModel.put("pay_type", ocBOrder.getPayType() == null ? "" : ocBOrder.getPayType().toString());
        sendModel.put("logistics_company_name", ocBOrder.getCpCLogisticsEname());
        sendModel.put("size", skuNum);
        JSONArray sendDetails = new JSONArray();
        List<OcBOrderDelivery> ocBOrderDeliveries = ocBOrderDeliveryMapper.selectOrderDeliveryByOrderId(ocBOrder.getId());
        Map<String, OcBOrderItem> skuOoidMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(orderItems)) {
            skuOoidMap = orderItems.stream()
                    .filter(obj -> obj != null && StringUtils.isNotEmpty(obj.getPsCSkuEcode()))
                    .collect(Collectors.toMap(OcBOrderItem::getPsCSkuEcode, a -> a, (k1, k2) -> k2));
        }
        //组装包裹信息
        if (CollectionUtils.isNotEmpty(ocBOrderDeliveries)) {
            List<String> proCodeList = ocBOrderDeliveries.stream()
                    .filter(obj -> obj != null && StringUtils.isNotEmpty(obj.getPsCProEcode()))
                    .map(obj -> obj.getPsCProEcode())
                    .collect(Collectors.toList());
            ProInfoQueryRequest proInfoQueryRequest = new ProInfoQueryRequest();
            proInfoQueryRequest.setProEcodeList(proCodeList);
            for (OcBOrderDelivery item : ocBOrderDeliveries) {
                JSONObject detail = new JSONObject();
                OcBOrderItem ocBOrderItem = skuOoidMap.get(item.getPsCSkuEcode());
                if (ocBOrderItem == null) {
                    continue;
                }
                detail.put("product_code", item.getPsCProEcode());
                long num = item.getQty() == null ? 0L : item.getQty().longValue();
                detail.put("num", num);
                detail.put("real_num", num);
                detail.put("lack_num", 0);
                detail.put("item_id", ocBOrderItem.getOoid());
                detail.put("logistics_number", item.getLogisticNumber());
                //物流公司查询物流档案中是否存在
                String logisticsCompany = companyCodeMap.get(item.getCpCLogisticsId());
                if (StringUtils.isEmpty(logisticsCompany)) {
                    logisticsCompany = cpRpcService.getPlatformLogisticEcode(ocBOrder.getCpCLogisticsId(), Long.valueOf(ocBOrder.getPlatform()));
                    companyCodeMap.put(item.getCpCLogisticsId(), logisticsCompany);
                }
                detail.put("logistics_company_no", logisticsCompany);
                detail.put("sku_id", ocBOrderItem.getPsCSkuPtEcode());
                detail.put("sku", item.getPsCSkuEcode());
                sendDetails.add(detail);
            }
        }
        sendModel.put("delivery_order_items", sendDetails);
        return sendModel;
    }
}
