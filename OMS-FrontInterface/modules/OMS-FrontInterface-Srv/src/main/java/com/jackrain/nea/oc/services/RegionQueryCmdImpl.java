package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.api.RegionQueryByNameCmd;
import com.jackrain.nea.oc.oms.services.RegionQueryByNameService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @program: R3-OC-OMS-V1.4
 * @author: Lijp
 * @create: 2019-03-25 16:42
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class RegionQueryCmdImpl implements RegionQueryByNameCmd {

    @Resource
    private RegionQueryByNameService resionQueryByNameService;

    @Override
    public ValueHolderV14 queryByName(JSONObject obj) {
        return resionQueryByNameService.queryByName(obj);
    }
}
