package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.oc.oms.api.OcJumpUrlCmd;
import com.jackrain.nea.oc.oms.api.OrderLogSearchCmd;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySessionImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * @author: 郑立轩
 * @since: 2019/3/12
 * create at : 2019/3/12 15:06
 */
@Api(value = "JumpUrlCtrl", tags = "退换货列表跳转接口")
@Slf4j
@RestController
public class JumpUrlCtrl {

    @Reference(version = "1.0", group = "oc")
    private OcJumpUrlCmd cmd;

    @Autowired
    private OrderLogSearchCmd searchCmd;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;


    @ApiOperation(value = "退换货列表点击跳转")
    @RequestMapping(value = "/api/cs/oc/oms/v1/jumpUrl", method = RequestMethod.POST)
    public JSONObject jumpUrl(HttpServletRequest request,
                              @RequestBody JSONObject obj) {
//        if (log.isDebugEnabled()) {
//            log.debug("Start jumpUrl. ReceiveParams=" + obj.toString() + ";");
//        }
        //JSONObject jsonObject = JSON.parseObject(param);
        //获取用户登录信息
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        //调用服务
        ValueHolderV14<String> vh = cmd.jumpUrl(obj, user);
        String result = JSON.toJSONStringWithDateFormat(vh.toJSONObject(),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue);
//        if (log.isDebugEnabled()) {
//            log.debug("Finish querySalesReturn. Return Result=" + result);
//        }
        return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));


    }

    @PostMapping("/api/cs/oc/oms/v1/searchLog")
    public JSONObject searchLog(HttpServletRequest request,
                                @RequestBody JSONObject obj) {
        QuerySessionImpl querySession = new QuerySessionImpl();
        DefaultWebEvent event = new DefaultWebEvent("search", request);
        event.put("param", obj);
        querySession.setEvent(event);
        ValueHolder execute = searchCmd.execute(querySession);
        return JSONObject.parseObject(JSONObject.toJSONString(execute.toJSONObject(), SerializerFeature.WriteMapNullValue));


    }


}
