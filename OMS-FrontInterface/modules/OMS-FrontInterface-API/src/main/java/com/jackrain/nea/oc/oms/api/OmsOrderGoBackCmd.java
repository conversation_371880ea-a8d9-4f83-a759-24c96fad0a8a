package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2019/12/4 1:08 下午
 * @Version 1.0
 * 退回
 */
public interface OmsOrderGoBackCmd {
    /**
     * @param ids 来源单据id(订单id)
     * @return
     */
    ValueHolderV14 orderGoBack(List<Long> ids, User user) throws NDSException;
}
