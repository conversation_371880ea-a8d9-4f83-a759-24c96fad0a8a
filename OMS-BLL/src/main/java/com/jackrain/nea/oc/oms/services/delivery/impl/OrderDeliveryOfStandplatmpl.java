package com.jackrain.nea.oc.oms.services.delivery.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.channel.model.request.product.SgChannelProductQueryRequest;
import com.burgeon.r3.sg.core.model.table.channel.product.SgBChannelProduct;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpLogisticsItem;
import com.jackrain.nea.data.basic.model.request.ProInfoQueryRequest;
import com.jackrain.nea.data.basic.services.BasicPsQueryService;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.hub.model.minipt.OrderDeliveryReq;
import com.jackrain.nea.ip.model.others.OrderDeliveryParcelbackModel;
import com.jackrain.nea.ip.model.others.StandplatLogisticsSendDataModel;
import com.jackrain.nea.ip.model.others.StandplatLogisticsSendModel;
import com.jackrain.nea.ip.model.others.StandplatLogisticsSendResult;
import com.jackrain.nea.oc.oms.mapper.IpBStandplatOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.IpBStandplatOrderMapper;
import com.jackrain.nea.oc.oms.mapper.IpBStandplatRefundMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderDeliveryMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderEqualExchangeItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderSourceRelationMapper;
import com.jackrain.nea.oc.oms.model.enums.GiftTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OcBOrderSourceRelationTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatOrder;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatOrderItemEx;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderDelivery;
import com.jackrain.nea.oc.oms.model.table.OcBOrderEqualExchangeItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.nums.LogisticsTelEnum;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.IpStandplatOrderService;
import com.jackrain.nea.oc.oms.services.OmsOrderItemService;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.services.OrderPlatformDeliveryService;
import com.jackrain.nea.oc.oms.services.advance.OmsOrderSpiltUtill;
import com.jackrain.nea.oc.oms.services.delivery.OrderDeliveryCmd;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.ps.api.table.PsCPro;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.HubRpcService;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.assertj.core.util.Sets;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description:通用平台发货实现类
 *
 * <AUTHOR> sunies
 * @since : 2020-11-03
 * create at : 2020-11-03 20:02
 */
@Slf4j
@Component
public class OrderDeliveryOfStandplatmpl implements OrderDeliveryCmd {

    /**
     * 拼多多非首单发货延迟（单位：毫秒）
     */
    @NacosValue(value = "${r3.oc.oms.pdd.delivery.delay:60000}", autoRefreshed = true)
    public Long pddDeliveryDelay;

    /**
     * 拼多多多包裹发货最大包裹数量限制
     */
    @NacosValue(value = "${r3.oc.oms.pdd.delivery.package.num:10}", autoRefreshed = true)
    public Integer pddDeliveryPackageNum;

    /**
     * 唯品会mp多包裹发货指定店铺ids
     */
    @NacosValue(value = "${r3.oc.oms.vph.delivery.shop.ids:1}", autoRefreshed = true)
    public String vphShopIds;

    @Autowired
    private OcBOrderDeliveryMapper ocBOrderDeliveryMapper;
    @Autowired
    private IpRpcService ipRpcService;
    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private BasicPsQueryService basicPsQueryService;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OmsOrderItemService omsOrderItemServie;
    @Autowired
    private OmsOrderService omsOrderService;
    @Autowired
    private IpBStandplatRefundMapper ipBStandplatRefundMapper;
    @Autowired
    private PropertiesConf pconf;
    @Autowired
    private OmsOrderLogService orderLogService;
    @Autowired
    OmsOrderSpiltUtill omsOrderSpiltUtill;
    @Autowired
    private SgRpcService sgRpcService;
    @Autowired
    private OcBOrderEqualExchangeItemMapper ocBOrderEqualExchangeItemMapper;
    @Autowired
    private HubRpcService hubRpcService;
    @Autowired
    private OcBOrderSourceRelationMapper sourceRelationMapper;
    @Autowired
    private IpBStandplatOrderMapper ipBStandplatOrderMapper;
    @Autowired
    private IpBStandplatOrderItemMapper ipBStandplatOrderItemMapper;

    @Override
    public boolean deliveryDeal(OcBOrderRelation ocBOrderRelation, List<String> tips) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("通用平台发货服务,订单id为={}", "通用平台发货服务", ocBOrderRelation.getOrderId()), ocBOrderRelation.getOrderId());
        }
        OcBOrder ocBOrder = ocBOrderRelation.getOrderInfo();

        //如果平台为POS，强制平台发货成功
        if (ocBOrderRelation.isPos()) {
            return ocBOrderMapper.updateOrderAfterPlatDeliverySuccess(ocBOrder.getId()) > 0;
        }

        /**
         * 0111测试未完成不上，等待下一个版本
         */
        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
        String property = config.getProperty("r3.oc.oms.exchange.send", "0");
        //抖音-拼多多 换货发货
        if (ocBOrder.getPlatform() != null && "1".equals(property)) {
            if (log.isDebugEnabled()) {
                log.debug(" 抖音换货发货服务.ocBOrderRelation {}", JSON.toJSONString(ocBOrderRelation));
            }
            if ((PlatFormEnum.DOU_YIN.getCode().equals(ocBOrder.getPlatform()) || PlatFormEnum.PINDUODUO.getCode().equals(ocBOrder.getPlatform())) && 2 == ocBOrder.getOrderType() && !"手工新增".equals(ocBOrder.getOrderSource())) {
                List<IpBStandplatRefund> ipBStandplatRefunds = ipBStandplatRefundMapper.selectList(new QueryWrapper<IpBStandplatRefund>().lambda().eq(IpBStandplatRefund::getOrderNo, ocBOrder.getTid()).eq(IpBStandplatRefund::getRefundType, 3));
                if (CollectionUtils.isEmpty(ipBStandplatRefunds)) {
                    return false;
                }
                String company_code = cpRpcService.getPlatformLogisticEcode(ocBOrder.getCpCLogisticsId(), Long.valueOf(ocBOrder.getPlatform()));

                ValueHolderV14 v14 = ipRpcService.sendStandPlatLogisticsExchange(ocBOrder, company_code, ipBStandplatRefunds.get(0).getReturnNo());
                if (v14 == null || v14.getCode() == ResultCode.FAIL) {
                    //更新发货状态，插入日志
                    String logMsg = "OrderId=" + ocBOrder.getId() + "换货发货通知平台失败," + (Objects.nonNull(v14) ? v14.getMessage() : Strings.EMPTY);
                    orderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.PLATFORM_SEND.getKey(), logMsg, "", null, null);
                    return false;
                } else {
                    //更新发货状态，插入日志
                    String logMsg = "OrderId=" + ocBOrder.getId() + "换货发货通知平台成功";
                    orderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.PLATFORM_SEND.getKey(), logMsg, "", null, null);
                    OcBOrder update = new OcBOrder();
                    update.setOrderStatus(OmsOrderStatus.PLATFORM_DELIVERY.toInteger());
                    update.setId(ocBOrder.getId());
                    ocBOrderMapper.updateById(update);
                }
                return true;
            }
        }

        List<OcBOrderItem> orderItemList = ocBOrderRelation.getOrderItemList();
        //增加多包裹处理方法
        if (ocBOrder.getIsMultiPack().equals(1L) && PlatFormEnum.SUNING_ONLINE_MARKET.getCode().equals(ocBOrder.getPlatform())) {
            List<OcBOrderDelivery> ocBOrderDeliveryList = ocBOrderDeliveryMapper.selectOrderDeliveryByOrderId(ocBOrder.getId());
            List<String> expressCodeList = ocBOrderDeliveryList.stream().map(OcBOrderDelivery::getLogisticNumber).distinct().collect(Collectors.toList());
            //调用多包裹平台发货方法
            multiplePakageSNCallInterface(ocBOrder, orderItemList, expressCodeList);
        } else {
            //抖音子单号追加包裹发货记录子单号
            Set<String> ooids = Sets.newHashSet();
            //没有平台发货过的子单号
            List<String> noPlatformDeliveryOoids = Lists.newArrayList();
            if (PlatFormEnum.DOU_YIN.getCode().equals(ocBOrder.getPlatform())) {
                //平台单号下所有订单
                List<OcBOrder> ocBOrders = ocBOrderMapper.selectOcBOrderByTid(ocBOrder.getTid());
                List<Long> ocborderIds = ocBOrders.stream().map(OcBOrder::getId).collect(Collectors.toList());

                //订单-明细(过滤退款&无子单号)
                List<OcBOrderItem> ocBOrderItems = ocBOrderItemMapper.selectOrderItemsNoRefundAndOoidByIds(ocborderIds);
                Map<Long, List<OcBOrderItem>> itemMap = ocBOrderItems.stream().collect(Collectors.groupingBy(OcBOrderItem::getOcBOrderId));

                //查找平台发货的订单，过滤补发/复制
                List<OcBOrder> platformOrders = ocBOrders.stream().filter(p ->
                        OmsOrderStatus.PLATFORM_DELIVERY.toInteger().equals(p.getOrderStatus())
                                && !YesNoEnum.Y.getVal().equals(p.getIsCopyOrder())
                                && !YesNoEnum.Y.getVal().equals(p.getIsResetShip())).collect(Collectors.toList());

                //当前订单明细
                List<OcBOrderItem> orderItems = itemMap.get(ocBOrder.getId());
                if (CollectionUtils.isEmpty(orderItems)) {
                    //查询平台单号下其他平台发货的订单，获取子订单号
                    if (CollectionUtils.isEmpty(platformOrders)) {
                        //发货失败，后面重试
                        return failRetryDelivery(ocBOrderRelation, "无符合发货条件的明细且无其他平台发货的订单，待下次重试");
                    }

                    platformOoids(ooids, itemMap, platformOrders);

                    if (CollectionUtils.isEmpty(ooids)) {
                        //发货失败，后面重试
                        return failRetryDelivery(ocBOrderRelation, "子单号无其他平台发货的订单，待下次重试");
                    }
                } else {
                    if (CollectionUtils.isNotEmpty(platformOrders)) {
                        platformDeal(ooids, noPlatformDeliveryOoids, ocBOrderItems, platformOrders, orderItems);
                    } else {
                        noPlatformDeliveryOoids = orderItems.stream().map(OcBOrderItem::getOoid).collect(Collectors.toList());
                    }
                }
            }

            List<StandplatLogisticsSendDataModel> standplatLogisticsSendDataModels = this.addCommonIpParam(ocBOrderRelation, null, null, null);
            //多包裹发货
            if (isManyDelivery(ocBOrder, ooids)) {
                boolean b = this.manySend(standplatLogisticsSendDataModels, ocBOrderRelation, ooids);
                log.info(LogUtil.format("多包裹多次发货 orderId:{},models:{},result:{}", ocBOrder.getPlatform(), "manySend"), ocBOrder.getId(), JSON.toJSONString(standplatLogisticsSendDataModels), b);
                return b;
            } else {
                try {
                    for (StandplatLogisticsSendDataModel model : standplatLogisticsSendDataModels) {
                        //快手首单0
                        if (PlatFormEnum.KUAISHOU.getCode().equals(ocBOrder.getPlatform())) {
                            kuaishouSend(ocBOrder, model);
                        }

                        //唯品会mp指定店铺发货
                        if (PlatFormEnum.VIP_MP_STANDARD.getCode().equals(ocBOrder.getPlatform())) {
                            weiPinHuiMpSend(ocBOrder, model);
                        }

                        //抖音首次发货
                        if (PlatFormEnum.DOU_YIN.getCode().equals(ocBOrder.getPlatform())) {
                            douyinOoidFirstSend(ocBOrder, model, noPlatformDeliveryOoids);
                        }

                        ValueHolderV14<List<StandplatLogisticsSendResult>> vh = ipRpcService.sendStandPlatLogistics(model);
                        if (vh == null || vh.getCode() == ResultCode.FAIL) {
                            ApplicationContextHandle.getBean(OmsOrderLogService.class).addUserOrderLog(ocBOrderRelation.getOrderId(), ocBOrderRelation.getOrderInfo().getBillNo(), OrderLogTypeEnum.PLATFORM_SEND.getKey(), "平台发货失败," + (vh == null ? "" : vh.getMessage()), null, null, SystemUserResource.getRootUser());
                            return false;
                        }
                    }
                    return true;
                } catch (Exception e) {
                    log.error("调用通用发货rpc异常", e);
                    return failRetryDelivery(ocBOrderRelation, e.getMessage());
                }
            }
        }
        return true;
    }

    private void platformOoids(Set<String> ooids, Map<Long, List<OcBOrderItem>> itemMap, List<OcBOrder> platformOrders) {
        for (OcBOrder platformOrder : platformOrders) {
            List<OcBOrderItem> bOrderItems = itemMap.get(platformOrder.getId());
            if (CollectionUtils.isNotEmpty(bOrderItems)) {
                ooids.add(bOrderItems.get(0).getOoid());
                break;
            }
        }
    }

    private void platformDeal(Set<String> ooids, List<String> noPlatformDeliveryOoids, List<OcBOrderItem> ocBOrderItems, List<OcBOrder> platformOrders, List<OcBOrderItem> orderItems) {
        //子单号是否已经平台发货过 ooid-orderIds
        List<Long> platformOrderIds = platformOrders.stream().map(OcBOrder::getId).collect(Collectors.toList());
        Map<String, Long> ooidOrderMap = Maps.newHashMap();
        for (OcBOrderItem orderItem : ocBOrderItems) {
            Long ocBOrderId = orderItem.getOcBOrderId();
            if (platformOrderIds.contains(ocBOrderId)) {
                ooidOrderMap.put(orderItem.getOoid(), ocBOrderId);
            }
        }

        //已发货过集合
        List<String> deliveryOoids = Lists.newArrayList();
        for (OcBOrderItem orderItem : orderItems) {
            String ooid = orderItem.getOoid();
            Long orderId = ooidOrderMap.get(ooid);
            if (Objects.isNull(orderId)) {
                //未发货过
                noPlatformDeliveryOoids.add(ooid);
            } else {
                //已发过货
                deliveryOoids.add(ooid);
            }
        }

        if (CollectionUtils.isEmpty(noPlatformDeliveryOoids)) {
            //追加包裹
            ooids.addAll(new HashSet<>(deliveryOoids));
        }
    }

    private boolean failRetryDelivery(OcBOrderRelation ocBOrderRelation, String s) {
        OcBOrder orderInfo = ocBOrderRelation.getOrderInfo();

        OcBOrder ocBOrderflag = new OcBOrder();
        ocBOrderflag.setId(orderInfo.getId());
        ocBOrderflag.setIsForce(0L);
        ocBOrderflag.setForceSendFailReason(s);
        omsOrderService.updateOrderInfo(ocBOrderflag);

        //更新发货状态，插入日志
        String logMsg = "OrderId=" + orderInfo.getId() + ",平台单号=" + orderInfo.getTid() + s;
        orderLogService.addUserOrderLog(orderInfo.getId(), orderInfo.getBillNo(), OrderLogTypeEnum.PLATFORM_SEND.getKey(), logMsg, "", null, null);

        return false;
    }

    /**
     * 符合条件 多包裹发货
     *
     * @param ocBOrder
     * @return
     */
    private boolean isManyDelivery(OcBOrder ocBOrder, Set<String> ooids) {
        Integer platform = ocBOrder.getPlatform();

        //万物心选
        if (PlatFormEnum.ALL_THINGS_CHOICE.getCode().equals(platform)) {
            return true;
        }

        //好衣库
        if (PlatFormEnum.HAO_YI_KU.getCode().equals(platform)) {
            return true;
        }

        //快手&非首单
        if (PlatFormEnum.KUAISHOU.getCode().equals(platform) && !deliveryIsFirst(ocBOrder)) {
            return true;
        }

        //拼多多%非首单
        if (PlatFormEnum.PINDUODUO.getCode().equals(platform) && !deliveryIsFirst(ocBOrder)) {
            return true;
        }

        //抖音追加包裹
        return PlatFormEnum.DOU_YIN.getCode().equals(platform) && CollectionUtils.isNotEmpty(ooids);
    }

    /**
     * 唯品会MP发货
     *
     * @param ocBOrder
     * @param model
     */
    private void weiPinHuiMpSend(OcBOrder ocBOrder, StandplatLogisticsSendDataModel model) {
        List<String> vShopIds = Arrays.asList(vphShopIds.split(","));

        if (!vShopIds.contains(ocBOrder.getCpCShopId().toString())) {
            return;
        }

        if (!isTailOrderSingle(ocBOrder, ocBOrderMapper.selectOcBOrderByTid(ocBOrder.getTid()))) {
            //非尾单，数量为0
            setNumZero(model);
            return;
        }

        //尾单
        List<StandplatLogisticsSendModel> logisticsSendModels = model.getLogisticsSendModels();
        for (StandplatLogisticsSendModel logisticsSendModel : logisticsSendModels) {
            String tid = logisticsSendModel.getTid();

            //isSplit设置为0
            logisticsSendModel.setIsSplit(0L);

            StandplatLogisticsSendModel.StandplatLogisticsSendDetails oldDetail = logisticsSendModel.getStandplatLogisticsSendDetails().get(0);
            IpBStandplatOrder ipBStandplatOrder = ipBStandplatOrderMapper.selectStandplatOrderByTid(tid);
            List<IpBStandplatOrderItemEx> ipBStandplatOrderItemExes = ipBStandplatOrderItemMapper.selectOrderItemList(ipBStandplatOrder.getId());
            List<StandplatLogisticsSendModel.StandplatLogisticsSendDetails> details = Lists.newArrayList();
            for (IpBStandplatOrderItemEx ipBStandplatOrderItemEx : ipBStandplatOrderItemExes) {
                StandplatLogisticsSendModel.StandplatLogisticsSendDetails sendDetails = new StandplatLogisticsSendModel.StandplatLogisticsSendDetails();
                sendDetails.setItemId(ipBStandplatOrderItemEx.getOid());
                sendDetails.setDeliveryNo(oldDetail.getDeliveryNo());
                sendDetails.setLackNum(oldDetail.getLackNum());
                sendDetails.setLogisticsCompany(oldDetail.getLogisticsCompany());
                sendDetails.setNum(ipBStandplatOrderItemEx.getNum());
                sendDetails.setRealNum(ipBStandplatOrderItemEx.getNum());
                sendDetails.setProductCode(ipBStandplatOrderItemEx.getOuterSkuId());
                sendDetails.setSku(ipBStandplatOrderItemEx.getOuterSkuId());
                sendDetails.setSkuId(formatVipMpSkuId(ipBStandplatOrderItemEx.getSkuId()));
                sendDetails.setExpressType(oldDetail.getExpressType());
                details.add(sendDetails);
            }
            logisticsSendModel.setStandplatLogisticsSendDetails(details);
        }
    }

    /**
     * 通用发货明细数量设置为0
     * ipSplit设置为0
     *
     * @param model
     */
    private void setNumZero(StandplatLogisticsSendDataModel model) {
        List<StandplatLogisticsSendModel> sendModels = model.getLogisticsSendModels();
        if (CollectionUtils.isEmpty(sendModels)) {
            return;
        }
        for (StandplatLogisticsSendModel logisticsSendModel : sendModels) {
            //isSplit设置为0
            logisticsSendModel.setIsSplit(0L);

            List<StandplatLogisticsSendModel.StandplatLogisticsSendDetails> sendDetails = logisticsSendModel.getStandplatLogisticsSendDetails();
            if (CollectionUtils.isEmpty(sendDetails)) {
                continue;
            }
            for (StandplatLogisticsSendModel.StandplatLogisticsSendDetails item : sendDetails) {
                item.setNum(0L);
                item.setRealNum(0L);
            }
        }
    }

    /**
     * 快手首单发货
     *
     * @param ocBOrder
     * @param model
     */
    private void kuaishouSend(OcBOrder ocBOrder, StandplatLogisticsSendDataModel model) {
        List<OcBOrderItem> ocBOrderItems = giftTypeCheck(ocBOrder.getTid());
        if (CollectionUtils.isEmpty(ocBOrderItems)) {
            //无平台赠品
            kuaiShouIsSplitZero(model);
            return;
        }
        //有平台赠品
        List<OcBOrderItem> ocBOrderItemList = ocBOrderItems.stream().filter(p -> StringUtils.isNotBlank(p.getOoid())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ocBOrderItemList)) {
            kuaiShouIsSplitZero(model);
            return;
        }

        Set<String> ooids = ocBOrderItemList.stream().map(OcBOrderItem::getOoid).collect(Collectors.toSet());

        IpBStandplatOrder ipBStandplatOrder = ipBStandplatOrderMapper.selectStandplatOrderByTid(ocBOrder.getTid());
        List<IpBStandplatOrderItemEx> ipBStandplatOrderItemExes = ipBStandplatOrderItemMapper.selectOrderItemList(ipBStandplatOrder.getId());
        Map<String, List<IpBStandplatOrderItemEx>> detailMap = ipBStandplatOrderItemExes.stream().collect(Collectors.groupingBy(IpBStandplatOrderItemEx::getOid));

        List<StandplatLogisticsSendModel> logisticsSendModels = model.getLogisticsSendModels();
        for (StandplatLogisticsSendModel logisticsSendModel : logisticsSendModels) {
            List<StandplatLogisticsSendModel.StandplatLogisticsSendDetails> standplatLogisticsSendDetails = logisticsSendModel.getStandplatLogisticsSendDetails();
            StandplatLogisticsSendModel.StandplatLogisticsSendDetails details = standplatLogisticsSendDetails.get(0);
            List<StandplatLogisticsSendModel.StandplatLogisticsSendDetails> newList = Lists.newArrayList();
            for (String ooid : ooids) {
                List<IpBStandplatOrderItemEx> itemExes = detailMap.get(ooid);
                //快手不考虑重复明细的情况
                IpBStandplatOrderItemEx orderItemEx = itemExes.get(0);

                StandplatLogisticsSendModel.StandplatLogisticsSendDetails sendDetails = new StandplatLogisticsSendModel.StandplatLogisticsSendDetails();
                sendDetails.setItemId(ooid);
                sendDetails.setDeliveryNo(details.getDeliveryNo());
                sendDetails.setLogisticsCompany(details.getLogisticsCompany());

                String skuId = orderItemEx.getSkuId();
                if (skuId == null) {
                    skuId = "";
                } else {
                    String[] split = skuId.split("-");
                    if (split.length > 1) {
                        skuId = split[1];
                    }
                }

                sendDetails.setSkuId(skuId);
                sendDetails.setSku(orderItemEx.getOuterSkuId());
                sendDetails.setProductCode(orderItemEx.getOuterSkuId());
                sendDetails.setNum(orderItemEx.getNum());
                sendDetails.setRealNum(orderItemEx.getNum());
                sendDetails.setLackNum(details.getLackNum());
                sendDetails.setExpressType(details.getExpressType());

                newList.add(sendDetails);
            }
            //不管是否拆单，都传1
            logisticsSendModel.setIsSplit(1L);
            logisticsSendModel.setStandplatLogisticsSendDetails(newList);
        }
    }

    /**
     * 订单明细是否含有平台赠品
     *
     * @param tid
     * @return
     */
    private List<OcBOrderItem> giftTypeCheck(String tid) {
        boolean platformGift = false;

        List<OcBOrder> ocBOrders = ocBOrderMapper.selectOcBOrderByTid(tid);
        //过滤取消状态的订单
        ocBOrders = ocBOrders.stream().filter(p -> !OmsOrderStatus.CANCELLED.toInteger().equals(p.getOrderStatus())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ocBOrders)) {
            return Lists.newArrayList();
        }

        List<Long> orderIds = ocBOrders.stream().map(OcBOrder::getId).collect(Collectors.toList());
        List<OcBOrderItem> ocBOrderItems = ocBOrderItemMapper.selectOrderItemsByOrderIds(orderIds);
        if (CollectionUtils.isEmpty(ocBOrderItems)) {
            return Lists.newArrayList();
        }

        for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
            String giftType = ocBOrderItem.getGiftType();
            if (GiftTypeEnum.PLATFORM.getVal().equals(giftType)) {
                platformGift = true;
                break;
            }
        }

        if (platformGift) {
            return ocBOrderItems;
        }

        return Lists.newArrayList();
    }

    /**
     * 云枢纽强制isSplit=0
     *
     * @param model
     */
    private void kuaiShouIsSplitZero(StandplatLogisticsSendDataModel model) {
        List<StandplatLogisticsSendModel> logisticsSendModels = model.getLogisticsSendModels();
        if (CollectionUtils.isNotEmpty(logisticsSendModels)) {
            for (StandplatLogisticsSendModel logisticsSendModel : logisticsSendModels) {
                logisticsSendModel.setIsSplit(0L);
            }
        }
    }

    /**
     * 查找所有仓库发货+平台发货的零售发货单
     *
     * @param tid
     * @return
     */
    private Map<Long, List<StandplatLogisticsSendDataModel>> allThingsChoiceDeliveryParam(String tid) {
        Map<Long, List<StandplatLogisticsSendDataModel>> sendModelMap = Maps.newHashMap();
        //查找所有有物流单号的零售发货单
        List<OcBOrder> ocBOrders = ocBOrderMapper.selectOcBOrderByTid(tid);
        for (OcBOrder bOrder : ocBOrders) {
            if (OrderTypeEnum.EXCHANGE.getVal().equals(bOrder.getOrderType())) {
                continue;
            }
            if (OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger().equals(bOrder.getOrderStatus()) || OmsOrderStatus.PLATFORM_DELIVERY.toInteger().equals(bOrder.getOrderStatus())) {
                List<OcBOrderItem> ocBOrderItems = ocBOrderItemMapper.selectOrderItemList(bOrder.getId());

                //无子订单号过滤
                List<OcBOrderItem> orderItems = ocBOrderItems.stream().filter(p -> StringUtils.isNotBlank(p.getOoid())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(orderItems)) {
                    continue;
                }

                OcBOrderRelation relation = new OcBOrderRelation();
                relation.setOrderInfo(bOrder);
                relation.setOrderItemList(ocBOrderItems);

                List<String> ooids = Lists.newArrayList();
                List<OcBOrderItem> ooidItems = ocBOrderItems.stream().filter(p -> p.getOoid() != null).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(ooidItems)) {
                    Set<String> ooidSets = ooidItems.stream().map(OcBOrderItem::getOoid).collect(Collectors.toSet());
                    ooids.addAll(ooidSets);
                }

                sendModelMap.put(bOrder.getId(), this.addCommonIpParam(relation, StringUtils.join(ooids, ","), null, null));
            }
        }
        return sendModelMap;
    }

    /**
     * 通用平台多包裹发货
     *
     * @param ocBOrder
     * @param ocBOrderItemList
     * @param expressCodeList
     */
    public void multiplePakageSNCallInterface(OcBOrder ocBOrder, List<OcBOrderItem> ocBOrderItemList, List<String> expressCodeList) {
        if (ocBOrderItemList.size() >= expressCodeList.size()) {
            for (int i = 0; i < expressCodeList.size(); i++) {
                OrderPlatformDeliveryService.threadSleep(true, 2000);
                String expressCode = expressCodeList.get(i);
                if (i + 1 == expressCodeList.size()) {
                    if (ocBOrderItemList.size() > 0) {
                        OcBOrderRelation ocBOrderRelation = new OcBOrderRelation();
                        ocBOrderRelation.setOrderInfo(ocBOrder);
                        ocBOrderRelation.setOrderItemList(ocBOrderItemList);
                        List<String> collect = ocBOrderItemList.stream().map(OcBOrderItem::getOoid).distinct().collect(Collectors.toList());
                        String newOid = StringUtils.join(collect, ",");
                        List<StandplatLogisticsSendDataModel> standplatLogisticsSendDataModels = this.addCommonIpParam(ocBOrderRelation, newOid, 1, expressCode);
                        if (CollectionUtils.isNotEmpty(standplatLogisticsSendDataModels)) {
                            for (StandplatLogisticsSendDataModel model : standplatLogisticsSendDataModels) {
                                ipRpcService.sendStandPlatLogistics(model);
                            }
                        }
                    }
                } else {
                    OcBOrderItem ocBOrderItem = ocBOrderItemList.get(0);
                    String oid = ocBOrderItem.getOoid();
                    OcBOrderRelation ocBOrderRelation = new OcBOrderRelation();
                    ocBOrderRelation.setOrderInfo(ocBOrder);
                    List<OcBOrderItem> items = new ArrayList<>();
                    items.add(ocBOrderItem);
                    ocBOrderRelation.setOrderItemList(items);
                    List<StandplatLogisticsSendDataModel> standplatLogisticsSendDataModels = this.addCommonIpParam(ocBOrderRelation, oid, 1, expressCode);
                    if (CollectionUtils.isNotEmpty(standplatLogisticsSendDataModels)) {
                        for (StandplatLogisticsSendDataModel model : standplatLogisticsSendDataModels) {
                            ValueHolderV14<List<StandplatLogisticsSendResult>> vh = ipRpcService.sendStandPlatLogistics(model);
                            log.debug(this.getClass().getName() + "通用小平台多包裹平台发货接口，请求云枢纽，返回信息为：" + vh.toString());
                        }
                    }
                    ocBOrderItemList.remove(ocBOrderItem);
                }
            }
        } else {
            for (int j = 0; j < ocBOrderItemList.size(); j++) {
                OrderPlatformDeliveryService.threadSleep(true, 2000);
                OcBOrderRelation ocBOrderRelation = new OcBOrderRelation();
                ocBOrderRelation.setOrderInfo(ocBOrder);
                List<OcBOrderItem> items = new ArrayList<>();
                items.add(ocBOrderItemList.get(j));
                ocBOrderRelation.setOrderItemList(items);

                String oid = ocBOrderItemList.get(j).getOoid();
                String expressCode = expressCodeList.get(j);
                List<StandplatLogisticsSendDataModel> standplatLogisticsSendDataModels = this.addCommonIpParam(ocBOrderRelation, oid, 1, expressCode);
                if (CollectionUtils.isNotEmpty(standplatLogisticsSendDataModels)) {
                    for (StandplatLogisticsSendDataModel model : standplatLogisticsSendDataModels) {
                        ValueHolderV14<List<StandplatLogisticsSendResult>> vh = ipRpcService.sendStandPlatLogistics(model);
                        log.debug(this.getClass().getName() + "苏宁多包裹平台发货接口，请求云枢纽，返回信息为：" + vh.toString());
                    }
                }
            }
        }
    }

    /**
     * 组装通用发货平台参数
     *
     * @param ocBOrderRelation
     * @param subTid
     * @param isSplit
     * @param expressCode
     * @return
     */
    private List<StandplatLogisticsSendDataModel> addCommonIpParam(OcBOrderRelation ocBOrderRelation, String subTid, Integer isSplit, String expressCode) {
        OcBOrder orderInfo = ocBOrderRelation.getOrderInfo();
        List<StandplatLogisticsSendDataModel> result = new ArrayList<>();
        List<OcBOrderItem> ocBOrderItems = null;
        if (StringUtils.isEmpty(expressCode)) {
            ocBOrderItems = omsOrderItemServie.selectUnSuccessRefund(orderInfo.getId());
        } else {
            ocBOrderItems = ocBOrderRelation.getOrderItemList();
        }

        if (CollectionUtils.isEmpty(ocBOrderItems)) {
            return result;
        }

        Map<String, List<OcBOrderItem>> lists = ocBOrderItems.stream().collect(Collectors.groupingBy(OcBOrderItem::getTid));
        for (Map.Entry<String, List<OcBOrderItem>> map : lists.entrySet()) {
            List<OcBOrderItem> orderItems = map.getValue();
            if (CollectionUtils.isNotEmpty(orderItems)) {
                //如果存在福袋、组合商品将真实商品明细转换为平台的虚拟明细
                Long id = orderInfo.getId();
                orderItems = transformCommonIpItemParam(orderItems, id, orderInfo.getPlatform());
                StandplatLogisticsSendDataModel model = getStandplatLogisticsSendDataModel(ocBOrderRelation, subTid, isSplit, expressCode, orderItems);
                if (model != null) {
                    result.add(model);
                }
                // 判断平台
                if (PlatFormEnum.DOUYIN_RETAIL.getCode().equals(orderInfo.getPlatform())) {
                    List<StandplatLogisticsSendModel> logisticsSendModels = model.getLogisticsSendModels();
                    if (CollectionUtils.isNotEmpty(logisticsSendModels)){
                        IpBStandplatOrder ipBStandplatOrder = ipBStandplatOrderMapper.selectStandplatOrderByTid(orderInfo.getTid());
                        for (StandplatLogisticsSendModel sendModel : logisticsSendModels){
                            sendModel.setSellerNick(ipBStandplatOrder.getSellerShopName());
                            sendModel.setStoreNumber(ipBStandplatOrder.getSellerShopId());
                        }
                    }
                }
            }
        }
        return result;
    }

    /**
     * 如果存在福袋、组合商品将真实商品明细转换为平台的虚拟明细
     *
     * @param orderItems
     * @param id
     * @return
     */
    private List<OcBOrderItem> transformCommonIpItemParam(List<OcBOrderItem> orderItems, Long id, Integer platform) {
        List<OcBOrderItem> newOrderItems = new ArrayList<>();
        List<OcBOrderItem> transformOrderItems = new ArrayList<>();
        for (OcBOrderItem item : orderItems) {
            Long proType = item.getProType();
            if (proType != null && (proType.intValue() == SkuType.COMBINE_PRODUCT || proType.intValue() == SkuType.GIFT_PRODUCT)) {
                transformOrderItems.add(item);
            } else {
                newOrderItems.add(item);
            }
        }
        if (CollectionUtils.isEmpty(transformOrderItems)) {
            //需要转换的明细为空时，直接返回原明细
            return orderItems;
        }
        List<String> ooidList = transformOrderItems.stream().filter(obj -> StringUtils.isNotBlank(obj.getOoid())).map(obj -> obj.getOoid()).distinct().collect(Collectors.toList());
        //好衣库、拼多多没有子订单编号
        if (CollectionUtils.isEmpty(ooidList) && !(PlatFormEnum.HAO_YI_KU.getCode().equals(platform)
                || PlatFormEnum.PINDUODUO.getCode().equals(platform))) {
            //ooid为空时返回过滤后的明细列表
            return newOrderItems;
        }
        List<OcBOrderItem> specialItems = ocBOrderItemMapper.selectList(new LambdaQueryWrapper<OcBOrderItem>().eq(OcBOrderItem::getOcBOrderId, id).eq(OcBOrderItem::getProType, Long.valueOf(SkuType.NO_SPLIT_COMBINE)).in(CollectionUtils.isNotEmpty(ooidList), OcBOrderItem::getOoid, ooidList));
        if (CollectionUtils.isNotEmpty(specialItems)) {
            newOrderItems.addAll(specialItems);
        }
        return newOrderItems;
    }

    /**
     * 组装单个通用发货平台参数
     *
     * @param ocBOrderRelation
     * @param subTid
     * @param isSplit
     * @param expressCode
     * @param orderItems
     * @return
     */
    private StandplatLogisticsSendDataModel getStandplatLogisticsSendDataModel(OcBOrderRelation ocBOrderRelation, String subTid, Integer isSplit, String expressCode, List<OcBOrderItem> orderItems) {
        // @20200830 爱库存平台发货需求TID取值逻辑（要查询中间表的）
        String itemTid = null;

        if (CollectionUtils.isNotEmpty(orderItems)) {
            for (OcBOrderItem item : orderItems) {
                if (Objects.nonNull(item) && Objects.nonNull(item.getTid())) {
                    itemTid = item.getTid();
                    break;
                }
            }
        }

        OcBOrder ocBOrder = ocBOrderRelation.getOrderInfo();
        BigDecimal skuNum = BigDecimal.ZERO;
        //爱库存拆单发货 子单号获取
        StringBuilder subIds = new StringBuilder();

        if (CollectionUtils.isNotEmpty(orderItems)) {
            for (OcBOrderItem ocBOrderItem : orderItems) {
                if (ocBOrderItem.getQty() != null) {
                    skuNum = skuNum.add(ocBOrderItem.getQty());
                }
                if (StringUtils.isNotBlank(ocBOrderItem.getOoid())) {
                    subIds.append(ocBOrderItem.getOoid()).append(",");
                }
            }
        }
        StandplatLogisticsSendDataModel standplatLogisticsSendDataModel = new StandplatLogisticsSendDataModel();
        StandplatLogisticsSendModel sendModel = new StandplatLogisticsSendModel();
        String company_code = cpRpcService.getPlatformLogisticEcode(ocBOrder.getCpCLogisticsId(), Long.valueOf(ocBOrder.getPlatform()));
        Map<Long, String> companyCodeMap = new HashMap<>();
        companyCodeMap.put(ocBOrder.getCpCLogisticsId(), company_code);
        log.debug("调用物流公司编码查询服务==============结束：结束后，物流编码为" + company_code);
        //mq回执返回过来用来订单ID
        sendModel.setErpOrderId(ocBOrder.getId());
        if (null != isSplit) {
            sendModel.setIsSplit(isSplit.longValue());
        } else {
            sendModel.setIsSplit(ocBOrder.getIsSplit() == null ? null : ocBOrder.getIsSplit().longValue());
        }
        //不拆单 传null
        sendModel.setSubTid(subTid);
        if (CollectionUtils.isNotEmpty(orderItems)) {
            if (PlatFormEnum.AI_KU_CUN.getCode().equals(ocBOrder.getPlatform()) && sendModel.getIsSplit() != null && sendModel.getIsSplit().toString().equals("1")) {
                log.debug(" 爱库存拆单必传sub_tid {}", JSON.toJSONString(subIds.toString()));
                sendModel.setSubTid(subIds.toString());
            }
        }
        if (StringUtils.isEmpty(expressCode)) {
            sendModel.setOutSid(ocBOrder.getExpresscode());
        } else {
            sendModel.setOutSid(expressCode);
        }
        sendModel.setOutSid(ocBOrder.getExpresscode());
        //平台物流公司编码
        sendModel.setCompanyCode(company_code);
        sendModel.setPayType(ocBOrder.getPayType() == null ? "" : ocBOrder.getPayType().toString());
        sendModel.setPlatform(ocBOrder.getPlatform() == null ? null : ocBOrder.getPlatform().longValue());
        sendModel.setSellerNick(ocBOrder.getCpCShopSellerNick());
        //换货单号 暂时没有
        sendModel.setDisputeId(null);
        sendModel.setOrderType(ocBOrder.getOrderType() == null ? "" : ocBOrder.getOrderType().toString());
        sendModel.setLogisticsCompanyName(ocBOrder.getCpCLogisticsEname());
        if (PlatFormEnum.HAO_YI_KU.getCode().equals(ocBOrder.getPlatform()) || PlatFormEnum.JD_MALL.getCode().equals(ocBOrder.getPlatform())) {
            CpLogisticsItem platformLogistic = cpRpcService.getPlatformLogistic(ocBOrder.getCpCLogisticsId(), Long.valueOf(ocBOrder.getPlatform()));
            if (Objects.nonNull(platformLogistic) && StringUtils.isNotBlank(platformLogistic.getCpCLogisticsEname())) {
                sendModel.setLogisticsCompanyName(platformLogistic.getCpCLogisticsEname());
            }
        }
        //订单来源 先传空字符串，后面设计为可配置
        sendModel.setRetailSource("");
        //sku数量
        sendModel.setSize(String.valueOf(skuNum.intValue()));
        //运单包裹状态(1发货完成，2部分发货， 取固定值)
        sendModel.setStatus("1");
        //物流电话
        sendModel.setLogisticsTel(LogisticsTelEnum.enumToTel(sendModel.getCompanyCode()));
        List<StandplatLogisticsSendModel.StandplatLogisticsSendDetails> sendDetails = new ArrayList<>();
        List<OcBOrderDelivery> ocBOrderDeliveries = ocBOrderDeliveryMapper.selectOrderDeliveryByOrderId(ocBOrder.getId());
        // 中台周期购订单 第一单发货时 需要使用母单的信息去发货
        if (ocBOrderRelation.getDeliveryOrderId() != null) {
            ocBOrderDeliveries = ocBOrderDeliveryMapper.selectOrderDeliveryByOrderId(ocBOrderRelation.getDeliveryOrderId());
            OcBOrderDelivery ocBOrderDelivery = ocBOrderDeliveries.get(0);
            ocBOrder.setCpCLogisticsId(ocBOrderDelivery.getCpCLogisticsId());
            ocBOrder.setExpresscode(ocBOrderDelivery.getLogisticNumber());
            // 重新生成delivery信息
            List<OcBOrderDelivery> cycleOrderDeliveryList = new ArrayList<>();
            // 根据订单明细来生成delivery信息
            for (OcBOrderItem ocBOrderItem : orderItems) {
                OcBOrderDelivery delivery = new OcBOrderDelivery();
                delivery.setPsCProEcode(ocBOrderItem.getPsCProEcode());
                delivery.setPsCProEname(ocBOrderItem.getPsCProEname());
                delivery.setPsCProId(ocBOrderItem.getPsCProId());
                delivery.setPsCSkuEcode(ocBOrderItem.getPsCSkuEcode());
                delivery.setPsCSkuId(ocBOrderItem.getPsCSkuId());
                delivery.setLogisticNumber(ocBOrderDelivery.getLogisticNumber());
                delivery.setCpCLogisticsId(ocBOrderDelivery.getCpCLogisticsId());
                delivery.setCpCLogisticsEcode(ocBOrderDelivery.getCpCLogisticsEcode());
                delivery.setCpCLogisticsEname(ocBOrderDelivery.getCpCLogisticsEname());
                cycleOrderDeliveryList.add(delivery);
            }
            ocBOrderDeliveries = cycleOrderDeliveryList;
        }
        log.debug("组装包裹信息：ocBOrderDeliveries={}", JSON.toJSONString(ocBOrderDeliveries));
        Map<String, OcBOrderItem> skuOoidMap = new HashMap<>();
        log.debug("平台发货信息：Platform={},isCombination={}", ocBOrder.getPlatform(), ocBOrder.getIsCombination());
        if (CollectionUtils.isNotEmpty(orderItems)) {
            // 抖音发货特殊处理
            if (PlatFormEnum.DOU_YIN.getCode().equals(ocBOrder.getPlatform())) {
                StandplatLogisticsSendDataModel sendDataModel = douYinSend(ocBOrderDeliveries, orderItems, sendModel, companyCodeMap, ocBOrder, false);
                return sendDataModel;
                // 组合商品整单发货特殊处理
            } else if (isCombination(ocBOrder)) {
                StandplatLogisticsSendDataModel sendDataModel = combinationSend(ocBOrderDeliveries, orderItems, sendModel, companyCodeMap, ocBOrder, itemTid);
                return sendDataModel;
                // 组合商品拆单特殊处理
            } else if (isCombinationSplit(orderItems, ocBOrder)) {
                StandplatLogisticsSendDataModel sendDataModel = combinationSplit(ocBOrderDeliveries, orderItems, sendModel, companyCodeMap, ocBOrder);
                return sendDataModel;
            } else {
                skuOoidMap = orderItems.stream().filter(obj -> obj != null && StringUtils.isNotEmpty(obj.getPsCSkuEcode())).collect(Collectors.toMap(OcBOrderItem::getPsCSkuEcode, a -> a, (k1, k2) -> k2));
            }
        }
        log.debug("组装包裹信息：ocBOrderDeliveries={}", JSON.toJSONString(ocBOrderDeliveries));
        log.debug("组装包裹信息：skuOoidMap={}", JSON.toJSONString(skuOoidMap));
        //组装包裹信息
        if (CollectionUtils.isNotEmpty(ocBOrderDeliveries)) {
            List<String> proCodeList = ocBOrderDeliveries.stream().filter(obj -> obj != null && StringUtils.isNotEmpty(obj.getPsCProEcode())).map(obj -> obj.getPsCProEcode()).collect(Collectors.toList());
            ProInfoQueryRequest proInfoQueryRequest = new ProInfoQueryRequest();
            proInfoQueryRequest.setProEcodeList(proCodeList);
            List<PsCPro> proInfoList = basicPsQueryService.getProInfo(proInfoQueryRequest);
            log.debug("组装包裹信息：proInfoList={}", JSON.toJSONString(proInfoList));
            Map<String, String> proCodeMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(proInfoList)) {
//                proCodeMap = proInfoList.stream().filter(obj -> obj != null && StringUtils.isNotEmpty(obj.getEcode()))
//                        .collect(Collectors.toMap(PsCPro::getEcode, PsCPro::getBrandEname, (k1, k2) -> k2));
            }
            for (OcBOrderDelivery item : ocBOrderDeliveries) {
                StandplatLogisticsSendModel.StandplatLogisticsSendDetails detail = new StandplatLogisticsSendModel.StandplatLogisticsSendDetails();
                OcBOrderItem ocBOrderItem = skuOoidMap.get(item.getPsCSkuEcode());
                if (ocBOrderItem == null) {
                    continue;
                }
                detail.setItemId(ocBOrderItem.getOoid());
                detail.setDeliveryNo(item.getLogisticNumber());
                //物流公司查询物流档案中是否存在
                String logisticsCompany = companyCodeMap.get(item.getCpCLogisticsId());
                if (StringUtils.isEmpty(logisticsCompany)) {
                    logisticsCompany = cpRpcService.getPlatformLogisticEcode(ocBOrder.getCpCLogisticsId(), Long.valueOf(ocBOrder.getPlatform()));
                    companyCodeMap.put(item.getCpCLogisticsId(), logisticsCompany);
                    detail.setLogisticsCompany(logisticsCompany);
                } else {
                    detail.setLogisticsCompany(logisticsCompany);
                }

                detail.setSku(getSku(ocBOrder, ocBOrderItem));
                detail.setSkuId(getSkuId(ocBOrder, ocBOrderItem));

                if (PlatFormEnum.MUSHROOM_STREET.getCode().equals(ocBOrder.getPlatform()) || PlatFormEnum.CHUCHU_STREET.getCode().equals(ocBOrder.getPlatform())) {
                    sendModel.setSubTid(ocBOrderItem.getOoid());
                }
                detail.setProductCode(item.getPsCProEcode());

                Long sendNum = getSendNum(ocBOrder, ocBOrderItem);
                detail.setNum(sendNum);
                detail.setRealNum(sendNum);
                detail.setLackNum(0L);
                //如果没有拆单就是 0 是拆单判断是否已经有平台发货了,有平台发货的就传1 没有就是0
                if (PlatFormEnum.WANGYIYANXUAN.getCode().equals(ocBOrder.getPlatform()) && OcBOrderConst.IS_STATUS_IY.equals(ocBOrder.getIsSplit())) {
                    //判断订单有平台发货的
                    List<OcBOrder> orderList = ocBOrderMapper.queryGsiBySourceCodeByTid(ocBOrder.getSourceCode());
                    if (CollectionUtils.isNotEmpty(orderList)) {
                        detail.setExpressType("1");
                    } else {
                        detail.setExpressType("0");
                    }
                } else {
                    detail.setExpressType("0");

                }
                sendModel.setTid(ocBOrderItem.getTid());
                sendDetails.add(detail);
            }
        }
        sendModel.setStandplatLogisticsSendDetails(sendDetails);
        // 如果是京东商仓 则需要补全四个值
        if (PlatFormEnum.JD_MALL.getCode().equals(ocBOrder.getPlatform())) {
            sendModel.setStatus("0");
            sendModel.setStoreNumber("11719309");
            sendModel.setOperateName("认养一头牛");
            sendModel.setOperateTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
            sendModel.setPickUpType("4");
        }
        if (PlatFormEnum.DOUYIN_RETAIL.getCode().equals(ocBOrder.getPlatform())) {
            // 平台以及卖家昵称 还有门店id需要设置值
            IpBStandplatOrder ipBStandplatOrder = ipBStandplatOrderMapper.selectStandplatOrderByTid(ocBOrder.getTid());
            sendModel.setSellerNick(ipBStandplatOrder.getSellerShopName());
            sendModel.setStoreNumber(ipBStandplatOrder.getSellerShopId());
        }
        List<StandplatLogisticsSendModel> list = new ArrayList<>();
        list.add(sendModel);

        // @20200830 爱库存平台发货加活动ID需求：赋值活动ID
        setActivityId4StandplatLogisticsSendModel(list, itemTid, ocBOrder.getPlatform());

        standplatLogisticsSendDataModel.setLogisticsSendModels(list);
        standplatLogisticsSendDataModel.setPlatform(sendModel.getPlatform());
        standplatLogisticsSendDataModel.setSellerNick(sendModel.getSellerNick());
        return standplatLogisticsSendDataModel;
    }

    private StandplatLogisticsSendDataModel dyCycleOrderDelivery(OcBOrderRelation ocBOrderRelation, Map<Long, String> companyCodeMap, StandplatLogisticsSendDataModel sendDataModel) {
        // 如果Relation里面的deliveryOrderId不为空的话 需要将sendDataModel里面的物流信息改掉
        if (ocBOrderRelation.getDeliveryOrderId() == null || sendDataModel == null) {
            return sendDataModel;
        }
        List<OcBOrderDelivery> deliveryByOrderIdList = ocBOrderDeliveryMapper.selectOrderDeliveryByOrderId(ocBOrderRelation.getDeliveryOrderId());
        OcBOrder deliveryOrder = new OcBOrder();
        if (CollectionUtils.isEmpty(deliveryByOrderIdList)) {
            return sendDataModel;
        }
        OcBOrderDelivery ocBOrderDelivery = deliveryByOrderIdList.get(0);
        List<StandplatLogisticsSendModel> logisticsSendModels = sendDataModel.getLogisticsSendModels();
        if (CollectionUtils.isEmpty(logisticsSendModels)) {
            return sendDataModel;
        }
        for (StandplatLogisticsSendModel logisticsSendModel : logisticsSendModels) {
            List<StandplatLogisticsSendModel.StandplatLogisticsSendDetails> standplatLogisticsSendDetails = logisticsSendModel.getStandplatLogisticsSendDetails();
            for (StandplatLogisticsSendModel.StandplatLogisticsSendDetails standplatLogisticsSendDetail : standplatLogisticsSendDetails) {
                standplatLogisticsSendDetail.setDeliveryNo(ocBOrderDelivery.getLogisticNumber());
                //物流公司查询物流档案中是否存在
                String logisticsCompany = companyCodeMap.get(ocBOrderDelivery.getCpCLogisticsId());
                if (StringUtils.isEmpty(logisticsCompany)) {
                    logisticsCompany = cpRpcService.getPlatformLogisticEcode(deliveryOrder.getCpCLogisticsId(), Long.valueOf(deliveryOrder.getPlatform()));
                    companyCodeMap.put(ocBOrderDelivery.getCpCLogisticsId(), logisticsCompany);
                    standplatLogisticsSendDetail.setLogisticsCompany(logisticsCompany);
                } else {
                    standplatLogisticsSendDetail.setLogisticsCompany(logisticsCompany);
                }
            }
        }
        return null;
    }

    private Long getSendNum(OcBOrder ocBOrder, OcBOrderItem ocBOrderItem) {
        // 存在替换商品数量
        if (Objects.nonNull(ocBOrderItem.getOriginSkuQty())) {
            return ocBOrderItem.getOriginSkuQty().longValue();
        }
        // 对等换货
        else if (OcBOrderConst.IS_STATUS_IY.equals(ocBOrder.getIsEqualExchange()) && OcBOrderConst.IS_STATUS_IY.equals(ocBOrderItem.getIsEqualExchange())) {
            //对等比例
            String ratio = ocBOrderItem.getEqualExchangeRatio();
            //计算对等换货原数量
            return paresEqualExchangeCount(ratio, ocBOrderItem.getQty());
        } else {
            return ocBOrderItem.getQty() == null ? 0L : ocBOrderItem.getQty().longValue();
        }
    }

    /**
     * 获取skuId信息
     *
     * @param ocBOrder
     * @param ocBOrderItem
     * @return
     */
    private String getSkuId(OcBOrder ocBOrder, OcBOrderItem ocBOrderItem) {
        //不取条码ID，取平台条码编码
        //skuid去掉 "-"  liuwj
        if (PlatFormEnum.VIP_MP_STANDARD.getCode().equals(ocBOrder.getPlatform())) {
            return formatVipMpSkuId(ocBOrderItem.getPsCSkuPtEcode());
        } else if (PlatFormEnum.XIAOMI_YOUPIN.getCode().equals(ocBOrder.getPlatform())) {
            // 云枢纽升级字段调整 20221012
            return formatSkuId(ocBOrderItem.getPsCSkuPtEcode());

        } else if (PlatFormEnum.PLATFORM62.getCode().equals(ocBOrder.getPlatform())
                || PlatFormEnum.ALL_THINGS_CHOICE.getCode().equals(ocBOrder.getPlatform())) {
            //云集特殊处理 taskID=60926 云集的平台发货时 skuId 截取第一个“-”符号后字符串
            String psCSkuPtEcode = ocBOrderItem.getPsCSkuPtEcode();
            if (StringUtils.isNotEmpty(psCSkuPtEcode) && psCSkuPtEcode.contains("-")) {

                String first = psCSkuPtEcode.substring(0, psCSkuPtEcode.indexOf("-"));
                return psCSkuPtEcode.substring(first.length() + 1);
            }
            return psCSkuPtEcode;

        } else {
            return formatSkuId(ocBOrderItem.getPsCSkuPtEcode());
        }
    }

    /**
     * 获取sku信息
     *
     * @param ocBOrder
     * @param ocBOrderItem
     * @return
     */
    private String getSku(OcBOrder ocBOrder, OcBOrderItem ocBOrderItem) {
        if (PlatFormEnum.XIAOMI_YOUPIN.getCode().equals(ocBOrder.getPlatform())) {
            // 一米有品的增加扩展字段存储数据信息 ，云枢纽升级字段调整 20221012
            return ocBOrderItem.getReserveVarchar04();
            // 万物心选截取平台条码id
        } else if (PlatFormEnum.ALL_THINGS_CHOICE.getCode().equals(ocBOrder.getPlatform())) {
            String psCSkuPtEcode = ocBOrderItem.getPsCSkuPtEcode();
            if (StringUtils.isNotEmpty(psCSkuPtEcode) && psCSkuPtEcode.contains("-")) {
                String first = psCSkuPtEcode.substring(0, psCSkuPtEcode.indexOf("-"));
                return psCSkuPtEcode.substring(first.length() + 1);
            }
            return psCSkuPtEcode;
        } else {
            return ocBOrderItem.getPsCSkuEcode();
        }
    }

    /**
     * 是否组合商品拆单
     *
     * @param orderItems 订单明细
     * @param ocBOrder   订单
     * @return boolean
     */
    private boolean isCombinationSplit(List<OcBOrderItem> orderItems, OcBOrder ocBOrder) {
        if (!PlatFormEnum.ALL_THINGS_CHOICE.getCode().equals(ocBOrder.getPlatform())) {
            return false;
        }
        return orderItems.stream().anyMatch(o -> StringUtils.isNotBlank(o.getGroupGoodsMark()) &&
                o.getGroupGoodsMark().contains("CG"));
    }


    /**
     * 组合商品拆单处理
     */
    private StandplatLogisticsSendDataModel combinationSplit(List<OcBOrderDelivery> ocBOrderDeliveries, List<OcBOrderItem> orderItems, StandplatLogisticsSendModel sendModel, Map<Long, String> companyCodeMap, OcBOrder order) {
        List<StandplatLogisticsSendModel.StandplatLogisticsSendDetails> sendDetails = new ArrayList<>();
        StandplatLogisticsSendDataModel standplatLogisticsSendDataModel = new StandplatLogisticsSendDataModel();

        List<OcBOrderItem> ocBOrderItemList = orderItems.stream().filter(obj -> obj != null && StringUtils.isNotEmpty(obj.getPsCSkuEcode()) && StringUtils.isNotEmpty(obj.getOoid())).collect(Collectors.toList());
        //组装包裹信息
        if (CollectionUtils.isNotEmpty(ocBOrderDeliveries)) {
            Map<String, List<OcBOrderDelivery>> deliveryNap = ocBOrderDeliveries.stream().collect(Collectors.groupingBy(OcBOrderDelivery::getPsCSkuEcode));
            for (OcBOrderItem orderItem : ocBOrderItemList) {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("combinationSplit.orderItem={}", "combinationSplit.orderItem"), JSON.toJSONString(orderItem));
                }
                StandplatLogisticsSendModel.StandplatLogisticsSendDetails detail = new StandplatLogisticsSendModel.StandplatLogisticsSendDetails();
                sendModel.setTid(orderItem.getTid());
                sendDetails.add(detail);
                List<OcBOrderDelivery> orderDeliveryList = deliveryNap.get(orderItem.getPsCSkuEcode());
                if (CollectionUtils.isEmpty(orderDeliveryList)) {
                    continue;
                }
                OcBOrderDelivery delivery = orderDeliveryList.get(0);
                detail.setItemId(orderItem.getOoid());
                detail.setDeliveryNo(delivery.getLogisticNumber());
                //物流公司查询物流档案中是否存在
                String logisticsCompany = companyCodeMap.get(delivery.getCpCLogisticsId());
                if (StringUtils.isEmpty(logisticsCompany)) {
                    logisticsCompany = cpRpcService.getPlatformLogisticEcode(order.getCpCLogisticsId(), Long.valueOf(order.getPlatform()));
                    companyCodeMap.put(delivery.getCpCLogisticsId(), logisticsCompany);
                    detail.setLogisticsCompany(logisticsCompany);
                } else {
                    detail.setLogisticsCompany(logisticsCompany);
                }

                detail.setSku(getSku(order, orderItem));
                detail.setSkuId(getSkuId(order, orderItem));

                Long sendNum = getSendNum(order, orderItem);
                detail.setNum(sendNum);
                detail.setRealNum(sendNum);
                detail.setLackNum(0L);
                detail.setProductCode(orderItem.getPsCProEcode());
                detail.setOid(orderItem.getOoid());
                // 为组合商品拆单明细
                if (StringUtils.isNotBlank(orderItem.getGroupGoodsMark())) {
                    if (!orderItem.getGroupGoodsMark().contains("CG")) {
                        throw new NDSException("查询原组合商品订单明细组合商品标识为空");
                    }
                    OcBOrderItem item = ocBOrderItemMapper.selectItemByTid(orderItem.getTid(), Long.valueOf(orderItem.getGroupGoodsMark().split("CG")[1]));
                    if (log.isDebugEnabled()) {
                        log.debug(LogUtil.format("combinationSplit.item={}", "combinationSplit.item"), JSON.toJSONString(item));
                    }
                    if (Objects.isNull(item)) {
                        throw new NDSException("查询原组合商品订单明细为空");
                    }
                    detail.setSku(getSku(order, item));
                    detail.setSkuId(getSkuId(order, item));
                    sendNum = getSendNum(order, item);
                    detail.setNum(sendNum);
                    detail.setRealNum(sendNum);
                }
            }
        }
        sendModel.setStandplatLogisticsSendDetails(sendDetails);
        List<StandplatLogisticsSendModel> list = new ArrayList<>();
        list.add(sendModel);
        standplatLogisticsSendDataModel.setLogisticsSendModels(list);
        standplatLogisticsSendDataModel.setPlatform(sendModel.getPlatform());
        standplatLogisticsSendDataModel.setSellerNick(sendModel.getSellerNick());
        return standplatLogisticsSendDataModel;
    }

    /**
     * description: 组合商品和特殊平台发货处理
     *
     * @Author: liuwenjin
     * @Date 2022/9/20 17:38
     */
    private StandplatLogisticsSendDataModel combinationSend(List<OcBOrderDelivery> ocBOrderDeliveries, List<OcBOrderItem> orderItems, StandplatLogisticsSendModel sendModel, Map<Long, String> companyCodeMap, OcBOrder ocBOrder, String itemTid) {

        if (log.isDebugEnabled()) {
            log.debug("组合商品和特殊平台发货处理：ocBOrderDeliveries={}", JSON.toJSONString(ocBOrderDeliveries));

        }

        StandplatLogisticsSendDataModel standplatLogisticsSendDataModel = new StandplatLogisticsSendDataModel();

        List<StandplatLogisticsSendModel.StandplatLogisticsSendDetails> sendDetails = new ArrayList<>();

        if (log.isDebugEnabled()) {
            log.debug("orderItems信息：orderItems={}", JSON.toJSONString(orderItems));
        }

        //组装包裹信息
        if (CollectionUtils.isNotEmpty(ocBOrderDeliveries)) {
            //默认取第一个发货信息
            OcBOrderDelivery delivery = ocBOrderDeliveries.get(0);

            //循环组合商品明细
            for (OcBOrderItem ocBOrderItem : orderItems) {
                StandplatLogisticsSendModel.StandplatLogisticsSendDetails detail = new StandplatLogisticsSendModel.StandplatLogisticsSendDetails();
                detail.setItemId(ocBOrderItem.getOoid());
                detail.setDeliveryNo(delivery.getLogisticNumber());
                //物流公司查询物流档案中是否存在
                String logisticsCompany = companyCodeMap.get(delivery.getCpCLogisticsId());
                if (StringUtils.isEmpty(logisticsCompany)) {
                    logisticsCompany = cpRpcService.getPlatformLogisticEcode(ocBOrder.getCpCLogisticsId(), Long.valueOf(ocBOrder.getPlatform()));
                    companyCodeMap.put(delivery.getCpCLogisticsId(), logisticsCompany);
                    detail.setLogisticsCompany(logisticsCompany);
                } else {
                    detail.setLogisticsCompany(logisticsCompany);
                }
                detail.setSku(getSku(ocBOrder, ocBOrderItem));

                detail.setSkuId(getSkuId(ocBOrder, ocBOrderItem));

                if (PlatFormEnum.MUSHROOM_STREET.getCode().equals(ocBOrder.getPlatform()) || PlatFormEnum.CHUCHU_STREET.getCode().equals(ocBOrder.getPlatform())) {
                    sendModel.setSubTid(ocBOrderItem.getOoid());
                }

                Long sendNum = getSendNum(ocBOrder, ocBOrderItem);
                detail.setNum(sendNum);
                detail.setRealNum(sendNum);
                detail.setLackNum(0L);
                detail.setProductCode(ocBOrderItem.getPsCProEcode());
                //如果没有拆单就是 0 是拆单判断是否已经有平台发货了,有平台发货的就传1 没有就是0
                if (PlatFormEnum.WANGYIYANXUAN.getCode().equals(ocBOrder.getPlatform()) && OcBOrderConst.IS_STATUS_IY.equals(ocBOrder.getIsSplit())) {
                    //判断订单有平台发货的
                    List<OcBOrder> orderList = ocBOrderMapper.queryGsiBySourceCodeByTid(ocBOrder.getSourceCode());
                    if (CollectionUtils.isNotEmpty(orderList)) {
                        detail.setExpressType("1");
                    } else {
                        detail.setExpressType("0");
                    }
                } else {
                    detail.setExpressType("0");
                }
                sendModel.setTid(ocBOrderItem.getTid());
                sendDetails.add(detail);
            }
        }
        sendModel.setStandplatLogisticsSendDetails(sendDetails);
        List<StandplatLogisticsSendModel> list = new ArrayList<>();
        list.add(sendModel);

        standplatLogisticsSendDataModel.setLogisticsSendModels(list);
        standplatLogisticsSendDataModel.setPlatform(sendModel.getPlatform());
        standplatLogisticsSendDataModel.setSellerNick(sendModel.getSellerNick());
        return standplatLogisticsSendDataModel;

    }

    /**
     * description:是否是组合商品且是特殊平台
     *
     * @Author: liuwenjin
     * @Date 2022/9/20 17:34
     */
    private static boolean isCombination(OcBOrder ocBOrder) {
//        return NumberUtils.INTEGER_ONE.equals(ocBOrder.getIsCombination())
//                && (PlatFormEnum.YOUZAN.getCode().equals(ocBOrder.getPlatform())
//                || PlatFormEnum.HAO_YI_KU.getCode().equals(ocBOrder.getPlatform())
//                || PlatFormEnum.WANGYIYANXUAN.getCode().equals(ocBOrder.getPlatform())
//                || PlatFormEnum.CHUCHU_STREET.getCode().equals(ocBOrder.getPlatform())
//                || PlatFormEnum.PLATFORM62.getCode().equals(ocBOrder.getPlatform())
//                || PlatFormEnum.MUSHROOM_STREET.getCode().equals(ocBOrder.getPlatform())
//        );
        // 20220929 组合商品发货调整 暂时先放开所有的平台 浩哥
        return NumberUtils.INTEGER_ONE.equals(ocBOrder.getIsCombination());
    }


    private StandplatLogisticsSendDataModel douYinSend(List<OcBOrderDelivery> ocBOrderDeliveries, List<OcBOrderItem> orderItems, StandplatLogisticsSendModel sendModel, Map<Long, String> companyCodeMap, OcBOrder order, Boolean isAsc) {
        List<StandplatLogisticsSendModel.StandplatLogisticsSendDetails> sendDetails = new ArrayList<>();
        StandplatLogisticsSendDataModel standplatLogisticsSendDataModel = new StandplatLogisticsSendDataModel();

        List<OcBOrderItem> ocBOrderItemList = orderItems.stream().filter(obj -> obj != null && StringUtils.isNotEmpty(obj.getPsCSkuEcode()) && StringUtils.isNotEmpty(obj.getOoid())).collect(Collectors.toList());
        //组装包裹信息
        if (CollectionUtils.isNotEmpty(ocBOrderDeliveries)) {
            Map<String, List<OcBOrderDelivery>> deliveryNap = ocBOrderDeliveries.stream().collect(Collectors.groupingBy(OcBOrderDelivery::getPsCSkuEcode));

            /**
             * 组合商品的发货直接取订单信息，不获取发货信息列表的，
             */
            if (NumberUtils.INTEGER_ONE.equals(order.getIsCombination())) {
                for (int i = 0; i < orderItems.size(); i++) {
                    OcBOrderItem s = orderItems.get(i);
                    StandplatLogisticsSendModel.StandplatLogisticsSendDetails detail = new StandplatLogisticsSendModel.StandplatLogisticsSendDetails();
                    detail.setItemId(s.getOoid());
                    detail.setDeliveryNo(order.getExpresscode());
                    //物流公司查询物流档案中是否存在
                    String logisticsCompany = companyCodeMap.get(order.getCpCLogisticsId());
                    if (StringUtils.isEmpty(logisticsCompany)) {
                        logisticsCompany = cpRpcService.getPlatformLogisticEcode(order.getCpCLogisticsId(), Long.valueOf(order.getPlatform()));
                        companyCodeMap.put(order.getCpCLogisticsId(), logisticsCompany);
                        detail.setLogisticsCompany(logisticsCompany);
                    } else {
                        detail.setLogisticsCompany(logisticsCompany);
                    }
                    //不取条码ID，取平台条码编码
                    detail.setSkuId(s.getPsCSkuPtEcode());
                    detail.setSku(s.getPsCSkuEcode());
                    detail.setProductCode(s.getPsCProEcode());

                    Long sendNum = getSendNum(order, s);
                    detail.setNum(sendNum);
                    detail.setRealNum(sendNum);
                    detail.setLackNum(0L);
                    sendModel.setTid(s.getTid());
                    sendDetails.add(detail);
                }
            } else {
                List<OcBOrderEqualExchangeItem> ocBOrderEqualExchangeItemList = null;
                if (OcBOrderConst.IS_STATUS_IY.equals(order.getIsEqualExchange())) {
                    ocBOrderEqualExchangeItemList = ocBOrderEqualExchangeItemMapper.selectOcBOrderEqualExchangeItemListByOrderId(order.getId());
                    if (log.isDebugEnabled()) {
                        log.debug(LogUtil.format("douYinSend.selectOcBOrderEqualExchangeItemListByOrderId={}",
                                "douYinSend.selectOcBOrderEqualExchangeItemListByOrderId"), JSON.toJSONString(ocBOrderEqualExchangeItemList));
                    }
                }
                for (OcBOrderItem orderItem : ocBOrderItemList) {
                    if (log.isDebugEnabled()) {
                        log.debug(LogUtil.format("douYinSend.orderItem={}", "douYinSend.orderItem"), JSON.toJSONString(orderItem));
                    }
                    List<StandplatLogisticsSendModel.Bundle> bundleList = Lists.newArrayList();
                    StandplatLogisticsSendModel.StandplatLogisticsSendDetails detail = new StandplatLogisticsSendModel.StandplatLogisticsSendDetails();
                    sendModel.setTid(orderItem.getTid());
                    sendDetails.add(detail);
                    List<OcBOrderDelivery> orderDeliveryList = deliveryNap.get(orderItem.getPsCSkuEcode());
                    if (CollectionUtils.isEmpty(orderDeliveryList)) {
                        continue;
                    }
                    OcBOrderDelivery delivery = orderDeliveryList.get(0);
                    detail.setItemId(orderItem.getOoid());
                    detail.setDeliveryNo(delivery.getLogisticNumber());
                    //物流公司查询物流档案中是否存在
                    String logisticsCompany = companyCodeMap.get(delivery.getCpCLogisticsId());
                    if (StringUtils.isEmpty(logisticsCompany)) {
                        logisticsCompany = cpRpcService.getPlatformLogisticEcode(order.getCpCLogisticsId(), Long.valueOf(order.getPlatform()));
                        companyCodeMap.put(delivery.getCpCLogisticsId(), logisticsCompany);
                        detail.setLogisticsCompany(logisticsCompany);
                    } else {
                        detail.setLogisticsCompany(logisticsCompany);
                    }

                    detail.setSku(getSku(order, orderItem));
                    detail.setSkuId(getSkuId(order, orderItem));

                    Long sendNum = getSendNum(order, orderItem);
                    detail.setNum(sendNum);
                    detail.setRealNum(sendNum);
                    detail.setLackNum(0L);
                    detail.setProductCode(orderItem.getPsCProEcode());
                    detail.setOid(orderItem.getOoid());
                    // 为组合商品拆单明细
                    if (StringUtils.isNotBlank(orderItem.getGroupGoodsMark())) {
                        if (!orderItem.getGroupGoodsMark().contains("CG")) {
                            throw new NDSException("查询原组合商品订单明细组合商品标识为空");
                        }
                        OcBOrderItem item = ocBOrderItemMapper.selectItemByTid(orderItem.getTid(), Long.valueOf(orderItem.getGroupGoodsMark().split("CG")[1]));
                        if (log.isDebugEnabled()) {
                            log.debug(LogUtil.format("douYinSend.item.item={}", "douYinSend.item.item"), JSON.toJSONString(item));
                        }
                        if (Objects.isNull(item) || StringUtils.isBlank(item.getNumIid())) {
                            throw new NDSException("查询原组合商品订单明细平台商品ID为空");
                        }
                        detail.setSku(getSku(order, item));
                        detail.setSkuId(getSkuId(order, item));
                        sendNum = getSendNum(order, item);
                        detail.setNum(sendNum);
                        detail.setRealNum(sendNum);

                        SgChannelProductQueryRequest sgChannelProductQueryRequest = new SgChannelProductQueryRequest();
                        sgChannelProductQueryRequest.setNumiidList(Collections.singletonList(item.getNumIid()));
                        // 根据平台商品id获取平台店铺商品表信息
                        // 后续没有维护组合商品条码 直接continue 整单发货，
                        ValueHolderV14<List<SgBChannelProduct>> v14 = sgRpcService.queryChannelProduct(sgChannelProductQueryRequest);
                        if (!v14.isOK() || CollectionUtils.isEmpty(v14.getData())) {
                            continue;
                        }

                        //按照时间排序，取最近一条
                        List<SgBChannelProduct> products = v14.getData();
                        List<SgBChannelProduct> sortProducts = products.stream().sorted(Comparator.comparing(SgBChannelProduct::getCreationdate).reversed()).collect(Collectors.toList());

                        SgBChannelProduct sgBChannelProduct = sortProducts.get(0);
                        String combinationSkuIds = sgBChannelProduct.getCombinationSkuIds();
                        if (StringUtils.isBlank(combinationSkuIds)) {
                            continue;
                        }
                        String[] splitCombinationSkuIds = combinationSkuIds.replaceAll("\\[", "").replaceAll("\\]", "").split(",");
                        if (ArrayUtils.isEmpty(splitCombinationSkuIds)) {
                            continue;
                        }
                        SgChannelProductQueryRequest queryRequest = new SgChannelProductQueryRequest();
                        List<String> list = Arrays.asList(splitCombinationSkuIds);
                        queryRequest.setNumiidList(list);
                        // 根据匹配关系的平台商品id获取平台店铺商品表信息
                        v14 = sgRpcService.queryChannelProduct(queryRequest);
                        if (!v14.isOK() || CollectionUtils.isEmpty(v14.getData())) {
                            continue;
                        }
                        List<SgBChannelProduct> sgBChannelProductList = v14.getData();
                        Map<String, SgBChannelProduct> sgBChannelProductMap = new HashMap<>();
                        if (isAsc) {
                            sgBChannelProductMap = sgBChannelProductList.stream().sorted(Comparator.comparing(SgBChannelProduct::getCreationdate).thenComparing(SgBChannelProduct::getId))
                                    .collect(Collectors.toMap(SgBChannelProduct::getPsCSkuEcode, Function.identity(), (k1, k2) -> k1));
                        } else {
                            sgBChannelProductMap = sgBChannelProductList.stream().sorted(Comparator.comparing(SgBChannelProduct::getCreationdate).thenComparing(SgBChannelProduct::getId).reversed())
                                    .collect(Collectors.toMap(SgBChannelProduct::getPsCSkuEcode, Function.identity(), (k1, k2) -> k1));
                        }

                        String skuEcode = StringUtils.isNotBlank(orderItem.getOriginSkuEcode()) ? orderItem.getOriginSkuEcode() : orderItem.getPsCSkuEcode();
                        // 对等换货
                        if (OcBOrderConst.IS_STATUS_IY.equals(order.getIsEqualExchange())
                                && OcBOrderConst.IS_STATUS_IY.equals(orderItem.getIsEqualExchange())) {
                            if (CollectionUtils.isEmpty(ocBOrderEqualExchangeItemList)) {
                                continue;
                            }
                            Map<String, OcBOrderEqualExchangeItem> ooidMap = ocBOrderEqualExchangeItemList.stream()
                                    .filter(obj -> StringUtils.isNotEmpty(obj.getOoid()))
                                    .collect(Collectors.toMap(OcBOrderEqualExchangeItem::getOoid, a -> a, (k1, k2) -> k2));

                            OcBOrderEqualExchangeItem equalExchangeItem = ooidMap.get(item.getOoid());
                            if (Objects.isNull(equalExchangeItem)) {
                                continue;
                            }
                            skuEcode = equalExchangeItem.getPsCSkuEcode();
                        }
                        if (!sgBChannelProductMap.containsKey(skuEcode)) {
                            continue;
                        }
                        StandplatLogisticsSendModel.Bundle bundle = new StandplatLogisticsSendModel.Bundle();
                        bundle.setSubSkuId(formatSkuId(sgBChannelProductMap.get(skuEcode).getSkuId()));
                        bundle.setSubProductId(sgBChannelProductMap.get(skuEcode).getNumiid());
                        // 获取数量
                        sendNum = getSendNum(order, orderItem);
                        bundle.setComboNum(sendNum);
                        bundleList.add(bundle);
                        detail.setBundleList(bundleList);
                    }
                }
            }
        }
        sendModel.setStandplatLogisticsSendDetails(sendDetails);
        List<StandplatLogisticsSendModel> list = new ArrayList<>();
        list.add(sendModel);
        standplatLogisticsSendDataModel.setLogisticsSendModels(list);
        standplatLogisticsSendDataModel.setPlatform(sendModel.getPlatform());
        standplatLogisticsSendDataModel.setSellerNick(sendModel.getSellerNick());
        return standplatLogisticsSendDataModel;
    }


    /**
     * description:平台发货skuid去掉"-"
     *
     * @Author: liuwenjin
     * @Date 2021/12/29 8:57 下午
     */
    private String formatSkuId(String psCSkuPtEcode) {
        if (StringUtils.isNotEmpty(psCSkuPtEcode) && psCSkuPtEcode.contains("-")) {
            String[] strings = psCSkuPtEcode.split("-");
            return strings[1];
        }
        return psCSkuPtEcode;
    }

    /**
     * 唯品会MP条码处理
     *
     * @param psCSkuPtEcode
     * @return
     */
    private String formatVipMpSkuId(String psCSkuPtEcode) {
        if (StringUtils.isNotEmpty(psCSkuPtEcode) && psCSkuPtEcode.contains("SKU")) {
            return psCSkuPtEcode.substring(psCSkuPtEcode.indexOf("SKU"));
        }
        return psCSkuPtEcode;
    }

    /**
     * description:解析未对等换货之前的数量    例如 原来的 8  新的 4 比例 2：1  计算 4*2 / 1 = 8
     *
     * @Author: liuwenjin
     * @Date 2022/9/27 12:58
     */
    private Long paresEqualExchangeCount(String ratioStr, BigDecimal qty) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format(" paresEqualExchange,ratioStr{},qty{}"), ratioStr, qty);
        }
        String[] ratio = ratioStr.split(":");
        if (ratio.length > 0) {
            List<String> list = Arrays.asList(ratio);
            //分母
            Long num1 = Long.valueOf(list.get(0));
            //分子
            Long num2 = Long.valueOf(list.get(1));
            //获取原数量 分母 * 数量 / 分子
            return num1 * qty.intValue() / num2;
        }
        return qty.longValue();
    }

    /**
     * 通用平台发货设置活动ID：爱库存
     *
     * @param sendModels
     * @param tid
     * @param platform
     */
    private void setActivityId4StandplatLogisticsSendModel(List<StandplatLogisticsSendModel> sendModels, String tid, Integer platform) {
        if (Objects.nonNull(tid)) {
            if (PlatFormEnum.AI_KU_CUN.getCode().equals(platform)) {
                // 针对爱库存：平台发货加活动ID字段
                String activityId = IpStandplatOrderService.getInstance().selectActivityidByTid(tid);
                if (Objects.nonNull(activityId)) {
                    if (CollectionUtils.isNotEmpty(sendModels)) {
                        sendModels.forEach(m -> {
                            if (Objects.nonNull(m)) {
                                m.setActivityid(activityId);
                            }
                        });
                    }
                }
            }
        }
    }

    /**
     * 抖音发货重试
     *
     * @param orderId 订单id
     */
    public void douyinRetry(Long orderId) {
        OcBOrderRelation ocBOrderRelation = omsOrderService.selectOmsOrderInfo(orderId);
        List<StandplatLogisticsSendDataModel> standplatLogisticsSendDataModels = this.addCommonIpParamDouyin(ocBOrderRelation, null, null, null);
        try {
            for (StandplatLogisticsSendDataModel model : standplatLogisticsSendDataModels) {
                ValueHolderV14<List<StandplatLogisticsSendResult>> vh = ipRpcService.sendStandPlatLogistics(model);
                if (vh == null || vh.getCode() == ResultCode.FAIL) {
                    ApplicationContextHandle.getBean(OmsOrderLogService.class).addUserOrderLog(ocBOrderRelation.getOrderId(), ocBOrderRelation.getOrderInfo().getBillNo(), OrderLogTypeEnum.PLATFORM_SEND.getKey(), "平台发货失败," + (vh == null ? "" : vh.getMessage()), null, null, SystemUserResource.getRootUser());
                }
            }
        } catch (Exception e) {
            log.error("调用通用发货rpc异常", e);
            OcBOrder ocBOrderflag = new OcBOrder();
            ocBOrderflag.setId(ocBOrderRelation.getOrderInfo().getId());
            ocBOrderflag.setIsForce(0L);
            ocBOrderflag.setForceSendFailReason(e.getMessage());
            omsOrderService.updateOrderInfo(ocBOrderflag);
        }
    }

    /**
     * 抖音发货重试
     */
    private List<StandplatLogisticsSendDataModel> addCommonIpParamDouyin(OcBOrderRelation ocBOrderRelation, String subTid, Integer isSplit, String expressCode) {
        OcBOrder orderInfo = ocBOrderRelation.getOrderInfo();
        List<StandplatLogisticsSendDataModel> result = new ArrayList<>();
        List<OcBOrderItem> ocBOrderItems = null;
        if (StringUtils.isEmpty(expressCode)) {
            ocBOrderItems = omsOrderItemServie.selectUnSuccessRefund(orderInfo.getId());
        } else {
            ocBOrderItems = ocBOrderRelation.getOrderItemList();
        }
        if (CollectionUtils.isNotEmpty(ocBOrderItems)) {
            Map<String, List<OcBOrderItem>> lists = ocBOrderItems.stream().collect(Collectors.groupingBy(OcBOrderItem::getTid));
            for (Map.Entry<String, List<OcBOrderItem>> map : lists.entrySet()) {
                List<OcBOrderItem> orderItems = map.getValue();
                if (CollectionUtils.isNotEmpty(orderItems)) {
                    //如果存在福袋、组合商品将真实商品明细转换为平台的虚拟明细
                    Long id = orderInfo.getId();
                    orderItems = transformCommonIpItemParam(orderItems, id, orderInfo.getPlatform());
                    StandplatLogisticsSendDataModel model = getStandplatLogisticsSendDataModelDouyin(ocBOrderRelation, subTid, isSplit, expressCode, orderItems);
                    if (model != null) {
                        result.add(model);
                    }
                }
            }
        }
        return result;
    }

    /**
     * 抖音发货重试
     */
    private StandplatLogisticsSendDataModel getStandplatLogisticsSendDataModelDouyin(OcBOrderRelation ocBOrderRelation, String subTid, Integer isSplit, String expressCode, List<OcBOrderItem> orderItems) {
        OcBOrder ocBOrder = ocBOrderRelation.getOrderInfo();
        BigDecimal skuNum = BigDecimal.ZERO;
        StandplatLogisticsSendModel sendModel = new StandplatLogisticsSendModel();
        String company_code = cpRpcService.getPlatformLogisticEcode(ocBOrder.getCpCLogisticsId(), Long.valueOf(ocBOrder.getPlatform()));
        Map<Long, String> companyCodeMap = new HashMap<>();
        companyCodeMap.put(ocBOrder.getCpCLogisticsId(), company_code);
        //mq回执返回过来用来订单ID
        sendModel.setErpOrderId(ocBOrder.getId());
        if (null != isSplit) {
            sendModel.setIsSplit(isSplit.longValue());
        } else {
            sendModel.setIsSplit(ocBOrder.getIsSplit() == null ? null : ocBOrder.getIsSplit().longValue());
        }
        //不拆单 传null
        sendModel.setSubTid(subTid);
        if (StringUtils.isEmpty(expressCode)) {
            sendModel.setOutSid(ocBOrder.getExpresscode());
        } else {
            sendModel.setOutSid(expressCode);
        }
        sendModel.setOutSid(ocBOrder.getExpresscode());
        //平台物流公司编码
        sendModel.setCompanyCode(company_code);
        sendModel.setPayType(ocBOrder.getPayType() == null ? "" : ocBOrder.getPayType().toString());
        sendModel.setPlatform(ocBOrder.getPlatform() == null ? null : ocBOrder.getPlatform().longValue());
        sendModel.setSellerNick(ocBOrder.getCpCShopSellerNick());
        //换货单号 暂时没有
        sendModel.setDisputeId(null);
        sendModel.setOrderType(ocBOrder.getOrderType() == null ? "" : ocBOrder.getOrderType().toString());
        sendModel.setLogisticsCompanyName(ocBOrder.getCpCLogisticsEname());
        //订单来源 先传空字符串，后面设计为可配置
        sendModel.setRetailSource("");
        //sku数量
        sendModel.setSize(String.valueOf(skuNum.intValue()));
        //运单包裹状态(1发货完成，2部分发货， 取固定值)
        sendModel.setStatus("1");
        //物流电话
        sendModel.setLogisticsTel(LogisticsTelEnum.enumToTel(sendModel.getCompanyCode()));
        List<OcBOrderDelivery> ocBOrderDeliveries = ocBOrderDeliveryMapper.selectOrderDeliveryByOrderId(ocBOrder.getId());
        StandplatLogisticsSendDataModel sendDataModel = douYinSend(ocBOrderDeliveries, orderItems, sendModel, companyCodeMap, ocBOrder, true);
        return sendDataModel;
    }

    /**
     * 判断平台单下是否首次发货通知平台
     * <p>
     * 首单规则：本单零售发货单为：仓库发货状态，且同平台单号下无其余零售发货单为“平台发货”状态，则视为首单
     *
     * @param ocBOrder
     * @return true:首次；false:不是首次
     */
    private boolean deliveryIsFirst(OcBOrder ocBOrder) {
        return OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger().equals(ocBOrder.getOrderStatus()) &&
                CollectionUtils.isEmpty(ocBOrderMapper.selectOcBOrderByTid(ocBOrder.getTid()).stream().filter(p ->
                        OmsOrderStatus.PLATFORM_DELIVERY.toInteger().equals(p.getOrderStatus())).collect(Collectors.toList()));
    }

    /**
     * 多包裹发货
     *
     * @param models
     * @param ocBOrderRelation
     * @return
     */
    public boolean manySend(List<StandplatLogisticsSendDataModel> models, OcBOrderRelation ocBOrderRelation, Set<String> ooids) {
        boolean result = true;

        Integer platform = ocBOrderRelation.getOrderInfo().getPlatform();
        //万物心选发货
        if (PlatFormEnum.ALL_THINGS_CHOICE.getCode().equals(platform)) {
            return allThingsChoiceDelivery(ocBOrderRelation);
        }

        //拼多多发货
        if (PlatFormEnum.PINDUODUO.getCode().equals(platform)) {
            return pddDelivery(ocBOrderRelation);
        }

        //抖音子单号添加包裹发货
        if (PlatFormEnum.DOU_YIN.getCode().equals(platform)) {
            return dyDelivery(ocBOrderRelation, ooids);
        }

        Long orderId = ocBOrderRelation.getOrderId();
        OcBOrder orderInfo = ocBOrderRelation.getOrderInfo();
        String billNo = orderInfo.getBillNo();
        String tid = orderInfo.getTid();
        try {
            for (StandplatLogisticsSendDataModel model : models) {
                OrderDeliveryReq orderDeliveryReq = new OrderDeliveryReq();
                orderDeliveryReq.setPtId(model.getPlatform());
                orderDeliveryReq.setShopId(orderInfo.getCpCShopId());
                orderDeliveryReq.setOrderCode(tid);
                orderDeliveryReq.setBillNo(orderInfo.getBillNo());
                orderDeliveryReq.setIsResetShip(orderInfo.getIsResetShip());

                if (PlatFormEnum.HAO_YI_KU.getCode().equals(orderInfo.getPlatform()) && YesNoEnum.Y.getVal().equals(orderInfo.getIsResetShip())) {
                    //查询原单信息
                    List<Long> list = sourceRelationMapper.querySourceOrderId(orderInfo.getId(), OcBOrderSourceRelationTypeEnum.REISSUE.getKey());
                    if (CollectionUtils.isNotEmpty(list)) {
                        Long sourceId = list.get(0);
                        OcBOrder order = ocBOrderMapper.selectByID(sourceId);
                        if (!Objects.isNull(order)) {
                            CpLogisticsItem platformLogistic = cpRpcService.getPlatformLogistic(order.getCpCLogisticsId(), Long.valueOf(order.getPlatform()));
                            if (Objects.nonNull(platformLogistic) && StringUtils.isNotBlank(platformLogistic.getCpCLogisticsEname())) {
                                orderDeliveryReq.setSourcePtExpressCompany(platformLogistic.getCpCLogisticsEname());
                            }
                            orderDeliveryReq.setSourceLogisticsCode(order.getExpresscode());
                        }
                    }
                }

                for (StandplatLogisticsSendModel logisticsSendModel : model.getLogisticsSendModels()) {
                    orderDeliveryReq.setLogisticsCode(logisticsSendModel.getOutSid());
                    orderDeliveryReq.setPtExpressCode(logisticsSendModel.getCompanyCode());
                    orderDeliveryReq.setPtExpressCompany(logisticsSendModel.getLogisticsCompanyName());
                    ValueHolderV14 v14 = hubRpcService.manyDelivery(orderDeliveryReq);
                    if (v14.getCode() == ResultCode.FAIL) {
                        //更新发货状态，插入日志
                        String logMsg = "OrderId=" + orderId + ",平台单号=" + tid + "发货通知平台失败," + v14.getMessage();
                        orderLogService.addUserOrderLog(orderId, billNo, OrderLogTypeEnum.PLATFORM_SEND.getKey(), logMsg, "", null, null);
                        result = false;
                    } else {
                        //更新发货状态，插入日志
                        String logMsg = "OrderId=" + orderId + ",平台单号=" + tid + "发货通知平台成功";
                        orderLogService.addUserOrderLog(orderId, billNo, OrderLogTypeEnum.PLATFORM_SEND.getKey(), logMsg, "", null, null);
                        ocBOrderItemMapper.updateItemsWhenDeliverySuccess(orderId, (String) v14.getData());

                        OcBOrder update = new OcBOrder();
                        update.setOrderStatus(OmsOrderStatus.PLATFORM_DELIVERY.toInteger());
                        update.setId(orderId);
                        ocBOrderMapper.updateById(update);
                    }
                }
            }
        } catch (Exception e) {
            String logMsg = "OrderId=" + orderId + ",平台单号=" + tid + "发货通知平台失败,";
            orderLogService.addUserOrderLog(orderId, billNo, OrderLogTypeEnum.PLATFORM_SEND.getKey(), logMsg, "", null, null);
            log.error(LogUtil.format("errorInfo:{}", "manySend.error"), Throwables.getStackTraceAsString(e));
            result = false;
        }
        return result;
    }

    /**
     * 万物心选发货组装
     *
     * @param ocBOrderRelation
     * @return
     */
    private boolean allThingsChoiceDelivery(OcBOrderRelation ocBOrderRelation) {
        boolean result = true;
        OcBOrder ocBOrder = ocBOrderRelation.getOrderInfo();
        Long orderId = ocBOrder.getId();
        String tid = ocBOrder.getTid();
        String billNo = ocBOrder.getBillNo();

        //查找所有仓库发货+平台发货的零售发货单
        Map<Long, List<StandplatLogisticsSendDataModel>> sendModelMap = allThingsChoiceDeliveryParam(ocBOrder.getTid());
        if (MapUtils.isEmpty(sendModelMap)) {
            return false;
        }

        //发货参数
        OrderDeliveryReq orderDeliveryReq = new OrderDeliveryReq();
        orderDeliveryReq.setPtId(Long.valueOf(ocBOrder.getPlatform()));
        orderDeliveryReq.setOrderCode(ocBOrder.getTid());
        orderDeliveryReq.setBillNo(ocBOrder.getBillNo());
        orderDeliveryReq.setShopId(ocBOrder.getCpCShopId());

        List<OrderDeliveryReq.DataDto> detail = Lists.newArrayList();
        for (Map.Entry<Long, List<StandplatLogisticsSendDataModel>> entry : sendModelMap.entrySet()) {
            List<StandplatLogisticsSendDataModel> value = entry.getValue();
            for (StandplatLogisticsSendDataModel model : value) {
                for (StandplatLogisticsSendModel logisticsSendModel : model.getLogisticsSendModels()) {
                    OrderDeliveryReq.DataDto dataDto = new OrderDeliveryReq.DataDto();
                    dataDto.setPtExpressCode(logisticsSendModel.getCompanyCode());
                    dataDto.setLogisticsCode(logisticsSendModel.getOutSid());

                    String subTid = logisticsSendModel.getSubTid();
                    if (StringUtils.isNotBlank(subTid)) {
                        dataDto.setSubOrderNos(Arrays.asList(subTid.split(",")));
                    } else {
                        dataDto.setSubOrderNos(Lists.newArrayList());
                    }

                    detail.add(dataDto);
                }
            }
        }
        orderDeliveryReq.setDetail(detail);

        try {
            ValueHolderV14 v14 = hubRpcService.manyDelivery(orderDeliveryReq);
            if (v14.getCode() == ResultCode.FAIL) {
                //更新发货状态，插入日志
                String logMsg = "OrderId=" + orderId + ",平台单号=" + tid + "发货通知平台失败," + v14.getMessage();
                orderLogService.addUserOrderLog(orderId, billNo, OrderLogTypeEnum.PLATFORM_SEND.getKey(), logMsg, "", null, null);
                result = false;
            } else {
                //更新发货状态，插入日志
                String logMsg = "OrderId=" + orderId + ",平台单号=" + tid + "发货通知平台成功";
                orderLogService.addUserOrderLog(orderId, billNo, OrderLogTypeEnum.PLATFORM_SEND.getKey(), logMsg, "", null, null);
                ocBOrderItemMapper.updateItemsWhenDeliverySuccess(orderId, (String) v14.getData());

                OcBOrder update = new OcBOrder();
                update.setOrderStatus(OmsOrderStatus.PLATFORM_DELIVERY.toInteger());
                update.setId(orderId);
                ocBOrderMapper.updateById(update);
            }
        } catch (Exception e) {
            String logMsg = "OrderId=" + orderId + ",平台单号=" + tid + "发货通知平台失败,";
            orderLogService.addUserOrderLog(orderId, billNo, OrderLogTypeEnum.PLATFORM_SEND.getKey(), logMsg, "", null, null);
            log.error(LogUtil.format("errorInfo:{}", "manySend.error"), Throwables.getStackTraceAsString(e));
            result = false;
        }
        return result;
    }

    /**
     * 拼多多发货
     *
     * @param ocBOrderRelation
     * @return
     */
    private boolean pddDelivery(OcBOrderRelation ocBOrderRelation) {
        log.info("pddDelivery.start ocBOrderRelation:{}", JSON.toJSONString(ocBOrderRelation));

        OcBOrder ocBOrder = ocBOrderRelation.getOrderInfo();
        Long orderId = ocBOrder.getId();
        String tid = ocBOrder.getTid();
        String billNo = ocBOrder.getBillNo();

        //发货延迟处理
        if (delayDeliveryCheck(orderId, tid, billNo)) {
            return false;
        }

        //可以去发货了,最多10个（平台限制）
        List<OcBOrder> ocBOrders = ocBOrderMapper.selectOcBOrderByTid(tid);
        Map<Long, OcBOrder> orderMap = ocBOrders.stream().collect(Collectors.toMap(OcBOrder::getId, x -> x, (a, b) -> a));
        boolean result = true;

        OrderDeliveryParcelbackModel deliveryParcelbackModel = new OrderDeliveryParcelbackModel();
        deliveryParcelbackModel.setPlatform(ocBOrder.getPlatform() == null ? "" : String.valueOf(ocBOrder.getPlatform()));
        deliveryParcelbackModel.setSellerNick(ocBOrder.getCpCShopSellerNick());
        deliveryParcelbackModel.setTid(tid);
        //0:线上 1:线下
        deliveryParcelbackModel.setType("0");

        Set<Long> deliveryList = Sets.newHashSet();
        List<OrderDeliveryParcelbackModel.StandPlatParcelBackItemDTO> logisticsList = Lists.newArrayList();
        Map<Long, List<StandplatLogisticsSendDataModel>> sendModelMap = this.pddDeliveryParam(tid, ocBOrders);
        for (Map.Entry<Long, List<StandplatLogisticsSendDataModel>> entry : sendModelMap.entrySet()) {
            List<StandplatLogisticsSendDataModel> value = entry.getValue();
            for (StandplatLogisticsSendDataModel model : value) {
                for (StandplatLogisticsSendModel logisticsSendModel : model.getLogisticsSendModels()) {
                    if (logisticsList.size() >= pddDeliveryPackageNum) {
                        break;
                    }
                    OrderDeliveryParcelbackModel.StandPlatParcelBackItemDTO dataDto = new OrderDeliveryParcelbackModel.StandPlatParcelBackItemDTO();
                    dataDto.setLogisticsCompanyCode(logisticsSendModel.getCompanyCode());
                    dataDto.setLogisticsNo(logisticsSendModel.getOutSid());
                    dataDto.setOid(tid);
                    logisticsList.add(dataDto);
                    deliveryList.add(logisticsSendModel.getErpOrderId());
                }
            }
        }
        deliveryParcelbackModel.setLogisticsList(logisticsList);

        try {
            ValueHolderV14 v14 = ipRpcService.deliveryParcel(deliveryParcelbackModel);
            if (v14.getCode() == ResultCode.FAIL) {
                //更新发货状态，插入日志
                String logMsg = "OrderId=" + orderId + ",平台单号=" + tid + "发货通知平台失败," + v14.getMessage();
                orderLogService.addUserOrderLog(orderId, billNo, OrderLogTypeEnum.PLATFORM_SEND.getKey(), logMsg, "", null, null);
                result = false;
            } else {
                //所有订单更新为平台发货成功
                for (Long id : deliveryList) {
                    OcBOrder order = orderMap.get(id);

                    //更新发货状态，插入日志
                    String logMsg = "OrderId=" + order.getId() + ",平台单号=" + tid + "发货通知平台成功";
                    orderLogService.addUserOrderLog(order.getId(), order.getBillNo(), OrderLogTypeEnum.PLATFORM_SEND.getKey(), logMsg, "", null, null);
                    ocBOrderItemMapper.updateItemsWhenDeliverySuccess(order.getId(), (String) v14.getData());

                    OcBOrder update = new OcBOrder();
                    update.setOrderStatus(OmsOrderStatus.PLATFORM_DELIVERY.toInteger());
                    update.setId(order.getId());
                    ocBOrderMapper.updateById(update);
                }
            }
        } catch (Exception e) {
            String logMsg = "OrderId=" + orderId + ",平台单号=" + tid + "发货通知平台失败,";
            orderLogService.addUserOrderLog(orderId, billNo, OrderLogTypeEnum.PLATFORM_SEND.getKey(), logMsg, "", null, null);
            log.error(LogUtil.format("errorInfo:{}", "manySend.error"), Throwables.getStackTraceAsString(e));
            result = false;
        }
        return result;
    }

    /**
     * 拼多多延迟发货
     *
     * @param orderId
     * @param tid
     * @param billNo
     * @return
     */
    private boolean delayDeliveryCheck(Long orderId, String tid, String billNo) {
        String redisKey = BllRedisKeyResources.getPddDeliveryDelayMark(tid);
        CusRedisTemplate<String, String> objRedisTemplate = RedisMasterUtils.getObjRedisTemplate();
        String twoDeliveryTime = objRedisTemplate.opsForValue().get(redisKey);
        //非首单第一次发货，记录当前时间戳，等待下次发货
        if (StringUtils.isBlank(twoDeliveryTime)) {
            long timeMillis = System.currentTimeMillis();
            objRedisTemplate.opsForValue().set(redisKey, String.valueOf(timeMillis), 90, TimeUnit.DAYS);

            //更新发货状态，插入日志
            String logMsg = "OrderId=" + orderId + ",平台单号=" + tid + "发货失败（延迟发货）";
            orderLogService.addUserOrderLog(orderId, billNo, OrderLogTypeEnum.PLATFORM_SEND.getKey(), logMsg, "", null, null);
            return true;
        }

        //延迟发货有记录，判断是否在延迟时间范围内
        long delayTime = Long.parseLong(twoDeliveryTime);
        long timeMillis = System.currentTimeMillis();
        long delay = timeMillis - delayTime;
        if (delay <= pddDeliveryDelay) {
            //更新发货状态，插入日志
            String logMsg = "OrderId=" + orderId + ",平台单号=" + tid + "发货失败（延迟发货）";
            orderLogService.addUserOrderLog(orderId, billNo, OrderLogTypeEnum.PLATFORM_SEND.getKey(), logMsg, "", null, null);
            return true;
        }
        return false;
    }

    private Map<Long, List<StandplatLogisticsSendDataModel>> pddDeliveryParam(String tid, List<OcBOrder> ocBOrders) {
        Map<Long, List<StandplatLogisticsSendDataModel>> sendModelMap = Maps.newHashMap();
        for (OcBOrder bOrder : ocBOrders) {
            //复制单过滤
            if (YesNoEnum.Y.getVal().equals(bOrder.getIsCopyOrder())) {
                continue;
            }
            //补发单过滤
            if (YesNoEnum.Y.getVal().equals(bOrder.getIsResetShip())) {
                continue;
            }
            if (!OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger().equals(bOrder.getOrderStatus())) {
                continue;
            }

            List<OcBOrderItem> ocBOrderItems = ocBOrderItemMapper.selectOrderItemList(bOrder.getId());
            OcBOrderRelation relation = new OcBOrderRelation();
            relation.setOrderInfo(bOrder);
            relation.setOrderItemList(ocBOrderItems);

            List<String> ooids = Lists.newArrayList();
            List<OcBOrderItem> ooidItems = ocBOrderItems.stream().filter(p -> p.getOoid() != null).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(ooidItems)) {
                Set<String> ooidSets = ooidItems.stream().map(OcBOrderItem::getOoid).collect(Collectors.toSet());
                ooids.addAll(ooidSets);
            }

            sendModelMap.put(bOrder.getId(), this.addCommonIpParam(relation, StringUtils.join(ooids, ","), null, null));
        }
        return sendModelMap;
    }

    /**
     * 唯品会mp 判断是否尾单
     * <p>
     * 当平台单号下的所有零售发货单的OM单（排除手工单、复制单和补发单）号都为”平台发货&已作废&已取消“，且本单零售发货单的状态为”仓库发货“。
     *
     * @param order
     * @param ocBOrders
     * @return true:是尾单;false:不是尾单
     */
    private boolean isTailOrderSingle(OcBOrder order, List<OcBOrder> ocBOrders) {
        //本单状态为仓库发货
        Integer orderStatus = order.getOrderStatus();
        if (!OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger().equals(orderStatus)) {
            return false;
        }

        //除本单外的所有其他订单
        List<OcBOrder> ocBOrderList = ocBOrders.stream().filter(p -> !p.getId().equals(order.getId())).collect(Collectors.toList());
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(ocBOrderList)) {
            //只有一单仓库发货
            return true;
        }

        //排除手工单、复制单和补发单
        List<OcBOrder> normalOrders = ocBOrderList.stream().filter(p ->
                !YesNoEnum.Y.getVal().equals(p.getIsResetShip())
                        && !YesNoEnum.Y.getVal().equals(p.getIsCopyOrder())
                        && !ObjectUtil.equal("手工新增", p.getOrderSource())
        ).collect(Collectors.toList());
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(normalOrders)) {
            return true;
        }

        //都为”平台发货&已作废&已取消“
        boolean isEnd = true;
        for (OcBOrder normalOrder : normalOrders) {
            Integer status = normalOrder.getOrderStatus();
            if (!OmsOrderStatus.CANCELLED.toInteger().equals(status)
                    && !OmsOrderStatus.SYS_VOID.toInteger().equals(status)
                    && !OmsOrderStatus.PLATFORM_DELIVERY.toInteger().equals(status)) {
                isEnd = false;
                break;
            }
        }

        return isEnd;
    }

    /**
     * 抖音发货添加多包裹
     *
     * @param ocBOrderRelation
     * @return
     */
    private boolean dyDelivery(OcBOrderRelation ocBOrderRelation, Set<String> ooids) {
        log.info("dyDelivery.start ocBOrderRelation:{}", JSON.toJSONString(ocBOrderRelation));
        boolean result = true;
        OcBOrder ocBOrder = ocBOrderRelation.getOrderInfo();
        Long orderId = ocBOrder.getId();
        String tid = ocBOrder.getTid();

        OrderDeliveryParcelbackModel deliveryParcelbackModel = new OrderDeliveryParcelbackModel();
        deliveryParcelbackModel.setPlatform(ocBOrder.getPlatform() == null ? "" : String.valueOf(ocBOrder.getPlatform()));
        deliveryParcelbackModel.setSellerNick(ocBOrder.getCpCShopSellerNick());
        deliveryParcelbackModel.setTid(tid);
        //0:线上 1:线下
        deliveryParcelbackModel.setType("0");

        List<OrderDeliveryParcelbackModel.StandPlatParcelBackItemDTO> logisticsList = Lists.newArrayList();
        List<StandplatLogisticsSendDataModel> standplatLogisticsSendDataModels = this.addCommonIpParam(ocBOrderRelation, null, null, null);
        StandplatLogisticsSendModel standplatLogisticsSendModel = standplatLogisticsSendDataModels.get(0).getLogisticsSendModels().get(0);
        if (standplatLogisticsSendModel == null) {
            return false;
        }

        for (String ooid : ooids) {
            OrderDeliveryParcelbackModel.StandPlatParcelBackItemDTO dataDto = new OrderDeliveryParcelbackModel.StandPlatParcelBackItemDTO();
            dataDto.setLogisticsCompanyCode(standplatLogisticsSendModel.getCompanyCode());
            dataDto.setLogisticsNo(standplatLogisticsSendModel.getOutSid());
            dataDto.setOid(ooid);
            dataDto.setLogisticsType("1");
            //写死1
            dataDto.setNum(1);
            logisticsList.add(dataDto);
        }
        deliveryParcelbackModel.setLogisticsList(logisticsList);

        try {
            ValueHolderV14 v14 = ipRpcService.deliveryParcel(deliveryParcelbackModel);
            if (v14.getCode() == ResultCode.FAIL) {
                //失败
                String logMsg = "OrderId=" + orderId + ",平台单号=" + tid + "发货通知平台失败," + v14.getMessage();
                orderLogService.addUserOrderLog(orderId, ocBOrder.getBillNo(), OrderLogTypeEnum.PLATFORM_SEND.getKey(), logMsg, "", null, null);
                result = false;
            } else {
                //成功
                String logMsg = "OrderId=" + ocBOrder.getId() + ",平台单号=" + tid + "发货通知平台成功";
                orderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.PLATFORM_SEND.getKey(), logMsg, "", null, null);
                ocBOrderItemMapper.updateItemsWhenDeliverySuccess(ocBOrder.getId(), (String) v14.getData());

                OcBOrder update = new OcBOrder();
                update.setOrderStatus(OmsOrderStatus.PLATFORM_DELIVERY.toInteger());
                update.setId(ocBOrder.getId());
                ocBOrderMapper.updateById(update);
            }
        } catch (Exception e) {
            String logMsg = "OrderId=" + orderId + ",平台单号=" + tid + "发货通知平台失败,";
            orderLogService.addUserOrderLog(orderId, ocBOrder.getBillNo(), OrderLogTypeEnum.PLATFORM_SEND.getKey(), logMsg, "", null, null);
            log.error(LogUtil.format("errorInfo:{}", "dyDelivery.error"), Throwables.getStackTraceAsString(e));
            result = false;
        }
        return result;
    }

    /**
     * 抖音子单号首次发货
     *
     * @param ocBOrder
     * @param model
     */
    private void douyinOoidFirstSend(OcBOrder ocBOrder, StandplatLogisticsSendDataModel model, List<String> noDeliveryOoids) {
        IpBStandplatOrder ipBStandplatOrder = ipBStandplatOrderMapper.selectStandplatOrderByTid(ocBOrder.getTid());
        List<IpBStandplatOrderItemEx> ipBStandplatOrderItemExes = ipBStandplatOrderItemMapper.selectOrderItemList(ipBStandplatOrder.getId());
        Map<String, List<IpBStandplatOrderItemEx>> detailMap = ipBStandplatOrderItemExes.stream().collect(Collectors.groupingBy(IpBStandplatOrderItemEx::getOid));

        List<String> ooids = Lists.newArrayList();
        List<StandplatLogisticsSendModel> logisticsSendModels = model.getLogisticsSendModels();
        for (StandplatLogisticsSendModel logisticsSendModel : logisticsSendModels) {
            List<StandplatLogisticsSendModel.StandplatLogisticsSendDetails> newSendDetails = Lists.newArrayList();
            for (StandplatLogisticsSendModel.StandplatLogisticsSendDetails standplatLogisticsSendDetail : logisticsSendModel.getStandplatLogisticsSendDetails()) {
                StandplatLogisticsSendModel.StandplatLogisticsSendDetails newSend = new StandplatLogisticsSendModel.StandplatLogisticsSendDetails();

                String oid = standplatLogisticsSendDetail.getOid() == null ? standplatLogisticsSendDetail.getItemId() : standplatLogisticsSendDetail.getOid();
                if (!noDeliveryOoids.contains(oid)) {
                    continue;
                }

                if (StringUtils.isNotBlank(oid) && ooids.contains(oid)) {
                    continue;
                }

                BeanUtils.copyProperties(standplatLogisticsSendDetail, newSend);

                standplatLogisticsSendDetail.setBundleList(null);
                standplatLogisticsSendDetail.setNum(detailMap.get(oid).get(0).getNum());
                standplatLogisticsSendDetail.setRealNum(detailMap.get(oid).get(0).getNum());
                newSendDetails.add(standplatLogisticsSendDetail);

                ooids.add(oid);
            }
            logisticsSendModel.setStandplatLogisticsSendDetails(newSendDetails);
        }
    }


}
