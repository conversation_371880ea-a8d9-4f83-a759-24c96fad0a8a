package com.jackrain.nea.oc.controller.patrol;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.api.patrol.OnLineForCheckCmd;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: 夏继超
 * @since: 2019/6/6
 * create at : 2019/6/6 10:59
 */
@RestController
@Slf4j
public class CheckOnLineDataCtrl {
    @Autowired
    OnLineForCheckCmd checkCmd;

    @ApiOperation(value = "检查明细是否存在")
    @RequestMapping(path = "/api/cs/oc/oms/v1/check1", method = RequestMethod.POST)
    public JSONObject check1() {
        String s = null;
        try {
            s = checkCmd.onCheck1();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return JSON.parseObject(s);
    }

    @ApiOperation(value = "检查拦截后的状态")
    @RequestMapping(path = "/api/cs/oc/oms/v1/check3", method = RequestMethod.POST)
    public JSONObject check13() {
        String s = null;
        try {
            s = checkCmd.onCheck3();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return JSON.parseObject(s);
    }
}
