package com.jackrain.nea.oc.oms.task.makeup;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;

/**
 * @Descroption 通用平台退货转换 补偿
 * <AUTHOR>
 * @Date 2019/4/24 19:04
 */

@Slf4j
@Component
public class AutoStandplatRefundMqTask extends BaseR3Task implements IR3Task {

    @Autowired
    private ThreadPoolTaskExecutor standRefundOrderToMqTaskThreadPoolExecutor;

    @Override
    public RunTaskResult execute(JSONObject params) {
        String taskName = this.getClass().getSimpleName();
        String tableName = "ip_b_standplat_refund";
        log.debug(LogUtil.format("AutoStandplatRefundMqTask.execute.{}"), taskName);
        RunTaskResult result = new RunTaskResult();
        long start = System.currentTimeMillis();
        try {
            PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
            Integer size = config.getProperty("lts.AutoStandplatRefundOrderMqTask.range", DEFAULT_PAGE_SIZE);
            Integer transCount = config.getProperty("lts.AutoStandplatRefundOrderMqTask.order.transCount", -1);
            Integer minutes = config.getProperty("lts.AutoStandplatRefundOrderMqTask.order.minutes", 7200);

            Map<String, String> topMap = null;
            Set<String> nodes = topMap.keySet();
            if (CollectionUtils.isEmpty(nodes)) {
                result.setSuccess(false);
                result.setMessage("请检查DRDS环境，node信息获取失败！");
                return result;
            }
            List<Future<Integer>> results = new ArrayList<>();
            for (String node : nodes) {
                results.add(standRefundOrderToMqTaskThreadPoolExecutor.submit(new RefundOrderTransferToMqMakeCallable(taskName, node
                        , topMap.get(node), "return_no", size, TransferOrderStatus.NOT_TRANSFER.toInteger()
                        , transCount, ChannelType.STANDPLAT, minutes)));
            }
            int executeCount = 0;
            for (Future<Integer> futureResult : results) {
                try {
                    executeCount += futureResult.get();
                } catch (InterruptedException e) {
                    log.error(LogUtil.format("Thread InterruptedException.异常: {}"), Throwables.getStackTraceAsString(e));
                } catch (ExecutionException e) {
                    log.error(LogUtil.format("Thread ExecutionException.异常: {}"), Throwables.getStackTraceAsString(e));
                }
            }
            long end = System.currentTimeMillis();
            result.setSuccess(true);
            result.setMessage(taskName + " 执行完毕, 数量：" + executeCount + ", 用时: " + (end - start) + " ms");
        } catch (Exception ex) {
            log.error(LogUtil.format("AutoStandplatRefundMqTask.execute.异常: {}"), Throwables.getStackTraceAsString(ex));
            result.setSuccess(false);
            result.setMessage(ex.getMessage());
        }
        return result;
    }
}
