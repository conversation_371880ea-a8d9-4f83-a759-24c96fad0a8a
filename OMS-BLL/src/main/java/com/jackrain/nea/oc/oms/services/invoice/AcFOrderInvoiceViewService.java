package com.jackrain.nea.oc.oms.services.invoice;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.ac.AcFOrderInvoiceMapper;
import com.jackrain.nea.oc.oms.model.table.AcFOrderInvoice;
import com.jackrain.nea.oc.oms.nums.OmsParamConstant;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.R3ParamUtils;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/09/12 13:20
 */
@Slf4j
@Component
public class AcFOrderInvoiceViewService {
    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    @Autowired
    private AcFOrderInvoiceMapper acOrderInvoiceMapper;

    public ValueHolder execute(QuerySession querySession) throws NDSException {
        SgR3BaseRequest request = R3ParamUtils.parseSaveObject(querySession, SgR3BaseRequest.class);
        request.setR3(true);
        AcFOrderInvoiceViewService service = ApplicationContextHandle.getBean(this.getClass());
        return R3ParamUtils.convertV14WithResult(service.view(request));
    }

    public ValueHolderV14<SgR3BaseResult> view(SgR3BaseRequest request) {
        List<Long> batchObjIds = R3ParamUtils.getBatchObjIds(request);
        ValueHolderV14<SgR3BaseResult> vh = new ValueHolderV14<>(ResultCode.FAIL, "fail!");
        if (batchObjIds.size() > 1) {
            vh.setMessage(Resources.getMessage(" 查看发票不支持选择多条订单，只能选择一条查看!"));
            return vh;
        }
        AcFOrderInvoice acOrderInvoice = acOrderInvoiceMapper.selectById(batchObjIds.get(0));
        if (acOrderInvoice == null) {
            vh.setMessage(Resources.getMessage(" 当前记录已不存在!"));
            return vh;
        }
        //若选择单条数据点击【查看发票】按钮，如果开票状态!=“开票成功”则提示：“选择的订单发票状态未开票，不能查看！”
        if (!OmsParamConstant.TWO.equals(acOrderInvoice.getInvoiceStatus())) {
            vh.setMessage(Resources.getMessage("选择的订单发票状态未开票，不能查看！"));
            return vh;
        }
        String invoiceLinkAddress = acOrderInvoice.getInvoiceLinkAddress();
        if (StringUtils.isBlank(invoiceLinkAddress)){
            vh.setMessage(Resources.getMessage("选择的订单发票无发票链接地址，请先获取开票结果！"));
            return vh;
        }
        SgR3BaseResult sgR3BaseResult = new SgR3BaseResult();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("url",invoiceLinkAddress);
        sgR3BaseResult.setDataJo(jsonObject);
        vh.setData(sgR3BaseResult);
        vh.setCode(ResultCode.SUCCESS);
        vh.setMessage("查看成功!");
        return vh;
    }

}
