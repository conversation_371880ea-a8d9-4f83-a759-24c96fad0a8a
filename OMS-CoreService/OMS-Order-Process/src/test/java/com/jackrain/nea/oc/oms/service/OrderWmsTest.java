package com.jackrain.nea.oc.oms.service;

import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sg.service.SgOutStockNoticeService;
import com.jackrain.nea.web.face.User;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Profile;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2019/5/9 2:17 PM
 * @Version 1.0
 */

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest()
@Profile(value = "dev")
public class OrderWmsTest {

    @Autowired
    private OmsOrderService omsOrderervice;
    @Autowired
    private SgOutStockNoticeService sgOutStockNoticeService;

    @Test
    public void test1() {
        List<Long> list = omsOrderervice.selectAuditedOrderList(0,
                200);
        User rootUser = SystemUserResource.getRootUser();
        if (CollectionUtils.isNotEmpty(list)) {
//            sgOutStockNoticeService.addOutStockNotice(rootUser,list);
        }
    }


    @Test
    public void test2() {
        List<Long> list = new ArrayList<>();
        list.add(4866918L);
        User rootUser = SystemUserResource.getRootUser();
//        sgOutStockNoticeService.addOutStockNotice(rootUser,list);
    }
}
