package com.jackrain.nea.oc.oms.services.delivery.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.services.OrderPlatformDeliveryService;
import com.jackrain.nea.oc.oms.services.delivery.OrderDeliveryCmd;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description:唯品会JITX平台发货实现类
 *
 * <AUTHOR> sunies
 * @since : 2020-11-03
 * create at : 2020-11-03 20:02
 */
@Slf4j
@Component
public class OrderDeliveryOfVipJitxImpl implements OrderDeliveryCmd {

    private static final String JITX_PLATFORM_DELIVERY_FAIL_MSG = "business_system:jitx_platform_delivery_fail_msg";

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;

    @Autowired
    private OrderPlatformDeliveryService orderPlatformDeliveryService;

    @Autowired
    private OcBOrderMapper ocBOrderMapper;


    @Override
    public boolean deliveryDeal(OcBOrderRelation ocBOrderRelation, List<String> tips) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("唯品会平台发货服务,订单id为={}", "唯品会平台发货服务", ocBOrderRelation.getOrderId()), ocBOrderRelation.getOrderId());
        }
        OcBOrder ocBOrder = ocBOrderRelation.getOrderInfo();
        Long id = ocBOrder.getId();
        ValueHolderV14 vh = orderPlatformDeliveryService.weiPinHuiPlaformSendGoods(ocBOrder, SystemUserResource.getRootUser(), false);
        String msg = vh.getMessage();
        if (vh.getCode() == 0) {
            //orderPlatformDeliveryService.updateOrderAndVoidReturnOrder(ocBOrder);
            return ocBOrderMapper.updateOrderAfterPlatDeliverySuccess(ocBOrder.getId()) > 0;
        } else {
            JSONObject retData = (JSONObject) vh.getData();
            if (retData != null && CollectionUtils.isNotEmpty(retData.getJSONArray("failed_list"))) {
                JSONArray failArray = retData.getJSONArray("failed_list");
                JSONObject tmpJson = failArray.getJSONObject(0);
                msg = tmpJson.getString("msg");
                //如果失败原因中包含特定说明，则更新为平台发货
                String failMsg = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(JITX_PLATFORM_DELIVERY_FAIL_MSG);
                if(StringUtils.isBlank(failMsg)){
                    log.error("JITX零售发货单 ID:{} 平台发货失败原因未配置，更新当前单据状态为平台发货失败！");
                }else{
                    List<String> msgList = Arrays.stream(failMsg.split("\\|")).map(String::trim).collect(Collectors.toList());
                    for(String s:msgList){
                        if("".equals(s)){
                            continue;
                        }
                        if(msg.contains(s)){
                            return ocBOrderMapper.updateOrderAfterPlatDeliverySuccess(ocBOrder.getId()) > 0;
                        }
                    }
                }
            }
            /**
             * ☆失败更新订单信息
             */
            OcBOrder failOrder = new OcBOrder();
            failOrder.setId(id);
            failOrder.setIsForce(0L);
            failOrder.setSysremark(msg);
            failOrder.setForceSendFailReason(vh.getMessage());
            failOrder.setMakeupFailNum(ocBOrder.getMakeupFailNum() + 1);
            omsOrderService.updateOrderInfo(failOrder);
            ocBOrderItemMapper.updateFailTimesByOrderid(id);

        }
        return vh.getCode() == 0;
    }

}
