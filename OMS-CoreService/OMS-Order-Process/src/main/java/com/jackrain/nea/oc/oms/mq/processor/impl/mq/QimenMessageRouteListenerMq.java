package com.jackrain.nea.oc.oms.mq.processor.impl.mq;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.mq.annotation.RocketMqMessageListener;
import com.burgeon.mq.core.BaseMessageListener;
import com.burgeon.mq.enums.MqTypeEnum;
import com.burgeon.mq.error.MqException;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.model.enums.QiMenMqMethodEnum;
import com.jackrain.nea.oc.oms.services.RefundOrderToWmsBackService;
import com.jackrain.nea.oc.oms.services.qimen.PosOrderStatusCallBackService;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;


/**
 * <AUTHOR> ruan.gz
 * @Description : 奇门MQ消息路由
 * @Date : 2020/8/14
 *
 *
 * 无用废弃
 **/

@Deprecated
@Slf4j
@RocketMqMessageListener(name = "QimenMessageRouteListenerMq", type = MqTypeEnum.CLOUD)
public class QimenMessageRouteListenerMq implements BaseMessageListener {

    @Autowired
    private RefundOrderToWmsBackService refundOrderToWmsBackService;

    @Autowired
    PosOrderStatusCallBackService posOrderStatusCallBackService;

    @Override
    public void consume(String messageBody, String messageTopic, String messageKey, String messageTag, Object obj) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("奇门MQ消息路由: {}", messageKey), messageBody);
        }
        try {
            JSONObject object = JSONObject.parseObject(messageBody);
            String method = object.getString("method");
            if (StringUtils.equals(QiMenMqMethodEnum.INSERT_REFUND_IN.getMethod(), method)) {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("奇门退单新增messageBody: {}"), messageBody);
                }
                refundOrderToWmsBackService.qimenInsertRefundIn(object);
            } else if (StringUtils.equals(QiMenMqMethodEnum.RECEIVE_ORDER_STATUS_CALLBACK.getMethod(), method)) {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("奇门POS-接单状态回传: {}"), messageBody);
                }
                posOrderStatusCallBackService.receiveOrderStatusCallBack(messageBody);
            } else {
                throw new MqException("无匹配奇门method,method:" + method);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("奇门MQ消息异常: {}"), Throwables.getStackTraceAsString(e));
            throw new MqException(e);
        }
    }
}
