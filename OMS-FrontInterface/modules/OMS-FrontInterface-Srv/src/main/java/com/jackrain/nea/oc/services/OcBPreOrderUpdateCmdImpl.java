package com.jackrain.nea.oc.services;

import com.jackrain.nea.oc.oms.api.OcBPreOrderUpdateCmd;
import com.jackrain.nea.oc.oms.services.OcBPreOrderUpdateService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @ClassName OcBPreOrderUpdateShopCmdImpl
 * @Description 修改店铺
 * <AUTHOR>
 * @Date 2022/10/12 15:55
 * @Version 1.0
 */
@Component
public class OcBPreOrderUpdateCmdImpl implements OcBPreOrderUpdateCmd {

    @Autowired
    private OcBPreOrderUpdateService updateService;

    @Override
    public ValueHolderV14<Void> updateShop(List<String> tids, String shopTitle, User user) {
        return updateService.updateShop(tids, shopTitle, user);
    }

    @Override
    public ValueHolderV14 transferBySerial(String serialNumber, User user) {
        return updateService.transferBySerial(serialNumber, user);
    }
}
