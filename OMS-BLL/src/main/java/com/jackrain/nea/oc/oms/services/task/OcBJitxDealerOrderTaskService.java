package com.jackrain.nea.oc.oms.services.task;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutNotices;
import com.burgeon.r3.sg.share.model.request.out.SgBShareOutSaveRequest;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.constants.R3CommonResultConstants;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.cpext.model.table.CpCStore;
import com.jackrain.nea.ip.model.wing.WingReturnResult;
import com.jackrain.nea.ip.model.wing.yy.JitxStoreChangeRequest;
import com.jackrain.nea.ip.model.wing.yy.JitxSxOrderAddRequest;
import com.jackrain.nea.ip.model.wing.yy.JitxSxOrderCancelRequest;
import com.jackrain.nea.oc.oms.mapper.*;
import com.jackrain.nea.oc.oms.mapper.task.OcBJitxDealerOrderTaskMapper;
import com.jackrain.nea.oc.oms.model.constant.VipConstant;
import com.jackrain.nea.oc.oms.model.enums.JitxDealerTaskOrderTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.JitxDealerTaskStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.JitxDealerTaskTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBJitxDealerOrderRelation;
import com.jackrain.nea.oc.oms.model.table.*;
import com.jackrain.nea.oc.oms.model.table.task.OcBJitxDealerOrderTask;
import com.jackrain.nea.oc.oms.nums.VipJitxWorkflowStateEnum;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.oc.oms.util.SplitMessageUtil;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.util.ValueHolderV14Utils;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * description：jitx经销商订单任务
 *
 * <AUTHOR>
 * @date 2021/12/20
 */
@Slf4j
@Component
public class OcBJitxDealerOrderTaskService {

    @Autowired
    private OcBJitxDealerOrderTaskMapper taskMapper;

    @Autowired
    private BuildSequenceUtil buildSequenceUtil;

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private IpBJitxDeliveryRecordMapper jitxDeliveryRecordMapper;

    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;

    @Autowired
    private IpBTimeOrderVipItemMapper timeOrderVipItemMapper;

    @Autowired
    private IpBTimeOrderVipMapper timeOrderVipMapper;

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private IpRpcService ipRpcService;

    @Autowired
    private SgRpcService sgRpcService;

    public static final String CUSTOMER_CODE = "YYOTO";

    public static final String YY_WMS_TYPE = "YYOTO";


    public void orderCancel(OcBJitxDealerOrderRelation relation) {
        if (log.isDebugEnabled()) {
            log.debug(" YY取消占用数据插入开始：{}", JSON.toJSONString(relation));
        }
        OcBOrder order = relation.getOcBOrder();
        Long warehouseId = relation.getWarehouseId();
        User user = relation.getUser();
        IpBTimeOrderVip timeOrderVip = relation.getTimeOrderVip();
        String tid = "";

        OcBJitxDealerOrderTask dealerOrderTask = new OcBJitxDealerOrderTask();
        if (timeOrderVip != null) {
            tid = timeOrderVip.getOrderSn();
            SgBShareOutSaveRequest request = new SgBShareOutSaveRequest();
            request.setSourceBillId(timeOrderVip.getId());
            request.setSourceBillType(SgConstantsIF.BILL_TYPE_VIPSHOP_TIME);
            ValueHolderV14<Map<Long, Boolean>> v14 = sgRpcService.queryShareStoreIsYy(Collections.singletonList(request));
            if (!v14.isOK() || ObjectUtils.isEmpty(v14.getData()) && Boolean.FALSE.equals(v14.getData().get(timeOrderVip.getId()))) {
                return;
            }

            this.setFeildByTimeOrder(timeOrderVip, dealerOrderTask);
            dealerOrderTask.setOrderType(JitxDealerTaskOrderTypeEnum.VIP_TIME_ORDER.getCode());
        }
        if (order != null) {
            tid = order.getTid();
            boolean jitxDealerYYWarehouse = this.isJitxDealerYYWarehouse(warehouseId);
            if (!jitxDealerYYWarehouse) {
                return;
            }
            this.setFeildValue(order, dealerOrderTask);
            dealerOrderTask.setOrderType(JitxDealerTaskOrderTypeEnum.OC_B_ORDER.getCode());
        }
        dealerOrderTask.setType(JitxDealerTaskTypeEnum.YY_CANCEL_OCCUPY.getCode());
        dealerOrderTask.setState(JitxDealerTaskStatusEnum.SUCCESS.getCode());
        this.save(dealerOrderTask, user);
        try {
            //TODO 调用wing接口
            JitxSxOrderCancelRequest request = new JitxSxOrderCancelRequest();
            request.setOrder_Sn(tid);
            request.setCustomerCode(CUSTOMER_CODE);
            ValueHolderV14<WingReturnResult> v14 = ipRpcService.cancelSxJitxOrder(request);

            if (v14.isOK()) {
                WingReturnResult wingReturnResult = v14.getData();
                if (!wingReturnResult.isOk()) {
                    this.failUpdate(dealerOrderTask.getId(), wingReturnResult.getMsg());
                } else {
                    if (CollectionUtils.isNotEmpty(wingReturnResult.getFaileds())) {
                        this.failUpdate(dealerOrderTask.getId(), wingReturnResult.getFaileds().get(0).getErrMsg());
                    }
                }
            } else {
                this.failUpdate(dealerOrderTask.getId(), SplitMessageUtil.splitMsgBySize(v14.getMessage(), SplitMessageUtil.SIZE_999));
            }
        } catch (Exception e) {
            this.failUpdate(dealerOrderTask.getId(), SplitMessageUtil.splitMsgBySize(e.getMessage(), SplitMessageUtil.SIZE_999));
        }
    }

    private void setFeildByTimeOrder(IpBTimeOrderVip timeOrderVip, OcBJitxDealerOrderTask dealerOrderTask) {
        dealerOrderTask.setTid(timeOrderVip.getOrderSn());
        dealerOrderTask.setBillNo(timeOrderVip.getBillNo());
        dealerOrderTask.setCpCShopId(timeOrderVip.getCpCShopId());
        dealerOrderTask.setCpCPlatformId(PlatFormEnum.VIP_JITX.getCode().longValue());
    }

    public void orderAdd(OcBJitxDealerOrderRelation relation) {
        if (log.isDebugEnabled()) {
            log.debug(" YY占用数据插入开始：{}", JSON.toJSONString(relation));
        }
        OcBOrder order = relation.getOcBOrder();
        Long warehouseId = relation.getWarehouseId();
        User user = relation.getUser();

        boolean jitxDealerYYWarehouse = this.isJitxDealerYYWarehouse(warehouseId);
        if (!jitxDealerYYWarehouse) {
            return;
        }
        OcBJitxDealerOrderTask dealerOrderTask = new OcBJitxDealerOrderTask();
        this.setFeildValue(order, dealerOrderTask);
        dealerOrderTask.setCpCPhyWarehouseId(warehouseId);
        dealerOrderTask.setType(JitxDealerTaskTypeEnum.YY_OCCUPY.getCode());
        dealerOrderTask.setOrderType(JitxDealerTaskOrderTypeEnum.OC_B_ORDER.getCode());
        dealerOrderTask.setState(JitxDealerTaskStatusEnum.SUCCESS.getCode());
        this.save(dealerOrderTask, user);
        try {
            JitxSxOrderAddRequest request = this.getJitxSxOrderAddRequest(order);
            ValueHolderV14<WingReturnResult> v14 = ipRpcService.addSxJitxOrder(Lists.newArrayList(request));
            if (v14.isOK()) {
                WingReturnResult wingReturnResult = v14.getData();
                if (!wingReturnResult.isOk()) {
                    this.failUpdate(dealerOrderTask.getId(), wingReturnResult.getMsg());
                } else {
                    if (CollectionUtils.isNotEmpty(wingReturnResult.getFaileds())) {
                        this.failUpdate(dealerOrderTask.getId(), wingReturnResult.getFaileds().get(0).getErrMsg());
                    }
                }
            } else {
                this.failUpdate(dealerOrderTask.getId(), SplitMessageUtil.splitMsgBySize(v14.getMessage(), SplitMessageUtil.SIZE_999));
            }
        } catch (Exception e) {
            this.failUpdate(dealerOrderTask.getId(), SplitMessageUtil.splitMsgBySize(e.getMessage(), SplitMessageUtil.SIZE_999));
        }
    }

    public void failUpdate(Long id, String msg) {
        OcBJitxDealerOrderTask update = new OcBJitxDealerOrderTask();
        update.setId(id);
        update.setState(JitxDealerTaskStatusEnum.FAIL.getCode());
        update.setMsg(SplitMessageUtil.splitMsgBySize(msg, SplitMessageUtil.SIZE_999));
        update.setFailNumber(1);
        taskMapper.updateById(update);
    }

    public JitxSxOrderAddRequest getJitxSxOrderAddRequest(OcBOrder order) {
        if (log.isDebugEnabled()) {
            log.debug("发货单占用调用接口数据转换开始");
        }
        try {
            JitxSxOrderAddRequest request = new JitxSxOrderAddRequest();
            List<OcBOrderItem> itemList = ocBOrderItemMapper.selectUnSuccessRefund(order.getId());
            List<IpBTimeOrderVip> timeOrderVips = timeOrderVipMapper.selectTimeOrderByOrderSn(order.getTid());
            if (CollectionUtils.isEmpty(itemList)) {
                return null;
            }
            OcBOrderItem orderItem = itemList.get(0);
            request.setVipOrder_sn(order.getTid());
            request.setSku(orderItem.getPsCSkuEcode());
            request.setBarCode(orderItem.getBarcode());
            BigDecimal qty = orderItem.getQty() == null ? BigDecimal.ZERO : orderItem.getQty();
            request.setQty(qty.intValue());
            request.setTyWmsType(YY_WMS_TYPE);
            request.setProvince(order.getCpCRegionProvinceEname());
            request.setCity(order.getCpCRegionCityEname());
            request.setArea(order.getCpCRegionAreaEname());
            request.setAddress_Code("");
            if (CollectionUtils.isNotEmpty(timeOrderVips)) {
                request.setAddress_Code(timeOrderVips.get(0).getAddressCode());
            }
            if (log.isDebugEnabled()) {
                log.debug("发货单占用调用接口数据转换结束:{}", JSON.toJSONString(request));
            }
            return request;
        } catch (Exception e) {
            log.error("{},发货单占用数据组装异常：{}", this.getClass().getSimpleName(), Throwables.getStackTraceAsString(e));
        }
        return null;
    }

    public JitxSxOrderAddRequest getJitxSxOrderAddRequest(IpBTimeOrderVip order) {
        if (log.isDebugEnabled()) {
            log.debug("时效单占用调用接口数据转换开始");
        }
        try {
            JitxSxOrderAddRequest request = new JitxSxOrderAddRequest();
            List<IpBTimeOrderVipItemEx> itemList = timeOrderVipItemMapper.selectOrderItemList(order.getId());
            IpBTimeOrderVipItemEx orderItem = itemList.get(0);
            request.setVipOrder_sn(order.getOrderSn());
            request.setSku(orderItem.getProdSku().getSkuEcode());
            request.setBarCode(orderItem.getBarcode());
            BigDecimal qty = orderItem.getAmount() == null ? BigDecimal.ZERO : orderItem.getAmount();
            request.setQty(qty.intValue());
            request.setTyWmsType(YY_WMS_TYPE);
            request.setProvince(order.getAddressCode());
            request.setCity(order.getAddressCode());
            request.setArea(order.getAddressCode());
            request.setAddress_Code(order.getAddressCode());
            if (log.isDebugEnabled()) {
                log.debug("时效单占用调用接口数据转换结束:{}", JSON.toJSONString(request));
            }
            return request;
        } catch (Exception e) {
            log.error("{},时效单占用数据组装异常：{}", this.getClass().getSimpleName(), Throwables.getStackTraceAsString(e));
        }
        return null;
    }

    public void handelChangeWarehouse(OcBJitxModifyWarehouseLog warehouseLog, OcBOrder order, User user) {
        //如果存在YY占单任务，且无取消任务，更新YY占单任务实体仓id字段
        warehouseLog.setBeforeChangeWarehouseId(-1L);
        List<OcBJitxDealerOrderTask> dealerOrderTasks = taskMapper
                .selectList(new LambdaQueryWrapper<OcBJitxDealerOrderTask>()
                        .eq(OcBJitxDealerOrderTask::getTid,order.getTid())
                        .eq(OcBJitxDealerOrderTask::getIsactive,R3CommonResultConstants.VALUE_Y));
        if(org.apache.commons.collections.CollectionUtils.isNotEmpty(dealerOrderTasks)){
            List<OcBJitxDealerOrderTask> occupyList = dealerOrderTasks.stream()
                    .filter(d -> JitxDealerTaskTypeEnum.YY_OCCUPY.getCode().equals(d.getType())).collect(Collectors.toList());
            List<OcBJitxDealerOrderTask> cancelList = dealerOrderTasks.stream()
                    .filter(d -> JitxDealerTaskTypeEnum.YY_CANCEL_OCCUPY.getCode().equals(d.getType())).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(occupyList) && CollectionUtils.isEmpty(cancelList)){
                occupyList.sort(Comparator.comparing(OcBJitxDealerOrderTask::getCreationdate));
                warehouseLog.setBeforeChangeWarehouseId(occupyList.get(occupyList.size()-1).getCpCPhyWarehouseId());
            }
        }
        OcBJitxDealerOrderRelation cancelRelation = new OcBJitxDealerOrderRelation();
        cancelRelation.setOcBOrder(order);
        cancelRelation.setWarehouseId(warehouseLog.getBeforeChangeWarehouseId());
        cancelRelation.setUser(user);
        this.orderCancel(cancelRelation);

        OcBJitxDealerOrderRelation occupyRelation = new OcBJitxDealerOrderRelation();
        occupyRelation.setOcBOrder(order);
        occupyRelation.setWarehouseId(warehouseLog.getAfterChangeWarehouseId());
        occupyRelation.setUser(user);
        this.orderAdd(occupyRelation);
    }

    public void setFeildValue(OcBOrder order, OcBJitxDealerOrderTask dealerOrderTask) {
        dealerOrderTask.setCpCShopId(order.getCpCShopId());
        dealerOrderTask.setCpCPlatformId(Long.valueOf(order.getPlatform()));
        dealerOrderTask.setBillNo(order.getBillNo());
        dealerOrderTask.setTid(order.getTid());
        dealerOrderTask.setFailNumber(0);
    }

    /**
     * description：判断是否是YY经销商仓库
     *
     * <AUTHOR>
     * @date 2021/12/21
     */
    public boolean isJitxDealerYYWarehouse(Long cpCPhyWarehouseId) {
        CpCPhyWarehouse warehouse = cpRpcService.queryByWarehouseId(cpCPhyWarehouseId);
        if (warehouse != null) {
            String wmsType = warehouse.getWmsType();
            return YY_WMS_TYPE.equals(wmsType);
        }
        return false;
    }


    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 save(OcBJitxDealerOrderTask dealerOrderTask, User user) {
        log.info("{},插入jitx经销商任务表数据开始：{}", this.getClass().getSimpleName(), JSON.toJSONString(dealerOrderTask));
        if (dealerOrderTask == null) {
            return ValueHolderV14Utils.getFailValueHolder("参数为空");
        }
        if (dealerOrderTask.getCpCShopId() == null || dealerOrderTask.getCpCShopId() == 0) {
            return ValueHolderV14Utils.getFailValueHolder("店铺为空");
        }
        if (dealerOrderTask.getCpCPlatformId() == null || dealerOrderTask.getCpCPlatformId() == 0) {
            return ValueHolderV14Utils.getFailValueHolder("平台为空");
        }
        if (StringUtils.isEmpty(dealerOrderTask.getBillNo())) {
            return ValueHolderV14Utils.getFailValueHolder("业务单据编号为空");
        }
        if (dealerOrderTask.getType() == null) {
            return ValueHolderV14Utils.getFailValueHolder("任务类型为空");
        }
        if (dealerOrderTask.getState() == null) {
            dealerOrderTask.setState(JitxDealerTaskStatusEnum.NOT.getCode());
        }
        BaseModelUtil.makeBaseCreateField(dealerOrderTask, user);
        dealerOrderTask.setId(buildSequenceUtil.buildJitxDealerOrderTaskId());
        int i = taskMapper.insert(dealerOrderTask);
        if (i > 0) {
            return ValueHolderV14Utils.getSuccessValueHolder("插入数据成功");
        } else {
            return ValueHolderV14Utils.getFailValueHolder("插入数据失败");
        }
    }


    /**
     * 根据平台单号和类型更新任务状态
     *
     * @param tid          【平台单号
     * @param taskTypeEnum 类型
     * @return
     */
    public int updateByTypeAndTid(String tid, JitxDealerTaskTypeEnum taskTypeEnum, JitxDealerTaskStatusEnum status, User user) {
        List<OcBJitxDealerOrderTask> taskList = taskMapper.selectList(new LambdaQueryWrapper<OcBJitxDealerOrderTask>()
                .eq(OcBJitxDealerOrderTask::getType, taskTypeEnum.getCode())
                .eq(OcBJitxDealerOrderTask::getTid, tid)
                .eq(OcBJitxDealerOrderTask::getIsactive, R3CommonResultConstants.VALUE_Y));
        if (log.isDebugEnabled()) {
            log.debug(" 更新任务，查询经销商任务结果：{}", JSON.toJSONString(taskList));
        }
        if (CollectionUtils.isEmpty(taskList)) {
            return 0;
        }

        OcBJitxDealerOrderTask update = new OcBJitxDealerOrderTask();
        update.setState(status.getCode());
        update.setModifierid(Long.valueOf(user.getId()));
        update.setModifiername(user.getName());
        update.setModifierename(user.getEname());

        return taskMapper.update(update, new LambdaQueryWrapper<OcBJitxDealerOrderTask>()
                .in(OcBJitxDealerOrderTask::getId,
                        taskList.stream().map(OcBJitxDealerOrderTask::getId).collect(Collectors.toList())));
    }

    /**
     * 反馈YY改仓结果
     *
     * @param modifyWarehouseLog 改仓中间表信息
     * @param isSuccess          是否改仓成功
     */
    public void notifyYy(OcBJitxModifyWarehouseLog modifyWarehouseLog, Boolean isSuccess) {
        ValueHolderV14<OcBOrder> valueHolderV14 = new ValueHolderV14<>(ResultCode.FAIL, "无需操作");
        try {
            valueHolderV14 = retryNotify(modifyWarehouseLog, isSuccess);
        } catch (Exception e) {
            log.error(" 通知换仓结果异常：{}", Throwables.getStackTraceAsString(e));
            valueHolderV14.setMessage(e.getMessage());
        }
        //新增一个结果状态表示无需进行改仓反馈
        if (valueHolderV14.getCode() == -2) {
            if (log.isDebugEnabled()) {
                log.debug(" 通知换仓结果：{}", JSON.toJSONString(valueHolderV14));
            }
            return;
        }

        //创建反馈换仓结果任务
        OcBJitxDealerOrderTask dealerOrderTask = new OcBJitxDealerOrderTask();
        this.setFeildValue(valueHolderV14.getData(), dealerOrderTask);
        dealerOrderTask.setModifyWarehouseLogId(modifyWarehouseLog.getId());
        dealerOrderTask.setMsg(valueHolderV14.getMessage());
        dealerOrderTask.setType(JitxDealerTaskTypeEnum.YY_CHANGE_WAREHOUSE.getCode());
        dealerOrderTask.setState(valueHolderV14.isOK() ?
                JitxDealerTaskStatusEnum.SUCCESS.getCode() : JitxDealerTaskStatusEnum.FAIL.getCode());
        this.save(dealerOrderTask, SystemUserResource.getRootUser());

    }

    /**
     * 换仓反馈失败补偿
     *
     * @param modifyWarehouseLog 换仓信息
     * @param isSuccess          是否成功
     * @return
     */
    public ValueHolderV14<OcBOrder> retryNotify(OcBJitxModifyWarehouseLog modifyWarehouseLog, Boolean isSuccess) {
        ValueHolderV14<OcBOrder> valueHolderV14 = new ValueHolderV14<>(ResultCode.FAIL, "处理失败");

        //根据平台单号查询是否存在申请改仓状态的JITX寻仓记录，没有得话直接返回
        List<IpBJitxDeliveryRecord> jitxDeliveryRecordList = jitxDeliveryRecordMapper
                .selectList(new LambdaQueryWrapper<IpBJitxDeliveryRecord>()
                        .eq(IpBJitxDeliveryRecord::getIsactive, R3CommonResultConstants.VALUE_Y)
                        .eq(IpBJitxDeliveryRecord::getTid, modifyWarehouseLog.getSourceCode())
                        .eq(!ObjectUtils.isEmpty(isSuccess), IpBJitxDeliveryRecord::getTransStatus, VipConstant.JITX_DELIVERY_RECORD_EXCHANGING));
        if (log.isDebugEnabled()) {
            log.debug(" 换仓反馈，查询换仓申请记录，结果：{}", JSON.toJSONString(jitxDeliveryRecordList));
        }
        if (CollectionUtils.isEmpty(jitxDeliveryRecordList)) {
            valueHolderV14.setMessage("未查询到换仓申请记录，无需操作");
            valueHolderV14.setCode(-2);
            return valueHolderV14;
        }
        //获取申请时间最新的换仓申请
        jitxDeliveryRecordList = jitxDeliveryRecordList.stream()
                .sorted(Comparator.comparing(IpBJitxDeliveryRecord::getApplicationTime)).collect(Collectors.toList());
        IpBJitxDeliveryRecord deliveryRecord = jitxDeliveryRecordList.get(jitxDeliveryRecordList.size() - 1);
        IpBJitxDeliveryRecord oldDeliveryRecord = null;
        if (jitxDeliveryRecordList.size() > 1) {
            oldDeliveryRecord = jitxDeliveryRecordList.get(jitxDeliveryRecordList.size() - 2);
        }


        //发货单信息
        List<OcBOrder> orderList = ocBOrderMapper.getOrdersUnionGsiBySourceCode(modifyWarehouseLog.getSourceCode());
        if (log.isDebugEnabled()) {
            log.debug(" 换仓反馈，查询发货单，结果：{}", JSON.toJSONString(orderList));
        }
        orderList = orderList.stream().filter(o -> R3CommonResultConstants.VALUE_Y.equals(o.getIsactive())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderList)) {
            valueHolderV14.setMessage("未查询到发货单信息，无需操作");
            return valueHolderV14;
        }

        OcBOrder ocBOrder = orderList.get(0);

        Date now = new Date();

        if (ObjectUtils.isEmpty(deliveryRecord.getEwaybillContent())) {
            //电子面单信息如果为空，重新获取电子面单信息
            //查询逻辑仓
            List<CpCStore> stores = cpRpcService.selectStoresByCodes(Collections.singletonList(deliveryRecord.getStoreCode()));
            if (CollectionUtils.isNotEmpty(stores)) {
                SgBStoOutNotices outNotices = new SgBStoOutNotices();
                outNotices.setId(ocBOrder.getSgBOutBillId());
                outNotices.setCpCPhyWarehouseId(stores.get(0).getCpCPhyWarehouseId());
                outNotices.setLogisticNumber(ocBOrder.getExpresscode());
                ValueHolderV14<String> labelResult = sgRpcService.getLabelByNoticeNo(outNotices);
                if (valueHolderV14.isOK()) {
                    deliveryRecord.setEwaybillContent(labelResult.getData());
                }else {
                    log.warn(" 换仓获取电子面单为空，结果【{}】",JSON.toJSONString(labelResult));
                    valueHolderV14.setMessage("换仓获取电子面单为空，等待下次任务");
                    return valueHolderV14;
                }
            } else {
                log.warn(" YY换仓查询逻辑仓为空，code【{}】", deliveryRecord.getStoreCode());
            }

        }

        //更新寻仓结果的换仓状态,及物流信息、电子面单信息
        IpBJitxDeliveryRecord recordUpdate = new IpBJitxDeliveryRecord();
        boolean isPass = VipJitxWorkflowStateEnum.PASS.getKey().equals(modifyWarehouseLog.getWorkflowState());
        recordUpdate.setTransStatus(isPass ? VipConstant.JITX_DELIVERY_RECORD_EXCHANGED :
                VipConstant.JITX_DELIVERY_RECORD_EXCHANGE_FAIL);
        recordUpdate.setModifieddate(now);
        recordUpdate.setCarrierCode(ocBOrder.getCpCLogisticsEcode());
        recordUpdate.setLogisticNumber(ocBOrder.getExpresscode());
        recordUpdate.setEwaybillContent(deliveryRecord.getEwaybillContent());
        jitxDeliveryRecordMapper.update(recordUpdate, new LambdaQueryWrapper<IpBJitxDeliveryRecord>()
                .in(IpBJitxDeliveryRecord::getId, jitxDeliveryRecordList.stream().map(IpBJitxDeliveryRecord::getId).collect(Collectors.toList())));

        //请求YY接口反馈换仓结果
        JitxStoreChangeRequest changeRequest = new JitxStoreChangeRequest();
        changeRequest.setTid(modifyWarehouseLog.getSourceCode());
        changeRequest.setId(ocBOrder.getSgBOutBillNo());
        changeRequest.setWorkFlow_Sn(modifyWarehouseLog.getWorkflowSn());
        changeRequest.setWorkFlow_States(modifyWarehouseLog.getWorkflowState());
        changeRequest.setNewCarrierCode(ocBOrder.getCpCLogisticsEcode());
        changeRequest.setNewTransPortNo(ocBOrder.getExpresscode());
        changeRequest.setNewOrdeLabel(Base64.getEncoder().encodeToString(Optional
                .ofNullable(deliveryRecord.getEwaybillContent()).orElse("").getBytes(StandardCharsets.UTF_8)));
        if (isPass) {
            changeRequest.setProcessStatus(1);
            changeRequest.setNewJitStore(modifyWarehouseLog.getNewDeliveryWarehouse());
            changeRequest.setNewStore(deliveryRecord.getYyStoreCode());
        } else {
            changeRequest.setProcessStatus(2);
            changeRequest.setNewJitStore(modifyWarehouseLog.getOldDeliveryWarehouse());
            changeRequest.setNewStore(ObjectUtils.isEmpty(oldDeliveryRecord) ? "" : oldDeliveryRecord.getYyStoreCode());
        }
        ValueHolderV14<WingReturnResult> v14 = ipRpcService.changeStoreJitxOrder(Lists.newArrayList(changeRequest));

        valueHolderV14.setCode(v14.getCode());
        valueHolderV14.setMessage(v14.getMessage());
        if (v14.isOK()) {
            WingReturnResult data = v14.getData();
            if (!data.isOk()) {
                valueHolderV14.setCode(ResultCode.FAIL);
                valueHolderV14.setMessage(data.getMsg());
            } else {
                List<WingReturnResult.FailMsg> faileds = data.getFaileds();
                if (CollectionUtils.isNotEmpty(faileds)) {
                    valueHolderV14.setCode(ResultCode.FAIL);
                    valueHolderV14.setMessage(JSON.toJSONString(faileds));
                }
            }
        }

        valueHolderV14.setData(ocBOrder);
        return valueHolderV14;
    }

}
