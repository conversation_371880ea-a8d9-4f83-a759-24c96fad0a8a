package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoOrder;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.jdbc.SQL;

import java.util.List;

@Mapper
public interface IpBTaobaoOrderMapper extends ExtentionMapper<IpBTaobaoOrder> {

    /**
     * REMARK最长数量。默认200字符
     */
    int MAX_REMARK_LENGTH = 200;

    /**
     * 依据TID进行查询淘宝订单数据
     *
     * @param orderNo 淘宝订单数据。平台订单数据
     * @return 淘宝订单数据
     */
    @Select("SELECT * FROM ip_b_taobao_order WHERE tid=#{orderNo}")
    IpBTaobaoOrder selectTaobaoOrderByTid(@Param("orderNo") String orderNo);

    /**
     * 淘宝订单SQL创建器
     */
    class TaobaoOrderSqlBuilder {
        /**
         * 创建更新订单转换状态SQL
         *
         * @param orderNo          订单编号
         * @param isTrans          转换状态
         * @param isUpdateTransNum 是否更新转换数量
         * @param remarks          转换备注信息
         * @return 更新SQL语句
         */
        public String buildUpdateOrderTransSQL(@Param("orderNo") String orderNo, @Param("isTrans") int isTrans,
                                               @Param("isUpdateTransNum") boolean isUpdateTransNum,
                                               @Param("remarks") String remarks) {

            return new SQL() {
                {
                    UPDATE("ip_b_taobao_order");
                    SET("istrans=#{isTrans}", "abnormal_type = NULL ");
                    if (isUpdateTransNum) {
                        SET("trans_count = IFNULL(trans_count, 0) + 1");
                    }
                    SET("sysremark=#{remarks}");
                    WHERE("tid=#{orderNo}");
                }
            }.toString();
        }


        public String buildUpdateOrderTransAndAbnormal(@Param("orderNo") String orderNo, @Param("isTrans") int isTrans,
                                                       @Param("remarks") String remarks, @Param("abnormalType") Integer abnormalType) {

            return new SQL() {
                {
                    UPDATE("ip_b_taobao_order");
                    SET("istrans=#{isTrans}");
                    SET("abnormal_type=#{abnormalType}");
                    SET("trans_count = IFNULL(trans_count, 0) + 1");
                    SET("transdate = now()");
                    SET("modifieddate = now()");
                    SET("sysremark=#{remarks}");
                    WHERE("tid=#{orderNo}");
                }
            }.toString();
        }

        /**
         * 查询DRDS的分库OrderNo
         *
         * @param node
         * @param name    table name
         * @param size
         * @param isTrans
         * @param remarks
         * @param minutes
         * @return
         */
        public String selectDynamicTaskOrder(String node, String name, int size, int isTrans, String remarks, int minutes) {
            StringBuilder sb = new StringBuilder();
            sb.append("/*!TDDL:NODE=");
            sb.append(node);
            sb.append("*/ ");
            sb.append("SELECT tid FROM ");
            sb.append(name);
            sb.append(" WHERE istrans = ");
            sb.append(isTrans);
            // remarks不为null的时候作为查询条件
            if (remarks != null) {
                if ("".equals(remarks)) {
                    sb.append(" AND (sysremark IS NULL OR sysremark = '') ");
                } else {
                    sb.append(" AND sysremark = ");
                    sb.append("'").append(remarks).append("'");
                }
            } else if (isTrans == TransferOrderStatus.TRANSFERFAIL.toInteger()) {
                sb.append(" AND sysremark !='补偿转单'");
            }
            if (minutes > 0) {
                sb.append(" AND creationdate > (DATE_SUB(NOW(),INTERVAL ");
                sb.append(minutes);
                sb.append(" MINUTE))");
            }
            sb.append(" ORDER BY creationdate DESC LIMIT ");
            sb.append(size);

            return sb.toString();
        }

        /**
         * update order sysremark
         *
         * @param orderNos
         * @param remarks
         * @return
         */
        public String updateSysRemarkWhenItIsNull(String orderNos, String remarks) {
            StringBuilder sb = new StringBuilder();
            sb.append("UPDATE ip_b_taobao_order SET sysremark='");
            sb.append(remarks);
            sb.append("' WHERE (sysremark IS NULL OR sysremark = '') AND tid IN (");
            sb.append(orderNos);
            sb.append(")");
            return sb.toString();
        }

        /**
         * update order sysremark
         *
         * @param orderNos
         * @param remarks
         * @return
         */
        public String updateSysRemark(String orderNos, String remarks) {
            StringBuilder sb = new StringBuilder();
            sb.append("UPDATE ip_b_taobao_order SET sysremark='");
            sb.append(remarks);
            sb.append("' WHERE tid IN (");
            sb.append(orderNos);
            sb.append(")");
            return sb.toString();
        }

    }

    /**
     * 更新订单转换状态
     *
     * @param orderNo          订单编号
     * @param isTrans          转换状态
     * @param isUpdateTransNum 是否更新转换数量
     * @param remarks          转换备注信息
     * @return 更新结果
     * //@Update("update ip_b_taobao_order set istrans=#{isTrans},abnormal_type = NULL,trans_count =
     * IFNULL(trans_count, 0) + 1,sysremark=#{remarks} where tid=#{orderNo}")
     * // @UpdateProvider(type = TaobaoOrderSqlBuilder.class, method = "buildUpdateOrderTransSQL")
     */
    @UpdateProvider(type = TaobaoOrderSqlBuilder.class, method = "buildUpdateOrderTransSQL")
    int updateOrderIsTrans(@Param("orderNo") String orderNo, @Param("isTrans") int isTrans,
                           @Param("isUpdateTransNum") boolean isUpdateTransNum, @Param("remarks") String remarks);


    @UpdateProvider(type = TaobaoOrderSqlBuilder.class, method = "buildUpdateOrderTransAndAbnormal")
    int updateOrderIsTransAndAbnormal(@Param("orderNo") String orderNo, @Param("isTrans") int isTrans,
                                      @Param("remarks") String remarks, @Param("abnormalType") Integer abnormalType);


    /***
     * 查询DRDS的分库OrderNo
     * @param node node name
     * @param name table name
     * @param size size
     * @param isTrans trans status
     * @param remarks remark
     * @param minutes before n minutes
     * @return OrderNo（TID）
     */
    @SelectProvider(type = TaobaoOrderSqlBuilder.class, method = "selectDynamicTaskOrder")
    List<String> selectDynamicTaskOrder(String node, String name, int size, int isTrans, String remarks, int minutes);
    /**
     * 批量更新淘宝订单的备注信息
     *
     * @param orderNos 订单编号集合(1,2,3...)
     * @param remarks  转换备注信息
     * @return int
     */
    @UpdateProvider(type = TaobaoOrderSqlBuilder.class, method = "updateSysRemarkWhenItIsNull")
    int updateSysRemarkWhenItIsNull(String orderNos, String remarks);

    /**
     * 批量更新淘宝订单的备注信息
     *
     * @param orderNos 订单编号集合(1,2,3...)
     * @param remarks  转换备注信息
     * @return int
     */
    @UpdateProvider(type = TaobaoOrderSqlBuilder.class, method = "updateSysRemark")
    int updateSysRemark(String orderNos, String remarks);

}