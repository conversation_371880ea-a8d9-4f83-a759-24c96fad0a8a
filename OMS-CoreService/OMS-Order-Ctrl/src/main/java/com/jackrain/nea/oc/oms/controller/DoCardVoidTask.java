//package com.jackrain.nea.oc.oms.controller;
//
//import com.alibaba.fastjson.JSONObject;
//import com.google.common.base.Throwables;
//import com.jackrain.nea.cpext.model.table.CpCPlatform;
//import com.jackrain.nea.hub.api.naika.NaiKaOrderCmd;
//import com.jackrain.nea.hub.request.naika.NaiKaReissueRequest;
//import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
//import com.jackrain.nea.oc.oms.mapper.OcBOrderNaiKaMapper;
//import com.jackrain.nea.oc.oms.mapper.OcBOrderNaikaVoidMapper;
//import com.jackrain.nea.oc.oms.mapper.OcBReturnAfSendMapper;
//import com.jackrain.nea.oc.oms.model.enums.CardAutoVoidEnum;
//import com.jackrain.nea.oc.oms.model.enums.NaikaVoidStatusEnum;
//import com.jackrain.nea.oc.oms.model.enums.naika.OmsOrderNaiKaStatusEnum;
//import com.jackrain.nea.oc.oms.model.table.OcBOrder;
//import com.jackrain.nea.oc.oms.model.table.OcBOrderNaiKa;
//import com.jackrain.nea.oc.oms.model.table.OcBOrderNaikaVoid;
//import com.jackrain.nea.oc.oms.model.table.OcBReturnAfSend;
//import com.jackrain.nea.rpc.CpRpcService;
//import com.jackrain.nea.sys.domain.ValueHolderV14;
//import com.jackrain.nea.utility.LogUtil;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.apache.dubbo.config.annotation.Reference;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.context.ApplicationContext;
//import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
//import org.springframework.stereotype.Component;
//import org.springframework.transaction.annotation.Transactional;
//
//import java.util.ArrayList;
//import java.util.Date;
//import java.util.List;
//import java.util.Map;
//import java.util.Set;
//import java.util.concurrent.Callable;
//import java.util.concurrent.Future;
//import java.util.stream.Collectors;
//
///**
// * @ClassName DoCardVoidTask
// * @Description 执行奶卡自动作废
// * <AUTHOR>
// * @Date 2023/3/6 17:55
// * @Version 1.0
// */
//@Component
//@Slf4j
//public class DoCardVoidTask {
//
//    @Autowired
//    private ThreadPoolTaskExecutor doCardVoidPollExecutor;
//    @Autowired
//    private OcBOrderNaikaVoidMapper naikaVoidMapper;
//    @Autowired
//    private OcBOrderNaiKaMapper naiKaMapper;
//    @Autowired
//    private OcBOrderMapper ocBOrderMapper;
//    @Autowired
//    private OcBReturnAfSendMapper returnAfSendMapper;
//    @Autowired
//    private CpRpcService cpRpcService;
//    @Autowired
//    private ApplicationContext applicationContext;
//
//    @Reference(group = "hub", version = "1.0")
//    private NaiKaOrderCmd naiKaOrderCmd;
//
//    public void execute(JSONObject params) {
//        if (log.isDebugEnabled()) {
//            log.debug(LogUtil.format("CardAutoVoidStatusTask execute start"));
//        }
//
//        long start = System.currentTimeMillis();
//        final String taskTableName = "oc_b_order_naika_void";
//        Map<String, String> topMap = null;
//        Set<String> nodes = topMap.keySet();
//        if (CollectionUtils.isEmpty(nodes)) {
//
//        }
//        List<Future<Boolean>> results = new ArrayList<>();
//        for (String nodeName : nodes) {
//            results.add(doCardVoidPollExecutor.submit(new CardVoidTaskWithResult(nodeName, topMap.get(nodeName))));
//        }
//        //线程执行结果获取
//        for (Future<Boolean> futureResult : results) {
//            try {
//                if (log.isDebugEnabled()) {
//                    log.debug(LogUtil.format("DoCardVoidTask------>线程结果:{}"), futureResult.get().toString());
//                }
//            } catch (Exception e) {
//                log.error(LogUtil.format("DoCardVoidTask多线程获取InterruptedException异常：{}", "DoCardVoidTask"), Throwables.getStackTraceAsString(e));
//            }
//        }
//        if (log.isDebugEnabled()) {
//            log.debug(LogUtil.format("DoCardVoidTask 奶卡自动作废 useTime : {}"), (System.currentTimeMillis() - start));
//        }
//    }
//
//    @Transactional(rollbackFor = Exception.class)
//    public void doFail(List<Long> cardIdList, OcBOrderNaikaVoid naikaVoid) {
//        OcBOrderNaikaVoid updateNaikaVoid = new OcBOrderNaikaVoid();
//        OcBReturnAfSend updateOcBReturnAfSend = new OcBReturnAfSend();
//        // 作废成功后 修改奶卡表数据
//        naiKaMapper.updateNaiKaStatusByIdList(cardIdList, OmsOrderNaiKaStatusEnum.VOID_FAILED.getStatus());
//
//        // 修改作废订单、作废状态为 作废成功
//        updateNaikaVoid.setVoidStatus(NaikaVoidStatusEnum.VOID_FAIL.getStatus());
//        updateNaikaVoid.setId(naikaVoid.getId());
//        updateNaikaVoid.setModifieddate(new Date());
//        naikaVoidMapper.updateById(updateNaikaVoid);
//
//        // 修改已发货退款单
//        updateOcBReturnAfSend.setId(naikaVoid.getOcBReturnAfSendId());
//        updateOcBReturnAfSend.setModifieddate(new Date());
//        updateOcBReturnAfSend.setCardAutoVoid(CardAutoVoidEnum.VOID_ERROR.getCode());
//        returnAfSendMapper.updateById(updateOcBReturnAfSend);
//    }
//
//    @Transactional(rollbackFor = Exception.class)
//    public void doSuccess(List<Long> cardIdList, OcBOrderNaikaVoid naikaVoid) {
//        OcBOrderNaikaVoid updateNaikaVoid = new OcBOrderNaikaVoid();
//        OcBReturnAfSend updateOcBReturnAfSend = new OcBReturnAfSend();
//        // 作废成功后 修改奶卡表数据
//        naiKaMapper.updateNaiKaStatusByIdList(cardIdList, OmsOrderNaiKaStatusEnum.VOID_SUCCESS.getStatus());
//
//        // 修改作废订单、作废状态为 作废成功
//        updateNaikaVoid.setVoidStatus(NaikaVoidStatusEnum.VOID_SUCCESS.getStatus());
//        updateNaikaVoid.setId(naikaVoid.getId());
//        updateNaikaVoid.setModifieddate(new Date());
//        naikaVoidMapper.updateById(updateNaikaVoid);
//
//        // 修改已发货退款单
//        updateOcBReturnAfSend.setId(naikaVoid.getOcBReturnAfSendId());
//        updateOcBReturnAfSend.setModifieddate(new Date());
//        updateOcBReturnAfSend.setCardAutoVoid(CardAutoVoidEnum.VOID_SUCCESS.getCode());
//        returnAfSendMapper.updateById(updateOcBReturnAfSend);
//    }
//
//    class CardVoidTaskWithResult implements Callable<Boolean> {
//
//        private final String nodeName;
//
//        private final String taskTableName;
//
//        public CardVoidTaskWithResult(String nodeName, String taskTableName) {
//            this.nodeName = nodeName;
//            this.taskTableName = taskTableName;
//        }
//
//        @Override
//        public Boolean call() throws Exception {
//            List<OcBOrderNaikaVoid> ocBOrderNaikaVoidList = naikaVoidMapper.selectCardAutoVoid(nodeName, 100, taskTableName);
//            if (CollectionUtils.isEmpty(ocBOrderNaikaVoidList)) {
//                return true;
//            }
//            for (OcBOrderNaikaVoid naikaVoid : ocBOrderNaikaVoidList) {
//                // 根据奶卡作废表数据 执行奶卡作废
//                List<OcBOrderNaiKa> ocBOrderNaiKaList = naiKaMapper.selectNaiKaByOcBOrderIdAndItemId(naikaVoid.getOcBOrderId(), naikaVoid.getOcBOrderItemId());
//                if (CollectionUtils.isEmpty(ocBOrderNaiKaList)) {
//                    return true;
//                }
//                OcBOrder ocBOrder = ocBOrderMapper.selectByID(naikaVoid.getOcBOrderId());
//                CpCPlatform cpCPlatform = cpRpcService.selectCpcPlatformById(Long.valueOf(ocBOrder.getPlatform()));
//                List<String> cardCodeList = ocBOrderNaiKaList.stream().map(OcBOrderNaiKa::getCardCode).collect(Collectors.toList());
//                List<Long> cardIdList = ocBOrderNaiKaList.stream().map(OcBOrderNaiKa::getId).collect(Collectors.toList());
//
//                NaiKaReissueRequest request = new NaiKaReissueRequest();
//                request.setBillNo(ocBOrder.getBillNo());
//                request.setSourceCode(ocBOrder.getSourceCode());
//                request.setPlatformCode(cpCPlatform.getEcode());
//                // 目前此字段无任何含义
//                request.setType(1);
//                request.setRemark("奶卡作废");
//                request.setCardList(cardCodeList);
//                request.setShopCode(ocBOrder.getCpCShopEcode());
//                ValueHolderV14 valueHolderV14;
//                Boolean success = true;
//                try {
//                    valueHolderV14 = naiKaOrderCmd.orderReissue(request);
//                    if (!valueHolderV14.isOK()) {
//                        success = false;
//                    }
//                } catch (Exception e) {
//                    log.error(LogUtil.format("DoCardVoidTask作废失败：{}", "DoCardVoidTask"), Throwables.getStackTraceAsString(e));
//                    success = false;
//                }
//                if (success) {
//                    doSuccess(cardIdList, naikaVoid);
//                } else {
//                    doFail(cardIdList, naikaVoid);
//                }
//
//            }
//            return true;
//        }
//    }
//}
