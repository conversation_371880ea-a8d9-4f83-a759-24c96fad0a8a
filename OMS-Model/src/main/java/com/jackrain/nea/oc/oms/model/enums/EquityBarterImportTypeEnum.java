package com.jackrain.nea.oc.oms.model.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * @author: lijin
 * @create: 2024-06-11
 * @description: 商品效期策略头明细导入类型
 **/
public enum EquityBarterImportTypeEnum {
    COVER(1, "覆盖"),
    ADD(2, "追加");
    @Getter
    private Integer key;
    @Getter
    private String desc;

    EquityBarterImportTypeEnum(Integer key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public static EquityBarterImportTypeEnum getByKey(String key) {
        for (EquityBarterImportTypeEnum current : values()) {
            if (Objects.equals(current.getKey(), key)) {
                return current;
            }
        }
        return null;
    }

    public static EquityBarterImportTypeEnum getByDesc(String desc) {
        for (EquityBarterImportTypeEnum current : values()) {
            if (Objects.equals(current.getDesc(), desc)) {
                return current;
            }
        }
        return null;
    }
}
