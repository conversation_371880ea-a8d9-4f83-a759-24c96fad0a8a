package com.jackrain.nea.oc.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OrderOutCmd;
import com.jackrain.nea.oc.oms.services.OrderOutService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @program: R3-OC-OMS-V1.4
 * @author: Lijp
 * @create: 2019-07-25 14:25
 */
@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OrderOutImpl extends CommandAdapter implements OrderOutCmd {

    @Autowired
    private OrderOutService orderOutService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return orderOutService.orderListQuery(session);
    }
}
