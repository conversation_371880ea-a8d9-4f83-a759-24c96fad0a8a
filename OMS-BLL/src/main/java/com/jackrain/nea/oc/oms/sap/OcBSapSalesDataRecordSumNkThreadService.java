package com.jackrain.nea.oc.oms.sap;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.constant.SapSalesDateConstant;
import com.jackrain.nea.oc.oms.mapper.MilkCardAmountOffsetOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBSapSalesDataGatherMapper;
import com.jackrain.nea.oc.oms.model.table.MilkCardAmountOffsetOrder;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * Description: 多线程调用奶卡冲抵单进行汇总
 *
 * @Author: guo.kw
 * @Since: 2022/9/14
 * create at: 2022/9/14 16:21
 */
@Slf4j
@Component
public class OcBSapSalesDataRecordSumNkThreadService {

    @Autowired
    private MilkCardAmountOffsetOrderMapper milkCardAmountOffsetOrderMapper;

    @Autowired
    private OcBSapSalesDataRecordSumNkService ocBSapSalesDataRecordSumNkService;

    @Autowired
    private OcBSapSalesDataGatherMapper ocBSapSalesDataGatherMapper;
    private static final SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");

    @NacosValue(value = "${lts.OcBSapSalesDataRecordSumNkService.range:100}", autoRefreshed = true)
    public Integer range;

    /**
     * 获取当前线程所有信息之后在进行后续汇总
     *
     * @return
     */
    public ValueHolderV14 executeThread() {
        ValueHolderV14 v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "奶卡冲抵单汇总定时任务成功！");
        String threadPoolName = "R3_OMS_MILK_CARD_AMOUNT_OFFSET_ORDER_%d";
        log.info(LogUtil.format("OcBSapSalesDataRecordSumNkService---->奶卡冲抵单汇总配置条数:{}", "OcBSapSalesDataRecordSumNkService"), range * 24);
        try {
            long start = System.currentTimeMillis();
            final String taskTableName = "milk_card_amount_offset_order";
            List<MilkCardAmountOffsetOrder> milkCardAmountOffsetOrdersObjects = milkCardAmountOffsetOrderMapper.selectSalesDataIdList(taskTableName, range * 24, simpleDateFormat.format(new Date()));
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("OcBSapSalesDataRecordSumNkService 当前线程池执行完毕 useTime:{}", "OcBSapSalesDataRecordSumNkService", (System.currentTimeMillis() - start)));
            }
            long startInfo = System.currentTimeMillis();
            // 当前所有节点执行完之后，如果有数据则继续执行下一次线程，如果所有节点执行完数据为空则更新所有汇总中的状态为已汇总
            if (CollectionUtils.isNotEmpty(milkCardAmountOffsetOrdersObjects)) {
                List<List<MilkCardAmountOffsetOrder>> partition = Lists.partition(milkCardAmountOffsetOrdersObjects, SapSalesDateConstant.QUERY_MAX_SIZE);
                //分批次执行每次执行一千
                for (List<MilkCardAmountOffsetOrder> infoList : partition) {
                    ocBSapSalesDataRecordSumNkService.execute(infoList);
                }
                milkCardAmountOffsetOrdersObjects.clear();
                this.executeThread();
            } else {
                List<Long> ids = ocBSapSalesDataGatherMapper.selectInfoBySumType();
                log.debug(LogUtil.format("OcBSapSalesDataRecordSumNkService ocBSapSalesDataGatherIds size:{}", "OcBSapSalesDataRecordSumNkService ocBSapSalesDataGatherIds", ids.size()));
                if (CollectionUtils.isNotEmpty(ids)) {
                    ocBSapSalesDataRecordSumNkService.updateSaleSumStatus(ids);
                }
            }
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("OcBSapSalesDataRecordSumNkService 奶卡冲抵单汇总任务完成 useTime:{}", "OcBSapSalesDataRecordSumNkService", (System.currentTimeMillis() - startInfo)));
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("OcBSapSalesDataRecordSumNkService.Execute Error：{}", threadPoolName, "OcBSapSalesDataRecordSumNkService"), Throwables.getStackTraceAsString(ex));
            v14.setMessage(Throwables.getStackTraceAsString(ex));
            v14.setCode(ResultCode.FAIL);
            return v14;
        } finally {
            /*if (executor != null) {
                executor.shutdown();
            }*/
        }
        return v14;
    }
}
