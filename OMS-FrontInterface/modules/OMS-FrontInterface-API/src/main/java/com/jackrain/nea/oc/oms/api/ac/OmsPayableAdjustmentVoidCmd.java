package com.jackrain.nea.oc.oms.api.ac;

import com.jackrain.nea.sys.Command;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;

/**
 * <AUTHOR> 陈俊明
 * @since : 2019-03-22
 * create at : 2019-03-22 13:29
 */
public interface OmsPayableAdjustmentVoidCmd extends Command {
    /**
     * 根据丢件单的id作废取消
     *
     * @param objId
     * @return
     */
    ValueHolder adjustmentVoidById(String objId, User user);
}
