package com.jackrain.nea.oc.oms.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * @Author: 黄世新
 * @Date: 2022/6/23 下午3:52
 * @Version 1.0
 */
@Data
public class ExpiryDateItem implements Serializable {

    @JSO<PERSON>ield(name = "sku_code")
    private String skuCode;

    @JSONField(name = "sku_title")
    private String skuTitle;

    @JSONField(name = "qty")
    private BigDecimal qty;

    @J<PERSON><PERSON>ield(name = "relation_ids")
    private Set<Long> relationIds;

    @J<PERSON><PERSON>ield(name = "item_ids")
    private Set<Long> itemIds;

    @JSONField(name = "appoint_type")
    private Integer appointType;

    @J<PERSON><PERSON>ield(name = "start_date_day")
    private String startDateDay;

    @J<PERSON>NField(name = "end_date_day")
    private String endDateDay;

    @JSONField(name = "date_num")
    private List<DateNum> dateNums;

    @Data
    public static class DateNum implements Serializable{

        @J<PERSON><PERSON>ield(name = "date")
        private String date;

        @JSONField(name = "num")
        private BigDecimal num;

    }

}
