//package com.jackrain.nea.oc.oms.task.oms2third;
//
//import com.alibaba.fastjson.JSONObject;
//import com.jackrain.nea.task.IR3Task;
//import com.jackrain.nea.task.RunTaskResult;
//import com.jackrain.nea.third.model.OmsThirdResult;
//import com.jackrain.nea.third.service.IOmsThird;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.util.ArrayList;
//import java.util.List;
//
///**
// * @Author: 黄世新
// * @Date: 2022/1/7 上午11:30
// * @Version 1.0
// */
//@Slf4j
//@Component
//public class OmsToThirdTask implements IR3Task {
//
//
//    @Autowired
//    private List<IOmsThird> thirdHashMap;
//
//    @Override
//    public RunTaskResult execute(JSONObject params) {
//        RunTaskResult result = new RunTaskResult();
//        List<OmsThirdResult> runTaskResultList = new ArrayList();
//        if (CollectionUtils.isEmpty(thirdHashMap)) {
//            result.setSuccess(true);
//            result.setMessage("无需要执行的任务");
//            return result;
//        }
//        for (IOmsThird iOmsThird : thirdHashMap) {
//            if (!iOmsThird.isClose()) {
//                continue;
//            }
//            OmsThirdResult start = iOmsThird.start(params);
//            runTaskResultList.add(start);
//        }
//        if (CollectionUtils.isEmpty(runTaskResultList)) {
//            result.setSuccess(true);
//            result.setMessage("无需要执行的任务1");
//            return result;
//        }
//        StringBuilder sb = new StringBuilder();
//        boolean flag = true;
//        for (OmsThirdResult omsThirdResult : runTaskResultList) {
//            boolean success = omsThirdResult.isSuccess();
//            if (!success){
//                flag = false;
//            }
//            sb.append(omsThirdResult.getMessage());
//        }
//        result.setSuccess(flag);
//        result.setMessage(sb.toString());
//        return result;
//    }
//}
