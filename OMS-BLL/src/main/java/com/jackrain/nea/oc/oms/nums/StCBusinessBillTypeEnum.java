package com.jackrain.nea.oc.oms.nums;

import com.jackrain.nea.exception.NDSException;
import lombok.Getter;

import java.util.Objects;

/**
 * @program: r3-oc-oms
 * @description: 业务单据类型
 * @author: caomalai
 * @create: 2022-07-14 15:19
 **/
public enum StCBusinessBillTypeEnum {
    ORDER(1,"订单"),
    RETURN(2,"退单"),
    REFUND(3,"退款单");

    @Getter
    private Integer value;

    @Getter
    private String desc;

    StCBusinessBillTypeEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static String getDescByValue(Integer value) {
        StCBusinessBillTypeEnum[] values = StCBusinessBillTypeEnum.values();
        for (StCBusinessBillTypeEnum nodeEnum : values) {
            if (Objects.equals(nodeEnum.value, value)) {
                return nodeEnum.desc;
            }
        }
        throw new NDSException("错误的节点配置:" + value);
    }
}
