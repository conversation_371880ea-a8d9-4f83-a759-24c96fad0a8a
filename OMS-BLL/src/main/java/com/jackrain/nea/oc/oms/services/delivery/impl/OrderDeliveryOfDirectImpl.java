package com.jackrain.nea.oc.oms.services.delivery.impl;

import cn.hutool.core.util.ObjectUtil;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpLogisticsItem;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ip.model.others.JdDirectLogisticsSendRequest;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.services.OrderPlatformDeliveryService;
import com.jackrain.nea.oc.oms.services.delivery.OrderDeliveryCmd;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.st.service.OmsStCShopStrategyService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: 秦雄飞
 * @time: 2022/10/17 11:11
 * @description: 京东厂直平台发货实现类
 */
@Slf4j
@Component
public class OrderDeliveryOfDirectImpl implements OrderDeliveryCmd {

    @Autowired
    private OrderPlatformDeliveryService orderPlatformDeliveryService;

    @Autowired
    private OmsStCShopStrategyService shopStrategyService;

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private IpRpcService ipRpcService;

    @Override
    public boolean deliveryDeal(OcBOrderRelation ocBOrderRelation, List<String> tips) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("京东厂直平台发货服务,订单id为={}",
                    "京东厂直平台发货服务", ocBOrderRelation.getOrderId()), ocBOrderRelation.getOrderId());
        }
        List<OcBOrderItem> orderItemList = ocBOrderRelation.getOrderItemList();
        OcBOrder orderInfo = ocBOrderRelation.getOrderInfo();
        orderItemList = orderItemList.stream().filter(s -> StringUtils.isNotEmpty(s.getOoid())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderItemList)) {
            return orderPlatformDeliveryService.updateOrderAfterPlatDeliverySuccess(orderInfo);
        }
        List<JdDirectLogisticsSendRequest> jdDirectLogisticsSendRequests = buildRequest(ocBOrderRelation);
        ValueHolderV14 v14 = ipRpcService.jingdongDirectDelivery(jdDirectLogisticsSendRequests);
        if (v14 == null || v14.getCode() == ResultCode.FAIL) {
            throw new NDSException(v14 != null ? v14.getMessage() : "发货失败");
        }
        return true;
    }

    private List<JdDirectLogisticsSendRequest> buildRequest(OcBOrderRelation ocBOrderRelation){
        JdDirectLogisticsSendRequest request = new JdDirectLogisticsSendRequest();

        CpLogisticsItem platformLogistic = cpRpcService.getPlatformLogistic(ocBOrderRelation.getOrderInfo().getCpCLogisticsId(),
                Long.valueOf(ocBOrderRelation.getOrderInfo().getPlatform()));
        OcBOrder ocBOrder = ocBOrderRelation.getOrderInfo();
        if (Objects.isNull(platformLogistic)) {
            throw new NDSException("物流公司编码查询为空");
        }
        // 店铺策略
        StCShopStrategyDO stCShopStrategyDO = shopStrategyService.selectOcStCShopStrategy(ocBOrder.getCpCShopId());
        if (ObjectUtil.isNotNull(stCShopStrategyDO) && ObjectUtil.isNotNull(stCShopStrategyDO.getAddressId())) {
            // 发货地址ID
            request.setAddressId(stCShopStrategyDO.getAddressId());
        }
        // 业务ID，OMS辨别请求所用
        request.setId(String.valueOf(ocBOrderRelation.getOrderId()));
        // 客单编号
        request.setCustomOrderId(Long.valueOf(ocBOrder.getTid()));
        // 运单号
        request.setShipNo(ocBOrder.getExpresscode());
        // 承运商名称
        request.setCarrierBusinessName(platformLogistic.getCpCLogisticsEname());
        // 承运商Id
        request.setCarrierId(Integer.valueOf(platformLogistic.getCpCLogisticsEcode()));
        // 预计到货时间
        request.setEstimateDate(DateUtils.addDays(new Date(), NumberUtils.INTEGER_ONE));
        // 卖家昵称
        request.setSellerNick(ocBOrder.getCpCShopSellerNick());

        return Collections.singletonList(request);
    }
}
