package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Set;

/**
 * <AUTHOR>
 * date ：Created in 18:08 2020/1/1
 * description ：
 * @ Modified By：
 */
@Api(value = "StStrategyCacheCtrl", tags = "策略缓存")
@RestController
@Slf4j
public class StStrategyCacheCtrl {
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @ApiOperation(value = "策略缓存清理")
    @RequestMapping("/api/cs/st/strategy/cache/clear")
    public JSONObject clearStStrategyCacheClear() {
        JSONObject retObj = new JSONObject();
        long stime = System.currentTimeMillis();
        Set<String> keys = stringRedisTemplate.keys("*st:*");
        if (CollectionUtils.isEmpty(keys)) {
            retObj.put("code", -1);
            retObj.put("message", "keys is empty");
            return retObj;
        }
        System.out.println(keys);
        long etime1 = System.currentTimeMillis();
        try {
            stringRedisTemplate.delete(keys);
            retObj.put("code", 0);
            retObj.put("keys", keys);
            return retObj;
        } catch (Exception e) {
            retObj.put("code", -1);
            retObj.put("message", e.getMessage());
            return retObj;
        }
    }

    @ApiOperation(value = "策略缓存redisKey获取")
    @RequestMapping("/api/cs/st/strategy/cache/redisKeys/get")
    public JSONObject getStStrategyCacheRedisKey() {
        JSONObject retObj = new JSONObject();
        long stime = System.currentTimeMillis();
        Set<String> keys = stringRedisTemplate.keys("*st:*");
        long etime1 = System.currentTimeMillis();
        retObj.put("code", 0);
        retObj.put("keys", keys);
        return retObj;
    }
}