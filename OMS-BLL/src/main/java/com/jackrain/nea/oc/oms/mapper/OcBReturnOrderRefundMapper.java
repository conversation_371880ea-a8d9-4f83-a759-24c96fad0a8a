package com.jackrain.nea.oc.oms.mapper;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefundExt;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.Update;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.jdbc.SQL;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

@Component
@Mapper
public interface OcBReturnOrderRefundMapper extends ExtentionMapper<OcBReturnOrderRefund> {

    @Select("select * from OC_B_RETURN_ORDER_REFUND where oc_b_return_order_id =#{id} and gift_type is null")
    List<OcBReturnOrderRefund> selectByOcOrderIdNoGift(@Param("id") Long id);

    @Select("SELECT SUM(REAL_SKU_QTY) FROM OC_B_RETURN_ORDER_REFUND WHERE OC_B_RETURN_ORDER_ID = #{id}")
    BigDecimal selectSumInQtyByOcOrderId(@Param("id") Long id);

    @Select("SELECT SUM(oi.qty) qty FROM oc_b_refund_in_product_item oi LEFT JOIN oc_b_refund_in o ON oi.oc_b_refund_in_id = o.id WHERE o.orig_order_no = #{id}")
    BigDecimal selectSumInQtyByOcRefundInItem(@Param("id") Long id);

    //**************整合diamante begin
    @Update("UPDATE oc_b_return_order_refund  SET qty_refund=#{qtyRefund} WHERE id=#{id} and oc_b_return_order_id=#{ocBreturnOrderId}")
    int updateByID(@Param("qtyRefund") BigDecimal qtyExchange, @Param("id") Long id, @Param("ocBreturnOrderId") Long ocBreturnOrderId);


    @Select("select * from oc_b_return_order_refund where oc_b_return_order_id =#{id} limit #{index},#{count} ")
    List<OcBReturnOrderRefundExt> selectByReturnId(@Param("id") String id, @Param("count") Integer count, @Param("index") int startIndex);

    @Select("select count(1) from oc_b_return_order_refund where oc_b_return_order_id =#{id}")
    Integer selectRefundCount(@Param("id") String id);


    //**************end
    @Select("select ps_c_sku_id from oc_b_return_order_refund where oc_b_return_order_id =#{id}")
    List<Long> selectSkuIdById(@Param("id") Long id);

    @Select("select ps_c_pro_id from oc_b_return_order_refund where oc_b_return_order_id =#{id} ")
    Long selectProId(@Param("id") Long id);

    @Select("select qty_refund,qty_in from oc_b_return_order_refund where ps_c_sku_id =#{skuId}")
    JSONObject selectQtyRefund(@Param("skuId") Long skuId);

    @Select("select id ,ps_c_pro_ecode,sku_spec,amt_refund,qty_in,qty_refund from oc_b_return_order_refund where oc_b_return_order_id =#{id}")
    List<JSONObject> selectSomeFile(@Param("id") Long id);

    @Select("select * from OC_B_RETURN_ORDER_REFUND where oc_b_return_order_id =#{id} ")
    List<OcBReturnOrderRefund> selectByOcOrderId(@Param("id") Long id);

    @Select("select * from OC_B_RETURN_ORDER_REFUND where oc_b_return_order_id in (${idsStr}) ")
    List<OcBReturnOrderRefund> selectByOcOrderIdsStr(@Param("idsStr") String idsStr);

    /**
     * 根据主表id查询 对应明细表的集合
     *
     * @param id 退换货订单id
     * @return
     * @20200831 bug#20200831 AC支付宝对账加过滤未入库的明细
     */
    @Select("select * from OC_B_RETURN_ORDER_REFUND where oc_b_return_order_id =#{id} and qty_in > 0 ")
    List<OcBReturnOrderRefund> selectQtyInLgZeroByOcOrderId(@Param("id") Long id);

    @Select("<script> "
            + "SELECT * FROM OC_B_RETURN_ORDER_REFUND WHERE qty_in > 0 and oc_b_return_order_id "
            + "in <foreach item='item' index='index' collection='ids' "
            + "open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<OcBReturnOrderRefund> selectQtyInLgZeroByOcOrderIds(@Param("ids") List<Long> ids);

    @SelectProvider(type = SqlProvider.class, method = "selectReturnRefundByPIds")
    List<OcBReturnOrderRefund> selectReturnRefundByPIds(@Param("pIds") String pIds);

    @SelectProvider(type = SqlProvider.class, method = "selectReturnRefundSomeFile")
    List<JSONObject> selectReturnRefundSomeFile(@Param("pIds") String pIds);

    @Select("SELECT COUNT(1) FROM  OC_B_RETURN_ORDER_REFUND  WHERE OC_B_RETURN_ORDER_ID=#{id} AND ID=#{itemId} AND ISACTIVE='Y'")
    int selectRecord(@Param("id") Long id, @Param("itemId") Long itemId);

    /**
     * 更新退单信息
     *
     * @param jsonObject
     * @return
     */
    @UpdateProvider(type = OcBReturnOrderRefundMapper.UpdateRecord.class, method = "updateSql")
    int updateRecord(JSONObject jsonObject);

    @Select("select ps_c_sku_id from oc_b_return_order_refund where oc_b_return_order_id =#{id}")
    List<Long> selectListSkuIdById(@Param("id") Long id);

    @Select("SELECT `ID`,PS_C_SKU_ID,PS_C_SKU_ECODE,QTY_REFUND,QTY_IN,OC_B_ORDER_ID,OC_B_ORDER_ITEM_ID,"
            + "OC_B_RETURN_ORDER_ID FROM OC_B_RETURN_ORDER_REFUND WHERE OC_B_RETURN_ORDER_ID =#{returnId}")
    List<OcBReturnOrderRefund> selectRefundQtyByReturnId(@Param("returnId") Long returnId);

    /**
     * 1.3
     * 根据退换货订单编号,查询退货商品信息
     *
     * @param oIds     退换货订单ids
     * @param isActive 是否启用
     * @return 退货商品信息, id,OC_B_RETURN_ORDER_ID, skuId,qtyIn,
     */
    @SelectProvider(type = SqlProvider.class, method = "queryReturnOrderRefundByoIds")
    List<OcBReturnOrderRefund> queryReturnOrderRefundByoIds(@Param("oIds") String oIds,
                                                            @Param("isActive") String isActive);

    /**
     * 根据退换货订单编号,查询退货商品信息
     *
     * @param oId      退换货订单id
     * @param isActive 是否启用
     * @return List<OcBReturnOrderRefund>
     */
    @Select("SELECT * FROM OC_B_RETURN_ORDER_REFUND WHERE OC_B_RETURN_ORDER_ID=#{oId}  AND ISACTIVE=#{isActive}")
    List<OcBReturnOrderRefund> queryRtnOrderRefundByoId(@Param("oId") Long oId, @Param("isActive") String isActive);

    /**
     * 根据退换货订单编号,查询退货商品信息
     *
     * @param returnOrderId 退换货订单id
     * @return List<OcBReturnOrderRefundExt>
     */
    @Select("SELECT * FROM OC_B_RETURN_ORDER_REFUND WHERE OC_B_RETURN_ORDER_ID=#{returnOrderId}  AND ISACTIVE='Y'")
    List<OcBReturnOrderRefundExt> selectListByReturnOrderId(@Param("returnOrderId") Long returnOrderId);

    /**
     * OC_B_RETURN_ORDER_REFUND
     * 更新退货明细条码的,入库数量,金额
     *
     * @param ocBReturnOrderRefund OcBReturnOrderRefund
     * @return int
     */
    @Update("UPDATE OC_B_RETURN_ORDER_REFUND SET QTY_IN=#{qtyIn}, AMT_REFUND=#{amtRefund} "
            + "WHERE OC_B_RETURN_ORDER_ID=#{ocBReturnOrderId} AND `ID`=#{id}")
    int updateReturnOdrRefundQtyAndAmt(OcBReturnOrderRefund ocBReturnOrderRefund);


    @Update("update oc_b_return_order_refund set qty_match=#{qtyMatch}, qty_in=#{qtyIn}, amt_refund=#{amtRefund} "
            + "where oc_b_return_order_id=#{ocBReturnOrderId} and `id`=#{id}")
    int updateMatchAndInQtyAndAmt(OcBReturnOrderRefund ocBReturnOrderRefund);

    @Update("update oc_b_return_order_refund set qty_match=#{qtyMatch}"
            + " where oc_b_return_order_id=#{ocBReturnOrderId} and `id`=#{id}")
    int updateMatchQtyWithOutInAmt(OcBReturnOrderRefund ocBReturnOrderRefund);

    @Update("UPDATE OC_B_RETURN_ORDER_REFUND SET QTY_MATCH = #{matchNum} WHERE OC_B_RETURN_ORDER_ID=#{ocBReturnOrderId} AND ID=#{id}")
    int updateMatchNum(@Param("matchNum") Long matchNum, @Param("ocBReturnOrderId") Long returnId, @Param("id") Long id);

    @Select("select * from OC_B_RETURN_ORDER_REFUND where oc_b_return_order_id =#{id} and oid = #{oid}")
    List<OcBReturnOrderRefund> selectReturnOrderById(@Param("id") Long id, @Param("oid") String oid);

    @Select("select * from OC_B_RETURN_ORDER_REFUND where oc_b_return_order_id =#{id} and oc_b_order_id = #{orderId}")
    List<OcBReturnOrderRefund> selectReturnOrderByOrderId(@Param("id") Long id, @Param("orderId") Long orderId);

    @Select("<script> "
            + "SELECT * FROM OC_B_RETURN_ORDER_REFUND WHERE oc_b_return_order_id "
            + "in <foreach item='item' index='index' collection='ids' "
            + "open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<OcBReturnOrderRefund> selectOcBReturnOrderRefundByOrderIds(@Param("ids") List<Long> ids);

    @Update("<script> "
            + "update OC_B_RETURN_ORDER_REFUND  set PRODUCT_MARK ='0' where oc_b_return_order_id = #{returnId} and id "
            + "in <foreach item='item' index='index' collection='retunRefundIds' "
            + "open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    Integer updateGoodRemake(@Param("returnId") Long returnId, @Param("retunRefundIds") List<Long> retunRefundIds);

    @Delete("delete from OC_B_RETURN_ORDER_REFUND where oc_b_return_order_id=#{orderId}")
    Integer deleteItemByorderId(@Param("orderId") Long orderId);

    @Update("<script> "
            + "update OC_B_RETURN_ORDER_REFUND  set refund_status = #{returnStatus} where refund_bill_no = #{refundId} and oc_b_return_order_id "
            + "in <foreach item='item' index='index' collection='returnIds' "
            + "open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    Integer updateReturnOrderRefund(@Param("returnIds") List<Long> returnIds, @Param("returnStatus") String returnStatus, @Param("refundId") String refundId);

    @Select("<script> "
            + "SELECT * FROM OC_B_RETURN_ORDER_REFUND WHERE isactive = 'Y' and tid "
            + "in <foreach item='item' index='index' collection='tids' "
            + "open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<OcBReturnOrderRefund> selectByTids(@Param("tids") List<String> tids);

    class UpdateRecord {

        public String updateSql(JSONObject map) {
            return new SQL() {
                {
                    UPDATE("oc_b_return_order_refund");
                    for (String key : map.keySet()) {
                        if (!("id".equalsIgnoreCase(key) || "oc_b_return_order_id".equalsIgnoreCase(key))) {
                            SET(key + "= #{" + key + "}");
                        }
                    }
                    if (map.getLong("id") == null) {
                        WHERE("oc_b_return_order_id = #{oc_b_return_order_id}");
                    } else {
                        WHERE("oc_b_return_order_id = #{oc_b_return_order_id} and id = #{id}");
                    }
                }
            }.toString();
        }
    }

    /**
     * sql
     */
    class SqlProvider {

        /**
         * 根据退换货订单,查询退货商品信息
         *
         * @param oIds     退换货订单ids
         * @param isActive 是否启用
         * @return 退货商品信息, // `ID`, OC_B_RETURN_ORDER_ID, PS_C_SKU_ID,QTY_IN,QTY_REFUND, REFUND_AMT
         */
        public String queryReturnOrderRefundByoIds(@Param("oIds") String oIds, @Param("isActive") String isActive) {
            StringBuilder sb = new StringBuilder();
            sb.append("SELECT * "
                    + "FROM OC_B_RETURN_ORDER_REFUND WHERE ");
            sb.append("OC_B_RETURN_ORDER_ID IN (");
            sb.append(oIds);
            sb.append(" )");
            sb.append(" AND ISACTIVE ='" + isActive + "'");
            return sb.toString();
        }

        public String selectReturnRefundByPIds(@Param("pIds") String pIds) {
            StringBuilder sb = new StringBuilder();
            sb.append("select * from OC_B_RETURN_ORDER_REFUND where oc_b_return_order_id in (");
            sb.append(pIds);
            sb.append(" )");
            return sb.toString();
        }

        public String selectReturnRefundSomeFile(@Param("pIds") String pIds) {
            StringBuilder sb = new StringBuilder();
            sb.append("select oc_b_return_order_id,id,ps_c_pro_ecode,sku_spec,amt_refund,qty_in,qty_refund ");
            sb.append(" from oc_b_return_order_refund where oc_b_return_order_id in (");
            sb.append(pIds);
            sb.append(" )");
            return sb.toString();
        }


    }


}
