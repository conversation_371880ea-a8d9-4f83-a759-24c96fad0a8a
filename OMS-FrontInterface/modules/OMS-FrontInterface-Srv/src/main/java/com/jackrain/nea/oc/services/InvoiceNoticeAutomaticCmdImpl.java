package com.jackrain.nea.oc.services;

import com.jackrain.nea.oc.oms.api.InvoiceNoticeAutomaticCmd;
import com.jackrain.nea.oc.oms.services.InvoiceNoticeAutomaticService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @program: R3-OC-OMS-V1.4
 * @author: huang.z<PERSON><PERSON>
 * @create: 2019-08-28 21:30
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class InvoiceNoticeAutomaticCmdImpl implements InvoiceNoticeAutomaticCmd {

    @Resource
    private InvoiceNoticeAutomaticService service;

    @Override
    public ValueHolderV14 createInvoiceNotice(Long ocBOrderId, User user) {
        return service.createInvoiceNotice(ocBOrderId, user);
    }
}
