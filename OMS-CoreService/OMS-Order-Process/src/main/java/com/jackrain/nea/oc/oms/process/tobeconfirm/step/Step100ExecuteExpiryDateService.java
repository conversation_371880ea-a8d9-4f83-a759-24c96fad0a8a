package com.jackrain.nea.oc.oms.process.tobeconfirm.step;

import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderParam;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.resources.OrderOccupyStatus;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.services.OmsExpiryDateStService;
import com.jackrain.nea.oc.oms.services.OmsOrderDifferenPriceService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.oc.oms.util.SplitMessageUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Step(order = 100, description = "执行商品效期策略")
@Slf4j
@Component
public class Step100ExecuteExpiryDateService extends BaseTobeConfirmedProcessStep implements IOmsOrderProcessStep<OcBOrderRelation> {

    @Autowired
    private OmsExpiryDateStService omsExpiryDateStService;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private OmsOrderDifferenPriceService omsOrderDifferenPriceService;

    @Override
    public ProcessStepResult<OcBOrderRelation> startProcess(OcBOrderRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        OcBOrder ocBOrder = orderInfo.getOrderInfo();
        try {
            OcBOrderParam param = new OcBOrderParam();
            param.setOcBOrder(orderInfo.getOrderInfo());
            param.setOrderItemList(orderInfo.getOrderItemList());
            boolean flag = omsExpiryDateStService.expiryDateStService(param, operateUser, "自动");
            if (flag) {
                List<OcBOrderItem> orderItems = ocBOrderItemMapper.selectOrderItemListOccupy(ocBOrder.getId());
                orderInfo.setOrderItemList(orderItems);
            }
            return new ProcessStepResult<>(StepStatus.SUCCESS);
        } catch (Exception e) {
            log.error(LogUtil.format("效期策略匹配失败:{}", ocBOrder.getId(), "效期策略匹配失败"), Throwables.getStackTraceAsString(e));
            OcBOrder errorOrderInfo = new OcBOrder();
            errorOrderInfo.setId(orderInfo.getOrderId());
            errorOrderInfo.setSysremark(SplitMessageUtil.splitMesssage("效期策略匹配失败"));
            errorOrderInfo.setOccupyStatus(OrderOccupyStatus.STATUS_50);
            omsOrderService.updateOrderInfo(errorOrderInfo);
            omsToBeConfirmedTaskService.updateToBeConfirmedTaskByOrderId(orderInfo.getOrderId());
            return new ProcessStepResult<>(StepStatus.FAILED, "效期策略匹配失败");
        }
    }
}
