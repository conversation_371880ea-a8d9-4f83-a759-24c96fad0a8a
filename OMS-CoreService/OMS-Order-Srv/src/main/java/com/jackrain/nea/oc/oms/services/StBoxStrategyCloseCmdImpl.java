package com.jackrain.nea.oc.oms.services;

import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.jackrain.nea.annotation.OmsOperationLog;
import com.jackrain.nea.cpext.utils.ValueHolderUtils;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.StBoxStrategyCloseCmd;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/2/2
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oc-core")
public class StBoxStrategyCloseCmdImpl extends CommonCommandAdapter implements StBoxStrategyCloseCmd {

    @Autowired
    private StCBoxStrategyService boxStrategyService;

    @Override
    @OmsOperationLog(mainTableName = "ST_C_BOX_STRATEGY", operationType = "VOID")
    public ValueHolder execute(QuerySession session) throws NDSException {

        List<Long> objIds = getObjIds(session);

        if (CollectionUtils.isEmpty(objIds)) {
            return ValueHolderUtils.fail("请选择停用数据");
        }
        log.info("箱型策略批量停用={}", JSON.toJSONString(objIds));

        User user = session.getUser();

        if (boxStrategyService.updateActive(user, objIds, "N")) {
            return ValueHolderUtils.success("停用成功");
        }

        return ValueHolderUtils.fail("停用失败");
    }
}
