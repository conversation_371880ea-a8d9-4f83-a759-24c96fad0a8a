package com.jackrain.nea.ac.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.mapper.ac.AcFInvoiceApplyItemMapper;
import com.jackrain.nea.oc.oms.mapper.ac.AcFInvoiceApplyMapper;
import com.jackrain.nea.oc.oms.model.constant.InvoiceConst;
import com.jackrain.nea.oc.oms.model.table.AcFInvoiceApply;
import com.jackrain.nea.oc.oms.model.table.AcFInvoiceApplyItem;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * @ClassName InvoiceApplyService
 * @Description
 * @Date 2022/9/18 下午1:54
 * @Created by wuhang
 */
@Slf4j
@Service
public class InvoiceApplyService {

    @Autowired
    private AcFInvoiceApplyMapper mapper;

    @Autowired
    private AcFInvoiceApplyItemMapper applyItemMapper;

    public ValueHolder failCountReset(JSONObject param, User user) {
        log.info("---| invoice | red rush param : " + param + ", user : " + JSON.toJSONString(user));
        ValueHolder vh = new ValueHolder();
        JSONArray ids = param.getJSONArray("ids");
        JSONArray errorMessage = new JSONArray(); // 错误信息
        int fail = 0;
        for(int i = 0; i < ids.size(); i++){
            Long id = ids.getLong(i);
            JSONObject jsonObject = new JSONObject();
            AcFInvoiceApply apply = mapper.selectById(id);
            String msg = checkFailCountReset(apply);
            if (StringUtils.isNotBlank(msg)) {
                vh.put("code", ResultCode.FAIL);
                vh.put("message", msg);
                jsonObject.put("code", ResultCode.FAIL);
                jsonObject.put("message", msg);
                jsonObject.put("objid", id);
                errorMessage.add(jsonObject);
                fail++;
                continue;
            }
            UpdateWrapper<AcFInvoiceApply> update = new UpdateWrapper<>();
            update.lambda().eq(AcFInvoiceApply::getId,id).set(AcFInvoiceApply::getFailCount,0);
            mapper.update(null,update);
            vh.put("code", ResultCode.SUCCESS);
            vh.put("message", "重置次数成功");
        }
        vh.put("data", errorMessage);
        if (ids.size() == 1) {
            return vh;
        }
        if (fail == 0) {
            vh.put("code", ResultCode.SUCCESS);
            vh.put("message", "重置次数成功");
        } else {
            vh.put("code", ResultCode.FAIL);
            vh.put("message", "重置次数成功" + (ids.size() - fail) + "条，失败" + fail + "条数据");
        }
        return vh;
    }

    private String checkFailCountReset(AcFInvoiceApply apply) {
        return "";
    }

    /**
     * 发票取消,红冲,作废后将原申请单明细的tid加上后缀,防止无法进行二次申请
     * @param applyId
     */
    public void updateInvoiceApplyItemTidSuffix(Long applyId){
        if(Objects.isNull(applyId)){
            return;
        }
        List<AcFInvoiceApplyItem> items = applyItemMapper.selectByApplyId(applyId);
        if(CollectionUtils.isEmpty(items)){
            return;
        }
        for(AcFInvoiceApplyItem item : items){
            UpdateWrapper<AcFInvoiceApplyItem> update = new UpdateWrapper<>();
            long sec = System.currentTimeMillis() / 1000;
            update.lambda().eq(AcFInvoiceApplyItem::getId,item.getId()).set(AcFInvoiceApplyItem::getTid,item.getTid() + "_" + sec);
            applyItemMapper.update(null,update);
        }
    }

    public IPage<AcFInvoiceApply> pageNotAndFailTransferRecord(Page<AcFInvoiceApply> page) {
        LambdaQueryWrapper<AcFInvoiceApply> wrapper = Wrappers.lambdaQuery(new AcFInvoiceApply())
                .in(AcFInvoiceApply::getTransStatus, Lists.newArrayList(InvoiceConst.TransStatus.TRANS_FAIL,InvoiceConst.TransStatus.NOT_TRANS))
                .orderByDesc(AcFInvoiceApply::getId);
        return mapper.selectPage(page, wrapper);
    }
}
