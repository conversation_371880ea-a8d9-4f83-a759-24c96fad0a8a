package com.jackrain.nea.oc.oms.model.result;

import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 给乔丹财务提供的实体
 *
 * @author: ming.fz
 * create at: 2019/4/19
 */
@Data
public class OrderFinanceResult implements Serializable {

    //订单主表
    private OcBOrder ocBOrder;

    //订单子表,key是订单字表的TID,VAL 是单行实际成交金额合计
    private List<OrderItemFinanceResult> orderItemFinanceResults;
}
