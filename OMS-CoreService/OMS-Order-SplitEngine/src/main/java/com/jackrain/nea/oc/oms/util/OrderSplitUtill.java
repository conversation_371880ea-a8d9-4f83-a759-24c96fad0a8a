package com.jackrain.nea.oc.oms.util;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @program: r3-oc-oms
 * @description: 单价数量
 * @author: liuwj
 * @create: 2021-05-13 10:51
 **/
@Data
public class OrderSplitUtill {
    //整单平摊金额
    private BigDecimal orderSplitAmt;

    //优惠金额
    private BigDecimal amtDiscount;

    //调整金额
    private BigDecimal adjustAmt;

    //单行实际成交金额
    private BigDecimal realAmt;

    //结算总额
    private BigDecimal totPriceSettle;

    //品类缺少数量
    private  Integer lackNum ;

    //品类拥有数量
    private BigDecimal qty;

    //品类id
    private Long psCProdimId;

    //明细
    private OcBOrderItem ocBOrderItem;
}
