package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TableName(value = "ac_f_store_kpi")
@Data
@Document(index = "ac_f_store_kpi",type = "ac_f_store_kpi")
@ApiModel(value = "ac_f_store_kpi", description = "门店KPI设置表")
public class AcFStoreKpi extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    @ApiModelProperty(value = "ID")
    private Long id;

    @JSONField(name = "CP_C_STORE_ID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(value = "门店编码")
    private Long cpCStoreId;

    @JSONField(name = "MONTH_OF_YEAR")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "年月")
    private String monthOfYear;

    @JSONField(name = "KPI")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "KPI得分")
    private String kpi;

    @JSONField(name = "REMARK")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "备注")
    private String remark;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "创建人姓名")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "修改人姓名")
    private String modifierename;

}