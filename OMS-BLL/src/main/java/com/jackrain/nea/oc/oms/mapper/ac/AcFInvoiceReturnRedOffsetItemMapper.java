package com.jackrain.nea.oc.oms.mapper.ac;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.AcFInvoiceReturnRedOffsetItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @ClassName AcFInvoiceReturnRedOffsetItemMapper
 * @Description
 * @Date 2022/8/31 下午7:36
 * @Created by wuhang
 */
@Mapper
public interface AcFInvoiceReturnRedOffsetItemMapper extends ExtentionMapper<AcFInvoiceReturnRedOffsetItem> {

    @Select("<script> "
            + "SELECT * FROM ac_f_invoice_return_red_offset_item WHERE AC_F_INVOICE_RETURN_RED_OFFSET_ID "
            + "in <foreach item='item' index='index' collection='ids' "
            + "open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<AcFInvoiceReturnRedOffsetItem> selectByReturnRedOffsetIds(@Param("ids") List<Long> idList);
}
