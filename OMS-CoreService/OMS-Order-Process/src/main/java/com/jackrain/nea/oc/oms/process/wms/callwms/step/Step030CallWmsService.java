package com.jackrain.nea.oc.oms.process.wms.callwms.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;

/**
 * 调用WMS服务更新状态
 *
 * @author: 易邵峰
 * @since: 2019-01-20
 * create at : 2019-01-20 02:20
 */
@Step(order = 30, description = "调用WMS服务更新状态")
public class Step030CallWmsService implements IOmsOrderProcessStep<OcBOrderRelation> {
    @Override
    public ProcessStepResult startProcess(OcBOrderRelation orderInfo, ProcessStepResult preStepResult,
                                          boolean isAutoMakeup, User operateUser) {
        return new ProcessStepResult(StepStatus.SUCCESS);
    }
}
