package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.Date;

/**
 * <AUTHOR>
 * @create 2020-12-01
 * @desc JITX订单改仓日志表
 **/
@ApiModel(value = "oc_b_jitx_modify_warehouse_log", description = "JITX订单改仓日志表")
@Document(index = "oc_b_jitx_modify_warehouse_log", type = "oc_b_jitx_modify_warehouse_log")
@TableName("oc_b_jitx_modify_warehouse_log")
@Data
@AllArgsConstructor(access = AccessLevel.PUBLIC)
@NoArgsConstructor(access = AccessLevel.PUBLIC)
@Builder(toBuilder = true)
public class OcBJitxModifyWarehouseLog extends BaseModel {

    @ApiModelProperty("ID")
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    @JSONField(name = "ID")
    private Long id;

    @ApiModelProperty("订单编号")
    @JSONField(name = "ORDER_ID")
    @Field(type = FieldType.Long)
    private Long orderId;

    @ApiModelProperty(value = "来源单据编号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "BILL_NO")
    private String billNo;

    @ApiModelProperty("平台单号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SOURCE_CODE")
    private String sourceCode;

    /**
     * 0:未创建
     * 1:创建成功
     * 2:创建失败
     */
    @ApiModelProperty("改仓工单创建状态")
    @Field(type = FieldType.Integer)
    @JSONField(name = "CREATED_STATUS")
    private Integer createdStatus;

    @ApiModelProperty("卖家昵称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SELLER_NICK")
    private String sellerNick;

    @ApiModelProperty("供应商ID")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "VENDOR_ID")
    private String vendorId;

    @ApiModelProperty("改仓工单号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "WORKFLOW_SN")
    private String workflowSn;

    /**
     * VipJitxWorkflowStateEnum.
     * CREATE("100", "新建"),
     * DOING("200", "处理中"),
     * PASS("300", "通过"),
     * REJECT("400", "驳回"),
     * CANCEL("900", "取消");
     */
    @ApiModelProperty("改仓工单状态")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "WORKFLOW_STATE")
    private String workflowState;

    @ApiModelProperty("下单店铺id")
    @Field(type = FieldType.Long)
    @JSONField(name = "CP_C_SHOP_ID")
    private Long cpCShopId;

    @ApiModelProperty("店铺编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CP_C_SHOP_ECODE")
    private String cpCShopEcode;

    @ApiModelProperty("下单店铺标题")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CP_C_SHOP_TITLE")
    private String cpCShopTitle;

    @ApiModelProperty("修改前发货仓ID")
    @Field(type = FieldType.Long)
    @JSONField(name = "BEFORE_CHANGE_WAREHOUSE_ID")
    private Long beforeChangeWarehouseId;

    @ApiModelProperty("修改前发货仓编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "BEFORE_CHANGE_WAREHOUSE_CODE")
    private String beforeChangeWarehouseCode;

    @ApiModelProperty("修改前发货仓名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "BEFORE_CHANGE_WAREHOUSE_ENAME")
    private String beforeChangeWarehouseEname;

    @ApiModelProperty("修改后发货仓ID")
    @Field(type = FieldType.Long)
    @JSONField(name = "AFTER_CHANGE_WAREHOUSE_ID")
    private Long afterChangeWarehouseId;

    @ApiModelProperty("修改后发货仓编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "AFTER_CHANGE_WAREHOUSE_CODE")
    private String afterChangeWarehouseCode;

    @ApiModelProperty("修改后发货仓名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "AFTER_CHANGE_WAREHOUSE_ENAME")
    private String afterChangeWarehouseEname;

    @ApiModelProperty("旧唯品会仓库编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "OLD_DELIVERY_WAREHOUSE")
    private String oldDeliveryWarehouse;

    @ApiModelProperty("新唯品会仓库编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "NEW_DELIVERY_WAREHOUSE")
    private String newDeliveryWarehouse;

    @ApiModelProperty("改仓原因代码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "REASON_CODE")
    private String reasonCode;

    @ApiModelProperty("改仓备注")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "REASON_REMARK")
    private String reasonRemark;

    @ApiModelProperty("失败原因")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "FAIL_REASON")
    private String failReason;

    @ApiModelProperty("驳回原因")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "REJECT_REMARK")
    private String rejectRemark;

    @ApiModelProperty("失败次数")
    @Field(type = FieldType.Integer)
    @JSONField(name = "FAIL_NUMBER")
    private Integer failNumber;

    @ApiModelProperty("重试结果")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RETRY_RESULT")
    private String retryResult;

    @ApiModelProperty("接口类型")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "INTERFACE_TYPE")
    private String interfaceType;

    @ApiModelProperty("IP地址")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "IP_ADDRESS")
    private String ipAddress;

    @ApiModelProperty("是否强制改仓：Y是 N否")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "FORCED_WAREHOUSE_CHANGE")
    private String forcedWarehouseChange;

    @ApiModelProperty("版本号")
    @Field(type = FieldType.Long)
    @JSONField(name = "VERSION")
    private Long version;

    @ApiModelProperty("创建人姓名")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @ApiModelProperty("修改人姓名")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    @ApiModelProperty("下一次执行时间")
    @Field(type = FieldType.Long)
    @JSONField(name = "NEXT_COMPENSATION_DATE")
    private Date nextCompensationDate;

}
