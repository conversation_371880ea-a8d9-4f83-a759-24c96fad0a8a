package com.jackrain.nea.oc.oms.model.enums.naika;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @ClassName OmsOrderNaiKaStatusEnum
 * @Description 奶卡状态枚举
 * <AUTHOR>
 * @Date 2022/6/25 09:24
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum OcBOrderNaiKaStatusEnum {


    /**
     * 无需解冻
     */
    UN_NECESSARY(0, "无需解冻"),

    /**
     * 待解冻
     */
    FREEZE(1, "待解冻"),

    /**
     * 解冻成功
     */
    FREEZE_SUCCESS(2, "解冻成功"),

    /**
     * 解冻失败
     */
    FREEZE_FAILED(3, "解冻失败"),
    ;

    private Integer status;

    private String desc;

    public static OcBOrderNaiKaStatusEnum getOrderStatusByStatus(Integer status) {
        for (OcBOrderNaiKaStatusEnum value : OcBOrderNaiKaStatusEnum.values()) {
            if (value.getStatus().equals(status)) {
                return value;
            }
        }
        return null;
    }

}
