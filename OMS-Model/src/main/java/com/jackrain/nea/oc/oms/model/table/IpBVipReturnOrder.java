package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 唯品会退供单
 */
@TableName(value = "ip_b_vip_return_order")
@Data
@Document(index = "ip_b_vip_return_order", type = "ip_b_vip_return_order")
public class IpBVipReturnOrder extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    /**
     * 单据编码
     */
    @JSONField(name = "ECODE")
    @Field(type = FieldType.Keyword)
    private String ecode;

    /**
     * 退供单号
     */
    @JSONField(name = "RETURN_SN")
    @Field(type = FieldType.Keyword)
    private String returnSn;

    /**
     * 退供发货仓
     */
    @JSONField(name = "WAREHOUSE")
    @Field(type = FieldType.Keyword)
    private String warehouse;

    /**
     * 退供类型
     * 1	一退
     * 2	二退
     * 3	三退
     * 20	VMI二退
     * 21	部分二退
     * 30	无PO退供
     * 40	残次按PO退供
     * 100	VIS二退（VIS使用）
     * 101	VIS普通退供（VIS使用）
     * 102	VIS残次退供（VIS使用）
     */
    @JSONField(name = "RETURN_TYPE")
    @Field(type = FieldType.Keyword)
    private String returnType;

    /**
     * 支付方式
     * 0:货到付款
     * 1:现付月结
     */
    @JSONField(name = "PAY_TYPE")
    @Field(type = FieldType.Keyword)
    private String payType;

    /**
     * 单据来源
     * 1:手工创建
     * 2:自动下载
     */
    @JSONField(name = "SOURCE_TYPE")
    @Field(type = FieldType.Keyword)
    private String sourceType;

    /**
     * 退供收货人
     */
    @JSONField(name = "CONSIGNEE")
    @Field(type = FieldType.Keyword)
    private String consignee;

    /**
     * 国家标识
     */
    @JSONField(name = "COUNTRY")
    @Field(type = FieldType.Keyword)
    private String country;

    /**
     * 省/州
     */
    @JSONField(name = "STATE")
    @Field(type = FieldType.Keyword)
    private String state;

    /**
     * 城市
     */
    @JSONField(name = "CITY")
    @Field(type = FieldType.Keyword)
    private String city;

    /**
     * 区/县
     */
    @JSONField(name = "REGION")
    @Field(type = FieldType.Keyword)
    private String region;

    /**
     * 乡镇/街道
     */
    @JSONField(name = "TOWN")
    @Field(type = FieldType.Keyword)
    private String town;

    /**
     * 收货地址
     */
    @JSONField(name = "ADDRESS")
    @Field(type = FieldType.Keyword)
    private String address;

    /**
     * 邮政编码
     */
    @JSONField(name = "POSTCODE")
    @Field(type = FieldType.Keyword)
    private String postcode;

    /**
     * 座机
     */
    @JSONField(name = "TELEPHONE")
    @Field(type = FieldType.Keyword)
    private String telephone;

    /**
     * 移动电话
     */
    @JSONField(name = "MOBILE")
    @Field(type = FieldType.Keyword)
    private String mobile;

    /**
     * 退供通知email地址
     */
    @JSONField(name = "TO_EMAIL")
    @Field(type = FieldType.Keyword)
    private String toEmail;

    /**
     * 退供抄送email地址
     */
    @JSONField(name = "CC_EMAIL")
    @Field(type = FieldType.Keyword)
    private String ccEmail;

    /**
     * 是否自提
     * 0:品骏配送
     * 1:供应商自提
     */
    @JSONField(name = "SELF_REFERENCE")
    @Field(type = FieldType.Keyword)
    private String selfReference;

    /**
     * 是否门店退供
     * 0:否
     * 1:是
     */
    @JSONField(name = "IS_STORE_DELIVERY")
    @Field(type = FieldType.Keyword)
    private String isStoreDelivery;

    /**
     * 供应商退供地址编码
     */
    @JSONField(name = "RETURN_ADDRESS_NO")
    @Field(type = FieldType.Keyword)
    private String returnAddressNo;

    /**
     * 转换状态
     * 0	未转换
     * 1	转换中
     * 2	已转换
     * 3	退款转换中
     * 4	转换失败
     * 5	转换异常
     */
    @JSONField(name = "TRANS_STATUS")
    @Field(type = FieldType.Integer)
    private Integer transStatus;

    /**
     * 转换时间
     */
    @JSONField(name = "TRANS_TIME")
    @Field(type = FieldType.Long)
    private Date transTime;

    /**
     * 转换次数
     */
    @JSONField(name = "TRANS_NUMS")
    @Field(type = FieldType.Long)
    private Long transNums;

    /**
     * 出仓时间
     */
    @JSONField(name = "OUT_TIME")
    @Field(type = FieldType.Long)
    private Date outTime;

    /**
     * 总箱数
     */
    @JSONField(name = "TOTAL_CASES")
    @Field(type = FieldType.Double)
    private BigDecimal totalCases;

    /**
     * 总商品条码数
     */
    @JSONField(name = "TOTAL_SKU")
    @Field(type = FieldType.Double)
    private BigDecimal totalSku;

    /**
     * 总商品数
     */
    @JSONField(name = "TOTAL_QTY")
    @Field(type = FieldType.Double)
    private BigDecimal totalQty;

    /**
     * 出仓明细总数量
     */
    @JSONField(name = "TOTAL_DETAIL")
    @Field(type = FieldType.Double)
    private BigDecimal totalDetail;

    /**
     * 卖家昵称
     */
    @JSONField(name = "SELLER_NICK")
    @Field(type = FieldType.Keyword)
    private String sellerNick;

    /**
     * 店铺
     */
    @JSONField(name = "CP_C_SHOP_ID")
    @Field(type = FieldType.Long)
    private Long cpCShopId;

    /**
     * 店铺标题
     */
    @JSONField(name = "CP_C_SHOP_TITLE")
    @Field(type = FieldType.Keyword)
    private String cpCShopTitle;

    /**
     * 备注
     */
    @JSONField(name = "REMARK")
    @Field(type = FieldType.Keyword)
    private String remark;

    @JSONField(name = "VERSION")
    @Field(type = FieldType.Long)
    private Long version;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;

    /**
     * 插入时间
     */
    @JSONField(name = "INSERTDATE")
    @Field(type = FieldType.Long)
    private Date insertdate;

    /**
     * 品牌
     */
    @JSONField(name = "PS_C_BRAND_ID")
    @Field(type = FieldType.Long)
    private Long psCBrandId;
}