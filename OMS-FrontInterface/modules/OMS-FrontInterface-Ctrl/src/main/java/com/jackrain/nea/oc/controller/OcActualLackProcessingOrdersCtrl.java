package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.ActualLackProcessingOrdersMarkCmd;
import com.jackrain.nea.oc.oms.api.ActualLackProcessingOrdersQueryCmd;
import com.jackrain.nea.oc.oms.model.result.QueryDeficiencyItemListResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * @author: Liqb
 * @since: 2019/07/18
 * create at : 2019/07/18 21:30
 */
@Api(value = "OcActualLackProcessingOrdersCtrl", tags = "实缺处理单")
@Slf4j
@RestController
public class OcActualLackProcessingOrdersCtrl {

    @Autowired
    private ActualLackProcessingOrdersMarkCmd markCmd;

    @Autowired
    private ActualLackProcessingOrdersQueryCmd queryCmd;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;


    /**
     * 标记处理按钮操作
     *
     * @param request 请求对象
     * @param obj     请求参数
     * @return json对象
     */
    @ApiOperation(value = "标记处理按钮操作")
    @RequestMapping(value = "/api/cs/oc/oms/v1/markOrder", method = RequestMethod.POST)
    public JSONObject markOrder(HttpServletRequest request,
                                @RequestBody JSONObject obj) {
        ValueHolderV14 vh = null;
        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        try {
            vh = markCmd.execute(obj, user);
            vh.setCode(vh.getCode());
            vh.setMessage(Resources.getMessage(vh.getMessage()));
            return vh.toJSONObject();
        } catch (NDSException e) {
            vh = new ValueHolderV14();
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("操作标记处理失败" + Resources.getMessage(e.getMessage()));
            return vh.toJSONObject();
        }
    }

    /**
     * 查询相关业务单据明细信息
     *
     * @param request req
     * @param param   string
     * @return string str
     * @throws Exception ex
     */
    @ApiOperation(value = "查询相关业务单据明细信息")
    @RequestMapping(path = "/api/cs/oc/oms/v1/getMxList", method = RequestMethod.POST)
    public JSONObject getMxList(HttpServletRequest request,
                                @RequestBody JSONObject param)
            throws Exception {
        User loginUser = r3PrimWebAuthService.getLoginPrimWebUser(request);
        ValueHolderV14<QueryDeficiencyItemListResult> vh = queryCmd.queryMx(param, loginUser);
        String result = JSON.toJSONStringWithDateFormat(vh.toJSONObject(), "yyyy-MM-dd HH:mm:ss",
                SerializerFeature.WriteMapNullValue);
        return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
    }
}