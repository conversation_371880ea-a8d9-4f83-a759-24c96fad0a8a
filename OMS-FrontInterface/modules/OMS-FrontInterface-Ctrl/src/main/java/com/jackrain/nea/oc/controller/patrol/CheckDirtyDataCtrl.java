package com.jackrain.nea.oc.controller.patrol;

import com.jackrain.nea.oc.oms.api.patrol.CheckDirtyDataCmd;
import com.jackrain.nea.oc.oms.api.patrol.GetOrderIdsCmd;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

/**
 * Description:
 *
 * <AUTHOR> 孙继东
 * @since : 2019-06-06
 * create at : 2019-06-06 10:45
 */
@RestController
public class CheckDirtyDataCtrl {

    @Autowired
    private GetOrderIdsCmd getOrderIdsCmd;

    @Autowired
    private CheckDirtyDataCmd checkDirtyDataCmd;

    /**
     * 主表明细金额不等的订单
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/api/cs/oc/oms/v1/getIds", method = RequestMethod.POST)
    public List test(HttpServletRequest request) {
        List ids = new ArrayList();
        try {
            ids = getOrderIdsCmd.getIds();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return ids;
    }

    /**
     * 订单总金额和已收金额不等的订单
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/api/cs/oc/oms/v1/getOrderIds", method = RequestMethod.POST)
    public List test1(HttpServletRequest request) {
        List ids = new ArrayList();
        try {
            ids = getOrderIdsCmd.getOrderIds();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return ids;
    }

    /**
     * 平台单号重复的订单
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/api/cs/oc/oms/v1/getTidRepeatIds", method = RequestMethod.POST)
    public List test2(HttpServletRequest request) {
        List ids = new ArrayList();
        try {
            ids = getOrderIdsCmd.getTidRepeatIds();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return ids;
    }

    /**
     * 检查 明细全部退款成功,订单没有作废入口
     *
     * @return 非法数据
     */
    @PostMapping(value = "/api/cs/oc/oms/v1/checkReturn1")
    public ValueHolderV14 checkReturn1() {
        return checkDirtyDataCmd.checkReturn1();
    }

    /**
     * 检查退款成功，明细在对应出库通知单存在
     *
     * @return 非法数据
     */
    @PostMapping(value = "/api/cs/oc/oms/v1/checkReturn2")
    public ValueHolderV14 checkReturn2() {
        return checkDirtyDataCmd.checkReturn2();
    }

    /**
     * 检查订单查询云仓 已发货，但是oms 已取消的单据
     *
     * @return 非法数据
     */
    @PostMapping(value = "/api/cs/oc/oms/v1/checkReturn3")
    public ValueHolderV14 checkReturn3() {
        return checkDirtyDataCmd.checkReturn3();
    }
}
