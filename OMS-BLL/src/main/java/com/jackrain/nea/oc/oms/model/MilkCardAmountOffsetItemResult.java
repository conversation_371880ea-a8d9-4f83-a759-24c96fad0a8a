package com.jackrain.nea.oc.oms.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import lombok.Data;

import java.math.BigDecimal;

/**
 * Description:
 *
 * @Author: guo.kw
 * @Since: 2022/9/9
 * create at: 2022/9/9 14:50
 */
@Data
public class MilkCardAmountOffsetItemResult {

    private Long id;

    @JSONField(name = "CARD_CODE")
    private String cardCode;

    @JSONField(name = "PS_C_SKU_ID")
    private Long psCSkuId;

    @JSONField(name = "PS_C_PRO_ECODE")
    private String psCSkuEcode;

    @JSONField(name = "PS_C_PRO_id")
    private Long psCProId;

    @JSONField(name = "PS_C_PRO_ECODE")
    private String psCProEcode;

    @Field(type = FieldType.Keyword)
    @JSONField(name = "PS_C_PRO_ENAME")
    private String psCProEname;

    @JSONField(name = "ROW_ITEM_TYPE")
    private Integer rowItemType;

    @JSONField(name = "QTY")
    private Integer qty;

    @JSONField(name = "UNIT")
    private String unit;

    @JSONField(name = "FACTORY_CODE")
    private String factoryCode;

    @JSONField(name = "OFFSET_PRICE")
    private BigDecimal offsetPrice;

    @JSONField(name = "ITEM_TYPE")
    private Integer itemType;

    @JSONField(name = "OFFSET_ORDER_ID")
    private Long offsetOrderId;

    //汇总码 collect_code
    @JSONField(name = "COLLECT_CODE")
    private String collectCode;
    //汇总类型 todo 缺失
    @JSONField(name = "SUM_TYPE")
    private String sumType;

}
