package com.jackrain.nea.oc.oms.nums;


import com.jackrain.nea.oc.oms.model.result.QueryOrderCheckBoxResult;

import java.util.ArrayList;
import java.util.List;

/**
 * 退款状态选项值
 *
 * @author: 周琳胜
 * create at: 2019/3/20 13:20
 */
public enum OcOrderRefundStatusEnum {

    NOTREFUND("未退款", 0),
    WAIT_SELLER_AGREE("买家已经申请退款，等待卖家同意", 1),
    WAIT_BUYER_RETURN_GOODS("卖家已经同意退款，等待买家退货", 2),
    WAIT_SELLER_CONFIRM_GOODS("买家已经退货，等待卖家确认收货", 3),
    SELLER_REFUSE_BUYER("卖家拒绝退款", 4),
    CLOSED("退款关闭", 5),
    SUCCESS("退款成功", 6);


    String key;
    int val;

    OcOrderRefundStatusEnum(String k, int v) {
        this.key = k;
        this.val = v;
    }

    public String getKey() {
        return key;
    }

    public int getVal() {
        return val;
    }

    /**
     * 转化成QueryOrderCheckBoxResult
     *
     * @return list<QueryOrderCheckBoxResult>
     */
    public static List<QueryOrderCheckBoxResult> toQueryOrderCheckBoxResult() {
        List<QueryOrderCheckBoxResult> list = new ArrayList<>();
        for (OcOrderRefundStatusEnum e : OcOrderRefundStatusEnum.values()) {
            QueryOrderCheckBoxResult o = new QueryOrderCheckBoxResult();
            o.setLabel(e.getKey());
            o.setValue(String.valueOf(e.getVal()));
            list.add(o);
        }
        return list;
    }

}


