package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

@TableName(value = "ST_C_HOLD_ORDER_REASON")
@Data
public class StCHoldOrderReason extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 类型
     */
    @JSONField(name = "TYPE")
    private Integer type;

    /**
     * 原因
     */
    @JSONField(name = "REASON")
    private String reason;

    /**
     * 备注
     */
    @JSONField(name = "REMARK")
    private String remark;

    /**
     * 数据类型 1、系统预留数据（不能删除）
     */
    @JSONField(name = "DATA_TYPE")
    private Integer dataType;

    /**
     * 创建人姓名
     */
    @JSONField(name = "OWNERENAME")
    private String ownerename;

    /**
     * 修改人姓名
     */
    @JSONField(name = "M<PERSON>IFIERENAME")
    private String modifierename;

}