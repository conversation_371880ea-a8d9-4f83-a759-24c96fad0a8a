package com.jackrain.nea.oc.oms.model.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * @program: r3-oc-oms
 * @description: 商品效期策略类型
 * @author: caomalai
 * @create: 2022-08-11 11:34
 **/
public enum AppointDimensionEnum {
    /**
     * old is 品项
     */
    CATEGORY(1, "四级分类"),

    PRO_CODE(2, "商品编码"),
    PRO_TITLE(3, "商品标题"),
    PLATFORM_PRO(4, "平台商品ID"),
    TITLE_PRO(5, "商品标题+商品编码"),
    /**
     * old is 品类
     */
    CATEGORY_CLASS(6, "一级分类"),

    SPECS_PRO(7, "规格ID+商品编码"),
    UID_PRO_CODE(8, "主播ID+商品编码");


    @Getter
    private Integer key;
    @Getter
    private String desc;

    AppointDimensionEnum(Integer key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public static AppointDimensionEnum getByKey(Integer key) {
        for (AppointDimensionEnum current : values()) {
            if (Objects.equals(current.getKey(), key)) {
                return current;
            }
        }
        return null;
    }

    public static AppointDimensionEnum getByDesc(String desc) {
        for (AppointDimensionEnum current : values()) {
            if (Objects.equals(current.getDesc(), desc)) {
                return current;
            }
        }
        return null;
    }
}
