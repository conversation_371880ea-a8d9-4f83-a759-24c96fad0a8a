package com.jackrain.nea.oc.oms.mapper;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongRefund;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.jdbc.SQL;

@Mapper
public interface IpBJingdongRefundMapper extends ExtentionMapper<IpBJingdongRefund> {

    class JingdongRefundSqlBuilder {
        public String updateRefund(JSONObject map) {
            return new SQL() {
                {
                    UPDATE("ip_b_jingdong_refund");
                    SET("trans_count = IFNULL(trans_count, 0) + 1");
                    for (String key : map.keySet()) {
                        if (!"afsserviceid".equals(key)) {
                            SET(key + "= #{" + key + "}");
                        }
                    }
                    WHERE("afsserviceid   = #{afsserviceid}");
                }
            }.toString();
        }

        public String updateTransTip(JSONObject map) {
            return new SQL() {
                {
                    UPDATE("ip_b_jingdong_refund");
                    for (String key : map.keySet()) {
                        if (!"afsserviceid".equals(key)) {
                            SET(key + "= #{" + key + "}");
                        }
                    }
                    WHERE("afsserviceid   = #{afsserviceid}");
                }
            }.toString();
        }
    }

    @Select("SELECT * FROM ip_b_jingdong_refund WHERE afsserviceId=#{afsserviceId} and istrans = 0")
    IpBJingdongRefund selectJingdongRefundByAfsserviceId(@Param("afsserviceId") Long afsserviceId);

    @UpdateProvider(type = IpBJingdongRefundMapper.JingdongRefundSqlBuilder.class, method = "updateRefund")
    int updateRefundOrder(JSONObject jsonObject);


    /**
     * 更新京东转单节点提示
     *
     * @param jsonObject jsonObject
     * @return int
     */
    @UpdateProvider(type = IpBJingdongRefundMapper.JingdongRefundSqlBuilder.class, method = "updateTransTip")
    int updateTransTip(JSONObject jsonObject);

    /**
     * 根据服务单号获取京东退单中间表信息
     *
     * @param afsserviceId
     * @return
     */
    @Select("SELECT * FROM ip_b_jingdong_refund WHERE afsserviceId=#{afsserviceId}")
    IpBJingdongRefund selectJingdongRefundByAfsserviceId4WareId(@Param("afsserviceId") Long afsserviceId);

}