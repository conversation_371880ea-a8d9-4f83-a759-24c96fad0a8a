package com.jackrain.nea.oc.oms.model.request;


import com.jackrain.nea.oc.oms.model.table.OcBRefundIn;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInProductItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.web.face.User;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> lin yu
 * @date : 2022/7/26 上午10:13
 * @describe :
 */
@Data
public class GenerateStorageBillRequest implements Serializable {

    private User user;

    private OcBReturnOrder returnOrder;

    private List<OcBReturnOrderRefund> returnOrderRefundList;

    private OcBRefundIn refundIn;

    private List<OcBRefundInProductItem> refundInProductItemList;

    private Integer adjustPropId;

    private OperationType operationType;

    /**
     * B2BRK  SAP下发
     * THRK   非SAP下发
     */
    private String billType;

    private Integer adjustSourceBillType;


    public enum OperationType{
        /**
         * 新增入库通知单和入库结果单
         */
        INSERT_NOTICES_AND_RESULT,

        /**
         * 新增入库通知单 并传wms
         */
        INSERT_NOTICES_PASS_WMS,

        /**
         * 新增逻辑入库单
         */
        INSERT_RESULT,

        /**
         * 新增库存调整单
         */
        INSERT_ADJUST,

        /**
         * 新增库存调整单 并传wms
         */
        INSERT_ADJUST_PASS_WMS;
    }


}
