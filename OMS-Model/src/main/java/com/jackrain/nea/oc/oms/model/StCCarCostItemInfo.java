package com.jackrain.nea.oc.oms.model;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @ClassName StCCarCostItemInfo
 * @Description 零担、整车报价明细
 * <AUTHOR>
 * @Date 2024/4/9 09:01
 * @Version 1.0
 */
@Data
public class StCCarCostItemInfo implements Serializable {
    private static final long serialVersionUID = 549561853984954864L;
    private Long id;
    private Long costId;
    private Long provinceId;
    private Long cityId;
    private Integer arrivalDays;
    private BigDecimal startWeight;
    private BigDecimal endWeight;
    private BigDecimal trunkFreight;
    private BigDecimal deliveryFee;
    private BigDecimal freight;
    private BigDecimal premium;
    private BigDecimal unloadingFee;
    private BigDecimal otherFee;
    private Long bigint1;
    private Long bigint2;
    private Long bigint3;
    private BigDecimal decimal1;
    private BigDecimal decimal2;
    private BigDecimal decimal3;
    private BigDecimal decimal4;
    private BigDecimal decimal5;
    private BigDecimal decimal6;
    private Long version;
    private String ownerename;
    private String modifierename;


    /**
     * 0是零担 1是整车
     */
    private Integer type;
}
