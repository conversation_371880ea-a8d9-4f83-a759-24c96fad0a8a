package com.jackrain.nea.oc.oms.task;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderBnTaskMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.BnReturnOrderColumnEnum;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderBnTask;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName AutoCreateBnTaskByReturnOrderTask
 * @Description 自动创建班牛工单任务，捞取退换货单中大于要求入库时间字段的退换货单，然后调用班牛创建工单
 * <AUTHOR>
 * @Date 2025/5/15 10:51
 * @Version 1.0
 */
@Component
@Slf4j
public class AutoCreateBnTaskByReturnOrderTask extends BaseR3Task implements IR3Task {

    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;

    @Autowired
    private OcBReturnOrderBnTaskMapper ocBReturnOrderBnTaskMapper;
    @Autowired
    private IpRpcService ipRpcService;
    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private StRpcService stRpcService;
    @Value("${bn.project.id:20940}")
    private String projectId;
    @Value("${bn.app.id:36039}")
    private String appId;
    @Value("${task.limit:100}")
    private Integer limit;

    @Override
    @XxlJob("AutoCreateBnTaskByReturnOrderTask")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult result = new RunTaskResult();
        result.setSuccess(true);

        try {
            log.info("开始执行AutoCreateBnTaskByReturnOrderTask定时任务");

            // 查询需要处理的退换货单
            List<OcBReturnOrder> returnOrders = queryReturnOrdersToProcess();

            if (CollectionUtils.isEmpty(returnOrders)) {
                log.info("没有需要处理的退换货单");
                result.setMessage("没有需要处理的退换货单");
                return result;
            }

            log.info("查询到{}个需要处理的退换货单", returnOrders.size());

            // 批量创建班牛工单
            createBnTasks(returnOrders);

            result.setMessage("成功处理" + returnOrders.size() + "个退换货单");
        } catch (Exception e) {
            log.error("执行AutoCreateBnTaskByReturnOrderTask定时任务异常", e);
            result.setSuccess(false);
            result.setMessage("执行定时任务异常: " + e.getMessage());
        }

        return result;
    }

    /**
     * 查询需要处理的退换货单
     * 条件：当前时间超过要求入库时间，是否超时入库不等于“是”，单据状态=等待退换入库，通用标记为空
     *
     * @return 需要处理的退换货单列表
     */
    private List<OcBReturnOrder> queryReturnOrdersToProcess() {
        // 查询符合条件的退换货单
        List<OcBReturnOrder> returnOrders = ocBReturnOrderMapper.selectReturnOrdersForBnTask(new Date(), limit);

        if (CollectionUtils.isEmpty(returnOrders)) {
            return returnOrders;
        }

        // 过滤掉已经创建过班牛工单的退换货单
        List<OcBReturnOrder> filteredOrders = new ArrayList<>();
        for (OcBReturnOrder returnOrder : returnOrders) {
            // 检查是否已经创建过班牛工单
            int count = ocBReturnOrderBnTaskMapper.countByReturnOrderId(returnOrder.getId());
            if (count == 0) {
                filteredOrders.add(returnOrder);
            } else {
                log.info("退换货单已创建过班牛工单，跳过处理，退换货单ID={}", returnOrder.getId());
            }
        }

        return filteredOrders;
    }

    /**
     * 批量创建班牛工单
     *
     * @param returnOrders 需要处理的退换货单列表
     */
    private void createBnTasks(List<OcBReturnOrder> returnOrders) {
        List<Map<String, String>> taskContents = new ArrayList<>();

        for (OcBReturnOrder returnOrder : returnOrders) {
            try {
                Map<String, String> content = buildTaskContent(returnOrder);
                if (content != null) {
                    taskContents.add(content);
                }
            } catch (Exception e) {
                log.error("构建工单内容异常，退换货单ID={}", returnOrder.getId(), e);
            }
        }

        if (CollectionUtils.isNotEmpty(taskContents)) {
            try {
                // 调用班牛API创建工单
                ValueHolderV14 result = ipRpcService.batchPushTask(taskContents, "21939", "36039", null);
                if (result.isOK()) {
                    log.info("成功创建{}个班牛工单", taskContents.size());

                    // 记录班牛工单信息
                    saveReturnOrderBnTasks(returnOrders, taskContents, result);

                    // 更新退换货单的超时入库状态
                    updateReturnOrdersOverdueStatus(returnOrders);
                } else {
                    log.error("创建班牛工单失败：{}", result.getMessage());
                }
            } catch (Exception e) {
                log.error("调用班牛API创建工单异常", e);
            }
        }
    }

    /**
     * 记录班牛工单信息
     *
     * @param returnOrders 退换货单列表
     * @param taskContents 工单内容列表
     * @param result 创建结果
     */
    private void saveReturnOrderBnTasks(List<OcBReturnOrder> returnOrders, List<Map<String, String>> taskContents, ValueHolderV14 result) {
        if (CollectionUtils.isEmpty(returnOrders) || CollectionUtils.isEmpty(taskContents)) {
            return;
        }

        List<OcBReturnOrderBnTask> taskList = new ArrayList<>();
        Date now = new Date();

        for (int i = 0; i < returnOrders.size() && i < taskContents.size(); i++) {
            OcBReturnOrder returnOrder = returnOrders.get(i);
            Map<String, String> content = taskContents.get(i);

            try {
                // 创建班牛工单关联记录
                OcBReturnOrderBnTask bnTask = new OcBReturnOrderBnTask();
                bnTask.setId(ModelUtil.getSequence("OC_B_RETURN_ORDER_BN_TASK")); // 生成ID
                bnTask.setOcBReturnOrderId(returnOrder.getId());
                bnTask.setBillNo(returnOrder.getBillNo());
                bnTask.setTaskParam(JSONObject.toJSONString(content));
                bnTask.setTaskStatus(0); // 0-已创建
                bnTask.setCreationdate(now);
                bnTask.setModifieddate(now);
                bnTask.setIsactive("Y");
//                BaseModelUtil.initialBaseModelSystemField(bnTask);
                taskList.add(bnTask);
            } catch (Exception e) {
                log.error("创建班牛工单关联记录异常，退换货单ID={}", returnOrder.getId(), e);
            }
        }

        if (CollectionUtils.isNotEmpty(taskList)) {
            try {
                // 批量插入班牛工单关联记录
                int insertCount = ocBReturnOrderBnTaskMapper.batchInsert(taskList);
                log.info("成功插入{}条班牛工单关联记录", insertCount);
            } catch (Exception e) {
                log.error("批量插入班牛工单关联记录异常", e);
            }
        }
    }

    /**
     * 更新退换货单的超时入库状态
     *
     * @param returnOrders 退换货单列表
     */
    private void updateReturnOrdersOverdueStatus(List<OcBReturnOrder> returnOrders) {
        if (CollectionUtils.isEmpty(returnOrders)) {
            return;
        }
        for (OcBReturnOrder returnOrder : returnOrders) {
            try {
                OcBReturnOrder updateReturnOrder = new OcBReturnOrder();
                updateReturnOrder.setId(returnOrder.getId());
                updateReturnOrder.setOverdueStorageStatus("1");
                updateReturnOrder.setModifieddate(new Date());
                ocBReturnOrderMapper.updateById(updateReturnOrder);
            } catch (Exception e) {
                log.error("准备更新退换货单超时状态异常，退换货单ID={}", returnOrder.getId(), e);
            }
        }
    }

    /**
     * 构建工单内容
     *
     * @param returnOrder 退换货单
     * @return 工单内容
     */
    private Map<String, String> buildTaskContent(OcBReturnOrder returnOrder) {
        if (returnOrder == null) {
            return null;
        }
        // 如果发货仓库为空 则不处理
        if (returnOrder.getCpCPhyWarehouseId() == null) {
            // 打日志
            log.info("退换货单ID={}发货仓库为空，不处理", returnOrder.getId());
            return null;
        }

        // 如果退货仓库为空 则不处理
        if (returnOrder.getCpCPhyWarehouseInId() == null) {
            // 打日志
            log.info("退换货单ID={}退货仓库为空，不处理", returnOrder.getId());
            return null;
        }
        CpCPhyWarehouse cpCPhyWarehouse = cpRpcService.queryByWarehouseId(returnOrder.getCpCPhyWarehouseId());
        CpCPhyWarehouse cpCPhyWarehouseIn = cpRpcService.queryByWarehouseId(returnOrder.getCpCPhyWarehouseInId());
        // 判断cpCPhyWarehouse与cpCPhyWarehouseIn是否为空
        if (cpCPhyWarehouse == null || cpCPhyWarehouseIn == null) {
            // 打日志
            log.info("退换货单ID={}发货仓库或退货仓库为空，不处理", returnOrder.getId());
            return null;
        }

        Map<String, String> content = new HashMap<>();

        // 发货仓库
        content.put(BnReturnOrderColumnEnum.WAREHOUSE_OUT.getColumnId(), cpCPhyWarehouse.getEname());

        // 退货仓库
        content.put(BnReturnOrderColumnEnum.WAREHOUSE_IN.getColumnId(), cpCPhyWarehouseIn.getEname());

        // 快递公司
        if (StringUtils.isNotBlank(returnOrder.getCpCLogisticsEname())) {
            content.put(BnReturnOrderColumnEnum.LOGISTICS_COMPANY.getColumnId(), returnOrder.getCpCLogisticsEname());
        }

        // 快递单号
        if (StringUtils.isNotBlank(returnOrder.getLogisticsCode())) {
            content.put(BnReturnOrderColumnEnum.LOGISTICS_CODE.getColumnId(), returnOrder.getLogisticsCode());
        }

        // 签收状态
        if (returnOrder.getSigningStatus() != null) {
            String signingStatusText = returnOrder.getSigningStatus().equals("1") ? "已签收" :
                                      (returnOrder.getSigningStatus().equals("2") ? "已退签" : "未签收");
            content.put(BnReturnOrderColumnEnum.SIGNING_STATUS.getColumnId(), signingStatusText);
        }


        // 退回类型 如果isBack不是空 并且为1 则为拦截
        if (returnOrder.getIsBack() != null && returnOrder.getIsBack() == 1) {
            content.put(BnReturnOrderColumnEnum.RETURN_TYPE.getColumnId(), "拦截");
        }else {
            content.put(BnReturnOrderColumnEnum.RETURN_TYPE.getColumnId(), "客退");
        }

        // 签收时间
        if (returnOrder.getSigningTime() != null) {
            content.put(BnReturnOrderColumnEnum.SIGNING_TIME.getColumnId(), DATE_FORMAT.format(returnOrder.getSigningTime()));
        }

        // OMS创建人
        content.put(BnReturnOrderColumnEnum.OMS_CREATOR.getColumnId(), "系统管理员");

        // 单据编号 - 使用标题字段
        if (StringUtils.isNotBlank(returnOrder.getBillNo())) {
            content.put(BnReturnOrderColumnEnum.TITLE.getColumnId(), returnOrder.getBillNo());
        }

        // 创建时间
        content.put(BnReturnOrderColumnEnum.CREATE_TIME.getColumnId(), DATE_FORMAT.format(new Date()));

        // 截止时间 - 设置为当前时间后7天
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, 7);
        content.put(BnReturnOrderColumnEnum.DEADLINE.getColumnId(), DATE_FORMAT.format(calendar.getTime()));

        return content;
    }
}
