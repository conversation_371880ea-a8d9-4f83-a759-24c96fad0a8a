package com.jackrain.nea.oc.oms.controller;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.cpext.model.table.CpCPlatform;
import com.jackrain.nea.hub.api.naika.NaiKaOrderCmd;
import com.jackrain.nea.hub.request.naika.NaiKaRefundStockInItemRequest;
import com.jackrain.nea.hub.request.naika.NaiKaRefundStockInRequest;
import com.jackrain.nea.oc.oms.es.ES4ReturnOrder;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderRefundMapper;
import com.jackrain.nea.oc.oms.model.enums.*;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;

/**
 * @ClassName ReturnInToNaiKaTask
 * @Description （奶卡提奶、周期购提奶、免费奶卡提奶、单品订单）退单 仓库入库 同步奶卡系统
 * <AUTHOR>
 * @Date 2022/8/15 20:59
 * @Version 1.0
 */
@Component
@Slf4j
public class ReturnInToNaiKaTask {

    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;
    @Autowired
    private OcBReturnOrderRefundMapper ocBReturnOrderRefundMapper;

    @Reference(group = "hub", version = "1.0")
    private NaiKaOrderCmd naiKaOrderCmd;
    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private ThreadPoolTaskExecutor naikaReturnInThreadPoolExecutor;

    private static final List<String> pickUpList =
            Lists.newArrayList(OrderBusinessTypeCodeEnum.MILK_CARD_PICK_UP_GOODS_RETURN.getCode(),
                    OrderBusinessTypeCodeEnum.FREE_MILK_CARD_PICK_UP_GOODS_RETURN.getCode(),
                    OrderBusinessTypeCodeEnum.CYCLE_PICK_UP_RETURN.getCode());


    public void execute(JSONObject params) {
        JSONObject whereKeyJson = new JSONObject();

        int pageIndex = 0;
        int pageSize = 100;
        JSONArray jsonArray = new JSONArray();
        jsonArray.add(ReturnStatusEnum.WAIT_AFTERSALE_ENSURE.getVal());
        jsonArray.add(ReturnStatusEnum.COMPLETION.getVal());

        // 先把传奶卡系统为待传、错收入库为否。  的取出来
        whereKeyJson.put("TO_NAIKA_STATUS", ReturnNaiKaStatusEnum.UN_PUSH.getStatus());
        whereKeyJson.put("IS_WRONG_RECEIVE", IsWrongReceive.NO.val());
        whereKeyJson.put("RETURN_STATUS", jsonArray);
        List<Long> returnOrderIdList = ES4ReturnOrder.queryEsReturnOrderList(whereKeyJson, pageIndex, pageSize);
        if (CollectionUtils.isEmpty(returnOrderIdList)) {
            return;
        }
        List<Future<Boolean>> results = new ArrayList<Future<Boolean>>();
        for (Long returnOrderId : returnOrderIdList) {
            results.add(naikaReturnInThreadPoolExecutor.submit(new CallableReturnInToNaiKaWithResult(returnOrderId)));
        }
        //线程执行结果获取
        for (Future<Boolean> futureResult : results) {
            try {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("ReturnInToNaiKaTask------>线程结果:{}"), futureResult.get().toString());
                }
            } catch (Exception e) {
                log.error(LogUtil.format("ReturnInToNaiKaTask多线程获取InterruptedException异常：{}", "ReturnInToNaiKaTask"), Throwables.getStackTraceAsString(e));
            }
        }
        return;
    }

    class CallableReturnInToNaiKaWithResult implements Callable<Boolean> {

        private Long id;

        public CallableReturnInToNaiKaWithResult(Long id) {
            this.id = id;
        }

        @Override
        public Boolean call() throws Exception {
            // 根据id 查询出数据
            OcBReturnOrder ocBReturnOrder = ocBReturnOrderMapper.selectByid(id);
            // 校验状态 因为es可能会存在数据延迟
            if (ObjectUtil.notEqual(ocBReturnOrder.getToNaikaStatus(), ReturnNaiKaStatusEnum.UN_PUSH.getStatus())) {
                return true;
            }
            if (ObjectUtil.notEqual(ocBReturnOrder.getIsWrongReceive(), IsWrongReceive.NO.val())) {
                return true;
            }
            CpCPlatform cpCPlatform = cpRpcService.selectCpcPlatformById(Long.valueOf(ocBReturnOrder.getPlatform()));

//    条件组1
//--传奶卡系统状态为【待传】
//            --退单的状态为【等待售后确认、完成】done
//            --退单业务类型为【电商销售退货】
//            --退单入库状态为【部分入库、全部入库】
//            --【错收入库】为否 done
//            条件组2
//--传奶卡系统状态为【待传】
//            --退单的状态为【等待售后确认、完成】 done
//            --退单业务类型为【提奶退货、免费提奶退货、周期购退货】
//            --退单入库状态为【全部入库】
//            --【错收入库】为否 done

            // 判断平台id 防止有错误数据
            if (ObjectUtil.notEqual(PlatFormEnum.CARD_CODE.getCode(), ocBReturnOrder.getPlatform()) &&
                    ObjectUtil.notEqual(PlatFormEnum.CREATE_CARD_CODE.getCode(), ocBReturnOrder.getPlatform())) {
                return false;
            }
            // 判断类型
            // 电商销售退货
            Boolean flag = false;
            Integer proReturnStatus = ocBReturnOrder.getProReturnStatus();
            String businessTypeCode = ocBReturnOrder.getBusinessTypeCode();
            if (ObjectUtil.equals(businessTypeCode, OrderBusinessTypeCodeEnum.E_COMMERCE_SALE_ORDER_RETURN.getCode())) {
                // 判断退单入库状态
                if (ObjectUtil.equals(proReturnStatus, ProReturnStatusEnum.PORTION.getVal()) ||
                        ObjectUtil.equals(proReturnStatus, ProReturnStatusEnum.WHOLE.getVal())) {
                    flag = true;
                }
            } else if (pickUpList.contains(businessTypeCode)) {
                if (ObjectUtil.equals(proReturnStatus, ProReturnStatusEnum.WHOLE.getVal())) {
                    flag = true;
                }
            }

            if (flag) {
                // 构造入库通知奶卡系统数据
                NaiKaRefundStockInRequest request = new NaiKaRefundStockInRequest();
                request.setTid(ocBReturnOrder.getTid());
                request.setRefundNo(ocBReturnOrder.getReturnId());
                List<OcBReturnOrderRefund> ocBReturnOrderRefundList =
                        ocBReturnOrderRefundMapper.selectByOcOrderId(ocBReturnOrder.getId());
                List<NaiKaRefundStockInItemRequest> itemRequestList = new ArrayList<>();
                for (OcBReturnOrderRefund ocBReturnOrderRefund : ocBReturnOrderRefundList) {
                    NaiKaRefundStockInItemRequest itemRequest = new NaiKaRefundStockInItemRequest();
                    itemRequest.setSubOrderId(ocBReturnOrderRefund.getOid());
                    itemRequest.setNum(ocBReturnOrderRefund.getQtyIn());
                    itemRequest.setActNum(ocBReturnOrderRefund.getQtyRefund());
                    itemRequest.setSkuCode(ocBReturnOrderRefund.getPsCSkuEcode());
                    itemRequestList.add(itemRequest);
                }
                request.setRefundStockInItemRequestList(itemRequestList);
                request.setPlatformCode(cpCPlatform.getEcode());
                OcBReturnOrder updateOcBReturnOrder = new OcBReturnOrder();
                try {
                    ValueHolderV14 valueHolderV14 = naiKaOrderCmd.orderRefundStockIn(request);
                    if (valueHolderV14.isOK()) {
                        updateOcBReturnOrder.setId(ocBReturnOrder.getId());
                        updateOcBReturnOrder.setToNaikaStatus(ReturnNaiKaStatusEnum.PUSHED.getStatus());
                        updateOcBReturnOrder.setModifieddate(new Date());
                        ocBReturnOrderMapper.updateById(updateOcBReturnOrder);
                        return true;
                    } else {
                        updateOcBReturnOrder.setId(ocBReturnOrder.getId());
                        updateOcBReturnOrder.setToNaikaStatus(ReturnNaiKaStatusEnum.PUSH_FAIL.getStatus());
                        updateOcBReturnOrder.setModifieddate(new Date());
                        ocBReturnOrderMapper.updateById(updateOcBReturnOrder);
                    }
                } catch (Exception e) {
                    log.error("error to push refund stock in to naika, id:{}, msg:{}", id, e.getMessage());
                    updateOcBReturnOrder.setId(ocBReturnOrder.getId());
                    updateOcBReturnOrder.setToNaikaStatus(ReturnNaiKaStatusEnum.PUSH_FAIL.getStatus());
                    updateOcBReturnOrder.setModifieddate(new Date());
                    ocBReturnOrderMapper.updateById(updateOcBReturnOrder);
                }
            }
            return false;
        }
    }
}
