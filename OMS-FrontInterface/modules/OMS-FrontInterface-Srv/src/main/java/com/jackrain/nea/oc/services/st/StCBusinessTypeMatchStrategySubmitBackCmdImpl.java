package com.jackrain.nea.oc.services.st;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.st.StCBusinessTypeMatchStrategySubmitBackCmd;
import com.jackrain.nea.st.service.StCBusinessTypeMatchStrategyService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @program: r3-oc-oms
 * @description: 业务类型匹配策略提交
 * @author: caomalai
 * @create: 2022-07-16 14:28
 **/
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class StCBusinessTypeMatchStrategySubmitBackCmdImpl extends CommandAdapter implements StCBusinessTypeMatchStrategySubmitBackCmd {
    @Autowired
    private StCBusinessTypeMatchStrategyService stCBusinessTypeMatchStrategyService;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        return stCBusinessTypeMatchStrategyService.submitBack(querySession);
    }
}
