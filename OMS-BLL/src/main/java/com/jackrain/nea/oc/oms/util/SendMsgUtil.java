package com.jackrain.nea.oc.oms.util;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.oc.oms.model.table.OcBMsgSendRecord;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.LinkedMultiValueMap;


/**
 * <AUTHOR> ruan.gz
 * @Description :
 * @Date : 2020/6/11
 **/
@Slf4j
@Repository
public class SendMsgUtil {

    @Autowired
    private PropertiesConf propertiesConf;

    public Boolean sendMessage(OcBMsgSendRecord task) {
        String isOpen = propertiesConf.getProperty("send.msg.task");
        if (StringUtils.isEmpty(isOpen) || isOpen.equals("false")) {
            log.debug(LogUtil.format("发送短信未开启开关", "发送短信未开启开关"));
            return false;
        }

        String userId = propertiesConf.getProperty("send.msg.userid");
        if (StringUtils.isEmpty(userId)) {
            log.debug(LogUtil.format("发送短信未配置userid"));
            return false;
        }

        String password = propertiesConf.getProperty("send.msg.password");
        if (StringUtils.isEmpty(password)) {
            log.debug(LogUtil.format("发送短信未配置password"));
            return false;
        }
        LinkedMultiValueMap map = new LinkedMultiValueMap<>();
        map.add("userId", userId);
        map.add("password", password);
        map.add("iMobiCount", "1");
        map.add("MsgId", System.currentTimeMillis() + "");
        map.add("pszMobis", task.getReceiverMobile());
        map.add("pszMsg", task.getContent());
        log.info(LogUtil.format("发送短信参数:{}", "发送短信参数"), JSONObject.toJSONString(map));
        String respose = HttpRestUtil.httpPostUrl("http://61.130.7.220:8023/MWGate/wmgw.asmx/MongateSendSubmit", map);
        log.info(LogUtil.format("短信接口返回结果:{}", "短信接口返回结果"), JSONObject.toJSONString(respose));
        return true;
    }

}
