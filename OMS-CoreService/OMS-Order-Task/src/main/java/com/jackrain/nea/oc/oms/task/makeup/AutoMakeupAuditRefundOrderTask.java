package com.jackrain.nea.oc.oms.task.makeup;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.model.util.AdParamUtil;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.process.audit.OrderAuditProcess;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.RuntimeCompute;
import com.jackrain.nea.util.Tools;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 自动审核退换货订单补偿定时任务（OrderType=换货订单）
 *
 * @author: chenxiulou
 * @since: 2019/3/27
 * create at : 2019/3/27 15:06
 */
@Slf4j
@Component
public class AutoMakeupAuditRefundOrderTask extends BaseR3Task implements IR3Task {

    @Autowired
    private OmsOrderService orderService;

    @Autowired
    private OrderAuditProcess orderAuditProcess;
    @Autowired
    private AutoMakeupAuditOrderTask makeupAuditOrderTask;

    @Override
    @XxlJob("AutoMakeupAuditRefundOrderTask")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult result = new RunTaskResult();
        try {
            RuntimeCompute runtimeCompute = new RuntimeCompute();
            runtimeCompute.startRuntime();
            //默认60分钟 条件：换货类型且已入库
            Long makeupAuditTime = Tools.getLong(AdParamUtil.getParam("oms.oc.order.autoMakeupAudit.time"), 60);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("订单（换货）审核补偿任务控制拉取时间##单位:分钟##makeupAuditTime:{}"), makeupAuditTime);
            }
            //查询自动审核状态为1[审核失败]的订单and自动审核状态为3[审核中]但是更新时间间隔大于3600s的数据
            int pullNum = Tools.getInt(AdParamUtil.getParam("oms.oc.order.autoMakeupAudit.pull.num"), 200);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("订单（换货）自动审核补偿单次最大拉取记录数{}"), pullNum);
            }
            List<Long> orderIdList = ES4Order.findIdByStatusAndTypeAndInterceptAndInStorage(0, pullNum, makeupAuditTime);

            if (log.isDebugEnabled()){
                log.debug(LogUtil.format("订单（换货）自动审核需要补偿的数量{}"), orderIdList.size());
            }

            List<OcBOrderRelation> orderRelationList = new ArrayList<>();
            for (Long orderId : orderIdList) {
                log.debug("订单（换货）orderId" + " -- " + orderId + "启动补偿任务审单");
                OcBOrderRelation orderRelation = this.orderService.selectOmsOrderInfo(orderId);
                if (orderRelation == null) {
                    String errorMessage = Resources.getMessage("AutoMakeupAuditRefundOrderTask Order NotExist!OrderId="
                            + orderId);
                    log.error(LogUtil.format(errorMessage));
                } else {
                    orderRelationList.add(orderRelation);
                }
            }
            threadOrderProcessor.startMultiThreadExecute(this.orderAuditProcess, orderRelationList);
            result.setSuccess(true);
            double usedTime = runtimeCompute.endRuntime();
            log.debug(LogUtil.format("自动审核订单（换货）补偿定时任务执行成功!UsedTime={}"), usedTime);
            result.setSuccess(true);
            result.setMessage("自动审核订单（换货）补偿定时任务执行成功!UsedTime=" + usedTime);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(LogUtil.format("AutoMakeupAuditRefundOrderTask.Execute Error: {}"), Throwables.getStackTraceAsString(ex));
            result.setSuccess(false);
            result.setMessage(ex.getMessage());
        }
        return result;
    }

}
