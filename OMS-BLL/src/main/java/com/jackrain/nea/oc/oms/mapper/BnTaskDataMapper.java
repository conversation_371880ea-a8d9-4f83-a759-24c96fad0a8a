package com.jackrain.nea.oc.oms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jackrain.nea.oc.oms.model.table.BnTaskData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * @InterfaceName BnTaskDataMapper
 * @Description 班牛工单数据Mapper接口
 * <AUTHOR>
 * @Date 2025/6/17 10:45
 * @Version 1.0
 */
@Mapper
public interface BnTaskDataMapper extends BaseMapper<BnTaskData> {

    /**
     * 更新任务状态
     * @param id
     * @param status
     * @return
     */
    @Update("UPDATE bn_task_data SET process_status = #{status}, update_time = now() WHERE id = #{id}")
    int updateStatus(Long id, Integer status);

    // 根据id的集合 来将任务状态修改
    @Update("<script> "
            + "UPDATE bn_task_data SET process_status = #{status}, update_time = now() WHERE id in "
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    int batchUpdateStatus(@Param("ids") List<Long> ids, @Param("status") Integer status);
}