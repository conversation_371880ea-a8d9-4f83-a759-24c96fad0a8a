package com.jackrain.nea.oc.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.UsersDO;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.OcBReturnAfSendItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnAfSendMapper;
import com.jackrain.nea.oc.oms.mapper.StCBusinessTypeMapper;
import com.jackrain.nea.oc.oms.model.AfterDeliveryRefundOrderImportDTO;
import com.jackrain.nea.oc.oms.model.enums.ReturnAfSendReturnBillTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.table.OcBReturnAfSend;
import com.jackrain.nea.oc.oms.model.table.OcBReturnAfSendItem;
import com.jackrain.nea.oc.oms.model.table.StCBusinessType;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.UserImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.NumberFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@RestController
@Api(value = "OcBAfterDeliveryRefundOrderImportController", tags = "已发货退款单导入")
@Slf4j
public class OcBAfterDeliveryRefundOrderImportController {

    public static final String cellStr = "cell_";
    public static final String rowStr = "row_";
    public static final int NUMERIC = 0;
    public static final int STRING = 1;
    public static final int FORMULA = 2;
    private static final NumberFormat nf = NumberFormat.getInstance();

    @Autowired
    private OcBReturnAfSendMapper returnAfSendMapper;
    @Autowired
    private OcBReturnAfSendItemMapper returnAfSendItemMapper;
    @Autowired
    private StCBusinessTypeMapper stCBusinessTypeMapper;
    @Autowired
    private BuildSequenceUtil sequenceGenUtil;
    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private PsRpcService psRpcService;
    @Autowired
    private ApplicationContext applicationContext;

    @ApiOperation(value = "已发货退款单导入")
    @RequestMapping(path = "/api/cs/oc/oms/v1/importAfterDeliveryRefund", method = RequestMethod.POST)
    public ValueHolderV14 importAfterDeliveryRefund(HttpServletRequest request, @RequestParam(value = "file", required = true) MultipartFile file) {
        //获取当前登陆用户

        List<String> userNameList = new ArrayList<>();
        userNameList.add("rouyelishi");
        List<UsersDO> usersDOList = cpRpcService.queryUserByNames(userNameList);
        if (CollectionUtils.isEmpty(usersDOList)){
            throw new NDSException("用户不存在!");
        }
        User user = new UserImpl();
        UsersDO usersDO = usersDOList.get(0);
        ((UserImpl) user).setId(usersDO.getId().intValue());
        ((UserImpl) user).setEname(usersDO.getEname());
        ((UserImpl) user).setTruename(usersDO.getTruename());
        ((UserImpl) user).setClientId(37);
        ((UserImpl) user).setOrgId(27);
        ((UserImpl) user).setName(usersDO.getName());
        ValueHolderV14 holderV14 = new ValueHolderV14();

        try {
            if (file == null) {
                throw new NDSException("请求参数不能为空!");
            }
            InputStream inputStream = file.getInputStream();
            Workbook hssfWorkbook = null;
            try {
                hssfWorkbook = new XSSFWorkbook(inputStream);
            } catch (Exception ex) {
                try {
                    hssfWorkbook = new HSSFWorkbook(inputStream);
                } catch (Exception e) {
                    throw new NDSException("Excel文件格式错误!");
                }
            }

            if (hssfWorkbook.getNumberOfSheets() != 1) {
                throw new NDSException("已发货退款单导入模板不正确");
            }

            // 处理Excel数据
            List<AfterDeliveryRefundOrderImportDTO> importList = getImportList(hssfWorkbook);
            if (CollectionUtils.isEmpty(importList)) {
                throw new NDSException("导入数据不能为空!");
            }

            // 数据校验
            validateImportData(importList);

            // 构建并保存数据
            saveImportData(importList, user);

            holderV14.setCode(ResultCode.SUCCESS);
            holderV14.setMessage("导入成功");

        } catch (Exception ex) {
            log.error("已发货退款单导入失败", ex);
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("导入异常：" + ex.getMessage());
        }

        return holderV14;
    }

    private List<AfterDeliveryRefundOrderImportDTO> getImportList(Workbook workbook) {
        List<AfterDeliveryRefundOrderImportDTO> importList = new ArrayList<>();
        Sheet sheet = workbook.getSheetAt(0);
        int lastRowNum = sheet.getLastRowNum();

        // 从第二行开始读取数据（第一行为表头）
        for (int i = 1; i <= lastRowNum; i++) {
            Row row = sheet.getRow(i);
            if (row == null) continue;

            AfterDeliveryRefundOrderImportDTO dto = new AfterDeliveryRefundOrderImportDTO();
            int j = 0;
            String billNo = getCellStringValue(row.getCell(j++));
            if (StringUtils.isEmpty(billNo)){
                continue;
            }
            if (StringUtils.isNotEmpty(billNo) && billNo.startsWith("TK")) {
                billNo = "AF" + billNo.substring(2);
            }
            dto.setBillNo(billNo);
            dto.setShopCode(getCellStringValue(row.getCell(j++)));
            dto.setReturnStatus(getCellStringValue(row.getCell(j++)));
            dto.setPlatformOrderNo(getCellStringValue(row.getCell(j++)));
            dto.setPlatformRefundNo(getCellStringValue(row.getCell(j++)));
            dto.setBillType(getCellStringValue(row.getCell(j++)));
            dto.setPlatformRefundStatus(getCellStringValue(row.getCell(j++)));
            dto.setRefundReason(getCellStringValue(row.getCell(j++)));
            dto.setApplyRefundAmount(getCellBigDecimalValue(row.getCell(j++)));
            dto.setActualRefundAmount(getCellBigDecimalValue(row.getCell(j++)));
            dto.setApplyRefundTime(getCellDateValue(row.getCell(j++)));
            dto.setAuditTime(getCellDateValue(row.getCell(j++)));
            dto.setRemark(getCellStringValue(row.getCell(j++)));
            dto.setBusinessTypeCode(getCellStringValue(row.getCell(j++)));
            dto.setProductCode(getCellStringValue(row.getCell(j++)));
            dto.setApplyRefundQty(getCellBigDecimalValue(row.getCell(j++)));
            dto.setRefundAmount(getCellBigDecimalValue(row.getCell(j++)));
            dto.setTransactionAmount(getCellBigDecimalValue(row.getCell(j++)));
            dto.setRefundableAmount(getCellBigDecimalValue(row.getCell(j++)));
            dto.setPurchaseQty(getCellBigDecimalValue(row.getCell(j++)));
            dto.setRefundSuccessTime(getCellDateValue(row.getCell(j)));

            importList.add(dto);
        }

        return importList;
    }

    private void validateImportData(List<AfterDeliveryRefundOrderImportDTO> importList) {
        // 收集需要校验的编码
        Set<String> shopCodeSet = importList.stream().map(AfterDeliveryRefundOrderImportDTO::getShopCode).collect(Collectors.toSet());
        Set<String> businessCodeSet = importList.stream().map(AfterDeliveryRefundOrderImportDTO::getBusinessTypeCode).collect(Collectors.toSet());
        Set<String> productCodeSet = importList.stream().map(AfterDeliveryRefundOrderImportDTO::getProductCode).collect(Collectors.toSet());

        // 查询并校验店铺信息
        List<CpShop> cpShopList = cpRpcService.queryShopByCodeList(new ArrayList<>(shopCodeSet));
        if (shopCodeSet.size() != cpShopList.size()) {
            throw new NDSException("存在无效的店铺编码");
        }

        // 校验业务类型
        List<StCBusinessType> businessTypeList = stCBusinessTypeMapper.selectList(
                new LambdaQueryWrapper<StCBusinessType>()
                        .in(StCBusinessType::getEcode, businessCodeSet)
                        .eq(StCBusinessType::getIsactive, YesNoEnum.Y.getKey()));
        if (businessCodeSet.size() != businessTypeList.size()) {
            throw new NDSException("存在无效的业务类型编码");
        }

        // 查询并校验商品信息
        List<ProductSku> productSkuList = psRpcService.selectProductSkuIgnoreActive(new ArrayList<>(productCodeSet));
        if (productCodeSet.size() != productSkuList.size()) {
            throw new NDSException("存在无效的商品编码");
        }
    }

    private void saveImportData(List<AfterDeliveryRefundOrderImportDTO> importList, User user) {
        // 查询主数据信息
        Set<String> shopCodeSet = importList.stream().map(AfterDeliveryRefundOrderImportDTO::getShopCode).collect(Collectors.toSet());
        Set<String> businessCodeSet = importList.stream().map(AfterDeliveryRefundOrderImportDTO::getBusinessTypeCode).collect(Collectors.toSet());
        Set<String> productCodeSet = importList.stream().map(AfterDeliveryRefundOrderImportDTO::getProductCode).collect(Collectors.toSet());

        List<CpShop> cpShopList = cpRpcService.queryShopByCodeList(new ArrayList<>(shopCodeSet));
        List<StCBusinessType> businessTypeList = stCBusinessTypeMapper.selectList(
                new LambdaQueryWrapper<StCBusinessType>()
                        .in(StCBusinessType::getEcode, businessCodeSet)
                        .eq(StCBusinessType::getIsactive, YesNoEnum.Y.getKey()));
        List<ProductSku> productSkuList = psRpcService.selectProductSkuIgnoreActive(new ArrayList<>(productCodeSet));

        // 转换为Map方便查询
        Map<String, CpShop> shopMap = cpShopList.stream().collect(Collectors.toMap(CpShop::getEcode, Function.identity()));
        Map<String, StCBusinessType> businessTypeMap = businessTypeList.stream().collect(Collectors.toMap(StCBusinessType::getEcode, Function.identity()));
        Map<String, ProductSku> productMap = productSkuList.stream().collect(Collectors.toMap(ProductSku::getEcode, Function.identity()));

        // 按平台退款单号分组
        Map<String, List<AfterDeliveryRefundOrderImportDTO>> groupMap = importList.stream()
                .collect(Collectors.groupingBy(AfterDeliveryRefundOrderImportDTO::getPlatformRefundNo));

        List<OcBReturnAfSend> returnAfSendList = new ArrayList<>();
        List<OcBReturnAfSendItem> returnAfSendItemList = new ArrayList<>();

        for (Map.Entry<String, List<AfterDeliveryRefundOrderImportDTO>> entry : groupMap.entrySet()) {
            List<AfterDeliveryRefundOrderImportDTO> dtoList = entry.getValue();
            AfterDeliveryRefundOrderImportDTO firstDto = dtoList.get(0);

            // 构建主表数据
            OcBReturnAfSend returnAfSend = new OcBReturnAfSend();
            returnAfSend.setId(ModelUtil.getSequence("oc_b_return_af_send"));
            returnAfSend.setBillNo(firstDto.getBillNo());
            returnAfSend.setTid(firstDto.getPlatformOrderNo());
            returnAfSend.setTReturnId(firstDto.getPlatformRefundNo());
            returnAfSend.setReturnStatus(ReturnAfSendReturnBillTypeEnum.REFUNDCOMPLETED.getVal());
            returnAfSend.setAmtReturnActual(firstDto.getActualRefundAmount());
            returnAfSend.setAmtReturnApply(firstDto.getApplyRefundAmount());
            returnAfSend.setReason(firstDto.getRefundReason());
            returnAfSend.setRemark(firstDto.getRemark());

            // 设置店铺信息
            CpShop shop = shopMap.get(firstDto.getShopCode());
            returnAfSend.setCpCShopId(shop.getId());
            returnAfSend.setCpCShopEcode(shop.getEcode());
            returnAfSend.setCpCShopTitle(shop.getCpCShopTitle());

            // 设置业务类型信息
            StCBusinessType businessType = businessTypeMap.get(firstDto.getBusinessTypeCode());
            returnAfSend.setBusinessTypeCode(businessType.getEcode());
            returnAfSend.setBusinessTypeName(businessType.getEname());
            returnAfSend.setBusinessTypeId(businessType.getId());

            returnAfSend.setReturnApplyTime(firstDto.getApplyRefundTime());
            returnAfSend.setCheckTime(firstDto.getAuditTime());
            returnAfSend.setSysremark("肉业切换,手工导入");
            returnAfSend.setRefundOrderSourceType(2);
            returnAfSend.setHasGoodReturn(ReturnStatusEnum.COMPLETION.getVal());
            returnAfSend.setBillType(firstDto.getBillType().equals("仅退款") ? 1 : 0);

            BaseModelUtil.makeBaseCreateField(returnAfSend, user);
            returnAfSendList.add(returnAfSend);


            // 构建并保存明细表数据
            for (AfterDeliveryRefundOrderImportDTO dto : dtoList) {
                OcBReturnAfSendItem item = new OcBReturnAfSendItem();
                item.setId(ModelUtil.getSequence("oc_b_return_af_send_item"));
                item.setOcBReturnAfSendId(returnAfSend.getId());

                // 设置商品信息
                ProductSku product = productMap.get(dto.getProductCode());
                item.setPsCSkuId(product.getId());
                item.setPsCSkuEcode(product.getEcode());
                item.setPsCSkuEname(product.getName());
                item.setPsCProId(product.getId());
                item.setPsCProEcode(product.getEcode());
                item.setPsCProEname(product.getName());

                item.setQtyReturnApply(dto.getApplyRefundQty());
                item.setQtyIn(dto.getApplyRefundQty());
                item.setAmtReturn(dto.getRefundAmount());
                item.setAmtHasReturn(dto.getRefundAmount());
                item.setRemark(dto.getRemark());
                item.setBusinessTypeId(businessType.getId());
                item.setBusinessTypeName(businessType.getEname());
                item.setBusinessTypeCode(businessType.getEcode());
                item.setAmtActual(dto.getTransactionAmount());
                BaseModelUtil.makeBaseCreateField(item, user);
                returnAfSendItemList.add(item);
            }
        }
        applicationContext.getBean(OcBAfterDeliveryRefundOrderImportController.class).batchInsertReturnOrderAndRefund(returnAfSendList, returnAfSendItemList);
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchInsertReturnOrderAndRefund(List<OcBReturnAfSend> returnAfSendList, List<OcBReturnAfSendItem> returnAfSendItemList) {
        returnAfSendMapper.batchInsert(returnAfSendList);
        returnAfSendItemMapper.batchInsert(returnAfSendItemList);
    }

    private String getCellStringValue(Cell cell) {
        if (cell == null) return null;
        cell.setCellType(CellType.STRING);
        try {
            return StringUtils.trimToNull(cell.getStringCellValue());
        }catch (Exception e){
            return null;
        }
    }

    private BigDecimal getCellBigDecimalValue(Cell cell) {
        if (cell == null) return null;
        try {
            cell.setCellType(CellType.STRING);
            String value = StringUtils.trimToNull(cell.getStringCellValue());
            return value == null ? null : new BigDecimal(value);
        } catch (Exception e) {
            return null;
        }
    }

    private Date getCellDateValue(Cell cell) {
        if (cell == null) return null;
        try {
            cell.setCellType(CellType.STRING);
            String value = StringUtils.trimToNull(cell.getStringCellValue());
            return value == null ? null : new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(value);
        } catch (ParseException e) {
            return null;
        }
    }
}