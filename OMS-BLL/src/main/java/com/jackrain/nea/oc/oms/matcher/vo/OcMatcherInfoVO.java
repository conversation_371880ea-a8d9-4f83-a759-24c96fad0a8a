package com.jackrain.nea.oc.oms.matcher.vo;

import com.jackrain.nea.st.model.result.StCLiveCastStrategyAllResult;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 解析参数信息对象
 */
@Data
public class OcMatcherInfoVO<T> {

    // 入参：查询
    // 店铺ID
    Long cpCShopId;
    // 下单日期
    Date orderDate;
    // 支付日期
    Date payTime;

    // 入参：查询结果
    List<StCLiveCastStrategyAllResult> strategies;

    // 入参：匹配
    ParamInputVO inputVO;

    // 平台原单内容
    T originalOrderRelation;
    // 其他参数，如果需要
    Map<Object, Object> parameter;

    // 出参
    ParamOutputVO outputVO;

    // 以下两个参数不支持同一个单多线程并行
    Integer currentRuleType;
    String currentRuleContext;
    boolean currentMatchResult = false;

    /**
     * 初始化
     */
    private void initParameter() {
        if (Objects.isNull(parameter)) {
            parameter = new ConcurrentHashMap<>();
        }
    }

    /**
     * 设置参数
     *
     * @param key
     * @param value
     */
    public void setParam(Object key, Object value) {
        initParameter();
        parameter.put(key, value);
    }

    /**
     * 取参数
     *
     * @param key
     * @return
     */
    public Object getParam(Object key) {
        return parameter.get(key);
    }

    /**
     * 删除
     *
     * @param key
     * @return
     */
    public Object removeParam(Object key) {
        return parameter.remove(key);
    }

    /**
     * 清除
     */
    public void clearParams() {
        parameter.clear();
    }

}
