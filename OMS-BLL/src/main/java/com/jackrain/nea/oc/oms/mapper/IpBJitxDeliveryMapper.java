package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.IpBJitxDelivery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Date;
import java.util.List;

@Mapper
public interface IpBJitxDeliveryMapper extends ExtentionMapper<IpBJitxDelivery> {
    /**
     * REMARK最长数量。默认200字符
     */
    int MAX_REMARK_LENGTH = 300;
    /**
     * REMARK最长数量。默认200字符
     */
    int MAX_FEEDBACKMSG_LENGTH = 50;

    /**
     * 依据order_sn进行查询JITX待寻仓订单
     *
     * @param orderSn 平台订单号
     * @return 淘宝订单数据
     */
    @Select("SELECT * FROM ip_b_jitx_delivery WHERE order_sn=#{orderSn}")
    IpBJitxDelivery selectJitxOrderByOrderSn(@Param("orderSn") String orderSn);

    @Select("<script>"
            + "SELECT * FROM ip_b_jitx_delivery "
            + "WHERE order_sn IN "
            + "<foreach item='item' index='index' collection='orderSns' open='(' separator=',' close=')'>"
            + " #{item} "
            + "</foreach>"
            + "</script>")
    List<IpBJitxDelivery> selectJitxDeliveryByOrderNos(@Param("orderSns") List<String> orderSns);

    /**
     * 通过订单号批量更新 转换状态为 同步中(3)
     **/
    @Update("<script> "
            + "UPDATE ip_b_jitx_delivery SET synstatus = 3, " +
            "  modifierid = #{modifierId, jdbcType=BIGINT}, " +
            "  modifiername = #{modifierName, jdbcType=VARCHAR}, " +
            "  modifierename = #{modifierEName, jdbcType=VARCHAR}, " +
            "  modifieddate = #{date, jdbcType=DATE}, " +
            "  sysremark = #{remark, jdbcType=VARCHAR} " +
            "where order_sn in "
            + "<foreach item='item' index='index' collection='orderSns' open='(' separator=',' close=')'>"
            + " #{item} "
            + "</foreach>"
            + " and status = 'NEW' "
            + " and synstatus = 0 "
            + "</script>")
    int batchuUpdateList(@Param("orderSns") List<String> orderSns,
                         @Param("modifierId") Long modifierId,
                         @Param("modifierName") String modifierName,
                         @Param("modifierEName") String modifierEName,
                         @Param("date") Date date,
                         @Param("remark") String remark);


    /**
     * 通过订单号批量更新 转换状态为
     **/
    @Update("<script> "
            + "UPDATE ip_b_jitx_delivery SET synstatus = #{synstatus, jdbcType=BIGINT}, " +
            "  modifieddate = #{date, jdbcType=DATE}, " +
            "  sysremark = #{remark, jdbcType=VARCHAR} " +
            "where order_sn in "
            + "<foreach item='item' index='index' collection='orderSns' open='(' separator=',' close=')'>"
            + " #{item} "
            + "</foreach>"
            + "</script>")
    int batchuUpdateDeliveryList(@Param("orderSns") List<String> orderSns,
                         @Param("synstatus") Integer synstatus,
                         @Param("date") Date date,
                         @Param("remark") String remark);


    @Select("<script>"
            + "SELECT * FROM ip_b_jitx_delivery "
            + "WHERE order_sn IN "
            + "<foreach item='item' index='index' collection='orderSns' open='(' separator=',' close=')'>"
            + " #{item} "
            + "</foreach>"
            + " and is_store_delivery = 1"
            + "</script>")
    List<IpBJitxDelivery> selectIsStoreJitxDeliveryByOrderNos(@Param("orderSns") List<String> orderSns);

}