package com.jackrain.nea.oc.oms.services.returnin;

import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.model.enums.IsToWmsEnum;
import com.jackrain.nea.oc.oms.model.enums.ProReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnOrderConfirmStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnStockInTimes;
import com.jackrain.nea.oc.oms.model.relation.OcReturnInRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsStockInMatchParam;
import com.jackrain.nea.oc.oms.model.relation.RefundInRelation;
import com.jackrain.nea.oc.oms.model.table.OcBRefundIn;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInLog;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInProductItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderActual;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderLog;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
import com.jackrain.nea.util.ThreadLocalUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2023/3/3
 */
@Slf4j
@Component
public class OcReturnInB2BMultiStockIn extends OcReturnInB2BStockIn implements IReturnMatchStockIn {


    @Override
    public OmsStockInMatchParam handleBilProcessing(RefundInRelation inRelation, OcReturnInRelation stockIn) {
        User user = ThreadLocalUtil.users.get();
        OcBRefundIn refundIn = inRelation.getRefundIn();
        OcBReturnOrder sourceReturn = stockIn.getItem();
        // 生成退货单
        logStep("multi.prepare new bil");
        OmsStockInMatchParam param = OmsStockInMatchParam.build(user);
        OcBReturnOrder newReturn = inSupport.generateBil4MultiInStock(sourceReturn);

        // 退换货单商品明细
        prepareReturnItems(stockIn, newReturn, param);
        inCommService.preUpdateReturn4FistMatch(newReturn, newReturn, refundIn);
        inCommService.assignReturnConfirmInfo(newReturn, newReturn, user);

        //  退单退货状态
        inCommService.statisticsProReturnStatus(sourceReturn, stockIn.getSubItems());
        OcBReturnOrder modReturn = new OcBReturnOrder();
        Long srcReturnId = sourceReturn.getId();
        modReturn.setId(srcReturnId);
        modReturn.setProReturnStatus(sourceReturn.getProReturnStatus());
        param.setModReturn(modReturn);

        // 退货入库结果单
        List<OcBRefundInProductItem> inItems = stockIn.getSubInMatchItems();
        OcBRefundIn mdRefundIn = inCommService.preUpdateRefund4FistMatch(inRelation, newReturn, inItems);
        param.setModRefundIn(mdRefundIn);

        //  退货入库结果单商品明细
        Long newReturnId = newReturn.getId();
        Map<String, BigDecimal> logSkuStatisticsMapping = new HashMap<>();
        for (OcBRefundInProductItem inItem : inItems) {
            OcBRefundInProductItem modItem = inCommService.preUpdateRefundInItem4FistMatch(inItem, newReturnId);
            String skuCode = inItem.getPsCSkuEcode();
            String mark = inItem.getProductMark();
            inCommService.statisticInQty(logSkuStatisticsMapping, skuCode + "[" + mark + "]", inItem.getQty());
            param.addModRefundItem(modItem);
        }
        List<OcBReturnOrderActual> newActualItems = inCommService.generateActualItems(inRelation, newReturn, user);
        param.setInsActualItems(newActualItems);
        // 6. 退货入库结果单日志
        logStep("prepare log bil");
        String inSkuMessage = inCommService.statisticMatchedSkuMessage(logSkuStatisticsMapping);
        String msg = String.format("匹配退单:%d 成功,新增入库退货单:%d, 匹配条码:%s", srcReturnId, newReturnId, inSkuMessage);
        OcBRefundInLog matchInLog = inCommService.buildRefundInLog("自动匹配退单", msg, refundIn.getId(), user);
        OcBRefundInLog inStockLog = inCommService.preInsertRefundLog4FistMatch(refundIn, logSkuStatisticsMapping, user);
        param.addInsRefundLog(matchInLog).addInsRefundLog(inStockLog);
        // 7. 退货单日志
        String content = String.format("入库结果单:%d入库完成[TOB], 入库条码:%s", refundIn.getId(), inSkuMessage);
        OcBReturnOrderLog returnLog1 = inCommService.buildReturnOderLog("退货单新增", "入库结果单:" + refundIn.getId() + "匹配新增退货单:" + newReturnId, srcReturnId, user);
        OcBReturnOrderLog returnLog2 = inCommService.buildReturnOderLog("退货单新增", "匹配新增退货单,来源退单:" + srcReturnId, newReturnId, user);
        OcBReturnOrderLog returnLog3 = inCommService.buildReturnOderLog("退货单入库", content, newReturnId, user);
        param.addInsReturnLog(returnLog1).addInsReturnLog(returnLog2).addInsReturnLog(returnLog3);
        return param;
    }


    @Override
    public boolean persistDataAndStockIn(OmsStockInMatchParam inParam) {
        logStep("update data start");
        try {
            inCommService.atomicReturnMultiGenInNoticeResultAndUpdate(inParam);
            inCommService.updateReturnOrderNoticeInfo(inParam.getMatchedReturn());
        } catch (Exception ex) {
            logStep(OcReturnInSupport.expMsgFun.apply(ex));
            log.error(LogUtil.format("B2B匹配入库更新数据,调用库存流程异常:{}"), Throwables.getStackTraceAsString(ex));
            return false;
        }
        logStep("update data end");
        return true;
    }

    /**
     * 退换货单商品明细处理
     *
     * @param stockIn   退单匹配相关数据
     * @param newReturn 新退单
     * @param param     更新,调用参数
     */
    private void prepareReturnItems(OcReturnInRelation stockIn, OcBReturnOrder newReturn, OmsStockInMatchParam param) {
        Map<Long, Map<String, BigDecimal>> returnItemMatchedMapping = stockIn.getSubItemMatchedMapping();
        AssertUtil.notEmpty(returnItemMatchedMapping, "gen new returnItem,mapping empty");
        newReturn.setReturnAmtActual(BigDecimal.ZERO);
        List<OcBReturnOrderRefund> newReturnItems = new ArrayList<>();
        Map<Long, BigDecimal> mapping = stockIn.getSubItemQtyMapping();
        List<OcBReturnOrderRefund> subItems = stockIn.getSubMatchItems();
        for (OcBReturnOrderRefund subItem : subItems) {
            Long id = subItem.getId();
            BigDecimal qty = mapping.get(id);
            OcBReturnOrderRefund newReturnItem = inSupport.generateReturnItem4MultiInStock(subItem);
            inCommService.swapItemIdUseMapping(id, newReturnItem.getId(), returnItemMatchedMapping);
            newReturnItem.setQtyIn(qty);
            BigDecimal subtractQty = subItem.getQtyIn().subtract(qty);
            BigDecimal amtRefund = inCommService.reCalcReturnItemAmtRefund(newReturnItem,subtractQty);
            newReturnItem.setQtyRefund(qty);
            newReturn.setReturnAmtActual(newReturn.getReturnAmtActual().add(amtRefund));
            newReturnItem.setAmtRefund(amtRefund);
            newReturnItem.setQtyMatch(qty.longValue());
            newReturnItem.setOcBReturnOrderId(newReturn.getId());
            newReturnItems.add(newReturnItem);
            // 退货明细更新
            OcBReturnOrderRefund modReturnItm = inCommService.preUpdateReturnItem4FistMatch(subItem, false);
            modReturnItm.setQtyIn(null);
            param.addModReturnItem(modReturnItm);
        }
        newReturn.setShipAmt(BigDecimal.ZERO);
        newReturn.setExchangeAmt(BigDecimal.ZERO);
        newReturn.setReturnAmtOther(BigDecimal.ZERO);
        newReturn.setReturnAmtList(newReturn.getReturnAmtActual());
        newReturn.setProReturnStatus(ProReturnStatusEnum.WHOLE.getVal());
        newReturn.setConfirmStatus(ReturnOrderConfirmStatusEnum.NOT_CONFIRM.getKey());
        newReturn.setStoInNoticesId(null);
        newReturn.setStoInNoticesNo(null);
        newReturn.setIsNeedToWms(IsToWmsEnum.NO.val());
        param.stockInParam(newReturn, newReturnItems);
    }

    @Override
    public ReturnStockInTimes stockInTimes() {
        return ReturnStockInTimes.MULTI;
    }
}
