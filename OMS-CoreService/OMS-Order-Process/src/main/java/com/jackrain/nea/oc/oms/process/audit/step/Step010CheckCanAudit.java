package com.jackrain.nea.oc.oms.process.audit.step;

import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.config.OmsSystemConfig;
import com.jackrain.nea.oc.oms.model.enums.AutoAuditStatus;
import com.jackrain.nea.oc.oms.model.enums.OmsAuditFailedReason;
import com.jackrain.nea.oc.oms.model.enums.OmsEffectiveConditionEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.taobao.TaoBaoOrderStatus;
import com.jackrain.nea.oc.oms.services.audit.AuditStrategyHandlerFactory;
import com.jackrain.nea.oc.oms.services.audit.OmsOrderAutoAuditService;
import com.jackrain.nea.oc.oms.services.OmsOrderItemService;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.oc.oms.util.SplitMessageUtil;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.st.model.StCAutoCheck;
import com.jackrain.nea.st.model.StCAutoCheckRelation;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 判断店铺自动审核订单数据完整性
 *
 * @author: heliu
 * @since: 2019-03-27
 * create at : 2019-03-27 01:48
 */
@Step(order = 10, description = "判断店铺自动审核订单数据完整性")
@Slf4j
public class Step010CheckCanAudit extends BaseAuditOrderProcessStep implements IOmsOrderProcessStep<OcBOrderRelation> {

    @Autowired
    private OmsOrderAutoAuditService omsOrderAutoAuditService;

    @Autowired
    private OmsOrderItemService omsOrderItemService;

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private RedisOpsUtil<String, String> redisUtil;

    @Autowired
    AuditStrategyHandlerFactory auditStrategyHandlerFactory;

    @Autowired
    private OmsSystemConfig omsSystemConfig;

    @Override
    public ProcessStepResult<OcBOrderRelation> startProcess(OcBOrderRelation orderInfo,
                                                            ProcessStepResult preStepResult,
                                                            boolean isAutoMakeup, User operateUser) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("audit.自动流程Step010CheckCanAudit[判断店铺自动审核订单数据完整性]：{}",
                    "判断店铺自动审核订单数据完整性",orderInfo.getOrderId()), orderInfo.getOrderId());
        }
        /**
         * 查一次全订单信息，后续审核校验自行排除
         */
        OcBOrderRelation newInfo = omsOrderService.selectOmsOrderInfoOccupy(orderInfo.getOrderId());
        List<OcBOrderItem> orderItemList = newInfo.getOrderItemList();
        orderInfo.setOrderInfo(newInfo.getOrderInfo());
        orderInfo.setOrderItemList(orderItemList);
        //未退款的只要protype(0,4)
        orderInfo.setNoRefundOrderItems(omsOrderService.getUnSuccessRefundAudit(orderItemList));
        //暂时关闭 单独开关
        if (this.omsSystemConfig.isAuditOrderSkipAuditEnabled()) {
            return new ProcessStepResult<>(StepStatus.SUCCESS, null, "审核压测直接进入",
                    Step240FinshedAuditOrderProcess.class);
        }
        try {
            boolean isAudit = auditStrategyHandlerFactory.doHandle(orderInfo, operateUser);
            if (isAudit) {
                return new ProcessStepResult<>(StepStatus.SUCCESS, null, Step240FinshedAuditOrderProcess.class);
            }

            return new ProcessStepResult<>(StepStatus.FAILED, "自动审核失败");
        } catch (Exception e) {
            String message = "审核失败!" + e.getMessage();
            log.error(LogUtil.format("审核失败,异常信息:{}", "审核失败", orderInfo.getOrderId()), Throwables.getStackTraceAsString(e));
            omsOrderAutoAuditService.updateOrderInfo(orderInfo.getOmsMethod(), orderInfo.getOrderInfo(),message, OmsAuditFailedReason.ERROR_99);
            return new ProcessStepResult<>(StepStatus.FAILED, e.getMessage());
        }
    }

    @Deprecated
    private ProcessStepResult<OcBOrderRelation> oldAudit(OcBOrderRelation orderInfo) {
        //查询订单是否为待审核状态
        OcBOrder ocBOrder = omsOrderService.selectAuditOrderInfo(orderInfo.getOrderId());
        if (ocBOrder == null) {
            String message = "订单OrderId=" + orderInfo.getOrderId() + "重置回待审核状态,继续开始自动审核流程";
            OcBOrder ocBOrderDto = new OcBOrder();
            ocBOrderDto.setId(orderInfo.getOrderId());
            ocBOrderDto.setAutoAuditStatus(AutoAuditStatus.Audit_INIT.toInteger());
            ocBOrderDto.setSysremark(SplitMessageUtil.splitMesssage(message));
            omsOrderService.updateOrderInfo(ocBOrderDto);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("OrderId={},message={}",
                        orderInfo.getOrderInfo().getId()),  orderInfo.getOrderInfo().getId(), message);
            }
            return new ProcessStepResult<>(StepStatus.FINISHED,
                    "订单状态异常,退出审单。OrderId=" + orderInfo.getOrderId());
        }
        //判断非退款明细是否存在
        List<OcBOrderItem> orderItemList = omsOrderItemService.selectUnSuccessRefund(orderInfo.getOrderId());
        orderInfo.setNoRefundOrderItems(orderItemList);
        //判断明细数据是否存在
        if (CollectionUtils.isEmpty(orderItemList)) {
            String message = "订单OrderId=" + orderInfo.getOrderId() + "订单不存在明细数据或者全部明细已退款,审核失败!";
            omsOrderAutoAuditService.updateOrderInfo(orderInfo.getOmsMethod(), orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_02);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("订单不存在明细数据或者全部明细已退款,审核失败:id：{}",
                        "订单不存在明细数据或者全部明细已退款", orderInfo.getOrderId()), orderInfo.getOrderId());
            }
            return new ProcessStepResult<>(StepStatus.FINISHED, message);
        }
        //检查下单店铺是否为空
        if (orderInfo.getOrderInfo().getCpCShopId() == null) {
            String message = "订单OrderId=" + orderInfo.getOrderId() + "订单不存在下单店铺,审核失败!";
            omsOrderAutoAuditService.updateOrderInfo(orderInfo.getOmsMethod(), orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_03);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("订单不存在下单店铺,审核失败:id：{}",
                        "订单不存在下单店铺审核失败", orderInfo.getOrderId()), orderInfo.getOrderId());
            }
            return new ProcessStepResult<>(StepStatus.FINISHED, message);
        }
        //下单日期为空结束
        if (orderInfo.getOrderInfo().getOrderDate() == null) {
            String message = "订单OrderId=" + orderInfo.getOrderId() + "订单下单日期为空,审核失败!";
            omsOrderAutoAuditService.updateOrderInfo(orderInfo.getOmsMethod(), orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_04);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("订单下单日期为空,审核失败:id：{}",
                        "订单下单日期为空", orderInfo.getOrderId()), orderInfo.getOrderId());
            }
            return new ProcessStepResult<>(StepStatus.FINISHED, message);
        }
        //然后付款时间是否在策略生效时间(先查出策略)
        StCAutoCheckRelation autoCheckRelation = omsOrderAutoAuditService.isExitsStrategy(orderInfo);
        StCAutoCheck exitsStrategy = autoCheckRelation.getAutoCheck();
        orderInfo.setStCAutoCheck(exitsStrategy);
        String effectiveCondition = exitsStrategy.getEffectiveCondition();
        boolean isContainsEffectiveFlag = omsOrderAutoAuditService.isContainsEffectiveValue(effectiveCondition,
                OmsEffectiveConditionEnum.PAY_TIME.parseValue());
        if (isContainsEffectiveFlag) {
            if (omsOrderAutoAuditService.checkDateType(orderInfo)) {
                String message = "订单OrderId=" + orderInfo.getOrderId() + "订单店铺自动审核策略限制了付款时间,"
                        + "该订单的付款时间不在自动审核范围内,审核失败!";
                omsOrderAutoAuditService.updateOrderInfo(orderInfo.getOmsMethod(), orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_05);
                if (log.isDebugEnabled()) {
                    log.debug("订单OrderId={},订单店铺自动审核策略限制了付款时间,该订单的付款时间不在自动审核范围内,审核失败", orderInfo.getOrderId());
                }
                return new ProcessStepResult<>(StepStatus.FINISHED, message);
            }
        }

        //判断订单店铺是否启用全赠品订单自动审核
        if (!omsOrderAutoAuditService.checkOrderGift(orderInfo)) {
            String message = "订单OrderId=" + orderInfo.getOrderId() + "订单店铺未启用全赠品订单自动审核，审核失败!";
            omsOrderAutoAuditService.updateOrderInfo(orderInfo.getOmsMethod(), orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_06);
            if (log.isDebugEnabled()) {
                log.debug("订单OrderId={},订单店铺未启用全赠品订单自动审核，审核失败", orderInfo.getOrderId());
            }
            return new ProcessStepResult<>(StepStatus.FINISHED, message);
        }

        //判断是否启用手工新增订单自动审核
        if (!omsOrderAutoAuditService.checkManualOrder(orderInfo)) {
            String message = "订单OrderId=" + orderInfo.getOrderId() + "订单店铺未启用手工单自动审核，审核失败!";
            omsOrderAutoAuditService.updateOrderInfo(orderInfo.getOmsMethod(), orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_07);

            if (log.isDebugEnabled()) {
                log.debug("订单OrderId={},订单店铺未启用手工单自动审核，审核失败", orderInfo.getOrderId());
            }
            return new ProcessStepResult<>(StepStatus.FINISHED, message);
        }

        //订单中省、市未维护，不允许审核！
        if (StringUtils.isBlank(orderInfo.getOrderInfo().getCpCRegionProvinceEname())
                || StringUtils.isBlank(orderInfo.getOrderInfo().getCpCRegionCityEname())) {
            String message = "订单OrderId=" + orderInfo.getOrderId() + "订单中省或者市未维护,审核失败!";
            omsOrderAutoAuditService.updateOrderInfo(orderInfo.getOmsMethod(), orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_08);
            if (log.isDebugEnabled()) {
                log.debug("订单OrderId={},订单中省或者市未维护,审核失败", orderInfo.getOrderId());
            }
            return new ProcessStepResult<>(StepStatus.FINISHED, message);
        }

        //付款类型为空直接结束
        if (orderInfo.getOrderInfo().getPayType() == null) {
            String message = "订单OrderId=" + orderInfo.getOrderId() + "订单付款类型为空,审核失败!";
            omsOrderAutoAuditService.updateOrderInfo(orderInfo.getOmsMethod(), orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_10);
            if (log.isDebugEnabled()) {
                log.debug("订单OrderId={},订单付款类型为空,审核失败", orderInfo.getOrderId());
            }
            return new ProcessStepResult<>(StepStatus.FINISHED, message);
        }

        //判断发货仓库是否为空
        if (orderInfo.getOrderInfo().getCpCPhyWarehouseId() == null
                || orderInfo.getOrderInfo().getCpCPhyWarehouseId() == 0L) {
            String message = "订单OrderId=" + orderInfo.getOrderId() + "订单发货仓库为空,审核失败!";
            omsOrderAutoAuditService.updateOrderInfo(orderInfo.getOmsMethod(), orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_09);
            if (log.isDebugEnabled()) {
                log.debug("订单OrderId={},订单发货仓库为空,审核失败", orderInfo.getOrderId());
            }
            return new ProcessStepResult<>(StepStatus.FINISHED, message);
        }

        //判断物流公司是否为空
        if (orderInfo.getOrderInfo().getCpCLogisticsId() == null
                || orderInfo.getOrderInfo().getCpCLogisticsId() == 0L) {
            String message = "订单OrderId=" + orderInfo.getOrderId() + "订单发货物流为空,审核失败!";
            omsOrderAutoAuditService.updateOrderInfo(orderInfo.getOmsMethod(), orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_11);
            if (log.isDebugEnabled()) {
                log.debug("订单OrderId={},订单发货物流为空,审核失败", orderInfo.getOrderId());
            }
            return new ProcessStepResult<>(StepStatus.FINISHED, message);
        }

        //判断订单总额是否为空
        if (orderInfo.getOrderInfo().getOrderAmt() == null) {
            String message = "订单OrderId=" + orderInfo.getOrderId() + "订单总额为空,审核失败!";
            omsOrderAutoAuditService.updateOrderInfo(orderInfo.getOmsMethod(), orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_12);
            if (log.isDebugEnabled()) {
                log.debug("订单OrderId={},订单总额为空,审核失败", orderInfo.getOrderId());
            }
            return new ProcessStepResult<>(StepStatus.FINISHED, message);
        }

        //检查平台类型是否为空
        if (orderInfo.getOrderInfo().getPlatform() == null) {
            String message = "订单OrderId=" + orderInfo.getOrderId() + "订单平台类型为空,审核失败!";
            omsOrderAutoAuditService.updateOrderInfo(orderInfo.getOmsMethod(), orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_13);
            if (log.isDebugEnabled()) {
                log.debug("订单OrderId={},订单平台类型为空,审核失败", orderInfo.getOrderId());
            }
            return new ProcessStepResult<>(StepStatus.FINISHED, message);
        }

        //检查订单类型是否为空
        if (orderInfo.getOrderInfo().getOrderType() == null) {
            String message = "订单OrderId=" + orderInfo.getOrderId() + "订单类型为空,审核失败!";
            omsOrderAutoAuditService.updateOrderInfo(orderInfo.getOmsMethod(), orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_14);

            if (log.isDebugEnabled()) {
                log.debug("订单OrderId={},订单类型为空,审核失败", orderInfo.getOrderId());
            }
            return new ProcessStepResult<>(StepStatus.FINISHED, message);
        }
        //检验预售尾款未付订单
        String reserveVarchar03 = orderInfo.getOrderInfo().getStatusPayStep();
        if (TaoBaoOrderStatus.FRONT_PAID_FINAL_NOPAID.equals(reserveVarchar03)) {
            String message = "订单OrderId=" + orderInfo.getOrderId() + "预售尾款未付订单,审核失败!";
            omsOrderAutoAuditService.updateOrderInfo(orderInfo.getOmsMethod(), orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_15);
            if (log.isDebugEnabled()) {
                log.debug("订单OrderId={},预售尾款未付订单,审核失败", orderInfo.getOrderId());
            }

            return new ProcessStepResult<>(StepStatus.FINISHED, message);
        }

        return new ProcessStepResult<>(StepStatus.SUCCESS);
    }
}
