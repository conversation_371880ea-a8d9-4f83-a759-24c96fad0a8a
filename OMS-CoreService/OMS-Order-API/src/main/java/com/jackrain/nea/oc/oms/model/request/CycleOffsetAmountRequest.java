package com.jackrain.nea.oc.oms.model.request;

import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 周期购冲抵
 *
 * <AUTHOR>
 */
@Data
public class CycleOffsetAmountRequest implements Serializable {

    private static final long serialVersionUID = 8494878220757941444L;

    /**
     * 周期购订单
     */
    private OcBOrder cycleOcBOrder;

    /**
     * sku
     */
    private List<SkuModel> skuModels;

    @Data
    public static class SkuModel implements Serializable {
        /**
         * 商品编码
         */
        private String skuECode;

        /**
         * 冲抵金额
         */
        private BigDecimal offsetAmount;

        /**
         * 数量
         */
        private Integer qty;
    }

}