package com.jackrain.nea.oc.oms.nums;

import com.jackrain.nea.oc.oms.model.result.QueryOrderTableHeaderResult;

import java.util.ArrayList;
import java.util.List;

/**
 * 订单列表-表头
 *
 * @author: xiwen.z
 * create at: 2019/3/13 0013
 */
public enum OcOrderTableHeaderEnum {

    FLAG("旗帜", 1), TAG("订单标识", 2), SOURCECODE("平台信息", 3), GOODSINFO("商品信息", 4),
    USERNICK("买家昵称", 5), RECEIVERADDRESS("收货信息", 6), ID("订单编号", 7), SHOPTITLE("下单店铺", 8),
    WAREHOUSE("发货仓库", 9), STORENAME("下单店仓", 10), PLATFORM("平台", 11),
    ORDERTYPE("订单类型", 12), ORDERFROM("订单来源", 13), ORDESTATUS("订单状态", 14),
    OCCUPYSTATUS("订单占单状态", 15), LOGISTICSENAME("物流公司", 16), LOGISTICSECODE("物流编码", 17),
    ORDERDATE("下单时间", 18), PAYTIME("付款时间", 19), AUDITTIME("审核时间", 20),
    RECEIVEREMAIL("买家邮件地址", 21), RECEIVERZIP("收货人的邮编", 22), PAYTYPE("付款方式", 23),
    BUYERMESSAGE("买家留言", 24), SELLERMEMO("卖家备注", 25), ORIORDER("原始订单号", 26),
    ORIORDERRETURN("原始退货单号", 27), SYSTEMREMARK("系统备注", 28), MERGESOURCECODE("合并后发货的订单号", 29),
    NEWORDER("合并新单号", 30), SPLITORDER("拆分原单单号", 31), ISTODRP("是否生成调拨零售", 32),
    WMSCANCELSTATUS("wms撤回状态", 33), RETURNSTATUS("退货状态", 34), AUTOAUDITSTATUS("自动审核状态", 35),
    PRODUCTAMOUNT("商品总额", 36), PRODUCTDISCOUNTAMT("商品优惠金额", 37), ORDERDISCOUNTAMT("订单优惠金额", 38),
    ADJUSTAMT("调整金额", 39), SHIPAMT("配送费用", 40), SERVICEAMT("服务费", 41), ORDERAMT("订单总额", 42),
    RECEIVEDAMT("已收金额", 43), CONSIGNAMT("代销结算金额", 44), CONSIGNSHIPAMT("代销运费", 45),
    RECEIVAMT("应收金额", 46), CODAMT("到付代收金额", 47), OPERATEAMT("操作费", 48),
    JDRECEIVEAMT("应收平台金额（京东）", 49), JDSETTLEAMT("京东结算金额", 50), LOGISTICSCOST("物流成本", 51),
    INVOICEHEADER("开票抬头", 52), INVOICECONTENT("开票内容", 53), ISGENVOICENOTICE("是否生成开票通知", 54),
    WEIGHT("商品重量", 55), ENDTIME("交易结束时间", 56), SENDTIME("预计发货时间", 57),
    DISTRIBUTIONTIME("配货时间", 58);

    String text;
    int sort;

    OcOrderTableHeaderEnum(String tx, int n) {
        this.text = tx;
        this.sort = n;
    }

    public String getText() {
        return text;
    }

    public int getSort() {
        return sort;
    }

    /**
     * 转换为QueryOrderTableHeaderResult
     *
     * @return list
     */
    public static List<QueryOrderTableHeaderResult> convertToModel() {
        List<QueryOrderTableHeaderResult> list = new ArrayList<>();
        for (OcOrderTableHeaderEnum o : OcOrderTableHeaderEnum.values()) {
            QueryOrderTableHeaderResult qothr = new QueryOrderTableHeaderResult();
            //  qothr.setText(o.getText());
            qothr.setSort(o.getSort());
            list.add(qothr);
        }
        return list;
    }
}
