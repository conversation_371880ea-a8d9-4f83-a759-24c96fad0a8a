package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.api.AddGift;
import com.jackrain.nea.oc.oms.services.AddGiftService;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: 夏继超
 * @since: 2019/3/12
 * create at : 2019/3/12 20:55
 */
@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class AddGiftImpl implements AddGift {
    @Autowired
    AddGiftService addGiftService;

    @Override
    public ValueHolder addGift(JSONObject obj, User loginUser) {
        return addGiftService.addGift(obj, loginUser);
    }
}
