package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @ClassName MilkCardAmountOffsetOrder
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/7/31 20:11
 * @Version 1.0
 */
@TableName(value = "milk_card_amount_offset_order")
@Data
@Document(index = "milk_card_amount_offset_order", type = "milk_card_amount_offset_order")
@NoArgsConstructor(access = AccessLevel.PUBLIC)
@ApiModel(value = "milk_card_amount_offset_order", description = "奶卡提奶金额冲抵")
public class MilkCardAmountOffsetOrder extends BaseModel {

    @ApiModelProperty(value = "id")
    @Field(type = FieldType.Long)
    @JSONField(name = "ID")
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @ApiModelProperty(value = "零售发货单id")
    @Field(type = FieldType.Long)
    @JSONField(name = "OC_B_ORDER_ID")
    private Long ocBOrderId;

    @ApiModelProperty(value = "零售发货单单据编号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "BILL_NO")
    private String billNo;

    @ApiModelProperty(value = "奶卡卡号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CARD_CODE")
    private String cardCode;

    @ApiModelProperty(value = "SAP单据类型")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SAP_ORDER_TYPE")
    private String sapOrderType;

    @ApiModelProperty(value = "店铺id")
    @Field(type = FieldType.Long)
    @JSONField(name = "CP_C_SHOP_ID")
    private Long cpCShopId;

    @ApiModelProperty(value = "合并码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "COLLECT_CODE")
    private String collectCode;

    @ApiModelProperty(value = "汇总状态")
    @Field(type = FieldType.Integer)
    @JSONField(name = "COLLECT_STATUS")
    private Integer collectStatus;

    @ApiModelProperty(value = "异常原因")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "ERROR_MSG")
    private String errorMsg;

    @ApiModelProperty(value = "店铺编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CP_C_SHOP_ECODE")
    private String cpCShopEcode;

    @ApiModelProperty(value = "店铺名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CP_C_SHOP_ENAME")
    private String cpCShopEname;

    @ApiModelProperty(value = "汇总单号ID")
    @Field(type = FieldType.Long)
    @JSONField(name = "OC_B_SAP_SALES_DATA_GATHER_ID")
    private Long ocBSapSalesDataGatherId;

    /**
     * 业务类型名称
     */
    @ApiModelProperty(value = "业务类型名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "MIDDLEGROUND_BILL_TYPE_NAME")
    private String middlegroundBillTypeName;

    /**
     * 业务类型编码
     */
    @ApiModelProperty(value = "业务类型编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "MIDDLEGROUND_BILL_TYPE_CODE")
    private String middlegroundBillTypeCode;

    /**
     * 汇总类型
     */
    @ApiModelProperty(value = "汇总类型")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SUM_TYPE")
    private String sumType;

    @ApiModelProperty(value = "汇总状态名称")
    @JSONField(name = "COLLECT_STATUS_NAME")
    private String collectStatusName;

    @ApiModelProperty(value = "销售组织")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SALES_ORGANIZATION")
    private String salesOrganization;

    @ApiModelProperty(value = "出入库时间")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "IN_TIME")
    private Date inTime;

}
