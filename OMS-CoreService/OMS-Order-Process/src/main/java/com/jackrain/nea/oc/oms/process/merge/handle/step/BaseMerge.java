package com.jackrain.nea.oc.oms.process.merge.handle.step;

import com.jackrain.nea.oc.oms.model.MergeOrderInfo;
import com.jackrain.nea.oc.oms.model.relation.MergeOderGroups;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.process.merge.handle.MergeEnum;
import com.jackrain.nea.oc.oms.process.merge.handle.MergeStrategyHandler;
import com.jackrain.nea.oc.oms.services.OcMergeOrderService;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service("BaseMergeStrategy")
class BaseMerge implements MergeStrategyHandler {

    @Autowired
    private OcMergeOrderService ocMergeOrderService;

    /**
     * 校验合单基本信息  不对返回删除
     *
     * @param info
     * @return
     */
    @Override
    public Boolean doSingleHandle(MergeOrderInfo info) {
        MergeOderGroups mergeOderGroups = info.getMergeOderGroups();
        OcBOrder ocBOrder = info.getOcBOrder();
        String isPassCheck = ocMergeOrderService.checkBaseOrderInfo(ocBOrder, mergeOderGroups, false);
        if (StringUtils.isNotBlank(isPassCheck)) {
            if (log.isDebugEnabled()){
                log.debug(LogUtil.format("合单剔除原因:{}",
                        "合单剔除原因", ocBOrder.getId()), isPassCheck);
            }
            return true;
        }
        return false;
    }

    @Override
    public Integer getSort(String name) {
        return MergeEnum.getValueFromValueTag(name);
    }

    @Override
    public Boolean doWholeHandle(MergeOrderInfo info) {
        return false;
    }
}




