package com.jackrain.nea.oc.oms.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @author: 秦雄飞
 * @time: 2021/12/31 11:04 上午
 * @description: wms退货回传实体
 */

@Data
public class ReturnOrderConfirmDTO implements Serializable {

    private static final long serialVersionUID = 8400113891371976301L;

    @JSONField(name = "request")
    private Request request;

    @Data
    public static class Request implements Serializable {

        private static final long serialVersionUID = 7737149485545254663L;

        @JSONField(name = "returnOrder")
        private ReturnOrder returnOrder;

        @JSONField(name = "orderLines")
        private List<OrderLines> orderLines;
    }

    @Data
    public static class ReturnOrder implements Serializable {

        private static final long serialVersionUID = 304532087577471527L;

        @JSONField(name = "returnOrderCode")
        private String returnOrderCode;

        @JSONField(name = "returnOrderId")
        private String returnOrderId;

        @JSONField(name = "warehouseCode")
        private String warehouseCode;

        @JSONField(name = "outBizCode")
        private String outBizCode;

        @JSONField(name = "orderType")
        private String orderType;

        @JSONField(name = "orderConfirmTime")
        private Date orderConfirmTime;

        @JSONField(name = "logisticsCode")
        private String logisticsCode;

        @JSONField(name = "logisticsName")
        private String logisticsName;

        @JSONField(name = "expressCode")
        private String expressCode;

        @JSONField(name = "returnReason")
        private String returnReason;

        @JSONField(name = "remark")
        private String remark;

        @JSONField(name = "senderInfo")
        private SenderInfo senderInfo;
    }

    @Data
    public static class OrderLines implements Serializable {

        @JSONField(name = "orderLine")
        private OrderLine orderLine;
    }

    @Data
    public static class OrderLine implements Serializable {

        @JSONField(name = "remark")
        private String remark;

        @JSONField(name = "orderLineNo")
        private String orderLineNo;

        @JSONField(name = "sourceOrderCode")
        private String sourceOrderCode;

        @JSONField(name = "subSourceOrderCode")
        private String subSourceOrderCode;

        @JSONField(name = "ownerCode")
        private String ownerCode;

        @JSONField(name = "itemCode")
        private String itemCode;

        @JSONField(name = "itemId")
        private String itemId;

        @JSONField(name = "inventoryType")
        private String inventoryType;

        @JSONField(name = "planQty")
        private String planQty;

        @JSONField(name = "batchCode")
        private String batchCode;

        @JSONField(name = "actualQty")
        private BigDecimal actualQty;
    }

    @Data
    public static class SenderInfo implements Serializable {

        private static final long serialVersionUID = -3501695879486384226L;

        @JSONField(name = "company")
        private String company;

        @JSONField(name = "name")
        private String name;

        @JSONField(name = "zipCode")
        private String zipCode;

        @JSONField(name = "mobile")
        private String mobile;

        @JSONField(name = "detailAddress")
        private String detailAddress;
    }
}
