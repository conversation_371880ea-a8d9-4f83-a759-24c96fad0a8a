package com.jackrain.nea.oc.oms.process.transfer.impl.refund.vip;

import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.MakeupOrderType;
import com.jackrain.nea.oc.oms.model.relation.IpVipReturnOrderRelation;
import com.jackrain.nea.oc.oms.process.AbstractOrderProcess;
import com.jackrain.nea.oc.oms.util.LockOrderType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date ：Created in 15:33 2020/6/19
 * description ：唯品会退供单操作
 * @ Modified By：
 */
@Component
public class VipTransferReturnOrderProcessImpl extends AbstractOrderProcess<IpVipReturnOrderRelation> {

    public VipTransferReturnOrderProcessImpl() {
        super();
    }

    @Override
    protected String getChildPackageName() {
        return "vip";
    }

    @Override
    protected long getProcessOrderId(IpVipReturnOrderRelation orderInfo) {
        return orderInfo.getOrderId();
    }

    @Override
    protected String getProcessOrderNo(IpVipReturnOrderRelation orderInfo) {
        return orderInfo.getOrderNo();
    }

    @Override
    protected LockOrderType getCurrentLockOrderType() {
        return LockOrderType.TRANSFER_VIP_RETURN_ORDER;
    }

    @Override
    protected ChannelType getCurrentChannelType() {
        return ChannelType.VIPJITX;
    }

    @Override
    protected MakeupOrderType getCurrentMakeupOrderType() {
        return MakeupOrderType.TRANSFER_ORDER;
    }
}
