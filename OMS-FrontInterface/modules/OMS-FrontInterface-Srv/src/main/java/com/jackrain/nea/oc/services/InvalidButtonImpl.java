package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.InvalidButton;
import com.jackrain.nea.oc.oms.services.ReturnCancelService;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: 夏继超
 * @since: 2019/3/26
 * create at : 2019/3/26 15:57
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class InvalidButtonImpl implements InvalidButton {
    @Autowired
    ReturnCancelService cancelService;

    @Override
    public ValueHolder returnCancel(JSONObject param, User user) throws NDSException {
        return cancelService.execute(param, user);
    }
}
