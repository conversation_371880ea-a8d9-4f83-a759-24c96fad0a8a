package com.jackrain.nea.oc.oms.process;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Profile;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.List;
import java.util.Map;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest()
@Profile(value = "dev")
public class QueryWrapperTest {

    @Autowired
    private OcBOrderMapper orderMapper;

    /**
     * <p>
     * 下方获取到queryWrapper后删除的查询条件为cp_c_shop_ecode字段为null的and订单id大于等于12的and订单order_status字段不为null的
     * </p>
     */
    @Test
    public void delete() {
        QueryWrapper<OcBOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper
                .isNull("cp_c_shop_ecode")
                .ge("id", 381375)
                .isNotNull("order_status");
        int delete = orderMapper.delete(queryWrapper);
        System.out.println("delete return count = " + delete);
    }


    /**
     * <p>
     * 根据 entity 条件，查询一条记录,
     * 这里和上方删除构造条件一样，只是seletOne返回的是一条实体记录，当出现多条时会报错
     * </p>
     */
    @Test
    public void selectOne() {
        QueryWrapper<OcBOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", "12473");
        OcBOrder ocBOrder = orderMapper.selectOne(queryWrapper);
        System.out.println(ocBOrder);
    }


    /**
     * <p>
     * 根据 Wrapper 条件，查询总记录数
     * </p>
     */
    @Test
    public void selectCount() {
        QueryWrapper<OcBOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("order_status", "1");
        Integer count = orderMapper.selectCount(queryWrapper);
        System.out.println(count);
    }


    /**
     * <p>
     * 根据 entity 条件，查询全部记录
     * </p>
     */
    @Test
    public void selectList() {
        List<OcBOrder> list = orderMapper.selectList(null);
        System.out.println(list.size());
    }

    /**
     * <p>
     * 根据 Wrapper 条件，查询全部记录
     * </p>
     */
    @Test
    public void selectMaps() {
        QueryWrapper<OcBOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.isNotNull("source_code");
        List<Map<String, Object>> maps = orderMapper.selectMaps(queryWrapper);
        for (Map<String, Object> map : maps) {
            System.out.println(map);
        }
    }


    /**
     * <p>
     * 根据 entity 条件，查询全部记录（并翻页）
     * </p>
     */
    @Test
    public void selectPage() {
        Page<OcBOrder> page = new Page<>(1, 5);
        QueryWrapper<OcBOrder> queryWrapper = new QueryWrapper<>();
        IPage<OcBOrder> userIPage = orderMapper.selectPage(page, queryWrapper);
        System.out.println(userIPage);
    }

    /**
     * 打印结果
     * ==>  Preparing: SELECT COUNT(1) FROM user
     * ==> Parameters:
     * <==    Columns: COUNT(1)
     * <==        Row: 100
     * ==>  Preparing: SELECT id,name,age,email,status FROM user LIMIT 0,5
     * ==> Parameters:
     * <==    Columns: id, name, age, email, status
     * <==        Row: 1046282328366391319, lqf, 12, <EMAIL>, 0
     * <==        Row: 1046282328366391320, lqf, 12, <EMAIL>, 0
     * <==        Row: 1046282328366391321, lqf, 12, <EMAIL>, 0
     * <==        Row: 1046282328366391322, lqf, 12, <EMAIL>, 0
     * <==        Row: 1046282328366391323, lqf, 12, <EMAIL>, 0
     * <==      Total: 5
     *
     *
     * 这里需要在项目中加入分页插件
     *   @Bean
     *     public PaginationInterceptor paginationInterceptor() {
     *         return new PaginationInterceptor();
     *     }
     */


    /**
     * <p>
     * 根据 Wrapper 条件，查询全部记录（并翻页）
     * </p>
     */
    @Test
    public void selectMapsPage() {
        Page<OcBOrder> page = new Page<>(1, 5);
        QueryWrapper<OcBOrder> queryWrapper = new QueryWrapper<>();
        IPage<Map<String, Object>> mapIPage = orderMapper.selectMapsPage(page, queryWrapper);
        System.out.println(mapIPage);
    }

    /**
     * <p>
     * 根据 whereEntity 条件，更新记录
     * </p>
     */
    @Test
    public void update() {
        //修改值
        OcBOrder ocBOrder = new OcBOrder();
        ocBOrder.setSysremark("test");
        ocBOrder.setOrderStatus(1);
        //修改条件s
        UpdateWrapper<OcBOrder> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", "12473");
        int update = orderMapper.update(ocBOrder, updateWrapper);
        System.out.println(update);
    }
}