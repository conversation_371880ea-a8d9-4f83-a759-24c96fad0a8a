package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONArray;
import com.jackrain.nea.oc.oms.api.OcOrderModifyOutStockTypeCmd;
import com.jackrain.nea.oc.oms.services.OcOrderModifyOutStockTypeService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date ：Created in 13:23 2020/3/6
 * description ：
 * @ Modified By：
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcOrderModifyOutStockTypeCmdImpl implements OcOrderModifyOutStockTypeCmd {

    @Autowired
    private OcOrderModifyOutStockTypeService modifyOutStockTypeService;

    @Override
    public ValueHolderV14<JSONArray> modifyOutStockType(JSONArray idArray, int type, User user) {
        return modifyOutStockTypeService.modifyOutStockType(idArray, type, user);
    }
}
