package com.jackrain.nea.oc.oms.model.enums.kdzs;

import lombok.Data;
import lombok.Getter;
import lombok.ToString;

/**
 * KDZS-快递公司编码枚举
 * <AUTHOR>
 * @date 2022/4/2 10:16
 */
@Getter
public enum CpCodeEnum {

    YTO	(	"YTO"	,	"圆通快递"	)	,
    YUNDA	(	"YUNDA"	,	"韵达快递"	)	,
    ZTO	(	"ZTO"	,	"中通快递"	)	,
    ZTOKY	(	"ZTOKY"	,	"中通快运"	)	,
    STO	(	"STO"	,	"申通快递"	)	,
    HTKY	(	"HTKY"	,	"百世快递"	)	,
    BESTQJT	(	"BESTQJT"	,	"百世快运"	)	,
    JT	(	"JT"	,	"极兔快递"	)	,
    POSTB	(	"POSTB"	,	"邮政国内小包"	)	,
    POST	(	"POST"	,	"中国邮政"	)	,
    EMS	(	"EMS"	,	"EMS"	)	,
    SF	(	"SF"	,	"顺丰速运"	)	,
    FW	(	"FW"	,	"丰网速运"	)	,
    JD	(	"JD"	,	"京东快递"	)	,
    KYE	(	"KYE"	,	"跨越速运"	)	,
    DBKD	(	"DBKD"	,	"德邦快递"	)	,
    XFWL	(	"XFWL"	,	"信丰快递"	)	,
    UC	(	"UC"	,	"优速快递"	)	,
    TTKDEX	(	"TTKDEX"	,	"天天快递"	)	,
    EYB	(	"EYB"	,	"EMS快递包裹/邮政快递包裹"	)	,
    ZJS	(	"ZJS"	,	"宅急送"	)	,
    ANE56	(	"ANE56"	,	"安能"	)	,
    FAST	(	"FAST"	,	"快捷"	)	,
    DBL	(	"DBL"	,	"德邦物流"	)	,
    DBKY	(	"DBKY"	,	"德邦快运"	)	,
    ZTKY	(	"ZTKY"	,	"中铁物流/飞豹快递"	)	,
    CRE	(	"CRE"	,	"中铁快运"	)	,
    SURE	(	"SURE"	,	"速尔快递"	)	,
    UAPEX	(	"UAPEX"	,	"全一快递"	)	,
    CNEX	(	"CNEX"	,	"佳吉快运"	)	,
    CP449455	(	"CP449455"	,	"京广速运"	)	,
    JYM	(	"JYM"	,	"加运美速递"	)	,
    YDKY	(	"YDKY"	,	"韵达快运"	)	,
    FEDEX	(	"FEDEX"	,	"联邦快递"	)	,
    POSTBK	(	"POSTBK"	,	"邮政标准快递"	)	,
    RRS	(	"RRS"	,	"日日顺"	)	,
    SNWL	(	"SNWL"	,	"苏宁快递"	)	,
    ZMKM	(	"ZMKM"	,	"丹鸟"	)	,
    RFD	(	"RFD"	,	"如风达"	)	,
    YCKY	(	"YCKY"	,	"远成快运"	)	,
    JYSCM	(	"JYSCM"	,	"九曳供应链"	)	,
    CP468398	(	"CP468398"	,	"圆通承诺达"	)	,
    GTO	(	"GTO"	,	"国通"	)	,
    QFKD	(	"QFKD"	,	"全峰"	)	,
    SDSD	(	"SDSD"	,	"山东递速"	)	,
    CP443514	(	"CP443514"	,	"百世云配"	)	,
    LE10032270	(	"LE10032270"	,	"韵达同城"	)	,
    PADTF	(	"PADTF"	,	"平安达腾飞"	)	,
    YMDD	(	"YMDD"	,	"壹米滴答"	)	,
    HOAU	(	"HOAU"	,	"天地华宇"	)	,
    STOKY	(	"STOKY"	,	"申通快运"	)	,
    CP471906	(	"CP471906"	,	"顺心捷达"	)	,
    EWINSHINE	(	"EWINSHINE"	,	"万象物流"	)	,
    QRT	(	"QRT"	,	"全日通"	)	,
    GZFY	(	"GZFY"	,	"凡宇快递"	)	,
    XBWL	(	"XBWL"	,	"新邦物流"	)	,
    CAE	(	"CAE"	,	"民航快递"	)	,
    FEC	(	"FEC"	,	"银捷速递"	)	,
    SFWL	(	"SFWL"	,	"盛丰物流"	)	,
    HLWL	(	"HLWL"	,	"恒路物流"	)	,
    JIAYI	(	"JIAYI"	,	"佳怡物流"	)	,
    LTS	(	"LTS"	,	"联昊通"	)	,
    SZSA56	(	"SZSA56"	,	"圣安物流"	)	,
    SHENGHUI	(	"SHENGHUI"	,	"盛辉物流"	)	,
    YTZG	(	"YTZG"	,	"运通中港快递"	)	,
    HMJ	(	"HMJ"	,	"黄马甲配送"	)	,
    CHENGBANG	(	"CHENGBANG"	,	"晟邦物流"	)	,
    GZLT	(	"GZLT"	,	"飞远配送"	)	,
    HZABC	(	"HZABC"	,	"飞远(爱彼西)配送"	)	,
    JDYWL	(	"JDYWL"	,	"筋斗云物流"	)	,
    BJCS	(	"BJCS"	,	"城市一百物流"	)	,
    YCT	(	"YCT"	,	"黑猫宅急便"	)	,
    ONTRAC	(	"ONTRAC"	,	"OnTrac"	)	,
    CITYLINK	(	"CITYLINK"	,	"City-Link"	)	,
    COE	(	"COE"	,	"COE"	)	,
    DTW	(	"DTW"	,	"大田物流"	)	,
    EES	(	"EES"	,	"百福东方"	)	,
    MBEX	(	"MBEX"	,	"民邦快递"	)	,
    HQKY	(	"HQKY"	,	"华企快运"	)	,
    YFEXPRESS	(	"YFEXPRESS"	,	"越丰物流"	)	,
    AIR	(	"AIR"	,	"亚风速递"	)	,
    MANCOWL	(	"MANCOWL"	,	"万家物流"	)	,
    SZML56	(	"SZML56"	,	"明亮物流"	)	,
    CXCOD	(	"CXCOD"	,	"传喜物流"	)	,
    D4PX	(	"D4PX"	,	"递四方"	)	,
    TNT	(	"TNT"	,	"TNT"	)	,
    UPS	(	"UPS"	,	"UPS"	)	,
    USPS	(	"USPS"	,	"USPS"	)	,
    DHLCN	(	"DHLCN"	,	"DHL中国"	)	,
    EMSGJ	(	"EMSGJ"	,	"EMS国际"	)	,
    CNPOSTGJ	(	"CNPOSTGJ"	,	"邮政国际包裹"	)	,
    DHLDE	(	"DHLDE"	,	"DHL德国"	)	,
    KERRYEAS	(	"KERRYEAS"	,	"嘉里大通"	)	,
    DHL	(	"DHL"	,	"DHL全球"	)	,
    FEDEXUS	(	"FEDEXUS"	,	"FedEx美国"	)	,
    FEDEXCN	(	"FEDEXCN"	,	"FedEx中国"	)	,
    ARAMEX	(	"ARAMEX"	,	"Aramex"	)	,
    SANTAI	(	"SANTAI"	,	"三态速递"	)	,
    OCS	(	"OCS"	,	"OCS"	)	,
    MYAAE	(	"MYAAE"	,	"AAE全球专递"	)	,
    YWWL	(	"YWWL"	,	"燕文物流"	)	,
    ASENDIA	(	"ASENDIA"	,	"Asendia USA"	)	,
    RUSTON	(	"RUSTON"	,	"俄速通"	)	,
    XLOBO	(	"XLOBO"	,	"贝海国际速递"	)	,
    SPSR	(	"SPSR"	,	"中俄快递"	)	,
    SAD	(	"SAD"	,	"赛澳递"	)	,
    SUIJIAWL	(	"SUIJIAWL"	,	"穗佳物流"	)	,
    ROYALMAIL	(	"ROYALMAIL"	,	"英国皇家邮政"	)	,
    JKYZ	(	"JKYZ"	,	"捷克邮政"	)	,
    POSTSE	(	"POSTSE"	,	"瑞典邮政"	)	,
    BLYZ	(	"BLYZ"	,	"波兰邮政"	)	,
    FGYZ	(	"FGYZ"	,	"法国邮政"	)	,
    POSTTR	(	"POSTTR"	,	"土耳其邮政"	)	,
    POSTCL	(	"POSTCL"	,	"智利邮政"	)	,
    POSTBY	(	"POSTBY"	,	"白俄罗斯邮政"	)	,
    POSTES	(	"POSTES"	,	"西班牙邮政"	)	,
    POSTUA	(	"POSTUA"	,	"乌克兰邮政"	)	,
    POSTNO	(	"POSTNO"	,	"挪威邮政"	)	,
    POSTZA	(	"POSTZA"	,	"南非邮政"	)	,
    POSTPT	(	"POSTPT"	,	"葡萄牙邮政"	)	,
    POSTSA	(	"POSTSA"	,	"沙特邮政"	)	,
    POSTIN	(	"POSTIN"	,	"印度邮政"	)	,
    POSTBG	(	"POSTBG"	,	"保加利亚邮政"	)	,
    POSTAE	(	"POSTAE"	,	"阿联酋邮政"	)	,
    POSTAU	(	"POSTAU"	,	"澳大利亚邮政"	)	,
    POSTPK	(	"POSTPK"	,	"巴基斯坦邮政"	)	,
    POSTMT	(	"POSTMT"	,	"马耳他邮政"	)	,
    POSTLB	(	"POSTLB"	,	"黎巴嫩邮政"	)	,
    POSTMD	(	"POSTMD"	,	"摩尔多瓦邮政"	)	,
    POSTSRB	(	"POSTSRB"	,	"塞尔维亚邮政"	)	,
    POSTHR	(	"POSTHR"	,	"克罗地亚邮政"	)	,
    POSTAM	(	"POSTAM"	,	"亚美尼亚邮政"	)	,
    POSTMK	(	"POSTMK"	,	"马其顿邮政"	)	,
    POSTFI	(	"POSTFI"	,	"芬兰邮政"	)	,
    POSTAR	(	"POSTAR"	,	"阿根廷邮政"	)	,
    POSTSK	(	"POSTSK"	,	"斯洛伐克邮政"	)	,
    SERPOST	(	"SERPOST"	,	"秘鲁邮政"	)	,
    POSTIT	(	"POSTIT"	,	"意大利邮政"	)	,
    POSTSI	(	"POSTSI"	,	"斯洛文尼亚邮政"	)	,
    POSTHU	(	"POSTHU"	,	"匈牙利邮政"	)	,
    POSTMU	(	"POSTMU"	,	"毛里求斯邮政"	)	,
    POSTAT	(	"POSTAT"	,	"奥地利邮政"	)	,
    POSTAL	(	"POSTAL"	,	"阿尔巴尼亚邮政"	)	,
    POSTEE	(	"POSTEE"	,	"爱沙尼亚邮政"	)	,
    POSTLV	(	"POSTLV"	,	"拉脱维亚邮政"	)	,
    POSTCO	(	"POSTCO"	,	"哥伦比亚邮政"	)	,
    POSTBR	(	"POSTBR"	,	"巴西邮政"	)	,
    POSTCH	(	"POSTCH"	,	"瑞士邮政"	)	,
    BLSYZ	(	"BLSYZ"	,	"比利时邮政"	)	,
    RBYZEMS	(	"RBYZEMS"	,	"日本邮政"	)	,
    EPOST	(	"EPOST"	,	"韩国邮政"	)	,
    POSTTH	(	"POSTTH"	,	"泰国邮政"	)
    ;


    private String key;

    private String val;

    CpCodeEnum(String key, String val) {
        this.key = key;
        this.val = val;
    }

    public String getKey() {
        return key;
    }

    public String getVal() {
        return val;
    }

}
