package com.jackrain.nea.oc.oms.api.ac;

import com.jackrain.nea.ac.model.AcFCompensationReason;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.sys.domain.ValueHolderV14;

import java.util.List;

/**
 * @Author: anna
 * @CreateDate: 2020/7/10$ 20:52$
 * @Description: 赔付类型赔付原因
 */

public interface OmsAcFCompensationTypeCmd {

    ValueHolderV14<List<AcFCompensationReason>> queryAcFCompensationReasonById(Integer id)  throws NDSException;

}
