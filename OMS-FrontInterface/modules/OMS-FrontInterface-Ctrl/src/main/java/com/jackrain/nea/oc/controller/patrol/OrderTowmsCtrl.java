package com.jackrain.nea.oc.controller.patrol;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.api.patrol.OrderTowmsCmd;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2019-08-05 17:09
 * @Version 1.0
 */

@Api(value = "OrderTowmsCtrl", tags = "订单传wms")
@Slf4j
@RestController
public class OrderTowmsCtrl {


    @Autowired
    private OrderTowmsCmd orderTowmsCmd;


    @ApiOperation(value = "删除订单数据")
    @RequestMapping(value = "/api/cs/oc/oms/v1/towms", method = RequestMethod.GET)
    public String delectOrder(HttpServletRequest request, String ids, String type) {
        String s = orderTowmsCmd.towms(ids, type);
        return s;
    }

    @ApiOperation(value = "删除订单数据")
    @RequestMapping(value = "/api/cs/oc/oms/v1/towmsCompensate", method = RequestMethod.GET)
    public String towmsCompensate(HttpServletRequest request, Long id) {
        String s = orderTowmsCmd.towmsCompensate(id);
        return s;
    }

    @ApiOperation(value = "京东优惠金额修复")
    @RequestMapping(value = "/api/cs/oc/oms/v1/discountMoney", method = RequestMethod.GET)
    public String discountMoney(HttpServletRequest request, Long id) {
        List<Long> longs = orderTowmsCmd.discountMoney(id);
        return JSONObject.toJSONString(longs);
    }

}
