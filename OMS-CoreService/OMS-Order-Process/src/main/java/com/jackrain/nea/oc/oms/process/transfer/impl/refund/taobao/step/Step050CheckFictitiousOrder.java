package com.jackrain.nea.oc.oms.process.transfer.impl.refund.taobao.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.TransNodeTipEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsTaobaoRefundRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.process.transfer.impl.refund.taobao.util.OrderStatusUtil;
import com.jackrain.nea.oc.oms.refund.util.TransRefundNodeTipUtil;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @Author: 黄世新
 * @Date: 2020/3/8 12:57 下午
 * @Version 1.0
 */
@Step(order = 50, description = "判断订单是否为虚拟订单")
@Slf4j
@Component
public class Step050CheckFictitiousOrder extends BaseTaobaoRefundProcessStep
        implements IOmsOrderProcessStep<OmsTaobaoRefundRelation> {
    @Override
    public ProcessStepResult<OmsTaobaoRefundRelation> startProcess(OmsTaobaoRefundRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        List<OmsOrderRelation> omsOrderRelations = orderInfo.getOmsOrderRelation();
        IpBTaobaoRefund ipBTaobaoRefund = orderInfo.getIpBTaobaoRefund();
        //判断订单是否为虚拟订单
        OcBOrder ocBOrder = omsOrderRelations.get(0).getOcBOrder();
        //判断订单是否为虚拟订单
        if (!Objects.equals(OrderTypeEnum.DIFFPRICE.getVal(), ocBOrder.getOrderType())) {
            return new ProcessStepResult<>(StepStatus.SUCCESS, "不为虚拟订单,继续下一阶段转换");
        }
        //按子订单维度创建发货后退款单（仅退款）
        List<OcBOrderItem> orderItems = new ArrayList<>();
        for (OmsOrderRelation orderRelation : omsOrderRelations) {
            boolean andVoid = OrderStatusUtil.checkOrderIsCancelAndVoid(orderRelation.getOcBOrder().getOrderStatus());
            if (andVoid) {
                continue;
            }
            orderItems.addAll(orderRelation.getOcBOrderItems());
        }
        //根据退款单号查询退款
        omsRefundOrderService.foundRefundSlipAfterRefundOnly(orderItems, ocBOrder, ipBTaobaoRefund, operateUser);
        omsReturnOrderService.giftsThenSend(omsOrderRelations,orderInfo.getIsGiftOrderRelation(),orderInfo.getIntermediateTableRelation(),operateUser);

        String remark = "生成发货后退款单(仅退款)成功";
        TransRefundNodeTipUtil.taoBaoTransTipCAS(ipBTaobaoRefund, TransNodeTipEnum.DEFAULT);
        ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                remark, ipBTaobaoRefund);
        return new ProcessStepResult<>(StepStatus.FINISHED, remark);
    }
}
