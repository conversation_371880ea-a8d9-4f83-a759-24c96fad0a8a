package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderRefundMapper;
import com.jackrain.nea.oc.oms.model.enums.ProReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @ClassName OcBReturnOrderUpdateService
 * @Description 退换货单
 * <AUTHOR>
 * @Date 2025/4/22 13:53
 * @Version 1.0
 */
@Slf4j
@Component
public class OcBReturnOrderUpdateService {

    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;

    @Autowired
    private OcBReturnOrderRefundMapper ocBReturnOrderRefundMapper;

    /**
     * 更新退货商品表和退换货单状态
     * i. oc_b_return_order_refund退货商品表
     *    1. qty_in入库数量 = qty_refund申请数量
     * ii. oc_b_return_order退换货单
     *    1. return_status 单据状态 = 完成
     *    2. pro_return_status 退货状态 = 全部入库
     *
     * @param billNo 退换货单编号
     * @return 是否更新成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateReturnOrderCompleted(String billNo) {
        log.info("开始更新退换货单状态, billNo={}", billNo);

        // 1. 查询退换货单
        OcBReturnOrder returnOrder = ocBReturnOrderMapper.selectByBillNo(billNo);
        if (returnOrder == null) {
            log.error("退换货单不存在, billNo={}", billNo);
            throw new NDSException("退换货单不存在");
        }

        Long returnOrderId = returnOrder.getId();
        try {

            // 2. 查询退货商品明细
            List<OcBReturnOrderRefund> refundItems = ocBReturnOrderRefundMapper.selectByOcOrderId(returnOrderId);
            if (refundItems == null || refundItems.isEmpty()) {
                log.error("退货商品明细不存在, billNo={}, returnOrderId={}", billNo, returnOrderId);
                throw new NDSException("退货商品明细不存在");
            }

            // 3. 更新退货商品表 - 将入库数量设置为申请数量
            for (OcBReturnOrderRefund refundItem : refundItems) {
                OcBReturnOrderRefund updateItem = new OcBReturnOrderRefund();
                updateItem.setId(refundItem.getId());
                updateItem.setOcBReturnOrderId(returnOrderId);
                updateItem.setQtyIn(refundItem.getQtyRefund()); // 入库数量 = 申请数量
                // 保持退款金额不变
                updateItem.setAmtRefund(refundItem.getAmtRefund());
                // 使用更新入库数量的专用方法
                ocBReturnOrderRefundMapper.updateReturnOdrRefundQtyAndAmt(updateItem);
            }

            // 4. 更新退换货单状态
            OcBReturnOrder updateOrder = new OcBReturnOrder();
            updateOrder.setId(returnOrderId);
            updateOrder.setReturnStatus(ReturnStatusEnum.WAIT_AFTERSALE_ENSURE.getVal()); // 单据状态 = 等待售后确认
            updateOrder.setProReturnStatus(ProReturnStatusEnum.WHOLE.getVal()); // 退货状态 = 全部入库
            // 使用更新退换货单信息的专用方法
            ocBReturnOrderMapper.updateOcBreturnOrderInfoById(updateOrder);

            log.info("退换货单状态更新成功, billNo={}, returnOrderId={}", billNo, returnOrderId);
            return true;
        } catch (Exception e) {
            log.error("更新退换货单状态失败, billNo={}, returnOrderId={}, error={}", billNo, returnOrderId, e.getMessage(), e);
            throw e;
        }
    }
}
