package com.jackrain.nea.oc.oms.model.enums;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * Description： 待传结算标志
 * Author: RESET
 * Date: Created in 2020/6/15 21:39
 * Modified By:
 */
public enum ToACStatusEnum {

    // 匹配策略类型
    INIT(0, "init", "初始默认值"),
    PENDING(1, "pending", "待传"),
    SUCCESS(2, "success", "已传"),
    FAILED(3, "failed", "失败"),
    ERROR(4, "error", "错误，比如数据不符合校验规则之类的"),
    ;

    @Getter
    private Integer value;
    @Getter
    private String code;
    @Getter
    private String description;

    ToACStatusEnum(Integer value, String code, String description) {
        this.value = value;
        this.code = code;
        this.description = description;
    }

    public Integer val() {
        return this.getValue();
    }

    /**
     * 加取Long值
     *
     * @return
     */
    public Long getLongValue() {
        return Long.valueOf(this.value.toString());
    }

    @Override
    public String toString() {
        return String.valueOf(value);
    }

    public static ToACStatusEnum fromValue(Integer v) {
        for (ToACStatusEnum c : ToACStatusEnum.values()) {
            if (Objects.equals(v, c.value)) {
                return c;
            }
        }
        throw new IllegalArgumentException(String.valueOf(v));
    }

    /**
     * 结算状态
     *
     * @return Map
     */
    public static Map<Long, String> toMap() {
        Map<Long, String> m = new HashMap<>();
        for (ToACStatusEnum o : ToACStatusEnum.values()) {
            m.put(Long.valueOf(o.getValue()), o.getDescription());
        }
        m.put(null, "");
        return m;
    }


}
