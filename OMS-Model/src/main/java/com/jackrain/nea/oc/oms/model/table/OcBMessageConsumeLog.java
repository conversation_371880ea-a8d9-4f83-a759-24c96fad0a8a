package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

/**
 * Description： 消息消费日志表
 * Author: RESET
 * Date: Created in 2020/8/23 16:07
 * Modified By:
 */
@TableName(value = "oc_b_message_consume_log")
@Data
@Document(index = "oc_b_message_consume_log", type = "oc_b_message_consume_log")
public class OcBMessageConsumeLog extends BaseModel {

    @JSONField(name = "ID")
    @Field(type = FieldType.Long)
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * messageMethod 标准接口字段，标识消息类型
     */
    @JSONField(name = "METHOD")
    @Field(type = FieldType.Keyword)
    private String method;

    /**
     * 单据类型
     */
    @JSONField(name = "BILL_TYPE")
    @Field(type = FieldType.Integer)
    private Integer billType;

    /**
     * 单据编码
     */
    @JSONField(name = "BILL_NO")
    @Field(type = FieldType.Keyword)
    private String billNo;

    /**
     * 消息topic
     */
    @JSONField(name = "TOPIC")
    @Field(type = FieldType.Keyword)
    private String topic;

    /**
     * 消息tag
     */
    @JSONField(name = "TAG")
    @Field(type = FieldType.Keyword)
    private String tag;

    /**
     * 消息key
     */
    @JSONField(name = "MESSAGE_KEY")
    @Field(type = FieldType.Keyword)
    private String messageKey;

    /**
     * 消息ID -- 消息中间件生成
     */
    @JSONField(name = "MESSAGE_ID")
    @Field(type = FieldType.Keyword)
    private String messageId;

    /**
     * 消费状态
     */
    @JSONField(name = "CONSUME_STATUS")
    @Field(type = FieldType.Integer)
    private Integer consumeStatus;

    /**
     * 重试次数
     */
    @JSONField(name = "RETRY_COUNT")
    @Field(type = FieldType.Integer)
    private Integer retryCount;

    /**
     * 异常信息
     */
    @JSONField(name = "ERROR_MSG")
    @Field(type = FieldType.Keyword)
    private String errorMsg;

    /**
     * 重试异常信息
     */
    @JSONField(name = "RETRY_ERROR_MSG")
    @Field(type = FieldType.Keyword)
    private String retryErrorMsg;

    /**
     * 消息体 -- 可能截取
     */
    @JSONField(name = "BODY")
    @Field(type = FieldType.Text)
    private String body;

}
