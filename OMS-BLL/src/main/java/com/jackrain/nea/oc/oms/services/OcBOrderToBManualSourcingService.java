package com.jackrain.nea.oc.oms.services;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.mq.core.DefaultProducerSend;
import com.burgeon.mq.model.MqSendResult;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.model.request.SgCTobStrategyQueryRequest;
import com.burgeon.r3.sg.basic.model.request.SgFreezeStorageQueryRequest;
import com.burgeon.r3.sg.basic.model.request.SgStorageRedisQueryLsRequest;
import com.burgeon.r3.sg.basic.model.request.SgStorageRedisQueryProduceRequest;
import com.burgeon.r3.sg.basic.model.result.SgStorageRedisQueryApiResult;
import com.burgeon.r3.sg.basic.model.result.vo.SgFreezeStorageQueryResult;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.table.basic.SgCTobStrategy;
import com.burgeon.r3.sg.core.model.table.store.freeze.out.SgBStoFreezeOut;
import com.burgeon.r3.sg.core.model.table.store.freeze.out.SgBStoFreezeOutItem;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOut;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutItem;
import com.burgeon.r3.sg.sourcing.common.StrategyConstants;
import com.burgeon.r3.sg.sourcing.model.request.SgFindSourceStrategy2BRequest;
import com.burgeon.r3.sg.sourcing.model.request.SgFindSourceStrategyC2SRequest;
import com.burgeon.r3.sg.sourcing.model.request.SkuItem2B;
import com.burgeon.r3.sg.sourcing.model.request.SkuItemC2S;
import com.burgeon.r3.sg.store.model.request.freeze.out.SgBStoFreezeOutOmsSaveItemRequest;
import com.burgeon.r3.sg.store.model.request.freeze.out.SgBStoFreezeOutOmsSaveRequest;
import com.burgeon.r3.sg.store.model.result.freeze.out.SgBStoFreezeOutQueryResult;
import com.burgeon.r3.sg.store.model.result.out.SgBStoOutQueryResult;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.ac.model.result.AcBubbleFeeConfigResult;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.api.CpcShopQueryCmd;
import com.jackrain.nea.cp.dto.CpCShopProfileExt;
import com.jackrain.nea.cp.services.CpSaleOrganizationService;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.cpext.model.table.CpCShopProfile;
import com.jackrain.nea.cpext.model.table.CpCStore;
import com.jackrain.nea.cpext.model.table.CpLogistics;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ip.api.qimen.QimenInventoryQueryCmd;
import com.jackrain.nea.ip.model.qimen.QimenInventoryQueryRequest;
import com.jackrain.nea.ip.model.result.QimenInventoryQueryResult;
import com.jackrain.nea.model.util.AdParamUtil;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.carpoolOrder.OmsOrderCarpoolOrderService;
import com.jackrain.nea.oc.oms.constant.Mq5Constants;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBTobFindSourceDiffLogMapper;
import com.jackrain.nea.oc.oms.mapper.StCBusinessTypeMapper;
import com.jackrain.nea.oc.oms.model.StCCarCostInfo;
import com.jackrain.nea.oc.oms.model.StCCarCostItemInfo;
import com.jackrain.nea.oc.oms.model.StCCarCostRelation;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderSaleProductAttrEnum;
import com.jackrain.nea.oc.oms.model.request.OcBOrderToBManualSourcingBatchRequest;
import com.jackrain.nea.oc.oms.model.request.OcBOrderToBManualSourcingRequest;
import com.jackrain.nea.oc.oms.model.result.OcBOrderStoOutInfoResult;
import com.jackrain.nea.oc.oms.model.result.OcBOrderToBItemTableResult;
import com.jackrain.nea.oc.oms.model.result.OcBOrderToBMainTableResult;
import com.jackrain.nea.oc.oms.model.result.OcBOrderToBManualSourcingBatchResult;
import com.jackrain.nea.oc.oms.model.result.OcBOrderToBManualSourcingResult;
import com.jackrain.nea.oc.oms.model.result.OcBOrderToBStorageInfoResult;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItemExt;
import com.jackrain.nea.oc.oms.model.table.OcBTobFindSourceDiffLog;
import com.jackrain.nea.oc.oms.model.table.StCBusinessType;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.nums.StConstant;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.oc.oms.util.OmsModelUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.ps.services.PsGetCommodityInformationService;
import com.jackrain.nea.psext.model.table.PsCPro;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.rpc.AcRpcService;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.request.StCAllocationCostQueryRequest;
import com.jackrain.nea.st.model.request.StCExpressCostDetailQueryRequest;
import com.jackrain.nea.st.model.request.StCExpressCostQueryRequest;
import com.jackrain.nea.st.model.request.StCFullcarCostDetailQueryRequest;
import com.jackrain.nea.st.model.request.StCFullcarCostQueryRequest;
import com.jackrain.nea.st.model.request.StCUnfullcarCostDetailQueryRequest;
import com.jackrain.nea.st.model.request.StCUnfullcarCostQueryRequest;
import com.jackrain.nea.st.model.request.StCWarehouseLogisticStrategyQueryRequest;
import com.jackrain.nea.st.model.result.StCAllocationCostQueryResult;
import com.jackrain.nea.st.model.result.StCExpressCostDetailQueryResult;
import com.jackrain.nea.st.model.result.StCExpressCostQueryResult;
import com.jackrain.nea.st.model.result.StCFullcarCostDetailQueryResult;
import com.jackrain.nea.st.model.result.StCFullcarCostQueryResult;
import com.jackrain.nea.st.model.result.StCUnfullcarCostDetailQueryResult;
import com.jackrain.nea.st.model.result.StCUnfullcarCostQueryResult;
import com.jackrain.nea.st.model.result.StCWarehouseLogisticStrategyQueryResult;
import com.jackrain.nea.st.model.table.StCAllocationCostItem;
import com.jackrain.nea.st.model.table.StCAllocationStorageCostStrategy;
import com.jackrain.nea.st.model.table.StCExpressCost;
import com.jackrain.nea.st.model.table.StCExpressCostItem;
import com.jackrain.nea.st.model.table.StCFullcarCost;
import com.jackrain.nea.st.model.table.StCFullcarCostItem;
import com.jackrain.nea.st.model.table.StCUnfullcarCost;
import com.jackrain.nea.st.model.table.StCUnfullcarCostItem;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.AssertUtils;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.OmsBusinessTypeUtil;
import com.jackrain.nea.util.Tools;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR> lin yu
 * @date : 2022/8/1 下午3:26
 * @describe : TOB寻仓寻物流功能
 */

@Component
@Slf4j
public class OcBOrderToBManualSourcingService {

    @Autowired
    private OcBOrderMapper orderMapper;

    @Autowired
    private OcBOrderItemMapper orderItemMapper;

    @Autowired
    private SgRpcService sgRpcService;

    @Autowired
    private StRpcService stRpcService;

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private StCBusinessTypeMapper businessTypeMapper;

    @Autowired
    private PropertiesConf propertiesConf;

    @Autowired
    private DefaultProducerSend defaultProducerSend;

    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    @Autowired
    private ModifyOrderLogisticsService modifyOrderLogisticsService;

    @Resource
    private OcBTobFindSourceDiffLogMapper ocBTobFindSourceDiffLogMapper;

    @Autowired
    private CpSaleOrganizationService cpSaleOrganizationService;
    @Autowired
    private OcBOrderItemExtService ocBOrderItemExtService;
    @Autowired
    private BuildSequenceUtil sequenceUtil;
    @Resource
    private BusinessSystemParamService businessSystemParamService;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private GetAppointDistLevel3Service getAppointDistLevel3Service;
    @Resource
    private OmsOrderCarpoolOrderService omsOrderCarpoolOrderService;

    @Autowired
    private PsGetCommodityInformationService psGetCommodityInformationService;

    @DubboReference(version = "1.0", group = "cp")
    private CpcShopQueryCmd cpcShopQueryCmd;

    @DubboReference(version = "1.4.0", group = "ip")
    private QimenInventoryQueryCmd qimenInventoryQueryCmd;

    @Autowired
    private AcRpcService acRpcService;

    @Autowired
    private PsRpcService psRpcService;

    private static final BigDecimal CAL_LIMIT_TON_CONVERT = new BigDecimal("1000");
    private final String TOB_ORDER_SOURCING_EXCEEDS_MAX_WEIGHT_CHECK_REASON = "business_system:max_weight_check_reason";

    private static final Map<String, String> storageTypeMap = MapUtil.builder(new HashMap<String, String>())
            .put("ZP", "正品").put("CC", "残次").put("FX", "返修").put("ZJ", "质检").put("DS", "丢失").build();


    /**
     * 数据查询接口
     */
    public ValueHolderV14<OcBOrderToBManualSourcingResult> dataQuery(OcBOrderToBManualSourcingRequest request) {
        log.info(LogUtil.format("OcBOrderToBManualSourcingService.dataQuery request:{}",
                "OcBOrderToBManualSourcingService.dataQuery", JSONObject.toJSONString(request)));
        ValueHolderV14<OcBOrderToBManualSourcingResult> resultHolder = new ValueHolderV14<>(ResultCode.SUCCESS, "SUCCESS");
        try {
            //参数校验
            paramCheck(request);
            //查询
            OcBOrderToBManualSourcingResult toBManualSourcingResult = queryData(request);
            resultHolder.setData(toBManualSourcingResult);
        } catch (Exception e) {
            log.info(LogUtil.format("OcBOrderToBManualSourcingService.dataQuery.error message:{}",
                    "OcBOrderToBManualSourcingService.dataQuery.error", Throwables.getStackTraceAsString(e)), e);
            resultHolder.setCode(ResultCode.FAIL);
            resultHolder.setMessage(e.getMessage());
        }
        return resultHolder;
    }

    /**
     * 占单接口
     */
    public ValueHolderV14<OcBOrderToBManualSourcingResult> confirm(OcBOrderToBManualSourcingRequest request) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("OcBOrderToBManualSourcingService.confirm request:{}",
                    "寻仓寻物流修改入参", JSONObject.toJSONString(request)));
        }

        ValueHolderV14<OcBOrderToBManualSourcingResult> resultHolder = new ValueHolderV14<>(ResultCode.SUCCESS, "SUCCESS");

        paramCheck(request);

        //原单检验
        OcBOrder order = orderMapper.selectById(request.getId());
        if (order == null) {
            throw new NDSException("当前订单已经不存在");
        }

        List<OcBOrderItem> orderItemList = orderItemMapper.selectUnSuccessRefund(order.getId());
        if (CollectionUtils.isEmpty(orderItemList)) {
            throw new NDSException("当前订单明细已经不存在");
        }
        boolean isCc = OmsBusinessTypeUtil.isToBOrder(order) && OrderSaleProductAttrEnum.isToBCC(order.getSaleProductAttr());

        String appointDistLevel3 = getAppointDistLevel3Service.getAppointDistLevel3(order, orderItemList);
        Map<Long, CpCShopProfileExt> cpCShopProfileMap = new HashMap<>();
        Map<Long, String> errorMap = new HashMap<>();
        if (!isCc) {
            if (StringUtils.isEmpty(appointDistLevel3)) {
                cpCShopProfileMap = cpSaleOrganizationService.querySaleOrganization(orderItemList, order.getCpCShopId(), errorMap);
                if (cpCShopProfileMap.containsValue(null)) {
                    throw new NDSException(nullKeyExecute(order, errorMap));
                } else {
                    ocBOrderItemExtService.insertAndDelete(order, cpCShopProfileMap, orderItemList);
                }
            } else {
                ocBOrderItemExtService.deleteByOrderId(order.getId());
                List<OcBOrderItemExt> ocBOrderItemExtList = new ArrayList<>();
                for (OcBOrderItem ocBOrderItem : orderItemList) {
                    OcBOrderItemExt ocBOrderItemExt = new OcBOrderItemExt();
                    Long id = sequenceUtil.buildOrderItemExtSequenceId();
                    ocBOrderItemExt.setId(id);
                    ocBOrderItemExt.setOcBOrderId(order.getId());
                    ocBOrderItemExt.setTid(order.getTid());
                    ocBOrderItemExt.setOrderItemId(ocBOrderItem.getId());
                    ocBOrderItemExt.setDistCodeLevelThree(appointDistLevel3);
                    BaseModelUtil.initialBaseModelSystemField(ocBOrderItemExt);
                    ocBOrderItemExtList.add(ocBOrderItemExt);
                }
                ocBOrderItemExtService.insertList(ocBOrderItemExtList);
            }
        }

        //插入寻源差异日志表前校验
        if (request.getConfirmType() != null && 1 == request.getConfirmType()) {
            checkFindSourceDiffLog(request, order, orderItemList);
        }

        if (1 == request.getConfirmType()) {
            //确认功能
            doConfirm(isCc, request, order, orderItemList, cpCShopProfileMap, appointDistLevel3);
        } else if (2 == request.getConfirmType()) {
            //重新占单功能
            doConfirmAgain(isCc, request, order, orderItemList, cpCShopProfileMap, appointDistLevel3);
        }

        try {
            //只有正常确认时候才做处理
            if (request.getConfirmType() != null && 1 == request.getConfirmType()) {
                //插入寻源差异日志表（没有展示，提供数据分析用）
                insertFindSourceDiffLog(request, order, request.getUser());
            }
        } catch (Exception e) {
            log.warn(LogUtil.format("error:{}",
                    "OcBOrderToBManualSourcingService.confirm"), Throwables.getStackTraceAsString(e));
        }

        return resultHolder;
    }

    private String nullKeyExecute(OcBOrder ocBOrder, Map<Long, String> errorMap) {
        List<Long> noneDmi12List = new ArrayList<>();
        List<Long> noneShopProfileList = new ArrayList<>();
        for (Long key : errorMap.keySet()) {
            String value = errorMap.get(key);
            if ("商品零级查询不到".equals(value)) {
                noneDmi12List.add(key);
            } else if ("平台店铺档案品项明细找不到".equals(value)) {
                noneShopProfileList.add(key);
            }
        }
        String message = "";
        if (CollectionUtils.isNotEmpty(noneDmi12List)) {
            List<OcBOrderItem> ocBOrderItemList = ocBOrderItemMapper.selectOrderItemListsByIds(ocBOrder.getId(), noneDmi12List);
            // 获取里面的skucode
            List<String> skuCodeList = ocBOrderItemList.stream().map(OcBOrderItem::getPsCSkuEcode).collect(Collectors.toList());
            message = String.join(",", skuCodeList);
            message = "商品零级查询不到:" + message;
        }
        if (CollectionUtils.isNotEmpty(noneShopProfileList)) {
            List<OcBOrderItem> ocBOrderItemList = ocBOrderItemMapper.selectOrderItemListsByIds(ocBOrder.getId(), noneShopProfileList);
            List<String> skuCodeList = ocBOrderItemList.stream().map(OcBOrderItem::getPsCSkuEcode).collect(Collectors.toList());
            if (StringUtils.isNotEmpty(message)) {
                message = message + ";平台店铺档案品项明细找不到:" + String.join(",", skuCodeList);
            } else {
                message = "平台店铺档案品项明细找不到:" + String.join(",", skuCodeList);
            }
        }
        return message;
    }

    /**
     * 插入寻源不一致的日志前校验
     *
     * @param request
     * @param orderItemList
     */
    private void checkFindSourceDiffLog(OcBOrderToBManualSourcingRequest request, OcBOrder order, List<OcBOrderItem> orderItemList) {
        if (request.getRecommendData() == null) {
            throw new NDSException("推荐的零担报价不能为空，请联系管理员！");
        }
        if (request.getChooseData() == null) {
            throw new NDSException("选择的数据不能为空，请联系管理员！");
        }
        if (request.getChooseData().getCpCPhyWarehouseId() == null) {
            throw new NDSException("请选择仓库！");
        }
        //寻仓原因为空，查询仓库是否仓辐射范围，否就报错
        if (StringUtils.isEmpty(request.getFindSourceReason())) {
            ValueHolderV14<List<SgCTobStrategy>> tobStrategyResult = queryTobStrategy(order);
            if (!tobStrategyResult.isOK() || CollectionUtils.isEmpty(tobStrategyResult.getData())) {
                throw new NDSException("仓库不能辐射，寻仓原因不能为空！");
            }
            Set<Long> warehouseIdSet = tobStrategyResult.getData().stream()
                    .map(SgCTobStrategy::getCpCPhyWarehouseId).collect(Collectors.toSet());
            if (!warehouseIdSet.contains(request.getChooseData().getCpCPhyWarehouseId())) {
                throw new NDSException("仓库不能辐射，寻仓原因不能为空！");
            }
            BigDecimal weight = orderItemList.stream().map(x -> x.getQty().multiply(x.getStandardWeight() == null ?
                    BigDecimal.ZERO : x.getStandardWeight())).reduce(BigDecimal.ZERO, BigDecimal::add);
            weight = weight.divide(CAL_LIMIT_TON_CONVERT, 2, BigDecimal.ROUND_HALF_UP);
            BigDecimal maxWeight = getBusinessSystemForMaxWeight();
            if (weight.compareTo(maxWeight) > 0) {
                throw new NDSException("重量大于" + maxWeight.stripTrailingZeros().toPlainString() + "吨，寻仓原因不能为空！");
            }
        }
    }

    public BigDecimal getBusinessSystemForMaxWeight() {
        BigDecimal maxWeight = BigDecimal.ZERO;
        try {
            String value = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(TOB_ORDER_SOURCING_EXCEEDS_MAX_WEIGHT_CHECK_REASON);
            if (StringUtils.isBlank(value)) {
                log.warn("getBusinessSystemForMaxWeight");
                return maxWeight;
            } else {
                maxWeight = new BigDecimal(value);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("OcBOrderToBManualSourcingService.getBusinessSystemForMaxWeight error:{}",
                    "OcBOrderToBManualSourcingService#getBusinessSystemForMaxWeight"), Throwables.getStackTraceAsString(e));
        }
        return maxWeight;
    }

    /**
     * 插入寻源不一致的日志
     *
     * @param request
     * @param order
     * @param user
     */
    private void insertFindSourceDiffLog(OcBOrderToBManualSourcingRequest request, OcBOrder order, User user) {
        // 寻仓原因不为空就覆盖内部备注
        if (StringUtils.isNotEmpty(request.getFindSourceReason())) {
            OcBOrder update = new OcBOrder();
            update.setId(order.getId());
            update.setInsideRemark(request.getFindSourceReason());
            if (update.getInsideRemark().length() > 500) {
                update.setInsideRemark(update.getInsideRemark().substring(0, 499));
            }
            orderMapper.updateById(update);
        }
        //删除历史差异日志
        ocBTobFindSourceDiffLogMapper.delete(new LambdaQueryWrapper<OcBTobFindSourceDiffLog>()
                .eq(OcBTobFindSourceDiffLog::getSourceBillId, order.getId()));
        //保存差异日志
        OcBTobFindSourceDiffLog recommendData = new OcBTobFindSourceDiffLog();
        BeanUtils.copyProperties(request.getRecommendData(), recommendData);
        recommendData.setId(ModelUtil.getSequence("oc_b_tob_find_source_diff_log"));
        recommendData.setChooseType(0);
        recommendData.setSourceBillId(order.getId());
        recommendData.setSourceBillNo(order.getBillNo());
        recommendData.setFindSourceReason(request.getFindSourceReason());
        OmsModelUtil.setDefault4Add(recommendData, user);
        OcBTobFindSourceDiffLog chooseData = new OcBTobFindSourceDiffLog();
        BeanUtils.copyProperties(request.getChooseData(), chooseData);
        chooseData.setId(ModelUtil.getSequence("oc_b_tob_find_source_diff_log"));
        chooseData.setChooseType(1);
        chooseData.setSourceBillId(order.getId());
        chooseData.setSourceBillNo(order.getBillNo());
        chooseData.setFindSourceReason(request.getFindSourceReason());
        OmsModelUtil.setDefault4Add(chooseData, user);
        ocBTobFindSourceDiffLogMapper.batchInsert(Lists.newArrayList(recommendData, chooseData));
    }

    /**
     * 批量占单接口
     */
    public ValueHolderV14<List<OcBOrderToBManualSourcingBatchResult>> batchConfirm(OcBOrderToBManualSourcingBatchRequest request) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("OcBOrderToBManualSourcingService.batchConfirm request:{}",
                    "批量寻仓寻物流入参", JSONObject.toJSONString(request)));
        }

        paramCheck(request);

        ValueHolderV14<List<OcBOrderToBManualSourcingBatchResult>> resultHolder
                = doBatchConfirm(request);

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("OcBOrderToBManualSourcingService.batchConfirm result:{}",
                    "批量寻仓寻物流出参", JSONObject.toJSONString(resultHolder)));
        }

        return resultHolder;
    }

    private ValueHolderV14<List<OcBOrderToBManualSourcingBatchResult>> doBatchConfirm(OcBOrderToBManualSourcingBatchRequest request) {

        ValueHolderV14<List<OcBOrderToBManualSourcingBatchResult>> listValueHolderV14 = new ValueHolderV14<>(ResultCode.SUCCESS, "SUCCESS");
        List<Long> idList = request.getIdList();

        //封装成页面单条的model
        OcBOrderToBManualSourcingRequest toBManualSourcingRequest =
                buildOcBOrderToBManualSourcingRequest(request);

        //仓库物流校验
        warehouseLogisticCheck(toBManualSourcingRequest, null, null);

        List<OcBOrderToBManualSourcingBatchResult> requestList = new ArrayList<>();

        for (Long id : idList) {

            try {

                toBManualSourcingRequest.setId(id);
                singleConfirm(toBManualSourcingRequest);

            } catch (Exception e) {

                OcBOrderToBManualSourcingBatchResult result = new OcBOrderToBManualSourcingBatchResult();

                result.setId(id);
                result.setCode(ResultCode.FAIL);
                result.setMessage(e.getMessage());
                requestList.add(result);
            }

        }

        listValueHolderV14.setData(requestList);
        return listValueHolderV14;
    }

    private OcBOrderToBManualSourcingRequest buildOcBOrderToBManualSourcingRequest(OcBOrderToBManualSourcingBatchRequest request) {

        CpCPhyWarehouse warehouse = cpRpcService.queryByWarehouseId(request.getCpCPhyWarehouseId());
        if (warehouse == null) {
            throw new NDSException("未查到当前实体仓信息");
        }

        CpLogistics logistics = cpRpcService.queryLogisticsById(request.getCpCLogisticsId());
        if (logistics == null) {
            throw new NDSException("未查到当前物流公司信息");
        }

        OcBOrderToBManualSourcingRequest toBManualSourcingRequest = new OcBOrderToBManualSourcingRequest();

        toBManualSourcingRequest.setCpCPhyWarehouseId(warehouse.getId());
        toBManualSourcingRequest.setCpCPhyWarehouseCode(warehouse.getEcode());
        toBManualSourcingRequest.setCpCLogisticsId(logistics.getId());
        toBManualSourcingRequest.setCpCLogisticsEcode(logistics.getEcode());
        toBManualSourcingRequest.setCpCLogisticsEname(logistics.getEname());
        toBManualSourcingRequest.setUser(request.getUser());

        return toBManualSourcingRequest;
    }


    private void singleConfirm(OcBOrderToBManualSourcingRequest request) {

        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(request.getId());
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);

        try {

            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {

                //确认功能
                doSingleConfirm(request);

            } else {

                throw new NDSException("当前订单正在被其他人操作,请稍后再试!");
            }

        } catch (Exception e) {

            log.error(LogUtil.format("OcBOrderToBManualSourcingService.error message:{}",
                    "寻仓寻物流确认接口异常", Throwables.getStackTraceAsString(e)), e);

            throw new NDSException(e.getMessage());

        } finally {
            redisLock.unlock();
        }
    }

    private void doSingleConfirm(OcBOrderToBManualSourcingRequest request) {

        //原单检验
        OcBOrder order = orderMapper.selectById(request.getId());
        if (order == null) {
            throw new NDSException("当前订单已经不存在");
        }
        boolean isCc = OmsBusinessTypeUtil.isToBOrder(order) && OrderSaleProductAttrEnum.isToBCC(order.getSaleProductAttr());


        List<OcBOrderItem> orderItemList = orderItemMapper.selectUnSuccessRefund(order.getId());
        if (CollectionUtils.isEmpty(orderItemList)) {
            throw new NDSException("当前订单明细已经不存在");
        }

        //订单状态校验
        statusCheckNew(order);
        boolean willOccupy = OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(order.getOrderStatus()) || (((OmsOrderStatus.UNCONFIRMED.toInteger().equals(order.getOrderStatus()) ||
                OmsOrderStatus.CHECKED.toInteger().equals(order.getOrderStatus())) && (!order.getCpCPhyWarehouseId().equals(request.getCpCPhyWarehouseId()))));

        // 判断订单是否会寻源。
        // 特殊的业务类型 是没有销售部门的
        String appointDistLevel3 = getAppointDistLevel3Service.getAppointDistLevel3(order, orderItemList);
        Map<Long, CpCShopProfileExt> cpCShopProfileMap = new HashMap<>();
        Map<Long, String> errorMap = new HashMap<>();
        if (!isCc && willOccupy) {
            if (StringUtils.isEmpty(appointDistLevel3)) {
                cpCShopProfileMap = cpSaleOrganizationService.querySaleOrganization(orderItemList, order.getCpCShopId(), errorMap);
                if (cpCShopProfileMap.containsValue(null)) {
                    // 获取errorMap里面的所有value，并且根据,隔开
                    throw new NDSException(StringUtils.join(errorMap.values(), ","));
                } else {
                    // 先根据订单id 清空老的数据,然后再新增新查询到的数据
                    ocBOrderItemExtService.insertAndDelete(order, cpCShopProfileMap, orderItemList);
                }
            } else {
                // 后续塞新的字段
                ocBOrderItemExtService.deleteByOrderId(order.getId());
                List<OcBOrderItemExt> ocBOrderItemExtList = new ArrayList<>();
                for (OcBOrderItem ocBOrderItem : orderItemList) {
                    OcBOrderItemExt ocBOrderItemExt = new OcBOrderItemExt();
                    Long id = sequenceUtil.buildOrderItemExtSequenceId();
                    ocBOrderItemExt.setId(id);
                    ocBOrderItemExt.setOcBOrderId(order.getId());
                    ocBOrderItemExt.setTid(order.getTid());
                    ocBOrderItemExt.setOrderItemId(ocBOrderItem.getId());
                    ocBOrderItemExt.setDistCodeLevelThree(appointDistLevel3);
                    BaseModelUtil.initialBaseModelSystemField(ocBOrderItemExt);
                    ocBOrderItemExtList.add(ocBOrderItemExt);
                }
                ocBOrderItemExtService.insertList(ocBOrderItemExtList);
            }
        }

        //物流费用配置校验
        modifyOrderLogisticsService.expressCheck(request.getCpCLogisticsId(), order.getId(), request.getCpCPhyWarehouseId(), order.getCpCRegionProvinceId());

        if (OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(order.getOrderStatus())) {

            //TOB寻源
            occupyOrder(isCc, order, orderItemList, request, cpCShopProfileMap, appointDistLevel3);

        } else if (OmsOrderStatus.UNCONFIRMED.toInteger().equals(order.getOrderStatus()) ||
                OmsOrderStatus.CHECKED.toInteger().equals(order.getOrderStatus())) {

            if (!order.getCpCPhyWarehouseId().equals(request.getCpCPhyWarehouseId())) {

                //库存占用释放
                voidOutOrder(isCc, order, request.getUser());
                if (StringUtils.isNotEmpty(order.getCarpoolNo())) {
                    omsOrderCarpoolOrderService.cancelCarpoolNo(Lists.newArrayList(order.getCarpoolNo()), request.getUser(), false);
                }
                //TOB寻源
                occupyOrder(isCc, order, orderItemList, request, cpCShopProfileMap, appointDistLevel3);

            } else if (order.getCpCPhyWarehouseId().equals(request.getCpCPhyWarehouseId())
                    && !Objects.equals(order.getCpCLogisticsId(), request.getCpCLogisticsId())) {
                updateLogisticsInfo(order, request);
                if (StringUtils.isNotEmpty(order.getCarpoolNo())) {
                    omsOrderCarpoolOrderService.cancelCarpoolNo(Lists.newArrayList(order.getCarpoolNo()), request.getUser(), false);
                }
            }

        }
    }

    private void statusCheckNew(OcBOrder order) {
        Integer orderStatus = order.getOrderStatus();

        if (!(OmsOrderStatus.UNCONFIRMED.toInteger().equals(orderStatus) ||
                OmsOrderStatus.CHECKED.toInteger().equals(orderStatus) ||
                OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(orderStatus))) {
            AssertUtils.logAndThrow("当前状态，不允许寻仓寻物流!");
        }

        Long businessTypeId = order.getBusinessTypeId();

        if (businessTypeId == null) {
            AssertUtils.logAndThrow("当前订单的业务类型为空");
        }

        StCBusinessType businessType = businessTypeMapper.selectById(businessTypeId);

        if (businessType == null) {
            AssertUtils.logAndThrow("未查到当前订单业务类型信息！");
        }

        if (!("RYCK16".equals(businessType.getEcode())
                || "RYCK17".equals(businessType.getEcode())
                || "RYCK18".equals(businessType.getEcode()))) {
            AssertUtils.logAndThrow("当前订单业务类型不允许执行寻仓寻物流！");
        }

    }

    private void updateLogisticsInfo(OcBOrder order, OcBOrderToBManualSourcingRequest request) {

        OcBOrder updateModel = new OcBOrder();
        updateModel.setId(order.getId());
        updateModel.setCpCLogisticsId(request.getCpCLogisticsId());
        updateModel.setCpCLogisticsEcode(request.getCpCLogisticsEcode());
        updateModel.setCpCLogisticsEname(request.getCpCLogisticsEname());
        OmsModelUtil.setDefault4Upd(updateModel, request.getUser());
        orderMapper.updateById(updateModel);

    }


    private void paramCheck(OcBOrderToBManualSourcingBatchRequest request) {

        if (request == null) {
            throw new NDSException("请求参数不能为空!");
        }

        if (request.getUser() == null) {
            throw new NDSException("请求用户不能为空!");
        }

        if (CollectionUtils.isEmpty(request.getIdList())) {
            throw new NDSException("请勾选至少一笔订单!");
        }

        if (request.getCpCLogisticsId() == null || request.getCpCPhyWarehouseId() == null) {
            throw new NDSException("物流或仓库不能为空!");
        }
    }

    private void doConfirmAgain(boolean isCc, OcBOrderToBManualSourcingRequest request,
                                OcBOrder order,
                                List<OcBOrderItem> orderItemList, Map<Long, CpCShopProfileExt> cpCShopProfileMap, String appointDistLevel3) {

        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(order.getId());
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);

        try {

            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {

                //订单状态校验
                statusCheckForDoConfirmAgain(order);

                //数量校验
                qtyCheck(orderItemList, request);

                // 校验效期范围
                // 需要校验指定的效期是否在订单效期范围内
                checkProductRange(orderItemList, request);
                //库存占用释放
                voidOutOrder(isCc, order, request.getUser());

                //TOB重新寻源
                occupyOrderAgain(isCc, order, orderItemList, request, cpCShopProfileMap, appointDistLevel3);
            } else {

                throw new NDSException("当前订单正在被其他人操作,请稍后再试!");
            }

        } catch (Exception e) {

            log.info(LogUtil.format("OcBOrderToBManualSourcingService.error message:{}",
                    "寻仓寻物流重新占单异常", Throwables.getStackTraceAsString(e)), e);

            throw new NDSException("寻仓寻物流重新占单异常:" + e.getMessage());

        } finally {
            redisLock.unlock();
        }

    }

    private void statusCheckForDoConfirmAgain(OcBOrder order) {

        Integer orderStatus = order.getOrderStatus();

        if (!(OmsOrderStatus.UNCONFIRMED.toInteger().equals(orderStatus) ||
                OmsOrderStatus.CHECKED.toInteger().equals(orderStatus))) {
            AssertUtils.logAndThrow("当前状态，不允许重新占单!");
        }

        Long businessTypeId = order.getBusinessTypeId();

        if (businessTypeId == null) {
            AssertUtils.logAndThrow("当前订单的业务类型为空");
        }

        StCBusinessType businessType = businessTypeMapper.selectById(businessTypeId);
        if (businessType != null && businessType.getIsSourceOccupy() != null && businessType.getIsSourceOccupy() == 1) {
            AssertUtils.logAndThrow("当前订单业务类型不允许执行寻仓寻物流！");
        }
    }

    private void checkProductRange(List<OcBOrderItem> orderItemList,
                                   OcBOrderToBManualSourcingRequest request) {
        List<OcBOrderStoOutInfoResult> stoOutInfoResultList = request.getStoOutInfoResultList();
        Map<Long, List<OcBOrderStoOutInfoResult>> itemIdAndModelListMap = stoOutInfoResultList.stream().collect(Collectors.groupingBy(OcBOrderStoOutInfoResult::getItemId));
        Map<Long, OcBOrderItem> itemIdAndItemMap = orderItemList.stream().collect(Collectors.toMap(OcBOrderItem::getId, item -> item));
        // 遍历 itemIdAndModelListMap
        for (Long itemId : itemIdAndModelListMap.keySet()) {
            List<OcBOrderStoOutInfoResult> modelList = itemIdAndModelListMap.get(itemId);
            OcBOrderItem item = itemIdAndItemMap.get(itemId);
            // 获取itemIdAndModelListMap 指定的 item
            if (CollectionUtils.isNotEmpty(modelList) && item != null) {
                String productRange = item.getExpiryDateRange();
                if (StringUtils.isEmpty(productRange)) {
                    continue;
                }
                Integer expiryDateType = item.getExpiryDateType();
                if (expiryDateType != null && expiryDateType == 2) {
                    String[] split = productRange.split("-");
                    if (split.length == 2) {
                        try {
                            int startDateDay = Integer.parseInt(split[0]);
                            int endDateDay = Integer.parseInt(split[1]);

                            LocalDate today = LocalDate.now();
                            LocalDate startDate = today.minusDays(endDateDay);
                            LocalDate endDate = today.minusDays(startDateDay);

                            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
                            productRange = startDate.format(formatter) + "-" + endDate.format(formatter);
                        } catch (NumberFormatException e) {
                            // 处理日期格式转换异常 直接抛异常
                            throw new NDSException("日期格式转换异常");
                        }
                    } else {
                        // 处理分割后数组长度不符合预期的情况。 直接抛异常
                        throw new NDSException("Invalid productRange format: " + productRange);
                    }
                }
                for (OcBOrderStoOutInfoResult model : modelList) {
                    // 获取出来效期
                    String expiryDate = model.getProduceDate();
                    // productRange的格式是 yyyyMMdd-yyyyMMdd
                    if (StringUtils.isNotEmpty(productRange)) {
                        String[] split = productRange.split("-");
                        String startDate = split[0];
                        String endDate = split[1];
                        // 判断expiryDate 是否在startDate与endDate 范围内
                        if (expiryDate.compareTo(startDate) < 0 || expiryDate.compareTo(endDate) > 0) {
                            throw new NDSException("商品" + model.getPsCProEcode() + "效期" + expiryDate + "不在指定效期" + productRange + "内");
                        }
                    }
                }
            }
        }
    }


    private void qtyCheck(List<OcBOrderItem> orderItemList,
                          OcBOrderToBManualSourcingRequest request) {

        List<OcBOrderStoOutInfoResult> stoOutInfoResultList = request.getStoOutInfoResultList();
        Map<String, List<OcBOrderStoOutInfoResult>> proEcodeAndModelListMap = stoOutInfoResultList.stream().collect(Collectors.groupingBy(OcBOrderStoOutInfoResult::getPsCProEcode));

        Map<String, List<OcBOrderItem>> proEcodeAndItemListMap = orderItemList.stream().collect(Collectors.groupingBy(OcBOrderItem::getPsCProEcode));

        for (String proCode : proEcodeAndItemListMap.keySet()) {
            //订单明细 商品维度 数量汇总
            BigDecimal totalQty = proEcodeAndItemListMap.get(proCode).stream()
                    .map(OcBOrderItem::getQty)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            //订单明细 商品维度 数量汇总
            BigDecimal totalQtyPreout = proEcodeAndModelListMap.get(proCode).stream()
                    .map(OcBOrderStoOutInfoResult::getQtyPreout)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            if (totalQtyPreout.compareTo(totalQty) != 0) {
                throw new NDSException("商品编码:" + proCode + "的占用数量:" + totalQtyPreout + "不等于订单明细数量:" + totalQty);
            }

        }

    }

    private void occupyOrderAgain(boolean isCc, OcBOrder order,
                                  List<OcBOrderItem> orderItemList,
                                  OcBOrderToBManualSourcingRequest request, Map<Long, CpCShopProfileExt> cpCShopProfileMap, String appointDistLevel3) {

        log.info(LogUtil.format("OcBOrderToBManualSourcingService.occupyOrder param: isCc:{} order:{} orderItemList:{} request:{}",
                "TOB寻仓寻物流寻源占单"), isCc, JSONObject.toJSONString(order), JSONObject.toJSONString(orderItemList), JSONObject.toJSONString(request));
        if (isCc) {
            occupyFreezeOrder(order, orderItemList, request);
            return;
        }

        //构建重新占单明细
        SgFindSourceStrategy2BRequest findSourceStrategy2BRequest = buildOccupyOrderAgain(order, orderItemList, request, cpCShopProfileMap);
        if (StringUtils.isNotEmpty(appointDistLevel3)) {
            findSourceStrategy2BRequest.setAppointDistLevel3(appointDistLevel3);
        }

        omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(), OrderLogTypeEnum.TOB_OCCUPY.getKey(), "OrderId=" + order.getId() + "TOB开始重新占单", "", "", request.getUser());

        //重新占单
        ValueHolderV14 result = sgRpcService.findSource2B(findSourceStrategy2BRequest);

        if (!result.isOK()) {
            //更新状态为TOB寻源占单中 更新为 "待寻源"
            OcBOrder updateModel = new OcBOrder();
            updateModel.setId(order.getId());
            updateModel.setOrderStatus(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger());
            OmsModelUtil.setDefault4Upd(updateModel, request.getUser());
            orderMapper.updateById(updateModel);

            omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(), OrderLogTypeEnum.TOB_OCCUPY.getKey(), "OrderId=" + order.getId() + "TOB重新占单失败:" + result.getMessage(), "", "", request.getUser());

            throw new NDSException(result.getMessage());
        } else {
            List<SkuItem2B> skuItem2BList = (List<SkuItem2B>) result.getData();
            Map<Long, List<SkuItem2B>> map = new HashMap<>();
            map = skuItem2BList.stream().collect(Collectors.groupingBy(SkuItem2B::getSourceItemId));
            for (Long key : map.keySet()) {
                List<SkuItem2B> skuItem2BS = map.get(key);
                ocBOrderItemExtService.updateByItemId(skuItem2BS.get(0));
            }
            omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(), OrderLogTypeEnum.TOB_OCCUPY.getKey(), "OrderId=" + order.getId() + "TOB重新占单成功", "", "", request.getUser());
        }
    }

    private SgFindSourceStrategy2BRequest buildOccupyOrderAgain(OcBOrder order,
                                                                List<OcBOrderItem> orderItemList,
                                                                OcBOrderToBManualSourcingRequest request, Map<Long, CpCShopProfileExt> cpCShopProfileMap) {

        SgFindSourceStrategy2BRequest findSourceStrategy2BRequest = new SgFindSourceStrategy2BRequest();

        findSourceStrategy2BRequest.setSourceBillId(order.getId());
        findSourceStrategy2BRequest.setSourceBillNo(order.getBillNo());
        findSourceStrategy2BRequest.setTid(order.getTid());
        findSourceStrategy2BRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_RETAIL);
        findSourceStrategy2BRequest.setBillDate(order.getOrderDate());
        findSourceStrategy2BRequest.setShopId(order.getCpCShopId());
        findSourceStrategy2BRequest.setProvinceId(order.getCpCRegionProvinceId());
        findSourceStrategy2BRequest.setCityId(order.getCpCRegionCityId());
        findSourceStrategy2BRequest.setAreaId(order.getCpCRegionAreaId());
        findSourceStrategy2BRequest.setWarehouseId(order.getCpCPhyWarehouseId());
        findSourceStrategy2BRequest.setUser(request.getUser());

        List<OcBOrderStoOutInfoResult> stoOutInfoResultList = request.getStoOutInfoResultList();

        //【商品ID,List<OcBOrderStoOutInfoResult>】
        Map<Long, List<OcBOrderStoOutInfoResult>> itemIdAndModelListMap = stoOutInfoResultList.stream().collect(Collectors.groupingBy(OcBOrderStoOutInfoResult::getPsCProId));

        //【商品ID,List<OcBOrderItem>】
        Map<Long, List<OcBOrderItem>> proIdAndItemListMap = orderItemList.stream().collect(Collectors.groupingBy(OcBOrderItem::getPsCProId));

        List<SkuItem2B> skuItem2BList = new ArrayList<>();

        for (Long proId : proIdAndItemListMap.keySet()) {

            List<OcBOrderItem> ocBOrderItems = proIdAndItemListMap.get(proId);

            if (ocBOrderItems.size() == 1) {

                OcBOrderItem ocBOrderItem = ocBOrderItems.get(0);
                Long id = ocBOrderItem.getId();

                List<OcBOrderStoOutInfoResult> orderStoOutInfoResultList = itemIdAndModelListMap.get(ocBOrderItem.getPsCProId());
                SkuItem2B skuItem2B = new SkuItem2B();
                skuItem2B.setSourceItemId(id);
                skuItem2B.setPsCSkuId(ocBOrderItem.getPsCSkuId());
                skuItem2B.setSkuId(ocBOrderItem.getSkuNumiid());
                skuItem2B.setNumiid(ocBOrderItem.getNumIid());
                skuItem2B.setPsCProId(ocBOrderItem.getPsCProId());
                skuItem2B.setPsCProdimId(ocBOrderItem.getMDim6Id());
                skuItem2B.setQty(ocBOrderItem.getQty());
                skuItem2B.setLabelingRequirements(ocBOrderItem.getReserveVarchar02());

                Map<String, BigDecimal> qtyMap = new HashMap<>();

                for (OcBOrderStoOutInfoResult orderStoOutInfoResult : orderStoOutInfoResultList) {

                    String produceDate = orderStoOutInfoResult.getProduceDate();
                    BigDecimal qtyPreout = orderStoOutInfoResult.getQtyPreout() == null ? BigDecimal.ZERO : orderStoOutInfoResult.getQtyPreout();

                    if (qtyMap.containsKey(produceDate)) {
                        BigDecimal orgQtyPreout = qtyMap.get(produceDate) == null ? BigDecimal.ZERO : qtyMap.get(produceDate);
                        qtyMap.put(produceDate, qtyPreout.add(orgQtyPreout));
                    } else {
                        qtyMap.put(produceDate, qtyPreout);
                    }
                }

                skuItem2B.setQtyMap(qtyMap);
                skuItem2BList.add(skuItem2B);
            } else {

                //商品维度 【效期,占用数量】
                Map<String, BigDecimal> produceDateAndQty = new HashMap<>();
                List<OcBOrderStoOutInfoResult> orderStoOutInfoResultList = itemIdAndModelListMap.get(proId);
                for (OcBOrderStoOutInfoResult ocBOrderStoOutInfoResult : orderStoOutInfoResultList) {
                    String produceDate = ocBOrderStoOutInfoResult.getProduceDate();
                    BigDecimal qtyPreout = ocBOrderStoOutInfoResult.getQtyPreout() == null ? BigDecimal.ZERO : ocBOrderStoOutInfoResult.getQtyPreout();

                    if (produceDateAndQty.containsKey(produceDate)) {
                        BigDecimal orgQtyPreout = produceDateAndQty.get(produceDate);
                        produceDateAndQty.put(produceDate, qtyPreout.add(orgQtyPreout));
                    } else {
                        produceDateAndQty.put(produceDate, qtyPreout);
                    }
                }

                for (OcBOrderItem ocBOrderItem : ocBOrderItems) {

                    Long id = ocBOrderItem.getId();

                    //数量【也是需要分的剩余数量】
                    BigDecimal itemQty = ocBOrderItem.getQty();

                    SkuItem2B skuItem2B = new SkuItem2B();
                    skuItem2B.setSourceItemId(id);
                    skuItem2B.setPsCSkuId(ocBOrderItem.getPsCSkuId());
                    skuItem2B.setSkuId(ocBOrderItem.getSkuNumiid());
                    skuItem2B.setNumiid(ocBOrderItem.getNumIid());
                    skuItem2B.setPsCProId(ocBOrderItem.getPsCProId());
                    skuItem2B.setPsCProdimId(ocBOrderItem.getMDim6Id());
                    skuItem2B.setQty(ocBOrderItem.getQty());
                    skuItem2B.setLabelingRequirements(ocBOrderItem.getReserveVarchar02());

                    Map<String, BigDecimal> qtyMap = new HashMap<>();

                    for (String produceDate : produceDateAndQty.keySet()) {

                        BigDecimal qtyPreout = produceDateAndQty.get(produceDate);

                        if (qtyPreout.compareTo(BigDecimal.ZERO) > 0) {

                            if (qtyPreout.compareTo(itemQty) >= 0) {
                                qtyMap.put(produceDate, itemQty);
                                produceDateAndQty.put(produceDate, produceDateAndQty.get(produceDate).subtract(itemQty));

                                break;
                            } else {
                                qtyMap.put(produceDate, qtyPreout);
                                produceDateAndQty.put(produceDate, BigDecimal.ZERO);
                                itemQty = itemQty.subtract(qtyPreout);

                                if (itemQty.compareTo(BigDecimal.ZERO) == 0) {
                                    break;
                                }
                            }

                        }

                    }

                    skuItem2B.setQtyMap(qtyMap);
                    skuItem2BList.add(skuItem2B);
                }
            }
        }

        //【条码ID,效期范围】
        Map<Long, Set<String>> skuIdAndExpiryDateRangeMap = new HashMap<>(16);

        //【明细ID,效期范围】
        Map<Long, String> itemIdAndExpiryDateRangeMap = new HashMap<>(16);

        //解析构造效期范围 和数量
        analysisExpiryDateRange(skuIdAndExpiryDateRangeMap, itemIdAndExpiryDateRangeMap, orderItemList);

        for (SkuItem2B skuItem2B : skuItem2BList) {
            String expiryDateRange = itemIdAndExpiryDateRangeMap.get(skuItem2B.getSourceItemId());
            if (cpCShopProfileMap != null && cpCShopProfileMap.get(skuItem2B.getSourceItemId()) != null) {
                CpCShopProfileExt cpCShopProfileExt = cpCShopProfileMap.get(skuItem2B.getSourceItemId());
                CpCShopProfile cpCShopProfile = cpCShopProfileExt.getCpCShopProfile();
                skuItem2B.setSalesDepartmentCode(cpCShopProfile.getSalesDepartmentCode());
                skuItem2B.setSalesGroupCode(cpCShopProfile.getSalesGroupCode());
                skuItem2B.setCategoryCode(cpCShopProfileExt.getCategoryCode());
            }
            if (!"UNLIMITED".equals(expiryDateRange)) {
                String[] split = expiryDateRange.split("-");
                skuItem2B.setBeginProduceDate(split[0]);
                skuItem2B.setEndProduceDate(split[1]);
            }
        }

        findSourceStrategy2BRequest.setSkuItems(skuItem2BList);
        return findSourceStrategy2BRequest;
    }

    private void doConfirm(boolean isCc, OcBOrderToBManualSourcingRequest request,
                           OcBOrder order,
                           List<OcBOrderItem> orderItemList, Map<Long, CpCShopProfileExt> cpCShopProfileMap, String appointDistLevel3) {


        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(order.getId());
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);

        try {

            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {

                //订单状态校验
                statusCheck(order);

                //仓库物流校验
                warehouseLogisticCheck(request, order.getCpCRegionProvinceId(), order.getWeight());

                //库存占用释放
                voidOutOrder(isCc, order, request.getUser());

                //拼车单号不为空调用清空逻辑
                if (StringUtils.isNotEmpty(order.getCarpoolNo())) {
                    omsOrderCarpoolOrderService.cancelCarpoolNo(Lists.newArrayList(order.getCarpoolNo()), request.getUser(), false);
                }

                //TOB寻源
                occupyOrder(isCc, order, orderItemList, request, cpCShopProfileMap, appointDistLevel3);

            } else {

                throw new NDSException("当前订单正在被其他人操作,请稍后再试!");
            }

        } catch (Exception e) {

            log.info(LogUtil.format("OcBOrderToBManualSourcingService.error message:{}",
                    "寻仓寻物流确认接口异常", Throwables.getStackTraceAsString(e)), e);

            throw new NDSException("寻仓寻物流确认接口异常:" + e.getMessage());

        } finally {
            redisLock.unlock();
        }
    }


    /**
     * 通过查询类型查询不同的数据
     */
    private OcBOrderToBManualSourcingResult queryData(OcBOrderToBManualSourcingRequest request) {
        //原单检验
        OcBOrder order = orderMapper.selectById(request.getId());
        if (order == null) {
            throw new NDSException("当前订单已经不存在");
        }
        // 是否残次销售
        boolean isCc = OmsBusinessTypeUtil.isToBOrder(order) && OrderSaleProductAttrEnum.isToBCC(order.getSaleProductAttr());
        List<OcBOrderItem> orderItemList = orderItemMapper.selectUnSuccessRefund(order.getId());
        if (CollectionUtils.isEmpty(orderItemList)) {
            throw new NDSException("当前订单明细已经不存在");
        }
        OcBOrderToBManualSourcingResult toBManualSourcingResult = new OcBOrderToBManualSourcingResult();
        if (1 == request.getQueryType()) {
            //订单状态校验
            statusCheck(order);

            //寻仓寻物流主表信息封装
            mainTableDataEncapsulation(order, toBManualSourcingResult);

            //寻仓寻物流明右侧细信息查寻与封装
            queryItemDataAndEncapsulation(order, orderItemList, toBManualSourcingResult);

            //占单结果明细查询
            queryPreoutItemDataAndEncapsulation(isCc, order, orderItemList, toBManualSourcingResult);

        } else if (2 == request.getQueryType()) {

            Long storeId = request.getStoreId();
            String storeCode = request.getStoreCode();
            if (StringUtils.isNotEmpty(storeCode)) {
                CpCStore cpStore = cpRpcService.queryStoreByEcodes(storeCode);
                storeId = cpStore.getId();
            }
            //寻仓寻物流左侧库存信息查询与封装
            queryStorageInfoAndEncapsulation(isCc, storeId, orderItemList,
                    toBManualSourcingResult, request.getUser(), order);

        } else if (3 == request.getQueryType()) {

            //占单结果明细查询
            queryPreoutItemDataAndEncapsulation(isCc, order, orderItemList, toBManualSourcingResult);

        }

        return toBManualSourcingResult;
    }

    private void queryPreoutItemDataAndEncapsulation(boolean isCc, OcBOrder order,
                                                     List<OcBOrderItem> orderItemList,
                                                     OcBOrderToBManualSourcingResult toBManualSourcingResult) {

        if (isCc) {
            List<SgBStoFreezeOutQueryResult> stoFreezeOutQueryResultList = doQueryFreezePreoutItem(order);
            doEncapsulationStoFreezeOutData(stoFreezeOutQueryResultList, orderItemList, toBManualSourcingResult);
        } else {
            //查询占单结果明细
            List<SgBStoOutQueryResult> stoOutQueryResultList = doQueryPreoutItem(order);

            //封装占单结果明细返回结果
            doEncapsulationStoOutData(stoOutQueryResultList, orderItemList, toBManualSourcingResult);
        }
    }

    private void doEncapsulationStoFreezeOutData(List<SgBStoFreezeOutQueryResult> stoFreezeOutQueryResultList,
                                                 List<OcBOrderItem> orderItemList,
                                                 OcBOrderToBManualSourcingResult toBManualSourcingResult) {

        int limit = 0;
        try {

            limit = Tools.getInt(AdParamUtil.getParam("oms.oc.order.batches.number.Limit"), 0);

        } catch (Exception e) {

            AssertUtils.logAndThrow("获取系统参数:批次个数限制异常");
        }

        if (CollectionUtils.isEmpty(stoFreezeOutQueryResultList)) {
            return;
        }

        SgBStoFreezeOutQueryResult stoOutQueryResult = stoFreezeOutQueryResultList.get(0);

        SgBStoFreezeOut main = stoOutQueryResult.getMain();

        List<SgBStoFreezeOutItem> items = stoOutQueryResult.getItemList();

        if (main == null || CollectionUtils.isEmpty(items)) {
            return;
        }

        String billNo = main.getBillNo();

        //【明细ID,数量】
//        Map<Long, OcBOrderItem> itemIdAndModelMap = orderItemList.stream().collect(Collectors.toMap(OcBOrderItem::getId, o -> o));
        Map<Long, List<OcBOrderItem>> itemIdAndModelMap = orderItemList.stream().collect(Collectors.groupingBy(OcBOrderItem::getPsCSkuId));

        Map<Long, List<SgBStoFreezeOutItem>> collect = items.stream().collect(Collectors.groupingBy(SgBStoFreezeOutItem::getPsCSkuId));

        List<OcBOrderStoOutInfoResult> orderStoOutInfoResultList = new ArrayList<>();
//        Map<String, String> storageTypeMap = AdLimitUtils.getValueMap("SG_B_STO_FREEZE_ITEM","STOCK_TYPE");
        for (SgBStoFreezeOutItem item : items) {
            BigDecimal qtyPreout = item.getQtyPreout();
            if (BigDecimal.ZERO.compareTo(qtyPreout) == 0) {
                continue;
            }
            List<OcBOrderItem> orderItems = itemIdAndModelMap.get(item.getPsCSkuId());

            if (CollectionUtils.isEmpty(orderItems)) {
                throw new NDSException("逻辑占用单条码:" + item.getPsCSkuEcode() + ",不存在于订单明细中");
            }

            OcBOrderItem orderItem = orderItems.get(0);
            OcBOrderStoOutInfoResult orderStoOutInfoResult = new OcBOrderStoOutInfoResult();
            orderStoOutInfoResult.setBillNo(billNo);
            orderStoOutInfoResult.setPsCProId(orderItem.getPsCProId());
            orderStoOutInfoResult.setPsCProEcode(orderItem.getPsCProEcode());
            orderStoOutInfoResult.setPsCProEname(orderItem.getPsCProEname());
            orderStoOutInfoResult.setCpCStoreId(item.getCpCStoreId());
            orderStoOutInfoResult.setCpCStoreEname(item.getCpCStoreEname());
            orderStoOutInfoResult.setProduceDate(item.getProduceDate());
            orderStoOutInfoResult.setQty(orderItem.getQty() == null ? BigDecimal.ZERO : orderItem.getQty());
            orderStoOutInfoResult.setQtyPreout(item.getQtyPreout());
            orderStoOutInfoResult.setItemId(item.getSourceBillItemId());
            orderStoOutInfoResult.setStorageType(storageTypeMap.get(item.getStorageType()));
            if (limit > 0 && collect.get(item.getPsCSkuId()).size() > limit) {
                orderStoOutInfoResult.setOverLimit(true);
            }
            orderStoOutInfoResultList.add(orderStoOutInfoResult);
        }

        toBManualSourcingResult.setStoOutInfoResultList(orderStoOutInfoResultList);
    }

    private void doEncapsulationStoOutData(List<SgBStoOutQueryResult> stoOutQueryResultList,
                                           List<OcBOrderItem> orderItemList,
                                           OcBOrderToBManualSourcingResult toBManualSourcingResult) {

        int limit = 0;
        try {

            limit = Tools.getInt(AdParamUtil.getParam("oms.oc.order.batches.number.Limit"), 0);

        } catch (Exception e) {

            AssertUtils.logAndThrow("获取系统参数:批次个数限制异常");
        }

        if (CollectionUtils.isEmpty(stoOutQueryResultList)) {
            return;
        }

        SgBStoOutQueryResult stoOutQueryResult = stoOutQueryResultList.get(0);

        SgBStoOut main = stoOutQueryResult.getMain();

        List<SgBStoOutItem> items = stoOutQueryResult.getItems();

        if (main == null || CollectionUtils.isEmpty(items)) {
            return;
        }

        String billNo = main.getBillNo();

        //【明细ID,数量】
//        Map<Long, OcBOrderItem> itemIdAndModelMap = orderItemList.stream().collect(Collectors.toMap(OcBOrderItem::getId, o -> o));
        Map<Long, List<OcBOrderItem>> itemIdAndModelMap = orderItemList.stream().collect(Collectors.groupingBy(OcBOrderItem::getPsCSkuId));

        Map<Long, List<SgBStoOutItem>> collect = items.stream().collect(Collectors.groupingBy(SgBStoOutItem::getPsCSkuId));

        List<OcBOrderStoOutInfoResult> orderStoOutInfoResultList = new ArrayList<>();
        for (SgBStoOutItem item : items) {
            BigDecimal qtyPreout = item.getQtyPreout();
            if (BigDecimal.ZERO.compareTo(qtyPreout) == 0) {
                continue;
            }
            List<OcBOrderItem> orderItems = itemIdAndModelMap.get(item.getPsCSkuId());

            if (CollectionUtils.isEmpty(orderItems)) {
                throw new NDSException("逻辑占用单条码:" + item.getPsCSkuEcode() + ",不存在于订单明细中");
            }

            OcBOrderItem orderItem = orderItems.get(0);
            OcBOrderStoOutInfoResult orderStoOutInfoResult = new OcBOrderStoOutInfoResult();
            orderStoOutInfoResult.setBillNo(billNo);
            orderStoOutInfoResult.setPsCProId(orderItem.getPsCProId());
            orderStoOutInfoResult.setPsCProEcode(orderItem.getPsCProEcode());
            orderStoOutInfoResult.setPsCProEname(orderItem.getPsCProEname());
            orderStoOutInfoResult.setCpCStoreId(item.getCpCStoreId());
            orderStoOutInfoResult.setCpCStoreEname(item.getCpCStoreEname());
            orderStoOutInfoResult.setProduceDate(item.getProduceDate());
            orderStoOutInfoResult.setQty(orderItem.getQty() == null ? BigDecimal.ZERO : orderItem.getQty());
            orderStoOutInfoResult.setQtyPreout(item.getQtyPreout());
            orderStoOutInfoResult.setItemId(item.getSourceBillItemId());

            if (limit > 0 && collect.get(item.getPsCSkuId()).size() > limit) {
                orderStoOutInfoResult.setOverLimit(true);
            }
            orderStoOutInfoResultList.add(orderStoOutInfoResult);
        }

        toBManualSourcingResult.setStoOutInfoResultList(orderStoOutInfoResultList);
    }

    private List<SgBStoOutQueryResult> doQueryPreoutItem(OcBOrder order) {
        List<Long> orderIdList = new ArrayList<>();
        orderIdList.add(order.getId());
        return sgRpcService.sgQuerySgBStoOutByIds(orderIdList);
    }

    private List<SgBStoFreezeOutQueryResult> doQueryFreezePreoutItem(OcBOrder order) {

        List<Long> orderIdList = new ArrayList<>();
        orderIdList.add(order.getId());
        return sgRpcService.sgQuerySgBStoFreezeOutByIds(orderIdList);
    }


    private void queryStorageInfoAndEncapsulation(boolean isCc, Long storeId,
                                                  List<OcBOrderItem> orderItemList,
                                                  OcBOrderToBManualSourcingResult toBManualSourcingResult,
                                                  User user, OcBOrder order) {
        int initialCapacity = Math.max((int) (orderItemList.size() / .75f) + 1, 16);

        //【条码ID,效期范围】
        Map<Long, Set<String>> skuIdAndExpiryDateRangeMap = new HashMap<>(initialCapacity);
        //【明细ID,效期范围】
        Map<Long, String> itemIdAndExpiryDateRangeMap = new HashMap<>(initialCapacity);
        //【条码ID,List<效期>】
        Map<Long, List<String>> skuIdAndExpiryDateMap = new HashMap<>(initialCapacity);

        //解析构造效期范围
        analysisExpiryDateRange(skuIdAndExpiryDateRangeMap, itemIdAndExpiryDateRangeMap, orderItemList);

        //条码ID集合
        List<Long> skuIdList = new ArrayList<>(skuIdAndExpiryDateRangeMap.keySet());

        if (isCc) {
            SgFreezeStorageQueryRequest request = new SgFreezeStorageQueryRequest();
            request.setStoreIds(Collections.singletonList(storeId));
            request.setSkuIds(skuIdList);
            ValueHolderV14<List<SgFreezeStorageQueryResult>> freezeStorageInfo = doQueryFreezeStorageInfo(request, user);
            Map<String, List<SgFreezeStorageQueryResult>> freezeStorageInfoMap = freezeStorageInfo.getData().stream()
                    .collect(Collectors.groupingBy(o -> o.getCpCStoreId() + SgConstants.SG_CONNECTOR_MARKS_4 + o.getPsCSkuId()));
            doEncapsulationFreezeStorageData(freezeStorageInfoMap, toBManualSourcingResult, itemIdAndExpiryDateRangeMap, storeId, orderItemList);
        } else {
            //查询具体日期
            ValueHolderV14<Map<String, List<String>>> lsProduceDateResult
                    = doQueryLsProduceDate(storeId, skuIdList, user);
            //解析具体日期构造库存查询入参
            List<SgStorageRedisQueryLsRequest> redisQueryLsRequestList
                    = analysisLsProduceDateAndEncapsulation(storeId, skuIdList,
                    skuIdAndExpiryDateRangeMap, lsProduceDateResult, skuIdAndExpiryDateMap);

            //查询具体到日期的逻辑仓库存
            ValueHolderV14<HashMap<String, SgStorageRedisQueryApiResult>> lsStorageInfoList
                    = doQueryLsStorageInfo(redisQueryLsRequestList, user);
            //查询WMS冻结库存
            Map<String, BigDecimal> inventoryMap = queryInventoryResult(storeId, order.getBillNo(), skuIdList);
            //左侧库存信息封装
            doEncapsulationLsStorageData(lsStorageInfoList, toBManualSourcingResult, skuIdAndExpiryDateMap,
                    itemIdAndExpiryDateRangeMap, storeId, orderItemList, inventoryMap);
        }
    }

    /**
     * 查询WMS冻结库存
     *
     * @param storeId   逻辑仓ID
     * @param billNo    单据编号（用于记录三方日志）
     * @param skuIdList skuId
     * @return
     */
    private Map<String, BigDecimal> queryInventoryResult(Long storeId, String billNo, List<Long> skuIdList) {
        QimenInventoryQueryRequest request = new QimenInventoryQueryRequest();
        request.setBillNo(billNo);
        request.setStoreId(storeId);
        List<QimenInventoryQueryRequest.Criteria> requestItemList = new ArrayList<>();
        request.setCriteriaList(requestItemList);
        for (Long skuId : skuIdList) {
            QimenInventoryQueryRequest.Criteria criteria = new QimenInventoryQueryRequest.Criteria();
            requestItemList.add(criteria);
            criteria.setSkuId(skuId);
        }
        ValueHolderV14<List<QimenInventoryQueryResult>> holderV14 = qimenInventoryQueryCmd.queryInventory(request);
        log.info(LogUtil.format("OcBOrderToBManualSourcingService.queryInventoryResult billNo:{},holderV14:{}",
                "OcBOrderToBManualSourcingService.queryInventoryResult"), billNo, JSONObject.toJSONString(holderV14));
        //WMS冻结库存处理
        Map<String, BigDecimal> inventoryMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(holderV14.getData())) {
            inventoryMap = holderV14.getData().stream()
                    .collect(Collectors.groupingBy(
                            result -> (result.getStoreId() + SgConstants.SG_CONNECTOR_MARKS_4 +
                                    result.getSkuId() + SgConstants.SG_CONNECTOR_MARKS_4 + result.getProductDate()),
                            Collectors.mapping(QimenInventoryQueryResult::getLockQuantity,
                                    Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))
                    ));
        }
        return inventoryMap;
    }

    private void doEncapsulationFreezeStorageData(Map<String, List<SgFreezeStorageQueryResult>> data,
                                                  OcBOrderToBManualSourcingResult toBManualSourcingResult,
                                                  Map<Long, String> itemIdAndExpiryDateRangeMap,
                                                  Long storeId,
                                                  List<OcBOrderItem> orderItemList) {

        List<OcBOrderToBStorageInfoResult> orderToBStorageInfoResultList = new ArrayList<>();
        Map<String, List<OcBOrderItem>> skuIdAndDateMap =
                orderItemList.stream().collect(Collectors.groupingBy(o -> o.getPsCSkuId() + ":" + o.getExpiryDateRange()));
        Set<String> skuAndDateList = skuIdAndDateMap.keySet();
        for (String skuAndDate : skuAndDateList) {
            List<OcBOrderItem> orderItems = skuIdAndDateMap.get(skuAndDate);
            BigDecimal sumQty = orderItems.stream().map(OcBOrderItem::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            OcBOrderItem ocBOrderItem = orderItems.get(0);
            Long skuId = ocBOrderItem.getPsCSkuId();
            OcBOrderToBStorageInfoResult toBStorageInfoResult = new OcBOrderToBStorageInfoResult();

            toBStorageInfoResult.setPsCProId(ocBOrderItem.getPsCProId());
            toBStorageInfoResult.setPsCProEcode(ocBOrderItem.getPsCProEcode());
            toBStorageInfoResult.setPsCProEname(ocBOrderItem.getPsCProEname());

            String s = itemIdAndExpiryDateRangeMap.get(ocBOrderItem.getId());
            if ("UNLIMITED".equals(s)) {
                s = "";
            }
            toBStorageInfoResult.setExpiryDateRange(s);
            //销售量
            toBStorageInfoResult.setQty(sumQty);
            //在库量
            BigDecimal qtyStorage = BigDecimal.ZERO;
            //占用量
            BigDecimal qtyPreOut = BigDecimal.ZERO;
            //可用量
            BigDecimal qtyAvailable = BigDecimal.ZERO;

            String key = storeId + SgConstants.SG_CONNECTOR_MARKS_4 + skuId;
            List<SgFreezeStorageQueryResult> freezeStorageQueryResults = data.get(key);
            if (CollectionUtils.isNotEmpty(freezeStorageQueryResults)) {
                for (SgFreezeStorageQueryResult freezeStorageQueryResult : freezeStorageQueryResults) {
                    // 判断当前库存是否在当前明细的效期范围内
                    boolean flag = withinRange(freezeStorageQueryResult.getProduceDate(), itemIdAndExpiryDateRangeMap, ocBOrderItem.getId());
                    if (flag) {
                        qtyStorage = qtyStorage.add(freezeStorageQueryResult.getQtyFreeze() == null ? BigDecimal.ZERO : freezeStorageQueryResult.getQtyFreeze());
                        qtyPreOut = qtyPreOut.add(freezeStorageQueryResult.getQtyPreout() == null ? BigDecimal.ZERO : freezeStorageQueryResult.getQtyPreout());
                        qtyAvailable = qtyAvailable.add(freezeStorageQueryResult.getQtyAvailable() == null ? BigDecimal.ZERO : freezeStorageQueryResult.getQtyAvailable());
                    }
                }
            }
            toBStorageInfoResult.setQtyStorage(qtyStorage);
            //已更换 此处为总占用
            toBStorageInfoResult.setQtyUnsharedPreout(qtyPreOut);
            toBStorageInfoResult.setQtyAvailable(qtyAvailable);
            toBStorageInfoResult.setQtyDiff(toBStorageInfoResult.getQtyAvailable().subtract(toBStorageInfoResult.getQty()));
            orderToBStorageInfoResultList.add(toBStorageInfoResult);
        }
        toBManualSourcingResult.setStorageInfoResultList(orderToBStorageInfoResultList);
    }

    private void doEncapsulationLsStorageData(ValueHolderV14<HashMap<String, SgStorageRedisQueryApiResult>> lsStorageInfoList,
                                              OcBOrderToBManualSourcingResult toBManualSourcingResult,
                                              Map<Long, List<String>> skuIdAndExpiryDateMap,
                                              Map<Long, String> itemIdAndExpiryDateRangeMap,
                                              Long storeId,
                                              List<OcBOrderItem> orderItemList,
                                              Map<String, BigDecimal> inventoryMap) {
        HashMap<String, SgStorageRedisQueryApiResult> data = lsStorageInfoList.getData();
        List<OcBOrderToBStorageInfoResult> orderToBStorageInfoResultList = new ArrayList<>();

        Map<String, List<OcBOrderItem>> skuIdAndDateMap =
                orderItemList.stream().collect(Collectors.groupingBy(o -> o.getPsCSkuId() + ":" + o.getExpiryDateRange()));
        Set<String> skuAndDateList = skuIdAndDateMap.keySet();
        for (String skuAndDate : skuAndDateList) {
            List<OcBOrderItem> orderItems = skuIdAndDateMap.get(skuAndDate);
            BigDecimal sumQty = orderItems.stream().map(OcBOrderItem::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            OcBOrderItem ocBOrderItem = orderItems.get(0);
            Long skuId = ocBOrderItem.getPsCSkuId();

            OcBOrderToBStorageInfoResult toBStorageInfoResult = new OcBOrderToBStorageInfoResult();
            toBStorageInfoResult.setPsCProId(ocBOrderItem.getPsCProId());
            toBStorageInfoResult.setPsCProEcode(ocBOrderItem.getPsCProEcode());
            toBStorageInfoResult.setPsCProEname(ocBOrderItem.getPsCProEname());
            toBStorageInfoResult.setQty(sumQty);

            String s = itemIdAndExpiryDateRangeMap.get(ocBOrderItem.getId());
            if ("UNLIMITED".equals(s)) {
                s = "";
            }
            toBStorageInfoResult.setExpiryDateRange(s);

            List<String> expiryDateList = skuIdAndExpiryDateMap.get(skuId);

            BigDecimal qtyStorage = BigDecimal.ZERO;
            BigDecimal qtySharedPreout = BigDecimal.ZERO;
            BigDecimal qtyUnSharedPreout = BigDecimal.ZERO;
            BigDecimal qtyAvailable = BigDecimal.ZERO;

            for (String expiryDate : expiryDateList) {

                boolean flag = withinRange(expiryDate, itemIdAndExpiryDateRangeMap, ocBOrderItem.getId());

                if (flag) {
                    String key = storeId + SgConstants.SG_CONNECTOR_MARKS_4 + skuId + SgConstants.SG_CONNECTOR_MARKS_4 + expiryDate;
                    SgStorageRedisQueryApiResult redisQueryApiResult = data.get(key);
                    //WMS冻结量
                    BigDecimal lockQty = inventoryMap.getOrDefault(key, BigDecimal.ZERO);
                    if (redisQueryApiResult != null) {
                        if (redisQueryApiResult.getQtyAvailable() != null &&
                                redisQueryApiResult.getQtyAvailable().subtract(lockQty).compareTo(BigDecimal.ZERO) > 0) {
                            BigDecimal currentQtyStorage = redisQueryApiResult.getQtyStorage() == null ?
                                    BigDecimal.ZERO : redisQueryApiResult.getQtyStorage();
                            qtyStorage = qtyStorage.add(currentQtyStorage.subtract(lockQty));
                            qtySharedPreout = qtySharedPreout.add(redisQueryApiResult.getQtySharedPreout() == null ?
                                    BigDecimal.ZERO : redisQueryApiResult.getQtySharedPreout());
                            qtyUnSharedPreout = qtyUnSharedPreout.add(redisQueryApiResult.getQtyUnsharedPreout() == null ?
                                    BigDecimal.ZERO : redisQueryApiResult.getQtyUnsharedPreout());
                            BigDecimal currentQtyAvailable = redisQueryApiResult.getQtyAvailable() == null ?
                                    BigDecimal.ZERO : redisQueryApiResult.getQtyAvailable();
                            qtyAvailable = qtyAvailable.add(currentQtyAvailable.subtract(lockQty));
                        }
                    }
                }
            }
            toBStorageInfoResult.setQtyStorage(qtyStorage);
            //已更换 此处为总占用
            toBStorageInfoResult.setQtyUnsharedPreout(qtySharedPreout.add(qtyUnSharedPreout));
            toBStorageInfoResult.setQtyAvailable(qtyAvailable);
            toBStorageInfoResult.setQtyDiff(toBStorageInfoResult.getQtyAvailable().subtract(toBStorageInfoResult.getQty()));

            orderToBStorageInfoResultList.add(toBStorageInfoResult);
        }

        toBManualSourcingResult.setStorageInfoResultList(orderToBStorageInfoResultList);
    }

    private boolean withinRange(String expiryDate,
                                Map<Long, String> itemIdAndExpiryDateRangeMap,
                                Long id) {

        String expiryDateRange = itemIdAndExpiryDateRangeMap.get(id);

        if ("UNLIMITED".equals(expiryDateRange)) {
            return true;
        }

        String[] split = expiryDateRange.split("-");

        return Integer.parseInt(expiryDate) >= Integer.parseInt(split[0])
                && Integer.parseInt(expiryDate) <= Integer.parseInt(split[1]);
    }


    private ValueHolderV14<List<SgFreezeStorageQueryResult>> doQueryFreezeStorageInfo(SgFreezeStorageQueryRequest request,
                                                                                      User user) {

        ValueHolderV14<List<SgFreezeStorageQueryResult>> holderV14
                = sgRpcService.queryFreezeStorage(request, user);

        if (!holderV14.isOK() || CollectionUtils.isEmpty(holderV14.getData())) {

            AssertUtils.logAndThrow("未查到对应逻辑仓冻结库存");

        }

        return holderV14;
    }

    private ValueHolderV14<HashMap<String, SgStorageRedisQueryApiResult>> doQueryLsStorageInfo(List<SgStorageRedisQueryLsRequest> redisQueryLsRequestList,
                                                                                               User user) {

        ValueHolderV14<HashMap<String, SgStorageRedisQueryApiResult>> holderV14
                = sgRpcService.queryLsStorageWithRedis(redisQueryLsRequestList, user);

        if (!holderV14.isOK() || MapUtils.isEmpty(holderV14.getData())) {

            AssertUtils.logAndThrow("未查到对应逻辑仓库存");

        }

        return holderV14;
    }

    private List<SgStorageRedisQueryLsRequest> analysisLsProduceDateAndEncapsulation(Long storeId,
                                                                                     List<Long> skuIdList,
                                                                                     Map<Long, Set<String>> skuIdAndExpiryDateRange,
                                                                                     ValueHolderV14<Map<String, List<String>>> lsProduceDateResult,
                                                                                     Map<Long, List<String>> skuIdAndExpiryDateMap) {
        Map<String, List<String>> data = lsProduceDateResult.getData();
        List<SgStorageRedisQueryLsRequest> redisQueryLsRequestList = new ArrayList<>();

        for (Long skuId : skuIdList) {
            List<String> newExpiryDateList = new ArrayList<>();

            //该条码的所有日期
            String key = storeId + SgConstants.SG_CONNECTOR_MARKS_6 + skuId;
            List<String> expiryDateList = data.get(key);

            Set<String> expiryDateRangeList = skuIdAndExpiryDateRange.get(skuId);
            for (String expiryDate : expiryDateList) {
                if (CollectionUtils.isNotEmpty(expiryDateRangeList)) {
                    if (expiryDateRangeList.contains("UNLIMITED")) {
                        SgStorageRedisQueryLsRequest redisQueryLsRequest = new SgStorageRedisQueryLsRequest();
                        redisQueryLsRequest.setCpCStoreId(storeId);
                        redisQueryLsRequest.setPsCSkuId(skuId);
                        redisQueryLsRequest.setProduceDate(expiryDate);

                        redisQueryLsRequestList.add(redisQueryLsRequest);

                        newExpiryDateList.add(expiryDate);
                    } else {
                        for (String expiryDateRange : expiryDateRangeList) {
                            String[] split = expiryDateRange.split("-");
                            if (Integer.parseInt(expiryDate) >= Integer.parseInt(split[0])
                                    && Integer.parseInt(expiryDate) <= Integer.parseInt(split[1])) {

                                SgStorageRedisQueryLsRequest redisQueryLsRequest = new SgStorageRedisQueryLsRequest();
                                redisQueryLsRequest.setCpCStoreId(storeId);
                                redisQueryLsRequest.setPsCSkuId(skuId);
                                redisQueryLsRequest.setProduceDate(expiryDate);

                                redisQueryLsRequestList.add(redisQueryLsRequest);

                                newExpiryDateList.add(expiryDate);
                                break;
                            }
                        }
                    }
                }
            }
            skuIdAndExpiryDateMap.put(skuId, newExpiryDateList);
        }

        return redisQueryLsRequestList;
    }


    private ValueHolderV14<Map<String, List<String>>> doQueryLsProduceDate(Long storeId,
                                                                           List<Long> skuIdList,
                                                                           User user) {
        ArrayList<SgStorageRedisQueryProduceRequest> queryProduceRequestList = new ArrayList<>();
        for (Long skuId : skuIdList) {
            SgStorageRedisQueryProduceRequest queryProduceRequest = new SgStorageRedisQueryProduceRequest();
            queryProduceRequest.setCpCStoreId(storeId);
            queryProduceRequest.setPsCSkuId(skuId);
            queryProduceRequestList.add(queryProduceRequest);
        }

        ValueHolderV14<Map<String, List<String>>> holderV14 = sgRpcService.queryLsProduceDate(queryProduceRequestList, user);

        if (!holderV14.isOK() || MapUtils.isEmpty(holderV14.getData())) {
            AssertUtils.logAndThrow("未查到对应生产日期");
        }

        return holderV14;
    }

    private void analysisExpiryDateRange(Map<Long, Set<String>> keyAndExpiryDateRangeMap,
                                         Map<Long, String> itemIdAndExpiryDateRangeMap,
                                         List<OcBOrderItem> orderItemList) {
        try {
            for (OcBOrderItem ocBOrderItem : orderItemList) {
                Integer expiryDateType = ocBOrderItem.getExpiryDateType();

                String newExpiryDateRange = "UNLIMITED";

                if (expiryDateType != null && StringUtils.isNotEmpty(ocBOrderItem.getExpiryDateRange())) {
                    if (expiryDateType == 2) {
                        String expiryDateRange = ocBOrderItem.getExpiryDateRange();

                        String[] split = expiryDateRange.split("-");

                        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");

                        Calendar calendar1 = Calendar.getInstance();
                        System.out.println(calendar1.getTime());
                        calendar1.add(Calendar.DATE, -Integer.parseInt(split[1]));
                        String stringStartDate = sdf.format(calendar1.getTime());

                        Calendar calendar2 = Calendar.getInstance();
                        System.out.println(calendar2.getTime());
                        calendar2.add(Calendar.DATE, -Integer.parseInt(split[0]));
                        String stringEndDate = sdf.format(calendar2.getTime());

                        newExpiryDateRange = stringStartDate + "-" + stringEndDate;

                    } else if (expiryDateType == 1) {
                        newExpiryDateRange = ocBOrderItem.getExpiryDateRange();
                    }
                }

                if (keyAndExpiryDateRangeMap.containsKey(ocBOrderItem.getPsCSkuId())) {
                    Set<String> dateRangeSet = keyAndExpiryDateRangeMap.get(ocBOrderItem.getPsCSkuId());
                    dateRangeSet.add(newExpiryDateRange);
                } else {
                    Set<String> dateRangeSet = new HashSet<>();
                    dateRangeSet.add(newExpiryDateRange);
                    keyAndExpiryDateRangeMap.put(ocBOrderItem.getPsCSkuId(), dateRangeSet);
                }

                itemIdAndExpiryDateRangeMap.put(ocBOrderItem.getId(), newExpiryDateRange);

            }
        } catch (Exception e) {
            AssertUtils.logAndThrow("效期解析异常");
        }

    }

    /**
     * 主表信息转换
     */
    private void mainTableDataEncapsulation(OcBOrder order, OcBOrderToBManualSourcingResult toBManualSourcingResult) {
        OcBOrderToBMainTableResult mainTableResult = new OcBOrderToBMainTableResult();
        mainTableResult.setSourceCode(order.getSourceCode());
        mainTableResult.setCpCShopTitle(order.getCpCShopTitle());
        mainTableResult.setCpCRegionProvinceEname(order.getCpCRegionProvinceEname());
        mainTableResult.setCpCRegionCityEname(order.getCpCRegionCityEname());
        mainTableResult.setCpCRegionAreaEname(order.getCpCRegionAreaEname());
        mainTableResult.setReceiverAddress(order.getReceiverAddress());
        mainTableResult.setSellerMemo(order.getSellerMemo());
        mainTableResult.setInsideRemark(order.getInsideRemark());
        toBManualSourcingResult.setMainTableResult(mainTableResult);
    }

    /**
     * 寻仓寻物流 右侧明细表查询
     */
    private void queryItemDataAndEncapsulation(OcBOrder order,
                                               List<OcBOrderItem> orderItemList,
                                               OcBOrderToBManualSourcingResult toBManualSourcingResult) {
        //查询TOB仓库辐射设置
        ValueHolderV14<List<SgCTobStrategy>> tobStrategyResult = queryTobStrategy(order);
        if (!tobStrategyResult.isOK() || CollectionUtils.isEmpty(tobStrategyResult.getData())) {
            return;
        }
        List<SgCTobStrategy> tobStrategyList = tobStrategyResult.getData();

        //计算订单总重量
        BigDecimal totalWeight = calculateTotalWeight(orderItemList);
        if (totalWeight.compareTo(BigDecimal.ZERO) <= 0) {
            totalWeight = new BigDecimal("0.01");
        }

        BigDecimal totalKgWeight = calculateKgTotalWeight(orderItemList);

        //计算订单总体积
        BigDecimal totalVolume = calculateTotalVolume(orderItemList);

        StCCarCostRelation stCCarCostRelation = buildRelation(totalWeight, order, totalKgWeight);

        if (CollectionUtils.isEmpty(stCCarCostRelation.getStCCarCostInfos())) {
            return;
        }
        List<StCCarCostInfo> stCCarCostInfos = stCCarCostRelation.getStCCarCostInfos();

        // 获取仓库信息
        List<Long> warehouseIdList = stCCarCostInfos.stream().map(StCCarCostInfo::getCpCPhyWarehouseId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(warehouseIdList)) {
            warehouseIdList = new ArrayList<>();
        }
        if (CollectionUtils.isEmpty(warehouseIdList)) {
            return;
        }

        //查询调拨报价设置
        ValueHolderV14<List<StCAllocationStorageCostStrategy>> v14 = stRpcService.queryFactoryCostByWarehouseIds(warehouseIdList);
        if (!v14.isOK()) {
            throw new NDSException("查询【调拨预估报价设置】失败！");
        }
        Map<Long, List<StCAllocationStorageCostStrategy>> factoryCostMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(v14.getData())) {
            factoryCostMap = v14.getData().stream()
                    .collect(Collectors.groupingBy(StCAllocationStorageCostStrategy::getCpCPhyWarehouseId));
        }
        //封装右边明细数据
        itemDataEncapsulation(stCCarCostRelation, order, totalWeight, toBManualSourcingResult, factoryCostMap, tobStrategyList, totalKgWeight, totalVolume);
    }

    private StCCarCostRelation buildRelation(BigDecimal totalWeight, OcBOrder order, BigDecimal totalKgWeight) {

        //查询零担报价设置
        ValueHolderV14<StCUnfullcarCostQueryResult> unfullcarCostResultHolder = queryUnfullcarCost(totalWeight, order);
        // 查询整车报价
        ValueHolderV14<StCFullcarCostQueryResult> fullcarCostResultHolder = queryFullcarCost(totalWeight, order);
        // 快运报价设置
        ValueHolderV14<StCExpressCostQueryResult> expressCostResultHolder = queryExpressCost(totalKgWeight, order);

        // 查询快运报价
        // 组装unfullcarCostResultHolder与fullcarCostResultHolder的数据到StCCarCostRelation中
        StCCarCostRelation stCCarCostRelation = new StCCarCostRelation();
        List<StCCarCostInfo> stCCarCostInfos = new ArrayList<>();
        List<StCCarCostItemInfo> stCCarCostItemInfos = new ArrayList<>();

        if (fullcarCostResultHolder.isOK() && fullcarCostResultHolder.getData() != null
                && CollectionUtils.isNotEmpty(fullcarCostResultHolder.getData().getFullcarCostList())) {
            // 构建信息
            for (StCFullcarCost fullcarCost : fullcarCostResultHolder.getData().getFullcarCostList()) {
                StCCarCostInfo stCCarCostInfo = new StCCarCostInfo();
                stCCarCostInfo.setCpCPhyWarehouseId(fullcarCost.getCpCPhyWarehouseId());
                stCCarCostInfo.setCpCLogisticsId(fullcarCost.getCpCLogisticsId());
                stCCarCostInfo.setStatus(fullcarCost.getStatus());
                stCCarCostInfo.setCloseStatus(fullcarCost.getCloseStatus());
                stCCarCostInfo.setOilPriceLinkage(fullcarCost.getOilPriceLinkage());
                stCCarCostInfo.setType(1);
                stCCarCostInfo.setId(fullcarCost.getId());
                stCCarCostInfos.add(stCCarCostInfo);
            }
            for (StCFullcarCostItem stCFullcarCostItem : fullcarCostResultHolder.getData().getFullcarCostItemList()) {
                StCCarCostItemInfo stCCarCostItemInfo = new StCCarCostItemInfo();
                stCCarCostItemInfo.setCostId(stCFullcarCostItem.getFullcarCostId());
                stCCarCostItemInfo.setCityId(stCFullcarCostItem.getCityId());
                stCCarCostItemInfo.setProvinceId(stCFullcarCostItem.getProvinceId());
                stCCarCostItemInfo.setStartWeight(stCFullcarCostItem.getStartWeight());
                stCCarCostItemInfo.setEndWeight(stCFullcarCostItem.getEndWeight());
                stCCarCostItemInfo.setTrunkFreight(stCFullcarCostItem.getTrunkFreight());
                stCCarCostItemInfo.setDeliveryFee(stCFullcarCostItem.getDeliveryFee());
                stCCarCostItemInfo.setFreight(stCFullcarCostItem.getFreight());
                stCCarCostItemInfo.setPremium(stCFullcarCostItem.getPremium());
                stCCarCostItemInfo.setUnloadingFee(stCFullcarCostItem.getUnloadingFee());
                stCCarCostItemInfo.setOtherFee(stCFullcarCostItem.getOtherFee());
                stCCarCostItemInfo.setArrivalDays(stCFullcarCostItem.getArrivalDays());
                stCCarCostItemInfo.setId(stCFullcarCostItem.getId());
                stCCarCostItemInfo.setType(1);
                stCCarCostItemInfos.add(stCCarCostItemInfo);
            }
        }
        if (unfullcarCostResultHolder.isOK() && unfullcarCostResultHolder.getData() != null
                && CollectionUtils.isNotEmpty(unfullcarCostResultHolder.getData().getUnfullcarCostList())) {
            // 构建信息
            for (StCUnfullcarCost unfullcarCost : unfullcarCostResultHolder.getData().getUnfullcarCostList()) {
                StCCarCostInfo stCCarCostInfo = new StCCarCostInfo();
                stCCarCostInfo.setCpCPhyWarehouseId(unfullcarCost.getCpCPhyWarehouseId());
                stCCarCostInfo.setStatus(unfullcarCost.getStatus());
                stCCarCostInfo.setCloseStatus(unfullcarCost.getCloseStatus());
                stCCarCostInfo.setCpCLogisticsId(unfullcarCost.getCpCLogisticsId());
                stCCarCostInfo.setOilPriceLinkage(unfullcarCost.getOilPriceLinkage());
                stCCarCostInfo.setType(0);
                stCCarCostInfo.setId(unfullcarCost.getId());
                stCCarCostInfos.add(stCCarCostInfo);
            }
            for (StCUnfullcarCostItem unfullcarCostItem : unfullcarCostResultHolder.getData().getUnfullcarCostItemList()) {
                StCCarCostItemInfo stCCarCostItemInfo = new StCCarCostItemInfo();
                stCCarCostItemInfo.setCostId(unfullcarCostItem.getUnfullcarCostId());
                stCCarCostItemInfo.setCityId(unfullcarCostItem.getCityId());
                stCCarCostItemInfo.setProvinceId(unfullcarCostItem.getProvinceId());
                stCCarCostItemInfo.setStartWeight(unfullcarCostItem.getStartWeight());
                stCCarCostItemInfo.setEndWeight(unfullcarCostItem.getEndWeight());
                stCCarCostItemInfo.setTrunkFreight(unfullcarCostItem.getTrunkFreight());
                stCCarCostItemInfo.setDeliveryFee(unfullcarCostItem.getDeliveryFee());
                stCCarCostItemInfo.setFreight(unfullcarCostItem.getFreight());
                stCCarCostItemInfo.setPremium(unfullcarCostItem.getPremium());
                stCCarCostItemInfo.setUnloadingFee(unfullcarCostItem.getUnloadingFee());
                stCCarCostItemInfo.setOtherFee(unfullcarCostItem.getOtherFee());
                stCCarCostItemInfo.setArrivalDays(unfullcarCostItem.getArrivalDays());
                stCCarCostItemInfo.setId(unfullcarCostItem.getId());
                stCCarCostItemInfo.setType(0);
                stCCarCostItemInfos.add(stCCarCostItemInfo);
            }
        }
        StCExpressCostQueryResult stCExpressCostQueryResult = expressCostResultHolder.getData();
        if (expressCostResultHolder.isOK() && stCExpressCostQueryResult != null
                && CollectionUtils.isNotEmpty(stCExpressCostQueryResult.getExpressCostList())){
            for (StCExpressCost stCExpressCost : stCExpressCostQueryResult.getExpressCostList()){
                StCCarCostInfo stCCarCostInfo = new StCCarCostInfo();
                // 构建数据
                stCCarCostInfo.setCpCPhyWarehouseId(stCExpressCost.getCpCPhyWarehouseId());
                stCCarCostInfo.setCpCLogisticsId(stCExpressCost.getCpCLogisticsId());
                stCCarCostInfo.setStatus(stCExpressCost.getStatus());
                stCCarCostInfo.setCloseStatus(stCExpressCost.getCloseStatus());
                stCCarCostInfo.setType(2);
                stCCarCostInfo.setId(stCExpressCost.getId());
                stCCarCostInfos.add(stCCarCostInfo);
            }
            for (StCExpressCostItem stCExpressCostItem : stCExpressCostQueryResult.getExpressCostItemList()){
                StCCarCostItemInfo stCCarCostItemInfo = new StCCarCostItemInfo();
                stCCarCostItemInfo.setCostId(stCExpressCostItem.getStCExpressCostId());
                stCCarCostItemInfo.setProvinceId(stCExpressCostItem.getProvinceId());
                stCCarCostItemInfo.setStartWeight(stCExpressCostItem.getStartWeight());
                stCCarCostItemInfo.setEndWeight(stCExpressCostItem.getEndWeight());
                stCCarCostItemInfo.setPriceExpress(stCExpressCostItem.getPriceExpress() == null ? BigDecimal.ZERO : stCExpressCostItem.getPriceExpress());
                stCCarCostItemInfo.setPriceFirstWeight(stCExpressCostItem.getPriceFirstWeight() == null ? BigDecimal.ZERO : stCExpressCostItem.getPriceFirstWeight());
                stCCarCostItemInfo.setType(2);
                stCCarCostItemInfos.add(stCCarCostItemInfo);
            }
        }


        stCCarCostRelation.setStCCarCostInfos(stCCarCostInfos);
        stCCarCostRelation.setStCCarCostItemInfos(stCCarCostItemInfos);
        return stCCarCostRelation;
    }

    private BigDecimal calculateTotalWeight(List<OcBOrderItem> orderItemList) {

        //订单明细【商品重量】的汇总
        return orderItemList.stream()
                .map(o -> o.getQty().multiply(o.getStandardWeight() == null ? BigDecimal.ZERO : o.getStandardWeight()))
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .divide(new BigDecimal(1000), 2, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 计算订单明细的总体积
     * @param orderItemList 订单明细列表
     * @return 总体积
     */
    private BigDecimal calculateTotalVolume(List<OcBOrderItem> orderItemList) {
        // 如果订单明细为空，直接返回0
        if (CollectionUtils.isEmpty(orderItemList)) {
            return BigDecimal.ZERO;
        }

        BigDecimal totalVolume = BigDecimal.ZERO;

        // 收集所有商品编码
        List<String> proEcodes = orderItemList.stream()
                .map(OcBOrderItem::getPsCProEcode)
                .filter(StringUtils::isNotEmpty)
                .distinct()
                .collect(Collectors.toList());

        // 如果没有商品编码，直接返回0
        if (CollectionUtils.isEmpty(proEcodes)) {
            return BigDecimal.ZERO;
        }

        // 创建商品编码到体积的映射
        Map<String, BigDecimal> proVolumeMap = new HashMap<>();
        List<PsCPro> psCProList = psRpcService.queryProByEcode(proEcodes);
        if (CollectionUtils.isEmpty(psCProList)){
            return BigDecimal.ZERO;
        }

        for (PsCPro psCPro : psCProList){
            // 如果商品体积为空，则根据长宽高来计算
            BigDecimal volume = psCPro.getVolume();
            if (volume == null){
                //体积为空，根据长宽高计算体积
                BigDecimal length = psCPro.getLength();
                BigDecimal width = psCPro.getWidth();
                BigDecimal height = psCPro.getHeight();
                //一个为空或者为0，不计算体积
                if (Objects.isNull(length) || Objects.isNull(width) || Objects.isNull(height) || length.compareTo(BigDecimal.ZERO) <= 0
                        || width.compareTo(BigDecimal.ZERO) <= 0 || height.compareTo(BigDecimal.ZERO) <= 0) {
                    continue;
                }
                volume = length.multiply(width).multiply(height);
            }
            proVolumeMap.put(psCPro.getEcode(), volume);
        }

        // 计算总体积：每个订单明细的数量 * 对应商品的体积
        for (OcBOrderItem orderItem : orderItemList) {
            String proEcode = orderItem.getPsCProEcode();
            if (StringUtils.isNotEmpty(proEcode) && proVolumeMap.containsKey(proEcode)) {
                BigDecimal volume = proVolumeMap.get(proEcode) == null ? BigDecimal.ZERO : proVolumeMap.get(proEcode);
                BigDecimal qty = orderItem.getQty() == null ? BigDecimal.ONE : orderItem.getQty();
                totalVolume = totalVolume.add(qty.multiply(volume));
            }
        }
        return totalVolume;
    }

    private BigDecimal calculateKgTotalWeight(List<OcBOrderItem> orderItemList) {

        //订单明细【商品重量】的汇总-- 公斤
        return orderItemList.stream()
                .map(o -> o.getQty().multiply(o.getStandardWeight() == null ? BigDecimal.ZERO : o.getStandardWeight()))
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private ValueHolderV14<StCAllocationCostQueryResult> queryAllocationCost(List<Long> warehouseIdList,
                                                                             BigDecimal totalWeight) {
        StCAllocationCostQueryRequest allocationCostQueryRequest = new StCAllocationCostQueryRequest();
        allocationCostQueryRequest.setTotalWeight(totalWeight);
        allocationCostQueryRequest.setWarehouseIdList(warehouseIdList);

        ValueHolderV14<StCAllocationCostQueryResult> resultValueHolder = stRpcService.queryAllocationCost(allocationCostQueryRequest);

        if (!resultValueHolder.isOK()) {
            throw new NDSException("查找调拨报价设置异常:" + resultValueHolder.getMessage());
        }

        return resultValueHolder;
    }

    private void itemDataEncapsulation(StCCarCostRelation stCCarCostRelation,
                                       OcBOrder order,
                                       BigDecimal totalWeight,
                                       OcBOrderToBManualSourcingResult toBManualSourcingResult,
                                       Map<Long, List<StCAllocationStorageCostStrategy>> factoryCostMap,
                                       List<SgCTobStrategy> tobStrategyList,
                                       BigDecimal totalKgWeight,
                                       BigDecimal totalVolume) {
        Long regionProvinceId = order.getCpCRegionProvinceId();
        Long regionCityId = order.getCpCRegionCityId();

        List<StCCarCostInfo> stCCarCostInfos = stCCarCostRelation.getStCCarCostInfos();
        List<StCCarCostItemInfo> stCCarCostItemInfos = stCCarCostRelation.getStCCarCostItemInfos();

        int initialCapacity = Math.max((int) (stCCarCostInfos.size() / .75f) + 1, 16);

        //【实体仓ID_物流公司ID,零担报价主表】
        Map<String, StCCarCostInfo> keyAndMainTableMap = stCCarCostInfos.stream().collect(Collectors.toMap(o -> o.getCpCPhyWarehouseId() + "_" + o.getCpCLogisticsId() + "_" + o.getType(), o -> o));

        //【零担报价主表ID,零担报价明细（省+市+重量 匹配的明细）】
        Map<String, StCCarCostItemInfo> mainTableIdAndItemMap = new HashMap<>(initialCapacity);

        //根据省+市+重量 匹配
        for (StCCarCostItemInfo stCCarCostItemInfo : stCCarCostItemInfos) {
            // 如果类型是2 走下面的逻辑
            if (stCCarCostItemInfo.getType() == 2) {
                if (regionProvinceId.equals(stCCarCostItemInfo.getProvinceId())
                        && totalKgWeight.compareTo(stCCarCostItemInfo.getStartWeight()) > 0
                        && totalKgWeight.compareTo(stCCarCostItemInfo.getEndWeight()) <= 0) {
                    mainTableIdAndItemMap.put(stCCarCostItemInfo.getCostId() + "-" + stCCarCostItemInfo.getType(), stCCarCostItemInfo);
                }
                continue;
            }
            if (regionProvinceId.equals(stCCarCostItemInfo.getProvinceId())
                    && regionCityId.equals(stCCarCostItemInfo.getCityId())
                    && totalWeight.compareTo(stCCarCostItemInfo.getStartWeight()) > 0
                    && totalWeight.compareTo(stCCarCostItemInfo.getEndWeight()) <= 0) {
                mainTableIdAndItemMap.put(stCCarCostItemInfo.getCostId() + "-" + stCCarCostItemInfo.getType(), stCCarCostItemInfo);
            }
        }
        //根据toB辐射仓集合和零担报价组装数据
        List<OcBOrderToBItemTableResult> toBItemTableResultList =
                calculateCar(totalWeight, tobStrategyList, stCCarCostInfos, keyAndMainTableMap, mainTableIdAndItemMap, totalKgWeight, totalVolume);
        if (CollectionUtils.isEmpty(toBItemTableResultList)) {
            return;
        }

        //再根据【调拨预估报价设置】看需不需要再分到更细
        List<OcBOrderToBItemTableResult> newToBItemTableResultList = new ArrayList<>();
        for (OcBOrderToBItemTableResult result : toBItemTableResultList) {
            if (MapUtils.isNotEmpty(factoryCostMap) && factoryCostMap.containsKey(result.getCpCPhyWarehouseId())) {
                List<StCAllocationStorageCostStrategy> costStrategieList = factoryCostMap.get(result.getCpCPhyWarehouseId());
                for (StCAllocationStorageCostStrategy strategy : costStrategieList) {
                    OcBOrderToBItemTableResult newResult = new OcBOrderToBItemTableResult();
                    BeanUtils.copyProperties(result, newResult);
                    newResult.setFactory(strategy.getFactory());
                    BigDecimal allocationCost = strategy.getAllocationCost() != null ? strategy.getAllocationCost() : BigDecimal.ZERO;
                    newResult.setTransferCost(allocationCost.multiply(totalWeight).setScale(2, BigDecimal.ROUND_HALF_UP));
                    BigDecimal storageCost = strategy.getStorageCost() != null ? strategy.getStorageCost() : BigDecimal.ZERO;
                    newResult.setWarehouseCost(storageCost.multiply(totalWeight).setScale(2, BigDecimal.ROUND_HALF_UP));
                    newResult.setTotalCost(newResult.getUnfullcarCost().add(newResult.getTransferCost()).add(newResult.getWarehouseCost()));
                    newToBItemTableResultList.add(newResult);
                }
            } else {
                //没配置默认山东工厂 1-山东，2-河北
                OcBOrderToBItemTableResult newResult = new OcBOrderToBItemTableResult();
                BeanUtils.copyProperties(result, newResult);
                newResult.setFactory(1);
                newResult.setTransferCost(BigDecimal.ZERO);
                newResult.setWarehouseCost(BigDecimal.ZERO);
                newResult.setTotalCost(newResult.getUnfullcarCost().add(newResult.getTransferCost()).add(newResult.getWarehouseCost()));
                newToBItemTableResultList.add(newResult);
            }
        }
        //按照总费用的升序排序
        newToBItemTableResultList.sort(Comparator.comparing(OcBOrderToBItemTableResult::getTotalCost));
        toBManualSourcingResult.setItemTableResultList(newToBItemTableResultList);
    }

    /**
     * @param totalWeight
     * @param tobStrategyList
     * @param stCCarCostInfos
     * @param keyAndMainTableMap
     * @param mainTableIdAndItemMap
     * @return
     */
    private List<OcBOrderToBItemTableResult> calculateCar(BigDecimal totalWeight,
                                                          List<SgCTobStrategy> tobStrategyList,
                                                          List<StCCarCostInfo> stCCarCostInfos,
                                                          Map<String, StCCarCostInfo> keyAndMainTableMap,
                                                          Map<String, StCCarCostItemInfo> mainTableIdAndItemMap,
                                                          BigDecimal totalKgWeight,
                                                          BigDecimal totalVolume) {
        List<OcBOrderToBItemTableResult> toBItemTableResultList = new ArrayList<>();
        Set<Long> warehouseIdSet = tobStrategyList.stream().map(SgCTobStrategy::getCpCPhyWarehouseId).collect(Collectors.toSet());
        for (StCCarCostInfo stCCarCostInfo : stCCarCostInfos) {
            Long warehouseId = stCCarCostInfo.getCpCPhyWarehouseId();
            Long logisticsId = stCCarCostInfo.getCpCLogisticsId();
            Integer type = stCCarCostInfo.getType();
            OcBOrderToBItemTableResult toBItemTableResult = new OcBOrderToBItemTableResult();

            CpCPhyWarehouse warehouse = cpRpcService.queryByWarehouseId(stCCarCostInfo.getCpCPhyWarehouseId());
            if (warehouse == null) {
                continue;
            }
            toBItemTableResult.setType(type);
            toBItemTableResult.setCpCPhyWarehouseId(warehouse.getId());
            toBItemTableResult.setCpCPhyWarehouseEcode(warehouse.getEcode());
            toBItemTableResult.setCpCPhyWarehouseEname(warehouse.getEname());
            CpLogistics logistics = cpRpcService.queryLogisticsById(stCCarCostInfo.getCpCLogisticsId());
            if (logistics == null) {
                continue;
            }
            toBItemTableResult.setCpCLogisticsId(logistics.getId());
            toBItemTableResult.setCpCLogisticsEcode(logistics.getEcode());
            toBItemTableResult.setCpCLogisticsEname(logistics.getEname());
            // 计算费用 如果是2 走快运的计算
            if (type == 2) {
                // 根据快运报价来计算金额
                calculateExpressCost(keyAndMainTableMap, mainTableIdAndItemMap, warehouseId, logisticsId, type, totalKgWeight, toBItemTableResult, totalVolume);
            } else {
                calculateCarCost(keyAndMainTableMap, mainTableIdAndItemMap, warehouseId, logisticsId, type, totalWeight, toBItemTableResult, totalVolume);
            }

            if (warehouseIdSet.contains(toBItemTableResult.getCpCPhyWarehouseId())) {
                toBItemTableResult.setIsRadiationWarehouse(StConstant.ISACTIVE_Y);
            } else {
                toBItemTableResult.setIsRadiationWarehouse(StConstant.ISACTIVE_N);
            }
            toBItemTableResultList.add(toBItemTableResult);
        }
        return toBItemTableResultList;
    }

    private void calculateExpressCost(Map<String, StCCarCostInfo> keyAndMainTableMap,
                                  Map<String, StCCarCostItemInfo> mainTableIdAndItemMap,
                                  Long warehouseId,
                                  Long logisticsId,
                                  Integer type,
                                  BigDecimal totalKgWeight, OcBOrderToBItemTableResult toBItemTableResult, BigDecimal totalVolume) {
        StCCarCostInfo carCost = keyAndMainTableMap.get(warehouseId + "_" + logisticsId + "_" + type);

        if (carCost == null) {
            toBItemTableResult.setArrivalDays(0);
            toBItemTableResult.setUnfullcarCost(BigDecimal.ZERO);
            return;
        }
        // 重新计算carCostItem， 如果物流公司是计泡的物流公司，需要根据体积 重新计算重量。得到的重量与商品的重量对比。 如果重新计算的重量更重的话 需要重新查询报价(如果没查到 则计算的结果为0)
        StCCarCostItemInfo carCostItem = getStCCarCostItemInfo(mainTableIdAndItemMap, logisticsId, totalKgWeight, totalVolume, carCost, type);
        if (carCostItem == null) {
            toBItemTableResult.setArrivalDays(0);
            toBItemTableResult.setUnfullcarCost(BigDecimal.ZERO);
            return;
        }
        totalKgWeight = carCostItem.getTotalWeight();
        BigDecimal logisticsFee = BigDecimal.ZERO;
        if ((totalKgWeight.multiply(carCostItem.getPriceExpress())).compareTo(carCostItem.getPriceFirstWeight()) <= 0) {
            logisticsFee = carCostItem.getPriceFirstWeight();
        } else {
            logisticsFee = totalKgWeight.multiply(carCostItem.getPriceExpress());
        }
        toBItemTableResult.setArrivalDays(null);
        toBItemTableResult.setUnfullcarCost(logisticsFee.setScale(2, BigDecimal.ROUND_HALF_UP));

    }

    private StCCarCostItemInfo getStCCarCostItemInfo(Map<String, StCCarCostItemInfo> mainTableIdAndItemMap, Long logisticsId, BigDecimal totalKgWeight, BigDecimal totalVolume, StCCarCostInfo carCost, Integer type) {
        StCCarCostItemInfo carCostItem = mainTableIdAndItemMap.get(carCost.getId() + "-" + carCost.getType());
        if (carCostItem == null) {
            return null;
        }
        carCostItem.setTotalWeight(totalKgWeight);
        // 判断是否是计泡的物流公司
        ValueHolderV14<List<AcBubbleFeeConfigResult>> valueHolderV14 = acRpcService.queryBubbleFeeConfig(logisticsId);
        if (valueHolderV14.isOK() && CollectionUtils.isNotEmpty(valueHolderV14.getData())){
            // 说明是计泡的物流公司
            List<AcBubbleFeeConfigResult> acBubbleFeeConfigResults = valueHolderV14.getData();
            AcBubbleFeeConfigResult acBubbleFeeConfigResult = acBubbleFeeConfigResults.get(0);
            BigDecimal coefficient = acBubbleFeeConfigResult.getCoefficient();
            BigDecimal multiply = totalVolume.multiply(new BigDecimal("1000000"));
            BigDecimal bubbleWeight = multiply.divide(coefficient, 4, BigDecimal.ROUND_HALF_UP);
            // 因为type为0或者1的时候 是吨。需要乘 1000
            if (type == 0 || type == 1){
                totalKgWeight = totalKgWeight.multiply(new BigDecimal("1000"));
            }
            // 计算出来的是千克
            // 判断 bubbleWeight与totalKgWeight
            if (bubbleWeight.compareTo(totalKgWeight) > 0) {
                totalKgWeight = bubbleWeight;
                if (type == 0 || type == 1){
                    totalKgWeight = totalKgWeight.divide(new BigDecimal("1000"));
                }
                // 如果 bubbleWeight大于totalKgWeight。需要根据报价的id+重量 重新查找数据库找明细
                // 根据类型来进行不同的查询
                if (type == 0){
                    // 零担
                    StCUnfullcarCostDetailQueryRequest request = new StCUnfullcarCostDetailQueryRequest();
                    request.setUnfullcarCostId(carCost.getId());
                    request.setWeight(bubbleWeight.divide(new BigDecimal(1000), 2, BigDecimal.ROUND_HALF_UP));
                    request.setProvinceId(carCostItem.getProvinceId());
                    request.setCityId(carCostItem.getCityId());
                    ValueHolderV14<StCUnfullcarCostDetailQueryResult> resultValueHolderV14 = stRpcService.queryUnfullcarCostDetail(request);
                    if (resultValueHolderV14.isOK()){
                        StCUnfullcarCostDetailQueryResult data = resultValueHolderV14.getData();
                        if (CollectionUtils.isNotEmpty(data.getUnfullcarCostItemList())){
                            List<StCUnfullcarCostItem> unfullcarCostItemList = data.getUnfullcarCostItemList();
                            StCUnfullcarCostItem unfullcarCostItem = unfullcarCostItemList.get(0);
                            carCostItem = new StCCarCostItemInfo();
                            carCostItem.setCostId(unfullcarCostItem.getUnfullcarCostId());
                            carCostItem.setCityId(unfullcarCostItem.getCityId());
                            carCostItem.setProvinceId(unfullcarCostItem.getProvinceId());
                            carCostItem.setStartWeight(unfullcarCostItem.getStartWeight());
                            carCostItem.setEndWeight(unfullcarCostItem.getEndWeight());
                            carCostItem.setTrunkFreight(unfullcarCostItem.getTrunkFreight());
                            carCostItem.setDeliveryFee(unfullcarCostItem.getDeliveryFee());
                            carCostItem.setFreight(unfullcarCostItem.getFreight());
                            carCostItem.setPremium(unfullcarCostItem.getPremium());
                            carCostItem.setUnloadingFee(unfullcarCostItem.getUnloadingFee());
                            carCostItem.setOtherFee(unfullcarCostItem.getOtherFee());
                            carCostItem.setArrivalDays(unfullcarCostItem.getArrivalDays());
                            carCostItem.setId(unfullcarCostItem.getId());
                            carCostItem.setType(0);
                        }else {
                            carCostItem.setStartWeight(BigDecimal.ZERO);
                            carCostItem.setEndWeight(BigDecimal.ZERO);
                            carCostItem.setTrunkFreight(BigDecimal.ZERO);
                            carCostItem.setDeliveryFee(BigDecimal.ZERO);
                            carCostItem.setFreight(BigDecimal.ZERO);
                            carCostItem.setPremium(BigDecimal.ZERO);
                            carCostItem.setUnloadingFee(BigDecimal.ZERO);
                            carCostItem.setOtherFee(BigDecimal.ZERO);
                            carCostItem.setArrivalDays(0);
                        }
                    }
                }else if (type == 1){
                    StCFullcarCostDetailQueryRequest request = new StCFullcarCostDetailQueryRequest();
                    request.setFullcarCostId(carCost.getId());
                    request.setWeight(bubbleWeight.divide(new BigDecimal(1000), 2, BigDecimal.ROUND_HALF_UP));
                    request.setProvinceId(carCostItem.getProvinceId());
                    request.setCityId(carCostItem.getCityId());
                    ValueHolderV14<StCFullcarCostDetailQueryResult>  resultValueHolderV14 = stRpcService.queryFullcarCostDetail(request);
                    if (resultValueHolderV14.isOK()){
                        StCFullcarCostDetailQueryResult data = resultValueHolderV14.getData();
                        if (CollectionUtils.isNotEmpty(data.getFullcarCostItemList())){
                            List<StCFullcarCostItem> fullcarCostItemList = data.getFullcarCostItemList();
                            StCFullcarCostItem stCFullcarCostItem = fullcarCostItemList.get(0);
                            carCostItem = new StCCarCostItemInfo();
                            carCostItem.setCostId(stCFullcarCostItem.getFullcarCostId());
                            carCostItem.setCityId(stCFullcarCostItem.getCityId());
                            carCostItem.setProvinceId(stCFullcarCostItem.getProvinceId());
                            carCostItem.setStartWeight(stCFullcarCostItem.getStartWeight());
                            carCostItem.setEndWeight(stCFullcarCostItem.getEndWeight());
                            carCostItem.setTrunkFreight(stCFullcarCostItem.getTrunkFreight());
                            carCostItem.setDeliveryFee(stCFullcarCostItem.getDeliveryFee());
                            carCostItem.setFreight(stCFullcarCostItem.getFreight());
                            carCostItem.setPremium(stCFullcarCostItem.getPremium());
                            carCostItem.setUnloadingFee(stCFullcarCostItem.getUnloadingFee());
                            carCostItem.setOtherFee(stCFullcarCostItem.getOtherFee());
                            carCostItem.setArrivalDays(stCFullcarCostItem.getArrivalDays());
                            carCostItem.setId(stCFullcarCostItem.getId());
                            carCostItem.setType(1);
                        }else {
                            carCostItem.setStartWeight(BigDecimal.ZERO);
                            carCostItem.setEndWeight(BigDecimal.ZERO);
                            carCostItem.setTrunkFreight(BigDecimal.ZERO);
                            carCostItem.setDeliveryFee(BigDecimal.ZERO);
                            carCostItem.setFreight(BigDecimal.ZERO);
                            carCostItem.setPremium(BigDecimal.ZERO);
                            carCostItem.setUnloadingFee(BigDecimal.ZERO);
                            carCostItem.setOtherFee(BigDecimal.ZERO);
                            carCostItem.setArrivalDays(null);
                            carCostItem.setType(1);
                        }
                    }
                }else if (type == 2){
                    StCExpressCostDetailQueryRequest request = new StCExpressCostDetailQueryRequest();
                    request.setExpressCostId(carCost.getId());
                    request.setWeight(bubbleWeight);
                    request.setProvinceId(carCostItem.getProvinceId());
                    ValueHolderV14<StCExpressCostDetailQueryResult> resultValueHolderV14 = stRpcService.queryExpressCostDetail(request);
                    if (resultValueHolderV14.isOK()){
                        StCExpressCostDetailQueryResult data = resultValueHolderV14.getData();
                        if (CollectionUtils.isNotEmpty(data.getExpressCostItemList())){
                            List<StCExpressCostItem> expressCostItemList = data.getExpressCostItemList();
                            StCExpressCostItem stCExpressCostItem = expressCostItemList.get(0);
                            carCostItem = new StCCarCostItemInfo();
                            carCostItem.setCostId(stCExpressCostItem.getStCExpressCostId());
                            carCostItem.setProvinceId(stCExpressCostItem.getProvinceId());
                            carCostItem.setStartWeight(stCExpressCostItem.getStartWeight());
                            carCostItem.setEndWeight(stCExpressCostItem.getEndWeight());
                            carCostItem.setPriceExpress(stCExpressCostItem.getPriceExpress() == null ? BigDecimal.ZERO : stCExpressCostItem.getPriceExpress());
                            carCostItem.setPriceFirstWeight(stCExpressCostItem.getPriceFirstWeight() == null ? BigDecimal.ZERO : stCExpressCostItem.getPriceFirstWeight());
                        }else {
                            // 如果没查到 产品要求给塞0(有问题找雷宏宇)
                            carCostItem.setCostId(carCost.getId());
                            carCostItem.setProvinceId(carCostItem.getProvinceId());
                            carCostItem.setStartWeight(BigDecimal.ZERO);
                            carCostItem.setEndWeight(BigDecimal.ZERO);
                            carCostItem.setPriceExpress(BigDecimal.ZERO);
                            carCostItem.setPriceFirstWeight(BigDecimal.ZERO);
                            carCostItem.setArrivalDays(null);
                        }
                    }
                }
                carCostItem.setTotalWeight(totalKgWeight);
            }
        }
        return carCostItem;
    }

    private void calculateCarCost(Map<String, StCCarCostInfo> keyAndMainTableMap,
                                  Map<String, StCCarCostItemInfo> mainTableIdAndItemMap,
                                  Long warehouseId,
                                  Long logisticsId,
                                  Integer type,
                                  BigDecimal totalWeight, OcBOrderToBItemTableResult toBItemTableResult, BigDecimal totalVolume) {
        StCCarCostInfo carCost = keyAndMainTableMap.get(warehouseId + "_" + logisticsId + "_" + type);

        if (carCost == null) {
            toBItemTableResult.setArrivalDays(0);
            toBItemTableResult.setUnfullcarCost(BigDecimal.ZERO);
            return;
        }

        StCCarCostItemInfo carCostItem = getStCCarCostItemInfo(mainTableIdAndItemMap, logisticsId, totalWeight, totalVolume, carCost, type);

        if (carCostItem == null) {
            toBItemTableResult.setArrivalDays(0);
            toBItemTableResult.setUnfullcarCost(BigDecimal.ZERO);
            return;
        }
        totalWeight = carCostItem.getTotalWeight();

        //所属重量范围干线运费 * （1+油价联动）* 订单重量
        BigDecimal cost1 = BigDecimal.ONE.add(carCost.getOilPriceLinkage().divide(new BigDecimal(100), 4, BigDecimal.ROUND_HALF_UP))
                .multiply(totalWeight)
                .multiply(carCostItem.getTrunkFreight());

        BigDecimal cost2 = carCostItem.getDeliveryFee() == null ? BigDecimal.ZERO : carCostItem.getDeliveryFee();

        //送货费
        BigDecimal cost3 = carCostItem.getFreight();
        //保费 * 订单的重量
        BigDecimal cost4 = totalWeight.multiply(carCostItem.getPremium() == null ? BigDecimal.ZERO : carCostItem.getPremium());
        //卸货 * 订单的重量
        BigDecimal cost5 = totalWeight.multiply(carCostItem.getUnloadingFee() == null ? BigDecimal.ZERO : carCostItem.getUnloadingFee());
        //其他费用费
        BigDecimal cost6 = carCostItem.getOtherFee() == null ? BigDecimal.ZERO : carCostItem.getOtherFee();

        BigDecimal cost = cost1.add(cost2).add(cost3).add(cost4).add(cost5).add(cost6);
        cost = cost.setScale(2, BigDecimal.ROUND_HALF_UP);

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("OcBOrderToBManualSourcingService.calculateCarCost 零担策略明细ID:{}",
                    cost1, cost2, cost3, cost4, cost5, cost6, cost, totalWeight), carCostItem.getId());
        }

        toBItemTableResult.setArrivalDays(carCostItem.getArrivalDays());
        toBItemTableResult.setUnfullcarCost(cost.setScale(2, BigDecimal.ROUND_HALF_UP));
    }


    @Deprecated
    private BigDecimal calculateTransferCost(Long cpCPhyWarehouseId,
                                             Map<Long, BigDecimal> idAndOilPriceLinkageMap,
                                             Map<Long, List<StCAllocationCostItem>> warehouseIdAndItemListMap,
                                             BigDecimal totalWeight) {
        List<StCAllocationCostItem> stCAllocationCostItems = warehouseIdAndItemListMap.get(cpCPhyWarehouseId);

        if (CollectionUtils.isEmpty(stCAllocationCostItems)) {
            return BigDecimal.ZERO;
        }

        BigDecimal costResult = BigDecimal.ZERO;

        for (StCAllocationCostItem stCAllocationCostItem : stCAllocationCostItems) {

            Long stCAllocationCostId = stCAllocationCostItem.getStCAllocationCostId();
            BigDecimal oilPriceLinkage = idAndOilPriceLinkageMap.get(stCAllocationCostId);

            if (oilPriceLinkage == null) {
                log.warn(LogUtil.format("OcBOrderToBManualSourcingService.calculateTransferCost 未查到调拨报价主标表信息 调拨报价明细ID:{}"), stCAllocationCostItem.getId());
                continue;
            }

            //所属重量范围干线运费 * （1+油价联动）* 订单重量
            BigDecimal cost1 = BigDecimal.ONE.add(oilPriceLinkage.divide(new BigDecimal(100), 4, BigDecimal.ROUND_HALF_UP))
                    .multiply(totalWeight)
                    .multiply(stCAllocationCostItem.getTrunkFreight() == null ? BigDecimal.ZERO : stCAllocationCostItem.getTrunkFreight());

            //其他费用费
            BigDecimal cost2 = stCAllocationCostItem.getOtherFee() == null ? BigDecimal.ZERO : stCAllocationCostItem.getOtherFee();

            //总费用
            BigDecimal cost = cost1.add(cost2).setScale(2, BigDecimal.ROUND_HALF_UP);

            if (costResult.compareTo(BigDecimal.ZERO) == 0 || cost.compareTo(costResult) < 0) {
                costResult = cost;
            }
        }

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("OcBOrderToBManualSourcingService.calculateTransferCost 最终费用:{}",
                    costResult, totalWeight), costResult);
        }

        return costResult;
    }

    private ValueHolderV14<StCFullcarCostQueryResult> queryFullcarCost(BigDecimal totalWeight, OcBOrder order) {
        StCFullcarCostQueryRequest fullcarCostQueryRequest = new StCFullcarCostQueryRequest();
        fullcarCostQueryRequest.setRegionProvinceId(order.getCpCRegionProvinceId());
        fullcarCostQueryRequest.setRegionCityId(order.getCpCRegionCityId());
        fullcarCostQueryRequest.setTotalWeight(totalWeight);
        return stRpcService.queryFullcarCost(fullcarCostQueryRequest);
    }

    private ValueHolderV14<StCExpressCostQueryResult> queryExpressCost(BigDecimal totalWeight, OcBOrder order){
        StCExpressCostQueryRequest expressCostQueryRequest = new StCExpressCostQueryRequest();
        expressCostQueryRequest.setProvinceId(order.getCpCRegionProvinceId());
        expressCostQueryRequest.setTotalWeight(totalWeight);
        return stRpcService.queryExpressCost(expressCostQueryRequest);
    }


    private ValueHolderV14<StCUnfullcarCostQueryResult> queryUnfullcarCost(BigDecimal totalWeight,
                                                                           OcBOrder order) {
        StCUnfullcarCostQueryRequest unfullcarCostQueryRequest = new StCUnfullcarCostQueryRequest();
        unfullcarCostQueryRequest.setRegionProvinceId(order.getCpCRegionProvinceId());
        unfullcarCostQueryRequest.setRegionCityId(order.getCpCRegionCityId());
        unfullcarCostQueryRequest.setTotalWeight(totalWeight);

        return stRpcService.queryUnfullcarCost(unfullcarCostQueryRequest);
    }

    /**
     * 查询仓库物流设置
     */
    private ValueHolderV14<StCWarehouseLogisticStrategyQueryResult> queryWarehouseLogisticStrategy(List<Long> warehouseIdList) {
        StCWarehouseLogisticStrategyQueryRequest warehouseLogisticStrategyQueryRequest = new StCWarehouseLogisticStrategyQueryRequest();
        warehouseLogisticStrategyQueryRequest.setWarehouseIdList(warehouseIdList);

        return stRpcService.queryLogisticStrategyByWarehouseId(warehouseLogisticStrategyQueryRequest);
    }

    /**
     * 查询TOB仓库辐射设置
     */
    private ValueHolderV14<List<SgCTobStrategy>> queryTobStrategy(OcBOrder order) {
        /*先根据下单店铺去平台店铺档案中找到店铺主数据，再去查询店铺的“组织编码（常规）”下的“销售部门编码”*/
        Long cpCDepartmentId = cpRpcService.queryDepartmentIdByShopId(order.getCpCShopId());

        SgCTobStrategyQueryRequest strategyQueryRequest = new SgCTobStrategyQueryRequest();
        strategyQueryRequest.setCpCRegionProvinceId(order.getCpCRegionProvinceId());
        strategyQueryRequest.setCpCRegionCityId(order.getCpCRegionCityId());
        strategyQueryRequest.setCpCDepartmentId(cpCDepartmentId);
        return sgRpcService.queryTobStrategy(strategyQueryRequest);
    }

    /**
     * 状态校验
     */
    private void statusCheck(OcBOrder order) {
        Integer orderStatus = order.getOrderStatus();
        if (!(OmsOrderStatus.UNCONFIRMED.toInteger().equals(orderStatus) ||
                OmsOrderStatus.CHECKED.toInteger().equals(orderStatus) ||
                OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(orderStatus))) {
            AssertUtils.logAndThrow("当前状态，不允许寻仓寻物流!");
        }
        Long businessTypeId = order.getBusinessTypeId();
        if (businessTypeId == null) {
            AssertUtils.logAndThrow("当前订单的业务类型为空");
        }
        StCBusinessType businessType = businessTypeMapper.selectById(businessTypeId);
        if (businessType != null && businessType.getIsSourceOccupy() != null && businessType.getIsSourceOccupy() == 1) {
            AssertUtils.logAndThrow("当前订单业务类型不允许执行寻仓寻物流！");
        }
    }

    private void paramCheck(OcBOrderToBManualSourcingRequest request) {
        if (request == null) {
            throw new NDSException("请求参数不能为空!");
        }

        if (request.getUser() == null) {
            throw new NDSException("请求用户不能为空!");
        }

        if (request.getConfirmType() != null && 1 == request.getConfirmType()) {
            if (request.getCpCLogisticsId() == null || request.getCpCPhyWarehouseId() == null) {
                throw new NDSException("物流或仓库不能为空!");
            }
        } else if (request.getConfirmType() != null && 2 == request.getConfirmType()) {
            if (CollectionUtils.isEmpty(request.getStoOutInfoResultList())) {
                throw new NDSException("重新占单数据不能为空!");
            }
        }

    }

    private void warehouseLogisticCheck(OcBOrderToBManualSourcingRequest request, Long provinceId, BigDecimal weight) {
        Long cpCPhyWarehouseId = request.getCpCPhyWarehouseId();
        Long cpCLogisticsId = request.getCpCLogisticsId();

        List<Long> warehouseIdList = new ArrayList<>();
        warehouseIdList.add(cpCPhyWarehouseId);
        ValueHolderV14<StCWarehouseLogisticStrategyQueryResult> logisticStrategyResult = queryWarehouseLogisticStrategy(warehouseIdList);

        if (!logisticStrategyResult.isOK()
                || logisticStrategyResult.getData() == null
                || CollectionUtils.isEmpty(logisticStrategyResult.getData().getLogisticStrategiesList())) {
            throw new NDSException("物流公司与仓库物流策略不匹配!");
        }

        StCWarehouseLogisticStrategyQueryResult data = logisticStrategyResult.getData();
        List<Long> logisticsIdList = data.getLogisticsIdList();

        if (!logisticsIdList.contains(cpCLogisticsId)) {
            throw new NDSException("物流公司与仓库物流策略不匹配!");
        }

        if (!Objects.isNull(provinceId) && !Objects.isNull(weight)) {
            modifyOrderLogisticsService.expressCheck(cpCLogisticsId, request.getId(), request.getCpCPhyWarehouseId(), provinceId);
        }
    }

    private void voidOutOrder(boolean isCc, OcBOrder order, User user) {
        if (OmsOrderStatus.UNCONFIRMED.toInteger().equals(order.getOrderStatus())
                || OmsOrderStatus.CHECKED.toInteger().equals(order.getOrderStatus())) {
            ValueHolderV14 valueHolderV14 = sgRpcService.invoildOutOrder(isCc, order, user);

            if (!valueHolderV14.isOK()) {
                throw new NDSException(valueHolderV14.getMessage());
            }
        }
    }

    public void occupyOrder(boolean isCc, OcBOrder order,
                            List<OcBOrderItem> orderItemList,
                            OcBOrderToBManualSourcingRequest request, Map<Long, CpCShopProfileExt> cpCShopProfileMap, String appointDistLevel3) {
        log.info(LogUtil.format("OcBOrderToBManualSourcingService.occupyOrder param: isCc:{} order:{} orderItemList:{} request:{}",
                "TOB寻仓寻物流寻源占单"), isCc, JSONObject.toJSONString(order), JSONObject.toJSONString(orderItemList), JSONObject.toJSONString(request));
        if (isCc) {
            occupyFreezeOrder(order, orderItemList, request);
            return;
        }

        SgFindSourceStrategyC2SRequest sgFindSourceStrategyC2SRequest = buildSgRequest(order, orderItemList, request, cpCShopProfileMap);
        if (StringUtils.isNotEmpty(appointDistLevel3)) {
            sgFindSourceStrategyC2SRequest.setAppointDistLevel3(appointDistLevel3);
        }
        log.info(LogUtil.format("OcBOrderToBManualSourcingService.occupyOrder request:{} 订单ID:{}",
                "TOB寻仓寻物流寻源占单", JSONObject.toJSONString(sgFindSourceStrategyC2SRequest), order.getId()));

        try {
            String topic = Mq5Constants.TOPIC_R3_SG_TOBECONFIRM;
            String tag = Mq5Constants.TAG_R3_SG_TOBECONFIRM;
            String jsonValue = JSONObject.toJSONString(sgFindSourceStrategyC2SRequest);

            //更新状态为TOB寻源占单中 更新为 "寻源中"
            OcBOrder updateModel = new OcBOrder();
            updateModel.setId(order.getId());
            updateModel.setOrderStatus(OmsOrderStatus.OCCUPY_IN.toInteger());
            updateModel.setSysremark("TOB寻源占单中");
            updateModel.setCpCLogisticsId(request.getCpCLogisticsId());
            updateModel.setCpCLogisticsEcode(request.getCpCLogisticsEcode());
            updateModel.setCpCLogisticsEname(request.getCpCLogisticsEname());
            OmsModelUtil.setDefault4Upd(updateModel, request.getUser());
            orderMapper.updateById(updateModel);

//            String result = r3MqSendHelper.sendMessage(jsonValue, topic, tag);
            //  30天内未使用
            MqSendResult result = defaultProducerSend.sendTopic(topic, tag, jsonValue, null);


            log.info(LogUtil.format("OcBOrderToBManualSourcingService.occupyOrder result:{} 订单ID:{}",
                    "TOB寻仓寻物流寻源占单MQ结果", JSONObject.toJSONString(result), order.getId()));

            omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(), OrderLogTypeEnum.TOB_OCCUPY.getKey(), "OrderId=" + order.getId() + "TOB寻源占单中", "", "", request.getUser());
        } catch (Exception e) {

            log.error(LogUtil.format("OcBOrderToBManualSourcingService.occupyOrder 订单ID:{} error:{}",
                    "TOB寻仓寻物流寻源占单异常", order.getId(), Throwables.getStackTraceAsString(e)));
        }
    }

    public void occupyFreezeOrder(OcBOrder order,
                                  List<OcBOrderItem> orderItemList,
                                  OcBOrderToBManualSourcingRequest request) {
        CpCPhyWarehouse warehouse = null;
        if (request.getCpCPhyWarehouseId() != null) {
            warehouse = cpRpcService.selectPhyWarehouseById(request.getCpCPhyWarehouseId());
            if (warehouse == null) {
                throw new NDSException("仓库信息不存在");
            }
        } else {
            if (order.getCpCPhyWarehouseId() == null) {
                throw new NDSException("重新占单发货仓库不能为空");
            }
            request.setCpCPhyWarehouseId(order.getCpCPhyWarehouseId());
        }
        SgBStoFreezeOutOmsSaveRequest stoFreezeOutBillSaveRequest = buildFreezeOutBillSaveRequest(order, orderItemList, request);
        try {
            ValueHolderV14 v14 = sgRpcService.findSourceSaveFreezeOut(stoFreezeOutBillSaveRequest);
            log.info(LogUtil.format("OcBOrderToBManualSourcingService.occupyFreezeOrder result:{} 订单ID:{}",
                    "TOB寻仓寻物流生成冻结占用单结果", JSONObject.toJSONString(v14), order.getId()));
            String message;
            Integer orderStatus;
            OcBOrder updateModel = new OcBOrder();
            if (v14.isOK()) {
                if (warehouse != null) {
                    updateModel.setCpCPhyWarehouseId(warehouse.getId());
                    updateModel.setCpCPhyWarehouseEcode(warehouse.getEcode());
                    updateModel.setCpCPhyWarehouseEname(warehouse.getEname());
                }
                orderStatus = OmsOrderStatus.UNCONFIRMED.toInteger();
                message = " 冻结库存占用成功！";
            } else {
                orderStatus = OmsOrderStatus.BE_OUT_OF_STOCK.toInteger();
                message = " 冻结库存占用失败," + v14.getMessage();
            }

            updateModel.setId(order.getId());
            updateModel.setOrderStatus(orderStatus);
            updateModel.setCpCLogisticsId(request.getCpCLogisticsId());
            updateModel.setCpCLogisticsEcode(request.getCpCLogisticsEcode());
            updateModel.setCpCLogisticsEname(request.getCpCLogisticsEname());
            OmsModelUtil.setDefault4Upd(updateModel, request.getUser());
            orderMapper.updateById(updateModel);
            omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(), OrderLogTypeEnum.TOB_OCCUPY.getKey(), "OrderId=" + order.getId() + message, "", "", request.getUser());
            AssertUtils.isTrue(v14.isOK(), message);
        } catch (Exception e) {
            log.error(LogUtil.format("OcBOrderToBManualSourcingService.occupyFreezeOrder 订单ID:{} error:{}",
                    "TOB寻仓寻物流生成冻结占用单异常", order.getId(), Throwables.getStackTraceAsString(e)));
            throw e;
        }

    }

    private SgBStoFreezeOutOmsSaveRequest buildFreezeOutBillSaveRequest(OcBOrder order,
                                                                        List<OcBOrderItem> orderItemList,
                                                                        OcBOrderToBManualSourcingRequest request) {
        SgBStoFreezeOutOmsSaveRequest stoFreezeOutOmsSaveRequest = new SgBStoFreezeOutOmsSaveRequest();
        stoFreezeOutOmsSaveRequest.setSourceBillId(order.getId());
        stoFreezeOutOmsSaveRequest.setSourceBillNo(order.getBillNo());
        stoFreezeOutOmsSaveRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_RETAIL);
        stoFreezeOutOmsSaveRequest.setBillDate(new Date());
        stoFreezeOutOmsSaveRequest.setSourceBillDate(order.getOrderDate());
        stoFreezeOutOmsSaveRequest.setWarehouseId(request.getCpCPhyWarehouseId());
        stoFreezeOutOmsSaveRequest.setShopId(order.getCpCShopId());
        stoFreezeOutOmsSaveRequest.setProvinceId(order.getCpCRegionProvinceId());
        stoFreezeOutOmsSaveRequest.setCityId(order.getCpCRegionCityId());
        stoFreezeOutOmsSaveRequest.setAreaId(order.getCpCRegionAreaId());
        stoFreezeOutOmsSaveRequest.setTid(order.getSourceCode());
        stoFreezeOutOmsSaveRequest.setUser(request.getUser());
        stoFreezeOutOmsSaveRequest.setSaleProductAttr(order.getSaleProductAttr());

        List<OcBOrderStoOutInfoResult> stoOutInfoResultList = request.getStoOutInfoResultList();
        List<SgBStoFreezeOutOmsSaveItemRequest> skuItems = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(stoOutInfoResultList)) {
            //【商品ID,List<OcBOrderStoOutInfoResult>】
            Map<Long, List<OcBOrderStoOutInfoResult>> itemIdAndModelListMap = stoOutInfoResultList.stream().collect(Collectors.groupingBy(OcBOrderStoOutInfoResult::getPsCProId));
            //【商品ID,List<OcBOrderItem>】
            Map<Long, List<OcBOrderItem>> proIdAndItemListMap = orderItemList.stream().collect(Collectors.groupingBy(OcBOrderItem::getPsCProId));
            for (Long proId : proIdAndItemListMap.keySet()) {
                List<OcBOrderItem> ocBOrderItems = proIdAndItemListMap.get(proId);

                if (ocBOrderItems.size() == 1) {
                    OcBOrderItem ocBOrderItem = ocBOrderItems.get(0);
                    Long id = ocBOrderItem.getId();

                    List<OcBOrderStoOutInfoResult> orderStoOutInfoResultList = itemIdAndModelListMap.get(ocBOrderItem.getPsCProId());
                    SgBStoFreezeOutOmsSaveItemRequest skuItem = new SgBStoFreezeOutOmsSaveItemRequest();
                    skuItem.setSourceItemId(id);
                    skuItem.setPsCSkuId(ocBOrderItem.getPsCSkuId());
                    skuItem.setSkuId(ocBOrderItem.getSkuNumiid());
                    skuItem.setNumiid(ocBOrderItem.getNumIid());
                    skuItem.setPsCProId(ocBOrderItem.getPsCProId());
                    skuItem.setPsCProdimId(ocBOrderItem.getMDim6Id());
                    skuItem.setQty(ocBOrderItem.getQty());
                    skuItem.setLabelingRequirements(ocBOrderItem.getReserveVarchar02());

                    Map<String, BigDecimal> qtyMap = new HashMap<>();

                    for (OcBOrderStoOutInfoResult orderStoOutInfoResult : orderStoOutInfoResultList) {
                        String produceDate = orderStoOutInfoResult.getProduceDate();
                        BigDecimal qtyPreout = orderStoOutInfoResult.getQtyPreout() == null ? BigDecimal.ZERO : orderStoOutInfoResult.getQtyPreout();

                        if (qtyMap.containsKey(produceDate)) {
                            BigDecimal orgQtyPreout = qtyMap.get(produceDate) == null ? BigDecimal.ZERO : qtyMap.get(produceDate);
                            qtyMap.put(produceDate, qtyPreout.add(orgQtyPreout));
                        } else {
                            qtyMap.put(produceDate, qtyPreout);
                        }
                    }

                    skuItem.setQtyMap(qtyMap);
                    skuItems.add(skuItem);
                } else {

                    //商品维度 【效期,占用数量】
                    Map<String, BigDecimal> produceDateAndQty = new HashMap<>();
                    List<OcBOrderStoOutInfoResult> orderStoOutInfoResultList = itemIdAndModelListMap.get(proId);
                    for (OcBOrderStoOutInfoResult ocBOrderStoOutInfoResult : orderStoOutInfoResultList) {
                        String produceDate = ocBOrderStoOutInfoResult.getProduceDate();
                        BigDecimal qtyPreout = ocBOrderStoOutInfoResult.getQtyPreout() == null ? BigDecimal.ZERO : ocBOrderStoOutInfoResult.getQtyPreout();

                        if (produceDateAndQty.containsKey(produceDate)) {
                            BigDecimal orgQtyPreout = produceDateAndQty.get(produceDate);
                            produceDateAndQty.put(produceDate, qtyPreout.add(orgQtyPreout));
                        } else {
                            produceDateAndQty.put(produceDate, qtyPreout);
                        }
                    }

                    for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
                        Long id = ocBOrderItem.getId();

                        //数量【也是需要分的剩余数量】
                        BigDecimal itemQty = ocBOrderItem.getQty();

                        SgBStoFreezeOutOmsSaveItemRequest skuItem = new SgBStoFreezeOutOmsSaveItemRequest();
                        skuItem.setSourceItemId(id);
                        skuItem.setPsCSkuId(ocBOrderItem.getPsCSkuId());
                        skuItem.setSkuId(ocBOrderItem.getSkuNumiid());
                        skuItem.setNumiid(ocBOrderItem.getNumIid());
                        skuItem.setPsCProId(ocBOrderItem.getPsCProId());
                        skuItem.setPsCProdimId(ocBOrderItem.getMDim6Id());
                        skuItem.setQty(ocBOrderItem.getQty());
                        skuItem.setLabelingRequirements(ocBOrderItem.getReserveVarchar02());

                        Map<String, BigDecimal> qtyMap = new HashMap<>();

                        for (String produceDate : produceDateAndQty.keySet()) {
                            BigDecimal qtyPreout = produceDateAndQty.get(produceDate);
                            if (qtyPreout.compareTo(BigDecimal.ZERO) > 0) {
                                if (qtyPreout.compareTo(itemQty) >= 0) {
                                    qtyMap.put(produceDate, itemQty);
                                    produceDateAndQty.put(produceDate, produceDateAndQty.get(produceDate).subtract(itemQty));
                                    break;
                                } else {
                                    qtyMap.put(produceDate, qtyPreout);
                                    produceDateAndQty.put(produceDate, BigDecimal.ZERO);
                                    itemQty = itemQty.subtract(qtyPreout);
                                    if (itemQty.compareTo(BigDecimal.ZERO) == 0) {
                                        break;
                                    }
                                }
                            }
                        }
                        skuItem.setQtyMap(qtyMap);
                        skuItems.add(skuItem);
                    }
                }
            }
        } else {
            int initialCapacity = Math.max((int) (orderItemList.size() / .75f) + 1, 16);
            //【条码ID,效期范围】
            Map<Long, Set<String>> skuIdAndExpiryDateRangeMap = new HashMap<>(initialCapacity);
            //【明细ID,效期范围】
            Map<Long, String> itemIdAndExpiryDateRangeMap = new HashMap<>(initialCapacity);
            //解析构造效期范围 和数量
            analysisExpiryDateRange(skuIdAndExpiryDateRangeMap, itemIdAndExpiryDateRangeMap, orderItemList);
            for (OcBOrderItem ocBOrderItem : orderItemList) {
                String expiryDateRange = itemIdAndExpiryDateRangeMap.get(ocBOrderItem.getId());
                SgBStoFreezeOutOmsSaveItemRequest skuItem = new SgBStoFreezeOutOmsSaveItemRequest();
                skuItem.setSourceItemId(ocBOrderItem.getId());
                skuItem.setPsCSkuId(ocBOrderItem.getPsCSkuId());
                skuItem.setSkuId(ocBOrderItem.getSkuNumiid());
                skuItem.setNumiid(ocBOrderItem.getNumIid());
                skuItem.setPsCProId(ocBOrderItem.getPsCProId());
                skuItem.setPsCProdimId(ocBOrderItem.getMDim6Id());
                skuItem.setQty(ocBOrderItem.getQty());
                skuItem.setLabelingRequirements(ocBOrderItem.getReserveVarchar02());
                if (!"UNLIMITED".equals(expiryDateRange)) {
                    String[] split = expiryDateRange.split("-");
                    skuItem.setBeginProduceDate(split[0]);
                    skuItem.setEndProduceDate(split[1]);
                }
                skuItems.add(skuItem);
            }
        }
        stoFreezeOutOmsSaveRequest.setSkuItems(skuItems);
        return stoFreezeOutOmsSaveRequest;
    }

    private SgFindSourceStrategyC2SRequest buildSgRequest(OcBOrder order,
                                                          List<OcBOrderItem> orderItemList,
                                                          OcBOrderToBManualSourcingRequest request, Map<Long, CpCShopProfileExt> cpCShopProfileMap) {
        SgFindSourceStrategyC2SRequest sgFindSourceStrategyC2SRequest = new SgFindSourceStrategyC2SRequest();
        sgFindSourceStrategyC2SRequest.setSourceBillId(order.getId());
        sgFindSourceStrategyC2SRequest.setSourceBillNo(order.getBillNo());
        sgFindSourceStrategyC2SRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_RETAIL);
        sgFindSourceStrategyC2SRequest.setBillDate(order.getOrderDate());
        sgFindSourceStrategyC2SRequest.setWarehouseEcode(request.getCpCPhyWarehouseCode());
        sgFindSourceStrategyC2SRequest.setSplitType(StrategyConstants.ORDER_SPLIT_TYPE_NO);
        sgFindSourceStrategyC2SRequest.setShopId(order.getCpCShopId());
        sgFindSourceStrategyC2SRequest.setShopTitle(order.getCpCShopTitle());
        sgFindSourceStrategyC2SRequest.setProvinceId(order.getCpCRegionProvinceId());
        sgFindSourceStrategyC2SRequest.setCityId(order.getCpCRegionCityId());
        sgFindSourceStrategyC2SRequest.setAreaId(order.getCpCRegionAreaId());
        sgFindSourceStrategyC2SRequest.setTid(order.getSourceCode());
        sgFindSourceStrategyC2SRequest.setIsTobOrder(OmsBusinessTypeUtil.isToBOrder(order));
        sgFindSourceStrategyC2SRequest.setBusinessTypeCode(order.getBusinessTypeCode());
        List<SkuItemC2S> skuItems = buildSgSkuItemC2S(orderItemList, cpCShopProfileMap);
        sgFindSourceStrategyC2SRequest.setSkuItems(skuItems);
        return sgFindSourceStrategyC2SRequest;
    }

    private List<SkuItemC2S> buildSgSkuItemC2S(List<OcBOrderItem> orderItemList, Map<Long, CpCShopProfileExt> cpCShopProfileMap) {
        int initialCapacity = Math.max((int) (orderItemList.size() / .75f) + 1, 16);

        //【条码ID,效期范围】
        Map<Long, Set<String>> skuIdAndExpiryDateRangeMap = new HashMap<>(initialCapacity);

        //【明细ID,效期范围】
        Map<Long, String> itemIdAndExpiryDateRangeMap = new HashMap<>(initialCapacity);

        //解析构造效期范围 和数量
        analysisExpiryDateRange(skuIdAndExpiryDateRangeMap, itemIdAndExpiryDateRangeMap, orderItemList);

        List<SkuItemC2S> skuItemC2SList = new ArrayList<>();

        for (OcBOrderItem ocBOrderItem : orderItemList) {
            String expiryDateRange = itemIdAndExpiryDateRangeMap.get(ocBOrderItem.getId());
            SkuItemC2S skuItemC2S = new SkuItemC2S();
            if (cpCShopProfileMap != null && cpCShopProfileMap.get(ocBOrderItem.getId()) != null) {
                CpCShopProfileExt cpCShopProfileExt = cpCShopProfileMap.get(ocBOrderItem.getId());
                CpCShopProfile cpCShopProfile = cpCShopProfileExt.getCpCShopProfile();
                skuItemC2S.setSalesDepartmentCode(cpCShopProfile.getSalesDepartmentCode());
                skuItemC2S.setSalesGroupCode(cpCShopProfile.getSalesGroupCode());
                skuItemC2S.setCategoryCode(cpCShopProfileExt.getCategoryCode());
            }
            skuItemC2S.setSourceItemId(ocBOrderItem.getId());
            skuItemC2S.setPsCSkuId(ocBOrderItem.getPsCSkuId());
            skuItemC2S.setQty(ocBOrderItem.getQty());
            if (ocBOrderItem.getQty() != null && ocBOrderItem.getStandardWeight() != null) {
                skuItemC2S.setItemTotWeight(ocBOrderItem.getStandardWeight().multiply(ocBOrderItem.getQty()));
            }
            skuItemC2S.setProType(ocBOrderItem.getProType().toString());
            skuItemC2S.setSkuId(ocBOrderItem.getSkuNumiid());
            skuItemC2S.setNumiid(ocBOrderItem.getNumIid());
            skuItemC2S.setPsCProId(ocBOrderItem.getPsCProId());
            skuItemC2S.setPasteLabel(ocBOrderItem.getReserveVarchar02());
            skuItemC2S.setPsCSkuEcode(ocBOrderItem.getPsCSkuEcode());
            skuItemC2S.setPsCProEcode(ocBOrderItem.getPsCProEcode());

            if (!"UNLIMITED".equals(expiryDateRange)) {
                String[] split = expiryDateRange.split("-");

                skuItemC2S.setBeginProduceDate(split[0]);
                skuItemC2S.setEndProduceDate(split[1]);
            }

            skuItemC2SList.add(skuItemC2S);
        }
        return skuItemC2SList;
    }
}
