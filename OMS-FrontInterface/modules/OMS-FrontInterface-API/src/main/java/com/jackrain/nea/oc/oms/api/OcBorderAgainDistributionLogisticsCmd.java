package com.jackrain.nea.oc.oms.api;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.sys.Command;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;

/**
 * description:重新分物流
 * @Author:  liuwenjin
 * @Date 2022/9/23 16:27
 */
public interface OcBorderAgainDistributionLogisticsCmd {


    /**
     * description:重新分物流
     * @Author:  liuwenjin
     * @Date 2022/9/23 16:32
     */
    ValueHolder againDistributionLogistics(JSONObject object, User operateUser) throws NDSException;
}
