package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.OcBLowTemperatureOccupyTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * @ClassName OcBLowTemperatureOccupyTaskMapper
 * @Description 低温寻源
 * <AUTHOR>
 * @Date 2024/5/9 18:41
 * @Version 1.0
 */
@Mapper
public interface OcBLowTemperatureOccupyTaskMapper extends ExtentionMapper<OcBLowTemperatureOccupyTask> {


    @Update("<script> "
            + "UPDATE oc_b_low_temperature_occupy_task SET isactive = 'N' ,modifieddate = now() where order_id in "
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    void updateTaskStatus(@Param("ids") List<Long> orderIds);

    @SelectProvider(type = OcBLowTemperatureOccupyTaskMapper.OcBLowTemperatureOccupyTaskMapperProvider.class, method = "selectLowTemperatureOccupyTaskWithPriority")
    List<Long> selectLowTemperatureOccupyTaskWithPriority(@Param("limit") int limit,
                                                          @Param("taskTableName") String taskTableName);

    @SelectProvider(type = OcBLowTemperatureOccupyTaskMapper.OcBLowTemperatureOccupyTaskMapperProvider.class, method = "selectLowTemperatureOccupyTaskWithOutPriority")
    List<Long> selectLowTemperatureOccupyTaskWithOutPriority(@Param("limit") int limit,
                                                             @Param("taskTableName") String taskTableName);


    class OcBLowTemperatureOccupyTaskMapperProvider {
        public String selectLowTemperatureOccupyTaskWithPriority(@Param("limit") int limit,
                                                                 @Param("taskTableName") String taskTableName) {
            StringBuffer sql = new StringBuffer();
            sql.append("select order_id from ")
                    .append(taskTableName)
                    .append(" where isactive = 'Y' ")
                    .append(" and next_time <= now() and item_num >= 2 ");
            sql.append(" order by creationdate asc ")
                    .append(" limit ").append(limit);
            return sql.toString();
        }


        public String selectLowTemperatureOccupyTaskWithOutPriority(@Param("limit") int limit,
                                                                    @Param("taskTableName") String taskTableName) {
            StringBuffer sql = new StringBuffer();
            sql.append("select order_id from ")
                    .append(taskTableName)
                    .append(" where isactive = 'Y' ")
                    .append(" and next_time <= now() ");
            sql.append(" order by creationdate asc ")
                    .append(" limit ").append(limit);
            return sql.toString();
        }
    }
}
