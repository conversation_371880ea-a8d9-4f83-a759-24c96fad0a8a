package com.jackrain.nea.oc.oms.vo;

import lombok.Data;

import java.util.Map;

/**
 * @program: r3-st
 * @description: 仓库物流设置导入实体
 * @author: caomalai
 * @create: 2022-07-01 15:19
 **/
@Data
public class StCExpiryDateImpVo {
    public static final String cellStr = "cell_";
    public static final String rowStr = "row_";
    //类型名称
    private String expiryTypeName;
    //平台店铺
    private String platformShopName;
    //开始时间
    private String startTime;
    //结束时间
    private String endTime;
    //指定维度
    private String appointDimensionName;
    //指定内容
    private String appointContent;
    //指定类型
    private String appointTypeName;
    //开始生产日期/天数
    private String startDateDay;
    //结束生产日期/天数
    private String endDateDay;
    //订单标签
    private String orderLabel;

    private String cheapestExpress;

    private Integer expiryType;
    private Integer appointDimension;
    private Integer appointType;
    private Long platformShopId;

    //行号
    private int rowNum;

    private String desc;

    public static StCExpiryDateImpVo importCreate(int index, Map<String, String> columnMap) {
        StCExpiryDateImpVo impVo = new StCExpiryDateImpVo();
        try {
            //类型名称
            impVo.setExpiryTypeName(columnMap.get(rowStr + index + cellStr + 0));
            //平台店铺
            impVo.setPlatformShopName(columnMap.get(rowStr + index + cellStr + 1));
            //开始时间
            impVo.setStartTime(columnMap.get(rowStr + index + cellStr + 2));
            //结束时间
            impVo.setEndTime(columnMap.get(rowStr + index + cellStr + 3));
            //指定维度
            impVo.setAppointDimensionName(columnMap.get(rowStr + index + cellStr + 4));
            //指定内容
            impVo.setAppointContent(columnMap.get(rowStr + index + cellStr + 5));
            //指定类型
            impVo.setAppointTypeName(columnMap.get(rowStr + index + cellStr + 6));
            //开始生产日期/天数
            impVo.setStartDateDay(columnMap.get(rowStr + index + cellStr + 7));
            //结束生产日期/天数
            impVo.setEndDateDay(columnMap.get(rowStr + index + cellStr + 8));
            //订单标签
            impVo.setOrderLabel(columnMap.get(rowStr + index + cellStr + 9));
            // 优先最便宜快递
            impVo.setCheapestExpress(columnMap.get(rowStr + index + cellStr + 10));
        } catch (Exception e) {

        }
        impVo.setRowNum(index + 1);
        return impVo;
    }
}
