package com.jackrain.nea.oc.oms.sap;

import com.jackrain.nea.exception.NDSException;

import java.util.List;
import java.util.Vector;
import java.util.function.Consumer;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2020/3/22
 */
public class Oms2SapDBMetaCache {

    /**
     * node names
     */
    private static final List<String> nodeCache = new Vector<>();

    private static final Consumer<String> stringCsm = o -> {
        if (o == null || o.trim().length() == 0) {
            throw new NDSException("Empty Value");
        }
    };


}
