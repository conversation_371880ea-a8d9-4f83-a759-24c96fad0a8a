//package com.jackrain.nea.oc.oms.task.settlement;
//
//import com.google.common.base.Throwables;
//import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
//import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
//import com.jackrain.nea.oc.oms.services.ReturnOrderAuditService;
//import com.jackrain.nea.utility.LogUtil;
//import com.jackrain.nea.web.face.User;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Component;
//
//import java.util.List;
//
///**
// * <AUTHOR>
// * @date 2021/1/13 16:38
// * @desc
// */
//@Slf4j
//@Component
//@Deprecated
//public class Return2SettlementTask extends {
//    @Override
//    protected String threadPoolName() {
//        return "OC_B_RETURN_ORDER_SETTLEMENT_POOL_%d";
//    }
//
//    @Override
//    protected String taskTableName() {
//        return "OC_B_RETURN_ORDER";
//    }
//
//    @Value("${lts.returnorder.rang:1000}")
//    private Integer pullNum;
//
//    @Autowired
//    private OcBReturnOrderMapper mapper;
//
//    @Autowired
//    private ReturnOrderAuditService returnOrderAuditService;
//
//
//    @Override
//    protected void executeRunnable(String nodeName, String tableName, User operateUser) {
//        if (log.isDebugEnabled()) {
//            log.debug(LogUtil.format("Start Return2SettlementTask execute sendBatchSettlementLog",  "Return2SettlementTask"));
//        }
//        try {
//            List<OcBReturnOrder> returnOrders = mapper.selectOrderListByIdsAndToSettleStatus(
//                    nodeName, tableName, pullNum);
//            if (CollectionUtils.isEmpty(returnOrders)) {
//                return;
//            }
//            returnOrderAuditService.returnSync2Settlement(returnOrders);
//        } catch (Exception e) {
//            log.error(LogUtil.format("Return2SettlementTask.synOrderSendGoodsLog.error:,异常：{}", "Return2SettlementTask"), Throwables.getStackTraceAsString(e));
//        }
//    }
//}
