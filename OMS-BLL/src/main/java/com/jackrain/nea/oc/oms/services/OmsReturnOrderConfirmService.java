package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.burgeon.r3.sg.store.model.request.in.SgBStoInNoticesBillSaveRequest;
import com.burgeon.r3.sg.store.model.result.in.SgBStoInNoticesBillSaveResult;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.es.ES4ReturnOrder;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderRefundMapper;
import com.jackrain.nea.oc.oms.model.constant.OcOmsReturnOrderConstant;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.oc.oms.services.returnin.OcRefundInGenerateStorageBillService;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.ValueHolderV14Utils;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR> zhuxing
 * @Date : 2022-08-15 16:58
 * @Description : 退换货单 -- 确认服务
 **/
@Slf4j
@Component
public class OmsReturnOrderConfirmService {

    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;

    @Autowired
    private OcBReturnOrderRefundMapper ocBReturnOrderRefundMapper;

    @Autowired
    private OcRefundInGenerateStorageBillService ocRefundInGenerateStorageBillService;

    @Autowired
    private SgRpcService sgRpcService;

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private ReturnOrderAuditService returnOrderAuditService;

    @NacosValue(value = "${oms.returnOrder.auto.createdInNotices:true}", autoRefreshed = true)
    private boolean isAutoCreatedInNotices;

    /**
     * 退换货单 - 二次确认
     * @param param
     * @return
     */
    public ValueHolderV14 returnOrderSecondaryVerify(JSONObject param) {
        ValueHolderV14 v14 = ValueHolderV14Utils.getSuccessValueHolder("校验成功！");
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("退换货单二次验证确认！request={}",
                    "OmsReturnOrderConfirmService"), JSONObject.toJSONString(param));
        }
        JSONArray ids = param.getJSONArray("ids");
        if (ids == null || ids.size() == 0) {
            return ValueHolderV14Utils.getFailValueHolder("请选择记录！");
        }
        List<Long> idList = JSON.parseArray(ids.toJSONString(), Long.class);

        List<OcBReturnOrder> ocBReturnOrderList = ocBReturnOrderMapper.selectBatchIds(idList);
        if(CollectionUtils.isEmpty(ocBReturnOrderList)){
            return ValueHolderV14Utils.getFailValueHolder("查询当前记录为空！");
        }
        List<OcBReturnOrder> returnOrders = ocBReturnOrderList.stream().filter(x -> StringUtils.isBlank(x.getLogisticsCode())).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(returnOrders)){
            List<Long> returnOrderIds = returnOrders.stream().map(OcBReturnOrder::getId).collect(Collectors.toList());
            JSONObject returnObj = new JSONObject();
            returnObj.put("ID",returnOrderIds);
            v14 = ValueHolderV14Utils.custom(ResultCode.FAIL,"存在退换货单没有物流单号，是否确认！",returnObj);
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("退换货单二次验证确认！result={}",
                    "OmsReturnOrderConfirmService"), JSONObject.toJSONString(v14));
        }
        return v14;
    }


        /**
         * 退换货单 - 确认服务
         * @param param
         * @param user
         * @return
         */
    public ValueHolderV14 returnOrderConfirm(JSONObject param, User user) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("退换货单确认！request={}",
                    "OmsReturnOrderConfirmService"), JSONObject.toJSONString(param));
        }
        JSONArray ids = param.getJSONArray("ids");
        if (ids == null || ids.size() == 0) {
            return ValueHolderV14Utils.getFailValueHolder("请选择记录！");
        }
        List<Long> longs = JSON.parseArray(ids.toJSONString(), Long.class);
        //分批进行修改
        int success = 0;
        int fail = 0;
        JSONArray listArray = new JSONArray();
        for (Long id : longs) {
            String lockRedisKey = BllRedisKeyResources.buildLockReturnOrderKey(id);
            RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
            JSONObject retJson;
            try {
                if (!redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                    throw new NDSException("该记录正在操作中，请稍后再试！");
                }
                OcBReturnOrder returnOrder = ocBReturnOrderMapper.selectById(id);
                if(returnOrder == null){
                    throw new NDSException("当前记录不存在！");
                }
                //确认状态
                String confirmStatus = returnOrder.getConfirmStatus();
                Integer returnStatus = returnOrder.getReturnStatus();
                Long phyWarehouseInId = returnOrder.getCpCPhyWarehouseInId();
                if(OcOmsReturnOrderConstant.CONFIRM_STATUS_Y.equals(confirmStatus) || ReturnStatusEnum.COMPLETION.getVal().equals(returnStatus)){
                    throw new NDSException("当前记录已取消或已确认，不允许重复确认！");
                }
                if(phyWarehouseInId == null){
                    throw new NDSException("当前单据没有选择入库实体仓库，不允许确认！");
                }
                List<OcBReturnOrderRefund> returnOrderRefunds = ocBReturnOrderRefundMapper.queryRtnOrderRefundByoId(id,"Y");
                if(CollectionUtils.isEmpty(returnOrderRefunds)){
                    throw new NDSException("当前记录明细为空！");
                }
                createInNotices(returnOrder,returnOrderRefunds,user);
                success++;
                continue;
            } catch (Exception ex) {
                retJson = new JSONObject();
                retJson.put("message", ex.getMessage());
                retJson.put("code", -1);
                retJson.put("objid", id);
                listArray.add(retJson);
                fail++;
                log.error(LogUtil.format("退换货单确认失败！ID={}，errorMsg={}",
                        "OmsReturnOrderConfirmService"), id,Throwables.getStackTraceAsString(ex));
            } finally {
                redisLock.unlock();
            }
        }
        if(fail <= 0){
            return ValueHolderV14Utils.getSuccessValueHolder("确认成功！成功:" + success+"条，失败:" + fail);
        }else {
            return ValueHolderV14Utils.custom(ResultCode.FAIL,"确认失败！成功:" + success+"条，失败:" + fail,listArray);
        }
    }

    /**
     * 退换货单确认服务
     * @param returnOrder
     * @param returnOrderRefunds
     * @param user
     * @return
     */
    public ValueHolderV14 autoConfirmReturnOrder(OcBReturnOrder returnOrder,List<OcBReturnOrderRefund> returnOrderRefunds,User user){
        if(!isAutoCreatedInNotices){
            return ValueHolderV14Utils.getFailValueHolder("退换货单确认！业务参数配置不自动确认生成入库通知单！");
        }
        //确认状态
        String confirmStatus = returnOrder.getConfirmStatus();
        Integer returnStatus = returnOrder.getReturnStatus();
        if(OcOmsReturnOrderConstant.CONFIRM_STATUS_Y.equals(confirmStatus) || ReturnStatusEnum.COMPLETION.getVal().equals(returnStatus)){
            return ValueHolderV14Utils.getFailValueHolder("当前记录已取消或已确认，不允许重复确认！");
        }
        Long phyWarehouseInId = returnOrder.getCpCPhyWarehouseInId();
        if (phyWarehouseInId == null) {
            return ValueHolderV14Utils.getFailValueHolder("当前单据没有选择入库实体仓库，不允许确认！");
        }
        //如果是SAP退单，没有物流单号也要生成入库通知单
        List<String> typeList = new ArrayList<>();
        typeList.add("RYTH11");
        typeList.add("RYTH12");
        //20230809阿姐让加上RYTH16类型允许没有物流单号也自动确认
        typeList.add("RYTH16");
        if (!typeList.contains(returnOrder.getBusinessTypeCode())) {
            String logisticsCode = returnOrder.getLogisticsCode();
            if (StringUtils.isBlank(logisticsCode)) {
                return ValueHolderV14Utils.getFailValueHolder("当前单据没有物流单号，不允许确认！");
            }
        }
        //生成入库通知单
        createInNotices(returnOrder, returnOrderRefunds, user);
        return ValueHolderV14Utils.getSuccessValueHolder("退换货单确认成功！");
    }

    /**
     * 生成入库通知单
     * @param returnOrder
     * @param returnOrderRefunds
     * @param user
     */
    public void createInNotices(OcBReturnOrder returnOrder,List<OcBReturnOrderRefund> returnOrderRefunds,User user){
        //如果已存在入库通知单号，则不再重新生成入库通知单
        if(StringUtils.isNotBlank(returnOrder.getStoInNoticesNo())){
            return;
        }
        //判断来源单据来源类型
        String sourceBillType = "";
        Integer platform = returnOrder.getPlatform();
        if (OcOmsReturnOrderConstant.PLATFORM_SAP.equals(platform) || OcOmsReturnOrderConstant.PLATFORM_DMS.equals(platform)) {
            sourceBillType = "B2BRK";
        } else {
            sourceBillType = "THRK";
        }
        //根据实体仓档案判断是否推WMS
        CpCPhyWarehouse warehouse = cpRpcService.queryByWarehouseId(returnOrder.getCpCPhyWarehouseInId());
        if (warehouse == null) {
            throw new NDSException("创建入库通知单失败！入库实体仓信息存在问题！");
        }
        //生成退换货单后，同时生成入库通知单
        SgBStoInNoticesBillSaveRequest stoInNoticesBillSaveRequest = null;
        if (warehouse.getWmsControlWarehouse() == null || YesNoEnum.Y.getVal().equals(warehouse.getWmsControlWarehouse())) {
            stoInNoticesBillSaveRequest = ocRefundInGenerateStorageBillService.encapsulationStoInNotices(returnOrder, returnOrderRefunds, true, sourceBillType);
        } else {
            stoInNoticesBillSaveRequest = ocRefundInGenerateStorageBillService.encapsulationStoInNotices(returnOrder, returnOrderRefunds, false, sourceBillType);
        }
        ValueHolderV14<SgBStoInNoticesBillSaveResult> saveResultV14 = sgRpcService.createSgBStoInNotice(stoInNoticesBillSaveRequest, user);
        if (!saveResultV14.isOK()) {
            throw new NDSException("创建入库通知单失败！" + saveResultV14.getMessage());
        }
        //回写退换货单入库通知单信息
        String stoInNoticesNo = saveResultV14.getData().getBillNo();
        Long stoInNoticesId = saveResultV14.getData().getId();

        //更新入库单通知单单号
        OcBReturnOrder updateReturnOrder = new OcBReturnOrder();
        updateReturnOrder.setId(returnOrder.getId());
        updateReturnOrder.setStoInNoticesNo(stoInNoticesNo);
        updateReturnOrder.setStoInNoticesId(stoInNoticesId);
        updateReturnOrder.setConfirmStatus(OcOmsReturnOrderConstant.CONFIRM_STATUS_Y);
        updateReturnOrder.setConfirmId(Long.valueOf(user.getId()));
        updateReturnOrder.setConfirmName(user.getName());
        updateReturnOrder.setConfirmDate(new Date());
        updateReturnOrder.setWmsCancelStatus(0);
        ocBReturnOrderMapper.updateById(updateReturnOrder);
    }


    /**
     * 退换货单自动确认
     * @return
     */
    public ValueHolderV14 autoExecuteConfirm(){
        List<Long> idList = ES4ReturnOrder.findIdByConfirmStatus();
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("退换货单自动确认！idList={}",
                    "OmsReturnOrderConfirmService"), JSONObject.toJSONString(idList));
        }
        if(CollectionUtils.isEmpty(idList)){
            return ValueHolderV14Utils.getFailValueHolder("没有可以执行的数据！");
        }
        for(Long id:idList){
            String lockRedisKey = BllRedisKeyResources.buildLockReturnOrderKey(id);
            RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
            try{
                if (!redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                    throw new NDSException("当前单据正在操作中！");
                }

                OcBReturnOrder returnOrder = ocBReturnOrderMapper.selectById(id);
                if(returnOrder == null){
                    throw new NDSException("当前记录不存在！");
                }
                List<OcBReturnOrderRefund> returnOrderRefunds = ocBReturnOrderRefundMapper.queryRtnOrderRefundByoId(id,"Y");
                if(CollectionUtils.isEmpty(returnOrderRefunds)){
                    throw new NDSException("当前记录明细为空！");
                }
                ValueHolderV14 v14 = autoConfirmReturnOrder(returnOrder,returnOrderRefunds, SystemUserResource.getRootUser());
                if(!v14.isOK()){
                    throw new NDSException(v14.getMessage());
                }
                //记录成功日志
                returnOrderAuditService.recordReturnOrderLog(id, "退货单确认", "退换货单确认完成", true, SystemUserResource.getRootUser());
            }catch (Exception e){
                log.info(LogUtil.format("退换货单确认失败！ID={}，errorMsg={}",
                        "OmsReturnOrderConfirmService"), id, Throwables.getStackTraceAsString(e));
                //更新修改时间，防止定时任务一直拉到同一批数据
                OcBReturnOrder updateModel = new OcBReturnOrder();
                updateModel.setId(id);
                updateModel.setModifieddate(new Date());
                ocBReturnOrderMapper.updateById(updateModel);
                //记录失败日志
                returnOrderAuditService.recordReturnOrderLog(id, "退货单确认", "退换货单确认失败:"+e.getMessage(), true, SystemUserResource.getRootUser());
            }finally {
                redisLock.unlock();
            }
        }
        return ValueHolderV14Utils.getSuccessValueHolder("执行成功！");
    }
}
