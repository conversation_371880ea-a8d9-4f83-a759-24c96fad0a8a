package com.jackrain.nea.oc.oms.task.tobeconfirm;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.config.PropertiesConf;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.resources.OrderOccupyStatus;
import com.jackrain.nea.oc.oms.process.tobeconfirm.ToBeConfirmedOrderProcess;
import com.jackrain.nea.oc.oms.services.OmsOrderItemService;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.RuntimeCompute;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 占单服务定时任务
 *
 * @author: 易邵峰
 * @since: 2019-03-23
 * create at : 2019-03-23 12:19
 */
@Slf4j
@Component
public class TobeConfirmedOrderTask extends BaseR3Task implements IR3Task {
    @Autowired
    private OmsOrderService orderService;

    @Autowired
    private OmsOrderItemService omsOrderItemService;

    @Autowired
    private ToBeConfirmedOrderProcess toBeConfirmedOrderProcess;

    @Override
    @XxlJob("TobeConfirmedOrderTask")
    public RunTaskResult execute(JSONObject params) {

        RunTaskResult result = new RunTaskResult();
        try {
            RuntimeCompute runtimeCompute = new RuntimeCompute();
            runtimeCompute.startRuntime();
            PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
            Integer pageSize = config.getProperty("lts.TobeConfirmedOrderTask.range", 200);
            List<Long> orderIdList = ES4Order.findIdsByOrderStatusAndOccupyStatus(0, pageSize);
            List<OcBOrderRelation> orderRelationList = new ArrayList<>();
            for (Long orderId : orderIdList) {
                log.debug(LogUtil.format("启动补偿任务占单",  "启动补偿任务占单", orderId));
                OcBOrderRelation orderRelation = this.orderService.selectOmsOrderInfoOccupy(orderId);
                if (orderRelation == null) {
                    String errorMessage = Resources.getMessage("TobeConfirmedOrderTask Order NotExist!OrderId="
                            + orderId);
                } else {
                    //排除促销hold
                    orderRelationList.add(orderRelation);
                }
            }
            threadOrderProcessor.startMultiThreadExecute(this.toBeConfirmedOrderProcess, orderRelationList);
            double usedTime = runtimeCompute.endRuntime();
            log.debug(LogUtil.format("占单补偿任务执行成功!UsedTime={}",  "占单补偿任务执行成功", usedTime));
            result.setSuccess(true);
            result.setMessage("占单补偿任务执行成功!UsedTime=" + usedTime);
        } catch (Exception ex) {
            log.error(LogUtil.format("TobeConfirmedOrderTask.Execute Error={}", "占单补偿任务执行成功"), Throwables.getStackTraceAsString(ex));
            result.setSuccess(false);
            result.setMessage(ex.getMessage());
        }
        return result;
    }
}
