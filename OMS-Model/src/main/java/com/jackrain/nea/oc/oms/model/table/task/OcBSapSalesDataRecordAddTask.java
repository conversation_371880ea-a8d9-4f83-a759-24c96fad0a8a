package com.jackrain.nea.oc.oms.model.table.task;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> wang<PERSON>yu
 * @since : 2022/9/20
 * description :
 */
@Data
@TableName(value = "oc_b_sap_sales_data_record_add_task")
public class OcBSapSalesDataRecordAddTask extends BaseModel implements Serializable {
    @Field(type = FieldType.Long)
    @JSONField(name = "ID")
    private Long id;

    @ApiModelProperty(value = "单据类型:0订单 1退单")
    @Field(type = FieldType.Integer)
    @JSONField(name = "BILL_TYPE")
    private Integer billType;

    @Field(type = FieldType.Long)
    @JSONField(name = "ORDER_ID")
    private Long orderId;

    @Field(type = FieldType.Integer)
    @JSONField(name = "STATUS")
    private Integer status;

    @ApiModelProperty(value = "重试次数")
    @Field(type = FieldType.Integer)
    @JSONField(name = "RETRY_NUMBER")
    private Integer retryNumber;

    @ApiModelProperty(value = "下次执行时间")
    @Field(type = FieldType.Long)
    @JSONField(name = "NEXT_TIME")
    private Date nextTime;

    @Field(type = FieldType.Keyword)
    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @Field(type = FieldType.Keyword)
    @JSONField(name = "MODIFIERENAME")
    private String modifierename;
}
