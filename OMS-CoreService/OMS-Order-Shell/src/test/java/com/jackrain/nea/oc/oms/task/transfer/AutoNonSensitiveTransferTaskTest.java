package com.jackrain.nea.oc.oms.task.transfer;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.OmsOrderProcessApplication;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import static org.junit.Assert.*;

/**
 * Description:
 *
 * <AUTHOR> sunies
 * @since : 2020-12-08
 * create at : 2020-12-08 10:59
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = OmsOrderProcessApplication.class)
@Slf4j
public class AutoNonSensitiveTransferTaskTest {

    @Resource
    private AutoNonSensitiveTransferTask autoNonSensitiveTransferTask;
    @Test
    public void execute() {
        autoNonSensitiveTransferTask.execute(new JSONObject());
    }
}