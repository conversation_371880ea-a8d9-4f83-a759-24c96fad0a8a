package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.IpBWhInnerOperate;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
public interface IpBWhInnerOperateMapper extends ExtentionMapper<IpBWhInnerOperate> {

    @Select("<script> "
            + "SELECT * FROM ip_b_wh_inner_operate WHERE trans_status = 0 and isactive = 'Y' and express_code in "
            + " <foreach item='item' index='index' collection='expressCode' "
            + "open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<IpBWhInnerOperate> selectIpBWhInnerOperateByExpressCode(@Param("expressCode") List<String> expressCode);


    @Update("UPDATE ip_b_wh_inner_operate SET trans_status = #{transStatus},sysremark = #{sysremark} WHERE express_code = #{expressCode}")
    int updateIpBWhInnerOperate(@Param("expressCode") String expressCode,
                                @Param("transStatus") Integer transStatus,
                                @Param("sysremark") String sysremark);


    @Select("SELECT * FROM ip_b_wh_inner_operate WHERE trans_status = 0 and isactive = 'Y' and express_code = #{expressCode}")
    IpBWhInnerOperate selectIpBWhInnerOperate(String expressCode);


    @Select("SELECT * FROM ip_b_wh_inner_operate WHERE isactive = 'Y' and express_code = #{expressCode}")
    IpBWhInnerOperate selectIpBWhInnerOperateByCode(String expressCode);

}