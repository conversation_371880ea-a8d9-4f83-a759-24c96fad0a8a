package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.util.ValueHolder;

import java.util.List;

/**
 * className: OcBOrderDrpCancelCmd
 * description: drp退款取消发货单
 *
 * <AUTHOR>
 * create: 2021-08-23
 * @since JDK 1.8
 */
public interface OcBOrderDrpCancelCmd {

    /**
     * drp退款取消发货单
     * @param billNos oms单据编号
     * @return ValueHolder
     */
    ValueHolder drpBatchCancel(List<String> billNos);
}
