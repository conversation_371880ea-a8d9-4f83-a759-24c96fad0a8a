package com.jackrain.nea.oc.services.qimen;

import com.jackrain.nea.oc.oms.api.OmsMonomerBStoInNoticeCallBackCmd;
import com.jackrain.nea.oc.oms.services.qimen.OmsWmsBackRefundInTask;
import com.jackrain.nea.oc.oms.services.qimen.OmsWmsBackRefundPackageTask;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;

/**
 * Description: 奇门回传接口
 *
 * @Author: guo.kw
 * @Since: 2022/7/18
 * create at: 2022/7/18 15:41
 */
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
@Slf4j
@Component
public class OmsMonomerBStoInNoticeCallBackImpl implements OmsMonomerBStoInNoticeCallBackCmd {

    /**
     * 退换货回传
     * @param method
     * @param msg
     * @return
     */
    @Override
    public ValueHolderV14 omsMonomerBRefundInTasCallBack(String method, String msg) {
        OmsWmsBackRefundInTask bean = ApplicationContextHandle.getBean(OmsWmsBackRefundInTask.class);
        return bean.apiProcess(msg);
    }

    /**
     * 退货包裹回传
     * @param method
     * @param msg
     * @return
     */
    @Override
    public ValueHolderV14 omsMonomerBRefundPackageCallBack(String method, String msg) {
        OmsWmsBackRefundPackageTask bean = ApplicationContextHandle.getBean(OmsWmsBackRefundPackageTask.class);
        return bean.apiProcess(msg);
    }
}
