package com.jackrain.nea.oc.oms.model.table.task;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 自动解挂单
 */
@TableName(value = "oc_b_auto_release_hang_task")
@Data
public class OcBAutoReleaseHangTaskTable extends BaseModel implements Serializable {

    @Field(type = FieldType.Long)
    @JSONField(name = "ID")
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @J<PERSON>NField(name = "ORDER_ID")
    private Long orderId;

    @J<PERSON>NField(name = "STATUS")
    private Integer status;

    /**
     * 自动解挂时间
     */
    @J<PERSON>NField(name = "AUTO_RELEASE_TIME")
    private Date autoReleaseTime;

}