//package com.jackrain.nea.oc.oms.task.orderwms;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import com.google.common.base.Throwables;
//import com.google.common.collect.Lists;
//import com.jackrain.nea.config.PropertiesConf;
//import com.jackrain.nea.oc.oms.config.AutoSplitOrderMqConfig;
//import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
//import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
//import com.jackrain.nea.oc.oms.mapper.task.DrdsHintDatas;
//import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
//import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
//import com.jackrain.nea.oc.oms.model.enums.OperateType;
//import com.jackrain.nea.oc.oms.model.table.OcBOrder;
//import com.jackrain.nea.oc.oms.sap.Oms2SapDBMetaCache;
//import com.jackrain.nea.oc.oms.services.OmsOrderService;
//import com.jackrain.nea.oc.oms.services.task.OmsWmsTaskService;
//import com.jackrain.nea.oc.oms.task.BaseR3Task;
//import com.jackrain.nea.r3.mq.exception.SendMqException;
//import com.jackrain.nea.r3.mq.util.R3MqSendHelper;
//import com.jackrain.nea.task.IR3Task;
//import com.jackrain.nea.task.RunTaskResult;
//import com.jackrain.nea.util.ApplicationContextHandle;
//import com.jackrain.nea.util.OMSThreadPoolFactory;
//import com.jackrain.nea.utility.LogUtil;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Map;
//import java.util.Set;
//import java.util.concurrent.Callable;
//import java.util.concurrent.ExecutorService;
//import java.util.concurrent.Future;
//import java.util.stream.Collectors;
//
//
///***
// * 订单传wms服务
// */
//@Slf4j
//@Component
//public class AutoOrderSplitWmsTask extends BaseR3Task implements IR3Task {
//
//
//    /**
//     * 基本线程池常量定义
//     */
//    int corePoolSize = 16;
//    int maxPoolSize = 20;
//    long keepAliveThreadTime = 60000;
//    String threadPoolName = "R3_OMS_SPLIT_BYGOODS_TASK_THREAD_POOL_%d";
//    //  ArrayBlockingQueue blockingQueue = new ArrayBlockingQueue(16);
//    @Autowired
//    private OmsOrderService omsOrderervice;
//    @Autowired
//    private OcBOrderMapper ocBOrderMapper;
//    @Autowired
//    private OcBOrderItemMapper orderItemMapper;
//    @Autowired
//    private DrdsHintDatas drdsHintDatas;
//    @Autowired
//    private OmsWmsTaskService wmsTaskService;
//    @Autowired
//    private R3MqSendHelper sendHelper;
//    @Autowired
//    private AutoSplitOrderMqConfig autoSplitOrderMqConfig;
//
//    @Override
//    public RunTaskResult execute(JSONObject params) {
//        if (log.isDebugEnabled()) {
//            log.debug(LogUtil.format("进入订单传wms split 定时任务",  "进入订单传wmssplit定时任务"));
//        }
//        RunTaskResult result = new RunTaskResult();
//       /* ExecutorService executor = new ThreadPoolExecutor(
//                32,
//                32,
//                0,
//                TimeUnit.SECONDS,
//                blockingQueue,
//                new ThreadFactoryBuilder().setNameFormat(threadPoolName).build());*/
//        ExecutorService executor = OMSThreadPoolFactory.getOrderToWmsSplitByGoodsAttrPool();
//        try {
//            final String taskTableName = "OC_B_WAREHOUSE_SPLIT_TASK";
//            Set<String> nodes = topMap.keySet();
//            if (CollectionUtils.isEmpty(nodes)) {
//                result.setSuccess(false);
//                result.setMessage("请检查环境，node获取不到！！");
//                return result;
//            }
//            List<Future<Boolean>> results = new ArrayList<>();
//
//            for (String nodeName : nodes) {
//                results.add(executor.submit(new CallableOrderSplitByGoodsTaskWithResult(nodeName,
//                        topMap.get(nodeName))));
//            }
//
//            //线程执行结果获取
//            for (Future<Boolean> futureResult : results) {
//                log.debug(LogUtil.format("AutoOrderSplitWmsTask------>线程结果:{}",  "AutoOrderSplitWmsTask"), JSON.toJSONString(futureResult.get()));
//            }
//            result.setSuccess(true);
//        } catch (Exception ex) {
//            log.error(LogUtil.format("AutoOrderSplitWmsTask订单按商品属性任务执行失败！{}",  "订单按商品属性任务执行失败"), Throwables.getStackTraceAsString(ex));
//            result.setSuccess(false);
//            result.setMessage(ex.getMessage());
//        } finally {
//            //   executor.shutdown();
//        }
//        return result;
//    }
//
//    /**
//     * 批量发送 MQ
//     *
//     * @param idsList
//     * @throws SendMqException
//     */
//    public void batchSendSplitOrderList(List<Long> idsList) {
//        if (log.isDebugEnabled()) {
//            log.debug("AutoOrderSplitWmsTask.batchSendSplitOrderList idsList={}",
//                    JSON.toJSONString(idsList));
//        }
//        List<List<Long>> allList = Lists.partition(idsList, 100);
//        if (CollectionUtils.isEmpty(allList)) {
//            return;
//        }
//        for (List<Long> splitList : allList) {
//            try {
//                List<OcBOrder> orders = omsOrderervice.selectOrderListByIdsList(splitList);
//                List<Long> sendMqIds = new ArrayList<>();
//                List<Long> unSendMqIds = new ArrayList<>();
//                for (OcBOrder ocBOrder : orders) {
//                    if (OmsOrderStatus.UNCONFIRMED.toInteger().equals(ocBOrder.getOrderStatus())) {
//                        sendMqIds.add(ocBOrder.getId());
//                    } else {
//                        unSendMqIds.add(ocBOrder.getId());
//                    }
//                }
//                if (CollectionUtils.isNotEmpty(unSendMqIds)) {
//                    wmsTaskService.batchUpdateOcBWarehouseSplitTask(unSendMqIds, 1);
//                }
//
//                if (CollectionUtils.isNotEmpty(sendMqIds)) {
//                    String idsString =
//                            sendMqIds.stream().map(x -> String.valueOf(x)).distinct().collect(Collectors.joining(","));
//                    this.sendMQ(idsString);
//                    wmsTaskService.batchUpdateOcBWarehouseSplitTask(sendMqIds, 1);
//                }
//            } catch (Exception e) {
//                log.error(LogUtil.format("AutoOrderSplitWmsTask.batchSendSplitOrderList,失败订单={},异常信息={}",  "AutoOrderSplitWmsTask"), Throwables.getStackTraceAsString(e));
//            }
//        }
//    }
//
//    /**
//     * 发送MQ消息执行缺货拆单
//     *
//     * @param idsString 订单信息
//     */
//    public void sendMQ(String idsString) throws SendMqException {
//        Long cutime = System.currentTimeMillis() * 1000; // 微秒
//        Long nanoTime = System.nanoTime(); // 纳秒
//        Long micTime = cutime + (nanoTime - nanoTime / 1000000 * 1000000) / 1000;
//        String msgKey = "SPLIT_ORDER_BY_GOODS_TR_BATCH" + "_" + micTime;
//        if (log.isDebugEnabled()) {
//            log.debug(LogUtil.format("发送MQ消息执行缺货拆单,msgKey:{}",  "发送MQ消息执行缺货拆单"), msgKey);
//        }
//        OperateOrderMqInfo orderMqInfo = new OperateOrderMqInfo();
//        orderMqInfo.setOperateType(OperateType.SPLIT_ORDER);
//        orderMqInfo.setOrderIds(idsString);
//        List<OperateOrderMqInfo> mqInfoList = new ArrayList<>();
//        mqInfoList.add(orderMqInfo);
//        Object jsonValue = JSON.toJSONString(mqInfoList);
//        sendHelper.sendMessage(jsonValue, autoSplitOrderMqConfig.getSendSplitOrderByGoodsMqTopic(),
//                autoSplitOrderMqConfig.getSendSplitOrderByGoodsTag(),
//                msgKey);
//    }
//
//    /**
//     * 开启线程类
//     */
//    class CallableOrderSplitByGoodsTaskWithResult implements Callable<Boolean> {
//
//        private String nodeName;
//
//        private String taskTableName;
//
//        public CallableOrderSplitByGoodsTaskWithResult(String nodeName, String taskTableName) {
//            this.nodeName = nodeName;
//            this.taskTableName = taskTableName;
//        }
//
//        @Override
//        public Boolean call() throws Exception {
//            PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
//            Integer pageSize = config.getProperty("lts.AutoOrderSplitWmsTask.range", 1000);
//            Integer orderStatus = config.getProperty("lts.AutoOrderSplitWmsTask.status", 0);
//            List<Long> list;
//            long start = System.currentTimeMillis();
//            list = wmsTaskService.selectOcBWarehouseSplitTask(nodeName, pageSize, taskTableName, orderStatus);
//            long endTime1 = System.currentTimeMillis();
//            // 分批发送MQ
//            batchSendSplitOrderList(list);
//
//            long endTime = System.currentTimeMillis();
//            return true;
//        }
//    }
//}
//
