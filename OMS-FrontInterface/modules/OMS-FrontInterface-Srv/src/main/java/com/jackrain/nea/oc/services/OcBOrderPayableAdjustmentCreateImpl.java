package com.jackrain.nea.oc.services;

import com.jackrain.nea.oc.oms.api.OcBOrderPayableAdjustmentCreateCmd;
import com.jackrain.nea.oc.oms.services.OcBOrderPayableAdjustmentCreateService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * description：零售发货单生成丢件单
 *
 * <AUTHOR>
 * @date 2021/5/20
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBOrderPayableAdjustmentCreateImpl implements OcBOrderPayableAdjustmentCreateCmd {

    @Autowired
    private OcBOrderPayableAdjustmentCreateService payableAdjustmentCreateService;

    @Override
    public ValueHolderV14 payableAdjustmentCreate(Long[]ids, User user){
        return payableAdjustmentCreateService.payableAdjustmentCreate(ids,user);
    }

}
