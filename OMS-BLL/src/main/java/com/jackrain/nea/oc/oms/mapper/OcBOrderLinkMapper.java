package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrderLink;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

@Mapper
@Component
public interface OcBOrderLinkMapper extends ExtentionMapper<OcBOrderLink> {

    /**
     * 根据分库建去查数据
     *
     * @param id      id
     * @param orderId 分库建
     * @return OcBOrderLink
     */
    @Select("SELECT * FROM oc_b_order_link WHERE oc_b_order_id=#{orderId} AND id=#{id} and isactive='Y' ")
    OcBOrderLink queryById(@Param("id") Long id, @Param("orderId") Long orderId);


    @Select("<script> "
            + "SELECT * FROM oc_b_order_link WHERE oc_b_order_id "
            + "in <foreach item='item' index='index' collection='orderIds' open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<OcBOrderLink> queryOcBOrderLinkList(@Param("orderIds") Set<Long> orderIds);
}