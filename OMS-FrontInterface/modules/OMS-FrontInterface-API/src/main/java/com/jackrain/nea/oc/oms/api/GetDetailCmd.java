package com.jackrain.nea.oc.oms.api;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.model.result.GetOrderResult;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnType;
import com.jackrain.nea.oc.oms.model.table.OcBReturnTypeItem;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;

import java.util.List;
import java.util.Map;


/**
 * 订单详情-查询
 */

/**
 * @Author: wangqiang
 * @Date: 2019-03-07 10:32
 * @Version 1.0
 */
public interface GetDetailCmd {
    ValueHolder getDetail(JSONObject obj, User user);

    ValueHolderV14<List<GetOrderResult>> getDetailList(List<JSONObject> jsonObjects, User user);

    ValueHolderV14<List<OcBOrderItem>> getOrderItem(Long mainId);

    ValueHolderV14<Map<OcBReturnType, List<OcBReturnTypeItem>>> selectTurnTypeByEname(String ename);
}
