# \u5E94\u7528\u7A0B\u5E8F\u540D
spring.application.name=R3-OC-OMS-Core-Ctrl
# \u591A\u8BED\u8A00\u9ED8\u8BA4\u8BBE\u7F6E\u3002\u5728Apollo\u4E2D\u914D\u7F6E\u7684\u65E0\u6CD5\u751F\u6548\uFF0C\u53EA\u80FD\u5728application.properties\u914D\u7F6E
spring.locale.default=zh_CN
# \u9ED8\u8BA4\u7CFB\u7EDF\u7AEF\u53E3\u53F7
server.port=9091
# \u5F53\u524D\u7CFB\u7EDF\u8FD0\u884C\u914D\u7F6E\u3002\u4E3B\u8981\u7528\u4E8E\u65E5\u5FD7\u7684\u8F93\u51FA\uFF0C\u751F\u4EA7\u7EA7\u522B\u5C06\u4E0D\u518D\u8F93\u51FAdebug\u76F8\u5173\u65E5\u5FD7
spring.profiles.active=dev
tlog.pattern=[$preHost][$traceId][$spanId][$userId]

# \u5E94\u7528\u7A0B\u5E8F\u540D-Dubbo\u5E94\u7528\u7A0B\u5E8F
dubbo.application.name=R3-OC-OMS-Core-Ctrl
# R3\u6807\u51C6\u670D\u52A1Group\u548CVersion\u5B9A\u4E49
app.id=oms-core

spring.cloud.nacos.discovery.group=r3-cloud

nacos.config.type=properties
nacos.config.bootstrap.enable=true
nacos.config.bootstrap.log-enable=false
nacos.config.username=nacos
nacos.config.password=nacos
nacos.config.group=r3-oms
#nacos.config.data-ids=oms-core,drds,redis,oss,reloadschema,oms-core-mq-producer,common,elasticsearch
nacos.config.data-ids=oms-core,drds,redis,oss,reloadschema,rocketmq,common,elasticsearch
nacos.config.auto-refresh=true