package com.jackrain.nea.oc.oms.process.transfer.impl.normal.taobao;

import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.MakeupOrderType;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoOrderRelation;
import com.jackrain.nea.oc.oms.process.AbstractOrderProcess;
import com.jackrain.nea.oc.oms.util.LockOrderType;
import org.springframework.stereotype.Component;

/**
 * 订单中间表转成待分配订单服务
 * <p>
 * 完成从平台订单到订单的转换
 *
 * @author: 易邵峰
 * @since: 2019-01-20
 * create at : 2019-01-20 02:33
 */
@Component
public class TaobaoTransferOrderProcessImpl extends AbstractOrderProcess<IpTaobaoOrderRelation> {

    public TaobaoTransferOrderProcessImpl() {
        super();
    }

    @Override
    protected String getChildPackageName() {
        return "taobao";
    }

    @Override
    protected long getProcessOrderId(IpTaobaoOrderRelation orderInfo) {
        return orderInfo.getOrderId();
    }

    @Override
    protected String getProcessOrderNo(IpTaobaoOrderRelation orderInfo) {
        return orderInfo.getOrderNo();
    }

    @Override
    protected LockOrderType getCurrentLockOrderType() {
        return LockOrderType.TRANSFER_TAOBAO_ORDER;
    }

    @Override
    protected ChannelType getCurrentChannelType() {
        return ChannelType.TAOBAO;
    }

    @Override
    protected MakeupOrderType getCurrentMakeupOrderType() {
        return MakeupOrderType.DO_NOT_MAKEUP;
    }

    /**
     * 淘宝订单接口，平台单号
     *
     * @param orderInfo 订单单据
     * @return
     */
    @Override
    protected String getSourceTid(IpTaobaoOrderRelation orderInfo) {
        return orderInfo.getOrderNo();
    }


}
