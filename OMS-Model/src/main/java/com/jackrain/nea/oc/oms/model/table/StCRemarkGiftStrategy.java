package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

/**
 * 卖家备注赠品策略
 *
 * <AUTHOR>
 */
@Data
@TableName("st_c_remark_gift_strategy")
public class StCRemarkGiftStrategy extends BaseModel {

    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 策略编码
     */
    @JSONField(name = "STRATEGY_CODE")
    private String strategyCode;

    /**
     * 店铺ID
     */
    @JSONField(name = "SHOP_ID")
    private Long shopId;

    /**
     * 关键字
     */
    @JSONField(name = "MATCH_KEYWORD")
    private String matchKeyword;

    /**
     * 赠品商品编码ID
     */
    @JSONField(name = "GIFT_SKU_ID")
    private Long giftSkuId;

    /**
     * 赠品商品编码
     */
    @JSONField(name = "GIFT_SKU_CODE")
    private String giftSkuCode;

    /**
     * 赠送数量
     */
    @JSONField(name = "GIFT_QUANTITY")
    private Integer giftQuantity;

    /**
     * 备注
     */
    @JSONField(name = "REMARK")
    private String remark;

    /**
     * 状态
     */
    @JSONField(name = "STATUS")
    private Integer status;
}