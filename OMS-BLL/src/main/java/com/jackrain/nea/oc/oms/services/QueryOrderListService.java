package com.jackrain.nea.oc.oms.services;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpCSaleOrganization;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.config.OmsSystemConfig;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.StCBusinessTypeMapper;
import com.jackrain.nea.oc.oms.matcher.live.LiveFlagEnum;
import com.jackrain.nea.oc.oms.model.enums.IsForbiddenDeliveryEnum;
import com.jackrain.nea.oc.oms.model.enums.LogisticsStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.OcBorderListEnums;
import com.jackrain.nea.oc.oms.model.enums.OcOrderCheckBoxEnum;
import com.jackrain.nea.oc.oms.model.enums.OcOrderFlagEnum;
import com.jackrain.nea.oc.oms.model.enums.OcOrderLockStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.OcOrderTagEum;
import com.jackrain.nea.oc.oms.model.enums.OmsAuditFailedType;
import com.jackrain.nea.oc.oms.model.enums.OrderExtendLabelEnum;
import com.jackrain.nea.oc.oms.model.enums.OrderSaleProductAttrEnum;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.RefundStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.StepTradeStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.TStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.ToACStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.perm.PermissionEnum;
import com.jackrain.nea.oc.oms.model.perm.PermissionModel;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.request.OrderQueryRequest;
import com.jackrain.nea.oc.oms.model.result.*;
import com.jackrain.nea.oc.oms.model.table.StCHoldOrderReason;
import com.jackrain.nea.oc.oms.nums.OrderGenericMarkEnum;
import com.jackrain.nea.oc.oms.permission.Permission4ESUtil;
import com.jackrain.nea.oc.oms.security.OrderPersonalInfoEncrypt;
import com.jackrain.nea.oc.oms.util.ElasticSearchUtil4Csm;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.st.service.StCHoldOrderReasonQueryService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.CommonEsSearchUtil;
import com.jackrain.nea.utility.ExceptionUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.zip.GZIPOutputStream;

import static com.jackrain.nea.resource.RedisKeyConst.CP_PLATFORM_ALL;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2020/5/2
 */
@Slf4j
@Component
public class QueryOrderListService {


    private final String GZIP_FLAG = "IS_UN_GZIP";
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private OrderPersonalInfoEncrypt orderPersonalInfoEncrypt;
    @Autowired
    private StCBusinessTypeMapper stCBusinessTypeMapper;

    @Autowired
    private StCHoldOrderReasonQueryService holdOrderReasonQueryService;

    @NacosValue(value = "${r3.oc.oms.query.order.deep.size:100000}", autoRefreshed = true)
    public Integer deepSize;

    @Autowired
    private OmsSystemConfig omsSystemConfig;

    /**
     * judge
     */
    private final Consumer<String> expCsm = s -> {
        throw new NDSException(s);
    };

    public ValueHolderV14 queryOrderList(User usr, String param) {

        ValueHolderV14 vh = new ValueHolderV14<>();
        try {
            QueryOrderListResult queryOrderListResult = new QueryOrderListResult();


            QueryEsListResult orderIdsFromEs = getOrderIdsFromEs(usr, param);
            if (orderIdsFromEs.getCheckPageOverDeepSizeVh()!=null){
                // 校验最大查询的数据量
                return orderIdsFromEs.getCheckPageOverDeepSizeVh();
            }
            boolean unGzip = orderIdsFromEs.isUnGzip();
            JSONObject paramJsn = orderIdsFromEs.getParamJsn();
            OrderQueryRequest queryDto = orderIdsFromEs.getQueryDto();


            // 1.1 增加TOB或TOC标识筛选订单数据
            //List<Long> businessTypeIdList = stCBusinessTypeMapper.getBusinessTypeIdByToB();
            //ElasticSearchOrderService.getToBOrToCParam(businessTypeIdList, paramJsn);

            // 2. arrange search condition, permission filter
            //ElasticSearchOrderService.execute(queryDto, paramJsn, omsSystemConfig.getOrderQueryElasticUseKeyword());

            // 3. search es
            //String shardKeys = queryKeysOnElasticSearch(queryDto);
            String shardKeys = orderIdsFromEs.getIds();
            if (StringUtils.isBlank(shardKeys)) {
                return reSetReturnResult(unGzip, "未查询到数据[ES]", queryDto, queryOrderListResult);
            }

            // 4. db search
            String sortCdt = dealSortCondition(queryDto);
            List<QueryOrderResult> orders = ocBOrderMapper.searchOrderList(shardKeys, sortCdt);
            if (orders == null || orders.size() < 1) {
                return reSetReturnResult(unGzip, "未查询到数据[DB]", queryDto, queryOrderListResult);
            }

            JSONObject statusJo = paramJsn.getJSONObject("status");
            if (statusJo != null) {
                String status = statusJo.getString("value");
                //平台发货失败
                boolean label101 = OrderExtendLabelEnum.LABEL101.getVal().equals(statusJo.getString("value"));
                //待换货标签页
                // boolean label102 = OrderExtendLabelEnum.LABEL102.getVal().equals(statusJo.getString("value"));
                //换货完成标签页
                boolean label103 = OrderExtendLabelEnum.LABEL103.getVal().equals(statusJo.getString("value"));
                if (label101) {
                    orders = orders.stream().filter(x -> StringUtils.isNotEmpty(x.getForceSendFailReason())).collect(Collectors.toList());
                }
                if (label103) {
                    JSONArray tidArray = new JSONArray();
                    Set<String> tidSet = orders.stream().map(QueryOrderResult::getTid).collect(Collectors.toSet());
                    JSONObject returnOrderWherekeys = new JSONObject();
                    returnOrderWherekeys.put("TID", tidArray.addAll(tidSet));
                    returnOrderWherekeys.put("RETURN_STATUS", ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal());
                    if (label103) {
                        returnOrderWherekeys.put("RETURN_STATUS", ReturnStatusEnum.WAIT_AFTERSALE_ENSURE.getVal());
                    }
                    List<String> tid = CommonEsSearchUtil.commonSearchFeildsForPage(returnOrderWherekeys, "TID",
                            OcElasticSearchIndexResources.OC_B_RETURN_ORDER_INDEX_NAME, OcElasticSearchIndexResources.OC_B_RETURN_ORDER_TYPE_NAME, tidArray.size());
                    if (CollectionUtils.isEmpty(tid)) {
                        return reSetReturnResult(unGzip, "未查询到数据[RETURN]", queryDto, queryOrderListResult);
                    } else {
                        orders = orders.stream().filter(x -> tid.contains(x.getTid())).collect(Collectors.toList());
                    }
                }
            }
            // 5. item

            // 6. union
            orderUnionItems(orders);
            queryOrderListResult.setQueryOrderResultList(orders);

            // 6.1 flag list
            List<QueryOrderFlagResult> flagList = OcOrderFlagEnum.toQueryOrderFlagResult();
            queryOrderListResult.setOrderFlagList(flagList);

            // 6.2 page info
            arrangeSplitPage(queryOrderListResult, queryDto);


            // 8. set
            if (unGzip) {
                vh.setData(queryOrderListResult);
            } else {
                String zipString = javaCompress(queryOrderListResult);
                vh.setData(zipString);
            }
            vh.setCode(ResultCode.SUCCESS);
            vh.setMessage(Resources.getMessage("Success", usr.getLocale()));

        } catch (Exception ex) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(ExceptionUtil.getMessage(ex));
            log.error(LogUtil.format("QueryOrderListService.Query.Exp,异常信息为:{}"), Throwables.getStackTraceAsString(ex));
        }
        return vh;
    }

    private ValueHolderV14 checkPageOverDeepSize(JSONObject paramJsn) {
        JSONObject pageJo = paramJsn.getJSONObject("page");
        int size = pageJo.getIntValue("pageSize");
        // current es not support long type
        int index = pageJo.getIntValue("pageNum");
        if (index < 1) {
            index = 1;
        }
        if (size < 1) {
            size = 20;
        }
        int start = (index - 1) * size;
        if (start > deepSize) {
            ValueHolderV14 vh = new ValueHolderV14();
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("查询数量超过" + deepSize + "行,建议增加其他条件的限制(如时间范围)");
            return vh;
        }
        return null;
    }

    /**
     * 从订单列表中抽离出的代码，可能返回的情况
     *  如果QueryEsListResult.checkPageOverDeepSizeVh 非空的情况下 不会去查询esResult 这个时候返回的数据结构就是esResult=null
     * @param usr
     * @param param
     * @return
     */
    public QueryEsListResult getOrderIdsFromEs(User usr, String param){

        OrderQueryRequest queryDto = new OrderQueryRequest();
        QueryEsListResult result = new QueryEsListResult();
        // 1. validate
        JSONObject paramJsn = checkParam(usr, param);
        convertSalesCenterToSalesDept(paramJsn);

        boolean unGzip = isReturnGzip(paramJsn);

        ValueHolderV14 checkPageOverDeepSizeVh = checkPageOverDeepSize(paramJsn);
        if (checkPageOverDeepSizeVh!=null){
            result.setCheckPageOverDeepSizeVh(checkPageOverDeepSizeVh);
            result.setUnGzip(unGzip);
            result.setParamJsn(paramJsn);
            return result;
        }

        // 1.1 增加TOB或TOC标识筛选订单数据
        List<Long> businessTypeIdList = stCBusinessTypeMapper.getBusinessTypeIdByToB();
        ElasticSearchOrderService.getToBOrToCParam(businessTypeIdList, paramJsn);

        // 2. arrange search condition, permission filter
        ElasticSearchOrderService.execute(queryDto, paramJsn, omsSystemConfig.getOrderQueryElasticUseKeyword());

        // 3. search es
        String shardKey = queryKeysOnElasticSearch(queryDto);
        result.setIds(shardKey);
        result.setCheckPageOverDeepSizeVh(checkPageOverDeepSizeVh);
        result.setUnGzip(unGzip);
        result.setParamJsn(paramJsn);
        result.setQueryDto(queryDto);
        return result;
    }

    /**
     * 销售中心的查询条件转化成销售部门的查询条件
     * 查询和导出是两个口子，都需要修改才行
     *
     * @param paramJsn
     */
    public void convertSalesCenterToSalesDept(JSONObject paramJsn) {
        JSONArray highSearch = paramJsn.getJSONArray("highSearch");
        String center = null;
        JSONObject deptObj = null;
        Iterator<Object> iterator = highSearch.iterator();
        while (iterator.hasNext()) {
            JSONObject object = (JSONObject) iterator.next();
            String queryName = object.getString("queryName");
            String value = object.getString("value");

            switch (queryName) {
                case "SALES_CENTER":
                    center = value;
                    iterator.remove();
                    break;
                case "SALES_DEPARTMENT_ID":
                    deptObj = object;
                    iterator.remove();
                    break;
                default:
                    break;
            }
        }

        if (StringUtils.isBlank(center)) {
            if (Objects.nonNull(deptObj)) {
                highSearch.add(deptObj);
            }
            return;
        }

        if (Objects.isNull(deptObj)) {
            deptObj = new JSONObject();
            deptObj.put("queryName", "SALES_DEPARTMENT_ID");
            deptObj.put("value", "");
            deptObj.put("type", "Select");
        }

        Set<Long> centerSet = Arrays.stream(center.split(","))
                .map(Long::parseLong)
                .collect(Collectors.toSet());
        List<CpCSaleOrganization> organizationList = cpRpcService.querySalesOrgBySalesCenter(centerSet);
        if (CollectionUtils.isEmpty(organizationList)) {
            highSearch.add(deptObj);
            return;
        }
        Set<Long> centerOrgSet = organizationList.stream()
                .map(CpCSaleOrganization::getCStoreattrib2Id).collect(Collectors.toSet());

        String dept = deptObj.getString("value");
        if (StringUtils.isBlank(dept)) {
            String centerOrgStr = centerOrgSet.stream()
                    .map(String::valueOf).collect(Collectors.joining(","));
            deptObj.put("value", centerOrgStr);
            highSearch.add(deptObj);
            return;
        }

        Set<Long> deptSet = Arrays.stream(dept.split(","))
                .map(Long::parseLong)
                .collect(Collectors.toSet());
        deptSet.retainAll(centerOrgSet);
        String newDept = deptSet.stream()
                .map(String::valueOf).collect(Collectors.joining(","));

        deptObj.put("value", newDept);
        highSearch.add(deptObj);
    }

    /**
     * es search by param conditions
     *
     * @param queryDto query param
     * @return shard kes then join to string
     */
    public String queryKeysOnElasticSearch(OrderQueryRequest queryDto) {
        JSONObject eso = ElasticSearchUtil4Csm.orderListSearch("oc_b_order", "oc_b_order", queryDto);
        clearQueryParam(queryDto);
        return splitOrderIds(eso, queryDto);
    }

    /**
     * validation
     *
     * @param usr   user
     * @param param origin param
     * @return json
     */
    private JSONObject checkParam(User usr, String param) {

        if (usr == null) {
            expCsm.accept("当前操作用户不存在");
        }
        if (StringUtils.isBlank(param)) {
            expCsm.accept("查询参数不能为空");
        }
        return JSONObject.parseObject(param);
    }

    /**
     * es提取主表id,并计算起始下标
     *
     * @param esJsn    jsonObject
     * @param queryDto OrderQueryParamDto
     */
    private String splitOrderIds(JSONObject esJsn, OrderQueryRequest queryDto) {

        if (esJsn == null) {
            return null;
        }

        long totalCount = esJsn.getLongValue("total");
        long totalPage = 0L;
        if (totalCount > 0) {
            long l = totalCount % queryDto.getSize();
            totalPage = totalCount / queryDto.getSize();
            if (l > 0) {
                totalPage += 1;
            }
        }
        queryDto.setTotalCount(totalCount);
        queryDto.setTotalPage(totalPage);

        StringBuilder sb = new StringBuilder();
        JSONArray ary = esJsn.getJSONArray("data");
        if (ary == null) {
            return null;
        }
        int i = 0, l = ary.size();
        for (; i < l; i++) {
            JSONObject o = ary.getJSONObject(i);
            if (o == null) {
                continue;
            }
            String id = o.getString("ID");
            if (id == null) {
                continue;
            }
            sb.append(id);
            i++;
            break;
        }
        for (; i < l; i++) {
            JSONObject o = ary.getJSONObject(i);
            if (o == null) {
                continue;
            }
            String id = o.getString("ID");
            if (id == null) {
                continue;
            }
            sb.append(",").append(id);
        }
        return sb.toString();

    }

    /**
     * sort
     *
     * @param queryDto param dto
     * @return order string
     */
    private String dealSortCondition(OrderQueryRequest queryDto) {

        JSONArray order = queryDto.getOrder();
        StringBuilder sb = new StringBuilder();

        for (int i = 0; i < order.size(); i++) {
            JSONObject jsn = order.getJSONObject(i);
            String name = jsn.getString("name");
            boolean asc = jsn.getBooleanValue("asc");
            if (i > 0) {
                sb.append(", ");
            }
            sb.append(name).append(" ");
            if (asc) {
                sb.append("ASC");
                continue;
            }
            sb.append("DESC");
        }
        return sb.toString();
    }

    /**
     * union, arrange , calc, convert field
     *
     * @param orderList order data
     */
    private void orderUnionItems(List<QueryOrderResult> orderList) {

        // 平台
        HashOperations<String, String, String> hps = redisTemplate.opsForHash();
        Map<String, String> pm = hps.entries(CP_PLATFORM_ALL);

        // 传结算状态
        Map<Long, String> toSettleMap = ToACStatusEnum.toMap();
        // SAP状态
        Map<Integer, String> sapMap = OcBorderListEnums.SendSAPEnum.toIntegerMap();

        // 菜鸟作业状态
        Map<String, String> caiNiaoMap = LogisticsStatusEnum.getStatusMap();

        // 锁单
        Map<Long, String> lockMap = OcOrderLockStatusEnum.convertAllToHashVal();

        // 淘宝.预售
        Map<String, String> preSaleMap = OcBorderListEnums.OrderPreSaleStatus.convertAllToHashVal();

        // 刷单.预
        Map<Integer, String> brushOrderMap = OcBorderListEnums.ScalpingTypeEnum.getAllConvertToMap();

        for (QueryOrderResult o : orderList) {
            if (o == null) {
                continue;
            }

            // 将o.getEstimateConTime() 设置成yyyy-MM-dd HH:mm:ss
            o.setMainEstimateConTime(DateUtil.format(o.getEstimateConTime(), DatePattern.NORM_DATETIME_PATTERN));

            // 0. 标签
            packageTags(o);

            // 1. 字段选项组
            exChangeOutPutField(o);

            // 2. 地址
            dealAddressInfo(o);

            // 3. 平台
            String platFormName = dealPlatform(pm, o.getPlatform());
            o.setPlatFormName(platFormName);

            // 4. 锁单
            Long lockStatus = o.getLockStatus();
            String lockStatusName = lockMap.get(lockStatus);
            o.setReserveBigint09Name(lockStatusName == null ? "" : lockStatusName);

            // 5. 预售
            String preSale = o.getStatusPayStep();
            String preSaleName = preSaleMap.get(preSale);
            o.setReserveVarchar03Name(preSaleName == null ? "非预售" : preSaleName);
            if (Objects.nonNull(o.getGenericMark())) {
                o.setGenericMark(OrderGenericMarkEnum.INTERCEPT.getTag().equals(o.getGenericMark()) ? OrderGenericMarkEnum.INTERCEPT.getDesc() : o.getGenericMark());
            }
            // hold单原因
            if(Objects.nonNull(o.getHoldReasonId())) {
                StCHoldOrderReason stCHoldOrderReason = holdOrderReasonQueryService.selectHoldOrderById(Long.valueOf(o.getHoldReasonId()));
                if (Objects.nonNull(stCHoldOrderReason)) {
                    o.setHoldReasonName(stCHoldOrderReason.getReason());
                }
            }
            // 卡单原因
            if(Objects.nonNull(o.getDetentionReasonId())) {
                StCHoldOrderReason detentionReason = holdOrderReasonQueryService.selectHoldOrderById(Long.valueOf(o.getDetentionReasonId()));
                if (Objects.nonNull(detentionReason)) {
                    o.setDetentionReasonName(detentionReason.getReason());
                }
            }

            // 票
            if (o.getInvoiceStatus() == null) {
                o.setInvoiceStatus(OcBOrderConst.IS_STATUS_IN);
                o.setInvoiceStatusName(OcBorderListEnums.InvoiceStatusEnum.getTextByVal(OcBOrderConst.IS_STATUS_IN));
            } else {
                o.setInvoiceStatusName(OcBorderListEnums.InvoiceStatusEnum.getTextByVal(o.getInvoiceStatus()));
            }

            // 是否是换货入库
            Long v1 = o.getIsExchangeNoIn();
            o.setReserveBigint03Name(v1 == null ? "否" : (v1.intValue() == 0 ? "否" : "是"));

            //退款状态
//            o.setRefundStatusName(OmsOrderRefundStatus.toStatusString(Optional.ofNullable(o.getRefundStatus()).orElse(0)));
            o.setRefundStatusName(RefundStatusEnum.getValueByKey(o.getRefundStatus()));

            String gwSourceCode = o.getGwSourceCode();
            if (StringUtils.isNotEmpty(gwSourceCode)){
                //查询店铺
                CpShop cpShop = cpRpcService.selectShopById(Long.valueOf(gwSourceCode));
                if (cpShop != null) {
                    o.setGwSourceCode(cpShop.getSellerNick());
                }
            }

            //销售商品属性
            String saleProductAttr = o.getSaleProductAttr();
            if (StringUtils.isNotBlank(saleProductAttr)) {
                o.setSaleProductAttr(OrderSaleProductAttrEnum.getDescriptionByVal(saleProductAttr));
            }

        }
    }

    /**
     * 订单值:  转换
     *
     * @param o 订单
     */
    private void exChangeOutPutField(QueryOrderResult o) {

        String orderStatusName = OcOrderCheckBoxEnum.enumToStringByValue(o.getOrderStatus());
        o.setOrderStatusName(orderStatusName);
        String orderTypeName = OrderTypeEnum.getTextByVal(o.getOrderType());
        o.setOrderTypeName(orderTypeName);
        String payTypeName = OcBorderListEnums.PayTypeEnum.getTextByVal(o.getPayType());
        o.setPayTypeName(payTypeName);
        String occupyStatusName = OcBorderListEnums.OccupyStatusEnum.getTextByVal(o.getOccupyStatus());
        o.setOccupyStatusName(occupyStatusName);
        String wmsCancelStatusName = OcBorderListEnums.WmsCanceStatusEnum.getTextByVal(o.getWmsCancelStatus());
        o.setWmsCancelStatusName(wmsCancelStatusName);
        String autoAuditStatusName = OcBorderListEnums.AutoAuditStatusEnum.getTextByVal(o.getAutoAuditStatus());
        o.setAutoAuditStatusName(autoAuditStatusName);
        String isGeninvoiceNoticeNm = OcBorderListEnums.IsGeninvoiceNoticeEnum.getTextByVal(o.getIsGeninvoiceNotice());
        o.setIsGeninvoiceNoticeName(isGeninvoiceNoticeNm);
        String returnName = OcBorderListEnums.ReturnStatusEnum.getTextByVal(o.getReturnStatus());
        o.setReturnStatusName(returnName);
        String refundStatusName = RefundStatusEnum.getValueByKey(o.getRefundStatus());
        o.setRefundStatusName(refundStatusName);
        String payStatusName = StepTradeStatusEnum.getValueByKey(o.getPayStatus());
        o.setPayStatusName(payStatusName);
        String platformStatusName = TStatusEnum.getValueByKey(o.getPlatformStatus());
        o.setPlatformStatusName(platformStatusName);
        // 审核失败类型 20210117版本 黄志优增加
        String auditFailedTypeName = OmsAuditFailedType.enumToStringByValue(o.getAuditFailedType());
        o.setAuditFailedTypeName(auditFailedTypeName);
        // @20201124 是否O2O订单
        String isO2oName = OcBorderListEnums.YesOrNoEnum.getTextByVal(o.getIsO2oOrder());
        o.setIsO2oName(isO2oName);
        // @20201124 是否HOLD单
        String isHoldName = OcBorderListEnums.YesOrNoEnum.getTextByVal(o.getIsInterecept());
        o.setIsHoldName(isHoldName);
        // @20201124 是否同城购
        String isSameCityPurchaseName = OcBorderListEnums.YesOrNoEnum.getTextByVal(o.getIsSameCityPurchase());
        o.setIsSameCityPurchaseName(isSameCityPurchaseName);
        o.setIsForbiddenDeliveryName(IsForbiddenDeliveryEnum.getName(o.getIsForbiddenDelivery()));
        //是否寻源失败
        String isOccupyStockFailName = OcBorderListEnums.YesOrNoEnum.getTextByVal(o.getIsOccupyStockFail());
        o.setIsOccupyStockFailName(isOccupyStockFailName);
        String saleProductAttr = OrderSaleProductAttrEnum.getDescriptionByVal(o.getSaleProductAttr());
        o.setSaleProductAttrName(saleProductAttr);
    }

    /**
     * 收货地址
     *
     * @param o QueryOrderResult
     */
    private void dealAddressInfo(QueryOrderResult o) {

        StringBuilder sb = new StringBuilder();
        String pn = o.getCpCRegionProvinceEname();
        if (pn != null && pn.trim().length() > 0) {
            sb.append(pn + "/");
        }
        String cn = o.getCpCRegionCityEname();
        if (cn != null && cn.trim().length() > 0) {
            sb.append(cn + "/");
        }
        String an = o.getCpCRegionAreaEname();
        if (an != null && an.trim().length() > 0) {
            sb.append(an + "/");
        }
        String tn = o.getCpCRegionTownEname();
        if (tn != null && tn.trim().length() > 0) {
            sb.append(tn + "/");
        }
        String rad = o.getReceiverAddress();
        if (rad != null && rad.trim().length() > 0) {
            sb.append(rad);
            o.setRegionReceiverAddress(sb.toString());
        } else {
            String s = sb.toString();
            if (s.length() > OcBOrderConst.IS_STATUS_IY) {
                s = s.substring(OcBOrderConst.IS_STATUS_IN, s.length() - OcBOrderConst.IS_STATUS_IY);
            }
            o.setRegionReceiverAddress(s);
        }
    }

    /**
     * 平台.转换
     *
     * @param m 集
     * @param p 值
     * @return 名称
     */
    private String dealPlatform(Map<String, String> m, Integer p) {

        if (m != null && m.size() > 0) {
            String s1 = m.get(String.valueOf(p));
            if (s1 != null) {
                return s1;
            } else {
                return cpRpcService.rpcQueryPlatformNameByCode(String.valueOf(p));
            }
        } else {
            return rpcGetPlatformName(String.valueOf(p));
        }
    }

    /**
     * RPC.查询.平台
     *
     * @param sp 值
     * @return 名称
     */
    private String rpcGetPlatformName(String sp) {
        try {
            String s = cpRpcService.rpcQueryPlatformNameByCode(sp);
            return s == null ? "" : s;
        } catch (Exception e) {
            log.error(LogUtil.format("QueryOrderListService.rpcGetPlatformName,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            return "";
        }
    }

    /**
     * clear notice gc
     *
     * @param queryDto query param
     */
    private void clearQueryParam(OrderQueryRequest queryDto) {
        queryDto.setWhere(null);
        queryDto.setChild(null);
        queryDto.setFilter(null);
        queryDto.setPermissionAlias(null);
        queryDto.setPermissionModel(null);
    }

    /**
     * gzip
     *
     * @return zip
     * @throws IOException io
     */
    private String javaCompress(QueryOrderListResult resultList) throws IOException {

        String gzipString = JSONObject.toJSONString(resultList, SerializerFeature.WriteMapNullValue);
        if (StringUtils.isBlank(gzipString)) {
            return gzipString;
        }
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        GZIPOutputStream gzip = new GZIPOutputStream(out);
        gzip.write(gzipString.getBytes(StandardCharsets.UTF_8));
        gzip.close();
        String zipString = out.toString("ISO-8859-1");
        out.close();
        return zipString;
    }

    /**
     * 根据标签值,转化为标签对象
     * 此处增加字段的话 导出记得也要加一下
     *
     * @param qor 订单查询结果
     */
    private void packageTags(QueryOrderResult qor) {

        List<String> list = new ArrayList<>();

        if (OcBOrderConst.IS_STATUS_IY.equals(qor.getIsMerge())) {
            list.add("IS_MERGE");
        }
        if (OcBOrderConst.IS_STATUS_IY.equals(qor.getIsInterecept())) {
            list.add("IS_INTERECEPT");
        }
        if (OcBOrderConst.IS_STATUS_IY.equals(qor.getIsInreturning())) {
            list.add("IS_INRETURNING");
        }
        if (OcBOrderConst.IS_STATUS_IY.equals(qor.getIsHasgift())) {
            list.add("IS_HASGIFT");
        }
        if (OcBOrderConst.IS_STATUS_IY.equals(qor.getIsSplit())) {
            list.add("IS_SPLIT");
        }
        if (OcBOrderConst.IS_STATUS_IY.equals(qor.getIsInvoice())) {
            list.add("IS_INVOICE");
        }
        if (OcBOrderConst.IS_STATUS_IY.equals(qor.getIsJcorder())) {
            list.add("IS_JCORDER");
        }
        if (OcBOrderConst.IS_STATUS_IY.equals(qor.getIsOutUrgency())) {
            list.add("IS_OUT_URGENCY");
        }
        if (OcBOrderConst.IS_STATUS_IY.equals(qor.getIsHasTicket())) {
            list.add("IS_HAS_TICKET");
        }
        if (OcBOrderConst.IS_STATUS_IY.equals(qor.getIsCombination())) {
            list.add("IS_COMBINATION");
        }
        if (qor.getOrderType() != null && OcBOrderConst.ORDER_PAY_TYPE == qor.getOrderType()) {
            list.add("ORDER_TYPE");
        }
        if (qor.getDouble11PresaleStatus() != null
                && !(OcBOrderConst.ORDER_STATUS_ALL.equals(qor.getDouble11PresaleStatus()))) {
            list.add("DOUBLE11_PRESALE_STATUS");
        }
        if (qor.getPayType() != null && OcBOrderConst.ORDER_PAY_TYPE == qor.getPayType()) {
            list.add("PAY_TYPE");
        }
        if (StringUtils.isNotEmpty(qor.getPriceLabel()) && OcBOrderConst.IS_ACTIVE_YES.equals(qor.getPriceLabel())) {
            list.add("PRICE_LABEL");
        }
        if (qor.getLockStatus() != null && OcOrderLockStatusEnum.LOCKED.getKey() == qor.getLockStatus()) {
            list.add("LOCK_STATUS");
        }
        // 直播标 @20200710
        if (LiveFlagEnum.Y.getValue().equals(qor.getLiveFlag())) {
            list.add("LIVE_FLAG");
        }
        if (OcBOrderConst.IS_STATUS_IY.equals(qor.getIsO2oOrder())) {
            list.add("IS_O2O_ORDER");
        }
        if (OcBOrderConst.IS_STATUS_IY.equals(qor.getIsCopyOrder())) {
            list.add("IS_COPY_ORDER");
        }
        if (OcBOrderConst.IS_STATUS_IY.equals(qor.getIsResetShip())) {
            list.add("IS_RESET_SHIP");
        }
        if (OcBOrderConst.IS_STATUS_IY.equals(qor.getIsPromOrder())) {
            list.add("IS_PROM_ORDER");
        }
        if (OcBOrderConst.IS_STATUS_IY.equals(qor.getIsEqualExchange())) {
            list.add("IS_EQUAL_EXCHANGE");
        }
        if (OcBOrderConst.IS_STATUS_IY.equals(qor.getIsOutStock())) {
            list.add("IS_OUT_STOCK");
        }
        if (OcBOrderConst.IS_DELIVERY_URGENT.equals(qor.getIsDeliveryUrgent())) {
            list.add(OcOrderTagEum.TAG_DELIVERY_URGENT.getKey());
        }
        if (OcBOrderConst.IS_MODIFIED_ORDER.equals(qor.getIsModifiedOrder())) {
            list.add(OcOrderTagEum.TAG_MODIFIED.getKey());
        }
        if (OcBOrderConst.IS_STATUS_IY.equals(qor.getIsExtra())) {
            list.add("IS_EXTRA");
        }
        if (OcBOrderConst.IS_STATUS_IY.equals(qor.getIsDetention())) {
            list.add("IS_DETENTION");
        }
        if (IsForbiddenDeliveryEnum.FORBIDDEN.getCode().equals(qor.getIsForbiddenDelivery())) {
            //JitX禁发
            list.add("IS_FORBIDDEN_DELIVERY");
        }
        if (YesNoEnum.Y.getVal().equals(qor.getIsVipUpdateWarehouse())) {
            //JitX改仓
            list.add("IS_VIP_UPDATE_WAREHOUSE");
        }
        if (YesNoEnum.ONE.getKey().equals(qor.getReverseAuditType())) {
            //反审核中
            list.add("REVERSE_AUDIT_TYPE");
        }
        if (OcBOrderConst.IS_STATUS_IY.equals(qor.getIsException())) {
            list.add("IS_EXCEPTION");
        }
        if (OcBOrderConst.IS_STATUS_IY.equals(qor.getIsMember())) {
            list.add("IS_MEMBER");
        }
        /**
         * 天猫周期购
         */
        if (OcBOrderConst.IS_STATUS_IY.equals(qor.getIsCycle())) {
            list.add("IS_CYCLE");
        }

        if (OcBOrderConst.IS_STATUS_IY.equals(qor.getIsNoRange())) {
            list.add("IS_NO_RANGE");
        }

        /**
         * 天猫打标-送货上门
         */
        if (OcBOrderConst.IS_STATUS_IY.equals(qor.getIsDeliveryToDoor())) {
            list.add("IS_DELIVERY_TO_DOOR");
        }

        /**
         * 是否逾期
         */
        if (OcBOrderConst.IS_STATUS_IY.equals(qor.getIsOverdue())) {
            list.add("IS_OVERDUE");
        }

        /**
         * 是否备赠
         */
        if (OcBOrderConst.IS_STATUS_IY.equals(qor.getIsRemarkGift())) {
            list.add("IS_REMARK_GIFT");
        }

        /**
         * 是否冻结店铺的订单
         */
        if (OcBOrderConst.IS_STATUS_IY.equals(qor.getIsUnavailableShop())) {
            list.add("IS_UNAVAILABLE_SHOP");
        }
        if (StringUtils.isNotEmpty(qor.getCarpoolNo())) {
            list.add("IS_CARPOOL");
        }

        // 手工单 新增逻辑2020-07-24：当存在复制标签时，去除手工单
        List<QueryOrderTagResult> tagList = OcOrderTagEum.toListQueryOrderTagResult(list);
        if (OcOrderTagEum.TAG_HAND.getVal().equals(qor.getOrderSource()) &&
                !list.contains("IS_COPY_ORDER") && !list.contains("IS_RESET_SHIP")) {
            QueryOrderTagResult q = OcOrderTagEum.getQueryOrderTagResult(OcOrderTagEum.TAG_HAND);
            tagList.add(q);
        }
        qor.setOrderTagList(tagList);
    }

    /**
     * 分页信息
     *
     * @param queryOrderListResult QueryOrderListResult
     * @param queryDto             QueryOrderRelation
     */
    private void arrangeSplitPage(QueryOrderListResult queryOrderListResult, OrderQueryRequest queryDto) {
        queryOrderListResult.setPageSize(queryDto.getSize());
        queryOrderListResult.setPageNum((int) queryDto.getIndex());
        queryOrderListResult.setTotalSize(queryDto.getTotalCount());
        queryOrderListResult.setTotalNum((int) queryDto.getTotalPage());
    }

    /**
     * 按需求: 异常,未查询到等情况,返回0
     *
     * @param unGzip      zip?
     * @param msg         exp msg
     * @param queryDto    query param
     * @param queryResult query result
     * @return vh
     */
    private ValueHolderV14 reSetReturnResult(boolean unGzip, String msg, OrderQueryRequest queryDto, QueryOrderListResult queryResult) {
        ValueHolderV14 vh = new ValueHolderV14();
        vh.setCode(ResultCode.SUCCESS);
        vh.setMessage(msg);

        queryResult.setPageSize(queryDto.getSize());
        queryResult.setPageNum(0);
        queryResult.setTotalSize(0L);
        queryResult.setTotalNum(0);
        queryResult.setQueryOrderResultList(new ArrayList<>());
        queryResult.setOrderFlagList(new ArrayList<>());
        vh.setData(queryResult);
        if (unGzip) {
            vh.setData(queryResult);
        } else {
            String zipString = null;
            try {
                zipString = javaCompress(queryResult);
            } catch (IOException e) {
                log.error(LogUtil.format("QueryOrderListService,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            }
            vh.setData(zipString);
        }
        return vh;
    }

    /**
     * Is Return GZip format
     *
     * @param paramJsn
     * @return
     */
    private boolean isReturnGzip(JSONObject paramJsn) {
        if (paramJsn.containsKey(GZIP_FLAG)) {
            boolean isZip = paramJsn.getBooleanValue(GZIP_FLAG);
            paramJsn.remove(GZIP_FLAG);
            return isZip;
        }
        return false;
    }

    /**
     * description:根据es条件查询出es结果
     *
     * @Author: liuwenjin
     * @Date 2021/12/13 2:41 下午
     */
    public JSONObject querySelectList(User usr, String param) {
        OrderQueryRequest queryDto = new OrderQueryRequest();

        // 1. validate
        JSONObject paramJsn = checkParam(usr, param);
        boolean unGzip = isReturnGzip(paramJsn);
        QueryOrderListResult queryOrderListResult = new QueryOrderListResult();

        // 2. arrange search condition, permission filter
        ElasticSearchOrderService.execute(queryDto, paramJsn, omsSystemConfig.getOrderQueryElasticUseKeyword());
        PermissionModel set = PermissionModel.build().set(PermissionEnum.SHOP).set(PermissionEnum.LOGISTICS);
        queryDto.setPermissionModel(set);
        boolean permissionResult = Permission4ESUtil.permissionHandler(usr, queryDto, true);
        if (!permissionResult) {

        }

        // 3. search es
        if (log.isDebugEnabled()) {
            log.debug("queryKeysOnElasticSearch 入参={}", JSON.toJSONString(queryDto));
        }
        JSONObject eso = ElasticSearchUtil4Csm.search("oc_b_order", "oc_b_order",
                "oc_b_order_item", queryDto);
        if (Objects.isNull(eso)) {
            throw new NDSException(Resources.getMessage("未查询到数据[P2]!", usr));
        }

        return eso;
    }
}
