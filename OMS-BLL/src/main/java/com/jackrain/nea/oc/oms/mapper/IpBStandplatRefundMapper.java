package com.jackrain.nea.oc.oms.mapper;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatRefund;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.jdbc.SQL;

import java.util.Date;
import java.util.List;


@Mapper
public interface IpBStandplatRefundMapper extends ExtentionMapper<IpBStandplatRefund> {

    @Select("SELECT * FROM ip_b_standplat_refund WHERE return_no=#{orderNo}")
    IpBStandplatRefund selectStandplatRefundByRefundId(@Param("orderNo") String orderNo);


    /**
     * 转单. 记录失败原因
     *
     * @param jsonObject 更新内容
     * @return 更新条数
     */
    @UpdateProvider(type = StandardPlatSql.class, method = "updateFailReason")
    int updateFailReason(JSONObject jsonObject);

    class StandardPlatSql {

        /**
         * 转单失败原因记录
         *
         * @param map 内容
         * @return sql String
         */
        public String updateFailReason(JSONObject map) {
            return new SQL() {
                {
                    UPDATE("ip_b_standplat_refund");
                    SET("trans_count = IFNULL(trans_count, 0) + 1");
                    for (String key : map.keySet()) {
                        if (!"return_no".equals(key)) {
                            SET(key + "= #{" + key + "}");
                        }
                    }
                    WHERE("return_no= #{return_no}");
                }
            }.toString();
        }
    }

    /**
     * 根据平台查询通用退单无物流单号的数据
     *
     * @param platformId
     * @param date
     * @return
     */
    @Select("SELECT * FROM ip_b_standplat_refund WHERE cp_c_platform_id = #{platformId} and creationdate >= #{date} and logistics_no is null")
    List<IpBStandplatRefund> selectNoLogisticNo(@Param("platformId") Integer platformId, @Param("date") Date date);

}