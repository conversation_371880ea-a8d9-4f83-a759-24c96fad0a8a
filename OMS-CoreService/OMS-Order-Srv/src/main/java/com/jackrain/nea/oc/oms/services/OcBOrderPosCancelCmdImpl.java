package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.oc.oms.api.OcBOrderPosCancelCmd;
import com.jackrain.nea.oc.oms.model.request.PosOrderCancelRequest;
import com.jackrain.nea.util.ValueHolder;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * className: OcBOrderPosCancelCmdImpl
 * description:云仓退款
 *
 * <AUTHOR>
 * create: 2021-08-25
 * @since JDK 1.8
 */
@Component
@Service(protocol = "dubbo", validation = "true", group = "oc-core", version = "1.0")
public class OcBOrderPosCancelCmdImpl implements OcBOrderPosCancelCmd {

    @Autowired
    private OcBOrderAllRefundService refundService;

    @Override
    public ValueHolder posCancel(List<PosOrderCancelRequest> requests) {
        return refundService.posCancel(requests);
    }
}
