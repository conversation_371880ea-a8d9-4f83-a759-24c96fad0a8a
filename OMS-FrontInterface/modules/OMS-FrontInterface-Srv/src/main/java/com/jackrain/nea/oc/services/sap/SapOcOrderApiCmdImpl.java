package com.jackrain.nea.oc.services.sap;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.api.sap.SapOcOrderApiCmd;
import com.jackrain.nea.oc.oms.sap.SapOcOrderService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2020/8/13
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class SapOcOrderApiCmdImpl implements SapOcOrderApiCmd {


    @Autowired
    private SapOcOrderService sapOcOrderService;



    @Override
    public ValueHolderV14 ocOrderSignFor(JSONObject params) {
        return sapOcOrderService.sapOcOrderSignFor(params);
    }

    @Override
    public ValueHolderV14 sapOcOrder(JSONObject params) {
        return sapOcOrderService.sapOcOrder(params);
    }
}
