package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.OcBSapSalesDataGather;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
public interface OcBSapSalesDataGatherMapper extends ExtentionMapper<OcBSapSalesDataGather> {

    @Select("select * from OC_B_SAP_SALES_DATA_GATHER where TO_SAP_STATUS in(0,3)" +
            " and GATHER_MIDDLE_STATUS='2' and TO_SAP_FAIL_NUM <5 " +
            " and isactive='Y' order by MODIFIEDDATE asc limit ${num}")
    List<OcBSapSalesDataGather> querySalesDataGather(@Param("num") Integer num);

    @Select("<script>" +
            "select * from OC_B_SAP_SALES_DATA_GATHER where TO_SAP_STATUS in(0,3) and isactive='Y' and id in"
            + "<foreach item='ids' index='index' collection='ids' open='(' separator=',' close=')'>"
            + " #{ids} "
            + "</foreach>"
            + "</script>")
    List<OcBSapSalesDataGather> queryButtonSalesDataGather(@Param("ids") List<Long> ids);

    /**
     * 通过id批量更新传SAP状态
     *
     * @param ids
     * @param toSapStatus
     * @return
     */
    @Update("<script> "
            + "UPDATE OC_B_SAP_SALES_DATA_GATHER SET TO_SAP_STATUS =#{toSapStatus}, modifieddate=now() " +
            "where id in "
            + "<foreach item='ids' index='index' collection='ids' open='(' separator=',' close=')'>"
            + " #{ids} "
            + "</foreach>"
            + "</script>")
    Integer batchUpdateSapStatus(@Param("ids") List<Long> ids, @Param("toSapStatus") Integer toSapStatus);


    @Update("<script> "
            + "UPDATE OC_B_SAP_SALES_DATA_GATHER SET TO_SAP_STATUS =#{toSapStatus}, TO_SAP_FAIL_NUM=0,SAP_FAIL_REASON=null, modifieddate=now() " +
            "where id in "
            + "<foreach item='ids' index='index' collection='ids' open='(' separator=',' close=')'>"
            + " #{ids} "
            + "</foreach>"
            + "</script>")
    Integer batchUpdateSapSuccessStatus(@Param("ids") List<Long> ids, @Param("toSapStatus") Integer toSapStatus);

    @Update("UPDATE OC_B_SAP_SALES_DATA_GATHER SET TO_SAP_STATUS =#{toSapStatus}, TO_SAP_FAIL_NUM=TO_SAP_FAIL_NUM+1,SAP_FAIL_REASON=#{failReason}, modifieddate=now()" +
            "where id=#{id}")
    Integer updateSapFailStatus(@Param("id") Long id, @Param("toSapStatus") Integer toSapStatus, @Param("failReason") String failReason);


    @Select("<script>" +
            "select * from oc_b_sap_sales_data_gather where GATHER_MIDDLE_STATUS ='1' and isactive='Y' and MERGE_CODE in"
            + "<foreach item='mergeCodes' index='index' collection='mergeCodes' open='(' separator=',' close=')'>"
            + " #{mergeCodes} "
            + "</foreach>"
            + "</script>")
    List<OcBSapSalesDataGather> selectSalesDataGatherByMergeCode(@Param("mergeCodes") List<String> mergeCode) ;

    @Select("select id from oc_b_sap_sales_data_gather  where GATHER_MIDDLE_STATUS ='1' and isactive='Y' " +
            "and SUM_TYPE in ('0','1','Z74','Z73','Z23')")
    List<Long> selectGatherIdBySumType() ;

    @Select("<script>" +
            " select id, MERGE_CODE, sum_type from OC_B_SAP_SALES_DATA_GATHER " +
            " where isactive = 'Y' and GATHER_MIDDLE_STATUS = '1'" +
            " and MERGE_CODE in "
            + "<foreach item='mergeCode' index='index' collection='mergeCodes' open='(' separator=',' close=')'> "
            + " #{mergeCode} "
            + "</foreach> "
            + "</script>")
    List<OcBSapSalesDataGather> selectInfoByMergeCodes(@Param("mergeCodes") List<String> mergeCodes);


    @Select("select id from OC_B_SAP_SALES_DATA_GATHER where sum_type in ('RYCD01', 'RYCD02', 'RYCD03', 'RYCD04') " +
            " and GATHER_MIDDLE_STATUS = '1' and isactive = 'Y'")
    List<Long> selectInfoBySumType();
}