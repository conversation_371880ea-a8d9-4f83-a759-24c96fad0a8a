package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.CommonIdempotent;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoOrderCycleBuy;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * 幂等表操作
 *
 * <AUTHOR>
 */
@Component
@Mapper
public interface CommonIdempotentMapper extends ExtentionMapper<CommonIdempotent> {

    /**
     * 查询对应类型的数据
     *
     * @param type
     * @return
     */
    @Select("SELECT * FROM common_idempotent WHERE type = #{type} AND isactive = 'Y' ORDER BY creationdate ASC LIMIT #{limit}")
    List<CommonIdempotent> selectAvailableDatasLimit(@Param("type") String type, @Param("limit") int limit);

    /**
     * 完成幂等
     *
     * @param orderId
     * @return
     */
    @Update("UPDATE common_idempotent SET isactive = 'N' WHERE business_code = #{orderId} AND isactive = 'Y'")
    int updateDone(@Param("orderId") String orderId);

}