package com.jackrain.nea.oc.oms.transfer;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.mapper.IpBOrderLockMapper;
import com.jackrain.nea.oc.oms.model.relation.IpOrderLockRelation;
import com.jackrain.nea.oc.oms.model.table.IpBOrderLock;
import com.jackrain.nea.oc.oms.nums.OrderLockLogTypeEnum;
import com.jackrain.nea.oc.oms.process.transfer.impl.lock.unlockorder.OrderUnlockProcessImpl;
import com.jackrain.nea.oc.oms.services.BatchExportOcBOrderService;
import com.jackrain.nea.oc.oms.services.CpQueryChangingOrRefundingService;
import com.jackrain.nea.oc.oms.services.IpOrderLockQueryService;
import com.jackrain.nea.oc.oms.services.IpOrderLockService;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Profile;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: 黄超
 * @since: 2019-06-27
 * create at : 2019-06-27 20:00
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest()
@Profile(value = "dev")
public class LockOrderTest {
    @Autowired
    private IpBOrderLockMapper lockMapper;
    @Autowired
    protected IpOrderLockService ipOrderLockService;
    @Autowired
    private OrderUnlockProcessImpl orderUnlockProcessImpl;

    @Autowired
    private IpOrderLockQueryService lockQueryService;

    @Autowired
    private CpQueryChangingOrRefundingService refundingService;

    @Autowired
    BatchExportOcBOrderService batchExportOcBOrderService;
    @Autowired
    private IpOrderLockService lockService;

    @Test
    public void pushES() {
        QueryWrapper<IpBOrderLock> wrapper = new QueryWrapper<>();
        wrapper.eq("isactive", "Y");
        List<IpBOrderLock> esLockList = lockMapper.selectList(wrapper);
//        try {
//            //重推ES
//            SpecialElasticSearchUtil.indexDocuments(OcElasticSearchIndexResources.IP_B_ORDER_LOCK_TYPE_NAME,
//                    OcElasticSearchIndexResources.IP_B_ORDER_LOCK_TYPE_NAME, esLockList);
//        } catch (Exception ex) {
//
//
//        }
    }

    @Test
    public void testDealLockOrderLockTime() {
        String errMsg = "调用订单拦截服务成功：";
        lockService.insetIpOrderLockLog(OrderLockLogTypeEnum.LOCK.getKey(), errMsg, 1L, null, SystemUserResource.getRootUser());
        // lockService.insetIpOrderLockLog("1","11",1L,1L, SystemUserResource.getRootUser());
//        ValueHolderV14 v14 = queryService.dealLockOrderLockTime(10);

//        IpOrderLockRelation ipOrderLockRelation = ipOrderLockService.getLockRelation(37L);
//        lockDetailProcessor.start(ipOrderLockRelation, false, SystemUserResource.getRootUser());

    }

    @Test
    public void testTask() {

        lockQueryService.dealLockOrderLockTime(1000);
//        String ids = "11,2,10,3";
//        //ids =  ids.replace(",", "','");
//        int update = lockMapper.updateLockOrderByBatchSQL(ids, 1, LockOrderConstant.ABLE_UNLOCK);
    }

    @Test
    public void testTask2() {
        //refundInService.dealWaitMatchRefundInException(200);
//        ReturnWrongAdjustmentServer sertver = ApplicationContextHandle.getBean(ReturnWrongAdjustmentServer.class);
        //sertver.testzxlcheshi();
//        sertver.execLogic(200);
        List<Long> idList = Lists.newArrayList();
        idList.add(13L);
        List<IpOrderLockRelation> ipOrderLockRelationList = new ArrayList<>();
        for (Long id : idList) {
            IpOrderLockRelation orderLockRelation = ipOrderLockService.getLockRelation(id);
            if (orderLockRelation == null) {
                String errorMessage = Resources.getMessage("###AutoOrderUnlockTask.Order.NotExist!###Id=" + id);
            } else {
                ipOrderLockRelationList.add(orderLockRelation);
            }
            orderUnlockProcessImpl.start(orderLockRelation, false, SystemUserResource.getRootUser());
        }
    }

    @Test
    public void queryTest() {
        String query = "{\"start\":1,\"count\":15,\"RETURN_STATUS\":\"\",\"RECEIVE_PROVINCE\":\"\",\"RECEIVE_PROVINCE_ID\":\"\",\"ORIG_SOURCE_CODE\":\"\",\"ID\":\"\",\"LOGISTICS_CODE\":\"\",\"ORIG_ORDER_ID\":\"\",\"INVENTED_STATUS\":[],\"TB_DISPUTE_ID\":\"\",\"BUYER_NICK\":\"\",\"RETURN_ID\":\"\",\"REMARK\":\"\",\"BILL_TYPE\":[],\"IS_TOWMS\":[],\"WMS_CANCEL_STATUS\":[],\"IS_ADD\":[],\"RESERVE_BIGINT07\":[],\"IS_TOAG\":[],\"CONSIGN_AMT_SETTLE\":\"\",\"CREATIONDATE\":[\"\",\"\"],\"RECEIVE_MOBILE\":\"\",\"IN_TIME\":[\"\",\"\"],\"AUDIT_TIME\":[\"\",\"\"],\"RETURN_CREATE_TIME\":[\"\",\"\"],\"PS_C_PRO_ECODE\":\"\",\"PS_C_SKU_ECODE\":\"\",\"RESERVE_BIGINT04\":\"\",\"IS_BACK\":[],\"CHANNEL_TYPE_ID\":[\"1\"]}";
        JSONObject object = JSONObject.parseObject(query);
        ValueHolderV14 v = refundingService.queryChangingOrRefunding(null, object, SystemUserResource.getRootUser());
//        String query="{\"page\":{\"pageSize\":20,\"pageNum\":1},\"label\":[],\"queryInfo\":[],\"status\":{\"label\":\"全部\",\"value\":\"0\",\"isShow\":true},\"highSearch\":[{\"type\":\"Select\",\"queryName\":\"CP_C_SHOP_ID\",\"value\":\"18\"},{\"type\":\"Select\",\"queryName\":\"CP_C_PHY_WAREHOUSE_ID\",\"value\":\"\"},{\"type\":\"Select\",\"queryName\":\"CP_C_REGION_PROVINCE_ID\",\"value\":\"\"},{\"type\":\"Select\",\"queryName\":\"CP_C_REGION_CITY_ID\",\"value\":\"\"},{\"type\":\"Select\",\"queryName\":\"CP_C_REGION_AREA_ID\",\"value\":\"\"},{\"type\":\"Select\",\"queryName\":\"CP_C_LOGISTICS_ID\",\"value\":\"\"},{\"type\":\"Select\",\"queryName\":\"CP_C_CUSTOMER_ID\",\"value\":\"\"},{\"type\":\"Select\",\"queryName\":\"OWNERID\",\"value\":\"\"},{\"type\":\"Select\",\"queryName\":\"ORDER_STATUS\",\"value\":\"\"},{\"type\":\"Select\",\"queryName\":\"RETURN_STATUS\",\"value\":\"\"},{\"type\":\"Input\",\"queryName\":\"SOURCE_CODE\",\"value\":\"\"},{\"type\":\"Input\",\"queryName\":\"ID\",\"value\":\"\"},{\"type\":\"Select\",\"queryName\":\"RESERVE_BIGINT03\",\"value\":\"\"},{\"type\":\"Select\",\"queryName\":\"RESERVE_BIGINT02\",\"value\":\"\"},{\"type\":\"date\",\"queryName\":\"ORDER_DATE\",\"value\":\"\"},{\"type\":\"Input\",\"queryName\":\"BILL_NO\",\"value\":\"\"},{\"type\":\"Select\",\"queryName\":\"ORDER_TYPE\",\"value\":\"\"},{\"type\":\"Input\",\"queryName\":\"EXPRESSCODE\",\"value\":\"\"},{\"type\":\"Input\",\"queryName\":\"RECEIVER_NAME\",\"value\":\"\"},{\"type\":\"Input\",\"queryName\":\"RECEIVER_MOBILE\",\"value\":\"\"},{\"type\":\"Input\",\"queryName\":\"SYSREMARK\",\"value\":\"\"},{\"type\":\"Select\",\"queryName\":\"PAY_TYPE\",\"value\":\"\"},{\"type\":\"date\",\"queryName\":\"PAY_TIME\",\"value\":\"\"},{\"type\":\"date\",\"queryName\":\"CREATIONDATE\",\"value\":\"\"},{\"type\":\"Select\",\"queryName\":\"WMS_CANCEL_STATUS\",\"value\":\"\"},{\"type\":\"date\",\"queryName\":\"AUDIT_TIME\",\"value\":\"\"},{\"type\":\"Input\",\"queryName\":\"PS_C_PRO_ECODE\",\"value\":\"\"},{\"type\":\"date\",\"queryName\":\"DISTRIBUTION_TIME\",\"value\":\"\"},{\"type\":\"date\",\"queryName\":\"SCAN_TIME\",\"value\":\"\"},{\"type\":\"Input\",\"queryName\":\"BUYER_MESSAGE\",\"value\":\"\"},{\"type\":\"Input\",\"queryName\":\"SELLER_MEMO\",\"value\":\"\"},{\"type\":\"Input\",\"queryName\":\"QTY_ALL\",\"value\":\"~\"},{\"type\":\"Input\",\"queryName\":\"USER_NICK\",\"value\":\"\"},{\"type\":\"Input\",\"queryName\":\"PS_C_SKU_ECODE\",\"value\":\"\"},{\"type\":\"Input\",\"queryName\":\"ORDER_AMT\",\"value\":\"~\"},{\"type\":\"Select\",\"queryName\":\"PLATFORM\",\"value\":\"\"},{\"type\":\"Select\",\"queryName\":\"INVOICE_STATUS\",\"value\":\"\"},{\"type\":\"Select\",\"queryName\":\"RESERVE_BIGINT09\",\"value\":\"\"},{\"type\":\"Select\",\"queryName\":\"RESERVE_VARCHAR03\",\"value\":\"\"},{\"type\":\"Select\",\"queryName\":\"CHANNEL_TYPE_ID\",\"value\":\"1\"}]}"; // "// JSONObject object = JSONObject.parseObject(query);
//
//        ValueHolderV14<QueryOrderListResult> v= orderListQueryService.queryOrderList(query, SystemUserResource.getRootUser(),null);


    }

    @Test
    public void importExcel() {
        String param = "{\"page\":{\"pageSize\":999999,\"pageNum\":1},\"label\":[],\"queryInfo\":[],\"status\":{\"label\":\"全部\",\"value\":\"0\",\"isShow\":true},\"highSearch\":[{\"type\":\"Select\",\"queryName\":\"CP_C_SHOP_ID\",\"value\":\"\"},{\"type\":\"Select\",\"queryName\":\"CP_C_PHY_WAREHOUSE_ID\",\"value\":\"\"},{\"type\":\"Select\",\"queryName\":\"CP_C_REGION_PROVINCE_ID\",\"value\":\"\"},{\"type\":\"Select\",\"queryName\":\"CP_C_REGION_CITY_ID\",\"value\":\"\"},{\"type\":\"Select\",\"queryName\":\"CP_C_REGION_AREA_ID\",\"value\":\"\"},{\"type\":\"Select\",\"queryName\":\"CP_C_LOGISTICS_ID\",\"value\":\"\"},{\"type\":\"Select\",\"queryName\":\"CP_C_CUSTOMER_ID\",\"value\":\"\"},{\"type\":\"Select\",\"queryName\":\"OWNERID\",\"value\":\"\"},{\"type\":\"Select\",\"queryName\":\"ORDER_STATUS\",\"value\":\"2\"},{\"type\":\"Select\",\"queryName\":\"RETURN_STATUS\",\"value\":\"\"},{\"type\":\"Input\",\"queryName\":\"SOURCE_CODE\",\"value\":\"\"},{\"type\":\"Input\",\"queryName\":\"ID\",\"value\":\"\"},{\"type\":\"Select\",\"queryName\":\"RESERVE_BIGINT03\",\"value\":\"\"},{\"type\":\"Select\",\"queryName\":\"RESERVE_BIGINT02\",\"value\":\"\"},{\"type\":\"date\",\"queryName\":\"ORDER_DATE\",\"value\":\"\"},{\"type\":\"Input\",\"queryName\":\"BILL_NO\",\"value\":\"\"},{\"type\":\"Select\",\"queryName\":\"ORDER_TYPE\",\"value\":\"\"},{\"type\":\"Input\",\"queryName\":\"EXPRESSCODE\",\"value\":\"\"},{\"type\":\"Input\",\"queryName\":\"RECEIVER_NAME\",\"value\":\"\"},{\"type\":\"Input\",\"queryName\":\"RECEIVER_MOBILE\",\"value\":\"\"},{\"type\":\"Input\",\"queryName\":\"SYSREMARK\",\"value\":\"\"},{\"type\":\"Select\",\"queryName\":\"PAY_TYPE\",\"value\":\"\"},{\"type\":\"date\",\"queryName\":\"PAY_TIME\",\"value\":\"\"},{\"type\":\"date\",\"queryName\":\"CREATIONDATE\",\"value\":\"\"},{\"type\":\"Select\",\"queryName\":\"WMS_CANCEL_STATUS\",\"value\":\"\"},{\"type\":\"date\",\"queryName\":\"AUDIT_TIME\",\"value\":\"\"},{\"type\":\"Input\",\"queryName\":\"PS_C_PRO_ECODE\",\"value\":\"\"},{\"type\":\"date\",\"queryName\":\"DISTRIBUTION_TIME\",\"value\":\"\"},{\"type\":\"date\",\"queryName\":\"SCAN_TIME\",\"value\":\"\"},{\"type\":\"Input\",\"queryName\":\"BUYER_MESSAGE\",\"value\":\"\"},{\"type\":\"Input\",\"queryName\":\"SELLER_MEMO\",\"value\":\"\"},{\"type\":\"Input\",\"queryName\":\"QTY_ALL\",\"value\":\"~\"},{\"type\":\"Input\",\"queryName\":\"USER_NICK\",\"value\":\"\"},{\"type\":\"Input\",\"queryName\":\"PS_C_SKU_ECODE\",\"value\":\"\"},{\"type\":\"Input\",\"queryName\":\"ORDER_AMT\",\"value\":\"~\"},{\"type\":\"Select\",\"queryName\":\"PLATFORM\",\"value\":\"\"},{\"type\":\"Select\",\"queryName\":\"INVOICE_STATUS\",\"value\":\"\"},{\"type\":\"Select\",\"queryName\":\"RESERVE_BIGINT09\",\"value\":\"\"},{\"type\":\"Select\",\"queryName\":\"CHANNEL_TYPE_ID\",\"value\":\"1\"},{\"type\":\"Select\",\"queryName\":\"RESERVE_VARCHAR03\",\"value\":\"\"}]}";

        ValueHolderV14 v14 = batchExportOcBOrderService.mainProgram(param, SystemUserResource.getRootUser(), null, null);
    }

}
