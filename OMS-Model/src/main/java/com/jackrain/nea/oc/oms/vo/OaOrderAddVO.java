package com.jackrain.nea.oc.oms.vo;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @ClassName OaOrderAddVO
 * @Description OA订单创建
 * <AUTHOR>
 * @Date 2024/1/23 14:50
 * @Version 1.0
 */
@Data
public class OaOrderAddVO implements Serializable {
    private static final long serialVersionUID = -7788965161188621865L;

    @NotEmpty(message = "下单店铺不能为空")
    private String cpCShopTitle;

    @NotEmpty(message = "平台单号不能为空")
    private String tid;

    @NotEmpty(message = "收货人姓名不能为空")
    private String receiverName;

    @NotEmpty(message = "收货人手机不能为空")
    private String receiverMobile;

    @NotEmpty(message = "收货人省不能为空")
    private String cpCRegionProvinceEname;

    @NotEmpty(message = "收货人市不能为空")
    private String cpCRegionCityEname;

    @NotEmpty(message = "收货人区不能为空")
    private String cpCRegionAreaEname;

    @NotEmpty(message = "收件人详细地址不能为空")
    private String receiverAddress;

    @NotEmpty(message = "商品编码不能为空")
    private String psCSkuEcode;

    @NotNull(message = "数量不能为空")
    private Integer qty;

    /**
     * 地址是否明文 0:否；1:是
     */
    private Integer isPlainAddr;

    /**
     * 成交单价
     */
    private BigDecimal priceActual;

    /**
     * 是否赠品
     */
    private Integer isGift;

    /**
     * 创建人
     */
    private String ownername;

}
