package com.jackrain.nea.oc.oms.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.oc.oms.model.table.OcBRefundIn;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInProductItem;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Desc : 退货入库单域模型
 * <AUTHOR> xiWen
 * @Date : 2020/11/12
 */
@Data
public class OmsRefundInRequest implements Serializable {
    private static final long serialVersionUID = 5856780929489029921L;


    @JSONField(name = "OC_B_REFUND_IN")
    private OcBRefundIn refundIn;

    @JSONField(name = "OC_B_REFUND_IN_PRODUCT_ITEM")
    private List<OcBRefundInProductItem> productItems;
}
