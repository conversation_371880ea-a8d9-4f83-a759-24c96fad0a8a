package com.jackrain.nea.oc.services;

import cn.hutool.core.util.ObjectUtil;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.services.RegionNewService;
import com.jackrain.nea.cpext.model.ProvinceCityAreaInfo;
import com.jackrain.nea.cpext.model.RegionInfo;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.oc.oms.api.OcBOrderImportCmd;
import com.jackrain.nea.oc.oms.api.OcBPreOrderImportCmd;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBPreOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBPreOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.result.OcBPreOrderModelResult;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBPreOrder;
import com.jackrain.nea.oc.oms.model.table.OcBPreOrderItem;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.oc.oms.vo.OcBOrderPreImpVO;
import com.jackrain.nea.oc.oms.vo.OcBPreOrderRelationVO;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.st.service.OmsStCShopStrategyService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.AddressResolutionUtils;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName OcBPreOrderImportCmdImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/11/16 13:57
 * @Version 1.0
 */
@Slf4j
@Component
public class OcBPreOrderImportCmdImpl implements OcBPreOrderImportCmd {

    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private PsRpcService psRpcService;
    @Autowired
    private OmsStCShopStrategyService omsStCShopStrategyService;
    @Autowired
    private AddressResolutionUtils addressResolutionUtils;
    @Autowired
    private RegionNewService regionNewService;
    @Autowired
    private BuildSequenceUtil sequenceUtil;
    @Autowired
    private OcBPreOrderMapper ocBPreOrderMapper;
    @Autowired
    private OcBPreOrderItemMapper ocBPreOrderItemMapper;
    @Autowired
    private RedisOpsUtil<String, String> redisOpsUtil;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private OcBOrderImportCmd ocBOrderImportCmd;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private ThreadPoolTaskExecutor preOrderImportThreadPoolExecutor;
    @Autowired
    private StRpcService stRpcService;

    /**
     * 获取当天结束还剩余多少秒
     *
     * @return
     */
    public static int getSeconds() {
        //获取今天当前时间
        Calendar curDate = Calendar.getInstance();
        //获取明天凌晨0点的⽇期
        Calendar tommorowDate = new GregorianCalendar(
                curDate.get(Calendar.YEAR),
                curDate.get(Calendar.MONTH),
                curDate.get(Calendar.DATE) + 1,
                0, 0, 0);
        //返回明天凌晨0点和今天当前时间的差值（秒数）
        return (int) (tommorowDate.getTimeInMillis() - curDate.getTimeInMillis()) / 1000;
    }


    @Override
    public ValueHolderV14 importPreOrder(List<OcBOrderPreImpVO> ocBOrderPreImpVOS, User user, String modelCode) {
        ValueHolderV14 valueHolderV14 = new ValueHolderV14();
        valueHolderV14.setCode(ResultCode.SUCCESS);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        String dateStr = simpleDateFormat.format(new Date());
        String serialNumber = dateStr + getSerialNumber();

        // 先根据excel中的店铺名称、商品编码 查询出来所有的店铺、商品编码 以防后面每次都要去查询
        List<String> cpCShopTitleList = ocBOrderPreImpVOS.stream().map(OcBOrderPreImpVO::getCpCShopTitle).distinct().collect(Collectors.toList());
        List<String> skuCodeList = ocBOrderPreImpVOS.stream().map(OcBOrderPreImpVO::getPsCSkuEcode).distinct().collect(Collectors.toList());
        Map<String, CpShop> cpShopMap = getShopInfoMap(cpCShopTitleList);
        Map<String, ProductSku> productSkuMap = getProductMap(skuCodeList);

        Map<String, List<OcBOrderPreImpVO>> preImpVoMap = ocBOrderPreImpVOS.stream().collect(Collectors.groupingBy(OcBOrderPreImpVO::getSourceCode));
        List<OcBPreOrderRelationVO> ocBPreOrderRelationVOList = new ArrayList<>();
        for (String tid : preImpVoMap.keySet()) {
            OcBPreOrderRelationVO relationVO = new OcBPreOrderRelationVO();
            relationVO.setTid(tid);
            relationVO.setOcBOrderPreImpVOS(preImpVoMap.get(tid));
            ocBPreOrderRelationVOList.add(relationVO);
        }

        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
        int maxImportSize = config.getProperty("r3.oc.oms.import.order.max.qty", 5000);
        int pageSize = maxImportSize / 20;
        List<List<OcBPreOrderRelationVO>> baseModelPageList = Lists.partition(ocBPreOrderRelationVOList, pageSize);
        List<OcBOrderPreImpVO> ocBOrderPreImpErrorList = new ArrayList<>();

        try {
            List<Future<ValueHolderV14<List<OcBOrderPreImpVO>>>> results = new ArrayList<>();
            for (List<OcBPreOrderRelationVO> ocBOrderExtends : baseModelPageList) {
                results.add(preOrderImportThreadPoolExecutor.submit(new CallableBuildOcBOrder(ocBOrderExtends, cpShopMap, productSkuMap, serialNumber)));
            }
            for (Future<ValueHolderV14<List<OcBOrderPreImpVO>>> futureResult : results) {
                try {
                    ValueHolderV14<List<OcBOrderPreImpVO>> result = futureResult.get();
                    if (!result.isOK()) {
                        ocBOrderPreImpErrorList.addAll(result.getData());
                    }
                } catch (InterruptedException e) {
                    log.error(LogUtil.format("订单预导入多线程获取InterruptedException异常：{}"),
                            Throwables.getStackTraceAsString(e));
                    Thread.currentThread().interrupt();
                } catch (ExecutionException e) {
                    log.error(LogUtil.format("订单预导入多线程获取ExecutionException异常：{}"), Throwables.getStackTraceAsString(e));
                }
            }
        } catch (Exception e) {
            log.error(LogUtil.format("订单预导入异常，异常信息,error：{}"), Throwables.getStackTraceAsString(e));
        }
        if (CollectionUtils.isNotEmpty(ocBOrderPreImpErrorList)) {
            valueHolderV14.setData(ocBOrderImportCmd.exportPreImpErrorResult(ocBOrderPreImpErrorList, user, modelCode));
            valueHolderV14.setCode(ResultCode.FAIL);
        }
        return valueHolderV14;
    }

    @Override
    public ValueHolderV14 getAllModel() {
        ValueHolderV14 valueHolderV14 = new ValueHolderV14(new ArrayList<>(), ResultCode.SUCCESS, "success");
        try {
            List<OcBPreOrderModelResult> resultList = stRpcService.getAllModel();
            valueHolderV14.setData(resultList);
        } catch (Exception e) {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage(e.getMessage());
        }
        return valueHolderV14;
    }

    /**
     * 查询订单明细对应的所有SKu对应的商品信息
     *
     * @param skuCodes
     * @return
     */
    private Map<String, ProductSku> getProductMap(List<String> skuCodes) {
        Map<String, ProductSku> proSkuMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(skuCodes)) {
            List<ProductSku> proSkus = new ArrayList<>();
            for (String skuCode : skuCodes) {
                ProductSku productSku = psRpcService.selectProductSku(skuCode);
                if (productSku != null) {
                    proSkus.add(productSku);
                }
            }
            if (CollectionUtils.isNotEmpty(proSkus)) {
                proSkuMap = proSkus.stream().collect(Collectors.toMap(ProductSku::getSkuEcode, sku -> sku, (v1, v2) -> v1));
            }
        }
        return proSkuMap;
    }

    /**
     * 查询所有订单对应的店铺信息
     *
     * @param cpCShopTitleList
     * @return
     */
    private Map<String, CpShop> getShopInfoMap(List<String> cpCShopTitleList) {
        Map<String, CpShop> cpShopMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(cpCShopTitleList)) {
            List<CpShop> cpShopList = cpRpcService.queryByShopTitle(cpCShopTitleList);
            if (CollectionUtils.isNotEmpty(cpShopList)) {
                cpShopMap = cpShopList.stream().collect(Collectors.toMap(CpShop::getCpCShopTitle, Function.identity(), (v1, v2) -> v1));
            }
        }
        return cpShopMap;
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchInsert(List<OcBPreOrder> insertOcBOrder, List<OcBPreOrderItem> insertOcBOrderItem) {
        ocBPreOrderMapper.batchInsert(insertOcBOrder);
        ocBPreOrderItemMapper.batchInsert(insertOcBOrderItem);
    }

    class CallableBuildOcBOrder implements Callable<ValueHolderV14<List<OcBOrderPreImpVO>>> {

        List<OcBPreOrderRelationVO> ocBPreOrderRelationVOList;
        Map<String, CpShop> cpShopMap;
        Map<String, ProductSku> productSkuMap;
        String serialNumber;


        public CallableBuildOcBOrder(List<OcBPreOrderRelationVO> ocBPreOrderRelationVOList, Map<String, CpShop> cpShopMap, Map<String, ProductSku> productSkuMap, String serialNumber) {
            this.ocBPreOrderRelationVOList = ocBPreOrderRelationVOList;
            this.cpShopMap = cpShopMap;
            this.productSkuMap = productSkuMap;
            this.serialNumber = serialNumber;
        }

        @Override
        public ValueHolderV14<List<OcBOrderPreImpVO>> call() {
            ValueHolderV14<List<OcBOrderPreImpVO>> valueHolderV14 = new ValueHolderV14<>();
            List<OcBOrderPreImpVO> ocBOrderPreImpErrorList = new ArrayList<>();
            List<OcBOrderPreImpVO> allOcBOrderPreImpVO = new ArrayList<>();
            // 先走一波校验 校验通过的再进行数据处理
            for (OcBPreOrderRelationVO relationVO : ocBPreOrderRelationVOList) {
                List<OcBOrderPreImpVO> ocBOrderPreImpVOS = relationVO.getOcBOrderPreImpVOS();
                String tid = relationVO.getTid();
                // 校验平台单号是否有在系统中存在
                List<OcBOrder> ocBOrderList = ocBOrderMapper.getOrdersUnionGsiBySourceCode(tid);
                // 1、 先判断同一个tid 对应的店铺是不是想同的
                for (OcBOrderPreImpVO ocBOrderPreImpVO : ocBOrderPreImpVOS) {
                    StringBuilder sb = new StringBuilder();
                    if (CollectionUtils.isNotEmpty(ocBOrderList)) {
                        sb.append(",平台单号在系统中已存在");
                    } else {
                        OcBPreOrder ocBPreOrder = ocBPreOrderMapper.getByTid(tid);
                        if (ObjectUtil.isNotNull(ocBPreOrder)) {
                            sb.append(",平台单号在系统中已存在");
                        }
                    }
                    // 校验店铺
                    CpShop cpShop = cpShopMap.get(ocBOrderPreImpVO.getCpCShopTitle());
                    if (ObjectUtil.isNull(cpShop)) {
                        sb.append(",店铺信息有误");
                    } else {
                        StCShopStrategyDO shopStrategyDO = omsStCShopStrategyService.selectOcStCShopStrategy(cpShop.getCpCShopId());
                        if (ObjectUtil.isNull(shopStrategyDO) || !YesNoEnum.Y.getKey().equals(shopStrategyDO.getIsManuallyCreate())) {
                            sb.append(",当前店铺未开启手工建单");
                        }
                    }
                    // sku校验
                    ProductSku productSku = productSkuMap.get(ocBOrderPreImpVO.getPsCSkuEcode().toUpperCase());
                    if (ObjectUtil.isNull(productSku)) {
                        sb.append(",商品编码不存在");
                    }
                    // 省市区校验
                    // 省名称或者市名称为空 ,就从详细地址中解析
                    if ((StringUtils.isBlank(ocBOrderPreImpVO.getCpCRegionProvinceEname())
                            || StringUtils.isBlank(ocBOrderPreImpVO.getCpCRegionCityEname()))
                            && StringUtils.isNotBlank(ocBOrderPreImpVO.getReceiverAddress())) {
                        Map<String, String> map = new HashMap<>();
                        try {
                            map = addressResolutionUtils.addressResolutionNew(ocBOrderPreImpVO.getReceiverAddress());
                        } catch (Exception e) {
                            sb.append("行政区域解析失败,请检查");
                        }
                        ocBOrderPreImpVO.setCpCRegionProvinceEname(map.get("province"));
                        ocBOrderPreImpVO.setCpCRegionCityEname(map.get("city"));
                        ocBOrderPreImpVO.setCpCRegionAreaEname(map.get("area"));
                    }

                    //新增根据省市区名称查询省市区的信息
                    if (StringUtils.isEmpty(sb.toString()) || !sb.toString().contains("行政区域解析失败,请检查")) {
                        ProvinceCityAreaInfo provinceCityAreaInfo = regionNewService.selectNewProvinceCityAreaInfo(
                                ocBOrderPreImpVO.getCpCRegionProvinceEname(), ocBOrderPreImpVO.getCpCRegionCityEname(), ocBOrderPreImpVO.getCpCRegionAreaEname());
                        RegionInfo province = provinceCityAreaInfo.getProvinceInfo();
                        if (ObjectUtil.isNull(province)) {
                            sb.append(",省不存在!请检查!");
                        }
                        //开始赋值市的信息
                        RegionInfo city = provinceCityAreaInfo.getCityInfo();
                        if (ObjectUtil.isNull(city)) {
                            sb.append(",市不存在!请检查!");
                        }
                    }

                    if (StringUtils.isNotEmpty(sb.toString())) {
                        ocBOrderPreImpVO.setErrorMsg(sb.toString());
                        ocBOrderPreImpErrorList.add(ocBOrderPreImpVO);
                    }
                }
                allOcBOrderPreImpVO.addAll(ocBOrderPreImpVOS);
            }

            // 可能存在同一笔订单两个明细 一个明细有异常 另外一个明细校验通过的场景
            if (CollectionUtils.isNotEmpty(ocBOrderPreImpErrorList)) {
                Map<String, List<OcBOrderPreImpVO>> errorMap = ocBOrderPreImpErrorList.stream().collect(Collectors.groupingBy(OcBOrderPreImpVO::getSourceCode));
                Map<String, List<OcBOrderPreImpVO>> preImpVoMap = allOcBOrderPreImpVO.stream().collect(Collectors.groupingBy(OcBOrderPreImpVO::getSourceCode));
                for (String tid : errorMap.keySet()) {
                    List<OcBOrderPreImpVO> preImpVOList = preImpVoMap.get(tid);
                    List<OcBOrderPreImpVO> errorList = errorMap.get(tid);
                    preImpVOList.removeAll(errorList);
                    if (CollectionUtils.isNotEmpty(preImpVOList)) {
                        for (OcBOrderPreImpVO ocBOrderPreImpVO : preImpVOList) {
                            ocBOrderPreImpVO.setErrorMsg("同订单存在其他异常明细");
                        }
                        ocBOrderPreImpErrorList.addAll(preImpVOList);
                    }
                }
            }

            // 先删除不能插入到数据库的 注意 这个时间ocBOrderPreImpVOS数据已经改变了
            allOcBOrderPreImpVO.removeAll(ocBOrderPreImpErrorList);

            // 删除异常订单后的订单(需要插入到数据库的)
            Map<String, List<OcBOrderPreImpVO>> preImpVoMapNew = allOcBOrderPreImpVO.stream().collect(Collectors.groupingBy(OcBOrderPreImpVO::getSourceCode));

            List<OcBPreOrder> insertOcBOrder = new ArrayList<>();
            List<OcBPreOrderItem> insertOcBOrderItem = new ArrayList<>();

            for (String tid : preImpVoMapNew.keySet()) {
                List<OcBOrderPreImpVO> ocBOrderPreImpVOList = preImpVoMapNew.get(tid);
                // 获取一条 用来做主数据
                OcBOrderPreImpVO ocBOrderPreImpVO = ocBOrderPreImpVOList.get(0);
                CpShop cpShop = cpShopMap.get(ocBOrderPreImpVO.getCpCShopTitle());

                buildOrderAndItem(insertOcBOrder, insertOcBOrderItem, tid, ocBOrderPreImpVOList, ocBOrderPreImpVO, cpShop);
            }
            if (CollectionUtils.isNotEmpty(insertOcBOrder) && CollectionUtils.isNotEmpty(insertOcBOrderItem)) {
                applicationContext.getBean(OcBPreOrderImportCmdImpl.class).batchInsert(insertOcBOrder, insertOcBOrderItem);
            }
            if (CollectionUtils.isNotEmpty(ocBOrderPreImpErrorList)) {
                valueHolderV14.setCode(ResultCode.FAIL);
                valueHolderV14.setData(ocBOrderPreImpErrorList);
            } else {
                valueHolderV14.setCode(ResultCode.SUCCESS);
            }
            return valueHolderV14;
        }

        private void buildOrderAndItem(List<OcBPreOrder> insertOcBOrder, List<OcBPreOrderItem> insertOcBOrderItem, String tid, List<OcBOrderPreImpVO> ocBOrderPreImpVOList,
                                       OcBOrderPreImpVO ocBOrderPreImpVO, CpShop cpShop) {
            Long id = sequenceUtil.buildPreOrderSequenceId();
            OcBPreOrder ocBPreOrder = new OcBPreOrder();
            ocBPreOrder.setOrderDate(ocBOrderPreImpVO.getOrderDate());
            ocBPreOrder.setBuyerMessage(ocBOrderPreImpVO.getBuyerMessage());
            ocBPreOrder.setCpCRegionAreaEname(ocBOrderPreImpVO.getCpCRegionAreaEname());
            ocBPreOrder.setCpCRegionCityEname(ocBOrderPreImpVO.getCpCRegionCityEname());
            ocBPreOrder.setCpCRegionProvinceEname(ocBOrderPreImpVO.getCpCRegionProvinceEname());
            ocBPreOrder.setCpCShopTitle(ocBOrderPreImpVO.getCpCShopTitle());
            ocBPreOrder.setCpCShopId(cpShop.getId());
            ocBPreOrder.setCpCShopEcode(cpShop.getEcode());
            ocBPreOrder.setOaid(ocBOrderPreImpVO.getOaid());
            ocBPreOrder.setPayTime(ocBOrderPreImpVO.getPayTime());
            ocBPreOrder.setSellerMemo(ocBOrderPreImpVO.getSellerMemo());
            ocBPreOrder.setTid(ocBOrderPreImpVO.getSourceCode());
            ocBPreOrder.setReceiverName(ocBOrderPreImpVO.getReceiverName());
            ocBPreOrder.setReceiverPhone(ocBOrderPreImpVO.getReceiverPhone());
            ocBPreOrder.setReceiverAddress(ocBOrderPreImpVO.getReceiverAddress());
            ocBPreOrder.setReceiverMobile(ocBOrderPreImpVO.getReceiverMobile());
            ocBPreOrder.setReceiverZip(ocBOrderPreImpVO.getReceiverZip());
            ocBPreOrder.setUserNick(ocBOrderPreImpVO.getUserNick());
            ocBPreOrder.setShipAmt(ocBOrderPreImpVO.getShipAmt() == null ? BigDecimal.ZERO : ocBOrderPreImpVO.getShipAmt());
            ocBPreOrder.setSalesmanName(ocBOrderPreImpVO.getSalesmanName());
            ocBPreOrder.setRowNum(ocBOrderPreImpVO.getRowNum());
            ocBPreOrder.setId(id);
            ocBPreOrder.setTransferStatus(0);
            ocBPreOrder.setSerialNumber(serialNumber);
            ocBPreOrder.setPriceActual(ocBOrderPreImpVO.getPriceActual());
            ocBPreOrder.setPayType(ocBOrderPreImpVO.getPayType());
            BaseModelUtil.initialBaseModelSystemField(ocBPreOrder);
            insertOcBOrder.add(ocBPreOrder);
            BigDecimal qtyAll = BigDecimal.ZERO;
            BigDecimal priceActual = BigDecimal.ZERO;
            BigDecimal platformPrice = BigDecimal.ZERO;

            for (OcBOrderPreImpVO ocBOrderPreImp : ocBOrderPreImpVOList) {
                OcBPreOrderItem ocBPreOrderItem = new OcBPreOrderItem();
                BaseModelUtil.initialBaseModelSystemField(ocBPreOrderItem);
                ocBPreOrderItem.setId(sequenceUtil.buildPreOrderItemSequenceId());
                ocBPreOrderItem.setOcBPreOrderId(id);
                ocBPreOrderItem.setTid(tid);
                ocBPreOrderItem.setPriceActual(ocBOrderPreImp.getPriceActual());
                ocBPreOrderItem.setPlatformPrice(ocBOrderPreImp.getPlatformPrice());
                ocBPreOrderItem.setQty(ocBOrderPreImp.getQty());
                ocBPreOrderItem.setPsCSkuEcode(ocBOrderPreImp.getPsCSkuEcode().toUpperCase());
                ocBPreOrderItem.setSerialNumber(serialNumber);
                ocBPreOrderItem.setIsGift(ocBOrderPreImp.getIsGift());
                qtyAll = qtyAll.add(ocBPreOrderItem.getQty());
                priceActual = priceActual.add(ocBPreOrderItem.getPriceActual().multiply(ocBPreOrderItem.getQty()));
                platformPrice = platformPrice.add(ocBPreOrderItem.getPlatformPrice() == null ? BigDecimal.ZERO : ocBPreOrderItem.getPlatformPrice().multiply(ocBPreOrderItem.getQty()));
                insertOcBOrderItem.add(ocBPreOrderItem);
            }
            priceActual = priceActual.add(ocBPreOrder.getShipAmt());
            ocBPreOrder.setQtyAll(qtyAll);
            ocBPreOrder.setPriceActual(priceActual);
            ocBPreOrder.setPlatformPrice(platformPrice);
        }
    }

    public String getSerialNumber() {
        String serialNumber = redisOpsUtil.strRedisTemplate.opsForValue().get("OcBPreOrderSerialNumber");
        log.info("OcBPreOrderSerialNumber serialNumber:{}, getSeconds:{}", serialNumber, getSeconds());
        if (org.apache.commons.lang.StringUtils.isEmpty(serialNumber)) {
            redisOpsUtil.strRedisTemplate.opsForValue().set("OcBPreOrderSerialNumber", "10000", getSeconds(), TimeUnit.SECONDS);
            serialNumber = "10000";
        } else {
            Long rsbsSerialNumber = redisOpsUtil.strRedisTemplate.opsForValue().increment("OcBPreOrderSerialNumber", 1L);
            serialNumber = String.valueOf(rsbsSerialNumber);
        }
        log.info(LogUtil.format("OcBPreOrderSerialNumber.getSerialNumber:{}",
                "serialNumber"), serialNumber);
        return serialNumber;
    }
}
