package com.jackrain.nea.oc.services;

import com.jackrain.nea.oc.oms.api.AcFPriceSettingSaveCmd;
import com.jackrain.nea.oc.oms.model.table.AcFPriceSetting;
import com.jackrain.nea.oc.oms.services.AcFPriceSettingService;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.util.ValueHolder;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * className: AcFPriceSettingSaveCmdImpl
 * description: 全渠道价格配置表
 *
 * <AUTHOR>
 * create: 2021-10-27
 * @since JDK 1.8
 */
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class AcFPriceSettingSaveCmdImpl implements AcFPriceSettingSaveCmd {

    @Autowired
    private AcFPriceSettingService settingService;

    @Override
    public ValueHolder save(AcFPriceSetting priceSetting) {
        return settingService.save(priceSetting, SystemUserResource.getRootUser());
    }
}
