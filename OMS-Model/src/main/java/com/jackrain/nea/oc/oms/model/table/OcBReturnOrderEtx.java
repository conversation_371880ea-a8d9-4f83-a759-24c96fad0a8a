package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: 夏继超
 * @since: 2019/4/9
 * create at : 2019/4/9 13:29
 */
@Data
public class OcBReturnOrderEtx extends OcBReturnOrder {
    //实体仓的名字
    @JSONField(name = "SHOPNAME")
    String shopName;
    //退货入库明细
    @JSONField(name = "PRODUCTITEMS")
    List<OcBReturnOrderRefund> productItems = new ArrayList<>();
}
