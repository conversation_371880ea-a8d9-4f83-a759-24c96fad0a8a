package com.jackrain.nea.oc.oms.model.enums;

/**
 * WMS撤销状态（1已撤销，0未撤销）
 *
 * @author: 胡林洋
 * @since: 2019-03-12
 * create at : 2019-03-12 18:52
 */
public enum OmsWmsCancelStatus {
    /**
     * 1已撤销【撤销成功】
     */
    OMS_WMS_CANCEL_STATUS_YES,

    /**
     * 0未撤销
     */
    OMS_WMS_CANCEL_STATUS_NO,

    /**
     * 撤销失败
     */
    OMS_WMS_CANCEL_STATUS_FAIL;

    public int toInteger() {
        if (this == OMS_WMS_CANCEL_STATUS_YES) {
            return 1;
        } else if (this == OMS_WMS_CANCEL_STATUS_NO) {
            return 0;
        } else if (this == OMS_WMS_CANCEL_STATUS_FAIL) {
            return 2;
        } else {
            return 9;
        }
    }

}
