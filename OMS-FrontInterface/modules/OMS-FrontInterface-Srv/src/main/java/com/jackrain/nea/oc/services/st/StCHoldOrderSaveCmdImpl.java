package com.jackrain.nea.oc.services.st;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.st.StCHoldOrderSaveCmd;
import com.jackrain.nea.st.service.StCHoldOrderSaveService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2020/07/02 17:07:00
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class StCHoldOrderSaveCmdImpl extends CommandAdapter implements StCHoldOrderSaveCmd {
    @Autowired
    private StCHoldOrderSaveService stCHoldOrderSaveService;
    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        return stCHoldOrderSaveService.execute(querySession);
    }
}

