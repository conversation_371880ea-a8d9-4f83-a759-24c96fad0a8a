package com.jackrain.nea.oc.services.sap;

import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.utils.ValueHolderUtils;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.sap.OcBSapSalesDataRsManualSumCmd;
import com.jackrain.nea.oc.oms.sap.OcBSapSalesDataRecordSumNkThreadService;
import com.jackrain.nea.oc.oms.sap.OcBSapSalesDataRecordSumService;
import com.jackrain.nea.oc.oms.sap.OcBSapSalesDataRecordTaskService;
import com.jackrain.nea.oc.oms.sap.SapSalesDataGatherService;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.concurrent.TimeUnit;

/**
 * Description: 销售数据汇总表动作定义 - 手工汇总
 *
 * @Author: guo.kw
 * @Since: 2022/8/30
 * create at: 2022/8/30 15:28
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBSapSalesDataRsManualSumImpl extends CommandAdapter implements OcBSapSalesDataRsManualSumCmd {
    @Autowired
    private OcBSapSalesDataRecordSumService service;
    @Autowired
    private SapSalesDataGatherService serviceSap;
    @Autowired
    private OcBSapSalesDataRecordTaskService ocBSapSalesDataRecordTaskService;

    @Autowired
    private OcBSapSalesDataRecordSumNkThreadService ocBSapSalesDataRecordSumNkThreadService;
    private final static String SAP_SALES_DATA_RECORD_SUN_NK = "OcBSapSalesDataRecordSumNkThreadTaskRedisKey";
    private final static String SAP_SALES_DATA_RECORD_SUN_XS = "OcBSapSalesDataRecordSumThreadTaskRedisKey";

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Finish OcBSapSalesDataRsManualSumImpl execute start"));
        }
        CusRedisTemplate<Object, Object> strRedisTemplate = RedisOpsUtil.getStrRedisTemplate();

        /*try {
            ValueHolderV14 execute = ocBSapSalesDataRecordTaskService.executeThread();
            if (ResultCode.SUCCESS == execute.getCode()) {
                //汇总成功之后、调用推送sap程序
                serviceSap.salesOrderDataGatherBack(new ArrayList<>());
                return ValueHolderUtils.success("执行成功");
            } else {
                return ValueHolderUtils.fail(execute.getMessage());
            }
            return executeTest();
        } catch (Exception e) {
            return ValueHolderUtils.fail(Throwables.getStackTraceAsString(e));
        }*/
        try {
            StringBuilder stringBuilder = new StringBuilder();
            //执行销售汇总
            ValueHolderV14 v14 = this.executeXs(strRedisTemplate);
            stringBuilder.append("销售数据记录汇总：").append(v14.getMessage()).append("\n");
            //执行奶卡提奶金额冲抵汇总
            ValueHolderV14 v15 = this.executeNk(strRedisTemplate);
            stringBuilder.append("奶卡提奶金额冲抵汇总：").append(v15.getMessage());

            serviceSap.salesOrderDataGatherBack(new ArrayList<>());
            return ValueHolderUtils.success(stringBuilder.toString());
        } catch (Exception e) {
            return ValueHolderUtils.fail(Throwables.getStackTraceAsString(e));
        }
    }

    /**
     * 奶卡提奶金额冲抵单汇总
     *
     * @param strRedisTemplate
     * @return
     */
    public ValueHolderV14 executeNk(CusRedisTemplate<Object, Object> strRedisTemplate) {
        ValueHolderV14<Object> v14 = new ValueHolderV14<>();
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Finish OcBSapSalesDataRsManualSumImpl execute executeNk"));
        }

        try {
            if (strRedisTemplate.hasKey(SAP_SALES_DATA_RECORD_SUN_NK)) {
                log.info("OcBSapSalesDataRsManualSumImpl executeNk is running");
                v14.setCode(1);
                v14.setMessage("奶卡提奶金额冲抵单正在执行，不可重复执行！！！");
                return v14;
            } else {
                strRedisTemplate.opsForValue().set(SAP_SALES_DATA_RECORD_SUN_NK, SAP_SALES_DATA_RECORD_SUN_NK, 6L, TimeUnit.HOURS);
            }
            ValueHolderV14 holderV14 = ocBSapSalesDataRecordSumNkThreadService.executeThread();
            if (holderV14.getCode() == ResultCode.FAIL) {
                log.info(LogUtil.format("OcBSapSalesDataRsManualSumImpl running executeNk errorMesssage:{}"), holderV14.getMessage());
                v14.setCode(-1);
                v14.setMessage("奶卡提奶金额冲抵单执行报错！！！");
            } else {
                v14.setCode(holderV14.getCode());
                v14.setMessage(holderV14.getMessage());
            }
            strRedisTemplate.delete(SAP_SALES_DATA_RECORD_SUN_NK);
        } catch (Exception e) {
            strRedisTemplate.delete(SAP_SALES_DATA_RECORD_SUN_NK);
            log.info(LogUtil.format("OcBSapSalesDataRsManualSumImpl running executeNk error:{}"), Throwables.getStackTraceAsString(e));
            v14.setCode(-1);
            v14.setMessage("奶卡提奶金额冲抵单执行报错！！！");
        }
        return v14;
    }

    /**
     * 销售数据记录表汇总
     *
     * @param strRedisTemplate
     * @return
     */
    public ValueHolderV14 executeXs(CusRedisTemplate<Object, Object> strRedisTemplate) {
        ValueHolderV14<Object> v14 = new ValueHolderV14<>();

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Finish OcBSalesDataRsManualSumImpl execute executeXs"));
        }
        try {
            if (strRedisTemplate.hasKey(SAP_SALES_DATA_RECORD_SUN_XS)) {
                log.info(LogUtil.format("OcBSapSalesDataRsManualSumImpl executeXs is running"));
                v14.setCode(1);
                v14.setMessage("销售数据记录单据正在执行，不可重复执行！！！");
                return v14;
            } else {
                strRedisTemplate.opsForValue().set(SAP_SALES_DATA_RECORD_SUN_XS, SAP_SALES_DATA_RECORD_SUN_XS, 6L, TimeUnit.HOURS);
            }
            ValueHolderV14 holderV14 = ocBSapSalesDataRecordTaskService.executeThread();
            if (holderV14.getCode() == ResultCode.FAIL) {
                log.info(LogUtil.format("OcBSapSalesDataRsManualSumImpl running executeXs errorMessage:{}"), holderV14.getMessage());
                v14.setCode(-1);
                v14.setMessage("销售数据记录执行报错！！！");
            } else {
                v14.setCode(holderV14.getCode());
                v14.setMessage(holderV14.getMessage());
            }
            strRedisTemplate.delete(SAP_SALES_DATA_RECORD_SUN_XS);
        } catch (Exception e) {
            strRedisTemplate.delete(SAP_SALES_DATA_RECORD_SUN_XS);
            log.info(LogUtil.format("OcBSapSalesDataRsManualSumImpl running executeXs error:{}"), Throwables.getStackTraceAsString(e));
            v14.setCode(-1);
            v14.setMessage("销售数据记录执行报错！！！");
        }
        return v14;
    }

    public ValueHolder executeTest() {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Finish OcBSapSalesDataRsManualSumImpl executeTest start"));
        }
        CusRedisTemplate<Object, Object> strRedisTemplate = RedisOpsUtil.getStrRedisTemplate();

        try {
            StringBuilder stringBuilder = new StringBuilder();
            //执行销售汇总
            ValueHolderV14 v14 = this.executeXs(strRedisTemplate);
            stringBuilder.append("销售数据记录汇总：").append(v14.getMessage()).append("\n");
            //执行奶卡提奶金额冲抵汇总
            ValueHolderV14 v15 = this.executeNk(strRedisTemplate);
            stringBuilder.append("奶卡提奶金额冲抵汇总：").append(v15.getMessage());

            serviceSap.salesOrderDataGatherBack(new ArrayList<>());
            return ValueHolderUtils.success(stringBuilder.toString());
        } catch (Exception e) {
            return ValueHolderUtils.fail(Throwables.getStackTraceAsString(e));
        }
    }
}
