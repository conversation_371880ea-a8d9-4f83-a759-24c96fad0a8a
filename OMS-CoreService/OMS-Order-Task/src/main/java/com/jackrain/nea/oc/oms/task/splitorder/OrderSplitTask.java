package com.jackrain.nea.oc.oms.task.splitorder;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.oc.oms.mapper.task.OcBOrderSplitTaskMapper;
import com.jackrain.nea.oc.oms.model.table.task.OcBOrderSplitTask;
import com.jackrain.nea.oc.oms.services.SplitOutStockOrderService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.stream.Collectors;


/**
 * 订单拆单任务
 *
 * @author: wang shuai
 * @since: 2020/12/8
 */
@Slf4j
@Component
public class OrderSplitTask extends BaseR3Task implements IR3Task {

    @Autowired
    private OcBOrderSplitTaskMapper ocBOrderSplitTaskMapper;
    @Autowired
    private SplitOutStockOrderService splitOutStockOrderService;
    @Autowired
    private ThreadPoolTaskExecutor omsOrderSplitThreadPoolExecutor;

    private static final String taskTableName = "oc_b_order_split_task";

    @Override
    @XxlJob("OrderSplitTask")
    public RunTaskResult execute(JSONObject params) {
        String prefix = "[" + this.getClass().getName() + "." + new Exception().getStackTrace()[0].getMethodName() + "].[";
        if (log.isDebugEnabled()) {
            log.debug("{} 开始执行]", prefix);
        }
        RunTaskResult result = new RunTaskResult();
        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
        Integer pageSize = config.getProperty("lts.orderSplitTask.range", 200);
        Integer status = config.getProperty("lts.orderSplitTask.status", 0);
        Integer limitSplitTimes = config.getProperty("lts.orderSplitTask.limit.splitTimes", 3);

        int count = ocBOrderSplitTaskMapper.countExeNum(status, limitSplitTimes);
        if (count == 0) {
            result.setSuccess(true);
            result.setMessage("没有执行的数据，任务结束。");
            return result;
        }

        long start = System.currentTimeMillis();
        try {
            Map<String, String> topMap = null;
            Set<String> nodes = topMap.keySet();
            if (CollectionUtils.isEmpty(nodes)) {
                log.debug("{} nodes not get!]", prefix);
                result.setSuccess(false);
                result.setMessage("请检查环境，node获取不到！！");
                return result;
            }

            List<Future<Boolean>> results = new ArrayList<>();
            for (String nodeName : nodes) {
                try {
                    Future<Boolean> exeResult = omsOrderSplitThreadPoolExecutor.submit(new CallableOrderSplitResult(nodeName, topMap.get(nodeName), status, limitSplitTimes, pageSize));
                    results.add(exeResult);
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("{} 异常：{}]", prefix, e);
                }
            }
            //线程执行结果获取
            for (Future<Boolean> futureResult : results) {
                try {
                    log.debug("{} 线程结果：{}]", prefix, futureResult.get().toString());
                } catch (InterruptedException e) {
                    log.error("{} 多线程获取InterruptedException异常：{}]", prefix, e);
                } catch (ExecutionException e) {
                    log.error("{} 多线程获取ExecutionException异常：{}]", prefix, e);
                }
            }
            result.setSuccess(true);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error("{} 订单拆单任务失败：{}]", prefix, ex);
            result.setSuccess(false);
            result.setMessage(ex.getMessage());
        }
        if (log.isDebugEnabled()) {
            log.debug("{} 执行结束 耗时:{}ms]", prefix, System.currentTimeMillis() - start);
        }
        return result;
    }


    /**
     * 开启线程类
     */
    class CallableOrderSplitResult implements Callable<Boolean> {

        private final String nodeName;
        private final String taskTableName;
        private final int status;
        private final int limitSplitTimes;
        private final int pageSize;

        public CallableOrderSplitResult(String nodeName, String taskTableName, int status, int limitSplitTimes, int pageSize) {
            this.nodeName = nodeName;
            this.taskTableName = taskTableName;
            this.status = status;
            this.limitSplitTimes = limitSplitTimes;
            this.pageSize = pageSize;
        }

        @Override
        public Boolean call() throws Exception {
            List<OcBOrderSplitTask> taskList = ocBOrderSplitTaskMapper.queryList(taskTableName, status, limitSplitTimes, pageSize);
            if (CollectionUtils.isNotEmpty(taskList)) {
                List<Long> orderIdList = taskList.stream().map(o -> o.getOcBOrderId()).collect(Collectors.toList());

                this.processTransactional(orderIdList);
            }
            return true;
        }

        @Transactional(rollbackFor = Exception.class)
        public void processTransactional(List<Long> orderIdList) {
            try {
                // 更新订单拆单任务为拆分中
                ocBOrderSplitTaskMapper.batchUpdateOne(1, orderIdList, 0);

                // 发货缺货重新占单MQ
                splitOutStockOrderService.sendSplitOrderMQ(orderIdList, true);
            } catch (Exception e) {
                log.error("发送MQ发生异常：", e);
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            }
        }
    }


}