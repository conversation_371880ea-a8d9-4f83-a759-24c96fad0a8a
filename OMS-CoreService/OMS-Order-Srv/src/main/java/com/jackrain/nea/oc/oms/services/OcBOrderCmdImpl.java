package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBOrderCmd;
import com.jackrain.nea.oc.request.CancelOrderModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> zhuxing
 * @Date : 2021-12-20 15:15
 * @Description : 零售发货单相关服务
 **/
@Component
@Service(protocol = "dubbo", validation = "true", group = "oc-core", version = "1.0")
public class OcBOrderCmdImpl implements OcBOrderCmd {

    @Autowired
    private OcBOrderCancelService ocBOrderCancelService;

    /**
     * 取消零售发货单
     * @param cancelOrderModel
     * @return
     * @throws NDSException
     */
    @Override
    public ValueHolderV14 cancelOrder(CancelOrderModel cancelOrderModel) throws NDSException {
        return ocBOrderCancelService.cancelOrderByTid(cancelOrderModel);
    }
}
