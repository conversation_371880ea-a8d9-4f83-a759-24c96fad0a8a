package com.jackrain.nea.oc.oms.model.enums;

import lombok.Getter;

/**
 * @Auther: 黄志优
 * @Date: 2020/8/30 14:56
 * @Description:
 */
public enum OmsQimenPosOrderStatusEnum {

    IN_DISTRIBUTION("配货中", 1),

    WAREHOUSE_DELIVERY("仓库发货", 2),

    PLATFORM_DELIVERY("平台发货", 3),

    CANCELLED("已取消", 4);

    OmsQimenPosOrderStatusEnum(String key, int val) {
        this.key = key;
        this.val = val;
    }

    @Getter
    String key;

    @Getter
    int val;
}
