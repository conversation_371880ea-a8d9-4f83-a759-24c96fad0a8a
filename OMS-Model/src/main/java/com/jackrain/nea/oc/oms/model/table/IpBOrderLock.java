package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.util.Date;

@TableName(value = "ip_b_order_lock")
@Data
@Document(index = "ip_b_order_lock", type = "ip_b_order_lock")
public class IpBOrderLock extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "BILL_STATUS")
    @Field(type = FieldType.Keyword)
    private String billStatus;

    @JSONField(name = "CP_C_SHOP_ID")
    @Field(type = FieldType.Long)
    private Long cpCShopId;

    @JSONField(name = "CP_C_SHOP_TITLE")
    @Field(type = FieldType.Keyword)
    private String cpCShopTitle;

    @JSONField(name = "BUYER_NICK")
    @Field(type = FieldType.Keyword)
    private String buyerNick;

    @JSONField(name = "SOURCE_CODE")
    @Field(type = FieldType.Keyword)
    private String sourceCode;

    @JSONField(name = "ITEM_ORDER_NO")
    @Field(type = FieldType.Keyword)
    private String itemOrderNo;

    @JSONField(name = "IS_AUTO_UNLOCK")
    @Field(type = FieldType.Integer)
    private Integer isAutoUnlock;

    @JSONField(name = "EXCEPT_UNLOCK_TIME")
    @Field(type = FieldType.Long)
    private Date exceptUnlockTime;

    @JSONField(name = "IS_QN_LOCK")
    @Field(type = FieldType.Integer)
    private Integer isQnLock;

    @JSONField(name = "SYNC_TYPE")
    @Field(type = FieldType.Keyword)
    private String syncType;

    @JSONField(name = "SYS_REMARK")
    @Field(type = FieldType.Keyword)
    private String sysRemark;

    @JSONField(name = "ORDER_LOCK_STATUS")
    @Field(type = FieldType.Keyword)
    private String orderLockStatus;

    @JSONField(name = "LOCK_TIME")
    @Field(type = FieldType.Long)
    private Date lockTime;

    @JSONField(name = "UNLOCK_TIME")
    @Field(type = FieldType.Long)
    private Date unlockTime;

    @JSONField(name = "DELER_ID")
    @Field(type = FieldType.Long)
    private Long delerId;

    @JSONField(name = "DELER_NAME")
    @Field(type = FieldType.Keyword)
    private String delerName;

    @JSONField(name = "DELER_ENAME")
    @Field(type = FieldType.Keyword)
    private String delerEname;

    @JSONField(name = "DEL_TIME")
    @Field(type = FieldType.Long)
    private Date delTime;

    @JSONField(name = "REMARK")
    @Field(type = FieldType.Keyword)
    private String remark;

    @JSONField(name = "VERSION_NO")
    @Field(type = FieldType.Long)
    private Long versionNo;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;

    @JSONField(name = "RETURN_QN")
    @Field(type = FieldType.Integer)
    private Integer returnQn;

    @JSONField(name = "FAIL_REASON")
    @Field(type = FieldType.Keyword)
    private String failReason;
}