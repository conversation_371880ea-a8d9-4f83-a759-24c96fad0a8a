package com.jackrain.nea.oc.oms.model.request;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/09/12 13:20
 * 发票管理新增发票申请单request
 */
@Data
public class AcFOrderInvoiceSaveRequest implements Serializable {

    private static final long serialVersionUID = -8868761360869591461L;
    /**
     * 订单明细id
     */
    private List<SaveData> tids;

    private String invoiceKind;

    private String invoiceType;

    private String invoiceHeader;

    private String headerType;

    private String invoiceAddress;

    private String taxpayerNo;

    private String mobile;

    private String openingBank;

    private String bankAccount;

    private String invoiceRemark;

    private String receiver;

    private String receiverPhone;

    private String email;

    private String receiverAddress;


    @Data
    public static class SaveData implements Serializable {
        private static final long serialVersionUID = -7298782905275321262L;

        /**
         * 订单明细ids
         */
        private List<Long> ids;
        /**
         * 订单ids
         */
        private List<Long> ocBOrderIds;
        /**
         * 店铺id
         */
        private Long cpCShopId;
        /**
         * 开票金额
         */
        private BigDecimal amt;
        /**
         * 平台单号
         */
        private String tid;
    }
}
