package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.AddGift;
import com.jackrain.nea.oc.oms.api.CheckAddGift;
import com.jackrain.nea.oc.oms.api.DeleteGift;
import com.jackrain.nea.oc.oms.api.ReturnGift;
import com.jackrain.nea.oc.oms.model.result.OrderInterceptionResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: 夏继超
 * @since: 2019/3/11
 * create at : 2019/3/11 17:39
 */
@Api(value = "OrderInterceptionCtrl", tags = "赠品的操作")
@Slf4j
@RestController
public class GiftCtrl {

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @Autowired
    private CheckAddGift checkAddGift;

    @Autowired
    private DeleteGift deleteGift;

    @Autowired
    private ReturnGift returnGift;

    @Autowired
    private AddGift addGift;
    /**
     *
     * 删除赠品
     *
     * @param request
     * @param obj     传入的参数
     * @return
     */
    @ApiOperation(value = "删除赠品")
    @RequestMapping(value = "/api/cs/oc/oms/v1/deleteGit", method = RequestMethod.POST)
    public JSONObject deleteGift(HttpServletRequest request,
                                 @RequestBody JSONObject obj/*@RequestParam(value = "orderId", required = true) Long orderId*/) {
        //记录日志信息
//        log.debug("start GiftCtrl.deleteGift.ReceiveParams=" + obj);
        ValueHolderV14<OrderInterceptionResult> vh = new ValueHolderV14<OrderInterceptionResult>();
        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
       /* User user = new UserImpl();
        ((UserImpl) user).setLocale(new Locale("zh"));
        ((UserImpl) user).setId(2);*/
        try {

            List<Long> id = new ArrayList<>();
            id.add(91L);
            // ValueHolderV14 valueHolder = deleteGift.deleteGift(orderId, id, user);
            ValueHolderV14 valueHolder = deleteGift.deleteGift(obj, user);
            //记录日志信息。Finish 标记结束
//            log.debug("Finish GiftCtrl.deleteGift. Return Result=" + valueHolder.toJSONObject());
            return JSONObject.parseObject(JSONObject.toJSONString(valueHolder.toJSONObject(), SerializerFeature.WriteMapNullValue));
        } catch (NDSException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
        }

    }

    /**
     * 新增赠品前置检查
     *
     * @param request 请求
     * @param obj     传入参数
     * @return
     */
    @ApiOperation(value = "新增赠品前置检查")
    @RequestMapping(value = "/api/cs/oc/oms/v1/checkGit", method = RequestMethod.POST)
    public JSONObject checkGIft(HttpServletRequest request,
                                @RequestBody JSONObject obj) {
        ValueHolderV14<OrderInterceptionResult> vh = new ValueHolderV14<OrderInterceptionResult>();
        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        try {


            ValueHolderV14 valueHolder = checkAddGift.checkAddGift(obj);
            //记录日志信息。Finish 标记结束
            return JSONObject.parseObject(JSONObject.toJSONString(valueHolder.toJSONObject(), SerializerFeature.WriteMapNullValue));
        } catch (NDSException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
        }
    }

    /**
     * 返回满足赠品的筛选列表
     *
     * @param request 请求
     * @param param   传入参数
     * @return
     */
    @ApiOperation(value = "返回满足赠品的筛选列表")
    @RequestMapping(value = "/api/cs/oc/oms/v1/returnGit", method = RequestMethod.POST)
    public JSONObject resultGIft(HttpServletRequest request,
                                 @RequestParam(value = "param", required = true) String param) {
        ValueHolderV14<OrderInterceptionResult> vh = new ValueHolderV14<OrderInterceptionResult>();
        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
       /* User user = new UserImpl();
        ((UserImpl) user).setLocale(new Locale("zh"));
        ((UserImpl) user).setId(2);*/
        try {

            // ValueHolderV14 valueHolder = deleteGift.deleteGift(orderId, id, user);
            ValueHolderV14 valueHolder = returnGift.returnGift(param);
             return JSONObject.parseObject(JSONObject.toJSONString(valueHolder.toJSONObject(), SerializerFeature.WriteMapNullValue));
        } catch (NDSException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            // return vh.toJSONObject();
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
        }
    }

    /**
     * 新增赠品
     *
     * @param request 请求
     * @param obj     传入参数
     * @return
     */
    @ApiOperation(value = "新增赠品")
    @RequestMapping(value = "/api/cs/oc/oms/v1/addGit", method = RequestMethod.POST)
    public JSONObject addGIft(HttpServletRequest request,
                              @RequestBody JSONObject obj) {
         ValueHolderV14<OrderInterceptionResult> vh = new ValueHolderV14<OrderInterceptionResult>();
        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
       /* User user = new UserImpl();
        ((UserImpl) user).setLocale(new Locale("zh"));
        ((UserImpl) user).setId(2);*/
        try {

            // ValueHolderV14 valueHolder = deleteGift.deleteGift(orderId, id, user);
            ValueHolder valueHolder = addGift.addGift(obj, user);
            return JSONObject.parseObject(JSONObject.toJSONString(valueHolder.toJSONObject(), SerializerFeature.WriteMapNullValue));
            //return valueHolder.toJSONObject();
        } catch (NDSException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            //return vh.toJSONObject();
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
        }
    }
}
