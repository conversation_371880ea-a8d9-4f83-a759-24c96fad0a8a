package com.jackrain.nea.oc.oms.es;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.oc.oms.model.relation.MergeOrderPromotionQbModel;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import org.apache.commons.lang.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;

/**
 * <AUTHOR>
 * @date 2020/11/11 5:29 下午
 * @description
 * @since version -1.0
 */
public class ES4OrderPromotion {

    /**
     * 根据PROMOTION_NAME优惠名称查询id
     *
     * @param mergeOrderModel
     * 用于前端接口查询使用
     */
    public static JSONArray findJSONArrayByWhereKeys(MergeOrderPromotionQbModel mergeOrderModel) {
        JSONObject whereKeys = new JSONObject();
        /*if (null != mergeOrderModel.getGiftItemCode()) {
            whereKeys.put("GIFT_ITEM_CODE", mergeOrderModel.getGiftItemCode());
        }*/
        if (null != mergeOrderModel.getPromotionName()) {
            whereKeys.put("PROMOTION_NAME", mergeOrderModel.getPromotionName());
        }

        JSONObject filterKeyJo = new JSONObject();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
        String createDate = " ";
        try {
            createDate = sdf.parse(mergeOrderModel.getBeginOrderDate()).getTime() + "~" + sdf.parse(mergeOrderModel.getEndOrderDate()).getTime();
        } catch (ParseException e) {
            e.printStackTrace();
        }
        if (StringUtils.isNotEmpty(createDate)) {
            filterKeyJo.put("CREATIONDATE", createDate);
        }

        JSONObject jsonObject = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_ORDER_PROMOTION_INDEX_NAME,
                OcElasticSearchIndexResources.OC_B_ORDER_PROMOTION_TYPE_NAME, whereKeys, filterKeyJo, null,
                100, 0, new String[]{"ID"});
        if (null == jsonObject) {
            return null;
        }
        JSONArray aryIds = jsonObject.getJSONArray("data");

        return aryIds;
    }
}
