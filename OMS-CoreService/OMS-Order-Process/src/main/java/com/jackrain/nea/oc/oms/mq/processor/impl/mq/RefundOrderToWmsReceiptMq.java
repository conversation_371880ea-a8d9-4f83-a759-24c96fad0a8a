package com.jackrain.nea.oc.oms.mq.processor.impl.mq;

import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.aliyun.openservices.ons.api.impl.util.MsgConvertUtil;
import com.burgeon.mq.annotation.RocketMqMessageListener;
import com.burgeon.mq.core.BaseMessageListener;
import com.burgeon.mq.enums.MqTypeEnum;
import com.burgeon.mq.error.MqException;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.services.RefundOrderToWmsReceiptService;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 零售退单发wms回执MQ(MQ已通)
 *
 * @author: 郑立轩
 * @since: 2019/5/7
 * create at : 2019/5/7 10:00
 */
@Slf4j
@RocketMqMessageListener(name = "RefundOrderToWmsReceiptMq", type = MqTypeEnum.DEFAULT)
public class RefundOrderToWmsReceiptMq implements BaseMessageListener {
    @Autowired
    private RefundOrderToWmsReceiptService service;

    @Override
    public void consume(String messageBody, String messageTopic, String messageKey, String messageTag, Object object) {
        try {
            log.debug(LogUtil.format("零售退单发wms回执MQ: {}", messageKey), messageBody);
            service.updateStatus(messageBody);
        } catch (Exception e) {
            log.error("RefundOrderToWmsReceiptMq.consumed.exception: {}", Throwables.getStackTraceAsString(e));
            throw new MqException(e);
        }
    }
}
