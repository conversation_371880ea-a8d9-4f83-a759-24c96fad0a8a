package com.jackrain.nea.oc.oms.api;


import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;

/**
 * @author: chenxiulou
 * @description: 开票通知单 新增
 * @since: 2019-07-20
 * create at : 2019-07-20 10:31
 */
public interface InvoicNoticeSaveCmd {
    ValueHolder saveInvoiceNotice(QuerySession session) throws NDSException;

    ValueHolder confirmInvoiceNotice(QuerySession session) throws NDSException;

    ValueHolder auditInvoiceNotice(QuerySession session) throws NDSException;
}
