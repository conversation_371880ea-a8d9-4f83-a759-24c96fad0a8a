package com.jackrain.nea.oc.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.InvoicNoticeSaveCmd;
import com.jackrain.nea.oc.oms.services.InvoiceNoticeSaveService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: chenxiulou
 * @description: 开票通知 保存
 * @since: 2019-07-20
 * create at : 2019-07-20 10:37
 */
@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class InvoicNoticeSaveCmdImpl extends CommandAdapter implements InvoicNoticeSaveCmd {
    @Autowired
    private InvoiceNoticeSaveService noticeSaveService;

    @Override
    public ValueHolder saveInvoiceNotice(QuerySession session) throws NDSException {
        return noticeSaveService.saveInvoiceNotice(session);
    }

    @Override
    public ValueHolder confirmInvoiceNotice(QuerySession session) throws NDSException {
        return noticeSaveService.confirmInvoiceNotice(session);
    }

    @Override
    public ValueHolder auditInvoiceNotice(QuerySession session) throws NDSException {
        return noticeSaveService.auditInvoiceNotice(session);
    }
}
