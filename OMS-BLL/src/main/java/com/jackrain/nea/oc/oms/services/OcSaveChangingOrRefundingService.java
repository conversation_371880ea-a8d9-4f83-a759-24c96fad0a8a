package com.jackrain.nea.oc.oms.services;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.inf.model.result.oms.SgOmsShopStorageQueryItemResult;
import com.burgeon.r3.sg.inf.model.result.oms.SgOmsShopStorageQueryResult;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.cpext.model.table.CpLogistics;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.config.OmsSystemConfig;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemFiMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderNaiKaMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderExchangeMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderLogMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderRefundMapper;
import com.jackrain.nea.oc.oms.model.constant.OcCommonConstant;
import com.jackrain.nea.oc.oms.model.constant.OcOmsReturnOrderConstant;
import com.jackrain.nea.oc.oms.model.enums.AGStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.InterceptStatus;
import com.jackrain.nea.oc.oms.model.enums.IsActiveEnum;
import com.jackrain.nea.oc.oms.model.enums.IsReservedEnum;
import com.jackrain.nea.oc.oms.model.enums.IsWrongReceive;
import com.jackrain.nea.oc.oms.model.enums.OcOrderTagEum;
import com.jackrain.nea.oc.oms.model.enums.OcReturnBillTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderBusinessTypeCodeEnum;
import com.jackrain.nea.oc.oms.model.enums.ProReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnNaiKaStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnOrderConfirmStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.WmsWithdrawalState;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.relation.IpOrderReturnRelation;
import com.jackrain.nea.oc.oms.model.relation.OcBReturnOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnAfSend;
import com.jackrain.nea.oc.oms.model.table.OcBReturnAfSendItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderExchange;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderLog;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.oc.oms.model.table.StCBusinessType;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.nums.ExchangeHoldTowingcConstant;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.nums.ReturnOrderNodeEnum;
import com.jackrain.nea.oc.oms.nums.WmsControlWarehouse;
import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
import com.jackrain.nea.oc.oms.services.calculate.qty.OmsOrderQtyCalculateService;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.oc.oms.util.DateConversionUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.st.service.OmsStCShopStrategyService;
import com.jackrain.nea.st.service.OmsSyncStockStrategyService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.OperateUserUtils;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.ExceptionUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @author: 李杰
 * @since: 2019/3/12
 * create at : 2019/3/12 19:42
 */
@Slf4j
@Component
public class OcSaveChangingOrRefundingService {

    private static final String PUSH_DELAY_TIME_KEY = "business_system:return.order.push.delay.time";

    @Value("${return.order.push.delay.time:72}")
    private int pushDelayTime;

    private static final String reg = "[^\\u0000-\\uFFFF]";

    @Autowired
    private OmsSystemConfig omsSystemConfig;

    @Autowired
    OmsOrderLogService omsOrderLogService;
    @Autowired
    PsRpcService psRpcService;
    @Autowired
    OmsSyncStockStrategyService syncStockStrategyService;
    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderFiMapper;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderItemFiMapper ocBorderItemMapper;
    @Autowired
    private OcBReturnOrderExchangeMapper ocBReturnOrderExchangeFiMapper;
    @Autowired
    private OcBReturnOrderRefundMapper ocBReturnOrderRefundFiMapper;
    @Autowired
    private OcBReturnOrderLogMapper ocBReturnOrderLogMapper;
    @Autowired
    private OcBReturnBuildService ocBReturnBuildService;
    @Autowired
    private BllRedisLockOrderUtil redisUtil;
    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private BuildSequenceUtil sequenceUtil;
    @Autowired
    private OmsStCShopStrategyService shopStrategyService;
    @Autowired
    private SgRpcService sgRpcService;

    @Autowired
    private RefundFormAfterDeliveryService refundFormAfterDeliveryService;

    @Autowired
    private OmsOrderCancellationService omsOrderCancellationService;

    @Autowired
    private ReturnChangeCompareOcBorderService compareOcBorderService;

    @Autowired
    private Refund2ExchangeService refund2ExchangeService;

    @Autowired
    private SaveBillService saveBillService;

    @Autowired
    private OmsOrderDistributeWarehouseService omsWarehouseRuleService;

    @Autowired
    OrderExchangeHoldTowingTaskService orderExchangeHoldTowingTaskService;

    @Autowired
    private OcBReturnOrderNodeRecordService nodeRecordService;

    @Autowired
    private OmsRefundOrderService omsRefundOrderService;

    @Autowired
    private OcBOrderNaiKaMapper ocBOrderNaiKaMapper;

    /**
     * 获取实例
     *
     * @return
     */
    public static OcSaveChangingOrRefundingService getInstance() {
        return ApplicationContextHandle.getBean(OcSaveChangingOrRefundingService.class);
    }

    private static boolean checkMobile(String mobile) {
        boolean flag = false;
        if (StringUtils.isEmpty(mobile)) {
            return false;
        }
        filterSpecialStr(mobile);
        filterEmoji(mobile);
        for (char c : mobile.toCharArray()) {
            if (c >= 0x4E00 && c <= 0x9FA5) {
                flag = true;
                break;
            }
        }
        return flag;
    }

    /**
     * 过滤特殊字符
     *
     * @param str
     * @return
     */
    private static String filterSpecialStr(String str) {
        String regEx = "[`~!@#$%^&*()+=|{}':;',\\[\\].<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。，、？]";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(str);
        return m.replaceAll("").trim();
    }

    private static String filterEmoji(String source) {
        if (source != null) {
            Pattern emoji = Pattern.compile(reg, Pattern.UNICODE_CASE | Pattern.CASE_INSENSITIVE);
            Matcher emojiMatcher = emoji.matcher(source);
            if (emojiMatcher.find()) {
                source = emojiMatcher.replaceAll("");
                return source;
            }
            return source;
        }
        return source;
    }

    /**
     * 退换货订单保存更新
     *
     * @param obj  obj 请求参数
     * @param user user 用户信息
     * @return ValueHolder 返回信息
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolder saveChangingOrRefunding(JSONObject obj, User user) {
        try {
            Integer isRefund2Exchange = obj.getInteger("isRefund2Exchange");
            if (isRefund2Exchange != null && isRefund2Exchange == 1) {
                return refund2ExchangeService.refund2Exchange(obj, user);
            }

            Long objid = obj.getLong("objid");
            Long copyType = obj.getLong("copytype");

            JSONObject returnOrderDto = obj.getJSONObject("OcBreturnOrder");
            JSONArray exchangeJsonAry = obj.getJSONArray("OcBreturnOrderExchange");
            JSONArray refundJsonAry = obj.getJSONArray("OcBreturnOrderRefund");

            String confirmStatus = returnOrderDto.getString("CONFIRM_STATUS");
            if (StringUtils.isNotBlank(confirmStatus)) {
                if (ReturnOrderConfirmStatusEnum.CONFIRM.getValue().equals(confirmStatus)) {
                    returnOrderDto.put("CONFIRM_STATUS", ReturnOrderConfirmStatusEnum.CONFIRM.getKey());
                }
                if (ReturnOrderConfirmStatusEnum.NOT_CONFIRM.getValue().equals(confirmStatus)) {
                    returnOrderDto.put("CONFIRM_STATUS", ReturnOrderConfirmStatusEnum.NOT_CONFIRM.getKey());
                }
            } else {
                returnOrderDto.put("CONFIRM_STATUS", null);
            }

            // 转换
            ReturnNaiKaStatusEnum returnNaiKaStatusEnum = ReturnNaiKaStatusEnum.getByDesc(returnOrderDto.getString("TO_NAIKA_STATUS"));
            if (ObjectUtil.isNotNull(returnNaiKaStatusEnum)) {
                returnOrderDto.put("TO_NAIKA_STATUS", returnNaiKaStatusEnum.getStatus());
            } else {
                returnOrderDto.put("TO_NAIKA_STATUS", null);
            }

            OcBReturnOrder returnOrder = JSONObject.parseObject(returnOrderDto.toString(), OcBReturnOrder.class);
            // 判断手机号是否有特殊字符
            if (checkMobile(returnOrder.getReceiveMobile())) {
                throw new NDSException("请填写正确的手机号");
            }
            if (checkMobile(returnOrder.getReceivePhone())) {
                throw new NDSException("请填写正确的电话号码");
            }
            String returnId = returnOrder.getReturnId();
            if (StringUtils.isNotBlank(returnId)) {
                List<OcBReturnOrder> returnOrderList = ocBReturnOrderFiMapper.selectByReturnId(returnId);
                if (CollectionUtils.isNotEmpty(returnOrderList)) {
                    List<String> typeCodeList = new ArrayList<>();
                    typeCodeList.add("RYTH11");
                    typeCodeList.add("RYTH12");
                    OcBReturnOrder ocBReturnOrder = returnOrderList.get(0);
                    //业务类型名称
                    String businessTypeCode = ocBReturnOrder.getBusinessTypeCode();
                    if (typeCodeList.contains(businessTypeCode)) {
                        throw new NDSException("不允许在中台新增SAP相关单据");
                    }
                }
            }

            List<OcBReturnOrderExchange> returnOrderExchanges = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(exchangeJsonAry)) {
                returnOrderExchanges = JSONObject.parseArray(exchangeJsonAry.toString(), OcBReturnOrderExchange.class);
            }
            List<OcBReturnOrderRefund> returnOrderRefunds = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(refundJsonAry)) {
                returnOrderRefunds = JSONObject.parseArray(refundJsonAry.toJSONString(), OcBReturnOrderRefund.class);
            }

            // 退单新增那里有判断申请数量和换货数量一样吗
            if (CollectionUtils.isNotEmpty(returnOrderRefunds) && CollectionUtils.isNotEmpty(returnOrderExchanges)) {
                // 退货数量
                BigDecimal returnCount = BigDecimal.ZERO;
                // 换货数量
                BigDecimal exchangeCount = BigDecimal.ZERO;
                for (OcBReturnOrderRefund returnOrderRefund : returnOrderRefunds) {
                    returnCount = returnCount.add(returnOrderRefund.getQtyRefund());
                }
                for (OcBReturnOrderExchange returnOrderExchange : returnOrderExchanges) {
                    exchangeCount = exchangeCount.add(returnOrderExchange.getQtyExchange());
                }
                AssertUtil.assertException(returnCount.compareTo(exchangeCount) != 0, "退货数量要与换货数量一致，请检查后重试");
            }
            //加入“空运单号延迟推单有效时间”字段
            returnOrder.setPushDelayTime(this.PushDelayTime(returnOrder));
            returnOrder.setIsWrongReceive(IsWrongReceive.NO.val());
            returnOrder.setPlatformRefundStatus(OcOmsReturnOrderConstant.PLATFORM_REFUND_STATUS_INIT);
            OcBReturnOrderRelation returnOrderRelation = new OcBReturnOrderRelation();
            returnOrderRelation.setReturnOrderInfo(returnOrder);
            returnOrderRelation.setOrderExchangeList(returnOrderExchanges);
            returnOrderRelation.setOrderRefundList(returnOrderRefunds);


            if (objid == -1) {
                // 新增
                String origOrderLockKey = BllRedisKeyResources.buildLockOrderKey(returnOrder.getOrigOrderId());
                RedisReentrantLock reentrantLock = RedisMasterUtils.getReentrantLock(origOrderLockKey);
                try {
                    if (!reentrantLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                        AssertUtil.assertException(true, "原订单正在操作, 请稍后尝试");
                    }
                    // 新增退换货单
                    addReturnOrder(user, copyType, returnOrderRelation);
                } catch (Exception ex) {
                    log.error(LogUtil.format("退单新增异常,异常信息:{},退单编号=", returnOrder.getId()),
                            ExceptionUtil.getMessage(ex));
                    throw new NDSException(ExceptionUtil.getMessage(ex));

                } finally {
                    reentrantLock.unlock();
                }
            } else {
                // 修改
                String lockRedisKey = BllRedisKeyResources.buildLockReturnOrderKey(objid);
                RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
                try {
                    // 锁定退换货
                    if (!redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                        AssertUtil.assertException(true, "当前退单已被锁定，不允许操作");
                    }
                    // 修改时判断是否传wms这个字段是否传成功，如果成功 点保存提示 传wms 成功，不允许更新保存失败。
                    OcBReturnOrder ocReturnOrder = ocBReturnOrderFiMapper.selectById(objid);

                    boolean isToWms = WmsWithdrawalState.YES.toInteger().equals(ocReturnOrder.getIsTowms())
                            || WmsWithdrawalState.PASS.toInteger().equals(ocReturnOrder.getIsTowms());
                    AssertUtil.assertException(isToWms, "退换货订单传wms成功或传wms中，不允许更新");

                    // 更新退换货订单
                    updateReturnOrderInfo(user, objid, returnOrderRelation);
                } catch (Exception ex) {
                    log.error(LogUtil.format("退单修改异常,异常信息:{},退单编号=",
                            returnOrderRelation.getReturnOrderInfo().getId()), ExceptionUtil.getMessage(ex));
                    AssertUtil.assertException(true, ExceptionUtil.getMessage(ex));
                } finally {
                    redisLock.unlock();
                }
            }

            // 同时新增已发货退款单
//            insertOcBReturnAfSend(user,returnOrder,returnOrderRefunds);
            ValueHolder vh = new ValueHolder();
            vh.put("code", ResultCode.SUCCESS);
            vh.put("message", "成功！");
            returnOrder = returnOrderRelation.getReturnOrderInfo();
            vh.put("objid", returnOrder.getId());
            vh.put("RETURN_STATUS_NAME", ReturnStatusEnum.getNameByCode(returnOrder.getReturnStatus()));
            return vh;
        } catch (Exception e) {
            log.error(LogUtil.format("新增退换货订单异常，error：{}"), Throwables.getStackTraceAsString(e));
            throw new NDSException(ExceptionUtil.getMessage(e));
        }
    }

    /**
     * 解密收货人信息
     *
     * @param returnOrder    换货单
     * @param oldOcBOrder    原始订单
     * @param oldReturnOrder 原始换货单
     */
    private void decryptBuyerInfo(OcBReturnOrder returnOrder, OcBOrder oldOcBOrder, OcBReturnOrder oldReturnOrder) {
        OcBOrder ocBOrder = new OcBOrder();

        //拼接参数
        ocBOrder.setReceiverName(returnOrder.getReceiveName());
        ocBOrder.setReceiverMobile(returnOrder.getReceiveMobile());
        ocBOrder.setReceiverPhone(returnOrder.getReceivePhone());
        ocBOrder.setReceiverAddress(returnOrder.getReceiveAddress());

        if (ObjectUtils.isEmpty(oldOcBOrder)) {
            oldOcBOrder = new OcBOrder();
            oldOcBOrder.setOaid(oldReturnOrder.getOaid());
            oldOcBOrder.setReceiverName(oldReturnOrder.getReceiveName());
            oldOcBOrder.setReceiverMobile(oldReturnOrder.getReceiveMobile());
            oldOcBOrder.setReceiverPhone(oldReturnOrder.getReceivePhone());
            oldOcBOrder.setReceiverAddress(oldReturnOrder.getReceiveAddress());
            oldOcBOrder.setPlatform(oldReturnOrder.getPlatform());
        }

        //解密
        saveBillService.copyBuyerInfo(ocBOrder, oldOcBOrder);

        returnOrder.setReceiveName(ocBOrder.getReceiverName());
        returnOrder.setReceiveMobile(ocBOrder.getReceiverMobile());
        returnOrder.setReceivePhone(ocBOrder.getReceiverPhone());
        returnOrder.setReceiveAddress(ocBOrder.getReceiverAddress());
        returnOrder.setOaid(ocBOrder.getOaid());
    }

    /**
     * 更新退换货订单
     *
     * @param user     操作用户
     * @param objId    退换货订单ID
     * @param relation 退换货订单关系对象
     */
    private void updateReturnOrderInfo(User user, Long objId, OcBReturnOrderRelation relation) {

        OcBReturnOrder returnOrder = relation.getReturnOrderInfo();
        Long phyWarehouseInId = returnOrder.getCpCPhyWarehouseInId();
        List<OcBReturnOrderRefund> returnOrderRefunds = relation.getOrderRefundList();
        List<OcBReturnOrderExchange> returnOrderExchanges = relation.getOrderExchangeList();

        // 单据类型
        Integer billType = returnOrder.getBillType();
        if (OcReturnBillTypeEnum.RETURN.getVal().equals(billType)) {
            // 退货单
            Map<String, Object> map = new HashMap<>();
            map.put("oc_b_return_order_id", objId);
            List<OcBReturnOrderRefund> refunds = ocBReturnOrderRefundFiMapper.selectByMap(map);
            refund(returnOrderRefunds, refunds, objId, user);
            // 是否换货预留库存为否
            returnOrder.setIsReserved(IsReservedEnum.NO.getValue());
        } else {
            // 退换货单
            Map<String, Object> map = new HashMap<>();
            map.put("oc_b_return_order_id", objId);
            List<OcBReturnOrderExchange> exchanges = ocBReturnOrderExchangeFiMapper.selectByMap(map);
            exchange(returnOrderExchanges, exchanges, objId, user);
            List<OcBReturnOrderRefund> refunds = ocBReturnOrderRefundFiMapper.selectByMap(map);
            refund(returnOrderRefunds, refunds, objId, user);
        }

        // 记录修改内容日志
        String updateMsg = this.logMessage(objId, returnOrder);

        returnOrder.setCpCLogisticsEcode(getLogisticsCode(returnOrder.getCpCLogisticsId()));
        // returnOrder.setId(objid);
        returnOrder.setModifierid(user.getId().longValue());
        returnOrder.setModifiername(user.getName());
        returnOrder.setModifierename(user.getEname());
        returnOrder.setModifieddate(new Date());
        returnOrder.setOrigSourceCode(null);
        returnOrder.setTbDisputeId(null);

        // 更新时判断 -只有在退换货单状态为【等待退货入库】，【是否生成换货单】的选项才能保存成功。
        OcBReturnOrder ocbReturnOrder = ocBReturnOrderFiMapper.selectById(objId);

        // 判断 是否生成换货单这个字段有没有变化 发生了变化,在进行 判断有在退换货单状态为【等待退货入库】【是否生成换货单】的选项才能保存成功。
        if (ocbReturnOrder.getIsReturnOrderExchange() == null ||
                ocbReturnOrder.getIsReturnOrderExchange().equals(returnOrder.getIsReturnOrderExchange()) ||
                ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal().equals(ocbReturnOrder.getReturnStatus())) {
            returnOrder = checkWmsCtrHouse(returnOrder);
            QueryWrapper<OcBReturnOrder> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("id", objId);
            //ocBReturnOrderFiMapper.updateById(returnOrder);
            ocBReturnOrderFiMapper.update(returnOrder, queryWrapper);
        } else {
            if (ocbReturnOrder.getIsReturnOrderExchange().equals(returnOrder.getIsReturnOrderExchange())) {
                returnOrder = checkWmsCtrHouse(returnOrder);
                //ocBReturnOrderFiMapper.updateById(returnOrder);
                QueryWrapper wrapper = new QueryWrapper();
                wrapper.eq("id", objId);
                ocBReturnOrderFiMapper.update(returnOrder, wrapper);
            } else {
                // 发生了变化 ，在进行 判断有在退换货单状态为【等待退货入库】，【是否生成换货单】的选项才能保存成功。
                if (ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal().equals(ocbReturnOrder.getReturnStatus())) {
                    returnOrder = checkWmsCtrHouse(returnOrder);
                    //ocBReturnOrderFiMapper.updateById(returnOrder);
                    QueryWrapper wrapper = new QueryWrapper();
                    wrapper.eq("id", objId);
                    ocBReturnOrderFiMapper.update(returnOrder, wrapper);
                } else {
                    returnOrder = checkWmsCtrHouse(returnOrder);
                    returnOrder.setIsReturnOrderExchange(ocbReturnOrder.getIsReturnOrderExchange());
                    //ocBReturnOrderFiMapper.updateById(returnOrder);
                    QueryWrapper wrapper = new QueryWrapper();
                    wrapper.eq("id", objId);
                    ocBReturnOrderFiMapper.update(returnOrder, wrapper);
                }
            }
        }
        // 专门用来更新 入库实体仓库 为空的情况
        if (phyWarehouseInId == null) {
            ocBReturnOrderFiMapper.updateBycpCPhyWarehouseInId(phyWarehouseInId, returnOrder.getId());
        }
        // 假如勾选了是否预留库存
        if (IsReservedEnum.YES.getValue().equals(returnOrder.getIsReserved())) {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("==========returnId/是否预留库存=", returnOrder.getId(),
                        returnOrder.getIsReserved()));
            }
            // 换货明细和零售发货单sku不一样 重新生成
            compareOcBorderService.compareOcBorder(returnOrderExchanges, returnOrder, user);
        }
        // 新增退换货订单日志记录
        addReturnOrderLog(user, objId, "退货单修改", String.format("修改%s成功", updateMsg));

        OcBReturnOrder ocBReturnOrder = ocBReturnOrderFiMapper.selectByid(objId);
        OcBReturnOrder ocBReturnOrder1 = checkBillType(ocBReturnOrder);

        //解密收货人信息
        decryptBuyerInfo(ocBReturnOrder1, null, ocbReturnOrder);
        //如果oaid有值，判断字段收货人、收货人手机、收货人电话是否有一个包含*，
        //如果没有一个包含*，清空oaid值 (只针对手工创建的退换货单)
        if (YesNoEnum.Y.getVal().equals(ocbReturnOrder.getIsAdd()) && StringUtils.isNotBlank(ocbReturnOrder.getOaid())) {
            boolean encrypted = getNotNullString(ocBReturnOrder1.getReceiveName()).contains("*") ||
                    getNotNullString(ocBReturnOrder1.getReceiveMobile()).contains("*") ||
                    getNotNullString(ocBReturnOrder1.getReceivePhone()).contains("*");
            if (!encrypted) {
                ocBReturnOrder1.setOaid("");
            } else {
                ocBReturnOrder1.setOaid(ocbReturnOrder.getOaid());
            }
        }

        //初始化失败次数
        ocBReturnOrder1.setToDrpCount(0);
        ocBReturnOrder1.setQtyWmsFail(0L);
        ocBReturnOrderFiMapper.updateById(ocBReturnOrder1);
        relation.setReturnOrderInfo(returnOrder);
    }

    /**
     * 退回物流公司编码查询
     *
     * @param logisticsId
     * @return
     */
    private String getLogisticsCode(Long logisticsId) {
        try {
            CpLogistics cpLogistics = cpRpcService.cpLogisticsInfo(logisticsId);
            if (cpLogistics != null) {
                return cpLogistics.getEcode();
            }
        } catch (Exception e) {
            log.error(LogUtil.format("查找物流公司编码异常,error:{},根据物流公司id=", logisticsId),
                    Throwables.getStackTraceAsString(e));
        }
        return null;
    }

    private CpLogistics getLogistics(Long logisticsId) {
        try {
            CpLogistics cpLogistics = cpRpcService.cpLogisticsInfo(logisticsId);
            return cpLogistics;
        } catch (Exception e) {
            log.error(LogUtil.format("查找物流公司编码异常,error:{},根据物流公司id=", logisticsId),
                    Throwables.getStackTraceAsString(e));
        }
        return null;
    }

    /**
     * 新增退换货单
     *
     * @param user     操作用户
     * @param copyType copyType
     * @param relation 退单明细
     * @return
     */
    private void addReturnOrder(User user, Long copyType, OcBReturnOrderRelation relation) {

        OcBReturnOrder returnOrder = relation.getReturnOrderInfo();
        List<OcBReturnOrderRefund> refunds = relation.getOrderRefundList();
        List<OcBReturnOrderExchange> exchanges = relation.getOrderExchangeList();

        Integer billType = returnOrder.getBillType();
        AssertUtil.notNull(billType, "退换货单: 单据类型不能为空");

        Long origOrderId = returnOrder.getOrigOrderId(); //原始订单编号
        String origSourceCode = returnOrder.getOrigSourceCode();
        OcBOrder ocBOrder = ocBOrderMapper.selectById(origOrderId);
        relation.setOcBOrder(ocBOrder);

        //匹配业务类型
        StCBusinessType resultType = omsRefundOrderService.queryReturnOrderType(ocBOrder);
        relation.getReturnOrderInfo().setBusinessTypeId(resultType.getId());
        relation.getReturnOrderInfo().setBusinessTypeCode(resultType.getEcode());
        relation.getReturnOrderInfo().setBusinessTypeName(resultType.getEname());


        AssertUtil.assertException(ocBOrder == null, "新增退单失败，原因是未找到原始订单");

        int orderStatus = ocBOrder.getOrderStatus();
        String sourceCode = ocBOrder.getSourceCode();
        boolean validRefunds = (OcReturnBillTypeEnum.RETURN.getVal().equals(billType)) && refunds.size() < 1;
        AssertUtil.assertException(validRefunds, "无退单明细，不能生成对应退单");

        boolean validExchanges = OcReturnBillTypeEnum.EXCHANGE.getVal().equals(billType) && exchanges.size() < 1;
        AssertUtil.assertException(validExchanges, "无退单明细，不能生成对应退换单");

        boolean validOrigOrderStatus = !OmsOrderStatus.PLATFORM_DELIVERY.toInteger().equals(orderStatus)
                && !OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger().equals(orderStatus)
                && !OmsOrderStatus.DEAL_DONE.toInteger().equals(orderStatus);
        AssertUtil.assertException(validOrigOrderStatus, "原始订单: " + origOrderId + "，订单未发货不能新增退换货订单");

        // 判断判断退单数据中的原始平台单号和原单中的平台单号是否一致
        if (!StringUtils.isBlank(sourceCode) && !sourceCode.equals(origSourceCode)) {
            returnOrder.setOrigSourceCode(sourceCode);
        }

        boolean notExistInOrigItems = skuExist(origOrderId, refunds);
        AssertUtil.assertException(notExistInOrigItems, "不允许添加不在原单内的商品");

        String allSku = "";
        //判断退单明细中是否有组合商品
        if (refunds.size() > 0) {
            // @20200811 bug#20832 校验可退数量 -- 数量校验逻辑和转单统一
            boolean qtyFlag = OmsOrderQtyCalculateService.getInstance().checkQtyCanReturn(refunds, origOrderId);
            AssertUtil.assertException(!qtyFlag, "退货数量大于发货订单的可退数量，不允许保存");

            for (OcBReturnOrderRefund refund : refunds) {
                allSku += refund.getPsCSkuEcode() + "(" + refund.getQtyRefund() + "),";
                try {
                    checkReEcode(refund);
                } catch (Exception e) {
                    AssertUtil.assertException(true, "此退货单含有组合商品明细，请检查后修改为实际出入库条码后重新操作");
                }
            }
        }
        boolean isMinusReturnAmtActual = returnOrder.getReturnAmtActual() != null
                && returnOrder.getReturnAmtActual().compareTo(BigDecimal.ZERO) < 0;
        AssertUtil.assertException(isMinusReturnAmtActual, "退单总金额不能为负，请修改后再保存");

        //解密收货人信息
        decryptBuyerInfo(returnOrder, ocBOrder, new OcBReturnOrder());
        //如果oaid有值，判断字段收货人、收货人手机、收货人电话是否有一个包含*，
        //如果没有一个包含*，清空oaid值(只针对手工创建的退换货单,这里就是手工新增的，不需要再判断了)
        if (StringUtils.isNotBlank(ocBOrder.getOaid())) {
            boolean encrypted = getNotNullString(returnOrder.getReceiveName()).contains("*") ||
                    getNotNullString(returnOrder.getReceiveMobile()).contains("*") ||
                    getNotNullString(returnOrder.getReceivePhone()).contains("*");
            if (!encrypted) {
                returnOrder.setOaid("");
            } else {
                returnOrder.setOaid(ocBOrder.getOaid());
            }
        }

        //防止allSku超长
        if(allSku != null && allSku.length()>500){
            allSku = allSku.substring(0,495);
        }
        // 新增退换货单
        if (billType.equals(OcReturnBillTypeEnum.RETURN.getVal())) {
            // 新增退货单
            this.createRefundOrder(user, copyType, relation, allSku);
        } else {

            ValueHolderV14 exchange = this.createExchange(user, copyType, relation, allSku);
            boolean isThrow = exchange == null || ResultCode.FAIL == exchange.getCode();
            String msg = exchange == null ? "新增退换货单异常" : exchange.getMessage();
            AssertUtil.assertException(isThrow, msg);
        }

        if (log.isDebugEnabled()) {
            log.debug(" saveChangingOrRefunding relation info:{}", JSONObject.toJSONString(relation));
        }
    }


    /**
     * 获取不为空的字符串
     *
     * @param value 字符
     * @return 最终结果
     */
    private String getNotNullString(String value) {
        return Optional.ofNullable(value).orElse("");
    }

    /**
     * 新增退换货单
     *
     * @param user     用户信息
     * @param copyType 退换货单复制类型
     * @param relation 退换货订单关系
     * @param allSku   换货订单sku信息
     * @return vh 新增结果
     */
    private ValueHolderV14 createExchange(User user, Long copyType, OcBReturnOrderRelation relation, String allSku) {
        OcBOrder ocBOrder = relation.getOcBOrder();
        List<OcBReturnOrderExchange> exchanges = relation.getOrderExchangeList();
        List<OcBReturnOrderRefund> refunds = relation.getOrderRefundList();
        OcBReturnOrder returnOrder = relation.getReturnOrderInfo();
        // 退换货单
        //701新增(退换货的换货数量不能超过退货数量)校验
        ValueHolderV14 vh = new ValueHolderV14();
        BigDecimal exchangeQty = BigDecimal.ZERO;
        BigDecimal refundQty = BigDecimal.ZERO;
        for (OcBReturnOrderExchange returnOrderExchange : exchanges) {
            exchangeQty = exchangeQty.add(returnOrderExchange.getQtyExchange());
        }
        for (OcBReturnOrderRefund returnOrderRefund : refunds) {
            refundQty = refundQty.add(returnOrderRefund.getQtyRefund());
        }
        if (exchangeQty.compareTo(refundQty) > 0) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("退换货的换货数量不能超过退货数量");
            return vh;
        }
        //是否生成预留库存
        Long id = ModelUtil.getSequence("oc_b_return_order");
        if (IsReservedEnum.YES.getValue().equals(returnOrder.getIsReserved())) {
            returnOrder.setId(id);
            //设置是否手工新增
            returnOrder.setIsAdd(1);
            //勾选了换货预留库存参数，调用生成换货订单服务
            IpOrderReturnRelation ocBOrderRelation = new IpOrderReturnRelation();
            OcBOrder newOrder = new OcBOrder();
            List<OcBOrderItem> list = new ArrayList<>();
            //生成订单主表对象
            newOrder.setOrderSource("手工新增");
            newOrder.setCpCShopId(returnOrder.getCpCShopId());
            newOrder.setQtyAll(returnOrder.getQtyInstore());
            newOrder.setCpCShopTitle(returnOrder.getCpCShopTitle());
            newOrder.setUserNick(returnOrder.getBuyerNick());
            newOrder.setProductAmt(returnOrder.getExchangeAmt());
            newOrder.setOrderAmt(returnOrder.getExchangeAmt());
            newOrder.setReceivedAmt(returnOrder.getExchangeAmt());
            newOrder.setAmtReceive(returnOrder.getExchangeAmt());
            newOrder.setReceiverName(returnOrder.getReceiveName());
            newOrder.setReceiverMobile(returnOrder.getReceiveMobile());
            newOrder.setReceiverPhone(returnOrder.getReceivePhone());
            newOrder.setCpCRegionProvinceId(returnOrder.getReceiverProvinceId());
            newOrder.setCpCRegionCityId(returnOrder.getReceiverCityId());
            newOrder.setCpCRegionAreaId(returnOrder.getReceiverAreaId());
            newOrder.setCpCRegionTownEname(returnOrder.getCpCRegionTownEname());
            newOrder.setReceiverAddress(returnOrder.getReceiveAddress());
            newOrder.setReceiverZip(returnOrder.getReceiveZip());
            newOrder.setOrderSource(OcOrderTagEum.TAG_HAND.getVal());
            newOrder.setOrigOrderId(returnOrder.getOrigOrderId());
            newOrder.setOrigReturnOrderId(id);
            newOrder.setSellerMemo(returnOrder.getRemark());
            newOrder.setPlatform(returnOrder.getPlatform());
            newOrder.setOaid(returnOrder.getOaid());
            if (returnOrder.getTbDisputeId() == null) {
                newOrder.setSourceCode(returnOrder.getTid());
            } else {
                newOrder.setSourceCode(returnOrder.getTbDisputeId().toString());
            }
            newOrder.setGwSourceCode(ocBOrder.getGwSourceCode());
            newOrder.setMergeSourceCode(returnOrder.getTid());
            newOrder.setTid(returnOrder.getTid());
            //订单明细
            for (OcBReturnOrderExchange returnOrderExchange : exchanges) {
                OcBOrderItem ocBOrderItem = new OcBOrderItem();
                ocBOrderItem.setReturnOrderId(returnOrder.getId());
                ocBOrderItem.setBarcode(returnOrderExchange.getBarcode());
                ocBOrderItem.setPsCProId(returnOrderExchange.getPsCProId());
                ocBOrderItem.setPsCProEcode(returnOrderExchange.getPsCProEcode());
                ocBOrderItem.setPsCProEname(returnOrderExchange.getPsCProEname());
                ocBOrderItem.setSkuSpec(returnOrderExchange.getSkuSpec());
                ocBOrderItem.setPsCSkuId(returnOrderExchange.getPsCSkuId());
                ocBOrderItem.setPsCSkuEcode(returnOrderExchange.getPsCSkuEcode());
                ocBOrderItem.setOoid(returnOrderExchange.getOid());
                ocBOrderItem.setPriceList(returnOrderExchange.getPrice());
                ocBOrderItem.setPrice(returnOrderExchange.getAmtRefund().divide(returnOrderExchange.getQtyExchange(),
                        4, BigDecimal.ROUND_HALF_UP));
                if (returnOrderExchange.getPrice() != null) {
                    /*ocBOrderItem.setAdjustAmt(returnOrderExchange.getAmtRefund().subtract(returnOrderExchange.getPrice().multiply(returnOrderExchange.getQtyExchange())));*/
                    // 调整金额 = 成交金额-平台售价*数量
                    ocBOrderItem.setAdjustAmt(returnOrderExchange.getAmtRefund().subtract(ocBOrderItem.getPrice().multiply(returnOrderExchange.getQtyExchange())));
                }
                ocBOrderItem.setPsCSizeEname(returnOrderExchange.getPsCSizeEname());
                ocBOrderItem.setPsCSizeEcode(returnOrderExchange.getPsCSizeEcode());
                ocBOrderItem.setPsCSizeId(returnOrderExchange.getPsCSizeId());
                ocBOrderItem.setPsCClrId(returnOrderExchange.getPsCClrId());
                ocBOrderItem.setPsCClrEcode(returnOrderExchange.getPsCClrEcode());
                ocBOrderItem.setPsCClrEname(returnOrderExchange.getPsCClrEname());
                ocBOrderItem.setRealAmt(returnOrderExchange.getAmtRefund());
                ocBOrderItem.setQty(returnOrderExchange.getQtyExchange());
                ocBOrderItem.setIsactive(IsActiveEnum.Y.getKey());
                BigDecimal priceActual = Optional.ofNullable(ocBOrderItem.getRealAmt()).orElse(BigDecimal.ZERO).divide(ocBOrderItem.getQty(), 4, BigDecimal.ROUND_HALF_UP);
                ocBOrderItem.setPriceActual(priceActual);
                ocBOrderItem.setPriceSettle(priceActual);
                ocBOrderItem.setTotPriceSettle(ocBOrderItem.getRealAmt());
                //京东优惠金额
                //ocBOrderItem.setReserveDecimal04(returnOrderExchange.getReserveDecimal04());
//                if (returnOrder.getTbDisputeId() == null) {
                ocBOrderItem.setTid(returnOrder.getTid());
//                } else {
//                    ocBOrderItem.setTid(returnOrder.getTbDisputeId().toString());
//                }
                convertProductType(ocBOrderItem);
                list.add(ocBOrderItem);
            }
            newOrder.setSkuKindQty(BigDecimal.valueOf(list.size(), 4));
            //原单奶卡
            ocBReturnBuildService.buildOrderNaikaFromOrigOrder(ocBOrder, ocBOrderRelation);
            //调用生成换货订单服务
            ocBOrderRelation.setOcBOrder(newOrder);
            ocBOrderRelation.setOcBOrderItems(list);
            ocBOrderRelation.setOcBReturnOrder(returnOrder);
            ocBOrderRelation.setForbidHoldExchangeOrder(false);
            try {
                ValueHolderV14 result = ocBReturnBuildService.buildExchange(ocBOrderRelation, user);
                if (result.getCode() == ResultCode.FAIL) {
                    vh.setCode(ResultCode.FAIL);
                    vh.setMessage(result.getMessage());
                    return vh;
                }
                try {
                    log.info("换货开始调用wing接口hold,库存orderId{}", ocBOrderRelation.getOcBOrder().getId());
                    //插入换货hold传wing中间表
                    orderExchangeHoldTowingTaskService.creatExchangeHoldTowingTask(ocBOrderRelation.getOcBOrder().getId(), ocBOrderRelation.getOcBOrder().getBillNo(), ExchangeHoldTowingcConstant.STATUS_0);
                } catch (Exception e) {
                    log.info("换货开始调用wing接口hold住库存异常,orderId{}", ocBOrderRelation.getOcBOrder().getId());
                }
            } catch (Exception e) {
                log.error(LogUtil.format("调用生成换货订单服务异常,error:{}"), Throwables.getStackTraceAsString(e));
                AssertUtil.assertException(true, "调用生成换货订单服务异常");
            }
        }
        CpLogistics cpLogistics = getLogistics(returnOrder.getCpCLogisticsId());
        if (ObjectUtil.isNotNull(cpLogistics)) {
            returnOrder.setCpCLogisticsEcode(cpLogistics.getEcode());
            returnOrder.setCpCLogisticsEname(cpLogistics.getEname());
        }
        setExchangePhyWarehouseInId(returnOrder, ocBOrder.getCpCShopId());
        checkWmsCtrHouse(returnOrder);
        returnOrder.setId(id);
        returnOrder.setReturnStatus(ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal());
        //待入库
        returnOrder.setProReturnStatus(ProReturnStatusEnum.WAIT.getVal());
        returnOrder.setIsAdd(1);
        returnOrder.setIsToag(0);
        returnOrder.setIsTransfer(0);
        returnOrder.setIsTodrp(0);
        returnOrder.setInventedStatus(0);
        returnOrder.setIsTowms(0);
        returnOrder.setIsReceiveConfirm(0);
        returnOrder.setWmsCancelStatus(0);
        returnOrder.setIsForce(0);
        returnOrder.setIsactive("Y");
        returnOrder.setAllSku(allSku);
        OperateUserUtils.saveOperator(returnOrder, user);
        // 退单编号
        returnOrder.setBillNo(sequenceUtil.buildReturnBillNo());
        returnOrder.setOrigOrderNo(ocBOrder.getBillNo());
        if (TaobaoReturnOrderExt.ReturnBillsStatus.GOODS_INTERCEPT.getCode().equals(returnOrder.getReturnProType())) {
            returnOrder.setIntercerptStatus(InterceptStatus.NO_LAUNCH_INTERCEPT.getCode());
        }
        // 设置店铺信息
        setShopInfo(returnOrder);
        //初始化失败次数
        returnOrder.setToDrpCount(0);
        returnOrder.setQtyWmsFail(0L);

        if (returnOrder.getIsBack() != null
                && returnOrder.getIsBack() == 1
                && StringUtils.isNotEmpty(returnOrder.getCpCLogisticsEcode())
                && returnOrder.getCpCLogisticsEcode().equals(OcCommonConstant.DNKD)
                && StringUtils.isNotEmpty(returnOrder.getLogisticsCode())
                && !returnOrder.getLogisticsCode().contains("T")) {
            returnOrder.setLogisticsCode("T" + returnOrder.getLogisticsCode());
        }
        ocBReturnOrderFiMapper.insert(returnOrder);
        OcBOrder ocBOrder1 = new OcBOrder();
        ocBOrder1.setReturnStatus(1);
        //ocBOrder.setReturnStatus(1);
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("id", ocBOrder.getId());
        //ocBOrderMapper.updateById(ocBOrder);
        ocBOrderMapper.update(ocBOrder1, wrapper);

        for (int i = 0; i < exchanges.size(); i++) {
            OcBReturnOrderExchange exchange = exchanges.get(i);
            Long exchangeId = ModelUtil.getSequence("oc_b_return_order_exchange");
            exchange.setOcBReturnOrderId(id);
            exchange.setId(exchangeId);
            exchange.setOwnerid(user.getId().longValue());
            exchange.setOwnername(user.getName());
            exchange.setOwnerename(user.getEname());
            exchange.setCreationdate(new Date());
            exchange.setAdClientId((long) user.getClientId());
            exchange.setAdOrgId((long) user.getOrgId());
            ocBReturnOrderExchangeFiMapper.insert(exchange);
        }
        // 退单明细oid集合
        List<OcBOrderItem> ocBOrderItems = ocBorderItemMapper.selectItemList(returnOrder.getOrigOrderId());
        Map<Long, String> oidMap = getOidMap(ocBOrderItems);
        Map<Long, String> tidMap = getTidMap(ocBOrderItems);
        Set<String> tidSet = new HashSet<>();
        for (int i = 0; i < refunds.size(); i++) {
            OcBReturnOrderRefund refund = refunds.get(i);
            Long ocBOrderItemId = refund.getOcBOrderItemId();
            Long reId = ModelUtil.getSequence("oc_b_return_order_refund");
            if (oidMap.size() > 0 && oidMap.containsKey(ocBOrderItemId)) {
                refund.setOid(oidMap.get(ocBOrderItemId));
            }
            if (tidMap.size() > 0 && tidMap.containsKey(ocBOrderItemId)) {
                refund.setTid(tidMap.get(ocBOrderItemId));
                tidSet.add(tidMap.get(ocBOrderItemId));
            }
            refund.setOcBReturnOrderId(id);
            refund.setId(reId);
            OperateUserUtils.saveOperator(refund, user);
            refund.setOcBOrderId(returnOrder.getOrigOrderId());
            ocBReturnOrderRefundFiMapper.insert(refund); //新增明细
            //加上订单表中的 已退数量
            QueryWrapper<OcBOrderItem> queryWrapper = new QueryWrapper<>();
            setOrderItemRetuenCount(ocBOrder.getId(), refund, ocBOrderItemId, queryWrapper);
        }
        String tid = String.join(",", tidSet);
        returnOrder.setTid(tid);
        ocBReturnOrderFiMapper.updateById(returnOrder);
        // 添加新增日志到退单操作日志中
        setLogTypeAndMsg(copyType, user, id, ocBOrder);
        vh.setMessage("成功！");
        vh.setData(returnOrder.getId());
        return vh;
    }

    /**
     * 退换货订单日志赋值
     *
     * @param copyType 退换货单新增方式
     * @param user     操作用户
     * @param id       退换货单ID
     * @param ocBOrder 订单主表信息
     */
    private void setLogTypeAndMsg(Long copyType, User user, Long id, OcBOrder ocBOrder) {
        String logType;
        String logMessage;
        if (Objects.nonNull(copyType) && copyType == 1) {
            logType = "手工新增复制退换货单";
            logMessage = "手工新增复制退单成功";
        } else {
            logType = "手工新增退换货单";
            logMessage = "手工新增退单成功";
        }
        addReturnOrderLog(user, id, logType, logMessage);
        // 新增退单成功添加订单日志
        omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                OrderLogTypeEnum.REFUND_ORDER_ADD.getKey(), logMessage, null, null, user);
    }

    /**
     * 退换货订单日志记录
     *
     * @param user    操作用户
     * @param objId   退换货订单ID
     * @param logType 订单类型
     * @param msg     订单修改消息
     */
    private void addReturnOrderLog(User user, Long objId, String logType, String msg) {
        Long logId = ModelUtil.getSequence("oc_b_return_order_log");
        OcBReturnOrderLog ocBReturnOrderLog = this.getLog(logId, logType, msg, user.getName(), objId);
        ocBReturnOrderLog.setIpAddress(user.getLastloginip());
        ocBReturnOrderLog.setAdClientId((long) user.getClientId());
        ocBReturnOrderLog.setAdOrgId((long) user.getOrgId());
        ocBReturnOrderLog.setOwnerid((long) user.getId());
        ocBReturnOrderLog.setOwnername(user.getName());
        ocBReturnOrderLog.setOwnerename(user.getEname());
        ocBReturnOrderLog.setCreationdate(new Date());
        ocBReturnOrderLogMapper.insert(ocBReturnOrderLog);
    }

    /**
     * @param ocBOrderItem 订单明细商品类型处理
     */
    private void convertProductType(OcBOrderItem ocBOrderItem) {
        ProductSku productSku = psRpcService.selectProductSku(ocBOrderItem.getPsCSkuEcode());
        if (productSku == null) {
            throw new NDSException("条码: " + ocBOrderItem.getPsCSkuEcode() + ", 未查询到对应商品信息");
        }
        int skuType = productSku.getSkuType();
        Long proType;
        if (skuType == 1 || skuType == 2) {
            proType = 4L;
        } else {
            proType = Long.valueOf(skuType);
        }
        ocBOrderItem.setProType(proType);
        // 补充效期和品类
        ocBOrderItem.setMDim4Id(productSku.getMDim4Id());
        ocBOrderItem.setMDim6Id(productSku.getMDim6Id());
        if ("Y".equals(productSku.getIsEnableExpiry())) {
            ocBOrderItem.setIsEnableExpiry(1);
        } else {
            ocBOrderItem.setIsEnableExpiry(0);
        }
    }

    /**
     * 新增退货单
     *
     * @param user     当前用户
     * @param copyType 手工新增复制退货单、手工新增退货单 标识
     * @param allSku   退货明细sku
     */
    private void createRefundOrder(User user, Long copyType, OcBReturnOrderRelation returnOrderRelation, String allSku) {
        OcBReturnOrder returnOrder = returnOrderRelation.getReturnOrderInfo();
        //退货单 或者预退货
        Long id = ModelUtil.getSequence("oc_b_return_order");
        String origOrderNo = returnOrder.getBillNo();
        returnOrder.setOrigOrderNo(origOrderNo);
        returnOrder.setProReturnStatus(ProReturnStatusEnum.WAIT.getVal());
        OcBOrder ocBOrder = returnOrderRelation.getOcBOrder();
        setPhyWarehouseInId(returnOrder, ocBOrder.getCpCShopId());
        CpLogistics cpLogistics = getLogistics(returnOrder.getCpCLogisticsId());
        if (ObjectUtil.isNotNull(cpLogistics)) {
            returnOrder.setCpCLogisticsEcode(cpLogistics.getEcode());
            returnOrder.setCpCLogisticsEname(cpLogistics.getEname());
        }
        // 检查管控仓判断
        returnOrder = this.checkWmsCtrHouse(returnOrder);
        returnOrder.setId(id);
        returnOrder.setReturnStatus(ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal());
        returnOrder.setIsAdd(1);
        returnOrder.setIsToag(0);
        returnOrder.setIsTransfer(0);
        returnOrder.setIsTodrp(0);
        returnOrder.setInventedStatus(0);
        returnOrder.setIsTowms(0);
        returnOrder.setIsReceiveConfirm(0);
        returnOrder.setWmsCancelStatus(0);
        returnOrder.setIsForce(0);
        returnOrder.setAllSku(allSku);
        returnOrder.setConfirmStatus(ReturnOrderConfirmStatusEnum.NOT_CONFIRM.getKey());
        OperateUserUtils.saveOperator(returnOrder, user);
        returnOrder.setIsactive("Y");
        // 新增默认拦截状态为未拦截
        if (TaobaoReturnOrderExt.ReturnBillsStatus.GOODS_INTERCEPT.getCode().equals(returnOrder.getReturnProType())) {
            returnOrder.setIntercerptStatus(InterceptStatus.NO_LAUNCH_INTERCEPT.getCode());
        }
        returnOrder.setAdOrgId((long) user.getOrgId());
        // 退单编号
        returnOrder.setBillNo(sequenceUtil.buildReturnBillNo());
        returnOrder.setOrigOrderNo(ocBOrder.getBillNo());
        // 设置店铺信息
        setShopInfo(returnOrder);

        if (returnOrder.getIsBack() != null
                && returnOrder.getIsBack() == 1
                && StringUtils.isNotEmpty(returnOrder.getCpCLogisticsEcode())
                && returnOrder.getCpCLogisticsEcode().equals(OcCommonConstant.DNKD)
                && StringUtils.isNotEmpty(returnOrder.getLogisticsCode())
                && !returnOrder.getLogisticsCode().contains("T")) {
            returnOrder.setLogisticsCode("T" + returnOrder.getLogisticsCode());
        }
        // 校验是否满足条件转成预退货还是退货
        OcBReturnOrder ocBReturnOrder = checkBillType(returnOrder);
        // 是否换货预留库存为否
        ocBReturnOrder.setIsReserved(IsReservedEnum.NO.getValue());
        //初始化失败次数
        ocBReturnOrder.setToDrpCount(0);
        ocBReturnOrder.setQtyWmsFail(0L);
        ocBReturnOrder.setGwSourceCode(ocBOrder.getGwSourceCode());
        // 新增主表
        ocBReturnOrderFiMapper.insert(ocBReturnOrder);
        ocBOrder.setReturnStatus(1);
        ocBOrderMapper.updateById(ocBOrder);
        List<OcBReturnOrderRefund> refunds = returnOrderRelation.getOrderRefundList();
        List<OcBOrderItem> ocBOrderItems = ocBorderItemMapper.selectItemList(returnOrder.getOrigOrderId());
        Map<Long, String> oidMap = getOidMap(ocBOrderItems);
        Map<Long, String> tidMap = getTidMap(ocBOrderItems);
        Set<String> tidSet = new HashSet<>();
        for (int i = 0; i < refunds.size(); i++) {
            OcBReturnOrderRefund refund = refunds.get(i);

            if (OrderBusinessTypeCodeEnum.MILK_CARD_PICK_UP_GOODS_RETURN.getCode().equals(ocBReturnOrder.getBusinessTypeCode()) ||
                    OrderBusinessTypeCodeEnum.CYCLE_PURCHASE_ORDER_PICK_UP_RETURN.getCode().equals(ocBReturnOrder.getBusinessTypeCode())) {
                refund.setAmtRefundSingle(new BigDecimal("0.01"));
                refund.setAmtRefund(refund.getAmtRefundSingle().multiply(refund.getQtyRefund()));
                refund.setPriceSettle(new BigDecimal("0.01"));
                refund.setAmtSettleTot(refund.getPriceSettle().multiply(refund.getQtyRefund()));
            }
            Long ocBOrderItemId = refund.getOcBOrderItemId();
            Long reId = ModelUtil.getSequence("oc_b_return_order_refund");
            refund.setOcBReturnOrderId(id);
            refund.setId(reId);
            if (oidMap.size() > 0 && oidMap.containsKey(ocBOrderItemId)) {
                refund.setOid(oidMap.get(ocBOrderItemId));
            }
            if (tidMap.size() > 0 && tidMap.containsKey(ocBOrderItemId)) {
                refund.setTid(tidMap.get(ocBOrderItemId));
                tidSet.add(tidMap.get(ocBOrderItemId));
            }
            OperateUserUtils.saveOperator(refund, user);
            // 原单ID（发货单） @20200714
            refund.setOcBOrderId(returnOrder.getOrigOrderId());
            // 新增明细
            ocBReturnOrderRefundFiMapper.insert(refund);
            QueryWrapper<OcBOrderItem> queryWrapper = new QueryWrapper<>();
            this.setOrderItemRetuenCount(returnOrder.getOrigOrderId(), refund, ocBOrderItemId, queryWrapper);
        }
        ocBReturnOrder.setTid(String.join(",", tidSet));
        ocBReturnOrderFiMapper.updateById(ocBReturnOrder);
        // 添加新增日志到退单操作日志中
        setLogTypeAndMsg(copyType, user, id, ocBOrder);
    }

    /**
     * <AUTHOR>
     * @Date 15:17 2021/5/19
     * @Description 查找系统参数 “退单无物流单号延迟推单”  【退单无物流单号延迟推单】系统参数+【退换货单】
     */
    public Date PushDelayTime(OcBReturnOrder returnOrder) {
        // 获取业务系统参数里的无物流单号推单延迟推送时间设置限制

        try {
            //记录物流信息更改时间
            OcBReturnOrder searchOrder = ocBReturnOrderFiMapper.selectByid(ObjectUtils.isEmpty(returnOrder) ? -1L :
                    Optional.ofNullable(returnOrder.getId()).orElse(-1L));
            String oldLogisticCode = "";
            if (!ObjectUtils.isEmpty(searchOrder)) {
                oldLogisticCode = searchOrder.getLogisticsCode();
            }
            if (!ObjectUtils.isEmpty(returnOrder) && !ObjectUtils.isEmpty(returnOrder.getId()) &&
                    !ObjectUtils.isEmpty(returnOrder.getLogisticsCode()) &&
                    !returnOrder.getLogisticsCode().equals(oldLogisticCode)) {
                nodeRecordService.insertByNode(ReturnOrderNodeEnum.LOGISTIC_MODIFIED_TIME, new Date(),
                        returnOrder.getId(), SystemUserResource.getRootUser());
            }

            if (omsSystemConfig.isResetDelayTime()) {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("开启根据物流信息更新延迟推送退单时间开始"));
                }
                if (returnOrder != null) {
                    if (StringUtils.isNotEmpty(returnOrder.getLogisticsCode())) {
                        return new Date();
                    } else {
                        if (searchOrder != null) {
                            if (StringUtils.isNotEmpty(searchOrder.getLogisticsCode())) {
                                return new Date();
                            }
                        }
                    }
                }
            }
            int num = pushDelayTime;
            String delayTimeStr = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(PUSH_DELAY_TIME_KEY);
            if (StringUtils.isNotEmpty(delayTimeStr)) {
                num = Integer.valueOf(delayTimeStr);
            }
            return DateConversionUtil.plusHours(new Date(), num);
        } catch (Exception e) {
            return DateConversionUtil.plusHours(new Date(), 72);
        }

    }


    private Map<Long, String> getTidMap(List<OcBOrderItem> ocBOrderItems) {
        return ocBOrderItems.stream().filter(x -> Objects.nonNull(x.getTid())).collect(
                Collectors.toMap(OcBOrderItem::getId, OcBOrderItem::getTid));
    }

    /**
     * 退换货单店铺信息赋值
     *
     * @param returnOrder 退换货单
     */
    private void setShopInfo(OcBReturnOrder returnOrder) {
        try {
            CpShop cpShop = cpRpcService.selectShopById(returnOrder.getCpCShopId());
            if (Objects.nonNull(cpShop)) {
                returnOrder.setSellerNick(cpShop.getSellerNick());
                returnOrder.setCpCShopEcode(cpShop.getEcode());
            }
        } catch (Exception e) {
            log.error(LogUtil.format("RPC查询店铺档案信息异常,error:{}"), Throwables.getStackTraceAsString(e));
            e.printStackTrace();
        }
    }

    /**
     * 退货入库实体仓库赋值
     *
     * @param returnOrder 退换货主表
     * @param shopId      下单店铺id
     */
    private void setPhyWarehouseInId(OcBReturnOrder returnOrder, Long shopId) {
        //新增入库实体仓库字段
        // @20200714 修改退货仓逻辑
        // 原退：发货实体仓档案关联的原退入库实体仓，为空则取发货实体仓
        // 否则：取店铺策略中的默认退货实体仓，为空则为空
        // @20200714 20:18 如果前端有传值，后台就不再做处理，如果没有，则按逻辑默认赋值
        if (Objects.nonNull(returnOrder.getCpCPhyWarehouseInId())) {
            return;
        }
        if (Objects.isNull(returnOrder.getIsBack()) || returnOrder.getIsBack() == 0) {
            // 非原退，取店铺策略
            if (Objects.nonNull(returnOrder.getCpCShopId())) {
                StCShopStrategyDO shopStrategy = shopStrategyService.selectOcStCShopStrategy(shopId);

                boolean isMultiReturnWarehouse = Objects.nonNull(shopStrategy) &&
                        (Objects.isNull(shopStrategy.getIsMultiReturnWarehouse()) ||
                                shopStrategy.getIsMultiReturnWarehouse() == 0);
                if (isMultiReturnWarehouse) {
                    Long wareId = shopStrategy.getCpCWarehouseDefId() == null ? shopStrategy.getDefaultStoreId() : shopStrategy.getCpCWarehouseDefId();
                    returnOrder.setCpCPhyWarehouseInId(wareId);
                }
            }
        } else {
            // 原退，取发货实体仓档案
            if (Objects.nonNull(returnOrder.getCpCPhyWarehouseId())) {
                CpCPhyWarehouse cpCPhyWarehouse = cpRpcService.queryByWarehouseId(
                        returnOrder.getCpCPhyWarehouseId());
                // @20200721 原先取错为消退，实际要的是原退：getOriginalReturnPhyWarehouseId
                if (Objects.nonNull(cpCPhyWarehouse) && Objects.nonNull(
                        cpCPhyWarehouse.getOriginalReturnPhyWarehouseId())) {
                    returnOrder.setCpCPhyWarehouseInId(cpCPhyWarehouse.getOriginalReturnPhyWarehouseId());
                } else {
                    // 使用发货实体仓
                    returnOrder.setCpCPhyWarehouseInId(returnOrder.getCpCPhyWarehouseId());
                }
            }
        }

    }

    /**
     * 退换货入库实体仓库赋值
     * 退货默认取店铺策略的默认换货仓库，默认仓库没有去默认退货仓库，退货没有取默认仓库
     *
     * @param returnOrder 退换货主表
     * @param shopId      下单店铺id
     */
    private void setExchangePhyWarehouseInId(OcBReturnOrder returnOrder, Long shopId) {
        //a.	若“默认换货仓库”有值，则退回仓库取【店铺策略】的“默认换货仓库”。
        //b.	若“默认换货仓库”为空，则退回仓库取【店铺策略】的“默认退货仓库”
        if (Objects.nonNull(returnOrder.getCpCPhyWarehouseInId())) {
            return;
        }
        // 非原退，取店铺策略
        if (Objects.nonNull(returnOrder.getCpCShopId())) {
            StCShopStrategyDO shopStrategy = shopStrategyService.selectOcStCShopStrategy(shopId);
            boolean isMultiReturnWarehouse = Objects.nonNull(shopStrategy) &&
                    (Objects.isNull(shopStrategy.getIsMultiReturnWarehouse()) ||
                            shopStrategy.getIsMultiReturnWarehouse() == 0);
            if (isMultiReturnWarehouse) {
                //退换仓库默认取店铺策略的换货仓库，取不到取退货仓库，还取不到取默认仓库
                Long wareId = shopStrategy.getCpCWarehouseExchangeId() == null ? shopStrategy.getCpCWarehouseDefId() : shopStrategy.getCpCWarehouseExchangeId();
                wareId = wareId == null ? shopStrategy.getDefaultStoreId() : wareId;
                returnOrder.setCpCPhyWarehouseInId(wareId);
            }
        }

    }

    /**
     * 根据原始订单ID查询订单明细的oid
     *
     * @param ocBOrderItems 退单明细
     * @return Map key-明细ID  val-明细oid
     */
    private Map<Long, String> getOidMap(List<OcBOrderItem> ocBOrderItems) {
        //【bugId = 28490】手动新增的退换单的明细oid(子订单id)赋值 分库键查询
        return ocBOrderItems.stream().filter(x -> Objects.nonNull(x.getOoid())).collect(
                Collectors.toMap(OcBOrderItem::getId, OcBOrderItem::getOoid)
        );
    }

    @Transactional(rollbackFor = Exception.class)
    public void compareOcBorder(List<OcBReturnOrderExchange> returnOrderExchanges, OcBReturnOrder returnOrder, User user) {

        if (CollectionUtils.isEmpty(returnOrderExchanges)) {
            return;
        }

        //查询原发货单 根据平台单号ES查询订单id
        List<Long> orderIds = ES4Order.getIdsByOrigReturnOrderId(returnOrder.getId());

        if (CollectionUtils.isEmpty(orderIds)) {
            return;
        }

        OcBReturnOrderExchange ocBReturnOrderExchange = returnOrderExchanges.get(0);

        orderIds.forEach(orderId -> {
            OcBOrder order = ocBOrderMapper.selectByID(orderId);
            if (null != order && order.getOrderStatus().intValue() != OmsOrderStatus.SYS_VOID.toInteger() && order.getOrderStatus().intValue() != OmsOrderStatus.CANCELLED.toInteger()) {
                List<OcBOrderItem> ocBOrderItems = ocBorderItemMapper.queryItemsByOrderId(orderId);
                if (!CollectionUtils.isEmpty(ocBOrderItems) && ocBOrderItems.size() == 1) {
                    OcBOrderItem ocBOrderItem = ocBOrderItems.get(0);
                    if (ocBOrderItem.getPsCProEcode().equals(ocBReturnOrderExchange.getPsCProEcode()) &&
                            !Objects.equals(ocBOrderItem.getPsCSkuId(), ocBReturnOrderExchange.getPsCSkuId())) {
                        //作废原单 生成新单
                        ValueHolderV14<Object> objectValueHolderV14 = omsOrderCancellationService.doInvoildOutOrder(order, user);
                        int code = objectValueHolderV14.getCode();
                        if (code == 0) {
                            //调用生成换货订单服务
                            IpOrderReturnRelation ocBOrderRelation = new IpOrderReturnRelation();
                            order.setId(null);
                            order.setBillNo(null);
                            order.setOrderStatus(OmsOrderStatus.TO_BE_ASSIGNED.toInteger());
                            order.setModifierid(user.getId().longValue());
                            order.setModifiername(user.getName());
                            ocBOrderRelation.setOcBOrder(order);
                            ocBOrderItem.setId(null);
                            ocBOrderItem.setOcBOrderId(null);
                            ocBOrderItem.setPsCSkuId(ocBReturnOrderExchange.getPsCSkuId());
                            ocBOrderItem.setPsCSkuEcode(ocBReturnOrderExchange.getPsCSkuEcode());
                            ocBOrderItem.setPsCSkuEname(ocBReturnOrderExchange.getPsCSkuEname());
                            ocBOrderItem.setPsCClrId(ocBReturnOrderExchange.getPsCClrId());
                            ocBOrderItem.setPsCClrEcode(ocBReturnOrderExchange.getPsCClrEcode());
                            ocBOrderItem.setPsCClrEname(ocBReturnOrderExchange.getPsCClrEname());
                            ocBOrderItem.setPsCSizeEcode(ocBReturnOrderExchange.getPsCSizeEcode());
                            ocBOrderItem.setPsCSizeId(ocBReturnOrderExchange.getPsCSizeId());
                            ocBOrderItem.setPsCSizeEname(ocBReturnOrderExchange.getPsCSizeEname());

                            List<OcBOrderItem> list = Lists.newArrayList(ocBOrderItem);
                            ocBOrderRelation.setOcBOrderItems(list);
                            returnOrder.setTid(order.getSourceCode());
                            ocBOrderRelation.setOcBReturnOrder(returnOrder);
                            ValueHolderV14 result = ocBReturnBuildService.buildExchange(ocBOrderRelation, user);
                            if (result != null && result.getCode() == -1) {
                                String message = result.getMessage();
                                throw new NDSException("生成退换货订单失败!" + message);
                            }
                        } else {
                            throw new NDSException("作废零售发货单失败!");
                        }
                    }
                }
            }
        });

    }

    /**
     * 新增退换货单同时，同时新增已发货退款单
     */
    private void insertOcBReturnAfSend(User user, OcBReturnOrder returnOrder, List<OcBReturnOrderRefund> returnOrderRefunds) {
        try {
            // 构建已发货退款单主表信息
            OcBReturnAfSend afSend = new OcBReturnAfSend();
            afSend.setId(-1L);
            // 默认退货退款类型
            afSend.setBillType(0);
            afSend.setSourceBillNo(returnOrder.getOrigOrderId().toString());
            afSend.setBillNo(returnOrder.getBillNo());
            afSend.setTid(returnOrder.getTid()); // 原平台单号
            afSend.setTReturnId(returnOrder.getId().toString());
            afSend.setVipNick(returnOrder.getBuyerNick());// 买家昵称
            afSend.setReturnApplyTime(returnOrder.getReturnCreateTime());
            // 应退金额
            afSend.setAmtReturnApply(returnOrder.getReturnAmtList());
            // 实际金额
            afSend.setAmtReturnActual(returnOrder.getReturnAmtActual());
            afSend.setCpCShopTitle(returnOrder.getCpCShopTitle());
            afSend.setCpCShopEcode(returnOrder.getCpCShopEcode());
            afSend.setCpCShopId(returnOrder.getCpCShopId());
//            afSend.setPayMode("");
//            afSend.setPayAccount("");

            // 构建已发货退款单明细信息
            List<OcBReturnAfSendItem> afSendItemList = new ArrayList<>();
            for (OcBReturnOrderRefund ocBReturnOrderRefund : returnOrderRefunds) {
                OcBReturnAfSendItem afSendItem = new OcBReturnAfSendItem();
                // 原订单明细ID
                afSendItem.setId(ocBReturnOrderRefund.getOcBOrderItemId());
                afSendItem.setAmtReturn(ocBReturnOrderRefund.getAmtRefund());
                afSendItem.setFreight(BigDecimal.ZERO);
                afSendItemList.add(afSendItem);
            }

            // 构建返回对象
            JSONObject param = new JSONObject();
            param.put("objId", -1);
            param.put("AfSend", afSend);
            param.put("AfSendItem", afSendItemList);

            // 保存已发货退款单
            ValueHolderV14 result = refundFormAfterDeliveryService.saveAfterDeliver(param, user);
            if (result.getCode() != ResultCode.SUCCESS) {
                saveReturnAfSendErrorLog(user, returnOrder.getId());
            }
        } catch (Exception e) {
            saveReturnAfSendErrorLog(user, returnOrder.getId());
        }
    }

    /**
     * 保存已发货退款单错误日志
     */
    private void saveReturnAfSendErrorLog(User user, Long id) {
        Long logId = ModelUtil.getSequence("oc_b_return_order_log");
        String logType = "手工新增已发货退款单";
        OcBReturnOrderLog ocBReturnOrderLog = getLog(logId, logType, logType, user.getName(), id);
        ocBReturnOrderLog.setIpAddress(user.getLastloginip());
        ocBReturnOrderLog.setAdClientId(user.getClientId() + 0L);
        ocBReturnOrderLog.setAdOrgId(user.getOrgId() + 0L);
        ocBReturnOrderLog.setOwnerid(user.getId() + 0L);
        ocBReturnOrderLog.setOwnername(user.getName());
        ocBReturnOrderLog.setOwnerename(user.getEname());
        ocBReturnOrderLog.setCreationdate(new Date());
        ocBReturnOrderLogMapper.insert(ocBReturnOrderLog);
    }

    /**
     * 判断新增或者更新的这个是不是wms管控仓
     *
     * @param returnOrder
     */
    public OcBReturnOrder checkWmsCtrHouse(OcBReturnOrder returnOrder) {
        /**
         * 如果入库实体仓是WMS管控仓，则将reserve_bigint03此字段赋值为1，如果不是WMS管控仓或者入库实体仓为空，则此字段赋值为0.
         */
        //实体仓id
        Long cpCPhyWarehouseInId = returnOrder.getCpCPhyWarehouseInId();
        if (cpCPhyWarehouseInId == null) {
            returnOrder.setIsNeedToWms(0L);
            return returnOrder;
        }
        CpCPhyWarehouse wareHouse = cpRpcService.queryByWarehouseId(cpCPhyWarehouseInId);
        //判断这个仓是不是wms 管控仓
        if (wareHouse != null) {
            //不是wms 管控仓或者为空，是否传WMS字段（reserve_bigint03）则此字段赋值为0.
            if (wareHouse.getWmsControlWarehouse() == null || WmsControlWarehouse.NOT_CONTROL.equals(wareHouse.getWmsControlWarehouse())) {
                returnOrder.setIsNeedToWms(0L);
            } else {
                //是wms 管控仓，是否传WMS字段（reserve_bigint03）则此字段赋值为0.
                returnOrder.setIsNeedToWms(1L);
            }
        } else {
            throw new NDSException("查询不到实体仓");
        }
        return returnOrder;
    }

    /**
     * 用来新增退换货或者退货 修改订单明细已退数量 和已申请数量
     *
     * @param origOrderId
     * @param refund
     * @param ocBOrderItemId
     * @param queryWrapper
     */
    public void setOrderItemRetuenCount(Long origOrderId, OcBReturnOrderRefund refund, Long ocBOrderItemId, QueryWrapper<OcBOrderItem> queryWrapper) {
        if (Objects.nonNull(ocBOrderItemId)) {
            queryWrapper.eq("oc_b_order_id", origOrderId).eq("id", ocBOrderItemId);
            //原单明细
            OcBOrderItem ocBOrderItem = ocBorderItemMapper.selectOne(queryWrapper);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("新增退单回写前原单明细{}"), ocBOrderItem);
            }
            updateOrderItem(ocBOrderItem, refund.getQtyRefund(), true);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("新增退单回写后原单明细{}"), ocBOrderItem);
            }
        } else {
            queryWrapper.eq("oc_b_order_id", origOrderId).eq("ps_c_sku_ecode", refund.getPsCSkuEcode());
            //原单明细
            List<OcBOrderItem> ocBOrderItems = ocBorderItemMapper.selectList(queryWrapper);
            BigDecimal qtyRefund = refund.getQtyRefund();
            for (int i1 = 0; i1 < ocBOrderItems.size(); i1++) {
                if (qtyRefund.compareTo(ocBOrderItems.get(i1).getQty()) > 0) {
                    qtyRefund = qtyRefund.subtract(ocBOrderItems.get(i1).getQty());
                    if (qtyRefund.compareTo(BigDecimal.ZERO) <= 0) {
                        break;
                    }
                    updateOrderItem(ocBOrderItems.get(i1), ocBOrderItems.get(i1).getQty(), true);
                } else {
                    updateOrderItem(ocBOrderItems.get(i1), qtyRefund, true);
                    qtyRefund = qtyRefund.subtract(qtyRefund);
                    if (qtyRefund.compareTo(BigDecimal.ZERO) <= 0) {
                        break;
                    }
                }
            }
          /*  log.debug(LogUtil.format("新增退单回写前原单明细{}", ocBOrderItem);
            updateOrderItem(ocBOrderItem, refund.getQtyRefund(), true);
            log.debug(LogUtil.format("新增退单回写后原单明细{}", ocBOrderItem);*/
        }
    }

    /**
     * 查询换货明细对应的库存是否大于可用库存 校验
     *
     * @param obj  传入的参数
     * @param user 当前登录用户
     * @return 返回判断结果
     */
    public ValueHolderV14 checkAllStroreStock(JSONObject obj, User user) {
        Long objid = obj.getLong("objid");
        JSONObject ocBreturnOrderDto = obj.getJSONObject("OcBreturnOrder"); //退换货订单
        JSONArray ocBreturnOrderExchange = obj.getJSONArray("OcBreturnOrderExchange"); //换货明细
        JSONArray ocBreturnOrderRefund = obj.getJSONArray("OcBreturnOrderRefund"); //退货明细

        OcBReturnOrder returnOrder = JSONObject.parseObject(ocBreturnOrderDto.toString(), OcBReturnOrder.class);
        List<OcBReturnOrderExchange> returnOrderExchanges = JSONObject.parseArray(ocBreturnOrderExchange.toString(),
                OcBReturnOrderExchange.class);
        List<OcBReturnOrderRefund> returnOrderRefunds = JSONObject.parseArray(ocBreturnOrderRefund.toJSONString(),
                OcBReturnOrderRefund.class);
        if (log.isDebugEnabled()) {
            log.debug(this.getClass().getName() + "退换货主表入参:{};", JSONObject.toJSONString(returnOrder));
            log.debug(this.getClass().getName() + "退货明细的入参:{};", JSONObject.toJSONString(returnOrderRefunds));
            log.debug(this.getClass().getName() + "换货明细的入参:{};", JSONObject.toJSONString(returnOrderExchanges));
        }
        ValueHolderV14 vh = new ValueHolderV14();
        //根据平台店铺id 查询逻辑仓集合
        Long cpCShopId = returnOrder.getCpCShopId();
        if (cpCShopId == null) {
            throw new NDSException("平台店铺为空");
        }
        List<Long> storeIds = syncStockStrategyService.queryShopStoreNextList(cpCShopId);
        if (log.isDebugEnabled()) {
            log.debug(this.getClass().getName() + "根据店铺查询店铺下的所有逻辑仓id的集合为:{}", JSONObject.toJSONString(storeIds));
        }
        if (CollectionUtils.isEmpty(storeIds)) {
            Long storeId = omsWarehouseRuleService.queryDefaultWarehouse(cpCShopId);
            if (Objects.isNull(storeId)) {
                throw new NDSException("根据店铺id查询不到逻辑仓");
            }
            storeIds.add(storeId);
        }
        //换货商品的 ecode集合
        List<Long> psCSkuIds = new ArrayList<>();
        List<String> psCSkuEcodes = new ArrayList<>();
        for (OcBReturnOrderExchange returnOrderExchange : returnOrderExchanges) {
            Long psCSkuId = returnOrderExchange.getPsCSkuId();
            String psCSkuEcode = returnOrderExchange.getPsCSkuEcode();
            if (psCSkuId != null) {
                psCSkuIds.add(psCSkuId);
            }
            if (psCSkuEcode != null) {
                psCSkuEcodes.add(psCSkuEcode);
            }
        }
        if (CollectionUtils.isEmpty(psCSkuIds)) {
            throw new NDSException("换货商品条码不能为空");
        }
        ValueHolderV14<SgOmsShopStorageQueryResult> sgOmsShopStorageQueryResultValueHolderV14 = null;
        try {
            long beginTime = System.currentTimeMillis();
            sgOmsShopStorageQueryResultValueHolderV14 = sgRpcService.queryOmsShopStorage(cpCShopId, psCSkuIds, null, user);
            long endTime = System.currentTimeMillis();
            log.debug(LogUtil.format("调用sgStorageQueryCmd.queryStorage()耗时") + (endTime - beginTime));
            if (ResultCode.FAIL == sgOmsShopStorageQueryResultValueHolderV14.getCode()) {
                throw new NDSException(this.getClass().getName() + sgOmsShopStorageQueryResultValueHolderV14.getMessage());
            }
        } catch (Exception e) {
            log.debug(LogUtil.format("调用库存中心服务异常，error:{}"), Throwables.getStackTraceAsString(e));
            throw new NDSException(this.getClass().getName() + e.getMessage());
        }
        log.debug(LogUtil.format("调用库存中心查询库存接口出参") + JSONObject.toJSONString(sgOmsShopStorageQueryResultValueHolderV14));
        //库存中心逻辑仓对象
        SgOmsShopStorageQueryResult sgBStorages = sgOmsShopStorageQueryResultValueHolderV14.getData();
        if (sgBStorages != null) {
            Map<Long, SgOmsShopStorageQueryItemResult> psSkuStorageResult = sgBStorages.getPsSkuStorageResult();
            //存放 可用库存和商品条码集合
            List<JSONObject> sameEcodeList = new ArrayList<>();
            if (MapUtils.isNotEmpty(psSkuStorageResult)) {
                for (int i = 0; i < returnOrderExchanges.size(); i++) {
                    OcBReturnOrderExchange ocBReturnOrderExchange = returnOrderExchanges.get(i);
                    // 定义逻辑仓底下的所有的这个条码的库存
                    BigDecimal allQty = BigDecimal.ZERO;
                    SgOmsShopStorageQueryItemResult itemResult = psSkuStorageResult.get(ocBReturnOrderExchange.getPsCSkuId());
                    if (itemResult != null) {
                        Map<Long, BigDecimal> shareStorageInfo = itemResult.getShareStorageInfo();
                        if (MapUtils.isNotEmpty(shareStorageInfo)) {
                            for (Long share : shareStorageInfo.keySet()) {
                                BigDecimal shareQty = shareStorageInfo.get(share) == null ? BigDecimal.ZERO : shareStorageInfo.get(share);
                                allQty = allQty.add(shareQty);
                            }
                        }
                    }
                    JSONObject object = new JSONObject();
                    object.put("psCEcode", ocBReturnOrderExchange.getPsCSkuEcode());
                    object.put("qtyAvailable", allQty);
                    sameEcodeList.add(object);
                }
                int tag = 0; //用于辨识明细中是不是存在换货数量大于可用库存的   0 代表不存在，大于0 表示存在
                StringBuilder builder = new StringBuilder("换货条码为");
                for (int i = 0; i < returnOrderExchanges.size(); i++) {
                    OcBReturnOrderExchange ocBReturnOrderExchange = returnOrderExchanges.get(i);
                    for (int j = 0; j < sameEcodeList.size(); j++) {
                        JSONObject jsonObject = sameEcodeList.get(j);
                        // 判断明细的商品编码是不是相同
                        if (jsonObject.getString("psCEcode").equals(ocBReturnOrderExchange.getPsCSkuEcode())) {
                            // 再判断商品的可用库存是不是大于 商品的换货数量
                            if (ocBReturnOrderExchange.getQtyExchange().compareTo(jsonObject.getBigDecimal("qtyAvailable")) > 0) {
                                tag++;
                                builder.append(jsonObject.getString("psCEcode")).append(",");
                                break;
                            }
                        }
                    }
                }
                builder.append("库存不足，确定要继续保存吗？");
                log.debug(LogUtil.format("判断满足库存的标记》》", tag));
                if (tag == 0) {
                    vh.setCode(ResultCode.SUCCESS);
                    vh.setMessage("库存满足换货");
                    vh.setData(true);
                } else {
                    vh.setCode(ResultCode.SUCCESS);
                    vh.setMessage(builder.toString());
                    vh.setData(false);
                }
            } else {
                StringBuilder builder = new StringBuilder("商品条码为");
                for (String psCSkuEcode : psCSkuEcodes) {
                    builder.append(psCSkuEcode).append(",");
                }
                builder.append("库存不足");
                vh.setCode(ResultCode.SUCCESS);
                vh.setData(false);
                vh.setMessage(builder.toString());
            }
        } else {
            vh.setCode(ResultCode.SUCCESS);
            vh.setData(false);
            vh.setMessage("查询库存出错");
        }
        return vh;
    }

    //生成日志对象
    private OcBReturnOrderLog getLog(Long id, String logType, String message, String userName, Long reOrderid) {
        OcBReturnOrderLog ocBReturnOrderLog = new OcBReturnOrderLog();
        ocBReturnOrderLog.setId(id);
        ocBReturnOrderLog.setLogType(logType);
        ocBReturnOrderLog.setLogMessage(message);
        ocBReturnOrderLog.setUserName(userName);
        ocBReturnOrderLog.setOcBReturnOrderId(reOrderid);
        return ocBReturnOrderLog;
    }

    //检查换货明细是否有组合商品
    private void checkExEcode(OcBReturnOrderExchange exchange) {
        Map map = psRpcService.querySku(exchange.getPsCSkuEcode());
        List<HashMap> hashMapList = (List<HashMap>) map.get("data");
        if (!hashMapList.isEmpty()) {
            String isGroup = (String) hashMapList.get(0).get("IS_GROUP");
            if ("Y".equals(isGroup)) {
                throw new NDSException("此退换货单含有组合商品明细，请检查后修改为实际出入库条码后重新操作");
            }
        }
    }

    /**
     * 检查退货明细是否有组合商品
     *
     * @param refund
     */
    private void checkReEcode(OcBReturnOrderRefund refund) {
        String ecode = refund.getPsCSkuEcode();
        Map map = psRpcService.querySku(ecode);
        List<HashMap> hashMapList = (List<HashMap>) map.get("data");
        if (!hashMapList.isEmpty()) {
            String isGroup = (String) hashMapList.get(0).get("IS_GROUP");
            if ("Y".equals(isGroup)) {
                throw new NDSException("此退换货单含有组合商品明细，请检查后修改为实际出入库条码后重新操作");
            }
        }
    }

    /**
     * //退货明细操作
     *
     * @param returnOrderRefunds 前端传过来的明细
     * @param refunds            数据库的退货，明细
     * @param objid              主表的id
     * @param user               当前登录用户
     */
    private void refund(List<OcBReturnOrderRefund> returnOrderRefunds, List<OcBReturnOrderRefund> refunds,
                        Long objid, User user) {
        for (int i = 0; i < returnOrderRefunds.size(); i++) {
            for (int j = 0; j < refunds.size(); j++) {
                if (returnOrderRefunds.get(i).getId().toString().equals(refunds.get(j).getId().toString())) {
                    refunds.remove(refunds.get(j));
                }
            }
        }
      /*  for (OcBReturnOrderRefund returnOrderRefund : refunds) {
            Map map = new HashMap();
            map.put("oc_b_return_order_id", objid);
            map.put("id", returnOrderRefund.getId());
            ocBReturnOrderRefundFiMapper.deleteByMap(map);
//            ocBReturnOrderRefundFiMapper.deleteById(returnOrderRefund.getId());
        }*/
        for (OcBReturnOrderRefund refund : returnOrderRefunds) {
            if (refund.getId() == -1) { //新增明细
                Long reId = ModelUtil.getSequence("oc_b_return_order_refund");
                refund.setOcBReturnOrderId(objid);
                refund.setId(reId);
                refund.setOwnerid(user.getId().longValue());
                refund.setOwnername(user.getName());
                refund.setOwnerename(user.getEname());
                refund.setCreationdate(new Date());
                refund.setAdClientId(user.getClientId() + 0L);
                refund.setAdOrgId(user.getOrgId() + 0L);
                ocBReturnOrderRefundFiMapper.insert(refund); //新增明细
            } else { //修改明细
                QueryWrapper<OcBReturnOrderRefund> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("oc_b_return_order_id", objid).eq("id", refund.getId());
                ocBReturnOrderRefundFiMapper.update(refund, queryWrapper);
            }
        }
    }

    //换货明细操作
    private void exchange(List<OcBReturnOrderExchange> returnOrderExchanges, List<OcBReturnOrderExchange> exchanges,
                          Long objid, User user) {
        for (int i = 0; i < returnOrderExchanges.size(); i++) {
            for (int j = 0; j < exchanges.size(); j++) {
                if (returnOrderExchanges.get(i).getId().toString().equals(exchanges.get(j).getId().toString())) {
                    exchanges.remove(exchanges.get(j));
                }
            }
        }
        for (OcBReturnOrderExchange exchange : exchanges) {
            Map map = new HashMap();
            map.put("oc_b_return_order_id", objid);
            map.put("id", exchange.getId());
            ocBReturnOrderExchangeFiMapper.deleteByMap(map);
        }
        for (OcBReturnOrderExchange returnOrderExchange : returnOrderExchanges) {
            if (returnOrderExchange.getId() == -1) {
                Long exchangeId = ModelUtil.getSequence("oc_b_return_order_exchange");
                returnOrderExchange.setOcBReturnOrderId(objid);
                returnOrderExchange.setId(exchangeId);
                BaseModelUtil.initialBaseModelSystemField(returnOrderExchange, user);
                ocBReturnOrderExchangeFiMapper.insert(returnOrderExchange);
            } else {
                QueryWrapper<OcBReturnOrderExchange> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("oc_b_return_order_id", objid).eq("id", returnOrderExchange.getId());
                ocBReturnOrderExchangeFiMapper.update(returnOrderExchange, queryWrapper);
            }
        }
    }

    /**
     * 日志记录修改内容
     *
     * @param id    退换货订单ID
     * @param order 退换货订单请求参数
     * @return 修改字段结果
     */
    private String logMessage(Long id, OcBReturnOrder order) {
        OcBReturnOrder returnOrder = ocBReturnOrderFiMapper.selectById(id);
        StringBuffer result = new StringBuffer();
        if (order.getReturnId() != null && !order.getReturnId().equals(returnOrder.getReturnId())) {
            result.append("平台退款单号:" + order.getReturnId() + ",");
        }
        if (order.getCpCLogisticsEname() != null && !order.getCpCLogisticsEname().equals(returnOrder.getCpCLogisticsEname())) {
            result.append("退回物流公司:" + order.getCpCLogisticsEname() + ",");
        }
        if (order.getLogisticsCode() != null && !order.getLogisticsCode().equals(returnOrder.getLogisticsCode())) {
            result.append("退回物流单号:" + order.getLogisticsCode() + ",");
        }
        if (order.getRemark() != null && !order.getRemark().equals(returnOrder.getRemark())) {
            result.append("备注:" + order.getRemark() + ",");
        }
        if (order.getSellerMemo() != null && !order.getSellerMemo().equals(returnOrder.getSellerMemo())) {
            result.append("卖家备注:" + order.getSellerMemo() + ",");
        }
        if (order.getReceiveName() != null && !order.getReceiveName().equals(returnOrder.getReceiveName())) {
            result.append("收货人:" + order.getReceiveName() + ",");
        }
        if (order.getReceiveMobile() != null && !order.getReceiveMobile().equals(returnOrder.getReceiveMobile())) {
            result.append("收货人手机:" + order.getReceiveMobile() + ",");
        }
        if (order.getReceivePhone() != null && !order.getReceivePhone().equals(returnOrder.getReceivePhone())) {
            result.append("收货人电话:" + order.getReceivePhone() + ",");
        }
        if (order.getReceiveZip() != null && !order.getReceiveZip().equals(returnOrder.getReceiveZip())) {
            result.append("收货人邮编:" + order.getReceiveZip() + ",");
        }
        if (order.getReceiverProvinceName() != null && !order.getReceiverProvinceName().equals(returnOrder.getReceiverProvinceName())) {
            result.append("收货人省份:" + order.getReceiverProvinceName() + ",");
        }
        if (order.getReceiverCityName() != null && !order.getReceiverCityName().equals(returnOrder.getReceiverCityName())) {
            result.append("收货人市:" + order.getReceiverCityName() + ",");
        }
        if (order.getReceiverAreaName() != null && !order.getReceiverAreaName().equals(returnOrder.getReceiverAreaName())) {
            result.append("收货人区:" + order.getReceiverAreaName() + ",");
        }
        if (order.getReceiveAddress() != null && !order.getReceiveAddress().equals(returnOrder.getReceiveAddress())) {
            result.append("收货人地址:" + order.getReceiveAddress() + ",");
        }
        if (order.getCpCPhyWarehouseInId() != null && !order.getCpCPhyWarehouseInId().equals(returnOrder.getCpCPhyWarehouseInId())) {
            result.append("入库实体仓库:" + order.getCpCPhyWarehouseInId() + ",");
        }
        if (order.getCpCPhyWarehouseInId() == null && returnOrder.getCpCPhyWarehouseInId() != null) {
            result.append("入库实体仓库:" + order.getCpCPhyWarehouseInId() + ",");
        }
        if (result.length() > 0) {
            return result.toString().substring(0, result.toString().length() - 1);
        } else {
            return result.toString();
        }
    }

    //新增退货订单，添加SKU时，不允许添加不在原单内的商品
    private boolean skuExist(Long orderId, List<OcBReturnOrderRefund> returnOrderRefunds) {
        List<OcBOrderItem> items = ocBorderItemMapper.selectList(new QueryWrapper<OcBOrderItem>().eq("oc_b_order_id", orderId));
        for (OcBReturnOrderRefund returnOrderRefund : returnOrderRefunds) {
            int i = 0;
            String psCSkuEcode = returnOrderRefund.getPsCSkuEcode();
            for (OcBOrderItem item : items) {
                String psCSkuEcode1 = item.getPsCSkuEcode();
                if (!psCSkuEcode.equals(psCSkuEcode1)) {
                    i++;
                }
            }
            if (i == items.size()) {
                return true;
            }
        }
        return false;
    }

    /**
     * 更新原单明细已退数量
     *
     * @param item 原单明细
     * @param qty  数量
     * @param flag 加减标识
     * @return
     */
    public void updateOrderItem(OcBOrderItem item, BigDecimal qty, Boolean flag) {
        BigDecimal add = BigDecimal.ZERO;
        BigDecimal applyQty = BigDecimal.ZERO;
        if (flag) {
            // 商品已退数量
            if (item.getQtyHasReturn() != null) {
                add = item.getQtyHasReturn().add(qty);
            } else {
                add = qty;
            }
            // 已申请退货数量
            if (item.getQtyReturnApply() != null) {
                applyQty = item.getQtyReturnApply().add(qty);
            } else {
                applyQty = qty;
            }
        } else {
            if (item.getQtyHasReturn() != null) {
                add = item.getQtyHasReturn().subtract(qty);
                if (add.compareTo(BigDecimal.ZERO) < 0) {
                    add = BigDecimal.ZERO;
                }
            } else {
                add = BigDecimal.ZERO;
            }
            if (item.getQtyReturnApply() != null) {
                applyQty = item.getQtyReturnApply().add(qty);
            } else {
                applyQty = qty;
            }
        }
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("id", item.getId());
        wrapper.eq("oc_b_order_id", item.getOcBOrderId());
        item.setQtyHasReturn(add);
        item.setQtyReturnApply(applyQty);
        //ocBorderItemMapper.updateById(item);
        ocBorderItemMapper.update(item, wrapper);
    }

    /**
     * 新增预退货单明细
     *
     * @param obj
     * @param user
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 addReturnOrderItem(JSONObject obj, User user) {
        ValueHolderV14 vh = new ValueHolderV14();
        Long orderId = obj.getLong("orderId");
        Long returnId = obj.getLong("returnId");
        JSONArray ocBreturnOrderRefundList = obj.getJSONArray("OcBreturnOrderRefund");
        List<OcBReturnOrderRefund> ocBReturnOrderRefunds = JSONArray.parseArray(ocBreturnOrderRefundList.toJSONString(), OcBReturnOrderRefund.class);
        List<OcBReturnOrderRefund> returnOrderRefunds = packageReturnOrderRefund(orderId, returnId, ocBReturnOrderRefunds, user);
        if (!CollectionUtils.isEmpty(returnOrderRefunds)) {
            ocBReturnOrderRefundFiMapper.batchInsert(returnOrderRefunds);
        }
        // 并跟新主表的应退金额 和 退款金额
        BigDecimal price = ocBReturnOrderFiMapper.selectAmtprice(returnId);
        OcBReturnOrder ocBReturnOrder = new OcBReturnOrder();
        ocBReturnOrder.setReturnAmtList(price);
        ocBReturnOrder.setReturnAmtActual(price);
        ocBReturnOrderFiMapper.update(ocBReturnOrder, new QueryWrapper<OcBReturnOrder>().eq("id", returnId));
        vh.setCode(ResultCode.SUCCESS);
        vh.setMessage("新增明细成功");
        return vh;
    }

    /**
     * 组装退货明细参数
     *
     * @param orderId               订单id
     * @param returnId              退换货主表id
     * @param ocBReturnOrderRefunds 退货明细集合
     */
    private List<OcBReturnOrderRefund> packageReturnOrderRefund(Long orderId, Long returnId, List<OcBReturnOrderRefund> ocBReturnOrderRefunds, User user) {
        List<OcBReturnOrderRefund> returnOrderRefunds = new ArrayList<>();
        for (OcBReturnOrderRefund returnOrderRefund : ocBReturnOrderRefunds) {
            Long ocBOrderItemId = returnOrderRefund.getOcBOrderItemId();
            OcBOrderItem ocBOrderItem = ocBorderItemMapper.selectOne(new QueryWrapper<OcBOrderItem>().
                    eq("oc_b_order_id", orderId).eq("id", ocBOrderItemId));
            List<OcBReturnOrderRefund> orderRefundList = ocBReturnOrderRefundFiMapper.selectList(new QueryWrapper<OcBReturnOrderRefund>().
                    eq("oc_b_return_order_id", returnId));
            OcBReturnOrderRefund returnOrderRefund1 = ajustItemIsExit(ocBOrderItem, orderRefundList);
            if (returnOrderRefund1 == null) {
                OcBReturnOrderRefund returnOrderRefund2 = new OcBReturnOrderRefund();
                // 新增一个明细
                returnOrderRefund2.setId(ModelUtil.getSequence("oc_b_return_order_refund"));
                returnOrderRefund2.setProductMark("1"); // 默认是正品
                returnOrderRefund2.setBarcode(ocBOrderItem.getBarcode());
                returnOrderRefund2.setSkuSpec(ocBOrderItem.getSkuSpec());
                returnOrderRefund2.setPsCSkuId(ocBOrderItem.getPsCSkuId());
                returnOrderRefund2.setPsCSkuEcode(ocBOrderItem.getPsCSkuEcode());
                returnOrderRefund2.setPsCSkuEname(ocBOrderItem.getPsCSkuEname());
                returnOrderRefund2.setPsCProId(ocBOrderItem.getPsCProId());
                returnOrderRefund2.setPsCProEcode(ocBOrderItem.getPsCProEcode());
                returnOrderRefund2.setPsCProEname(ocBOrderItem.getPsCProEname());


                returnOrderRefund2.setPrice(ocBOrderItem.getPriceList()); //商品单价
                returnOrderRefund2.setOcBReturnOrderId(returnId);

                returnOrderRefund2.setQtyIn(BigDecimal.ZERO);
                //                默认为原单中可退数量，可进行编辑操作
                BigDecimal qtyHasReturn = ocBOrderItem.getQtyHasReturn() == null ? BigDecimal.ZERO : ocBOrderItem.getQtyHasReturn();
                returnOrderRefund2.setQtyRefund(ocBOrderItem.getQty().subtract(qtyHasReturn));

                returnOrderRefund2.setQtyCanRefund(BigDecimal.ZERO);
                returnOrderRefund2.setAdOrgId(user.getOrgId() + 0L);
                returnOrderRefund2.setAdClientId(user.getClientId() + 0L);
                returnOrderRefund2.setOwnerid(user.getId() + 0L);
                returnOrderRefund2.setOwnerename(user.getEname());
                returnOrderRefund2.setOwnername(user.getName());
                returnOrderRefund2.setCreationdate(new Date());
                returnOrderRefund2.setModifierid(user.getId() + 0L);
                returnOrderRefund2.setModifierename(user.getEname());
                returnOrderRefund2.setModifiername(user.getName());
                returnOrderRefund2.setModifieddate(new Date());
                returnOrderRefund2.setIsactive("Y");
                //单件退货金额g)通过原订单明细表中的成交价计算得出计算公式 单件退货金额=单行实际成交价/数量（为订单明细中的数量）
                returnOrderRefund2.setAmtRefundSingle(ocBOrderItem.getRealAmt().divide(ocBOrderItem.getQty(), 4, BigDecimal.ROUND_HALF_UP));
                returnOrderRefund2.setAmtRefund(returnOrderRefund2.getAmtRefundSingle().multiply(returnOrderRefund2.getQtyRefund()));  // 退货基金额 （单价*数量）
             /*   returnOrderRefund.setSourceBillNo();
                returnOrderRefund.setRefundBillNo();
                returnOrderRefund.setRefundStatus();*/
                returnOrderRefund2.setToAgStatus(AGStatusEnum.INIT.getVal() + ""); // 默认未传AG
                returnOrderRefund2.setGiftType(ocBOrderItem.getGiftType());
                returnOrderRefund2.setGiftRelation(ocBOrderItem.getGiftRelation());
                returnOrderRefund2.setOcBOrderItemId(ocBOrderItemId);
                returnOrderRefunds.add(returnOrderRefund2);
                updateOrderItem(ocBOrderItem, returnOrderRefund2.getQtyRefund(), true);
            } else {
                OcBReturnOrderRefund refund = new OcBReturnOrderRefund();
                // 更新存在的明细
                refund.setId(returnOrderRefund1.getId());
                refund.setQtyRefund(returnOrderRefund1.getQtyRefund().add(ocBOrderItem.getQty().subtract(ocBOrderItem.getQtyHasReturn())));
                refund.setAmtRefund(returnOrderRefund1.getAmtRefundSingle().multiply(returnOrderRefund1.getQtyRefund()));
                refund.setModifierename(user.getEname());
                refund.setModifieddate(new Date());
                refund.setModifierid(user.getId() + 0L);
                ocBReturnOrderRefundFiMapper.update(refund, new QueryWrapper<OcBReturnOrderRefund>().
                        eq("oc_b_return_order_id", returnId).eq("id", returnOrderRefund1.getId()));
                updateOrderItem(ocBOrderItem, refund.getQtyRefund(), true);
            }

        }
        return returnOrderRefunds;
    }

    /**
     * 判断是不是已经存在相同明细进行累加还是 新增一条明细
     *
     * @param ocBOrderItem
     * @param orderRefundList
     * @return
     */
    private OcBReturnOrderRefund ajustItemIsExit(OcBOrderItem ocBOrderItem, List<OcBReturnOrderRefund> orderRefundList) {
        for (OcBReturnOrderRefund returnOrderRefund : orderRefundList) {
            if (ocBOrderItem.getPriceList().compareTo(returnOrderRefund.getPrice()) == 0 &&
                    ocBOrderItem.getPsCSkuEcode().equals(returnOrderRefund.getPsCSkuEcode())) {
                return returnOrderRefund;
            }
        }
        return null;
    }

    /**
     * 删除预退货单明细
     *
     * @param obj
     * @param user
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 deleteReturnOrderItem(JSONObject obj, User user) {
        ValueHolderV14 vh = new ValueHolderV14();
        Long returnId = obj.getLong("returnId");
        JSONArray ids = obj.getJSONArray("ids");
        List<OcBReturnOrderRefund> returnOrderRefunds = ocBReturnOrderRefundFiMapper.selectList(new QueryWrapper<OcBReturnOrderRefund>()
                .eq("oc_b_return_order_id", returnId).in("id", ids));
        ocBReturnOrderRefundFiMapper.delete(new QueryWrapper<OcBReturnOrderRefund>().eq("oc_b_return_order_id", returnId).in("id", ids));
        // 并跟新主表的应退金额 和 退款金额
        BigDecimal price = ocBReturnOrderFiMapper.selectAmtprice(returnId);
        if (price == null) {
            price = BigDecimal.ZERO;
        }
        OcBReturnOrder ocBReturnOrder = new OcBReturnOrder();
        ocBReturnOrder.setReturnAmtList(price);
        ocBReturnOrder.setReturnAmtActual(price);
        ocBReturnOrderFiMapper.update(ocBReturnOrder, new QueryWrapper<OcBReturnOrder>().eq("id", returnId));

        for (OcBReturnOrderRefund returnOrderRefund : returnOrderRefunds) {
            OcBOrderItem ocBOrderItem = ocBorderItemMapper.selectById(returnOrderRefund.getOcBOrderItemId());
            updateOrderItem(ocBOrderItem, returnOrderRefund.getQtyRefund(), false);
        }

        vh.setCode(ResultCode.SUCCESS);
        vh.setMessage("删除成功");
        return vh;
    }

    /**
     * 校验是否是新增的预退货订单
     *
     * @param ocBReturnOrder 退货单
     * @return
     */
    public OcBReturnOrder checkBillType(OcBReturnOrder ocBReturnOrder) {
        /*预退货单据的条件  （无运单号、无退货仓、退单类型是拦截且非菜鸟仓《第三方仓库不是菜鸟》）
        OcBReturnOrder 参数。*/
//        if (StringUtils.isEmpty(ocBReturnOrder.getLogisticsCode())) {
//            ocBReturnOrder.setBillType(3);
//            return ocBReturnOrder;
//        }
//        if (ocBReturnOrder.getCpCPhyWarehouseInId() == null) {
//            ocBReturnOrder.setBillType(3);
//            return ocBReturnOrder;
//        } else {
//
//            CpCPhyWarehouse cpCPhyWarehouse = cpRpcExtService.queryByWarehouseId(ocBReturnOrder.getCpCPhyWarehouseId());
//            if (cpCPhyWarehouse != null) {
//
//                ocBReturnOrder.setThirdWarehouseType(cpCPhyWarehouse.getThirdWarehouseType()); // 设置第三方仓库
//            }
//        }
//        if (ReturnProTypeEnum.INTERCEPT.getVal().equals(ocBReturnOrder.getReturnProType())) {
//            // 是拦截状态并 非拦截成功的
//            if (!InterceptStatus.DELIVERY_INTERCEPT_SUCCESS.getCode().equals(ocBReturnOrder.getIntercerptStatus())) {
//                ocBReturnOrder.setBillType(3);
//            } else {
//                ocBReturnOrder.setBillType(1);
//            }
//
//        } else {
//            ocBReturnOrder.setBillType(1);
//        }

        // @20200715 这里没有预退货，全部替换为换货
        if (Objects.nonNull(ocBReturnOrder.getCpCPhyWarehouseInId())) {
            CpCPhyWarehouse cpCPhyWarehouse = cpRpcService.queryByWarehouseId(ocBReturnOrder.getCpCPhyWarehouseId());
            if (Objects.nonNull(cpCPhyWarehouse)) {
                // ocBReturnOrder.setThirdWarehouseType(cpCPhyWarehouse.getThirdWarehouseType()); // 设置第三方仓库
            }
        }

        // 这里统一设置成换货
        if (Objects.isNull(ocBReturnOrder.getBillType())) {
            ocBReturnOrder.setBillType(OcReturnBillTypeEnum.RETURN.getVal());
        }

        return ocBReturnOrder;
    }
}