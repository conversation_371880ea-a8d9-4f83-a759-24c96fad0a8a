package com.jackrain.nea.oc.oms.model.result;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR> lin yu
 * @date : 2022/8/4 上午10:14
 * @describe :
 */

@Data
public class OcBOrderStoOutInfoResult implements Serializable {

    @JSONField(name = "BILL_NO")
    private String billNo;

    @JSONField(name = "PS_C_PRO_ID")
    private Long psCProId;

    @J<PERSON><PERSON>ield(name = "PS_C_PRO_ECODE")
    private String psCProEcode;

    @J<PERSON><PERSON>ield(name = "PS_C_PRO_ENAME")
    private String psCProEname;

    @J<PERSON>NField(name = "QTY")
    private BigDecimal qty;

    @JSONField(name = "CP_C_STORE_ID")
    private Long cpCStoreId;

    @JSONField(name = "CP_C_STORE_ENAME")
    private String cpCStoreEname;

    @JSO<PERSON>ield(name = "QTY_PREOUT")
    private BigDecimal qtyPreout;

    @J<PERSON><PERSON>ield(name = "PRODUCE_DATE")
    private String produceDate;

    @J<PERSON><PERSON>ield(name = "OVER_LIMIT")
    private Boolean overLimit;

    @JSONField(name = "ITEM_ID")
    private Long itemId;

    @JSONField(name = "STORAGE_TYPE")
    private String storageType;

    public OcBOrderStoOutInfoResult(){
        overLimit = false;
    }

}
