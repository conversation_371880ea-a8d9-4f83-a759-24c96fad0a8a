package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName OcBOrderNaikaUnfreeze
 * @Description 奶卡解冻中间表
 * <AUTHOR>
 * @Date 2022/6/30 11:05
 * @Version 1.0
 */
@TableName(value = "oc_b_order_naika_unfreeze")
@Data
public class OcBOrderNaikaUnfreeze extends BaseModel {

    @ApiModelProperty(value = "订单解冻id")
    @Field(type = FieldType.Long)
    @JSONField(name = "ID")
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @ApiModelProperty(value = "订单编号")
    @Field(type = FieldType.Long)
    @JSONField(name = "OC_B_ORDER_ID")
    private Long ocBOrderId;

    @ApiModelProperty(value = "初始平台单号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "TID")
    private String tid;

    @ApiModelProperty(value = "解冻状态")
    @Field(type = FieldType.Integer)
    @JSONField(name = "UNFREEZE_STATUS")
    private Integer unfreezeStatus;

    @ApiModelProperty(value = "解冻次数")
    @Field(type = FieldType.Integer)
    @JSONField(name = "UNFREEZE_TIMES")
    private Integer unfreezeTimes;

}
