package com.jackrain.nea.oc.services.invoice;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.invoice.AcFOrderInvoiceVoidCmd;
import com.jackrain.nea.oc.oms.services.invoice.AcFOrderInvoiceVoidService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/09/12 13:20
 *
 */
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class AcFOrderInvoiceVoidCmdImpl extends CommandAdapter implements AcFOrderInvoiceVoidCmd {

    @Autowired
    private AcFOrderInvoiceVoidService service;


    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return service.execute(session);
    }

}
