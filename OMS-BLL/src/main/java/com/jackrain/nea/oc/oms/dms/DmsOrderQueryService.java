package com.jackrain.nea.oc.oms.dms;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOut;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutItem;
import com.burgeon.r3.sg.store.model.result.out.SgBStoOutQueryResult;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.api.dms.DmsOrderItemBatchResult;
import com.jackrain.nea.oc.oms.api.dms.DmsOrderItemQueryResult;
import com.jackrain.nea.oc.oms.api.dms.DmsOrderQueryResult;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName DmsOrderQueryService
 * @Description DMS订单查询
 * <AUTHOR>
 * @Date 2025/3/17 17:40
 * @Version 1.0
 */
@Component
@Slf4j
public class DmsOrderQueryService {

    @Autowired
    private SgRpcService sgRpcService;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;

    public ValueHolderV14<List<DmsOrderQueryResult>> queryOrder(JSONObject params) {
        ValueHolderV14<List<DmsOrderQueryResult>> valueHolderV14 = new ValueHolderV14<>();
        JSONArray billNo = params.getJSONArray("billNo");

        if (CollectionUtils.isEmpty(billNo)) {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("请输入订单号");
            return valueHolderV14;
        }
        // billNo转换成List<String>
        List<String> billNoList = billNo.toJavaList(String.class);
        List<OcBOrder> ocBOrderList = ocBOrderMapper.queryAuditOrderByBillNo(billNoList);
        if (CollectionUtils.isEmpty(ocBOrderList)){
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("无要查询的数据内容");
            return valueHolderV14;
        }
        // 获取ocBOrderList的id集合
        List<Long> orderIdList = ocBOrderList.stream().map(OcBOrder::getId).collect(Collectors.toList());
        // ocBOrderList组装成map，id为key
        Map<Long, OcBOrder> ocBOrderMap = ocBOrderList.stream().collect(Collectors.toMap(OcBOrder::getId, ocBOrder -> ocBOrder));
        List<SgBStoOutQueryResult> sgBStoOutQueryResults = sgRpcService.sgQuerySgBStoOutByIds(orderIdList);
        List<DmsOrderQueryResult> resultList = new ArrayList<>();
        for (SgBStoOutQueryResult sgBStoOutQueryResult : sgBStoOutQueryResults){
            DmsOrderQueryResult result = new DmsOrderQueryResult();
            SgBStoOut main = sgBStoOutQueryResult.getMain();
            Long sourceId = main.getSourceBillId();
            OcBOrder ocBOrder = ocBOrderMap.get(sourceId);
            List<SgBStoOutItem> sgBStoOutItems = sgBStoOutQueryResult.getItems();
            if (CollectionUtils.isEmpty(sgBStoOutItems)){
                valueHolderV14.setCode(ResultCode.FAIL);
                valueHolderV14.setMessage("占用单明细不存在");
                return valueHolderV14;
            }
            // 对sgBStoOutItems中的sku进行分组。这里面可能每个sku会有多个。而且里面有批次的概念
            Map<String, List<SgBStoOutItem>> skuGroupMap = sgBStoOutItems.stream()
                    .collect(Collectors.groupingBy(SgBStoOutItem::getPsCSkuEcode));

            List<DmsOrderItemQueryResult> itemResults = new ArrayList<>();
            skuGroupMap.forEach((skuECode, items) -> {
                DmsOrderItemQueryResult itemResult = new DmsOrderItemQueryResult();
                itemResult.setSkuECode(skuECode);
                itemResult.setNum(items.stream().mapToInt(item -> item.getQty() == null ? 0 : item.getQty().intValue()).sum());

                List<DmsOrderItemBatchResult> batchResults = items.stream().map(item -> {
                    DmsOrderItemBatchResult batchResult = new DmsOrderItemBatchResult();
                    batchResult.setBatchCode(item.getProduceDate());
                    batchResult.setBatchNum(String.valueOf(item.getQty()));
                    return batchResult;
                }).collect(Collectors.toList());
                itemResult.setBatch(batchResults);

                itemResults.add(itemResult);
            });
            result.setBillNo(ocBOrder.getBillNo());
            result.setSgBOutBillNo(ocBOrder.getSgBOutBillNo());
            result.setItem(itemResults);
            resultList.add(result);
        }
        valueHolderV14.setCode(ResultCode.SUCCESS);
        valueHolderV14.setData(resultList);
        return valueHolderV14;
    }

}
