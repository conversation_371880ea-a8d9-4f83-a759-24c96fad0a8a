package com.jackrain.nea.oc.oms.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * @author: 易邵峰
 * @since: 2019-04-19
 * create at : 2019-04-19 22:23
 */
@Configuration
@Data
public class TransferOrderMqConfig {

    @Value("${r3.oc.oms.transfer.mq.topic:}")
    private String sendMqTopic;

    @Value("${r3.oc.oms.transfer.mq.tag:}")
    private String sendMqTag;

    /**
     * 转单topic
     */
    @Value("${r3.oms.call.transfer.topic:}")
    private String sendTransferMqTopic;

    /**
     * 转单tag
     */
    @Value("${r3.oms.call.transfer.tag:}")
    private String sendTransferMqTag;


}
