package com.jackrain.nea.oc.services;

import com.jackrain.nea.oc.oms.api.ModifyReturnOrderWarehouseCmd;
import com.jackrain.nea.oc.oms.model.request.UpdateReturnOrderRequest;
import com.jackrain.nea.oc.oms.services.ModifyReturnOrderWarehouseService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: xiWen.z
 * create at: 2019/7/16 0016
 */
@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class ModifyReturnOrderWarehouseCmdImpl implements ModifyReturnOrderWarehouseCmd {

    @Autowired
    ModifyReturnOrderWarehouseService modifyReturnOrderWarehouseService;

    @Override
    public ValueHolderV14 modifyReturnOrderWarehouse(UpdateReturnOrderRequest req, User usr) {
        return modifyReturnOrderWarehouseService.updateWarehouse(req, usr);
    }
}
