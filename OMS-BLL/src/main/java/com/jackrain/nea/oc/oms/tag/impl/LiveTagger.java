package com.jackrain.nea.oc.oms.tag.impl;

import com.jackrain.nea.oc.oms.matcher.live.LiveMatchManager;
import com.jackrain.nea.oc.oms.tag.AbstractTagger;
import com.jackrain.nea.oc.oms.tag.vo.TaggerRelation;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * Description： 直播打标服务
 * Author: RESET
 * Date: Created in 2020/7/8 23:23
 * Modified By:
 */
@Component
public class LiveTagger extends AbstractTagger {

    /**
     * 打标逻辑
     *
     * @param relation
     */
    @Override
    public void doTag(TaggerRelation relation) {
        if (Objects.nonNull(relation)) {
            // 调用直播打标服务
            LiveMatchManager.getInstance().cleanUp(relation.getOcBOrder(), relation.getOcBOrderItemList());
        }
    }

}
