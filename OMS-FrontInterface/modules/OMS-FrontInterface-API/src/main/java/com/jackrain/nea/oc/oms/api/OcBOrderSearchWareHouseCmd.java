package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.model.request.OrderSerarchCheckRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

public interface OcBOrderSearchWareHouseCmd {

    ValueHolderV14 queryOrderSearchWareHouse(OrderSerarchCheckRequest param, User user) throws NDSException;
}
