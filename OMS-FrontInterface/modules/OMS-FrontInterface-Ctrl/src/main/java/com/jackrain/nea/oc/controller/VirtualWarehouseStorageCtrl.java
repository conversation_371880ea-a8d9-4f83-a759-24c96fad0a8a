package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.VirtualWarehouseStorageCmd;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

/**
 * 退换货订单-虚拟仓库入库
 *
 * <AUTHOR>
 * @date 2019/10/09
 */
@Api(value = "VirtualWarehouseStorageCtrl", tags = "手动入库")
@Slf4j
@RestController
public class VirtualWarehouseStorageCtrl {

    @Autowired
    private VirtualWarehouseStorageCmd virtualWarehouseStorage;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    /**
     * 手动入库
     *
     * @param request
     * @param obj
     * @return
     */
    @ApiOperation(value = "手动入库")
    @RequestMapping(value = "/api/cs/oc/oms/v1/virtualWarehouseStorage", method = RequestMethod.POST)
    public JSONObject virtualWarehouseStorage(HttpServletRequest request,
                                              @RequestBody JSONObject obj) {

        ValueHolderV14 vh = new ValueHolderV14();
        //获取当前登陆用户
        // User user = VirtualWarehouseStorageCtrl.getRootUser();

        //获取用户登录信息
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        if (obj == null) {
            throw new NDSException(Resources.getMessage("请求参数不能为空!", user.getLocale()));
        }
        List<Long> ids = new ArrayList<>();
        JSONArray idsArray = obj.getJSONArray("ids");
        if (idsArray == null || idsArray.size() == 0) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("请选择一个退换单进行手动入库");
            return vh.toJSONObject();
        }
        try {
            for (int i = 0; i < idsArray.size(); i++) {
                ids.add(Long.valueOf(idsArray.get(i).toString()));
            }
            vh = virtualWarehouseStorage.execute(ids, user);
            //记录日志信息。Finish 标记结束
            return vh.toJSONObject();
        } catch (NDSException e) {
            log.error(LogUtil.format("退换单进行手动入库异常：{}",  "退换单进行手动入库"), Throwables.getStackTraceAsString(e));
            vh = new ValueHolderV14();
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return vh.toJSONObject();
        }

    }


}

