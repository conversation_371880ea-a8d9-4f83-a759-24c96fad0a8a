package com.jackrain.nea.oc.oms.controller;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.store.model.result.out.SgOutResultMilkCardItemMqResult;
import com.burgeon.r3.sg.store.model.result.out.SgOutResultSendMsgResult;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.request.NaiKaAccountRequest;
import com.jackrain.nea.oc.oms.model.request.naika.NaiKaOrderQueryRequest;
import com.jackrain.nea.oc.oms.model.request.naika.NaiKaReturnQueryRequest;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.services.NaiKaAccountService;
import com.jackrain.nea.oc.oms.services.delivery.impl.OrderDeliveryNaiKaImpl;
import com.jackrain.nea.oc.oms.services.naika.OmsNaiKaOrderQueryService;
import com.jackrain.nea.oc.oms.services.naika.OmsNaiKaReturnQueryService;
import com.jackrain.nea.oc.oms.services.naika.TestService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.AddressResolutionUtils;
import com.jackrain.nea.util.JsonUtils;
import com.jackrain.nea.util.ValueHolder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @ClassName TestController
 * @Description 测试用的controller
 * <AUTHOR>
 * @Date 2022/7/22 09:35
 * @Version 1.0
 */
@Deprecated
@RestController
public class TestController {

    @Autowired
    private OmsNaiKaOrderQueryService omsNaiKaOrderQueryService;
    @Autowired
    private OmsNaiKaReturnQueryService returnQueryService;
    @Autowired
    private TestService testService;
    @Autowired
    private NaiKaAccountService naiKaAccountService;
    @Autowired
    private OrderDeliveryNaiKaImpl orderDeliveryNaiKa;
    @Autowired
    private AddressResolutionUtils addressResolutionUtils;

    @RequestMapping(path = "/api/oms/v1/address/resolution", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolderV14 resolutionAddress(@RequestParam("param") String param){
        ValueHolderV14 valueHolder = new ValueHolderV14(ResultCode.SUCCESS, "success");
        String[] addressArr = param.split(",");
        List<String> addressList = new ArrayList<>();
        for (String address : addressArr){
            Map<String, String> map = addressResolutionUtils.addressResolutionNew(address);
            addressList.add(map.get("province") + " " + map.get("city") + " " + map.get("area"));
        }
        valueHolder.setData(addressList);
        return valueHolder;
    }

    @RequestMapping(path = "/api/oms/v1/naika/delivery", method = {RequestMethod.POST, RequestMethod.GET})
    public void delivery(@RequestParam("param") String param) {
        OcBOrderRelation ocBOrderRelation = JsonUtils.parseJSON(OcBOrderRelation.class, param);
        orderDeliveryNaiKa.deliveryDeal(ocBOrderRelation, Lists.newArrayList());
    }

    @RequestMapping(path = "/api/oms/v1/naika/account", method = {RequestMethod.POST, RequestMethod.GET})
    public void account(@RequestBody NaiKaAccountRequest request) {
        naiKaAccountService.execute(request);
    }

    @RequestMapping(path = "/api/oms/v1/naika/order/query", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder test(HttpServletRequest request, @RequestBody NaiKaOrderQueryRequest naiKaOrderQueryRequest) {
        ValueHolder vh = new ValueHolder();
        JSONObject resultData = new JSONObject();
        Integer range = 10;
        Integer startIndex = 0;
        return omsNaiKaOrderQueryService.getValueHolder(null, vh, resultData, range, startIndex, naiKaOrderQueryRequest);
    }

    @RequestMapping(path = "/api/oms/v1/naika/order/return/query", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder test(HttpServletRequest request, @RequestBody NaiKaReturnQueryRequest returnQueryRequest) {
        ValueHolder vh = new ValueHolder();
        JSONObject resultData = new JSONObject();
        Integer range = 10;
        Integer startIndex = 0;
        return null;
    }

    @RequestMapping(path = "/api/oms/v1/naika/outprocess", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder test() {
        SgOutResultSendMsgResult sendMsgResult = getSgOutResultSendMsgResult();

        OcBOrder ocBOrder = new OcBOrder();
        ocBOrder.setId(1000L);
        ocBOrder.setTid("tidtest");
        testService.extracted(sendMsgResult, ocBOrder);
        return null;
    }

    private SgOutResultSendMsgResult getSgOutResultSendMsgResult() {
        SgOutResultSendMsgResult sendMsgResult = new SgOutResultSendMsgResult();
        List<SgOutResultMilkCardItemMqResult> mqResultMilkCardItems = new ArrayList<>();
        SgOutResultMilkCardItemMqResult result = new SgOutResultMilkCardItemMqResult();
        result.setMilkCard("card01");
        result.setPsCProEcode("proecode01");
        result.setPsCProId(1L);
        result.setPsCSkuEcode("ecode01");
        result.setPsCSkuId(1L);

        SgOutResultMilkCardItemMqResult result2 = new SgOutResultMilkCardItemMqResult();
        result2.setMilkCard("card02");
        result2.setPsCProEcode("proecode01");
        result2.setPsCProId(1L);
        result2.setPsCSkuEcode("ecode01");
        result2.setPsCSkuId(1L);

        SgOutResultMilkCardItemMqResult result3 = new SgOutResultMilkCardItemMqResult();
        result3.setMilkCard("card03");
        result3.setPsCProEcode("proecode01");
        result3.setPsCProId(1L);
        result3.setPsCSkuEcode("ecode01");
        result3.setPsCSkuId(1L);

        SgOutResultMilkCardItemMqResult result4 = new SgOutResultMilkCardItemMqResult();
        result4.setMilkCard("card04");
        result4.setPsCProEcode("proecode01");
        result4.setPsCProId(1L);
        result4.setPsCSkuEcode("ecode02");
        result4.setPsCSkuId(1L);

        SgOutResultMilkCardItemMqResult result5 = new SgOutResultMilkCardItemMqResult();
        result5.setMilkCard("card05");
        result5.setPsCProEcode("proecode01");
        result5.setPsCProId(1L);
        result5.setPsCSkuEcode("ecode02");
        result5.setPsCSkuId(1L);

        mqResultMilkCardItems.add(result);
        mqResultMilkCardItems.add(result2);
        mqResultMilkCardItems.add(result3);
        mqResultMilkCardItems.add(result4);
        mqResultMilkCardItems.add(result5);
        sendMsgResult.setMqResultMilkCardItems(mqResultMilkCardItems);
        return sendMsgResult;
    }
}
