//package com.jackrain.nea.oc.oms.task.refundorder;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//import com.google.common.base.Throwables;
//import com.jackrain.nea.config.PropertiesConf;
//import com.jackrain.nea.oc.oms.es.ES4ReturnOrder;
//import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
//import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
//import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
//import com.jackrain.nea.oc.oms.services.ReturnOrderLogisticSubscribeFromKdzsService;
//import com.jackrain.nea.oc.oms.task.BaseR3Task;
//import com.jackrain.nea.st.model.table.StCShopStrategyDO;
//import com.jackrain.nea.st.service.OmsStCShopStrategyService;
//import com.jackrain.nea.task.IR3Task;
//import com.jackrain.nea.task.RunTaskResult;
//import com.jackrain.nea.util.ApplicationContextHandle;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.util.ArrayList;
//import java.util.List;
//import java.util.stream.Collectors;
//
///**
// * <AUTHOR>
// * @description: 退单物流轨迹订阅
// * @DateTime 2022/4/1 18:12
// */
//@Slf4j
//@Component
//public class AutoReturnOrderLogisticsSubscribeTask extends BaseR3Task implements IR3Task {
//    @Autowired
//    private ReturnOrderLogisticSubscribeFromKdzsService subscribeFromKdzsService;
//
//    @Autowired
//    private OmsStCShopStrategyService omsStCShopStrategyService;
//
//    @Autowired
//    private OcBReturnOrderMapper returnOrderMapper;
//
//    @Override
//    public RunTaskResult execute(JSONObject params) {
//        if (log.isDebugEnabled()) {
//            log.debug("AutoReturnOrderLogisticsSubscribeTask.start");
//        }
//        RunTaskResult result = new RunTaskResult();
//        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
//        Integer range = config.getProperty("lts.AutoReturnOrderLogisticsSubscribeTask.range", 1000);
//        List<StCShopStrategyDO> stCShopStrategyDOS = omsStCShopStrategyService.selectAllShopStrategy();
//        List<Long> needHandleShopList = stCShopStrategyDOS.stream()
//                .filter(x -> YesNoEnum.Y.getKey().equals(x.getIsReturnOrderTmsTrack())).map(StCShopStrategyDO::getCpCShopId).distinct().collect(Collectors.toList());
//        if (CollectionUtils.isEmpty(needHandleShopList)) {
//            if (log.isDebugEnabled()) {
//                log.debug("未获取到需要订阅物流轨迹的店铺");
//            }
//            result.setSuccess(true);
//            result.setMessage("未获取到需要订阅物流轨迹的店铺");
//            return result;
//        }
//        JSONArray shopIdArray = new JSONArray();
//        shopIdArray.addAll(needHandleShopList);
//        List<Long> idList = ES4ReturnOrder.searchReturnOrderForLogisticsTrace(range, shopIdArray);
//        if (log.isDebugEnabled()) {
//            log.debug("查询到符合条件的退单ID:{}", JSON.toJSONString(idList));
//        }
//        if (params != null) {
//            String ids = params.getString("ID");
//            if (StringUtils.isNotEmpty(ids)) {
//                String[] split = ids.split(",");
//                idList = new ArrayList<>(split.length);
//                for (String s : split) {
//                    if (StringUtils.isNotEmpty(s)) {
//                        idList.add(Long.valueOf(s));
//                    }
//                }
//                List<OcBReturnOrder> ocBReturnOrders = returnOrderMapper.selectOcBReturnOrderByOrder(idList);
//                idList = ocBReturnOrders.stream().filter(x -> needHandleShopList.contains(x.getCpCShopId())).distinct().map(OcBReturnOrder::getId).collect(Collectors.toList());
//                if (CollectionUtils.isEmpty(idList)) {
//                    result.setSuccess(true);
//                    result.setMessage("未有店铺策略开启退单物流轨迹订阅的单据");
//                    return result;
//                }
//            }
//        }
//        if (CollectionUtils.isEmpty(idList)) {
//            result.setSuccess(true);
//            result.setMessage("未从ES查询到符合条件的数据");
//            return result;
//        }
//        try {
//            subscribeFromKdzsService.pushData(idList);
//            result.setSuccess(true);
//            result.setMessage("执行完成");
//        } catch (Exception ex) {
//            //发生异常 所有数据返回最开始的状态
//            log.error("AutoReturnOrderLogisticsSubscribeTask.Execute Error:{}", Throwables.getStackTraceAsString(ex));
//            result.setSuccess(false);
//            result.setMessage(ex.getMessage());
//        }
//        return result;
//    }
//}
//
//
