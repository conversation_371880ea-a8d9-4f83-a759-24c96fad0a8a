package com.jackrain.nea.oc.oms.services.audit;

import com.jackrain.nea.exception.NDSException;
import lombok.Getter;

import java.util.Objects;

/**
 * @Auther: 黄志优
 * @Date: 2020/11/3 19:29
 * @Description:
 */
public enum AuditEnum {

    /**
     * 基础策略
     */
    BASE_AUDIT_STRATEGY(1, "baseAudit"),

    /**
     * 店铺 策略
     */
    STORE_AUDIT_STRATEGY(2, "storeStrategyAudit"),


    /**
     * 业务审核
     */
    BUSINESS_AUDIT(1001, "businessAudit");


    @Getter
    private Integer value;

    @Getter
    private String tag;

    AuditEnum(Integer value, String tag) {
        this.value = value;
        this.tag = tag;
    }

    public static Integer getValueFromValueTag(String value) {
        AuditEnum[] values = AuditEnum.values();
        for (AuditEnum dictStatusEnum : values) {
            if (Objects.equals(dictStatusEnum.tag, value)) {
                return dictStatusEnum.value;
            }
        }
        throw new NDSException("错误的审单配置:" + value);
    }
}
