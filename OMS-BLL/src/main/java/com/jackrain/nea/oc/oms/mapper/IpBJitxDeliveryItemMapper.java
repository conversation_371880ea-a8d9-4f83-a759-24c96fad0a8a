package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.IpBJitxDeliveryItem;
import com.jackrain.nea.oc.oms.model.table.IpBJitxDeliveryItemEx;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface IpBJitxDeliveryItemMapper extends ExtentionMapper<IpBJitxDeliveryItem> {
    @Select("SELECT * FROM ip_b_jitx_delivery_item WHERE isactive='Y' and ip_b_jitx_delivery_id=#{orderId}")
    List<IpBJitxDeliveryItemEx> selectDeliveryItemList(@Param("orderId") long orderId);


    @Select("<script>"
            + "SELECT * FROM ip_b_jitx_delivery_item "
            + "WHERE ip_b_jitx_delivery_id IN "
            + "<foreach item='item' index='index' collection='deliveryIdList' open='(' separator=',' close=')'>"
            + " #{item} "
            + "</foreach>"
            + "</script>")
    List<IpBJitxDeliveryItemEx> selectDeliveryItemListByDeliveryIdList(@Param("deliveryIdList") List<Long> deliveryIdList);
}