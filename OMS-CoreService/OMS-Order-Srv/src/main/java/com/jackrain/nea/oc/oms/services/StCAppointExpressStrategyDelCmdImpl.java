package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.annotation.OmsOperationLog;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.StCAppointExpressStrategyDelCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName StCAppointExpressStrategyDelCmdImpl
 * @Description 指定快递策略作废
 * <AUTHOR>
 * @Date 2024/4/16 10:07
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", group = "oc-core", version = "1.0")
public class StCAppointExpressStrategyDelCmdImpl extends CommandAdapter implements StCAppointExpressStrategyDelCmd {

    @Autowired
    private StCAppointExpressStrategyService strategyService;

    @Override
    @OmsOperationLog(mainTableName = "ST_C_APPOINT_EXPRESS_STRATEGY", operationType = "VOID")
    public ValueHolder execute(QuerySession session) throws NDSException {
        ValueHolder valueHolder = new ValueHolder();
        valueHolder.put("code", ResultCode.SUCCESS);
        valueHolder.put("message", "success");
        // 根据session获取id
        DefaultWebEvent event = session.getEvent();
        User user = session.getUser();
        JSONObject jsonObject = (JSONObject) event.getParameterValue("PARAM");
        JSONArray jsonArray = jsonObject.getJSONArray("ids");
        List<Long> idList = new ArrayList<>();
        // jsonarray转成list  long
        for (Object o : jsonArray) {
            idList.add(Long.parseLong(o.toString()));
        }
        // 进行作废
        strategyService.updateActive(user, idList, "N");
        return valueHolder;
    }
}
