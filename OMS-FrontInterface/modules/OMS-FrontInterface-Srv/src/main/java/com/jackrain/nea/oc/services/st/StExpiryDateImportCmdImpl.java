package com.jackrain.nea.oc.services.st;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.api.st.StExpiryDateImportCmd;
import com.jackrain.nea.oc.oms.model.enums.AppointTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.ExpiryDateTypeEnum;
import com.jackrain.nea.oc.oms.util.ExportUtil;
import com.jackrain.nea.oc.oms.vo.StCExpiryDateImpVo;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.st.service.StExpiryDateImportService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.UserImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @program: r3-oc-oms
 * @description: 效期策略导入
 * @author: caomalai
 * @create: 2022-08-11 10:44
 **/
@Slf4j
@Component
public class StExpiryDateImportCmdImpl implements StExpiryDateImportCmd {
    @Autowired
    private ExportUtil exportUtil;
    @Autowired
    private StExpiryDateImportService stExpiryDateImportService;

    SimpleDateFormat sdfBar = new SimpleDateFormat("yyyy-MM-dd HH:mm");
    SimpleDateFormat sdfSecondBar = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    SimpleDateFormat sdfSlash = new SimpleDateFormat("yyyy/MM/dd HH:mm");
    SimpleDateFormat sdfSecondSlash = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");


    public ValueHolderV14 queryTemplateDownloadUrl() {
        ValueHolderV14 holderV14 = new ValueHolderV14(ResultCode.SUCCESS, "商品效期策略头明细导入模板下载成功！");
        /**
         *  拼接Excel主表sheet表头字段
         * */
        String[] mainNames = {"类型", "平台店铺", "开始时间","结束时间","指定维度","指定内容","指定类型","开始生产日期/天数","结束生产日期/天数","订单标签","优先最便宜快递"};
        String[] mustNames = {"类型","指定维度","指定内容","指定类型","开始生产日期/天数","结束生产日期/天数","订单标签","优先最便宜快递"};
        String[] orderKeys = {"expiryTypeName", "appointDimensionName", "appointContent", "appointTypeName", "startDateDay", "endDateDay", "orderLabel","cheapestExpress"};

        User user = new UserImpl();
        ((UserImpl) user).setName("");
        List<String> mainList = Lists.newArrayList(mainNames);
        List<String> mustList = Lists.newArrayList(mustNames);
        List<String> orderKeyList = Lists.newArrayList(orderKeys);
        //生成Excel
        XSSFWorkbook hssfWorkbook = new XSSFWorkbook();
        hssfWorkbook = exportUtil.executeSheet(hssfWorkbook, "商品效期策略头明细导入头明细", "", mainList, mustList,
                orderKeyList, Lists.newArrayList(), false);
        //hssfWorkbook = exportUtil.executeOrderSheet("商品效期策略头明细导入头明细",  mainList);
        String putMsg = exportUtil.saveFileAndPutOss(hssfWorkbook, "商品效期策略头明细导入模板",
                user, "OSS-Bucket/EXPORT/ST_C_EXPIRY_DATE_STRATEGY/");
        holderV14.setData(putMsg);
        return holderV14;
    }


    public ValueHolderV14 importDataList(List<StCExpiryDateImpVo> dataImpVos, User user) {
        ValueHolderV14<Object> holderV14 = new ValueHolderV14<>(ResultCode.SUCCESS, "导入成功！");
        String key =
                dataImpVos.stream().map(o -> Optional.ofNullable(o.getExpiryTypeName()).orElse(""))
                        .distinct().sorted().collect(Collectors.joining(","));
        String redisKey = "oc:oms:st_c_expiry_date_strategy:import:"+key;
        CusRedisTemplate<String, String> objRedisTemplate = RedisOpsUtil.getStrRedisTemplate();
        if (objRedisTemplate.hasKey(redisKey)) {
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("请勿重复导入！");
            return holderV14;
        }
        List<StCExpiryDateImpVo> validDataImpVos = checkValid(dataImpVos);
        int successNum = stExpiryDateImportService.batchSaveData(validDataImpVos, user);
        int failNum = dataImpVos.size()-successNum;
        if (failNum>0) {
            holderV14 = new ValueHolderV14<>();
            holderV14.setData(exportResult(dataImpVos, user));
            holderV14.setCode(ResultCode.FAIL); //
            holderV14.setMessage("商品效期策略头明细导入失败条数:["+failNum+"]，成功条数:["+successNum+"]，详情见文件内容");
        }else{
            holderV14.setMessage("商品效期策略头明细导入成功条数:["+successNum+"]");
        }
        log.info("商品效期策略头明细导入结果 {}", JSON.toJSONString(holderV14));
        return holderV14;
    }


    /**
     * 参数有效性校验
     * @param dataImpVos
     * @return
     */
    private List<StCExpiryDateImpVo> checkValid(List<StCExpiryDateImpVo> dataImpVos) {
        List<StCExpiryDateImpVo> validDataImpVos = new ArrayList<>();
        // 数据缺失校验
        StringBuilder checkMessage = new StringBuilder();
        for(StCExpiryDateImpVo vo:dataImpVos) {
            if(log.isDebugEnabled()){
                log.debug("商品效期头明细导入行数据："+JSON.toJSONString(vo));
            }
            if (Objects.isNull(vo)) {
                checkMessage.append("[商品效期策略入参不能为空]");
            }else {
                if (Objects.isNull(vo.getExpiryTypeName())) {
                    checkMessage.append("[类型不能为空]");
                }
                if(StringUtils.isBlank(vo.getStartTime())){
                    checkMessage.append("[付款开始时间不能为空]");
                }
                if(StringUtils.isBlank(vo.getEndTime())){
                    checkMessage.append("[付款结束时间不能为空]");
                }
                Date startDatePay = checkDateFormat(vo.getStartTime());
                if(startDatePay==null){
                    checkMessage.append("[付款开始日期格式有误]");
                }
                Date endDatePay = checkDateFormat(vo.getEndTime());
                if(endDatePay==null){
                    checkMessage.append("[付款结束日期格式有误]");
                }
                if(startDatePay!=null && endDatePay!=null && startDatePay.getTime()>endDatePay.getTime()){
                    checkMessage.append("[付款结束日期不允许小于付款开始日期]");
                }
                if(ExpiryDateTypeEnum.APPOINT_SHOP.getKey().equals(vo.getExpiryType())){
                    if(StringUtils.isBlank(vo.getPlatformShopName())){
                        checkMessage.append("[指定店铺时平台店铺不能为空]");
                    }
                }
                if(StringUtils.isBlank(vo.getAppointDimensionName())){
                    checkMessage.append("[指定维度不能为空]");
                }
                if(StringUtils.isBlank(vo.getAppointContent())){
                    checkMessage.append("[指定内容不能为空]");
                }
                if(StringUtils.isBlank(vo.getAppointTypeName())){
                    checkMessage.append("[指定类型不能为空]");
                }
                if(StringUtils.isBlank(vo.getStartDateDay())){
                    checkMessage.append("[开始生产日期/天数不能为空]");
                }
                if(StringUtils.isBlank(vo.getEndDateDay())){
                    checkMessage.append("[结束生产日期/天数不能为空]");
                }
                if(StringUtils.isBlank(vo.getOrderLabel())){
                    checkMessage.append("[订单标签不能为空]");
                }
                if(AppointTypeEnum.DATE_SCOPE.getKey().equals(vo.getAppointType())){
                    Date endDate = null;
                    Date startDate = null;
                    try{
                        startDate = DateUtils.parseDate(vo.getStartDateDay(),"yyyyMMdd");
                    }catch (Exception e){
                        checkMessage.append("[开始生产日期格式有误"+vo.getStartDateDay()+"]");
                    }
                    try{
                        endDate = DateUtils.parseDate(vo.getEndDateDay(),"yyyyMMdd");
                    }catch (Exception e){
                        checkMessage.append("[结束生产日期格式有误"+vo.getEndDateDay()+"]");
                    }
                    if(startDate!=null && endDate!=null && startDate.getTime()>endDate.getTime()){
                        checkMessage.append("[结束生产日期不允许小于开始日期]");
                    }
                }else if(AppointTypeEnum.DAYS_SCOPE.getKey().equals(vo.getAppointType())){
                    boolean isValid = true;
                    if(!StringUtils.isNumeric(vo.getStartDateDay()) || Long.valueOf(vo.getStartDateDay())<0){
                        checkMessage.append("[开始生产天数必须为正整数]");
                        isValid = false;
                    }
                    if(!StringUtils.isNumeric(vo.getEndDateDay()) || Long.valueOf(vo.getEndDateDay())<0){
                        checkMessage.append("[结束生产天数必须为正整数]");
                        isValid = false;
                    }
                    if(isValid && Long.valueOf(vo.getStartDateDay())>Long.valueOf(vo.getEndDateDay())){
                        checkMessage.append("[结束生产天数不允许小于开始生产天数]");
                    }
                }
            }
            if (StringUtils.isNotEmpty(checkMessage)) {
                if(StringUtils.isNotBlank(vo.getDesc())){
                    vo.setDesc(vo.getDesc()+checkMessage.toString());
                }else{
                    vo.setDesc(checkMessage.toString());
                }
                checkMessage.setLength(0);
            }else if(StringUtils.isBlank(vo.getDesc())){
                validDataImpVos.add(vo);
            }
        }
        return validDataImpVos;
    }

    /**
     * 日期格式转换
     * @param param
     * @return
     */
    public Date checkDateFormat(String param){
        if(StringUtils.isBlank(param)){
            return null;
        }
        try {
            return sdfSlash.parse(param);
        }catch (Exception e) {
            try {
                return sdfSecondSlash.parse(param);
            }catch (Exception e1) {
                try {
                    return sdfSecondBar.parse(param);
                }catch (Exception e2) {
                    try {
                        return sdfBar.parse(param);
                    }catch (Exception e3) {
                        e3.getMessage();
                        return null;
                    }
                }
            }
        }
    }


    /**
     *
     * @param dataImpVos
     * @param user
     * @return
     */
    private String exportResult(List<StCExpiryDateImpVo> dataImpVos, User user) {
        List<StCExpiryDateImpVo> errorList = dataImpVos.parallelStream().filter(x -> x.getDesc() != null).collect(Collectors.toList());
        // 列名
        String[] columnNames = {"主表行号", "错误原因"};
        List<String> c = Lists.newArrayList(columnNames);
        // map中的key
        String[] keys = {"rowNum", "desc"};
        List<String> k = Lists.newArrayList(keys);
        Workbook hssfWorkbook = exportUtil.execute("商品效期策略头明细导入", "商品效期策略头明细导入", c, k, errorList);
        return exportUtil.saveFileAndPutOss(hssfWorkbook, "商品效期策略头明细导入错误信息", user, "OSS-Bucket/IMPORT/ST_C_EXPIRY_DATE_STRATEGY/");
    }

}
